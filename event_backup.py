# -*- coding: utf-8 -*-
"""
Author: TruongNV
Company: MobioVN
Created Date: 04/11/2019
Describe:
"""

# ######################### Administration System  ###########################
# # version: 1.0.0
# ############################################################################################
# """
# @api {post} /events/api/v1.0/systems/administrator/config/merchant  Get data
# @apiDescription API nhận user_event từ hệ thống Administrator
# @apiVersion 1.0.0
# @apiGroup Administrator
# @apiName ImportMerchantConfig
# @apiIgnore
#
# @apiHeader (Headers:) {String} Content-Type <code>application/json</code>
# @apiUse 401
# @apiUse 404
# @apiUse 405
# @apiUse 412
# @apiUse 500
# @apiUse lang
#
# @apiParam (Body:)   {string}        merchant_id                  UUID merchant_id tương ứng với merchant
# @apiParam (Body:)   {Object}       [data]                        Thông tin cấu hình cho merchant, nếu <code>null</code> hệ thống sẽ setup giá trị defaul
#
# @apiParam (datas:)   {string}       [timezone]                   Timezone của merchant. </br>
# <li><code>default:  "Asia/Ho_Chi_Minh"</code></li>
# <li>Reference: Reference: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones</li>
# @apiParam (datas:)   {Array}       [excel_fields]                List các import fields default, setup cho merchant trong việc import profile theo fields. </br>
# <li><code>default:  "phone_number", "email", "name", "people_id", "gender", "address", "birthday", "salary", "tags"</code></li>
#
#
# @apiParamExample    {json}      Body example:
# {
#   "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
#   "data": {
#     "timezone": "Asia/Dubai",
#     "excel_fields": [
#       "phone_number",
#       "email"
#     ]
#   }
# }
# """
#
# ######################### Import B2C  ###########################
# # version: 1.0.0
# ############################################################################################
# """
# @api {post} /events/api/v1.0/systems/e33/b2c  E33 - Import B2C
# @apiDescription API import B2C user_event
# @apiVersion 1.0.0
# @apiGroup Sunworld
# @apiName ImportB2C
#
# @apiHeader (Headers:) {String} Content-Type <code>application/json</code>
# @apiUse 401
# @apiUse 404
# @apiUse 405
# @apiUse 412
# @apiUse 500
# @apiUse lang
#
# @apiParam (Body:)   {string}        merchant_id                 UUID merchant_id quản lý khách hàng
# @apiParam (Body:)   {string}       booking_date                Ngày booking (YYYY-MM-DD)
# @apiParam (Body:)   {string}       business_code               Business code mà khách hàng tương tác:
# <li><code>SMARTWF_PXP_1010:  Fansipan</code></li>
# <li><code>SMARTWF_HALONG_1010:  HaLong</code></li>
# @apiParam (Body:)   {Array[]}       datas                       Tập thông tin khách hàng. Tối đa 1000 phần tử
# @apiParam (datas:)   {string}       phone_number                SDT của Khách hàng
# @apiParam (datas:)   {string}       email                       Email của Khách hàng
# @apiParam (datas:)   {string}       name                        Tên của Khách hàng
# @apiParam (datas:)   {string}       people_id                   Số CMND/CCCD của Khách hàng
# @apiParam (datas:)   {int}          quantity_of_ticket          Số lượng vé Khách hàng mua
# @apiParam (datas:)   {string}       booking_time                Thời gian user tương tác (timestamp)
# @apiParam (datas:)   {string}       check_in_day                Ngày sử dụng ticket (timestamp)
#
#
# @apiParamExample    {json}      Body example:
# {
#   "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
#   "business_code": "SMARTWF_PXP_1010",
#   "booking_date": "2018-11-11",
#   "datas": [
#     {
#       "booking_time": "1538703463.440775",
#       "check_in_day": "1544576400.000000"
#       "phone_number": "0986223344",
#       "email": "<EMAIL>",
#       "name": "Nguyen Van A",
#       "people_id": "012xxxxxx",
#       "quantity_of_ticket": 2
#     },
#     {
#       "booking_time": "1538803663.440775",
#       "check_in_day": "1544586400.000000"
#       "phone_number": "0912789987",
#       "email": "<EMAIL>",
#       "name": "Nguyen Thi A",
#       "people_id": "012xxxxxx",
#       "quantity_of_ticket": 5
#     }
#   ]
# }
#
# @apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
# {
#    "code": 200,
#    "lang": "vi",
#    "message": "request thành công."
# }
# """
#
# ######################### Import B2B  ###########################
# # version: 1.0.0
# #################################################################
# """
# @api {post} /events/api/v1.0/systems/e32/b2b  E32 - Import B2B
# @apiDescription API import B2B user_event
# @apiVersion 1.0.0
# @apiGroup Sunworld
# @apiName ImportB2B
#
# @apiHeader (Headers:) {String} Content-Type <code>application/json</code>
# @apiUse 401
# @apiUse 404
# @apiUse 405
# @apiUse 412
# @apiUse 500
# @apiUse lang
#
# @apiParam (Body:)   {string}        merchant_id                 UUID merchant_id quản lý khách hàng
# @apiParam (Body:)   {string}        booking_date                Ngày booking (YYYY-MM-DD)
# @apiParam (Body:)   {string}       business_code               Business code mà khách hàng tương tác:
# <li><code>SMARTWF_PXP_1010:  Fansipan</code></li>
# <li><code>SMARTWF_HALONG_1010:  HaLong</code></li>
# @apiParam (Body:)   {Array[]}       datas                       Tập thông tin khách hàng. Tối đa 1000 phần tử
# @apiParam (datas:)   {string}       phone_number                SDT của Khách hàng
# @apiParam (datas:)   {string}       email                       Email của Khách hàng
# @apiParam (datas:)   {string}       name                        Tên của Khách hàng
# @apiParam (datas:)   {string}       people_id                   Số CMND/CCCD của Khách hàng
# @apiParam (datas:)   {int}          quantity_of_ticket          Số lượng vé Khách hàng mua
# @apiParam (datas:)   {string}       booking_time                Thời gian user tương tác (timestamp)
# @apiParam (datas:)   {string}       check_in_day                Ngày sử dụng ticket (timestamp)
# @apiParam (datas:)   {string}       company_id                  Uuid Travel Agency
# @apiParam (datas:)   {string}       company_name                Tên của công ty Travel Agency
#
#
# @apiParamExample    {json}      Body example:
# {
#   "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
#   "business_code": "SMARTWF_HALONG_1010",
#   "booking_date": "2018-11-11",
#   "datas": [
#     {
#       "booking_time": "1538703463.440775",
#       "check_in_day": "1544576400.000000"
#       "phone_number": "0986223344",
#       "email": "<EMAIL>",
#       "name": "Nguyen Van A",
#       "people_id": "012xxxxxx",
#       "quantity_of_ticket": 2,
#       "company_id": "uuid",
#       "company_name": "Cong Ty Du Lich A"
#     },
#     {
#       "booking_time": "1538703463.440775",
#       "check_in_day": "1544576400.000000"
#       "phone_number": "0912789987",
#       "email": "<EMAIL>",
#       "name": "Nguyen Thi A",
#       "people_id": "012xxxxxx",
#       "quantity_of_ticket": 5,
#       "company_id": "uuid",
#       "company_name": "Cong Ty Du Lich B"
#     }
#   ]
# }
#
# @apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
# {
#    "code": 200,
#    "lang": "vi",
#    "message": "request thành công."
# }
# """
#
#
#
# # ######################## Mobio Mobile App - News Event ################
# # version: 1.0.0
# # ################################################################
# """
# @api {post} /events/api/v1.0/mobio_app/news  Mobile News Event
# @apiDescription API record all profile action react to news
# @apiVersion 1.0.0
# @apiGroup Mobile Event
# @apiName MobileNewsAction
#
# @apiHeader (Headers:) {String} Content-Type <code>application/json</code>
# @apiUse 401
# @apiUse 404
# @apiUse 405
# @apiUse 412
# @apiUse 500
# @apiUse lang
#
# @apiParam (Body:)   {string}        merchant_id             UUID của merchant
# @apiParam (Body:)   {string}        business_code           Code tương ứng business_case_id do bên mobio cung cấp
# @apiParam (Body:)   {string}        profile_id              UUID của profile trong hệ thống
# @apiParam (Body:)   {string}        [phone_number]          Số điện thoại của khách hàng
# @apiParam (Body:)   {string}        [third_party_id]        ID định danh profile do bên thứ 3 cung cấp cho mobio mobile app (Facebook Google ... etc)
# @apiParam (Body:)   {string}        [third_party_source]    Định danh để xác định third_party <code>(do bên Mobio Mobile App định nghĩa)</code>
# @apiParam (Body:)   {string}        news_id                 ID định danh bài viết đó trên hệ thống MOBIO
# @apiParam (Body:)   {float}         action_time             Thời gian khách hàng tương tác với bài viết (timestamp định dạng Python)
# @apiParam (Body:)   {array}         [category_tag]          Các category sản phẩm tương ứng với bài viết
# @apiParam (Body:)   {array}         [feature_tag]           Các tag định danh tương ứng với bài viết
#
#
# @apiParamExample    {json}      Body example:
# {
#   "merchant_id": "92552057-f185-49df-a514-52feb459c3c3",
#   "business_code": "PingComShop",
#   "profile_id": "92552057-f185-49df-a514-52feb459c3c3",
#   "phone_number": "0985111111".
#   "third_party_id": "assdxc-asceca-xcdefvac",
#   "third_party_source": "GOOGLE",
#   "news_id": "123123-sdaq-2312",
#   "action_time": 1554871899.558573,
#   "category_tag": ["Mỹ phẩm", "hàng tiêu dùng"],
#   "feature_tag": ["hàng cao cấp"]
# }
# """
#
# # ######################## NGINX LOG ################
# # version: 1.0.0
# # ################################################################
# """
# @api {post} /events/api/v1.0/nginx_log  Nginx Log API
# @apiDescription API record all information when profile find store of merchant
# @apiVersion 1.0.0
# @apiGroup Nginx Log
# @apiName GetDataFromNginx
#
# @apiHeader (Headers:) {String} Content-Type <code>application/json</code>
# @apiUse 401
# @apiUse 404
# @apiUse 405
# @apiUse 412
# @apiUse 500
# @apiUse lang
#
# @apiParam (Body:)   {string}        source                  Source log nhận từ nginx, do bên event quy định
# <li>Allow values: <code>"Mobile"</code></li>
# @apiParam (Body:)   {string}        url                     URL nhận được request đầu nginx_log
# <li><code>Đăng ký với event_module</code></li>
# @apiParam (Body:)   {object}        data                    Các thông tin nhận được khi parse request từ nginx_log
#
#
# @apiParamExample    {json}      Body example:
# {
#   "source": "Mobile",
#   "url": "https://zzzzzzzzzzz.need.sleep",
#   "data": {
#     "body": {
#         "a": a,
#         "b": b
#     },
#     "header": {
#         "c": c
#     },
#     .....
#   }
# }
# """
#
# # ######################## Mobile - Voucher Action Event ################
# # version: 1.0.0
# # ################################################################
# """
# @api {post} /events/api/v1.0/mobio_app/voucher_reaction  Mobile Voucher Action Event
# @apiDescription API record all information when profile setting filter to find voucher or product
# @apiVersion 1.0.0
# @apiGroup Mobile Event
# @apiName MobileVoucherAction
#
# @apiHeader (Headers:) {String} Content-Type <code>application/json</code>
# @apiUse 401
# @apiUse 404
# @apiUse 405
# @apiUse 412
# @apiUse 500
# @apiUse lang
#
# @apiParam (Body:)   {string}        merchant_id             UUID của merchant
# @apiParam (Body:)   {string}        business_code           Code tương ứng business_case_id do bên mobio cung cấp
# @apiParam (Body:)   {string}        profile_id              UUID của profile trong hệ thống
# @apiParam (Body:)   {string}        [phone_number]          Số điện thoại của khách hàng
# @apiParam (Body:)   {string}        [third_party_id]        ID định danh profile do bên thứ 3 cung cấp cho mobio mobile app (Facebook Google ... etc)
# @apiParam (Body:)   {string}        [third_party_source]    Định danh để xác định third_party
# <li><code>Cần Mobio Mobile cung </code></li>
# @apiParam (Body:)   {string}        voucher_id              ID của voucher trong hệ thống Mobio
# @apiParam (Body:)   {float}         action_time             Thời gian khách hàng tương tác (timestamp định dạng Python)
# @apiParam (Body:)   {string}        action_type             Hành động của khách hàng đối với voucher
# <li>Allow values: <code>"read_info", "add_favorite", "get_voucher", "rate_voucher", "comment_voucher", "reply_comment_voucher"</code></li>
# @apiParam (Body:)   {int}        [rate_point]               Điểm đánh giá của voucher
# <li>Thông tin cần có khi action_type == "rate_voucher"</li>
# @apiParam (Body:)   {int}        [max_rate]                 Thang điểm đánh giá
# <li>Thông tin cần có khi action_type == "rate_voucher"</li>
# @apiParam (Body:)   {string}        [comment_id]            ID của comment về voucher
# <li>Thông tin cần có khi action_type == ["comment_voucher", "reply_comment_voucher"]</li>
# @apiParam (Body:)   {string}        [comment_content]       Nội dung comment về voucher
# <li>Thông tin cần có khi action_type == ["comment_voucher", "reply_comment_voucher"]</li>
# @apiParam (Body:)   {string}        [reply_id]              ID của reply một comment về voucher
# <li>Thông tin cần có khi action_type == "reply_comment_voucher"</li>
# @apiParam (Body:)   {string}        [reply_content]        Nội dung reply một comment về voucher
# <li>Thông tin cần có khi action_type == "reply_comment_voucher"</li>
#
#
# @apiParamExample    {json}      Body example:
# {
#   "merchant_id": "92552057-f185-49df-a514-52feb459c3c3",
#   "business_code": "PingComShop",
#   "profile_id": "92552057-f185-49df-a514-52feb459c3c3",
#   "phone_number": "0985111111".
#   "third_party_id": "assdxc-asceca-xcdefvac",
#   "third_party_source": "GOOGLE",
#   "voucher_id": "asd-sdcqswx-21243we",
#   "action_time": 1554871899.558573,
#   "action_type": "rate_voucher",
#   "rate_point": 3,
#   "max_rate": 5
# }
# """
#
# # ######################## Mobile - Filter Setting Event ################
# # version: 1.0.0
# # ################################################################
# """
# @api {post} /events/api/v1.0/mobio_app/find_store/  Mobile Filter Setting Event
# @apiDescription API record all information when profile find store of merchant
# @apiVersion 1.0.0
# @apiGroup Mobile Event
# @apiName MobileProfileFindStore
#
# @apiHeader (Headers:) {String} Content-Type <code>application/json</code>
# @apiUse 401
# @apiUse 404
# @apiUse 405
# @apiUse 412
# @apiUse 500
# @apiUse lang
#
# @apiParam (Body:)   {string}        merchant_id             UUID của merchant
# @apiParam (Body:)   {string}        business_code           Code tương ứng business_case_id do bên mobio cung cấp
# @apiParam (Body:)   {string}        profile_id              UUID của profile trong hệ thống
# @apiParam (Body:)   {string}        [phone_number]          Số điện thoại của khách hàng
# @apiParam (Body:)   {string}        [third_party_id]        ID định danh profile do bên thứ 3 cung cấp cho mobio mobile app (Facebook Google ... etc)
# @apiParam (Body:)   {string}        [third_party_source]    Định danh để xác định third_party
# <li><code>Cần Mobio Mobile cung </code></li>
# @apiParam (Body:)   {object}        filtering_setting       Setting lọc filter voucher và product của merchant trên giao diện app
# <li><code>Chỉ gửi khi khách hàng chỉnh setting</code></li>
# <li><code>Mỗi merchant có thay đổi setting bộ lọc voucher product không ? Cần Mobile cung cấp </code></li>
# @apiParam (Body:)   {float}         action_time             Thời gian khách hàng tương tác (timestamp định dạng Python)
#
#
#
# @apiParamExample    {json}      Body example:
# {
#   "merchant_id": "92552057-f185-49df-a514-52feb459c3c3",
#   "business_code": "PingComShop",
#   "profile_id": "92552057-f185-49df-a514-52feb459c3c3",
#   "phone_number": "0985111111".
#   "third_party_id": "assdxc-asceca-xcdefvac",
#   "third_party_source": "GOOGLE",
#   "filtering_setting": {
#     "city": "Ha Noi",
#     "danh_muc": ["Ẩm thực", "giải trí"],
#     "sort: "lastest",
#     "filter_from": 0
#     "filter_to": 600,
#     "active_when_enough_point": 1
#   }
#   "action_time": 1554871899.558573
# }
# """
#
# # ######################## Mobile - Add Product ################
# # version: 1.0.0
# # ################################################################
# """
# @api {post} /events/api/v1.0/mobio_app/add_product/  Mobile Add Product Event
# @apiDescription API record all product add to bag
# @apiVersion 1.0.0
# @apiGroup Mobile Event
# @apiName Mobile Add Product
#
# @apiHeader (Headers:) {String} Content-Type <code>application/json</code>
# @apiUse 401
# @apiUse 404
# @apiUse 405
# @apiUse 412
# @apiUse 500
# @apiUse lang
#
# @apiParam (Body:)   {string}        merchant_id             UUID của merchant
# @apiParam (Body:)   {string}        business_code           Code tương ứng business_case_id do bên mobio cung cấp
# @apiParam (Body:)   {string}        profile_id              UUID của profile trong hệ thống
# @apiParam (Body:)   {string}        [phone_number]          Số điện thoại của khách hàng
# @apiParam (Body:)   {string}        [third_party_id]        ID định danh profile do bên thứ 3 cung cấp cho mobio mobile app (Facebook Google ... etc)
# @apiParam (Body:)   {string}        [third_party_source]    Định danh để xác định third_party
# <li><code>Cần Mobio Mobile cung </code></li>
# @apiParam (Body:)   {array}         drop_product            IDs product khách hàng đã bỏ vào giỏ nhưng lại bỏ ra khỏi giỏ hàng
# @apiParam (Body:)   {array}         select_product          IDs product khách hàng đã bỏ vào giỏ và thanh toán
# @apiParam (Body:)   {string}        action_time             Timestamp xảy ra action
# @apiParam (Body:)   {string}        bag_id                  ID của giỏ
#
#
# @apiParamExample    {json}      Body example:
# {
#   "merchant_id": "92552057-f185-49df-a514-52feb459c3c3",
#   "business_code": "PingComShop",
#   "profile_id": "92552057-f185-49df-a514-52feb459c3c3",
#   "phone_number": "0985111111".
#   "third_party_id": "assdxc-asceca-xcdefvac",
#   "third_party_source": "GOOGLE",
#   "drop_product": [
#     "1234-asd-124-asd3trqa",
#     "1234-asd-124-asd3trqa"
#   ],
#   "select_product": [
#     "1234-asd-124-asd3trqa",
#     "1234-asd-124-asd3trqa"
#   ],
#   "action_time": ***********.3452341234,
#   "bag_id": "assdxc-asceca-xcdefvac"
# }
# """


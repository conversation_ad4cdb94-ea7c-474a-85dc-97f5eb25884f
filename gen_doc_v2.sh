#!/bin/bash

list_folder=($(ls ./src))
i=0
for folder in ${list_folder[@]}; do
    echo "$i. $folder"
    i=$((i+1))
done

read -e -p "Enter folder index [0 - $((i-1))]: " index

echo "-------------------------------------"
FOLDER_NAME=${list_folder[$index]}
echo "You selected $FOLDER_NAME"
read -e -p "Enter domain [1 - 'https://dev.mobio.vn'; 2 - '{domain}'; 3 - 'https://api-test1.mobio.vn'; others]: " DOMAIN
if [[ "$DOMAIN" -eq '1' ]]; then
    echo "enter"
    DOMAIN="https://dev.mobio.vn"
elif [[ "$DOMAIN" -eq '2' ]]; then
    DOMAIN="{domain}"
elif [[ "$DOMAIN" -eq '3' ]]; then
    DOMAIN="https://api-test1.mobio.vn"
fi
echo "Domain is: $DOMAIN"
echo ""

TITLE_NAME=`echo $FOLDER_NAME | tr [a-z] [A-Z]`
read -e -p "Enter title [empty - $TITLE_NAME Mobio APIs document]: " TITLE
if [[ $TITLE -eq "" ]]; then
    TITLE="$TITLE_NAME Mobio APIs document"
fi
echo "Title is: $TITLE"
echo ""

list_module=("Administration" "Notify Manager" "Audience" "CallCenter" "CallCenter-v2" "Marketing" "Social" "EmailMarketing" "Ads" "ticket" "chattool" "licensing" "sale" "user_event" "profiling" "journey" "tag" "license_v2" "mobio-v2" "mobio_report" "DNC" "Social-V2" "Mail client" "Shorten url" "company" "company_crawler" "dynamic_event" "sessionmanager" "mcc_merchant" "DataOut" "Location" "Profile Factory" "Workflow" "segment" "audience_segment" "Digienty" "Workflow Report" "Workflow Internal" "Onpage Journey" "Onpage Journey Internal" "Onpage Journey Report" "Onpage Journey SDK" "Data Dictionary" "Mobio ETL" "Profiling Report", "profiling_internal", "Form Management", "Orchestration", "Master Data", "Data Management")
list_path=("adm" "nm" "audience" "callcenter/api/v1.0" "callcenter/api/v2.0" "mkt" "social/api/v1.0" "emk" "ads" "ticket" "chattool/api/v2.0" "licensing" "sale" "events/api/v1.0" "profiling_v4" "journey" "tag/api/v1.0" "license/api/v1.0" "mobio-v2" "rapporteur" "dnc/api/v1.0" "social/api/v2.0" "mailclient" "shortenurl" "company" "company_crawler" "dynamic-event" "sessionmanager" "mcc-merchant" "datasync" "location" "profile_factory" "workflow/api/v1.0" "segment" "segment/audience" "digienty" "workflow/report/api/v1.0" "workflow/internal/api/v1.0" "onpage-journey/api/v1.0" "onpage-journey/internal/api/v1.0" "onpage-journey/report/api/v1.0" "onpage-journey/sdk/api/v1.0" "data_dictionary" "mobio-etl" "profiling-report" "profiling_internal" "form" "orchestration" "master_data" "data-mgt")

j=0
for path in ${list_path[@]}; do
    echo "$j. ${list_module[$j]} --- $path"
    j=$((j+1))
done
read -e -p "Enter path api [0 - $((j-1))] : " PATH_API
if [[ ($PATH_API -ge 0) || ($PATH_API -le $(j-1)) ]]; then
    PATH_API=${list_path[$PATH_API]}
fi
echo "Path is: $PATH_API"
echo ""

DIRECTORY=$FOLDER_NAME
DOMAIN="$DOMAIN/$PATH_API"
echo "DIRECTORY: " $DIRECTORY
echo "DOMAIN: " $DOMAIN
echo "create backup..."
cp "apidoc.json" "apidoc.bak"
echo "replace text..."
sed -E "s/(\"title\"[ ]*:[ ]*)\"[^\"]+\"/\1\"$TITLE\"/g" apidoc_v2.json > apidoc.tmp
sed -E "s#(\"url\"[ ]*:[ ]*)\"[^\"]+\"#\1\"$DOMAIN\"#g" apidoc.tmp > apidoc.json
echo "======"
cat apidoc.json
echo "======"
echo "generate doc..."
echo "execute: apidoc -i src/common/ -i src/$DIRECTORY/ -o dist/$DIRECTORY/"
apidoc -i src/common/ -i src/$DIRECTORY/ -o dist/$DIRECTORY/
echo "restore backup..."
mv apidoc.bak apidoc.json
echo "clean temp..."
rm apidoc.tmp
echo "OK"

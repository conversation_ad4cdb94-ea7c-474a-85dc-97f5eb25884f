#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 02/12/2022
"""
"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/assignment-status  Danh sách đơn hàng theo trạng thái phân công
@apiGroup Deals
@apiVersion 1.0.1
@apiName DealListAssignmentStatus

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header_app_code

@apiParam	(Query:)			{String}    type_status=NEW_ASSIGNMENT   	  Kiểu trạng thái muốn lấy dữ liệu 
                                                                              <ul>
                                                                                    <li><code>NEW_ASSIGNMENT</code>: mới được phân công </li>
                                                                                    <li><code>OUT_DATE</code>: quá hạn</li>
                                                                              </ul>
@apiParam	(BODY:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả. 
                                                              VD: ["name","code"]. Tr<PERSON><PERSON><PERSON> hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính                                                             

@apiUse paging_tokens


@apiSuccess {Array}             data                          Danh sách đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": [
        {
            "state_code": "",
            "name":"Tên đơn hàng 1",
            "code": "NGHK8385",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "sale_process_id":"5d7f150be6481e870a8ce0ad",
            "process_name":"Quy trình 1",
            "state_name":"Có Lead",
            "state_ratio":10,
            "sale_value":  1569000,
            "description": "Mô tả đơn hàng",
            "assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
            "estimate_time":"2019-11-26T12:00:00Z",
            "reason_success":"",
            "reason_fail":"",
            "profiles":[
                "uuid1",
                "uuid2"
            ],
            "products":[
                "uuid1",
                "uuid2"
            ],
            "tickets":[
                "uuid1",
                "uuid2"
            ],
            "companies":[
                "uuid1",
                "uuid2"
            ],
            "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
            "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
            "updated_time":"2019-11-26T12:00:00Z",
            "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
            "deal_new": 1 //1:mới, 0: hết mới,
            "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
        },
        ...
    ]
    "code": 200,
    "message": "request thành công."
}
"""

"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/assignment-status  Danh sách đơn hàng theo trạng thái phân công
@apiGroup Deals
@apiVersion 1.0.2
@apiName DealListAssignmentStatus

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header_app_code

@apiParam	(BODY:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả. 
                                                              VD: ["name","code"]. Trường hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính                                                             

@apiUse paging_tokens


@apiSuccess {Array}             data                          Danh sách đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": [
        {
            "state_code": "",
            "name":"Tên đơn hàng 1",
            "code": "NGHK8385",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "sale_process_id":"5d7f150be6481e870a8ce0ad",
            "process_name":"Quy trình 1",
            "state_name":"Có Lead",
            "state_ratio":10,
            "sale_value":  1569000,
            "description": "Mô tả đơn hàng",
            "assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
            "estimate_time":"2019-11-26T12:00:00Z",
            "reason_success":"",
            "reason_fail":"",
            "profiles":[
                "uuid1",
                "uuid2"
            ],
            "products":[
                "uuid1",
                "uuid2"
            ],
            "tickets":[
                "uuid1",
                "uuid2"
            ],
            "companies":[
                "uuid1",
                "uuid2"
            ],
            "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
            "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
            "updated_time":"2019-11-26T12:00:00Z",
            "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
            "deal_new": 1 //1:mới, 0: hết mới,
            "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
        },
        ...
    ]
    "code": 200,
    "message": "request thành công."
}
"""

# ------------------ Cập nhật bước bán hàng của deal --------------------
"""
@api {PUT} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/change/steps           Cập nhật bước bán hàng của deal         
@apiGroup Deals
@apiVersion 1.0.2
@apiName UpdateSaleStepsDeal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header_app_code

@apiParam	(BODY:)			{string}	    state_code		                Mã của bước bán hàng mới.
@apiParam	(BODY:)			{object}	    result_before_step		        Kết quả của bước trước.
@apiParam	(BODY:)			{string}	    result_after_step.description   Mô tả   
@apiParam	(BODY:)			{timestamp}	    result_after_step.contract_time Thời gian cần liên hệ
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                           Dữ liệu




@apiSuccessExample {json} Response Example
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

#############################################################################
# Phân công xử lý đơn hàng
# version: 1.0.1
#############################################################################

"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/action/assignment  Phân công xử lý đơn hàng
@apiGroup Deals
@apiVersion 1.0.1
@apiName DealAssignment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header_app_code

@apiParam	(BODY:)			{Array}	    deals_info			    Danh sách đơn hàng cần được phân công
@apiParam	(BODY:)			{String}	deals_info.deal_id      ID đơn hàng		        
@apiParam	(BODY:)			{String}	deal_id.team_id         Team ID		        
@apiParam	(BODY:)			{String}	[deal_id.assignee_id]   Id nhân viên được chọn làm deal owner		        


@apiParamExample {json} Body example
{
    "deals_info": [
        {
            "deal_id": "610cab22d1cac521b4ed19d4",
            "team_id": "46ac5b84-c8a9-4dc5-81fa-f1e0f58bab3f",
            "assignee_id": "0ee5b357-7669-4698-8160-2a960ca109ae"
        }
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apisuccess {Array}             data                          Thống kê số lượng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
}
"""

####################################################################################################
# THÊM MỚI ĐƠN HÀNG
# version: 1.0.1
####################################################################################################
# Version 1.0.2
"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/action/add  Thêm mới đơn hàng
@apiDescription Dịch vụ thêm mới đơn hàng bằng tay (Bổ sung kiểm tra vị trí saveview)
@apiGroup Deals
@apiVersion 1.0.2
@apiName DealAdd

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header_app_code
@apiUse json_header

@apiParam	(Body:)			{String}	deal_type			  Loại đơn hàng tạo mới
                                                              <ul>
                                                                <li><code>ADD_CUSTOMER</code> : Khách hàng Cá nhân</li>
                                                                <li><code>ADD_PERSONAL</code> : Khách hàng Doanh Nghiệp</li>
                                                              </ul>
@apiParam	(Body:)			{String}	name			      Tên đơn hàng
@apiParam	(Body:)			{String}	sale_process_id		  ID Quy trình bán hàng (<code>Gọi API lấy danh sách quy trình</code> )
@apiParam	(Body:)			{String}	state_code		      Mã code trạng thái đơn hàng (<code>Lấy trạng thái nằm trong quy trình được chọn</code>)
@apiParam	(Body:)			{Array}	    assignee		      Người phụ trách đơn hàng (<code>Gọi API lấy danh sách Nhân viên phụ trách</code>)
@apiParam	(Body:)			{string}	assignee.assignee_id  ID người phụ trách (<code>account_id: Ở API lấy thông tin nhân viên</code>
@apiParam	(Body:)			{string}	assignee.permission	  Quyền trên đơn hàng
                                                              <ul>
                                                                <li><code>owner</code> : Deal owner</li>
                                                                <li><code>supporter</code> : Deal supporter</li>
                                                              </ul>
@apiParam	(Body:)			{number}	[sale_value]		  Giá trị đơn hàng
@apiParam	(Body:)			{string}	[description]		  Mô tả
@apiParam	(Body:)			{datetime}	[estimate_time]		  Thời gian chốt đơn hàng
                                                              <code>Định dạng: dd/mm/yyyy </code>
@apiParam	(Body:)			{Array}	    [profiles]		      Danh sách profile (<b>Bắt buộc khi tạo KHCN</b>
@apiParam	(Body:)			{String}	[product_line]        Dòng sản phẩm  (<code>Call API lấy dòng sản phẩm</code>)
@apiParam	(Body:)			{Array}	    [product_types]       Loại sản phẩm (<code>Call API lấy loại sản phẩm theo dòng sản phẩm</code>)
@apiParam	(Body:)			{String}	[products_bank]       Sản phẩm (<code>Call API lấy sản phẩm theo dòng, loại sản phẩm(nếu có)</code>)
@apiParam	(Body:)			{String}	[description]         Mô tả
@apiParam	(Body:)			{Array}	    [tickets]		      Danh sách ticket (<code>Call API lấy danh sách Ticket (Nếu có)</code>)       
@apiParam	(Body:)			{Array}	    [companies]		      Danh sách công ty (<code>Bắt buộc khi tạo KHDN</code>)                                                  
@apiParam	(Body:)			{string}	[reason_success]	  Lý do chốt đơn thành công
@apiParam	(Body:)			{string}	[reason_fail]	      Lý do chốt đơn thất bại
@apiParam	(Body:)			{Integer}	ignore_duplicate	  <code>1- update kể cả trùng,0- gửi lại thông báo nếu trùng</code>
@apiParam	(Body:)			{String}	_dyn_khoi_nghiep_vu_1670403167230	Khối nghiệp vụ (Click vào màn hình tạo đơn hàng [KHCN, KHDN, NHBH] thì chọn giá trị tương ứng)
                                                              <ul>
                                                                <li><code>Khách hàng Cá nhân</code> : Khách hàng Cá nhân</li>
                                                                <li><code>Khách hàng Doanh nghiệp</code> : Khách hàng Doanh nghiệp</li>
                                                                <li><code>Ngân hàng bảo hiểm</code> : Ngân hàng bảo hiểm</li>
                                                              </ul>
@apiParam	(Body:)			{Array}	    [sale_filters]		  danh sách các điều kiện lọc đơn hàng
@apiUse sale_filters 
                                                              

@apiParamExample {json} Body example
{
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "name":"Tên đơn hàng 1",
    "sale_process_id":"5d7f150be6481e870a8ce0ad",
    "state_code" : "LEAD",
    "sale_value":  1569000,
    "description": "Mô tả đơn hàng",
    "assignee": [
        {
            "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
            "permission": "owner"
        },
        {
            "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
            "permission": "owner"
        }
    ],
    "estimate_time":"24/12/2020",
    "reason_success":"",
    "reason_fail":"",
    "profiles":[
        "45042df5-2202-4964-b05f-d53e21f5f895",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "product_types": [
        "63315f75c5a5f8000e59a16c"
    ],
    "product_line": "63315f75c5a5f8000e59a0ef",
    "products_bank": "63315f76c5a5f8000e59a55b",
    "tickets":[
        "45042df5-2202-4964-b05f-d53e21f5f890",
        "45042df5-2202-4964-b05f-d53e21f5f820"
    ],
    "companies":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "sale_filters": [
        {
            "criteria_key": "cri_reason_success",
            "operator_key": "op_is_has",
            "values": [
                "khách hàng chốt đơn"
            ]
        }
    ]
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {Boolean}           validate_filter               Kết quả kiểm tra bộ lọc
                                                              <ul>
                                                                <li><code>true</code>: Thỏa mãn </li>
                                                                <li><code>false</code>: Không thỏa mãn </li>
                                                              </ul>
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "data": {
        "_id":"5dde29fda2596203036b12c4",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name":"Tên đơn hàng 1",
        "code": "NGHK8385",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "sale_process_id":"5d7f150be6481e870a8ce0ad",
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee": [
            {
                "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                "permission": "owner"
            },
            {
                "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                "permission": "owner"
            }
        ],
        "estimate_time":"2019-11-26T12:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "product_types": [
            "63315f75c5a5f8000e59a16c"
        ],
        "product_line": "63315f75c5a5f8000e59a0ef",
        "products_bank": "63315f76c5a5f8000e59a55b",
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
        "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
        "updated_time":"2019-11-26T12:00:00Z",
        "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
        "deal_new": 1, //1:mới, 0: hết mới,
        "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
    },
    "validate_filter": true,
    "code": 200,
    "message": "request thành công."
}

@apiSuccessExample {json} Response duplicate deal
{
     "data": [
    	{
    		"name":"Trư bát giới",
    		"code":"ZECQQQ271219",
    		"assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895","45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    	{
    		"name":"Đường tăng",
    		"code":"GCFJIR271219",
    		"assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891"]
    	},
    	{
    		"name":"Bạch cốt tinh",
    		"code":"VXWTED271219",
            "assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    ]
    "code": 413,
    "message": "i18n_message_valid_product_profiles_order_exist"
}
"""

# Version 1.0.1
"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/action/add  Thêm mới đơn hàng
@apiDescription Dịch vụ thêm mới đơn hàng bằng tay
@apiGroup Deals
@apiVersion 1.0.1
@apiName DealAdd

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header_app_code
@apiUse json_header

@apiParam	(Body:)			{String}	deal_type			  Loại đơn hàng tạo mới
                                                              <ul>
                                                                <li><code>ADD_CUSTOMER</code> : Khách hàng Cá nhân</li>
                                                                <li><code>ADD_PERSONAL</code> : Khách hàng Doanh Nghiệp</li>
                                                              </ul>
@apiParam	(Body:)			{String}	name			      Tên đơn hàng
@apiParam	(Body:)			{String}	sale_process_id		  ID Quy trình bán hàng (<code>Gọi API lấy danh sách quy trình</code> )
@apiParam	(Body:)			{String}	state_code		      Mã code trạng thái đơn hàng (<code>Lấy trạng thái nằm trong quy trình được chọn</code>)
@apiParam	(Body:)			{Array}	    assignee		      Người phụ trách đơn hàng (<code>Gọi API lấy danh sách Nhân viên phụ trách</code>)
@apiParam	(Body:)			{string}	assignee.assignee_id  ID người phụ trách (<code>account_id: Ở API lấy thông tin nhân viên</code>
@apiParam	(Body:)			{string}	assignee.permission	  Quyền trên đơn hàng
                                                              <ul>
                                                                <li><code>owner</code> : Deal owner</li>
                                                                <li><code>supporter</code> : Deal supporter</li>
                                                              </ul>
@apiParam	(Body:)			{number}	[sale_value]		  Giá trị đơn hàng
@apiParam	(Body:)			{string}	[description]		  Mô tả
@apiParam	(Body:)			{datetime}	[estimate_time]		  Thời gian chốt đơn hàng
                                                              <code>Định dạng: dd/mm/yyyy </code>
@apiParam	(Body:)			{Array}	    [profiles]		      Danh sách profile (<b>Bắt buộc khi tạo KHCN</b>
@apiParam	(Body:)			{String}	[product_line]        Dòng sản phẩm  (<code>Call API lấy dòng sản phẩm</code>)
@apiParam	(Body:)			{Array}	    [product_types]       Loại sản phẩm (<code>Call API lấy loại sản phẩm theo dòng sản phẩm</code>)
@apiParam	(Body:)			{String}	[products_bank]       Sản phẩm (<code>Call API lấy sản phẩm theo dòng, loại sản phẩm(nếu có)</code>)
@apiParam	(Body:)			{String}	[description]         Mô tả
@apiParam	(Body:)			{Array}	    [tickets]		      Danh sách ticket (<code>Call API lấy danh sách Ticket (Nếu có)</code>)       
@apiParam	(Body:)			{Array}	    [companies]		      Danh sách công ty (<code>Bắt buộc khi tạo KHDN</code>)                                                  
@apiParam	(Body:)			{string}	[reason_success]	  Lý do chốt đơn thành công
@apiParam	(Body:)			{string}	[reason_fail]	      Lý do chốt đơn thất bại
@apiParam	(Body:)			{Integer}	ignore_duplicate	  <code>1- update kể cả trùng,0- gửi lại thông báo nếu trùng</code>
@apiParam	(Body:)			{String}	_dyn_khoi_nghiep_vu_1670403167230	Khối nghiệp vụ (Click vào màn hình tạo đơn hàng [KHCN, KHDN, NHBH] thì chọn giá trị tương ứng)
                                                              <ul>
                                                                <li><code>Khách hàng Cá nhân</code> : Khách hàng Cá nhân</li>
                                                                <li><code>Khách hàng Doanh nghiệp</code> : Khách hàng Doanh nghiệp</li>
                                                                <li><code>Ngân hàng bảo hiểm</code> : Ngân hàng bảo hiểm</li>
                                                              </ul>

@apiParamExample {json} Body example
{
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "name":"Tên đơn hàng 1",
    "sale_process_id":"5d7f150be6481e870a8ce0ad",
    "state_code" : "LEAD",
    "sale_value":  1569000,
    "description": "Mô tả đơn hàng",
    "assignee": [
        {
            "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
            "permission": "owner"
        },
        {
            "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
            "permission": "owner"
        }
    ],
    "estimate_time":"24/12/2020",
    "reason_success":"",
    "reason_fail":"",
    "profiles":[
        "45042df5-2202-4964-b05f-d53e21f5f895",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "product_types": [
        "63315f75c5a5f8000e59a16c"
    ],
    "product_line": "63315f75c5a5f8000e59a0ef",
    "products_bank": "63315f76c5a5f8000e59a55b",
    "tickets":[
        "45042df5-2202-4964-b05f-d53e21f5f890",
        "45042df5-2202-4964-b05f-d53e21f5f820"
    ],
    "companies":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ]
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "data": {
        "_id":"5dde29fda2596203036b12c4",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name":"Tên đơn hàng 1",
        "code": "NGHK8385",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "sale_process_id":"5d7f150be6481e870a8ce0ad",
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee": [
            {
                "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                "permission": "owner"
            },
            {
                "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                "permission": "owner"
            }
        ],
        "estimate_time":"2019-11-26T12:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "product_types": [
            "63315f75c5a5f8000e59a16c"
        ],
        "product_line": "63315f75c5a5f8000e59a0ef",
        "products_bank": "63315f76c5a5f8000e59a55b",
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
        "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
        "updated_time":"2019-11-26T12:00:00Z",
        "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
        "deal_new": 1, //1:mới, 0: hết mới,
        "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
    },
    "code": 200,
    "message": "request thành công."
}

@apiSuccessExample {json} Response duplicate deal
{
     "data": [
    	{
    		"name":"Trư bát giới",
    		"code":"ZECQQQ271219",
    		"assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895","45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    	{
    		"name":"Đường tăng",
    		"code":"GCFJIR271219",
    		"assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891"]
    	},
    	{
    		"name":"Bạch cốt tinh",
    		"code":"VXWTED271219",
            "assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    ]
    "code": 413,
    "message": "i18n_message_valid_product_profiles_order_exist"
}
"""

####################################################################################################
# Chi tiết đơn hàng
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/detail  Chi tiết đơn hàng
@apiGroup Deals
@apiVersion 1.0.1
@apiName DealDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header_app_code

@apisuccess {Array}             data                          Thông tin chi tiết của đơn hàng
@apisuccess {Array}             data.profiles_info            Danh sách thông tin khách hàng
@apisuccess {Array}             data.assignee                 Thông tin nhân viên phụ trách
@apisuccess {String}            data.assignee.assignee_id     ID nhân viên
@apisuccess {String}            data.assignee.permission      Quyền của nhân viên trên đơn hàng
                                                              <ul>
                                                                <li><code>owner</code>: Nhân viên phụ trách chính 
                                                                (Chỉ 1 người phụ trách chính)</li>
                                                                <li><code>supporter</code>: Nhân viên hỗ trợ xử lý đơn hàng</li>
                                                              </ul>       

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response detail deal
{
    "code": 200,
    "data": {
        "_id": "618e3609916180df8914bda3",
        "assignee": [
            {
                "assignee_id": "e9e7a69a-1981-45b9-971f-a2f52a37d112",
                "permission": "owner"
            }
        ],
        "assignee_id": "e9e7a69a-1981-45b9-971f-a2f52a37d112",
        "brand_ids": [
            "bdee9565-3cad-40ae-b2dc-421121d70279"
        ],
        "code": "GNMPPT121121",
        "companies": [],
        "contract_time": "",
        "created_by": "cb18ab04-a673-4dce-99c6-091c1ef189ce",
        "created_merchant_id": "bdee9565-3cad-40ae-b2dc-421121d70279",
        "created_time": "2021-11-12T16:38:16Z",
        "currency": "VNĐ",
        "deal_new": 0,
        "description": "",
        "estimate_time": "",
        "id": "618e3609916180df8914bda3",
        "merchant_id": "bdee9565-3cad-40ae-b2dc-421121d70279",
        "name": "Mình là Linh nè các bạn",
        "order": **********.899148,
        "pin_to_top": 0,
        "process_name": "PVCOMBANK",
        "product_sku": null,
        "products": [],
        "profiles": [
            "61820b1e-0aa0-42b7-99d9-ae98e530a240"
        ],
        "reason_fail": "",
        "reason_success": "",
        "sale_process_id": "6141da39bd77bb76ec717417",
        "sale_value": null,
        "source": "Nhập thủ công",
        "state_code": "GQHVL7TU",
        "state_name": "GỌI",
        "state_ratio": 10,
        "status": 1,
        "tag_ids": null,
        "team_id": "430e50bc-9c09-4c81-a688-c0b82c5a9805",
        "third_party_created_time": "",
        "third_party_updated_time": "",
        "tickets": [],
        "type_create": "MANUAL",
        "updated_by": "e9e7a69a-1981-45b9-971f-a2f52a37d112",
        "updated_merchant_id": "bdee9565-3cad-40ae-b2dc-421121d70279",
        "updated_time": "2021-11-12T16:55:18Z"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

####################################################################################################
# Sửa đơn hàng
# version: 1.0.2                                                                                   #
# version: 1.0.1                                                                                   #
####################################################################################################

# Version 1.0.2
"""
@api {PUT} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/update  Cập nhật thông tin đơn hàng
@apiGroup Deals
@apiVersion 1.0.1
@apiName DealUpdateV2

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header_app_code
@apiUse json_header

@apiParam	(Body:)			{String}	deal_type			  Loại đơn hàng tạo mới
                                                              <ul>
                                                                <li><code>ADD_CUSTOMER</code> : Khách hàng Cá nhân</li>
                                                                <li><code>ADD_PERSONAL</code> : Khách hàng Doanh Nghiệp</li>
                                                              </ul>
@apiParam	(Body:)			{Array}	    assignee		      Người phụ trách đơn hàng
@apiParam	(Body:)			{string}	assignee.assignee_id  ID người phụ trách
@apiParam	(Body:)			{string}	assignee.permission	  Quyền trên đơn hàng
                                                              <ul>
                                                                <li><code>owner</code> : Deal owner</li>
                                                                <li><code>supporter</code> : Deal supporter</li>
                                                              </ul>
@apiParam   (Body:)         {String}    state_code              Mã code trạng thái đơn hàng
@apiParam   (Body:)         {number}    sale_value              Giá trị đơn hàng
@apiParam   (Body:)         {string}    description             Mô tả
@apiParam   (Body:)         {string}    assignee_id             ID người phụ trách đơn hàng
@apiParam   (Body:)         {datetime}  estimate_time           Thời gian chốt đơn hàng
                                                                 <code>Định dạng: dd/mm/yyyy </code>
@apiParam   (Body:)         {string}    reason_success          Lý do chốt đơn thành công
@apiParam   (Body:)         {string}    reason_fail             Lý do chốt đơn thất bại
@apiParam   (Body:)         {Array}     profiles                Danh sách profile
@apiParam   (Body:)         {Array}     products                Danh sách sản phẩm
@apiParam   (Body:)         {Array}     tickets                 Danh sách ticket
@apiParam   (Body:)         {Array}     companies               Danh sách công ty
@apiParam   (Body:)         {Array}     product_categories      Danh sách danh mục sản phẩm
@apiParam   (Body:)         {Integer}   ignore_duplicate        <code>1- update kể cả trùng,0- gửi lại thông báo nếu trùng</code>
@apiParam	(Body:)			{String}	[_dyn_khoi_nghiep_vu_1670403167230]	Khối nghiệp vụ (Click vào màn hình tạo đơn hàng [KHCN, KHDN, NHBH] thì chọn giá trị tương ứng)
                                                                  <ul>
                                                                    <li><code>Khách hàng Cá nhân</code> : Khách hàng Cá nhân</li>
                                                                    <li><code>Khách hàng Doanh nghiệp</code> : Khách hàng Doanh nghiệp</li>
                                                                    <li><code>Ngân hàng bảo hiểm</code> : Ngân hàng bảo hiểm</li>
                                                                  </ul>
@apiParam	(Body:)			{Array}	    [sale_filters]		  danh sách các điều kiện lọc đơn hàng
@apiUse sale_filters                                                                   

@apiParamExample {json} Body example
{
    "deal_type": "ADD_CUSTOMER",
    "name":"Tên đơn hàng 1",
    "ignore_duplicate":1,
    "sale_process_id":"5d7f150be6481e870a8ce0ad",
    "state_code" : "LEAD",
    "sale_value":  1569000,
    "description": "Mô tả đơn hàng",
    "assignee": [
        {
            "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
            "permission": "owner"
        },
        {
            "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
            "permission": "owner"
        }
    ],
    "estimate_time":"24/12/2022",
    "reason_success":"",
    "reason_fail":"",
    "profiles":[
        "45042df5-2202-4964-b05f-d53e21f5f895",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894"
    ],
    "tickets":[
        "45042df5-2202-4964-b05f-d53e21f5f890",
        "45042df5-2202-4964-b05f-d53e21f5f820"
    ],
    "companies":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "product_categories": [
        "5eafe2b8e2a37eb592100e4b",
        "5eafe2f4ae89793519483d10"
    ]
    type_update:"quick"
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {Boolean}           validate_filter               Kết quả kiểm tra bộ lọc
                                                              <ul>
                                                                <li><code>true</code>: Thỏa mãn </li>
                                                                <li><code>false</code>: Không thỏa mãn </li>
                                                              </ul>
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "code":200,
    "data":{
        "id": "5de61bb97dfdcfe3cbb5d89e",
        "name":"Tên đơn hàng 1",
        "ignore_duplicate":1,
        "assignee": [
            {
                "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                "permission": "owner"
            },
            {
                "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                "permission": "owner"
            }
        ],
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
        "estimate_time":"2022-12-24T00:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "product_categories": [
            "5eafe2b8e2a37eb592100e4b",
            "5eafe2f4ae89793519483d10"
        ],
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ]
    },
    "validate_filter": false,
    "lang": "vi",
    "message": "request thành công."

}

@apiSuccessExample {json} Response duplicate deal
{
    "data": [
    	{
    		"name":"Trư bát giới",
    		"code":"ZECQQQ271219",
            "assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895","45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	}
    ]
    "code": 413,
    "message": "i18n_message_valid_product_profiles_order_exist"
}
"""

# Version 1.0.1

"""
@api {PUT} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/update  Cập nhật thông tin đơn hàng
@apiGroup Deals
@apiVersion 1.0.1
@apiName DealUpdateV2

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header_app_code
@apiUse json_header

@apiParam	(Body:)			{String}	deal_type			  Loại đơn hàng tạo mới
                                                              <ul>
                                                                <li><code>ADD_CUSTOMER</code> : Khách hàng Cá nhân</li>
                                                                <li><code>ADD_PERSONAL</code> : Khách hàng Doanh Nghiệp</li>
                                                              </ul>
@apiParam	(Body:)			{Array}	    assignee		      Người phụ trách đơn hàng
@apiParam	(Body:)			{string}	assignee.assignee_id  ID người phụ trách
@apiParam	(Body:)			{string}	assignee.permission	  Quyền trên đơn hàng
                                                              <ul>
                                                                <li><code>owner</code> : Deal owner</li>
                                                                <li><code>supporter</code> : Deal supporter</li>
                                                              </ul>
@apiParam   (Body:)         {String}    state_code              Mã code trạng thái đơn hàng
@apiParam   (Body:)         {number}    sale_value              Giá trị đơn hàng
@apiParam   (Body:)         {string}    description             Mô tả
@apiParam   (Body:)         {string}    assignee_id             ID người phụ trách đơn hàng
@apiParam   (Body:)         {datetime}  estimate_time           Thời gian chốt đơn hàng
                                                                 <code>Định dạng: dd/mm/yyyy </code>
@apiParam   (Body:)         {string}    reason_success          Lý do chốt đơn thành công
@apiParam   (Body:)         {string}    reason_fail             Lý do chốt đơn thất bại
@apiParam   (Body:)         {Array}     profiles                Danh sách profile
@apiParam   (Body:)         {Array}     products                Danh sách sản phẩm
@apiParam   (Body:)         {Array}     tickets                 Danh sách ticket
@apiParam   (Body:)         {Array}     companies               Danh sách công ty
@apiParam   (Body:)         {Array}     product_categories      Danh sách danh mục sản phẩm
@apiParam   (Body:)         {Integer}   ignore_duplicate        <code>1- update kể cả trùng,0- gửi lại thông báo nếu trùng</code>
@apiParam	(Body:)			{String}	[_dyn_khoi_nghiep_vu_1670403167230]	Khối nghiệp vụ (Click vào màn hình tạo đơn hàng [KHCN, KHDN, NHBH] thì chọn giá trị tương ứng)
                                                                  <ul>
                                                                    <li><code>Khách hàng Cá nhân</code> : Khách hàng Cá nhân</li>
                                                                    <li><code>Khách hàng Doanh nghiệp</code> : Khách hàng Doanh nghiệp</li>
                                                                    <li><code>Ngân hàng bảo hiểm</code> : Ngân hàng bảo hiểm</li>
                                                                  </ul>

@apiParamExample {json} Body example
{
    "deal_type": "ADD_CUSTOMER",
    "name":"Tên đơn hàng 1",
    "ignore_duplicate":1,
    "sale_process_id":"5d7f150be6481e870a8ce0ad",
    "state_code" : "LEAD",
    "sale_value":  1569000,
    "description": "Mô tả đơn hàng",
    "assignee": [
        {
            "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
            "permission": "owner"
        },
        {
            "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
            "permission": "owner"
        }
    ],
    "estimate_time":"24/12/2022",
    "reason_success":"",
    "reason_fail":"",
    "profiles":[
        "45042df5-2202-4964-b05f-d53e21f5f895",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894"
    ],
    "tickets":[
        "45042df5-2202-4964-b05f-d53e21f5f890",
        "45042df5-2202-4964-b05f-d53e21f5f820"
    ],
    "companies":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "product_categories": [
        "5eafe2b8e2a37eb592100e4b",
        "5eafe2f4ae89793519483d10"
    ]
    type_update:"quick"
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {datetime}          estimate_time           Thời gian chốt đơn hàng
                                                                 <code>Định dạng: "yyyy-mm-dd-Th:m:sZ" </code>
@apiSuccessExample {json} Response success
{
    "code":200,
    "data":{
        "id": "5de61bb97dfdcfe3cbb5d89e",
        "name":"Tên đơn hàng 1",
        "ignore_duplicate":1,
        "assignee": [
            {
                "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                "permission": "owner"
            },
            {
                "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                "permission": "owner"
            }
        ],
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
        "estimate_time":"2022-12-24T00:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "product_categories": [
            "5eafe2b8e2a37eb592100e4b",
            "5eafe2f4ae89793519483d10"
        ],
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ]
    },
    "lang": "vi",
    "message": "request thành công."

}

@apiSuccessExample {json} Response duplicate deal
{
    "data": [
    	{
    		"name":"Trư bát giới",
    		"code":"ZECQQQ271219",
            "assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895","45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	}
    ]
    "code": 413,
    "message": "i18n_message_valid_product_profiles_order_exist"
}
"""

# ----- Danh sách đơn hàng theo IDS ---

"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/list_by_ids  Danh sách đơn hàng bởi ids
@apiGroup Deals
@apiVersion 1.0.1
@apiName DealListByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header_app_code
@apiUse json_header

@apisuccess {Array}             data                          Danh sách đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiParam (Body) {Array} deal_ids Danh sách deal_id
@apiParam (Body) {Array} [fields] Danh sách field cần lấy dữ liệu

@apiParamExample {json} Body example
{
    "deal_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"]
}

@apiSuccess {Integer}   status  Trạng thái đơn hàng (status = 1 'Đang hoạt động', status = 2 'Lưu trữ')


@apiSuccessExample {json} Response
{
    "data": [
        {
            "name":"Tên đơn hàng 1",
            "code": "NGHK8385",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "sale_process_id":"5d7f150be6481e870a8ce0ad",
            "state_code" : "LEAD",
            "sale_value":  1569000,
            "description": "Mô tả đơn hàng",
            "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
            "estimate_time":"2019-11-26T12:00:00Z",
            "status": 1 // với 1 = đang hoạt động, 2 = Lưu trữ, 3 = Đang phân công
        }
    ]
    "code": 200,
    "message": "request thành công."
}

"""

# ------------------ Lấy danh sách các bước bán hàng của deal --------------------
"""
@api {GET} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/sale_steps   Danh sách các bước bán hàng của Deal
@apiGroup Deals
@apiVersion 1.0.2
@apiName ListSaleStepsDeal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header_app_code

@apiParam	(QUERY:)			{string}	    sale_process_id		        Định danh quy trình bán hàng

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Dữ liệu


@apiSuccess {String}            data.state_name               Tên bước bán hàng
@apiSuccess {String}            data.states_code              Mã của bước bán hàng
@apiSuccess {Number}            data.ratio                    Tỷ lệ thành công tương ứng.
                                                              <ul>
                                                                <li><code>0</code>: Thất bại</li>
                                                                <li><code>100</code>: Thành công</li>
                                                              </ul>

@apiSuccess {Object}            [data.result]                       Thông tin kết quả của bước bán hàng.
@apiSuccess {Object}            [data.result.status]                Trạng thái của bước bán hàng.
                                                                    <ul>
                                                                        <li><code>0</code>: Thất bại</li>
                                                                        <li><code>1</code>: Thành công</li>
                                                                    </ul>
@apiSuccess {Object}            [data.result.reason]                       Lý do


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "name": "Thất bại",
            "ratio": 0,
            "state_code": "47de7430-67dc-4bc4-935c-eb4d5199d072"
        },
        {
            "name": "Có thông tin Leads đơn hàng của",
            "ratio": 10,
            "state_code": "56f27e81-3f10-49b1-b0ec-1faab0715282"
        },
        {
            "name": "Liên lạc",
            "ratio": 20,
            "state_code": "bcaa317c-165e-4863-89d1-46e83325aa53",
            "result": {
                "status": 0,
                "reason": "Buồn nên không mua."
            }
        },
        {
            "name": "Giới thông tin, báo giá",
            "ratio": 30,
            "state_code": "391fc012-19c5-4708-9bd5-e8e922c95832"
        },
        {
            "name": "Thuyết phục",
            "ratio": 40,
            "state_code": "0dcefe09-3b37-457d-a255-4817a668ec75"
        },
        {
            "name": "Thành công",
            "ratio": 100,
            "state_code": "7270bac1-c9a1-470b-a2db-0878f9b7a381"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

#############################################################################
# Chuyển trạng thái đơn hàng
# version: 1.0.2
#############################################################################

"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/action/change-state  Chuyển trạng thái nhiều đơn hàng
@apiGroup Deals
@apiVersion 1.0.1
@apiName DealsChangeState

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header_app_code

@apiParam	(BODY:)			{Array}	    deals_info			                Danh sách đơn hàng cần được phân công
@apiParam	(BODY:)			{String}	deals_info.deal_id                  ID đơn hàng		        
@apiParam	(BODY:)			{String}	deals_info.sale_process_id          ID quy trình bán hàng có mã trạng thái cần chuyển đến     
@apiParam	(BODY:)			{String}	deals_info.state_code               Mã trạng thái	        
@apiParam	(BODY:)			{String}	[deals_info.info_change_state]      Danh sách field trong cấu hình chuyển bước        
@apiParam	(BODY:)			{String}	deal_id.info_change_state.name                    
@apiParam	(BODY:)			{String}	deal_id.info_change_state.sale_value                    
@apiParam	(BODY:)			{String}	deal_id.info_change_state.reason_fail                    
@apiParamExample {json} Body example
{
    "deals_info": [
        {
            "deal_id": "610cab22d1cac521b4ed19d4",
            "sale_process_id": "60fe8f5a3601f65b0a3da235",
            "state_code": "3be8bbb8-f3ad-4244-8a4d-7cda308bcbe9",
            "info_change_state": {
                "name": "ĐƠn hàng 1",
                "sale_value": 10000,
                "reason_fail": "Lý do thất bại"
            }
        }
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apisuccess {Array}             data                          Thống kê số lượng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
}
"""

####################################################################################################
# Xóa danh sách CHB
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/action/delete  Xóa danh sách CHB
@apiGroup Deals
@apiVersion 1.0.1
@apiName DealsActionDelete

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(BODY:)			{Array}	    deal_ids	      Danh sách ID đơn hàng (Tối đa 100 phần tử)

@apiParamExample {json} Body example
{
    "deal_ids": [
        "654c7bd9d0c005cc50d61970"
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}           data_delete                     Danh sách deal_ids xóa thành công

@apiSuccessExample {json} Response
{
    "code": 200,
    "data_delete" : [
        "654c7bd9d0c005cc50d61970"
    ],
    "message": "request thành công."
}
"""

# ============== Nguồn ghi nhận đơn hàng ================

"""
@api {GET} {domain}/sale/mobile/api/v1.0/deal/source   Nguồn ghi nhận đơn hàng
@apiGroup Deals
@apiVersion 1.0.2
@apiName DealSource

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apisuccess {Array}             data                          Danh sách nguồn ghi nhận đơn hàng
@apisuccess {String}            data.name                     Tên nguồn
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "name": "Nhập thủ công"
        },
        {
            "name": "Tạo tự động"
        },
        {
            "name": "Nhập từ file"
        },
        {
            "name": "Nhập từ file a"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}

"""

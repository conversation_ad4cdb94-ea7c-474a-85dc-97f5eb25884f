######################### List Log Dynamic Event theo Tracking code  ###########################
# version: 1.0.0
#################################################################
"""
@api {get} /api/v1.0/profile/event-logs [GET] Danh sách Log Dynamic Event theo danh sách Tracking code
@apiDescription Danh sách Log Dynamic Event theo danh sách Tracking code
@apiGroup DynamicEvent
@apiVersion 1.0.0
@apiName LogDynamicEventList

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiParam      (Query:)             {String}    tracking_codes    Code đối soát, cách nhau bởi dấu <code>,</code>; VD: <code>tracking_codes=code1,code2</code>
@apiUse lang
@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {Array[log_event_object]}      data                   Danh sách log dynamic event

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [{
        "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "profile_id" : "2fe497ae-e8f8-44c8-8049-fe5cbc748396",
        "event_key" : "thuy_thu_bai_bai",
        "tracking_code" : "booking",
        "event_data" : {
            "booking_code": "A1777",
            "location": "West Kellyland",
            "action_time": 1626817630.84592
        },
        "created_time" : "2021-09-07T03:20:01Z",
        "updated_time" : "2021-09-07T03:20:01Z"
    }],
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccess   (log_event_object:)    {String}     merchant_id           Merchant ID
@apiSuccess   (log_event_object:)    {String}     profile_id            Profile ID
@apiSuccess   (log_event_object:)    {String}     event_key             Event key
@apiSuccess   (log_event_object:)    {String}     tracking_code         Code đối soát
@apiSuccess   (log_event_object:)    {Object[event_data_object]}        event_data            Event data
@apiSuccess   (log_event_object:)    {String}     created_time          Thời gian tạo Event. Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ (UTC)</code>. <br/>Example: <code>2021-09-07T03:20:01Z</code>
@apiSuccess   (log_event_object:)    {String}     updated_time          Thời gian cập nhật Event. Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ (UTC)</code>. <br/>Example: <code>2021-09-07T03:20:01Z</code>


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""

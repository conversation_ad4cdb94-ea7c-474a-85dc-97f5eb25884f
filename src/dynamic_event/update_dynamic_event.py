"""
@api {PUT} /api/v1.0/update    C<PERSON><PERSON> nhật dynamic event
@apiGroup DynamicEvent
@apiVersion 1.0.0
@apiName UpdateDynamicEvent
@apiDescription API cập nhật dynamic event. </br>

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse dyn_event_response

@apiParam   (Body:)         {Object}         data                     Dữ liệu của event được cập nhật
@apiParam   (Body:)         {String}         data.event_key           Key định danh của event
@apiParam   (Body:)         {Array}          data.fields              Danh sách các field của event
@apiParam   (Body:)         {Integer}        data.status              Trạng thái của event. 1: active, 0: inactive
@apiParam   (Body:)         {String}         data.name                Tên của event
@apiParam   (Body:)         {String}         data.line_event          Event hiển thị trên cột nào. Nhận 2 giá trị 'merchant' và 'object'
@apiParam   (Body:)         {String}         data.fields.field_key    Key định danh của field
@apiParam   (Body:)         {String}         data.fields.name         Tên hiểu thị của field

@apiParamExample [json] Body update dynamic event:
{
  "data": {
    "event_key": "tuy_bien_vent_01_1739439152",
    "line_event": "merchant",
    "fields": [
        {"field_key": "action_time", "name": "Thời gian phát sinh"},
        {"field_key": "string", "name": "Field 123"},
    ],
    "status": 1,
    "name": "tuy biến vênt 010"
  }
}

@apisuccess       {Object{dyn_event_response}}                      data                                              Detail dynamic event
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {dyn_event_object}, 
  "lang": "vi", 
  "message": "Request thành công"
}
"""

"""
@api {GET} /api/v1.0/events/check_exists [GET] Kiểm tra event đã tồn tại chưa
@apiGroup DynamicEvent
@apiVersion 1.0.0
@apiName CheckEventExist
@apiDescription API kiểm tra event đã tồn tại chưa.

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)    {String}         event_keys      Event Key; limit 100 event_key; example: <code>event_keys=de_a,de_b</code>

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "event_key": "de_a",
            "exist": true
        },
        {
            "event_key": "de_b",
            "exist": false
        }
    ]
}
"""
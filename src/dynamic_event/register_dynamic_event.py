# Define dyn_event respone
"""
@apiDefine dyn_event_response
@apiParam   (dyn_event_object:)       {String}                        merchant_id                     ID merchant
@apiParam   (dyn_event_object:)       {String}                        name                            tên dynamic event
@apiParam   (dyn_event_object:)       {Integer}                       fields                          Danh sách các trường thông tin của event
@apiParam   (dyn_event_object:)       {String}                        filter_setting                  <ul>
                                                                                                        <li> 0:  nếu tổng số trường thông tin từng field đều >= 6 </code></li>
                                                                                                        <li>1:  nếu tổng số trường thông tin bất kỳ < 6</li>
                                                                                                      </ul>
@apiParam   (dyn_event_object:)       {String}                        version                         <ul>
                                                                                                        <li> 0:  nếu tổng số trường thông tin từng field đều >= 6 </code></li>
                                                                                                        <li>1:  nếu tổng số trường thông tin bất kỳ < 6</li>
                                                                                                      </ul>
@apiParam   (dyn_event_object:)       {String}                        status                          Trạng thái dynamic event <ul>
                                                                                                        <li> 0: INACTIVE </code></li>
                                                                                                        <li> 1: ACTIVE</li>
                                                                                                        <li> 2: WAITING_INIT_EVENT</li>
                                                                                                        <li> 3: INIT_EVENT_ERROR</li>
                                                                                                      </ul>
@apiParam   (dyn_event_object:)       {Integer}                       event_key                   key định danh của event
@apiParam   (dyn_event_object:)	 	    {Datetime}                      created_time	                  Thời gian bắt đầu ghi nhận thông tin event <code>Định dạng: yyyy-MM-ddTHH:mm:ss.sZ </code>
@apiParam   (dyn_event_object:)	 	    {Datetime}                      updated_time	                  Thời gian cập nhật thông tin event <code>Định dạng: yyyy-MM-ddTHH:mm:ss.sZ </code>
@apiParam   (dyn_event_object:)       {Object}                        last_user_update                Thông tin người cập nhật cuối cùng
@apiParam   (dyn_event_object:)       {String}                        last_user_update.staff_id                        ID của nhân viên
@apiParam   (dyn_event_object:)       {String}                        last_user_update.fullname                        Tên đầy đủ của nhân viên
@apiParam   (dyn_event_object:)       {String}                        last_user_update.username                        Tên đăng nhập của nhân viên
@apiParam   (dyn_event_object:)       {Datetime}                      last_user_update.created_time                    Thời gian cập nhật <code>Định dạng: yyyy-MM-ddTHH:mm:ss.sZ </code>

@apiSuccessExample {json} dyn_event_object:
{
  "name": "Tên của Dynamic Event",
  "status": 2,
  "event_key": "ten_cua_dynamic_event_1716334861",
  "fields": [
      {
          "field_name": "Thời gian phát sinh Event",
          "field_key": "action_time",
          "field_property": "date",
          "position": 0,
          "require": true,
          "sync_require": true,
          "field_suggest": false,
          "group": "dynamic",
          "is_base": false
      },
      {
          "field_name": "Địa điểm giao dịch",
          "field_key": "location",
          "field_property": "string",
          "position": 1,
          "require": true,
          "sync_require": true,
          "field_suggest": true,
          "group": "dynamic",
          "is_base": false
      }
  ],
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "filter_setting": 0,
  "version": 1,
  "only_action_time_required": false,
  "created_time": "2024-05-22T06:41:10.053441Z",
  "updated_time": "2024-05-22T06:41:10.053447Z",
  "last_user_update": {
    "staff_id": "2d6a856e-51d0-41cb-ac6c-e1a3c805471e",
    "fullname": "EIB",
    "username": "admin@eibtest",
    "created_time": "2025-04-24T10:19:49.560Z"
  },
  "curl": "curl -X POST https://t1.mobio.vn/digienty/partner/api/v1.0/sync_events/test_event_giao_dich_2_1716334861 -H 'x-merchant-id: 1b99bdcf-d582-4f49-9715-1b61dfff3924' -H 'content-type: application/json' -H 'authorization: authorization' -d '{\"data\": [{\"profile_info\": \"profile_info\", \"event_data\": {}}]}'"
}
"""

"""
@api {GET} /api/v1.0/register    Đăng ký dynamic event
@apiGroup DynamicEvent
@apiVersion 1.0.0
@apiName RegisterDynamicEvent
@apiDescription API đăng ký dynamic event. </br>
Note: <b>mỗi event luôn luôn bắt buộc phải có event_field là action_time đại diện cho thời gian phát sinh event.</b>

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse dyn_event_response


@apiParam   (Body:)    {String}         name        Tên của event
@apiParam   (Body:)    {String}         line_event        Event hiển thị trên cột nào. Nhận 2 giá trị 'merchant' và 'object'
@apiParam   (Body:)    {Array}         fields        Danh sách các trường thông tin của event

@apiParam   (field:)    {String}         field_name        Tên của trường thông tin.
@apiParam   (field:)    {String}         field_key         Key định danh của trường thông tin.
@apiParam   (field:)    {String}         field_property    Kiểu dữ liệu của trường thông tin.
<li><code>int: Kiểu số nguyên</code></li>
<li><code>float: Kiểu số thực</code></li>
<li><code>string: Kiểu chữ</code></li>
<li><code>date: Kiểu thời gian</code></li>
<li><code>bool: Kiểu đúng (true) và sai (false)</code></li>
@apiParam   (field:)    {Int}         [position]    Vị trí hiển thị trên giao diện.
@apiParam   (field:)    {Bool}         [require]    Bắt buộc phải nhập khi chạy "phát sinh event".
@apiParam   (field:)    {Bool}         sync_require    Bắt buộc phải có data khi đồng bộ event.
@apiParam   (field:)    {Bool}         [field_suggest]    có field suggest hay không.


@apiParamExample [json] Body register dynamic event:
{
  "data": {
    "name": "Tên của Dynamic Event",
    "line_event": "merchant",
    "fields": [
      {
        "field_name": "Thời gian phát sinh Event",
        "field_key": "action_time",
        "field_property": "date",
        "position": 0,
        "require": true
        "sync_require": true
      },
      {
        "field_name": "Địa điểm giao dịch",
        "field_key": "location",
        "field_property": "string",
        "position": 1,
        "require": true
        "sync_require": true
      },
      {
        "field_name": "Số tiền giao dịch",
        "field_key": "transaction_amount",
        "field_property": "float",
        "position": 2,
        "require": true
        "sync_require": true
      }
    ]
  }
}


@apisuccess       {Object{dyn_event_response}}                      data                                              Detail dynamic event
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {dyn_event_object}, 
  "lang": "vi", 
  "message": "Đăng ký thành công!"
}

"""
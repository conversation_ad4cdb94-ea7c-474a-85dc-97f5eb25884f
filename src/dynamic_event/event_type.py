# ---------- <PERSON><PERSON> sách event type -----------
"""
@api {GET} /api/v1.0/list_event    L<PERSON><PERSON> danh sách event type
@apiGroup EventType
@apiVersion 1.0.0
@apiName ListEventType

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (param:)  {String}  [per_page]  Số lượng bản tin trên 1 trang; <code>Default per_page=-1</code> l<PERSON><PERSON> t<PERSON>t cả
@apiParam   (param:)  {String}  [after_token]  after_token <PERSON><PERSON><PERSON> trang tiếp theo
@apiParam   (param:)  {String}  [search]  Tìm kiếm theo tên event


@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "_id": "61601a2acb97585cd950d847",
            "name": "<PERSON>ê<PERSON> luôn",
            "status": 1,
            "event_key": "len_luon",
            "fields": [
                {
                    "field_name": "Thời gian phát sinh Event",
                    "field_property": "date",
                    "field_key": "action_time",
                    "position": 0,
                    "require": true,
                    "group": "dynamic",
                    "is_base": false
                }
            ],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "is_delete": 0,
            "created_time": "2021-10-08T10:15:06.902000Z",
            "updated_time": "2021-10-08T10:15:06.902000Z",
            "last_user_update": {
                "staff_id": "2d6a856e-51d0-41cb-ac6c-e1a3c805471e",
                "fullname": "EIB",
                "username": "admin@eibtest",
                "created_time": "2025-04-24T10:19:49.560Z"
            }
        }
    ],
    "paging": {
        "cursors": {
            "after": "MjAyMS0xMC0wN1QwNzoyOToyNS4yNDYwMDBa",
            "before": ""
        }
        "per_page": 10,
        "total_items": 100
    }
}
"""
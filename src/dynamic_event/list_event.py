######################### List Dynamic Event theo Profile  ###########################
# version: 1.0.0
#################################################################
"""
@api {get} /api/v1.0/events [GET] Danh sách Dynamic Event
@apiDescription Danh sách Dynamic Event
@apiGroup DynamicEvent
@apiVersion 1.0.0
@apiName DynamicEventV1

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiParam      (Query:)             {String}    [search]           Từ khóa tìm kiếm
@apiParam      (Query:)             {Boolean}   [only_action_time_required]           true: trả về các dynamic event chỉ require action_time khi đồng bộ dữ liệu
@apiParam      (Query:)             {Number}    [per_page]         Số lượng item trên page. <br/>Default: <code>&per_page=10</code>
@apiParam      (Query:)             {Number}    [after_token]      Token cho page tiếp theo.
@apiParam      (Query:)             {Boolean}   [all_brand]        <code>true</code>: get event all merchant;
@apiParam      (Query:)             {String}    [merchant_ids]     Danh sách merchant_ids muốn lấy event, cách nhau bới dấu <code>,</code>; trường hợp không truyền sẽ lấy event theo X-Merchant-ID
@apiParam      (Query:)             {Number}    [status]           Trạng thái <ul>
                                                                                    <li><code> 0: INACTIVE </code></li>
                                                                                    <li><code> 1: ACTIVE </code></li>
                                                                                    <li><code> 2: WAITING_INIT_EVENT </code></li>
                                                                                    <li><code> 3: INIT_EVENT_ERROR </code></li>
                                                                                </ul>
@apiParam      (Query:)             {String}    [event_type]       Loại event, giá trị <code>base_event</code> hoặc <code>dynamic_event</code>: example: status=<code>base_event,dynamic_event</code>; Default event_type=dynamic_event
                                                                   <br>Lưu ý: List base_event sẽ nằm bên trên và không sort hoặc tìm kiếm được base_event
@apiParam      (Query:)             {Number}    [filter_setting]   Biến nay để xác định dynamic event đã được cấu hình Bộ lọc Profiles hay chưa;
                                                                   <br><code>1</code>: Đã cấu hình; <code>0</code>: Chưa cấu hình; Nếu không truyền thì mặc định lấy hết

@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {Array[event_object]}          data                   Danh sách dynamic event
@apiSuccess                 {Object[paging]}               paging                 Thông tin phân trang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "Success!",
    "data": [
        {
            "_id": "_base_e34",
            "name": "Giao dịch",
            "status": 1,
            "event_key": "_base_e34",
            "fields": [
                {
                    "field_name": "Thời gian giao dịch",
                    "field_key": "action_time",
                    "field_property": "datetime",
                    "display_type": "single_line"
                },
                {
                    "field_name": "Kênh mua hàng",
                    "field_key": "source",
                    "field_property": "string",
                    "display_type": "single_line"
                },
                {
                    "field_name": "Tổng giá trị",
                    "field_key": "transaction_amount",
                    "field_property": "double",
                    "display_type": "single_line"
                },
                {
                    "field_name": "Nơi giao dịch",
                    "field_key": "store",
                    "field_property": "string",
                    "display_type": "single_line"
                },
                {
                    "field_name": "Mã giao dịch",
                    "field_key": "transaction_code",
                    "field_property": "string",
                    "display_type": "single_line"
                },
                {
                    "field_name": "Điểm tiêu dùng",
                    "field_key": "point",
                    "field_property": "double",
                    "display_type": "single_line"
                },
                {
                    "field_name": "Điểm xét hạng",
                    "field_key": "rank_point",
                    "field_property": "double",
                    "display_type": "single_line"
                }
            ],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "version": 1,
            "event_type": "base_event",
            "has_product": true
        },
        {
            "_id": "_base_6359f69a0a921ba653fdb114",
            "name": "Thay đổi trạng thái tài khoản/thẻ",
            "status": 1,
            "event_key": "_base_6359f69a0a921ba653fdb114",
            "fields": [
                {
                    "field_name": "Thời gian phát sinh Event",
                    "field_key": "action_time",
                    "field_property": "date"
                }
            ],
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "version": 1,
            "event_type": "base_event"
        },
        {
            "_id": "61ef5f6dd868643c1219cd64",
            "name": "Popup Open",
            "status": 1,
            "event_key": "popup_open",
            "fields": [
                {
                    "field_name": "Thời gian phát sinh Event",
                    "field_key": "action_time",
                    "field_property": "date",
                    "sync_require": true,
                    "group": "dynamic",
                    "is_base": false,
                    "position": 0,
                    "position_filter": 0,
                    "require": true,
                    "require_filter": true
                }
            ],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "filter_setting": 1,
            "version": 1,
            "is_delete": 0,
            "created_time": "2022-01-25T02:24:45.509000Z",
            "updated_time": "2022-03-01T07:21:39.439000Z",
            "only_action_time_required": true,
            "event_type": "dynamic_event"
        }
    ],
    "paging": {
        "cursors": {
            "after": "MjAyMi0wMS0yNVQwMjoyNDo0NS41MDkwMDBa",
            "before": null
        },
        "per_page": 10,
        "total_items": 45
    }
}

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""


######################### Get detail event by event_ids  ###
# version: 1.0.0
#################################################################
"""
@api {get} /api/v1.0/events/detail_by_id [GET] Lấy chi tiết Dynamic Event bằng event_ids
@apiDescription Lấy Dynamic Event theo event_ids
@apiGroup DynamicEvent
@apiVersion 1.0.0
@apiName DynamicEventDetailByIDS

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiParam      (Query:)             {String}    [event_ids]           event_id nối với nhau bằng dấu <code>,</code>: VD: <code>event_ids=event_id_1,_base_event_id_2</code>

@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {Array[event_object]}          data                   Danh sách dynamic event

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "Success!",
    "data": [
        {
            "_id": "61ef5f6dd868643c1219cd64",
            "name": "Popup Open",
            "status": 1,
            "event_key": "popup_open",
            "fields": [
                {
                    "field_name": "Thời gian phát sinh Event",
                    "field_key": "action_time",
                    "field_property": "date",
                    "sync_require": true,
                    "group": "dynamic",
                    "is_base": false,
                    "position": 0,
                    "position_filter": 0,
                    "require": true,
                    "require_filter": true
                }
            ],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "filter_setting": 1,
            "version": 1,
            "is_delete": 0,
            "created_time": "2022-01-25T02:24:45.509000Z",
            "updated_time": "2022-03-01T07:21:39.439000Z",
            "only_action_time_required": true,
            "event_type": "dynamic_event"
        }
    ]
}

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""

######################### Get detail event by event_key  ###
# version: 1.0.0
#################################################################
"""
@api {get} /api/v1.0/events/detail_by_key [GET] Lấy chi tiết Dynamic Event bằng event_key
@apiDescription Lấy Dynamic Event theo event_key
@apiGroup DynamicEvent
@apiVersion 1.0.0
@apiName DynamicEventDetailByKey

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiParam      (Query:)             {String}    [event_key]           event_key nối với nhau bằng dấu <code>,</code>: VD: <code>event_key=event_key_1,_base_event_key_2</code>

@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {Array[event_object]}          data                   Danh sách dynamic event

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "Success!",
    "data": [
        {
            "_id": "61ef5f6dd868643c1219cd64",
            "name": "Popup Open",
            "status": 1,
            "event_key": "popup_open",
            "fields": [
                {
                    "field_name": "Thời gian phát sinh Event",
                    "field_key": "action_time",
                    "field_property": "date",
                    "sync_require": true,
                    "group": "dynamic",
                    "is_base": false,
                    "position": 0,
                    "position_filter": 0,
                    "require": true,
                    "require_filter": true
                }
            ],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "filter_setting": 1,
            "version": 1,
            "is_delete": 0,
            "created_time": "2022-01-25T02:24:45.509000Z",
            "updated_time": "2022-03-01T07:21:39.439000Z",
            "only_action_time_required": true,
            "event_type": "dynamic_event"
        }
    ]
}

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""

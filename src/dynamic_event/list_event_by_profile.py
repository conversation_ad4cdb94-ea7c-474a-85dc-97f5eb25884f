######################### List Dynamic Event theo Profile  ###########################
# version: 1.0.0
#################################################################
"""
@api {get} /api/v1.0/profile/filter/{profile_id}/events [GET] Danh sách Dynamic Event theo Profile
@apiDescription Danh sách Dynamic Event theo Profile
@apiGroup DynamicEvent
@apiVersion 1.0.0
@apiName DynamicEventList

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiParam      (Path:)              {String}    profile_id         Profile ID cần lọc
@apiParam      (Query:)             {String}    [merchant_ids]     Mảng merchant được phân cách nhau bởi dấu phẩy (<code>,</code>). <br/>Example: <code>merchant_1,merchant_2</code>
@apiParam      (Query:)             {String}    [event_ids]       Mảng event_id được phân cách nhau bởi dấu phẩy (<code>,</code>). <br/>Example: <code>event_key_1,event_key_2</code>
@apiUse lang
@apiParam      (Query:)             {String}    [from_date]        Từ ngày (ngày tạo). Định dạng:<code>yyyy-MM-ddTHH:mm:ssZ (UTC)</code>. <br/>Example: <code>2021-09-07T03:20:01Z</code>
@apiParam      (Query:)             {String}    [to_date]          Đến ngày (ngày tạo). Định dạng:<code>yyyy-MM-ddTHH:mm:ssZ (UTC)</code>. <br/>Example: <code>2021-09-07T03:20:01Z</code>
@apiParam      (Query:)             {Number}    [per_page]         Số lượng item trên page. <br/>Example: <code>&per_page=5</code>
@apiParam      (Query:)             {Number}    [after_token]      Token cho page tiếp theo.

@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {Array[event_object]}          data                   Danh sách dynamic event
@apiSuccess                 {Object[paging]}               paging                 Thông tin phân trang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [event_object],
    "paging": {paging},
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccess   (event_object:)    {String}     merchant_id           Merchant ID
@apiSuccess   (event_object:)    {String}     profile_id            Profile ID
@apiSuccess   (event_object:)    {String}     event_key             Event key
@apiSuccess   (event_object:)    {String}     event_id              Event ID
@apiSuccess   (event_object:)    {String}     [name]                Tên event
@apiSuccess   (event_object:)    {Object}     [fields]              Cấu trúc event
@apiSuccess   (event_object:)    {Object[event_data_object]}        event_data            Event data
@apiSuccess   (event_object:)    {String}     created_time          Thời gian tạo Event. Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ (UTC)</code>. <br/>Example: <code>2021-09-07T03:20:01Z</code>
@apiSuccess   (event_object:)    {String}     updated_time          Thời gian cập nhật Event. Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ (UTC)</code>. <br/>Example: <code>2021-09-07T03:20:01Z</code>

@apiSuccessExample {json} event_object
{
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "profile_id" : "2fe497ae-e8f8-44c8-8049-fe5cbc748396",
    "event_key" : "thuy_thu_bai_bai",
    "event_id" : "6167f0bd0eecc4e1171003e6",
    "event_data" : {
        "booking_code": "A1777",
        "location": "West Kellyland",
        "action_time": 1626817630.84592
    },
    "name": "Thay đổi hạn mức đề xuất",
    "fields": [
        {
            "field_name": "Code booking",
            "field_property": "string",
            "field_key": "booking_code",
            "position": 0,
            "require": true,
            "group": "dynamic",
            "is_base": false
        },
        {
            "field_name": "Vị trí",
            "field_property": "string",
            "field_key": "location",
            "position": 1,
            "require": true,
            "group": "dynamic",
            "is_base": false
        },
        {
            "field_name": "Thời gian phát sinh",
            "field_property": "string",
            "field_key": "action_time",
            "position": 2,
            "require": true,
            "group": "dynamic",
            "is_base": false
        }
    ],
    "created_time" : "2021-09-07T03:20:01Z",
    "updated_time" : "2021-09-07T03:20:01Z"
}

@apiSuccess   (event_data_object:)    {Number}     action_time                    Thời gian phát sinh event
@apiSuccess   (event_data_object:)    {String}     truong_thong_tin_1             Trường thông tin 1. Tên trường có thể dynamic
@apiSuccess   (event_data_object:)    {String}     [truong_thong_tin_n]           Trường thông tin n. Tên trường có thể dynamic


@apiSuccess     (paging:)          {Object}    [cursors]                   Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     (paging:)          {String}    [cursors..after]            Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     (paging:)          {String}    [cursors..before]           Token để lấy dữ liệu trang trước đó.
@apiSuccess     (paging:)          {Number}    [per_page]                  Số lượng yêu cầu trên 1 page.
@apiSuccess     (paging:)          {Number}    [total_items]               Tổng số lượng

@apiSuccessExample {json} paging
{
    "cursors": {
        "after": "eydhY3Rpb25fdGltZSc6IDE2MjYwNTA2MTkuMjE1MTI3LCAnaGFzaCc6ICcwMzkyOWQ2YjRlZmY0MDE4OTI0NTcwOWY4ODViNmI4NSd9",
        "before": null
    },
    "per_page": 20,
    "total_items": 4
}

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""

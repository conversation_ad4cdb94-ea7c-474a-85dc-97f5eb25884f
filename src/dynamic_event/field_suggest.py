****************************  Danh sách dữ liệu gợi ý theo field của Event  ************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/field/suggest Danh sách dữ liệu gợi ý theo field của Event
@apiDescription Danh sách dữ liệu gợi ý theo field của Event
@apiGroup Field Suggest
@apiVersion 1.0.0
@apiName FieldSuggest

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} [search]  Chuỗi tìm kiếm
@apiParam (Query:) {Integer} [per_page] Số bản tin trên 1 trang; <code>default: 20</code>; <code>per_page phải là số nguyên dương</code>
<code>Đối với bản web v3 max per_page: 20000; web v4 max per_page: 2000 (BA Ngân)</code>
@apiParam (Query:) {String} [after_token] token để lấy dữ liệu trang tiếp theo;
@apiParam (Query:) {String} event_id event_id
@apiParam (Query:) {String} field_key field_key của event

@apiSuccess {Array}   data    Danh sách
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	created_time		Thời gian tạo
@apiSuccess (data) {String} 	value				Giá trị: <code>VD: ECOM, PAYMENT</code>

@apiSuccessExample {json} Response example
{
  "data": [
        {
            "value": "PAYMENT",
            "created_time" : "2021-07-20T07:54:07.424Z"
        }
  ],
  "paging": {
        "cursors": {
            "after": "MjAyMS0xMC0wN1QwNzoyOToyNS4yNDYwMDBa",
            "before": ""
        }
        "per_page": 20,
        "total_items": 100
  },
  "code": 200,
  "message": "request thành công"
}
"""

"""
@api {PUT} /api/v1.0/refresh/{event_key}    Refresh dynamic event
@apiGroup DynamicEvent
@apiVersion 1.0.0
@apiName RefreshDynamicEvent
@apiDescription API Refresh dynamic event. </br>

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse dyn_event_response

@apisuccess       {Object{dyn_event_response}}                      data                                              Detail dynamic event
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {dyn_event_object}, 
  "lang": "vi", 
  "message": "Refresh Success"
}

"""

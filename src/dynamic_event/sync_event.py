"""
@api {GET} /api/v1.0/sync    Đồng bộ event
@apiGroup DynamicEvent
@apiVersion 1.0.0
@apiName SyncDynamicEvent
@apiDescription API đồng bộ dữ liệu event. </br>
NOTE:  <b>Dữ liệu profile_info bắt buộc phải có source và có primary_phone, primary_email, customer_id tương ứng</b>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)    {Object}         event_data        Dữ liệu của event được đồng bộ, tùy theo từng event đã đăng ký mà các trường thông tin bên trong sẽ thay đổi tương ứng.
@apiParam   (Body:)    {Object}         profile_info      Thông tin khách hàng tương ứng với event
@apiParam   (Body:)    {String}         event_key         Key định danh của event

@apiParam   (profile_info:)    {String}         source        Nguồn dữ liệu, nguồn phát sinh event.
@apiParam   (profile_info:)    {String}         name          Tên khách hàng.
@apiParam   (profile_info:)    {String}         [primary_phone]    Số điện thoại của khách hàng.
@apiParam   (profile_info:)    {String}         [primary_email]    Email của khách hàng.
@apiParam   (profile_info:)    {String}         [customer_id]      ID của khách hàng.  

@apiParamExample [json] Body register dynamic event:
{
  "data": {
    "event_data": {
        
    },
    "event_key": "test_event_giao_dich",
    "profile_info": {
        "source": "POS",
        "name": "Nguyen Van A",
        "primary_phone": "0987654321",
        "primary_email": "<EMAIL>",
        "customer_id": "cust1"
    }
  }
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "message": "Success"
}
"""
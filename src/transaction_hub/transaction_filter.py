#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: ThongNV
    Company: M O B I O
    Date Created: 4/11/22
"""

"""
@api {get} {host}/transaction/api/v1.0/transactions/profile  Danh sách giao dịch
@apiDescription Danh sách giao dịch của profile theo customer_id

@apiVersion 1.0.2
@apiGroup TransactionFilter
@apiName TransactionFilterProfile

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam   (Query:)    {String}    customer_id   ID profile trên hệ thống tenant


@apiSuccess {String}    message     Nội dung phản hồi
@apiSuccess {Array}     data        Thông tin danh sách giao dịch
@apiSuccess {Integer}   code        Mã phản hồi

@apiSuccess     (data)   {String}   action_type       Loại hành động giao dịch:
                                                        <ul>
                                                            <li><code>buy</code> : Mua hàng</li>
                                                            <li><code>returns</code> : Trả hàng</li>
                                                            <li><code>redeem_voucher</code> : Đổi voucher</li>
                                                        </ul>
@apiSuccess     (data)   {Number}   gift_point          Số điểm được tặng khi thanh toán giao dịch
@apiSuccess     (data)   {Number}   payment_point       Số điểm được dùng khi thanh toán giao dịch
@apiSuccess     (data)   {Number}   amount              Tổng số tiền giao dịch.
@apiSuccess     (data)   {String}  currency_code        Đơn vị tiền tệ.
                                                        <ul>
                                                            <li><code>VND</code>: Việt Nam Đồng</li>
                                                            <li><code>USD</code>: Đô la mỹ</li>
                                                        </ul>
@apiSuccess     (data)   {String}   code                Mã giao dịch
@apiSuccess     (data)   {Array}    vouchers            Danh sách mã voucher được sử dụng trong giao dịch
@apiSuccess     (data)   {Object}   store               Thông tin cửa hàng
@apiSuccess     (data)   {Object}   [store]             Thông tin cửa hàng thực hiện giao dịch.
@apiSuccess     (data)   {String}   store.code          Mã cửa hàng.
@apiSuccess     (data)   {String}   store.name          Tên cửa hàng.
@apiSuccess     (data)   {String}   [store.address]     Địa chỉ cửa hàng
@apiSuccess     (data)   {Number}   action_time         Thời gian giao dịch. (Timestamps, lấy đến <b>miliseconds</b>. VD: 1649415177352)                                                        

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "action_type": "buy",
            "gift_point": 10,
            "amount": 100000,
            "code": "07e8c3db-a3bf",
            "action_time": 1649739213572,
            "currency_code": "VND",
            "vouchers": ["AIOTAKU"],
            "store": {
              "code": "CH001",
              "name": "Guardian Hà Đông",
              "address": "Hà Đông, Hà Nội"
            }
        }
    ]
}
"""

# --------------- Danh sách giao dịch theo token -----

"""
@api {get} {host}/transaction/api/v1.0/transactions/profile/list  Danh sách giao dịch của profile
@apiDescription Danh sách giao dịch của profile lấy theo token

@apiVersion 1.0.2
@apiGroup TransactionFilter
@apiName TransactionFilterProfileList

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging


@apiSuccess {String}    message     Nội dung phản hồi
@apiSuccess {Array}     data        Thông tin danh sách giao dịch
@apiSuccess {Integer}   code        Mã phản hồi

@apiSuccess     (data)   {String}   action_type       Loại hành động giao dịch:
                                                        <ul>
                                                            <li><code>buy</code> : Mua hàng</li>
                                                            <li><code>returns</code> : Trả hàng</li>
                                                            <li><code>redeem_voucher</code> : Đổi voucher</li>
                                                        </ul>
@apiSuccess     (data)   {Number}   gift_point          Số điểm được tặng khi thanh toán giao dịch
@apiSuccess     (data)   {Number}   payment_point       Số điểm được dùng khi thanh toán giao dịch
@apiSuccess     (data)   {Number}   amount              Tổng số tiền giao dịch.
@apiSuccess     (data)   {String}  currency_code        Đơn vị tiền tệ.
                                                        <ul>
                                                            <li><code>VND</code>: Việt Nam Đồng</li>
                                                            <li><code>USD</code>: Đô la mỹ</li>
                                                        </ul>
@apiSuccess     (data)   {String}   code                Mã giao dịch
@apiSuccess     (data)   {Array}    vouchers            Danh sách mã voucher được sử dụng trong giao dịch
@apiSuccess     (data)   {Object}   store               Thông tin cửa hàng
@apiSuccess     (data)   {Object}   [store]             Thông tin cửa hàng thực hiện giao dịch.
@apiSuccess     (data)   {String}   store.code          Mã cửa hàng.
@apiSuccess     (data)   {String}   store.name          Tên cửa hàng.
@apiSuccess     (data)   {String}   [store.address]     Địa chỉ cửa hàng
@apiSuccess     (data)   {Number}   action_time         Thời gian giao dịch. (Timestamps, lấy đến <b>miliseconds</b>. VD: 1649415177352)                                                        

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "action_type": "buy",
            "gift_point": 10,
            "amount": 100000,
            "code": "07e8c3db-a3bf",
            "action_time": 1649739213572,
            "currency_code": "VND",
            "vouchers": ["AIOTAKU"],
            "store": {
              "code": "CH001",
              "name": "Guardian Hà Đông",
              "address": "Hà Đông, Hà Nội"
            }
        }
    ]
}
"""

# -------------- Danh sách giao dịch theo nhân viên -----------------
# Version 1.0.0

"""
@api {post} {host}/transaction/external/api/v1.0/transactions/staff Danh sách giao dịch theo nhân viên
@apiDescription Danh sách giao dịch theo nhân viên, support app nutifood
@apiName TransactionHubStaff
@apiGroup TransactionFilter
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging

@apiParam	 (Query:)	  {Integer}	[order_by]	   Sắp xếp theo thuộc tính nào. 
                                                    <ul>
                                                        <li><code>action_time</code> Thời gian giao dịch</li>
                                                    </ul>                        
@apiParam	 (Query:)	{Integer}	[order_type]   Kiểu sắp xếp: 
                                                    <ul>
                                                        <li><code>asc</code> Tăng dần</li>
                                                        <li><code>desc</code> Giảm dần</li>
                                                    </ul>

@apiParam   (Body:)   {String}  staff_id    ID nhân viên
@apiParam   (Body:)   {Array}  [source]    Nguồn giao dịch
@apiParam   (Body:)   {String}  [start_time]  Thời gian giao dịch từ (Format: %Y-%m-%d %H:%M:%S) Giờ UTC 
@apiParam   (Body:)   {String}  [end_time]    Thời gian giao dịch đến (Format: %Y-%m-%d %H:%M:%S) Giờ UTC

@apiParamExample    {json}      Body example:
{
    "staff_id": "73d9010a-2a35-4da8-8025-c781cde22ce9",
    "start_time": "2023-07-08 10:00:00"
}


@apiSuccess {Array}   data  Danh sách giao dịch
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {string} currency_code        Đơn vị tiền tệ
@apiSuccess (data) {string} amount       Tổng số tiền giao dịch
@apiSuccess (data) {string} code               Mã giao dịch
@apiSuccess (data) {Array} items               Danh sách sản phẩm  
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id<span class="label label-optional">optional</span></td>
      <td>String</td>
      <td>Định danh item trên hệ thống Mobio</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên của item</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>StringArray</td>
      <td>Danh sách tag được gán cho item</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>Number</td>
      <td>Số lượng item</td>
    </tr>
    <tr>
      <td>price</td>
      <td>Number</td>
      <td>Đơn giá của item (sản phẩm)</td>
    </tr>
    <tr>
      <td>total_amount</td>
      <td>Number</td>
      <td>tổng giá trị của item (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã item (mã sản phẩm)</td>
    </tr>
    <tr>
        <td>supplier <span class="label label-optional">optional</span></td>
        <td>object </td>
        <td>Thông tin thương hiệu của sản phẩm </td>
    </tr>
    <tr>
        <td>supplier.code</td>
        <td>string </td>
        <td>Mã thương hiệu </td>
    </tr>
    <tr>
        <td>supplier.name</td>
        <td>string </td>
        <td>Tên thương hiệu</td>
    </tr>
  </tbody>
</table>

@apiSuccess (data) {string} profile_id         ID Profile
@apiSuccess (data) {string} store_id           ID cửa hàng
@apiSuccess (data) {string} store_id           ID cửa hàng
@apiSuccess (data) {string} action_time   Thời gian giao dịch (Format: %Y-%m-%d %H:%M:%S) Giờ UTC
@apiSuccess (data) {String} source             Nguồn phát sinh giao dịch

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "amount_unit": "VND",
            "amount_value": "1040000",
            "code": "d27d3e14-baf1-4977-1212m1x",
            "items": [
                {
                    "category": "",
                    "code": "AK10661143",
                    "currency_code": "VND",
                    "id": "60c96a4649246196de46d170",
                    "name": "[AK10661143]  M.Yellow. AK 1 lớp, dây kéo contrast",
                    "price": 490000.0,
                    "quantity": 1
                }
            ],
            "profile_id": "d8573fe3-2141-400d-a45e-a0d50c42bd75",
            "store_id": "c4c2669a-13c9-43d3-91d5-0789a6304e2e",
            "transaction_time": "2023-08-29 10:08:33",
            "source": "Offline",
            "voucher_code": [
                "AAIOWNDAFJ"
            ]
        },
        {
            "amount_unit": "VND",
            "amount_value": "1040000",
            "code": "d27d3e14-baf1-4977-a12121a",
            "items": [
                {
                    "category": "",
                    "code": "AK10661143",
                    "currency_code": "VND",
                    "id": "60c96a4649246196de46d170",
                    "name": "[AK10661143]  M.Yellow. AK 1 lớp, dây kéo contrast",
                    "price": 490000.0,
                    "quantity": 1
                }
            ],
            "profile_id": "d8573fe3-2141-400d-a45e-a0d50c42bd75",
            "store_id": "c4c2669a-13c9-43d3-91d5-0789a6304e2e",
            "transaction_time": "2023-08-29 16:30:34",
            "source": "Offline",
            "voucher_code": [
                "AAIOWNDAFJ"
            ]
        }
    ],
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 15,
        "total_count": 2,
        "total_page": 1
    }
}

"""

# -------------- Dashboard giao dịch theo nguồn -----------------
# Version 1.0.0

"""
@api {post} {host}/transaction/external/api/v1.0/dashboard/transaction/source Số lượng giao dịch theo nguồn
@apiDescription Số lượng giao dịch theo nguồn giao dịch
@apiName DashboardTransactionSource
@apiGroup Dashboard 
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Body:)   {String}  staff_id    ID nhân viên
@apiParam   (Body:)   {Array}  [source]    Nguồn giao dịch
@apiParam   (Body:)   {String}  [start_time]  Thời gian giao dịch từ (Format: %Y-%m-%d %H:%M:%S) Giờ UTC 
@apiParam   (Body:)   {String}  [end_time]    Thời gian giao dịch đến (Format: %Y-%m-%d %H:%M:%S) Giờ UTC

@apiParamExample    {json}      Body example:
{
    "staff_id": "73d9010a-2a35-4da8-8025-c781cde22ce9",
    "start_time": "2023-07-08 10:00:00"
}


@apiSuccess {Array}   data  Danh sách giao dịch
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {Array}  detail      Chi tiết số lượng giao dịch theo nguồn 
@apiSuccess (data) {String} detail.source Nguồn giao dịch 
@apiSuccess (data) {Number} detail.total  Số lượng giao dịch 
@apiSuccess (data) {Number} total       Tổng số lượng giao dịch


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "detail": [
            {
                "source": "offline",
                "total": 10
            }
        ],
        "total": 10
    } 
}

"""

# ---- Chi tiết giao dịch ---
# Version 1.0.0

"""
@api {get} {host}/transaction/external/api/v1.0/transactions/<transaction_id>/detail Chi tiết giao dịch
@apiDescription Thông tin chi tiết giao dịch
@apiName TransactionDetail
@apiGroup Transaction
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Object}  data  Thông tin giao dịch
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {string} currency_code        Đơn vị tiền tệ
@apiSuccess (data) {string} amount       Tổng số tiền giao dịch
@apiSuccess (data) {string} code               Mã giao dịch
@apiSuccess (data) {Array} items               Danh sách sản phẩm  
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id<span class="label label-optional">optional</span></td>
      <td>String</td>
      <td>Định danh item trên hệ thống Mobio</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên của item</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>StringArray</td>
      <td>Danh sách tag được gán cho item</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>Number</td>
      <td>Số lượng item</td>
    </tr>
    <tr>
      <td>price</td>
      <td>Number</td>
      <td>Đơn giá của item (sản phẩm)</td>
    </tr>
    <tr>
      <td>total_amount</td>
      <td>Number</td>
      <td>tổng giá trị của item (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã item (mã sản phẩm)</td>
    </tr>
    <tr>
        <td>supplier <span class="label label-optional">optional</span></td>
        <td>object </td>
        <td>Thông tin thương hiệu của sản phẩm </td>
    </tr>
    <tr>
        <td>supplier.code</td>
        <td>string </td>
        <td>Mã thương hiệu </td>
    </tr>
    <tr>
        <td>supplier.name</td>
        <td>string </td>
        <td>Tên thương hiệu</td>
    </tr>
  </tbody>
</table>

@apiSuccess (data) {string} profile_id         ID Profile
@apiSuccess (data) {string} store_id           ID cửa hàng
@apiSuccess (data) {string} store_id           ID cửa hàng
@apiSuccess (data) {string} action_time   Thời gian giao dịch (Format: %Y-%m-%d %H:%M:%S) Giờ UTC
@apiSuccess (data) {String} source             Nguồn phát sinh giao dịch

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":{
        "amount_unit": "VND",
        "amount_value": "1040000",
        "code": "d27d3e14-baf1-4977-1212m1x",
        "items": [
            {
                "category": "",
                "code": "AK10661143",
                "currency_code": "VND",
                "id": "60c96a4649246196de46d170",
                "name": "[AK10661143]  M.Yellow. AK 1 lớp, dây kéo contrast",
                "price": 490000.0,
                "quantity": 1
            }
        ],
        "profile_id": "d8573fe3-2141-400d-a45e-a0d50c42bd75",
        "store_id": "c4c2669a-13c9-43d3-91d5-0789a6304e2e",
        "transaction_time": "2023-08-29 10:08:33",
        "source": "Offline",
        "voucher_code": [
            "AAIOWNDAFJ"
        ]
    }
    
}

"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: ThongNV
    Company: M O B I O
    Date Created: 4/8/22
"""

# ----- Tạo mới danh sách giao dịch ----
# Version 1.0.3
# Version 1.0.2
# --------------------------------------

# Version 1.0.3


"""
@api {post} {host}/transaction/external/api/v1.0/add/list  Tạo mới danh sách giao dịch  
@apiDescription Dịch vụ ghi nhận danh sách giao dịch
<li>Nếu profile chưa có trên hệ thống --> tạo mới profile</li>

@apiVersion 1.0.3
@apiGroup Transaction
@apiName TransactionAddList

@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)               {Array}   transaction_data   Danh sách giao dịch. (Giới hạn 50 giao dịch)

@apiParam   (transaction_data:)   {Object}  profile   Thông tin profile thực hiện giao dịch. Hệ thống sẽ tự động tạo profile nếu chưa có.
                                                      <p><b>Bắt buộc có 1 trong các trường profile định danh (id, phone_number, email) dưới đây</b></p>
@apiParam   (transaction_data:)   {Object}  transaction   Thông tin giao dịch
@apiParam   (transaction_data:)   {String}  source  Nguồn phát sinh giao dịch. Example: offline, online, affilite
@apiParam   (transaction_data:)   {Object}  extend  Thông tin mở rộng của giao dịch
@apiParam   (transaction_data:)   {String}  extend.profile_type  Loại khách hàng (Mới, cũ)

@apiParam   (Profile)   {String}  [id]                  Định danh profile trên hệ thống Mobio.
@apiParam   (Profile)   {String}  [phone_number]        Số điện thoại.
@apiParam   (Profile)   {String}  [email]               Địa chỉ Email.
@apiParam   (Profile)   {Object}  [third_party_info]    Thông tin thêm của profile trên hệ thống của tenant.
@apiParam   (Profile)   {String}  third_party_info.id   ID profile trên hệ thống tenant

@apiParam   (Transaction)   {String}  code              Mã giao dịch. (mã giao dịch chưa tồn tại trong hệ thống).
@apiParam   (Transaction)   {String}  action_type       Loại hành động giao dịch:
                                                        <ul>
                                                            <li><code>buy</code> : Mua hàng</li>
                                                            <li><code>returns</code> : Trả hàng</li>
                                                        </ul>

@apiParam   (Transaction)   {Number}   action_time       Thời gian giao dịch. (Timestamps, lấy đến <b>miliseconds</b>. VD: 1649415177352)
@apiParam   (Transaction)   {Number}  amount            Tổng số tiền giao dịch.
@apiParam   (Transaction)   {String}  currency_code     Đơn vị tiền tệ.
                                                        <ul>
                                                            <li><code>VND</code>: Việt Nam Đồng</li>
                                                            <li><code>USD</code>: Đô la mỹ</li>
                                                        </ul>
@apiParam   (Transaction)   {Number}  [discount]        Tổng số tiền được chiết khấu                                                        
@apiParam   (Transaction)   {Number}  [payment_point]   Số điểm được dùng để thanh toán
@apiParam   (Transaction)   {Number}  [gift_point]      Số điểm được tặng khi thanh toán giao dịch

@apiParam   (Transaction)   {string}   status           Trạng thái giao dịch. 
                                                        <ul>
                                                            <li><code>SUCCESS</code>: Thành công</li>
                                                        </ul>
@apiParam   (Transaction)   {Array}  [items]           Danh sách item(sản phẩm) trong giao dịch.
@apiParam   (Transaction)   {String} [items.id]        ID định danh sản phẩm trên hệ thống MOBIO
@apiParam   (Transaction)   {String} items.name        Tên sản phẩm
@apiParam   (Transaction)   {String} items.code        Mã sản phẩm
@apiParam   (Transaction)   {Number} items.quantity    Số lượng sản phẩm
@apiParam   (Transaction)   {Array}  [items.tags]      Danh sách tag được gắn cho sản phẩm
@apiParam   (Transaction)   {int}    items.tags        Số lượng sản phẩm
@apiParam   (Transaction)   {int}    items.price       Đơn giá của sản phẩm
@apiParam   (Transaction)   {int}    items.total_amount     tổng giá trị của sản phẩm (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))
@apiParam   (Transaction)   {Object} [items.supplier]    Thông tin thương hiệu của sản phẩm
@apiParam   (Transaction)   {String} items.supplier.code    Mã thương hiệu
@apiParam   (Transaction)   {String} items.supplier.name    Tên thương hiệu

@apiParam   (Transaction)   {Array}   [vouchers]       Danh sách voucher sử dụng trong lần giao dịch.
@apiParam   (Transaction)   {string}  vouchers.code    Mã voucher
@apiParam   (Transaction)   {Object}  [store]          Thông tin cửa hàng thực hiện giao dịch.
@apiParam   (Transaction)   {String}  [store.id]       ID cửa hàng trên hệ thống MOBIO
@apiParam   (Transaction)   {String}  store.code       Mã cửa hàng.
@apiParam   (Transaction)   {String}  store.name       Tên cửa hàng.
@apiParam   (Transaction)   {String}  [store.address]    Địa chỉ cửa hàng
@apiParam   (Transaction)   {Object}  [payment]        Thông tin thanh toán.
@apiParam   (Transaction)   {String}  payment.type     Hình thức thanh toán: ATM, CASH
@apiParam   (Transaction)   {String}  payment.name     Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM
@apiParam   (Transaction)   {String}  payment.code     Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card
@apiParam   (Transaction)   {Object}  [staff_info]   Thông tin nhân viên thực hiện giao dịch
@apiParam   (Transaction)   {String}  [staff_info.staff_id]   ID nhân viên trên hệ thống Mobio

@apiParamExample    {json}      Body example:
{
    "transaction_data": [
        {
          "profile": {
            "id": "d071c51e-4cec-43a0-85e1-c0768982787d",
            "phone_number": "84399285833",
            "email": "<EMAIL>",
            "third_party_info": {
              "id": "86a6d10d-7b19-46f0-bdf2-7daef3010680"
            }
          },
          "transaction": {
            "code": "07e8c3db-a3bf-4508-8f79-297a31e2c02e",
            "action_type": "buy",
            "action_time": 1649739213572,
            "amount": 100000,
            "payment_point": 100,
            "gift_point": 10,
            "currency_code": "VND",
            "status": "SUCCESS",
            "items": [
              {
                "id": "ea4d37b1-5522-4494-ac21-3a3012b2efb2",
                "name": "",
                "tags": ["TAG1", "TAG2"],
                "quantity": 2,
                "code": "SP001",
                "price": 100000,
                "total_amount": 200000,
                "supplier":{
                    "code": "BG618T7X",
                    "name": "TOHATO"
                }
              }
            ],
            "vouchers": [
              {
                "code": "AAIOWNDAFJ"
              }
            ],
            "store": {
              "id": "a2bd8df0-ba59-436d-a9a4-f94e0db90802",
              "code": "CH001",
              "name": "Guardian Hà Đông",
              "address": "Hà Đông, Hà Nội"
            },
            "payment": {
              "type": "mc",
              "code": "866025029",
              "name": ""
            },
            "staff_info": {
                "staff_id": "b3350cbc-974e-4130-983d-ac1289eb53f6"
            },
          },
          "source": "offline",
        }
    ]
}    

@apiSuccess {string}    message     Nội dung phản hồi
@apiSuccess {Integer}   code        Mã phản hồi
@apiSuccess {String}    tracking_code   Mã tracking api. Dùng để đối soát dữ liệu

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "tracking_code": "2f52a44f-25fd-42c9-945f-03a412c8cf22"
}
"""

# Version 1.0.2
"""
@api {post} {host}/transaction/external/api/v1.0/add/list  Tạo mới danh sách giao dịch  
@apiDescription Dịch vụ ghi nhận danh sách giao dịch
<li>Nếu profile chưa có trên hệ thống --> tạo mới profile</li>

@apiVersion 1.0.2
@apiGroup Transaction
@apiName TransactionAddList

@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)               {Array}   transaction_data   Danh sách giao dịch. (Giới hạn 50 giao dịch)

@apiParam   (transaction_data:)   {Object}  profile   Thông tin profile thực hiện giao dịch. Hệ thống sẽ tự động tạo profile nếu chưa có.
                                                      <p><b>Bắt buộc có 1 trong các trường profile định danh (id, phone_number, email) dưới đây</b></p>
@apiParam   (transaction_data:)   {Object}  transaction   Thông tin giao dịch
@apiParam   (transaction_data:)   {String}  source  Nguồn phát sinh giao dịch. Example: offline, online, affilite

@apiParam   (Profile)   {String}  [id]                  Định danh profile trên hệ thống Mobio.
@apiParam   (Profile)   {String}  [phone_number]        Số điện thoại.
@apiParam   (Profile)   {String}  [email]               Địa chỉ Email.
@apiParam   (Profile)   {Object}  [third_party_info]    Thông tin thêm của profile trên hệ thống của tenant.
@apiParam   (Profile)   {String}  third_party_info.id   ID profile trên hệ thống tenant

@apiParam   (Transaction)   {String}  code              Mã giao dịch. (mã giao dịch chưa tồn tại trong hệ thống).
@apiParam   (Transaction)   {String}  action_type       Loại hành động giao dịch:
                                                        <ul>
                                                            <li><code>buy</code> : Mua hàng</li>
                                                            <li><code>returns</code> : Trả hàng</li>
                                                        </ul>

@apiParam   (Transaction)   {Number}   action_time       Thời gian giao dịch. (Timestamps, lấy đến <b>miliseconds</b>. VD: 1649415177352)
@apiParam   (Transaction)   {Number}  amount            Tổng số tiền giao dịch.
@apiParam   (Transaction)   {String}  currency_code     Đơn vị tiền tệ.
                                                        <ul>
                                                            <li><code>VND</code>: Việt Nam Đồng</li>
                                                            <li><code>USD</code>: Đô la mỹ</li>
                                                        </ul>
@apiParam   (Transaction)   {Number}  [payment_point]   Số điểm được dùng để thanh toán
@apiParam   (Transaction)   {Number}  [gift_point]      Số điểm được tặng khi thanh toán giao dịch
                                                                                          
@apiParam   (Transaction)   {str}   status              Trạng thái giao dịch. 
                                                        <ul>
                                                            <li><code>SUCCESS</code>: Thành công</li>
                                                        </ul>
@apiParam   (Transaction)   {Array}  [items]           Danh sách item(sản phẩm) trong giao dịch.
@apiParam   (Transaction)   {String} [items.id]        ID định danh sản phẩm trên hệ thống MOBIO
@apiParam   (Transaction)   {String} items.name        Tên sản phẩm
@apiParam   (Transaction)   {String} items.code        Mã sản phẩm
@apiParam   (Transaction)   {Number} items.quantity    Số lượng sản phẩm
@apiParam   (Transaction)   {Array}  [items.tags]      Danh sách tag được gắn cho sản phẩm
@apiParam   (Transaction)   {int}    items.tags        Số lượng sản phẩm
@apiParam   (Transaction)   {int}    items.price       Đơn giá của sản phẩm
@apiParam   (Transaction)   {int}    items.total_amount     tổng giá trị của sản phẩm (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))
@apiParam   (Transaction)   {Object} [items.supplier]    Thông tin thương hiệu của sản phẩm
@apiParam   (Transaction)   {String} items.supplier.code    Mã thương hiệu
@apiParam   (Transaction)   {String} items.supplier.name    Tên thương hiệu

@apiParam   (Transaction)   {Array}   [vouchers]       Danh sách voucher sử dụng trong lần giao dịch.
@apiParam   (Transaction)   {string}  vouchers.code    Mã voucher
@apiParam   (Transaction)   {Object}  [store]          Thông tin cửa hàng thực hiện giao dịch.
@apiParam   (Transaction)   {String}  [store.id]       ID cửa hàng trên hệ thống MOBIO
@apiParam   (Transaction)   {String}  store.code       Mã cửa hàng.
@apiParam   (Transaction)   {String}  store.name       Tên cửa hàng.
@apiParam   (Transaction)   {String}  [store.address]    Địa chỉ cửa hàng
@apiParam   (Transaction)   {Object}  [payment]        Thông tin thanh toán.
@apiParam   (Transaction)   {String}  payment.type     Hình thức thanh toán: ATM, CASH
@apiParam   (Transaction)   {String}  payment.name     Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM
@apiParam   (Transaction)   {String}  payment.code     Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card

@apiParamExample    {json}      Body example:
{
    "transaction_data": [
        {
          "profile": {
            "id": "d071c51e-4cec-43a0-85e1-c0768982787d",
            "phone_number": "84399285833",
            "email": "<EMAIL>",
            "third_party_info": {
              "id": "86a6d10d-7b19-46f0-bdf2-7daef3010680"
            }
          },
          "transaction": {
            "code": "07e8c3db-a3bf-4508-8f79-297a31e2c02e",
            "action_type": "buy",
            "action_time": 1649739213572,
            "amount": 100000,
            "payment_point": 100,
            "gift_point": 10,
            "currency_code": "VND",
            "status": "SUCCESS",
            "items": [
              {
                "id": "ea4d37b1-5522-4494-ac21-3a3012b2efb2",
                "name": "",
                "tags": ["TAG1", "TAG2"],
                "quantity": 2,
                "code": "SP001",
                "price": 100000,
                "total_amount": 200000,
                "supplier":{
                    "code": "BG618T7X",
                    "name": "TOHATO"
                }
              }
            ],
            "vouchers": [
              {
                "code": "AAIOWNDAFJ"
              }
            ],
            "store": {
              "id": "a2bd8df0-ba59-436d-a9a4-f94e0db90802",
              "code": "CH001",
              "name": "Guardian Hà Đông",
              "address": "Hà Đông, Hà Nội"
            },
            "payment": {
              "type": "mc",
              "code": "866025029",
              "name": ""
            }
          },
          "source": "offline",
        }
    ]
}    

@apiSuccess {string}    message     Nội dung phản hồi
@apiSuccess {Integer}   code        Mã phản hồi
@apiSuccess {String}    tracking_code   Mã tracking api. Dùng để đối soát dữ liệu

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "tracking_code": "2f52a44f-25fd-42c9-945f-03a412c8cf22"
}
"""

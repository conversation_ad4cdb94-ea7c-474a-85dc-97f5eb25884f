******************************** Feedback Lead ********************************
* version: 1.0.4                                                              *
*******************************************************************************
"""
@api {post} https://api-sunworld.dev.mobio.vn/profiling/v2.0/feedback/leads     Feedback lead
@apiDescription Dịch vụ để hệ thống CRM-VPBank update lại thông tin của lead sau khi sale.
@apiGroup Customer
@apiVersion 1.0.4
@apiName FeedbackLead

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam     (Body:)     {Object[]}    leads       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>Lead</code>

@apiParam     (Lead)      {String}      id          Định danh của khách hàng trên hệ thống CRM Oddo-MOBIO.
@apiParam     (Lead)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (Lead)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam  (UserInfo)      {String}        [display_name]                Tên khách hàng
@apiParam     (UserInfo)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (UserInfo)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (UserInfo)      {String}        [email]                       Email của khách hàng
@apiParam  (UserInfo)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (UserInfo)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (UserInfo)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (UserInfo)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (UserInfo)       {String}        [address]                      Địa chỉ
@apiParam  (UserInfo)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (UserInfo)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (UserInfo)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (UserInfo)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (UserInfo)       {Number}        [district_code]                Mã quận huyện
@apiParam  (UserInfo)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (UserInfo)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (UserInfo)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (UserInfo)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam  (UserInfo)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (UserInfo)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (UserInfo)      {Number}        [religiousness]                Tôn giáo
@apiParam  (UserInfo)      {Number}        [nation]                       Dân tộc.
@apiParam  (UserInfo)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (UserInfo)      {String}        [location]                     Vị trí.
@apiParam  (UserInfo)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam   (UserInfo)         {Array}     answers         Danh sách câu trả lời của khách hàng.
@apiParam   (answers)       {Number}    question_id     ID Câu hỏi.
<li><code>1: Có hợp đồng bảo hiểm trên 1 năm không?</code></li>
<li><code>2: Có sở hữu ô tô không?</code></li>
<li><code>3: Có sở hữu Bất động sản không?</code></li>
@apiParam   (answers)       {Number}    int_result      Câu trả lời dạng number.
@apiParam   (answers)       {String}    string_result   Câu trả lời dạng text.
@apiParam   (UserInfo)    {Object}    [third_party_info]    Thông tin bổ sung của third party. Thông tin này sẽ được gửi lại cho third party sau khi phân tích user.

@apiParam     (Feedback)    {Array}  [status]   Cập nhật tình trạng lead.
@apiParam     (Feedback)    {Number}  [status..value]   Trạng thái lead.<br/>
<li><code>1: Chưa liên hệ</code></li>
<li><code>2: KH không nghe máy</code></li>
<li><code>3: KH hẹn gọi lại sau</code></li>
<li><code>4: KH tắt máy hoặc sai số</code></li>
<li><code>5: KH từ chối</code></li>
<li><code>6: KH không đủ điều kiện</code></li>
<li><code>7: KH đã sử dụng sản phẩm</code></li>
<li><code>8: KH đồng ý</code></li>
<li><code>9: Khác</code></li>
<li><code>10: KH nộp hồ sơ</code></li>
<li><code>11: KH mở thẻ thành công</code></li>
<li><code>12: KH đủ điều kiện nhưng ở xa không hỗ trợ</code></li>
@apiParam     (Feedback)    {Number}  [status..updated_time]   Thời điểm cập nhật trạng thái. Đơn vị tính timestamp. Ví dụ: <code>"updated_time": 1528881775138</code><br/>

@apiParamExample    {json}      Body example:
{
    "leads":[
        {
            "id":"9035b72d-4b8e-471d-935d-e73f73063246",
            "user_info": {
              "display_name": "AnNV",
              "phone_number": "+84904123456",
              "email": "<EMAIL>",
              "status": 1,
              "avatar": "",
              "birthday": "29/11/1989",
              "gender": 2,
              "address": "Cầu Giấy, Hà Nội",
              "phone_number2": "",
              "national_phone_code": "84",
              "province_code": 1,
              "district_code": 1,
              "ward_code": 1,
              "marital_status": 1,
              "monthly_income": 0,
              "income_unit": 1,
              "income_type": 1,
              "religiousness": 1,
              "nation": 1,
              "job": 39,
              "location": "",
              "people_id": "1234567890",
              "frequently_demands": [1, 2, 3],
              "answers": [
                {
                    "question_id": 1,
                    "int_result": 1,
                    "string_result": "Có"
                },
                {
                    "question_id": 2,
                    "int_result": 0,
                    "string_result": "Không"
                }
              ],
              "third_party_info":{
                "id": "f79a7c11-31ae-4637-9a09-a0fce3184dd4"
              }
            },
            "feedback":{
                "status":[
                  {
                    "value": 1,
                    "updated_time": 1528861712428
                  },
                  {
                    "value": 8,
                    "updated_time": 1528861712428
                  }
                ]
            }

        }
    ]
}

@apiSuccess     {String[]}      success     Danh sách id của khách hàng đã update thành công.
@apiSuccess     {Object[]}      error       Danh sách khách hàng update không thành công.
@apiSuccess     {String}      error..id   Id của khách hàng trên hệ thống CRM Odoo-Mobio.
@apiSuccess     {String}      error..message   Nguyên nhân lỗi không update được thông tin khách hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "success":[
        "0755ee00-8610-424f-ba4b-b264f64c3b46",
        "ccca9bfb-0f21-47e2-8f90-8d590e094b77"
    ],
    "error":[
        {
            "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",
            "message": "..."
        },
        {
            "id": "a33bf63e-5f0e-4023-8bb4-f6793666ddcf",
            "message": "..."
        }
    ]
}
"""

#!/usr/bin/python
# -*- coding: utf8 -*-
************************ Thêm domain ********************************
* version: 1.0.0                                                   *
********************************************************************
"""
@api {post} /domain/create Thêm domain
@apiDescription thêm domain mới
@apiGroup SettingBackend
@apiVersion 1.0.0
@apiName /domain/create

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse 401
@apiHeader (Header) {String} Content-Type multipart/form-data
@apiParam   (Body:)  {File}   [image]  	  Gửi 1 image(có thể bỏ trống) các type ảnh support .png, .jpg, jpeg
@apiParam   (Body:)  {String} name_site     Name site (form-data)
@apiParam   (Body:)  {String} link_site     Link site (form-data)
@apiParam   (Body:)  {String} [link_image]    Link ảnh đại diện.
@apiParamExample    {form}  Body example:
		image: {Files} Ảnh mô tả https://drive.google.com/file/d/1_vau3ihqMg5r0v2WFkYAOhDjGt8JwATk/view?usp=sharing
		name_site:{String} mobio
		link_site:{String} mobio.vn
        link_image: {String} https://api-test1.mobio.vn/social/static/message/c3d509d0-f7c7-4497-ab70-ba1d682b1486.jpg

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "domain_id": "5da6ef6c1c5ab50f4d24dbfc",
  "lang": "vi",
  "link_site": "mobio.vn",
  "message": "Đã tồn tại domain",
  "name_site": "mobio",
  "script": "<script src='https://test1.mobio.vn/chat/mo.chat.min.js?v=1.0'></script><script>(function(){mochatjs.init({domain_id:'5da6ef6c1c5ab50f4d24dbfc'});})();</script>"
}
"""
************************ Update domain *****************************
* version: 2.0.0                                                   *
********************************************************************
"""
@api {put} /domain/save/domain_id Chỉnh sửa domain 
@apiDescription Chỉnh sửa domain
@apiGroup SettingBackend
@apiVersion 2.0.0
@apiName /domain/save/domain_id

@apiUse 401
@apiUse 404
@apiUse 412
@apiUse 500
@apiHeader (Header) {String} Content-Type multipart/form-data
@apiParam   (Body:)     {String} _id     	Domain id (form-data)
@apiParam   (Body:)     {String} name_site 	Name site (form-data)
@apiParam   (Body:)  	{File}   [image]  	Gửi 1 image các type ảnh support .png, .jpg, jpeg
@apiParam   (Body:)  	{String} [delete_avatar]  	Tùy chọn xóa avartar.<code>null hoặc không truyền nếu không xóa avatar; true-nếu xóa avatar</code>
@apiParam   (Body:)     {String} [link_image]   Link ảnh đại diện.
@apiParamExample    {form}  Body example:
		image: {Files} Ảnh mô tả https://drive.google.com/file/d/1_vau3ihqMg5r0v2WFkYAOhDjGt8JwATk/view?usp=sharing
		name_site:{Text} mobio
		_id:{Text} 5db0209109eebf9fd2ce790e
		delete_avatar:true

@apiSuccess   (Body:)	  {String}   name_site                  name site của domain_id
@apiSuccess   (Body:)	  {String}   updated_time               thời gian update
@apiSuccess   (Body:)	  {String}   updated_user               người update

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "link_site": "kaowd,awd",
    "name_site": "olaha",
    "updated_time": "Wed, 04 Dec 2019 08:43:04 GMT",
    "updated_user": "8ba5e082-393f-47e0-a4a4-b91643056583"
  },
  "lang": "vi",
  "message": "Request thành công."
}
"""
************************ Update domain *****************************
* version: 1.0.0                                                   *
********************************************************************
"""
@api {post} /domain/save Chỉnh sửa domain 
@apiDescription Chỉnh sửa domain
@apiGroup SettingBackend
@apiVersion 1.0.0
@apiName /domain/save

@apiUse 401
@apiUse 404
@apiUse 412
@apiUse 500
@apiHeader (Header) {String} Content-Type multipart/form-data
@apiParam   (Body:)     {String} _id     	Domain id (form-data)
@apiParam   (Body:)     {String} name_site 	Name site (form-data)
@apiParam   (Body:)  	{File}   [image]  	Gửi 1 image các type ảnh support .png, .jpg, jpeg
@apiParamExample    {form}  Body example:
		image: {Files} Ảnh mô tả https://drive.google.com/file/d/1_vau3ihqMg5r0v2WFkYAOhDjGt8JwATk/view?usp=sharing
		name_site:{Text} mobio
		link_site:{Text} mobio.vn
		_id:{Text} 5db0209109eebf9fd2ce790e

@apiSuccess   (Body:)	  {String}   link_site                  link site của domain_id
@apiSuccess   (Body:)	  {String}   name_site                  name site của domain_id
@apiSuccess   (Body:)	  {String}   updated_time               thời gian update
@apiSuccess   (Body:)	  {String}   updated_user               người update

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "link_site": "kaowd,awd",
    "name_site": "olaha",
    "updated_time": "Wed, 04 Dec 2019 08:43:04 GMT",
    "updated_user": "8ba5e082-393f-47e0-a4a4-b91643056583"
  },
  "lang": "vi",
  "message": "Request thÃ nh cÃ´ng."
}
"""
************************ Update config chattool ********************
* version: 1.0.3                                                   *
********************************************************************
"""
@api {post} /config/save Cập nhập config chattool
@apiDescription Cập nhập config chattool , các config có thể thêm mới hoặc bỏ qua hoặc để trống
@apiGroup SettingBackend
@apiVersion 1.0.3
@apiName /config/save
@apiUse 401
@apiUse 404
@apiUse 412
@apiUse 500


@apiParam   (Body:)     {String}   _id                  Domain id
@apiParam   (Body:)     {Object}   chat_tool_properties Thông tin cấu hình chat tool.
@apiParam   (Body:)     {Object}   chat_tool_properties.notify_frame         Thông tin cấu hình thông báo và ẩn hiện khung chat.
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_bell_notifi          Âm thanh thông báo.
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_web_notifi           Thông báo trên cửa sổ trình duyệt.
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.hide_offline         Ẩn khung chat khi offline
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.hide_mobile          Ẩn khung chat trên di động
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_upload       Tắt tính năng tải file
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_sticker      Tắt tính năng dùng biểu tượng cảm xúc
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_review_chat  Tắt đánh giá hội thoại  
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_display_status_chat  Tắt không cho khách hàng thấy trạng thái đã xem của chat      
@apiParam   (Body:)     {Object}   chat_tool_properties.config_frame         Thông tin cấu hình hiển thị khung chat.
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.style_mobile         Dạng hiển thị trên mobile  
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.style_desktop         Dạng hiển thị trên destop 
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.width                Chiều rộng khi mở rộng  
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.height               Chiều cao khi mở rộng  
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.width_desktop_close                Chiều rộng khi đóng ở style desktop  2
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.height_desktop_close               Chiều cao khi đóng ở style desktop  2
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.chat_box_position    Vị trí chatbox (1-9)  
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.bg_header_color      Màu nền header 
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.text_header_color    Màu chữ header  
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.bg_chat_color_supporter  Màu nền tin nhắn supporter
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.bg_chat_color_visitor    Màu nền tin nhắn khách hàng
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.txt_message_color_supporter  Màu chữ tin nhắn khách hàng  
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.txt_message_color_visitor    Màu chũ tin nhắn supporter 
@apiParam   (Body:)     {Object}   chat_tool_properties.frame_setting        Cấu hình nội dung khung chat.
@apiParam   (Body:)     {String}   chat_tool_properties.frame_setting.notify               Thông báo khi bật chatbox tối đa 100 kí tự 
@apiParam   (Body:)     {Boolean}  chat_tool_properties.frame_setting.require_info_user    Yêu cầu điền vào mẫu để bắt đầu chat  
@apiParam   (Body:)     {String}   chat_tool_properties.frame_setting.notify_info          Thông báo khi điền thông tin tối đa 100 kí tự
@apiParam   (Body:)     {int}      chat_tool_properties.frame_setting.name                 Setting tên ( 0-là không hỏi, 1- không bắt buộc, 2-bắt buộc)  
@apiParam   (Body:)     {int}      chat_tool_properties.frame_setting.email                Setting email ( 0-là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiParam   (Body:)     {int}      chat_tool_properties.frame_setting.phone_number         Setting phone_number ( 0- là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiParam   (Body:)     {object}   chat_tool_properties.greeting_setting     Cấu hình lời chào
@apiParam   (Body:)     {String}   chat_tool_properties.greeting_setting.popup_message      Lời chào
@apiParam   (Body:)     {int}      chat_tool_properties.greeting_setting.time_popup         Thời gian pop up lời chào
@apiParam   (Body:)     {Boolean}  chat_tool_properties.greeting_setting.popup_message_on   Bật lời chào pop up
@apiParam   (Body:)     {Object}   messenger_properties Thông tin cấu hinhf messager
@apiParam   (Body:)     {Object}   widget_properties Thông tin cấu hình widget

@apiParamExample    {json}  Body example:
{
    "_id":"5d884c7c0f73be560fa75879",
    "chat_tool_properties": {      
        "notify_frame":{
          "disable_bell_notifi":true,
          "disable_web_notifi":true
          "hide_offline":false,
          "hide_mobile":false,
          "disable_upload":false,
          "disable_sticker":false,     
          "disable_review_chat":false,
          "disable_display_status_chat":false
          },
        "config_frame":{
          "style_mobile":1,
          "style_desktop":2,
          "width":400,
          "height":600,
          "width_desktop_close":32,
          "height_desktop_close":149,
          "chat_box_position":9,
          "bg_header_color":"#000",
          "text_header_color":"white",
          "bg_chat_color_supporter": "red",
          "bg_chat_color_visitor":"white",
          "txt_message_color_supporter":"white",
          "txt_message_color_visitor":"#000"
        },
        "frame_setting":{
          "notify":"Hãy gửi tin nhắn và chúng tôi sẽ cố gắng trả lời bạn trong thời gian ngắn nhất",           
          "require_info_user": true,
          "notify_info":"xin vui lòng điền vào thông tin dưới đây để chúng tôi có thể dễ dàng liên lạc với bạn"
          "name":1,
          "email":1,
          "phone_number":1                        
         },
         "greeting_setting":{
          "popup_message_on":false,
          "popup_message":"Bạn có hứng thú với sản phẩm này không ?",
          "time_popup":13
         }   
     },
  	"messenger_properties": {
            "config_frame": {
                "frame_color": "#fa3c4c",
                "open_in_new_tab": false,
                "open_popup": true
            },
            "greeting_setting": {
                "message": "Nhập nội dung."
            }
        },
    "widget_properties": {
            "config_frame": {
                "chat_box_position": 9,
                "frame_color": "#4cb7ff",
                "messenger": true,
                "mobiochat": true
            },
            "greeting_setting": {
                "bg_icon_close_color": "#e6e5e5",
                "bg_popup_color": "#000",
                "font_size": "16px",
                "popup_message": "Xin chào. Hãy nhắn tin nếu bạn cần chúng tôi giúp đỡ?",
                "popup_message_on": true,
                "txt_popup_color": "#fff"
            }
        }
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "update config thành công"
}
"""
************************ Update config chattool ********************
* version: 1.0.2                                                   *
********************************************************************
"""
@api {post} /config/save Cập nhập config chattool
@apiDescription Cập nhập config chattool , các config có thể thêm mới hoặc bỏ qua hoặc để trống
@apiGroup SettingBackend
@apiVersion 1.0.2
@apiName /config/save
@apiUse 401
@apiUse 404
@apiUse 412
@apiUse 500


@apiParam   (Body:)     {String}   _id                  Domain id
@apiParam   (Body:)     {Object}   chat_tool_properties Thông tin cấu hình chat tool.
@apiParam   (Body:)     {Object}   chat_tool_properties.notify_frame         Thông tin cấu hình thông báo và ẩn hiện khung chat.
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_bell_notifi          Âm thanh thông báo.
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_web_notifi           Thông báo trên cửa sổ trình duyệt.
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.hide_offline         Ẩn khung chat khi offline
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.hide_mobile          Ẩn khung chat trên di động
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_upload       Tắt tính năng tải file
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_sticker      Tắt tính năng dùng biểu tượng cảm xúc
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_review_chat  Tắt đánh giá hội thoại      
@apiParam   (Body:)     {Object}   chat_tool_properties.config_frame         Thông tin cấu hình hiển thị khung chat.
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.style_mobile         Dạng hiển thị trên mobile  
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.style_desktop         Dạng hiển thị trên destop 
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.width                Chiều rộng khi mở rộng  
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.height               Chiều cao khi mở rộng  
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.width_desktop_close                Chiều rộng khi đóng ở style desktop  2
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.height_desktop_close               Chiều cao khi đóng ở style desktop  2
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.chat_box_position    Vị trí chatbox (1-9)  
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.bg_header_color      Màu nền header 
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.text_header_color    Màu chữ header  
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.bg_chat_color_supporter  Màu nền tin nhắn supporter
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.bg_chat_color_visitor    Màu nền tin nhắn khách hàng
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.txt_message_color_supporter  Màu chữ tin nhắn khách hàng  
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.txt_message_color_visitor    Màu chũ tin nhắn supporter 
@apiParam   (Body:)     {Object}   chat_tool_properties.frame_setting        Cấu hình nội dung khung chat.
@apiParam   (Body:)     {String}   chat_tool_properties.frame_setting.notify               Thông báo khi bật chatbox tối đa 100 kí tự 
@apiParam   (Body:)     {Boolean}  chat_tool_properties.frame_setting.require_info_user    Yêu cầu điền vào mẫu để bắt đầu chat  
@apiParam   (Body:)     {String}   chat_tool_properties.frame_setting.notify_info          Thông báo khi điền thông tin tối đa 100 kí tự
@apiParam   (Body:)     {int}      chat_tool_properties.frame_setting.name                 Setting tên ( 0-là không hỏi, 1- không bắt buộc, 2-bắt buộc)  
@apiParam   (Body:)     {int}      chat_tool_properties.frame_setting.email                Setting email ( 0-là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiParam   (Body:)     {int}      chat_tool_properties.frame_setting.phone_number         Setting phone_number ( 0- là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiParam   (Body:)     {object}   chat_tool_properties.greeting_setting     Cấu hình lời chào
@apiParam   (Body:)     {String}   chat_tool_properties.greeting_setting.popup_message        Lời chào
@apiParam   (Body:)     {int}      chat_tool_properties.greeting_setting.time_popup         	Thời gian pop up lời chào

@apiParamExample    {json}  Body example:
{
    "_id":"5d884c7c0f73be560fa75879",
    "chat_tool_properties": {      
        "notify_frame":{
          "disable_bell_notifi":true,
          "disable_web_notifi":true
          "hide_offline":false,
          "hide_mobile":false
          "disable_upload":false,
          "disable_sticker":false,     
          "disable_review_chat":false
          },
        "config_frame":{
          "style_mobile":1,
          "style_desktop":2,
          "width":400,
          "height":600,
          "width_desktop_close":32,
          "height_desktop_close":149,
          "chat_box_position":9,
          "bg_header_color":"#000",
          "text_header_color":"white",
          "bg_chat_color_supporter": "red",
          "bg_chat_color_visitor":"white",
          "txt_message_color_supporter":"white",
          "txt_message_color_visitor":"#000"
        },
        "frame_setting":{
          "notify":"Hãy gửi tin nhắn và chúng tôi sẽ cố gắng trả lời bạn trong thời gian ngắn nhất"           
          "require_info_user": true,
          "notify_info":"xin vui lòng điền vào thông tin dưới đây để chúng tôi có thể dễ dàng liên lạc với bạn"
          "name":1,
          "email":1,
          "phone_number":1                        
         },
         "greeting_setting":{
          "popup_message":"Bạn có hứng thú với sản phẩm này không ?",
          "time_popup":13
         }   
     }
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "update config thành công"
}
"""
************************ Update config chattool ********************
* version: 1.0.1                                                   *
********************************************************************
"""
@api {post} /config/save Cập nhập config chattool
@apiDescription Cập nhập config chattool , các config có thể thêm mới hoặc bỏ qua hoặc để trống
@apiGroup SettingBackend
@apiVersion 1.0.1
@apiName /config/save
@apiUse 401
@apiUse 404
@apiUse 412
@apiUse 500


@apiParam   (Body:)     {String}   _id                  Domain id
@apiParam   (Body:)     {Object}   chat_tool_properties Thông tin cấu hình chat tool.
@apiParam   (Body:)     {Object}   chat_tool_properties.notify_frame         Thông tin cấu hình thông báo và ẩn hiện khung chat.
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_bell_notifi          Âm thanh thông báo.
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_web_notifi           Thông báo trên cửa sổ trình duyệt.
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.hide_offline         Ẩn khung chat khi offline
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.hide_mobile          Ẩn khung chat trên di động
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_upload       Tắt tính năng tải file
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_sticker      Tắt tính năng dùng biểu tượng cảm xúc
@apiParam   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_review_chat  Tắt đánh giá hội thoại      
@apiParam   (Body:)     {Object}   chat_tool_properties.config_frame         Thông tin cấu hình hiển thị khung chat.
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.style_mobile         Dạng hiển thị trên mobile  
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.style_desktop         Dạng hiển thị trên destop 
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.width                Chiều rộng khi mở rộng  
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.height               Chiều cao khi mở rộng  
@apiParam   (Body:)     {int}      chat_tool_properties.config_frame.chat_box_position    Vị trí chatbox (1-9)  
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.bg_header_color      Màu nền header 
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.text_header_color    Màu chữ header  
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.bg_chat_color_supporter  Màu nền tin nhắn supporter
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.bg_chat_color_visitor    Màu nền tin nhắn khách hàng
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.txt_message_color_supporter  Màu chữ tin nhắn khách hàng  
@apiParam   (Body:)     {String}   chat_tool_properties.config_frame.txt_message_color_visitor    Màu chũ tin nhắn supporter 
@apiParam   (Body:)     {Object}   chat_tool_properties.frame_setting        Cấu hình nội dung khung chat.
@apiParam   (Body:)     {String}   chat_tool_properties.frame_setting.notify               Thông báo khi bật chatbox tối đa 100 kí tự 
@apiParam   (Body:)     {Boolean}  chat_tool_properties.frame_setting.require_info_user    Yêu cầu điền vào mẫu để bắt đầu chat  
@apiParam   (Body:)     {String}   chat_tool_properties.frame_setting.notify_info          Thông báo khi điền thông tin tối đa 100 kí tự
@apiParam   (Body:)     {int}      chat_tool_properties.frame_setting.name                 Setting tên ( 0-là không hỏi, 1- không bắt buộc, 2-bắt buộc)  
@apiParam   (Body:)     {int}      chat_tool_properties.frame_setting.email                Setting email ( 0-là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiParam   (Body:)     {int}      chat_tool_properties.frame_setting.phone_number         Setting phone_number ( 0- là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiParam   (Body:)     {object}   chat_tool_properties.greeting_setting     Cấu hình lời chào
@apiParam   (Body:)     {String}   chat_tool_properties.greeting_setting.popup_message        Lời chào
@apiParam   (Body:)     {int}      chat_tool_properties.greeting_setting.time_popup         	Thời gian pop up lời chào

@apiParamExample    {json}  Body example:
{
    "_id":"5d884c7c0f73be560fa75879",
    "chat_tool_properties": {      
        "notify_frame":{
          "disable_bell_notifi":true,
          "disable_web_notifi":true
          "hide_offline":false,
          "hide_mobile":false
          "disable_upload":false,
          "disable_sticker":false,     
          "disable_review_chat":false
          },
        "config_frame":{
          "style_mobile":1,
          "style_desktop":2,
          "width":400,
          "height":600,
          "chat_box_position":9,
          "bg_header_color":"#000",
          "text_header_color":"white",
          "bg_chat_color_supporter": "red",
          "bg_chat_color_visitor":"white",
          "txt_message_color_supporter":"white",
          "txt_message_color_visitor":"#000"
        },
        "frame_setting":{
          "notify":"Hãy gửi tin nhắn và chúng tôi sẽ cố gắng trả lời bạn trong thời gian ngắn nhất"           
          "require_info_user": true,
          "notify_info":"xin vui lòng điền vào thông tin dưới đây để chúng tôi có thể dễ dàng liên lạc với bạn"
          "name":1,
          "email":1,
          "phone_number":1                        
         },
         "greeting_setting":{
          "popup_message":"Bạn có hứng thú với sản phẩm này không ?",
          "time_popup":13
         }   
     }
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "update config thành công"
}
"""

************************ Update config chattool ********************
* version: 1.0.0                                                   *
********************************************************************
"""
@api {post} /config/save Cập nhập config chattool
@apiDescription Cập nhập config chattool , các config có thể thêm mới hoặc bỏ qua hoặc để trống
@apiGroup SettingBackend
@apiVersion 1.0.0
@apiName /config/save
@apiUse 401
@apiUse 404
@apiUse 412
@apiUse 500


@apiParam   (Body:)     {String}   _id                  Domain id
@apiParam   (Body:)     {Object}   chat_tool_properties Thông tin cấu hình chat tool.
@apiParam   (Body:)     {Object}   notify_frame         Thông tin cấu hình thông báo và ẩn hiện khung chat.
@apiParam   (Body:)     {Boolean}   bell_notifi          Âm thanh thông báo.
@apiParam   (Body:)     {Boolean}   web_notifi           Thông báo trên cửa sổ trình duyệt.
@apiParam   (Body:)     {Boolean}   hide_offline         Ẩn khung chat khi offline
@apiParam   (Body:)     {Boolean}   hide_mobile          Ẩn khung chat trên di động
@apiParam   (Body:)     {Boolean}   disable_upload       Tắt tính năng tải file
@apiParam   (Body:)     {Boolean}   disable_sticker      Tắt tính năng dùng biểu tượng cảm xúc
@apiParam   (Body:)     {Boolean}   disable_review_chat  Tắt đánh giá hội thoại      
@apiParam   (Body:)     {Object}   config_frame         Thông tin cấu hình hiển thị khung chat.
@apiParam   (Body:)     {int}      style_mobile         Dạng hiển thị trên mobile  
@apiParam   (Body:)     {int}      style_destop         Dạng hiển thị trên destop 
@apiParam   (Body:)     {int}      width                Chiều rộng khi mở rộng  
@apiParam   (Body:)     {int}      height               Chiều cao khi mở rộng  
@apiParam   (Body:)     {int}      chat_box_position    Vị trí chatbox (1-9)  
@apiParam   (Body:)     {String}   bg_header_color      Màu nền header 
@apiParam   (Body:)     {String}   text_header_color    Màu chữ header  
@apiParam   (Body:)     {String}   bg_chat_color_supporter  Màu nền tin nhắn supporter
@apiParam   (Body:)     {String}   bg_chat_color_visitor    Màu nền tin nhắn khách hàng
@apiParam   (Body:)     {String}   txt_message_color_supporter  Màu chữ tin nhắn khách hàng  
@apiParam   (Body:)     {String}   txt_message_color_visitor    Màu chũ tin nhắn supporter 
@apiParam   (Body:)     {Object}   frame_setting        Cấu hình nội dung khung chat.
@apiParam   (Body:)     {String}   notify               Thông báo khi bật chatbox tối đa 100 kí tự 
@apiParam   (Body:)     {Boolean}  require_info_user    Yêu cầu điền vào mẫu để bắt đầu chat  
@apiParam   (Body:)     {String}   notify_info          Thông báo khi điền thông tin tối đa 100 kí tự
@apiParam   (Body:)     {int}      name                 Setting tên ( 0-là không hỏi, 1- không bắt buộc, 2-bắt buộc)  
@apiParam   (Body:)     {int}      email                Setting email ( 0-là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiParam   (Body:)     {int}      phone_number         Setting phone_number ( 0- là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiParam   (Body:)     {object}   greeting_setting     Cấu hình lời chào
@apiParam   (Body:)     {String}   popup_messege        Lời chào
@apiParam   (Body:)     {int}      time_popup         	Thời gian pop up lời chào

@apiParamExample    {json}  Body example:
{
    "_id":"5d884c7c0f73be560fa75879",
    "chat_tool_properties": {      
        "notify_frame":{
          "bell_notifi":true,
          "web_notifi":true
          "hide_offline":false,
          "hide_mobile":false
          "disable_upload":false,
          "disable_sticker":false,     
          "disable_review_chat":false
          },
        "config_frame":{
          "style_mobile":1,
          "style_destop":2,
          "width":400,
          "height":600,
          "chat_box_position":9,
          "bg_header_color":"#000",
          "text_header_color":"white",
          "bg_chat_color_supporter": "red",
          "bg_chat_color_visitor":"white",
          "txt_message_color_supporter":"white",
          "txt_message_color_visitor":"#000"
        },
        "frame_setting":{
          "notify":"Hãy gửi tin nhắn và chúng tôi sẽ cố gắng trả lời bạn trong thời gian ngắn nhất"           
          "require_info_user": true,
          "notify_info":"xin vui lòng điền vào thông tin dưới đây để chúng tôi có thể dễ dàng liên lạc với bạn"
          "name":1,
          "email":1,
          "phone_number":1                        
         },
         "greeting_setting":{
          "popup_messege":"Bạn có hứng thú với sản phẩm này không ?",
          "time_popup":13
         }   
     }
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "update config thành công"
}
"""
************************Lấy danh sách domain đang quản lý **********
* version: 2.0.0                                                   *
********************************************************************
"""
@api {get} /domain/list Lấy danh sách domain đang quản lý 
@apiDescription Lấy danh sách domain đang quản lý  không lấy domain đã close
@apiGroup SettingBackend
@apiVersion 2.0.0
@apiName /domain/list

@apiParam   (Params) {String} [id_domains] filter id tìm theo id domain <code>id_domains=5dc8476717df33af8a646f65,5de76314ff167cfa9d5dae04</code>
@apiParam   (Params) {String} [type] kiểu danh sách <code>all-lấy cả domain đang tắt; default- lấy domain đang hoạt động</code> mặc định lấy default nếu không tryền vào </code>

@apiUse 404
@apiUse 412
@apiUse 500
@apiUse 401

@apiSuccess   (Body:)	  {String}   _id                  Domain id
@apiSuccess   (Body:)     {Object}   chat_tool_properties Thông tin cấu hình chat tool.
@apiSuccess   (Body:)     {Object}   chat_tool_properties.notify_frame         Thông tin cấu hình thông báo và ẩn hiện khung chat.
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_bell_notifi          Âm thanh thông báo.
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_web_notifi           Thông báo trên cửa sổ trình duyệt.
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.hide_offline         Ẩn khung chat khi offline
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.hide_mobile          Ẩn khung chat trên di động
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_upload       Tắt tính năng tải file
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_sticker      Tắt tính năng dùng biểu tượng cảm xúc
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_review_chat  Tắt đánh giá hội thoại 
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_display_status_chat  Tắt không cho khách hàng thấy trạng thái đã xem của chat      
@apiSuccess   (Body:)     {Object}   chat_tool_properties.config_frame         Thông tin cấu hình hiển thị khung chat.
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.style_mobile         Dạng hiển thị trên mobile  
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.style_desktop         Dạng hiển thị trên destop 
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.width                Chiều rộng khi mở rộng  
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.height               Chiều cao khi mở rộng  
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.width_desktop_close                Chiều rộng khi đóng ở style desktop  2 (luôn luôn được trả về)
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.height_desktop_close               Chiều cao khi đóng ở style desktop  2 (luôn luôn được trả về)
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.chat_box_position    Vị trí chatbox (1-9)  
@apiSuccess   (Body:)     {String}   chat_tool_properties.config_frame.bg_header_color      Màu nền header 
@apiSuccess   (Body:)     {String}   chat_tool_properties.config_frame.text_header_color    Màu chữ header  
@apiSuccess   (Body:)     {String}   chat_tool_properties.config_frame.bg_chat_color_supporter  Màu nền tin nhắn supporter
@apiSuccess   (Body:)     {String}   chat_tool_properties.config_frame.bg_chat_color_visitor    Màu nền tin nhắn khách hàng
@apiSuccess   (Body:)     {String}   chat_tool_properties.config_frame.txt_message_color_supporter  Màu chữ tin nhắn khách hàng  
@apiSuccess   (Body:)     {String}   chat_tool_properties.config_frame.txt_message_color_visitor    Màu chũ tin nhắn supporter 
@apiSuccess   (Body:)     {Object}   chat_tool_properties.frame_setting        Cấu hình nội dung khung chat.
@apiSuccess   (Body:)     {String}   chat_tool_properties.frame_setting.notify               Thông báo khi bật chatbox tối đa 100 kí tự 
@apiSuccess   (Body:)     {Boolean}  chat_tool_properties.frame_setting.require_info_user    Yêu cầu điền vào mẫu để bắt đầu chat  
@apiSuccess   (Body:)     {String}   chat_tool_properties.frame_setting.notify_info          Thông báo khi điền thông tin tối đa 100 kí tự
@apiSuccess   (Body:)     {int}      chat_tool_properties.frame_setting.name                 Setting tên ( 0-là không hỏi, 1- không bắt buộc, 2-bắt buộc)  
@apiSuccess   (Body:)     {int}      chat_tool_properties.frame_setting.email                Setting email ( 0-là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiSuccess   (Body:)     {int}      chat_tool_properties.frame_setting.phone_number         Setting phone_number ( 0- là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiSuccess   (Body:)     {object}   chat_tool_properties.greeting_setting     Cấu hình lời chào
@apiSuccess   (Body:)     {String}   chat_tool_properties.greeting_setting.popup_message        Lời chào
@apiSuccess   (Body:)     {int}      chat_tool_properties.greeting_setting.time_popup         	Thời gian pop up lời chào
@apiSuccess   (Body:)     {Boolean}  chat_tool_properties.greeting_setting.popup_message_on   Bật lời chào pop up

@apiParamExample    {json}  Success-Response:
    HTTP/1.1 200 OK
{
  "List_Domain": [
    {
      "_id": "5db0209109eebf9fd2ce790e",
       "chat_tool_properties": {      
        "notify_frame":{
          "disable_bell_notifi":true,
          "disable_web_notifi":true
          "hide_offline":false,
          "hide_mobile":false
          "disable_upload":false,
          "disable_sticker":false,     
          "disable_review_chat":false,
          "disable_display_status_chat": false
          },
        "config_frame":{
          "style_mobile":1,
          "style_desktop":2,
          "width":400,
          "height":600,
          "width_desktop_close":32,
          "height_desktop_close":149,
          "chat_box_position":9,
          "bg_header_color":"#000",
          "text_header_color":"white",
          "bg_chat_color_supporter": "red",
          "bg_chat_color_visitor":"white",
          "txt_message_color_supporter":"white",
          "txt_message_color_visitor":"#000"
        },
        "frame_setting":{
          "notify":"Hãy gửi tin nhắn và chúng tôi sẽ cố gắng trả lời bạn trong thời gian ngắn nhất"           
          "require_info_user": true,
          "notify_info":"xin vui lòng điền vào thông tin dưới đây để chúng tôi có thể dễ dàng liên lạc với bạn"
          "name":1,
          "email":1,
          "phone_number":1                        
         },
         "greeting_setting":{
         	"popup_message_on":true,
          "popup_message":"Bạn có hứng thú với sản phẩm này không ?",
          "time_popup":13
         }   
     },
      "messenger_properties": {
            "config_frame": {
                "frame_color": "#fa3c4c",
                "open_in_new_tab": false,
                "open_popup": true
            },
            "greeting_setting": {
                "message": "Nhập nội dung."
            }
        },
      "widget_properties": {
            "config_frame": {
                "chat_box_position": 9,
                "frame_color": "#4cb7ff",
                "messenger": true,
                "mobiochat": true
            },
            "greeting_setting": {
                "bg_icon_close_color": "#e6e5e5",
                "bg_popup_color": "#000",
                "font_size": "16px",
                "popup_message": "Xin chào. Hãy nhắn tin nếu bạn cần chúng tôi giúp đỡ?",
                "popup_message_on": true,
                "txt_popup_color": "#fff"
            }
        },
      "created_time": "Wed, 23 Oct 2019 16:42:41 GMT",
      "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "link_site": "kaowd,awd",
      "login_required": true,
      "max_user_support": 3,
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name_site": "lakwdo",
      "script": "<script src='https://test1.mobio.vn/chat/mo.chat.min.js?v=1.0'></script><script>(function(){mochatjs.init({domain_id:'5db0209109eebf9fd2ce790e'});})();</script>",
      "status": 1,
      "updated_time": "Wed, 23 Oct 2019 17:04:33 GMT",
      "updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
    },
    ....
  ],
  "code": "001",
  "lang": "vi",
  "message": "Request thÃ nh cÃ´ng."
}
"""
************************Lấy danh sách domain đang quản lý **********
* version: 1.0.1                                                   *
********************************************************************
"""
@api {get} /domain/list Lấy danh sách domain đang quản lý 
@apiDescription Lấy danh sách domain đang quản lý  không lấy domain đã close
@apiGroup SettingBackend
@apiVersion 1.0.1
@apiName /domain/list

@apiParam   (Params) {String} [id_domains] filter id tìm theo id domain <code>id_domains=5dc8476717df33af8a646f65,5de76314ff167cfa9d5dae04</code>

@apiUse 404
@apiUse 412
@apiUse 500
@apiUse 401

@apiSuccess   (Body:)	  {String}   _id                  Domain id
@apiSuccess   (Body:)     {Object}   chat_tool_properties Thông tin cấu hình chat tool.
@apiSuccess   (Body:)     {Object}   chat_tool_properties.notify_frame         Thông tin cấu hình thông báo và ẩn hiện khung chat.
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_bell_notifi          Âm thanh thông báo.
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_web_notifi           Thông báo trên cửa sổ trình duyệt.
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.hide_offline         Ẩn khung chat khi offline
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.hide_mobile          Ẩn khung chat trên di động
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_upload       Tắt tính năng tải file
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_sticker      Tắt tính năng dùng biểu tượng cảm xúc
@apiSuccess   (Body:)     {Boolean}   chat_tool_properties.notify_frame.disable_review_chat  Tắt đánh giá hội thoại      
@apiSuccess   (Body:)     {Object}   chat_tool_properties.config_frame         Thông tin cấu hình hiển thị khung chat.
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.style_mobile         Dạng hiển thị trên mobile  
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.style_desktop         Dạng hiển thị trên destop 
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.width                Chiều rộng khi mở rộng  
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.height               Chiều cao khi mở rộng  
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.width_desktop_close                Chiều rộng khi đóng ở style desktop  2 (luôn luôn được trả về)
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.height_desktop_close               Chiều cao khi đóng ở style desktop  2 (luôn luôn được trả về)
@apiSuccess   (Body:)     {int}      chat_tool_properties.config_frame.chat_box_position    Vị trí chatbox (1-9)  
@apiSuccess   (Body:)     {String}   chat_tool_properties.config_frame.bg_header_color      Màu nền header 
@apiSuccess   (Body:)     {String}   chat_tool_properties.config_frame.text_header_color    Màu chữ header  
@apiSuccess   (Body:)     {String}   chat_tool_properties.config_frame.bg_chat_color_supporter  Màu nền tin nhắn supporter
@apiSuccess   (Body:)     {String}   chat_tool_properties.config_frame.bg_chat_color_visitor    Màu nền tin nhắn khách hàng
@apiSuccess   (Body:)     {String}   chat_tool_properties.config_frame.txt_message_color_supporter  Màu chữ tin nhắn khách hàng  
@apiSuccess   (Body:)     {String}   chat_tool_properties.config_frame.txt_message_color_visitor    Màu chũ tin nhắn supporter 
@apiSuccess   (Body:)     {Object}   chat_tool_properties.frame_setting        Cấu hình nội dung khung chat.
@apiSuccess   (Body:)     {String}   chat_tool_properties.frame_setting.notify               Thông báo khi bật chatbox tối đa 100 kí tự 
@apiSuccess   (Body:)     {Boolean}  chat_tool_properties.frame_setting.require_info_user    Yêu cầu điền vào mẫu để bắt đầu chat  
@apiSuccess   (Body:)     {String}   chat_tool_properties.frame_setting.notify_info          Thông báo khi điền thông tin tối đa 100 kí tự
@apiSuccess   (Body:)     {int}      chat_tool_properties.frame_setting.name                 Setting tên ( 0-là không hỏi, 1- không bắt buộc, 2-bắt buộc)  
@apiSuccess   (Body:)     {int}      chat_tool_properties.frame_setting.email                Setting email ( 0-là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiSuccess   (Body:)     {int}      chat_tool_properties.frame_setting.phone_number         Setting phone_number ( 0- là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiSuccess   (Body:)     {object}   chat_tool_properties.greeting_setting     Cấu hình lời chào
@apiSuccess   (Body:)     {String}   chat_tool_properties.greeting_setting.popup_message        Lời chào
@apiSuccess   (Body:)     {int}      chat_tool_properties.greeting_setting.time_popup         	Thời gian pop up lời chào


@apiParamExample    {json}  Success-Response:
    HTTP/1.1 200 OK
{
  "List_Domain": [
    {
      "_id": "5db0209109eebf9fd2ce790e",
       "chat_tool_properties": {      
        "notify_frame":{
          "disable_bell_notifi":true,
          "disable_web_notifi":true
          "hide_offline":false,
          "hide_mobile":false
          "disable_upload":false,
          "disable_sticker":false,     
          "disable_review_chat":false
          },
        "config_frame":{
          "style_mobile":1,
          "style_desktop":2,
          "width":400,
          "height":600,
          "width_desktop_close":32,
          "height_desktop_close":149,
          "chat_box_position":9,
          "bg_header_color":"#000",
          "text_header_color":"white",
          "bg_chat_color_supporter": "red",
          "bg_chat_color_visitor":"white",
          "txt_message_color_supporter":"white",
          "txt_message_color_visitor":"#000"
        },
        "frame_setting":{
          "notify":"Hãy gửi tin nhắn và chúng tôi sẽ cố gắng trả lời bạn trong thời gian ngắn nhất"           
          "require_info_user": true,
          "notify_info":"xin vui lòng điền vào thông tin dưới đây để chúng tôi có thể dễ dàng liên lạc với bạn"
          "name":1,
          "email":1,
          "phone_number":1                        
         },
         "greeting_setting":{
          "popup_message":"Bạn có hứng thú với sản phẩm này không ?",
          "time_popup":13
         }   
     }
      "created_time": "Wed, 23 Oct 2019 16:42:41 GMT",
      "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "link_site": "kaowd,awd",
      "login_required": true,
      "max_user_support": 3,
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name_site": "lakwdo",
      "script": "<script src='https://test1.mobio.vn/chat/mo.chat.min.js?v=1.0'></script><script>(function(){mochatjs.init({domain_id:'5db0209109eebf9fd2ce790e'});})();</script>",
      "status": 1,
      "updated_time": "Wed, 23 Oct 2019 17:04:33 GMT",
      "updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
    },
    {
      "_id": "5db162c209eebf9fd2cea996",
      "chat_tool_properties": {
        "config_frame": {
          "bg_chat_color_supporter": "red",
          "bg_chat_color_visitor": "blue",
          "bg_header_color": "#000",
          "chat_box_position": 9,
          "height": 600,
          "style_desktop": 1,
          "style_mobile": 1,
          "text_header_color": "white",
          "txt_message_color_supporter": "white",
          "txt_message_color_visitor": "#000",
          "width": 400,
          "width_desktop_close":32,
          "height_desktop_close":149,
        },
        "frame_setting": {
          "email": 1,
          "name": 1,
          "notify": "Hãy gửi tin nhắn và chúng tôi sẽ cố gắng trả lời bạn trong thời gian ngắn nhất",
          "notify_info": "xin vui lòng điền vào thông tin dưới đây để chúng tôi có thể dễ dàng liên lạc với bạn",
          "phone_number": 1,
          "require_info_user": true
        },
        "greeting_setting": {
          "popup_message": "Bạn có hứng thú với sản phẩm này không ?",
          "time_popup": 13
        },
        "notify_frame": {
          "disable_bell_notifi": true,
          "disable_review_chat": false,
          "disable_sticker": false,
          "disable_upload": false,
          "hide_mobile": false,
          "hide_offline": false,
          "disable_web_notifi": true
        }
      },
      "created_time": "Thu, 24 Oct 2019 15:37:22 GMT",
      "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "link_site": "mobio.vn",
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name_site": "Mobio",
      "script": "<script src='https://test1.mobio.vn/chat/mo.chat.min.js?v=1.0'></script><script>(function(){mochatjs.init({domain_id:'5db162c209eebf9fd2cea996'});})();</script>",
      "status": 1,
      "updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
    }
  ],
  "code": "001",
  "lang": "vi",
  "message": "Request thÃ nh cÃ´ng."
}
"""
************************Lấy danh sách domain đang quản lý **********
* version: 1.0.0                                                   *
********************************************************************
"""
@api {get} /domain/list Lấy danh sách domain đang quản lý 
@apiDescription Lấy danh sách domain đang quản lý  không lấy domain đã close
@apiGroup SettingBackend
@apiVersion 1.0.0
@apiName /domain/list

@apiParam   (Params) {Int} [id] filter id tìm theo id domain

@apiUse 404
@apiUse 412
@apiUse 500
@apiUse 401

@apiSuccess       {String}   _id                  Domain id
@apiSuccess       {Object}   chat_tool_properties Thông tin cấu hình chat tool.
@apiSuccess       {Object}   notify_frame         Thông tin cấu hình thông báo và ẩn hiện khung chat.
@apiSuccess       {Boolean}   bell_notifi          Âm thanh thông báo.
@apiSuccess       {Boolean}   web_notifi           Thông báo trên cửa sổ trình duyệt.
@apiSuccess       {Boolean}   hide_offline         Ẩn khung chat khi offline
@apiSuccess       {Boolean}   hide_mobile          Ẩn khung chat trên di động
@apiSuccess       {Boolean}   disable_upload       Tắt tính năng tải file
@apiSuccess       {Boolean}   disable_sticker      Tắt tính năng dùng biểu tượng cảm xúc
@apiSuccess       {Boolean}   disable_review_chat  Tắt đánh giá hội thoại      
@apiSuccess       {Object}   config_frame         Thông tin cấu hình hiển thị khung chat.
@apiSuccess       {int}      style_mobile         Dạng hiển thị trên mobile  
@apiSuccess       {int}      style_destop         Dạng hiển thị trên destop 
@apiSuccess       {int}      width                Chiều rộng khi mở rộng  
@apiSuccess       {int}      height               Chiều cao khi mở rộng  
@apiSuccess       {int}      chat_box_position    Vị trí chatbox (1-9)  
@apiSuccess       {String}   bg_header_color      Màu nền header 
@apiSuccess       {String}   text_header_color    Màu chữ header  
@apiSuccess       {String}   bg_chat_color_supporter  Màu nền tin nhắn supporter
@apiSuccess       {String}   bg_chat_color_visitor    Màu nền tin nhắn khách hàng
@apiSuccess       {String}   txt_message_color_supporter  Màu chữ tin nhắn khách hàng  
@apiSuccess       {String}   txt_message_color_visitor    Màu chũ tin nhắn supporter 
@apiSuccess       {Object}   frame_setting        Cấu hình nội dung khung chat.
@apiSuccess       {String}   notify               Thông báo khi bật chatbox tối đa 100 kí tự 
@apiSuccess       {Boolean}  require_info_user    Yêu cầu điền vào mẫu để bắt đầu chat  
@apiSuccess       {String}   notify_info          Thông báo khi điền thông tin tối đa 100 kí tự
@apiSuccess       {int}      name                 Setting tên ( 0- là không hỏi, 1- không bắt buộc, 2-bắt buộc)  
@apiSuccess       {int}      email                Setting email ( 0- là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiSuccess       {int}      phone_number         Setting phone_number ( 0- là không hỏi, 1- không bắt buộc, 2-bắt buộc)
@apiSuccess       {object}   greeting_setting     Cấu hình lời chào
@apiSuccess       {String}   popup_messege        Lời chào
@apiSuccess       {int}      time_popup         	Thời gian pop up lời chào


@apiParamExample    {json}  Success-Response:
    HTTP/1.1 200 OK
{
  "List_Domain": [
    {
      "_id": "5db0209109eebf9fd2ce790e",
       "chat_tool_properties": {      
        "notify_frame":{
          "bell_notifi":true,
          "web_notifi":true
          "hide_offline":false,
          "hide_mobile":false
          "disable_upload":false,
          "disable_sticker":false,     
          "disable_review_chat":false
          },
        "config_frame":{
          "style_mobile":1,
          "style_destop":2,
          "width":400,
          "height":600,
          "chat_box_position":9,
          "bg_header_color":"#000",
          "text_header_color":"white",
          "bg_chat_color_supporter": "red",
          "bg_chat_color_visitor":"white",
          "txt_message_color_supporter":"white",
          "txt_message_color_visitor":"#000"
        },
        "frame_setting":{
          "notify":"Hãy gửi tin nhắn và chúng tôi sẽ cố gắng trả lời bạn trong thời gian ngắn nhất"           
          "require_info_user": true,
          "notify_info":"xin vui lòng điền vào thông tin dưới đây để chúng tôi có thể dễ dàng liên lạc với bạn"
          "name":1,
          "email":1,
          "phone_number":1                        
         },
         "greeting_setting":{
          "popup_messege":"Bạn có hứng thú với sản phẩm này không ?",
          "time_popup":13
         }   
     }
      "created_time": "Wed, 23 Oct 2019 16:42:41 GMT",
      "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "link_site": "kaowd,awd",
      "login_required": true,
      "max_user_support": 3,
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name_site": "lakwdo",
      "script": "<script src='https://test1.mobio.vn/chat/mo.chat.min.js?v=1.0'></script><script>(function(){mochatjs.init({domain_id:'5db0209109eebf9fd2ce790e'});})();</script>",
      "status": 1,
      "updated_time": "Wed, 23 Oct 2019 17:04:33 GMT",
      "updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
    },
    {
      "_id": "5db162c209eebf9fd2cea996",
      "chat_tool_properties": {
        "config_frame": {
          "bg_chat_color_supporter": "red",
          "bg_chat_color_visitor": "blue",
          "bg_header_color": "#000",
          "chat_box_position": 9,
          "height": 600,
          "style_destop": 2,
          "style_mobile": 1,
          "text_header_color": "white",
          "txt_message_color_supporter": "white",
          "txt_message_color_visitor": "#000",
          "width": 400
        },
        "frame_setting": {
          "email": 1,
          "name": 1,
          "notify": "Hãy gửi tin nhắn và chúng tôi sẽ cố gắng trả lời bạn trong thời gian ngắn nhất",
          "notify_info": "xin vui lòng điền vào thông tin dưới đây để chúng tôi có thể dễ dàng liên lạc với bạn",
          "phone_number": 1,
          "require_info_user": true
        },
        "greeting_setting": {
          "popup_messege": "Bạn có hứng thú với sản phẩm này không ?",
          "time_popup": 13
        },
        "notify_frame": {
          "bell_notifi": true,
          "disable_review_chat": false,
          "disable_sticker": false,
          "disable_upload": false,
          "hide_mobile": false,
          "hide_offline": false,
          "web_notifi": true
        }
      },
      "created_time": "Thu, 24 Oct 2019 15:37:22 GMT",
      "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "link_site": "mobio.vn",
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name_site": "Mobio",
      "script": "<script src='https://test1.mobio.vn/chat/mo.chat.min.js?v=1.0'></script><script>(function(){mochatjs.init({domain_id:'5db162c209eebf9fd2cea996'});})();</script>",
      "status": 1,
      "updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
    }
  ],
  "code": "001",
  "lang": "vi",
  "message": "Request thÃ nh cÃ´ng."
}

"""
************************ Change status domain *****************************
* version: 1.0.1                                                   *
********************************************************************
"""
@api {post} /domain/close Thay đổi trạng thái kết nối domain
@apiDescription  Thay đổi trạng thái kết nối domain
@apiGroup SettingBackend
@apiVersion 1.0.1
@apiName /domain/close

@apiUse 401
@apiUse 404
@apiUse 412
@apiUse 500

@apiParam   (Body:)     {String} _id     Domain id


@apiParamExample    {json}  Body example:
{
    "_id":"5db0209109eebf9fd2ce790e"
}

@apiSuccess       {int}      status    Trạng thái của domain <code>1-OPEN,2-CLOSE</code>
@apiSuccess       {string}   domain_id id của domain
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "domain_id": "5da3e24363b44dc104867cb9",
  "lang": "vi",
  "message": "Request thÃ nh cÃ´ng.",
  "status": 2
}

"""
************************ Close domain *****************************
* version: 1.0.0                                                   *
********************************************************************
"""
@api {post} /domain/close Gỡ kết nối domain
@apiDescription  Gỡ kết nối domain
@apiGroup SettingBackend
@apiVersion 1.0.0
@apiName /domain/close

@apiUse 401
@apiUse 404
@apiUse 412
@apiUse 500

@apiParam   (Body:)     {String} _id     Domain id


@apiParamExample    {json}  Body example:
{
    "_id":"5db0209109eebf9fd2ce790e"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "Gỡ domain thành công"
}
"""
************************ Get image domain **************************
* version: 1.0.0                                                   *
********************************************************************
"""
@api {get} /domain/image/<domain_id> Lấy ảnh avatar domain
@apiDescription  Lấy ảnh avatar domain
@apiGroup SettingBackend
@apiVersion 1.0.0
@apiName /domain/image/<domain_id>

@apiUse 401
@apiUse 404
@apiUse 412
@apiUse 500


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
}
"""
************************ Cấu hình kết nối khung chat ***************
* version: 2.0.0                                                   *
********************************************************************
"""
@api {post} /domain/connection Cấu hình kết nối khung chat
@apiDescription Cấu hình kết nối khung chat
@apiGroup SettingBackend
@apiVersion 2.0.0
@apiName /domain/connection

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse 401

@apiSuccess   (Body:)	  {Object}   connection_config             Cấu hình kết nối khung chat
@apiSuccess   (Body:)     {String}   connection_config.facebook  Cấu hình kết nối cho facebook
@apiSuccess   (Body:)	  {String}   merchant_id             ID của merchant
@apiSuccess   (Body:)	  {String}   id_config             ID của website 

@apiParamExample    {json}  Body example:
{
	"merchant_id": "uuid",
	"connection_config":{
		"facebook": "123983749120"
		},
	"id_config": "uuid"
}

@apiSuccess       {Object}      connection_config    Cấu hình kết nối khung chat
@apiSuccess       {string}   connection_config.facebook  Cấu hình kết nối cho facebook
@apiSuccess     {String}   merchant_id             id của merchant


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
	"code": 200,
	"data":{
  			"connection_config":{
  					"facebook": "123983749120"
  					},
  			"merchant_id": "uuid",
  			"id_config": "uuid"
  		},
  	"message": "Request thành công"
	
}
"""
************************** Summary domain **************************
* version: 1.0.0                                                   *
********************************************************************
"""
@api {get} /domain/summary Thống kê domain theo domain_type
@apiDescription  Thống kê domain theo domain_type
@apiGroup SettingBackend
@apiVersion 1.0.0
@apiName /domain/summary

@apiUse 401
@apiUse 404
@apiUse 412
@apiUse 500

@apiParam   (Params) {String} [type] kiểu danh sách <code>all-lấy cả domain đang tắt; default- lấy domain đang hoạt động</code> mặc định lấy default nếu không tryền vào </code>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "domain_type": "WEBSITE",
            "total": 254
        },
        {
            "domain_type": "MOBILE_APP",
            "total": 1
        }
    ],
    "lang": "vi",
    "message": "Request thành công."
}
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 30/05/2023
"""

"""
@apiDefine GetListTaskByModuleResponse

@apiParam   (BODY:) {array}     source                   Source (TICKET, SALE, COMPANY, PROFILE) 
@apiParam   (BODY:) {string}    id_related_to            Id của liên quan tới (VD: source là Ticket thì id_related_to là id của ticket đó)
@apiParam   (BODY:) {string}    [keywords]               Keywords
@apiParam   (BODY:) {array}     [task_type]              Kiểu công việc
@apiParam   (BODY:) {string}    [assign_ids]            Người được giao
@apiParam   (BODY:) {string}    [task_status]            Trạng thái của công việc
@apiParam   (BODY:) {float}     [start_time]              Thời gian bắt đầu tìm kiếm (format: %Y-%m-%dT%H:%M:%SZ)
@apiParam   (BODY:) {float}     [end_time]                Thời gian kết thúc tìm kiếm (format: %Y-%m-%dT%H:%M:%SZ)
@apiParam   (BODY:) {float}     [complete_time_start]     Thời gian hoan thành thuc te bắt đầu tìm kiếm (format: %Y-%m-%dT%H:%M:%SZ)
@apiParam   (BODY:) {float}     [complete_time_end]       Thời gian hoan thanh thuc te kết thúc tìm kiếm (format: %Y-%m-%dT%H:%M:%SZ)
@apiParam   (BODY:) {array}     [account_id]             Người được nhắc đến
@apiParam   (BODY:) {string}    [created_by]             Người tạo
@apiParam   (BODY:) {object}    [deadline]               Thời hạn hoàn thành.
@apiParam   (BODY:) {string}    [deadline.start_time]    Thời gian bắt đầu của thời hạn hoàn thành
@apiParam   (BODY:) {string}    [deadline.end_time]      Thời gian kết thúc của thời hạn hoàn thành


@apiParam   (BODY:) {array}     [status_process]         Trạng thái xử lý
@apiParam   (Param:) {string}   after_token              Mặc định là None
@apiParam   (Param:) {string}   per_page                 Mặc định là 10




@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
                                        <li><code>page:</code>Vị trí page request</li>
                                        <li><code>per_page:</code>Số lượng phần tử trên một page</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after_token]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before_token]    Token để lấy dữ liệu trang trước đó.
@apiSuccess     {Object}    [paging..per_page]          Số lượng phần tử trên 1 page
@apiSuccess     {Object}    [paging..page_count]        Tổng số page
@apiSuccess     {Object}    [paging..total_count]       Tổng số phần tử

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "account_mentions": [],
            "assign_ids": [
                "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            ],
            "assign_type": "STAFF",
            "comment_count": 1,
            "company_ids": [],
            "complete_time": null,
            "compulsory_completion": false,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": **********.00594,
            "deadline_end_time": **********,
            "deadline_start_time": **********,
            "deadline_type": "unspecified_time",
            "description": "<div>fvfgvfv</div> <style></style>",
            "description_attachment_ids": [],
            "file_usage_status": "ARCHIVED",
            "id": "63e9f6210d48aed9df718f6d",
            "code_task": "IOQPAS1233",
            "task_parent_id": "",
            "is_new": 0,
            "keywords": "fff",
            "mentions": [],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config_channels": [
                "email"
            ],
            "notification_config_type": "NO_REMINDER",
            "object_id": "63e766581c5d7500126d8e9d",
            "order_ids": [],
            "planned_start_time": null,
            "priority_level": 1,
            "profile_ids": [],
            "related_to_not_delete": {
                "company_ids": [],
                "order_ids": [],
                "profile_ids": [],
                "ticket_ids": [
                    "63e766581c5d7500126d8e9d"
                ]
            },
            "source": "TICKET",
            "status_process": 4,
            "tags_id": [],
            "task_type": "GENERAL",
            "ticket_ids": [
                "63e766581c5d7500126d8e9d"
            ],
            "time_check_status": 1676282340,
            "title": "fff",
            "type_create": "MANUAL",
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": 1676282353.09
        }
    ],
    "lang": "en",
    "message": "request successful.",
    "scroll_size": 1,
    "paging": {
        "cursors": {
            "after_token": "WzE2ODU1NTk2MDAuMCwgIjY0NzcxN2Y0YjVmNzVlMDAwZTU1MTY5MiJd",
            "before_token": "********************************************************"
        },
        "page_count": 5,
        "per_page": 10,
        "total_count": 41
    }
}
"""
"""
@apiDefine ResponseDetailTaskMobileV2
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin file Upload

@apiSuccess {array}             data.account_mentions           Danh sách người được nhắc đến trong công việc
@apiSuccess {array}             data.assign_ids                 Danh sách <code>ID</code> định danh đối tượng được assign 
@apiSuccess {string}             data.assign_type               Kiểu đối tượng được assgin
                                                                <ul>
                                                                    <li><code>STAFF:</code> nhân viên</li>
                                                                    <li><code>TEAM:</code> team</li>
                                                                </ul>
@apiSuccess {array}             data.attachment_ids             Danh sách <code>ID</code> các file được đính kèm với Task
@apiSuccess {array}             data.attachments                Danh sách thông tin file đính kèm
@apiSuccess {Array}             data.attachments.capacity     Dung lượng của file
@apiSuccess {Array}             data.attachments.format_file  Định dạng file
@apiSuccess {Array}             data.attachments.id           <code>ID</code> của file đính kèm
@apiSuccess {Array}             data.attachments.title        Tên file đính kèm
@apiSuccess {Array}             data.attachments.url          Link truy cập file đính kèm
@apiSuccess {bool}             data.calendar_sync_status          Trang thái sync task lên calendar 
@apiSuccess {string}             data.code_task               Mã công việc
@apiSuccess {int}             data.comment_count              Số lượng comment  của task
@apiSuccess {array}             data.company_ids              Danh sách các <code>ID</code> công ty liên quan tới 
@apiSuccess {string}             data.complete_time                 Thời gian hoàn thành task
@apiSuccess {bool}             data.compulsory_completion
@apiSuccess {string}             data.created_by                ID người tạo task
@apiSuccess {string}             data.created_time              Thời gian tạo
@apiSuccess {string}             data.deadline_end_time         Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                </ul>
@apiSuccess {string}             data.deadline_start_time
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                </ul>
@apiSuccess {string}             data.deadline_type
                                                                <ul>
                                                                    <li><code>time_slot</code>: Khung giờ</li>
                                                                    <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                </ul>
@apiSuccess {string}             data.description                   Nội dung của task 
@apiSuccess {array}             data.description_attachment_ids     Danh sách <code>ID</code> các file được đính kèm trong nội dung của Task
@apiSuccess {string}             data.file_usage_status             Trạng thái lưu trữ file của công việc 
@apiSuccess {string}             data.id                                <code>ID</code> task
@apiSuccess {int}             data.is_new                               Field thể hiện task mới tạo
@apiSuccess {string}             data.keywords
@apiSuccess {array}             data.mentions                           Danh sách người được mentions
@apiSuccess {string}             data.merchant_id                       <code>ID</code> merchant
@apiSuccess {array}             data.notification_config_channels       Kênh thông báo
@apiSuccess {string}             data.notification_config_time_unit     Đơn vị của thời gian cấu hình. Áp dụng với type: <code>ADVANCE_REMINDER</code>
                                                                        <ul>
                                                                            <li><code>minute:</code> đơn vị tính là phút</li>
                                                                            <li><code>hour:</code> đơn vị tính là giờ</li>
                                                                            <li><code>year:</code> đơn vị tính là năm</li>
                                                                        <ul> 
@apiSuccess {string}             data.notification_config_time_value    Giá trị thời gian thông báo
@apiSuccess {string}             data.notification_config_type          Kiểu thông báo
                                                                    <ul>
                                                                        <li><code>NO_REMINDER:</code>Không nhắc nhở</li>
                                                                        <li><code>ADVANCE_REMINDER:</code>Nhắc nhở trước</li>
                                                                        <li><code>TIMELY_REMINDER:</code>nhắc nhở đúng thời hạn</li>
                                                                        <li><code>OPTION:</code>Tùy chọn</li>
                                                                    </ul>
@apiSuccess {string}             data.object_id                         Định danh của phần tạo tác. Ví dụ ở SALE thì sẽ là định danh của đơn hàng.
@apiSuccess {array}             data.order_ids                          Danh sách các <code>ID</code> order liên quan tới
@apiSuccess {string}             data.planned_start_time                Thời gian dự kiến bắt đầu 
@apiSuccess {int}             data.priority_level                       Mức độ ưu tiên
                                                                        <ul>
                                                                                <li><code>1:</code> Cao</li>
                                                                                <li><code>2:</code> Trung bình</li>
                                                                                <li><code>3:</code> Thấp</li>
                                                                        </ul>
@apiSuccess {array}             data.profile_ids                        Danh sách các <code>ID</code> profile liên quan tới
@apiSuccess {object}             data.related_to                        Thông tin liên quan tới
@apiSuccess {object}             data.related_to.profile_ids            Danh sách profile liên quan tới
@apiSuccess {object}             data.related_to.order_ids              Danh sách đơn hàng liên quan tới 
@apiSuccess {object}             data.related_to.company_ids            Danh sách công ty liên quan tới
@apiSuccess {object}             data.related_to.ticket_ids             Danh sách ticket liên quan tới
@apiSuccess {object}             data.related_to_not_delete             Thông tin liên quan tới không được xóa
@apiSuccess {object}             data.related_to_not_delete.profile_ids            Danh sách profile liên quan tới
@apiSuccess {object}             data.related_to_not_delete.order_ids              Danh sách đơn hàng liên quan tới 
@apiSuccess {object}             data.related_to_not_delete.company_ids            Danh sách công ty liên quan tới
@apiSuccess {object}             data.related_to_not_delete.ticket_ids             Danh sách ticket liên quan tới
@apiSuccess {bool}               data.required_push_socket_overdue        Bắt buộc push socket khi task quá hạn
@apiSuccess {string}             data.source                            Định danh nguồn tạo Task
@apiSuccess {int}             data.status_process
                                                        <ul>
                                                            <li><code>1:</code> Chưa thực hiện </li>
                                                            <li><code>2:</code> Đang thực hiện </li>
                                                            <li><code>3:</code> Đang chờ </li>
                                                            <li><code>4:</code> Tạm hoãn </li>
                                                            <li><code>5:</code> Hoàn thành </li>
                                                        </ul> 
@apiSuccess {string}             data.tab                               tab trong giao diện
@apiSuccess {object}             data.position                          Thông tin vị trí
@apiSuccess {float}             data.position.latitude                  Thông tin vĩ độ
@apiSuccess {float}             data.position.longitude                 Thông tin kinh độ

@apiSuccess {string}             data.tab                               tab trong giao diện
@apiSuccess {array}             data.tag_ids                            Danh sách các <code>ID</code> tag liên quan tới
@apiSuccess {string}             data.task_parent_id                    <code>ID</code> công việc cha 
@apiSuccess {string}             data.task_type                         Kiểu công việc
@apiSuccess {array}             data.ticket_ids                         Danh sách các <code>ID</code> ticket liên quan tới
@apiSuccess {string}             data.time_change_status                 Thời gian thay đổi trạng thái task
@apiSuccess {string}             data.time_check_status                 Phân loại task quá hạn theo field này    
@apiSuccess {string}             data.title                               Tên task
@apiSuccess {string}             data.type_create                        Kiểu tạo task
@apiSuccess {string}             data.updated_by                        ID người update task gần nhất
@apiSuccess {string}             data.updated_time                      Thời gian update task gần nhất
@apiSuccess {Object}             data.config_repeat                        Cấu hình có lặp task không?
@apiSuccess   {Int=0,1}          data.config_repeat.is_use                 Có sử dụng cấu hình không?
                                                                            <ul>
                                                                                <li><code>0</code>: không sử dụng</li>
                                                                                <li><code>1</code>: sử dụng</li>
                                                                            </ul>
@apiSuccess {object}          data.config_repeat.start                   Cấu hình bắt đầu lặp task
@apiSuccess {String}          data.config_repeat.start.type              Kiểu lặp
                                                                            <ul>
                                                                                <li><code>day</code>: Ngày</li>
                                                                                <li><code>week</code>: Tuần</li>
                                                                                <li><code>month</code>: Tháng</li>
                                                                                <li><code>year</code>: Năm</li>
                                                                            </ul>
@apiSuccess {Array}          data.config_repeat.start.values            Cấu hình thông tin lặp vào (Ví dụ: thứ 2, thứ 3)
                                                                            <ul>
                                                                                <li> Case: type=<code>day</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                                <li> Case: type=<code>week</code> thì <code>values: danh sách các thứ trong tuần - ví dụ ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]</code></li>
                                                                                <li> Case: type=<code>month</code> thì <code>values: danh sách các ngày trong tháng (Mặc định là 31 ngày) - ví dụ [1,2,3,4,5,6,7]</code></li>
                                                                                <li> Case: type=<code>year</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                            </ul>
@apiSuccess {int}           data.config_repeat.start.frequency           Tần suất lặp
@apiSuccess {object}          data.config_repeat.stop                   Cấu hình dừng lặp task
@apiSuccess {String}          data.config_repeat.stop.type              Loại dừng
                                                                            <ul>
                                                                                <li><code>never</code>: Không bao giờ dừng</li>
                                                                                <li><code>on_the_day</code>: Vào ngày</li>
                                                                                <li><code>after_number_times</code>: Sau số lần</li>
                                                                            </ul>
@apiSuccess {Array}          data.config_repeat.stop.value             Giá trị của type tương ứng
                                                                            <ul>
                                                                                <li>Với type=<code>never</code>: thì value để là None hoặc string rỗng</li>
                                                                                <li>Với type=<code>on_the_day</code>: thì value string và có format sau: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                                <li>Với type=<code>after_number_times</code>: thì value để số</li>
                                                                            </ul>
"""

"""
@apiDefine PagingTaskMobile
@apiVersion 2.0.0
@apiParam   (Query:)    {Number}    [per_page] Số phần tử trên một page.<br/> Example: <code>&per_page=5</code>
@apiParam   (Query:)    {String}    [after_token] Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)    {String}    [before_token] Token để request lấy dữ liệu trang trước đó. Nếu trong danh sách tham số gửi lên có <code>after_token</code> thì ưu tiên xử lý <code>after_token</code>
  
@apiSuccess {Paging}            paging               Thông tin phân trang.
@apiSuccess {Integer}           paging.per_page      Số lượng item được trả ra ở mỗi trang
@apiSuccess {Integer}           paging.total_count   Tổng số lượng phần tử 
@apiSuccess {String}            paging.token         Chuỗi token được tạo để sử dụng lấy ra những dữ liẹu tiếp đó của lần request tiếp theo
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        'per_page': 1,
        'total_count': 100,
        'token': "hj1233j3l3l3l3l10mamsmxxxx"
    }
}
"""

"""
@apiDefine DetailTaskv2Response

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "_id": "641ab667fb252c0395897514",
        "id": "641ab667fb252c0395897514",
        "assign_ids": [
            "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        ],
        "assign_type": "STAFF",
        "attachment_ids": [],
        "calendar_sync_status": null,
        "company_ids": [],
        "complete_time": null,
        "time_change_status": "2023-03-22T08:03Z",
        "compulsory_completion": false,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2023-03-22T08:03Z",
        "deadline_end_time": "2023-03-22T08:03Z",
        "deadline_start_time": "2023-03-22T08:03Z",
        "deadline_type": "time_slot",
        "description": "<div>fnosnado</div>",
        "description_attachment_ids": [],
        "file_usage_status": "ARCHIVED",
        "follow_task": {},
        "id": "641ab667fb252c0395897514",
        "is_new": 1,
        "keywords": "anh_2",
        "mentions": [],
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "notification_config_channels": [
            "email"
        ],
        "notification_config_time_unit": null,
        "notification_config_time_value": null,
        "notification_config_type": "NO_REMINDER",
        "object_id": "64186cdcd0d9b300124f6178",
        "order_ids": [],
        "planned_start_time": "2023-03-22T08:03Z",
        "priority_level": 1,
        "profile_ids": [],
        "source": "TICKET",
        "status_process": 1,
        "tag_ids": [],
        "task_type": "GENERAL",
        "task_parent_id":"",
        "ticket_ids": [
            "64186cdcd0d9b300124f6178"
        ],
        "time_check_status": "2023-03-22T08:03Z",
        "title": "anh_2",
        "type_create": "MANUAL",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_time": "2023-03-22T08:03Z",
        "position": {
            "latitude":  40.712776,
            "longitude": -74.005974,
        }
    }
}
"""

"""
@apiDefine ListTaskv2ParamAndResponse

@apiParam (Params:) {string} per_page               Số lượng item trên 1 trang. per_page = -1 nếu muốn lấy ra tất cả item
@apiParam (Params:) {string} after_token            Token được trả ra ở lần request trước đó
                                                    (Dùng để làm mốc cho lượt query tiếp theo đề lấy ra những phần tử tiếp theo)

@apiParam   (BODY:) {string}                         [keywords]            Từ khóa cần tìm kiếm theo title
@apiParam   (BODY:) {string=UPCOMING,DONE,OVER_DUE}  status                Trạng thái của Task. 
                                                                            <ul>
                                                                                <li><code>UPCOMING:</code>Sắp tới</li>
                                                                                <li><code>DONE:</code>Hoàn thành</li>
                                                                                <li><code>OVER_DUE:</code>Quá hạn</li>
                                                                            </ul>

@apiParam   (BODY:) {string=GENERAL,CALL,SENT_EMAIL,MEETING}    task_type           Kiểu công việc
                                                                        <ul>
                                                                            <li><code>GENERAL:</code>Chung</li>
                                                                            <li><code>SENT_EMAIL:</code>Gửi email</li>
                                                                            <li><code>CALL:</code>Gọi điện</li>
                                                                            <li><code>MEETING:</code>Hẹn gặp</li>
                                                                        </ul>

@apiParam   (BODY:) {string}                          [source]              Nguồn tạo công việc  
@apiParam   (BODY:) {Datetime}                        [start_time]       Thời gian bắt đầu tìm kiếm. Định dạng: <code>%Y-%m-%dT%H:%M:%SZ</code>
@apiParam   (BODY:) {Datetime}                        [end_time]         Thời gian kết thúc tìm kiếm. Định dạng: <code>%Y-%m-%dT%H:%M:%SZ</code> 
@apiParam   (BODY:) {Integer}                         [priority_level]     Mức độ ưu tiên
                                                        <ul>
                                                            <li><code>1:</code> mức độ ưu tiên cao </li>
                                                            <li><code>2:</code> mức độ ưu tiên thấp </li>
                                                            <li><code>3:</code> mức độ ưu tiên trung bình </li>
                                                        </ul>
@apiParam   (BODY:) {Array}                           [assignee_ids]     Danh sách người được giao công việc
@apiParam   (BODY:) {Array}                           [brand_ids]     Danh sách thương hiệu
@apiParam   (BODY:) {String}                          object_id    Định danh của thành phân cần lấy danh sách task. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {array}                           account_mentions   Danh sách <code>ID</code> người được mentions

@apiParamExample {json} Body example
{
    "keywords": "gọi điện cho khách hàng",
    "status": "COMING",
    "due_date": "",
    "current_time": "2021-10-03T08:10Z",
    "type": ["GENERAL", "CALL", "SENT_EMAIL"],
    "assignee_ids": [],
    "brand_ids": [],
    "start_time": "2021-01-01T18:01:01Z",
    "end_time": "2021-01-01T18:01:01Z"
    "source": "SALE",
    "object_id": "82f95839-5be5-4a1b-bf20-a1f3a14025e3"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách tương ứng các file.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "_id": "641ab667fb252c0395897514",
            "id": "641ab667fb252c0395897514",
            "assign_ids": [
                "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            ],
            "assign_type": "STAFF",
            "attachment_ids": [],
            "calendar_sync_status": null,
            "company_ids": [],
            "complete_time": null,
            "time_change_status": "2023-03-22T08:03Z",
            "compulsory_completion": false,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2023-03-22T08:03Z",
            "deadline_end_time": "2023-03-22T08:03Z",
            "deadline_start_time": "2023-03-22T08:03Z",
            "deadline_type": "time_slot",
            "description": "<div>fnosnado</div>",
            "description_attachment_ids": [],
            "file_usage_status": "ARCHIVED",
            "follow_task": {},
            "id": "641ab667fb252c0395897514",
            "is_new": 1,
            "keywords": "anh_2",
            "mentions": [],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config_channels": [
                "email"
            ],
            "notification_config_time_unit": null,
            "notification_config_time_value": null,
            "notification_config_type": "NO_REMINDER",
            "object_id": "64186cdcd0d9b300124f6178",
            "order_ids": [],
            "planned_start_time": "2023-03-22T08:03Z",
            "priority_level": 1,
            "profile_ids": [],
            "source": "TICKET",
            "status_process": 1,
            "tag_ids": [],
            "task_type": "GENERAL",
            "task_parent_id": "",
            "ticket_ids": [
                "64186cdcd0d9b300124f6178"
            ],
            "time_check_status": "2023-03-22T08:03Z",
            "title": "anh_2",
            "type_create": "MANUAL",
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": "2023-03-22T08:03Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------- Danh sách Task theo bộ lọc -----------
"""
@api {POST} {domain}/task/mobile/api/v2.0/tasks/action/filter              Lấy danh sách Task
@apiGroup Mobile Task
@apiVersion 2.0.0
@apiName ListTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse ResponseDetailTaskMobileV2
@apiUse PagingTaskMobile
@apiUse merchant_id_header

@apiParam (Params:) {string} sort_by               field sort (name, created_time, priority_level, status_process, deadline_end_time)
@apiParam (Params:) {string} sort_order            thứ tự sort (asc/desc)

@apiUse FieldsSelect
@apiUse ListTaskv2ParamAndResponse
"""
# ---------- Danh sách Task theo bộ lọc từ module task-----------
"""
@api {POST}  {domain}/task/mobile/api/v2.0/tasks/actions/list-from-task/filter Lấy danh sách task V2 từ task
@apiGroup Mobile Task
@apiVersion 1.0.0
@apiName GetListTaskFilterFromTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse ParamQueryDisableCheckAccountAccess

@apiUse filters

@apiUse GetListTaskFilterParamAndResponse                                                              
@apiUse paging_tokens

"""

"""
@api {POST} {domain}/task/mobile/api/v2.0/tasks/v2/actions/list-from-other                   Lấy danh sách task theo module
@apiGroup Mobile Task
@apiVersion 1.0.0
@apiName GetListTaskByModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiParam (Params:) {string} sort_by               field sort (name, created_time, priority_level, status_process, deadline_end_time)
@apiParam (Params:) {string} sort_order            thứ tự sort (asc/desc)
@apiUse GetListTaskByModuleResponse
"""

# ---------- Thêm Task ----------------------
"""
@api {POST} {domain}/task/mobile/api/v2.0/tasks                    Thêm Task
@apiGroup Mobile Task
@apiVersion 2.0.0
@apiName AddTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    title                         Tiêu đề của Task
@apiParam   (BODY:) {string}    description                   Nội dung của Task
@apiParam   (BODY:) {Array}     assign_ids                    Định danh đối tượng được Assign
@apiParam   (BODY:) {String=STAFF,TEAM}    assign_type=STAFF                            Kiểu đối tượng được Assign
@apiParam   (BODY:) {string}    notification_config_type      Loại cấu hình
                                                                    <ul>
                                                                        <li><code>NO_REMINDER:</code>Không nhắc nhở</li>
                                                                        <li><code>ADVANCE_REMINDER:</code>Nhắc nhở trước</li>
                                                                        <li><code>TIMELY_REMINDER:</code>nhắc nhở đúng thời hạn</li>
                                                                        <li><code>OPTION:</code>Tùy chọn</li>
                                                                    </ul>
@apiParam   (BODY:) {string}    [notification_config_time_value]      Thời gian cấu hình
                                                                        <ul>
                                                                            <li>Với type là <code>NO_REMINDER:</code>Không cần truyền time_value</li>
                                                                            <li>Với type là <code>ADVANCE_REMINDER:</code>Cần truyền số phút muốn thông báo nhắc nhở trước lên</li>
                                                                            <li>Với type là <code>TIMELY_REMINDER:</code>Không cần truyền time_value</li>
                                                                            <li>Với type là <code>OPTION:</code>Truyền lên ngày giờ được chọn. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                        </ul> 
@apiParam   (BODY:) {string}    [notification_config_time_unit]        Đơn vị của thời gian cấu hình. Áp dụng với type: <code>ADVANCE_REMINDER</code> 
                                                                            <ul>
                                                                                <li><code>minute:</code> đơn vị tính là phút</li>
                                                                                <li><code>hour:</code> đơn vị tính là giờ</li>
                                                                                <li><code>day:</code> đơn vị tính là ngày</li>
                                                                                <li><code>year:</code> đơn vị tính là năm</li>
                                                                            <ul> 


@apiParam   (BODY:) {string=GENERAL,CALL,SENT_EMAIL,MEETING}    task_type           Kiểu công việc
                                                                        <ul>
                                                                            <li><code>GENERAL:</code>Chung</li>
                                                                            <li><code>SENT_EMAIL:</code>Gửi email</li>
                                                                            <li><code>CALL:</code>Gọi điện</li>
                                                                            <li><code>MEETING:</code>Hẹn gặp</li>
                                                                        </ul>

@apiParam (BODY:) {int=1,2,3}     priority_level      Mức độ ưu tiên
                                                    <ul>
                                                        <li><code>1:</code> Cao </li>
                                                        <li><code>2:</code> Trung bình </li>
                                                        <li><code>3:</code> Thấp </li>
                                                    </ul>      

@apiParam (BODY:) {int=1,2,3,4,5}     status_process      Trạng thái xử lý
                                                    <ul>
                                                        <li><code>1:</code> Chưa thực hiện </li>
                                                        <li><code>2:</code> Đang thực hiện </li>
                                                        <li><code>3:</code> Đang chờ </li>
                                                        <li><code>4:</code> Tạm hoãn </li>
                                                        <li><code>5:</code> Hoàn thành </li>
                                                    </ul>   
    
@apiParam   (BODY:) {Array}     [attachment_ids]                Danh sách <code>ID</code> file đính kèm           
@apiParam   (BODY:) {String}    source                        Nguồn tạo. Tức là đang đứng ở Module nào thì sẽ cần truyền vào Module đó. Ví dụ: <code>SALE</code>
@apiParam   (BODY:) {String}    [object_id]                   Định danh source tạo task. <code>Default: </code>task id được tạo. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {boolean}    compulsory_completion        Bắt buộc hoàn thành task(<code>False:</code> Không, <code>True:</code> Có) 
@apiParam   (BODY:) {String}    [planned_start_time]          Dự kiến thời gian bắt đầu
@apiParam   (BODY:) {String}    [calendar_sync_status]        Trạng thái đồng bộ lên calendar

@apiParam   (BODY:) {Array}    [company_ids]                  Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    [ticket_ids]                   Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    [order_ids]                     Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    [profile_ids]                  Danh sách profile liên quan tới.
@apiParam   (BODY:) {Array}    [tag_ids]                      Danh sách tag liên quan tới.
@apiParam   (BODY:) {Object}   relate_to_not_delete                Liên quan tới phần chính nào
@apiParam   (BODY:) {Array}   relate_to_not_delete.company_ids    Danh sách các <code>ID</code> công ty liên quan tới
@apiParam   (BODY:) {Array}   relate_to_not_delete.profile_ids    Danh sách các <code>ID</code> profile liên quan tới
@apiParam   (BODY:) {Array}   relate_to_not_delete.order_ids       Danh sách các <code>ID</code> order liên quan tới
@apiParam   (BODY:) {Array}   relate_to_not_delete.ticket_ids     Danh sách các <code>ID</code> ticket liên quan tới

@apiParam   (BODY:) {Array}    [mentions]                             Danh sách người được mentions
@apiParam   (BODY:) {String}   [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiParam   (BODY:) {String}   [mentions.account_id]                  <code>ID</code> của người được mentions
@apiParam   (BODY:) {Array}    [description_attachment_ids]            Danh sách <code>ID</code> file được upload trong nội dung của Task

@apiParam   (BODY:) {Object}   [follow_task]                  Tạo công việc tiếp nối.
@apiParam   (BODY:) {Int}      [follow_task.status]             Trạng thái tạo công việc tiếp nối.
                                                                 <ul>
                                                                    <li>0: là không tạo task</li>
                                                                    <li>1: là tạo task</li>
                                                                 </ul>
@apiParam   (BODY:) {String}   [follow_task.title]             Tiêu đề công việc tiếp nối
@apiParam   (BODY:) {String}   [follow_task.deadline_type]     Loại thời hạn hoàn thành của công việc tiếp nối
                                                                <ul>
                                                                    <li><code>time_slot</code>: Khung giờ</li>
                                                                    <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                </ul>
@apiParam   (BODY:) {String}    [follow_task.deadline_start_time]      Thời hạn hoàn thành công việc.
                                                                        <ul>
                                                                            <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                            <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                        </ul>
@apiParam   (BODY:) {Object}    [follow_task.deadline_end_time]        Thời hạn hoàn thành công việc.
                                                                        <ul>
                                                                            <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                            <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                        </ul>

@apiParam   (BODY:) {String}    deadline_type            Loại thời hạn hoàn thành của công việc tiếp nối
                                                                        <ul>
                                                                            <li><code>time_slot</code>: Khung giờ</li>
                                                                            <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                        </ul>
@apiParam   (BODY:) {String}    deadline_start_time      Thời hạn Bắt đầu công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                </ul>
@apiParam   (BODY:) {String}    deadline_end_time        Thời hạn Kết thúc công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                </ul>

@apiParam   (BODY:) {Object}           config_repeat                        Cấu hình có lặp task không?
@apiParam   (BODY:) {Int=0,1}          config_repeat.is_use                 Có sử dụng cấu hình không?
                                                                            <ul>
                                                                                <li><code>0</code>: không sử dụng</li>
                                                                                <li><code>1</code>: sử dụng</li>
                                                                            </ul>
@apiParam   (BODY:) {object}          config_repeat.start                   Cấu hình bắt đầu lặp task
@apiParam   (BODY:) {String}          config_repeat.start.type              Kiểu lặp
                                                                            <ul>
                                                                                <li><code>day</code>: Ngày</li>
                                                                                <li><code>week</code>: Tuần</li>
                                                                                <li><code>month</code>: Tháng</li>
                                                                                <li><code>year</code>: Năm</li>
                                                                            </ul>
@apiParam   (BODY:) {Array}          config_repeat.start.values            Cấu hình thông tin lặp vào (Ví dụ: thứ 2, thứ 3)
                                                                            <ul>
                                                                                <li> Case: type=<code>day</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                                <li> Case: type=<code>week</code> thì <code>values: danh sách các thứ trong tuần - ví dụ ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]</code></li>
                                                                                <li> Case: type=<code>month</code> thì <code>values: danh sách các ngày trong tháng (Mặc định là 31 ngày) - ví dụ [1,2,3,4,5,6,7]</code></li>
                                                                                <li> Case: type=<code>year</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                            </ul>
@apiParam   (BODY:) {int}           config_repeat.start.frequency           Tần suất lặp
@apiParam   (BODY:) {object}          config_repeat.stop                   Cấu hình dừng lặp task
@apiParam   (BODY:) {String}          config_repeat.stop.type              Loại dừng
                                                                            <ul>
                                                                                <li><code>never</code>: Không bao giờ dừng</li>
                                                                                <li><code>on_the_day</code>: Vào ngày</li>
                                                                                <li><code>after_number_times</code>: Sau số lần</li>
                                                                            </ul>
@apiParam   (BODY:) {Array}          config_repeat.stop.value             Giá trị của type tương ứng
                                                                            <ul>
                                                                                <li>Với type=<code>never</code>: thì value để là None hoặc string rỗng</li>
                                                                                <li>Với type=<code>on_the_day</code>: thì value string và có format sau: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                                <li>Với type=<code>after_number_times</code>: thì value để số</li>
                                                                            </ul>
@apiParam   (BODY:) {object}        [position]                          Thông tin vị trí
@apiParam   (BODY:) {float}         [position.latitude]                  Thông tin vĩ độ
@apiParam   (BODY:) {float}         [position.longitude]                 Thông tin kinh độ
                                                                            
                                                                            
@apiParamExample    {json}      BODY:
{
    "source": "TICKET",
    "object_id": "64186cdcd0d9b300124f6178",
    "title": "anh_2",
    "assign_ids": ["7fc0a33c-baf5-11e7-a7c2-0242ac180003"],
    "assign_type": "STAFF",
    "task_type": "GENERAL",
    "priority_level": 1,
    "status_process": 1,
    "description": "<div>fnosnado</div>",
    "notification_config_type": "NO_REMINDER",
    "attachment_ids": [],
    "profile_ids": [],
    "company_ids": [],
    "order_ids": [],
    "ticket_ids": ["64186cdcd0d9b300124f6178"],
    "mentions": [],
    "description_attachment_ids": [],
    "follow_task": {},
    "planned_start_time": "2023-03-22T08:03Z",
    "deadline_type": "time_slot",
    "deadline_start_time": "2023-03-22T08:03Z",
    "deadline_end_time": "2023-03-22T08:03Z",
    "compulsory_completion": false,
    "config_repeat": {
        "is_use": 1,
        "start": { 
            "type": "day",
            "values": [],
            "frequency": 1
        },
        "stop": { 
            "type": "never",
            "value": ""
        }
    },
    "position": {
        "latitude": 10.823215,
        "longitude": 106.629659
    }
}                                               


@apiUse ResponseDetailTaskMobileV2

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "_id": "641ab667fb252c0395897514",
        "id": "641ab667fb252c0395897514",
        "assign_ids": [
            "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        ],
        "assign_type": "STAFF",
        "attachment_ids": [],
        "calendar_sync_status": null,
        "company_ids": [],
        "complete_time": null,
        "time_change_status": "2023-03-22T08:03Z",
        "compulsory_completion": false,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2023-03-22T08:03Z",
        "deadline_end_time": "2023-03-22T08:03Z",
        "deadline_start_time": "2023-03-22T08:03Z",
        "deadline_type": "time_slot",
        "description": "<div>fnosnado</div>",
        "description_attachment_ids": [],
        "file_usage_status": "ARCHIVED",
        "follow_task": {},
        "id": "641ab667fb252c0395897514",
        "is_new": 1,
        "keywords": "anh_2",
        "mentions": [],
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "notification_config_channels": [
            "email"
        ],
        "notification_config_time_unit": null,
        "notification_config_time_value": null,
        "notification_config_type": "NO_REMINDER",
        "object_id": "64186cdcd0d9b300124f6178",
        "order_ids": [],
        "planned_start_time": "2023-03-22T08:03Z",
        "priority_level": 1,
        "profile_ids": [],
        "source": "TICKET",
        "status_process": 1,
        "tag_ids": [],
        "task_type": "GENERAL",
        "ticket_ids": [
            "64186cdcd0d9b300124f6178"
        ],
        "time_check_status": "2023-03-22T08:03Z",
        "title": "anh_2",
        "type_create": "MANUAL",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_time": "2023-03-22T08:03Z",
        "config_repeat": {
	        "is_use": 1,
	        "start": { 
		        "type": "day",
		        "values": [],
		        "frequency": 1
	        },
	        "stop": { 
		        "type": "never",
		        "value": ""
	        }
        },
        "position": {
            "latitude": 10.823215,
            "longitude": 106.629659
        }
    }
}
"""
# ---------- Lấy chi tiết của Task-----------
"""
@api {GET} {domain}/task/mobile/api/v2.0/task/<task_id>                    Lấy chi tiết của Task
@apiGroup Mobile Task
@apiVersion 2.0.0
@apiName DetailTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse FieldsSelect

@apiUse ResponseDetailTaskMobileV2

@apiUse DetailTaskv2Response
"""
# ---------- Lấy chi tiết của Task từ module task-----------
"""
@api {GET} {domain}/task/mobile/api/v2.0/tasks/actions/read-from-task/<task_id>                    Lấy chi tiết của Task từ module task
@apiGroup Mobile Task
@apiVersion 2.0.0
@apiName DetailTaskFromTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse FieldsSelect

@apiUse ResponseDetailTaskMobileV2

@apiUse DetailTaskv2Response
"""
# ---------- Lấy chi tiết của Task từ module khác-----------
"""
@api {GET} {domain}/task/mobile/api/v2.0/tasks/actions/read-from-other/<task_id>                    Lấy chi tiết của Task từ module khác
@apiGroup Mobile Task
@apiVersion 2.0.0
@apiName DetailTaskFromOther

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse FieldsSelect

@apiUse ResponseDetailTaskMobileV2

@apiUse DetailTaskv2Response
"""

# ---------- Cập nhật chi tiết của Task-----------
"""
@api {PUT} {domain}/task/mobile/api/v2.0/task/<task_id>                    Cập nhật chi tiết Task
@apiGroup Mobile Task
@apiVersion 2.0.0
@apiName UpdateDetailTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (BODY:) {string}    [title]                         Tiêu đề của Task
@apiParam   (BODY:) {string}    [description]                   Nội dung của Task
@apiParam   (BODY:) {Array}     assign_ids                    Định danh đối tượng được Assign
@apiParam   (BODY:) {String=STAFF,TEAM}    assign_type=STAFF                            Kiểu đối tượng được Assign
@apiParam   (BODY:) {string}    [notification_config_type]      Loại cấu hình
                                                                    <ul>
                                                                        <li><code>NO_REMINDER:</code>Không nhắc nhở</li>
                                                                        <li><code>ADVANCE_REMINDER:</code>Nhắc nhở trước</li>
                                                                        <li><code>TIMELY_REMINDER:</code>nhắc nhở đúng thời hạn</li>
                                                                        <li><code>OPTION:</code>Tùy chọn</li>
                                                                    </ul>
@apiParam   (BODY:) {string}    [notification_config_time_value]      Thời gian cấu hình
                                                                        <ul>
                                                                            <li>Với type là <code>NO_REMINDER:</code>Không cần truyền time_value</li>
                                                                            <li>Với type là <code>ADVANCE_REMINDER:</code>Cần truyền số phút muốn thông báo nhắc nhở trước lên</li>
                                                                            <li>Với type là <code>TIMELY_REMINDER:</code>Không cần truyền time_value</li>
                                                                            <li>Với type là <code>OPTION:</code>Truyền lên ngày giờ được chọn. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                        </ul> 
@apiParam   (BODY:) {string}    [notification_config_time_unit]        Đơn vị của thời gian cấu hình. Áp dụng với type: <code>ADVANCE_REMINDER</code> 
                                                                            <ul>
                                                                                <li><code>minute:</code> đơn vị tính là phút</li>
                                                                                <li><code>hour:</code> đơn vị tính là giờ</li>
                                                                                <li><code>year:</code> đơn vị tính là năm</li>
                                                                            <ul> 


@apiParam   (BODY:) {string=general,call,sent_email,metting}    task_type           Kiểu công việc
                                                                        <ul>
                                                                            <li><code>GENERAL:</code>Chung</li>
                                                                            <li><code>SENT_EMAIL:</code>Gửi email</li>
                                                                            <li><code>CALL:</code>Gọi điện</li>
                                                                            <li><code>MEETING:</code>Hẹn gặp</li>
                                                                        </ul>

@apiParam (BODY:) {int=1,2,3}     [priority_level]      Mức độ ưu tiên
                                                    <ul>
                                                        <li><code>1:</code> Cao </li>
                                                        <li><code>2:</code> Trung bình </li>
                                                        <li><code>3:</code> Thấp </li>
                                                    </ul>      

@apiParam (BODY:) {int=1,2,3,4,5}     [status_process]      Trạng thái xử lý
                                                    <ul>
                                                        <li><code>1:</code> Chưa thực hiện </li>
                                                        <li><code>2:</code> Đang thực hiện </li>
                                                        <li><code>3:</code> Đang chờ </li>
                                                        <li><code>4:</code> Tạm hoãn </li>
                                                        <li><code>5:</code> Hoàn thành </li>
                                                    </ul>   
    
@apiParam   (BODY:) {Array}     [attachment_ids]                Danh sách <code>ID</code> file đính kèm  
@apiParam   (BODY:) {String}     [task_parent_id]               <code>ID</code> công việc cha
@apiParam   (BODY:) {String}    [complete_time]                 Thời gian Hoàn thành của Task
@apiParam   (BODY:) {boolean}   [compulsory_completion]        Bắt buộc hoàn thành task(<code>false:</code> Không, <code>true:</code> Có) 
@apiParam   (BODY:) {String}    [planned_start_time]          Dự kiến thời gian bắt đầu
@apiParam   (BODY:) {String}    [calendar_sync_status]        Trạng thái đồng bộ lên calendar

@apiParam   (BODY:) {Array}    [company_ids]                  Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    [ticket_ids]                   Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    [order_ids]                     Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    [profile_ids]                  Danh sách profile liên quan tới.
@apiParam   (BODY:) {Array}    [tag_ids]                      Danh sách tag liên quan tới.

@apiParam   (BODY:) {Array}    [mentions]                             Danh sách người được mentions
@apiParam   (BODY:) {String}   [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiParam   (BODY:) {String}   [mentions.account_id]                  <code>ID</code> của người được mentions
@apiParam   (BODY:) {Array}    [description_attachment_ids]            Danh sách <code>ID</code> file được upload trong nội dung của Task


@apiParam   (BODY:) {Object}    [deadline]                 Thời hạn hoàn thành của task công việc tiếp nối
@apiParam   (BODY:) {String}    [deadline_type]            Loại thời hạn hoàn thành của công việc tiếp nối
                                                                        <ul>
                                                                            <li><code>time_slot</code>: Khung giờ</li>
                                                                            <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                        </ul>
@apiParam   (BODY:) {String}    [deadline_start_time]      Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                </ul>
@apiParam   (BODY:) {Object}    [deadline_end_time]        Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                </ul>
@apiParam   (BODY:) {Object}           config_repeat                        Cấu hình có lặp task không?
                                                                
@apiParam   (BODY:) {Int=0,1}          config_repeat.is_use                 Có sử dụng cấu hình không?
                                                                            <ul>
                                                                                <li><code>0</code>: không sử dụng</li>
                                                                                <li><code>1</code>: sử dụng</li>
                                                                            </ul>
@apiParam   (BODY:) {object}          config_repeat.start                   Cấu hình bắt đầu lặp task
@apiParam   (BODY:) {String}          config_repeat.start.type              Kiểu lặp
                                                                            <ul>
                                                                                <li><code>day</code>: Ngày</li>
                                                                                <li><code>week</code>: Tuần</li>
                                                                                <li><code>month</code>: Tháng</li>
                                                                                <li><code>year</code>: Năm</li>
                                                                            </ul>
@apiParam   (BODY:) {Array}          config_repeat.start.values            Cấu hình thông tin lặp vào (Ví dụ: thứ 2, thứ 3)
                                                                            <ul>
                                                                                <li> Case: type=<code>day</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                                <li> Case: type=<code>week</code> thì <code>values: danh sách các thứ trong tuần - ví dụ ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]</code></li>
                                                                                <li> Case: type=<code>month</code> thì <code>values: danh sách các ngày trong tháng (Mặc định là 31 ngày) - ví dụ [1,2,3,4,5,6,7]</code></li>
                                                                                <li> Case: type=<code>year</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                            </ul>
@apiParam   (BODY:) {int}           config_repeat.start.frequency           Tần suất lặp
@apiParam   (BODY:) {object}          config_repeat.stop                   Cấu hình dừng lặp task
@apiParam   (BODY:) {String}          config_repeat.stop.type              Loại dừng
                                                                            <ul>
                                                                                <li><code>never</code>: Không bao giờ dừng</li>
                                                                                <li><code>on_the_day</code>: Vào ngày</li>
                                                                                <li><code>after_number_times</code>: Sau số lần</li>
                                                                            </ul>
@apiParam   (BODY:) {Array}          config_repeat.stop.value             Giá trị của type tương ứng
                                                                            <ul>
                                                                                <li>Với type=<code>never</code>: thì value để là None hoặc string rỗng</li>
                                                                                <li>Với type=<code>on_the_day</code>: thì value string và có format sau: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                                <li>Với type=<code>after_number_times</code>: thì value để số</li>
                                                                            </ul>
@apiParam   (BODY:) {object}        [position]                          Thông tin vị trí
@apiParam   (BODY:) {float}         [position.latitude]                  Thông tin vĩ độ
@apiParam   (BODY:) {float}         [position.longitude]                 Thông tin kinh độ
                                                                            

@apiParamExample    {json}      BODY:
{
    "assign_ids": [
        "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
    ],
    "assign_type": "STAFF",
    "attachment_ids": [],
    "task_parent_id":"",
    "company_ids": [],
    "compulsory_completion": false,
    "deadline_end_time": "2023-03-22T08:03Z",
    "deadline_start_time": "2023-03-22T08:03Z",
    "time_change_status": "2023-03-22T08:03Z",
    "deadline_type": "time_slot",
    "description": "<div>fnosnado</div>",
    "description_attachment_ids": [],
    "follow_task": {},
    "mentions": [],
    "notification_config_channels": [
        "email"
    ],
    "notification_config_type": "NO_REMINDER",
    "object_id": "64186cdcd0d9b300124f6178",
    "order_ids": [],
    "planned_start_time": "2023-03-22T08:03Z",
    "priority_level": 1,
    "profile_ids": [],
    "source": "TICKET",
    "status_process": 1,
    "tag_ids": [],
    "task_type": "GENERAL",
    "ticket_ids": [
        "64186cdcd0d9b300124f6178"
    ],
    "title": "anh_2",
    "config_repeat": {
        "is_use": 1,
        "start": {
            "type": "day",
            "values": [],
            "frequency": 1
        },
        "stop": {
            "type": "never",
            "value": ""
        }
    }
}                                       
                                     


@apiUse ResponseDetailTaskMobileV2

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "account_mentions": [],
        "assign_ids": [
            "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        ],
        "assign_type": "STAFF",
        "attachment_ids": [],
        "calendar_sync_status": null,
        "company_ids": [],
        "complete_time": null,
        "compulsory_completion": false,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2023-03-22T08:03Z",
        "deadline_end_time": "2023-03-22T08:03Z",
        "deadline_start_time": "2023-03-22T08:03Z",
        "deadline_type": "time_slot",
        "description": "<div>fnosnado</div>",
        "description_attachment_ids": [],
        "file_usage_status": "ARCHIVED",
        "follow_task": {},
        "id": "641ab667fb252c0395897514",
        "is_new": 1,
        "keywords": "anh_2",
        "mentions": [],
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "notification_config_channels": [
            "email"
        ],
        "notification_config_time_unit": null,
        "notification_config_time_value": null,
        "notification_config_type": "NO_REMINDER",
        "object_id": "64186cdcd0d9b300124f6178",
        "order_ids": [],
        "planned_start_time": "2023-03-22T08:03Z",
        "priority_level": 1,
        "profile_ids": [],
        "source": "TICKET",
        "status_process": 1,
        "tag_ids": [],
        "task_type": "GENERAL",
        "ticket_ids": [
            "64186cdcd0d9b300124f6178"
        ],
        "time_check_status": "2023-03-22T08:03Z",
        "title": "anh_2",
        "type_create": "MANUAL",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_time": "2023-03-22T08:03Z",
        "task_parent_id":"",
        "config_repeat": {
            "is_use": 1,
            "start": {
                "type": "day",
                "values": [],
                "frequency": 1
            },
            "stop": {
                "type": "never",
                "value": ""
            }
        },
        "position": {
            "latitude": 10.835881,
            "longitude": 106.665598
        }
        
    }
}
"""
# ---------- Danh sách Task cần xử lý hôm nay -----------
"""
@api {GET} {domain}/task/api/v1.0/tasks/need-handle-today              Lấy danh sách Task cần xử lý hôm nay
@apiGroup Mobile Task
@apiVersion 2.0.0
@apiName ListTaskNeedHandle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Params:) {string} per_page               Số lượng item trên 1 trang.
@apiParam (Params:) {string} after_token            Token được trả ra ở lần request trước đó
                                                    (Dùng để làm mốc cho lượt query tiếp theo đề lấy ra những phần tử tiếp theo)

@apiUse ResponseDetailTaskMobileV2                                                                                                                  

@apiSuccess {String}            data.created_by      Được tạo bởi ai
@apiSuccess {String}            data.created_time    Thời gian tạo

@apiUse PagingTaskMobile

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "account_mentions": [],
            "assign_ids": [
                "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            ],
            "assign_type": "STAFF",
            "attachment_ids": [],
            "calendar_sync_status": null,
            "company_ids": [],
            "complete_time": null,
            "compulsory_completion": false,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2023-03-22T08:03Z",
            "deadline_end_time": "2023-03-22T08:03Z",
            "deadline_start_time": "2023-03-22T08:03Z",
            "deadline_type": "time_slot",
            "description": "<div>fnosnado</div>",
            "description_attachment_ids": [],
            "file_usage_status": "ARCHIVED",
            "follow_task": {},
            "id": "641ab667fb252c0395897514",
            "is_new": 1,
            "keywords": "anh_2",
            "mentions": [],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config_channels": [
                "email"
            ],
            "notification_config_time_unit": null,
            "notification_config_time_value": null,
            "notification_config_type": "NO_REMINDER",
            "object_id": "64186cdcd0d9b300124f6178",
            "order_ids": [],
            "planned_start_time": "2023-03-22T08:03Z",
            "priority_level": 1,
            "profile_ids": [],
            "source": "TICKET",
            "status_process": 1,
            "tag_ids": [],
            "task_type": "GENERAL",
            "task_parent_id": "",
            "ticket_ids": [
                "64186cdcd0d9b300124f6178"
            ],
            "time_check_status": "2023-03-22T08:03Z",
            "title": "anh_2",
            "type_create": "MANUAL",
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": "2023-03-22T08:03Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
}
"""
# ---------- Danh sách Task cần xử lý hôm nay -----------
"""
@api {POST} {domain}/task/mobile/api/v2.0/tasks/action/search-by-name              Lấy danh sách Task theo tên
@apiGroup Mobile Task
@apiVersion 2.0.0
@apiName ListTaskByName

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Body:) {string} search                 Từ khoá công việc cần tìm kiếm theo tên.

@apiParamExample {json} Body example
{
    "search": "gọi điện cho khách hàng",
}

@apiParam (Params:) {int=1,-1} order_by=1           order
                                                    <ul>
                                                        <li>1: Sort theo thứ tự tăng dần</li>
                                                        <li>-1: Sort theo thứ tự giảm dần</li>
                                                    </ul>          
@apiParam (Params:) {string} sort_by=created_time                Field sort
@apiParam (Params:) {string} per_page               Số lượng item trên 1 trang.
@apiParam (Params:) {string} after_token            Token được trả ra ở lần request trước đó
                                                    (Dùng để làm mốc cho lượt query tiếp theo đề lấy ra những phần tử tiếp theo)

@apiUse ResponseDetailTaskMobileV2

@apiUse PagingTaskMobile

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "account_mentions": [],
            "assign_ids": [
                "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            ],
            "assign_type": "STAFF",
            "attachment_ids": [],
            "calendar_sync_status": null,
            "company_ids": [],
            "complete_time": null,
            "compulsory_completion": false,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2023-03-22T08:03Z",
            "deadline_end_time": "2023-03-22T08:03Z",
            "deadline_start_time": "2023-03-22T08:03Z",
            "deadline_type": "time_slot",
            "description": "<div>fnosnado</div>",
            "description_attachment_ids": [],
            "file_usage_status": "ARCHIVED",
            "follow_task": {},
            "id": "641ab667fb252c0395897514",
            "is_new": 1,
            "keywords": "anh_2",
            "mentions": [],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config_channels": [
                "email"
            ],
            "notification_config_time_unit": null,
            "notification_config_time_value": null,
            "notification_config_type": "NO_REMINDER",
            "object_id": "64186cdcd0d9b300124f6178",
            "order_ids": [],
            "planned_start_time": "2023-03-22T08:03Z",
            "priority_level": 1,
            "profile_ids": [],
            "source": "TICKET",
            "status_process": 1,
            "tag_ids": [],
            "task_type": "GENERAL",
            "ticket_ids": [
                "64186cdcd0d9b300124f6178"
            ],
            "time_check_status": "2023-03-22T08:03Z",
            "title": "anh_2",
            "type_create": "MANUAL",
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "task_parent_id": "",
            "updated_time": "2023-03-22T08:03Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------- Chuyển trạng thái nhiều task -----------
"""
@api {POST} {domain}/task/mobile/api/v2.0/tasks/change-status-process         Chuyển trạng thái xử lý nhiều task
@apiGroup Mobile Task
@apiVersion 1.0.0
@apiName ChangeStatusProcessTasks

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(BODY:)			{Array}	    task_ids			      Thông tin Task cần chuyển
@apiParam	(BODY:)			{Int}	    to_status_process			  Value trạng thái xử lý chuyển đến

@apiParamExample {json} Body example
{
    "task_ids": ["8a841218-6e36-4565-a0d2-41f0896df548", "8a841218-6e36-4565-a0d2-41f0896df547"]
    "to_status_process": 1
}


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Object}            data                        Thông tin phản hồi
@apiSuccess {Array}             data.task_ids                 Danh sách ID task chuyển thành công
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "task_ids": ["8a841218-6e36-4565-a0d2-41f0896df548", "8a841218-6e36-4565-a0d2-41f0896df547"]
    },
    "message": "request thành công."
}
"""

# ---------- Lấy số lượng task theo trạng thái của user đang nhập ----------
"""
@api {GET} {domain}/task/mobile/api/v1.0/tasks/count_by_assign_status         Lấy số lượng task theo trạng thái của user đang nhập
@apiGroup Mobile Task
@apiVersion 1.0.0
@apiName CountByAssignStatus

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(Query:)			{string}	    status			Danh sách status cần lấy dữ liệu. Ngăn cách nhau bởi dấu ,
                                                                <ul>
                                                                    <li><code>UPCOMING</code>: Sắp tới</li>
                                                                    <li><code>OVER_DUE</code>: Quá hạn</li>
                                                                </ul>
@apiParam	(Query:)			{string}	    [source]		Đối tượng muốn lấy dữ liệu. Nếu không truyền thì mặc định sẽ lấy all.                                                                
@apiParam	(Query:)			{string}	    [assign_id]		<code>ID</code> nhân viên muốn lấy dữ liệu. Không truyền thì mặc định sẽ lấy user đăng nhập.                                                                
@apiParam	(Query:)			{string}	    [assign_type]	<code>Kiểu phân công</code>. Không truyền thì mặc định sẽ lấy là nhân viên.                                                                
                                                                

@apiParamExample {string} Query example
{
    ?status=UPCOMING,OVER_DUE
}


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Array}             data                        Thông tin phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "UPCOMING": 10,
        "OVER_DUE": 10
    },
    "message": "request thành công."
}
"""

"""
@api  {POST} {domain}/task/mobile/api/v2.0/tasks/<task_id>/remove-child                 Gỡ công việc con khỏi công việc cha
@apiGroup Mobile Task
@apiVersion 1.0.0
@apiName RemoveChildTaskParentTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {Array}     child_task_ids      Danh sách <code>ID</code> công việc con cần gỡ.                         

@apiParamExample    {json}      Body Example:
{
    "child_task_ids": []
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample    {json}      Response Example:
{
    "code": 200,
    "lang": "vi",
    "message": "Request thành công."
}    

"""
"""
@api  {POST} {domain}/task/mobile/api/v2.0/tasks/action/get-list-relate                Lấy danh sách công vệc liên quan tới (Cha, con của công việc hiện tại)
@apiGroup Mobile Task
@apiVersion 1.0.0
@apiName ListTaskRelateTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {string}   task_id                          <code>ID</code> công việc cần lấy danh sách công việc liên quan.
@apiParam   (Body:) {String}   [search]                         Tên công việc cần tìm kiếm
@apiParam   (Body:) {String=child,parent}   type                Loại công việc muốn lấy danh sách
                                                                <ul>
                                                                    <li><code>child</code>: Lấy danh sách công việc con của công việc hiện tại</li>
                                                                    <li><code>parent</code>: Lấy danh sách công việc cha của công việc hiện tại</li>
                                                                </ul>
                                                                

@apiParamExample    {json}      Body Example:
{
    "task_id": "",
    "type": "child"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Dữ liệu

@apiUse paging_tokens

@apiUse ResponseDetailTaskMobileV2
"""

"""
@api  {POST} {domain}/task/mobile/api/v2.0/tasks/action/total-list-relate                Lấy tổng số công vệc liên quan tới (Cha, con của công việc hiện tại)
@apiGroup Mobile Task
@apiVersion 1.0.0
@apiName TotalTaskRelateTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {string}   task_id                          <code>ID</code> công việc cần lấy danh sách công việc liên quan.
@apiParam   (Body:) {String=child,parent}   [type]              Loại công việc muốn lấy danh sách
                                                                <ul>
                                                                    <li><code>child</code>: Lấy tổng số công việc con</li>
                                                                    <li><code>parent</code>: Lấy tổng số công việc cha</li>
                                                                    <li><code>Nếu không truyền lên thì mặc định tính tổng của cả child và parent</code></li>
                                                                </ul>


@apiParamExample    {json}      Body Example:
{
    "task_id": "",
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Dữ liệu
@apiSuccess {Int}            data.total_child                 Tổng số task con
@apiSuccess {Int}            data.total_parent                Tổng số task cha
@apiSuccess {Int}            data.total                       Tổng sô task


@apiSuccessExample    {json}      Response Example:
{
    "code": 200,
    "data": {
        "total_child": 0,
        "total_parent": 0,
        "total": 0,
    },
    "lang": "vi",
    "message": "Request thành công."
}    

"""

"""
@api  {POST} {domain}/task/mobile/api/v1.0/tasks/actions/quickview                 Lấy danh sách công việc xem nhanh
@apiGroup Mobile Task
@apiVersion 1.0.0
@apiName ListTaskQuickView

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {Array}    [fields]                         Danh sách fields cần lấy dữ liệu (<code>Default: </code> sẽ lấy: id, code_task, title).
                                                                Nếu truyền dữ liệu lên thì sẽ trả về toàn bộ field request.
@apiParam   (Body:) {Array}    [filters]                        Danh sách điều kiện lọc
@apiParam   (Body:) {String}   [search]                         Tên công việc cần tìm kiếm

@apiParamExample    {json}      Body Example:
{
    "search": "",
    "filters": [],
    "fields": []
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Dữ liệu
@apiSuccess {String}               data.id                    <code>ID</code> của công việc
@apiSuccess {String}               data.code_task             Mã công việc
@apiSuccess {String}               data.title                 Tên công việc


@apiUse paging_tokens

@apiSuccessExample    {json}      Response Example:
{
    "code": 200,
    "data": [
        {
            "id": "63e9f6210d48aed9df718f6d",
            "title": "1000",
            "code_task": "",
        }
    ],
    "lang": "vi",
    "message": "Request thành công."
}    
"""

"""
@api {GET} {domain}/task/mobile/api/v2.0/tasks/actions/list-status-process  Lấy danh sách trạng thái task
@apiGroup Mobile Task
@apiVersion 1.0.0
@apiName ListStatusProcess   

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query) {string=1,2,3,4,5} value              Gía trị trạng thái xử lý task (mặc định là lấy tất cả)

@apiParamExample    {json}      Response Example:
{
    "code": 200,
    "data": [
        {
            "allow_change_order": false,
            "allow_edit_status_display_value": false,
            "allow_edit_value": false,
            "allow_remove_value": false,
            "bg_color": "#F8F9FA",
            "color": "#6A7383",
            "enable": 1,
            "order": 0,
            "status": 1,
            "translate_key": "i18n_to_do",
            "value": 1
        },
        {
            "allow_change_order": false,
            "allow_edit_status_display_value": false,
            "allow_edit_value": false,
            "allow_remove_value": false,
            "bg_color": "#E9F1FE",
            "color": "#226FF5",
            "enable": 1,
            "order": 1,
            "status": 1,
            "translate_key": "i18n_doing",
            "value": 2
        },
        {
            "allow_change_order": false,
            "allow_edit_status_display_value": false,
            "allow_edit_value": false,
            "allow_remove_value": false,
            "bg_color": "#FEEEE6",
            "color": "#F05800",
            "enable": 1,
            "order": 2,
            "status": 1,
            "translate_key": "i18n_waiting",
            "value": 3
        },
        {
            "allow_change_order": false,
            "allow_edit_status_display_value": false,
            "allow_edit_value": false,
            "allow_remove_value": false,
            "bg_color": "#F0EBEE",
            "color": "#663259",
            "enable": 1,
            "order": 3,
            "status": 1,
            "translate_key": "i18n_pending",
            "value": 4
        },
        {
            "allow_change_order": false,
            "allow_edit_status_display_value": false,
            "allow_edit_value": false,
            "allow_remove_value": false,
            "bg_color": "#E6FAF0",
            "color": "#00BC62",
            "enable": 1,
            "order": 4,
            "status": 1,
            "translate_key": "i18n_complete",
            "value": 5
        }
    ],
    "lang": "en",
    "message": "request successful."
}                             

"""
"""
    Author: KIEUANH
    Company: MOBIO
    Date Created: 20/03/2023
"""

"""
@apiDefine GetDetailTaskResponse

@apiSuccessExample {json} Response 
{
        "_id": "659ceebe5016dc6bede24f98",
        "assign_ids": [
            "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        ],
        "assign_type": "STAFF",
        "attachment_ids": [],
        "attachments": [],
        "calendar_sync_status": null,
        "child_task_id": null,
        "code_task": "KC53Y2HJ",
        "comment_count": 0,
        "company_ids": [],
        "complete_time": null,
        "compulsory_completion": false,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2024-01-09T06:59Z",
        "deadline_end_time": "2024-01-09T00:00Z",
        "deadline_start_time": "2024-01-09T00:00Z",
        "deadline_type": "unspecified_time",
        "description": "<div>123</div>",
        "description_attachment_ids": [],
        "file_usage_status": "ARCHIVED",
        "follow_task": {
            "deadline_type": "",
            "status": 0
        },
        "id": "659ceebe5016dc6bede24f98",
        "is_new": 1,
        "keywords": "666",
        "mentions": [],
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "notification_config_channels": [
            "email"
        ],
        "notification_config_time_unit": null,
        "notification_config_time_value": null,
        "notification_config_type": "NO_REMINDER",
        "object_id": "659ceebe5016dc6bede24f98",
        "order_ids": [],
        "planned_start_time": "2024-01-09T06:58Z",
        "priority_level": 1,
        "profile_ids": [
            "8adbd6b6-be87-4fea-9da4-748d70c8be06"
        ],
        "related_to": {
            "company_ids": [],
            "order_ids": [],
            "profile_ids": [
                "8adbd6b6-be87-4fea-9da4-748d70c8be06"
            ],
            "ticket_ids": []
        },
        "related_to_not_delete": {},
        "required_push_socket_overdue": true,
        "source": "TASK",
        "status_process": 1,
        "tab": "UPCOMING",
        "tag_ids": [],
        "task_parent_id": "",
        "task_type": "GENERAL",
        "ticket_ids": [],
        "time_change_status": "2024-01-09T06:59Z",
        "time_check_status": "2024-01-09T16:59Z",
        "title": "666",
        "type_create": "MANUAL",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_time": "2024-01-09T06:59Z"
    }
"""

"""
@apiDefine GetListTaskByModuleResponse

@apiParam   (BODY:) {array}     source                   Source (TICKET, SALE, COMPANY, PROFILE) 
@apiParam   (BODY:) {string}    id_related_to            Id của liên quan tới (VD: source là Ticket thì id_related_to là id của ticket đó)
@apiParam   (BODY:) {string}    [keywords]               Keywords
@apiParam   (BODY:) {array}     [task_type]              Kiểu công việc
@apiParam   (BODY:) {string}    [assign_ids]            Người được giao
@apiParam   (BODY:) {string}    [task_status]            Trạng thái của công việc
@apiParam   (BODY:) {float}     [start_time]              Thời gian bắt đầu tìm kiếm (format: %Y-%m-%dT%H:%M:%SZ)
@apiParam   (BODY:) {float}     [end_time]                Thời gian kết thúc tìm kiếm (format: %Y-%m-%dT%H:%M:%SZ)
@apiParam   (BODY:) {float}     [complete_time_start]     Thời gian hoan thành thuc te bắt đầu tìm kiếm (format: %Y-%m-%dT%H:%M:%SZ)
@apiParam   (BODY:) {float}     [complete_time_end]       Thời gian hoan thanh thuc te kết thúc tìm kiếm (format: %Y-%m-%dT%H:%M:%SZ)
@apiParam   (BODY:) {array}     [account_id]             Người được nhắc đến
@apiParam   (BODY:) {string}    [created_by]             Người tạo
@apiParam   (BODY:) {object}    [deadline]               Thời hạn hoàn thành.
@apiParam   (BODY:) {string}    [deadline.start_time]    Thời gian bắt đầu của thời hạn hoàn thành
@apiParam   (BODY:) {string}    [deadline.end_time]      Thời gian kết thúc của thời hạn hoàn thành


@apiParam   (BODY:) {array}     [status_process]         Trạng thái xử lý
@apiParam   (Param:) {string}   after_token              Mặc định là None
@apiParam   (Param:) {string}   per_page                 Mặc định là 10

@apiParamExample {json} Body example
{
    "deadline": {
        "start_time": "2024-01-01T00:00Z",
        "end_time": "2024-01-31T23:59Z"
    }
}




@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
                                        <li><code>page:</code>Vị trí page request</li>
                                        <li><code>per_page:</code>Số lượng phần tử trên một page</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after_token]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before_token]    Token để lấy dữ liệu trang trước đó.
@apiSuccess     {Object}    [paging..per_page]          Số lượng phần tử trên 1 page
@apiSuccess     {Object}    [paging..page_count]        Tổng số page
@apiSuccess     {Object}    [paging..total_count]       Tổng số phần tử

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "account_mentions": [],
            "assign_ids": [
                "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            ],
            "assign_type": "STAFF",
            "comment_count": 1,
            "company_ids": [],
            "complete_time": null,
            "compulsory_completion": false,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": **********.00594,
            "deadline_end_time": **********,
            "deadline_start_time": **********,
            "deadline_type": "unspecified_time",
            "description": "<div>fvfgvfv</div> <style></style>",
            "description_attachment_ids": [],
            "file_usage_status": "ARCHIVED",
            "id": "63e9f6210d48aed9df718f6d",
            "code_task": "IOQPAS1233",
            "task_parent_id": "",
            "is_new": 0,
            "keywords": "fff",
            "mentions": [],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config_channels": [
                "email"
            ],
            "notification_config_type": "NO_REMINDER",
            "object_id": "63e766581c5d7500126d8e9d",
            "order_ids": [],
            "planned_start_time": null,
            "priority_level": 1,
            "profile_ids": [],
            "related_to_not_delete": {
                "company_ids": [],
                "order_ids": [],
                "profile_ids": [],
                "ticket_ids": [
                    "63e766581c5d7500126d8e9d"
                ]
            },
            "source": "TICKET",
            "status_process": 4,
            "tags_id": [],
            "task_type": "GENERAL",
            "ticket_ids": [
                "63e766581c5d7500126d8e9d"
            ],
            "time_check_status": 1676282340,
            "title": "fff",
            "type_create": "MANUAL",
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": 1676282353.09
        }
    ],
    "lang": "en",
    "message": "request successful.",
    "scroll_size": 1,
    "paging": {
        "cursors": {
            "after_token": "WzE2ODU1NTk2MDAuMCwgIjY0NzcxN2Y0YjVmNzVlMDAwZTU1MTY5MiJd",
            "before_token": "WzE2ODU1MzQ1MjAuMCwgIjY0NzcyNzY2NzZmYjA3MmRjOGU4NTE4NCJd"
        },
        "page_count": 5,
        "per_page": 10,
        "total_count": 41
    }
}
"""

"""
@apiDefine GetListTaskFilterParamAndResponse

@apiParam	(BODY:)			{String}	[search]			      Từ khoá tìm kiếm theo tên task, ...
@apiParam	(BODY:)			{Array}	    [filters]	          Danh sách các điều kiện lọc task
@apiParam	(BODY:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả. 
                                                              VD: ["title",...]. Trường hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính
                                                              
@apiParam	(Query:)		{String}	[sort]   	          Tên field trường thông tin cần sắp xếp                                                                             
@apiParam	(Query:)		{String}	[order]   	          Kiểu sắp xếp:
                                                              <ul>
                                                                <li><code>asc: </code> sắp xếp tăng dần</li>
                                                                <li><code>desc: </code> sắp xếp giảm dần</li>
                                                              </ul>               
                                                              allow value: asc, desc   

@apiParamExample {json} Body example
{
    "search": "",
    "fields": [
        "title",
    ],
    "filters": [
        {
            "criteria_key" : "cri_time_check_status",
            "operator_key" : "op_is_less",
            "values" : []
        }, 
        {
            "criteria_key" : "cri_status_process",
            "operator_key" : "op_is_not_equal",
            "values" : []
        }
    ]
}


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách công việc

@apiSuccess {array}             data.account_mentions           Danh sách người được nhắc đến trong công việc
@apiSuccess {array}             data.assign_ids                 Danh sách <code>ID</code> định danh đối tượng được assign 
@apiSuccess {string}             data.assign_type               Kiểu đối tượng được assgin
                                                                <ul>
                                                                    <li><code>STAFF:</code> nhân viên</li>
                                                                    <li><code>TEAM:</code> team</li>
                                                                </ul>
@apiSuccess {array}             data.attachment_ids             Danh sách <code>ID</code> các file được đính kèm với Task
@apiSuccess {array}             data.attachments                Danh sách thông tin file đính kèm
@apiSuccess {Array}             data.attachments.capacity     Dung lượng của file
@apiSuccess {Array}             data.attachments.format_file  Định dạng file
@apiSuccess {Array}             data.attachments.id           <code>ID</code> của file đính kèm
@apiSuccess {Array}             data.attachments.title        Tên file đính kèm
@apiSuccess {Array}             data.attachments.url          Link truy cập file đính kèm
@apiSuccess {bool}             data.calendar_sync_status          Trang thái sync task lên calendar 
@apiSuccess {string}             data.code_task               Mã công việc
@apiSuccess {int}             data.comment_count              Số lượng comment  của task
@apiSuccess {array}             data.company_ids              Danh sách các <code>ID</code> công ty liên quan tới 
@apiSuccess {string}             data.complete_time                 Thời gian hoàn thành task
@apiSuccess {bool}             data.compulsory_completion
@apiSuccess {string}             data.created_by                ID người tạo task
@apiSuccess {string}             data.created_time              Thời gian tạo
@apiSuccess {string}             data.deadline_end_time         Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                </ul>
@apiSuccess {string}             data.deadline_start_time
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                </ul>
@apiSuccess {string}             data.deadline_type
                                                                <ul>
                                                                    <li><code>time_slot</code>: Khung giờ</li>
                                                                    <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                </ul>
@apiSuccess {string}             data.description                   Nội dung của task 
@apiSuccess {array}             data.description_attachment_ids     Danh sách <code>ID</code> các file được đính kèm trong nội dung của Task
@apiSuccess {string}             data.file_usage_status             Trạng thái lưu trữ file của công việc 
@apiSuccess {string}             data.id                                <code>ID</code> task
@apiSuccess {int}             data.is_new                               Field thể hiện task mới tạo
@apiSuccess {string}             data.keywords
@apiSuccess {array}             data.mentions                           Danh sách người được mentions
@apiSuccess {string}             data.merchant_id                       <code>ID</code> merchant
@apiSuccess {array}             data.notification_config_channels       Kênh thông báo
@apiSuccess {string}             data.notification_config_time_unit     Đơn vị của thời gian cấu hình. Áp dụng với type: <code>ADVANCE_REMINDER</code>
                                                                        <ul>
                                                                            <li><code>minute:</code> đơn vị tính là phút</li>
                                                                            <li><code>hour:</code> đơn vị tính là giờ</li>
                                                                            <li><code>year:</code> đơn vị tính là năm</li>
                                                                        <ul> 
@apiSuccess {string}             data.notification_config_time_value    Giá trị thời gian thông báo
@apiSuccess {string}             data.notification_config_type          Kiểu thông báo
                                                                    <ul>
                                                                        <li><code>NO_REMINDER:</code>Không nhắc nhở</li>
                                                                        <li><code>ADVANCE_REMINDER:</code>Nhắc nhở trước</li>
                                                                        <li><code>TIMELY_REMINDER:</code>nhắc nhở đúng thời hạn</li>
                                                                        <li><code>OPTION:</code>Tùy chọn</li>
                                                                    </ul>
@apiSuccess {string}             data.object_id                         Định danh của phần tạo tác. Ví dụ ở SALE thì sẽ là định danh của đơn hàng.
@apiSuccess {array}             data.order_ids                          Danh sách các <code>ID</code> order liên quan tới
@apiSuccess {string}             data.planned_start_time                Thời gian dự kiến bắt đầu 
@apiSuccess {int}             data.priority_level                       Mức độ ưu tiên
                                                                        <ul>
                                                                                <li><code>1:</code> Cao</li>
                                                                                <li><code>2:</code> Trung bình</li>
                                                                                <li><code>3:</code> Thấp</li>
                                                                        </ul>
@apiSuccess {array}             data.profile_ids                        Danh sách các <code>ID</code> profile liên quan tới
@apiSuccess {object}             data.related_to                        Thông tin liên quan tới
@apiSuccess {object}             data.related_to.profile_ids            Danh sách profile liên quan tới
@apiSuccess {object}             data.related_to.order_ids              Danh sách đơn hàng liên quan tới 
@apiSuccess {object}             data.related_to.company_ids            Danh sách công ty liên quan tới
@apiSuccess {object}             data.related_to.ticket_ids             Danh sách ticket liên quan tới
@apiSuccess {object}             data.related_to_not_delete             Thông tin liên quan tới không được xóa
@apiSuccess {object}             data.related_to_not_delete.profile_ids            Danh sách profile liên quan tới
@apiSuccess {object}             data.related_to_not_delete.order_ids              Danh sách đơn hàng liên quan tới 
@apiSuccess {object}             data.related_to_not_delete.company_ids            Danh sách công ty liên quan tới
@apiSuccess {object}             data.related_to_not_delete.ticket_ids             Danh sách ticket liên quan tới
@apiSuccess {bool}               data.required_push_socket_overdue        Bắt buộc push socket khi task quá hạn
@apiSuccess {string}             data.source                            Định danh nguồn tạo Task
@apiSuccess {int}             data.status_process
                                                        <ul>
                                                            <li><code>1:</code> Chưa thực hiện </li>
                                                            <li><code>2:</code> Đang thực hiện </li>
                                                            <li><code>3:</code> Đang chờ </li>
                                                            <li><code>4:</code> Tạm hoãn </li>
                                                            <li><code>5:</code> Hoàn thành </li>
                                                        </ul> 
@apiSuccess {string}             data.tab                               tab trong giao diện
@apiSuccess {array}             data.tag_ids                            Danh sách các <code>ID</code> tag liên quan tới
@apiSuccess {string}             data.task_parent_id                    <code>ID</code> công việc cha 
@apiSuccess {string}             data.task_type                         Kiểu công việc
@apiSuccess {array}             data.ticket_ids                         Danh sách các <code>ID</code> ticket liên quan tới
@apiSuccess {string}             data.time_change_status                 Thời gian thay đổi trạng thái task
@apiSuccess {string}             data.time_check_status                 Phân loại task quá hạn theo field này    
@apiSuccess {string}             data.title                               Tên task
@apiSuccess {string}             data.type_create                        Kiểu tạo task
@apiSuccess {string}             data.updated_by                        ID người update task gần nhất
@apiSuccess {string}             data.updated_time                      Thời gian update task gần nhất

@apiSuccessExample {json} Response
{
    "data": [
        {
            "account_mentions": [],
            "assign_ids": [
                "31ffb374-8e22-4d80-8477-ab6267cdd076"
            ],
            "assign_type": "STAFF",
            "attachment_ids": [],
            "attachments": [],
            "calendar_sync_status": null,
            "code_task": "C5TSZ5X4",
            "comment_count": 0,
            "company_ids": [],
            "complete_time": null,
            "compulsory_completion": false,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2024-01-09T03:15Z",
            "deadline_end_time": "2024-01-09T04:11Z",
            "deadline_start_time": "2024-01-09T04:11Z",
            "deadline_type": "time_slot",
            "description": "<div>\u0110\u01b0\u1ee3c t\u1ea1o khi chuy\u1ec3n tr\u1ea1ng th\u00e1i c\u01a1 h\u1ed9i b\u00e1n t\u1eeb tr\u1ea1ng th\u00e1i \" C\u00f3 th\u00f4ng tin Leads\" sang tr\u1ea1ng th\u00e1i \"H\u1eb9n g\u1eb7p\" , v\u00e0o l\u00fac 09/01/2024 10:15</div><br><div>55555</div>",
            "description_attachment_ids": [],
            "file_usage_status": "ARCHIVED",
            "id": "659cba60cfd78dbb5d172374",
            "is_new": 0,
            "keywords": "656",
            "mentions": [],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config_channels": [
                "email"
            ],
            "notification_config_time_unit": "minute",
            "notification_config_time_value": "30",
            "notification_config_type": "ADVANCE_REMINDER",
            "object_id": "652798f0f33eb117e6eae709",
            "order_ids": [
                "652798f0f33eb117e6eae709"
            ],
            "planned_start_time": "",
            "priority_level": 1,
            "profile_ids": [],
            "related_to": {
                "company_ids": [],
                "order_ids": [
                    "652798f0f33eb117e6eae709"
                ],
                "profile_ids": [],
                "ticket_ids": []
            },
            "related_to_not_delete": {
                "company_ids": [],
                "order_ids": [
                    "652798f0f33eb117e6eae709"
                ],
                "profile_ids": [],
                "ticket_ids": []
            },
            "required_push_socket_overdue": false,
            "source": "SALE",
            "status_process": 1,
            "tab": "OVER_DUE",
            "tag_ids": [],
            "task_parent_id": "",
            "task_type": "MEETING",
            "ticket_ids": [],
            "time_change_status": "2024-01-09T03:15Z",
            "time_check_status": "2024-01-09T04:11Z",
            "title": "656",
            "type_create": "MANUAL",
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": "2024-01-09T03:15Z"
        }
    ]
    "code": 200,
    "message": "request thành công."
}

"""

"""
@apiDefine filters
@apiParam	(filters:)  {Array}	    values			  Danh sách các giá trị cần filter(Body <code>Values</code>").

@apiParam	(filters:)  {String}	criteria_key		  Các key để filter task.
                                                              Allow values:
                                                              <ul>
                                                                <li><code>cri_title </code>: Tiêu để task</li>
                                                                <li><code>cri_assign_ids </code>: Danh sách ID định danh đối tượng được assign</li>
                                                                <li><code>cri_assign_type </code>: Kiểu đối tượng được assgin</li>
                                                                <li><code>cri_task_type </code>: Kiểu công việc</li>
                                                                <li><code>cri_priority_level </code>: Mức độ ưu tiên</li>
                                                                <li><code>cri_description </code>: Mô tả Task</li>
                                                                <li><code>cri_status_process </code>: Trạng thái xử lý</li>
                                                                <li><code>cri_created_time </code>: Thời gian tạo</li>
                                                                <li><code>cri_updated_time </code>: Thời gian sửa</li>
                                                                <li><code>cri_profile_ids </code>: Danh sách ID của profile</li>
                                                                <li><code>cri_company_ids </code>: Danh sách ID của company</li>
                                                                <li><code>cri_ticket_ids </code>: Danh sách ID của ticket</li>
                                                                <li><code>cri_order_ids </code>: Danh sách ID của order</li>
                                                                <li><code>cri_tag_ids </code>: Danh sách ID của tag</li>
                                                                <li><code>cri_account_mentions </code>: Danh sách ID account được nhắc đến (trong công việc + bình luận)</li>
                                                                <li><code>cri_created_by </code>: Người tạo</li>
                                                                <li><code>cri_updated_by </code>: Người sửa</li>
                                                                <li><code>cri_attachment_ids </code>: Danh sách ID file attachment</li>
                                                                <li><code>cri_complete_time </code>: Thời gian hoàn thành task</li>
                                                                <li><code>cri_time_change_status </code>: Thời gian thay đổi trạng thái task</li>
                                                                <li><code>cri_notification_config_type </code>: Kiểu thông báo</li>
                                                                <li><code>cri_source </code>: Nguồn tạo Task</li>
                                                                <li><code>cri_type_create </code>: Kiểu tạo Task</li>
                                                                <li><code>cri_planned_start_time </code>: Thời gian dự kiến bắt đầu</li>
                                                                <li><code>cri_is_new </code>: Ticket mới hay không</li>
                                                                <li><code>cri_task_parent_id</code>: <code>ID</code> công việc cha.</li>
                                                                <li><code>cri_create_or_assign_me</code>: Lọc theo điều kiện là người tạo công việc hoặc được giao là tài khoản đăng nhập</li>
                                                                <li><code>cri_create_or_assign_me_and_assign_team_member</code>: Lọc theo điều kiện là người tạo công việc hoặc được giao là tài khoản đăng nhập và member của team</li>
                                                                <li>Nhóm bộ lọc custom</li>
                                                                <li><code>cri_related_to</code>: Nhóm liên quan tới</li>
                                                                <li><code>cri_processing_time</code>: Nhóm thời hạn xử lý (Sắp tới/Quá hạn/Hôm nay/Ngày mai/Tuần này)</li>
                                                                <ul>
                                                                    <li>TODAY: Hôm nay</li>
                                                                    <li>TOMORROW: Ngày mai</li>
                                                                    <li>THIS_WEEK: Tuần này</li>
                                                                </ul>
                                                                <li><code>cri_deadline</code>: Nhóm thời hạn hoàn thành (Nhiều khung giờ)</li>
                                                                <li><code>cri_status_by_time</code>: Nhóm trạng thái xử lý theo thời gian</li>
                                                                <li><code>cri_team</code>: Bộ lọc theo team</li>
                                                              </ul>

@apiParam	(filters:)  {String}	operator_key		  Các toán tử để filter Task.
                                                              Allow value:
                                                              <code> 
                                                                "op_is_between", "op_is_greater_equal", "op_is_in",
                                                                 "op_is_equal", "op_is_greater", "op_is_has", 
                                                                 "op_is_has_not", "op_is_less_equal", "op_is_less" 
                                                              </code>  
                                                              
@apiParamExample    {json}      Values:
{
    "cri_related_to": ["profile_ids", "company_ids",...],
    "cri_processing_time": [
        "UPCOMING": sắp tới,    //"OVER_DUE": quá hạn
    ],
    "cri_deadline": [
        {
            "criteria_key": "cri_date"
            "operator_key": "",
            "values": [
                "2023-05-12T09:20:00.000Z", "2023-05-13T09:20:00.000Z"
            ],
            
        },
        {
            "criteria_key": "cri_hour"
            "operator_key": "",
            "values": [
                {
                    "from": "08:00",
                    "to": "09:00"
                }
            ]
        }
    ],
    "cri_status_by_time": [
        {
            "criteria_key": "cri_status_process"
            "operator_key": "",
            "values": [
                1   // Nhập giá trị nếu "Bất kì trạng thái nào được chọn" ngược lại rỗng "Bất kì giá trị nào"
            ]
        },
        {
            "criteria_key": "cri_time_change_status"
            "operator_key": "",
            "values": [1],        // Bất kì giá trị nào
                                  // [2, "day|month", "1"] Trong khoảng thời gian gần đây
                                  // [3, "2023-05-12T09:20:00.000Z", "2023-05-13T09:20:00.000Z"] Trong 1 khoảng thời gian cụ thể
        }
    ],
    "cri_team": [
        {
            "criteria_key": "cri_team_module"
            "operator_key": "op_is_in",
            "values": [
                "TICKET", "SALE" // Tên module
            ]
        },
        {
            "criteria_key": "cri_team_id"
            "operator_key": "op_is_in",
            "values": [""], // Danh sách team id
        },
        {
            "criteria_key": "cri_account_ids"
            "operator_key": "op_is_in",
            "values": [""], // Danh sách nhân viên id của team
        },
    ]
}       
"""

"""
@apiDefine ResponseDetailTaskUpgrade
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Dữ liệu trả về

@apiSuccess {string}             data._id                           <code>ID</code> task
@apiSuccess {array}             data.assign_ids                     Danh sách <code>ID</code> định danh đối tượng được assign 
@apiSuccess {string}             data.assign_type
                                                                    <ul>
                                                                        <li><code>STAFF:</code> nhân viên</li>
                                                                        <li><code>TEAM:</code> team</li>
                                                                    </ul>
@apiSuccess {array}             data.attachment_ids                 Danh sách <code>ID</code> các file được đính kèm với Task
@apiSuccess {array}             data.attachments
@apiSuccess {Array}             data.attachments.capacity     Dung lượng của file
@apiSuccess {Array}             data.attachments.format_file  Định dạng file
@apiSuccess {Array}             data.attachments.id           <code>ID</code> của file đính kèm
@apiSuccess {Array}             data.attachments.title        Tên file đính kèm
@apiSuccess {Array}             data.attachments.url          Link truy cập file đính kèm
@apiSuccess {bool}             data.calendar_sync_status          Trang thái sync task lên calendar
@apiSuccess {string}             data.child_task_id                  <code>ID</code> task con
@apiSuccess {string}             data.code_task                     Mã công việc
@apiSuccess {int}             data.comment_count                  Số lượng comment  của task
@apiSuccess {array}             data.company_ids                Danh sách các <code>ID</code> công ty liên quan tới 
@apiSuccess {string}             data.complete_time             Thời gian hoàn thành task
@apiSuccess {bool}             data.compulsory_completion       Bắt buộc hoàn thành
@apiSuccess {string}             data.created_by                ID người tạo task
@apiSuccess {string}             data.created_time              Thời gian tạo
@apiSuccess {string}             data.deadline_end_time         Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                </ul>
@apiSuccess {string}             data.deadline_start_time       Thời hạn bắt đầu công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                </ul>
@apiSuccess {string}             data.deadline_type
                                                                <ul>
                                                                    <li><code>time_slot</code>: Khung giờ</li>
                                                                    <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                </ul>
@apiSuccess {string}             data.description                    Noi dung task
@apiSuccess {array}             data.description_attachment_ids       Danh sách <code>ID</code> các file là đính kèm trong nội dung task
@apiSuccess {string}             data.file_usage_status
@apiSuccess {object}             data.follow_task                   Thông tin công việc tiếp nối
@apiSuccess {string}             data.id                            <code>ID</code> task
@apiSuccess {int}             data.is_new                           Field thể hiện task mới tạo       
@apiSuccess {string}             data.keywords
@apiSuccess {array}             data.mentions                       Danh sách người được nhắc tới
@apiSuccess {string}             data.merchant_id                   Định danh merchant
@apiSuccess {String}            data.notification_config_type      Kiểu thông báo
@apiSuccess {String}            data.notification_config_channels      Kênh thông báo
@apiSuccess {String}            data.notification_config_time_value      Giá trị thời gian thông báo
@apiSuccess {String}            data.notification_config_time_unit      Đơn vị thời gian thông báo
@apiSuccess {string}             data.object_id                         Định danh của phần tạo tác. Ví dụ ở SALE thì sẽ là định danh của đơn hàng.
@apiSuccess {array}             data.order_ids                                      Danh sách các <code>ID</code> order liên quan tới
@apiSuccess {string}             data.planned_start_time                        Thời gian dự kiến bắt đầu
@apiSuccess {int}             data.priority_level                               Mức độ ưu tiên
                                                                                <ul>
                                                                                        <li><code>1:</code> Cao</li>
                                                                                        <li><code>2:</code> Trung bình</li>
                                                                                        <li><code>3:</code> Thấp</li>
                                                                                </ul>
@apiSuccess {array}             data.profile_ids                                        Danh sách các <code>ID</code> profile liên quan tới
@apiSuccess {object}             data.related_to                        Thông tin liên quan tới
@apiSuccess {object}             data.related_to.profile_ids            Danh sách profile liên quan tới
@apiSuccess {object}             data.related_to.order_ids              Danh sách đơn hàng liên quan tới 
@apiSuccess {object}             data.related_to.company_ids            Danh sách công ty liên quan tới
@apiSuccess {object}             data.related_to.ticket_ids             Danh sách ticket liên quan tới
@apiSuccess {object}             data.related_to_not_delete             Thông tin liên quan tới không được xóa
@apiSuccess {object}             data.related_to_not_delete.profile_ids            Danh sách profile liên quan tới
@apiSuccess {object}             data.related_to_not_delete.order_ids              Danh sách đơn hàng liên quan tới 
@apiSuccess {object}             data.related_to_not_delete.company_ids            Danh sách công ty liên quan tới
@apiSuccess {object}             data.related_to_not_delete.ticket_ids             Danh sách ticket liên quan tới
@apiSuccess {bool}             data.required_push_socket_overdue                   Trạng thái có bắt buộc push socket khi đến hạn không
@apiSuccess {string}             data.source                                            Định danh nguồn tạo Task
@apiSuccess {int}             data.status_process                                       Trạng thái xử lý  
                                                                                        <ul>
                                                                                            <li><code>1:</code> Chưa thực hiện </li>
                                                                                            <li><code>2:</code> Đang thực hiện </li>
                                                                                            <li><code>3:</code> Đang chờ </li>
                                                                                            <li><code>4:</code> Tạm hoãn </li>
                                                                                            <li><code>5:</code> Hoàn thành </li>
                                                                                        </ul>
@apiSuccess {string}             data.tab                                               tab trong giao diện
@apiSuccess {array}             data.tag_ids                                            Danh sách các <code>ID</code> tag liên quan tới
@apiSuccess {string}             data.task_parent_id                                    <code>ID</code> task cha
@apiSuccess {string}             data.task_type                                          Kiểu task
@apiSuccess {array}             data.ticket_ids                                         Danh sách các <code>ID</code> ticket liên quan tới
@apiSuccess {string}             data.time_change_status                                Thời gian thay đổi trạng thái task
@apiSuccess {string}             data.time_check_status                                    Phân loại task quá hạn theo field này
@apiSuccess {string}             data.title                                            Tên task
@apiSuccess {string}             data.type_create                                       Kiểu tạo task
@apiSuccess {string}             data.updated_by                                        ID người update task gần nhất
@apiSuccess {string}             data.updated_time                                      Thời gian update task gần nhất
@apiSuccess {Object}             data.config_repeat                        Cấu hình có lặp task không?
@apiSuccess   {Int=0,1}          data.config_repeat.is_use                 Có sử dụng cấu hình không?
                                                                            <ul>
                                                                                <li><code>0</code>: không sử dụng</li>
                                                                                <li><code>1</code>: sử dụng</li>
                                                                            </ul>
@apiSuccess {object}          data.config_repeat.start                   Cấu hình bắt đầu lặp task
@apiSuccess {String}          data.config_repeat.start.type              Kiểu lặp
                                                                            <ul>
                                                                                <li><code>day</code>: Ngày</li>
                                                                                <li><code>week</code>: Tuần</li>
                                                                                <li><code>month</code>: Tháng</li>
                                                                                <li><code>year</code>: Năm</li>
                                                                            </ul>
@apiSuccess {Array}          data.config_repeat.start.values            Cấu hình thông tin lặp vào (Ví dụ: thứ 2, thứ 3)
                                                                            <ul>
                                                                                <li> Case: type=<code>day</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                                <li> Case: type=<code>week</code> thì <code>values: danh sách các thứ trong tuần - ví dụ ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]</code></li>
                                                                                <li> Case: type=<code>month</code> thì <code>values: danh sách các ngày trong tháng (Mặc định là 31 ngày) - ví dụ [1,2,3,4,5,6,7]</code></li>
                                                                                <li> Case: type=<code>year</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                            </ul>
@apiSuccess {int}           data.config_repeat.start.frequency           Tần suất lặp
@apiSuccess {object}          data.config_repeat.stop                   Cấu hình dừng lặp task
@apiSuccess {String}          data.config_repeat.stop.type              Loại dừng
                                                                            <ul>
                                                                                <li><code>never</code>: Không bao giờ dừng</li>
                                                                                <li><code>on_the_day</code>: Vào ngày</li>
                                                                                <li><code>after_number_times</code>: Sau số lần</li>
                                                                            </ul>
@apiSuccess {Array}          data.config_repeat.stop.value             Giá trị của type tương ứng
                                                                            <ul>
                                                                                <li>Với type=<code>never</code>: thì value để là None hoặc string rỗng</li>
                                                                                <li>Với type=<code>on_the_day</code>: thì value string và có format sau: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                                <li>Với type=<code>after_number_times</code>: thì value để số</li>
                                                                            </ul>

"""
# ---------- Thêm Task ----------------------
"""
@api {POST} /task/api/v1.0/tasks/actions                    Thêm Task
@apiDescription Path api: /tasks/v2 sẽ được call chung vào path này
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName AddTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse ParamQueryDisableCheckAccountAccess

@apiParam   (BODY:) {string}    title                         Tiêu đề của Task
@apiParam   (BODY:) {string}    [task_parent_id]              <code>ID</code> công việc cha
@apiParam   (BODY:) {string}    description                   Nội dung của Task
@apiParam   (BODY:) {Array}     assign_ids                    Định danh đối tượng được Assign
@apiParam   (BODY:) {String=STAFF,TEAM}    assign_type=STAFF                            Kiểu đối tượng được Assign
@apiParam   (BODY:) {string}    notification_config_type      Loại cấu hình
                                                                    <ul>
                                                                        <li><code>NO_REMINDER:</code>Không nhắc nhở</li>
                                                                        <li><code>ADVANCE_REMINDER:</code>Nhắc nhở trước</li>
                                                                        <li><code>TIMELY_REMINDER:</code>nhắc nhở đúng thời hạn</li>
                                                                        <li><code>OPTION:</code>Tùy chọn</li>
                                                                    </ul>
@apiParam   (BODY:) {string}    [notification_config_time_value]      Thời gian cấu hình
                                                                        <ul>
                                                                            <li>Với type là <code>NO_REMINDER:</code>Không cần truyền time_value</li>
                                                                            <li>Với type là <code>ADVANCE_REMINDER:</code>Cần truyền số phút muốn thông báo nhắc nhở trước lên</li>
                                                                            <li>Với type là <code>TIMELY_REMINDER:</code>Không cần truyền time_value</li>
                                                                            <li>Với type là <code>OPTION:</code>Truyền lên ngày giờ được chọn. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                        </ul> 
@apiParam   (BODY:) {string}    [notification_config_time_unit]        Đơn vị của thời gian cấu hình. Áp dụng với type: <code>ADVANCE_REMINDER</code> 
                                                                            <ul>
                                                                                <li><code>minute:</code> đơn vị tính là phút</li>
                                                                                <li><code>hour:</code> đơn vị tính là giờ</li>
                                                                                <li><code>year:</code> đơn vị tính là năm</li>
                                                                            <ul> 


@apiParam   (BODY:) {string=general,call,sent_email,metting}    task_type           Kiểu công việc
                                                                        <ul>
                                                                            <li><code>GENERAL:</code>Chung</li>
                                                                            <li><code>SENT_EMAIL:</code>Gửi email</li>
                                                                            <li><code>CALL:</code>Gọi điện</li>
                                                                            <li><code>MEETING:</code>Hẹn gặp</li>
                                                                        </ul>

@apiParam (BODY:) {int=1,2,3}     priority_level      Mức độ ưu tiên
                                                    <ul>
                                                        <li><code>1:</code> Cao </li>
                                                        <li><code>2:</code> Trung bình </li>
                                                        <li><code>3:</code> Thấp </li>
                                                    </ul>      

@apiParam (BODY:) {int=1,2,3,4,5}     status_process      Trạng thái xử lý
                                                    <ul>
                                                        <li><code>1:</code> Chưa thực hiện </li>
                                                        <li><code>2:</code> Đang thực hiện </li>
                                                        <li><code>3:</code> Đang chờ </li>
                                                        <li><code>4:</code> Tạm hoãn </li>
                                                        <li><code>5:</code> Hoàn thành </li>
                                                    </ul>   
    
@apiParam   (BODY:) {Array}     [attachment_ids]                Danh sách <code>ID</code> file đính kèm           
@apiParam   (BODY:) {String}    source                        Nguồn tạo. Tức là đang đứng ở Module nào thì sẽ cần truyền vào Module đó. Ví dụ: <code>SALE</code>
@apiParam   (BODY:) {String}    [object_id]                   Định danh source tạo task. <code>Default: </code>task id được tạo. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {boolean}   compulsory_completion        Bắt buộc hoàn thành task(<code>False:</code> Không, <code>True:</code> Có) 
@apiParam   (BODY:) {String}    [planned_start_time]          Dự kiến thời gian bắt đầu
@apiParam   (BODY:) {String}    [calendar_sync_status]        Trạng thái đồng bộ lên calendar

@apiParam   (BODY:) {Array}    [company_ids]                  Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    [ticket_ids]                   Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    [order_ids]                     Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    [profile_ids]                  Danh sách profile liên quan tới.
@apiParam   (BODY:) {Array}    [tag_ids]                      Danh sách tag liên quan tới.

@apiParam   (BODY:) {Array}    [mentions]                             Danh sách người được mentions
@apiParam   (BODY:) {String}   [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiParam   (BODY:) {String}   [mentions.account_id]                  <code>ID</code> của người được mentions
@apiParam   (BODY:) {Array}    [description_attachment_ids]            Danh sách <code>ID</code> file được upload trong nội dung của Task

@apiParam   (BODY:) {Object}   [follow_task]                  Tạo công việc tiếp nối.
@apiParam   (BODY:) {Int}      [follow_task.status]             Trạng thái tạo công việc tiếp nối.
                                                                 <ul>
                                                                    <li>0: là không tạo task</li>
                                                                    <li>1: là tạo task</li>
                                                                 </ul>
@apiParam   (BODY:) {String}   [follow_task.title]             Tiêu đề công việc tiếp nối
@apiParam   (BODY:) {String}   [follow_task.deadline_type]     Loại thời hạn hoàn thành của công việc tiếp nối
                                                                <ul>
                                                                    <li><code>time_slot</code>: Khung giờ</li>
                                                                    <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                    <li><code>at_specific_time</code>: Vào giờ cụ thể</li>
                                                                </ul>
@apiParam   (BODY:) {String}    [follow_task.deadline_start_time]      Thời hạn hoàn thành công việc.
                                                                        <ul>
                                                                            <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                            <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                            <li>Trong trường hợp type=<code>at_specific_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                        </ul>
@apiParam   (BODY:) {Object}    [follow_task.deadline_end_time]        Thời hạn hoàn thành công việc.
                                                                        <ul>
                                                                            <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                            <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                            <li>Trong trường hợp type=<code>at_specific_time</code> thì không cần truyền giá trị này</li>
                                                                        </ul>

@apiParam   (BODY:) {String}    deadline_type            Loại thời hạn hoàn thành của công việc tiếp nối
                                                                        <ul>
                                                                            <li><code>time_slot</code>: Khung giờ</li>
                                                                            <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                            <li><code>at_specific_time</code>: Vào giờ cụ thể</li>
                                                                        </ul>
@apiParam   (BODY:) {String}    deadline_start_time      Thời hạn Bắt đầu công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                    <li>Trong trường hợp type=<code>at_specific_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                </ul>
@apiParam   (BODY:) {String}    deadline_end_time        Thời hạn Kết thúc công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                    <li>Trong trường hợp type=<code>at_specific_time</code> thì không cần truyền giá trị này.</li>
                                                                </ul>
@apiParam   (BODY:) {Object}            related_to_not_delete       Thông tin liên quan tới không được xóa. 
                                                                    <code>Khi đứng tại module công việc tạo task thì sẽ không cần truyền</code>
                                                                    <code>Khi đứng tại các module khác như: Công ty, Đơn hàng, Profile, Ticket thì sẽ phải truyền</code>
@apiParam   (BODY:) {Array}            related_to_not_delete.profile_ids    Danh sách ID profile không được xoá. Chính là giá trị của object_id đối với đối tượng source là PROFILE                                                                                                                              
@apiParam   (BODY:) {Array}            related_to_not_delete.company_ids    Danh sách ID công ty không được xoá. Chính là giá trị của object_id đối với đối tượng source là COMPANY                                                                                                                              
@apiParam   (BODY:) {Array}            related_to_not_delete.order_ids      Danh sách ID đơn hàng không được xoá. Chính là giá trị của object_id đối với đối tượng source là SALE                                                                                                                           
@apiParam   (BODY:) {Array}            related_to_not_delete.ticket_ids     Danh sách ID ticket không được xoá.  Chính là giá trị của object_id đối với đối tượng source là TICKET
@apiParam   (BODY:) {Object}           config_repeat                        Cấu hình có lặp task không?
@apiParam   (BODY:) {Int=0,1}          config_repeat.is_use                 Có sử dụng cấu hình không?
                                                                            <ul>
                                                                                <li><code>0</code>: không sử dụng</li>
                                                                                <li><code>1</code>: sử dụng</li>
                                                                            </ul>
@apiParam   (BODY:) {object}          config_repeat.start                   Cấu hình bắt đầu lặp task
@apiParam   (BODY:) {String}          config_repeat.start.type              Kiểu lặp
                                                                            <ul>
                                                                                <li><code>day</code>: Ngày</li>
                                                                                <li><code>week</code>: Tuần</li>
                                                                                <li><code>month</code>: Tháng</li>
                                                                                <li><code>year</code>: Năm</li>
                                                                            </ul>
@apiParam   (BODY:) {Array}          config_repeat.start.values            Cấu hình thông tin lặp vào (Ví dụ: thứ 2, thứ 3)
                                                                            <ul>
                                                                                <li> Case: type=<code>day</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                                <li> Case: type=<code>week</code> thì <code>values: danh sách các thứ trong tuần - ví dụ ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]</code></li>
                                                                                <li> Case: type=<code>month</code> thì <code>values: danh sách các ngày trong tháng (Mặc định là 31 ngày) - ví dụ [1,2,3,4,5,6,7]</code></li>
                                                                                <li> Case: type=<code>year</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                            </ul>
@apiParam   (BODY:) {int}           config_repeat.start.frequency           Tần suất lặp
@apiParam   (BODY:) {object}          config_repeat.stop                   Cấu hình dừng lặp task
@apiParam   (BODY:) {String}          config_repeat.stop.type              Loại dừng
                                                                            <ul>
                                                                                <li><code>never</code>: Không bao giờ dừng</li>
                                                                                <li><code>on_the_day</code>: Vào ngày</li>
                                                                                <li><code>after_number_times</code>: Sau số lần</li>
                                                                            </ul>
@apiParam   (BODY:) {Array}          config_repeat.stop.value             Giá trị của type tương ứng
                                                                            <ul>
                                                                                <li>Với type=<code>never</code>: thì value để là None hoặc string rỗng</li>
                                                                                <li>Với type=<code>on_the_day</code>: thì value string và có format sau: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                                <li>Với type=<code>after_number_times</code>: thì value để số</li>
                                                                            </ul>


                                                                    
                                                                                                                              

@apiParamExample    {json}      BODY:
{
    "source": "TICKET",
    "object_id": "64186cdcd0d9b300124f6178",
    "task_parent_id": "",
    "title": "anh_2",
    "assign_ids": ["7fc0a33c-baf5-11e7-a7c2-0242ac180003"],
    "assign_type": "STAFF",
    "task_type": "GENERAL",
    "priority_level": 1,
    "status_process": 1,
    "description": "<div>fnosnado</div>",
    "notification_config_type": "NO_REMINDER",
    "attachment_ids": [],
    "profile_ids": [],
    "company_ids": [],
    "order_ids": [],
    "ticket_ids": ["64186cdcd0d9b300124f6178"],
    "mentions": [],
    "description_attachment_ids": [],
    "follow_task": {},
    "planned_start_time": "2023-03-22T08:03Z",
    "deadline_type": "time_slot",
    "deadline_start_time": "2023-03-22T08:03Z",
    "deadline_end_time": "2023-03-22T08:03Z",
    "compulsory_completion": false,
    "related_to_not_delete": {
        "profile_ids": [],
        "company_ids": [],
        "order_ids": [],
        "ticket_ids": ["64186cdcd0d9b300124f6178"]
    },
    "config_repeat": {
	"is_use": 1,
	"start": { 
		"type": "day",
		"values": [],
		"frequency": 1
	},
	"stop": { 
		"type": "never",
		"value": ""
	}
}
}                               


@apiUse ResponseDetailTaskUpgrade

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "_id": "659ceebe5016dc6bede24f98",
        "assign_ids": [
            "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        ],
        "assign_type": "STAFF",
        "attachment_ids": [],
        "attachments": [],
        "calendar_sync_status": null,
        "child_task_id": null,
        "code_task": "KC53Y2HJ",
        "comment_count": 0,
        "company_ids": [],
        "complete_time": null,
        "compulsory_completion": false,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2024-01-09T06:59Z",
        "deadline_end_time": "2024-01-09T00:00Z",
        "deadline_start_time": "2024-01-09T00:00Z",
        "deadline_type": "unspecified_time",
        "description": "<div>123</div>",
        "description_attachment_ids": [],
        "file_usage_status": "ARCHIVED",
        "follow_task": {
            "deadline_type": "",
            "status": 0
        },
        "id": "659ceebe5016dc6bede24f98",
        "is_new": 1,
        "keywords": "666",
        "mentions": [],
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "notification_config_channels": [
            "email"
        ],
        "notification_config_time_unit": null,
        "notification_config_time_value": null,
        "notification_config_type": "NO_REMINDER",
        "object_id": "659ceebe5016dc6bede24f98",
        "order_ids": [],
        "planned_start_time": "2024-01-09T06:58Z",
        "priority_level": 1,
        "profile_ids": [
            "8adbd6b6-be87-4fea-9da4-748d70c8be06"
        ],
        "related_to": {
            "company_ids": [],
            "order_ids": [],
            "profile_ids": [
                "8adbd6b6-be87-4fea-9da4-748d70c8be06"
            ],
            "ticket_ids": []
        },
        "related_to_not_delete": {},
        "required_push_socket_overdue": true,
        "source": "TASK",
        "status_process": 1,
        "tab": "UPCOMING",
        "tag_ids": [],
        "task_parent_id": "",
        "task_type": "GENERAL",
        "ticket_ids": [],
        "time_change_status": "2024-01-09T06:59Z",
        "time_check_status": "2024-01-09T16:59Z",
        "title": "666",
        "type_create": "MANUAL",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_time": "2024-01-09T06:59Z",
        "config_repeat": {
	        "is_use": 1,
	        "start": { 
		        "type": "day",
		        "values": [],
		        "frequency": 1
	        },
	        "stop": { 
		        "type": "never",
		        "value": ""
	        }
        }
}
"""

# ---------- Cập nhật chi tiết của Task-----------
"""
@api {PUT} /task/api/v1.0/task/actions/<task_id>                    Cập nhật chi tiết Task
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName UpdateDetailTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (BODY:) {string}    [title]                         Tiêu đề của Task
@apiParam   (BODY:) {string}    [task_parent_id]                <code>ID</code> công việc cha
@apiParam   (BODY:) {string}    [description]                   Nội dung của Task
@apiParam   (BODY:) {Array}     assign_ids                    Định danh đối tượng được Assign
@apiParam   (BODY:) {String=STAFF,TEAM}    assign_type=STAFF                            Kiểu đối tượng được Assign
@apiParam   (BODY:) {string}    [notification_config_type]      Loại cấu hình
                                                                    <ul>
                                                                        <li><code>NO_REMINDER:</code>Không nhắc nhở</li>
                                                                        <li><code>ADVANCE_REMINDER:</code>Nhắc nhở trước</li>
                                                                        <li><code>TIMELY_REMINDER:</code>nhắc nhở đúng thời hạn</li>
                                                                        <li><code>OPTION:</code>Tùy chọn</li>
                                                                    </ul>
@apiParam   (BODY:) {string}    [notification_config_time_value]      Thời gian cấu hình
                                                                        <ul>
                                                                            <li>Với type là <code>NO_REMINDER:</code>Không cần truyền time_value</li>
                                                                            <li>Với type là <code>ADVANCE_REMINDER:</code>Cần truyền số phút muốn thông báo nhắc nhở trước lên</li>
                                                                            <li>Với type là <code>TIMELY_REMINDER:</code>Không cần truyền time_value</li>
                                                                            <li>Với type là <code>OPTION:</code>Truyền lên ngày giờ được chọn. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                        </ul> 
@apiParam   (BODY:) {string}    [notification_config_time_unit]        Đơn vị của thời gian cấu hình. Áp dụng với type: <code>ADVANCE_REMINDER</code> 
                                                                            <ul>
                                                                                <li><code>minute:</code> đơn vị tính là phút</li>
                                                                                <li><code>hour:</code> đơn vị tính là giờ</li>
                                                                                <li><code>year:</code> đơn vị tính là năm</li>
                                                                            <ul> 


@apiParam   (BODY:) {string=general,call,sent_email,metting}    task_type           Kiểu công việc
                                                                        <ul>
                                                                            <li><code>GENERAL:</code>Chung</li>
                                                                            <li><code>SENT_EMAIL:</code>Gửi email</li>
                                                                            <li><code>CALL:</code>Gọi điện</li>
                                                                            <li><code>MEETING:</code>Hẹn gặp</li>
                                                                        </ul>

@apiParam (BODY:) {int=1,2,3}     [priority_level]      Mức độ ưu tiên
                                                    <ul>
                                                        <li><code>1:</code> Cao </li>
                                                        <li><code>2:</code> Trung bình </li>
                                                        <li><code>3:</code> Thấp </li>
                                                    </ul>      

@apiParam (BODY:) {int=1,2,3,4,5}     [status_process]      Trạng thái xử lý
                                                    <ul>
                                                        <li><code>1:</code> Chưa thực hiện </li>
                                                        <li><code>2:</code> Đang thực hiện </li>
                                                        <li><code>3:</code> Đang chờ </li>
                                                        <li><code>4:</code> Tạm hoãn </li>
                                                        <li><code>5:</code> Hoàn thành </li>
                                                    </ul>   
    
@apiParam   (BODY:) {Array}     [attachment_ids]                Danh sách <code>ID</code> file đính kèm  
@apiParam   (BODY:) {String}    [complete_time]                 Thời gian Hoàn thành của Task
@apiParam   (BODY:) {boolean}   [compulsory_completion]        Bắt buộc hoàn thành task(<code>false:</code> Không, <code>true:</code> Có) 
@apiParam   (BODY:) {String}    [planned_start_time]          Dự kiến thời gian bắt đầu
@apiParam   (BODY:) {String}    [calendar_sync_status]        Trạng thái đồng bộ lên calendar

@apiParam   (BODY:) {Array}    [company_ids]                  Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    [ticket_ids]                   Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    [order_ids]                     Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    [profile_ids]                  Danh sách profile liên quan tới.
@apiParam   (BODY:) {Array}    [tag_ids]                      Danh sách tag liên quan tới.

@apiParam   (BODY:) {Array}    [mentions]                             Danh sách người được mentions
@apiParam   (BODY:) {String}   [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiParam   (BODY:) {String}   [mentions.account_id]                  <code>ID</code> của người được mentions
@apiParam   (BODY:) {Array}    [description_attachment_ids]            Danh sách <code>ID</code> file được upload trong nội dung của Task


@apiParam   (BODY:) {Object}    [deadline]                 Thời hạn hoàn thành của task công việc tiếp nối
@apiParam   (BODY:) {String}    [deadline_type]            Loại thời hạn hoàn thành của công việc tiếp nối
                                                                        <ul>
                                                                            <li><code>time_slot</code>: Khung giờ</li>
                                                                            <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                            <li><code>at_specific_time</code>: Không xác định thời gian</li>
                                                                        </ul>
@apiParam   (BODY:) {String}    [deadline_start_time]      Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                    <li>Trong trường hợp type=<code>at_specific_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                </ul>
@apiParam   (BODY:) {Object}    [deadline_end_time]        Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                    <li>Trong trường hợp type=<code>at_specific_time</code> thì không cần truyền giá trị này.</li>
                                                                </ul>
@apiParam   (BODY:) {Object}           config_repeat                        Cấu hình có lặp task không?
@apiParam   (BODY:) {Int=0,1}          config_repeat.is_use                 Có sử dụng cấu hình không?
                                                                            <ul>
                                                                                <li><code>0</code>: không sử dụng</li>
                                                                                <li><code>1</code>: sử dụng</li>
                                                                            </ul>
@apiParam   (BODY:) {object}          config_repeat.start                   Cấu hình bắt đầu lặp task
@apiParam   (BODY:) {String}          config_repeat.start.type              Kiểu lặp
                                                                            <ul>
                                                                                <li><code>day</code>: Ngày</li>
                                                                                <li><code>week</code>: Tuần</li>
                                                                                <li><code>month</code>: Tháng</li>
                                                                                <li><code>year</code>: Năm</li>
                                                                            </ul>
@apiParam   (BODY:) {Array}          config_repeat.start.values            Cấu hình thông tin lặp vào (Ví dụ: thứ 2, thứ 3)
                                                                            <ul>
                                                                                <li> Case: type=<code>day</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                                <li> Case: type=<code>week</code> thì <code>values: danh sách các thứ trong tuần - ví dụ ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]</code></li>
                                                                                <li> Case: type=<code>month</code> thì <code>values: danh sách các ngày trong tháng (Mặc định là 31 ngày) - ví dụ [1,2,3,4,5,6,7]</code></li>
                                                                                <li> Case: type=<code>year</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                            </ul>
@apiParam   (BODY:) {int}           config_repeat.start.frequency           Tần suất lặp
@apiParam   (BODY:) {object}          config_repeat.stop                   Cấu hình dừng lặp task
@apiParam   (BODY:) {String}          config_repeat.stop.type              Loại dừng
                                                                            <ul>
                                                                                <li><code>never</code>: Không bao giờ dừng</li>
                                                                                <li><code>on_the_day</code>: Vào ngày</li>
                                                                                <li><code>after_number_times</code>: Sau số lần</li>
                                                                            </ul>
@apiParam   (BODY:) {Array}          config_repeat.stop.value             Giá trị của type tương ứng
                                                                            <ul>
                                                                                <li>Với type=<code>never</code>: thì value để là None hoặc string rỗng</li>
                                                                                <li>Với type=<code>on_the_day</code>: thì value string và có format sau: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                                <li>Với type=<code>after_number_times</code>: thì value để số</li>
                                                                            </ul>
@apiParamExample    {json}      BODY:
{
        "_id": "659ceebe5016dc6bede24f98",
        "assign_ids": [
            "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        ],
        "assign_type": "STAFF",
        "attachment_ids": [],
        "attachments": [],
        "calendar_sync_status": null,
        "child_task_id": null,
        "code_task": "KC53Y2HJ",
        "comment_count": 0,
        "company_ids": [],
        "complete_time": null,
        "compulsory_completion": false,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2024-01-09T06:59Z",
        "deadline_end_time": "2024-01-09T00:00Z",
        "deadline_start_time": "2024-01-09T00:00Z",
        "deadline_type": "unspecified_time",
        "description": "<div>123</div>",
        "description_attachment_ids": [],
        "file_usage_status": "ARCHIVED",
        "follow_task": {
            "deadline_type": "",
            "status": 0
        },
        "id": "659ceebe5016dc6bede24f98",
        "is_new": 1,
        "keywords": "666",
        "mentions": [],
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "notification_config_channels": [
            "email"
        ],
        "notification_config_time_unit": null,
        "notification_config_time_value": null,
        "notification_config_type": "NO_REMINDER",
        "object_id": "659ceebe5016dc6bede24f98",
        "order_ids": [],
        "planned_start_time": "2024-01-09T06:58Z",
        "priority_level": 1,
        "profile_ids": [
            "8adbd6b6-be87-4fea-9da4-748d70c8be06"
        ],
        "related_to": {
            "company_ids": [],
            "order_ids": [],
            "profile_ids": [
                "8adbd6b6-be87-4fea-9da4-748d70c8be06"
            ],
            "ticket_ids": []
        },
        "related_to_not_delete": {},
        "required_push_socket_overdue": true,
        "source": "TASK",
        "status_process": 1,
        "tab": "UPCOMING",
        "tag_ids": [],
        "task_parent_id": "",
        "task_type": "GENERAL",
        "ticket_ids": [],
        "time_change_status": "2024-01-09T06:59Z",
        "time_check_status": "2024-01-09T16:59Z",
        "title": "666",
        "type_create": "MANUAL",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_time": "2024-01-09T06:59Z",
        "config_repeat": {
            "is_use": 1,
            "start": {
                "type": "day",
                "values": [],
                "frequency": 1
            },
            "stop": {
                "type": "never",
                "value": ""
            }
        }
}                                    
                                     


@apiUse ResponseDetailTaskUpgrade

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "account_mentions": [],
        "assign_ids": [
            "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        ],
        "assign_type": "STAFF",
        "attachment_ids": [],
        "calendar_sync_status": null,
        "company_ids": [],
        "complete_time": null,
        "compulsory_completion": false,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2023-03-22T08:03Z",
        "deadline_end_time": "2023-03-22T08:03Z",
        "deadline_start_time": "2023-03-22T08:03Z",
        "deadline_type": "time_slot",
        "description": "<div>fnosnado</div>",
        "description_attachment_ids": [],
        "file_usage_status": "ARCHIVED",
        "follow_task": {},
        "id": "641ab667fb252c0395897514",
        "task_parent_id": "",
        "code_task": ""
        "is_new": 1,
        "keywords": "anh_2",
        "mentions": [],
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "notification_config_channels": [
            "email"
        ],
        "notification_config_time_unit": null,
        "notification_config_time_value": null,
        "notification_config_type": "NO_REMINDER",
        "object_id": "64186cdcd0d9b300124f6178",
        "order_ids": [],
        "planned_start_time": "2023-03-22T08:03Z",
        "priority_level": 1,
        "profile_ids": [],
        "source": "TICKET",
        "status_process": 1,
        "tag_ids": [],
        "task_type": "GENERAL",
        "ticket_ids": [
            "64186cdcd0d9b300124f6178"
        ],
        "time_check_status": "2023-03-22T08:03Z",
        "title": "anh_2",
        "type_create": "MANUAL",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_time": "2023-03-22T08:03Z",
        "config_repeat": {
            "is_use": 1,
            "start": {
                "type": "day",
                "values": [],
                "frequency": 1
            },
            "stop": {
                "type": "never",
                "value": ""
            }
        }
    }
}
"""
# ---------- Xóa nhiều Task -----------
"""
@api {POST} /task/api/v1.0/tasks/action/delete          Xóa nhiều task
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName DeleteTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(Body:)			{Array}     ids             Danh sách các <code>ID</code> tasks cần xóa

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {String}            data                        Thông tin phản hồi
@apiSuccess {String}            data.number_task_delete_success                 Số lượng task xóa thành công                   
@apiSuccess {String}            data.lst_task_fail                        Số lượng task xóa thất bại

@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,,
    "data": {
        "number_task_delete_success": 0,
        "lst_task_fail": 3
    }
    "message": "request thành công."
}
"""

"""
@api {POST} /task/api/v1.0/tasks/v2/actions/create/check-abac-follow-task          check quyền tạo cv tiếp nối
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName CheckAbacFollowTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParamExample    {json}      Body Example:
{
    "source:": "TICKET",                   //required
    "account_id":"7fc0a33c-baf5-11e7-a7c2-0242ac180003"  //required
    "status": 1,
    "title": "dedede",
    "deadline_type": "unspecified_time",    
    "deadline_start_time": "2023-07-27"    
}

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

# ---------- Lấy Danh sách task theo filters -----------
"""
@api {POST} /task/api/v1.0/tasks/v2/action/filter  Lấy danh sách task V2
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName GetListTaskFilter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse ParamQueryDisableCheckAccountAccess

@apiUse filters


@apiUse GetListTaskFilterParamAndResponse                                                              
@apiUse paging_tokens


"""
# ---------- Lấy Danh sách task theo filters từ module task-----------
"""
@api {POST} /task/api/v1.0/tasks/v2/action/list-from-task/filter  Lấy danh sách task V2 từ task
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName GetListTaskFilterFromTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse ParamQueryDisableCheckAccountAccess

@apiUse filters

@apiUse GetListTaskFilterParamAndResponse                                                              
@apiUse paging_tokens


"""

# ---------- Lấy số lượng Task theo thẻ save view -----------
"""
@api {POST} /task/api/v1.0/save-views/tasks/count         Lấy số lượng task theo thẻ save view
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName GetListTaskBySaveView

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(BODY:)			{String}	[search]			      Từ khoá tìm kiếm theo tên task, ...
@apiParam	(BODY:)			{Array}	    [filters]	          Danh sách các điều kiện lọc task
@apiParam	(BODY:)			{String}	    [view_config_id]			  ID của thẻ save view

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {String}            data                        Thông tin phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "view_config_id": "64186cdcd0d9b300124f6178",
        "count": 1
    },
    "message": "request thành công."
}
"""

# ---------- Chuyển trạng thái nhiều task -----------
"""
@api {POST} /task/api/v1.0/tasks/change-status-process         Chuyển trạng thái xử lý nhiều task
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName ChangeStatusProcessTasks

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(BODY:)			{Array}	    task_ids			      Thông tin Task cần chuyển
@apiParam	(BODY:)			{Int}	    to_status_process			  Value trạng thái xử lý chuyển đến

@apiParamExample {json} Body example
{
    "task_ids": ["8a841218-6e36-4565-a0d2-41f0896df548", "8a841218-6e36-4565-a0d2-41f0896df547"]
    "to_status_process": 1
}


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Object}            data                        Thông tin phản hồi
@apiSuccess {Array}             data.task_ids                 Danh sách ID task chuyển thành công
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "task_ids": ["8a841218-6e36-4565-a0d2-41f0896df548", "8a841218-6e36-4565-a0d2-41f0896df547"]
    },
    "message": "request thành công."
}
"""

"""
@api {POST} /task/api/v1.0/tasks/actions/find-by-ids                    Lấy thông tin Task theo ids
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName GetListTaskByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:) {Array}    [list_task_id]                    Danh sách id của task cần tìm 
@apiParam   (Query:) {Array}    [fields]                         Danh sách fields cần lấy dữ liệu (<code>Default: </code> all field)


@apiUse ResponseDetailTaskUpgrade

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "account_mentions": [],
            "assign_ids": [
                "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            ],
            "assign_type": "STAFF",
            "attachment_ids": [],
            "calendar_sync_status": null,
            "company_ids": [],
            "complete_time": null,
            "compulsory_completion": false,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2023-03-22T08:03Z",
            "deadline_end_time": "2023-03-22T08:03Z",
            "deadline_start_time": "2023-03-22T08:03Z",
            "deadline_type": "time_slot",
            "description": "<div>fnosnado</div>",
            "description_attachment_ids": [],
            "file_usage_status": "ARCHIVED",
            "follow_task": {},
            "id": "641ab667fb252c0395897514",
            "task_parent_id": "",
            "code_task": "",
            "is_new": 1,
            "keywords": "anh_2",
            "mentions": [],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config_channels": [
                "email"
            ],
            "notification_config_time_unit": null,
            "notification_config_time_value": null,
            "notification_config_type": "NO_REMINDER",
            "object_id": "64186cdcd0d9b300124f6178",
            "order_ids": [],
            "planned_start_time": "2023-03-22T08:03Z",
            "priority_level": 1,
            "profile_ids": [],
            "source": "TICKET",
            "status_process": 1,
            "tag_ids": [],
            "task_type": "GENERAL",
            "ticket_ids": [
                "64186cdcd0d9b300124f6178"
            ],
            "time_check_status": "2023-03-22T08:03Z",
            "title": "anh_2",
            "type_create": "MANUAL",
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": "2023-03-22T08:03Z"
        }
    ]
}
"""

"""
@api {GET} /task/api/v1.0/task/actions/<task_id>                    Lấy thông tin chi tiết Task
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName GetDetailTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:) {String}    [fields]                         Danh sách fields cần lấy dữ liệu (<code>Default: </code> all field)


@apiUse ResponseDetailTaskUpgrade
"""

"""
@api {GET} /task/api/v1.0/task/actions/read-from-task/<task_id>                    Lấy thông tin chi tiết Task từ module task
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName GetDetailTaskFromTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:) {String}    [fields]                         Danh sách fields cần lấy dữ liệu (<code>Default: </code> all field)


@apiUse ResponseDetailTaskUpgrade

@apiUse GetDetailTaskResponse
"""

"""
@api {GET} /task/api/v1.0/task/actions/read-from-other/<task_id>                    Lấy thông tin chi tiết Task từ module khác
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName GetDetailTaskFromOther

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:) {String}    [fields]                         Danh sách fields cần lấy dữ liệu (<code>Default: </code> all field)


@apiUse ResponseDetailTaskUpgrade

@apiUse GetDetailTaskResponse
"""

"""
@api {POST} /task/api/v1.0/tasks/v2/actions/list-by-module                   Lấy danh sách task theo module
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName GetListTaskByModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse GetListTaskByModuleResponse
"""

"""
@api {POST} /task/api/v1.0/tasks/v2/actions/list-from-other                   Lấy danh sách task theo module khác
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName GetListTaskFromOther

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse GetListTaskByModuleResponse
"""

# ---------- Bỏ trạng thái mới của danh sách Task -----------
"""
@api {POST} /task/api/v1.0/tasks/actions/un-new          Bỏ trạng thái mới của danh sách Task
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName TasksUnNew

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(Body:)			{Array}     task_ids             Danh sách các <code>ID</code> tasks cần cập nhật

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {String}            data                        Thông tin phản hồi

@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

# ---------- Task V4x -----------
"""
@api {GET} /task/api/v1.0/tasks/v2/actions/list-status-process                    Lấy danh sách trạng thái task
@apiGroup Task v4x
@apiVersion 1.0.0
@apiName Lấy danh sách trạng thái xử lý của công việc

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query) {string=1,2,3,4,5} value              Gía trị trạng thái xử lý task (mặc định là lấy tất cả)

@apiParamExample    {json}      Response Example:
{
    "code": 200,
    "data": [
        {
            "allow_change_order": false,
            "allow_edit_status_display_value": false,
            "allow_edit_value": false,
            "allow_remove_value": false,
            "bg_color": "#F8F9FA",
            "color": "#6A7383",
            "enable": 1,
            "order": 0,
            "status": 1,
            "translate_key": "i18n_to_do",
            "value": 1
        },
        {
            "allow_change_order": false,
            "allow_edit_status_display_value": false,
            "allow_edit_value": false,
            "allow_remove_value": false,
            "bg_color": "#E9F1FE",
            "color": "#226FF5",
            "enable": 1,
            "order": 1,
            "status": 1,
            "translate_key": "i18n_doing",
            "value": 2
        },
        {
            "allow_change_order": false,
            "allow_edit_status_display_value": false,
            "allow_edit_value": false,
            "allow_remove_value": false,
            "bg_color": "#FEEEE6",
            "color": "#F05800",
            "enable": 1,
            "order": 2,
            "status": 1,
            "translate_key": "i18n_waiting",
            "value": 3
        },
        {
            "allow_change_order": false,
            "allow_edit_status_display_value": false,
            "allow_edit_value": false,
            "allow_remove_value": false,
            "bg_color": "#F0EBEE",
            "color": "#663259",
            "enable": 1,
            "order": 3,
            "status": 1,
            "translate_key": "i18n_pending",
            "value": 4
        },
        {
            "allow_change_order": false,
            "allow_edit_status_display_value": false,
            "allow_edit_value": false,
            "allow_remove_value": false,
            "bg_color": "#E6FAF0",
            "color": "#00BC62",
            "enable": 1,
            "order": 4,
            "status": 1,
            "translate_key": "i18n_complete",
            "value": 5
        }
    ],
    "lang": "en",
    "message": "request successful."
}                             

"""
"""
@api {PUT} /task/api/v1.0/save-view-config/order                    Cập nhật lại vị trí thẻ hiển thị
@apiGroup Task v4x
@apiVersion 1.0.0
@apiName SaveViewConfigUpdateOrder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {Array}      data                                    Danh sách thẻ hiển thị
@apiParam   (BODY:) {string}     data.id                    id của thẻ hiển thị
@apiParam   (BODY:) {int}        data.order                             vị trí của thẻ hiển thị
@apiParamExample {json} BodyExample
{
    "data": [
        {
            "id":"6447820d9c4d140011715347",
            "order":1
        },
        {
            "id":"6447820d9c4d140011715348",
            "order":2
        },
        {
            "id":"6447820d9c4d140011715349",
            "order":3
        },
        {
            "id":"64b4b71417f340000ca62466",
            "order":4
        }
    ]
}
"""

"""
@api {POST} /task/api/v1.0/tasks/v2/actions/list-task-by-status-process                    Danh sách task theo trạng thái
@apiGroup Task v4x
@apiVersion 1.0.0
@apiName ListTaskByStatusProcess

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParamExample {json} Body Example
{
    "status_process": 4,
    "filter": [],
    "search": "",
}
@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "account_mentions": [],
            "assign_ids": [
                "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            ],
            "assign_type": "STAFF",
            "comment_count": 1,
            "company_ids": [],
            "complete_time": null,
            "compulsory_completion": false,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": **********.00594,
            "deadline_end_time": **********,
            "deadline_start_time": **********,
            "deadline_type": "unspecified_time",
            "description": "<div>fvfgvfv</div> <style></style>",
            "description_attachment_ids": [],
            "file_usage_status": "ARCHIVED",
            "id": "63e9f6210d48aed9df718f6d",
            "is_new": 0,
            "keywords": "fff",
            "mentions": [],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config_channels": [
                "email"
            ],
            "notification_config_type": "NO_REMINDER",
            "object_id": "63e766581c5d7500126d8e9d",
            "order_ids": [],
            "planned_start_time": null,
            "priority_level": 1,
            "profile_ids": [],
            "related_to_not_delete": {
                "company_ids": [],
                "order_ids": [],
                "profile_ids": [],
                "ticket_ids": [
                    "63e766581c5d7500126d8e9d"
                ]
            },
            "source": "TICKET",
            "status_process": 4,
            "tags_id": [],
            "task_type": "GENERAL",
            "ticket_ids": [
                "63e766581c5d7500126d8e9d"
            ],
            "time_check_status": 1676282340,
            "title": "fff",
            "type_create": "MANUAL",
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": 1676282353.09
        }
    ],
    "lang": "en",
    "message": "request successful.",
    "scroll_size": 1,
    "paging": {
        "cursors": {
            "after_token": "WzE2ODU1NTk2MDAuMCwgIjY0NzcxN2Y0YjVmNzVlMDAwZTU1MTY5MiJd",
            "before_token": "WzE2ODU1MzQ1MjAuMCwgIjY0NzcyNzY2NzZmYjA3MmRjOGU4NTE4NCJd"
        },
        "page_count": 5,
        "per_page": 10,
        "total_count": 41
    }
}
"""

"""
@api {POST} /task/api/v1.0/tasks/v2/actions/quick-view-task/<task_id>                    Lấy thông tin chi tiết Task (xem nhanh)
@apiGroup Task v4x
@apiVersion 1.0.0
@apiName GetDetailTaskQuickView

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {Array}    [fields]                         Danh sách fields cần lấy dữ liệu (<code>Default: </code> all field)

@apiParamExample {json} Body Example
{
    "fields": []
}

@apiUse ResponseDetailTaskUpgrade

@apiUse GetDetailTaskResponse
"""

"""
@api {PUT} /task/api/v1.0/tasks/v2/actions/<task_id>                    Cập nhật task theo từng field
@apiGroup Task v4x
@apiVersion 1.0.0
@apiName UpdateDetailTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParamExample {json} Body Example
{
    "id": "63e9f6210d48aed9df718f6d",
    "field_key": "task_type",
    "value": "GENERAL"
}

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "account_mentions": [],
        "assign_ids": [
            "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        ],
        "assign_type": "STAFF",
        "task_parent_id": "",
        "comment_count": 1,
        "company_ids": [],
        "complete_time": null,
        "compulsory_completion": false,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": **********.00594,
        "deadline_end_time": **********,
        "deadline_start_time": **********,
        "deadline_type": "unspecified_time",
        "description": "<div>fvfgvfv</div> <style></style>",
        "description_attachment_ids": [],
        "file_usage_status": "ARCHIVED",
        "id": "63e9f6210d48aed9df718f6d",
        "task_parent_id":"",
        "code_task": "",
        "is_new": 0,
        "keywords": "fff",
        "mentions": [],
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "notification_config_channels": [
            "email"
        ],
        "notification_config_type": "NO_REMINDER",
        "object_id": "63e766581c5d7500126d8e9d",
        "order_ids": [],
        "planned_start_time": null,
        "priority_level": 1,
        "profile_ids": [],
        "related_to_not_delete": {
            "company_ids": [],
            "order_ids": [],
            "profile_ids": [],
            "ticket_ids": [
                "63e766581c5d7500126d8e9d"
            ]
        },
        "source": "TICKET",
        "status_process": 4,
        "tags_id": [],
        "task_type": "GENERAL",
        "ticket_ids": [
            "63e766581c5d7500126d8e9d"
        ],
        "time_check_status": 1676282340,
        "title": "fff",
        "type_create": "MANUAL",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_time": 1676282353.09
    }
}
"""

"""
@api {POST} /task/api/v1.0/tasks/v2/actions/count-task-by-status-process                    Lấy số lượng task theo trạng thái
@apiGroup Task v4x
@apiVersion 1.0.0
@apiName CountByStatusProcess

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParamExample    {json}      Body Example:
{
  "search": "",
  "filters": [
  ],
  "lst_status":[1,2,3,4,5]
}

@apiSuccessExample    {json}      Response Example:
{
    "code": 200,
    "data": [
        {
            "status": 1,
            "total_item": 1694
        },
        {
            "status": 2,
            "total_item": 62
        },
        {
            "status": 3,
            "total_item": 11
        },
        {
            "status": 4,
            "total_item": 18
        },
        {
            "status": 5,
            "total_item": 176
        }
    ],
    "lang": "vi",
    "message": "Request thành công."
}                        

"""

"""
@api {GET} /task/api/v1.0/tasks/get-default-view                   Lấy loại tab hiển thị mặc định của tài khoản
@apiGroup Task v4x
@apiVersion 1.0.0
@apiName GetDefaultView

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample    {json}      Response Example:
{
    "code": 200,
    "data": {
        "default_view_config_type": "table"
    },
    "lang": "vi",
    "message": "Request thành công."
}                            

"""

"""
@api {POST} /task/api/v1.0/tasks/set-default-view                   Thay đổi loại tab hiển thị mặc định của tài khoản
@apiGroup Task v4x
@apiVersion 1.0.0
@apiName SetDefaultView

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParamExample    {json}      Body Example:
{
    "default_view_config_type": "table"
}

@apiSuccessExample    {json}      Response Example:
{
    "code": 200,
    "data": {
        "default_view_config_type": "table"
    },
    "lang": "vi",
    "message": "Request thành công."
}                            

"""
"""
@api  {POST} /task/api/v1.0/tasks/action/quickview                 Lấy danh sách công việc xem nhanh
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName ListTaskQuickView

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {Array}    [fields]                         Danh sách fields cần lấy dữ liệu (<code>Default: </code> sẽ lấy: id, code_task, title).
                                                                Nếu truyền dữ liệu lên thì sẽ trả về toàn bộ field request.
@apiParam   (Body:) {Array}    [filters]                        Danh sách điều kiện lọc
@apiParam   (Body:) {String}   [search]                         Tên công việc cần tìm kiếm

@apiParamExample    {json}      Body Example:
{
    "search": "",
    "filters": [],
    "fields": []
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Dữ liệu

@apiUse paging_tokens

@apiSuccessExample    {json}      Response Example:
{
    "code": 200,
    "data": [
        {
            "id": "63e9f6210d48aed9df718f6d",
            "title": "1000",
            "code_task": "",
        }
    ],
    "lang": "vi",
    "message": "Request thành công."
}    

"""
"""
@api  {POST} /task/api/v1.0/tasks/action/get-list-relate                Lấy danh sách công vệc liên quan tới (Cha, con của công việc hiện tại)
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName ListTaskRelateTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {string}   task_id                          <code>ID</code> công việc cần lấy danh sách công việc liên quan.
@apiParam   (Body:) {String}   [search]                         Tên công việc cần tìm kiếm
@apiParam   (Body:) {String=child,parent}   type                Loại công việc muốn lấy danh sách
                                                                <ul>
                                                                    <li><code>child</code>: Lấy danh sách công việc con của công việc hiện tại</li>
                                                                    <li><code>parent</code>: Lấy danh sách công việc cha của công việc hiện tại</li>
                                                                </ul>
                                                                

@apiParamExample    {json}      Body Example:
{
    "task_id": "",
    "type": "child"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Dữ liệu

@apiUse paging_tokens

@apiUse ResponseDetailTaskUpgrade
"""
"""
@api  {POST} /task/api/v1.0/tasks/action/total-list-relate                Lấy tổng số công vệc liên quan tới (Cha, con của công việc hiện tại)
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName TotalTaskRelateTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {string}   task_id                          <code>ID</code> công việc cần lấy danh sách công việc liên quan.
@apiParam   (Body:) {String=child,parent}   [type]              Loại công việc muốn lấy danh sách
                                                                <ul>
                                                                    <li><code>child</code>: Lấy tổng số công việc con</li>
                                                                    <li><code>parent</code>: Lấy tổng số công việc cha</li>
                                                                    <li><code>Nếu không truyền lên thì mặc định tính tổng của cả child và parent</code></li>
                                                                </ul>


@apiParamExample    {json}      Body Example:
{
    "task_id": "",
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Dữ liệu
@apiSuccess {Int}            data.total_child                 Tổng số task con
@apiSuccess {Int}            data.total_parent                Tổng số task cha
@apiSuccess {Int}            data.total                       Tổng sô task



@apiUse paging_tokens

@apiSuccessExample    {json}      Response Example:
{
    "code": 200,
    "data": {
        "total_child": 0,
        "total_parent": 0,
        "total": 0,
    },
    "lang": "vi",
    "message": "Request thành công."
}    

"""
"""
@api  {POST} /task/api/v1.0/tasks/<task_id>/remove-child                 Gỡ công việc con khỏi công việc cha
@apiGroup Task Upgrade
@apiVersion 1.0.0
@apiName RemoveChildTaskParentTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {Array}     child_task_ids      Danh sách <code>ID</code> công việc con cần gỡ.                         

@apiParamExample    {json}      Body Example:
{
    "child_task_ids": []
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample    {json}      Response Example:
{
    "code": 200,
    "lang": "vi",
    "message": "Request thành công."
}    

"""

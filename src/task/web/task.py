#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TungDD
    Company: Mobio
    Date Created: 03/12/2021
"""

"""
@apiDefine DetailTaskResponse

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "title": "task",
        "description": "",
        "assignee_id": "82f95839-5be5-4a1b-bf20-a1f3a14025e3".
        "completion_time": "",
        "relate_to": {
            "profile_ids":[
                "5df8acd0f0c38bb7962737bc"
            ],
            "company_ids":[
                 
            ],
            "order_ids":[
                "5df8af0b115ea9dab22737ba",
                "5df8b12e115ea9dab22737bc"
            ],
            "ticket_ids":[
                 
            ]
        },
        "type": "GENERAL",
        "priority_level": 1,
        "notification_config": {
            "type": "NO_REMINDER"
        },
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z",
        "mentions": [
            {
                "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
                "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
            }
        ],
        "comment_count": 5
    }
}

"""

"""
@apiDefine ResponseDetailTask
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin file Upload

@apiSuccess {String}            data.id                       <code>ID</code> định danh của Task
@apiSuccess {String}            data.source                   Định danh nguồn tạo Note
@apiSuccess {String}            data.object_id                Định danh của phần tạo tác. Ví dụ ở SALE thì sẽ là định danh của đơn hàng.
@apiSuccess {String}            data.title                    Tiêu đề của Task
@apiSuccess {String}            data.description              Nội dung của task 
@apiSuccess {Object}            data.assign                   Thông tin người được Assign
@apiSuccess {Array}             data.assign.ids                       Danh sách <code>ID</code> định danh đối tượng được assign 
@apiSuccess {String}            data.assign.type                     Kiểu đối tượng được assgin
                                                              <ul>
                                                                <li><code>STAFF:</code> nhân viên</li>
                                                                <li><code>TEAM:</code> team</li>
                                                              </ul>
@apiSuccess {String}            data.completion_time          Thời gian hoàn thành
@apiSuccess {Object}            data.relate_to                Liên quan tới
@apiSuccess {String}            data.relate_to.company_ids    Danh sách các <code>ID</code> công ty liên quan tới
@apiSuccess {String}            data.relate_to.profile_ids    Danh sách các <code>ID</code> profile liên quan tới
@apiSuccess {String}            data.relate_to.order_ids       Danh sách các <code>ID</code> order liên quan tới
@apiSuccess {String}            data.relate_to.ticket_ids     Danh sách các <code>ID</code> ticket liên quan tới

@apiSuccess {Object}            data.relate_to_not_delete                Liên quan tới phần chính nào
@apiSuccess {String}            data.relate_to_not_delete.company_ids    Danh sách các <code>ID</code> công ty liên quan tới
@apiSuccess {String}            data.relate_to_not_delete.profile_ids    Danh sách các <code>ID</code> profile liên quan tới
@apiSuccess {String}            data.relate_to_not_delete.order_ids       Danh sách các <code>ID</code> order liên quan tới
@apiSuccess {String}            data.relate_to_not_delete.ticket_ids     Danh sách các <code>ID</code> ticket liên quan tới

@apiSuccess {String}            data.type                     Kiểu công việc
                                                              <ul>
                                                                    <li><code>GENERAL:</code>Chung</li>
                                                                    <li><code>SENT_EMAIL:</code>Gửi email</li>
                                                                    <li><code>CALL:</code>Gọi điện</li>
                                                              </ul>
@apiSuccess {Object}            data.deadline                 Thời hạn hoàn thành của task công việc tiếp nối
@apiSuccess {String}            data.deadline.type            Loại thời hạn hoàn thành của công việc tiếp nối
                                                                        <ul>
                                                                            <li><code>time_slot</code>: Khung giờ</li>
                                                                            <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                        </ul>
@apiSuccess {String}            data.deadline.start_time      Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                </ul>
@apiSuccess {Object}            data.deadline.end_time        Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                </ul>
@apiSuccess {String}            data.priority_level           Mức độ ưu tiên
                                                              <ul>
                                                                    <li><code>1:</code> mức độ ưu tiên cao</li>
                                                                    <li><code>2:</code> mức độ ưu tiên trung bình</li>
                                                                    <li><code>3:</code> mức độ ưu tiên thấp</li>
                                                             </ul>
@apiSuccess {String}            data.notification_config      Cấu hình thông báo
@apiSuccess {String}            data.notification_config.type       Loại cấu hình
                                                                    <ul>
                                                                        <li><code>NO_REMINDER:</code>Không nhắc nhở</li>
                                                                        <li><code>ADVANCE_REMINDER:</code>Nhắc nhở trước</li>
                                                                        <li><code>TIMELY_REMINDER:</code>nhắc nhở đúng thời hạn</li>
                                                                        <li><code>OPTION:</code>Tùy chọn</li>
                                                                    </ul>
@apiSuccess {String}            [data.notification_config.time_value]       Thời gian cấu hình                                                                                                                                    
@apiSuccess {String}            [data.notification_config.time_unit]        Đơn vị của thời gian cấu hình. Áp dụng với type: <code>ADVANCE_REMINDER</code>
                                                                            <ul>
                                                                                <li><code>minute:</code> đơn vị tính là phút</li>
                                                                                <li><code>hour:</code> đơn vị tính là giờ</li>
                                                                                <li><code>year:</code> đơn vị tính là năm</li>
                                                                            <ul> 
@apiSuccess {Array}             [data.mentions]                             Danh sách người được mentions
@apiSuccess {String}            [data.mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiSuccess {String}            [data.mentions.account_id]                  <code>ID</code> của người được mentions                                                                                                                                   
@apiSuccess {Int}               data.comment_count                             Số lượng comment có trong task                                                                                                                                  
        
@apiSuccess {String}            data.notification_config      Cấu hình thông báo
@apiSuccess {String}            data.created_by      Được tạo bởi ai
@apiSuccess {String}            data.created_time    Thời gian tạo
@apiSuccess {Array}             [data.description_attachment_ids]              Danh sách <code>ID</code> các file được đính kèm trong nội dung của Task
"""
"""
@apiDefine FieldsSelect
@apiParam   (QUERY:) {String}  [fields]    Danh sách fields cần lấy dữ liệu.<code>Mặc định:</code>nếu không truyền field nào lên thì mặc định sẽ lấy tất cả các field bên trên.
                                            <ul>
                                                <li><code>id:</code>Định danh của task</li>
                                                <li><code>title:</code>Tiêu đề</li>
                                                <li><code>description:</code>Nội dung của Task</li>
                                                <li><code>assign:</code>Thông tin đối tượng được giao Task</li>
                                                <li><code>completion_time:</code>Thời gian hoàn thành của Task</li>
                                                <li><code>relate_to:</code>Liên quan tới</li>
                                                <li><code>type:</code>Kiểu công việc</li>
                                                <li><code>priority_level:</code>Mức độ ưu tiên</li>
                                                <li><code>notification_config:</code>Liên quan tới</li>
                                                <li><code>created_by:</code>Tạo bởi ai</li>
                                                <li><code>created_time:</code>Thời gian tạo</li>
                                            </ul>

"""

"""
@apiDefine ListTaskParam

@apiParam (Params:) {string} per_page               Số lượng item trên 1 trang. per_page = -1 nếu muốn lấy ra tất cả item
@apiParam (Params:) {string} after_token            Token được trả ra ở lần request trước đó
                                                    (Dùng để làm mốc cho lượt query tiếp theo đề lấy ra những phần tử tiếp theo)

@apiParam   (BODY:) {string}                         [keywords]            Từ khóa cần tìm kiếm theo title
@apiParam   (BODY:) {string=UPCOMING,DONE,OVER_DUE}  status                Trạng thái của Task. 
                                                                            <ul>
                                                                                <li><code>UPCOMING:</code>Sắp tới</li>
                                                                                <li><code>DONE:</code>Hoàn thành</li>
                                                                                <li><code>OVER_DUE:</code>Quá hạn</li>
                                                                            </ul>
                                    
@apiParam   (BODY:) {string}                         [current_time]      Thời gian tính toán. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code>
@apiParam   (BODY:) {Array=general,sent_email,call}  [types]             Kiểu công việc cần tìm kiếm.
                                                                            <ul>
                                                                                <li><code>GENERAL:</code>Chung</li>
                                                                                <li><code>SENT_EMAIL:</code>Gửi email</li>
                                                                                <li><code>CALL:</code>Gọi điện</li>
                                                                            </ul>

@apiParam   (BODY:) {string}                          [completion_time]     Thời gian hoàn thành 
@apiParam   (BODY:) {string}                          [source]              Nguồn tạo công việc  
@apiParam   (BODY:) {Datetime}                        [created_time]     Ngày tạo công việc
@apiParam   (BODY:) {Integer}                         [priority_level]     Mức độ ưu tiên
                                                        <ul>
                                                            <li><code>1:</code> mức độ ưu tiên cao </li>
                                                            <li><code>2:</code> mức độ ưu tiên thấp </li>
                                                            <li><code>3:</code> mức độ ưu tiên trung bình </li>
                                                        </ul>
@apiParam   (BODY:) {Array}                           [assignee_ids]     Danh sách người được giao công việc
@apiParam   (BODY:) {Array}                           [brand_ids]     Danh sách thương hiệu
@apiParam   (BODY:) {String}                          object_id    Định danh của thành phân cần lấy danh sách task. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {array}                           account_mentions   Danh sách <code>ID</code> người được mentions

@apiParamExample {json} Body example
{
    "keywords": "gọi điện cho khách hàng",
    "status": "COMING",
    "status": "coming",
    "current_time": "2021-10-03T08:10Z",
    "type": ["GENERAL", "CALL", "SENT_EMAIL"],
    "assignee_ids": [],
    "brand_ids": [],
    "source": "SALE",
    "object_id": "82f95839-5be5-4a1b-bf20-a1f3a14025e3"
    "account_mentions": []
}

"""

"""
@apiDefine ListTaskResponse

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách tương ứng các file.

@apiSuccess {String}            data.id                       <code>ID</code> định danh của Task
@apiSuccess {String}            data.title                    Tiêu đề của Task
@apiSuccess {String}            data.description              Nội dung của task 
@apiSuccess {Object}            data.assign                   Thông tin người được Assign
@apiSuccess {Array}             data.ids                       Danh sách <code>ID</code> định danh đối tượng được assign 
@apiSuccess {String}            data.type                     Kiểu đối tượng được assgin
                                                              <ul>
                                                                <li><code>STAFF:</code> nhân viên</li>
                                                                <li><code>TEAM:</code> team</li>
                                                              </ul>
@apiSuccess {String}            data.completion_time          Thời gian hoàn thành
@apiSuccess {Array}             data.relate_to                Liên quan tới
@apiSuccess {String}            data.relate_to.company_ids          Danh sách <code>ID</code> của công ty liên quan tới
@apiSuccess {String}            data.relate_to.order_ids          Danh sách <code>ID</code> của đơn hàng liên quan tới
@apiSuccess {String}            data.relate_to.ticket_ids          Danh sách <code>ID</code> của ticket liên quan tới
@apiSuccess {String}            data.relate_to.profile_ids          Danh sách <code>ID</code> của profile liên quan tới

@apiSuccess {Array}             data.relate_to_not_delete                Liên quan tới phần chính nào 
@apiSuccess {String}            data.relate_to_not_delete.company_ids          Danh sách <code>ID</code> của công ty liên quan tới
@apiSuccess {String}            data.relate_to_not_delete.order_ids          Danh sách <code>ID</code> của đơn hàng liên quan tới
@apiSuccess {String}            data.relate_to_not_delete.ticket_ids          Danh sách <code>ID</code> của ticket liên quan tới
@apiSuccess {String}            data.relate_to_not_delete.profile_ids          Danh sách <code>ID</code> của profile liên quan tới
                                                            
@apiSuccess {String}            data.type                       Kiểu công việc
                                                                <ul>
                                                                    <li><code>GENERAL:</code>Chung</li>
                                                                    <li><code>SENT_EMAIL:</code>Gửi email</li>
                                                                    <li><code>CALL:</code>Gọi điện</li>
                                                                </ul>
@apiSuccess {String}            data.priority_level             Mức độ ưu tiên
                                                                <ul>
                                                                    <li><code>1:</code> mức độ ưu tiên cao</li>
                                                                    <li><code>2:</code> mức độ ưu tiên trung bình</li>
                                                                    <li><code>3:</code> mức độ ưu tiên thấp</li>
                                                                </ul>
@apiSuccess {String}            data.notification_config      Cấu hình thông báo
@apiSuccess {String}            data.notification_config.type       Loại cấu hình
                                                                    <ul>
                                                                        <li><code>NO_REMINDER:</code>Không nhắc nhở</li>
                                                                        <li><code>ADVANCE_REMINDER:</code>Nhắc nhở trước</li>
                                                                        <li><code>TIMELY_REMINDER:</code>nhắc nhở đúng thời hạn</li>
                                                                        <li><code>OPTION:</code>Tùy chọn</li>
                                                                    </ul>
@apiSuccess {String}            [data.notification_config.time_value]      Thời gian cấu hình                                                                                                                                    
@apiSuccess {String}            [data.notification_config.time_unit]        Đơn vị của thời gian cấu hình. Áp dụng với type: <code>ADVANCE_REMINDER</code>
                                                                            <ul>
                                                                                <li><code>second:</code> đơn vị tính là giây</li>
                                                                                <li><code>minute:</code> đơn vị tính là phút</li>
                                                                                <li><code>hour:</code> đơn vị tính là giờ</li>
                                                                                <li><code>year:</code> đơn vị tính là năm</li>
                                                                            <ul>                                                                                                                                       
@apiSuccess {Array}             data.attachments                              Danh sách file đính kèm                                                                                                                                    
@apiSuccess {string}            data.attachments.format_file                  Định dạng của File                                                                                                                                 
@apiSuccess {string}            data.attachments.id                           Định danh của file                                                                                                                                 
@apiSuccess {string}            data.attachments.title                        Tiêu đề của File upload lên hệ thống                                                                                                                                 
@apiSuccess {Array}             data.attachments.url                          Url truy cập                                                                                                                                    
        
@apiSuccess {String}            data.created_by      Được tạo bởi ai
@apiSuccess {String}            data.created_time    Thời gian tạo
@apiSuccess {Int}               data.comment_count   Số lượng comment có trong task
@apiSuccess {Array}             data.account_mentions   Danh sách người được mentions trong comment và nội dung task
@apiSuccess {Object}            data.deadline                 Thời hạn hoàn thành của task công việc tiếp nối
@apiSuccess {String}            data.deadline.type            Loại thời hạn hoàn thành của công việc tiếp nối
                                                                        <ul>
                                                                            <li><code>time_slot</code>: Khung giờ</li>
                                                                            <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                        </ul>
@apiSuccess {String}            data.deadline.start_time      Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                </ul>
@apiSuccess {Object}            data.deadline.end_time        Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                </ul>
@apiSuccess {Array}             [data.mentions]                             Danh sách người được mentions
@apiSuccess {String}            [data.mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiSuccess {String}            [data.mentions.account_id]                  <code>ID</code> của người được mentions
@apiSuccess {Array}             [data.description_attachment_ids]              Danh sách <code>ID</code> các file được đính kèm trong nội dung của Task
@apiSuccess {Object}            data.deadline                 Thời hạn hoàn thành của task công việc tiếp nối
@apiSuccess {String}            data.deadline.type            Loại thời hạn hoàn thành của công việc tiếp nối
                                                                        <ul>
                                                                            <li><code>time_slot</code>: Khung giờ</li>
                                                                            <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                        </ul>
@apiSuccess {String}            data.deadline.start_time      Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                </ul>
@apiSuccess {Object}            data.deadline.end_time        Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                </ul>


@apiSuccess {Object}            paging               Đối tượng dữ liệu phân trang
@apiSuccess {Integer}           paging.per_page      Số lượng item được trả ra ở mỗi trang
@apiSuccess {String}            paging.token         Chuỗi token được tạo để sử dụng lấy ra những dữ liẹu tiếp đó của lần request tiếp theo

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "_id": "616ce83e017ba28235cc046f",
            "assign": {
                "ids": [
                    "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
                ],
                "type": "STAFF"
            },
            "attachments": [],
            "mentions": [],
            "code": 31,
            "completion_time": null,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2021-10-18 03:21",
            "description": "<p>ko cần gửi</p>",
            "file_usage_status": "ARCHIVED",
            "id": "616ce83e017ba28235cc046f",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config": {
                "channels": [
                    "email"
                ],
                "time_value": "2021-10-25 10:21",
                "type": "OPTION"
            },
            "object_id": "615f45ee32e3c8801c26f8f6",
            "priority_level": 3,
            "related_to": {
                "company_ids": [],
                "order_ids": [],
                "profile_ids": [],
                "ticket_ids": []
            },
            "source": "SALE",
            "status": "UPCOMING",
            "title": "cong viec 1",
            "type": "SENT_EMAIL",
            "type_create": "MANUAL",
            "account_mentions": []
            "updated_by": null,
            "updated_time": "2021-10-18 03:21"
        },
        {
            "_id": "616ce8aa4c460f60fbdc52ed",
            "assign": {
                "ids": [
                    "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
                ],
                "type": "STAFF"
            },
            "attachments": [
                {
                    "format_file": "image/jpeg",
                    "id": "616ce8a87f66d7d662b0a131",
                    "title": "1634527397_gratisography-157H-free-stock-photo.jpg",
                    "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1634527397_gratisography-157H-free-stock-photo.jpg"
                }
            ],
            "mentions": [],
            "code": 32,
            "completion_time": null,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2021-10-18 03:23",
            "description": "<p>&aacute;dasdsad</p>\n",
            "file_usage_status": "ARCHIVED",
            "id": "616ce8aa4c460f60fbdc52ed",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config": {
                "channels": [
                    "email"
                ],
                "time_value": "2021-10-29 10:22",
                "type": "TIMELY_REMINDER"
            },
            "object_id": "615f45ee32e3c8801c26f8f6",
            "priority_level": 1,
            "related_to": {
                "company_ids": [],
                "order_ids": [],
                "profile_ids": [],
                "ticket_ids": []
            },
            "related_to_not_delete": {
                "company_ids": [],
                "order_ids": [],
                "profile_ids": [],
                "ticket_ids": []
            },
            "source": "SALE",
            "status": "UPCOMING",
            "title": "kèm file",
            "type": "CALL",
            "type_create": "MANUAL",
            "updated_by": null,
            "updated_time": "2021-10-18 03:23"
        },
        {
            "_id": "616ce675f2a27d37df34df26",
            "assign": {
                "ids": [
                    "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
                ],
                "type": "STAFF"
            },
            "mentions": [],
            "attachments": [],
            "code": 22,
            "completion_time": null,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2021-10-18 03:13",
            "description": "<p>aaaaaaaaaaaaaaaaaa</p>\n",
            "file_usage_status": "ARCHIVED",
            "id": "616ce675f2a27d37df34df26",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config": {
                "channels": [
                    "email"
                ],
                "type": "NO_REMINDER"
            },
            "object_id": "615f45ee32e3c8801c26f8f6",
            "priority_level": 2,
            "related_to": {
                "company_ids": [],
                "order_ids": [],
                "profile_ids": [],
                "ticket_ids": []
            },
            "related_to_not_delete": {
                "company_ids": [],
                "order_ids": [],
                "profile_ids": [],
                "ticket_ids": []
            },
            "source": "SALE",
            "status": "UPCOMING",
            "title": "12345",
            "type": "GENERAL",
            "type_create": "MANUAL",
            "updated_by": null,
            "updated_time": "2021-10-18 03:13"
        },
        {
            "_id": "616ce390ce108444cf1924e1",
            "assign": {
                "ids": [
                    "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
                ],
                "type": "STAFF"
            },
            "attachments": [],
            "mentions": [],
            "code": 16,
            "completion_time": null,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2021-10-18 03:01",
            "description": "<p>bbbbbbbbbb</p>\n",
            "file_usage_status": "ARCHIVED",
            "id": "616ce390ce108444cf1924e1",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config": {
                "channels": [
                    "email"
                ],
                "type": "NO_REMINDER"
            },
            "object_id": "615f461132e3c8801c26f8fe",
            "priority_level": 1,
            "related_to": {
                "company_ids": [],
                "order_ids": [],
                "profile_ids": [],
                "ticket_ids": []
            },
            "related_to_not_delete": {
                "company_ids": [],
                "order_ids": [],
                "profile_ids": [],
                "ticket_ids": []
            },
            "source": "SALE",
            "status": "UPCOMING",
            "title": "aaaaaaaaaaa",
            "type": "GENERAL",
            "type_create": "MANUAL",
            "updated_by": null,
            "updated_time": "2021-10-18 03:01"
        },
        {
            "_id": "616ce3aaf2a27d37df34df25",
            "assign": {
                "ids": [
                    "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
                ],
                "type": "STAFF"
            },
            "attachments": [],
            "mentions": [],
            "code": 17,
            "completion_time": null,
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2021-10-18 03:02",
            "description": "<p>bbbbbbbbbb</p>\n",
            "file_usage_status": "ARCHIVED",
            "id": "616ce3aaf2a27d37df34df25",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notification_config": {
                "channels": [
                    "email"
                ],
                "time_value": "2021-10-19 10:01",
                "type": "OPTION"
            },
            "object_id": "615f461132e3c8801c26f8fe",
            "priority_level": 1,
            "related_to": {
                "company_ids": [],
                "order_ids": [],
                "profile_ids": [],
                "ticket_ids": []
            },
            "related_to_not_delete": {
                "company_ids": [],
                "order_ids": [],
                "profile_ids": [],
                "ticket_ids": []
            },
            "source": "SALE",
            "status": "UPCOMING",
            "title": "aaaaaaaaaaa",
            "type": "GENERAL",
            "type_create": "MANUAL",
            "updated_by": null,
            "updated_time": "2021-10-18 03:02"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "pagging": {
        "per_page": 5,
        "token": "eyJsYXN0X2lkIjogIjYxNmNlM2FhZjJhMjdkMzdkZjM0ZGYyNSIsICJmaWVsZCI6IHsia2V5IjogImR1ZV9kYXRlIiwgInZhbHVlIjogIjIwMjEtMTAtMjEgMTA6MDEifX0="
    }
}
"""

# ---------- Danh sách Task theo bộ lọc -----------
"""
@api {POST} /task/api/v1.0/tasks/action/filter              Lấy danh sách Task
@apiGroup Task
@apiVersion 1.0.0

@apiName ListTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUSe ListTaskParam
@apiUse FieldsSelect
@apiUSe ListTaskResponse
                                        
"""
# ---------- Danh sách Task theo bộ lọc từ task-----------
"""
@api {POST} /task/api/v1.0/tasks/action/list-from-task/filter              Lấy danh sách Task từ module Task
@apiGroup Task
@apiVersion 1.0.0

@apiName ListTaskFromTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUSe ListTaskParam
@apiUse FieldsSelect
@apiUSe ListTaskResponse
                                        
"""

# ---------- Thêm Task ----------------------
"""
@api {POST} /task/api/v1.0/tasks                    Thêm Task
@apiDescription Path api: /tasks/v2 sẽ được call chung vào path này
@apiGroup Task
@apiVersion 1.0.0
@apiName AddTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    title                         Tiêu đề của Task
@apiParam   (BODY:) {string}    description                   Nội dung của Task
@apiParam   (BODY:) {Object}    assign                        Thông tin được Assign
@apiParam   (BODY:) {Array}    assign.ids                            Định danh đối tượng được Assign
@apiParam   (BODY:) {String=STAFF,TEAM}    assign.type=STAFF                            Định danh đối tượng được Assign
@apiParam   (BODY:) {string}    assign                        Thông tin được Assign
@apiParam   (BODY:) {Object}    notification_config           Thông báo nhắc nhở
@apiParam   (BODY:) {string}    notification_config.type      Loại cấu hình
                                                                    <ul>
                                                                        <li><code>NO_REMINDER:</code>Không nhắc nhở</li>
                                                                        <li><code>ADVANCE_REMINDER:</code>Nhắc nhở trước</li>
                                                                        <li><code>TIMELY_REMINDER:</code>nhắc nhở đúng thời hạn</li>
                                                                        <li><code>OPTION:</code>Tùy chọn</li>
                                                                    </ul>
@apiParam   (BODY:) {string}    [notification_config.time_value]      Thời gian cấu hình
                                                                        <ul>
                                                                            <li>Với type là <code>NO_REMINDER:</code>Không cần truyền time_value</li>
                                                                            <li>Với type là <code>ADVANCE_REMINDER:</code>Cần truyền số phút muốn thông báo nhắc nhở trước lên</li>
                                                                            <li>Với type là <code>TIMELY_REMINDER:</code>Không cần truyền time_value</li>
                                                                            <li>Với type là <code>OPTION:</code>Truyền lên ngày giờ được chọn. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                        </ul> 
@apiParam   (BODY:) {string}    [notification_config.time_unit]        Đơn vị của thời gian cấu hình. Áp dụng với type: <code>ADVANCE_REMINDER</code> 
                                                                            <ul>
                                                                                <li><code>minute:</code> đơn vị tính là phút</li>
                                                                                <li><code>hour:</code> đơn vị tính là giờ</li>
                                                                                <li><code>year:</code> đơn vị tính là năm</li>
                                                                            <ul> 


@apiParam   (BODY:) {string=general,call,sent_email}    type           Kiểu công việc
                                                                        <ul>
                                                                            <li><code>GENERAL:</code>Chung</li>
                                                                            <li><code>SENT_EMAIL:</code>Gửi email</li>
                                                                            <li><code>CALL:</code>Gọi điện</li>
                                                                        </ul>
                                                                        
@apiParam (BODY:) {int=1,2,3}     priority_level      Mức độ ưu tiên
                                                    <ul>
                                                        <li><code>1:</code> mức độ ưu tiên cao </li>
                                                        <li><code>2:</code> mức độ ưu tiên trung bình </li>
                                                        <li><code>3:</code> mức độ ưu tiên thấp </li>
                                                    </ul>      


@apiParam   (BODY:) {String=GENERAL,SENT_EMAIL,CALL}  type      Kiểu công việc
                                                                <ul>
                                                                    <li><code>GENERAL:</code>Chung</li>
                                                                    <li><code>SENT_EMAIL:</code>Gửi email</li>
                                                                    <li><code>CALL:</code>Gọi điện</li>
                                                                </ul>      
@apiParam   (BODY:) {Array}     attachment_ids                        Danh sách <code>ID</code> file đính kèm           
@apiParam   (BODY:) {String}    source                                Nguồn tạo. Tức là đang đứng ở Module nào thì sẽ cần truyền vào Module đó. Ví dụ: <code>SALE</code>
@apiParam   (BODY:) {String}    object_id                             Định danh của thành phân cần lấy danh sách task. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {Object}    relate_to                             Liên quan tới phần nào.
@apiParam   (BODY:) {Array}    relate_to.company_ids                  Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.ticket_ids                   Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.order_ids                     Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.profile_ids                  Danh sách profile liên quan tới.

@apiParam   (BODY:) {Object}    relate_to_not_delete                             Liên quan tới phần chính nào.
@apiParam   (BODY:) {Array}    relate_to_not_delete.company_ids                  Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.ticket_ids                   Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.order_ids                     Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.profile_ids                  Danh sách profile liên quan tới.
@apiParam   (BODY:) {Array}    [mentions]                             Danh sách người được mentions
@apiParam   (BODY:) {String}   [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiParam   (BODY:) {String}   [mentions.account_id]                  <code>ID</code> của người được mentions
@apiParam   (BODY:) {Array}    [description_attachment_ids]            Danh sách <code>ID</code> file được upload trong nội dung của Task
@apiParam   (BODY:) {Object}            follow_task                          Tạo công việc tiếp nối.
@apiParam   (BODY:) {Int}               follow_task.status                   Trạng thái tạo công việc tiếp nối.
                                                                             <ul>
                                                                                <li>0: là không tạo task</li>
                                                                                <li>1: là tạo task</li>s
                                                                             </ul>
@apiParam   (BODY:) {String}            [follow_task.title]                    Tiêu đề công việc tiếp nối
@apiParam   (BODY:) {Object}            [follow_task.deadline]                 Thời hạn hoàn thành của task công việc tiếp nối
@apiParam   (BODY:) {String}            [follow_task.deadline.type]            Loại thời hạn hoàn thành của công việc tiếp nối
                                                                        <ul>
                                                                            <li><code>time_slot</code>: Khung giờ</li>
                                                                            <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                        </ul>
@apiParam   (BODY:) {String}            [follow_task.deadline.start_time]      Thời hạn hoàn thành công việc.
                                                                        <ul>
                                                                            <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                            <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                        </ul>
@apiParam   (BODY:) {Object}            [follow_task.deadline.end_time]        Thời hạn hoàn thành công việc.
                                                                        <ul>
                                                                            <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                            <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                        </ul>
                                                       
@apiParam   (BODY:) {Object}            deadline                 Thời hạn hoàn thành của task công việc tiếp nối
@apiParam   (BODY:) {String}            deadline.type            Loại thời hạn hoàn thành của công việc tiếp nối
                                                                        <ul>
                                                                            <li><code>time_slot</code>: Khung giờ</li>
                                                                            <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                        </ul>
@apiParam   (BODY:) {String}            deadline.start_time      Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                </ul>
@apiParam   (BODY:) {Object}            deadline.end_time        Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                </ul>
   
@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
    "title": "Task name",
    "assign": {
        "ids": ["5de5f623b64d2a30d5e552e6"],
        "type": "TEAM"
    },
    "type": "GENERAL",
    "priority_level": 1,
    "description": "Nội dung công việc",
    "notification_config": {
        "type": "ADVANCE_REMINDER",
        "time_value": 30,
        "time_unit": "minute"
    },
    "attachment_ids": [
        "615eb693a861dbd7e9da782f",
        "615eb67fe19bf0ff11616d8c"
    ],
    "related_to": {
        "profile_ids": [
            "5df8acd0f0c38bb7962737bc"
        ],
        "company_ids": [],
        "order_ids": [
            "5df8af0b115ea9dab22737ba",
            "5df8b12e115ea9dab22737bc"
        ],
        "ticket_ids": []
    },
    "related_to_not_delete": {
        "profile_ids": [
            "5df8acd0f0c38bb7962737bc"
        ],
        "company_ids": [],
        "order_ids": [
            "5df8af0b115ea9dab22737ba"
        ],
        "ticket_ids": []
    },
    "mentions": [
        {
            "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
            "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
        }
    ],
    "description_attachment_ids": []
}                                       

                                                       
@apiUse ResponseDetailTask

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "title": "task",
        "description": "",
        "assignee_id": "82f95839-5be5-4a1b-bf20-a1f3a14025e3".
        "completion_time": "",
        "relate_to": {
            "profile_ids":[
                "5df8acd0f0c38bb7962737bc"
            ],
            "company_ids":[
                 
            ],
            "order_ids":[
                "5df8af0b115ea9dab22737ba",
                "5df8b12e115ea9dab22737bc"
            ],
            "ticket_ids":[
                 
            ]
        },
        "mentions": [
            {
                "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
                "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
            }
        ],
        "type": "GENERAL",
        "priority_level": 1,
        "notification_config": {
            "type": "NO_REMINDER"
        },
        "description_attachment_ids": [],
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""

# ---------- Lấy chi tiết của Task-----------
"""
@api {GET} /task/api/v1.0/task/<task_id>                    Lấy chi tiết của Task
@apiDescription Path: /task/v2/<task_id> sẽ được gọi chung vào path này. 
@apiGroup Task
@apiVersion 1.0.0
@apiName DetailTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse FieldsSelect
                                            
@apiUse ResponseDetailTask

@apiUse DetailTaskResponse
"""

# ---------- Lấy chi tiết của Task từ module Task-----------
"""
@api {GET} /task/api/v1.0/task/action/read-from-task/<task_id>                    Lấy chi tiết của Task từ module task
@apiDescription Path: /task/v2/action/read-from-task/<task_id> sẽ được gọi chung vào path này. 
@apiGroup Task
@apiVersion 1.0.0
@apiName DetailTaskFromTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse FieldsSelect

@apiUse ResponseDetailTask

@apiUse DetailTaskResponse
"""

# ---------- Lấy chi tiết của Task từ module khác-----------
"""
@api {GET} /task/api/v1.0/task/action/read-from-other/<task_id>                    Lấy chi tiết của từ module khác
@apiDescription Path: /task/v2/task/action/read-from-other/<task_id> sẽ được gọi chung vào path này. 
@apiGroup Task
@apiVersion 1.0.0
@apiName DetailTaskFromOther

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse FieldsSelect

@apiUse ResponseDetailTask

@apiUse DetailTaskResponse
"""

# ---------- Lấy số lượng Task theo trạng thái -----------
"""
@api {GET} /task/api/v1.0/tasks/count_by_status           Lấy số lượng Task theo trạng thái
@apiGroup Task
@apiVersion 1.0.0
@apiName CountByStatus

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (QUERY:)      {string=DONE,UPCOMING,OVER_DUE}    status                    Danh sách các trạng thái của Task cần lấy cách nhau bới dấu phẩy.  
@apiParam (QUERY:)      {string}    source                    Nguồn cần đếm Task
@apiParam (QUERY:)      {string}    object_id                 Định danh thành phần cần đếm Task.

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Số lượng Task theo trạng thái

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "UPCOMING": 10,
        "OVER_DUE": 0,
        "DONE": 0
        
    },
    "message": "request thành công."
}
"""

# ---------- Cập nhật trạng thái hoàn thành của nhiều Task -----------
"""
@api {PUT} /task/api/v1.0/tasks/done          Cập nhật trạng thái hoàn thành của nhiều Task
@apiGroup Task
@apiVersion 1.0.0
@apiName UpdateStatusTasksDone

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(BODY:)			{Array}     ids             Danh sách các <code>ID</code> tasks cần được cập nhật trạng thái hoàn thành

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

# ---------- Bỏ trạng thái hoàn thành của nhiều Task -----------
"""
@api {PUT} /task/api/v1.0/tasks/unchecked_done          Bỏ check trạng thái hoàn thành của nhiều Task
@apiGroup Task
@apiVersion 1.0.0
@apiName UncheckedDoneTasks

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(BODY:)			{Array}     ids             Danh sách các <code>ID</code> tasks cần được cập nhật

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

# ---------- Xóa nhiều Task -----------
"""
@api {DELETE} /task/api/v1.0/tasks          Xóa nhiều task
@apiGroup Task
@apiVersion 1.0.0
@apiName DeleteTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(Query:)			{Array}     ids             Danh sách các <code>ID</code> tasks cần xóa

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

# ---------- Cập nhật chi tiết của Task-----------
"""
@api {PUT} /task/api/v1.0/task/<task_id>                    Cập nhật chi tiết Task
@apiGroup Task
@apiVersion 1.0.0
@apiName UpdateDetailTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (BODY:) {string}    title                         Tiêu đề của Task
@apiParam   (BODY:) {string}    description                   Nội dung của Task
@apiParam   (BODY:) {Object}    assign                        Thông tin được Assign
@apiParam   (BODY:) {Array}    assign.ids                            Định danh đối tượng được Assign
@apiParam   (BODY:) {String=STAFF,TEAM}    assign.type=STAFF                            Định danh đối tượng được Assign
@apiParam   (BODY:) {string}    assign                        Thông tin được Assign
@apiParam   (BODY:) {Object}    notification_config           Thông báo nhắc nhở
@apiParam   (BODY:) {string}    notification_config.type      Loại cấu hình
                                                                    <ul>
                                                                        <li><code>NO_REMINDER:</code>Không nhắc nhở</li>
                                                                        <li><code>ADVANCE_REMINDER:</code>Nhắc nhở trước</li>
                                                                        <li><code>TIMELY_REMINDER:</code>nhắc nhở đúng thời hạn</li>
                                                                        <li><code>OPTION:</code>Tùy chọn</li>
                                                                    </ul>
@apiParam   (BODY:) {string}    [notification_config.time_value]      Thời gian cấu hình
                                                                        <ul>
                                                                            <li>Với type là <code>NO_REMINDER:</code>Không cần truyền time_value</li>
                                                                            <li>Với type là <code>ADVANCE_REMINDER:</code>Cần truyền số phút muốn thông báo nhắc nhở trước lên</li>
                                                                            <li>Với type là <code>TIMELY_REMINDER:</code>Không cần truyền time_value</li>
                                                                            <li>Với type là <code>OPTION:</code>Truyền lên ngày giờ được chọn. Định dạng: <code>"%Y-%m-%dT%H:%mZ"</code></li>
                                                                        </ul> 
@apiParam   (BODY:) {string}    [notification_config.time_unit]        Đơn vị của thời gian cấu hình. Áp dụng với type: <code>ADVANCE_REMINDER</code> 
                                                                            <ul>
                                                                                <li><code>minute:</code> đơn vị tính là phút</li>
                                                                                <li><code>hour:</code> đơn vị tính là giờ</li>
                                                                                <li><code>year:</code> đơn vị tính là năm</li>
                                                                            <ul> 


@apiParam   (BODY:) {string=general,call,sent_email}    type           Kiểu công việc
                                                                        <ul>
                                                                            <li><code>GENERAL:</code>Chung</li>
                                                                            <li><code>SENT_EMAIL:</code>Gửi email</li>
                                                                            <li><code>CALL:</code>Gọi điện</li>
                                                                        </ul>
                                                                        
@apiParam (BODY:) {int=1,2,3}     priority_level      Mức độ ưu tiên
                                                    <ul>
                                                        <li><code>1:</code> mức độ ưu tiên cao </li>
                                                        <li><code>2:</code> mức độ ưu tiên trung bình </li>
                                                        <li><code>3:</code> mức độ ưu tiên thấp </li>
                                                    </ul>      
@apiParam   (BODY:) {String=GENERAL,SENT_EMAIL,CALL}  type      Kiểu công việc
                                                                <ul>
                                                                    <li><code>GENERAL:</code>Chung</li>
                                                                    <li><code>SENT_EMAIL:</code>Gửi email</li>
                                                                    <li><code>CALL:</code>Gọi điện</li>
                                                                </ul>       
@apiParam   (BODY:) {Array}     attachment_ids                        Danh sách <code>ID</code> file đính kèm           
@apiParam   (BODY:) {String}    source                                Nguồn tạo. Tức là đang đứng ở Module nào thì sẽ cần truyền vào Module đó. Ví dụ: <code>SALE</code>
@apiParam   (BODY:) {String}    object_id                             Định danh của thành phân cần lấy danh sách task. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {Object}    relate_to                             Liên quan tới phần nào.
@apiParam   (BODY:) {Array}    relate_to.company_ids                  Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.ticket_ids                   Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.order_ids                     Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.profile_ids                  Danh sách profile liên quan tới.
                                                       
                                                       
@apiParam   (BODY:) {Object}    relate_to_not_delete                             Liên quan tới phần chính nào.
@apiParam   (BODY:) {Array}    relate_to_not_delete.company_ids                  Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.ticket_ids                   Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.order_ids                     Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.profile_ids                  Danh sách profile liên quan tới.
@apiParam   (BODY:) {Array}    [mentions]                             Danh sách người được mentions
@apiParam   (BODY:) {String}   [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiParam   (BODY:) {String}   [mentions.account_id]                  <code>ID</code> của người được mentions
@apiParam   (BODY:) {Array}    [description_attachment_ids]           Danh sách <code>ID</code> file đính kèm trong nội dung Task.
@apiParam   (BODY:) {Object}   deadline                 Thời hạn hoàn thành của task công việc tiếp nối
@apiParam   (BODY:) {String}   deadline.type            Loại thời hạn hoàn thành của công việc tiếp nối
                                                                        <ul>
                                                                            <li><code>time_slot</code>: Khung giờ</li>
                                                                            <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                        </ul>
@apiParam   (BODY:) {String}   deadline.start_time      Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                </ul>
@apiParam   (BODY:) {Object}   deadline.end_time        Thời hạn hoàn thành công việc.
                                                                <ul>
                                                                    <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                    <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                </ul>
   
@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
    "title": "Task name",
    "assign": {
        "ids": ["5de5f623b64d2a30d5e552e6"],
        "type": "TEAM"
    },
    "type": "GENERAL",
    "priority_level": 1,
    "description": "Nội dung công việc",
    "notification_config": {
        "type": "ADVANCE_REMINDER",
        "time_value": 30,
        "time_unit": "minute"
    },
    "attachment_ids": [
        "615eb693a861dbd7e9da782f",
        "615eb67fe19bf0ff11616d8c"
    ],
    "related_to": {
        "profile_ids": [
            "5df8acd0f0c38bb7962737bc"
        ],
        "company_ids": [],
        "order_ids": [
            "5df8af0b115ea9dab22737ba",
            "5df8b12e115ea9dab22737bc"
        ],
        "ticket_ids": []
    },
    "mentions":[
        {
            "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
            "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
        }
    ],
    "description_attachment_ids": []
}                                       

                                                       
@apiUse ResponseDetailTask

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "title": "task",
        "description": "",
        "assignee_id": "82f95839-5be5-4a1b-bf20-a1f3a14025e3".
        "completion_time": "",
        "relate_to": {
            "profile_ids":[
                "5df8acd0f0c38bb7962737bc"
            ],
            "company_ids":[
                 
            ],
            "order_ids":[
                "5df8af0b115ea9dab22737ba",
                "5df8b12e115ea9dab22737bc"
            ],
            "ticket_ids":[
                 
            ]
        },
        "type": "GENERAL",
        "priority_level": 1,
        "notification_config": {
            "type": "NO_REMINDER"
        },
        "mentions":[
            {
                "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
                "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
            }
        ],
        "description_attachment_ids": [],
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""
"""
@api {POST} /task/api/v1.0/delete_task_external         Xoá task từ các module ngoài.
@apiGroup ExternalTask
@apiVersion 1.0.0
@apiName DeleteExternalTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    source                        Nguồn của Task
@apiParam   (BODY:) {array}     object_ids                    Danh sách các ID cần xoá task của Source

@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "object_ids": ["e4868f12-aedd-43c5-9e62-60c47158d805"]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}

"""

"""
@api {POST} /task/api/v1.0/calendar/on_connect       Bật kết nối đồng bộ calendar của tài khoản
@apiGroup Calendar
@apiVersion 1.0.0
@apiName OnConnectCalendar

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    staff_id                       Account bật connect
@apiParam   (BODY:) {float}     action_time                    Thời gian thao tác

@apiParamExample    {json}      BODY:
{
    "staff_id": "e4868f12-aedd-43c5-9e62-60c47158d805",
    "action_time": ************
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}

"""

"""
@api {POST} /task/api/v1.0/tasks/action/get/latest       Lấy công việc mới nhất
@apiGroup Task
@apiVersion 1.0.0
@apiName TaskLatest

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    object_id                      <code>ID</code> định danh đối tượng cần lấy thông tin
@apiParam   (BODY:) {string=SALE,COMPANY,PROFILE,TICKET}    source                         Định danh module của đối tượng cần lấy
@apiParam   (BODY:) {string=UPCOMING,DONE,OVER_DUE}  status                Trạng thái của Task. 
                                                                            <ul>
                                                                                <li><code>UPCOMING:</code>Sắp tới</li>
                                                                                <li><code>DONE:</code>Hoàn thành</li>
                                                                                <li><code>OVER_DUE:</code>Quá hạn</li>
                                                                            </ul>
@apiParam   (BODY:) {string=created_time,updated_time,due_date}    field=created_time                         Định danh field thao tác muốn lấy dữ liệu
                                                                                                            <ul>
                                                                                                                <li><code>created_time</code>: Thời gian tạo</li>
                                                                                                                <li><code>updated_time</code>: Thời gian cập nhật</li>
                                                                                                                <li><code>due_date</code>: Thời gian hoàn thành</li>
                                                                                                            </ul>
@apiParam   (BODY:) {int=-1,1}    sort=-1                                    Sort
                                                                            <ul>
                                                                                <li>1: Tăng dần</li>
                                                                                <li>-1: Giảm dần</li>
                                                                            </ul>                                                                              
                                                                                                            


@apiParamExample    {json}      BODY:
{
    "object_id": "e4868f12-aedd-43c5-9e62-60c47158d805",
    "source": "SALE",
    "status": "UPCOMING",
    "field": "due_date",
    "sort": -1
}

@apiSuccess {object}            data                          Thông tin của task
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiUse ResponseDetailTask

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "title": "task",
        "description": "",
        "assignee_id": "82f95839-5be5-4a1b-bf20-a1f3a14025e3".
        "completion_time": "",
        "relate_to": {
            "profile_ids":[
                "5df8acd0f0c38bb7962737bc"
            ],
            "company_ids":[
                 
            ],
            "order_ids":[
                "5df8af0b115ea9dab22737ba",
                "5df8b12e115ea9dab22737bc"
            ],
            "ticket_ids":[
                 
            ]
        },
        "mentions": [
            {
                "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
                "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
            }
        ],
        "type": "GENERAL",
        "priority_level": 1,
        "notification_config": {
            "type": "NO_REMINDER"
        },
        "description_attachment_ids": [],
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""

"""
@api {put} /task/api/v1.0/task/un-new          unnew task.
@apiDescription unnew task
@apiGroup  Task
@apiVersion 1.0.0
@apiName   unnew task
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiParam   (BODY:) {String}    task_id                       id của task cần unnew
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công."
}
"""

********************************** Create tags v2 ****************************
* version: 1.0.0                                                             *
******************************************************************************
"""
@api {post} /tags/work_assign Tạo tag phân công công việc 
@apiDescription Nếu tên tag đã có trên hệ thống --> báo lỗi
@apiGroup TagWorkAssign
@apiVersion 1.0.0
@apiName TagWorkCreate 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body:) {Object}    tag     Thông tin tag cần tạo.
@apiParam   (Body:) {String}    tag..value   Tên tag.
@apiParam   (Body:) {Object}    tag..properties     Thuộc tính của tag, để hiển thị trên client. Key do client chủ động tạo.
@apiParamExample    {json}  Body example:
{
    "tag":{
        "value": "phục vụ",
        "properties": {
            "background_color": "#ff0000",
            "foreground_color": "#ffffff"
        }
    }
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "tag":{
            "account_create": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "account_update": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "created_time": "2024-03-18T07:01:31Z",
            "properties": {
                "background_color": "#ff0000",
                "foreground_color": "#ffffff"
            },
            "id": "592ac76e-e4f5-11ee-8e19-5da9fabc5dbb",
            "updated_time": "2024-03-18T07:01:31Z",
            "value": "phục vụ"
        }
}
"""


********************************** Update tags v2 *******************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {PATCH} /tags/work_assign/<tag_id> Sửa tag phân công công việc
@apiDescription  Nếu tên tag được sửa trùng với tag đã có trên hệ thống --> báo lỗi
@apiGroup TagWorkAssign
@apiVersion 1.0.0
@apiName TagWorkUpdate 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body:) {Object}    tag     Thông tin tag cần sửa.
@apiParam   (Body:) {String}    tag..value   Tên tag.
@apiParam   (Body:) {Object}    tag..properties    Thuộc tính của tag, để hiển thị trên client. Key do client chủ động tạo.
@apiParamExample    {json}  Body example:
{
    "tag": {
        "value": "phục vụ",
        "properties": {
            "background_color": "#ff0000",
            "foreground_color": "#ffffff"
        }
    }
}


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "tag":{
            "account_create": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "account_update": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "properties": {
                "background_color": "#ff0000",
                "foreground_color": "#ffffff"
            },
            "id": "592ac76e-e4f5-11ee-8e19-5da9fabc5dbb",
            "created_time": "2024-03-18T07:01:31Z",
            "updated_time": "2024-03-18T07:05:31Z",
            "value": "phục vụ"
        }
}
"""


********************************* Delete tags v2 ********************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {delete} /tags/work_assign/<tag_id> Xoá tag phân công công việc
@apiDescription Xoá tag 
@apiGroup TagWorkAssign
@apiVersion 1.0.0
@apiName TagWorkDelete 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "id": "64fef441-8624-4533-b03b-6618ae93e38d"
}
"""


*********************************** List tags v2 ********************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {get} /tags/work_assign/list Lấy danh sách tags công việc có phân trang 
@apiDescription Lấy danh sách 
@apiGroup TagWorkAssign
@apiVersion 1.0.0
@apiName TagWorkList 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Query:)    {String}    [ids]   danh sách ids của các tag cần lấy (cách nhau dấu phẩy). Nếu không gửi lên thì trả về tất cả.
@apiParam   (Query:)    {String}    [search]   lọc theo tên tag
@apiParam   (Query:)    {String}    [sort]   field cần sắp xếp, chọn 1 trong các giá trị được hỗ trợ <code>value, create_time</code>
@apiParam   (Query:)    {String}    [order]   Thứ tự sắp xếp <code>asc, desc</code>, khi sắp xếp bắt buộc phải có <code>sort và order</code>, mặc định sort theo thời gian tạo mới đến cũ

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "tag":[
        {
            "id": "592ac76e-e4f5-11ee-8e19-5da9fabc5dbb",
            "account_create": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "account_update": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "properties": {
                "background_color": "#ff0000",
                "foreground_color": "#ffffff"
            },
            "created_time": "2024-03-18T07:01:31Z",
            "updated_time": "2024-03-18T07:05:31Z",
            "value": "phục vụ"
        },
        {
            "id": "592ac76d-e4f5-11ee-8e19-5da9fabc5dbb",
            "account_create": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "account_update": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "properties": {
                "background_color": "#000001",
                "foreground_color": "#ff5454"
            },
            "created_time": "2024-03-18T07:01:31Z",
            "updated_time": "2024-03-18T07:01:31Z",
            "value": "test 11"
        },
    ]
}
"""

"""
@api {get} /tags/work_assign/<tag_id> Chi tiết tag phân công công việc
@apiDescription Chi tiết
@apiGroup TagWorkAssign
@apiVersion 1.0.0
@apiName TagWorkDetail 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "created_account": null,
        "created_time": "2020-07-01T11:54:58Z",
        "id": "1f6fe10c-c14b-404f-9a55-2238c599b2a1",
        "properties": {
            "background_color": "#000000",
            "foreground_color": "#FFFFFF"
        },
        "updated_account": null,
        "updated_time": "2024-03-18T04:38:59Z",
        "value": "test 2"
    },
}
"""


********************************** upsert multi *******************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} /tags/work_assign/upsert/bulk Upsert nhiều tag 
@apiDescription  Nếu tên tag đã có thì trả về id mới nhất 
@apiGroup TagWorkAssign
@apiVersion 1.0.0
@apiName TagWorkUpsert

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body:) {String}    [account_id]     Id định danh của account, nếu không lấy được account_id từ token thì sẽ sử dụng params này
@apiParam   (Body:) {Array}    tags     Danh sách tag 
@apiParam   (Body:) {String}    tags..value   Tên tag.
@apiParam   (Body:) {Object}    tags..properties    option, Thuộc tính của tag, để hiển thị trên client. Key do client chủ động tạo.
@apiParamExample    {json}  Body example:
{
    "tags": [
        {
            "value": "test3",
            "properties": {
                "background_color": "#000000",
                "foreground_color": "#ffffff"
            }
        },
        {
            "value": "test11",
        }
    ]
}


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "created_account": null,
            "created_time": "2024-03-18T07:01:31Z",
            "id": "592ac76d-e4f5-11ee-8e19-5da9fabc5dbb",
            "properties": {
                "background_color": "#000001",
                "foreground_color": "#ff5454"
            },
            "updated_account": null,
            "updated_time": "2024-03-18T07:01:31Z",
            "value": "test 11"
        },
        {
            "created_account": null,
            "created_time": "2024-03-18T07:01:31Z",
            "id": "592ac76e-e4f5-11ee-8e19-5da9fabc5dbb",
            "properties": {
                "background_color": "#000001",
                "foreground_color": "#ff5454"
            },
            "updated_account": null,
            "updated_time": "2024-03-18T07:01:31Z",
            "value": "test 12"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {post} /tags/work_assign/ids lấy thông tin tag phân loại công việc theo id  
@apiDescription  trả về tất cả tag nếu tìm thấy 
@apiGroup TagWorkAssign
@apiVersion 1.0.0
@apiName TagWorkGetByID 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body:) {Array}    tag_ids     Danh sách tag id 
@apiParamExample    {json}  Body example:
{
    "tag_ids": ["befd6515-bcc3-4baf-9e3a-c6b19d1e1fd7",
        "ce1812a9-41b5-4225-9381-5504a9386ef8"]
}


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "created_account": null,
            "created_time": "2020-07-01T11:54:58Z",
            "id": "befd6515-bcc3-4baf-9e3a-c6b19d1e1fd7",
            "properties": {
                "background_color": "#000000",
                "foreground_color": "#FFFFFF"
            },
            "updated_account": null,
            "updated_time": "2020-07-01T11:54:58Z",
            "value": "test2"
        },
        {
            "created_account": null,
            "created_time": "2020-07-01T11:54:58Z",
            "id": "ce1812a9-41b5-4225-9381-5504a9386ef8",
            "properties": {
                "background_color": "#000000",
                "foreground_color": "#FFFFFF"
            },
            "updated_account": null,
            "updated_time": "2020-07-01T11:54:58Z",
            "value": "test2"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

********************************* Delete multi tags v2 **************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {POST} /tags/work_assign/delete Xoá nhiều tag phân công công việc
@apiDescription Xoá nhiều tag 
@apiGroup TagWorkAssign
@apiVersion 1.0.0
@apiName TagWorkMultiDelete 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body:) {Array}    tag_ids     Danh sách tag id cần xoá

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200
    "lang": "vi",
    "message": "request thành công."
}
"""
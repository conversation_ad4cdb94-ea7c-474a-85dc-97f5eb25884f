#!/usr/bin/python
# -*- coding: utf8 -*-

****************************************  Danh sách tag  *******************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /tags Danh sách tag
@apiDescription Dịch vụ lấy danh sách tag có phân trang. (API profiling: /profiling/v3.0/merchants/<merchant_id>/tags)
@apiGroup Tag
@apiVersion 1.0.0
@apiName SearchTag

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam          (Query:) {String}        [search_text] Chuỗi tìm kiếm
@apiParam          (Query:) {String}        [roles]         Quyền, nhiều quyền cách nhau bằng dấu , ("assign,view")
@apiParam   (Query:)    {String}    [sort]   field cần sắp xếp, chọn 1 trong các giá trị được hỗ trợ <code>tag_name, create_time</code>
@apiParam   (Query:)    {String}    [order]   Thứ tự sắp xếp <code>asc, desc</code>, khi sắp xếp bắt buộc phải có <code>sort và order</code>, mặc định sort theo thời gian tạo mới đến cũ


@apiSuccess {Array}   data    Danh sách tag

@apiSuccess (data) {String} 	tag_id						    Mã định danh tag
@apiSuccess (data) {String} 	tag_name				  Tên tag
@apiSuccess (data) {String} 	description				    Mô tả tag
@apiSuccess (data) {String}   merchant_id                      Mã định danh merchant
@apiSuccess (data) {String}   created_by                      id tài khoản tạo
@apiSuccess (data) {String}   updated_by                      id tài khoản cập nhật 
@apiSuccess (data) {object}   time_expiry                     cấu hình thời gian hiệu lực 
@apiSuccess (time_expiry) {String}   unit                     đơn vị thời gian: "day", "month", "year" 
@apiSuccess (time_expiry) {Number}   value                    số lượng theo thời gian 


@apiSuccessExample {json} Response list tag example
{
  "data": [
      {
        "created_time": "Mon, 29 Mar 2021 08:08:40 GMT",
        "description": null,
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "roles": [
            "assign",
            "view"
        ],
        "tag_id": "394f9237-48ae-48be-b4da-db469b99ef16",
        "tag_name": "kkkkkkkk",
        "tag_type": 100,
        "updated_time": "Mon, 29 Mar 2021 08:08:40 GMT",
        "time_expiry": {
          "unit": "day",
          "value": 10,
        },
        "created_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
        "updated_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
    }
  ],
  "paging": {
    "page": 1,
    "per_page": 20,
    "total_items": 1645,
    "total_pages": 83
  }
  
}
"""


****************************************  Danh sách tag hay dùng  *******************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /tags/sort Danh sách tag hay dùng
@apiDescription Dịch vụ lấy danh sách tag hay dùng của nhân viên hoặc của team có phân trang 
@apiGroup Tag
@apiVersion 1.0.0
@apiName SortTag

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam          (Query:) {String}        [team_id]       ID team, nếu team_id có giá trị thì tìm kiếm theo team_id
@apiParam          (Query:) {String}        [staff_id]      ID nhân viên, nếu không có team_id thì tìm kiếm theo staff_id nếu có giá trị 
@apiParam          (Query:) {String}        [search_text]   Chuỗi tìm kiếm
@apiParam          (Query:) {String}        [roles]         Quyền, nhiều quyền cách nhau bằng dấu , ("assign,view")

@apiSuccess {Array}   data    Danh sách tag

@apiSuccess (data) {String} 	tag_id						    Mã định danh tag
@apiSuccess (data) {String} 	tag_name				  Tên tag
@apiSuccess (data) {String} 	description				    Mô tả tag
@apiSuccess (data) {String}   merchant_id                      Mã định danh merchant

@apiSuccessExample {json} Response list tag example
{
  "data": [
      {
        "description": null,
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "roles": [
            "assign",
            "view"
        ],
        "tag_id": "394f9237-48ae-48be-b4da-db469b99ef16",
        "tag_name": "kkkkkkkk",
        "tag_type": 100,
    }
  ],
  "paging": {
    "page": 1,
    "per_page": 20,
    "total_items": 1645,
    "total_pages": 83
  }
  
}
"""


****************************************  Tạo tag  *************************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************


"""
@api {post} /tags Tạo tag
@apiDescription Dịch vụ tạo tag nếu tag chưa tồn tại. (API profiling: /profiling/v3.0/tags/create)
@apiGroup Tag
@apiVersion 1.0.0
@apiName CreateTag

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam       (Body:) {String}    tag_name    Tên tag
@apiParam       (Body:) {Array}   [roles]       Các quyền
<li><code>view: Sử dụng chức năng tìm kiếm</code></li>
<li><code>assign: Sử dụng chức năng gán vào profile</code></li>
@apiParam       (Body:) {String}    description     Mô tả tag
@apiParam       (Body:) {String}    account_id     id tài khoản
@apiParam       (Body:) {object}    time_expiry     thời gian hiệu lực 
@apiParam       (time_expiry:) {String}    unit     đơn vị thời gian: "day", "month", "year" 
@apiParam       (time_expiry:) {Number}    value     số lượng theo thời gian 

                
@apiParamExample {json} Body request example
{
    "tag_name": "Tag A",
    "tag_type": 100,
    "roles": ["view", "assign"],
    "description": "Mo ta cua tag",
    "account_id": "c0713108-7449-4bd6-86d5-1198b73495c7",
    "time_expiry": {
      "unit": "day",
      "value": 10,
    },
}

@apiSuccess {Object}   data    Thông tin tag
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String}   tag_id                Mã định danh tag
@apiSuccess (data) {String}   tag_name          Tên tag
@apiSuccess       (data) {Array}   [roles]       Các quyền
<li><code>view: Sử dụng chức năng tìm kiếm</code></li>
<li><code>assign: Sử dụng chức năng gán vào profile</code></li>
@apiSuccess (data) {Number}   tag_type          Loại tag
@apiSuccess (data) {String}   description           Mô tả tag
@apiSuccess (data) {String}   merchant_id                      Mã định danh merchant

@apiSuccessExample {json} Response create tag example
{
  "data": {
    "tag_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
        "tag_name": "Tag A",
        "tag_type": 100,
        "roles": ["view", "assign"],
        "description": "Mo ta cua tag",
        "merchant_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
        "created_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
        "updated_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
        "time_expiry": {
          "unit": "day",
          "value": 10,
        },
  },
  "code": 200,
  "message": "request thành công"
}

"""


****************************************  Cập nhật tag  ********************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************

"""
@api {put} /tags/<tag_id> Cập nhật tag
@apiDescription Dịch vụ cập nhật tag. (API profiling: /profiling/v3.0/tags/update)
@apiGroup Tag
@apiVersion 1.0.0
@apiName UpdateTag

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam       (Body:)     tag_name    Tên tag
@apiParam       (Body:) {Array}   [roles]       Các quyền
<li><code>view: Sử dụng chức năng tìm kiếm</code></li>
<li><code>assign: Sử dụng chức năng gán vào profile</code></li>
@apiParam       (Body:)     [description]     Mô tả tag
@apiParam       (Body:) {String}    account_id     id tài khoản
@apiParam       (Body:) {object}    time_expiry     thời gian hiệu lực 
@apiParam       (time_expiry:) {String}    unit     đơn vị thời gian: "day", "month", "year" 
@apiParam       (time_expiry:) {Number}    value     số lượng theo thời gian 

@apiParamExample {json} Body request example
{
    "tag_name": "Tag A",
    "roles": ["view", "assign"],
    "description": "Mo ta cua tag",
    "account_id": "c0713108-7449-4bd6-86d5-1198b73495c7",
    "time_expiry": {
      "unit": "day",
      "value": 10,
    },
}

@apiSuccess {Object}   data    Thông tin tag
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi
@apiSuccess (data) {String}   tag_id                Mã định danh tag
@apiSuccess (data) {String}   tag_name          Tên tag
@apiSuccess (data) {Array}   roles       Các quyền
@apiSuccess (data) {String}   description           Mô tả tag
@apiSuccess (data) {String}   merchant_id                      Mã định danh merchant


@apiSuccessExample {json} Response update tag example
{
  "data": {
    "tag_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
    "tag_name": "Tag A",
    "roles": ["view", "assign"],
    "description": "Mo ta cua tag",
    "merchant_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
    "created_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
    "updated_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
    "time_expiry": {
      "unit": "day",
      "value": 10,
    },
  },
  "code": 200,
  "message": "request thành công"
}
"""

****************************************  Lấy thông tin tag  ***************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /tags/<tag_id> Lấy thông tin tag
@apiDescription Dịch vụ lấy chi tiết tag. (API profiling: /profiling/v3.0/tags/<tag_id>/detail)
@apiGroup Tag
@apiVersion 1.0.0
@apiName GetTagById

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiSuccess {Object}   data    Thông tin tag
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi
@apiSuccess (data) {String} 	tag_id						    Mã định danh tag
@apiSuccess (data) {String} 	tag_name				  Tên tag
@apiSuccess (data) {String} 	description				    Mô tả tag
@apiSuccess (data) {String}   merchant_id                      Mã định danh merchant

@apiSuccessExample {json} Response get tag by id example
{
  "data": {
  	"tag_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
    "tag_name": "Tag A",
    "description": "Mo ta cua tag",
    "merchant_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
    "created_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
    "updated_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
    "time_expiry": {
      "unit": "day",
      "value": 10,
    },
  },
  "code": 200,
  "message": "request thành công"
}

"""
****************************************  Create multi tags  ***************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /tags/bulk  Tạo nhiều tag
@apiDescription Dịch vụ tạo nhiều tag, nếu 1 trong số các tag đã tồn tại thì không thêm mới tag nào cả. (API profiling: /profiling/v3.0/tags/multi_create)
@apiGroup Tag
@apiVersion 1.0.0
@apiName CreateMultiTag

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam       (Body:)  {array}    tags                   Thông tin các tag cần thêm
@apiParam       (Body:)  {string}   tags.tag_name          Tên tag
@apiParam       (Body:)  {string}   [tags.description]     Mô tả tag
@apiParam       (Body:) {String}    account_id     id tài khoản
@apiParam       (Body:) {object}    time_expiry     thời gian hiệu lực 
@apiParam       (time_expiry:) {String}    unit     đơn vị thời gian: "day", "month", "year" 
@apiParam       (time_expiry:) {Number}    value     số lượng theo thời gian 

@apiParamExample {json} Body request example
{
  "tags": [
    {
      "tag_name": "test1"
    },
    {
      "tag_name": "test2",
      "description": "mo ta test2",
      "account_id": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "time_expiry": {
        "unit": "day",
        "value": 10,
      },
    }
  ]
}

@apiSuccess {Array}   data    Thông tin các tag đã thêm
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String}   tag_id                Mã định danh tag
@apiSuccess (data) {String}   tag_name              Tên tag
@apiSuccess (data) {String}   description           Mô tả tag
@apiSuccess (data) {String}   merchant_id           Mã định danh merchant

@apiSuccessExample {json} Response create multi tag example
{
  "code": 200,
  "data": [
    {
      "description": null,
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "tag_id": "68f2006e-8c65-467b-ad6f-6980933afe27",
      "tag_name": "test1",
      "tag_type": 200,
      "created_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "updated_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "time_expiry": {
        "unit": "day",
        "value": 10,
      },
    },
    {
      "description": "mo ta test2",
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "tag_id": "2858518f-cd35-48ed-80f0-dcde80a6bad7",
      "tag_name": "test2",
      "tag_type": 200,
      "created_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "updated_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "time_expiry": {
        "unit": "day",
        "value": 10,
      },
    }
  ],
  "lang": "vi",
  "message": "request thành công."
}

"""

***************************************  Tìm kiếm tags  ********************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /tags/find Tìm kiếm tags
@apiDescription Tìm kiếm tags dựa trên ID hoặc TAG_NAME. (API profiling: /profiling/v3.0/tags/find)
@apiGroup Tag
@apiVersion 1.0.0
@apiName FindTag

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam       (Query:)     [id]                Id của Tag
@apiParam       (Query:)     [tag_name]          Tên của Tags cần tìm kiếm
@apiParam       (Query:)     [tag_ids]           Danh sách các tag cần lấy theo id, các id cách nhau dấu , 


@apiSuccess {Object}   data    Thông tin tag
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	tag_id						    Mã định danh tag
@apiSuccess (data) {String} 	tag_name				  Tên tag
@apiSuccess (data) {String} 	description				    Mô tả tag
@apiSuccess (data) {String}   merchant_id                      Mã định danh merchant
@apiSuccess (data) {String}   used_by           Danh sách các modules sử dụng tag này.

@apiSuccessExample {json} Response find tag by ID example
{
  "data": {
  	"tag_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
    "tag_name": "Tag A",
    "description": "Mo ta cua tag",
    "merchant_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
    "created_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
    "updated_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
    "time_expiry": {
      "unit": "day",
      "value": 10,
    },
  },
  "code": 200,
  "message": "request thành công"
}

@apiSuccessExample {json} Response find tag by TAG_NAME example
{
  "data": [
    {
      "tag_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
      "tag_name": "Tag A",
      "description": "Mo ta cua tag",
      "merchant_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
      "created_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "updated_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "time_expiry": {
        "unit": "day",
        "value": 10,
      },
    }
  ],
  "code": 200,
  "message": "request thành công"
}

@apiSuccessExample {json} Response find tag by TAG_IDS example
{
  "data": [
    {
      "tag_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
      "tag_name": "Tag A",
      "description": "Mo ta cua tag",
      "merchant_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
      "created_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "updated_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "time_expiry": {
        "unit": "day",
        "value": 10,
      },
    }
  ],
  "code": 200,
  "message": "request thành công"
}
"""


*************************************  Check tag tồn tại  ******************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /tags/exists Kiểm tra tag tồn tại
@apiDescription Kiểm tra tag name đã tồn tại trên hệ thống. (API profiling: /profiling/v3.0/tags/exists)
@apiGroup Tag
@apiVersion 1.0.0
@apiName CheckTagExists

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiParam       (Body:)     tag_name          Tên của Tag cần tìm kiếm

@apiParamExample {json} Body request example
{
  "tag_name": "test1"
}

@apiSuccess {Bool}    exists    Trạng thái tag này đã tồn tại hay chưa?
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi


@apiSuccessExample {json} Response
{
  "exists": true,
  "code": 200,
  "message": "request thành công"
}
"""


****************************************  Upsert multi tags  ***************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /tags/upsert/bulk  Upsert nhiều tag
@apiDescription Dịch vụ tạo nhiều tag, nếu 1 trong số các tag đã tồn tại thì lấy thông tin trả về cùng các tag tạo mới. (API profiling: /profiling/v3.0/tags/multi_create)
@apiGroup Tag
@apiVersion 1.0.0
@apiName UpsertMultiTag

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam       (Body:)  {array}    tags                   Thông tin các tag cần thêm
@apiParam       (Body:)  {string}   tags.tag_name          Tên tag
@apiParam       (Body:)  {string}   [tags.description]     Mô tả tag
@apiParam       (Body:) {String}    account_id     id tài khoản
@apiParam       (Body:) {object}    time_expiry     thời gian hiệu lực 
@apiParam       (time_expiry:) {String}    unit     đơn vị thời gian: "day", "month", "year" 
@apiParam       (time_expiry:) {Number}    value     số lượng theo thời gian 


@apiParamExample {json} Body request example
{
  "tags": [
    {
      "tag_name": "test1"
    },
    {
      "tag_name": "test2",
      "description": "mo ta test2",
      "account_id": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "time_expiry": {
        "unit": "day",
        "value": 10,
      },
    }
  ]
}

@apiSuccess {Array}   data    Thông tin các tag đã thêm
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi
@apiSuccess (data) {String}   tag_id                Mã định danh tag
@apiSuccess (data) {String}   tag_name              Tên tag
@apiSuccess (data) {String}   description           Mô tả tag
@apiSuccess (data) {String}   merchant_id           Mã định danh merchant

@apiSuccessExample {json} Response create multi tag example
{
  "code": 200,
  "data": [
    {
      "description": null,
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "tag_id": "68f2006e-8c65-467b-ad6f-6980933afe27",
      "tag_name": "test1",
      "tag_type": 200,
      "created_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "updated_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "time_expiry": {
        "unit": "day",
        "value": 10,
      },
    },
    {
      "description": "mo ta test2",
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "tag_id": "2858518f-cd35-48ed-80f0-dcde80a6bad7",
      "tag_name": "test2",
      "tag_type": 200,
      "created_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "updated_by": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "time_expiry": {
        "unit": "day",
        "value": 10,
      },
    }
  ],
  "lang": "vi",
  "message": "request thành công."
}

"""



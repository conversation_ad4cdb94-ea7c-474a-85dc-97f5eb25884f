#!/usr/bin/python
# -*- coding: utf8 -*-

"""
@api {get} /tags/fields Danh sách tag field theo merchant
@apiDescription Danh sách tag field
@apiGroup TagFieldModule
@apiVersion 1.0.0
@apiName SearchTagField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiSuccessExample {json} Response list tag field example
{
  "sale": [
            {
                "field_key": "KHD",
                "tag_name": ["tag1", "tag2"]
            },
            ]
  "profile": [
                {
                    "field_key": "KHD2",
                    "tag_name": ["tag3", "tag4"]
                },
                ]

}
"""

"""
@api {POST} /tags/fields Tạo tag field
@apiDescription Create Tag Field
@apiGroup TagFieldModule
@apiVersion 1.0.0
@apiName TagFieldCreate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body:) {String}   field_key        Tên key
@apiParam   (Body:) {Array}    tag_name         Tên tag
@apiParam   (Body:) {String}   module_name      Tên module

@apiParamExample {json}  Body example:
{
    "field_key": "KHD",
    "tag_name": ["tag100", "tag200"],
    "module_name": "sale"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "field_key": "KHD",
        "module_name": "sale",
        "tag_name": [
            "tag100",
            "tag200"
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
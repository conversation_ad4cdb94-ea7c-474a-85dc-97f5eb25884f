#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 15/08/2023
"""
"""

@apiDefine ParamExampleQuestionBlock

@apiParamExample   {json}  Body question type image(Kh<PERSON>i hình ảnh ) Example:
    {
        "ma_theme" : "Cam",
        "bg_color_theme": "#226ff5",
        "is_system_log": true,
        "survey_html": "aGVsbG8gZG1t",
        "pages": [
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [
                    {
                        "id": "6e319027-d62d-4f2b-9507-531f75176f52",
                        "type": "image",
                        "title": "", // Tiêu đề của khối,
                        "block_image": {
                            "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                            "url": "", // <PERSON> hình ảnh,
                            "style": {
                                
                            } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                        },
                        "block_note": {
                            "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng,
                            "description": "", // Nội dung Ghi chú
                        }
                        "order": 1
                    },
                ]
            }
        ],
        "session_commit": "abs9812nasgd12asd"
    }

@apiParamExample   {json}  Body question type block Example:
    {
        "ma_theme" : "Cam",
        "bg_color_theme": "#226ff5",
        "is_system_log": true,
        "survey_html": "aGVsbG8gZG1t",
        "pages": [
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [{
                    "id": "6e319027-d62d-4f2b-9507-531f75176f52",
                    "type": "block",
                    "description": "Example description block 1",
                    "order": 1,
                    "block_image": {
                        "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                        "url": "", // Link hình ảnh,
                        "style": {
                            
                        } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                    }
                },
                {
                    "id": "e15902df-6733-49af-b4d3-6a53c18d2b68",
                    "type": "block",
                    "description": "Example description block 2",
                    "order": 2,
                    "block_image": {
                        "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                        "url": "", // Link hình ảnh,
                        "style": {
                            
                        } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                    }
                },
                ]
            }
        ],
        "session_commit": "abs9812nasgd12asd"
    }

@apiParamExample   {json}  Body question type dropdown example:
    {
        "id" : "19021128-a322-4b08-92db-a2c96a982e36",
        "ma_theme" : "Cam",
        "bg_color_theme": "#226ff5",
        "status": 1,
        "pages": [
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [{
                    "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                    "type": "dropdown",
                    "title": "Example title dropdown"
                    "order": 1,
                    "is_required": false,
                    "views": "vertical",
                    "block_image": {
                        "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                        "url": "", // Link hình ảnh,
                        "style": {
                            
                        } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                    },
                    "answers":[{
                        "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                        "value": "Example answer dropdown 1",
                        "order": 1,
                        "block_image": {
                            "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                            "url": "", // Link hình ảnh,
                            "style": {
                                
                            } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                        }
                    },
                    {
                        "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                        "value": "Example answer dropdown 2",
                        "order": 2,
                        "block_image": {
                            "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                            "url": "", // Link hình ảnh,
                            "style": {
                                
                            } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                        }
                    }]
                }
                ]
            }
        ],
        "session_commit": "abs9812nasgd12asd"
    }

@apiParamExample   {json}  Body question type multiple choices example:
    {
        "id" : "19021128-a322-4b08-92db-a2c96a982e36",
        "ma_theme" : "Cam",
        "bg_color_theme": "#226ff5",
        "status": 1,
        "pages": [
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [{
                    "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                    "type": "multiple_choices",
                    "title": "Example title multiple choices"
                    "order": 1,
                    "is_required": false,
                    "views": "vertical",
                    "block_image": {
                        "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                        "url": "", // Link hình ảnh,
                        "style": {
                            
                        } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                    },
                    "answers":[{
                        "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                        "value": "Example answer multiple choices 1",
                        "order": 1,
                        "block_image": {
                            "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                            "url": "", // Link hình ảnh,
                            "style": {
                                
                            } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                        }
                    },
                    {
                        "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                        "value": "Example answer multiple choices 1",
                        "order": 2,
                        "block_image": {
                            "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                            "url": "", // Link hình ảnh,
                            "style": {
                                
                            } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                        }
                    }]
                }
                ]
            }
        ],
        "session_commit": "abs9812nasgd12asd"
    }

@apiParamExample   {json}  Body question type single choice example:
    {
        "id" : "19021128-a322-4b08-92db-a2c96a982e36",
        "ma_theme" : "Cam",
        "bg_color_theme": "#226ff5",
        "status": 1,
        "pages": [
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [{
                    "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                    "type": "single_choice",
                    "title": "Example title single choice"
                    "order": 1,
                    "is_required": false,
                    "views": "vertical",
                    "block_image": {
                        "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                        "url": "", // Link hình ảnh,
                        "style": {
                            
                        } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                    },
                    "answers":[{
                        "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                        "value": "Example answer single choice 1",
                        "type": "choice",
                        "order": 1,
                        "block_image": {
                            "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                            "url": "", // Link hình ảnh,
                            "style": {
                                
                            } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                        }
                    },
                    {
                        "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                        "value": "Example answer single choice 1",
                        "order": 2,
                        "type": "choice",
                        "block_image": {
                            "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                            "url": "", // Link hình ảnh,
                            "style": {
                                
                            } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                        }
                    },
                    {
                        "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                        "value": "",
                        "order": 2,
                        "type": "other",
                        "block_image": {
                            "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                            "url": "", // Link hình ảnh,
                            "style": {
                                
                            } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                        }
                    }]
                }
                ]
            }
        ],
        "session_commit": "abs9812nasgd12asd"
    }

@apiParamExample   {json}  Body question type date example:
    {
        "id" : "19021128-a322-4b08-92db-a2c96a982e36",
        "ma_theme" : "Cam",
        "bg_color_theme": "#226ff5",
        "status": 1,
        "pages": [
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [{
                    "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                    "type": "date",
                    "title": "Example title date time"
                    "order": 1,
                    "is_required": true,
                    "block_image": {
                        "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                        "url": "", // Link hình ảnh,
                        "style": {
                            
                        } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                    },
                    "views": "vertical",
                    "answers":[]
                }
                ]
            }
        ],
        "session_commit": "abs9812nasgd12asd"
    }


@apiParamExample   {json}  Body question type short answers:
    {
        "id" : "19021128-a322-4b08-92db-a2c96a982e36",
        "ma_theme" : "Cam",
        "bg_color_theme": "#226ff5",
        "status": 1,
        "pages": [
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [{
                    "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                    "type": "short_answers",
                    "title": "Example title short answers",
                    "order": 1,
                    "is_required": true,
                    "views": "vertical",
                    "block_image": {
                        "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                        "url": "", // Link hình ảnh,
                        "style": {
                            
                        } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                    },
                    "answers":[]
                }
                ]
            }
        ],
        "session_commit": "abs9812nasgd12asd"
    }

@apiParamExample   {json}  Body question type paragraph:
    {
        "id" : "19021128-a322-4b08-92db-a2c96a982e36",
        "ma_theme" : "Cam",
        "bg_color_theme": "#226ff5",
        "status": 1,
        "pages": [
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [{
                    "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                    "type": "paragraph",
                    "title": "Example title paragraph",
                    "order": 1,
                    "is_required": false,
                    "views": "vertical",
                    "block_image": {
                        "status": 0, // Trạng thái sử dụng hình ảnh với số 0 thì sẽ là không sử dụng khối này. 1 thì là sử dụng
                        "url": "", // Link hình ảnh,
                        "style": {
                            
                        } // Cấu hình style hình ảnh, phần này FE sẽ lưu lại các config cần thiết. BE sẽ không quy định phần này.
                    },
                    "answers":[]
                }
                ]
            }
        ],
        "session_commit": "abs9812nasgd12asd"
    }

@apiParamExample   {json}  Body question type slider:
    {
        "id" : "19021128-a322-4b08-92db-a2c96a982e36",
        "ma_theme" : "Cam",
        "bg_color_theme": "#226ff5",
        "status": 1,
        "pages": [
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [{
                    "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                    "type": "slider",
                    "title": "Example title slider",
                    "order": 1,
                    "is_required": false,
                    "views": "vertical",
                    "slider": {
                        "min_value": 1,
                        "min_title": "Min title",
                        "max_value": 10,
                        "max_title": "Max title",
                    }
                }
                ]
            }
        ],
        "session_commit": "abs9812nasgd12asd"
    }
    
@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
    {
        "code": 200,
        "lang": "vi",
        "message": "Lưu nháp thành công!",
        "data": {
            "session_commit": "ca693c99-156e-4072-9da8-cde9980300ec",
            "status_update_survey": true,
            "title": "Title survey",
            "status": 1,
            "updated_time": "2021-04-09T15:11:43Z"
        }   
    }
"""


"""
@apiDefine ParamRequestUpsertSurvey
@apiParam (Body:) {String} ma_theme                            Mã theme của Survey
@apiParam (Body:) {String} bg_color_theme                      Mã bg color theme của Survey
@apiParam (Body:) {Boolean} is_system_log                      Kiểm tra xem hệ thống tự save hay khách hàng save. <li><code>true:</code> hệ thống save</li><li><code>false:</code> khách hàng save</li>
@apiParam (Body:) {String}  survey_html                        Dạng base64 của survey html (Dùng để generate thumbnail)
@apiParam (Body:) {Array[]} pages                              Danh sách các page ở trong Survey
@apiParam (Body:) {String} pages.id                            <code>Id</code> của trang
@apiParam (Body:) {String} pages.title                         Tên của trang
@apiParam (Body:) {String} pages.description                   Mô tả của trang
@apiParam (Body:) {Int} pages.order                            Vị trí của trang
@apiParam (Body:) {Array[]} pages.questions                    Thông tin chung của question
@apiParam (Body:) {String} pages.questions.id                  Id của câu hỏi
@apiParam (Body:) {String} pages.questions.type                Loại câu hỏi. Các loại câu hỏi: <li><code>image </code></li> với type dạng này sẽ được định nghĩa là khối hình ảnh.<li><code>block </code></li> với type dạng này sẽ được định nghĩa là khối. <li><code>dropdown </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời thả xuống. <li><code>multiple_choices </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có nhiều lựa chọn trả lời. <li><code>date </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dạng ngày tháng năm. <li><code>single_choice </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có 01 đáp án. <li><code>short_answers </code></li> với type dạng này sẽ được định nghĩa là câu trả lời ngắn. <li><code>paragraph </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dài. <li><code>slider </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời là phạm vi đánh giá.
@apiParam (Body:) {String} pages.questions.title               Tên của câu hỏi hoặc khối.
@apiParam (Body:) {String} [pages.questions.description]         Mô tả của câu hỏi hoặc khối.
@apiParam (Body:) {String} pages.questions.order               Vị trí của câu hỏi hoặc khối.
@apiParam (Body:) {Boolean} pages.questions.is_required        Câu hỏi bắt buộc phải trả lời. <code>Chỉ áp dụng cho câu hỏi.</code>
@apiParam (Body:) {String} pages.questions.view                Dạng xem của đáp án. <li><code>vertical: </code></li> các câu trả lời hiện theo chiều dọc. <li><code>horizontal: </code></li> các câu trả lời hiện theo chiều ngang.
@apiParam (Body:) {Array[]} [pages.questions.answers]            Danh sách đáp án.
@apiParam (Body:) {string} [pages.questions.answers.id]          <code>ID</code> của đáp án
@apiParam (Body:) {string} [pages.questions.answers.value]       Nội dung của đáp án.
@apiParam (Body:) {Int} [pages.questions.answers.order]          Vị trí của đáp án.
@apiParam (Body:) {Object} [pages.questions.answers.block_image]    Khối hình ảnh của câu trả lời.
@apiParam (Body:) {Object} [pages.questions.answers.type=text]      Loại của câu trả lời.
                                                                    <ul>
                                                                        <li>text: là loại bình thường</li>
                                                                        <li>other: là loại câu trả lời dạng other, có thể cho user nhập</li>
                                                                    </ul>
@apiParam (Body:) {Object} [pages.questions.slider]              Các thuộc tính của câu hỏi dạng <code>phạm vi đánh giá</code>.
@apiParam (Body:) {Int} [pages.questions.slider.min_value]       Giá trị nhỏ nhất.
@apiParam (Body:) {String} [pages.questions.slider.min_title]    Title của giá trị nhỏ nhất.
@apiParam (Body:) {Int} [pages.questions.slider.max_value]       Giá trị lớn nhất.
@apiParam (Body:) {Int} [pages.questions.slider.max_title]       Title của giá trị lớn
@apiParam (Body:) {String} [session_commit]                         Định nghĩa phiên thực hiện thao tác với cơ sở dữ liệu.

@apiSuccess (Data:) {String} session_commit                     Phiên thực hiện thao tác
@apiSuccess (Data:) {Boolean} status_update_survey               Trạng thái update survey
@apiSuccess (Data:) {String} title                              Tiêu đề của survey
@apiSuccess (Data:) {String} status                             Trạng thái của Survey
@apiSuccess (Data:) {String} updated_time                       Thời gian cập nhật mới nhất của survey
@apiSuccess (Data:) {Object}     header                          Header Survey
@apiSuccess (Data:) {Int}        header.use_header               Sử dụng header hay không?
                                                                 <ul>
                                                                     <li><code>0</code>: Không sử dụng</li>
                                                                     <li><code>1</code>: Sử dụng</li>
                                                                 </ul>
@apiSuccess (Data:) {String}     header.url_image                Link ảnh header

"""
"""
@apiDefine SuccessRequestUpsertSurvey
@apiSuccess (Data:) {String} id                                  <code>Id</code> của Survey
@apiSuccess (Data:) {String} ma_theme                            Mã theme của Survey
@apiSuccess (Data:) {String} bg_color_theme                      Mã bg color theme của Survey
@apiSuccess (Data:) {String} title                               Tên của Survey
@apiSuccess (Data:) {String} short_description                   Mô tả ngắn về Survey
@apiSuccess (Data:) {String} description                         Mô tả về Survey
@apiSuccess (Data:) {String} survey_draft_id                     <code>Id</code> bản nháp của survey đó, nếu không có thì là rỗng 
@apiSuccess (Data:) {Int=1, 2, 3} status                         Trạng thái của Survey. <li><code>1:</code> activate</li><li><code>2:</code> deactivate</li><li><code>3:</code> draft</li>
@apiSuccess (Data:) {Array[]} pages                              Danh sách các page ở trong Survey
@apiSuccess (Data:) {String} pages.id                            Id của trang
@apiSuccess (Data:) {String} pages.title                         Tên của trang
@apiSuccess (Data:) {String} pages.description                   Mô tả của trang
@apiSuccess (Data:) {Int} pages.order                            Vị trí của trang
@apiSuccess (Data:) {Array[]} pages.questions                    Thông tin chung của câu hỏi
@apiSuccess (Data:) {String} pages.questions.id                  Id của câu hỏi
@apiSuccess (Data:) {String} pages.questions.type                Loại câu hỏi. Các loại câu hỏi: <li><code>image </code></li> với type dạng này sẽ được định nghĩa là khối hình ảnh.<li><code>block </code></li> với type dạng này sẽ được định nghĩa là khối. <li><code>dropdown </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời thả xuống. <li><code>multiple_choices </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có nhiều lựa chọn trả lời. <li><code>date </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dạng ngày tháng năm. <li><code>single_choice </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có 01 đáp án. <li><code>short_answers </code></li> với type dạng này sẽ được định nghĩa là câu trả lời ngắn. <li><code>paragraph </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dài. <li><code>slider </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời là phạm vi đánh giá.
@apiSuccess (Data:) {String} pages.questions.title               Tên của câu hỏi hoặc khối.
@apiSuccess (Data:) {String} [pages.questions.description]         Mô tả của câu hỏi hoặc khối.
@apiSuccess (Data:) {String} pages.questions.order               Vị trí của câu hỏi hoặc khối.
@apiSuccess (Data:) {Boolean} pages.questions.is_required        Câu hỏi bắt buộc phải trả lời. <code>Chỉ áp dụng cho câu hỏi.</code>
@apiSuccess (Data:) {String} pages.questions.view                Dạng xem của đáp án. <li><code>vertical: </code></li> các câu trả lời hiện theo chiều dọc. <li><code>horizontal: </code></li> các câu trả lời hiện theo chiều ngang.
@apiSuccess (Data:) {Array[]} [pages.questions.answers]            Danh sách đáp án.
@apiSuccess (Data:) {string} [pages.questions.answers.id]          <code>Id</code> của đáp án
@apiSuccess (Data:) {string} [pages.questions.answers.value]       Nội dung của đáp án.
@apiSuccess (Data:) {Int} [pages.questions.answers.order]          Vị trí của đáp án.
@apiSuccess (Data:) {Object} [pages.questions.answers.type=text]   Loại của câu trả lời. <code>Nếu trong trường hợp không tồn tại field này thì vui lòng set mặc địn là text.</code>
                                                                    <ul>
                                                                        <li>text: là loại bình thường</li>
                                                                        <li>other: là loại câu trả lời dạng other, có thể cho user nhập</li>
                                                                    </ul>
@apiSuccess (Data:) {Object[]} [pages.questions.slider]              Các thuộc tính của câu hỏi dạng <code>phạm vi đánh giá</code>.
@apiSuccess (Data:) {Int} [pages.questions.slider.min_value]       Giá trị nhỏ nhất.
@apiSuccess (Data:) {String} [pages.questions.slider.min_title]    Title của giá trị nhỏ nhất.
@apiSuccess (Data:) {Int} [pages.questions.slider.max_value]       Giá trị lớn nhất.
@apiSuccess (Data:) {Int} [pages.questions.slider.max_title]       Title của giá trị lớn nhất

"""

"""
@api {post}          {domain}/survey/api/v1.0/surveys                              Danh sách toàn bộ survey
@apiName list survey
@apiGroup Survey
@apiDescription                                             API lấy danh sách Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging


@apiParam   (Body:) {String}    [sort]                            Sắp xếp theo các trường chỉ định. Ví dụ <code>&sort=name</code> <li><code>name</code> sort theo tên</li> <li><code>created_time</code> sort theo thời gian tạo</li>  <li><code>updated_time</code> sort theo thời gian cập nhật cuối cùng</li>
@apiParam   (Body:) {String}    [order]                           Kiểu sắp xếp: <code>asc</code> <code>desc</code>
@apiParam   (Body:) {String}    [status=1]                        Lấy theo trạng thái của bản khảo sát. Ví dụ <code>&status=1,2</code>  <li><code>1</code>danh sách các mẫu khảo sát đang hoạt động (activate)</li> <li><code>2</code>danh sách các mẫu khảo sát không hoạt động (deaactivate)</li>  <li><code>3</code> danh sách các mẫu khảo sát nháp (draft)</li>
@apiParam   (Body:) {Number}    [page]                            Vị trí page cần lấy dữ liệu. Set <code>&page=-1</code> nếu muốn lấy tất cả dữ liệu.<code>MIN_VALUE=1</code>Example: <code>2</code>Default value: <code>1</code>
@apiParam   (Body:) {Number}    [per_page]                        Số phần tử trên một page. Example: <code>&per_page=5</code>
@apiParam   (Body:) {String}    [search]                          Tìm kiếm theo title. Ví dụ <code>"thương hiệu"</code>

@apiParamExample   {json}  Body:
{
  "statuses": 1,
  "page": 1,
  "per_page": 15,
  "search": ""
}

@apiSuccess (Data:) {String}     id                              <code>Id</code> của mỗi Survey
@apiSuccess (Data:) {Object}     header                          Header Survey
@apiSuccess (Data:) {Int}        header.use_header               Sử dụng header hay không?
                                                                 <ul>
                                                                     <li><code>0</code>: Không sử dụng</li>
                                                                     <li><code>1</code>: Sử dụng</li>
                                                                 </ul>
@apiSuccess (Data:) {String}     header.url_image                Link ảnh header
@apiSuccess (Data:) {String}     title                           Tên của Survey
@apiSuccess (Data:) {Integer}    status                          Trạng thái của Survey
@apiSuccess (Data:) {String}     survey_draft_id                 <code>Id</code> bản nháp của survey đó, nếu không có thì là rỗng 
@apiSuccess (Data:) {String}     public_url                      Link published Survey
@apiSuccess (Data:) {String}     thumbnail                       Link ảnh thumbnail Survey
@apiSuccess (Data:) {Boolean}    has_reported                    Survey này đã có báo cáo hay chưa <li><code>true: </code>đã báo cáo</li>  <li><code> false:</code>  chưa báo cáo</li>
@apiSuccess (Data:) {Datetime}   created_time                    Ngày tạo thông tin Survey
@apiSuccess (Data:) {Datetime}   updated_time                    Ngày cuối cùng cập nhật thông tin Survey


@apiParamExample {json} Body:
    { 
        "search": "Bản khảo sát 1"
    }


@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
{
    "code": 200,
    "data":
        [
              {
                "id": "19021128-a322-4b08-92db-a2c96a998w39",
                "title": "Khảo sát về cách đặt tên thương hiệu",
                "header": {
                    "use_header": 0,
                    "url_image": ""
                },
                "status": 1,
                "survey_draft_id": "19021128-a322-4b08-92db-h2d91j220n37",
                "public_url": "htts://mobio.survey.vn/forms/d/@dcW2&hM",
                "thumbnail": "https://mobio.img.vn/img/thumb/i38hFe#FEWFj",
                "has_reported": true,
                "created_time": "2021-03-08T03:24:08Z",
                "updated_time": "2021-03-08T03:24:08Z"
              },
              {
                "id": "355be00c-b103-475c-aadf-9cfbb796be49",
                "title": "Khảo sát về nhà mạng đang sử dụng",
                "header": {
                    "use_header": 0,
                    "url_image": ""
                },
                "status": 1,
                "survey_draft_id": "",
                "public_url": "htts://mobio.survey.vn/forms/d/@dcW2&hM",
                "thumbnail": "https://mobio.img.vn/img/thumb/i38hFe#FEWFj",
                "has_reported": false,
                "created_time": "2021-03-08T03:24:08Z",
                "updated_time": "2021-03-08T03:24:08Z"
              },
              ...
        ],
    "lang": "en",
    "message": "request successful!."
}

"""


******************************************  List all surveys with name   ***************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post}          {domain}/survey/api/v1.0/surveys/shortage                                Danh sách theo tên của survey
@apiName list short survey
@apiGroup Survey
@apiDescription                                             API lấy danh sách tên Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging


@apiParam   (Query:) {String}    [statuses=1]                      Lấy theo trạng thái của bản khảo sát. Ví dụ <code>&statuses=1,2</code>  <li><code>1</code>danh sách các mẫu khảo sát đang hoạt động (activate)</li> <li><code>2</code>danh sách các mẫu khảo sát không hoạt động (deaactivate)</li>  <li><code>3</code> danh sách các mẫu khảo sát nháp (draft)</li>
@apiParam   (Query:) {Number}    [page]                            Vị trí page cần lấy dữ liệu. Set <code>&page=-1</code> nếu muốn lấy tất cả dữ liệu.<code>MIN_VALUE=1</code>Example: <code>2</code>Default value: <code>1</code>
@apiParam   (Query:) {Number}    [per_page]                        Số phần tử trên một page. Example: <code>&per_page=5</code>
@apiParam   (Body:) {String}    [search]                          Tìm kiếm theo title. Ví dụ <code>"thương hiệu"</code>



@apiSuccess (Data:) {String}     id                              <code>Id</code> của mỗi Survey
@apiSuccess (Data:) {String}     title                           Tên của Survey

@apiParamExample {json} Body:
    { 
        "search": "Bản khảo sát 1"
    }

@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
{
    "code": 200,
    "data":
        [
              {
                "id": "19021128-a322-4b08-92db-a2c96a998w39",
                "title": "Khảo sát về cách đặt tên thương hiệu",
              },
              {
                "id": "355be00c-b103-475c-aadf-9cfbb796be49",
                "title": "Khảo sát về nhà mạng đang sử dụng",
              },
              ...
        ],
    "lang": "en",
    "message": "request successful!."
}

"""

******************************************  details survey   ***************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************


"""
@api {get}          {domain}/survey/api/v1.0/survey/<id>                               Chi tiết survey
@apiName  survey detail
@apiGroup Survey
@apiDescription                                                API lấy chi tiết một Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:) {String=none, brief_details}    [option=none]  <li>Nếu client không request lên mặc định là <code>none</code> thì sẽ lấy chi tiết toàn bộ thông tin của Survey</li>
                                                                    <li><code>option=brief_details</code> thì lấy chi tiết chỉ bao gồm id, title của Survey</li>

SuccessRequestUpsertSurvey

@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
{
    "code": 200,
    "data": 
    {
        "id": "19021128-a322-4b08-92db-a2c96a998w39",
        "ma_theme" : "Cam",
        "bg_color_theme": "#226ff5",
        "header": {
            "use_header": 0,
            "url_image": ""
        },
        "title": "Khảo sát về cách đặt tên thương hiệu",
        "description": "Description survey 1",
        "status": 1,
        "short_description": "Short description survey 1",
        "pages":
        [
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [
                    {
                        "id": "6e319027-d62d-4f2b-9507-531f75176f52",
                        "type": "block",
                        "description": "Example description block 1",
                        "order": 1
                    },
                    {
                        "id": "e15902df-6733-49af-b4d3-6a53c18d2b68",
                        "type": "block",
                        "description": "Example description block 2",
                        "order": 2
                    }
                ]
            },
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions":
                [
                    {
                        "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                        "type": "dropdown",
                        "title": "Example title dropdown",
                        "order": 1,
                        "is_required": false,
                        "view": "vertical",
                        "answers":
                        [
                            {
                                "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                                "value": "Example answer dropdown 1",
                                "order": 1
                            },
                            {
                                "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                                "value": "Example answer dropdown 2",
                                "order": 2
                            }
                        ]
                    }
                ]
            },
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [{
                    "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                    "type": "multiple_choices",
                    "title": "Example title multiple choices",
                    "order": 1,
                    "is_required": false,
                    "view": "vertical",
                    "answers":[{
                        "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                        "value": "Example answer multiple choices 1",
                        "order": 1
                    },
                    {
                        "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                        "value": "Example answer multiple choices 1",
                        "order": 2
                    }]
                }
                ]
            },
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [{
                    "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                    "type": "single_choice",
                    "title": "Example title single choice",
                    "order": 1,
                    "is_required": false,
                    "view": "vertical",
                    "answers":[{
                        "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                        "value": "Example answer single choice 1",
                        "order": 1
                    },
                    {
                        "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                        "value": "Example answer single choice 1",
                        "order": 2
                    }]
                }
                ]
            },
            {
                "id": "16e308de-b939-44e4-bec7-6b8110091929",
                "title": "Page 1",
                "description": "Description page 1",
                "order": 1,
                "questions": [
                    {
                        "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                        "type": "paragraph",
                        "title": "Example title paragraph",
                        "order": 1,
                        "is_required": false,
                        "view": "vertical",
                        "answers": []
                    }
                ]
            },
            ...
        ]
    },
    "lang": "en",
    "message": "request successful!."
}

"""
******************************************  count survey   *****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************

"""
@api {post}          {domain}/survey/api/v1.0/surveys/count                                       Tổng Số lượng các survey tương ứng với trạng thái
@apiName  survey count
@apiGroup Survey
@apiDescription                                                           API lấy số lượng Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String}  [search]                      Từ khoá tìm kiếm theo tên.

@apiSuccess (Data) {String}     status                          Trạng thái Survey <li><code>1:</code>activate</li> <li><code>2:</code>deactivate</li>  <li><code>3:</code>draft</li>
@apiSuccess (Data) {String}     total                           Tổng số Survey của từng trạng thái

@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
{
    "code": 200,
    "data":
        [
            {
                "status": 1,
                "total": 10
            },
            {
                "status": 2,
                "total": 13
            },
            {
                "status": 3,
                "total": 8
            }
        ]
    "lang": "en",
    "message": "request successful!."
}

"""

******************************************  rename survey   ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************

"""
@api {put}          {domain}/survey/api/v1.0/survey/<id>/actions/rename                            Cập nhật tên của Survey
@apiName rename survey
@apiGroup Survey 
@apiDescription                                                API cập nhật tên của Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:) {String} title                                Tên của Survey

@apiSuccess (Data:) {String} id                                 <code>ID</code> của Survey
@apiSuccess (Data:) {String} title                              Tiêu đề của Survey
@apiSuccess (Data:) {String} updated_time                       Thời gian cập nhật của Survey
@apiSuccess (Data:) {String} session_commit                     Phiên thao tác với cơ sở diệu

@apiParamExample {json} Body:
    { 
        "title": "Bản khảo sát 1"
    }
    
@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
    {
        "code": 200,
        "data": {
            "id": "485fefef-45ac-4a82-b16d-5fc003c5b12a",
            "title": "Bản khảo sát 1",
            "updated_time": "2021-03-24T08:48:08Z",
            "session_commit": "123871231kjmaskhdajksdas"
        },
        "lang": "vi",
        "message": "request thành công."
    }
"""

******************************************  Delete survey   ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************


"""
@api {delete}          {domain}/survey/api/v1.0/surveys                        Delete Survey
@apiName delete survey
@apiGroup Survey 
@apiDescription                                             API cập nhật xoá Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:) {String} ids                                 Danh sách <code>Id</code> của survey cần xóa

@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "code": 200,
        "message": "request successful!.",
        "lang": "en"
     }
"""


******************************************  Create survey   ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************


"""
@api {post}          {domain}/survey/api/v1.0/survey                               Tạo mới Survey (tạo survey từ tiêu đề)
@apiName create survey
@apiGroup Survey
@apiDescription                                             API tạo mới Survey từ tiêu đề.
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:) {String} title                               Tiêu đề của Survey


@apiParamExample {Json} Body:
    {
        "title" : "Khảo sát sự hài lòng của nhân viên công ty mobio"
    }
    
@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "code": 200,
        "data":
            {
                "id": "19021128-a322-4b08-92db-a2c96a982e36",
                "created_time": "2021-03-08T03:24:08Z",
                "session_commit": "123981283nmjasdasjkdas"
            },
        "message": "request successful!",
        "lang": "en"
     }

"""

******************************************  save draft  ********************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************


"""
@api {put}          {domain}/survey/api/v1.0/surveys/<id>/actions/save_draft         Lưu nháp thông tin Survey
@apiName Save draft survey
@apiGroup Survey
@apiDescription                                                             API lưu nháp thông tin Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang




@apiUse ParamExampleQuestionBlock

@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
    {
        "code": 200,
        "lang": "vi",
        "message": "Lưu nháp thành công!",
        "data": {
            "session_commit": "ca693c99-156e-4072-9da8-cde9980300ec",
            "status_update_survey": true,
            "title": "Title survey",
            "status": 1,
            "updated_time": "2021-04-09T15:11:43Z"
        }   
    }

"""

"""
@api {put}          {domain}/survey/api/v1.0/surveys/<id>/actions/save_draft_v2         Lưu nháp thông tin Survey V2
@apiName Save draft survey V2
@apiGroup Survey
@apiDescription                                                             API lưu nháp thông tin Survey V2
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiUse ParamRequestUpsertSurvey

@apiSuccess (Data:) {String} session_commit                     Phiên thực hiện thao tác
@apiSuccess (Data:) {Boolean} status_update_survey               Trạng thái update survey
@apiSuccess (Data:) {String} title                              Tiêu đề của survey
@apiSuccess (Data:) {String} status                             Trạng thái của Survey
@apiSuccess (Data:) {String} updated_time                       Thời gian cập nhật mới nhất của survey

@apiUse ParamExampleQuestionBlock

@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
    {
        "code": 200,
        "lang": "vi",
        "message": "Lưu nháp thành công!",
        "data": {
            "session_commit": "ca693c99-156e-4072-9da8-cde9980300ec",
            "status_update_survey": true,
            "title": "Title survey",
            "status": 1,
            "updated_time": "2021-04-09T15:11:43Z"
        }   
    }
"""

"""
@api {put}          {domain}/survey/api/v1.0/surveys/<id>/actions/finish         Hoàn thành Survey
@apiName Finish survey
@apiGroup Survey
@apiDescription                                                             API hoàn thành Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiUse ParamRequestUpsertSurvey

@apiUse ParamExampleQuestionBlock
    
@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
{
    "code": 200,
    "data": {
        "created_time": "2021-04-09T15:11:17Z",
        "id": "606bdef821e56945d1e1855f",
        "ma_theme": "009cdb",
        "bg_color_theme": "#226ff5",
        "public_url": "https://mobio.vn/survey/public_url/c9cab5abdc",
        "session_commit": "ca693c99-156e-4072-9da8-cde9980300ec",
        "status": 1,
        "survey_draft_id": "",
        "thumbnail": "https://api-test1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/6066ce10bc2e4c89d4b2c43d.png",
        "title": "132",
        "updated_time": "2021-04-09T15:11:43Z",
        "header": {
            "use_header": 0,
            "url_image": ""
        },
    },
    "lang": "vi",
    "message": "Hoàn tất thành công!",
    "session_commit": "ca693c99-156e-4072-9da8-cde9980300ec"
}
"""

"""
@api {put}          {domain}/survey/api/v1.0/survey/<id>/actions/finish_v2         Hoàn thành Survey V2
@apiName Finish survey V2
@apiGroup Survey
@apiDescription                                                             API hoàn thành Survey V2
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiUse ParamRequestUpsertSurvey

@apiUse ParamExampleQuestionBlock

@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
{
    "code": 200,
    "data": {
        "created_time": "2021-04-09T15:11:17Z",
        "id": "606bdef821e56945d1e1855f",
        "ma_theme": "009cdb",
        "bg_color_theme": "#226ff5",
        "public_url": "https://mobio.vn/survey/public_url/c9cab5abdc",
        "session_commit": "ca693c99-156e-4072-9da8-cde9980300ec",
        "status": 1,
        "survey_draft_id": "",
        "thumbnail": "https://api-test1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/6066ce10bc2e4c89d4b2c43d.png",
        "title": "132",
        "updated_time": "2021-04-09T15:11:43Z"
    },
    "lang": "vi",
    "message": "Hoàn tất thành công!",
    "session_commit": "ca693c99-156e-4072-9da8-cde9980300ec"
}
"""

"""
@api {put}          {domain}/survey/api/v1.0/survey/<id>/actions/finish/deactive        Hoàn thành Survey nhưng không xuất bản
@apiName Finish survey dont export
@apiGroup Survey
@apiDescription                                                             API hoàn thành Survey nhưng không xuất bản
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiUse ParamRequestUpsertSurvey

@apiUse ParamExampleQuestionBlock

@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
{
    "code": 200,
    "data": {
        "created_time": "2021-04-09T15:11:17Z",
        "id": "606bdef821e56945d1e1855f",
        "ma_theme": "009cdb",
        "bg_color_theme": "#226ff5",
        "public_url": "https://mobio.vn/survey/public_url/c9cab5abdc",
        "session_commit": "ca693c99-156e-4072-9da8-cde9980300ec",
        "status": 2,
        "survey_draft_id": "",
        "thumbnail": "https://api-test1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/6066ce10bc2e4c89d4b2c43d.png",
        "title": "132",
        "updated_time": "2021-04-09T15:11:43Z",
        "header": {
            "use_header": 0,
            "url_image": ""
        },
    },
    "lang": "vi",
    "message": "Hoàn tất thành công!",
    "session_commit": "ca693c99-156e-4072-9da8-cde9980300ec"
}
"""


********************************** Save config survey  *********************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************



"""
@api {post}          {domain}/survey/api/v1.0/surveys/<survey_id>/actions/setting       Lưu cấu hình Survey(hỗ trợ FE v2)
@apiName save_customer_config 
@apiGroup Survey
@apiDescription                                             API lưu cấu hình Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:) {Boolean} require_name                                       Người trả lời khảo sát có bắt buộc nhập tên hay k? <li><code>True: </code> bắt buộc</li><li><code>False: </code> không bắt buộc</li> <li><code>Default: </code> False</li>
@apiParam (Body:) {Object}   limit                                             Cài đặt trước khi trả lời
@apiParam (Body:) {Integer}   limit.number                                     Số lần trả lời <li><code>default: </code> 1 </li>
@apiParam (Body:) {Boolean} limit.checked                                      Nếu <code>true: </code> chỉ trả lời duy nhất một lần và bắt buộc nhập phone hoặc email  
@apiParam (Body:) {String}  limit.type                                         Số điện hoại hoặc email. <li><code>email: </code>nhập email</li><li><code>phone: </code> nhập số điện thoại</li>
@apiParam (Body:) {Object}  config_end                                               Cài đặt kết thúc
@apiParam (Body:) {Datetime}  config_end.deadline                                    Thời gian kết thúc 
@apiParam (Body:) {String}  config_end.email                                         Email người nhận

@apiParamExample   {json}  Body:
    { 
        "require_name": true,
        "limit": {
            "number": 1,
            "checked": true,
            "type": "phone"
        },
        "config_end": {
            "deadline": "2021-03-08T03:24:08Z",
            "email": "<EMAIL>"
        }
    }


@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "code": 200,
        "message": "request successful!.",
        "lang": "en"
     }

"""

********************************** Save config survey sp FE v3 *********************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************



"""
@api {post}          {domain}/survey/api/v1.0/surveys/<survey_id>/actions/setting_config       Lưu cấu hình Survey(hỗ trợ front end v3)
 
@apiName save_customer_config_sp_fe_v3
@apiGroup Survey
@apiDescription                                             API lưu cấu hình Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
 
@apiParam (Body:) {String}  key                                    4 key: require_name, limit, time, email 
@apiParam (Body:) {Integer}  status                                Trạng thái 1:on, 0:off của survey  
@apiParam (Body:) {Datetime}  value                                nếu key la time<li><code>datetime: </code> Thoi gian ket thuc</li> nếu key: email, update value -> values<li><code>array[]:</code> Danh sach email nhan</li> nếu key la require_name<li><code>Boolean: </code> Người trả lời khảo sát có bắt buộc nhập tên hay k?(True: bắt buộc, False: không bắt buộc, default: false)</li>

@apiParamExample   {json}  Body:
    [
        
        {
          "key": "time",
          "status": 1,
          "value": "" // ngay thang
        },
        {
          "key": "require_name",
          "status": 1,
          "value": "" // default rong
        },
        {
          "key": "limit",
          "status": 1,
          "value": "" // phone la sdt, email 
        },
        {
          "key": "email",
          "status": 1, // 1:on 0: off,
          "values": [] // danh sach email
        }
    ]


@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "code": 200,
        "message": "request successful!.",
        "lang": "en"
     }

"""


**********************************  Get config of customer to survey  ******************************
* version: 1.0.0                                                                                   *
****************************************************************************************************



"""
@api {get}          {domain}/survey/api/v1.0/surveys/<survey_id>/actions/setting        Lấy cấu hình Survey
@apiName get_customer_config 
@apiGroup Survey
@apiDescription                                             API lấy cấu hình Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess (setting) {Boolean} require_name                                       Người trả lời khảo sát có bắt buộc nhập tên hay k? <li><code>True: </code> bắt buộc</li><li><code>False: </code> không bắt buộc</li> <li><code>Default: </code> False</li>
@apiSuccess (setting) {Object}   limit                                             Cài đặt trước khi trả lời
@apiSuccess (setting) {Integer}   limit.number                                     Số lần trả lời <li><code>default: </code> 1 </li>
@apiSuccess (setting) {Boolean} limit.checked                                      Nếu <code>true: </code> chỉ trả lời duy nhất một lần và bắt buộc nhập phone hoặc email  
@apiSuccess (setting) {String}  limit.type                                         Số điện hoại hoặc email. <li><code>email: </code>nhập email</li><li><code>phone: </code> nhập số điện thoại</li>
@apiSuccess (setting) {Object}  config_end                                         Cài đặt kết thúc
@apiSuccess (setting) {Datetime}  config_end.deadline                              Thời gian kết thúc 
@apiSuccess (setting) {String}  config_end.email                                   Email người nhận

@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "code": 200,
        "data": 
            {
                "setting": {
                    "require_name": true,
                    "limit": {
                        "number": 1,
                        "checked": false,
                        "type": "phone"
                    },
                    "config_end": {
                        "deadline": "2021-03-08T03:24:08Z",
                        "email": "<EMAIL>"
                    }
                }
            },
        "lang": "en",
        "message": "request successful!."
     }

"""

**********************************  Cancel draft survey  ******************************
* version: 1.0.0                                                                                   *
****************************************************************************************************



"""
@api {delete}          {domain}/survey/api/v1.0/surveys/<survey_id>/actions/cancel_draft        Huỷ bỏ bản nháp của Survey
@apiName cancel_draft 
@apiGroup Survey
@apiDescription                                             API huỷ bỏ bản nháp của Survey đang hoạt động
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse SuccessRequestUpsertSurvey

@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "created_time": "2021-04-02T10:02:44Z",
        "has_reported": false,
        "id": "6066895419f784fc7da729c8",
        "header": {
            "use_header": 0,
            "url_image": ""
        },
        "pages": [
            {
                "description": "description 2",
                "id": "80f1f5bf-aaaa-4906-a496-307670f41f05",
                "questions": [
                    {
                        "answers": [
                            {
                                "disableTypeCheck": true,
                                "order": 1,
                                "value": "da1"
                            },
                            {
                                "disableTypeCheck": true,
                                "id": "8305c12b-c2be-4db3-9984-c7e881adf23b",
                                "order": 2,
                                "value": "da2"
                            },
                            {
                                "disableTypeCheck": true,
                                "id": "5fab146b-5bd2-4a18-a9c8-9ae737855b50",
                                "order": 3,
                                "value": "da3"
                            }
                        ],
                        "id": "c1839b47-0733-4803-9f5f-72ea6fd31bb6",
                        "is_required": false,
                        "title": "c1",
                        "type": "dropdown",
                        "view": "vertical"
                    }
                ],
                "title": "trang 1"
            }
        ],
        "session_commit": "348dc4f3-4345-49fb-9f56-38bb92f89e53",
        "survey_draft_id": "6068151d5b764fbdbd65f95f",
        "title": "Hí Survey",
        "updated_time": "2021-04-02T10:02:44Z"
     }

"""









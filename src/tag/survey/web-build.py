#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 27/06/2022
"""
"""
@api {GET}          {domain}/survey/api/v1.0/web-build        L<PERSON>y thông tin chi tiết survey từ web-build
@apiName  Survey Detail By Web Build
@apiGroup Survey
@apiDescription                                                Lấy thông tin chi tiết survey từ web-build
@apiVersion 1.0.0

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:) {String}    token                          Token được cấp dùng đề lấy thông tin survey
                                                  

@apiSuccess (Data:) {String} id                                  <code>Id</code> của Survey
@apiSuccess (Data:) {String} ma_theme                            Mã theme của Survey
@apiSuccess (Data:) {String} bg_color_theme                      Mã theme của Survey
@apiSuccess (Data:) {String} title                               Tên của Survey
@apiSuccess (Data:) {Object} setting                             Cài đặt Survey
@apiSuccess (Data:) {Array[]} body_data                           Dữ liệu survey
@apiSuccess (Data:) {String} body_data.id                            Id của trang
@apiSuccess (Data:) {String} body_data.title                         Tên của trang
@apiSuccess (Data:) {String} body_data.description                   Mô tả của trang
@apiSuccess (Data:) {Int} body_data.order                            Vị trí của trang
@apiSuccess (Data:) {Array[]} body_data.questions                    Thông tin chung của câu hỏi
@apiSuccess (Data:) {String} body_data.questions.id                  Id của câu hỏi
@apiSuccess (Data:) {String} body_data.questions.type                Loại câu hỏi. Các loại câu hỏi: <li><code>block </code></li> với type dạng này sẽ được định nghĩa là khối. <li><code>dropdown </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời thả xuống. <li><code>multiple_choices </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có nhiều lựa chọn trả lời. <li><code>date </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dạng ngày tháng năm. <li><code>single_choice </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có 01 đáp án. <li><code>short_answers </code></li> với type dạng này sẽ được định nghĩa là câu trả lời ngắn. <li><code>paragraph </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dài. <li><code>slider </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời là phạm vi đánh giá.
@apiSuccess (Data:) {String} body_data.questions.title               Tên của câu hỏi hoặc khối.
@apiSuccess (Data:) {String} [body_data.questions.description]         Mô tả của câu hỏi hoặc khối.
@apiSuccess (Data:) {String} body_data.questions.order               Vị trí của câu hỏi hoặc khối.
@apiSuccess (Data:) {Boolean} body_data.questions.is_required        Câu hỏi bắt buộc phải trả lời. <code>Chỉ áp dụng cho câu hỏi.</code>
@apiSuccess (Data:) {String} body_data.questions.view                Dạng xem của đáp án. <li><code>vertical: </code></li> các câu trả lời hiện theo chiều dọc. <li><code>horizontal: </code></li> các câu trả lời hiện theo chiều ngang.
@apiSuccess (Data:) {Array[]} [body_data.questions.answers]            Danh sách đáp án.
@apiSuccess (Data:) {string} [body_data.questions.answers.id]          <code>Id</code> của đáp án
@apiSuccess (Data:) {string} [body_data.questions.answers.value]       Nội dung của đáp án.
@apiSuccess (Data:) {Int} [body_data.questions.answers.order]          Vị trí của đáp án.
@apiSuccess (Data:) {Object[]} [body_data.questions.slider]              Các thuộc tính của câu hỏi dạng <code>phạm vi đánh giá</code>.
@apiSuccess (Data:) {Int} [body_data.questions.slider.min_value]       Giá trị nhỏ nhất.
@apiSuccess (Data:) {String} [body_data.questions.slider.min_title]    Title của giá trị nhỏ nhất.
@apiSuccess (Data:) {Int} [body_data.questions.slider.max_value]       Giá trị lớn nhất.
@apiSuccess (Data:) {Int} [body_data.questions.slider.max_title]       Title của giá trị lớn nhất

@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
{
   "code":200,
   "data":{
      "id":"19021128-a322-4b08-92db-a2c96a998w39",
      "ma_theme":"Cam",
      "bg_color_theme": "#226ff5",
      "title":"Khảo sát về cách đặt tên thương hiệu",
      "setting":[
         {
            "key":"time",
            "status":0,
            "value":""
         },
         {
            "key":"require_name",
            "status":0,
            "value":""
         },
         {
            "key":"limit",
            "status":1,
            "value":"email"
         },
         {
            "key":"email",
            "status":0,
            "values":[
               
            ]
         }
      ],
      "body_data":[
         {
            "id":"16e308de-b939-44e4-bec7-6b8110091929",
            "title":"Page 1",
            "description":"Description page 1",
            "order":1,
            "questions":[
               {
                  "id":"6e319027-d62d-4f2b-9507-531f75176f52",
                  "type":"block",
                  "description":"Example description block 1",
                  "order":1
               },
               {
                  "id":"e15902df-6733-49af-b4d3-6a53c18d2b68",
                  "type":"block",
                  "description":"Example description block 2",
                  "order":2
               }
            ]
         },
         {
            "id":"16e308de-b939-44e4-bec7-6b8110091929",
            "title":"Page 1",
            "description":"Description page 1",
            "order":1,
            "questions":[
               {
                  "id":"067bd4a1-b718-4ede-825c-19cab5abdcbd",
                  "type":"dropdown",
                  "title":"Example title dropdown",
                  "order":1,
                  "is_required":false,
                  "view":"vertical",
                  "answers":[
                     {
                        "id":"5df73049-121c-483f-90e8-f21eb2e9607d",
                        "value":"Example answer dropdown 1",
                        "order":1
                     },
                     {
                        "id":"272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                        "value":"Example answer dropdown 2",
                        "order":2
                     }
                  ]
               }
            ]
         },
         {
            "id":"16e308de-b939-44e4-bec7-6b8110091929",
            "title":"Page 1",
            "description":"Description page 1",
            "order":1,
            "questions":[
               {
                  "id":"067bd4a1-b718-4ede-825c-19cab5abdcbd",
                  "type":"multiple_choices",
                  "title":"Example title multiple choices",
                  "order":1,
                  "is_required":false,
                  "view":"vertical",
                  "answers":[
                     {
                        "id":"5df73049-121c-483f-90e8-f21eb2e9607d",
                        "value":"Example answer multiple choices 1",
                        "order":1
                     },
                     {
                        "id":"272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                        "value":"Example answer multiple choices 1",
                        "order":2
                     }
                  ]
               }
            ]
         },
         {
            "id":"16e308de-b939-44e4-bec7-6b8110091929",
            "title":"Page 1",
            "description":"Description page 1",
            "order":1,
            "questions":[
               {
                  "id":"067bd4a1-b718-4ede-825c-19cab5abdcbd",
                  "type":"single_choice",
                  "title":"Example title single choice",
                  "order":1,
                  "is_required":false,
                  "view":"vertical",
                  "answers":[
                     {
                        "id":"5df73049-121c-483f-90e8-f21eb2e9607d",
                        "value":"Example answer single choice 1",
                        "order":1
                     },
                     {
                        "id":"272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                        "value":"Example answer single choice 1",
                        "order":2
                     }
                  ]
               }
            ]
         },
         {
            "id":"16e308de-b939-44e4-bec7-6b8110091929",
            "title":"Page 1",
            "description":"Description page 1",
            "order":1,
            "questions":[
               {
                  "id":"067bd4a1-b718-4ede-825c-19cab5abdcbd",
                  "type":"paragraph",
                  "title":"Example title paragraph",
                  "order":1,
                  "is_required":false,
                  "view":"vertical",
                  "answers":[
                     
                  ]
               }
            ]
         },
         "..."
      ]
   },
   "lang":"en",
   "message":"request successful!."
}

"""

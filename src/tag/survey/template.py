******************************************  List all template   ************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************

"""
@api {get}          {domain}/survey/api/v1.0/templates                              Danh sách toàn bộ survey mẫu
@apiName template of survey
@apiGroup Template
@apiDescription                                             API lấy danh sách Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse paging
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:) {Number}    [page]                            Vị trí page cần lấy dữ liệu. Set <code>page=-1</code> nếu muốn lấy tất cả dữ liệu.<code>MIN_VALUE=1</code>Example: <code>&page=2</code>Default value: <code>1</code>
@apiParam   (Query:) {Number}    [per_page]                        Số phần tử trên một page. Example: <code>&per_page=15</code>

@apiSuccess (data) {String}     id                              <code>Id</code> của mỗi Survey
@apiSuccess (data) {String}     title                           Tên của Survey
@apiSuccess (data) {String}     thumbnail                       Link ảnh thumbnail Survey
@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
{
    "code": 200,
    "data":
        [
              {
                "id": "19021128-a322-4b08-92db-a2c96a998w39",
                "title": "Khảo sát về cách đặt tên thương hiệu",
                "thumbnail": "https://mobio.img.vn/img/thumb/i38hFe#FEWFj"
              },
              {
                "id": "355be00c-b103-475c-aadf-9cfbb796be49",
                "title": "Khảo sát về nhà mạng đang sử dụng",
                "thumbnail": "https://mobio.img.vn/img/thumb/i38hFe#FEIKj"
              },
              ...
        ],
    "lang": "en",
    "message": "request successful!."
}

"""

******************************************  Get detail template survey   **************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get}          {domain}/survey/api/v1.0/templates/<id>                               Chi tiết mẫu survey
@apiName  survey detail template 
@apiGroup Template
@apiDescription                                                API lấy chi tiết mẫu survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiSuccess (Data:) {String} id                                  <code>Id</code> của mẫu Survey
@apiSuccess (Data:) {String} ma_theme                            Mã theme của mẫu Survey
@apiSuccess (Data:) {String} bg_color_theme                      Mã bg color theme của mẫu Survey
@apiSuccess (Data:) {String} title                               Tên của mẫu Survey
@apiSuccess (Data:) {String} short_description                   Mô tả ngắn về mẫu Survey
@apiSuccess (Data:) {String} description                         Mô tả về mẫu Survey
@apiSuccess (Data:) {Int=1, 2, 3} status                         Trạng thái của mẫu Survey. <li><code>1:</code> activate</li><li><code>2:</code> deactivate</li><li><code>3:</code> draft</li>
@apiSuccess (Data:) {Array[]} pages                              Danh sách các page ở trong mẫu Survey
@apiSuccess (Data:) {String} pages.id                            <code>Id</code> của trang
@apiSuccess (Data:) {String} pages.title                         Tên của trang
@apiSuccess (Data:) {String} pages.description                   Mô tả của trang
@apiSuccess (Data:) {Int} pages.order                            Vị trí của trang
@apiSuccess (Data:) {Array[]} pages.questions                    Thông tin chung của question
@apiSuccess (Data:) {String} pages.questions.id                  <code>Id</code> của câu hỏi
@apiSuccess (Data:) {String} pages.questions.type                Loại câu hỏi. Các loại câu hỏi: <li><code>block </code></li> với type dạng này sẽ được định nghĩa là khối. <li><code>dropdown </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời thả xuống. <li><code>multiple_choices </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có nhiều lựa chọn trả lời. <li><code>date </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dạng ngày tháng năm. <li><code>single_choice </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có 01 đáp án. <li><code>short_answers </code></li> với type dạng này sẽ được định nghĩa là câu trả lời ngắn. <li><code>paragraph </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dài. <li><code>slider </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời là phạm vi đánh giá.
@apiSuccess (Data:) {String} pages.questions.title               Tên của câu hỏi hoặc khối.
@apiSuccess (Data:) {String} [pages.questions.description]       Mô tả của câu hỏi hoặc khối.
@apiSuccess (Data:) {String} pages.questions.order               Vị trí của câu hỏi hoặc khối.
@apiSuccess (Data:) {Boolean} pages.questions.is_required        Câu hỏi bắt buộc phải trả lời. <code>Chỉ áp dụng cho câu hỏi.</code>
@apiSuccess (Data:) {String} pages.questions.view                Dạng xem của đáp án. <li><code>vertical: </code></li> các câu trả lời hiện theo chiều dọc. <li><code>horizontal: </code></li> các câu trả lời hiện theo chiều ngang.
@apiSuccess (Data:) {Array[]} [pages.questions.answers]            Danh sách đáp án.
@apiSuccess (Data:) {string} [pages.questions.answers.id]          <code>ID</code> của đáp án
@apiSuccess (Data:) {string} [pages.questions.answers.value]       Nội dung của đáp án.
@apiSuccess (Data:) {Int} [pages.questions.answers.order]          Vị trí của đáp án.
@apiSuccess (Data:) {Object[]} [pages.questions.slider]            Các thuộc tính của câu hỏi dạng <code>phạm vi đánh giá</code>.
@apiSuccess (Data:) {Int} [pages.questions.slider.min_value]       Giá trị nhỏ nhất.
@apiSuccess (Data:) {String} [pages.questions.slider.min_title]    Title của giá trị nhỏ nhất.
@apiSuccess (Data:) {Int} [pages.questions.slider.max_value]       Giá trị lớn nhất.
@apiSuccess (Data:) {Int} [pages.questions.slider.max_title]       Title của giá trị lớn nhất

@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
{
    "code": 200,
    "data":
          {
            "id": "19021128-a322-4b08-92db-a2c96a998w39",
            "ma_theme" : "Cam",
            "bg_color_theme": "#226ff5",
            "title": "Mẫu khảo sát về cách đặt tên thương hiệu",
            "description": "Description template survey 1",
            "header": {
                "use_header": 0,
                "url_image": ""
            },
            
            "status": 1,
            "short_description": "Short description template survey 1",
            "pages":
            [
                {
                    "id": "16e308de-b939-44e4-bec7-6b8110091929",
                    "title": "Page 1",
                    "description": "Description template page 1",
                    "order": 1,
                    "questions": [
                        {
                            "id": "6e319027-d62d-4f2b-9507-531f75176f52",
                            "type": "block",
                            "description": "Example description template block 1",
                            "order": 1
                        },
                        {
                            "id": "e15902df-6733-49af-b4d3-6a53c18d2b68",
                            "type": "block",
                            "description": "Example description template block 2",
                            "order": 2
                        }
                    ]
                },
                {
                    "id": "16e308de-b939-44e4-bec7-6b8110091929",
                    "title": "Page 1",
                    "description": "Description page 1",
                    "order": 1,
                    "questions":
                    [
                        {
                            "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                            "type": "dropdown",
                            "title": "Example title dropdown",
                            "order": 1,
                            "is_required": false,
                            "view": "vertical",
                            "answers":
                            [
                                {
                                    "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                                    "value": "Example answer dropdown 1",
                                    "order": 1
                                },
                                {
                                    "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                                    "value": "Example answer dropdown 2",
                                    "order": 2
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "16e308de-b939-44e4-bec7-6b8110091929",
                    "title": "Page 1",
                    "description": "Description page 1",
                    "order": 1,
                    "questions": [{
                        "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                        "type": "multiple_choices",
                        "title": "Example title multiple choices",
                        "order": 1,
                        "is_required": false,
                        "view": "vertical",
                        "answers":[{
                            "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                            "value": "Example answer multiple choices 1",
                            "order": 1
                        },
                        {
                            "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                            "value": "Example answer multiple choices 1",
                            "order": 2
                        }]
                    }
                    ]
                },
                {
                    "id": "16e308de-b939-44e4-bec7-6b8110091929",
                    "title": "Page 1",
                    "description": "Description page 1",
                    "order": 1,
                    "questions": [{
                        "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                        "type": "single_choice",
                        "title": "Example title single choice",
                        "order": 1,
                        "is_required": false,
                        "view": "vertical",
                        "answers":[{
                            "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                            "value": "Example answer single choice 1",
                            "order": 1
                        },
                        {
                            "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                            "value": "Example answer single choice 1",
                            "order": 2
                        }]
                    }
                    ]
                },
                {
                    "id": "16e308de-b939-44e4-bec7-6b8110091929",
                    "title": "Page 1",
                    "description": "Description page 1",
                    "order": 1,
                    "questions": [
                        {
                            "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                            "type": "paragraph",
                            "title": "Example title paragraph",
                            "order": 1,
                            "is_required": false,
                            "view": "vertical",
                            "answers": []
                        }
                    ]
                },
                ...
            ]
    },
    "lang": "en",
    "message": "request successful!."
}

"""

**********************************  Survey copy template  ******************************
* version: 1.0.0                                                                                   *
****************************************************************************************************


"""
@api {put}          {domain}/survey/api/v1.0/survey/<survey_id>/actions/copy_template       Survey copy từ Template (hỗ trợ Fe v2)
@apiName get_data_template 
@apiGroup Template
@apiDescription                                             Survey copy từ Template
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}  template_id                             Id của bản mẫu cần copy.


@apiSuccess (Data:) {String} id                                  <code>Id</code> của Survey
@apiSuccess (Data:) {String} ma_theme                            Mã theme của Survey
@apiSuccess (Data:) {String} bg_color_theme                      Mã bg color theme của Survey
@apiSuccess (Data:) {String} title                               Tên của Survey
@apiSuccess (Data:) {String} short_description                   Mô tả ngắn về Survey
@apiSuccess (Data:) {String} description                         Mô tả về Survey
@apiSuccess (Data:) {String} survey_draft_id                     <code>Id</code> bản nháp của survey đó, nếu không có thì là rỗng 
@apiSuccess (Data:) {Int=1, 2, 3} status                         Trạng thái của Survey. <li><code>1:</code> activate</li><li><code>2:</code> deactivate</li><li><code>3:</code> draft</li>
@apiSuccess (Data:) {Array[]} pages                              Danh sách các page ở trong Survey
@apiSuccess (Data:) {String} pages.id                            Id của trang
@apiSuccess (Data:) {String} pages.title                         Tên của trang
@apiSuccess (Data:) {String} pages.description                   Mô tả của trang
@apiSuccess (Data:) {Int} pages.order                            Vị trí của trang
@apiSuccess (Data:) {Array[]} pages.questions                    Thông tin chung của câu hỏi
@apiSuccess (Data:) {String} pages.questions.id                  Id của câu hỏi
@apiSuccess (Data:) {String} pages.questions.type                Loại câu hỏi. Các loại câu hỏi: <li><code>block </code></li> với type dạng này sẽ được định nghĩa là khối. <li><code>dropdown </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời thả xuống. <li><code>multiple_choices </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có nhiều lựa chọn trả lời. <li><code>date </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dạng ngày tháng năm. <li><code>single_choice </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có 01 đáp án. <li><code>short_answers </code></li> với type dạng này sẽ được định nghĩa là câu trả lời ngắn. <li><code>paragraph </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dài. <li><code>slider </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời là phạm vi đánh giá.
@apiSuccess (Data:) {String} pages.questions.title               Tên của câu hỏi hoặc khối.
@apiSuccess (Data:) {String} [pages.questions.description]         Mô tả của câu hỏi hoặc khối.
@apiSuccess (Data:) {String} pages.questions.order               Vị trí của câu hỏi hoặc khối.
@apiSuccess (Data:) {Boolean} pages.questions.is_required        Câu hỏi bắt buộc phải trả lời. <code>Chỉ áp dụng cho câu hỏi.</code>
@apiSuccess (Data:) {String} pages.questions.view                Dạng xem của đáp án. <li><code>vertical: </code></li> các câu trả lời hiện theo chiều dọc. <li><code>horizontal: </code></li> các câu trả lời hiện theo chiều ngang.
@apiSuccess (Data:) {Array[]} [pages.questions.answers]            Danh sách đáp án.
@apiSuccess (Data:) {string} [pages.questions.answers.id]          <code>Id</code> của đáp án
@apiSuccess (Data:) {string} [pages.questions.answers.value]       Nội dung của đáp án.
@apiSuccess (Data:) {Int} [pages.questions.answers.order]          Vị trí của đáp án.
@apiSuccess (Data:) {Object[]} [pages.questions.slider]              Các thuộc tính của câu hỏi dạng <code>phạm vi đánh giá</code>.
@apiSuccess (Data:) {Int} [pages.questions.slider.min_value]       Giá trị nhỏ nhất.
@apiSuccess (Data:) {String} [pages.questions.slider.min_title]    Title của giá trị nhỏ nhất.
@apiSuccess (Data:) {Int} [pages.questions.slider.max_value]       Giá trị lớn nhất.
@apiSuccess (Data:) {Int} [pages.questions.slider.max_title]       Title của giá trị lớn nhất

@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "code": 200,
        "data": 
            {
                "id": "19021128-a322-4b08-92db-a2c96a998w39",
                "ma_theme" : "Cam",
                "bg_color_theme": "#226ff5",
                "title": "Mẫu khảo sát về cách đặt tên thương hiệu",
                "description": "Description template survey 1",
                "status": 1,
                "header": {
                    "use_header": 0,
                    "url_image": ""
                },
                "short_description": "Short description template survey 1",
                "pages":
                [
                    {
                        "id": "16e308de-b939-44e4-bec7-6b8110091929",
                        "title": "Page 1",
                        "description": "Description template page 1",
                        "order": 1,
                        "questions": [
                            {
                                "id": "6e319027-d62d-4f2b-9507-531f75176f52",
                                "type": "block",
                                "description": "Example description template block 1",
                                "order": 1
                            },
                            {
                                "id": "e15902df-6733-49af-b4d3-6a53c18d2b68",
                                "type": "block",
                                "description": "Example description template block 2",
                                "order": 2
                            }
                        ]
                    },
                    {
                        "id": "16e308de-b939-44e4-bec7-6b8110091929",
                        "title": "Page 1",
                        "description": "Description page 1",
                        "order": 1,
                        "questions":
                        [
                            {
                                "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                                "type": "dropdown",
                                "title": "Example title dropdown",
                                "order": 1,
                                "is_required": false,
                                "view": "vertical",
                                "answers":
                                [
                                    {
                                        "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                                        "value": "Example answer dropdown 1",
                                        "order": 1
                                    },
                                    {
                                        "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                                        "value": "Example answer dropdown 2",
                                        "order": 2
                                    }
                                ]
                            }
                            ]
                        },
                        {
                            "id": "16e308de-b939-44e4-bec7-6b8110091929",
                            "title": "Page 1",
                            "description": "Description page 1",
                            "order": 1,
                            "questions": [{
                                "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                                "type": "multiple_choices",
                                "title": "Example title multiple choices",
                                "order": 1,
                                "is_required": false,
                                "view": "vertical",
                                "answers":[{
                                    "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                                    "value": "Example answer multiple choices 1",
                                    "order": 1
                                },
                                {
                                    "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                                    "value": "Example answer multiple choices 1",
                                    "order": 2
                                }]
                            }
                            ]
                        },
                        {
                            "id": "16e308de-b939-44e4-bec7-6b8110091929",
                            "title": "Page 1",
                            "description": "Description page 1",
                            "order": 1,
                            "questions": [{
                                "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                                "type": "single_choice",
                                "title": "Example title single choice",
                                "order": 1,
                                "is_required": false,
                                "view": "vertical",
                                "answers":[{
                                    "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                                    "value": "Example answer single choice 1",
                                    "order": 1
                                },
                                {
                                    "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                                    "value": "Example answer single choice 1",
                                    "order": 2
                                }]
                            }
                            ]
                        },
                        {
                            "id": "16e308de-b939-44e4-bec7-6b8110091929",
                            "title": "Page 1",
                            "description": "Description page 1",
                            "order": 1,
                            "questions": [
                                {
                                    "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                                    "type": "paragraph",
                                    "title": "Example title paragraph",
                                    "order": 1,
                                    "is_required": false,
                                    "view": "vertical",
                                    "answers": []
                                }
                            ]
                        },
                        ...
                    ]
            },
        "lang": "en",
        "message": "request successful!."
     }

"""

**********************************  Survey copy template sp Fe v3 ******************************
* version: 1.0.0                                                                                   *
****************************************************************************************************


"""
@api {post}          {domain}/survey/api/v1.0/survey/actions/copy_template_update       Survey copy từ Template (hỗ trợ FE v3)
@apiName get_data_template_sp_fe_v3
@apiGroup Template
@apiDescription                                             Survey copy từ Template
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:) {String}   title                                        Tên của Survey
@apiParam (Body:) {String}   [template_id]                                  Id của bản mẫu cần copy.

@apiParam (Body:) {Array[]} pages                              Danh sách các page ở trong Survey
@apiParam (Body:) {String} pages.id                            Id của trang
@apiParam (Body:) {String} pages.title                         Tên của trang
@apiParam (Body:) {String} pages.description                   Mô tả của trang
@apiParam (Body:) {Int} pages.order                            Vị trí của trang
@apiParam (Body:) {Array[]} pages.questions                    Thông tin chung của câu hỏi
@apiParam (Body:) {String} pages.questions.id                  Id của câu hỏi
@apiParam (Body:) {String} pages.questions.type                Loại câu hỏi. Các loại câu hỏi: <li><code>block </code></li> với type dạng này sẽ được định nghĩa là khối. <li><code>dropdown </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời thả xuống. <li><code>multiple_choices </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có nhiều lựa chọn trả lời. <li><code>date </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dạng ngày tháng năm. <li><code>single_choice </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có 01 đáp án. <li><code>short_answers </code></li> với type dạng này sẽ được định nghĩa là câu trả lời ngắn. <li><code>paragraph </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dài. <li><code>slider </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời là phạm vi đánh giá.
@apiParam (Body:) {String} pages.questions.title               Tên của câu hỏi hoặc khối.
@apiParam (Body:) {String} [pages.questions.description]         Mô tả của câu hỏi hoặc khối.
@apiParam (Body:) {String} pages.questions.order               Vị trí của câu hỏi hoặc khối.
@apiParam (Body:) {Boolean} pages.questions.is_required        Câu hỏi bắt buộc phải trả lời. <code>Chỉ áp dụng cho câu hỏi.</code>
@apiParam (Body:) {String} pages.questions.view                Dạng xem của đáp án. <li><code>vertical: </code></li> các câu trả lời hiện theo chiều dọc. <li><code>horizontal: </code></li> các câu trả lời hiện theo chiều ngang.
@apiParam (Body:) {Array[]} [pages.questions.answers]            Danh sách đáp án.
@apiParam (Body:) {string} [pages.questions.answers.id]          <code>Id</code> của đáp án
@apiParam (Body:) {string} [pages.questions.answers.value]       Nội dung của đáp án.
@apiParam (Body:) {Int} [pages.questions.answers.order]          Vị trí của đáp án.
@apiParam (Body:) {Object[]} [pages.questions.slider]              Các thuộc tính của câu hỏi dạng <code>phạm vi đánh giá</code>.
@apiParam (Body:) {Int} [pages.questions.slider.min_value]       Giá trị nhỏ nhất.
@apiParam (Body:) {String} [pages.questions.slider.min_title]    Title của giá trị nhỏ nhất.
@apiParam (Body:) {Int} [pages.questions.slider.max_value]       Giá trị lớn nhất.
@apiParam (Body:) {Int} [pages.questions.slider.max_title]       Title của giá trị lớn nhất




@apiSuccess (Data:) {String} id                                  <code>Id</code> của Survey
@apiSuccess (Data:) {String} ma_theme                            Mã theme của Survey
@apiSuccess (Data:) {String} bg_color_theme                      Mã bg color theme của Survey
@apiSuccess (Data:) {String} title                               Tên của Survey
@apiSuccess (Data:) {String} short_description                   Mô tả ngắn về Survey
@apiSuccess (Data:) {String} description                         Mô tả về Survey
@apiSuccess (Data:) {String} survey_draft_id                     <code>Id</code> bản nháp của survey đó, nếu không có thì là rỗng 
@apiSuccess (Data:) {Int=1, 2, 3} status                         Trạng thái của Survey. <li><code>1:</code> activate</li><li><code>2:</code> deactivate</li><li><code>3:</code> draft</li>
@apiSuccess (Data:) {String} session_commit                      session_commit
@apiSuccess (Data:) {Array[]} pages                              Danh sách các page ở trong Survey
@apiSuccess (Data:) {String} pages.id                            Id của trang
@apiSuccess (Data:) {String} pages.title                         Tên của trang
@apiSuccess (Data:) {String} pages.description                   Mô tả của trang
@apiSuccess (Data:) {Int} pages.order                            Vị trí của trang
@apiSuccess (Data:) {Array[]} pages.questions                    Thông tin chung của câu hỏi
@apiSuccess (Data:) {String} pages.questions.id                  Id của câu hỏi
@apiSuccess (Data:) {String} pages.questions.type                Loại câu hỏi. Các loại câu hỏi: <li><code>block </code></li> với type dạng này sẽ được định nghĩa là khối. <li><code>dropdown </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời thả xuống. <li><code>multiple_choices </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có nhiều lựa chọn trả lời. <li><code>date </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dạng ngày tháng năm. <li><code>single_choice </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có 01 đáp án. <li><code>short_answers </code></li> với type dạng này sẽ được định nghĩa là câu trả lời ngắn. <li><code>paragraph </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời dài. <li><code>slider </code></li> với type dạng này sẽ được định nghĩa là câu hỏi có câu trả lời là phạm vi đánh giá.
@apiSuccess (Data:) {String} pages.questions.title               Tên của câu hỏi hoặc khối.
@apiSuccess (Data:) {String} [pages.questions.description]         Mô tả của câu hỏi hoặc khối.
@apiSuccess (Data:) {String} pages.questions.order               Vị trí của câu hỏi hoặc khối.
@apiSuccess (Data:) {Boolean} pages.questions.is_required        Câu hỏi bắt buộc phải trả lời. <code>Chỉ áp dụng cho câu hỏi.</code>
@apiSuccess (Data:) {String} pages.questions.view                Dạng xem của đáp án. <li><code>vertical: </code></li> các câu trả lời hiện theo chiều dọc. <li><code>horizontal: </code></li> các câu trả lời hiện theo chiều ngang.
@apiSuccess (Data:) {Array[]} [pages.questions.answers]            Danh sách đáp án.
@apiSuccess (Data:) {string} [pages.questions.answers.id]          <code>Id</code> của đáp án
@apiSuccess (Data:) {string} [pages.questions.answers.value]       Nội dung của đáp án.
@apiSuccess (Data:) {Int} [pages.questions.answers.order]          Vị trí của đáp án.
@apiSuccess (Data:) {Object[]} [pages.questions.slider]              Các thuộc tính của câu hỏi dạng <code>phạm vi đánh giá</code>.
@apiSuccess (Data:) {Int} [pages.questions.slider.min_value]       Giá trị nhỏ nhất.
@apiSuccess (Data:) {String} [pages.questions.slider.min_title]    Title của giá trị nhỏ nhất.
@apiSuccess (Data:) {Int} [pages.questions.slider.max_value]       Giá trị lớn nhất.
@apiSuccess (Data:) {Int} [pages.questions.slider.max_title]       Title của giá trị lớn nhất

@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "code": 200,
        "data": 
            {
                "id": "19021128-a322-4b08-92db-a2c96a998w39",
                "ma_theme" : "Cam",
                "bg_color_theme": "#226ff5",
                "title": "Mẫu khảo sát về cách đặt tên thương hiệu",
                "description": "Description template survey 1",
                "status": 1,
                "header": {
                    "use_header": 0,
                    "url_image": ""
                },
                "session_commit": "123981283nmjasdasjkdas",
                "short_description": "Short description template survey 1",
                "pages":
                [
                    {
                        "id": "16e308de-b939-44e4-bec7-6b8110091929",
                        "title": "Page 1",
                        "description": "Description template page 1",
                        "order": 1,
                        "questions": [
                            {
                                "id": "6e319027-d62d-4f2b-9507-531f75176f52",
                                "type": "block",
                                "description": "Example description template block 1",
                                "order": 1
                            },
                            {
                                "id": "e15902df-6733-49af-b4d3-6a53c18d2b68",
                                "type": "block",
                                "description": "Example description template block 2",
                                "order": 2
                            }
                        ]
                    },
                    {
                        "id": "16e308de-b939-44e4-bec7-6b8110091929",
                        "title": "Page 1",
                        "description": "Description page 1",
                        "order": 1,
                        "questions":
                        [
                            {
                                "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                                "type": "dropdown",
                                "title": "Example title dropdown",
                                "order": 1,
                                "is_required": false,
                                "view": "vertical",
                                "answers":
                                [
                                    {
                                        "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                                        "value": "Example answer dropdown 1",
                                        "order": 1
                                    },
                                    {
                                        "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                                        "value": "Example answer dropdown 2",
                                        "order": 2
                                    }
                                ]
                            }
                            ]
                        },
                        {
                            "id": "16e308de-b939-44e4-bec7-6b8110091929",
                            "title": "Page 1",
                            "description": "Description page 1",
                            "order": 1,
                            "questions": [{
                                "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                                "type": "multiple_choices",
                                "title": "Example title multiple choices",
                                "order": 1,
                                "is_required": false,
                                "view": "vertical",
                                "answers":[{
                                    "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                                    "value": "Example answer multiple choices 1",
                                    "order": 1
                                },
                                {
                                    "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                                    "value": "Example answer multiple choices 1",
                                    "order": 2
                                }]
                            }
                            ]
                        },
                        {
                            "id": "16e308de-b939-44e4-bec7-6b8110091929",
                            "title": "Page 1",
                            "description": "Description page 1",
                            "order": 1,
                            "questions": [{
                                "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                                "type": "single_choice",
                                "title": "Example title single choice",
                                "order": 1,
                                "is_required": false,
                                "view": "vertical",
                                "answers":[{
                                    "id": "5df73049-121c-483f-90e8-f21eb2e9607d",
                                    "value": "Example answer single choice 1",
                                    "order": 1
                                },
                                {
                                    "id": "272de9ae-ffd1-4f39-94c3-1a55a12a234e",
                                    "value": "Example answer single choice 1",
                                    "order": 2
                                }]
                            }
                            ]
                        },
                        {
                            "id": "16e308de-b939-44e4-bec7-6b8110091929",
                            "title": "Page 1",
                            "description": "Description page 1",
                            "order": 1,
                            "questions": [
                                {
                                    "id": "067bd4a1-b718-4ede-825c-19cab5abdcbd",
                                    "type": "paragraph",
                                    "title": "Example title paragraph",
                                    "order": 1,
                                    "is_required": false,
                                    "view": "vertical",
                                    "answers": []
                                }
                            ]
                        },
                        ...
                    ]
            },
        "lang": "en",
        "message": "request successful!."
     }

"""
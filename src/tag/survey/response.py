
**********************************  Feedback of customer to survey  ********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************



"""
@api {post}          {domain}/survey/api/v1.0/surveys/<survey_id>/actions/submit_answer                         Lưu response người trả lời khảo sát
@apiName response trả lời
@apiGroup Response 
@apiDescription                                             API lưu các câu trả lời Survey
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:) {String} title                                            Tiêu đề của bản khảo sát
@apiParam (Body:) {String} [name]                                           Tên của người trả lời khảo sát
@apiParam (Body:) {String} [phone]                                          Số điện thoại người trả lời khảo sát
@apiParam (Body:) {String} [mail]                                           Email của người trả lời khảo sát
@apiParam (Body:) {String} [profile_id]                                     <code>ID</code> profile trên hệ thống Mobio
@apiParam (Body:) {String} [device_id]                                      <code>ID</code> device trên hệ thống Mobio
@apiParam (Body:) {list}   questions                                        Danh sách các câu trả lời tương ứng với  câu hỏi
@apiParam (Body:) {String} questions.question_id                            <code>ID</code> của từng question
@apiParam (Body:) {String} questions.question_tile                          title của question
@apiParam (Body:) {String} questions.question_type                          type của question
@apiParam (Body:) {String} questions.answers                                Danh sách câu trả lời
@apiParam (Body:) {String} [questions.answers.id]                           <code>ID</code> câu trả lời
@apiParam (Body:) {String} [questions.answers.value]                        Value câu trả lời
@apiParam (Body:) {String} [questions.answers.type=text]                         Loại câu trả lời

@apiParamExample   {json}  Body:
    { 
        "title": "Đây là title của Survey",
        "name": "survey",
        "phone": "0123456789",
        "mail": "<EMAIL>",
        "profile_id": "4211de08-fe8d-457a-xxxx-424f64768e76",
        "device_id": "4211de08-fe8d-457a-xxxx-424f64732e76",
        "questions": [
            {
                "id" : "askdaskdaksda19123111",
                "title": "title 1",
                "type": "multiple_choices",
                "answers": [
                    {
                        "id":  "19021128-a322-4b08-92db-a2c96a982k31",
                        "value": "Đáp án 1",
                        "type": "text"
                    },
                    {
                        "id":  "19021128-a322-4b08-92db-a2c96a982k21",
                        "value": "Đáp án 2",
                        "type": "text"
                    }  
                ]
            },
            {
                "id" : "19021128123812381238",
                "title": "title 2",
                "type": "single_choice",
                "answers":
                    {
                        "id":  "19021128-a322-4b08-92db-a2c96a9a33k3",
                        "value": "Đáp án 3",
                        "type": "text"
                    }
            },
            {
                "id" : "19021128-a322-4b08-92db-a2c96a9z2e36",
                "title": "title 3",
                "type": "paragraph",
                "answers": "Đây là câu trả lời đoạn văn"
            },
            {
                "id" : "12adsadsdas-a322-4b08-92db-a2c96a9z2e36",
                "title": "title 4",
                "type": "short_answers",
                "answers": "Đây là câu trả lời ngắn"
            },
            {
                "id" : "12adsadsdas-a322-4b08-92db-a2c96a9z2e5",
                "title": "title 5",
                "type": "date",
                "answers": "2021-03-08T03:24:08Z"
            },
            ...
        ]
    }


@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "code": 200,
        "message": "request successful!.",
        "lang": "en"
     }

"""

**********************************  Feedback of customer to survey V2 ********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************


"""
@api {post}          {domain}/survey/api/v1.0/surveys/<survey_id>/actions/submit_answer_v2                        Lưu response người trả lời khảo sát v2
@apiName response trả lời v2
@apiGroup Response 
@apiDescription                                             API lưu các câu trả lời Survey v2
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:) {String} title                                            Tiêu đề của bản khảo sát
@apiParam (Body:) {String} [name]                                           Tên của người trả lời khảo sát
@apiParam (Body:) {String} [phone]                                          Số điện thoại người trả lời khảo sát
@apiParam (Body:) {String} [mail]                                           Email của người trả lời khảo sát
@apiParam (Body:) {String} [profile_id]                                     <code>ID</code> profile trên hệ thống Mobio
@apiParam (Body:) {String} [device_id]                                      <code>ID</code> device trên hệ thống Mobio
@apiParam (Body:) {list}   questions                                        Danh sách các câu trả lời tương ứng với  câu hỏi
@apiParam (Body:) {String} questions.question_id                            <code>ID</code> của từng question
@apiParam (Body:) {String} questions.question_tile                          title của question
@apiParam (Body:) {String} questions.question_type                          type của question
@apiParam (Body:) {String} questions.answers                                Danh sách câu trả lời
@apiParam (Body:) {String} [questions.answers.id]                           <code>ID</code> câu trả lời
@apiParam (Body:) {String} [questions.answers.value]                        Value câu trả lời
@apiParam (Body:) {String} [questions.answers.row_id]                        Value câu trả lời dạng lưới.
@apiParam (Body:) {String} [questions.answers.value_row]                        Value hàng câu trả lời dạng lưới.
@apiParam (Body:) {String} [questions.answers.column_id]                        Id cột câu trả lời dạng lưới.
@apiParam (Body:) {String} [questions.answers.value_column]                 Value cột câu trả lời dạng lưới.
@apiParam (Body:) {Object[]} [questions.answers.column]                 Danh sách cột câu trả lời dạng lưới.
@apiParam (Body:) {String} [questions.answers.column.id]                  ID cột câu trả lời dạng lưới.
@apiParam (Body:) {String} [questions.answers.column.value]                Value cột câu trả lời dạng lưới.

@apiParamExample   {json}  Body:
    { 
        "title": "Đây là title của Survey",
        "name": "survey",
        "phone": "0123456789",
        "mail": "<EMAIL>",
        "profile_id": "4211de08-fe8d-457a-xxxx-424f64768e76",
        "device_id": "4211de08-fe8d-457a-xxxx-424f64732e76",
        "questions": [
            {
                "id" : "askdaskdaksda19123111",
                "title": "title 1",
                "type": "multiple_choices",
                "answers": [
                    {
                        "id":  "19021128-a322-4b08-92db-a2c96a982k31",
                        "value": "Đáp án 1"
                    },
                    {
                        "id":  "19021128-a322-4b08-92db-a2c96a982k21",
                        "value": "Đáp án 2",
                    }  
                ]
            },
            {
                "id" : "19021128123812381238",
                "title": "title 2",
                "type": "single_choice",
                "answers":
                    {
                        "id":  "19021128-a322-4b08-92db-a2c96a9a33k3",
                        "value": "Đáp án 3",
                    }
            },
            {
                "id" : "19021128-a322-4b08-92db-a2c96a9z2e36",
                "title": "title 3",
                "type": "paragraph",
                "answers": "Đây là câu trả lời đoạn văn"
            },
            {
                "id" : "12adsadsdas-a322-4b08-92db-a2c96a9z2e36",
                "title": "title 4",
                "type": "short_answers",
                "answers": "Đây là câu trả lời ngắn"
            },
            {
                "id" : "12adsadsdas-a322-4b08-92db-a2c96a9z2e5",
                "title": "title 5",
                "type": "date",
                "answers": "2021-03-08T03:24:08Z"
            },
            {
                "id" : "12adsadsdas-a322-4b08-92db-a2c96a9z2e5",
                "title": "title 5",
                "type": "matrix_radio",
                "answers": [
                    {
                        "row_id": "12adsadsdas-a322-4b08-92db-a2c96a9z2e5",
                        "value_row": "",
                        "column_id": "12adsadsdas-a322-4b08-92db-a2c96a9z2e5"
                        "value_column": ""
                    },
                    {
                        "row_id": "12adsadsdas-a322-4b08-92db-a2c96a9z2e5",
                        "value_row": "",
                        "column_id": "12adsadsdas-a322-4b08-92db-a2c96a9z2e5"
                        "value_column": ""
                    }
                ]
            },
            {
                "id" : "12adsadsdas-a322-4b08-92db-a2c96a9z2e5",
                "title": "title 5",
                "type": "matrix_checkbox",
                "answers": [
                    {
                        "row_id": "12adsadsdas-a322-4b08-92db-a2c96a9z2e5",
                        "value_row": "",
                        "column": [
                            {
                                "id": "12adsadsdas-a322-4b08-92db-a2c96a9z2e5"
                                "value": ""
                            },
                            {
                                "id": "12adsadsdas-a322-4b08-92db-a2c96a9z2e5"
                                "value": ""
                            }
                        ]
                    },
                    {
                        "row_id": "12adsadsdas-a322-4b08-92db-a2c96a9z2e5",
                        "value_row": "",
                        "column": [
                            {
                                "id": "12adsadsdas-a322-4b08-92db-a2c96a9z2e5"
                                "value": ""
                            },
                            {
                                "id": "12adsadsdas-a322-4b08-92db-a2c96a9z2e5"
                                "value": ""
                            }
                        ]
                    }
                ]
            },
            {
                "id" : "19021128123812381238",
                "title": "title 2",
                "type": "emoji_rating",
                "answers":
                    {
                        "id":  "19021128-a322-4b08-92db-a2c96a9a33k3",
                        "value": "Đáp án 3",
                    }
            }
            ...
        ]
    }


@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "code": 200,
        "message": "request successful!.",
        "lang": "en"
     }

"""

**********************************  Count view survey  ********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************



"""
@api {put}          {domain}/survey/api/v1.0/surveys/actions/view      Lưu lượt truy cập link survey
@apiName Lưu lượt truy cập link Survey
@apiGroup Response 
@apiDescription                                             API lưu lượt truy cập link Survey
@apiVersion 1.0.0

@apiParam   (Query:) {String}    id                         <code>ID</code> của survey

@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "code": 200,
        "message": "request successful!.",
        "lang": "en"
     }

"""

**********************************  Check setting survey  ********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************

"""
@api {post}          {domain}/survey/api/v1.0/surveys/<survey_id>/limit      Kiểm tra giới hạn  số lần trả lời
@apiName Kiểm tra giới hạn  số lần trả lời
@apiGroup Response 
@apiDescription                                             API Kiểm tra giới hạn  số lần trả lời
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess (Body) {String}      [phone]                           Số điên thoại của người trả lời câu hỏi
@apiSuccess (Body) {String}      [mail]                            Email của người trả lời câu hỏi
@apiSuccess (Body) {String}      [profile_id]                      profile_id của người trả lời câu hỏi
@apiSuccess (Body) {String}     device_id                       Thiết bị sử dụng trả lời survey của người trả lời câu hỏi.

@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "message": "request successful!.",
        "lang": "en",
        "code": 200
     }

"""




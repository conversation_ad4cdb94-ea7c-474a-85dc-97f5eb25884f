******************************************  Reports of survey  ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************

"""
@api {get}          {domain}/survey/api/v1.0/surveys/<survey_id>/report                   Thống kê câu hỏi/câu trả lời của survey
@apiName thống kê
@apiGroup Report 
@apiDescription                                             API danh sách các response của Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:) {String}    start_time                             Thời gian bắt đầu vd: &start_time=2020-10-19T00:00:00.000Z
@apiParam   (Query:) {String}    end_time                               Thời gian kết thúc

@apiSuccess (data) {Int}         total_view                              Số lượt xem survey
@apiSuccess (data) {Int}         total_submit                            Số lượt submit survey
@apiSuccess (data) {array[]}     questions                               Danh sách các câu hỏi trong Survey
@apiSuccess (data) {String}      questions.id                            <code>ID</code> của câu hỏi
@apiSuccess (data) {Integer}     questions.total_response                Tổng số response từ người trả lời của câu hỏi
@apiSuccess (data) {String}     [questions.url_download]                Link download file câu trả lời, nếu câu hỏi dạng text, date
@apiSuccess (data) {array[]}     questions.values                        Đáp án của câu hỏi
@apiSuccess (data) {String}      [questions.values.key]                  Value của đáp án
@apiSuccess (data) {String}      [questions.values.percent]              Phần trăm đáp án được chọn
@apiSuccess (data) {String}      [questions.values.total]                Tổng số lượt chọn đáp án


@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        {
            "code": 200,
            "data": {
                "questions": [
                    {
                        "id": "f00d7f83-0d1b-48fa-9c86-3da681865ae0",
                        "total_response": 6,
                        "values": [
                            {
                                "key": "Đáp án 1",
                                "percent": 100.0,
                                "total": 6
                            }
                        ]
                    },
                    {
                        "id": "a17c9aae-fe4c-47c3-aceb-8ea70d156e44",
                        "total_response": 6,
                        "values": [
                            {
                                "key": "Đáp án multiple choices 2",
                                "percent": 100.0,
                                "total": 6
                            },
                            {
                                "key": "Đáp án multiple choices 3",
                                "percent": 100.0,
                                "total": 6
                            }
                        ]
                    },
                    {
                        "id": "fc377125-22d0-4b5a-9193-19112e81ac0e",
                        "total_response": 6,
                        "values": [
                            {
                                "key": "Đáp án single choice 5",
                                "percent": 100.0,
                                "total": 6
                            }
                        ]
                    },
                    {
                        "id": "034c4d49-3ed2-4301-adfb-96ee6e20770d",
                        "total_response": 6,
                        "url_download": "https://mobio.vn/survey/download/034c4d49-3ed2-4301-adfb-96ee6e20770d",
                        "values": [
                            "2021-03-08T03:24:08Z",
                            "2021-03-08T03:24:08Z",
                            "2021-03-08T03:24:08Z",
                            "2021-03-08T03:24:08Z",
                            "2021-03-08T03:24:08Z",
                            "2021-03-08T03:24:08Z"
                        ]
                    },
                    {
                        "id": "878bf38b-0312-4cf4-8393-9fec0d91b2fc",
                        "total_response": 6,
                        "url_download": "https://mobio.vn/survey/download/878bf38b-0312-4cf4-8393-9fec0d91b2fc",
                        "values": [
                            "Câu trả lời đoạn văn thứ 2",
                            "Câu trả lời đoạn văn thứ 2",
                            "Câu trả lời đoạn văn thứ 2",
                            "Câu trả lời đoạn văn thứ 2",
                            "Câu trả lời đoạn văn thứ 2",
                            "Câu trả lời đoạn văn thứ 2"
                        ]
                    },
                    {
                        "id": "a359d2de-bae4-4897-963e-673d45d65f9ea",
                        "total_response": 6,
                        "url_download": "https://mobio.vn/survey/download/a359d2de-bae4-4897-963e-673d45d65f9ea",
                        "values": [
                            "Câu trả lời đoạn văn ngắn test",
                            "Câu trả lời đoạn văn ngắn test",
                            "Câu trả lời đoạn văn ngắn test",
                            "Câu trả lời đoạn văn ngắn test",
                            "Câu trả lời đoạn văn ngắn test",
                            "Câu trả lời đoạn văn ngắn test"
                        ]
                    },
                    {
                        "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba",
                        "total_response": 6,
                        "values": [
                            {
                                "key": "4",
                                "percent": 100.0,
                                "total": 6
                            }
                        ]
                    }
                ],
                "total_submit": 6,
                "total_view": 33
            },
            "lang": "vi",
            "message": "request thành công."
        }
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** Reports of survey v2** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **

"""
@api {get}          {domain}/survey/api/v1.0/surveys/<survey_id>/report_v2                   Thống kê câu hỏi/câu trả lời của survey V2
@apiName thống kê v2
@apiGroup Report 
@apiDescription                                             API danh sách các response của Survey V2
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:) {String}    start_time                             Thời gian bắt đầu vd: &start_time=2020-10-19T00:00:00.000Z
@apiParam   (Query:) {String}    end_time                               Thời gian kết thúc

@apiSuccess (data) {Int}         total_view                              Số lượt xem survey
@apiSuccess (data) {Int}         total_submit                            Số lượt submit survey
@apiSuccess (data) {array[]}     questions                               Danh sách các câu hỏi trong Survey
@apiSuccess (data) {String}      questions.id                            <code>ID</code> của câu hỏi
@apiSuccess (data) {Integer}     questions.total_response                Tổng số response từ người trả lời của câu hỏi
@apiSuccess (data) {String}     [questions.url_download]                Link download file câu trả lời, nếu câu hỏi dạng text, date
@apiSuccess (data) {array[]}     questions.values                        Đáp án của câu hỏi
@apiSuccess (data) {String}      [questions.values.key]                  Value của đáp án
@apiSuccess (data) {String}      [questions.values.percent]              Phần trăm đáp án được chọn
@apiSuccess (data) {String}      [questions.values.total]                Tổng số lượt chọn đáp án
@apiSuccess (data) {Object[]}      [questions.values.column]               Danh sách các cột.
@apiSuccess (data) {String}      [questions.values.column.value]          Giá trị của cột.
@apiSuccess (data) {String}      [questions.values.column.percent]        Phần trăm đáp án được chọn
@apiSuccess (data) {String}      [questions.values.column.total]          Tổng số lượt chọn đáp án


@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        {
            "code": 200,
            "data": {
                "questions": [
                    {
                        "id": "f00d7f83-0d1b-48fa-9c86-3da681865ae0",
                        "total_response": 6,
                        "values": [
                            {
                                "key": "Đáp án 1",
                                "percent": 100.0,
                                "total": 6
                            }
                        ]
                    },
                    {
                        "id": "a17c9aae-fe4c-47c3-aceb-8ea70d156e44",
                        "total_response": 6,
                        "values": [
                            {
                                "key": "Đáp án multiple choices 2",
                                "percent": 100.0,
                                "total": 6
                            },
                            {
                                "key": "Đáp án multiple choices 3",
                                "percent": 100.0,
                                "total": 6
                            }
                        ]
                    },
                    {
                        "id": "fc377125-22d0-4b5a-9193-19112e81ac0e",
                        "total_response": 6,
                        "values": [
                            {
                                "key": "Đáp án single choice 5",
                                "percent": 100.0,
                                "total": 6
                            }
                        ]
                    },
                    {
                        "id": "034c4d49-3ed2-4301-adfb-96ee6e20770d",
                        "total_response": 6,
                        "url_download": "https://mobio.vn/survey/download/034c4d49-3ed2-4301-adfb-96ee6e20770d",
                        "values": [
                            "2021-03-08T03:24:08Z",
                            "2021-03-08T03:24:08Z",
                            "2021-03-08T03:24:08Z",
                            "2021-03-08T03:24:08Z",
                            "2021-03-08T03:24:08Z",
                            "2021-03-08T03:24:08Z"
                        ]
                    },
                    {
                        "id": "878bf38b-0312-4cf4-8393-9fec0d91b2fc",
                        "total_response": 6,
                        "url_download": "https://mobio.vn/survey/download/878bf38b-0312-4cf4-8393-9fec0d91b2fc",
                        "values": [
                            "Câu trả lời đoạn văn thứ 2",
                            "Câu trả lời đoạn văn thứ 2",
                            "Câu trả lời đoạn văn thứ 2",
                            "Câu trả lời đoạn văn thứ 2",
                            "Câu trả lời đoạn văn thứ 2",
                            "Câu trả lời đoạn văn thứ 2"
                        ]
                    },
                    {
                        "id": "a359d2de-bae4-4897-963e-673d45d65f9ea",
                        "total_response": 6,
                        "url_download": "https://mobio.vn/survey/download/a359d2de-bae4-4897-963e-673d45d65f9ea",
                        "values": [
                            "Câu trả lời đoạn văn ngắn test",
                            "Câu trả lời đoạn văn ngắn test",
                            "Câu trả lời đoạn văn ngắn test",
                            "Câu trả lời đoạn văn ngắn test",
                            "Câu trả lời đoạn văn ngắn test",
                            "Câu trả lời đoạn văn ngắn test"
                        ]
                    },
                    {
                        "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba",
                        "total_response": 6,
                        "values": [
                            {
                                "key": "4",
                                "percent": 100.0,
                                "total": 6
                            }
                        ]
                    },
                    // matrix_radio
                    {
                        "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba",
                        "total_response": 6,
                        "values": [
                            {
                                "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba",
                                "key": "1",
                                "column": [
                                    {
                                        "value": "xyz"
                                        "percent": 66.66,
                                        "total": 4,
                                        "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba"
                                    },
                                    {
                                        "value": "mno"
                                        "percent": 66.66,
                                        "total": 4,
                                        "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba"
                                    }
                                ] 
                            },
                            {
                                "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba",
                                "key": "2",
                                "column": [
                                    {
                                        "value": "xyz"
                                        "percent": 66.66,
                                        "total": 4,
                                        "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba"
                                    },
                                    {
                                        "value": "mno"
                                        "percent": 66.66,
                                        "total": 4,
                                        "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba"
                                    }
                                ] 
                            }
                        ]
                    },
                    // matrix_checkbox
                    {
                        "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba",
                        "total_response": 6,
                        "values": [
                            {
                                "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba",
                                "key": "1",
                                "column": [
                                    {
                                        "value": "xyz"
                                        "percent": 66.66,
                                        "total": 4,
                                        "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba"
                                    },
                                    {
                                        "value": "mno"
                                        "percent": 66.66,
                                        "total": 4,
                                        "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba"
                                    }
                                ] 
                            },
                            {
                                "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba",
                                "key": "2",
                                "column": [
                                    {
                                        "value": "xyz"
                                        "percent": 66.66,
                                        "total": 4,
                                        "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba"
                                    },
                                    {
                                        "value": "mno"
                                        "percent": 66.66,
                                        "total": 4,
                                        "id": "b7ef5e6f-f0e8-4b85-9016-c7c33c6988ba"
                                    }
                                ] 
                            }
                        ]
                    },
                    // emoji
                    {
                        "id": "a17c9aae-fe4c-47c3-aceb-8ea70d156e44",
                        "total_response": 6,
                        "values": [
                            {
                                "key": "1",
                                "percent": 66.66,
                                "total": 4
                            },
                            {
                                "key": "2",
                                "percent": 33.34,
                                "total": 2
                            }
                        ]
                    }

                ],
                "total_submit": 6,
                "total_view": 33
            },
            "lang": "vi",
            "message": "request thành công."
        }
"""

******************************************  Dinamic load reports of survey  ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************

"""
@api {get}          {domain}/survey/api/v1.0/surveys/<survey_id>/report/questions/<question_id>                   Danh sách các câu trả lời dạng text
@apiName loadmore
@apiGroup Report 
@apiDescription                                             API trả về danh sách các câu trả lời dạng text của Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging
@apiUse lang

@apiParam   (Query:) {Number}    [page]                             Vị trí page cần lấy dữ liệu. Set <code>page=-1</code> nếu muốn lấy tất cả dữ liệu.<code>MIN_VALUE=1</code>Example: <code>&page=2</code>Default value: <code>1</code>
@apiParam   (Query:) {Number}    [per_page]                         Số phần tử trên một page. Example: <code>&per_page=15</code>

@apiSuccess (data) {list}      data                                   Tất cả các câu trả lời dạng text


@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "message": "request successful!.",
        "data": [
                 "Thái độ phục vụ tốt", 
                 "Cửa hàng bạn cần cải thiện nhiều hơn",
                 "",
                 ...
        ]
        "lang": "en"
     }
"""

******************************************  export report to a file  ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************

"""
@api {post}          {domain}/survey/api/v1.0/surveys/<survey_id>/report/actions/export                  Xuất báo cáo thành định dạng excel 
@apiName export
@apiGroup Report 
@apiDescription                                             API export báo cáo thành các dạng khác nhau 
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:) {String}    [format]                                định dạng file. Example: <li><code>excel:</code>định dạng excel</li><li><code>csv:</code>định csv...</li><li><code>default:</code>excel</li>

@apiParam (Body) {String}      title                                   Tên bản khảo sát
@apiParam (Body) {Int}         total_view                              Số lượt xem survey
@apiParam (Body) {Int}         total_submit                            Số lượt submit survey
@apiParam (Body) {String}      title                                   Tên bản khảo sát
@apiParam (Body) {Integer}     status                                  Trạng thái của Survey. <li><code>1</code>danh sách các mẫu khảo sát đang hoạt động (active)</li> <li><code>2</code>danh sách các mẫu khảo sát không hoạt động (deactive)</li>  <li><code>3</code> danh sách các mẫu khảo sát nháp (draft)</li>
@apiParam (Body) {array[]}     questions                               Danh sách các câu hỏi trong Survey
@apiParam (Body) {String}      questions.id                            <code>ID</code> của câu hỏi
@apiParam (Body) {String}      questions.title                         Tên câu hỏi
@apiParam (Body) {Integer}     questions.total_response                Tổng số response từ người trả lời của câu hỏi
@apiParam (Body) {String}      questions.type                          Loại câu hỏi
@apiParam (Body) {Object[]}    questions.values                        Danh sách các câu trả lời.
@apiParam (Body) {String}      questions.values.key                    Giá trị của mỗi đáp án.




@apiParamExample {json} body:
     HTTP/1.1 200 OK
     {
        "message": "request successful!.",
        "data": {
            "title": "Phiếu Khảo sát chất lượng dịch vụ khu vực public onsen",
            "status": 1,
            "total_view": 696,
            "total_submit": 1692,
            "questions": [
                {
                    "id": "31e34dfb-8239-4b5c-9561-bd29ffc14e62",
                    "title": "Dịch vụ cung cấp bộ khăn, áo yukata? Tower and yukata services",
                    "total_response": 100,
                    "type": "multiple_choices",
                    "values": [
                        {"key": "Đáp án 1", "total": 1, "percent": 1},
                        {"key": "Đáp án 2", "total": 3, "percent": 3},
                        {"key": "Đáp án 3", "total": 35, "percent": 35},
                        {"key": "Đáp án 4", "total": 60, "percent": 60},
                        {"key": "Đáp án 5", "total": 1, "percent": 1},
                        {"key": "Đáp án 6", "total": 0, "percent": 0}
                    ]
                },
                {
                    "id": "26dfc27b-bdaf-411e-9163-6e1f4a6c72a1",
                    "title": "Sự linh hoạt và nhiệt tình hỗ trợ khách hàng của nhân viên",
                    "total_response": 100,
                    "type": "slider",
                    "slider": {
                        "min_value": 1,
                        "max_value": 5
                    },
                    "values": [
                        {"key": "1", "total": 10, "percent": 10},
                        {"key": "2", "total": 10, "percent": 10},
                        {"key": "3", "total": 10, "percent": 10},
                        {"key": "4", "total": 60, "percent": 60},
                        {"key": "5", "total": 10, "percent": 10}
                    ]
                },
                {
                    "id": "e9b2eca1-6df1-411d-bd29-ec9b1f66df5e",
                    "title": "Sự linh hoạt và nhiệt tình hỗ trợ khách hàng của nhân viên 2",
                    "total_response": 100,
                    "type": "dropdown",
                    "values": [
                        {"key": "1", "total": 10, "percent": 10},
                        {"key": "2", "total": 30, "percent": 30},
                        {"key": "3", "total": 10, "percent": 10},
                        {"key": "4", "total": 40, "percent": 40},
                        {"key": "5", "total": 10, "percent": 10}
                    ]
                },
                {
                    "id": "c1e2b59a-b0b3-4fae-bfc8-66db35f09057",
                    "title": "Sự linh hoạt và nhiệt tình hỗ trợ khách hàng của nhân viên 3",
                    "total_response": 100,
                    "type": "single_choice",
                    "values": [
                        {"key": "1", "total": 15, "percent": 15},
                        {"key": "2", "total": 30, "percent": 30},
                        {"key": "3", "total": 10, "percent": 10},
                        {"key": "4", "total": 40, "percent": 40},
                        {"key": "5", "total": 5, "percent": 5}
                    ]
                },
                {
                    "id": "50b62edf-521e-400a-a685-1ca2afe57cdf",
                    "title": "what is your birthday?",
                    "total_response": 100,
                    "type": "date",
                    "values": []
                },
                {
                    "id": "b49449e9-5ea5-4731-a852-e79f5faf4e34",
                    "title": "xin chao ",
                    "total_response": 100,
                    "type": "paragraph",
                    "values": []
                },
                {
                    "id": "1f853b81-5cca-4775-9896-8b0ceb2c5c8b",
                    "title": "this is a short answer question!",
                    "total_response": 100,
                    "type": "short_answers",
                    "values": []
                }     
            ]
        }
        "lang": "en"
     }

@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "message": "request successful!.",
        "lang": "en"
     }
"""

******************************************  export report to a file V2 ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************

"""
@api {post}          {domain}/survey/api/v1.0/surveys/<survey_id>/report/actions/export_v2                  Xuất báo cáo thành định dạng excel V2
@apiName export v2
@apiGroup Report 
@apiDescription                                             API export báo cáo thành các dạng khác nhau v2
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:) {String}    [format]                                định dạng file. Example: <li><code>excel:</code>định dạng excel</li><li><code>csv:</code>định csv...</li><li><code>default:</code>excel</li>

@apiParam (Body) {String}      title                                   Tên bản khảo sát
@apiParam (Body) {Int}         total_view                              Số lượt xem survey
@apiParam (Body) {Int}         total_submit                            Số lượt submit survey
@apiParam (Body) {String}      title                                   Tên bản khảo sát
@apiParam (Body) {Integer}     status                                  Trạng thái của Survey. <li><code>1</code>danh sách các mẫu khảo sát đang hoạt động (active)</li> <li><code>2</code>danh sách các mẫu khảo sát không hoạt động (deactive)</li>  <li><code>3</code> danh sách các mẫu khảo sát nháp (draft)</li>
@apiParam (Body) {array[]}     questions                               Danh sách các câu hỏi trong Survey
@apiParam (Body) {String}      questions.id                            <code>ID</code> của câu hỏi
@apiParam (Body) {String}      questions.title                         Tên câu hỏi
@apiParam (Body) {Integer}     questions.total_response                Tổng số response từ người trả lời của câu hỏi
@apiParam (Body) {String}      questions.type                          Loại câu hỏi
@apiParam (Body) {Object[]}    questions.values                        Danh sách các câu trả lời.
@apiParam (Body) {String}      questions.values.key                    Giá trị của mỗi đáp án.
@apiParam (Body) {Object[]}     questions.values.column                 Danh sách các cột trong câu hỏi dạng lưới.
@apiParam (Body) {String}      questions.values.column.value            Giá trị của cột trong câu hỏi dạng lưới.
@apiParam (Body) {String}      questions.values.column.total            Tổng số lượt chọn đáp án.
@apiParam (Body) {String}      questions.values.column.percent          Phần trăm lượt chọn đáp án.

@apiParam (Body) {Object[]}    [questions.slider]              Các thuộc tính của câu hỏi dạng <code>phạm vi đánh giá</code>.
@apiParam (Body) {Int}         [questions.slider.min_value]       Giá trị nhỏ nhất.
@apiParam (Body) {Int}         [questions.slider.max_value]       Giá trị lớn nhất.




@apiParamExample {json} body:
     HTTP/1.1 200 OK
     {
        "message": "request successful!.",
        "data": {
            "title": "Phiếu Khảo sát chất lượng dịch vụ khu vực public onsen",
            "status": 1,
            "total_view": 696,
            "total_submit": 1692,
            "questions": [
                {
                    "id": "31e34dfb-8239-4b5c-9561-bd29ffc14e62",
                    "title": "Dịch vụ cung cấp bộ khăn, áo yukata? Tower and yukata services",
                    "total_response": 100,
                    "type": "multiple_choices",
                    "values": [
                        {"key": "Đáp án 1", "total": 1, "percent": 1},
                        {"key": "Đáp án 2", "total": 3, "percent": 3},
                        {"key": "Đáp án 3", "total": 35, "percent": 35},
                        {"key": "Đáp án 4", "total": 60, "percent": 60},
                        {"key": "Đáp án 5", "total": 1, "percent": 1},
                        {"key": "Đáp án 6", "total": 0, "percent": 0}
                    ]
                },
                {
                    "id": "26dfc27b-bdaf-411e-9163-6e1f4a6c72a1",
                    "title": "Sự linh hoạt và nhiệt tình hỗ trợ khách hàng của nhân viên",
                    "total_response": 100,
                    "type": "slider",
                    "slider": {
                        "min_value": 1,
                        "max_value": 5
                    },
                    "values": [
                        {"key": "1", "total": 10, "percent": 10},
                        {"key": "2", "total": 10, "percent": 10},
                        {"key": "3", "total": 10, "percent": 10},
                        {"key": "4", "total": 60, "percent": 60},
                        {"key": "5", "total": 10, "percent": 10}
                    ]
                },
                {
                    "id": "e9b2eca1-6df1-411d-bd29-ec9b1f66df5e",
                    "title": "Sự linh hoạt và nhiệt tình hỗ trợ khách hàng của nhân viên 2",
                    "total_response": 100,
                    "type": "dropdown",
                    "values": [
                        {"key": "1", "total": 10, "percent": 10},
                        {"key": "2", "total": 30, "percent": 30},
                        {"key": "3", "total": 10, "percent": 10},
                        {"key": "4", "total": 40, "percent": 40},
                        {"key": "5", "total": 10, "percent": 10}
                    ]
                },
                {
                    "id": "c1e2b59a-b0b3-4fae-bfc8-66db35f09057",
                    "title": "Sự linh hoạt và nhiệt tình hỗ trợ khách hàng của nhân viên 3",
                    "total_response": 100,
                    "type": "single_choice",
                    "values": [
                        {"key": "1", "total": 15, "percent": 15},
                        {"key": "2", "total": 30, "percent": 30},
                        {"key": "3", "total": 10, "percent": 10},
                        {"key": "4", "total": 40, "percent": 40},
                        {"key": "5", "total": 5, "percent": 5}
                    ]
                },
                {
                    "id": "50b62edf-521e-400a-a685-1ca2afe57cdf",
                    "title": "what is your birthday?",
                    "total_response": 100,
                    "type": "date",
                    "values": []
                },
                {
                    "id": "b49449e9-5ea5-4731-a852-e79f5faf4e34",
                    "title": "xin chao ",
                    "total_response": 100,
                    "type": "paragraph",
                    "values": []
                },
                {
                    "id": "1f853b81-5cca-4775-9896-8b0ceb2c5c8b",
                    "title": "this is a short answer question!",
                    "total_response": 100,
                    "type": "short_answers",
                    "values": []
                },
                {
                    "id": "1f853b81-5cca-4775-9896-8b0ceb2c5c8b",
                    "title": "this is a matrix radio question!",
                    "total_response": 4,
                    "type": "matrix_radio",
                    "values": [
                        {
                            "key": "1",
                            "column": [
                                {
                                    "value": "xyz",
                                    "total": 3,
                                    "percent": 75
                                },
                                {
                                    "value": "mno",
                                    "total": 1,
                                    "percent": 25
                                }  
                            ]
                        },
                        {
                            "key": "2",
                            "column": [
                                {
                                    "value": "xyz",
                                    "total": 3,
                                    "percent": 75
                                },
                                {
                                    "value": "mno",
                                    "total": 1,
                                    "percent": 25
                                }  
                            ]
                        }
                    ]
                },
                {
                    "id": "1f853b81-5cca-4775-9896-8b0ceb2c5c8b",
                    "title": "this is a matrix_checkbox question!",
                    "total_response": 10,
                    "type": "matrix_checkbox",
                    "values": [
                        {
                            "key": "1",
                            "column": [
                                {
                                    "value": "xyz",
                                    "total": 8,
                                    "percent": 80
                                },
                                {
                                    "value": "mno",
                                    "total": 4,
                                    "percent": 40
                                }  
                            ]
                        },
                        {
                            "key": "2",
                            "column": [
                                {
                                    "value": "xyz",
                                    "total": 3,
                                    "percent": 30
                                },
                                {
                                    "value": "mno",
                                    "total": 7,
                                    "percent": 70
                                }
                            ]
                        }
                    ]
                },
                {
                    "id": "26dfc27b-bdaf-411e-9163-6e1f4a6c72a1",
                    "title": "Sự linh hoạt và nhiệt tình hỗ trợ khách hàng của nhân viên",
                    "total_response": 100,
                    "type": "rating_emoji",
                    "slider": {
                        "min_value": 1,
                        "max_value": 5
                    },
                    "values": [
                        {"key": "1", "total": 5, "percent": 5},
                        {"key": "2", "total": 10, "percent": 10},
                        {"key": "3", "total": 10, "percent": 10},
                        {"key": "4", "total": 60, "percent": 60},
                        {"key": "5", "total": 15, "percent": 15}
                    ]
                }
            ]
        }
        "lang": "en"
     }

@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "message": "request successful!.",
        "lang": "en"
     }
"""


******************************************  survey export history to file excel  ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************

"""
@api {GET}          {domain}/survey/api/v1.0/survey/<survey_id>/report_excel       Export thông tin trả lời của survey 
@apiName Export thông tin trả lời của survey
@apiGroup Report 
@apiDescription                                             API export thông tin trả lời của survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

"""

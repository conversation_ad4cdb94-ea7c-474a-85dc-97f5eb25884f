
******************************************  Activate survey  ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************



"""
@api {put}          {domain}/survey/api/v1.0/surveys/actions/activate                       Kích hoạt Surveys
@apiName activate survey
@apiGroup Status 
@apiDescription                                             API kích hoạt Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:) {String} id                                 Danh sách <code>Id</code> của survey muốn kích hoạt
@apiParamExample   {json}  Body:
    { 
        "id" : ["19021128-a322-4b08-92db-a2c96a982e36", "19021128-a322-4b08-92db-a2c96a982e37"]
    }
    
    
@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "code": 200,
        "message": "request successful!.",
        "lang": "en"
     }

"""
******************************************  Deactivate survey  ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************


"""
@api {put}          {domain}/survey/api/v1.0/surveys/actions/deactivate                    Dừng hoạt động Surveys
@apiName deactivate survey
@apiGroup Status 
@apiDescription                                             API dừng hoạt động Survey
@apiVersion 1.0.0

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam (Body:) {String} id                                 Danh sách <code>Id</code> của survey muốn dừng hoạt động
@apiParamExample   {json}  Body:
    {
        "id" : ["19021128-a322-4b08-92db-a2c96a982e36", "19021128-a322-4b08-92db-a2c96a982e37"]
    }


@apiSuccessExample {json} Success-Response:
     HTTP/1.1 200 OK
     {
        "code": 200,
        "message": "request successful!.",
        "lang": "en"
     }

"""

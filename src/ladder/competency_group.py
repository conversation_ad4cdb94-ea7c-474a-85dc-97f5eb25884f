# ================================================ Competency Group Define ================================================
"""
@apiDefine ResponseDetailCompetencyGroup

@apiSuccess {String}            data.id                                 <code>ID</code> Id của competency group.
@apiSuccess {String}            data.name                               Tên nhóm năng lực.
@apiSuccess {String}            data.category_code                      Mã loại nhóm năng lực.
@apiSuccess {Array}             data.lst_competency                     Danh sách năng lực.
@apiSuccess {String}            data.description                        Mô tả nhóm năng lực.
@apiSuccess {Boolean}           data.is_default                         Kiểm tra có phải nhóm năng lực mặc định hay không.
@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật

"""

# ================================================== Danh sách loại nhóm năng lực ================================================== #
"""
@api {GET} {HOST}/api/v1.0/competency-groups-categories   Lấy danh sách loại nhóm năng lực
@apiDescription Danh sách loại nhóm năng lực
@apiGroup CompetencyGroup
@apiVersion 1.0.0
@apiName CategoryCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiSuccess       {String}       message          Mô tả phản hồi.
@apiSuccess       {Integer}      code             Mã phản hồi.
@apiSuccess       {Array}        data             Danh sách loại nhóm năng lực.
@apiSuccess       {String}       data.name        Tên loại nhóm năng lực.
@apiSuccess       {String}       data.code        Mã loại nhóm năng lực.
@apiSuccess       {String}       data.order       Thứ tự sắp xếp.
@apiSuccess       {Boolean}      data.is_editable   Kiểm tra có thể chỉnh sửa hay không.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Lấy danh sách loại nhóm năng lực thành công.",
  "data": [
    {
      "name": "Nhóm tự tạo",
      "code": "CUSTOM_GROUP",
      "order": 1,
      "is_editable": true
    },
    {
      "name": "Nhóm PKSA",
      "code": "PKSA_GROUP",
      "order": 2,
      "is_editable": false
    }
  ]
  
}
"""

# ================================================== Cập nhật nhóm năng lực ================================================== #
"""
@api {PUT} {HOST}/api/v1.0/competency-groups-categories/<category_code>   Cập nhật loại nhóm năng lực
@apiDescription API cập nhật nhóm năng lực
@apiGroup CompetencyGroup
@apiVersion 1.0.0
@apiName UpdateCategoryCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}  name  Tên của loại nhóm năng lực.
@apiParamExample  {json}   Body example
{
  "name": "Nhóm năng lực tự tạo edit",
}



@apiSuccess       {String}       message          Mô tả phản hồi.
@apiSuccess       {Integer}      code             Mã phản hồi.
@apiSuccess       {Object}       data             Danh sách loại nhóm năng lực.
@apiSuccess       {String}       data.name        Tên loại nhóm năng lực.
@apiSuccess       {String}       data.code        Mã loại nhóm năng lực.
@apiSuccess       {String}       data.order       Thứ tự sắp xếp.
@apiSuccess       {Boolean}      data.is_editable   Kiểm tra có thể chỉnh sửa hay không.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Lấy danh sách loại nhóm năng lực thành công.",
  "data": {
    "name": "Nhóm năng lực tự tạo edit",
    "code": "CUSTOM_GROUP",
    "order": 1,
    "is_editable": true
  }
  
}
"""

# ================================================== Create competency group ================================================== #
"""
@api {post} {HOST}/api/v1.0/competency_groups               Tạo nhóm năng lực
@apiDescription API tạo nhóm năng lực
@apiGroup CompetencyGroup
@apiVersion 1.0.0
@apiName AddCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}  name  Tên của nhóm năng lực.
@apiParam   (Body:)   {String}  [description]   Mô tả nhóm năng lực.

@apiParamExample  {json}   Body example
{
  "name": "Group 1",
  "description": "Group 1 description"
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Add success"
}
"""


# ================================================== Update competency group ================================================== #
"""
@api {put} {HOST}/api/v1.0/competency-groups/<competency_group_id> Cập nhật nhóm năng lực
@apiDescription API cập nhật nhóm năng lực
@apiGroup CompetencyGroup
@apiVersion 1.0.0
@apiName UpdateCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Resource:)   {string}      competency_group_id         Định danh nhóm năng lực cần sửa.

@apiParam   (Body:)   {String}  [name]  Tên của nhóm năng lực.
@apiParam   (Body:)   {String}  [description]   Mô tả nhóm năng lực.

@apiParamExample  {json}   Body example
{
  "name": "Group 1",
  "description": "Group 1 description"
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Update success"
}
"""


# ================================================== List competency group ================================================== #
"""
@api {get} {HOST}/api/v1.0/competency-groups            Lấy danh sách nhóm năng lực
@apiDescription API lấy danh sách nhóm năng lực
@apiGroup CompetencyGroup
@apiVersion 1.0.0
@apiName ListCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:)      {String}      search                              Tên nhóm năng lực muốn search
@apiParam (Query:)      {String}      [competency_group_category_code]    Mã loại nhóm năng lực
@apiParam (Query:)      {String}      sort                                Key của cột cần sắp xếp
@apiParam (Query:)      {String}      order                               Tăng dần hoặc giảm dần, chỉ nhận 2 giá trị <code>1:Tăng-dần, -1:Giảm-dần</code>

@apiUse ResponseDetailCompetencyGroup
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "List success",
  "data": [
    {
      "id": "641a87b0b6f97fd812c09c5a",
      "name": "Group 1",
      "description": "Group 1 description",
      "is_default": false,
      "created_by": "admin",
      "created_time": "2022-09-13T00:00:00",
      "updated_by": "admin",
      "updated_time": "2022-09-13T00:00:00"
    }
  ]
}
"""

# ================================================== Get default competency group ================================================== #
"""
@api {get} {HOST}/api/v1.0/competency-groups/actions/get-default-apply              Lấy danh sách nhóm năng lực áp dụng mặc định
@apiDescription API lấy danh sách nhóm năng lực áp dụng mặc định
@apiGroup CompetencyGroup
@apiVersion 1.0.0
@apiName GetDefaultApplyCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:)      {String}      department_id           Danh sách phòng ban

@apiUse ResponseDetailCompetencyGroup
@apiSuccess      {Array}       data.lst_competency                                               Danh sách năng lực áp dụng.
@apiSuccess      {String}      data.lst_competency.pask_code                                     Code nhóm năng lực theo tiêu chí PASK
                                                                                                  <ul>
                                                                                                  <li><code>personality</code> :: P (Personality - Tố chất con người)</li>
                                                                                                  <li><code>attitude</code> :: A (Attitude - Thái độ)</li>
                                                                                                  <li><code>skill</code> :: S (Skill - Kỹ năng)</li>
                                                                                                  <li><code>knowledge</code> :: K (Knowledge - Kiến thức)</li>
                                                                                                  </ul>
@apiSuccess      {String}      [data.lst_competency.reference_info]                              Thông tin tham khảo
@apiSuccess      {String}      data.lst_competency.name                                          Tên của năng lực.
@apiSuccess      {Boolean}     data.lst_competency.is_default_apply                              Mặc định áp dụng?
@apiSuccess      {Array}       data.lst_competency.default_apply_department_ids                  Danh sách phòng ban mặc định áp dụng năng lực này
@apiSuccess      {String}      data.lst_competency.created_by                                    Người tạo.
@apiSuccess      {Integer}     data.lst_competency.weight                                        Trọng số của năng lực.
@apiSuccess      {Array}       data.lst_competency.behavior_expressions                          Danh sách biểu hiện hành vi của năng lực.
@apiSuccess      {Integer}     data.lst_competency.behavior_expressions.level                    Level biểu hiện hành vi.
@apiSuccess      {Boolean}     data.lst_competency.behavior_expressions.is_activate              Hành vi có đang được kích hoạt hay không.
@apiSuccess      {Integer}     data.lst_competency.behavior_expressions.point_min                Điểm tối thiểu của biểu hiện hành vi.
@apiSuccess      {Integer}     [data.lst_competency.behavior_expressions.point_max]              Điểm tối đa của biểu hiện hành vi.
@apiSuccess      {String}      data.lst_competency.behavior_expressions.description              Mô tả biểu hiện hành vi.
@apiSuccess      {Array}       data.lst_competency.job_title_levels                              Danh sách thông tin level và điểm tương ứng trong khung năng lực.
@apiSuccess      {String}      data.lst_competency.job_title_levels.job_title_level_id           Id của cấp độ chức danh.
@apiSuccess      {Integer}     data.lst_competency.job_title_levels.behavior_expression_level    Level biểu hiện hành vi.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "List success",
  "data": [
    {
      "id": "641a87b0b6f97fd812c09c5a",
      "name": "Group 1",
      "description": "Group 1 description",
      "is_default": false,
      "created_by": "admin",
      "lst_competency": [],
      "created_time": "2022-09-13T00:00:00",
      "updated_by": "admin",
      "updated_time": "2022-09-13T00:00:00"
    }
  ]
}
"""


# ================================================== Delete competency group ================================================== #
"""
@api {delete} {HOST}/api/v1.0/competency-groups/<competency_group_id>             Xoá nhóm năng lực
@apiDescription API xoá nhóm năng lực
@apiGroup CompetencyGroup
@apiVersion 1.0.0
@apiName DeleteCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Delete success"
}
"""
# ================================================== Category competency group ================================================== #
"""
@api {GET} {HOST}/api/v1.0/competency-groups/category/actions/get-list   Lấy danh sách phân loại các nhóm năng lực
@apiDescription API lấy danh sách phân loại các nhóm năng lực
@apiGroup CompetencyGroup
@apiVersion 1.0.0
@apiName CategoryCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess       {String}       message          Mô tả phản hồi.
@apiSuccess       {Integer}      code             Mã phản hồi.
@apiSuccess       {Array}        data             Danh sách phân loại các nhóm năng lực.
@apiSuccess       {String}       data.name        Tên phân loại.
@apiSuccess       {String}       data.code        Mã phân loại.
@apiSuccess       {String}       data.description  Mô tả phân loại.
@apiSuccess       {Integer}      data.order       Thứ tự sắp xếp.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "List success",
  "data": [
    {
      "name": "Nhóm tự tạo",
      "code": "CUSTOM_GROUP",
      "description": "",
      "order": 1
    },
    {
      "name": "Nhóm PKSA",
      "code": "PKSA_GROUP",
      "description": "",
      "order": 2
    }
  ]
}
"""

# ================================================== Update category competency group ================================================== #
"""
@api {PUT} {HOST}/api/v1.0/competency-groups/category/<category_code>   Cập nhật loại nhóm năng lực
@apiDescription API cập nhật nhóm năng lực
@apiGroup CompetencyGroup
@apiVersion 1.0.0
@apiName UpdateCategoryCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Body:)   {String}  name  Tên của loại nhóm năng lực.

@apiParamExample  {json}   Body example
{
  "name": "Group 1",
  "description": "Group 1 description"
}

@apiSuccess       {String}       message          Mô tả phản hồi.
@apiSuccess       {Integer}      code             Mã phản hồi.
@apiSuccess       {Object}       data            Category nhóm năng lực.
@apiSuccess       {String}       data.name        Tên phân loại.
@apiSuccess       {String}       data.code        Mã phân loại.
@apiSuccess       {String}       data.description  Mô tả phân loại.
@apiSuccess       {Integer}      data.order       Thứ tự sắp xếp.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "List success",
  "data": {
    "name": "Group 1",
    "code": "GROUP_1",
    "description": "Group 1 description",
    "order": 1
  }
}
"""
# ================================================== Add category competency group ================================================== #
"""
@api {POST} {HOST}/api/v1.0/competency-groups/category/actions/add       Thêm loại nhóm năng lực
@apiDescription API thêm loại nhóm năng lực
@apiGroup CompetencyGroup
@apiVersion 1.0.0
@apiName AddCategoryCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Body:)   {String}  name  Tên của loại nhóm năng lực.
@apiParam   (Body:)   {String}  description  Mô tả của loại nhóm năng lực.

@apiParamExample  {json}   Body example
{
  "name": "Group 1",
  "description": "Group 1 description"
}

@apiSuccess       {String}       message          Mô tả phản hồi.
@apiSuccess       {Integer}      code             Mã phản hồi.
@apiSuccess       {Object}       data            Category nhóm năng lực.
@apiSuccess       {String}       data.name        Tên phân loại.
@apiSuccess       {String}       data.code        Mã phân loại.
@apiSuccess       {String}       data.description  Mô tả phân loại.
@apiSuccess       {Integer}      data.order       Thứ tự sắp xếp.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "List success",
  "data": {
    "name": "Group 1",
    "code": "GROUP_1",
    "description": "Group 1 description",
    "order": 1
  }
}
"""

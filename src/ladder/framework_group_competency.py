# ================================================ Framework Competency Group Define ================================================
"""
@apiDefine ResponseDetailFrameworkCompetencyGroup

@apiSuccess {String}            data.id                                 <code>ID</code> Id của competency group.
@apiSuccess {String}            data.framework_id                       <code>ID</code> Id của khung năng lực.
@apiSuccess {String}            data.name                               Tên nhóm năng lực.
@apiSuccess {Array}             data.lst_competency                     Danh sách năng lực.
@apiSuccess {String}            data.description                        Mô tả nhóm năng lực.
@apiSuccess {Boolean}           data.is_default                         Kiểm tra có phải nhóm năng lực mặc định hay không.
@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật

"""

# ================================================== Create framework competency group ================================================== #

"""
@api {post} {HOST}/api/v1.0/framework/competency_groups               Tạo nhóm năng lực của khung năng lực
@apiDescription API tạo nhóm năng lực của khung năng lực
@apiGroup FrameworkCompetencyGroup
@apiVersion 1.0.0
@apiName AddFrameworkCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}  name  Tên của nhóm năng lực.
@apiParam   (Body:)   {String}  [description]   Mô tả nhóm năng lực.

@apiParamExample  {json}   Body example
{
  "name": "Group 1",
  "description": "Group 1 description"
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Add success"
}
"""

# ================================================== Update framework competency group ================================================== #

"""
@api {put} {HOST}/api/v1.0/framework/competency-groups/<competency_group_id>        Cập nhật nhóm năng lực trong khung năng lực
@apiDescription API cập nhật nhóm năng lực trong khung năng lực
@apiGroup FrameworkCompetencyGroup
@apiVersion 1.0.0
@apiName UpdateFrameworkCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Resource:)   {string}      competency_group_id         Định danh nhóm năng lực cần sửa.

@apiParam   (Body:)   {String}  [name]  Tên của nhóm năng lực.
@apiParam   (Body:)   {String}  [description]   Mô tả nhóm năng lực.

@apiParamExample  {json}   Body example
{
  "name": "Group 1",
  "description": "Group 1 description"
}

@apiUse ResponseDetailFrameworkCompetencyGroup

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "id": "68ba89003b68caaac72bb51a",
  "name": "Group 1",
  "framework_id": "68ba89003b68caaac72bb51b",
  "description": "Group 1 description",
  "is_default": false,
  "lst_competency" : [
    {
        "competency_ref_id" : ObjectId("68ba89003b68caaac72bb51b"),
        "weight" : NumberInt(1),
        "order" : NumberInt(1),
        "source" : "framework"
    },
    {
        "competency_ref_id" : ObjectId("68ba89003b68caaac72bb51c"),
        "weight" : NumberInt(1),
        "order" : NumberInt(1),
        "source" : "framework"
    },
    "created_by": "admin",
    "created_time": "2025-02-19T07:09Z",
    "updated_by": "admin",
    "updated_time": "2025-02-19T07:09Z"
  ]
}
"""

# ================================================== Delete framework competency group ================================================== #

"""
@api {delete} {HOST}/api/v1.0/framework/competency-groups/<competency_group_id>             Xoá nhóm năng lực trong khung năng lực
@apiDescription API xoá nhóm năng lực trong khung năng lực
@apiGroup FrameworkCompetencyGroup
@apiVersion 1.0.0
@apiName DeleteFrameworkCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Delete success"
}
"""

# ======================================== Detail framework competency group ======================================== #
"""
@api {GET} {HOST}/api/v1.0/framework/competency-groups/<competency_group_id>         Lấy chi tiết năng lực trong khung năng lực
@apiDescription API chi tiết năng lực trong khung năng lực
@apiGroup FrameworkCompetencyGroup
@apiVersion 1.0.0
@apiName DetailFrameworkCompetencyGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Object}            data                        Chỉ tiết năng lực.
@apiUse ResponseDetailCompetency

"""

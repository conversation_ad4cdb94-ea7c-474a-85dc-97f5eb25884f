""""""
"""
@apiDefine ResponseDetailCompetency

@apiSuccess                {String}                data.id                                                 <code>ID</code> Id của competency.
@apiSuccess                {String}                data.name                                               Tên năng lực.
@apiSuccess                {String}                data.pask_code                                          Code nhóm năng lực theo tiêu chí PASK
                                                                                                            <ul>
                                                                                                                <li><code>personality</code> :: P (Personality - Tố chất con người)</li>
                                                                                                                <li><code>attitude</code> :: A (Attitude - Thái độ)</li>
                                                                                                                <li><code>skill</code> :: S (Skill - Kỹ năng)</li>
                                                                                                                <li><code>knowledge</code> :: K (Knowledge - Kiến thức)</li>
                                                                                                            </ul>
@apiSuccess                {String}                [data.reference_info]                                     Thông tin tham khảo
@apiSuccess                {String}                data.description                                        Mô tả năng lực.
@apiSuccess                {String}                data.competency_group_id                                <code>ID</code> Id của competency group.
@apiSuccess                {String}                data.created_by                                         <PERSON><PERSON><PERSON><PERSON> t<PERSON>
@apiSuccess                {Array}                 data.lst_behavior_expression                            Danh sách hành vi.
@apiSuccess                {String}                data.lst_behavior_expression.description                Mô tả hành vi.
@apiSuccess                {Bool}                  data.lst_behavior_expression.is_activate                Kiểm tra hành vi có được sử dụng không.
@apiSuccess                {Integer}               data.lst_behavior_expression.level                      Level hành vi [1-5].
@apiSuccess                {String}                data.created_time                                       Thời điểm khởi tạo
@apiSuccess                {String}                data.updated_by                                         Thông tin của người cập nhật
@apiSuccess                {String}                data.updated_time                                       Thời điểm cập nhật
@apiSuccessExample  {json}   Response example
{
    "_id": "67b583c68f90ae0ce550b3c4",
    "updated_time": "2025-02-19T07:09Z",
    "created_time": "2025-02-19T07:09Z",
    "created_by": "0a4c639d-4c18-4820-a80c-3764b2ce29e8",
    "updated_by": "0a4c639d-4c18-4820-a80c-3764b2ce29e8",
    "name": "cot loi",
    "pask_code": "skill",
    "reference_info": "https://www.notion.so/67b583c68f90ae0ce550b3c4",
    "description": "Good",
    "competency_group_id": "66d2ecc520ee838e7600a582",
    "behavior_expressions": [
        {
            "description": "Call the API on behalf of the logged-in user.",
            "level": 1,
            "is_activate": true
        },
        {
            "description": "Call the API on behalf of the logged-in user.",
            "level": 2,
            "is_activate": true
        },
        {
            "description": "Call the API on behalf of the logged-in user.",
            "level": 3,
            "is_activate": true
        },
        {
            "description": "Call the API on behalf of the logged-in user.",
            "level": 4,
            "is_activate": true
        },
        {
            "description": "Call the API on behalf of the logged-in user.",
            "level": 5,
            "is_activate": true
        }
    ],
    "lower_case_name": "cot loi",
    "company_id": "4302feda-826d-4419-b1f5-c9a8291ffb51",
    "id": "67b583c68f90ae0ce550b3c4"
}

"""

# ================================================== Create framework competency ================================================== #
"""
@api {POST} {HOST}/api/v1.0/framework/competencies                                    Tạo năng lực mới trong khung năng lực
@apiDescription                                                             API tạo năng lực trong khung năng lực
@apiGroup FrameworkCompetency
@apiVersion 1.0.0
@apiName AddFrameworkCompetency

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500


@apiParam          (Body:)         {String}          name                                       Tên của năng lực.
@apiParam          (Body:)         {String}          [description]                              Mô tả năng lực.
@apiParam          (Body:)         {String}          competency_group_id                        <code>ID</code> nhóm năng lực.
@apiParam          (Body:)         {Boolean}         is_default_apply                           Được mặc định áp dụng.
@apiParam          (Body:)         {String}          pask_code                                  <code>Mã</code> nhóm năng lực theo tiêu chí PASK
                                                                                                <ul>
                                                                                                  <li><code>personality</code> :: P (Personality - Tố chất con người)</li>
                                                                                                  <li><code>attitude</code> :: A (Attitude - Thái độ)</li>
                                                                                                  <li><code>skill</code> :: S (Skill - Kỹ năng)</li>
                                                                                                  <li><code>knowledge</code> :: K (Knowledge - Kiến thức)</li>
                                                                                                </ul> 
@apiParam          (Body:)         {String}          [reference_info]                          Thông tin tham khảo
@apiParam          (Body:)         {Array}           lst_behavior_expression                   Năng lực theo từng mức độ
@apiParam          (Body:)         {String}          lst_behavior_expression.description       Mô tả hành vi.
@apiParam          (Body:)         {Bool}            lst_behavior_expression.is_activate       Kiểm tra hành vi có được sử dụng không.
@apiParam          (Body:)         {Integer}         lst_behavior_expression.level             Level hành vi [1-5].


@apiParamExample  {json}   Body example
{
  "name": "Tạo năng lực mới",
  "description": "Tạo năng lực mới",
  "competency_group_id": "66d2ecc520ee838e7600a584",
  "reference_info": "https://dev.mobio.vn/docs/ladder/#api-Competency-ListCompetency",
  "pask_code": "skill",
  "lst_behavior_expression": [
      {
          "description": "Tạo năng lực mới",
          "is_activate": true,
          "level": 1
      },
      {
          "description": "Tạo năng lực mới",
          "is_activate": true,
          "level": 2
      },
      {
          "description": "Tạo năng lực mới",
          "is_activate": true,
          "level": 3
      },
      {
          "description": "Tạo năng lực mới",
          "is_activate": true,
          "level": 4
      },
      {
          "description": "Tạo năng lực mới",
          "is_activate": true,
          "level": 5
      }
  ]
}

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Object}            data                          Thông tin dữ liệu trả về.

@apiUse ResponseDetailCompetency

"""

# ================================================== Update competency ================================================== #

"""
@api {PUT} {HOST}/api/v1.0/framework/competencies/<competency_id>               Cập nhật năng lực trong khung năng lực
@apiDescription API cập nhật năng lực trong khung năng lực
@apiGroup FrameworkCompetency
@apiVersion 1.0.0
@apiName UpdateFrameworkCompetency

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam    (Body:)   {String}     [name]                                  Tên của năng lực.
@apiParam    (Body:)   {String}     [description]                           Mô tả năng lực.
@apiParam    (Body:)   {String}     [competency_group_id]                   Id nhóm năng lực.
@apiParam    (Body:)   {Array}      [lst_behavior_expression]               Danh sách hành vi.
@apiParam    (Body:)   {String}     [lst_behavior_expression.description]   Mô tả hành vi.
@apiParam    (Body:)   {Bool}       [lst_behavior_expression.is_activate]   Kiểm tra hành vi có được sử dụng không.
@apiParam    (Body:)   {Integer}    [lst_behavior_expression.level]         Level hành vi [1-5].


@apiParamExample  {json}   Body example
{
  "name": "Group 1",
  "description": "Group 1 description",
  "competency_group_id": "bdae6f6a-7f40-4669-b2d5-5a1011e3f188",
  "lst_behavior_expression": [
    {
      "description": "Behavior 1",
      "is_activate": true,
      "level": 1
    },
    {
      "description": "Behavior 2",
      "is_activate": true,
      "level": 2
    }
  ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin dữ liệu trả về.

@apiUse ResponseDetailCompetency

"""

# ================================================== Detail competency ================================================== #
"""
@api {GET} {HOST}/api/v1.0/framework/competencies/<competency_id> Lấy chi tiết năng lực trong khung năng lực
@apiDescription API chi tiết năng lực trong khung năng lực
@apiGroup FrameworkCompetency
@apiVersion 1.0.0
@apiName DetailFrameworkCompetency

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Object}            data                        Chỉ tiết năng lực.
@apiUse ResponseDetailCompetency

"""

# ================================================== Delete competency ================================================== #

"""
@api {POST} {HOST}/api/v1.0/framework/competencies/actions/delete                 Xoá năng lực trong khung năng lực
@apiDescription API xóa năng lực trong khung năng lực
@apiGroup FrameworkCompetency
@apiVersion 1.0.0
@apiName DeleteFrameworkCompetency

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam       (Body:)    {Array}        competency_ids         Danh sách các Id năng lực cần xoá.

@ApiParamExample  {json}   Body example
{
  "competency_ids": [
    "bdae6f6a-7f40-4669-b2d5-5a1011e3f188"
  ]
}

@apiSuccess {String}            message                  Mô tả phản hồi.
@apiSuccess {Integer}           code                     Mã phản hồi.
@apiSuccess {Object}            data                     Thông tin dữ liệu trả về.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Delete success",
  "data": {}
}
"""
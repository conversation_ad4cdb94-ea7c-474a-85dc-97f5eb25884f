******************************** Feedback Lead ********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} https://api.bank.mobio.vn/profiling/v2.0/batches/feedback/leads     Feedback lead
@apiDescription Dịch vụ để hệ thống CRM-VPBank update lại thông tin của lead sau khi sale.<br/>
Sau khi API thực hiện ghi nhận feedback khách hàng sẽ gọi url callback lại hệ thống VPBank-CRM kết quả thực thi nghiệp vụ.
@apiGroup Feedback
@apiVersion 1.0.0
@apiName FeedbackLead
@apiIgnore

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiHeader (Headers:) {String} Authorization Token/api-key để sử dụng api. Nếu typically là:
<li><code>Basic</code> thì Authenticate là API-Key</li>
@apiHeaderExample Basic Token Example:
{
    "Authorization":"Basic ********-2976-444a-ba7a-3ca1b48fcc3a"
}
@apiError (Error 4xx) 401-Unauthorized token/api-key hết hạn hoặc sai định dạng. Yêu cầu login lại.
<li><code>code:</code> 401</li>
<li><code>message:</code> Mô tả lỗi.</li>
<br/>
@apiErrorExample    {json}  HTTP/1.1 401
HTTP/1.1 401 Unauthorized
{
    "code": 401,
    "message":"Token is invalid or is expired. Please login again."
}

@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện business của merchant.

@apiParam     (Body:)     {Object[]}    leads       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>Lead</code>
@apiParam     (Body:)      {String}      callback    URL callback kết quả thực thi nghiệp vụ. API thực thi dạng <code>POST</code> với body:
<pre class="prettyprint language-json prettyprinted"><span class="str">
{</br>
  {</br>
    "success":[</br>
      "0755ee00-8610-424f-ba4b-b264f64c3b46",<br/>
      "ccca9bfb-0f21-47e2-8f90-8d590e094b77"<br/>
    ],<br/>
    "error":[<br/>
      {<br/>
        "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",<br/>
        "message": "..."<br/>
      },<br/>
      {<br/>
        "id": "79ccd5d3-299f-41b4-8fea-2b91668da2b8",<br/>
        "message": "..."<br/>
      }<br/>
]</span></pre>


@apiParam     (Lead)      {String}      id          Định danh của khách hàng trên hệ thống VPBank-MOBIO.
@apiParam     (Lead)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (Lead)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam   (UserInfo:)     {String}    name                                    Tên khách hàng
@apiParam   (UserInfo:)     {String}    phone_number                            Số điện thoại của khách hàng.
@apiParam   (UserInfo:)     {String}    email                                   Email của khách hàng.
@apiParam   (UserInfo:)     {Number=1:Unknown 2:Nam 3:Nữ}    gender             Giới tính.
@apiParam   (UserInfo:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (UserInfo:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (UserInfo:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (UserInfo:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (UserInfo:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (UserInfo:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (UserInfo:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (UserInfo:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (UserInfo:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (UserInfo:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (UserInfo:)     {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (UserInfo:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.

@apiParam   (UserInfo:)    {Object}    [partner_info]    Thông tin bổ sung (format <code>JSON</code>) của VPBank. Thông tin này sẽ được gửi lại cho third party để đối soát thông tin (VD: <code>creditCardApplicationId</code>). Value của <code>third_party_info</code> do third party tự định nghĩa.

@apiParam     (Feedback)    {Array}  [status]   Cập nhật tình trạng lead.
@apiParam     (Feedback)    {Number}  [status..value]   Trạng thái lead.<br/>
<li><code>111: NotContacted</code></li>
<li><code>112: CicCheck</code></li>
<li><code>113: Reject</code></li>
<li><code>114: CancelLOS</code></li>
<li><code>115: FailKYC</code></li>
<li><code>116: Quick</code></li>
<li><code>117: Approve</code></li>
<li><code>118: Pending</code></li>
<li><code>119: SubmittedLOS</code></li>
<li><code>120: CompleteKYC</code></li>

@apiParam     (Feedback)    {Number}  [status..updated_time]   Thời điểm cập nhật trạng thái. Đơn vị tính timestamp. Ví dụ: <code>"updated_time": *************</code><br/>

@apiParamExample    {json}      Body example:
{
  "leads": [
    {
      "id": "9035b72d-4b8e-471d-935d-e73f73063246",
      "user_info": {
        "name": "andrew",
        "phone_number": "0904123456",
        "email": "<EMAIL>",
        "address": "Hà Nội",
        "gender": 2,
        "birthday": "01/01/1989",
        "income_low_threshold": 7000000,
        "income_high_threshold": 15000000,
        "partner_info": {
          "creditCardApplicationId": "2ccfe4f8-5dab-4c0e-92ab-80fbc30012ce",
        }
      },
      "feedback": {
        "status": [
          {
            "value": 111,
            "updated_time": 1528861712428
          },
          {
            "value": 119,
            "updated_time": 1528861712428
          }
        ]
      }
    }
  ],
  "callback": "https://some.domain.to.callback"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Hệ thống đã ghi nhận thông tin feedback của customer."
}
"""
****************

************************************************************************************************************
****************************************** API Upsert user *************************************************
************************************************************************************************************

"""
@api {post} https://api.bank.mobio.vn/profiling/v2.0/merchants/<merchant_id>/users/actions/upsert    Import khách hàng
@apiDescription API lưu trữ thông tin khách hàng. Trường hợp không có thông tin khách hàng thì tạo mới, ngược lại thì update dữ liệu mới.
@apiGroup Import
@apiVersion 1.0.1
@apiName UpsertUsers
@apiIgnore

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện business của merchant.

@apiParam   (Body:)     {String}    name                                    Tên khách hàng
@apiParam   (Body:)     {String}    phone_number                            Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    email                                   Email của khách hàng.
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    gender             Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (Body:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (Body:)     {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.

@apiParam   (Body:)    {Object}    [third_party_info]    Thông tin bổ sung (format <code>JSON</code>) của third party. Thông tin này sẽ được gửi lại cho third party để đối soát thông tin. Value của <code>third_party_info</code> do third party tự định nghĩa.


@apiParamExample [json] Body example:
{
    "name": "andrew",
    "phone_number": "0904123456",
    "email": "<EMAIL>",
    "address": "Hà Nội",
    "gender": 2,
    "birthday": "01/01/1989",
    "income_low_threshold": 7000000,
    "income_high_threshold": 15000000,
    "third_party_info": {
      "lead_id": "2ccfe4f8-5dab-4c0e-92ab-80fbc30012ce",
      "acquired_time": *************,
      "article_topic": "",
      "timestamp": *************
    }
}


@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng

@apiSuccess (Body:)     {String}     profile_id                                 ID khách hàng
@apiSuccess   (Body:)     {String}      name                            Tên khách hàng
@apiSuccess   (Body:)     {Array}     phone_number                      Mảng Số điện thoại của khách hàng.
@apiSuccess   (Body:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (Body:)     {Number}    created_account_type              Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (Body:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (Body:)     {Number}    gender                            Giới tính
@apiSuccess   (Body:)     {String}    address                           Địa chỉ
@apiSuccess   (Body:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (Body:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (Body:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (Body:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (Body:)     {String}    birthday                          Ngày sinh
@apiSuccess   (Body:)     {Number}    income_low_threshold                                 Mức thu nhập thấp.
@apiSuccess   (Body:)     {Number}    income_high_threshold                                Mức thu nhập cao.
@apiSuccess   (Body:)     {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    income_type          Hình thức thu nhập.


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "profile_id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "name": "andrew",
  "phone_number": [
    "+***********",
    "+***********"
  ],
  "email": [
    "<EMAIL>",
    "<EMAIL>"
  ],
  "created_account_type": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "province_code": 1,
  "district_code": 1,
  "ward_code": 1,
  "marital_status": 1,
  "birthday": "1989-09-17",
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "operation": null,
  "third_party_info": {
      "lead_id": "2ccfe4f8-5dab-4c0e-92ab-80fbc30012ce",
      "acquired_time": *************,
      "article_topic": "",
      "timestamp": *************
    }
}

"""


********************


"""
@api {post} https://api.bank.mobio.vn/profiling/v2.0/merchants/<merchant_id>/users/actions/upsert    Import khách hàng
@apiDescription API lưu trữ thông tin khách hàng. Trường hợp không có thông tin khách hàng thì tạo mới, ngược lại thì update dữ liệu mới.
@apiGroup Import
@apiVersion 1.0.0
@apiName UpsertUsers
@apiIgnore

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện business của merchant.

@apiParam   (Body:)     {String}    name                                    Tên khách hàng
@apiParam   (Body:)     {String}    phone_number                            Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    email                                   Email của khách hàng.
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    gender             Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (Body:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (Body:)     {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.

@apiParamExample [json] Body example:
{
    "name": "andrew",
    "phone_number": "0904123456",
    "email": "<EMAIL>",
    "address": "Hà Nội",
    "gender": 2,
    "birthday": "01/01/1989",
    "income_low_threshold": 7000000,
    "income_high_threshold": 15000000
}


@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng

@apiSuccess (Body:)     {String}     profile_id                                 ID khách hàng
@apiSuccess   (Body:)     {String}      name                            Tên khách hàng
@apiSuccess   (Body:)     {Array}     phone_number                      Mảng Số điện thoại của khách hàng.
@apiSuccess   (Body:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (Body:)     {Number}    created_account_type              Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (Body:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (Body:)     {Number}    gender                            Giới tính
@apiSuccess   (Body:)     {String}    address                           Địa chỉ
@apiSuccess   (Body:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (Body:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (Body:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (Body:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (Body:)     {String}    birthday                          Ngày sinh
@apiSuccess   (Body:)     {Number}    income_low_threshold                                 Mức thu nhập thấp.
@apiSuccess   (Body:)     {Number}    income_high_threshold                                Mức thu nhập cao.
@apiSuccess   (Body:)     {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    income_type          Hình thức thu nhập.


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "profile_id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "name": "andrew",
  "phone_number": [
    "+***********",
    "+***********"
  ],
  "email": [
    "<EMAIL>",
    "<EMAIL>"
  ],
  "created_account_type": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "province_code": 1,
  "district_code": 1,
  "ward_code": 1,
  "marital_status": 1,
  "birthday": "1989-09-17",
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "operation": null
}

"""
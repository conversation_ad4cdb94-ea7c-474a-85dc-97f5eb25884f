************************************************************************************************************************
************************************************** CHECK VALID EMAILS **************************************************
************************************************************************************************************************
"""
@api {post} /check-emails Kiểm tra email có tồn tại hay không
@apiDescription Kiểm tra email có tồn tại hay không
@apiGroup CheckValid
@apiVersion 2.0.0
@apiName PostCheckValid

@apiUse 405

@apiParam  {String} merchant_id UUID doanh nghiệp
@apiParam  {ArrayString} data Danh sách email muốn check
@apiParam  {String} [session_id] UUID lượt check. Nếu có sẽ gửi kèm sang profiling khi check xong
@apiParamExample {json} Body
{
  "merchant_id": "7f85c440-33f1-44e3-93b8-ead76c3ff063",
  "data": [
    "<EMAIL>",
    "<EMAIL>",
    ...
  ],
  "session_id": "d4196736-703b-4fde-91a2-a57d4a499c92"
}

@apiSuccess {Boolean} [is_valid] Trạng thái valid của email. Chỉ trả về nếu <code>len(data) = 1</code>
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "is_valid": true
}
"""
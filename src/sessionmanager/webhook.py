""" Author: TiepTh
    Company: MobioVN
    Date created: 14/02/2020
"""
#
# ==================================================================================================================================
# ************************************************** Webhook  **************************************************
# * v2.0.0                                                                                                                         *
# **********************************************************************************************************************************
"""
@api {GET} /webhook/<channel>/sent Webhook gửi tin
@apiVersion v2.0.0
@apiName Webhook sent
@apiGroup Webhook

@apiParam (Resources) {string=email sms facebook zalo mobile socket} channel Kênh gửi tin

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query) {string}       id                      id của message
@apiParam (Query) {int}          status                  Trạng thái message
@apiParam (Query) {string}       reason                  Lý do không gửi được

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message":"request successful."
}
"""


"""
@api {GET} /webhook/<channel>/open Webhook open tin
@apiVersion v2.0.0
@apiName Webhook open
@apiGroup Webhook

@apiParam (Resources) {string=email sms facebook zalo mobile socket} channel Kênh gửi tin

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query) {string}       id                      id của message

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message":"request successful."
}
"""


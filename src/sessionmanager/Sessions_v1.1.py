#!/usr/bin/python
# -*- coding: utf8 -*-

"""
@api {post} /sessions/notifies   <PERSON><PERSON><PERSON> thông báo
@apiDescription T<PERSON><PERSON> thông báo khi có thông tin thay đổi. <i>Content-type</i>: <b>application/json</b>
@apiGroup Sessions
@apiVersion 1.1.0
@apiName CreateNotify

@apiUse lang
@apiUse 404
@apiUse 405

@apiUse from_param
@apiUse to_param
@apiParam     (To object)      {String}    [receiver_ids]      Danh sách UUID nhận tin. Ưu tiên sử dụng <code>receiver_ids</code>, nếu không có sẽ dùng <code>receiver_id</code>
@apiParam   (Body:)     {Object}    body    Nội dung thông báo. Chi tiết nội dung tuỳ từng kiểu notify.

@apiParamExample {json} Body example receiver_id
{
  "body": {
    "assign": [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "conversation_id": "20290c28-7a99-4b7e-9298-a6dca61a7420",
        "created_time": "2019-10-29T10:30:16Z",
        "created_user": null,
        "id": "64fb7484-f098-49ff-8167-53bf5c1a4b62",
        "note": null,
        "status": 1
      }
    ],
    "attachments": [],
    "classify": 1
  },
  "from": {
    "sender_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "sender_type": 3,
    "source": "social-crm",
    "status": 0
  },
  "to": {
    "channel": "MOBIO_PARTNER_CEM",
    "receiver_id": "8ba5e082-393f-47e0-a4a4-b91643056583",
    "receiver_type": 3
  }
}

@apiParamExample {json} Body example receiver_ids
{
  "body": {
    "assign": [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "conversation_id": "20290c28-7a99-4b7e-9298-a6dca61a7420",
        "created_time": "2019-10-29T10:30:16Z",
        "created_user": null,
        "id": "64fb7484-f098-49ff-8167-53bf5c1a4b62",
        "note": null,
        "status": 1
      }
    ],
    "attachments": [],
    "classify": 1
  },
  "from": {
    "sender_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "sender_type": 3,
    "source": "social-crm",
    "status": 0
  },
  "to": {
    "channel": "MOBIO_PARTNER_CEM",
    "receiver_ids": [
        "8ba5e082-393f-47e0-a4a4-b91643056583",
        "877c3637-343d-448c-9cf2-ea9225fa1e04"
    ],
    "receiver_type": 3
  }
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "notify":{
        "id":"c4cef897-c900-401a-be61-9fe33f8f7b76",
        "is_read":1
    }
}
"""

"""
@api {post} /sessions/notifies/<notify_id>/read Đánh dấu đã đọc
@apiDescription Đánh dấu một notify là đã có nhân viên đọc/biết thông tin thay đổi.
@apiGroup Sessions
@apiVersion 1.1.0
@apiName MarkReadNotify

@apiUse lang
@apiUse 404
@apiUse 405

@apiParam   (Resource:)     {String}    notify_id   UUID của notify

@apiSuccess     {String}    id          UUID của notify
@apiSuccess     {Number=0-Unread,1-Read}    is_read     Trạng thái của notify

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "notify":{
        "id":"ab305af8-5e29-4017-a8e9-a6cd87dc3b77",
        "is_read":1
    }
}
"""

#########################################################################################################################################
################################################## LẤY THÔNG BÁO STAFF THEO CHỨC NĂNG  ##################################################
#########################################################################################################################################
"""
@api {get}  /sessions/notifies/<function_name>/staffs/<staff_id>  Lấy thông báo staff theo chức năng
@apiDescription Lấy danh sách các thông báo theo chức năng của staff. Danh sách thông báo của staff bao gồm các notify tới nhân viên đó và các notify đến nhãn hàng.
@apiGroup Sessions
@apiVersion 1.1.2
@apiName GetNotifyByFunction

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiParam       (Resource:)     {String=NOTIFY,INBOX}   function_name   Chức năng cần lấy danh sách thông báo.
@apiParam       (Resource:)     {String}                staff_id   UUID của staff.

@apiSuccess     {Notify[]}      notifies    Danh sách các thông báo. Xem chi tiết đối tượng <code>Notify</code>
@apiSuccess     {Object}        summary     Thông tin tổng hợp.
<li><code>unread_number</code> (Number): Số lượng thông báo chưa đọc.</li>

@apiSuccess     (Notify)    {String}    id          UUID notify.
@apiSuccess     (Notify)    {Object}    from        Thông tin người gửi. Xem <code>From object</code>
@apiSuccess     (Notify)    {Object}    to          Thông tin người nhận. Xem <code>To object</code>
@apiSuccess     (Notify)    {Object}    body        Nội dung event. Tuỳ thuộc vào event sẽ có nội dung khác nhau.
@apiSuccess     (Notify)    {Number=0-Unread,1-Read}    is_read     Trạng thái notify.
@apiSuccess     (Notify)    {DateTime}  created_time    Thời điểm tạo.
@apiSuccess     (Notify)    {DateTime}  updated_time    Thời điểm cập nhật.

@apiUse from_success
@apiUse to_success

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "notifies":[
        {
            "id":"09086da3-f307-4bc2-aecf-723ca6893242",
            "from": { 
                "source": "CRM",
                "sender_id": "xxxxxyyyyzzzz",
                "sender_type": 2
            },
            "to": {
                "receiver_id": "xxxxxyyyyzvvvzzz",
                "receiver_type": 2,
                "function": "NOTIFY",
            }
            "is_read": 1,
            "body": {
                // nội dung body
            },
            "created_time":"2017-08-07T04:02:28.002Z",
            "updated_time":"2017-08-07T04:02:28.002Z"
        },
        {
            "id":"1f97b4f7-2646-4b44-8558-c8a02d25c917",
            "from": { 
                "source": "CRM",
                "sender_id": "xxxxxyyyyzzzz",
                "sender_type": 2
            },
            "to": {
                "receiver_id": "xxxxxyyyyzvvvzzz",
                "receiver_type": 2,
                "function": "NOTIFY",
            }
            "is_read": 0,
            "body": {
                // nội dung body
            },
            "created_time":"2017-08-07T04:02:28.002Z",
            "updated_time":"2017-08-07T04:02:28.002Z"
        }
    ],
    "summary":{
        "unread_number":5
    }
}
"""
"""
@api {get}  /sessions/notifies/<function_name>/staffs/<staff_id>  Lấy thông báo staff theo chức năng
@apiDescription Lấy danh sách các thông báo theo chức năng của staff. Danh sách thông báo của staff bao gồm các notify tới nhân viên đó và các notify đến nhãn hàng.
@apiGroup Sessions
@apiVersion 1.1.1
@apiName GetNotifyByFunction

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiParam       (Resource:)     {String=NOTIFY,INBOX}   function_name   Chức năng cần lấy danh sách thông báo.
@apiParam       (Resource:)     {String}                staff_id   UUID của staff.

@apiSuccess     {Notify[]}      notifies    Danh sách các thông báo. Xem chi tiết đối tượng <code>Notify</code>
@apiSuccess     {Object}        summary     Thông tin tổng hợp.
<li><code>unread_number</code> (Number): Số lượng thông báo chưa đọc.</li>

@apiSuccess     (Notify)    {String}    id          UUID notify.
@apiSuccess     (Notify)    {Object}    from        Thông tin người gửi. Xem <code>From object</code>
@apiSuccess     (Notify)    {Object}    to          Thông tin người nhận. Xem <code>To object</code>
@apiSuccess     (Notify)    {Object}    body        Nội dung event. Tuỳ thuộc vào event sẽ có nội dung khác nhau.
@apiSuccess     (Notify)    {Number=0-Unread,1-Read}    is_read     Trạng thái notify.

@apiUse from_success
@apiUse to_success

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "notifies":[
        {
            "id":"09086da3-f307-4bc2-aecf-723ca6893242",
            "from": { 
                "source": "CRM",
                "sender_id": "xxxxxyyyyzzzz",
                "sender_type": 2
            },
            "to": {
                "receiver_id": "xxxxxyyyyzvvvzzz",
                "receiver_type": 2,
                "function": "NOTIFY",
            }
            "is_read": 1,
            "body": {
                // nội dung body
            }
        },
        {
            "id":"1f97b4f7-2646-4b44-8558-c8a02d25c917",
            "from": { 
                "source": "CRM",
                "sender_id": "xxxxxyyyyzzzz",
                "sender_type": 2
            },
            "to": {
                "receiver_id": "xxxxxyyyyzvvvzzz",
                "receiver_type": 2,
                "function": "NOTIFY",
            }
            "is_read": 0,
            "body": {
                // nội dung body
            }
        }
    ],
    "summary":{
        "unread_number":5
    }
}
"""

"""
@api {get}  /sessions/notifies/<function_name>/staffs/<staff_id>  Lấy thông báo staff theo chức năng
@apiDescription Lấy danh sách các thông báo theo chức năng của staff. Danh sách thông báo của staff bao gồm các notify tới nhân viên đó và các notify đến nhãn hàng.
@apiGroup Sessions
@apiVersion 1.1.0
@apiName GetNotifyByFunction

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiParam       (Resource:)     {String=NOTIFY,INBOX}   function_name   Chức năng cần lấy danh sách thông báo.
@apiParam       (Resource:)     {String}                staff_id   UUID của staff.

@apiSuccess     {Notify[]}      notifies    Danh sách các thông báo. Xem chi tiết đối tượng <code>Notify</code>

@apiSuccess     (Notify)    {String}    id          UUID notify.
@apiSuccess     (Notify)    {Object}    from        Thông tin người gửi. Xem <code>From object</code>
@apiSuccess     (Notify)    {Object}    to          Thông tin người nhận. Xem <code>To object</code>
@apiSuccess     (Notify)    {Object}    body        Nội dung event. Tuỳ thuộc vào event sẽ có nội dung khác nhau.
@apiSuccess     (Notify)    {Number=0-Unread,1-Read}    is_read     Trạng thái notify.

@apiUse from_success
@apiUse to_success

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "notifies":[
        {
            "id":"09086da3-f307-4bc2-aecf-723ca6893242",
            "from": { 
                "source": "CRM",
                "sender_id": "xxxxxyyyyzzzz",
                "sender_type": 2
            },
            "to": {
                "receiver_id": "xxxxxyyyyzvvvzzz",
                "receiver_type": 2,
                "function": "NOTIFY",
            }
            "is_read": 1,
            "body": {
                // nội dung body
            }
        },
        {
            "id":"1f97b4f7-2646-4b44-8558-c8a02d25c917",
            "from": { 
                "source": "CRM",
                "sender_id": "xxxxxyyyyzzzz",
                "sender_type": 2
            },
            "to": {
                "receiver_id": "xxxxxyyyyzvvvzzz",
                "receiver_type": 2,
                "function": "NOTIFY",
            }
            "is_read": 0,
            "body": {
                // nội dung body
            }
        }
    ]
}
"""

"""
@apiDeprecated
@api {get}  /sessions/notifies/<function_name>/merchants/<merchant_id>  Lấy thông báo merchant theo chức năng
@apiDescription Lấy danh sách các thông báo theo chức năng của merchant.
@apiGroup Sessions
@apiVersion 1.1.0
@apiName GetMerchantNotifyByFunction

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiParam       (Resource:)     {String=NOTIFY,INBOX}   function_name   Chức năng cần lấy danh sách thông báo.
@apiParam       (Resource:)     {String}                merchant_id   UUID của merchant.

@apiSuccess     {Notify[]}      notifies    Danh sách các thông báo. Xem chi tiết đối tượng <code>Notify</code>

@apiSuccess     (Notify)    {String}    id          UUID notify.
@apiSuccess     (Notify)    {Object}    from        Thông tin người gửi. Xem <code>From object</code>
@apiSuccess     (Notify)    {Object}    to          Thông tin người nhận. Xem <code>To object</code>
@apiSuccess     (Notify)    {Object}    body        Nội dung event. Tuỳ thuộc vào event sẽ có nội dung khác nhau.
@apiSuccess     (Notify)    {Number=1,0}    is_read     Trạng thái notify.

@apiUse from_success
@apiUse to_success

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "notifies":[
        {
            "id":"09086da3-f307-4bc2-aecf-723ca6893242",
            "from": { 
                "source": "CRM",
                "sender_id": "xxxxxyyyyzzzz",
                "sender_type": 2
            },
            "to": {
                "receiver_id": "xxxxxyyyyzvvvzzz",
                "receiver_type": 2,
                "function": "NOTIFY",
            }
            "is_read": 1,
            "body": {
                // nội dung body
            }
        },
        {
            "id":"1f97b4f7-2646-4b44-8558-c8a02d25c917",
            "from": { 
                "source": "CRM",
                "sender_id": "xxxxxyyyyzzzz",
                "sender_type": 2
            },
            "to": {
                "receiver_id": "xxxxxyyyyzvvvzzz",
                "receiver_type": 2,
                "function": "NOTIFY",
            }
            "is_read": 0,
            "body": {
                // nội dung body
            }
        }
    ]
}
"""

"""
@api {get}  /sessions/merchants  Lấy danh sách merchant
@apiDescription Lấy danh sách các merchant có staff online.
@apiGroup Sessions
@apiVersion 1.1.0
@apiName GetMerchants

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiSuccess     {Merchant[]}        merchants   Danh sách đối tượng merchant. Chi tiết đối tượng merchant:
<li><code>id</code> (String): UUID của merchant.</li>
<li><code>staff_number</code> (Number): Số lượng nhân viên đang online của nhãn hàng.</li>
</br>
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "merchants":[
        {
            "id":"5ffffebb-b925-487d-81b2-6cff9d11f7f3",
            "staff_number":3
        },
        {
            "id":"b02f0640-de14-4815-9518-c634f1ea2873",
            "staff_number":1
        }
    ]
}
"""

"""
@api {get} /sessions/staffs/<staff_id>/status  Kiểm tra staff online
@apiDescription Kiểm tra xem một nhân viên đang online hay offline.
@apiGroup Sessions
@apiVersion 1.1.0
@apiName CheckStaffOn

@apiUse lang
@apiUse 404
@apiUse 405

@apiParam   (Resource:)     staff_id    UUID của staff.

@apiSuccess     {Object}    staff   Thông tin trạng thái staff.
<li><code>id</code>(String): UUID của nhân viên</li>
<li><code>sessions</code>(String[]): Mảng string danh sách các session_id của nhân viên.</li>
<li><code>status</code>(Number): Trạng thái của nhân viên: <code>1-Online</code>, <code>0-Offline</code></li>
</br>
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "staff":{
        "id":"6d827a0f-8868-42bc-98e3-28d6ea001284",
        "sessions":[
            "04c6298a-7e24-414a-8e35-afa914d20b80",
            "8a88e7cf-cf04-474f-8f2f-ed9c6e15456c"
        ],
        "status":1
    }
}
"""

"""
@api {get} /sessions/staffs  Lấy staffs online của merchant
@apiDescription Lấy danh sách các nhân viên đang online.
<li>Nếu param <code>merchant_id</code> <b>không có</b> hoặc <b>empty</b> thì trả về tất cả nhân viên đang online trên hệ thống.</li>
<li>Nếu param <code>merchant_id</code> có dữ liệu(<i>not empty</i>) thì trả về tất cả nhân viên đang online của merchant.</li>
@apiGroup Sessions
@apiVersion 1.1.0
@apiName GetSessionStaffs

@apiUse lang
@apiUse 404
@apiUse 405

@apiParam   (Query:)     merchant_id     UUID của merchant.

@apiSuccess     {Staff[]}    staffs    Danh sách các staff đang online. Chi tiết đối tượng <code>staff</code>:
<li><code>id (String):</code>UUID của staff.</li>
<li><code>connection_number (Number):</code>connection hiện tại của staff.</li>
<li><code>sessions (String[]):</code>Danh sách các session của staff. Ví dụ: Do trong quá trình sử dụng nhân viên mở nhiều tab webCRM để thao tác.</li>
<li><code>merchant_id (String):</code>UUID của merchant.</li>
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "staffs":[
        {
            "id":"a042dc4d-82bf-4a07-ba4e-8dfdd1ed8e8f",
            "connection_number":1,
            "sessions":[
                "d4561ec8-e8fe-40fe-9b07-937a77b427cb"
            ]
            "merchant_id":"91d6a525-fe50-4b97-9e52-cc814b4ddbf8"
        },
        {
            "id":"29606a76-9cd7-475a-86cb-c357515e2c9b",
            "connection_number":2,
            "sessions":[
                "d4561ec8-e8fe-40fe-9b07-937a77b427cb",
                "30c7e6a0-34f1-48cd-a8a0-25f189632d3b"
            ]
            "merchant_id":"91d6a525-fe50-4b97-9e52-cc814b4ddbf8"
        }
    ]
}
"""

"""
@apiDefine to_param
@apiVersion 1.0.0
@apiParam     (To object)      {String=MOBIO_PARTNER_CEM}    channel      Kênh nhận tin.
@apiParam     (To object)      {String}    receiver_id      UUID của đối tượng nhận.
<li>Nếu <code>receiver_id</code> là merchant_id thì tất cả nhân viên của nhãn hàng sẽ nhận được thông báo này.</li>
<li>Nếu <code>receiver_id</code> là staff_id thì chỉ có nhân viên này sẽ nhận được thông báo.</li>
@apiParam     (To object)      {Number=1-MOBIO,2-MERCHANT,3-STAFF}    receiver_type      Kiểu đối tượng nhận. Xác định giá trị cho <code>receiver_id</code>
"""

"""
@apiDefine from_param
@apiVersion 1.0.0
@apiParam     (From object)      {String=call-center,retail,social-crm,marketing,sale}    source      Tên nguồn phát sinh event.
@apiParam     (From object)      {String}    sender_id      UUID của đối tượng gửi.
@apiParam     (From object)      {Number=1-MOBIO,2-MERCHANT,3-STAFF,4-CUSTOMER,5-FB_USER}    sender_type      Kiểu đối tượng gửi. Xác định giá trị cho <code>sender_id</code>
"""

"""
@apiDefine to_success
@apiVersion 1.0.0
@apiSuccess     (To object)      {String}    receiver_id      UUID của đối tượng nhận.
<li>Nếu <code>receiver_id</code> là merchant_id thì tất cả nhân viên của nhãn hàng sẽ nhận được thông báo này.</li>
<li>Nếu <code>receiver_id</code> là staff_id thì chỉ có nhân viên này sẽ nhận được thông báo.</li>
@apiSuccess     (To object)      {Number=1-MOBIO,2-MERCHANT,3-STAFF}    receiver_type      Kiểu đối tượng nhận. Xác định giá trị cho <code>receiver_id</code>
"""

"""
@apiDefine from_success
@apiVersion 1.0.0
@apiSuccess     (From object)      {String=call-center,retail,social-crm,marketing,sale}    source      Tên nguồn phát sinh event.
@apiSuccess     (From object)      {String}    sender_id      UUID của đối tượng gửi.
@apiSuccess     (From object)      {Number=1-MOBIO,2-MERCHANT,3-STAFF,4-CUSTOMER,5-FB_USER}    sender_type      Kiểu đối tượng gửi. Xác định giá trị cho <code>sender_id</code>
"""
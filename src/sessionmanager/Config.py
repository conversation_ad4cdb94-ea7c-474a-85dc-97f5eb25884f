""" Author: TiepTh
    Company: MobioVN
    Date created: 18/02/2020
"""
############################################################################################
"""
@api {put} /merchants/<merchant_id>/configs
Insert or update config
@apiDescription Insert or update config
@apiVersion v2.0.0
@apiGroup Config

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {String}         auth_name           
@apiParam  {String}         auth_pass           
@apiParam  {String}         auth_attachment     
@apiParam  {int}            provider_type       
@apiParam  {String}         provider_api        
@apiParam  {int}            status              
@apiParam  {String}         others             


@apiParamExample {json} Body
{
    "auth_name":"123aaaa....",
    "auth_pass":"asdasd.....",
    "auth_attachment":"Mobio",
    "provider_type":102,
    "provider_api":"https://secure.brandsms.vn/vmgapi.asmx/aaaaa",
    "status":1,
    "others":""
}

@apiSuccess {Boolean} [is_valid] Trạng thái valid của email. Chỉ trả về nếu <code>len(data) = 1</code>
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "is_valid": true
}
"""

"""
@api {get} /merchants/<merchant_id>/configs
Lấy danh sách config merchant
@apiDescription Lấy danh sách config merchant
@apiVersion v2.0.0
@apiGroup Config

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess (data:)   {string}     auth_attachment
@apiSuccess (data:)   {string}     auth_name
@apiSuccess (data:)   {string}     id
@apiSuccess (data:)   {string}     provider_api
@apiSuccess (data:)   {int}        provider_type

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK

{
  "code": 200,
  "data": [
    {
      "auth_attachment": "MOBIO",
      "auth_name": "9XMf948F4Ym3efdLklnUvu5sebbbIqHLrQsMed1QKeqtqMNY3qODCa3up23nl3zP",
      "id": "127500d4-2055-48e3-a151-36c3da7a8a73",
      "provider_api": "https://secure.brandsms.vn/vmgapi.asmx/BulkSendSms",
      "provider_type": 100
    },
    {
      "auth_attachment": "ck_mobiosakuko.pem",
      "auth_name": "9XMf948F4Ym3efdLklnUvkzO6f1xnm02tW2T24IiRRr30g76-89cgTHTPrdT5qC8",
      "id": "166dcd3e-9b9f-41cc-aef3-3e0cd2c93cfe",
      "provider_api": "com.mobio.sakuko.loyalty",
      "provider_type": 301
    }
  ],
  "lang": "vi",
  "message": "request thành công."
}
"""

"""
@api {get} /merchants/<merchant_id>/configs/config_id
Lấy chi tiết config
@apiDescription Lấy chi tiết config
@apiVersion v2.0.0
@apiGroup Config

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess (data:)   {string}     auth_attachment
@apiSuccess (data:)   {string}     auth_name
@apiSuccess (data:)   {string}     id
@apiSuccess (data:)   {string}     provider_api
@apiSuccess (data:)   {int}        provider_type

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK

{
  "code": 200,
  "data": [
    {
      "auth_attachment": "MOBIO",
      "auth_name": "9XMf948F4Ym3efdLklnUvu5sebbbIqHLrQsMed1QKeqtqMNY3qODCa3up23nl3zP",
      "id": "127500d4-2055-48e3-a151-36c3da7a8a73",
      "provider_api": "https://secure.brandsms.vn/vmgapi.asmx/BulkSendSms",
      "provider_type": 100
    }
  ],
  "lang": "vi",
  "message": "request thành công."
}
"""

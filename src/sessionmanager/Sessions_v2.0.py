**********************************************************************************************************************************************
************************************************** LẤY NỘI DUNG EMAIL ĐÃ GỬI CHO KHÁCH HÀNG **************************************************
**********************************************************************************************************************************************
"""
@api {get} /emails/marketing/content Lấy nội dung email đã gửi cho khách hàng
@apiDescription Lấy nội dung email đã gửi cho khách hàng
@apiGroup Content
@apiVersion 2.0.0
@apiName GetContentEmail

@apiUse 404
@apiUse 405

@apiParam (Query) {string} user_id UUID Khách hàng
@apiParam (Query) {string} campaign_id UUID campaign
@apiParam (Query) {string} root_message_id UUID root message của campaign

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "subject": "Tiêu đề email",
    "html": "Nội dung email"
  }
}
"""

****************************************************************************************************************************
************************************************** KIỂM TRA OBJECT ONLINE **************************************************
****************************************************************************************************************************
"""
@api {get} /sessions/objects/<object_id>/status  Kiểm tra object online
@apiDescription Kiểm tra xem một đối tượng (staff hoặc user) đang online hay offline.
@apiGroup Sessions
@apiVersion 2.0.0
@apiName CheckStaffOn

@apiUse lang
@apiUse 404
@apiUse 405

@apiParam   (Resource:)     object_id    UUID của staff.

@apiSuccess  {UUID} id UUID của nhân viên hoặc người dùng
@apiSuccess  {Number} status Trạng thái của đối tượng: <code>1-Online</code>, <code>0-Offline</code></li>
@apiSuccessExample    {json}    Response: HTTP/1.1 200 OK
{
    "id": "6d827a0f-8868-42bc-98e3-28d6ea001284",
    "status": 1
}
"""

************************************************************************************************************************************************
************************************************** LẤY DANH SÁCH CÁC MERCHANT CÓ STAFF ONLINE **************************************************
************************************************************************************************************************************************
"""
@api {get}  /sessions/merchants  Lấy danh sách các merchant có staff online.
@apiDescription Lấy danh sách các merchant có staff online.
@apiGroup Sessions
@apiVersion 2.0.0
@apiName GetMerchants

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiSuccess (data) {UUID} id UUID của merchant.
@apiSuccess (data) {Number} staff_number Số lượng nhân viên đang online của nhãn hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "5ffffebb-b925-487d-81b2-6cff9d11f7f3",
      "staff_number": 3
    },
    {
      "id": "b02f0640-de14-4815-9518-c634f1ea2873",
      "staff_number": 1
    }
  ],
  "lang": "vi",
  "paging": {
    "page": 1,
    "per_page": 10,
    "page_count": 10,
    "total_count": 100
  }
}
"""

************************************************************************************************************************************
************************************************** LẤY STAFFS ONLINE CỦA MERCHANT **************************************************
************************************************************************************************************************************
"""
@api {get} /sessions/staffs  Lấy staffs online của merchant
@apiDescription Lấy danh sách các nhân viên đang online.
<li>Nếu param <code>merchant_id</code> <b>không có</b> hoặc <b>empty</b> thì trả về tất cả nhân viên đang online trên hệ thống.</li>
<li>Nếu param <code>merchant_id</code> có dữ liệu(<i>not empty</i>) thì trả về tất cả nhân viên đang online của merchant.</li>
@apiGroup Sessions
@apiVersion 2.0.0
@apiName GetSessionStaffs

@apiUse lang
@apiUse 404
@apiUse 405

@apiParam (Query:) {String} merchant_id UUID của merchant. Ví dụ <code>&merchant_id=91d6a525-fe50-4b97-9e52-cc814b4ddbf8</code>

@apiSuccess     {Staff[]}    staffs    Danh sách các staff đang online. Chi tiết đối tượng <code>staff</code>:
<li><code>id (String):</code>UUID của staff.</li>
<li><code>merchant_id (String):</code>UUID của merchant.</li>
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "staffs":[
        {
            "id":"a042dc4d-82bf-4a07-ba4e-8dfdd1ed8e8f",
            "merchant_id":"91d6a525-fe50-4b97-9e52-cc814b4ddbf8"
        },
        {
            "id":"29606a76-9cd7-475a-86cb-c357515e2c9b",
            "merchant_id":"91d6a525-fe50-4b97-9e52-cc814b4ddbf8"
        }
    ]
}
"""

***********************************************************************************************************************************************************
************************************************** LẤY THÔNG BÁO CỦA OBJECT(STAFF, USER) THEO CHỨC NĂNG **************************************************
***********************************************************************************************************************************************************
"""
@api {get}  /sessions/notifies/<channel>/objects/<object_id>  Lấy thông báo của một object (staff  hoặc user) theo chức năng
@apiDescription Lấy danh sách các thông báo theo chức năng của object (staff hoặc user). Danh sách thông báo của object bao gồm các notify tới object đó và các notify đến nhãn hàng(nếu object là nhân viên).
@apiGroup Notify
@apiVersion 2.0.0
@apiName GetNotifyByFunction

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiParam       (Resource:)     {String=NOTIFY MOBIO_APP PARTNER_CRM}   channel   Chức năng cần lấy danh sách thông báo.
@apiParam       (Resource:)     {String}                object_id   UUID của object (staff hoặc user).

@apiParam (Query:) {Number} [notify_type] Loại notify do client tự đinh nghĩa. Nếu <code>notify_type=None</code> thì không lọc theo trường này
@apiParam (Query:) {Number} [os_push_id] Hệ điều hành. android,ios
@apiParam (Query:) {Number} [push_id] push_id

@apiSuccess     {Notify[]}      notifies    Danh sách các thông báo. Xem chi tiết đối tượng <code>Notify</code>
@apiSuccess  {Number} unread_number Số lượng thông báo chưa đọc.

@apiSuccess     (Notify)    {String}    id          UUID notify.
@apiSuccess     (Notify)    {Object}    from        Thông tin người gửi. Xem <code>From object</code>
@apiSuccess     (Notify)    {Object}    to          Thông tin người nhận. Xem <code>To object</code>
@apiSuccess     (Notify)    {Object}    body        Nội dung event. Tuỳ thuộc vào event sẽ có nội dung khác nhau.
@apiSuccess     (Notify)    {Number=0-Unread,1-Read}    is_read     Trạng thái notify.
@apiSuccess     (Notify)    {DateTime}  created_time    Thời điểm tạo.
@apiSuccess     (Notify)    {DateTime}  updated_time    Thời điểm cập nhật.

@apiUse from_success
@apiUse to_success

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "notifies":[
        {
            "id":"09086da3-f307-4bc2-aecf-723ca6893242",
            "notify_type": 1,
            "from": { 
                "source": "CRM",
                "sender_id": "xxxxxyyyyzzzz",
                "sender_type": 2
            },
            "to": {
                "receiver_id": "xxxxxyyyyzvvvzzz",
                "receiver_type": 2,
                "channel": "NOTIFY",
            }
            "is_read": 1,
            "body": {
                // nội dung body
            },
            "created_time":"2017-08-07T04:02:28.002Z",
            "updated_time":"2017-08-07T04:02:28.002Z"
        },
        {
            "id":"1f97b4f7-2646-4b44-8558-c8a02d25c917",
            "notify_type": 1,
            "from": { 
                "source": "CRM",
                "sender_id": "xxxxxyyyyzzzz",
                "sender_type": 2
            },
            "to": {
                "receiver_id": "xxxxxyyyyzvvvzzz",
                "receiver_type": 2,
                "channel": "NOTIFY",
            }
            "is_read": 0,
            "body": {
                // nội dung body
            },
            "created_time":"2017-08-07T04:02:28.002Z",
            "updated_time":"2017-08-07T04:02:28.002Z"
        }
    ],
    "unread_number":5,
    "paging": {
        ...
    }
}
"""

***********************************************************************************************************************************************************
************************************************** LẤY SỐ LƯỢNG THÔNG BÁO  **************************************************
***********************************************************************************************************************************************************
"""
@api {get}  /sessions/notifies/<channel>/objects/<object_id>/total  LẤY SỐ LƯỢNG THÔNG BÁO
@apiDescription LẤY SỐ LƯỢNG THÔNG BÁO
@apiGroup Notify
@apiVersion 2.0.0
@apiName GetTotalNotifyByFunction

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiParam       (Resource:)     {String=NOTIFY MOBIO_APP PARTNER_CRM}   channel   Chức năng cần lấy danh sách thông báo.
@apiParam       (Resource:)     {String}                object_id   UUID của object (staff hoặc user).

@apiParam (Query:) {Number} [notify_type] Loại notify do client tự đinh nghĩa. Nếu <code>notify_type=None</code> thì không lọc theo trường này
@apiParam (Query:) {Number} [os_push_id] Hệ điều hành. android,ios
@apiParam (Query:) {Number} [push_id] push_id

@apiSuccess  {Number} unread_number Số lượng thông báo chưa đọc.

@apiUse from_success
@apiUse to_success

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "unread_number":5
}
"""


***********************************************************************************************************************************************************
************************************************** Đánh dấu đọc toàn bộ thông báo  **************************************************
***********************************************************************************************************************************************************
"""
@api {post}  /sessions/notifies/<channel>/objects/<object_id>/read  Đánh dấu đọc toàn bộ thông báo 
@apiDescription Đánh dấu đọc toàn bộ thông báo
@apiGroup Notify
@apiVersion 2.0.0
@apiName ReadAllNotifyByFunction

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiParam       (Resource:)     {String=NOTIFY MOBIO_APP PARTNER_CRM}   channel   Chức năng cần lấy danh sách thông báo.
@apiParam       (Resource:)     {String}                object_id   UUID của object (staff hoặc user).

@apiParam (Query:) {Number} [notify_type] Loại notify do client tự đinh nghĩa. Nếu <code>notify_type=None</code> thì không lọc theo trường này
@apiParam (Query:) {Number} [os_push_id] Hệ điều hành. android,ios
@apiParam (Query:) {Number} [push_id] push_id

@apiSuccess  {Number} unread_number Số lượng thông báo chưa đọc.

@apiUse from_success
@apiUse to_success

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "unread_number":5
}
"""


***********************************************************************************************************************************************************
************************************************** LẤY THÔNG BÁO CỦA OBJECT(STAFF, USER) THEO CHỨC NĂNG **************************************************
***********************************************************************************************************************************************************
"""
@api {get}  /sessions/notifies/<channel>/objects/<object_id>  Lấy thông báo của một object (staff  hoặc user) theo chức năng
@apiDescription Lấy danh sách các thông báo theo chức năng của object (staff hoặc user). Danh sách thông báo của object bao gồm các notify tới object đó và các notify đến nhãn hàng(nếu object là nhân viên).
@apiGroup Notify
@apiVersion 2.0.0
@apiName GetNotifyByFunction

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiParam       (Resource:)     {String=NOTIFY MOBIO_APP PARTNER_CRM}   channel   Chức năng cần lấy danh sách thông báo.
@apiParam       (Resource:)     {String}                object_id   UUID của object (staff hoặc user).

@apiParam (Query:) {Number} [notify_type] Loại notify do client tự đinh nghĩa. Nếu <code>notify_type=None</code> thì không lọc theo trường này
@apiParam (Query:) {Number} [os_push_id] Hệ điều hành. android,ios
@apiParam (Query:) {Number} [push_id] push_id

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": {
            'unread_number': 1,
            'total_count': 2
        }
}
"""


*******************************************************************************************************************
************************************************** TẠO THÔNG BÁO **************************************************

*v2.0.2
*******************************************************************************************************************

"""
@api {post} /sessions/notifies Tạo thông báo
@apiDescription Tạo thông báo khi có thông tin thay đổi. <i>Content-type</i>: <b>application/json</b>
@apiGroup Notify
@apiVersion 2.0.2
@apiName CreateNotify

@apiUse lang
@apiUse 404
@apiUse 405

@apiUse from_param
@apiUse to_param
@apiParam  {String} web_hook Link nhận thông báo kết quả gửi
@apiParam   (Body:)     {Object}    body    Nội dung thông báo. Chi tiết nội dung tuỳ từng kiểu notify.
@apiParam  {Number} notify_type Loại notify thông tin này do client tự đinh nghĩa
@apiParam (from) {status=0-NOT_SAVE 1-SAVE} [status=0] Trạng thái lưu giữ notification.
@apiParamExample {json} Body example
{
    "web_hook": "https://mobio.vn/adm/v1.0/merchants/<merchant_id>/accounts/<account_id>/receiver_push",
    "from": { 
        "source": "social-crm",
        "sender_id": "0c214f46-a6d6-47b7-b9ea-282144d0a52c",
        "sender_type": 3,
        "status": 0
    },
    "to": {
        "receiver_id": "2cf390b5-8953-4c56-b96c-906a1191a9a3",
        "receiver_type": 3,
        "channel": "NOTIFY",
    },
    "notify_type": 1,
    "body": {
        "id":"09f8ee23-713f-424b-ae2e-a839db65c120",
        "page_social_id":"***************",
        "social_type":1,
        "status":3,
        "token_name":"Mẹ và bé",
        "title":"Đã ngắt kết nối đến page Facebook: Mẹ và bé",
        "description":"",
        "action":1000 //Xem Action 1000
    }
}
@apiParamExample {json} App mobile example
{
    "web_hook": "url_web_hook", /* link webhook báo kết quả gửi sms */
    "from": {
      "merchant_id": "659a...1297c"   /* uuid merchant yêu cầu gửi tin nhắn */
    },
    "to": {
      "receiver_id": "",
      "receiver_type": "",
      "channel": "MOBIO_APP",
      "push_id": "ALDKKAWIELASDLJFDF29ADKASL0K32L32KLDSAK210",
      "os_push_id": "ANDROID"
    },
    "body": {
        "mapush": 6,
        "noidunghienthi": {
            "title": "title", <===> key 'alert'
            "body": "Day la push duoc gui tu Notify Manager abc xyz" /*Nội dung hiển thị*/
        },
        "noidungan": "Noi dung an ne" /*Nội dung ẩn*/
    }
}
@apiParamExample {json} SMS example
{
    "web_hook": "url_web_hook", /* link webhook báo kết quả gửi sms */
    "from": {
        "merchant_id": "659a...1297c"   /* uuid merchant yêu cầu gửi tin nhắn */
    },
    "to": {
        "phone_number": "**********",   /* số điện thoại cần gửi tin nhắn */
        "channel": "MOBIO_SMS"
    },
    "body": {
        "content": "Day la message" /* nội dung tin nhắn */
    }
}
@apiParamExample {json} email example
{
    "web_hook": "http://mobio.vn",  /* link webhook báo kết quả gửi email */,
    "open_webhook": "http://..." /* link webhook nhận thông báo khi mail được open */
    "from": { 
        "sender_id": "<EMAIL>", /* email người gửi */
        "sender_type": 6, /* Kiểu gửi */
        "source": "marketing",  /* nguồn gửi */
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
        "name": "Support Mobio" /* tên người gửi */
    },
    "to": {
        "email": "<EMAIL>",  /* email cần gửi đi. Có thể gửi 1 mảng. VD: ["Lộc lá <<EMAIL>>", "Lộc lấp lánh <<EMAIL>>"] */,
        "cc": ["<EMAIL>"],
        "bcc": ["<EMAIL>"],
        "name": "Lộc lấp lánh", /* tên người nhận */
        "channel": "MOBIO_EMAIL"
    },
    "notify_type": 1,
    "body": {
      "subject": "V/v Test email",  /* Subject của email */
      "content": "Hello from NM"  /* nội dung email */
    }
}
@apiParamExample {json} Facebook Messenger example
{
    "web_hook": "http://mobio.vn",  /* link webhook báo kết quả gửi email */
    "from": { 
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
        "page_social_id": "", /* ID page mạng xã hội */
        "social_type": 1 /* kiểu mạng xã hội: 1: Facebook, 2: Zalo; 3: Instagram; 4: Youtube*/
    },
    "to": {
      "user_social_ids": [] /* danh sách id mạng xã hội của 1 user / 1 mạng xã hội (do FB có nhiều ID / user)*/
      "callbacks": [  /* danh sách object nhận call back từ social */
        {
          "type": 1 /* 1-MessageRead (khách hàng đọc message) */
          "url": "https://yourdomain/callback"  /* URL callback */
        }
        ...
      ]
    },
    "body": {
      "content": "Day la message" /* nội dung tin nhắn */
    }
}


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message":"request successful."
}
"""

*v2.0.1
*******************************************************************************************************************
"""
@api {post} /sessions/notifies Tạo thông báo
@apiDescription Tạo thông báo khi có thông tin thay đổi. <i>Content-type</i>: <b>application/json</b>
@apiGroup Notify
@apiVersion 2.0.1
@apiName CreateNotify

@apiUse lang
@apiUse 404
@apiUse 405

@apiUse from_param
@apiUse to_param
@apiParam  {String} web_hook Link nhận thông báo kết quả gửi
@apiParam   (Body:)     {Object}    body    Nội dung thông báo. Chi tiết nội dung tuỳ từng kiểu notify.
@apiParam  {Number} notify_type Loại notify thông tin này do client tự đinh nghĩa
@apiParamExample {json} Body example
{
    "web_hook": "https://mobio.vn/adm/v1.0/merchants/<merchant_id>/accounts/<account_id>/receiver_push",
    "from": { 
        "source": "social-crm",
        "sender_id": "0c214f46-a6d6-47b7-b9ea-282144d0a52c",
        "sender_type": 3
    },
    "to": {
        "receiver_id": "2cf390b5-8953-4c56-b96c-906a1191a9a3",
        "receiver_type": 3,
        "channel": "NOTIFY",
    },
    "notify_type": 1,
    "body": {
        "id":"09f8ee23-713f-424b-ae2e-a839db65c120",
        "page_social_id":"***************",
        "social_type":1,
        "status":3,
        "token_name":"Mẹ và bé",
        "title":"Đã ngắt kết nối đến page Facebook: Mẹ và bé",
        "description":"",
        "action":1000 //Xem Action 1000
    }
}
@apiParamExample {json} App mobile example
{
    "web_hook": "url_web_hook", /* link webhook báo kết quả gửi sms */
    "from": {
      "merchant_id": "659a...1297c"   /* uuid merchant yêu cầu gửi tin nhắn */
    },
    "to": {
      "receiver_id": "",
      "receiver_type": "",
      "channel": "MOBIO_APP",
      "push_id": "ALDKKAWIELASDLJFDF29ADKASL0K32L32KLDSAK210",
      "os_push_id": "ANDROID"
    },
    "body": {
        "mapush": 6,
        "noidunghienthi": {
            "title": "title", <===> key 'alert'
            "body": "Day la push duoc gui tu Notify Manager abc xyz" /*Nội dung hiển thị*/
        },
        "noidungan": "Noi dung an ne" /*Nội dung ẩn*/
    }
}
@apiParamExample {json} SMS example
{
    "web_hook": "url_web_hook", /* link webhook báo kết quả gửi sms */
    "from": {
        "merchant_id": "659a...1297c"   /* uuid merchant yêu cầu gửi tin nhắn */
    },
    "to": {
        "phone_number": "**********",   /* số điện thoại cần gửi tin nhắn */
        "channel": "MOBIO_SMS"
    },
    "body": {
        "content": "Day la message" /* nội dung tin nhắn */
    }
}
@apiParamExample {json} email example
{
    "web_hook": "http://mobio.vn",  /* link webhook báo kết quả gửi email */,
    "open_webhook": "http://..." /* link webhook nhận thông báo khi mail được open */
    "from": { 
        "sender_id": "<EMAIL>", /* email người gửi */
        "sender_type": 6, /* Kiểu gửi */
        "source": "marketing",  /* nguồn gửi */
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
        "name": "Support Mobio" /* tên người gửi */
    },
    "to": {
        "email": "<EMAIL>",  /* email cần gửi đi. Có thể gửi 1 mảng. VD: ["Lộc lá <<EMAIL>>", "Lộc lấp lánh <<EMAIL>>"] */,
        "cc": ["<EMAIL>"],
        "bcc": ["<EMAIL>"],
        "name": "Lộc lấp lánh", /* tên người nhận */
        "channel": "MOBIO_EMAIL"
    },
    "notify_type": 1,
    "body": {
      "subject": "V/v Test email",  /* Subject của email */
      "content": "Hello from NM"  /* nội dung email */
    }
}
@apiParamExample {json} Facebook Messenger example
{
    "web_hook": "http://mobio.vn",  /* link webhook báo kết quả gửi email */
    "from": { 
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
        "page_social_id": "", /* ID page mạng xã hội */
        "social_type": 1 /* kiểu mạng xã hội: 1: Facebook, 2: Zalo; 3: Instagram; 4: Youtube*/
    },
    "to": {
      "user_social_ids": [] /* danh sách id mạng xã hội của 1 user / 1 mạng xã hội (do FB có nhiều ID / user)*/
      "callbacks": [  /* danh sách object nhận call back từ social */
        {
          "type": 1 /* 1-MessageRead (khách hàng đọc message) */
          "url": "https://yourdomain/callback"  /* URL callback */
        }
        ...
      ]
    },
    "body": {
      "content": "Day la message" /* nội dung tin nhắn */
    }
}


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message":"request successful."
}
"""

********************************************************************************************************************************************
************************************************** TẠO THÔNG BÁO DẠNG GỬI ĐẾN NHIỀU NGƯỜI **************************************************

*v2.0.1
********************************************************************************************************************************************
"""
@api {post} /sessions/multi-notifies Tạo thông báo dạng gửi đến nhiều người
@apiDescription Tạo thông báo đến nhiều người khi có thông tin thay đổi. <i>Content-type</i>: <b>application/json</b>
@apiGroup Notify
@apiVersion 2.0.1
@apiName CreateMultiNotifies

@apiUse lang
@apiUse 404
@apiUse 405

@apiUse from_param
@apiParam  {String} web_hook Link nhận thông báo kết quả gửi

@apiParam (from) {status=0-NOT_SAVE 1-SAVE} [status=0] Trạng thái lưu giữ notification.

@apiParam  {to[]} to Danh sách nguồn nhận thông báo

@apiParam (To object) {String} receiver_id UUID của đối tượng nhận.
@apiParam (To object) {Number} receiver_type Kiểu đối tượng nhận. Xác định giá trị cho <code>receiver_id</code>
@apiParam (To object) {String=MOBIO_APP:AppMobile MOBIO_MERCHANT_APP:AppMobile MOBIO_PARTNER_CEM:WebSocket MOBIO_ADMIN_CEM:WebSocket MOBIO_SMS:SMSSender MOBIO_EMAIL:EmailSender MOBIO_NOTIFY_APP: App notify} channel Chức năng được gửi đến.
@apiParam (To object) {Number} notify_type Loại notify thông tin này do client tự đinh nghĩa
@apiParam (To object) {Object} body Nội dung thông báo. Chi tiết nội dung tuỳ từng kiểu notify.
@apiParam (To object) {String} [push_id] Push id đến thiết bị Android hoặc iOS. Required khi <code>channel</code> là <code>AppMobile</code>
@apiParam (To object) {String=ANDROID IOS} [os_push_id] Hệ điều hành của push id. Required khi <code>channel</code> là <code>AppMobile</code>
@apiParamExample {json} Body example
{
  "from": {
    "source": "social-crm",
    "sender_id": "0c214f46-a6d6-47b7-b9ea-282144d0a52c",
    "sender_type": 3,
    "status": 0
  },
  "to": [
    {
      "web_hook": "https://mobio.vn/adm/v1.0/merchants/<merchant_id>/accounts/<account_id>/receiver_push",
      "receiver_id": "2cf390b5-8953-4c56-b96c-906a1191a9a3",
      "receiver_type": 3,
      "channel": "NOTIFY",
      "notify_type": 1,
      "body": {
        "id": "09f8ee23-713f-424b-ae2e-a839db65c120",
        "page_social_id": "***************",
        "social_type": 1,
        "status": 3,
        "token_name": "Mẹ và bé",
        "title": "Đã ngắt kết nối đến page Facebook: Mẹ và bé",
        "description": "",
        "action": 1000
      }
    }
  ]
}
@apiParamExample {json} App mobile example
{
  "from": {
    "merchant_id": "659a...1297c"   /* uuid merchant yêu cầu gửi tin nhắn */
  },
  "to": [
    {
      "web_hook": "https://mobio.vn/adm/v1.0/merchants/<merchant_id>/accounts/<account_id>/receiver_push",
      "receiver_id": "",
      "receiver_type": "",
      "channel": "MOBIO_APP",
      "push_id": "ALDKKAWIELASDLJFDF29ADKASL0K32L32KLDSAK210",
      "os_push_id": "ANDROID",
      "body": {
        "mapush" /*Mã push*/ : 105,
        "noidunghienthi": {
          "title": "title", <===> key 'alert'
          "body": "Day la push duoc gui tu Notify Manager abc xyz" /*Nội dung hiển thị*/
        },
        "noidungan" /*Nội dung ẩn*/
      }
    },
    ...
  ]
}
@apiParamExample {json} SMS example
{
  "from": {
    "merchant_id": "659a...1297c"   /* uuid merchant yêu cầu gửi tin nhắn */
  },
  "to": [
    {
      "web_hook": "url_web_hook", /* link webhook báo kết quả gửi sms */
      "phone_number": "**********",   /* số điện thoại cần gửi tin nhắn */
      "channel": "MOBIO_SMS",
      "body": {
        "content": "Day la message" /* nội dung tin nhắn */
      }
    },
    ...
  ]
}
@apiParamExample {json} email example
{
  "from": {
    "sender_id": "<EMAIL>", /* email người gửi */
    "sender_type": 6, /* Kiểu gửi */
    "source": "marketing",  /* nguồn gửi */
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
    "name": "Support Mobio" /* tên người gửi */
  },
  "to": [
    {
      "web_hook": "url_web_hook", /* link webhook báo kết quả gửi email */
      "open_webhook": "http://...",  /* link webhook nhận thông báo khi mail được open */
      "email": "<EMAIL>",  /* email cần gửi đi. Có thể gửi 1 mảng. VD: ["Lộc lá <<EMAIL>>", "Lộc lấp lánh <<EMAIL>>"] */
      "name": "Lộc lá",       /* tên người nhận */
      "cc": ["<EMAIL>"],
      "bcc": ["<EMAIL>"],
      "channel": "MOBIO_EMAIL",
      "body": {
        "subject": "Day la subject",    /* Subject của email */
        "content": "Day la html thì phải"   /* nội dung email */
      }
    },
    ...
  ]
}
@apiParamExample {json} Facebook Messenger example
{
  "from": {
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
    "page_social_id": "", /* ID page mạng xã hội */
    "social_type": 1 /* kiểu mạng xã hội: 1: Facebook, 2: Zalo; 3: Instagram; 4: Youtube*/
  },
  "to": [
    {
      "web_hook": "url_web_hook", /* link webhook báo kết quả gửi */
      "user_social_ids": [] /* danh sách id mạng xã hội của 1 user / 1 mạng xã hội (do FB có nhiều ID / user)*/
      "channel": "MOBIO_EMAIL",
      "body": {
        "content": "Day la html thì phải"   /* nội dung tin nhắn */
      },
      "callbacks": [  /* danh sách object nhận call back từ social */
        {
          "type": 1 /* 1-MessageRead (khách hàng đọc message) */
          "url": "https://yourdomain/callback"  /* URL callback */
        },
        ...
      ]
    },
    ...
  ]
}

"""

*v2.0.0
********************************************************************************************************************************************
"""
@api {post} /sessions/multi-notifies Tạo thông báo dạng gửi đến nhiều người
@apiDescription Tạo thông báo đến nhiều người khi có thông tin thay đổi. <i>Content-type</i>: <b>application/json</b>
@apiGroup Notify
@apiVersion 2.0.0
@apiName CreateMultiNotifies

@apiUse lang
@apiUse 404
@apiUse 405

@apiUse from_param
@apiParam  {String} web_hook Link nhận thông báo kết quả gửi
@apiParam  {to[]} to Danh sách nguồn nhận thông báo

@apiParam (To object) {String} receiver_id UUID của đối tượng nhận.
@apiParam (To object) {Number} receiver_type Kiểu đối tượng nhận. Xác định giá trị cho <code>receiver_id</code>
@apiParam (To object) {String=MOBIO_APP:AppMobile MOBIO_MERCHANT_APP:AppMobile MOBIO_PARTNER_CEM:WebSocket MOBIO_ADMIN_CEM:WebSocket MOBIO_SMS:SMSSender MOBIO_EMAIL:EmailSender MOBIO_APP_NOTIFY: App notify} channel Chức năng được gửi đến.
@apiParam (To object) {Number} notify_type Loại notify thông tin này do client tự đinh nghĩa
@apiParam (To object) {Object} body Nội dung thông báo. Chi tiết nội dung tuỳ từng kiểu notify.
@apiParam (To object) {String} [push_id] Push id đến thiết bị Android hoặc iOS. Required khi <code>channel</code> là <code>AppMobile</code>
@apiParam (To object) {String=ANDROID IOS} [os_push_id] Hệ điều hành của push id. Required khi <code>channel</code> là <code>AppMobile</code>
@apiParamExample {json} Body example
{
  "from": {
    "source": "social-crm",
    "sender_id": "0c214f46-a6d6-47b7-b9ea-282144d0a52c",
    "sender_type": 3
  },
  "to": [
    {
      "web_hook": "https://mobio.vn/adm/v1.0/merchants/<merchant_id>/accounts/<account_id>/receiver_push",
      "receiver_id": "2cf390b5-8953-4c56-b96c-906a1191a9a3",
      "receiver_type": 3,
      "channel": "NOTIFY",
      "notify_type": 1,
      "body": {
        "id": "09f8ee23-713f-424b-ae2e-a839db65c120",
        "page_social_id": "***************",
        "social_type": 1,
        "status": 3,
        "token_name": "Mẹ và bé",
        "title": "Đã ngắt kết nối đến page Facebook: Mẹ và bé",
        "description": "",
        "action": 1000
      }
    }
  ]
}
@apiParamExample {json} App mobile example
{
  "from": {
    "merchant_id": "659a...1297c"   /* uuid merchant yêu cầu gửi tin nhắn */
  },
  "to": [
    {
      "web_hook": "https://mobio.vn/adm/v1.0/merchants/<merchant_id>/accounts/<account_id>/receiver_push",
      "receiver_id": "",
      "receiver_type": "",
      "channel": "MOBIO_APP",
      "push_id": "ALDKKAWIELASDLJFDF29ADKASL0K32L32KLDSAK210",
      "os_push_id": "ANDROID",
      "body": {
        "mapush" /*Mã push*/ : 105,
        "noidunghienthi": {
          "title": "title", <===> key 'alert'
          "body": "Day la push duoc gui tu Notify Manager abc xyz" /*Nội dung hiển thị*/
        },
        "noidungan" /*Nội dung ẩn*/
      }
    },
    ...
  ]
}

@apiParamExample {json} App notify mobile example
{
    "from": {
        "source": "sale",  /* Nguồn yêu cầu gửi tin */
        "merchant_id": "PVBANK",  /* uuid merchant yêu cầu gửi tin nhắn */
        "app_code": "MOBIO",
         "status": 0
    },
    "to": [
    {
        "sent_webhook": "https://...",    /* link webhook nhận thông báo trạng thái gửi */,
        "open_webhook": "http://...",  /* link webhook nhận thông báo khi mail được open */,       
        "profile_id": "48ebedfb-22af-45b3-b7cc-22ef533b6de5",
        "channel": "MOBIO_NOTIFY_APP"
        "to": [
            {"os_push_id": "ANDROID", "push_id": "ALDKKAWIELASDLJFDF29ADKASL0K32L32KLDSAK210"},
            {"os_push_id": "IOS", "push_id": "ALDKKAWIELASDLJFDF29ADKASL0K32L32KLDSAK210"}
        ],
        "body": {
            "alert": {
                "title": "title",     <===> key 'alert'
                "body": "Day la push duoc gui tu Notify Manager abc xyz"      /*Nội dung hiển thị*/
            },
            "badge": 10
        }
    }
    ...
  ]
}

@apiParamExample {json} SMS example
{
  "from": {
    "merchant_id": "659a...1297c"   /* uuid merchant yêu cầu gửi tin nhắn */
  },
  "to": [
    {
      "web_hook": "url_web_hook", /* link webhook báo kết quả gửi sms */
      "phone_number": "**********",   /* số điện thoại cần gửi tin nhắn */
      "channel": "MOBIO_SMS",
      "body": {
        "content": "Day la message" /* nội dung tin nhắn */
      }
    },
    ...
  ]
}
@apiParamExample {json} email example
{
  "from": {
    "sender_id": "<EMAIL>", /* email người gửi */
    "sender_type": 6, /* Kiểu gửi */
    "source": "marketing",  /* nguồn gửi */
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
    "name": "Support Mobio" /* tên người gửi */
  },
  "to": [
    {
      "web_hook": "url_web_hook", /* link webhook báo kết quả gửi email */
      "open_webhook": "http://...",  /* link webhook nhận thông báo khi mail được open */
      "email": "<EMAIL>",  /* email cần gửi đi. Có thể gửi 1 mảng. VD: ["Lộc lá <<EMAIL>>", "Lộc lấp lánh <<EMAIL>>"] */
      "name": "Lộc lá",       /* tên người nhận */
      "cc": ["<EMAIL>"],
      "bcc": ["<EMAIL>"],
      "channel": "MOBIO_EMAIL",
      "body": {
        "subject": "Day la subject",    /* Subject của email */
        "content": "Day la html thì phải"   /* nội dung email */
      }
    },
    ...
  ]
}
@apiParamExample {json} Facebook Messenger example
{
  "from": {
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
    "page_social_id": "", /* ID page mạng xã hội */
    "social_type": 1 /* kiểu mạng xã hội: 1: Facebook, 2: Zalo; 3: Instagram; 4: Youtube*/
  },
  "to": [
    {
      "web_hook": "url_web_hook", /* link webhook báo kết quả gửi */
      "user_social_ids": [] /* danh sách id mạng xã hội của 1 user / 1 mạng xã hội (do FB có nhiều ID / user)*/
      "channel": "MOBIO_EMAIL",
      "body": {
        "content": "Day la html thì phải"   /* nội dung tin nhắn */
      },
      "callbacks": [  /* danh sách object nhận call back từ social */
        {
          "type": 1 /* 1-MessageRead (khách hàng đọc message) */
          "url": "https://yourdomain/callback"  /* URL callback */
        },
        ...
      ]
    },
    ...
  ]
}

"""

*********************************************************************************************************************
************************************************** ĐÁNH DẤU ĐÃ ĐỌC **************************************************
*********************************************************************************************************************
"""
@api {post} /sessions/notifies/<notify_id>/read Đánh dấu đã đọc
@apiDescription Đánh dấu một notify là đã có nhân viên đọc/biết thông tin thay đổi.
@apiGroup Notify
@apiVersion 2.0.0
@apiName MarkReadNotify

@apiUse lang
@apiUse 404
@apiUse 405

@apiParam   (Resource:)     {String}    notify_id   UUID của notify

@apiSuccess     {String}    id          UUID của notify
@apiSuccess     {Number=0-Unread,1-Read}    is_read     Trạng thái của notify

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "id":"ab305af8-5e29-4017-a8e9-a6cd87dc3b77",
    "is_read":1
}
"""

*********************************************************************************************************************
************************************************** ĐÁNH DẤU ĐÃ ĐỌC LIST THÔNG BÁO **************************************************
*********************************************************************************************************************
"""
@api {post} /sessions/notifies/reads Đánh dấu đã đọc list thông báo
@apiDescription Đánh dấu đã đọc list thông báo.
@apiGroup Notify
@apiVersion 2.0.0
@apiName MarkReadsNotifies

@apiUse lang
@apiUse 404
@apiUse 405

@apiParam   (Resource:)     {String}    notify_id   UUID của notify

@apiSuccess     {String}    id          UUID của notify
@apiSuccess     {Number=0-Unread,1-Read}    is_read     Trạng thái của notify

@apiParamExample {json} Body
{
    "notify_ids":["e00bd50e-1618-48fb-98f8-a675dc62bdab"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "data":[{ 
      "id":"ab305af8-5e29-4017-a8e9-a6cd87dc3b77",
      "is_read":1
    }]
}
"""

*********************************************************************************************************************
************************************************** Lay chi tiet thong bao **************************************************
*********************************************************************************************************************
"""
@api {get} /sessions/notifies/<notify_id> Lấy chi tiết notify 
@apiDescription Lấy chi tiết notify.
@apiGroup Notify
@apiVersion 2.0.0
@apiName GetDetailNotify

@apiUse lang
@apiUse 404
@apiUse 405


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "data":{
        "id": "notify_id",
       "source": "JB",
       "receiver_id": "ALDKKAWIELASDLJFDF29ADKASL0K32L32KLDSAK210",
       "os_push_id": "IOS",
       "profile_id": "profile_id",
       "channel": "MOBIO_NOTIFY_APP",
       "state": 0,
       "body":  {
            "alert": {
                "title": "title",     <===> key 'alert'
                "body": "Day la push duoc gui tu Notify Manager abc xyz"      /*Nội dung hiển thị*/
            },
            "badge": 10
        },
       "created_time": "2021-11-24 17:38:17.498820",
       "updated_time": "2021-11-24 17:38:17.498820",
}
}
"""


*********************************************************************************************************************
************************************************** Xoa thong bao **************************************************
*********************************************************************************************************************
"""
@api {delete} /sessions/notifies/<notify_id> Delete notify 
@apiDescription Delete notify.
@apiGroup Notify
@apiVersion 2.0.0
@apiName DeleteNotify

@apiUse lang
@apiUse 404
@apiUse 405

@apiParam   (Resource:)     {String}    notify_id   UUID của notify

@apiSuccess     {String}    id          UUID của notify

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}
"""


*************************************************************************************************************************************************
************************************************** CHECK SERVER CAN BE REQUEST TO SENT MESSAGE **************************************************
*************************************************************************************************************************************************
"""
@api {get} /sessions/notifies/available Kiểm tra server có thể gửi yêu cầu gửi tin hay không
@apiGroup Notify
@apiVersion 1.0.0
@apiName CheckSentAvailable

@apiSuccessExample     {json}    Failed: HTTP/1.1 200 OK
{
    "code": 500,
    "message":"IP: xxx.xxx.xxx.xxx had blocked"
}

@apiSuccessExample     {json}    Success: HTTP/1.1 200 OK
{
    "code": 200,
    "message":"Can sent message"
}
"""



************************************************************************************************************
************************************************** COMMON **************************************************
************************************************************************************************************
"""
@apiDefine to_success
@apiVersion 2.0.0
@apiSuccess     (To object)      {String}    receiver_id      UUID của đối tượng nhận.
<li>Nếu <code>receiver_id</code> là merchant_id thì tất cả nhân viên của nhãn hàng sẽ nhận được thông báo này.</li>
<li>Nếu <code>receiver_id</code> là staff_id thì chỉ có nhân viên này sẽ nhận được thông báo.</li>
@apiSuccess     (To object)      {Number=1-MOBIO,2-MERCHANT,3-STAFF}    receiver_type      Kiểu đối tượng nhận. Xác định giá trị cho <code>receiver_id</code>
@apiSuccess (To object) {String} channel Chức năng được gửi đến
"""

*****************************************************************************************************************************
************************************************** UPDATE CONFIGS MERCHANT **************************************************
*****************************************************************************************************************************
"""
"""

*******to_param *******
"""
@apiDefine to_param
@apiVersion 2.0.1
@apiParam     (To object)      {String}    receiver_id      UUID của đối tượng nhận.
<li>Nếu <code>receiver_id</code> là merchant_id thì tất cả nhân viên của nhãn hàng sẽ nhận được thông báo này.</li>
<li>Nếu <code>receiver_id</code> là staff_id thì chỉ có nhân viên này sẽ nhận được thông báo.</li>
@apiParam     (To object)      {Number=1-MOBIO,2-MERCHANT,3-STAFF}    receiver_type      Kiểu đối tượng nhận. Xác định giá trị cho <code>receiver_id</code>
@apiParam (To object) {String=MOBIO_APP:AppMobile MOBIO_MERCHANT_APP:AppMobile MOBIO_PARTNER_CEM:WebSocket MOBIO_ADMIN_CEM:WebSocket MOBIO_SMS:SMSSender MOBIO_EMAIL:EmailSender} channel Chức năng được gửi đến.
@apiParam (To object) {Number} notify_type Loại notify thông tin này do client tự đinh nghĩa
@apiParam (To object) {Object} body Nội dung thông báo. Chi tiết nội dung tuỳ từng kiểu notify.
@apiParam (To object) {String} [push_id] Push id đến thiết bị Android hoặc iOS. Required khi <code>channel</code> là <code>AppMobile</code>
@apiParam (To object) {String=ANDROID IOS} [os_push_id] Hệ điều hành của push id. Required khi <code>channel</code> là <code>AppMobile</code>
"""

"""
@apiDefine to_param
@apiVersion 2.0.0
@apiParam     (To object)      {String}    receiver_id      UUID của đối tượng nhận.
<li>Nếu <code>receiver_id</code> là merchant_id thì tất cả nhân viên của nhãn hàng sẽ nhận được thông báo này.</li>
<li>Nếu <code>receiver_id</code> là staff_id thì chỉ có nhân viên này sẽ nhận được thông báo.</li>
@apiParam     (To object)      {Number=1-MOBIO,2-MERCHANT,3-STAFF}    receiver_type      Kiểu đối tượng nhận. Xác định giá trị cho <code>receiver_id</code>
@apiParam (To object) {String} channel Chức năng được gửi đến
"""

*******from_param *******
"""
@apiDefine from_param
@apiVersion 2.0.1
@apiParam     (From object)      {String=call-center,retail,social-crm,marketing}    source      Tên nguồn phát sinh event.
@apiParam     (From object)      {String}    sender_id      UUID của đối tượng gửi.
@apiParam     (From object)      {Number=1-MOBIO,2-MERCHANT,3-STAFF,4-CUSTOMER,5-FB_USER,6-EMAIL}    sender_type      Kiểu đối tượng gửi. Xác định giá trị cho <code>sender_id</code>
"""

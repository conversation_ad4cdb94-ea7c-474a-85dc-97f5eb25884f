
**************************************************************************************************************************************
************************************************** LẤY SỐ LƯỢNG MESSAGE THEO THÁNG ***************************************************
**************************************************************************************************************************************
"""
@api {post} /emails/reports/months Lấy số lượng tin theo tháng
@apiDescription Lấy số lượng tin đã được gửi thành công theo tháng
@apiGroup ReportEmail
@apiVersion 2.0.0
@apiName PostReportEmailMonth


@apiUse 405


@apiParam  (Body:) {number=1:Month 2:Day} type Kiểu tìm kiếm.
@apiParam (Body:) {string} from Thời gian bắt đầu lấy dữ liệu (Format: <code>yyy-MM-dd</code>)
@apiParam (Body:) {string} to Thời gian kết thúc lấy dữ liệu (Format: <code>yyy-MM-dd</code>)
@apiParam (Body:) {Array} [list_business_id] Danh sách master campaign muốn lọc. Nếu không gửi lên thì lấy tất cả
@apiParam  (Body:) {Object} sort Chứa các trường muốn sắp xếp : desc hay asc 

@apiParamExample {json} Body
{
  "type": 1,
  "from": "2018-12-01",
  "to": "2019-01-30",
  "list_business_id": [
    "b81c3978-8ef9-4cdb-baca-a0a831086285",
    "90c30744-20a7-458b-abc5-2a7a408265ff",
    ...
  ],
  "sort":{
      "sent_time":"desc",
      "sum":"desc"
  }
}
@apiSuccess (data) {int} [total] Số lượng tổng 
@apiSuccess (data) {string} month Tháng lấy dữ liệu
@apiSuccess (data) {number} sum Số lượng tin gửi thành công theo tháng
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Thành công",
  "data": [
    {
      "sent_time": "01/12/2018",
      "sum": 50000
    },
    {
      "sent_time": "01/01/2019",
      "sum": 56000
    }
  ]
}
"""

**************************************************************************************************************************
************************************************** LẤY CHI TIẾT TIN GỬI **************************************************
**************************************************************************************************************************
"""
@api {post} /emails/reports/details Lấy chi tiết tin gửi
@apiDescription Lấy thông tin chi tiết gửi mail
@apiGroup ReportEmail
@apiVersion 2.0.0
@apiName PostReportEmailDetail


@apiUse 405


@apiParam  {number=1:Month 2:Day} type Kiểu tìm kiếm.
@apiParam  {string} [keyword] email cần filter
@apiParam  {string} from Thời gian bắt đầu lấy dữ liệu (Format: <code>yyy-MM-dd</code>)
@apiParam  {string} to Thời gian kết thúc lấy dữ liệu (Format: <code>yyy-MM-dd</code>)
@apiParam  {Array} list_campaign_id Danh sách chiến dịch muốn lọc.
@apiParam  {Object} sort Chứa các trường muốn sắp xếp : desc hay asc 
@apiParam (Query) {int} per_page Số item tối đa của 1 page 
@apiParam (Query) {string} after token lấy danh sách tiếp theo 
@apiParam (Query) {string} before token lấy danh sách trước 

@apiParamExample {json} Body
{
  "type": 1,
  "keyword": "<EMAIL>",
  "from": "2018-12-01",
  "to": "2019-01-30",
  "list_campaign_id": [
    "767b58a2-6bbe-4d7b-be47-c14cb1d5aadb",
    "f2fbfee7-f97d-406b-b764-2159213e4629",
    ...
  ],
  "sort":{
    "created_time":"desc",
    "to_email":"desc",
    "campaign_id":"desc",
    "business_id":"desc"
  }
}

@apiSuccess (data) {datetime} created_time Thời gian gửi tin
@apiSuccess (data) {string} to_email Tên email được gửi
@apiSuccess (data) {string} [business_id] Master campaign id
@apiSuccess (data) {string} [campaign_id] campaign id được sử dụng để gửi
@apiSuccess (data) {int} total Số lượng tổng 
@apiSuccess (data) {string} [after] token lấy danh sách tiếp theo 
@apiSuccess (data) {string} [before] token lấy danh sách trước 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Thành công",
  "data": [
    {
      "created_time": "2019-01-10T17:00:00Z",
      "to_email": "<EMAIL>",
      "business_id": "92966823-1691-43fa-a1c3-c1b622203d30",
      "campaign_id": "91240793-a923-4e7b-9abe-ee7e4bfd8563"
    },
    ...
  ]
}
"""

**************************************************************************************************************************
*************************************** Export  file excel báo cáo chi tiết email đã gửi  ********************************
**************************************************************************************************************************
"""
@api {post} /emails/reports/details/export Lấy file báo cáo chi tiết tin gửi
@apiDescription Lấy file báo cáo  thông tin chi tiết gửi mail
@apiGroup ReportEmail
@apiVersion 2.0.0
@apiName PostExportEmailDetail


@apiUse 405

@apiParam  {number=1:Month 2:Day} type Kiểu tìm kiếm.
@apiParam  {string} [keyword] email cần filter
@apiParam  {string} from Thời gian bắt đầu lấy dữ liệu (Format: <code>yyy-MM-dd</code>)
@apiParam  {string} to Thời gian kết thúc lấy dữ liệu (Format: <code>yyy-MM-dd</code>)
@apiParam  {Array} list_campaign_id Danh sách chiến dịch muốn lọc.
@apiParam  {Array} list_business_id Danh sách Master Campaign muốn lọc .
@apiParam  {map} mapping_business Key-value id và tên Master Campaign 
@apiParam  {map} mapping_campaigns Key-value  id và tên chiến dịch 
@apiParamExample {json} Body
{
  "type": 1,
  "keyword": "<EMAIL>",
  "from": "2018-12-01",
  "to": "2019-01-30",
  "list_campaign_id": [
    "767b58a2-6bbe-4d7b-be47-c14cb1d5aadb",
    "f2fbfee7-f97d-406b-b764-2159213e4629",
    ...
  ],
  "mapping_business":{
    "6bbe-4d7b-be47-c14cb1d5aadb-as5dg55": "Tên của Master Campaign (business) "
  },
  "mapping_campaigns":{
    "f2fbfee7-f97d-406b-b764-2159213e4629": "Tên của chiến dịch  ",
    "767b58a2-6bbe-4d7b-be47-c14cb1d5aadb": "Tên của chiến dịch"
  }
  
}

@apiSuccessExample {file} Response: HTTP/1.1 200 OK
Trả về file excel chứa thông tin nội dung báo cáo 
"""


**************************************************************************************************************************
*************************************** Export  file excel báo cáo số lượng  email đã gửi  ********************************
**************************************************************************************************************************
"""
@api {post} /emails/reports/months/export Lấy file báo cáo số lượng  tin gửi
@apiDescription Lấy file báo cáo  thông tin số lượng  gửi mail
@apiGroup ReportEmail
@apiVersion 2.0.0
@apiName PostExportEmailCount


@apiUse 405

@apiParam  {number=1:Month 2:Day} type Kiểu tìm kiếm.
@apiParam  {string} [keyword] email cần filter
@apiParam  {string} from Thời gian bắt đầu lấy dữ liệu (Format: <code>yyy-MM-dd</code>)
@apiParam  {string} to Thời gian kết thúc lấy dữ liệu (Format: <code>yyy-MM-dd</code>)
@apiParam  {Array} list_campaign_id Danh sách chiến dịch muốn lọc.
@apiParam  {Array} list_business_id Danh sách Master Campaign muốn lọc .
@apiParam  {map} mapping_business Key-value id và tên Master Campaign 
@apiParam  {map} mapping_campaigns Key-value  id và tên chiến dịch 
@apiParamExample {json} Body
{
  "type": 1,
  "keyword": "<EMAIL>",
  "from": "2018-12-01",
  "to": "2019-01-30",
  "list_campaign_id": [
    "767b58a2-6bbe-4d7b-be47-c14cb1d5aadb",
    "f2fbfee7-f97d-406b-b764-2159213e4629",
    ...
  ],
  "mapping_business":{
    "6bbe-4d7b-be47-c14cb1d5aadb-as5dg55": "Tên của Master Campaign (business) "
  },
  "mapping_campaigns":{
    "f2fbfee7-f97d-406b-b764-2159213e4629": "Tên của chiến dịch  ",
    "767b58a2-6bbe-4d7b-be47-c14cb1d5aadb": "Tên của chiến dịch"
  }
  
}

@apiSuccessExample {file} Response: HTTP/1.1 200 OK
Trả về file excel chứa thông tin nội dung báo cáo 
"""
#!/usr/bin/python
# -*- coding: utf8 -*-

"""
@apiIgnore URI không chính xác. <PERSON><PERSON><PERSON> sang sử dụng api mới (#Sessions:GetSessionStaffs). <PERSON><PERSON><PERSON> thêm tham số <code>merchant_id</code> để lấy danh sách nhân viên đang online của merchant.
@api {get} /merchants/<merchant_id>/sessions/  lấy danh sách các staff đang kết nỗi tới SM
@apiGroup Sessions
@apiVersion 1.0.0
@apiDeprecated URI không chính xác. <PERSON><PERSON><PERSON> sang sử dụng api mới (#Sessions:GetSessionStaffs). G<PERSON><PERSON> thêm tham số <code>merchant_id</code> để lấy danh sách nhân viên đang online của merchant.

@apiUse lang
@apiUse 404
@apiUse 405

@apiSuccess     {Array} staff   Array of staff.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "staffs":[
        {
            "id":"xxxxyyyzzz",
            "staff_name": "<PERSON>",
            "connection_number":1,
            "merchant_id":"xxxxyyyzzz"
        },
        {
            "id":"xxxxyyyzzt",
            "staff_name": "Sơn",
            "connection_number":2,
            "merchant_id":"xxxxyyyzzz"
        }
    ]
}
"""

"""
@apiIgnore URI không chính xác. Đổi sang sử dụng api mới (#Sessions:GetSessionStaffs)
@api {get} /sessions/  lấy danh sách tất cả các staff đang kết nỗi tới SM
@apiGroup Sessions
@apiVersion 1.0.0
@apiDeprecated URI không chính xác. Đổi sang sử dụng api mới (#Sessions:GetSessionStaffs)

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiSuccess     {Array} staff   Array of staff.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "staffs":[
        {
            "id":"xxxxyyyzzz",
            "staff_name": "Chung",
            "connection_number":1,
            "merchant_id":"xxxxyyyzzz"
        },
        {
            "id":"xxxxyyyzzt",
            "staff_name": "Sơn",
            "connection_number":2,
            "merchant_id":"xxxxyyyzzz"
        }
    ]
}
"""


"""
@apiIgnore URI không chính xác. Đổi sang sử dụng api mới (#Sessions:CheckStaffOn)
@api {get} /sessions/<staff_id>  kiểm tra một staff có online hay ko
@apiGroup Sessions
@apiVersion 1.0.0
@apiDeprecated URI không chính xác. Đổi sang sử dụng api mới (#Sessions:CheckStaffOn)

@apiUse lang
@apiUse 404
@apiUse 405

@apiSuccess     {Array} staff   Array of staff.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "staff_id":"xxxxyyyzzz",
    "status": :0  // nếu online status==1 nếu offline status=0    
}
"""


"""
@apiIgnore URI không chính xác. Đổi sang sử dụng api mới (#Sessions:GetMerchants)
@api {get}  /merchants/sessions/  lấy danh sách tất cả các merchant có staff online.
@apiGroup Sessions
@apiVersion 1.0.0
@apiDeprecated URI không chính xác. Đổi sang sử dụng api mới (#Sessions:GetMerchants)

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiSuccess     {Array} merchant   Array of merchant.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "merchants":[
        {
            "id":"xxxxyyyzzz",
            "merchant_name": "May",
            "staff_number":3,            
        },
        {
            "id":"xxxxyyyzzz",
            "merchant_name": "Babe Shop",
            "staff_number":1,
        }
    ]
}
"""

"""
@apiIgnore URI không chính xác. Đổi sang sử dụng api mới (#Sessions:GetMerchantNotifyByFunction)
@api {get}  /merchants/<merchant_id>/sessions/notifies/{function}  lấy danh sách các thông báo của merchant theo chức năng.
@apiGroup Sessions
@apiVersion 1.0.0
@apiDeprecated URI không chính xác. Đổi sang sử dụng api mới (#Sessions:GetMerchantNotifyByFunction)

@apiUse lang
@apiUse 404
@apiUse 405
@apiUse paging

@apiSuccess     {Array} merchant   Array of merchant.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
    "notifies":[
        {
            "from": { 
            "source": "CRM",
            "sender_id": "xxxxxyyyyzzzz",
            "sender_type": 2
            },
            "to": {
            "receiver_id": "xxxxxyyyyzvvvzzz",
            "receiver_type": 2,
            "function": "NOTIFY",
            }
            "is_read": true,
            "body": {
                // nội dung body
            }
        }
        ,
        {
            "from": { 
            "source": "CRM",
            "sender_id": "xxxxxyyyyzzzz",
            "sender_type": 2
            },
            "to": {
            "receiver_id": "xxxxxyyyyzvvvzzz",
            "receiver_type": 2,
            "function": "NOTIFY",
            }
            "is_read": false,
            "body": {
                // nội dung body
            }
        }
    ]
}
"""

"""
@apiIgnore URI không chính xác. Đổi sang sử dụng api mới (#Sessions:MarkReadNotify)
@api {post}  /merchants/<merchant_id>/sessions/notifies/{notify_id}  đánh giấu một thông báo là đã đọc.
@apiGroup Sessions
@apiVersion 1.0.0
@apiDeprecated URI không chính xác. Đổi sang sử dụng api mới (#Sessions:MarkReadNotify)

@apiUse lang
@apiUse 404
@apiUse 405

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "C":"001",
}
"""

"""
@apiIgnore URI không chính xác. Đổi sang sử dụng api mới (#Sessions:CreateNotify)
@api {post}  /merchants/<merchant_id>/sessions/notifies/  đẩy một thông báo lên SM.
@apiGroup Sessions
@apiVersion 1.0.0
@apiDeprecated URI không chính xác. Đổi sang sử dụng api mới (#Sessions:CreateNotify)

@apiUse lang
@apiUse 404
@apiUse 405

@apiParam   (Body:)  {Object} [from] đối tượng gửi
@apiParam   (Body:)  {Object} [to] đối tượng nhận
@apiParam   (Body:)  {Object} [body] nội dung gửi

@apiParamExample {json} Example
{
	"from": { 
		"source": "[SubSystemKey]",
		"sender_id": "[merchant_id/staff_id/customer_id/social_user_id]",
		"sender_type": number
	},
	"to": {
		"receiver_id": "[merchant_id/staff_id/customer_id/social_user_id]",
		"receiver_type": number,
		"function": "NOTIFY",
	}
	"body": {
		// nội dung body
	}
}


@apiSuccessExample {json} Success
HTTP/1.1 200 OK
{
  "C": "001",
  "D": "Successful"
}
"""
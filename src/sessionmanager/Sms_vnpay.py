""" Author: TiepTh
    Company: MobioVN
    Date created: 14/02/2020
"""

==================================================================================================================================
************************************************** Webhook  **************************************************
* v2.0.0                                                                                                                         *
**********************************************************************************************************************************
"""
@api {POST} /nm/webhook/api/v2.0/webhook/channel/<channel>/batch Nhận thông tin message vnpay
@apiVersion v2.0.0
@apiName Webhook_message_vnpay
@apiGroup Webhook

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Resources) {string=email sms facebook zalo app} channel Kênh gửi tin

@apiParam (Body:)   {string}       id                      id của tin nhắn
@apiParam (Body:)   {int}          status                  Trạng thái của tin nhắn: 1 - thành công, 0 - thất bại 
@apiParam (Body:)   {string}       reason                  Lý do không gửi được tin nhắn
@apiParam (Body:)   {string}       type                    Loại event: sent - gửi, open - mở 

@apiParamExample {json} Body Example
{
    "data": [
        {
            "id": "id",
            "status": 1,
            "reason": "",
            "type": "sent" 
        }
    ]
}
"""


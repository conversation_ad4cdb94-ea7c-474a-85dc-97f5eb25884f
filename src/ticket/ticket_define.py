#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 05/03/2022
"""
"""
@apiDefine Response

@apiSuccess {Number} code       Response status
@apiSuccess {String} message    Response message
"""
"""
@apiDefine ResponseDashboardExport

@apiSuccess {Number} code       Response status
@apiSuccess {String} message    Response message
@apiSuccess {Object} data        Thông tin export


@apiSuccess (data)   {Number}   type            Kiểu xuất file.
                                                <ul>
                                                    <li><code>1</code>: download</li> 
                                                    <li><code>2</code>: sent mail</li> 
                                                </ul>
@apiSuccess (data)   {String}   [link]          Link download file excel.
@apiSuccess (data)   {Number}   [limit]         Giới hạn bản ghi chuyển sang kiểu xuất file qua mail.


@apiSuccessExample Response link:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "type": 1,
        "link": "https://api-p.test1.mobio.vn/ticket/static/export_excel/Bao_cao_20210921_0832AM.xlsx"
    }
}

@apiSuccessExample Response sent mail:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "type": 2,
        "limit": 1000
    }
}
"""
"""
@apiDefine BodyFieldDynamic
@apiParam   (Body:)    {String}         field_name        Tên field mới
@apiParam   (Body:)     {Number=1:Int,2:String,3:DateTime}    field_property            Kiểu dữ liệu của field.
@apiParam   (Body:)     {String}    display_type            Kiểu hiển thị dữ liệu.
                                                            <ul>
                                                                <li><code>single_line: Single Line</code></li>
                                                                <li><code>multi_line: Multi Line</code></li>
                                                                <li><code>dropdown_single_line: Dropdown Single Line</code></li>
                                                                <li><code>dropdown_multi_line: Dropdown Multi Line</code></li>
                                                                <li><code>radio: Radio</code></li>
                                                                <li><code>checkbox: Checkbox</code></li>
                                                                <li><code>date_picker: Date Picker</code></li>
                                                            </ul>
@apiParam   (Body:)     {Array}    [data_selected]         Dữ liệu hiển thị.
@apiParam   (Body:)     {String='dd/mm','dd/mm/yyyy','dd/mm/yyyy hh:mm','dd-mm-yyyy','dd-mm-yyyy hh:mm','mm/dd/yyyy',
                        'mm/dd/yyyy hh:mm'}   [format] Data format.
@apiParam   (Body:)     {String}    description            Mô tả ngắn.
@apiParam   (Body:)     {String}    group                  Group của field
@apiParam   (Body:)     {String="object", "text"}          [type_save_data="text"]         Kiểu lưu dữ liệu
                                                        
@apiParam   (Body:)     {Bool}    display_in_form         Hiển thị field này ở form chọn dữ liệu?
@apiParam   (Body:)     {Bool}    display_detail          Hiển thị field này ở chi tiết ticket?
@apiParam   (Body:)     {Bool}    display_in_form_add     Hiển thị field này ở popup tạo mới ticket?
@apiParam   (Body:)     {Bool}    display_in_form_update  Hiển thị field này ở popup sửa ticket?
@apiParam   (Body:)     {Bool}    display_in_list_field   Hiển thị field này ở popup chọn thông tin hiển thị trong danh sách ticket?
@apiParam   (Body:)     {Bool}    display_in_filter       Hiển thị field này ở bộ lọc ticket?
@apiParam   (Body:)     {Bool}    display_in_import_file  Hiện thị field này khi import file?
@apiParam   (Body:)     {Bool}    disable_remove_form_add  Không cho phép xóa ở form thêm
@apiParam   (Body:)     {Bool}    disable_required_form_add  Không cho phép required ở form thêm
@apiParam   (Body:)     {Bool}    disable_remove_form_update  Không cho phép xóa ở form sửa
@apiParam   (Body:)     {Bool}    disable_required_form_update  Không cho phép required ở form sửa
@apiParam   (Body:)     {Bool}    [allow_use_color]  Cho phép sử dụng màu chỉ áp dụng cho
@apiParam   (Body:)     {Bool}    [status_display_color]  Trạng thái hiển thị màu
@apiParam   (Body:)     {Bool}    [allow_change_position]  Cho phép thay đổi vị trí
@apiParam   (Body:)     {Bool}    [allow_edit_name]  Cho phép thay đổi tên
@apiParam   (Body:)     {Bool}    [allow_edit_description]  Cho phép thay đổi mô tả 
@apiParam   (Body:)     {Bool}    [allow_edit_position_display]  Cho phép thay đổi vị trí hiển thị
@apiParam   (Body:)     {Bool}    [allow_add_data_select]  Cho phép thêm giá trị
@apiParam   (Body:)     {Bool}    [allow_change_position]  Cho phép thay đổi vị trí
@apiParam   (Body:)     {Bool}    [permission_remove_field]  Quyền xoá field
@apiParam   (Body:)     {Array}    [data_select_position_pin]  Vị trí ghim của các data select



@apiParam (data_selected)   {string}       id                           Định danh của giá trị chọn
@apiParam (data_selected)   {String}    value                           Giá trị 
@apiParam (data_selected)   {String}    color                           Màu 
@apiParam (data_selected)   {int}       enable                          Trường này được bật hay không ?
@apiParam (data_selected)   {int}       order                           Vị trí
@apiParam (data_selected)   {Boolean}   allow_change_order=true         Quyền được thay đổi vị trí
                                                                        <ul>
                                                                            <li><code>true</code>: được quyền thay đổi vị trí</li>
                                                                            <li><code>false</code>: không được quyền thay đổi vị trí</li>
                                                                        </ul>
@apiParam (data_selected)   {Boolean}   enable_data_color=true          Có được bật tắt màu hay không ?
                                                                        <ul>
                                                                            <li><code>true</code>: bật hiển thị màu</li>
                                                                            <li><code>false</code>: tắt hiển thị màu</li>
                                                                        </ul>
@apiParam (data_selected)   {Boolean}   allow_edit_value=true           Có được thay đổi giá trị không?
                                                                        <ul>
                                                                            <li><code>true</code>: có</li>
                                                                            <li><code>false</code>: không</li>
                                                                        </ul>
@apiParam (data_selected)   {Boolean}   allow_remove_value=true         Có được xoá giá trị không?
                                                                        <ul>
                                                                            <li><code>true</code>: có/li>
                                                                            <li><code>false</code>: không</li>
                                                                        </ul>
@apiParam (data_selected)   {Boolean}   allow_edit_status_display_value=true         Có được thay đổi quyền hiển thị của giá trị không?
                                                                                    <ul>
                                                                                        <li><code>true</code>: có</li>
                                                                                        <li><code>false</code>: không</li>
                                                                                    </ul>
@apiParam (data_selected)   {Int}   status                              Trạng thái của giá trị
                                                                        <ul>
                                                                            <li><code>1</code>: hoạt động</li>
                                                                            <li><code>-1</code>: xoá</li>
                                                                        </ul>
@apiParam (data_selected)   {String}   [translate_key]                     Translate_key của value
"""

"""
@apiDefine ParamExampleBodyDynamic
@apiParamExample [json] Body example text single line:
{
  "field_name": "field 1",
  "group": "dynamic",
  "field_property": 1,
  "type_save_data": "text",
  "display_type": "single_line",
  "description": "mo ta ngan cua field",
  "display_in_form": true,
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_list_field": true,
  "display_in_filter": false,
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true
}

@apiParamExample [json] Body example text multi line:
{
  "field_name": "field 1",
  "group": "dynamic",
  "field_property": 1,
  "type_save_data": "text",
  "display_type": "multi_line",
  "display_in_form": true,
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_list_field": true,
  "display_in_filter": false,
  "description": "mo ta ngan cua field",
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true
}

@apiParamExample [json] Body example dropdown single line:
{
  "field_name": "field 1",
  "group": "dynamic",
  "field_property": 1,
  "type_save_data": "text",
  "display_type": "dropdown_single_line",
  "data_selected": [
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "color": "#C1C1C1",
        "enable": 1,
        "enable_data_color": false,
        "id": 1,
        "order": 0,
        "status": 1,
        "translate_key": "i18n_priority_level_urgent",
        "value": "urgent"
    },
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "color": "#C1C1C1",
        "enable": 1,
        "enable_data_color": false,
        "id": 2,
        "order": 1,
        "status": 1,
        "translate_key": "i18n_priority_level_high",
        "value": "high"
    },
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "color": "#C1C1C1",
        "enable": 1,
        "enable_data_color": false,
        "id": 3,
        "order": 2,
        "status": 1,
        "translate_key": "i18n_priority_level_medium",
        "value": "medium"
    },
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "enable": 1,
        "enable_data_color": false,
        "id": 4,
        "order": 3,
        "status": 1,
        "translate_key": "i18n_priority_level_low",
        "value": "low"
    }
    ],
  "display_in_form": true,
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_list_field": true,
  "display_in_filter": false
  "description": "mo ta ngan cua field",
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true,
  "allow_use_color": true,
  "data_select_position_pin": [],
  "type_save_data": "object",
  "allow_use_color": True,  # Cho phép sử dùng màu
  "status_display_color": True,
  "allow_change_position": True,
  "allow_add_data_select": True,
  "permission_remove_field": True
}

@apiParamExample [json] Body example dropdown multi line:
{
  "field_name": "field 1",
  "group": "dynamic",
  "field_property": 1,
  "type_save_data": "text",
  "display_type": "dropdown_multi_line",
  "data_selected": [
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "color": "#C1C1C1",
        "enable": 1,
        "enable_data_color": false,
        "id": 1,
        "order": 0,
        "status": 1,
        "translate_key": "i18n_priority_level_urgent",
        "value": "urgent"
    },
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "color": "#C1C1C1",
        "enable": 1,
        "enable_data_color": false,
        "id": 2,
        "order": 1,
        "status": 1,
        "translate_key": "i18n_priority_level_high",
        "value": "high"
    },
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "color": "#C1C1C1",
        "enable": 1,
        "enable_data_color": false,
        "id": 3,
        "order": 2,
        "status": 1,
        "translate_key": "i18n_priority_level_medium",
        "value": "medium"
    },
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "enable": 1,
        "enable_data_color": false,
        "id": 4,
        "order": 3,
        "status": 1,
        "translate_key": "i18n_priority_level_low",
        "value": "low"
    }
    ],
  "display_in_form": true,
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_list_field": true,
  "display_in_filter": false,
  "description": "mo ta ngan cua field",
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true,
  "data_select_position_pin": []
  "allow_use_color": True,  # Cho phép sử dùng màu
  "status_display_color": True,
  "allow_change_position": True,
  "allow_add_data_select": True,
  "permission_remove_field": True
}

@apiParamExample [json] Body example radio:
{
  "field_name": "field 1",
  "group": "dynamic",
  "field_property": 1,
  "type_save_data": "text",
  "display_type": "radio",
  "data_selected": [
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "color": "#C1C1C1",
        "enable": 1,
        "enable_data_color": false,
        "id": 1,
        "order": 0,
        "status": 1,
        "translate_key": "i18n_priority_level_urgent",
        "value": "urgent"
    },
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "color": "#C1C1C1",
        "enable": 1,
        "enable_data_color": false,
        "id": 2,
        "order": 1,
        "status": 1,
        "translate_key": "i18n_priority_level_high",
        "value": "high"
    },
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "color": "#C1C1C1",
        "enable": 1,
        "enable_data_color": false,
        "id": 3,
        "order": 2,
        "status": 1,
        "translate_key": "i18n_priority_level_medium",
        "value": "medium"
    },
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "enable": 1,
        "enable_data_color": false,
        "id": 4,
        "order": 3,
        "status": 1,
        "translate_key": "i18n_priority_level_low",
        "value": "low"
    }
    ],
  "display_in_form": true,
  "description": "mo ta ngan cua field",
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true,
  "data_select_position_pin": [],
  "allow_use_color": True,  # Cho phép sử dùng màu
  "status_display_color": True,
  "allow_change_position": True,
  "allow_add_data_select": True,
  "permission_remove_field": True
}

@apiParamExample [json] Body example checkbox:
{
  "field_name": "field 1",
  "field_property": 1,
  "type_save_data": "text",
  "display_type": "checkbox",
  "group": "dynamic",
  "data_selected": [
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "color": "#C1C1C1",
        "enable": 1,
        "enable_data_color": false,
        "id": 1,
        "order": 0,
        "status": 1,
        "translate_key": "i18n_priority_level_urgent",
        "value": "urgent"
    },
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "color": "#C1C1C1",
        "enable": 1,
        "enable_data_color": false,
        "id": 2,
        "order": 1,
        "status": 1,
        "translate_key": "i18n_priority_level_high",
        "value": "high"
    },
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "color": "#C1C1C1",
        "enable": 1,
        "enable_data_color": false,
        "id": 3,
        "order": 2,
        "status": 1,
        "translate_key": "i18n_priority_level_medium",
        "value": "medium"
    },
    {
        "allow_change_order": false,
        "allow_edit_status_display_value": false,
        "allow_edit_value": false,
        "allow_remove_value": false,
        "enable": 1,
        "enable_data_color": false,
        "id": 4,
        "order": 3,
        "status": 1,
        "translate_key": "i18n_priority_level_low",
        "value": "low"
    }
    ],
  "display_in_form": true,
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_list_field": true,
  "display_in_filter": false,
  "description": "mo ta ngan cua field",
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true,
  "data_select_position_pin": [],
  "allow_use_color": True,  # Cho phép sử dùng màu
  "status_display_color": True,
  "allow_change_position": True,
  "allow_add_data_select": True,
  "permission_remove_field": True
}

@apiParamExample [json] Body example date_picker:
{
  "field_name": "field 1",
  "group": "dynamic",
  "type_save_data": "text",
  "field_property": 1,
  "display_type": "date_picker",
  "format": "dd/mm/yyyy",
  "display_in_form": true,
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_list_field": true,
  "display_in_filter": false,
  "description": "mo ta ngan cua field",
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true
}
"""

"""
@apiDefine TicketAutomationBodyExampleConditionOther
@apiParamExample {json} Body Example: Điều kiện khác
{
    1. Kiểu tạo ticket:
    {
        "code": "other_type_create",
        "value": 1      # 1: thủ công, 2: tự động
    }
    2. Kiểu ticket:
    {
        "code": "other_type_ticket",
        "type": "have_info", # have_info có thông tin, not_have_info không có thông tin
        "value": []    
    }
    3. File đính kèm:
    {
        "code": "other_media",
        "type": "have_info/not_have_info",
    }
    4. Mức độ ưu tiên:
    {
        "code": "other_priority_level",
        "type": "have_info/not_have_info",
        "value": []
    }
    5. Nguồn ghi nhận ticket
    {
        "code": "other_source",
        "type": "have_info/not_have_info",
        "value": []
    }
    
    6. Sản phẩm
    {
        "code": "other_product",
        "type": "have_info/not_have_info",
        "value": []
    }
    
    7. Team phụ trách
    {
        "code": "other_team",
        "type": "have_info/not_have_info",
        "value": [ ]
    }
    
    8. Ticket owner
    {
        "code": "other_ticket_owner",
        "type": "have_info/not_have_info",
        "value": [ ]
    }
    
    9. Ticket supporter
    {
        "code": "other_ticket_supporter",
        "type": "have_info/not_have_info",
        "value": [ ]
    }
    
    10. Thời gian cập nhật ticket
    {
        "code": "other_time_update_ticket",
        "type": "",
        "value_from": ""
        "value_to": ""
    type: 
    before_time: trước thời gian 
    after_time: sau thời gian 
    in_time: vào thời gian
    between_time: trong khoảng thời gian
    // ngoài "trong khoảng thời gian" thì lấy theo giá trị "value_from"
    }
    
    11. Thời gian tạo ticket
    {
        "code": "other_time_create_ticket",
        "type": "",
        "value_from": ""
        "value_to": ""
    type: 
    before_time: trước thời gian
    after_time: sau thời gian
    in_time: vào thời gian
    between_time: trong khoảng thời gian
    // ngoài "trong khoảng thời gian" thì lấy theo giá trị "value_from"
    }
    
    12. Trạng thái xử lý
    {
        "code": "other_status_process_ticket",
        "type": "have_info/not_have_info",
        "value": []
    }
    
    13. Trường tùy biến của ticket
    13.1 Date picker
        type: 
            in_time: vào thời gian
            before_time: trước thời gian
            after_time: sau thời gian
            between_time: trong khoảng thời gian
            have_info: có thông tin
            not_have_info: chưa có thông tin
        {
            "code": "other_dynamic_field",
            "display_type": "date_picker",
            "field_key": "_dyn_date_dd_mm_123",
            "format": "dd/mm",
            "type": "",
            "value": ""
        }
    13.2 Multiple line text chữ, single line text chữ
        type:
            have_content: có nội dung này
            not_have_content: không có nội dung này
            have_info: có thông tin  // value: True
            not_have_info: không có thông tin   // value: True
        {
            "code": "other_dynamic_field",
            "display_type": "multi_line",
            "field_key": ""
            "field_property": 2,
            "type": ‘"
            "value": ""
        }
    13.3 Multiple line text số, single line text số
        type:
            eq_vlaue: bằng
            gte_value: lớn hơn hoặc bằng
            gt_value: lớn hơn 
            lte_value: nhỏ hơn hoặc bằng   
            lt_value: nhỏ hơn 
            in_value: giữa  
            have_info: có thông tin  // value: True
            not_have_info: không có thông tin   // value: True
        {
            "code": "other_dynamic_field",
            "display_type": "multi_line",
            "field_property": 1,
            "type": ‘"
            "value_from": ""
            "value_to":
        }
    13.4 Dropdown single/multi select, radio/check box
    Type:
        have_any_content: có bất kì nội dung này
        not_have_any_content: không có nội dung này
        have_info: có thông tin  // value: True
        not_have_info: không có thông tin   // value: True
        {
            "code": "other_dynamic_field",
            "display_type": "radio",
            "field_key": "",
            "type": ‘"
            "value": [ ]
        }
    14. Lĩnh vực hoạt động
    {
        "code": "other_active_area",
        "type": "have_info/not_have_info",
        "value": [ ]
    }
    
    15. Quốc gia
        type:
            have_content: có nội dung này
            not_have_content: không có nội dung này
            have_info: có thông tin  // value: True
            not_have_info: không có thông tin   // value: True
        {
            "code": "other_nation",
            "type": ‘"
            "value": ""
        }
    
    16. Tỉnh thành
        {
            "code": "other_province",
            "type": "have_info/not_have_info",
            "value": []
        }
    
    17. Vùng miền
        {
            "code": "other_region",
            "type": "have_info/not_have_info",
            "value": [ ]
        }

}
"""

"""
@apiDefine TicketAutomationBodyExampleConditionTrigger
@apiParamExample {json} Body Example: Điều kiện phát sinh event
{
    1. Với Ticket được tạo mới
    {
        "code": "trigger_ticket_created_new",
        "value": True
    }
    
    2. Thay đổi mức độ ưu tiên
    {
        "code": "trigger_priority_level_updated",
        "from_ids": [], # Danh sách ID mức độ ưu tiên nguồn
        "to_ids": [] # Danh sách ID mức độ ưu tiên đích
    }
    
    3. Thay đổi kiểu ticket
    {
        "code": "trigger_type_ticket_updated",
        "from_ids": [], # Danh sách ID kiểu ticket nguồn
        "to_ids": [] # Danh sách ID kiểu ticket đích
    }
    4. Trạng thái xử lý thay đổi
    {
        "code": "trigger_status_process_updated",
        "from_ids": [], # Danh sách ID trạng thái xử lý Ticket nguồn
        "to_ids": []  # Danh sách ID trạng thái xử lý Ticket đích
    }
    5. Ticket owner thay đổi 
    {
        "code": "trigger_ticket_owner_updated",
        "from": {
            "team_id": "",
            "account_id": ""
        },
        "to": {
            "team_id": "",
            "account_id": ""
        }
    }
    6. Thay đổi người hỗ trợ của Ticket
    {
        "code": "trigger_ticket_supporter_updated",
        "value": "ticket_add_supporter"
        + ticket supporter đc gán vào ticket "ticket_add_supporter"
        + ticket supporter bị gỡ khỏi ticket: "ticket_remove_supporter"
    }
    7. Profile được gán vào ticket
    {
        "code": "trigger_profile_added_to_ticket",
        "value": True
    }
    8. Công ty được gán vào ticket
    {
        "code": "trigger_company_added_to_ticket",
        "value": True
    }
    9. Sản phẩm được gán vào ticket
    {
        "code": "trigger_product_added_to_ticket",
        "value": True
    }
    10. Công việc liên quan được gán vào ticket
    {
        "code": "trigger_task_added_to_ticket",
        "value": True
    }
    11. Phát sinh ghi chú 
    {
        "code": "trigger_note_incurred",
        "value": ["general", "email", "call", "meeting"], 
        "result_call": []# Danh sách các loại ghi chú phát sinh. Phần này có thể lấy từ API của ghi chú.
        "result_meeting": []
    }
    12. Khách hàng gửi email liên quan tới ticket
    {
        "code": "trigger_customer_reply_email",
        "value": True
    }
    
    13. Số lượng trường thông tin ticket cập nhật
    {
        "code": "trigger_number_field_updated",
        "value": []  # Danh sách các field cập nhật,
        "number_field": # số lượng field
    }
    14. Trường tùy biến thay đổi
    14.1 Dạng datetime
        {
            "code": "trigger_dynamic_field",
            "display_type": "date_picker",
            "field_key": "_dyn_dd_mm_1631181457112",
            "format": "dd/mm", # Định dạng datetime
            "update_type": "", # Kiểu cập nhật thông tin trường tuỳ biến. add_value, delete_value, change_value
            "added_value": {
                "type": "any_value", # any_value Bất kỳ giá trị nào hoặc giá trị đang có, between_value nằm trong khoảng được chọn
                "value_from": "",
                "value_to": ""
            },
            "deleted_value": {
                "type": "any_value", # any_value Bất kỳ giá trị nào hoặc giá trị đang có, between_value nằm trong khoảng được chọn
                "value_from": "",
                "value_to": ""
            },
            "update_type": "change_value"
        }
    14.2 Single line text so, multi line text so
        {
            "code": "trigger_dynamic_field",
            "display_type": "single_line",
            "field_key": "_dyn_single_line_text_so_1631181457112",
            "field_property": 1
            "update_type": "", 
            "added_value": {
                "type": "any_value", 
                "value_from": "",
                "value_to": ""
            },
            "deleted_value":  {
                "type": "any_value",
                "value_from": "",
                "value_to": ""
            },
            type: 
                "any_value": Bất kì giá trị nào,
                "eq_value": Bằng,
                "gte_value": Lớn hơn hoặc bằng,
                "lte_value": Nhỏ hơn hoặc bằng,
                "gt_value": Lớn hơn,
                "lt_value": Nhỏ hơn,
                "between_value": Giữa
        }
    14.3 Single line text chu
        {
            "code": "trigger_dynamic_field",
            "display_type": "single_line",
            "field_key": "_dyn_single_line_text_chu_1631181457112",
            "field_property": 2,
            "added_value": {
                "type": "any_value", # at_least_one_checked_value: 1 trong những giá trị được chọn, any_value: Bất kỳ giá trị nào.
                "values": []
            },
            "deleted_value": {
                "type": "any_value", # at_least_one_checked_value: 1 trong những giá trị được chọn, any_value: Bất kỳ giá trị nào.
                "values": []
            },
            "update_type": "change_value"
        }
    14.4 Multiple line text chữ
        {
            "code": "trigger_dynamic_field",
            "display_type": "multi_line",
            "field_key": "_dyn_multi_line_text_chu_1631181457112",
            "field_property": 2,
            "added_value": {
                "type": "any_value",
                "values": []
            },
            "deleted_value":  {
                "type": "any_value",
                "values": []
            },
            "update_type": "change_value"
        type: 
        - add
        any_value: Bất kì giá trị nào/ giá trị đang có.
        at_least_one_checked_value: Ít nhất 1 trong những giá trị được chọn.
        all_checked_value: Rất cả giá trị được chọn
        - delete
        at_least_one_value_exist: ít nhất 1 giá trị đang có
        all_value_exist: tất cả giá trị đang có
        at_least_one_checked_value: ít nhất 1 trong những giá trị được chọn
        all_checked_value: tất cả giá trị được chọn
        }
    14.5 Dropdown single select, radio
        {
            "code": "trigger_dynamic_field",
            "display_type": "radio/dropdown_single_line",
            "field_key": "_dyn_radio_1631181457112",
            "added_value": {
                "type": "any_value",
                "values": []
            },
            "deleted_value":  {
                "type": "any_value",
                "values": []
            },
            "update_type": "change_value"
            type: 
            any_value: bất kì giá trị nào/ giá trị đang có,
            at_least_one_checked_value: một trong những giá trị được chọn
        }
    14.6 Dropdown multi select, checkbox
        {
            "code": "trigger_dynamic_field",
            "display_type": "checkbox/dropdown_multi_line",
            "field_key": "_dyn_check_box_1631181457112",
            "added_value": {
                "type": "any_value",
                "values": []
            },
            "deleted_value":  {
                "type": "any_value",
                "values": []
            },
            "update_type": "change_value"
            type: 
            - add
            any_value: bất kì giá trị nào/ giá trị đang có
            at_least_one_checked_value: ít nhất 1 trong những giá trị được chọn
            all_checked_value: tất cả giá trị được chọn
            - delete
            at_least_one_value_exist: ít nhất 1 giá trị đang có
            all_value_exist: tất cả giá trị đang có
            at_least_one_checked_value: ít nhất 1 trong những giá trị được chọn
            all_checked_value: tất cả giá trị được chọn: all_checked_value
        }
    15. Đơn hàng được gán vào ticket
    {
        "code": "trigger_deal_added_to_ticket",
        "value": True
    }
    16. Ticket không thay đổi trạng thái trong 1 khoảng thời gian
    {
        "code": "trigger_ticket_status_didnt_change",
        "value" : 5
    }
}
"""

"""
@apiDefine TicketAutomationBodyExampleAction
@apiParamExample {json} Body Example: Hành động
{
    1. Phân công cho team:
        {
            "code": "action_assign_team"
            "team_id": ""
        }
    2. Gán ticket owner:
        {
            "code": "action_assign_ticket_owner"
            "account_id": [],
            "team_id": []    // ko bắt buộc   
        }
    3. Gán ticket supporter:
        {
            "code": "action_add_ticket_supporter"
            "account_id": []
        }
    7. Thay đổi mức độ ưu tiên
        {
            "code": "action_update_priority_level"
            "value": ""
        }
    8. Thay đổi kiểu ticket
        {
            "code": "action_update_type_ticket"
            "value": ""
        }
    9. Thay đổi trạng thái xử lý
        {
            "code": "action_update_status_process"
            "value": ""
        }
    10. Thêm tag phân loại công việc
        {
            "code": "action_add_tag"
            "value": ""
        }
    11. Gửi thông báo nội bộ
        {
            "code": "action_send_notify_internal",
            "channel": [
                {
                    "type_channel": "email",
                    "content": "",
                    "receiver": [
                        {
                            "account": "ticket_owner",
                            "value": True
                        },
                        {
                            "account": "ticket_supporter",
                            "value": True
                        },
                        {
                            "account": "account_other",
                            "value": [ ]	// id
                        },
                        {
                            "account": "email_other",
                            "value": []	//email
                        }
                    ]
                },
                {
                    "type_channel": "push_notification",
                    "content": "",
                    "receiver": [
                        {
                            "account": "ticket_owner",
                            "value": True
                        },
                        {
                            "account": "ticket_supporter",
                            "value": True
                        },
                        {
                            "account": "account_other",
                            "value": [ ]	// id
                        }
                    ]
                }
            ]
        }
    11. Tạo công việc
        {
            "code": "action_add_task",
            "name": "Tiêu đề của Task *|NAME|*",
            "describe": "Nhập nội dung của Task",
            "manager": "ticket_owner", // Người được giao  
            "due_date": {
                "unit": "day", // Đơn vị thời hạn hoàn thành
                "value": 1 // Số ngày
            }
            "notify": {
                "type": "NO_REMINDER",
                "time_value": "",
                "unit": "minute"   //or hour/day
            }
            //note:
                "type": "Loại cấu hình"
                        + NO_REMINDER: Không nhắc nhở
                        + ADVANCE_REMINDER: Nhắc nhở trước
                        + TIMELY_REMINDER: Nhắc nhở đúng thời hạn
                        + OPTION: Tùy chọn
                "time_value": "Thời gian cấu hình"
                                "Định dạng": "%Y-%m-%dT%H:%mZ"
                "unit" :Đơn vị của thời gian cấu hình. Áp dụng với type: ADVANCE_REMINDER
                                                                            
            "priority_level": "",      // Mức độ ưu tiên    
            "type": "",      //Kiểu công việc
            "dynamic_fields": [
                {
                    "field_key" : "name",
                    "replace" : "*|NAME|*"
                }
            ]
        }
    12. Thêm bình luận
    13. Cập nhật giá trị trường tùy biến dynamic
    13.1 Date_picker
        {
            "code": "action_update_dynamic_field"
            "field_key": "_dyn_dd_mm_yyyy_01234",
            "display_type": "date_picker",
            "value": ""
        }
    13.2 Multi_line text số/ multi line text chữ
        type:
            all_value_exist: tất cả giá trị đang có           // value: True
            all_checked_value: tất cả giá trị được chọn   //value: [ ]
        {
            "code": "action_update_dynamic_field"
            "field_key": "_dyn_multi_line_text_so_01234",
            "display_type": "multi_line",
            "field_property": 1     // 1: so, 2: chu
            "update_type": add_value/delete_value/change_value",
            "deleted_value": {
                "type": "",
                "values" : []
            },
            "added_value": {
                "type": "",
                "values": []
            }
        }
    13.3 Single line text số/ single line text chữ
        {
            "code": "action_update_dynamic_field"
            "field_key": "_dyn_single_line_text_so_01234",
            "display_type": "single_line",
            "field_property": 1 //1: số, 2: chữ
            "value": ""
        }
    13.4 Dropdown single select/ radio
        {
            "code": "action_update_dynamic_field"
            "field_key": "_dyn_radio_01234",
            "display_type": "radio",
            "value": ""
        }
    13.5 Dropdown multi select/ check box
        type:
            all_value_exist: tất cả giá trị đang có           // value: True
            all_checked_value: tất cả giá trị được chọn   //value: [ ]
        {
            "code": "action_update_dynamic_field"
            "field_key": "_dyn_checkbox_01234",
            "display_type": "check_box",
            "update_type": "add_value/delete_value/change_value",
            "deleted_value": {
                "type": "",
                "values" : []
            },
            "added_value": {
                "type": "",
                "values": []
            }
        }
}
"""

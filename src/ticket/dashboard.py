#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 03/03/2022
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/ticket-new-and-completed-by-time         Dashboard ticket mới và đã hoàn tất theo thời gian.                     
@apiDescription Dashboard ticket mới và đã hoàn tất theo thời gian.
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardTicketNewAndCompletedByTime
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    start_time              Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)   
@apiParam	(BODY:)			{datetime}	    end_time                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)
                                                                           

@apiUse Response

@apiSuccess {Object}     data            Dữ liệu báo cáo

@apiSuccess (data)      {Object}        ticket_new                                       Thông tin báo cáo số lương ticket mới.
@apiSuccess (data)      {Int}           ticket_new.current_corresponding_date            Số lương ticket mới trong khoảng thời gian query
@apiSuccess (data)      {Float}         ticket_new.previous_corresponding_date           Số lượng ticket mới trong khoảng thời gian trước đó tương ứng
@apiSuccess (data)      {Object}        ticket_completed                                 Thông tin báo cáo số lượng ticket đã hoàn thành.
@apiSuccess (data)      {Int}           ticket_completed.current_corresponding_date      Số lương ticket mới trong khoảng thời gian query
@apiSuccess (data)      {Float}         ticket_completed.previous_corresponding_date     Số lượng ticket mới trong khoảng thời gian trước đó tương ứng
@apiSuccess (data)      {Array}         details                                 Chi tiết
@apiSuccess (data)      {String}        details.date                            Thời gian (Format: <code>%Y-%m-%d</code>)
@apiSuccess (data)      {String}        details.number_ticket_new               Số lượng ticket mới tương ứng với ngày          
@apiSuccess (data)      {String}        details.number_ticket_completed         Số lượng ticket hoàn tất tương ứng với ngày          

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "ticket_new": {
            "current_corresponding_date": 400,
            "previous_corresponding_date": 20
        },
        "ticket_completed": {
            "current_corresponding_date" : 200,
            "previous_corresponding_date": 50
        },
        "details": [
            {
                "date": "01/06/2021",
                "number_ticket_new": 200,
                "number_ticket_completed": 400
            }
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/export/ticket-new-and-completed-by-time         Export dashboard ticket mới và đã hoàn tất theo thời gian.                     
@apiDescription Export dashboard ticket mới và đã hoàn tất theo thời gian.
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  ExportDashboardTicketNewAndCompletedByTime
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    start_time              Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)   
@apiParam	(BODY:)			{datetime}	    end_time                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)   

@apiUse ResponseDashboardExport
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/source-ticket         Dashboard ticket theo nguồn ghi nhận                     
@apiDescription Dashboard ticket theo nguồn ghi nhận
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardSourceTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    end_time                Thời gian bắt đầu (Format: <code>%Y-%m-%d</code>. Ví dụ: 2022-03-21)   


@apiUse Response

@apiSuccess {Array}     data            Dữ liệu báo cáo. <code>Dữ liệu được mặc định sắp xếp theo tỷ lệ cao xuống thấp</code>

@apiSuccess (data)      {Object}        source                              Nguồn ghi nhận đơn hàng
@apiSuccess (data)      {Int}           count                              Số lượng Ticket tương ứng         

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "source": "Nguon 1",
            "count": 75
        },
        {
            "source": "Nguon 2",
            "count": 25,
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/export/source-ticket         Export dashboard ticket theo nguồn ghi nhận                     
@apiDescription Export dashboard ticket theo nguồn ghi nhận
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  ExportDashboardSourceTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    end_time                Thời gian bắt đầu (Format: <code>%Y-%m-%d</code>. Ví dụ: 2022-03-21)   

@apiUse ResponseDashboardExport
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/status-process-ticket        Dashboard ticket theo trạng thái xử lý Ticket                   
@apiDescription Dashboard ticket theo trạng thái xử lý Ticket
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardStatusProcessTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    end_time                Thời gian bắt đầu (Format: <code>%Y-%m-%d</code>. Ví dụ: 2022-03-21)   


@apiUse Response

@apiSuccess {Array}     data            Dữ liệu báo cáo. <code>Dữ liệu được mặc định sắp xếp theo tỷ lệ cao xuống thấp</code>

@apiSuccess (data)      {Object}        status_process_id                   <code>ID</code> định danh trạng thái xử lý đơn hàng
@apiSuccess (data)      {Int}           count                               Số lượng Ticket tương ứng         

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "status_process_id": "28b40d99-72c4-47fa-b46f-87353e335b07",
            "count": 75
        },
        {
            "status_process_id": "92b2aa1f-f798-4f86-a189-29597fb5b420",
            "count": 25,
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/export/status-process-ticket         Export dashboard ticket theo trạng thái xử lý Ticket                   
@apiDescription Export dashboard ticket theo trạng thái xử lý Ticket
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  ExportDashboardStatusProcessTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    end_time                Thời gian bắt đầu (Format: <code>%Y-%m-%d</code>. Ví dụ: 2022-03-21)   

@apiUse ResponseDashboardExport
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/priority-level-ticket         Dashboard ticket theo mức độ ưu tiên                     
@apiDescription Dashboard ticket theo mức độ ưu tiên
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardPriorityLevel
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    end_time                Thời gian bắt đầu (Format: <code>%Y-%m-%d</code>. Ví dụ: 2022-03-21)   
@apiParam	(BODY:)			{array}	        lst_status_process      Danh sách <code>ID</code> trạng thái xử lý cần lọc.   


@apiUse Response

@apiSuccess {Object}     data            Dữ liệu báo cáo. <code>Dữ liệu được mặc định sắp xếp theo tỷ lệ cao xuống thấp</code>

@apiSuccess (data)      {Int}           total                               Tổng số Ticket
@apiSuccess (data)      {Array}         details                             Chi tiết

@apiSuccess (data)      {String}        details.priority_level_id           <code>ID</code> mức độ ưu tiên     
@apiSuccess (data)      {Int}           details.number                      Số lượng Ticket         

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "total": 400,
        "details": [
            {
                "priority_level_id": "57b23937-26c6-4129-a7a8-b8c4fbee1fd6",
                "number": 200
            }
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/export/priority-level         Export dashboard ticket theo mức độ ưu tiên                     
@apiDescription Export dashboard ticket theo mức độ ưu tiên
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  ExportDashboardPriorityLevel
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    end_time                Thời gian bắt đầu (Format: <code>%Y-%m-%d</code>. Ví dụ: 2022-03-21)
@apiParam	(BODY:)			{array}	        lst_status_process      Danh sách <code>ID</code> trạng thái xử lý cần lọc.   

@apiUse ResponseDashboardExport
"""

"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/ticket-type         Dashboard ticket theo kiểu ticket                     
@apiDescription Dashboard ticket theo kiểu ticket
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardTypeTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    end_time                Thời gian bắt đầu (Format: <code>%Y-%m-%d</code>. Ví dụ: 2022-03-21)


@apiUse Response

@apiSuccess {Array}     data            Dữ liệu báo cáo. <code>Dữ liệu được mặc định sắp xếp theo tỷ lệ cao xuống thấp</code>


@apiSuccess (data)      {String}        type_ticket_id                      <code>ID</code> loại ticket   
@apiSuccess (data)      {Int}           number                              Số lượng Ticket         

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
                "type_ticket_id": 1,
                "number": 200
        },
        {
                "type_ticket_id": 2,
                "number": 200
        }
    ]
    ,
    "lang": "vi",
    "message": "request thành công."
}
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/export/ticket-type         Export dashboard ticket theo kiểu Ticket                  
@apiDescription Export dashboard ticket theo kiểu ticket
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  ExportDashboardTypeTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    end_time                Thời gian bắt đầu (Format: <code>%Y-%m-%d</code>. Ví dụ: 2022-03-21)

@apiUse ResponseDashboardExport
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/ticket-wait-assignment         Dashboard ticket chưa được phân công                  
@apiDescription Dashboard ticket chưa được phân công
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    end_time                Thời gian bắt đầu (Format: <code>%Y-%m-%d</code>. Ví dụ: 2022-03-21)


@apiUse Response

@apiSuccess {Object}     data            Dữ liệu báo cáo. <code>Dữ liệu được mặc định sắp xếp theo tỷ lệ cao xuống thấp</code>
    
@apiSuccess (data)      {Int}           number                              Số lượng Ticket         

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": 
        {
                "number": 200
        }
    ,
    "lang": "vi",
    "message": "request thành công."
}
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/average-response-handle-times-ticket        Dashboard thời gian phản hồi và giải quyết trung bình của Ticket                
@apiDescription Dashboard thời gian phản hồi và giải quyết trung bình của Ticket
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardAverageHandleTimesTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    start_time              Thời gian bắt đầu  (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)
@apiParam	(BODY:)			{datetime}	    end_time                Thời gian kết thúc  (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)


@apiUse Response

@apiSuccess {Object}     data               Dữ liệu báo cáo
    
@apiSuccess (data)      {Object}            first_response_time                     Thời gian phản hồi đầu tiên     
@apiSuccess (data)      {Float}             first_response_time.current_corresponding_date     Thời gian phản hồi đầu tiên trong thời gian Query. Tính đơn vị bằng <code>Giây</code>     
@apiSuccess (data)      {Float}             first_response_time.previous_corresponding_date    Thời gian phản hồi đầu tiên của những ngày trước. Tính đơn vị bằng <code>Giây</code>     
@apiSuccess (data)      {Object}            average_response_time                       Thời gian phản hồi trung bình
@apiSuccess (data)      {Float}             average_response_time.current_corresponding_date            Thời gian phản hồi trung bình trong thời gian Query. Tính đơn vị bằng <code>Giây</code>     
@apiSuccess (data)      {Float}             average_response_time.previous_corresponding_date           Thời gian phản hồi trung bình x ngày trước. Tính đơn vị bằng <code>Giây</code>
@apiSuccess (data)      {Object}            average_handle_time                         Thời gian giải quyết trung bình
@apiSuccess (data)      {Float}             average_handle_time.current_corresponding_date              Thời gian giải quyết trung bình trong thời gian Query. Tính đơn vị bằng <code>Giây</code>     
@apiSuccess (data)      {Float}             average_handle_time.previous_corresponding_date             Thời gian giải quết trung bình x ngày trước. Tính đơn vị bằng <code>Giây</code>     

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "first_response_time": {
            "current_corresponding_date": 13333,
            "previous_corresponding_date": 123333,
        },
        "average_response_time": {
            "current_corresponding_date": 13333,
            "previous_corresponding_date": 123333,
        },
        "average_handle_time": {
            "current_corresponding_date": 13333,
            "previous_corresponding_date": 123333,
        }
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/export/average-response-handle-times-ticket        Export dashboard thời gian phản hồi và giải quyết trung bình của Ticket                 
@apiDescription Export dashboard thời gian phản hồi và giải quyết trung bình của Ticket
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  ExportDashboardAverageHandleTimesTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    start_time                Thời gian bắt đầu  (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)
@apiParam	(BODY:)			{datetime}	    end_time                Thời gian kết thúc  (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)

@apiUse ResponseDashboardExport
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/response-complete-times-ticket        Dashboard theo thời gian phản hồi và hoàn tất của Ticket                
@apiDescription Dashboard theo thời gian phản hồi và hoàn tất của Ticket
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardResponseCompleteTimes
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    start_time                Thời gian bắt đầu  (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)
@apiParam	(BODY:)			{datetime}	    end_time                Thời gian kết thúc  (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)


@apiUse Response

@apiSuccess {Object}     data               Dữ liệu báo cáo

@apiSuccess (data)      {Array}             first_response_time                                 Thời gian phản hồi đầu tiên     
@apiSuccess (data)      {String}            first_response_time.key                                                 Thời gian phản hồi 
                                                                                                <ul>
                                                                                                    <li><code>less_15_minutes</code>: Thời gian phản hồi đầu tiên nhỏ hơn 15 phút</li>
                                                                                                    <li><code>about_15_to_30_minutes</code>: Thời gian phản hồi đầu tiên trong khoảng 15 đến 30 phút</li>
                                                                                                    <li><code>about_30_to_60_minutes</code>: Thời gian phản hồi đầu tiên trong khoảng 30 đến 60 phút</li>
                                                                                                    <li><code>about_1_to_2_hours</code>: Thời gian phản hồi đầu tiên trong khoảng 1 đến 2 giờ</li>
                                                                                                    <li><code>about_2_to_4_hours</code>: Thời gian phản hồi đầu tiên trong khoảng 2 đến 4 giờ</li>
                                                                                                    <li><code>about_4_to_8_hours</code>: Thời gian phản hồi đầu tiên trong khoảng 4 đến 8 giờ</li>
                                                                                                    <li><code>about_12_to_24_hours</code>: Thời gian phản hồi đầu tiên trong khoảng 12 đến 24 giờ</li>
                                                                                                    <li><code>about_24_to_48_hours</code>: Thời gian phản hồi đầu tiên trong khoảng 24 đến 48 giờ</li>
                                                                                                    <li><code>greater_48_hours</code>: Thời gian phản hồi đầu tiên lớn hơn hoặc bằng 48 giờ</li>
                                                                                                </ul>
@apiSuccess (data)      {Int}               first_response_time.value                                               Số lượng ticket phản hồi trong khoảbìnhng thời gian tương ứng
@apiSuccess (data)      {Object}            average_response_time                               Thời gian phản hồi trung bình     
@apiSuccess (data)      {String}            average_response_time.key                           Thời gian phản hồi 
                                                                                                <ul>
                                                                                                    <li><code>less_15_minutes</code>: Thời gian phản hồi trung bình nhỏ hơn 15 phút</li>
                                                                                                    <li><code>about_15_to_30_minutes</code>: Thời gian phản hồi trung bình trong khoảng 15 đến 30 phút</li>
                                                                                                    <li><code>about_30_to_60_minutes</code>: Thời gian phản hồi trung bình trong khoảng 30 đến 60 phút</li>
                                                                                                    <li><code>about_1_to_2_hours</code>: Thời gian phản hồi trung bình trong khoảng 1 đến 2 giờ</li>
                                                                                                    <li><code>about_2_to_4_hours</code>: Thời gian phản hồi trung bình trong khoảng 2 đến 4 giờ</li>
                                                                                                    <li><code>about_4_to_8_hours</code>: Thời gian phản hồi trung bình trong khoảng 4 đến 8 giờ</li>
                                                                                                    <li><code>about_12_to_24_hours</code>: Thời gian phản hồi trung bình trong khoảng 12 đến 24 giờ</li>
                                                                                                    <li><code>about_24_to_48_hours</code>: Thời gian phản hồi trung bình trong khoảng 24 đến 48 giờ</li>
                                                                                                    <li><code>greater_48_hours</code>: Thời gian phản hồi trung bình lớn hơn hoặc bằng 48 giờ</li>
                                                                                                </ul>
@apiSuccess (data)      {Int}               average_response_time.value                         Số lượng ticket phản hồi trong khoảng thời gian tương ứng
@apiSuccess (data)      {Object}            average_handle_time                                 Thời gian giải quyết trung bình     
@apiSuccess (data)      {String}            average_handle_time.key                           Thời gian phản hồi 
                                                                                                <ul>
                                                                                                    <li><code>less_15_minutes</code>: Thời gian phản hồi giải quyết nhỏ hơn 15 phút</li>
                                                                                                    <li><code>about_15_to_30_minutes</code>: Thời gian phản hồi giải quyết trong khoảng 15 đến 30 phút</li>
                                                                                                    <li><code>about_30_to_60_minutes</code>: Thời gian phản hồi giải quyết trong khoảng 30 đến 60 phút</li>
                                                                                                    <li><code>about_1_to_2_hours</code>: Thời gian phản hồi giải quyết trong khoảng 1 đến 2 giờ</li>
                                                                                                    <li><code>about_2_to_4_hours</code>: Thời gian phản hồi giải quyết trong khoảng 2 đến 4 giờ</li>
                                                                                                    <li><code>about_4_to_8_hours</code>: Thời gian phản hồi giải quyết trong khoảng 4 đến 8 giờ</li>
                                                                                                    <li><code>about_12_to_24_hours</code>: Thời gian phản hồi giải quyết trong khoảng 12 đến 24 giờ</li>
                                                                                                    <li><code>about_24_to_48_hours</code>: Thời gian phản hồi giải quyết trong khoảng 24 đến 48 giờ</li>
                                                                                                    <li><code>greater_48_hours</code>: Thời gian phản hồi giải quyết lớn hơn hoặc bằng 48 giờ</li>
                                                                                                </ul>
@apiSuccess (data)      {Int}               average_handle_time.value                         Số lượng ticket phản hồi trong khoảng thời gian tương ứng
@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "first_response_time": [
            {
                "key": "less_15_minutes",
                "value": 20
            },
            {
                "key": "about_15_to_30_minutes",
                "value": 20
            },
            {
                "key": "about_30_to_60_minutes",
                "value": 20
            },
            {
                "key": "about_1_to_2_hours",
                "value": 20
            },
            {
                "key": "about_2_to_4_hours",
                "value": 20
            },
            {
                "key": "about_4_to_8_hours",
                "value": 20
            },
            {
                "key": "about_8_to_12_hours",
                "value": 20
            },
            {
                "key": "about_12_to_24_hours",
                "value": 20
            },
            {
                "key": "greater_48_hours",
                "value": 20
            },
        ],
        "average_response_time": [
            {
                "key": "less_15_minutes",
                "value": 20
            },
            {
                "key": "about_15_to_30_minutes",
                "value": 20
            },
            {
                "key": "about_30_to_60_minutes",
                "value": 20
            },
            {
                "key": "about_1_to_2_hours",
                "value": 20
            },
            {
                "key": "about_2_to_4_hours",
                "value": 20
            },
            {
                "key": "about_4_to_8_hours",
                "value": 20
            },
            {
                "key": "about_8_to_12_hours",
                "value": 20
            },
            {
                "key": "about_12_to_24_hours",
                "value": 20
            },
            {
                "key": "greater_48_hours",
                "value": 20
            }
        ],
        "average_handle_time": [
            {
                "key": "less_15_minutes",
                "value": 20
            },
            {
                "key": "about_15_to_30_minutes",
                "value": 20
            },
            {
                "key": "about_30_to_60_minutes",
                "value": 20
            },
            {
                "key": "about_1_to_2_hours",
                "value": 20
            },
            {
                "key": "about_2_to_4_hours",
                "value": 20
            },
            {
                "key": "about_4_to_8_hours",
                "value": 20
            },
            {
                "key": "about_8_to_12_hours",
                "value": 20
            },
            {
                "key": "about_12_to_24_hours",
                "value": 20
            },
            {
                "key": "greater_48_hours",
                "value": 20
            }
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/export/response-complete-times-ticket        Export dashboard theo thời gian phản hồi và hoàn tất của Ticket                 
@apiDescription Export dashboard theo thời gian phản hồi và hoàn tất của Ticket
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  ExportDashboardResponseCompleteTimesTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    start_time                Thời gian bắt đầu  (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)
@apiParam	(BODY:)			{datetime}	    end_time                Thời gian kết thúc  (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)

@apiUse ResponseDashboardExport
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/export/average-response-handle-times-ticket        Export dashboard thời gian phản hồi và giải quyết trung bình của Ticket                 
@apiDescription Export dashboard thời gian phản hồi và giải quyết trung bình của Ticket
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  ExportDashboardAverageHandleTimesTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    start_time                Thời gian bắt đầu  (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)
@apiParam	(BODY:)			{datetime}	    end_time                Thời gian kết thúc  (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)

@apiUse ResponseDashboardExport
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/performance-handle-ticket        Dashboard hiệu suất xử lý ticket theo từng nhân viên                
@apiDescription Dashboard hiệu suất xử lý ticket theo từng nhân viên
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardPerformanceHandleTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    start_time                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)
@apiParam	(BODY:)			{datetime}	    end_time                Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)
@apiParam	(BODY:)			{Array}	        team_ids                Danh sách <code>ID</code> team cần Query
@apiParam	(BODY:)			{Array}	        staff_ids               Danh sách <code>ID</code> nhân viên cần Query

@apiParam   (Query:) {string=1,-1} order_by                      Kiểu sắp xếp
                                                                <ul>
                                                                    <li><code>1</code>: Kiểu tăng dần</li> 
                                                                    <li><code>-1</code>: Kiểu giảm dần</li>
                                                                </ul>
@apiParam   (Query:) {string=ticket_assigned,ticket_complete,ticket_forward,first_response_time,average_response_time} sort_by        Sắp xếp theo các trường chỉ định 
                                                                                    <ul>
                                                                                        <li><code>ticket_assigned</code>: SL ticket được phân công</li> 
                                                                                        <li><code>ticket_complete</code>: SL ticket hoàn thành</li> 
                                                                                        <li><code>ticket_forward</code>: SL ticket chuyển tiếp</li> 
                                                                                        <li><code>first_response_time</code>: Thời gian phản hồi đầu tiên</li> 
                                                                                        <li><code>average_response_time</code>: THời gian hoàn thành ticket</li> 
                                                                                    </ul>
                                                                                    
@apiUse Response

@apiSuccess {Object}     data                           Dữ liệu báo cáo
@apiSuccess {Array}     data.performances               Chi tiết báo cáo

@apiSuccess       {String}            data.performances.staff_id                                            <code>ID</code> nhân viên     
@apiSuccess       {int}               data.performances.ticket_assigned                                     Số lượng ticket được phân công     
@apiSuccess       {int}               data.performances.ticket_complete                                     Số lượng ticket hoàn tất     
@apiSuccess       {int}               data.performances.ticket_forward                                      Số lượng ticket chuyển tiếp     
@apiSuccess       {Float}             data.performances.first_response_time                                 Thời gian phản hồi đầu tiên. Tính theo giây     
@apiSuccess       {Float}             data.performances.average_response_time                               Thời gian phản hồi trung bình. Tính theo giây     
@apiSuccess       {Float}             data.performances.average_handle_time                                 Thời gian giải quyết trung bình. Tính theo giây

@apiSuccess {Object}    data.total     Tổng    
@apiSuccess {Number}    data.total.ticket_assigned                                          Số lượng ticket được phân công     
@apiSuccess {Number}    data.total.ticket_complete                                          Số lượng ticket hoàn tất 
@apiSuccess {Number}    data.total.ticket_forward                                           Số lượng ticket chuyển tiếp
@apiSuccess {Number}    data.total.first_response_time                                      Thời gian phản hồi đầu tiên. Tính theo giây <code>Đơn vị: giây </code> 
@apiSuccess {Number}    data.total.average_response_time                                    Thời gian phản hồi trung bình. Tính theo giây <code>Đơn vị: giây </code>
@apiSuccess {Number}    data.total.average_handle_time                                    Thời gian giải quyết trung bình. Tính theo giây <code>Đơn vị: giây </code>



@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "performances":[
            {
                "staff_id": "af85852b-8b14-40b5-88a0-db9b2d31335f",
                "ticket_assigned": 20,
                "ticket_complete": 20,
                "ticket_forward": 20,
                "first_response_time": 10,
                "average_response_time": 10,
                "average_handle_time": 10,
            }
        ],
        "total": {
            "ticket_assigned": 20,
            "ticket_complete": 20,
            "ticket_forward": 20,
            "first_response_time": 10,
            "average_response_time": 10,
            "average_handle_time": 10,
        }
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/total-performance-handle-ticket        Dashboard tổng hiệu suất xử lý ticket theo từng nhân viên                
@apiDescription Dashboard tổng hiệu suất xử lý ticket theo từng nhân viên
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardTotalPerformanceHandleTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    start_time                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)
@apiParam	(BODY:)			{datetime}	    end_time                Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)
@apiParam	(BODY:)			{Array}	        team_ids                Danh sách <code>ID</code> team cần Query
@apiParam	(BODY:)			{Array}	        staff_ids               Danh sách <code>ID</code> nhân viên cần Query

@apiParam   (Query:) {string=1,-1} order_by                      Kiểu sắp xếp
                                                                <ul>
                                                                    <li><code>1</code>: Kiểu tăng dần</li> 
                                                                    <li><code>-1</code>: Kiểu giảm dần</li>
                                                                </ul>
@apiParam   (Query:) {string=ticket_assigned,ticket_complete,ticket_forward,first_response_time,average_response_time} sort_by        Sắp xếp theo các trường chỉ định 
                                                                                    <ul>
                                                                                        <li><code>ticket_assigned</code>: SL ticket được phân công</li> 
                                                                                        <li><code>ticket_complete</code>: SL ticket hoàn thành</li> 
                                                                                        <li><code>ticket_forward</code>: SL ticket chuyển tiếp</li> 
                                                                                        <li><code>first_response_time</code>: Thời gian phản hồi đầu tiên</li> 
                                                                                        <li><code>average_response_time</code>: THời gian hoàn thành ticket</li> 
                                                                                    </ul>
                                                                                    
@apiUse Response

@apiSuccess {Object}     data                           Dữ liệu báo cáo

@apiSuccess {Number}    data.total_ticket_assigned                                          Tổng số lượng ticket được phân công     
@apiSuccess {Number}    data.total_ticket_complete                                          Tổng số lượng ticket hoàn tất 
@apiSuccess {Number}    data.total_ticket_forward                                           Tổng số lượng ticket chuyển tiếp
@apiSuccess {Number}    data.total_first_response_time                                      Tổng thời gian phản hồi đầu tiên. Tính theo giây <code>Đơn vị: giây </code> 
@apiSuccess {Number}    data.total_average_response_time                                    Tổng thời gian phản hồi trung bình. Tính theo giây <code>Đơn vị: giây </code>
@apiSuccess {Number}    data.total_average_handle_time                                    Tổng thời gian giải quyết trung bình. Tính theo giây <code>Đơn vị: giây </code>



@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "total_ticket_assigned": 20,
        "total_ticket_complete": 20,
        "total_ticket_forward": 20,
        "total_first_response_time": 10,
        "total_average_response_time": 10,
        "total_average_handle_time": 10,
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/actions/export/performance-handle-ticket        Export dashboard hiệu suất xử lý ticket theo từng nhân viên                  
@apiDescription Export dashboard hiệu suất xử lý ticket theo từng nhân viên
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  ExportDashboardPerformanceHandleTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{datetime}	    start_time                Thời gian bắt đầu  (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)
@apiParam	(BODY:)			{datetime}	    end_time                Thời gian kết thúc  (Format: <code>%Y-%m-%dT%H:%MZ</code>. Ví dụ: 2022-03-21T09:47Z)
@apiParam	(BODY:)			{Array}	        team_ids                Danh sách <code>ID</code> team cần Query
@apiParam	(BODY:)			{Array}	        staff_ids               Danh sách <code>ID</code> nhân viên cần Query

@apiUse ResponseDashboardExport
"""

# Dashboard phục vụ bên workflow

"""
@api {post} {domain}/ticket/api/v1.0/dashboard/wf/get-number-ticket-by-wf       Lấy số lượng ticket và trạng thái được tạo từ workflow
@apiDescription Lấy số lượng ticket và trạng thái được tạo từ workflow
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardGetNumberTicketByWF
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{string}	    workflow_id                <code>ID</code> workflow cần Query

@apiUse Response

@apiSuccess {Object}     data                                           Dữ liệu báo cáo
@apiSuccess {Array}     data.detail_number_ticket_status                Chi tiết báo cáo

@apiSuccess       {String}            data.detail_number_ticket_status.status_process_id        <code>ID</code> trạng thái xử lý
@apiSuccess       {int}               data.detail_number_ticket_status.number                   Số lượng ticket đang ở trạng thái tương ứng
@apiSuccess       {int}               data.detail_number_ticket_status.percent                  Phần trăm số lượng ticket đang ở trạng thái tương ứng

@apiSuccess {Object}    data.total     Tổng    
@apiSuccess {Number}    data.total.ticket_created                                          Số lượng ticket được tạo     



@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "detail_number_ticket_status":[
            {
                "status_process_id": "",
                "number": 20,
                "percent": 20
            },
            {
                "status_process_id": "",
                "number": 20,
                "percent": 20
            },
            {
                "status_process_id": "",
                "number": 20,
                "percent": 20
            }
        ],
        "total": {
            "ticket_created": 2000,
        }
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {post} {domain}/ticket/api/v1.0/dashboard/wf/get-number-ticket-by-block-action       Lấy số lượng ticket và trạng thái được tạo từ workflow theo khối hành động
@apiDescription Lấy số lượng ticket và trạng thái được tạo từ workflow theo khối hành động
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardGetNumberTicketByBlockAction
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{string}	    workflow_id                <code>ID</code> workflow cần Query
@apiParam	(BODY:)			{Array}	        block_action_ids           Danh sách <code>ID</code> khối hành động
@apiParam	(BODY:)			{datetime}	    [start_time]              Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)   
@apiParam	(BODY:)			{datetime}	    [end_time]                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)

@apiUse Response

@apiSuccess {Object}     data                                           Dữ liệu báo cáo
@apiSuccess {Array}     data.detail_number_ticket_block_action                Chi tiết báo cáo

@apiSuccess       {String}              data.detail_number_ticket_block_action.block_action_id        <code>ID</code> khối hành động
@apiSuccess       {Int}                 data.detail_number_ticket_block_action.total_ticket_created   Tổng số ticket được tạo của khối hành động
@apiSuccess       {Array}               data.detail_number_ticket_block_action.details       Danh sách trạng thái của khối hành động
@apiSuccess       {String}              data.detail_number_ticket_block_action.details.key                      <code>key</code> trạng thái xử lý ticket
                                                                                                            <ul>
                                                                                                                <li><code>unprocessed</code>: Thống kê số lượng ticket chưa xử lý (Trạng thái open)</li>
                                                                                                                <li><code>processing</code>: Thống kê số lượng ticket đang xử lý (Tất cả trạng thái ngoại trừ open và finish )</li>
                                                                                                                <li><code>finish</code>: Thống kê số lượng ticket hoàn thành </li>
                                                                                                            </ul>
@apiSuccess       {int}                 data.detail_number_ticket_block_action.details.number                   Số lượng ticket đang ở trạng thái tương ứng
@apiSuccess       {float}               data.detail_number_ticket_block_action.details.percent                  Phần trăm số lượng ticket đang ở trạng thái tương ứng


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "detail_number_ticket_status":[
            {
                "block_action_id": "",
                "total_ticket_created": 200,
                "details": [
                    {
                        "key": "unprocessed",
                        "number": 20,
                        "percent": 20
                    },
                    {
                        "key": "processing",
                        "number": 20,
                        "percent": 20
                    },
                    {
                        "key": "finish",
                        "number": 20,
                        "percent": 20
                    }
                ]
            }
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {post} {domain}/ticket/api/v1.0/dashboard/wf/get-report-status-ticket                  Lấy báo cáo trạng thái xử lý ticket
@apiDescription         Lấy báo cáo trạng thái xử lý ticket
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardGetReportStatusTicket
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{string}	    workflow_id                <code>ID</code> workflow cần Query
@apiParam	(BODY:)			{datetime}	    [start_time]              Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)   
@apiParam	(BODY:)			{datetime}	    [end_time]                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)

@apiUse Response

@apiSuccess {Object}     data                                           Dữ liệu báo cáo
@apiSuccess {Array}     data.detail_number_ticket_status                Chi tiết báo cáo

@apiSuccess       {String}            data.detail_number_ticket_status.key                      <code>key</code> trạng thái xử lý ticket
                                                                                                <ul>
                                                                                                    <li><code>unprocessed</code>: Thống kê số lượng ticket chưa xử lý (Trạng thái open)</li>
                                                                                                    <li><code>processing</code>: Thống kê số lượng ticket đang xử lý (Tất cả trạng thái ngoại trừ open và finish )</li>
                                                                                                    <li><code>finish</code>: Thống kê số lượng ticket hoàn thành </li>
                                                                                                </ul>
@apiSuccess       {int}               data.detail_number_ticket_status.number                   Số lượng ticket đang ở trạng thái tương ứng

@apiSuccess {Object}    data.total     Tổng    
@apiSuccess {Number}    data.total.ticket_created                                          Số lượng ticket được tạo     



@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "detail_number_ticket_status":[
            {
                "key": "",
                "number": 20,
            },
            {
                "key": "",
                "number": 20,
            },
            {
                "key": "",
                "number": 20,
            }
        ],
        "total": {
            "ticket_created": 2000,
        }
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
"""
@api {post} {domain}/ticket/api/v1.0/dashboard/wf/get-total-ticket-by-wf-range-time       Lấy tổng số lượng ticket được tạo trong khoảng thời gian
@apiDescription Lấy tổng số lượng ticket được tạo trong khoảng thời gian
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName  DashboardGetTotalTicketByWFRangeTime
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(BODY:)			{string}	    workflow_id                <code>ID</code> workflow cần Query
@apiParam	(BODY:)			{datetime}	    start_time              Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)   
@apiParam	(BODY:)			{datetime}	    end_time                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)

@apiUse Response

@apiSuccess {Object}     data                                           Dữ liệu báo cáo
@apiSuccess {Int}     data.total                Tổng số ticket được tạo trong khoảng thời gian



@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "total": 2000,
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

#!/usr/bin/python
# -*- coding: utf8 -*-

# **********************************************************************************************************************************
# ************************************************** API LẤY DANH SÁCH NHÓM QUYỀN **************************************************
# **********************************************************************************************************************************
"""
@api {get} /merchants/sub-brands/<sub_brand_id>/modules  Lấy danh sách nhóm quyền
@apiDescription Lấy danh sách nhóm quyền
@apiVersion 2.1.0
@apiGroup Module
@apiName Get

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse search

@apiSuccess (Query:)    {string}       [type]         Loại quyền <code>'created'- Tự tạo, 'default'- Mặc định </code>

@apiSuccess (data)    {string}       id               ID nhóm quyền
@apiSuccess (data)    {string}       name             Tên nhóm quyền
@apiSuccess (data)    {string}       description      Mô tả nhóm quyền
@apiSuccess (data)    {Array}        accounts         Danh sách accounts
@apiSuccess (data)    {String}       type           Loại quyền <code>'created'- Tự tạo, 'default'- Mặc định </code>
@apiSuccess (data)    {Array}        accounts         Danh sách accounts

@apiSuccess (accounts)    {string}           id                 ID accounts
@apiSuccess (accounts)    {string}           username           username tên đăng nhập  của accounts
@apiSuccess (accounts)    {string}           avatar             Ảnh đại diện của accounts
@apiSuccess (accounts)    {string}           fullname           Tên đầy đủ của accounts
@apiSuccess (accounts)    {string}           phone_number       Số điện thoại của accounts
@apiSuccess (accounts)    {string}           email              Email của accounts


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
  "data": [
    {
        "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
        "name": "Chăm sóc khách hàng",
        "type": "created",
        "description": "",
        "accounts": [
            {
                  "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                  "username": "Locnh",
                  "fullname": "Nguyễn Hữu Lộc",
                  "avatar": "",
                  "phone_number": "",
                  "email": "<EMAIL>"
            },
            ...
        ]
    },
    ...
  ]
}
"""

# **********************************************************************************************************************************
# ************************************************** API LẤY DANH SÁCH NHÓM QUYỀN **************************************************
# **********************************************************************************************************************************
"""
@api {get} /merchants/sub-brands/<sub_brand_id>/modules  Lấy danh sách nhóm quyền
@apiDescription Lấy danh sách nhóm quyền
@apiVersion 2.0.0
@apiGroup Module
@apiName Get

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse search


@apiSuccess (data)    {string}       id               ID nhóm quyền
@apiSuccess (data)    {string}       name             Tên nhóm quyền
@apiSuccess (data)    {string}       description      Mô tả nhóm quyền
@apiSuccess (data)    {Array}        accounts         Danh sách accounts

@apiSuccess (data)    {Array}        accounts         Danh sách accounts

@apiSuccess (accounts)    {string}           id                 ID accounts
@apiSuccess (accounts)    {string}           username           username tên đăng nhập  của accounts
@apiSuccess (accounts)    {string}           avatar             Ảnh đại diện của accounts
@apiSuccess (accounts)    {string}           fullname           Tên đầy đủ của accounts
@apiSuccess (accounts)    {string}           phone_number       Số điện thoại của accounts
@apiSuccess (accounts)    {string}           email              Email của accounts


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
  "data": [
    {
        "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
        "name": "Chăm sóc khách hàng",
        "description": "",
        "accounts": [
            {
                  "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                  "username": "Locnh",
                  "fullname": "Nguyễn Hữu Lộc",
                  "avatar": "",
                  "phone_number": "",
                  "email": "<EMAIL>"
            },
            ...
        ]
    },
    ...
  ]
}
"""

# ***********************************************************************************************************************
# ************************************************** API XÓA NHÓM QUYỀN **************************************************
# ***********************************************************************************************************************
"""
@api {DELETE} /merchants/sub-brands/<sub_brand_id>/modules/delete Xóa nhóm quyền 
@apiDescription Xóa nhóm quyền 
@apiVersion 2.0.0
@apiGroup Module
@apiName Delete Module

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {Array}    ids      Tập hợp các UUID của nhóm quyền.

@apiParamExample {json} Body example
{
	"ids": [
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703"
	]
}

"""

# ***********************************************************************************************************************
# ************************************************** API TẠO NHÓM QUYỀN **************************************************
# ***********************************************************************************************************************
"""
@api{post} /merchants/sub-brands/<sub_brand_id>/modules/create Tạo nhóm quyền 
@apiDescription  Tạo nhóm quyền
@apiVersion 2.1.0
@apiGroup Module
@apiName Post

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {String}    name                 Tên nhóm quyền 
@apiParam      (Body:)     {String}    description          Mô tả nhóm quyên
@apiParam      (Body:)     {Array}     functions            Danh sách chức năng 
@apiParam      (Body:)     {number}    [is_all_function]    Chọn tất cả các quyền. <code>0: Không</code>, <code>1: Chọn tất cả</code>  
@apiSuccess    (Body:)     {String}       [type]           Loại quyền <code>'created'- Tự tạo, 'default'- Mặc định (không truyền lên lấy mặc định)</code>
@apiParam      (Functions:)     {String}    function_id     uuid chức năng 
@apiParam      (Functions:)     {Array}     action_ids      Danh sách uuid action
@apiParam      (Body:)     {Array}     [policies]           Danh sách ID policies cần truy cập

@apiParamExample {json} Body example
{   
    "merchant_id": "c7229324-ffe3-4090-8acb-27093003f703",
  "name": "Chăm sóc khách hàng",
  "description": "",
  "is_all_function": 1,
  "functions": [
      {
          "function_id": "c7229324-ffe3-4090-8acb-27093003f703",
          "action_ids": ["c7229324-ffe3-4090-8acb-27093003f703", "c7229324-ffe3-4090-8acb-27093003f703"]
      }
  ],
  "policies": []
}
"""

# ***********************************************************************************************************************
# ************************************************** API TẠO NHÓM QUYỀN **************************************************
# ***********************************************************************************************************************
"""
@api{post} /merchants/sub-brands/<sub_brand_id>/modules/create Tạo nhóm quyền 
@apiDescription  Tạo nhóm quyền
@apiVersion 2.0.0
@apiGroup Module
@apiName Post

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {String}    name                 Tên nhóm quyền 
@apiParam      (Body:)     {String}    description          Mô tả nhóm quyên
@apiParam      (Body:)     {Array}     functions            Danh sách chức năng 
@apiParam      (Body:)     {number}    [is_all_function]    Chọn tất cả các quyền. <code>0: Không</code>, <code>1: Chọn tất cả</code> 
@apiParam      (Functions:)     {String}    function_id     uuid chức năng 
@apiParam      (Functions:)     {Array}     action_ids      Danh sách uuid action

@apiParamExample {json} Body example
{   
    "merchant_id": "c7229324-ffe3-4090-8acb-27093003f703",
	"name": "Chăm sóc khách hàng",
	"description": "",
	"is_all_function": 1,
	"functions": [
	    {
	        "function_id": "c7229324-ffe3-4090-8acb-27093003f703",
	        "action_ids": ["c7229324-ffe3-4090-8acb-27093003f703", "c7229324-ffe3-4090-8acb-27093003f703"]
	    }
	]
}
"""

# **********************************************************************************************************************************
# ************************************************** UPDATE NHÓM QUYỀN  **************************************************
# **********************************************************************************************************************************
"""
@api{put} /merchants/sub-brands/<sub_brand_id>/modules/<module_id>  Update nhóm quyền 
@apiDescription Update nhóm quyền
@apiVersion 2.0.0
@apiGroup Module
@apiName put

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {String}    name                 Tên nhóm quyền 
@apiParam      (Body:)     {String}    description          Mô tả nhóm quyên
@apiParam      (Body:)     {Array}     functions            Danh sách chức năng 
@apiParam      (Body:)     {number}    [is_all_function]    Chọn tất cả các quyền. <code>0: Không</code>, <code>1: Chọn tất cả</code>  

@apiParam      (Functions:)     {String}    function_id     uuid chức năng 
@apiParam      (Functions:)     {Array}     action_ids      Danh sách uuid action

@apiParamExample {json} Body example
{   
	"name": "Chăm sóc khách hàng",
	"description": "",
	"is_all_function": 1,
	"functions": [
	    {
	        "function_id": "c7229324-ffe3-4090-8acb-27093003f703",
	        "action_ids": ["c7229324-ffe3-4090-8acb-27093003f703", "c7229324-ffe3-4090-8acb-27093003f703"]
	    }
	]

}
"""

# **********************************************************************************************************************************
# ************************************************** LẤY CHI TIẾT NHÓM QUYỀN **************************************************
# **********************************************************************************************************************************
"""
@api{get} /merchants/sub-brands/<sub_brand_id>/modules/<module_id> Lấy chi tiết nhóm quyền
@apiDescription Lấy chi tiết nhóm quyền
@apiVersion 2.0.0
@apiGroup Module
@apiName get

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@@apiSuccess       (data:)     {String}    name                 Tên nhóm quyền 
@@apiSuccess       (data:)     {String}    description          Mô tả nhóm quyên
@@apiSuccess       (data:)     {Array}     functions            Danh sách chức năng 
@@apiSuccess       (data:)     {number}    [is_all_function]    Chọn tất cả các quyền. <code>0: Không</code>, <code>1: Chọn tất cả</code>  

@@apiSuccess       (Functions:)     {String}    function_id     uuid chức năng 
@@apiSuccess       (Functions:)     {Array}     action_ids      Danh sách uuid action

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "name": "Chăm sóc khách hàng",
        "merchant_id": "c7229324-ffe3-4090-8acb-27093003f703",
        "accounts": [
                {
                      "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                      "username": "Locnh",
                      "fullname": "Nguyễn Hữu Lộc",
                      "avatar": "",
                      "phone_number": "",
                      "email": "<EMAIL>"
                },
                ...
            ],

        "description": "",
        "is_all_function": 1,
        "functions": [
            {
                "function_id": "c7229324-ffe3-4090-8acb-27093003f703",
                "action_ids": ["c7229324-ffe3-4090-8acb-27093003f703", "c7229324-ffe3-4090-8acb-27093003f703"],
            }
        ]
    }

}
"""

# **********************************************************************************************************************************
# ************************************************** LẤY CHI TIẾT NHÓM QUYỀN **************************************************
# **********************************************************************************************************************************
"""
@api{get} /merchants/sub-brands/<sub_brand_id>/modules/<module_id> Lấy chi tiết nhóm quyền
@apiDescription Lấy chi tiết nhóm quyền
@apiVersion 2.0.0
@apiGroup Module
@apiName get

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@@apiSuccess       (data:)     {String}    name                 Tên nhóm quyền 
@@apiSuccess       (data:)     {String}    description          Mô tả nhóm quyên
@@apiSuccess       (data:)     {Array}     functions            Danh sách chức năng 
@@apiSuccess       (data:)     {number}    [is_all_function]    Chọn tất cả các quyền. <code>0: Không</code>, <code>1: Chọn tất cả</code>  

@@apiSuccess       (Functions:)     {String}    function_id     uuid chức năng 
@@apiSuccess       (Functions:)     {Array}     action_ids      Danh sách uuid action

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "name": "Chăm sóc khách hàng",
        "merchant_id": "c7229324-ffe3-4090-8acb-27093003f703",
        "accounts": [
                {
                      "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                      "username": "Locnh",
                      "fullname": "Nguyễn Hữu Lộc",
                      "avatar": "",
                      "phone_number": "",
                      "email": "<EMAIL>"
                },
                ...
            ],

        "description": "",
        "is_all_function": 1,
        "functions": [
            {
                "function_id": "c7229324-ffe3-4090-8acb-27093003f703",
                "action_ids": ["c7229324-ffe3-4090-8acb-27093003f703", "c7229324-ffe3-4090-8acb-27093003f703"],
            }
        ]
    }

}
"""
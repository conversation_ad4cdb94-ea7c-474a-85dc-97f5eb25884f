#!/usr/bin/python
# -*- coding: utf8 -*-

# ***********************************************************************************************************************
# ************************************************** API TẠO NHÂN VIÊN **************************************************
# ***********************************************************************************************************************
"""
@api {post} /adm/v2.0/merchants/sub-brands/<sub_brands_id>/accounts Tạo nhân viên
@apiDescription Tạo một account nhân viên theo thương hiệu. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiVersion 2.1.0
@apiGroup Account
@apiName Post

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {File}      avatar                           Ảnh đại diện
@apiParam      (Body:)     {String}    info                            Thông tin  sản phẩm, String object json


@apiParam (info) {string} username         Tên truy cập
@apiParam (info) {string} password         Mật khẩu
@apiParam (info) {string} fullname         Tên đầu đủ
@apiParam (info) {string} phone_number     Số điện thoại
@apiParam (info) {string} email            Email của tài khoản
@apiParam (info) {string} [module_ids]     Mảng các module_id:
@apiParam (info) {int}    role_group         Nhóm quyền <code> 1- owner, 2- admin, 3- manager, 4- user</code>

@apiParamExample {json} Info example
{
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "phone_number": "",
  "email": "<EMAIL>",
  "password":"1234",
  "role_group": 2,
  "module_ids":["f702b59b-312c-4a04-b62f-ad34b7c8d904"],
  "merchant_id": "86b15b04-d4d1-41aa-95f8-b826e4bca0be"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "86b15b04-d4d1-41aa-95f8-b826e4bca0be",
  "id": "cbce917d-f9f8-4088-bcad-da01975d3163",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "avatar": "",
  "phone_number": "",
  "role_group": 2,
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "is_online": 0,
  "modules":[
          {
                "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                "name": "Chăm sóc khách hàng",
                "description": ""
          },
          ...
  ]
}
"""

# ***********************************************************************************************************************
# ************************************************** API TẠO NHÂN VIÊN **************************************************
# ***********************************************************************************************************************
"""
@api {post} /adm/v2.0/merchants/sub-brands/<sub_brands_id>/accounts Tạo nhân viên
@apiDescription Tạo một account nhân viên theo thương hiệu. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiVersion 2.0.0
@apiGroup Account
@apiName Post

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {File}      avatar                          	Ảnh đại diện
@apiParam      (Body:)     {String}    info                            Thông tin  sản phẩm, String object json


@apiParam (info) {string} username         Tên truy cập
@apiParam (info) {string} password         Mật khẩu
@apiParam (info) {string} fullname         Tên đầu đủ
@apiParam (info) {string} phone_number     Số điện thoại
@apiParam (info) {string} email            Email của tài khoản
@apiParam (info) {string} [module_ids]     Mảng các module_id:

@apiParamExample {json} Info example
{
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "phone_number": "",
  "email": "<EMAIL>",
  "password":"1234",
  "module_ids":["f702b59b-312c-4a04-b62f-ad34b7c8d904"],
  "merchant_id": "86b15b04-d4d1-41aa-95f8-b826e4bca0be"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "86b15b04-d4d1-41aa-95f8-b826e4bca0be",
  "id": "cbce917d-f9f8-4088-bcad-da01975d3163",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "avatar": "",
  "phone_number": "",
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "modules":[
          {
                "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                "name": "Chăm sóc khách hàng",
                "description": ""
          },
          ...
  ]
}
"""

# ***********************************************************************************************************************
# ************************************************** UPDATE NHÂN VIÊN **************************************************
# ***********************************************************************************************************************
"""
@api {put} /merchants/sub-brands/<sub_brands_id>/accounts/<profile_id> Update nhân viên
@apiDescription Update nhân viên. Dịch vụ gửi lên request dạng <code>form-data</code>  
@apiVersion 2.0.0
@apiGroup Account
@apiName put

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {File}      avatar                          	Ảnh đại diện
@apiParam      (Body:)     {String}    info                            Thông tin  sản phẩm, String object json


@apiParam (info) {string} password         Mật khẩu
@apiParam (info) {string} fullname         Tên đầu đủ
@apiParam (info) {string} phone_number     Số điện thoại
@apiParam (info) {string} email            Email của tài khoản
@apiParam (info) {string} [module_ids]     Mảng các module_id:


@apiParamExample {json} Info example
{
  "fullname": "Nguyễn Hữu Lộc",
  "phone_number": "**********",
  "module_ids":["18a07df3-5fdc-41c4-9bc9-00cee694c68a"]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "86b15b04-d4d1-41aa-95f8-b826e4bca0be",
  "id": "cbce917d-f9f8-4088-bcad-da01975d3163",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "avatar": "",
  "phone_number": "",
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "modules":[
          {
                "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                "name": "Chăm sóc khách hàng",
                "description": ""
          },
          ...
  ]
}

"""
# **********************************************************************************************************************************
# ************************************************** API LẤY DANH SÁCH NHÂN VIÊN  **************************************************
# **********************************************************************************************************************************
"""
@api {Get} /merchants/sub-brands/<sub_brands_id>/accounts Lấy danh sách nhân viên 
@apiDescription Lấy danh sách nhân viên của 1 nhãn hàng
@apiVersion 2.1.0
@apiGroup Account
@apiName Get

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse search

@apiParam (Query:)     {int}    [is_online]    Trạng thái đăng nhập <code> 1- online, 0- offline</code>
@apiParam (Query:)     {int}    [role_group]   Nhóm quyền <code> 1- owner, 2- admin, 3- manager, 4- user</code>
@apiParam (Query:)     {string}    [day_offline]   Ngày không hoạt động <code> "equal", "higher_equal", "higher", "between", "lower", "lower_equal"</code>
@apiParam (Query:)     {string}    [day_value_start]   Dùng cho bettween thời gian bắt đầu etc: "2017-08-07T04:02:28.002Z"
@apiParam (Query:)     {string}    [day_value_end]   Dùng cho bettween thời gian kết thúc etc: "2017-08-07T04:02:28.002Z"
@apiParam (Query:)     {string}    [day_value]   dùng để lọc ngày offline etc: "2017-08-07T04:02:28.002Z"

@apiSuccess {string} merchant_id        UUID sub-brand
@apiSuccess {object} data               Danh sách accounts

@apiSuccess (data) {string} id          ID tài khoản nhân viên
@apiSuccess (data) {string} username    Tên truy cập nhân viên
@apiSuccess (data) {string} fullname    Tên dầy đủ nhân viên
@apiSuccess (data) {string} avatar      Ảnh đại điện nhân viên
@apiSuccess (data) {string} phone_number        Số điện thoại nhân viên
@apiSuccess (data) {string} email               Thư điện tử nhân viên
@apiSuccess (data) {string} created_account     Người tạo 
@apiSuccess (data) {int} status         Trạng thái 
@apiSuccess (data) {int} is_online       Trạng thái đăng nhập <code> 1- online, 0- offline</code>

@apiSuccess (data) {Object}         modules     Danh sách nhóm quyền

@apiSuccess (modules)    {string}       id               ID nhóm quyền
@apiSuccess (modules)    {string}       name             Tên nhóm quyền
@apiSuccess (modules)    {string}       description      Mô tả nhóm quyền

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
  "data": [
    {
      "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
      "username": "Locnh",
      "fullname": "Nguyễn Hữu Lộc",
      "avatar": "",
      "phone_number": "",
      "email": "<EMAIL>",
      "create_time": "2017-08-07T04:02:28.002Z",
      "update_time": "2017-08-07T04:02:28.002Z",
      "created_account": "admin@mobio",
      "is_offline": 1,
      "role_group": 1,
      "modules":[
          {
                "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                "name": "Chăm sóc khách hàng",
                "description": ""
          },
          ...
      ]
    },
    ...
  ],
  "lang": "vi",
  "paging": {
    ...
  }
}

"""
# **********************************************************************************************************************************
# ************************************************** API LẤY DANH SÁCH NHÂN VIÊN  **************************************************
# **********************************************************************************************************************************
"""
@api {Get} /merchants/sub-brands/<sub_brands_id>/accounts Lấy danh sách nhân viên 
@apiDescription Lấy danh sách nhân viên của 1 nhãn hàng
@apiVersion 2.0.0
@apiGroup Account
@apiName Get

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse search


@apiSuccess {string} merchant_id        UUID sub-brand
@apiSuccess {object} data               Danh sách accounts

@apiSuccess (data) {string} id          ID tài khoản nhân viên
@apiSuccess (data) {string} username    Tên truy cập nhân viên
@apiSuccess (data) {string} fullname    Tên dầy đủ nhân viên
@apiSuccess (data) {string} avatar      Ảnh đại điện nhân viên
@apiSuccess (data) {string} phone_number        Số điện thoại nhân viên
@apiSuccess (data) {string} email               Thư điện tử nhân viên
@apiSuccess (data) {string} created_account     Người tạo 
@apiSuccess (data) {int} status         Trạng thái 

@apiSuccess (data) {Object}         modules     Danh sách nhóm quyền

@apiSuccess (modules)    {string}       id               ID nhóm quyền
@apiSuccess (modules)    {string}       name             Tên nhóm quyền
@apiSuccess (modules)    {string}       description      Mô tả nhóm quyền

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
  "data": [
    {
      "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
      "username": "Locnh",
      "fullname": "Nguyễn Hữu Lộc",
      "avatar": "",
      "phone_number": "",
      "email": "<EMAIL>",
      "create_time": "2017-08-07T04:02:28.002Z",
      "update_time": "2017-08-07T04:02:28.002Z",
      "created_account": "admin@mobio",
      "modules":[
          {
                "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                "name": "Chăm sóc khách hàng",
                "description": ""
          },
          ...
      ]
    },
    ...
  ],
  "lang": "vi",
  "paging": {
    ...
  }
}
"""

# ***********************************************************************************************************************
# ************************************************** API XÓA NHÂN VIÊN **************************************************
# ***********************************************************************************************************************
"""
@api {DELETE} /merchants/<sub_brands_id>/accounts/delete Xóa nhân viên
@apiDescription Xóa account nhân viên theo thương hiệu 
@apiVersion 2.0.0
@apiGroup Account
@apiName Delete Accounts

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}    ids      Tập hợp các UUID của nhân viên.

@apiParamExample {json} Body example
{
	"ids": [
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703"
	]
}
"""


#!/usr/bin/python
# -*- coding: utf8 -*-

# ***********************************************************************************************************************
# ************************************************** API Lấy log **************************************************
# ***********************************************************************************************************************
"""
@api {get} /adm/v2.0/accounts/logs Lấy danh sách log của nhân viên
@apiDescription Lấy danh sách log của nhân viên
@apiVersion 2.1.0
@apiGroup Account
@apiName Get Log
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse search


@apiParam      (Query:)     {String}    [id_account]                   user_id
@apiParam      (Query:)     {String}    [start_time]                   tìm log từ thời gian này
@apiParam      (Query:)     {String}    [end_time]                     tìm log cho đến thời gian này

@apiParam (info) {string} created_time     Thời gian tạo log
@apiParam (info) {string} action           Hoạt động của hoạt động
@apiParam (info) {string} name             Tên tài khoản 
@apiParam (info) {string} email            Email của tài khoản
@apiParam (info) {string} ip_address       IP khi log hoạt động

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": [
      {
      	"id_account": "0006d21a-2eaf-40a6-88d5-9c77188c7c23",
        "created_time": "2017-08-07T04:02:28.002Z",
        "action": {
                    "id": 1,
                    "name_vi": "Thêm",
                    "name_en": "Add",
                    "description": "Thêm item"
                },
        "email": "<EMAIL>",
        "name": "Jollibee",
        "ip_address": "*************"
      },
      {
      	"id_account":"0006d21a-2eaf-40a6-88d5-9c77188c7c23",
        "created_time": "2017-08-07T04:02:28.002Z",
        "action": {
                    "id": 2,
                    "name_vi": "Sửa",
                    "name_en": "Edit",
                    "description": "Thêm item"
                },
        "email": "<EMAIL>",
        "name": "Jollibee",
        "ip_address": "*************"
      },
      ....
    ]
}
"""


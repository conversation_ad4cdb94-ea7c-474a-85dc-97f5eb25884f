#!/usr/bin/python
# -*- coding: utf8 -*-

"""
@api {get} /merchants/sub-brands/<sub-brand_id>/functions Lấy danh sách chức năng theo band
@apiDescription  Lấy danh sách chức năng theo band
<li>C<PERSON> hỗ trợ sắp xếp theo: <code>tên, đường dẫn</code>;</li>
<li>Có hỗ trợ tìm kiếm theo tên chức năng;</li>
<li>Có hỗ trợ phân trang;</li>
@apiGroup Function
@apiVersion 1.0.0
@apiName GetFuncs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse lang_success
@apiUse paging
@apiUse search
@apiUse order_sort

@apiSuccess     {Function[]}    data                        Danh sách chức năng của hệ thống.

@apiSuccess    (Function)     {String}        id                     Id của chức năng.
@apiSuccess    (Function)     {String}        name                   Tên của chức năng.
@apiSuccess    (Function)     {String}        description            Mô tả của chức năng.
@apiSuccess    (Function)     {String}        path                   Đường dẫn đến chức năng. Dành riêng cho client là kiểu WebCRM.
@apiSuccess    (Function)     {String}        parent                 Xâu theo format để quy định vị trí của chức năng trong menu.
@apiSuccess    (Function)     {String}        [icon_name]            Tên icon đại diện cho chức năng. Tên icon sử dụng theo kiểu font. Ví dụ: <code>fas fa-edit</code> (Font Awesome Icons)
@apiSuccess    (Function)     {Number}        [type]                 Xác định function sẽ có hiệu lực trên ứng dụng nào.
                                                                    <li><code>1-MerchantCRM</code>: function được phép chạy trên ứng dụng Merchant CRM;</li>
                                                                    <li><code>2-AdminCRM</code>: function được phép chạy trên ứng dụng Admin CRM;</li>
                                                                    <li><code>3-PartnerMobileApp</code>: function được phép chạy trên ứng dụng mobile Partner;</li>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data":[
        {
            "id": "7a810df9-7be5-486f-bd4e-1d0d825d593d",
            "name": "Quản lý cửa hàng",
            "description": "Chức năng quản lý danh sách cửa hàng.",
            "path":"customer-care/content/shop",
            "parent":"CSKH::QLND",
            "icon_name":"fas fa-home",
            "type":1,
            "create_time": "2017-08-07T04:02:28.002Z",
            "update_time": "2017-08-07T04:02:28.002Z",
            "actions": [
                {
                    "id": 1,
                    "name_vi": "Thêm",
                    "name_en": "Add",
                    "description": "Thêm item"
                },
                {
                    "id": 2,
                    "name_vi": "Sửa",
                    "name_en": "Edit",
                    "description": "Sửa item"
                },
                {
                    "id": 3,
                    "name_vi": "Xoá",
                    "name_en": "Delete",
                    "description": "Xoá item"
                }
            ]
        },
        {
            "id": "d699b141-375e-4235-b91f-17850b806d03",
            "name": "Quản lý sản phẩm",
            "description": "Chức năng quản lý danh sách sản phẩm.",
            "path":"customer-care/content/product",
            "parent":"CSKH::QLND",
            "icon_name":"fas fa-home",
            "type":1,
            "create_time": "2017-08-07T04:02:28.002Z",
            "update_time": "2017-08-07T04:02:28.002Z",
            "actions": [
                {
                    "id": 1,
                    "name_vi": "Thêm",
                    "name_en": "Add",
                    "description": "Thêm item"
                },
                {
                    "id": 2,
                    "name_vi": "Sửa",
                    "name_en": "Edit",
                    "description": "Sửa item"
                },
                {
                    "id": 3,
                    "name_vi": "Xoá",
                    "name_en": "Delete",
                    "description": "Xoá item"
                }
            ]
        }
    ],
    "sort":"name_vi",
    "order":"asc",
    "paging": {
    ...
    }
}
"""

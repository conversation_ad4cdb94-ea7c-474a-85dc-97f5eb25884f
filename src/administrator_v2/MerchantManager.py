#!/usr/bin/python
# -*- coding: utf8 -*-

# *********************************************************************************************************************************
# ************************************************** API LẤY DANH SÁCH SUB BRAND **************************************************
# *********************************************************************************************************************************
# *********************************************************************************************************************************
"""
@api {get} /merchants/sub-brands Lấy danh sách sub-brand của merchant
@apiDescription Lấy danh sách sub-brand của merchant
@apiVersion 2.0.0
@apiGroup MerchantModule
@apiName GetMerchantSubBrand

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse search


@apiSuccess  {ArrayObject} data Danh sách sub-brand
@apiSuccess (data) {string} id                      UUID sub-brand
@apiSuccess (data) {string} name                    Tên sub-brand
@apiSuccess (data) {string} avatar                  Ảnh đại diện sub-brand
@apiSuccess (data) {string} status                  Trạng thái của sub-brand,<code>3: Ẩn</code>, <code>2: Hiển thị</code>
@apiSuccess (data) {number} total_module            Tổng số nhóm chức năng
@apiSuccess (data) {ArrayObject} modules            Nhóm chức năng
@apiSuccess (data) {Object} account                 Đầu mối liên lạc

@apiSuccess (modules) {string} id                      UUID chức năng
@apiSuccess (modules) {string} name                    Tên chức năng

@apiSuccess (account) {string} id                      UUID đầu mối liên lạc
@apiSuccess (account) {string} phone_number            Số điện thoại 
@apiSuccess (account) {string} email                   Email 
@apiSuccess (account) {string} address                 Địa chỉ

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "mesage": "request thành công.",
  "data": [
    {
      "id": "********-3b4f-4dfc-a863-a2087280be9b",
      "name": "Brand 1",
      "avatar": "https://.....",
      "account":{
        "id": "********-3b4f-4dfc-a863-a2087280be9b",
	    "fullname": "Tran Van A B",
	    "email": "<EMAIL>",
	    "phone_number": "036333...."
      },
      "status": 2,
      "modules": [
        {
            "id": "********-3b4f-4dfc-a863-a2087280be9b",
            "name": "Trang chủ"
        }
      ],
      total_module: 13
    },
    ...
  ] 
}
"""

# *********************************************************************************************************************************
# ************************************************** API UPDATE STATUS SUB BRAND **************************************************
# *********************************************************************************************************************************
# *********************************************************************************************************************************
"""
@api {put} /merchants/sub-brands/status Update status sub-brand của merchant 
@apiDescription Lấy danh sách sub-brand của merchant
@apiVersion 2.0.0
@apiGroup MerchantModule
@apiName UpdateStatusSub-brands

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}    ids              Tập hợp các UUID của sub-brand.
@apiParam      (Body:)     {Number}    status           Trạng thái của sub-brand,<code>3: Ẩn</code>, <code>2: Hiển thị</code>

@apiParamExample {json} Body example
{
	"ids": [
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703"
	],
	"status": 2
}

"""

# ***********************************************************************************************************************
# ************************************************** API TẠO SUB-BRAND CỦA MERCHANT **************************************************
# ***********************************************************************************************************************
"""
@api {post} /merchants/sub-brands/create Tạo một Sub-brand của merchant 
@apiDescription Tạo một Sub-brand của merchant 
@apiVersion 2.0.0
@apiGroup MerchantModule
@apiName Post

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {File}      avatar                          	Ảnh đại diện
@apiParam      (Body:)     {String}    infor                            Thông tin sub-brands, String object json


@apiParam (infor) {string}      name             Tên thương hiệu
@apiParam (infor) {string}      description      Mô tả
@apiParam (infor) {string}      phone_number     Số điện thoại(hotline)
@apiParam (infor) {string}      email            Email thương hiệu
@apiParam (infor) {string}      status           Trạng thái của sub-brand,<code>3: Ẩn</code>, <code>2: Hiển thị</code>
@apiParam (infor) {string}      module_ids       Mảng các module_id
@apiParam (infor) {Object}      account          Thông tin tài khoản

@apiParam (Object) {string}     username        Tên tài khoản 
@apiParam (Object) {string}     fullname        Họ và tên  
@apiParam (Object) {string}     email           Email  
@apiParam (Object) {string}     phone_number    Số điện thoại  
@apiParam (Object) {string}     password        Mật khẩu 

@apiParamExample {json} Infor example
{
	"name": "AAAAA",
	"description": "",
	"phone_number": "036333....",
	"email": "<EMAIL>",
	"status": 1,
	"module_ids":[
        "1234-1234-abcd-abcd",
        "1234-1234-abcd-abcc"
	],
	"status": 2,
	"description": "",
	"account":{
	    "username": "BBBBB",
	    "fullname": "Tran Van A B",
	    "email": "<EMAIL>",
	    "phone_number": "036333....",
	    "password": "BBBBB",
	}
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{     
    "code": 200,
    "message": "request thành công.",
    "data":
        {
          "id": "********-3b4f-4dfc-a863-a2087280be9b",
          "name": "Brand 1",
          "avatar": "https://.....",
          "account":{
            "id": "********-3b4f-4dfc-a863-a2087280be9b",
            "fullname": "Tran Van A B",
            "email": "<EMAIL>",
            "phone_number": "036333...."
          },
          "status": 2,
          "modules": [
            {
                "id": "********-3b4f-4dfc-a863-a2087280be9b",
                "name": "Trang chủ"
            }
          ],
          total_module: 13
        }
}
"""

# ********************************************************************
# ********************** CẬP NHẬP SUB-BRAND CỦA MERCHANT **********************
# ********************************************************************

"""
@api {PUT} /merchants/sub-brands/<sub-brand_id>/update  Cập nhập Sub-brand của merchant
@apiDescription Cập nhập Sub-brand của merchant. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiName UpdateSub-brands
@apiGroup MerchantModule
@apiVersion 2.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {File}      avatar                          	File ảnh đại diện sub-brands
@apiParam      (Body:)     {String}    infor                            Thông tin sub-brands

@apiParam (infor) {string}      name             Tên thương hiệu
@apiParam (infor) {string}      description      Mô tả
@apiParam (infor) {string}      phone_number     Số điện thoại(hotline)
@apiParam (infor) {string}      email            Email thương hiệu
@apiParam (infor) {string}      status           Trạng thái của sub-brand,<code>3: Ẩn</code>, <code>2: Hiển thị</code>
@apiParam (infor) {string}      [module_ids]     Mảng các module_id
@apiParam (infor) {Object}      account          Thông tin tài khoản

@apiParam (account) {string}     fullname        Họ và tên  
@apiParam (account) {string}     email           Email  
@apiParam (account) {string}     phone_number    Số điện thoại  
@apiParam (account) {string}     password        Mật khẩu 

@apiParamExample {json} Infor example
{
	"name": "AAAAA",
	"description": "",
	"phone_number": "036333....",
	"email": "<EMAIL>",
	"status": 1,
	"module_ids":[
        "1234-1234-abcd-abcd",
        "1234-1234-abcd-abcc"
	],
	"status": 2,
	"description": "",
	"account":{
	    "fullname": "Tran Van A B",
	    "email": "<EMAIL>",
	    "phone_number": "036333....",
	    "password": "BBBBB",
	}
}


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{     
    "code": 200,
    "message": "request thành công.",
    "data":
        {
          "id": "********-3b4f-4dfc-a863-a2087280be9b",
          "name": "Brand 1",
          "avatar": "https://.....",
          "account":{
            "id": "********-3b4f-4dfc-a863-a2087280be9b",
            "fullname": "Tran Van A B",
            "email": "<EMAIL>",
            "phone_number": "036333...."
          },
          "status": 2,
          "modules": [
            {
                "id": "********-3b4f-4dfc-a863-a2087280be9b",
                "name": "Trang chủ"
            }
          ],
          total_module: 13
        }
}
"""

# ********************************************************************
# ********************** Lấy chi tiết Sub-brand của merchant **********************
# ********************************************************************
"""
@api {GET} /merchants/sub-brands/<sub-brand_id> Lấy chi tiết Sub-brand của merchant
@apiDescription Lấy chi tiết Sub-brand của merchant
@apiName GetDetailSub-brands
@apiGroup MerchantModule
@apiVersion 2.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiParam (Body) {string}      name             Tên thương hiệu
@apiParam (Body) {string}      description      Mô tả
@apiParam (Body) {string}      phone_number     Số điện thoại(hotline)
@apiParam (Body) {string}      email            Email thương hiệu
@apiParam (Body) {string}      status           Trạng thái của sub-brand,<code>3: Ẩn</code>, <code>2: Hiển thị</code>
@apiParam (Body) {string}      [module_ids]     Mảng các module_id
@apiParam (Body) {Object}      account          Thông tin tài khoản

@apiParam (Object) {string}     username        Tên tài khoản 
@apiParam (Object) {string}     fullname        Họ và tên  
@apiParam (Object) {string}     email           Email  
@apiParam (Object) {string}     phone_number    Số điện thoại  
@apiParam (Object) {string}     password        Mật khẩu 

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": 
        {
            "name": "AAAAA",
            "avatar": "https://.....",
            "description": "",
            "phone_number": "036333....",
            "email": "<EMAIL>",
            "status": 2,
            "module_ids":[
                "1234-1234-abcd-abcd",
                "1234-1234-abcd-abcc"
            ],
            "status": 1,
            "description": "",
            "account":{
                "fullname": "Tran Van A B",
                "email": "<EMAIL>",
                "phone_number": "036333....",
            }
        }
}

"""

# *********************************************************************************************************************************
# *********************************************************************************************************************************
# *********************************************************************************************************************************
# #1.0.0
"""
@api {get} /merchants/sub-brands/get_all Lấy All danh sách subbrand của merchant và merchant
@apiDescription Lấy All danh sách subbrand của merchant và merchant
@apiVersion 1.0.0
@apiName GetAllSub-brands
@apiGroup MerchantModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "brand": {
            "id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "PingcomShop",
         "sub_brand": [
                {
                    "id": "867c7d4e-726e-40aa-b22c-fc3c033f1e7a",
                    "name": "Mobio"
                },
                {
                    "id": "23bff849-7f65-4497-9dcd-89e1c76f00f7",
                    "name": "AAAAA"
                },
                {
                    "id": "c5d3b0d8-01e4-4acb-8505-3ff009125a6f",
                    "name": "BIBO MART"
                },
                {
                    "id": "4bd3db9e-731e-4270-b43e-2e2fefbe3260",
                    "name": "test_sub_brand_4"
                },
                {
                    "id": "b0b34fc6-1d6e-405f-ac3a-b8de459cde2c",
                    "name": "test_sub_brand_3"
                },
                {
                    "id": "c2207852-cb8a-4749-9a83-cbaca17d693d",
                    "name": "Hoang Anh"
                }
            ]
        }
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
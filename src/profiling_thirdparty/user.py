#!/usr/bin/python
# -*- coding: utf8 -*-
************************************ Upsert Profile ***********************************
* version: 1.0.0                                                                      *
* version: 1.0.1                                                                      *
* version: 1.0.2                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/customers/actions/upsert Upsert Profile.
@apiDescription Upsert Profile
@apiGroup Customers
@apiVersion 1.0.0
@apiName UpsertProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.

@apiParam   (Body_Bussiness:)    {int=0,1}   is_company=0         Xác định profile này là Business hay Individual
<li><code>0:  Individual</code></li>
<li><code>1:  Business</code></li>
@apiParam   (Body_Bussiness:)    {String[]}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (Body_Bussiness:)    {Array}         [social_user]        Danh sách các mạng xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body_Bussiness:)    {String[]}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (Body_Bussiness:)    {String}        tax_code             Mã số thuế của Business.
@apiParam   (Body_Bussiness:)    {Int}           created_account_type             Nguồn import Profile.
<li><code>15: PHONGVU_ODOO</code></li>
<li><code>16: PHONGVU_MAGENTO</code></li>
<li><code>17: PHONGVU_INHOUSE</code></li>
<li><code>18: PHONGVU_ASIA</code></li>
@apiParam   (Body_Bussiness:)    {Array}          [bank_acc]             Danh sách các tài khoản ngân hàng mà profile này sở hữu. Xem Data_Bank_Account bên dưới.
@apiParam   (Body_Bussiness:)    {String}         fullname             Tên của Business.
@apiParam   (Body_Bussiness:)    {String}         [address]             Địa chỉ.
@apiParam   (Body_Bussiness:)    {String}         [tax_name]             Tên người nộp thuế.
@apiParam   (Body_Bussiness:)    {String}         [tax_address]          Địa chỉ kê khai nộp thuế.
@apiParam   (Body_Bussiness:)    {String}         [business_license]          Số đăng ký kinh doanh.
@apiParam   (Body_Bussiness:)    {int}            [business_type]          Loại hình doanh nghiệp.
<li><code>5:	SME (Doanh nghiệp vừa và nhỏ)</code></li>
<li><code>6:	Doanh nghiệp nước ngoài</code></li>
<li><code>7:	Nhà nước nói chung</code></li>
<li><code>8:	Doanh nghiệp NGO(non-governmental organization)</code></li>
<li><code>9:	Hành chính công (các VP nhà nước: UBND phường/xã...)</code></li>
@apiParam   (Body_Bussiness:)    {String}         [fax]          Số Fax.

@apiParam   (Body:)    {int=0,1}   is_company=0         Xác định profile này là Business hay Individual
<li><code>0:  Individual</code></li>
<li><code>1:  Business</code></li>
@apiParam   (Body:)    {String[]}      [face_id]            face_id
@apiParam   (Body:)    {String[]}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (Body:)    {Array}         [social_user]        Danh sách các mạng xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)    {String[]}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (Body:)    {Int}           created_account_type             Nguồn import Profile.
<li><code>15: PHONGVU_ODOO</code></li>
<li><code>16: PHONGVU_MAGENTO</code></li>
<li><code>17: PHONGVU_INHOUSE</code></li>
<li><code>18: PHONGVU_ASIA</code></li>
@apiParam   (Body:)    {Array}          [bank_acc]             Danh sách các tài khoản ngân hàng mà profile này sở hữu. Xem Data_Bank_Account bên dưới.
@apiParam   (Body:)    {Int}          [gender]             Giới tính.
<li><code>1: UNKNOWN</code></li>
<li><code>2: MALE</code></li>
<li><code>3: FEMALE</code></li>
@apiParam   (Body:)    {String}          [birthday]             Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (Body:)    {String}          fullname             Tên đầy đủ.
@apiParam   (Body:)    {String}          [address]       Địa chỉ.
@apiParam   (Body:)     {Int}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {Int}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (Body:)     {Int}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>


@apiParam   (Data_Social_User:)    {String}         social_id        Id của mạng mạng xã hội.
@apiParam   (Data_Social_User:)    {Int}            social_type      Mạng xã hội.
<li><code>1: FACEBOOK</code></li>
<li><code>2: ZALO</code></li>
<li><code>3: INSTAGRAM</code></li>
<li><code>4: YOUTUBE</code></li>
@apiParam   (Data_Social_User:)    {String}         [access_token]      Access_Token của mạng xã hội.
@apiParamExample {json} SocialUser-Example:
[{"social_id": "social_id", "social_type": 1, "access_token": "access_token"}]

@apiParam   (Data_Bank_Account:)    {String}         [bank_code]        Mã nhận dạng của ngân hàng.
@apiParam   (Data_Bank_Account:)    {String}         [bank_name]        Tên của ngân hàng.
@apiParam   (Data_Bank_Account:)    {String}         [acc_name]        Tên tài khoản ngân hàng.
@apiParam   (Data_Bank_Account:)    {String}         [acc_code]        Mã tài khoản ngân hàng.
@apiParam   (Data_Bank_Account:)    {String}         [branch_name]        Tên của chi nhánh thuộc ngân hàng.
@apiParamExample {json} Data_Bank_Account-Example:
[{"bank_code": "BFTVVNVX", "bank_name": "Bank for Foreign Trade of Vietnam", "branch_name": "Hochiminh Branch", "acc_name": "Nguyen Van A", "acc_code": "*************"}]


@apiParamExample [json] Body example:
{
    "fullname": "andrew",
    "is_company": 0,
    "phone_number": ["**********"],
    "created_account_type": 15,
    "email": ["<EMAIL>"],
    "address": "Hà Nội",
    "gender": 2,
    "birthday": "1989-12-21"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "data":[
        {
            "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
            "merchant_id": "4a9f6012-5627-4239-b281-4adb85c00827",
            "address": "",
            "avatar": "",
            "birthday": "1993-10-10T00:00:00",
            "birth_year": 1993,
            "email": ["<EMAIL>"],
            "fax": "",
            "gender": 1,
            "hobby": [""],
            "job": [""],
            "marital_status": 1,
            "name": "andrew",
            "nation": [""],
            "phone_number": (""),
            "province_code": [""],
            "tax_code": "",
            "ward_code": [""],
            "workplace": ""
        }
    ]
}
"""

"""
@api {post} [HOST]/profiling/v3.0/customers/actions/upsert Upsert Profile.
@apiDescription Upsert Profile
@apiGroup Customers
@apiVersion 1.0.1
@apiName UpsertProfile
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.
@apiParam   (Query:)    {String}  [group]     Chuỗi query nhận diện group của Merchant.

@apiParam   (Body:)    {String[]}      [face_id]            face_id
@apiParam   (Body:)    {String[]}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (Body:)    {Array}         [social_user]        Danh sách các mạng xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)    {String[]}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (Body:)    {Int}          [gender]             Giới tính.
<li><code>1: UNKNOWN</code></li>
<li><code>2: MALE</code></li>
<li><code>3: FEMALE</code></li>
@apiParam   (Body:)    {String}          [birthday]             Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (Body:)    {String}          fullname             Tên đầy đủ.
@apiParam   (Body:)    {String}          [address]       Địa chỉ.
@apiParam   (Body:)     {Int}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {Int}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (Body:)     {Int}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>


@apiParam   (Data_Social_User:)    {String}         social_id        Id của mạng mạng xã hội.
@apiParam   (Data_Social_User:)    {Int}            social_type      Mạng xã hội.
<li><code>1: FACEBOOK</code></li>
<li><code>2: ZALO</code></li>
<li><code>3: INSTAGRAM</code></li>
<li><code>4: YOUTUBE</code></li>
@apiParam   (Data_Social_User:)    {String}         [access_token]      Access_Token của mạng xã hội.
@apiParamExample {json} SocialUser-Example:
[{"social_id": "social_id", "social_type": 1, "access_token": "access_token"}]

@apiParamExample [json] Body example:
{
    "fullname": "andrew",
    "is_company": 0,
    "phone_number": ["**********"],
    "created_account_type": 15,
    "email": ["<EMAIL>"],
    "address": "Hà Nội",
    "gender": 2,
    "birthday": "1989-12-21"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "data":[
        {
            "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
            "merchant_id": "4a9f6012-5627-4239-b281-4adb85c00827",
            "address": "",
            "avatar": "",
            "birthday": "1993-10-10T00:00:00",
            "birth_year": 1993,
            "email": ["<EMAIL>"],
            "fax": "",
            "gender": 1,
            "hobby": [""],
            "job": [""],
            "marital_status": 1,
            "name": "andrew",
            "nation": [""],
            "phone_number": (""),
            "province_code": [""],
            "tax_code": "",
            "ward_code": [""],
            "workplace": ""
        }
    ]
}
"""

"""
@api {post} [HOST]/profiling/v3.0/customers/actions/upsert Upsert Profile.
@apiDescription Upsert Profile
@apiGroup Customers
@apiVersion 1.0.2
@apiName UpsertProfile
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.
@apiParam   (Query:)    {String}  [group]     Chuỗi query nhận diện group của Merchant.

@apiParam   (Body:)    {String[]}      [face_id]            face_id
@apiParam   (Body:)    {String[]}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (Body:)    {Array}         [social_user]        Danh sách các mạng xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)    {String[]}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (Body:)    {Int}          [gender]             Giới tính.
<li><code>1: UNKNOWN</code></li>
<li><code>2: MALE</code></li>
<li><code>3: FEMALE</code></li>
@apiParam   (Body:)    {String}          [birthday]             Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (Body:)    {String}          name             Tên đầy đủ.
@apiParam   (Body:)    {Int}          created_account_type             Kiểu tạo tài khoản.
<br/><br/>Allowed values:<br/>
<li><code>0: Web CEM</code></li>
<li><code>21: Chat Tool</code></li>

@apiParam   (Body:)    {String}          [address]       Địa chỉ.
@apiParam   (Body:)     {Int}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {Int}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (Body:)     {Int}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>


@apiParam   (Data_Social_User:)    {String}         social_id        Id của mạng mạng xã hội.
@apiParam   (Data_Social_User:)    {Int}            social_type      Mạng xã hội.
<li><code>1: FACEBOOK</code></li>
<li><code>2: ZALO</code></li>
<li><code>3: INSTAGRAM</code></li>
<li><code>4: YOUTUBE</code></li>
@apiParam   (Data_Social_User:)    {String}         [access_token]      Access_Token của mạng xã hội.
@apiParamExample {json} SocialUser-Example:
[{"social_id": "social_id", "social_type": 1, "access_token": "access_token"}]

@apiParamExample [json] Body example:
{
    "fullname": "andrew",
    "is_company": 0,
    "phone_number": ["**********"],
    "created_account_type": 0,
    "email": ["<EMAIL>"],
    "address": "Hà Nội",
    "gender": 2,
    "birthday": "1989-12-21"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "data":[
        {
            "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
            "merchant_id": "4a9f6012-5627-4239-b281-4adb85c00827",
            "address": "",
            "avatar": "",
            "birthday": "1993-10-10T00:00:00",
            "birth_year": 1993,
            "email": ["<EMAIL>"],
            "fax": "",
            "gender": 1,
            "hobby": [""],
            "job": [""],
            "marital_status": 1,
            "name": "andrew",
            "nation": [""],
            "phone_number": (""),
            "province_code": [""],
            "tax_code": "",
            "ward_code": [""],
            "workplace": ""
        }
    ]
}
"""

************************************* Bulk Upsert Profile **********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/customers/actions/bulk_upsert Bulk Upsert Profile.
@apiDescription Api hỗ trợ upsert nhiều profile. Lưu ý với kiểu dữ liệu datetime, chỉ support kiểu hiển thị là date_picker và thời gian gửi lên theo UTC.
@apiGroup Customers
@apiVersion 1.0.0
@apiName BulkUpsertProfile
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [group]     Chuỗi query nhận diện group của Merchant.

@apiParam   (Body:)    {Array}      dynamic_fields       Danh sách các dynamic fields.
@apiParam   (dynamic_fields:)    {String}          field_name             Tên field. Không được trùng với các field khác. MIN: 3, MAX: 50
@apiParam   (dynamic_fields:)    {String}          field_property         Kiểu dữ liệu của field.
<li><code>string</code></li>
<li><code>integer</code></li>
<li><code>datetime</code></li>
@apiParam   (dynamic_fields:)    {String}          display_type           Kiểu hiển thị của field.
<li><code>single_line</code></li>
<li><code>multi_line</code></li>
<li><code>date_picker</code></li>
@apiParam   (Body:)    {Array}      data                 Danh sách các profiles cần upsert. MAX: 50 rows
@apiParam   (data:)    {String}          name             Tên đầy đủ.
@apiParam   (data:)    {String[]}      [face_id]            face_id
@apiParam   (data:)    {String[]}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (data:)    {String[]}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (data:)    {Int}          [gender]             Giới tính.
<li><code>1: UNKNOWN</code></li>
<li><code>2: MALE</code></li>
<li><code>3: FEMALE</code></li>
@apiParam   (data:)    {String}          [birthday]             Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (data:)    {String}          [address]       Địa chỉ.

@apiParamExample [json] Body example:
{ 
  "dynamic_fields":[
    {
      "field_name":"products",
      "field_property": "string",
      "display_type": "multi_line"
    },
    {
      "field_name":"ngày cuối cùng đăng nhập",
      "field_property": "datetime",
      "display_type": "date_picker"
    },
    {
      "field_name":"Tổng số tiền đã chi tiêu",
      "field_property": "integer",
      "display_type": "single_line"
    }
  ],
  "data":[
      {
          "name": "andrew",
          "phone_number": ["**********"],
          "created_account_type": 15,
          "email": ["<EMAIL>"],
          "address": "Hà Nội",
          "gender": 2,
          "birthday": "21-01-1989",
          "sản phẩm đã mua": ["laptop asus"],
          "ngày cuối cùng đăng nhập": "2020-02-12 10:55:51",
          "Tổng số tiền đã chi tiêu": ********
      }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công."
}
"""


************************************ List Workflow ***************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /workflow/list Lấy danh sách workflow
@apiGroup Workflow
@apiVersion 1.0.0
@apiName ListWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}    [target_type]     Đối tượng workflow (Gửi lên nhiều đối tượng cách nhau bởi dấy phẩy). Giá trị: <br/>
                                                                                 <code>USER</code>=User<br/>
                                                                                 <code>MAILBOX</code>=Mailbox<br/>
                                                                                 <code>TASK</code>=Task<br/>
                                                                                 <code>PROFILE</code>=Profile<br/>
                                                                                 <code>OPPTY</code>=Oppty<br/>
@apiParam   (Query:)     {String}    [workflow_type]  Loại workflow (Gửi lên nhiều loại cách nhau bởi dấy phẩy). Giá trị: <br/><code>EVENT</code>=Khi phát sinh event
                                                                                                                            <br/><code>SCHEDULE</code>=Lịch trình
@apiParam   (Query:)    {String}    [status]      Trạng thái workflow (Có thể gửi 1 hoặc nhiều, cách nhau bởi dấu phẩy). Giá trị: <br/><code>PROCESSING</code>=Đang diễn ra
                                                                                                                                <br/><code>PROCESS_WAITING</code>=Chờ đến lịch
                                                                                                                                <br/><code>DRAFT</code>=Nháp
                                                                                                                                <br/><code>FINISH</code>=Đã hoàn thành
                                                                                                                                <br/><code>PAUSED</code>=Tạm dừng
@apiParam   (Query:)    {String}    [search_text]     Chuỗi tìm kiếm (Tên workflow,...)
@apiParam   (Query:)    {String}    [after_token]     Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)    {Number}    [per_page]    Số phần tử trên một page. Mặc định: 10
@apiParam   (Query:)    {String}    [start_created_time] Thời gian tạo bắt đầu tìm kiếm. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>    
@apiParam   (Query:)     {String}    [end_created_time]  Thời gian tạo kết thúc tìm kiếm. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 

@apiParam   (Body:)    {ArrayString}    [target_type]     Đối tượng workflow. Giá trị: <br/>
                                                                                 <code>USER</code>=User<br/>
                                                                                 <code>MAILBOX</code>=Mailbox<br/>
                                                                                 <code>TASK</code>=Task<br/>
                                                                                 <code>PROFILE</code>=Profile<br/>
                                                                                 <code>OPPTY</code>=Oppty<br/>
@apiParam   (Body:)     {ArrayString}    [workflow_type]  Loại workflow. Giá trị:   <br/><code>EVENT</code>=Khi phát sinh event
                                                                                    <br/><code>SCHEDULE</code>=Lịch trình
@apiParam   (Body:)    {ArrayString}    [status]      Trạng thái workflow. Giá trị: <br/><code>PROCESSING</code>=Đang diễn ra
                                                                                    <br/><code>PROCESS_WAITING</code>=Chờ đến lịch
                                                                                    <br/><code>DRAFT</code>=Nháp
                                                                                    <br/><code>FINISH</code>=Đã hoàn thành
                                                                                    <br/><code>PAUSED</code>=Tạm dừng
@apiParam   (Body:)    {String}    [search_text]     Chuỗi tìm kiếm (Tên workflow,...)
@apiParam   (Body:)    {String}    [after_token]     Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Body:)    {Number}    [per_page]    Số phần tử trên một page. Mặc định: 10
@apiParam   (Body:)    {String}    [start_created_time] Thời gian tạo bắt đầu tìm kiếm. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>    
@apiParam   (Body:)     {String}    [end_created_time]  Thời gian tạo kết thúc tìm kiếm. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Body:)    {ArrayString}    [created_user]     Người tạo

@apiParamExample    {json}  Body:
{
    "target_type": ["MAILBOX", "OPPTY"],
    "workflow_type": ["EVENT", "SCHEDULE"],
    "status": ["DRAFT", "PROCESSING"],
    "search_text": "Nội dung cần tìm kiếm",
    "after_token": "abcdxyz",
    "per_page": 10,
    "start_created_time": "2025-02-28T10:00:00+00:00",
    "end_created_time": "2025-02-28T10:00:00+00:00",
    "created_user": ["c3d6e459-24f4-405f-8733-b0e9c345fc3e", "8c13b293-c009-4ecb-83b7-016a47960526"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "workflow_id": "4c06480e-0e1e-4d2b-83e1-6660f6b3a2bd"
            "workflow_name": "Ten workflow"
            "workflow_description" : "Mo ta workflow",
            "status" : "PROCESSING",
            "target_type" : "USER",
            "workflow_type": "EVENT",
            "updated_time": "2023-03-15T04:02:28.002Z",
            "updated_user": "653f6d66-c6ef-11ed-8fe8-c7a3239de083"
        },
        {
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "workflow_id": "4c06480e-0e1e-4d2b-83e1-6660f6b3a2bd"
            "workflow_name": "Ten workflow"
            "workflow_description" : "Mo ta workflow",
            "status" : "PROCESSING",
            "target_type" : "USER",
            "workflow_type": "SCHEDULE",
            "updated_time": "2023-03-15T04:02:28.002Z",
            "updated_user": "653f6d66-c6ef-11ed-8fe8-c7a3239de083"
        }
    ],
    "paging": {
        "after_token": "YXNkaGZha2RoZmFrZGZh"
    }
}
"""
************************************ Summary Workflow ************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /workflow/summary Thống kê số lượng workflow theo trạng thái
@apiGroup Workflow
@apiVersion 1.0.0
@apiName SummaryWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}    [target_type]     Đối tượng workflow (Gửi lên nhiều đối tượng cách nhau bởi dấy phẩy). Giá trị: <br/>
                                                                                 <code>USER</code>=User<br/>
                                                                                 <code>MAILBOX</code>=Mailbox<br/>
                                                                                 <code>TASK</code>=Task<br/>
                                                                                 <code>PROFILE</code>=Profile<br/>
                                                                                 <code>OPPTY</code>=Oppty<br/>
@apiParam   (Query:)     {String}    [workflow_type]  Loại workflow (Gửi lên nhiều loại cách nhau bởi dấy phẩy). Giá trị: <br/><code>EVENT</code>=Khi phát sinh event
                                                                                                                            <br/><code>SCHEDULE</code>=Lịch trình
@apiParam   (Query:)    {String}    [search_text]     Chuỗi tìm kiếm (Tên workflow,...)
@apiParam   (Query:)    {String}    [start_created_time] Thời gian tạo bắt đầu tìm kiếm. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>    
@apiParam   (Query:)     {String}    [end_created_time]  Thời gian tạo kết thúc tìm kiếm. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Query:)    {String}    [created_user]     Người tạo (Có thể gửi 1 hoặc nhiều, cách nhau bởi dấu phẩy)

@apiParam   (Body:)    {ArrayString}    [target_type]     Đối tượng workflow. Giá trị: <br/>
                                                                                 <code>USER</code>=User<br/>
                                                                                 <code>MAILBOX</code>=Mailbox<br/>
                                                                                 <code>TASK</code>=Task<br/>
                                                                                 <code>PROFILE</code>=Profile<br/>
                                                                                 <code>OPPTY</code>=Oppty<br/>
@apiParam   (Body:)     {ArrayString}    [workflow_type]  Loại workflow. Giá trị:   <br/><code>EVENT</code>=Khi phát sinh event
                                                                                    <br/><code>SCHEDULE</code>=Lịch trình
@apiParam   (Body:)    {String}    [search_text]     Chuỗi tìm kiếm (Tên workflow,...)
@apiParam   (Body:)    {String}    [start_created_time] Thời gian tạo bắt đầu tìm kiếm. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>    
@apiParam   (Body:)     {String}    [end_created_time]  Thời gian tạo kết thúc tìm kiếm. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Body:)    {ArrayString}    [created_user]     Người tạo

@apiParamExample    {json}  Body:
{
    "target_type": ["MAILBOX", "OPPTY"],
    "workflow_type": ["EVENT", "SCHEDULE"],
    "search_text": "Nội dung cần tìm kiếm",
    "start_created_time": "2025-02-28T10:00:00+00:00",
    "end_created_time": "2025-02-28T10:00:00+00:00",
    "created_user": ["c3d6e459-24f4-405f-8733-b0e9c345fc3e", "8c13b293-c009-4ecb-83b7-016a47960526"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "count": 1,
            "status": "DRAFT"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
************************************ Create Workflow *************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /workflow/create Tạo workflow
@apiGroup Workflow
@apiVersion 1.0.0
@apiName CreateWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    workflow_name  Tên workflow cần tạo
@apiParam   (Body:)    {String}    [workflow_description]  Mô tả workflow
@apiParam   (Body:)    {String}    target_type  Đối tượng workflow. Giá trị: <br/>
                                                                                 <code>USER</code>=User<br/>
                                                                                 <code>MAILBOX</code>=Mailbox<br/>
                                                                                 <code>TASK</code>=Task<br/>
                                                                                 <code>PROFILE</code>=Profile<br/>
                                                                                 <code>OPPTY</code>=Oppty<br/>
@apiParam   (Body:)     {String}    workflow_type  Loại workflow. Giá trị: <br/><code>EVENT</code>=Khi phát sinh event
                                                                            <br/><code>SCHEDULE</code>=Lịch trình
@apiParam   (Body:)    {String}    [template_id]  Mã template để tạo workflow
@apiParamExample    {json}  Body:
{
    "workflow_name": "Tên workflow",
    "workflow_description": "Mô tả workflow",
    "target_type": "MAILBOX",
    "workflow_type": "EVENT"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "workflow_id": "4c06480e-0e1e-4d2b-83e1-6660f6b3a2bd"
        "workflow_name": "Ten workflow"
        "workflow_description" : "Mo ta workflow",
        "status" : "DRAFT",
        "target_type" : "USER",
        "workflow_type": "SCHEDULE",
        "updated_time": "2023-03-15T04:02:28.002Z",
        "updated_user": "653f6d66-c6ef-11ed-8fe8-c7a3239de083"
    }
}
"""
************************************ Update Workflow *************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {put} /workflow/update Sửa workflow
@apiGroup Workflow
@apiVersion 1.0.0
@apiName UpdateWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    workflow_id  Mã workflow cần sửa
@apiParam   (Body:)    {String}    workflow_name  Tên workflow
@apiParam   (Body:)    {String}    [workflow_description]  Mô tả workflow
@apiParam   (Body:)    {String}    target_type  Đối tượng workflow. Giá trị: <br/>
                                                                                 <code>USER</code>=User<br/>
                                                                                 <code>MAILBOX</code>=Mailbox<br/>
                                                                                 <code>TASK</code>=Task<br/>
                                                                                 <code>PROFILE</code>=Profile<br/>
                                                                                 <code>OPPTY</code>=Oppty<br/>
@apiParam   (Body:)     {String}    workflow_type  Loại workflow. Giá trị: <br/><code>EVENT</code>=Khi phát sinh event
                                                                            <br/><code>SCHEDULE</code>=Lịch trình

@apiParamExample    {json}  Body:
{   
    "workflow_id": "965217ca-c6f2-11ed-8fe8-c7a3239de083",
    "workflow_name": "Tên workflow",
    "workflow_description": "Mô tả workflow",
    "workflow_type": "SCHEDULE",
    "target_type": "USER"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "workflow_id": "965217ca-c6f2-11ed-8fe8-c7a3239de083"
        "workflow_name": "Ten workflow"
        "workflow_description" : "Mo ta workflow",
        "status" : "DRAFT",
        "target_type" : "USER",
        "workflow_type": "SCHEDULE",
        "updated_time": "2023-03-15T04:02:28.002Z",
        "updated_user": "653f6d66-c6ef-11ed-8fe8-c7a3239de083"
    }
}
"""
********************************* Cập nhật trạng thái Workflow ***************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {put} /workflow/update/status Cập nhật trạng thái workflow
@apiGroup Workflow
@apiVersion 1.0.0
@apiName UpdateStatusWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    workflow_id  Mã workflow cần sửa
@apiParam   (Body:)    {String}    status  Trạng thái workflow. Giá trị: <br/><code>PROCESSING</code>=Đang diễn ra
                                                                        <br/><code>PROCESS_WAITING</code>=Chờ đến lịch
                                                                        <br/><code>DRAFT</code>=Nháp
                                                                        <br/><code>FINISH</code>=Đã hoàn thành
                                                                        <br/><code>PAUSED</code>=Tạm dừng
@apiParam   (Body:)    {String}    [finish_reason]  Lý do kết thúc workflow. (Chỉ dùng cho status=<code>FINISH</code>)

@apiParamExample    {json}  Body:
{   
    "workflow_id": "965217ca-c6f2-11ed-8fe8-c7a3239de083",
    "status": "DRAFT"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "workflow_id": "4c06480e-0e1e-4d2b-83e1-6660f6b3a2bd"
        "workflow_name": "Ten workflow"
        "workflow_description" : "Mo ta workflow",
        "status" : "DRAFT",
        "target_type" : "USER",
        "workflow_type": "SCHEDULE",
        "updated_time": "2023-03-15T04:02:28.002Z",
        "updated_user": "653f6d66-c6ef-11ed-8fe8-c7a3239de083"
    }
}
"""
************************************ Cấu hình Workflow ***********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /workflow/config Cấu hình workflow
@apiGroup Workflow
@apiVersion 1.0.0
@apiName ConfigWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    workflow_id  Mã workflow cần cấu hình
@apiParam   (Body:)    {Array}    [diagram]  Thông tin cấu hình dành cho Frontend sử dụng.
@apiParam   (Body:)    {ArrayObject}    nodes   Danh sách các thành phần chi tiết của workflow.
@apiParam   (Body:)    {String}    nodes.node_id  Mã định danh node
@apiParam   (Body:)    {String}    nodes.node_name   Tên node.
@apiParam   (Body:)    {String}    nodes.element_type  Nhóm các khối (Frontend sử dụng). Giá trị: <br/><code>OPERATION</code>=Thao tác<br/>
                                                                                                        <code>TRIGGER</code>=Trigger<br/>
                                                                                                        <code>SPECIFIC_CONDITION</code>=Kiểm tra điều kiện<br/>
                                                                                                        <code>OUTER_ACTION</code>=Giao tiếp với bên ngoài<br/>
                                                                                                        <code>INNER_ACTION</code>=Giao tiếp trong nội bộ<br/>
                                                                                                        <code>MANAGEMENT_ACTION</code>=Hành động quản lý<br/>
@apiParam   (Body:)    {String}    nodes.node_type  Kiểu node. Giá trị: <br/><code>ROOT</code>=node bắt đầu<br/>
                                                                            <code>TRIGGER</code>=node trigger<br/>
                                                                            <code>TRIGGER_EVENT</code>=node trigger phát sinh event<br/>
                                                                            <code>TRIGGER_SCHEDULE</code>=node trigger lịch trình<br/>
                                                                            <code>CONDITION_FILTER</code>=node thỏa mãn bộ lọc<br/>
                                                                            <code>CONDITION_FILTER_LIST_PROFILE</code>=node thỏa mãn bộ lọc profile<br/>
                                                                            <code>CONDITION_FILTER_LIST_USER</code>=node thỏa mãn bộ lọc user<br/>
                                                                            <code>CONDITION_FILTER_LIST_TASK</code>=node thỏa mãn bộ lọc task<br/>
                                                                            <code>CONDITION_MULTI_BRANCH</code>=node tách nhánh theo điều kiện <br/>
                                                                            <code>CONDITION_FILTER_PROFILE</code>=Kiểm tra thông tin profile<br/>
                                                                            <code>CONDITION_FILTER_ORDER</code>=Kiểm tra thông tin Đơn hàng trong profile<br/>
                                                                            <code>CONDITION_FILTER_COMPANY</code>=Kiểm tra thông tin công ty trong profile<br/>
                                                                            <code>CONDITION_FILTER_TICKET</code>=Kiểm tra thông tin ticket trong profile<br/>
                                                                            <code>CONDITION_FILTER_CALL</code>=Kiểm tra thông tin cuộc gọi trong profile<br/>
                                                                            <code>ACTION_CREATE_TICKET</code>=node hành động sinh ticket<br/>
                                                                            <code>ACTION_CREATE_ORDER</code>=node hành động tạo đơn hàng<br/>
                                                                            <code>ACTION_CREATE_TASK</code>=node hành động tạo công việc<br/>
                                                                            <code>ACTION_SEND_SMS</code>=node hành động gửi sms<br/>
                                                                            <code>ACTION_SEND_EMAIL</code>=node hành động gửi email<br/>
                                                                            <code>ACTION_PUSH_WEB_NOTIFICATION</code>=node hành động gửi thông báo trên web<br/>
                                                                            <code>ACTION_SEND_EMAIL_INTERNAL</code>=node hành động gửi email nội bộ<br/>
                                                                            <code>ACTION_COMMUNICATE_INTERNAL</code>=node giao tiếp nội bộ<br/>
                                                                            <code>EXIT</code>=node thoát<br/>
                                                                            <code>WAIT</code>=node chờ<br/>
                                                                            <code>ACTION_MULTI_IN_ONE</code>=node hành động<br/>
                                                                            <code>CHECK_NOTIFY_INTERACTION</code>=node kiểm tra tương tác thông báo<br/>
                                                                            <code>CHECK_ACTION_INTERACTION</code>=node kiểm tra tương tác hành động<br/>
                                                                            <code>CHECK_REPLY_CUSTOMER_STATUS</code>=node kiểm tra trạng thái phản hồi khách hàng<br/>
                                                                            <code>ACTION_CREATE_RECORD</code>=node tạo bản ghi<br/>
                                                                            <code>ACTION_REPLY_EMAIL</code>=node phản hồi email<br/>
                                                                            <code>ACTION_FORWARD_EMAIL_STAFF</code>=node chuyển tiếp email cho nhân viên<br/>
                                                                            <code>EXECUTE_MISSION</code>=node thực hiện nhiệm vụ<br/>
                                                                            <code>CHECK_CUSTOMER_NOTIFY_INTERACTION</code>=node kiểm tra tương tác giao tiếp khách hàng
@apiParam   (Body:)    {Object}    nodes.node_connection   Thông tin liên kết node.
@apiParam   (Body:)    {String}    nodes.node_connection.next_node_id   Mã định danh node kế tiếp
@apiParam   (Body:)    {String}    nodes.node_connection.previous_node_id   Mã định danh node trước đó.
@apiParam   (Body:)    {Object}    [nodes.node_connection.result]  Kết quả tương ứng để rẽ nhánh khối điều kiện
@apiParam   (Body:)    {Object}    [nodes.node_config]  Thông tin cấu hình chi tiết của node. Các node Trigger event, điều kiện bộ lọc sẽ gửi lên theo bộ cấu hình tương ứng từng bên (Event, Bộ lọc profile...)
@apiParamExample    {json}  Body:
{
    "workflow_id": "",
    "diagram": [],
    "nodes":[
        {
            "node_id": "72d95c56-c6f5-11ed-8fe8-c7a3239de083",
            "node_name": "Start"
            "node_type": "ROOT",
            "element_type": "OPERATION",
            "node_connection": [{
                "next_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083",
                "previous_node_id": null
            }]
        },
        {
            "node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083",
            "node_type": "TRIGGER_EVENT",
            "element_type": "TRIGGER",
            "node_name": "Ten khoi"
            "node_config": {
                "audience_id": "c830e338-e287-11ed-b545-8150b6abec2b",
                "condition_wait_all_event": {
                    "unit": "minutes/hours/days",
                    "value": 5
                },
                "repeat_setting": {
                    "status": true/false
                    "repeat_type": "PROFILE_EXIT/PROFILE_EXIT_AFTER_TIME/PROFILE_EXIT_AND_VALID_NODE/PROFILE_EXIT_OR_VALID_NODE",
                    "wait_time": {
                        "value": 1,
                        "unit": "minutes/days"
                    }
                }
            },
            "node_connection": [{
                "next_node_id": "84497070-c6f5-11ed-8fe8-c7a3239de083",
                "previous_node_id": "72d95c56-c6f5-11ed-8fe8-c7a3239de083"
            }]
        },
        {
            "node_id": "4506c31a-c7a2-11ed-a58f-cd3807f6ef5e",
            "node_type": "CONDITION_FILTER_PROFILE",
            "element_type": "SPECIFIC_CONDITION",
            "node_name": "Ten khoi",
            "node_config": {
                "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b"   
            },
            "node_connection": [
                {
                    "result": "yes",
                    "next_node_id": "94d5413a-c6f5-11ed-8fe8-c7a3239de083",
                    "previous_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083"
                },
                {
                    "result": "no",
                    "next_node_id": "2d5db6d8-c7a2-11ed-a58f-cd3807f6ef5e",
                    "previous_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083"
                }
            ]
        },
        {
            "node_id": "4506c31a-c7a2-11ed-a58f-cd3807f6ef5e",
            "node_type": "CONDITION_FILTER_COMPANY",
            "element_type": "SPECIFIC_CONDITION",
            "node_name": "Ten khoi",
            "node_config": {
                "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b"
            },
            "node_connection": [
                {
                    "result": "yes",
                    "next_node_id": "94d5413a-c6f5-11ed-8fe8-c7a3239de083",
                    "previous_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083"
                },
                {
                    "result": "no",
                    "next_node_id": "2d5db6d8-c7a2-11ed-a58f-cd3807f6ef5e",
                    "previous_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083"
                }
            ]
        },
        {
            "node_id": "2d5db6d8-c7a2-11ed-a58f-cd3807f6ef5e",
            "node_type": "ACTION_CREATE_TICKET",
            "element_type": "MANAGEMENT_ACTION",
            "node_name": "Ten khoi",
            "node_config": {
                "name": "Ten ticket",
                "type_ticket_id": "Kieu ticket",
                "priority_level": "Muc do uu tien",
                "status_process_id": "Trang thai xu ly",
                "description": "Mo ta",
                "assign_information": {
                    "type_assign": "wait_assignment/staff",
                    "assign_id": "",
                    "type_staff_assign": "assign_staff/staff_charge_switchboard/profile_owner"
                }
            },
            "node_connection": [{
                "next_node_id": "b28edc7c-c6f5-11ed-8fe8-c7a3239de083",
                "previous_node_id": "4506c31a-c7a2-11ed-a58f-cd3807f6ef5e"
            }]
        },
        {
            "node_id": "b28edc7c-c6f5-11ed-8fe8-c7a3239de083",
            "node_name": "Thoat",
            "node_type": "EXIT",
            "element_type": "OPERATION",
            "node_connection": [{
                "next_node_id": null,
                "previous_node_id": "94d5413a-c6f5-11ed-8fe8-c7a3239de083"
            }]
        }
    ]
}

@apiParam   (TRIGGER_EVENT node_config:)    {String}    audience_id  ID audience event
@apiParam   (TRIGGER_EVENT node_config:)    {Object}    [condition_wait_all_event]  Thông tin thời gian phát sinh event
@apiParam   (TRIGGER_EVENT node_config:)    {String}    condition_wait_all_event.unit Đơn vị thời gian. Giá trị: <br/><code>minutes</code>=Phút
                                                                                                                            <br/><code>hours</code>=Giờ
                                                                                                                            <br/><code>days</code>=Ngày 
@apiParam   (TRIGGER_EVENT node_config:)    {Number}    condition_wait_all_event.value  Giá trị thời gian.
@apiParam   (TRIGGER_EVENT node_config:)    {Object}    [repeat_setting]  Thông tin lặp workflow
@apiParam   (TRIGGER_EVENT node_config:)    {Boolean}   repeat_setting.status  Trạng thái bật/tắt lặp workflow. Giá trị: true/false
@apiParam   (TRIGGER_EVENT node_config:)    {String}    repeat_setting.repeat_type  Kiểu lặp. Giá trị: <br/><code>PROFILE_EXIT</code>=Profile đã thoát khỏi Workflow
                                                                                                        <br/><code>PROFILE_EXIT_AFTER_TIME</code>=Profile đã thoát khỏi Workflow được 1 khoảng thời gian
                                                                                                        <br/><code>PROFILE_EXIT_AND_VALID_NODE</code>=Profile đã thoát khỏi Workflow và thời điểm thỏa mãn sau cách trước 1 khoảng thời gian
                                                                                                        <br/><code>PROFILE_EXIT_OR_VALID_NODE</code>=Profile đã thoát khỏi Workflow hoặc chưa thoát nhưng thời điểm thỏa mãn sau cách trước 1 khoảng thời gian
                                                                                                        <br/><code>EMAIL_EXIT</code>=Email đã thoát khỏi Workflow
                                                                                                        <br/><code>EMAIL_EXIT_AFTER_TIME</code>=Email đã thoát khỏi Workflow được 1 khoảng thời gian
                                                                                                        <br/><code>EMAIL_EXIT_AND_VALID_NODE</code>=Email đã thoát khỏi Workflow và thời điểm thỏa mãn sau cách trước 1 khoảng thời gian
                                                                                                        <br/><code>EMAIL_EXIT_OR_VALID_NODE</code>=Email đã thoát khỏi Workflow hoặc chưa thoát nhưng thời điểm thỏa mãn sau cách trước 1 khoảng thời gian
                                                                                                        <br/><code>TASK_EXIT</code>=Task đã thoát khỏi Workflow
                                                                                                        <br/><code>TASK_EXIT_AFTER_TIME</code>=Task đã thoát khỏi Workflow được 1 khoảng thời gian
                                                                                                        <br/><code>TASK_EXIT_AND_VALID_NODE</code>=Task đã thoát khỏi Workflow và thời điểm thỏa mãn sau cách trước 1 khoảng thời gian
                                                                                                        <br/><code>TASK_EXIT_OR_VALID_NODE</code>=Task đã thoát khỏi Workflow hoặc chưa thoát nhưng thời điểm thỏa mãn sau cách trước 1 khoảng thời gian
                                                                                                        <br/><code>USER_EXIT</code>=USER đã thoát khỏi Workflow
                                                                                                        <br/><code>USER_EXIT_AFTER_TIME</code>=USER đã thoát khỏi Workflow được 1 khoảng thời gian
                                                                                                        <br/><code>USER_EXIT_AND_VALID_NODE</code>=USER đã thoát khỏi Workflow và thời điểm thỏa mãn sau cách trước 1 khoảng thời gian
                                                                                                        <br/><code>USER_EXIT_OR_VALID_NODE</code>=USER đã thoát khỏi Workflow hoặc chưa thoát nhưng thời điểm thỏa mãn sau cách trước 1 khoảng thời gian
                                                                                                        <br/><code>OPPTY_EXIT</code>=OPPTY đã thoát khỏi Workflow
                                                                                                        <br/><code>OPPTY_EXIT_AFTER_TIME</code>=OPPTY đã thoát khỏi Workflow được 1 khoảng thời gian
                                                                                                        <br/><code>OPPTY_EXIT_AND_VALID_NODE</code>=OPPTY đã thoát khỏi Workflow và thời điểm thỏa mãn sau cách trước 1 khoảng thời gian
                                                                                                        <br/><code>OPPTY_EXIT_OR_VALID_NODE</code>=OPPTY đã thoát khỏi Workflow hoặc chưa thoát nhưng thời điểm thỏa mãn sau cách trước 1 khoảng thời gian
@apiParam   (TRIGGER_EVENT node_config:)    {Object}    repeat_setting.wait_time  Thông tin thời gian chờ để lặp. Chỉ sử dụng với các repeat_type : <br/><code>PROFILE_EXIT_AFTER_TIME</code>
                                                                                                                                        <br/><code>PROFILE_EXIT_AND_VALID_NODE</code>
                                                                                                                                        <br/><code>PROFILE_EXIT_OR_VALID_NODE</code>
                                                                                                                                        <br/><code>EMAIL_EXIT_AFTER_TIME</code>
                                                                                                                                        <br/><code>EMAIL_EXIT_AND_VALID_NODE</code>
                                                                                                                                        <br/><code>EMAIL_EXIT_OR_VALID_NODE</code>
                                                                                                                                        <br/><code>TASK_EXIT_AFTER_TIME</code>
                                                                                                                                        <br/><code>TASK_EXIT_AND_VALID_NODE</code>
                                                                                                                                        <br/><code>TASK_EXIT_OR_VALID_NODE</code>
                                                                                                                                        <br/><code>USER_EXIT_AFTER_TIME</code>
                                                                                                                                        <br/><code>USER_EXIT_AND_VALID_NODE</code>
                                                                                                                                        <br/><code>USER_EXIT_OR_VALID_NODE</code>
                                                                                                                                        <br/><code>OPPTY_EXIT_AFTER_TIME</code>
                                                                                                                                        <br/><code>OPPTY_EXIT_AND_VALID_NODE</code>
                                                                                                                                        <br/><code>OPPTY_EXIT_OR_VALID_NODE</code>
@apiParam   (TRIGGER_EVENT node_config:)    {Number}    repeat_setting.wait_time.value  Giá trị thời gian.
@apiParam   (TRIGGER_EVENT node_config:)    {String}    repeat_setting.wait_time.unit  Đơn vị thời gian. Giá trị:<br/><code>hours</code>=Giờ
                                                                                                            <br/><code>days</code>=Ngày 
@apiParamExample    {json}  TRIGGER_EVENT node_config:
{
    "audience_id": "c830e338-e287-11ed-b545-8150b6abec2b",
    "condition_wait_all_event": {
        "unit": "minutes/hours/days",
        "value": 5
    },
    "repeat_setting": {
        "status": true/false
        "repeat_type": "PROFILE_EXIT/PROFILE_EXIT_AFTER_TIME/PROFILE_EXIT_AND_VALID_NODE/PROFILE_EXIT_OR_VALID_NODE",
        "wait_time": {
            "value": 1,
            "unit": "minutes/days"
        }
    }
}

@apiParam   (TRIGGER_SCHEDULE node_config:)    {String}    schedule_type  Loại đặt lịch. Giá trị: <br/><code>DAILY</code>=hàng ngày
                                                                                                    <br/><code>WEEKLY</code>=hàng tuần
                                                                                                    <br/><code>MONTHLY</code>=hàng tháng
                                                                                                    <br/><code>YEARLY</code>=hàng năm
                                                                                                    <br/><code>SPECIFIC_DAY</code>=ngày cụ thể
@apiParam   (TRIGGER_SCHEDULE node_config:)    {String}    time_in_day  Thời gian của ngày. Định dạng: <code>HH:MM</code> <br/>
                                                                                            Sử dụng theo định dạng 24h.
@apiParam   (TRIGGER_SCHEDULE node_config:)    {String}    day_value  Giá trị ngày kích hoạt. Định dạng: <code>%Y-%m-%d</code> <br/> (Chỉ sử dụng cho lựa chọn Ngày cụ thể)
@apiParam   (TRIGGER_SCHEDULE node_config:)    {ArrayString}     [day_in_week]  Thứ trong tuần. <br/>(Chỉ sử dụng cho lựa chọn Hàng tuần)  <br/>Giá trị:<br/><code>MONDAY</code>=Thứ 2
                                                                                                                                            <br/><code>TUESDAY</code>=Thứ 3
                                                                                                                                            <br/><code>WEDNESDAY</code>=Thứ 4
                                                                                                                                            <br/><code>THURSDAY</code>=Thứ 5
                                                                                                                                            <br/><code>FRIDAY</code>=Thứ 6
                                                                                                                                            <br/><code>SATURDAY</code>=Thứ 7
                                                                                                                                            <br/><code>SUNDAY</code>=Chủ nhật
@apiParam   (TRIGGER_SCHEDULE node_config:)    {String}     [month_day_type]       Loại ngày trong tháng.<br/>(Chỉ sử dụng cho lựa chọn Hàng tháng) <br/> Giá trị: <br/><code>SPECIFIC_DAY</code>=Ngày cụ thể
                                                                                                                <br/><code>CONFIG_DAY</code>=Ngày linh hoạt
@apiParam   (TRIGGER_SCHEDULE node_config:)    {ArrayString}     [month_day_value]     Giá trị ngày trong tháng.<br/>(Chỉ sử dụng cho lựa chọn Hàng tháng) <br/> Giá trị: <br/> Nếu chọn loại là <code>SPECIFIC_DAY</code> thì giá trị sẽ là mảng các ngày. Ví dụ: <code>["1", "2", "3"]</code>
                                                                                                                                <br/> Nếu chọn loại là <code>CONFIG_DAY</code> thì giá trị như sau: <br/><code>FIRST_DAY</code>=Ngày đầu tiên trong tháng
                                                                                                                                                                                         <br/><code>LAST_DAY</code>=Ngày cuối cùng trong tháng. Ví dụ: <code>["FIRST_DAY"]</code>
@apiParam   (TRIGGER_SCHEDULE node_config:)    {Number}     [month_in_year] Tháng trong năm. <br/>(Chỉ sử dụng cho lựa chọn Hàng năm) <br/> Giá trị:<br/><code>1</code>=Tháng 1
                                                                                                                                            <br/><code>2</code>=Tháng 2
                                                                                                                                            <br/><code>3</code>=Tháng 3
                                                                                                                                            <br/><code>4</code>=Tháng 4
                                                                                                                                            <br/><code>5</code>=Tháng 5
                                                                                                                                            <br/><code>6</code>=Tháng 6
                                                                                                                                            <br/><code>7</code>=Tháng 7
                                                                                                                                            <br/><code>8</code>=Tháng 8
                                                                                                                                            <br/><code>9</code>=Tháng 9
                                                                                                                                            <br/><code>10</code>=Tháng 10
                                                                                                                                            <br/><code>11</code>=Tháng 11
                                                                                                                                            <br/><code>12</code>=Tháng 12
@apiParam   (TRIGGER_SCHEDULE node_config:)    {Number}     [day_in_month]  Ngày trong tháng. <br/>(Chỉ sử dụng cho lựa chọn Hàng năm)
@apiParamExample    {json}  TRIGGER_SCHEDULE node_config:
{
    "schedule_type": "DAILY/WEEKLY/MONTHLY/YEARLY",
    "time_in_day": "23:23",
    "day_in_week": ["MONDAY"],
    "month_day_type": "SPECIFIC_DAY/CONFIG_DAY",
    "month_day_value": ["1", "2", "3"],
    "month_in_year": 1,
    "day_in_month": 31
}

@apiParam   (TRIGGER_SPECIFIC_DAY node_config:)    {String}     specific_day  Ngày cụ thể. 
@apiParamExample    {json}  TRIGGER_SPECIFIC_DAY node_config:
{
    "specific_day": "2023-07-16T13:45:00Z"
}

@apiParam   (CONDITION_FILTER_PROFILE node_config:)    {String}     audience_id     ID audience bộ lọc
@apiParamExample    {json}  CONDITION_FILTER_PROFILE node_config:
{
    "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b"
}

@apiParam   (CONDITION_FILTER_COMPANY node_config:)    {String}     audience_id     ID audience bộ lọc
@apiParamExample    {json}  CONDITION_FILTER_COMPANY node_config:
{
    "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b"
}

@apiParam   (CONDITION_FILTER_ORDER node_config:)    {String}     audience_id     ID audience bộ lọc
@apiParamExample    {json}  CONDITION_FILTER_ORDER node_config:
{
    "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b"
}

@apiParam   (CONDITION_FILTER_CALL node_config:)    {String}     audience_id     ID audience bộ lọc
@apiParamExample    {json}  CONDITION_FILTER_CALL node_config:
{
    "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b"
}

@apiParam   (CONDITION_FILTER_TICKET node_config:)    {String}     audience_id     ID audience bộ lọc
@apiParamExample    {json}  CONDITION_FILTER_TICKET node_config:
{
    "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b"
}

@apiParam   (CONDITION_FILTER node_config:)    {Array}     audience_filter  Thông tin các nhóm điều kiện. 
@apiParam   (CONDITION_FILTER node_config:)    {Array}     audience_filter.audiences     Các bộ lọc con trong nhóm điều kiện
@apiParam   (CONDITION_FILTER node_config:)    {String}     audience_filter.audiences.audience_id     ID audience bộ lọc
@apiParam   (CONDITION_FILTER node_config:)    {Number}     audience_filter.audiences.position    Vị trí bộ lọc
@apiParam   (CONDITION_FILTER node_config:)    {String}     audience_filter.audiences.type    Thuộc tính xác định bộ lọc thuộc module nào. Giá trị: <br/><code>PROFILE</code>=Bộ lọc của Profiles
                                                                                                                                                    <br/><code>ORDER</code>=Bộ lọc đơn hàng
                                                                                                                                                    <br/><code>COMPANY</code>=Bộ lọc công ty
                                                                                                                                                    <br/><code>TICKET</code>=Bộ lọc ticket
                                                                                                                                                    <br/><code>CALL</code>=Bộ lọc cuộc gọi
                                                                                                                                                    <br/><code>TASK</code>=Bộ lọc task
@apiParam   (CONDITION_FILTER node_config:)    {Number}     audience_filter.position    Vị trí nhóm điều kiện
@apiParamExample    {json}  CONDITION_FILTER node_config:
{
    "audience_filter": [
        {
            "audiences": [
                {
                    "audience_id": "63c929cc-e3ff-11ed-90bb-e1e67bd2f23b",
                    "position": 0,
                    "type": "PROFILE/ORDER/COMPANY/TICKET/CALL/TASK"
                },
                {
                    "audience_id": "a22ad5e4-e3ff-11ed-90bb-e1e67bd2f23b",
                    "position": 1,
                    "type": "PROFILE/ORDER/COMPANY/TICKET/CALL/TASK"
                }
            ]
            "position": 0
        }
    ]
}

@apiParam   (CONDITION_FILTER_LIST_PROFILE node_config:)    {String}     audience_id     ID audience bộ lọc
@apiParamExample    {json}  CONDITION_FILTER_LIST_PROFILE node_config:
{
    "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b"
}

@apiParam   (CONDITION_MULTI_BRANCH node_config:)    {Array}     condition_branchs  Thông tin các nhánh điều kiện. 
@apiParam   (CONDITION_MULTI_BRANCH node_config:)    {Array}     condition_branchs.condition_groups     Các nhóm bộ lọc con trong nhánh
@apiParam   (CONDITION_MULTI_BRANCH node_config:)    {Number}     condition_branchs.condition_groups.position     Vị trí nhóm
@apiParam   (CONDITION_MULTI_BRANCH node_config:)    {Array}     condition_branchs.condition_groups.audiences     Các bộ lọc con trong nhóm
@apiParam   (CONDITION_MULTI_BRANCH node_config:)    {String}     condition_branchs.condition_groups.audiences.audience_id     ID audience bộ lọc
@apiParam   (CONDITION_MULTI_BRANCH node_config:)    {Number}     condition_branchs.condition_groups.audiences.position    Vị trí bộ lọc
@apiParam   (CONDITION_MULTI_BRANCH node_config:)    {String}     condition_branchs.condition_groups.audiences.type    Thuộc tính xác định bộ lọc thuộc module nào. Giá trị: <br/><code>PROFILE</code>=Bộ lọc Profiles
                                                                                                                                                                            <br/><code>USER</code>=Bộ lọc user
                                                                                                                                                                            <br/><code>MAILBOX</code>=Bộ lọc mailbox
                                                                                                                                                                            <br/><code>COMPANY</code>=Bộ lọc công ty
                                                                                                                                                                            <br/><code>TASK</code>=Bộ lọc task
                                                                                                                                                                            <br/><code>KPI</code>=Bộ lọc KPI
                                                                                                                                                                            <br/><code>OPPTY</code>=Bộ lọc Cơ hội bán
@apiParam   (CONDITION_MULTI_BRANCH node_config:)    {Number}     condition_branchs.name    Tên nhánh
@apiParam   (CONDITION_MULTI_BRANCH node_config:)    {Number}     condition_branchs.position    Vị trí nhánh
@apiParam   (CONDITION_MULTI_BRANCH node_config:)    {Array}     condition_branchs.node_connection    Thông tin liên kết khối tiếp theo nếu thỏa mãn điều kiện của nhánh.
@apiParam   (CONDITION_MULTI_BRANCH node_config:)    {String}    condition_branchs.node_connection.next_node_id   Mã định danh node kế tiếp
@apiParam   (CONDITION_MULTI_BRANCH node_config:)    {String}    condition_branchs.node_connection.previous_node_id   Mã định danh node trước đó.
@apiParamExample    {json}  CONDITION_MULTI_BRANCH node_config:
{
    "condition_branchs": [
        {
            "condition_groups": [
                {
                    "position": 0,
                    "audiences": [
                        {
                            "audience_id": "63c929cc-e3ff-11ed-90bb-e1e67bd2f23b",
                            "position": 0,
                            "type": "PROFILE/USER/MAILBOX/COMPANY/TASK/KPI"
                        },
                        {
                            "audience_id": "a22ad5e4-e3ff-11ed-90bb-e1e67bd2f23b",
                            "position": 1,
                            "type": "PROFILE/USER/MAILBOX/COMPANY/TASK/KPI"
                        }
                    ]
                }
            ]
            "name": "Nhánh 1",
            "position": 0,
            "node_connection": [
                {
                    "next_node_id": "84497070-c6f5-11ed-8fe8-c7a3239de083",
                    "previous_node_id": "72d95c56-c6f5-11ed-8fe8-c7a3239de083"
                }
            ]
        }
    ]
}

@apiParam   (ACTION_CREATE_TICKET node_config:)    {String}     name    Tên ticket
@apiParam   (ACTION_CREATE_TICKET node_config:)    {String}     type_ticket_id  Kiểu ticket
@apiParam   (ACTION_CREATE_TICKET node_config:)    {String}     priority_level  Mức độ ưu tiên
@apiParam   (ACTION_CREATE_TICKET node_config:)    {String}     status_process_id Trạng thái xử lý
@apiParam   (ACTION_CREATE_TICKET node_config:)    {String}     [description] Mô tả
@apiParam   (ACTION_CREATE_TICKET node_config:)     {Array}     [personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_CREATE_TICKET node_config:)    {Object}     assign_information Thông tin phân công
@apiParam   (ACTION_CREATE_TICKET node_config:)    {String}     assign_information.type_assign Phụ trách ticket. Giá trị: <br/><code>wait_assignment</code>=Vào hàng chờ chung
                                                                                                                            <br/><code>staff</code>=phân công cho nhân viên
@apiParam   (ACTION_CREATE_TICKET node_config:)    {String}     [assign_information.type_staff_assign]   Kiểu phân công nhân viên. <br/>
                                                                                                                                Khi type_assign=staff: <br/>Giá trị: <br/> <code>staff_charge_switchboard</code>=Nhân viên phụ trách tổng đài
                                                                                                                                                                    <br/> <code>profile_owner</code>=Profile owner
                                                                                                                                                                    <br/> <code>assign_staff</code>=Phân công cho nhân viên cụ thể

@apiParam   (ACTION_CREATE_TICKET node_config:)    {String}     [assign_information.assign_id] ID nhân viên phân công. 
                                                                                                                    <br/>Sử dụng cho <code>type_assign=staff</code> khi <code>type_staff_assign=assign_staff</code>
@apiParamExample    {json}  ACTION_CREATE_TICKET node_config:
{
    "name": "Ten ticket",
    "type_ticket_id": "Kieu ticket",
    "priority_level": "Muc do uu tien",
    "status_process_id": "Trang thai xu ly",
    "description": "Mo ta",
    "personalizes": [
        {
            "field_name": "Đối tượng",
            "key": "WORKFLOW_TARGET",
            "replace": "*|WORKFLOW_TARGET|*",
            "source": "WORKFLOW"
        },
        {
            "field_name": "Tên workflow",
            "key": "WORKFLOW_NAME",
            "replace": "*|WORKFLOW_NAME|*",
            "source": "WORKFLOW"
        }
    ],
    "assign_information": {
        "type_assign": "wait_assignment/staff",
        "assign_id": "",
        "type_staff_assign": "assign_staff/staff_charge_switchboard/profile_owner"
    }
}

@apiParam   (ACTION_CREATE_ORDER node_config:)    {String}     name  Tên đơn hàng
@apiParam   (ACTION_CREATE_ORDER node_config:)    {String}     sale_process_id  Quy trình bán hàng
@apiParam   (ACTION_CREATE_ORDER node_config:)    {String}     state_code  Trạng thái đơn hàng
@apiParam   (ACTION_CREATE_ORDER node_config:)    {ArrayString}     tag_ids  Danh sách ID tag phân loại công việc.
@apiParam   (ACTION_CREATE_ORDER node_config:)     {Array}     [personalizes]  Thông tin cá nhân hóa
@apiParamExample    {json}  ACTION_CREATE_ORDER node_config:
{
    "name": "Ten don hang",
    "sale_process_id": "Quy trinh ban hang",
    "state_code": "Trang thai don hang",
    "tag_ids": ["tag_id"],
    "personalizes": [
        {
            "field_name": "Đối tượng",
            "key": "WORKFLOW_TARGET",
            "replace": "*|WORKFLOW_TARGET|*",
            "source": "WORKFLOW"
        },
        {
            "field_name": "Tên workflow",
            "key": "WORKFLOW_NAME",
            "replace": "*|WORKFLOW_NAME|*",
            "source": "WORKFLOW"
        }
    ]
}

@apiParam   (ACTION_CREATE_TASK node_config:)    {String}     title  Tên công việc
@apiParam   (ACTION_CREATE_TASK node_config:)    {String}     description  Nội dung công việc
@apiParam   (ACTION_CREATE_TASK node_config:)    {String}     assign_type  Người được giao. Giá trị: <br/><code>PROFILE_OWNER</code>=Profile owner
@apiParam   (ACTION_CREATE_TASK node_config:)    {Number}     due_date  Thời hạn hoàn thành công việc.
@apiParam   (ACTION_CREATE_TASK node_config:)    {String}     type  Kiểu công việc. Giá trị: <br/><code>GENERAL</code>=Chung
                                                                                            <br/><code>SENT_EMAIL</code>=Email
                                                                                            <br/><code>CALL</code>=Gọi điện
@apiParam   (ACTION_CREATE_TASK node_config:)    {Number}     [priority_level] Giá trị: <br/><code>1</code>=Cao
                                                                                        <br/><code>2</code>=Trung bình
                                                                                        <br/><code>3</code>=Thấp
@apiParam   (ACTION_CREATE_TASK node_config:)     {Array}     [personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_CREATE_TASK node_config:)    {Object}   notification_config     Thông tin thông báo nhắc nhở
@apiParam   (ACTION_CREATE_TASK node_config:)    {String}   notification_config.type    Loại thông báo nhắc nhở. Giá trị: <br/><code>NO_REMINDER</code>=Không nhắc nhở
                                                                                                                            <br/><code>ADVANCE_REMINDER_30_MINUTES</code>=Nhắc nhở trước thời hạn hoàn thành 30 phút
                                                                                                                            <br/><code>ADVANCE_REMINDER_1_HOUR</code>=Nhắc nhở trước thời hạn hoàn thành 1 giờ
                                                                                                                            <br/><code>ADVANCE_REMINDER_1_DAY</code>=Nhắc nhở trước thời hạn hoàn thành 1 ngày
                                                                                                                            <br/><code>TIMELY_REMINDER</code>=Nhắc nhở đúng hạn
                                                                                                                            <br/><code>OPTION</code>=Tùy chỉnh
@apiParam   (ACTION_CREATE_TASK node_config:)    {Number}   [notification_config.time_value]      Giá trị thời gian. <br/> Nếu <code>notification_config.type=OPTION</code> thì gửi lên số lượng thời gian cần nhắc nhở trước. Ví dụ: 30
@apiParam   (ACTION_CREATE_TASK node_config:)    {Number}   [notification_config.time_unit]       Đơn vị thời gian. (Chỉ sử dụng với <code>notification_config.type=OPTION</code>) <br/> Giá trị: <br/><code>minutes</code>=Phút
                                                                                                                            <br/><code>hours</code>=Giờ
                                                                                                                            <br/><code>years</code>=Năm
@apiParamExample    {json}  ACTION_CREATE_TASK node_config:
{
    "title": "Tên công việc",
    "description": "Noi dung cong viec",
    "assign_type": "PROFILE_OWNER",
    "due_date": 10,
    "priority_level": 1/2/3,
    "type": "GENERAL/SENT_EMAIL/CALL",
    "personalizes": [
        {
            "field_name": "Đối tượng",
            "key": "WORKFLOW_TARGET",
            "replace": "*|WORKFLOW_TARGET|*",
            "source": "WORKFLOW"
        },
        {
            "field_name": "Tên workflow",
            "key": "WORKFLOW_NAME",
            "replace": "*|WORKFLOW_NAME|*",
            "source": "WORKFLOW"
        }
    ],
    "notification_config": {
        "type": "NO_REMINDER/ADVANCE_REMINDER_30_MINUTES/ADVANCE_REMINDER_1_HOUR/ADVANCE_REMINDER_1_DAY/TIMELY_REMINDER/OPTION",
        "time_value": "",
        "time_unit": "minutes/hours/years"
    }
}

@apiParam   (ACTION_SEND_SMS node_config:)    {String}   content       Nội dung tin nhắn
@apiParam   (ACTION_SEND_SMS node_config:)     {Array}     [personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_SEND_SMS node_config:)     {Array}    links  Thông tin chèn link
@apiParam   (ACTION_SEND_SMS node_config:)     {String}    links.url  URL
@apiParam   (ACTION_SEND_SMS node_config:)     {String}    [links.domain_shorting_link]  Domain rút gọn URL
@apiParam   (ACTION_SEND_SMS node_config:)     {String}    links.key  Key link. Giá trị: <code>\*|LINK|\*</code>
@apiParam   (ACTION_SEND_SMS node_config:)    {Object}   sender_config  Thông tin cấu hình gửi
@apiParam   (ACTION_SEND_SMS node_config:)    {String}   sender_config.brand_name  SMS Brandname
@apiParam   (ACTION_SEND_SMS node_config:)    {String}   sender_config.send_to  Số điện thoại nhận thông điệp. Giá trị: <br/><code>primary_phone</code>=Chỉ gửi đến số điện thoại chính
                                                                                                                        <br/><code>all</code>=Gửi tới tất cả số đt của Profile
@apiParamExample    {json}  ACTION_SEND_SMS node_config:
{
    "content": "Noi dung sms",
    "personalizes": [
        {
            "field_name": "Đối tượng",
            "key": "WORKFLOW_TARGET",
            "replace": "*|WORKFLOW_TARGET|*",
            "source": "WORKFLOW"
        },
        {
            "field_name": "Tên workflow",
            "key": "WORKFLOW_NAME",
            "replace": "*|WORKFLOW_NAME|*",
            "source": "WORKFLOW"
        }
    ],
    "links": [
        {
            "url": "http://abc.com",
            "domain_shorting_link": "https://url.mobio.io/",
            "key": "*|LINK|*"
        }
    ],
    "sender_config":{
      "brand_name":"MOBIO",
      "send_to": "primary_phone/all"
    }
}

@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   email_type       Loại email. Giá trị: <br/><code>TRIGGER_EVENT_MAIL</code>=Là email phát sinh event
                                                                                                            <br/><code>OTHER_DOMAIN_MAIL</code>=Là email có domain bất kỳ
                                                                                                            <br/><code>WORKING_EMAIL</code>=Là e-mail working của doanh nghiệp
                                                                                                            <br/><code>WORKING_EMAIL_PERSONAL</code>=Là e-mail working của cá nhân
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   title       Tiêu đề email
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   content       Nội dung email
@apiParam   (ACTION_SEND_EMAIL node_config:)     {Array}     [personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_SEND_EMAIL node_config:)    {Object}   sender_config  Thông tin cấu hình gửi
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   [sender_config.email_config_id]  config_id của email gửi được chọn. Áp dụng với email_type=<code>WORKING_EMAIL</code>
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   [sender_config.sender_type]  Loại người gửi. Áp dụng với email_type=<code>WORKING_EMAIL_PERSONAL</code>. <br/> Giá trị: </br><code>PROFILE_OWNER</code>=Profile owner
                                                                                                                                                              </br><code>WORKFLOW_CREATED_USER</code>=Người tạo workflow
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   sender_config.sender_id  Email người gửi
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   sender_config.sender_name  Tên người gửi
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   sender_config.domain     Tên miền gửi email
@apiParam   (ACTION_SEND_EMAIL node_config:)    {Boolean}   sender_config.reply_to   Sử dụng làm email nhận phản hồi. Giá trị: <br/><code>True</code>/<code>False</code>
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   [sender_config.reply_email]   Email nhận phản hồi.
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   sender_config.send_to    Email nhận thông điệp. Giá trị: <br/><code>primary_email</code>=Chỉ gửi đến email chính
                                                                                                                        <br/><code>all</code>=Gửi tới tất cả email của Profile
@apiParam   (ACTION_SEND_EMAIL node_config:)    {Object}   [sender_config.cc_to]    Thông tin email CC.
@apiParam   (ACTION_SEND_EMAIL node_config:)    {ArrayString}   sender_config.cc_to.type    Loại email CC. Giá trị: <br/><code>PROFILE_OWNER</code>=CC đến profiles owner
                                                                                                                            <br/><code>SPECIFIC_EMAIL</code>=CC đến mail cụ thể.
                                                                                                                            <br/><code>PRIMARY_EMAIL</code>=CC đến email chính
                                                                                                                        <br/><code>ALL_PROFILE_EMAIL</code>=CC tới tất cả email của Profile
@apiParam   (ACTION_SEND_EMAIL node_config:)    {ArrayObject}   [sender_config.cc_to.value]    Danh sách email cụ thể kèm account id. (Chỉ sử dụng cho <code>sender_config.cc_to.type==SPECIFIC_EMAIL</code>)
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   [sender_config.cc_to.value.email]    Email gửi
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   [sender_config.cc_to.value.account_id]    ID Tài khoản tương ứng
@apiParam   (ACTION_SEND_EMAIL node_config:)    {Object}   [sender_config.bcc_to]    Thông tin email BCC.
@apiParam   (ACTION_SEND_EMAIL node_config:)    {ArrayString}   sender_config.bcc_to.type    Loại email BCC. Giá trị: <br/><code>PROFILE_OWNER</code>=BCC đến profiles owner
                                                                                                                            <br/><code>SPECIFIC_EMAIL</code>=BCC đến mail cụ thể
                                                                                                                            <br/><code>PRIMARY_EMAIL</code>=BCC đến email chính
                                                                                                                        <br/><code>ALL_PROFILE_EMAIL</code>=BCC tới tất cả email của Profile
@apiParam   (ACTION_SEND_EMAIL node_config:)    {ArrayObject}   [sender_config.bcc_to.value]    Danh sách email cụ thể kèm account id. (Chỉ sử dụng cho <code>sender_config.bcc_to.type==SPECIFIC_EMAIL</code>)
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   [sender_config.bcc_to.value.email]    Email gửi
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   [sender_config.bcc_to.value.account_id]    ID Tài khoản tương ứng
@apiParam   (ACTION_SEND_EMAIL node_config:)    {ArrayObject}   [attachments]  Thông tin tệp đính kèm
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   attachments.url  Url file
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   attachments.local_path  Đường dẫn đến file trên server
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   attachments.format     Định dạng file
@apiParam   (ACTION_SEND_EMAIL node_config:)    {String}   attachments.filename   Tên file
@apiParamExample    {json}  ACTION_SEND_EMAIL node_config:
{
    "title": "Tieu de email",
    "content": "Noi dung email",
    "attachments": [
        {
            "url": "https://aaa.aaa/...",
            "local_path": "",
            "format": "",
            "filename": ""
        }  
    ],
    "personalizes": [
        {
            "field_name": "Đối tượng",
            "key": "WORKFLOW_TARGET",
            "replace": "*|WORKFLOW_TARGET|*",
            "source": "WORKFLOW"
        },
        {
            "field_name": "Tên workflow",
            "key": "WORKFLOW_NAME",
            "replace": "*|WORKFLOW_NAME|*",
            "source": "WORKFLOW"
        }
    ],
    "sender_config":{
      "sender_id": "<EMAIL>",
      "sender_name": "hoadq",
      "domain": "unsub.mobio.vn",
      "reply_to": True/False,
      "reply_email": "<EMAIL>",
      "send_to": "primary_email/all",
      "cc_to": {
        "type": ["PROFILE_OWNER", "SPECIFIC_EMAIL"],
        "value": [
            {
                "email": "<EMAIL>",
                "account_id": "2222222"
            }
        ]
      },
      "bcc_to": {
        "type": ["PROFILE_OWNER", "SPECIFIC_EMAIL"],
        "value": [
            {
                "email": "<EMAIL>",
                "account_id": "2222222"
            }
        ]
      }
    }
}

@apiParam   (ACTION_PUSH_WEB_NOTIFICATION node_config:)    {String}   title       Tiêu đề thông báo
@apiParam   (ACTION_PUSH_WEB_NOTIFICATION node_config:)    {String}   content       Nội dung thông báo
@apiParam   (ACTION_PUSH_WEB_NOTIFICATION node_config:)     {Array}     [personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_PUSH_WEB_NOTIFICATION node_config:)    {Object}   sender_config  Thông tin cấu hình gửi
@apiParam   (ACTION_PUSH_WEB_NOTIFICATION node_config:)    {ArrayString}   sender_config.send_to_staff  Danh sách ID nhân viên cần gửi thông báo.
@apiParam   (ACTION_PUSH_WEB_NOTIFICATION node_config:)    {ArrayString}   sender_config.send_to_owner  Danh sách loại owner cần gửi. Giá trị: <br/><code>PROFILE_OWNER</code>=Gửi đến profiles owner
@apiParam   (ACTION_PUSH_WEB_NOTIFICATION node_config:)    {ArrayObject}   [icon]  Thông tin icon
@apiParam   (ACTION_PUSH_WEB_NOTIFICATION node_config:)    {String}   icon.url  Url file
@apiParam   (ACTION_PUSH_WEB_NOTIFICATION node_config:)    {String}   icon.local_path  Đường dẫn đến file trên server
@apiParam   (ACTION_PUSH_WEB_NOTIFICATION node_config:)    {String}   icon.format     Định dạng file
@apiParam   (ACTION_PUSH_WEB_NOTIFICATION node_config:)    {String}   icon.filename   Tên file
@apiParamExample    {json}  ACTION_PUSH_WEB_NOTIFICATION node_config:
{
    "title": "Tiêu đề thông báo",
    "content": "Noi dung thông báo",
    "personalizes": [
        {
            "field_name": "Đối tượng",
            "key": "WORKFLOW_TARGET",
            "replace": "*|WORKFLOW_TARGET|*",
            "source": "WORKFLOW"
        },
        {
            "field_name": "Tên workflow",
            "key": "WORKFLOW_NAME",
            "replace": "*|WORKFLOW_NAME|*",
            "source": "WORKFLOW"
        }
    ],
    "sender_config":{
      "send_to_staff": ["0d1cf99a-e4d7-11ed-bd3e-d1fad1002864", "0fcd89f2-e4d7-11ed-bd3e-d1fad1002864"],
      "send_to_owner": ["PROFILE_OWNER"]
    },
    "icon": [
        {
            "url": "https://aaa.aaa/...",
            "local_path": "",
            "format": "",
            "filename": ""
        }  
    ]
}

@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   email_type       Loại email. Giá trị: <br/><code>TRIGGER_EVENT_MAIL</code>=Là email phát sinh event
                                                                                                            <br/><code>OTHER_DOMAIN_MAIL</code>=Là email có domain bất kỳ
                                                                                                            <br/><code>WORKING_EMAIL</code>=Là e-mail working
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   title       Tiêu đề email
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   content       Nội dung email
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)     {Array}     [personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {Object}   sender_config  Thông tin cấu hình gửi
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   [sender_config.email_config_id]  config_id của email gửi được chọn. Áp dụng với email_type=<code>WORKING_EMAIL</code>
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   sender_config.sender_id  Email người gửi
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   sender_config.sender_name  Tên người gửi
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   sender_config.domain     Tên miền gửi email
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {Boolean}   sender_config.reply_to   Sử dụng làm email nhận phản hồi. Giá trị: <br/><code>True</code>/<code>False</code>
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   [sender_config.reply_email]   Email nhận phản hồi.
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {Object}   sender_config.send_to    Thông tin email nhận.
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {ArrayString}   sender_config.send_to.type    Loại email nhận. Giá trị: <br/><code>MANAGER</code>=Quản lý của nhân viên
                                                                                                                            <br/><code>PROFILE_OWNER</code>=Profile owner
                                                                                                                            <br/><code>MATCHING_CONDITION_STAFF</code>=Nhân viên thỏa mãn điều kiện</br>
                                                                                                                            <br/><code>SPECIFIC_EMAIL</code>=Gửi đến mail cụ thể
                                                                                                                            <br/><code>SPECIFIC_STAFF</code>=Gửi đến nhân viên cụ thể
                                                                                                                            <br/><code>OBJECT_CREATED_BY</code>=Gửi đến người tạo
                                                                                                                            <br/><code>OBJECT_ASSIGNEE</code>=Gửi đến người được giao
                                                                                                                            <br/><code>MANAGER_OBJECT_ASSIGNEE</code>=Gửi đến quản lý của người được giao
                                                                                                                            <br/><code>TEAM_MEMBER</code>=Gửi đến Các thành viên trong team
                                                                                                                            <br/><code>OPPTY_OWNER</code>=Gửi đến Oppty owner
                                                                                                                            <br/><code>OPPTY_OWNER_MANAGER</code>=Gửi đến quản lý của oppty owner
                                                                                                                            <br/><code>OPPTY_SUPPORTER</code>=Gửi đến Oppty supporter
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {ArrayObject}   [sender_config.send_to.value]    Danh sách nhân viên cụ thể kèm email. (Chỉ sử dụng cho <code>sender_config.send_to.type==SPECIFIC_STAFF</code>)
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   [sender_config.send_to.value.email]    Email gửi
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   [sender_config.send_to.value.account_id]    ID Tài khoản tương ứng
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {ArrayString}   [sender_config.send_to.specific_email_value]    Danh sách email cụ thể. (Chỉ sử dụng cho <code>sender_config.send_to.type==SPECIFIC_EMAIL</code>)
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {Object}   [sender_config.cc_to]    Thông tin email CC.
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {ArrayString}   sender_config.cc_to.type    Loại email CC. Giá trị: <br/><code>MANAGER</code>=Quản lý của nhân viên
                                                                                                                            <br/><code>SPECIFIC_EMAIL</code>=CC đến mail cụ thể
                                                                                                                            <br/><code>PROFILE_OWNER</code>=Profile owner
                                                                                                                            <br/><code>SPECIFIC_STAFF</code>=CC đến nhân viên cụ thể
                                                                                                                            <br/><code>MANAGER_OBJECT_ASSIGNEE</code>=CC đến quản lý của người được giao
                                                                                                                            <br/><code>OPPTY_OWNER</code>=CC đến Oppty owner
                                                                                                                            <br/><code>OPPTY_OWNER_MANAGER</code>=CC đến quản lý của oppty owner
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {ArrayObject}   [sender_config.cc_to.value]    Danh sách nhân viên cụ thể kèm email. (Chỉ sử dụng cho <code>sender_config.cc_to.type==SPECIFIC_STAFF</code>)
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   [sender_config.cc_to.value.email]    Email gửi
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   [sender_config.cc_to.value.account_id]    ID Tài khoản tương ứng
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {ArrayString}   [sender_config.cc_to.specific_email_value]    Danh sách email cụ thể. (Chỉ sử dụng cho <code>sender_config.cc_to.type==SPECIFIC_EMAIL</code>)
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {Object}   [sender_config.bcc_to]    Thông tin email BCC.
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {ArrayString}   sender_config.bcc_to.type    Loại email BCC. Giá trị: <br/><code>MANAGER</code>=Quản lý của nhân viên
                                                                                                                            <br/><code>SPECIFIC_EMAIL</code>=BCC đến mail cụ thể
                                                                                                                            <br/><code>PROFILE_OWNER</code>=Profile owner
                                                                                                                            <br/><code>SPECIFIC_STAFF</code>=BCC đến nhân viên cụ thể
                                                                                                                            <br/><code>MANAGER_OBJECT_ASSIGNEE</code>=BCC đến quản lý của người được giao
                                                                                                                            <br/><code>OPPTY_OWNER</code>=BCC đến Oppty owner
                                                                                                                            <br/><code>OPPTY_OWNER_MANAGER</code>=BCC đến quản lý của oppty owner
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {ArrayObject}   [sender_config.bcc_to.value]    Danh sách nhân viên cụ thể kèm email. (Chỉ sử dụng cho <code>sender_config.bcc_to.type==SPECIFIC_STAFF</code>)
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   [sender_config.bcc_to.value.email]    Email gửi
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   [sender_config.bcc_to.value.account_id]    ID Tài khoản tương ứng
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {ArrayString}   [sender_config.bcc_to.specific_email_value]    Danh sách email cụ thể. (Chỉ sử dụng cho <code>sender_config.bcc_to.type==SPECIFIC_EMAIL</code>)
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {ArrayObject}   [attachments]  Thông tin tệp đính kèm
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   attachments.url  Url file
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   attachments.local_path  Đường dẫn đến file trên server
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   attachments.format     Định dạng file
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)    {String}   attachments.filename   Tên file
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)     {Array}    links  Thông tin chèn link
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)     {String}    links.url  URL
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)     {String}    [links.domain_shorting_link]  Domain rút gọn URL
@apiParam   (ACTION_SEND_EMAIL_INTERNAL node_config:)     {String}    links.key  Key link. Giá trị: <code>\*|LINK|\*</code>

@apiParamExample    {json}  ACTION_SEND_EMAIL_INTERNAL node_config:
{
    "title": "Tieu de email",
    "content": "Noi dung email",
    "attachments": [
        {
            "url": "https://aaa.aaa/...",
            "local_path": "",
            "format": "",
            "filename": ""
        }  
    ],
    "personalizes": [
        {
            "field_name": "Đối tượng",
            "key": "WORKFLOW_TARGET",
            "replace": "*|WORKFLOW_TARGET|*",
            "source": "WORKFLOW"
        },
        {
            "field_name": "Tên workflow",
            "key": "WORKFLOW_NAME",
            "replace": "*|WORKFLOW_NAME|*",
            "source": "WORKFLOW"
        }
    ],
    "links": [
        {
            "url": "http://abc.com",
            "domain_shorting_link": "https://url.mobio.io/",
            "key": "*|LINK|*"
        }
    ],
    "sender_config":{
      "sender_id": "<EMAIL>",
      "sender_name": "hoadq",
      "domain": "unsub.mobio.vn",
      "reply_to": True/False,
      "reply_email": "<EMAIL>",
      "send_to": {
        "type": ["MANAGER", "SPECIFIC_EMAIL", "MATCHING_CONDITION_STAFF", "SPECIFIC_STAFF"],
        "value": [
            {
                "email": "<EMAIL>",
                "account_id": "2222222"
            }
        ],
        "specific_email_value": ["<EMAIL>", "<EMAIL>"]
      },
      "cc_to": {
        "type": ["MANAGER", "SPECIFIC_EMAIL", "SPECIFIC_STAFF"],
        "value": [
            {
                "email": "<EMAIL>",
                "account_id": "2222222"
            }
        ],
        "specific_email_value": ["<EMAIL>", "<EMAIL>"]
      },
      "bcc_to": {
        "type": ["MANAGER", "SPECIFIC_EMAIL", "SPECIFIC_STAFF"],
        "value": [
            {
                "email": "<EMAIL>",
                "account_id": "2222222"
            }
        ],
        "specific_email_value": ["<EMAIL>", "<EMAIL>"]
      }
    }
}

@apiParam   (WAIT node_config:)    {String}    wait_type  Kiểu chờ. Giá trị: <code>SPECIFIC_TIME</code>=Chờ tới ngày cụ thể</br>
                                                                            <code>PERIOD_TIME</code>=Chờ 1 khoảng thời gian</br>
                                                                            <code>EVEN_TRIGGER</code>=Chờ tới khi event xảy ra</br>
@apiParam   (WAIT node_config:)    {Object}    wait_config  Thông tin thời gian chờ
@apiParam   (WAIT node_config:)    {Number}    wait_config.days  Số ngày. Sử dụng khi wait_type = PERIOD_TIME/EVEN_TRIGGER
@apiParam   (WAIT node_config:)    {Number}    wait_config.hours  Số giờ. Sử dụng khi wait_type = PERIOD_TIME/EVEN_TRIGGER
@apiParam   (WAIT node_config:)    {Number}    wait_config.minutes  Số phút. Sử dụng khi wait_type = PERIOD_TIME/EVEN_TRIGGER
@apiParam   (WAIT node_config:)    {String}    wait_config.specific_date  Chờ tới ngày. Sử dụng khi wait_type = SPECIFIC_TIME. Định dạng: <code>%Y/%m/%d</code>
@apiParam   (WAIT node_config:)    {String}    wait_config.specific_hour  Thời gian chờ đến. Sử dụng khi wait_type = SPECIFIC_TIME. Định dạng: <code>%H:%M</code>
@apiParam   (WAIT node_config:)    {String}    wait_config.event_id  Mã định danh bộ event đã chọn. Sử dụng khi wait_type = EVEN_TRIGGER
@apiParam   (WAIT node_config:)    {String}    wait_config.process_after_wait  Hành động xử lý sau khi chờ 1 khoảng thời gian mà k có event nào xảy ra. Sử dụng khi wait_type = EVEN_TRIGGER. Giá trị: <code>EXIT_WORKFLOW</code>=Thoát khỏi worklfow</br>
                                                                                                                                                                                                        <code>NEXT_NODE</code>=Chuyển đến khối tiếp theo</br>
@apiParamExample    {json}  WAIT node_config:
{
    "wait_type": "SPECIFIC_TIME/PERIOD_TIME/EVEN_TRIGGER",
    "wait_config": {
        "days": 1,
        "hours": 1,
        "minutes": 1,
        "specific_date": "2024/01/31",
        "specific_hour": "16:00",
        "event_id": "a50e751d-abe4-4965-9af4-af48bd0b5e92",
        "process_after_wait": "EXIT_WORKFLOW/NEXT_NODE"
    }
}

@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {String}   notification_type       Loại thông báo. Giá trị: <code>APP</code>=Thông báo qua APP</br>
                                                                                                                        <code>WEBSITE</code>=Thông báo qua Website</br>
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {ArrayString}   [app_ids]       Danh sách ID ứng dụng nhận thông báo. Chỉ sử dụng khi <code>notification_type</code>=<code>APP</code>
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {Boolean}   is_html       Sử dụng nội dung html. Giá trị: true/false
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {String}   external_content       Nội dung thông báo hiển thị ngoài app.
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {String}   title       Tiêu đề thông báo
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {String}   content       Nội dung thông báo
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)     {Array}     [personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {Object}   sender_config  Thông tin cấu hình gửi
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {ArrayString}   sender_config.send_to_type  Loại đối tượng nhận thông báo. Giá trị: </br><code>MANAGER</code>=Quản lý của User
                                                                                                                        </br><code>MATCHING_CONDITION_STAFF</code>=Nhân viên thỏa mãn điều kiện
                                                                                                                        </br><code>SPECIFIC_STAFF</code>=Nhân viên cụ thể
                                                                                                                        <br/><code>OBJECT_CREATED_BY</code>=Gửi đến người tạo
                                                                                                                        <br/><code>OBJECT_ASSIGNEE</code>=Gửi đến người được giao
                                                                                                                        <br/><code>MANAGER_OBJECT_ASSIGNEE</code>=Gửi đến quản lý của người được giao
                                                                                                                        <br/><code>TEAM_MEMBER</code>=Gửi đến Các thành viên trong team
                                                                                                                        <br/><code>OPPTY_OWNER</code>=Gửi đến Oppty owner
                                                                                                                        <br/><code>OPPTY_OWNER_MANAGER</code>=Gửi đến quản lý của oppty owner
                                                                                                                        <br/><code>OPPTY_SUPPORTER</code>=Gửi đến Oppty supporter
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {ArrayString}   [sender_config.staff_ids]  Danh sách ID nhân viên được chọn khi <code>send_to_type</code> = <code>SPECIFIC_STAFF</code>
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {ArrayObject}   [icon]  Thông tin icon
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {String}   icon.url  Url file
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {String}   icon.local_path  Đường dẫn đến file trên server
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {String}   icon.format     Định dạng file
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)    {String}   icon.filename   Tên file
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)     {Array}    links  Thông tin chèn link
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)     {String}    links.url  URL
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)     {String}    [links.domain_shorting_link]  Domain rút gọn URL
@apiParam   (ACTION_COMMUNICATE_INTERNAL node_config:)     {String}    links.key  Key link. Giá trị: <code>\*|LINK|\*</code>
@apiParamExample    {json}  ACTION_COMMUNICATE_INTERNAL node_config:
{
    "notification_type": "APP",
    "app_ids": ["4f6eb6ed-d53b-4670-8c2c-a7c3a1e2a3d1"],
    "is_html": false,
    "title": "Tiêu đề thông báo",
    "content": "Noi dung thông báo",
    "personalizes": [
        {
            "field_name": "Đối tượng",
            "key": "WORKFLOW_TARGET",
            "replace": "*|WORKFLOW_TARGET|*",
            "source": "WORKFLOW"
        },
        {
            "field_name": "Tên workflow",
            "key": "WORKFLOW_NAME",
            "replace": "*|WORKFLOW_NAME|*",
            "source": "WORKFLOW"
        }
    ],
    "sender_config":{
      "send_to_type": ["SPECIFIC_STAFF", "MATCHING_CONDITION_STAFF", "MANAGER"],
      "staff_ids": ["feed1547-fc5d-40ad-a533-0a560de554cb", "3766d759-b6b4-4d82-8988-3569dec52564"]
    },
    "icon": [
        {
            "url": "https://aaa.aaa/...",
            "local_path": "",
            "format": "",
            "filename": ""
        }  
    ],
    "links": [
        {
            "url": "http://abc.com",
            "domain_shorting_link": "https://url.mobio.io/",
            "key": "*|LINK|*"
        }
    ]
}

@apiParam   (CONDITION_FILTER_LIST_USER node_config:)    {String}     audience_id     ID audience bộ lọc
@apiParamExample    {json}  CONDITION_FILTER_LIST_USER node_config:
{
    "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b"
}

@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {ArrayObject}     actions    Danh sách hành động
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_type    Kiểu hành động. Giá trị: <br/><code>CREATE_TASK</code>=Tạo công việc
                                                                                                            <br/><code>CREATE_TICKET</code>=Tạo ticket
                                                                                                            <br/><code>CREATE_ORDER</code>=Tạo cơ hội bán
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_id    Mã định danh hành động
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_name    Tên hành động
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {Object}     actions.action_config    Cấu hình hành động
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}      actions.action_config.CREATE_TICKET Thông tin cấu hình khi hành động là CREATE_TICKET
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}      actions.action_config.CREATE_TICKET.name    Tên ticket
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_TICKET.type_ticket_id  Kiểu ticket
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_TICKET.priority_level  Mức độ ưu tiên
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_TICKET.status_process_id Trạng thái xử lý
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     [actions.action_config.CREATE_TICKET.description] Mô tả
@apiParam   (ACTION_MULTI_IN_ONE node_config:)     {Array}     [actions.action_config.CREATE_TICKET.personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {Object}     actions.action_config.CREATE_TICKET.assign_information Thông tin phân công
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_TICKET.assign_information.type_assign Phụ trách ticket. Giá trị: <br/><code>wait_assignment</code>=Vào hàng chờ chung
                                                                                                                            <br/><code>staff</code>=phân công cho nhân viên cụ thể
                                                                                                                            <br/><code>profile_owner</code>=Profile owner
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     [actions.action_config.CREATE_TICKET.assign_information.type_staff_assign]   Kiểu phân công nhân viên. <br/>
                                                                                                                                Khi type_assign=staff: <br/>Giá trị: <br/> <code>matching_condition_staff</code>=Nhân viên thỏa mãn điều kiện
                                                                                                                                                                    <br/> <code>manager</code>=Quản lý của nhân viên
                                                                                                                                                                    <br/> <code>assign_staff</code>=Nhân viên được chỉ định

@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     [actions.action_config.CREATE_TICKET.assign_information.assign_id] ID nhân viên phân công. 
                                                                                                                    <br/>Sử dụng cho <code>type_assign=staff</code> khi <code>type_staff_assign=assign_staff</code>
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}      actions.action_config.CREATE_TASK Thông tin cấu hình khi hành động là CREATE_TASK
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {Boolean}     actions.action_config.CREATE_TASK.is_child_task  Có phải công việc con không. Giá trị: true/false
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_TASK.title  Tên công việc
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_TASK.description  Nội dung công việc
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_TASK.assign_type  Người được giao. Giá trị: <br/><code>MATCHING_CONDITION_STAFF</code>=Nhân viên thỏa mãn điều kiện
                                                                                                    <br/><code>MANAGER</code>=Quản lý của nhân viên
                                                                                                    <br/><code>SPECIFIC_STAFF</code>=Nhân viên cụ thể
                                                                                                    <br/><code>PROFILE_OWNER</code>=Profile owner
                                                                                                    <br/><code>OBJECT_CREATED_BY</code>=Người tạo công việc
                                                                                                    <br/><code>OBJECT_ASSIGNEE</code>=Nhân viên thực hiện chính
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {ArrayString}     actions.action_config.CREATE_TASK.assign_ids  Danh sách ID người được giao. Áp dụng khi assign_type=<code>SPECIFIC_STAFF</code>
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {Number}     actions.action_config.CREATE_TASK.due_date  Thời hạn hoàn thành công việc.
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_TASK.type  Kiểu công việc. Giá trị: <br/><code>GENERAL</code>=Chung
                                                                                            <br/><code>SENT_EMAIL</code>=Email
                                                                                            <br/><code>CALL</code>=Gọi điện
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {Number}     [actions.action_config.CREATE_TASK.priority_level] Giá trị: <br/><code>1</code>=Cao
                                                                                        <br/><code>2</code>=Trung bình
                                                                                        <br/><code>3</code>=Thấp
@apiParam   (ACTION_MULTI_IN_ONE node_config:)     {Array}     [actions.action_config.CREATE_TASK.personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {Object}   actions.action_config.CREATE_TASK.notification_config     Thông tin thông báo nhắc nhở
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}    actions.action_config.CREATE_TASK.notification_config.type    Loại thông báo nhắc nhở. Giá trị: <br/><code>NO_REMINDER</code>=Không nhắc nhở
                                                                                                                            <br/><code>ADVANCE_REMINDER_30_MINUTES</code>=Nhắc nhở trước thời hạn hoàn thành 30 phút
                                                                                                                            <br/><code>ADVANCE_REMINDER_1_HOUR</code>=Nhắc nhở trước thời hạn hoàn thành 1 giờ
                                                                                                                            <br/><code>ADVANCE_REMINDER_1_DAY</code>=Nhắc nhở trước thời hạn hoàn thành 1 ngày
                                                                                                                            <br/><code>TIMELY_REMINDER</code>=Nhắc nhở đúng hạn
                                                                                                                            <br/><code>OPTION</code>=Tùy chỉnh
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {Number}   [actions.action_config.CREATE_TASK.notification_config.time_value]      Giá trị thời gian. <br/> Nếu <code>notification_config.type=OPTION</code> thì gửi lên số lượng thời gian cần nhắc nhở trước. Ví dụ: 30
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {Number}   [actions.action_config.CREATE_TASK.notification_config.time_unit]       Đơn vị thời gian. (Chỉ sử dụng với <code>notification_config.type=OPTION</code>) <br/> Giá trị: <br/><code>minutes</code>=Phút
                                                                                                                            <br/><code>hours</code>=Giờ
                                                                                                                            <br/><code>years</code>=Năm

@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}      actions.action_config.CREATE_ORDER Thông tin cấu hình khi tạo cơ hội bán
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_ORDER.name  Tên cơ hội bán
@apiParam   (ACTION_MULTI_IN_ONE node_config:)     {Array}     [actions.action_config.CREATE_ORDER.personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_ORDER.sale_process_id  Quy trình bán hàng
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_ORDER.state_code  Trạng thái cơ hội bán  
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {ArrayString}     actions.action_config.CREATE_ORDER.tag_ids  Danh sách tag phân loại công việc
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_ORDER.description  Nội dung công việc
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {String}     actions.action_config.CREATE_ORDER.assign_type  Người được giao. Giá trị: <br/><code>WAIT_ASSIGNMENT</code>=Đưa vào hàng chờ chung
                                                                                                    <br/><code>SPECIFIC_STAFF</code>=Nhân viên cụ thể
                                                                                                    <br/><code>PROFILE_OWNER</code>=Profile owner
@apiParam   (ACTION_MULTI_IN_ONE node_config:)    {ArrayString}     actions.action_config.CREATE_ORDER.assign_ids  Danh sách ID người được giao. Áp dụng khi assign_type=<code>SPECIFIC_STAFF</code>
@apiParamExample    {json}  ACTION_MULTI_IN_ONE node_config:
{
    "actions": [
        {
            "action_id": "be3081d7-d37d-43a0-b4e3-9a1807e448f4",
            "action_name": "",
            "action_type": "CREATE_TICKET",
            "action_config": {
                "name": "Ten ticket",
                "type_ticket_id": "Kieu ticket",
                "priority_level": "Muc do uu tien",
                "status_process_id": "Trang thai xu ly",
                "description": "Mo ta",
                "personalizes": [
                    {
                        "field_name": "Đối tượng",
                        "key": "WORKFLOW_TARGET",
                        "replace": "*|WORKFLOW_TARGET|*",
                        "source": "WORKFLOW"
                    },
                    {
                        "field_name": "Tên workflow",
                        "key": "WORKFLOW_NAME",
                        "replace": "*|WORKFLOW_NAME|*",
                        "source": "WORKFLOW"
                    }
                ],
                "assign_information": {
                    "type_assign": "wait_assignment/staff",
                    "assign_id": "",
                    "type_staff_assign": "assign_staff/manager/matching_condition_staff"
                }
            }
        },
        {
            "action_id": "242bbd88-c40a-4493-aa5b-bfce089b99f8",
            "action_name": "",
            "action_type": "CREATE_TASK",
            "action_config": {
                "title": "Tên công việc",
                "description": "Noi dung cong viec",
                "assign_type": "SPECIFIC_STAFF",
                "assign_ids": ["ba4f76f9-1552-4a85-b550-af8651bf22da", "080cac1a-a72d-424f-b463-8b224e4f954d"]
                "due_date": 10,
                "priority_level": 1/2/3,
                "type": "GENERAL/SENT_EMAIL/CALL",
                "personalizes": [
                    {
                        "field_name": "Đối tượng",
                        "key": "WORKFLOW_TARGET",
                        "replace": "*|WORKFLOW_TARGET|*",
                        "source": "WORKFLOW"
                    },
                    {
                        "field_name": "Tên workflow",
                        "key": "WORKFLOW_NAME",
                        "replace": "*|WORKFLOW_NAME|*",
                        "source": "WORKFLOW"
                    }
                ],
                "notification_config": {
                    "type": "NO_REMINDER/ADVANCE_REMINDER_30_MINUTES/ADVANCE_REMINDER_1_HOUR/ADVANCE_REMINDER_1_DAY/TIMELY_REMINDER/OPTION",
                    "time_value": "",
                    "time_unit": "minutes/hours/years"
                }
            }
        },
        {
            "action_id": "3d310166-ecb9-11ee-b148-38d57a786a3e",
            "action_type": "CREATE_ORDER",
            "action_name": "def",
            "action_config": {
                "name": "Tên cơ hội bán",
                "personalizes": [
                    {
                        "field_name": "Đối tượng",
                        "key": "WORKFLOW_TARGET",
                        "replace": "*|WORKFLOW_TARGET|*",
                        "source": "WORKFLOW"
                    },
                    {
                        "field_name": "Tên workflow",
                        "key": "WORKFLOW_NAME",
                        "replace": "*|WORKFLOW_NAME|*",
                        "source": "WORKFLOW"
                    }
                ],
                "sale_process_id": "abcxyz",
                "state_code": "abcxyz",
                "tag_ids": ["5f36be92-ecb9-11ee-9e52-38d57a786a3e", "61204edc-ecb9-11ee-92fb-38d57a786a3e"],
                "description": "Noi dung cong viec",
                "assign_type": "SPECIFIC_STAFF",
                "assign_ids": ["ba4f76f9-1552-4a85-b550-af8651bf22da", "080cac1a-a72d-424f-b463-8b224e4f954d"]
            }
        }
    ]
}

@apiParam   (CHECK_NOTIFY_INTERACTION node_config:)    {String}    interaction_type  Kiểu tương tác. Giá trị: </br><code>SENT</code>=Đã nhận thông báo
                                                                                                              </br><code>OPENED</code>=Đã mở thông báo
                                                                                                              </br><code>CLICKED_LINK</code>=Đã bấm link
@apiParam   (CHECK_NOTIFY_INTERACTION node_config:)    {Object}    wait_config  Thông tin thời gian chờ
@apiParam   (CHECK_NOTIFY_INTERACTION node_config:)    {Number}    wait_config.days  Số ngày.
@apiParam   (CHECK_NOTIFY_INTERACTION node_config:)    {Number}    wait_config.hours  Số giờ.
@apiParam   (CHECK_NOTIFY_INTERACTION node_config:)    {Number}    wait_config.minutes  Số phút.
@apiParamExample    {json}  CHECK_NOTIFY_INTERACTION node_config:
{
    "interaction_type": "SENT/OPENED/CLICKED_LINK",
    "wait_config": {
        "days": 1,
        "hours": 1,
        "minutes": 1
    }
}

@apiParam   (CHECK_ACTION_INTERACTION node_config:)    {ArrayObject}    actions  Danh sách các hành động cần kiểm tra
@apiParam   (CHECK_ACTION_INTERACTION node_config:)    {String}    actions.action_id  Hành động cần kiểm tra
@apiParam   (CHECK_ACTION_INTERACTION node_config:)    {ArrayString}    actions.action_status  Trạng thái xử lý cần kiểm tra
@apiParam   (CHECK_ACTION_INTERACTION node_config:)    {String}    actions.action_type  Loại hành động
@apiParam   (CHECK_ACTION_INTERACTION node_config:)    {Object}    wait_config  Thông tin thời gian chờ
@apiParam   (CHECK_ACTION_INTERACTION node_config:)    {Number}    wait_config.days  Số ngày.
@apiParam   (CHECK_ACTION_INTERACTION node_config:)    {Number}    wait_config.hours  Số giờ.
@apiParam   (CHECK_ACTION_INTERACTION node_config:)    {Number}    wait_config.minutes  Số phút.
@apiParamExample    {json}  CHECK_ACTION_INTERACTION node_config:
{
    "actions": [
        {
            "action_id": "dd04f28f-b2a5-41a0-82df-3afbd39e428b",
            "action_type": "CREATE_TICKET",
            "action_status": [""]
        }
    ],
    "wait_config": {
        "days": 1,
        "hours": 1,
        "minutes": 1
    }
}

@apiParam   (ACTION_CREATE_RECORD node_config:)    {ArrayObject}     records    Danh sách bản ghi cần tạo
@apiParam   (ACTION_CREATE_RECORD node_config:)    {String}     records.record_type    Loại bản ghi. Giá trị: <br/><code>PROFILE</code>=Profile
                                                                                                            <br/><code>COMPANY</code>=Công ty
                                                                                                            <br/><code>ORDER</code>=Cơ hội bán
@apiParam   (ACTION_CREATE_RECORD node_config:)    {String}     records.record_id    Mã định danh hành động tạo bản ghi
@apiParam   (ACTION_CREATE_RECORD node_config:)    {String}     records.record_name    Tên hành động bản ghi
@apiParam   (ACTION_CREATE_RECORD node_config:)    {Object}     records.record_config    Cấu hình bản ghi
@apiParam   (ACTION_CREATE_RECORD node_config:)    {String}      records.record_config.ORDER Thông tin cấu hình khi tạo cơ hội bán
@apiParam   (ACTION_CREATE_RECORD node_config:)    {String}     records.record_config.ORDER.name  Tên cơ hội bán
@apiParam   (ACTION_CREATE_RECORD node_config:)     {Array}     [records.record_config.ORDER.personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_CREATE_RECORD node_config:)    {String}     records.record_config.ORDER.sale_process_id  Quy trình bán hàng
@apiParam   (ACTION_CREATE_RECORD node_config:)    {String}     records.record_config.ORDER.state_code  Trạng thái cơ hội bán  
@apiParam   (ACTION_CREATE_RECORD node_config:)    {ArrayString}     records.record_config.ORDER.tag_ids  Danh sách tag phân loại công việc
@apiParam   (ACTION_CREATE_RECORD node_config:)    {String}     records.record_config.ORDER.description  Nội dung công việc
@apiParam   (ACTION_CREATE_RECORD node_config:)    {String}     records.record_config.ORDER.assign_type  Người được giao. Giá trị: <br/><code>WAIT_ASSIGNMENT</code>=Đưa vào hàng chờ chung
                                                                                                    <br/><code>SPECIFIC_STAFF</code>=Nhân viên cụ thể
                                                                                                    <br/><code>PROFILE_OWNER</code>=Profile owner
@apiParam   (ACTION_CREATE_RECORD node_config:)    {ArrayString}     records.record_config.ORDER.assign_ids  Danh sách ID người được giao. Áp dụng khi assign_type=<code>SPECIFIC_STAFF</code>
@apiParamExample    {json}  ACTION_CREATE_RECORD node_config:
{
    "records": [
        {
            "record_id": "53754fca-ecb8-11ee-b748-38d57a786a3e",
            "record_type": "PROFILE",
            "record_name": "abc",
            "record_config": {
            }
        },
        {
            "record_id": "a754fbd9-adab-44a5-ae58-ae9f84a2f539",
            "record_type": "COMPANY",
            "record_name": "xyz",
            "record_config": {
            }
        },
        {
            "record_id": "3d310166-ecb9-11ee-b148-38d57a786a3e",
            "record_type": "ORDER",
            "record_name": "def",
            "record_config": {
                "name": "Tên cơ hội bán",
                "personalizes": [
                    {
                        "field_name": "Đối tượng",
                        "key": "WORKFLOW_TARGET",
                        "replace": "*|WORKFLOW_TARGET|*",
                        "source": "WORKFLOW"
                    },
                    {
                        "field_name": "Tên workflow",
                        "key": "WORKFLOW_NAME",
                        "replace": "*|WORKFLOW_NAME|*",
                        "source": "WORKFLOW"
                    }
                ],
                "sale_process_id": "abcxyz",
                "state_code": "abcxyz",
                "tag_ids": ["5f36be92-ecb9-11ee-9e52-38d57a786a3e", "61204edc-ecb9-11ee-92fb-38d57a786a3e"],
                "description": "Noi dung cong viec",
                "assign_type": "SPECIFIC_STAFF",
                "assign_ids": ["ba4f76f9-1552-4a85-b550-af8651bf22da", "080cac1a-a72d-424f-b463-8b224e4f954d"]
            }
        }
    ]
}

@apiParamExample    {json}  CHECK_REPLY_CUSTOMER_STATUS node_config:
{
}

@apiParam   (ACTION_REPLY_EMAIL node_config:)    {String}   title       Tiêu đề email
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {String}   content       Nội dung email
@apiParam   (ACTION_REPLY_EMAIL node_config:)     {Array}     [personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {Object}   sender_config  Thông tin cấu hình gửi
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {Object}   [sender_config.cc_to]    Thông tin email CC.
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {ArrayString}   sender_config.cc_to.type    Loại email CC. Giá trị: 
                                                                                                                            <br/><code>SPECIFIC_EMAIL</code>=CC đến mail cụ thể
                                                                                                                            <br/><code>SPECIFIC_STAFF</code>=CC đến nhân viên cụ thể
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {ArrayObject}   [sender_config.cc_to.value]    Danh sách nhân viên cụ thể kèm email. (Chỉ sử dụng cho <code>sender_config.cc_to.type==SPECIFIC_STAFF</code>)
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {String}   [sender_config.cc_to.value.email]    Email gửi
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {String}   [sender_config.cc_to.value.account_id]    ID Tài khoản tương ứng
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {ArrayString}   [sender_config.cc_to.specific_email_value]    Danh sách email cụ thể. (Chỉ sử dụng cho <code>sender_config.cc_to.type==SPECIFIC_EMAIL</code>)
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {Object}   [sender_config.bcc_to]    Thông tin email BCC.
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {ArrayString}   sender_config.bcc_to.type    Loại email BCC. Giá trị: 
                                                                                                                            <br/><code>SPECIFIC_EMAIL</code>=BCC đến mail cụ thể
                                                                                                                            <br/><code>SPECIFIC_STAFF</code>=BCC đến nhân viên cụ thể
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {ArrayObject}   [sender_config.bcc_to.value]    Danh sách nhân viên cụ thể kèm email. (Chỉ sử dụng cho <code>sender_config.bcc_to.type==SPECIFIC_STAFF</code>)
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {String}   [sender_config.bcc_to.value.email]    Email gửi
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {String}   [sender_config.bcc_to.value.account_id]    ID Tài khoản tương ứng
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {ArrayString}   [sender_config.bcc_to.specific_email_value]    Danh sách email cụ thể. (Chỉ sử dụng cho <code>sender_config.bcc_to.type==SPECIFIC_EMAIL</code>)
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {ArrayObject}   [attachments]  Thông tin tệp đính kèm
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {String}   attachments.url  Url file
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {String}   attachments.local_path  Đường dẫn đến file trên server
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {String}   attachments.format     Định dạng file
@apiParam   (ACTION_REPLY_EMAIL node_config:)    {String}   attachments.filename   Tên file
@apiParam   (ACTION_REPLY_EMAIL node_config:)     {Array}    links  Thông tin chèn link
@apiParam   (ACTION_REPLY_EMAIL node_config:)     {String}    links.url  URL
@apiParam   (ACTION_REPLY_EMAIL node_config:)     {String}    [links.domain_shorting_link]  Domain rút gọn URL
@apiParam   (ACTION_REPLY_EMAIL node_config:)     {String}    links.key  Key link. Giá trị: <code>\*|LINK|\*</code>

@apiParamExample    {json}  ACTION_REPLY_EMAIL node_config:
{
    "title": "Tieu de email",
    "content": "Noi dung email",
    "attachments": [
        {
            "url": "https://aaa.aaa/...",
            "local_path": "",
            "format": "",
            "filename": ""
        }  
    ],
    "personalizes": [
        {
            "field_name": "Đối tượng",
            "key": "WORKFLOW_TARGET",
            "replace": "*|WORKFLOW_TARGET|*",
            "source": "WORKFLOW"
        },
        {
            "field_name": "Tên workflow",
            "key": "WORKFLOW_NAME",
            "replace": "*|WORKFLOW_NAME|*",
            "source": "WORKFLOW"
        }
    ],
    "links": [
        {
            "url": "http://abc.com",
            "domain_shorting_link": "https://url.mobio.io/",
            "key": "*|LINK|*"
        }
    ],
    "sender_config":{
      "cc_to": {
        "type": ["SPECIFIC_EMAIL", "SPECIFIC_STAFF"],
        "value": [
            {
                "email": "<EMAIL>",
                "account_id": "2222222"
            }
        ],
        "specific_email_value": ["<EMAIL>", "<EMAIL>"]
      },
      "bcc_to": {
        "type": ["SPECIFIC_EMAIL", "SPECIFIC_STAFF"],
        "value": [
            {
                "email": "<EMAIL>",
                "account_id": "2222222"
            }
        ],
        "specific_email_value": ["<EMAIL>", "<EMAIL>"]
      }
    }
}

@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {String}   content       Nội dung email
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)     {Array}     [personalizes]  Thông tin cá nhân hóa
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {Object}   sender_config  Thông tin cấu hình gửi
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {Object}   [sender_config.send_to]    Thông tin email gửi.
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {ArrayString}   sender_config.send_to.type    Loại email gửi. Giá trị: 
                                                                                                                            <br/><code>PROFILE_OWNER</code>=Profile owner
                                                                                                                            <br/><code>SPECIFIC_EMAIL</code>=Email cụ thể
                                                                                                                            <br/><code>SPECIFIC_STAFF</code>=Nhân viên cụ thể
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {ArrayObject}   [sender_config.send_to.value]    Danh sách nhân viên cụ thể kèm email. (Chỉ sử dụng cho <code>sender_config.send_to.type==SPECIFIC_STAFF</code>)
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {String}   [sender_config.send_to.value.email]    Email gửi
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {String}   [sender_config.send_to.value.account_id]    ID Tài khoản tương ứng
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {ArrayString}   [sender_config.send_to.specific_email_value]    Danh sách email cụ thể. (Chỉ sử dụng cho <code>sender_config.send_to.type==SPECIFIC_EMAIL</code>)
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {Object}   [sender_config.cc_to]    Thông tin email CC.
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {ArrayString}   sender_config.cc_to.type    Loại email CC. Giá trị: <br/><code>PROFILE_OWNER</code>=Profile owner
                                                                                                                            <br/><code>SPECIFIC_EMAIL</code>=CC đến mail cụ thể
                                                                                                                            <br/><code>SPECIFIC_STAFF</code>=CC đến nhân viên cụ thể
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {ArrayObject}   [sender_config.cc_to.value]    Danh sách nhân viên cụ thể kèm email. (Chỉ sử dụng cho <code>sender_config.cc_to.type==SPECIFIC_STAFF</code>)
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {String}   [sender_config.cc_to.value.email]    Email gửi
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {String}   [sender_config.cc_to.value.account_id]    ID Tài khoản tương ứng
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {ArrayString}   [sender_config.cc_to.specific_email_value]    Danh sách email cụ thể. (Chỉ sử dụng cho <code>sender_config.cc_to.type==SPECIFIC_EMAIL</code>)
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {Object}   [sender_config.bcc_to]    Thông tin email BCC.
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {ArrayString}   sender_config.bcc_to.type    Loại email BCC. Giá trị: <br/><code>PROFILE_OWNER</code>=Profile owner
                                                                                                                            <br/><code>SPECIFIC_EMAIL</code>=BCC đến mail cụ thể
                                                                                                                            <br/><code>SPECIFIC_STAFF</code>=BCC đến nhân viên cụ thể
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {ArrayObject}   [sender_config.bcc_to.value]    Danh sách nhân viên cụ thể kèm email. (Chỉ sử dụng cho <code>sender_config.bcc_to.type==SPECIFIC_STAFF</code>)
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {String}   [sender_config.bcc_to.value.email]    Email gửi
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {String}   [sender_config.bcc_to.value.account_id]    ID Tài khoản tương ứng
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {ArrayString}   [sender_config.bcc_to.specific_email_value]    Danh sách email cụ thể. (Chỉ sử dụng cho <code>sender_config.bcc_to.type==SPECIFIC_EMAIL</code>)
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {ArrayObject}   [attachments]  Thông tin tệp đính kèm
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {String}   attachments.url  Url file
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {String}   attachments.local_path  Đường dẫn đến file trên server
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {String}   attachments.format     Định dạng file
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)    {String}   attachments.filename   Tên file
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)     {Array}    links  Thông tin chèn link
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)     {String}    links.url  URL
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)     {String}    [links.domain_shorting_link]  Domain rút gọn URL
@apiParam   (ACTION_FORWARD_EMAIL_STAFF node_config:)     {String}    links.key  Key link. Giá trị: <code>\*|LINK|\*</code>

@apiParamExample    {json}  ACTION_FORWARD_EMAIL_STAFF node_config:
{
    "title": "Tieu de email",
    "content": "Noi dung email",
    "attachments": [
        {
            "url": "https://aaa.aaa/...",
            "local_path": "",
            "format": "",
            "filename": ""
        }  
    ],
    "personalizes": [
        {
            "field_name": "Đối tượng",
            "key": "WORKFLOW_TARGET",
            "replace": "*|WORKFLOW_TARGET|*",
            "source": "WORKFLOW"
        },
        {
            "field_name": "Tên workflow",
            "key": "WORKFLOW_NAME",
            "replace": "*|WORKFLOW_NAME|*",
            "source": "WORKFLOW"
        }
    ],
    "links": [
        {
            "url": "http://abc.com",
            "domain_shorting_link": "https://url.mobio.io/",
            "key": "*|LINK|*"
        }
    ],
    "sender_config":{
        "send_to": {
            "type": ["PROFILE_OWNER", "SPECIFIC_EMAIL", "SPECIFIC_STAFF"],
            "value": [
                {
                    "email": "<EMAIL>",
                    "account_id": "2222222"
                }
            ],
            "specific_email_value": ["<EMAIL>", "<EMAIL>"]
          },
        "cc_to": {
            "type": ["PROFILE_OWNER", "SPECIFIC_EMAIL", "SPECIFIC_STAFF"],
            "value": [
                {
                    "email": "<EMAIL>",
                    "account_id": "2222222"
                }
            ],
            "specific_email_value": ["<EMAIL>", "<EMAIL>"]
        },
        "bcc_to": {
            "type": ["PROFILE_OWNER", "SPECIFIC_EMAIL", "SPECIFIC_STAFF"],
            "value": [
                {
                    "email": "<EMAIL>",
                    "account_id": "2222222"
                }
            ],
            "specific_email_value": ["<EMAIL>", "<EMAIL>"]
        }
    }
}

@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions     Danh sách nhiệm vụ cần thực hiện
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_id     Mã nhiệm vụ
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_name     Tên nhiệm vụ
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_type     Loại nhiệm vụ. <br/>Giá trị: <br/><code>RELATED_TO_ORDER</code>=Liên quan tới cơ hội bán<br/>
                                                                                                            <code>RELATED_TO_TASK</code>=Liên quan tới công việc<br/>
                                                                                                            <code>RELATED_TO_TICKET</code>=Liên quan tới ticket<br/>
@apiParam   (EXECUTE_MISSION node_config:)    {Object}     missions.mission_config     Cấu hình nhiệm vụ



@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_ORDER     Nhiệm vụ liên quan tới cơ hội bán
@apiParam   (EXECUTE_MISSION node_config:)    {ArrayObject}     missions.mission_config.RELATED_TO_ORDER.attributes     Danh sách thuộc tính
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_ORDER.attributes.attribute_type     Thông tin thuộc tính.<br/> Giá trị:<br/> <code>forward_deal</code>=Chuyển tiếp cơ hội bán của User</br>
                                                                                                                                                        <code>add_deal_supporter</code>=Gán User làm Oppty Supporter</br>
                                                                                                                                                        <code>remove_deal_supporter</code>=Xóa vai trò supporter của User</br>
                                                                                                                                                        <code>revoke_deal</code>=Thu hồi cơ hội bán</br>
                                                                                                                                                        <code>update_deal</code>=Cập nhật thông tin cơ hội bán</br>
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_ORDER.attributes.attribute_option     Thông tin thuộc tính.<br/> Giá trị:<br/> <code>forward_to_leader</code>=Chuyển đến quản lý của nhân viên(Dùng với attribute_type=forward_deal)</br>
                                                                                                                                                        <code>forward_to_team</code>=Chuyển đến hàng chờ của team(Dùng với attribute_type=forward_deal)</br>
                                                                                                                                                        <code>add_all_deal_staff_in_team</code>=Gán vào toàn bộ cơ hội bán của các thành viên trong team(Dùng với attribute_type=add_deal_supporter)</br>
                                                                                                                                                        <code>add_deal_in_staff_detail</code>=Gán vào cơ hội bán của thành viên cụ thể(Dùng với attribute_type=add_deal_supporter)</br>
                                                                                                                                                        <code>remove_deal_supporter_all_deal</code>=Xóa vai trò Supporter trên toàn bộ Cơ hội bán(Dùng với attribute_type=remove_deal_supporter)</br>
                                                                                                                                                        <code>remove_deal_supporter_all_deal_in_sale_process_detail</code>=Xóa vai trò Supporter trong quy trình bán hàng cụ thể(Dùng với attribute_type=remove_deal_supporter)</br>
                                                                                                                                                        <code>queue_common</code>=Chuyển tiếp (thu hồi) về hàng chờ chung (Dùng với attribute_type=revoke_deal)</br>
                                                                                                                                                        <code>queue_team</code>=Chuyển tiếp (thu hồi) về hàng chờ team. (Dùng với attribute_type=revoke_deal)</br>
                                                                                                                                                        <code>specific_member</code>=Chuyển tiếp cho nhân viên cụ thể.(Dùng với attribute_type=forward_deal)</br>
                                                                                                                                                        <code>deal_owner_leader</code>=Chuyển tiếp cho leader của deal owner. (Dùng với attribute_type=forward_deal)</br>
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_ORDER.attributes.assignee_id     ID nhân viên cụ thể. <br/>Dùng với attribute_option=specific_member
@apiParam   (EXECUTE_MISSION node_config:)    {Object}     missions.mission_config.RELATED_TO_ORDER.attributes.config_not_found_target     Cấu hình khi không tìm thấy cấu hình hợp lệ để chuyển tiếp.
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_ORDER.attributes.config_not_found_target.forward_type     Loại chuyển tiếp mặc định khi không tìm thấy cấu hình hợp lệ.<br/> Giá trị:<br/>
                                                                                                                                                        <code>queue_common</code>=Hàng chờ chung</br>
                                                                                                                                                        <code>queue_team</code>=Hàng chờ team.</br>
                                                                                                                                                        <code>specific_member</code>=Nhân viên cụ thể.</br>
                                                                                                                                                        <code>stop_and_push_error</code>=Không thực hiện chuyển tiếp</br>
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_ORDER.attributes.config_not_found_target.assignee_id     ID nhân viên cụ thể. <br/>Dùng với forward_type=specific_member
@apiParam   (EXECUTE_MISSION node_config:)    {ArrayString}     missions.mission_config.RELATED_TO_ORDER.attributes.specific_staff     Danh sách ID nhân viên cụ thể. <br/>Dùng với attribute_option=add_deal_in_staff_detail
@apiParam   (EXECUTE_MISSION node_config:)    {ArrayString}     missions.mission_config.RELATED_TO_ORDER.attributes.sale_process_id     Danh sách ID quy trình bán hàng. <br/>Dùng với attribute_option=remove_deal_supporter_all_deal_in_sale_process_detail
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_ORDER.attributes.field_key     Thông tin thay đổi. <br/>Dùng với attribute_type=update_deal
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_ORDER.attributes.modify_type     Kiểu cập nhật. Giá trị: </br><code>update</code>=Cập nhật giá trị</br>
                                                                                                                                                        <code>remove</code>=Xóa giá trị.</br>
                                                                                                                                                        <code>add</code>=Thêm giá trị.</br>
                                                                                                                                                         <br/>Dùng với attribute_type=update_deal
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_ORDER.attributes.remove_option     Tùy chọn xóa giá trị.Giá trị: </br><code>all</code>=Xóa tất cả giá trị</br>
                                                                                                                                                        <code>values</code>=Xóa giá trị cụ thể.</br>
                                                                                                                                                         <br/>Dùng với attribute_type=update_deal
@apiParam   (EXECUTE_MISSION node_config:)    {Array}     missions.mission_config.RELATED_TO_ORDER.attributes.update_values     Giá trị cần cập nhật. (Bắt buộc khi <code>modify_type</code> là <code>update</code>)
                                                                                                                                                         <br/>Dùng với attribute_type=update_deal
@apiParam   (EXECUTE_MISSION node_config:)    {Array}     missions.mission_config.RELATED_TO_ORDER.attributes.remove_values     Giá trị cần xóa. (Bắt buộc khi <code>modify_type</code> là <code>remove</code> hoặc <code>update</code>)
                                                                                                                                                         <br/>Dùng với attribute_type=update_deal
@apiParam   (EXECUTE_MISSION node_config:)    {Array}     missions.mission_config.RELATED_TO_ORDER.attributes.add_values     Giá trị cần thêm. (Bắt buộc khi <code>modify_type</code> là <code>add</code> hoặc <code>update</code>)
                                                                                                                                                         <br/>Dùng với attribute_type=update_deal
@apiParam   (EXECUTE_MISSION node_config:)    {Object}     missions.mission_config.RELATED_TO_ORDER.attribute_condition    Điều kiện thay đổi thuộc tính
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_ORDER.attribute_condition.audience_id    Mã audience



@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK     Nhiệm vụ liên quan tới công việc
@apiParam   (EXECUTE_MISSION node_config:)    {ArrayObject}     missions.mission_config.RELATED_TO_TASK.attributes     Danh sách thuộc tính
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attributes.attribute_type     Thông tin thuộc tính.<br/> Giá trị:<br/> <code>forward_task</code>=Chuyển tiếp công việc cho người khác</br>
                                                                                                                                                        <code>update_status_task</code>=Thay đổi trạng thái công việc</br>
                                                                                                                                                        <code>update_priority_level_task</code>=Thay đổi mức độ ưu tiên</br>
                                                                                                                                                        <code>add_tags_task</code>=Thêm tag phân loại công việc</br>
                                                                                                                                                        <code>update_dynamic_task</code>=Cập nhật giá trị trường tùy biến của công việc</br>
                                                                                                                                                        <code>add_comment_task</code>=Thêm bình luận công việc</br>
                                                                                                                                                        <code>update_parent_id_task</code>=Gán công việc cha</br>
                                                                                                                                                        <code>update_deadline</code>=Thay đổi thời hạn hoàn thành</br>
                                                                                                                                                        <code>update_planned_start_time</code>=Thay đổi thời gian dự kiến bắt đầu</br>
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attributes.attribute_option     Thông tin thuộc tính.<br/> Giá trị:<br/> <code>forward_to_leader</code>=Chuyển đến quản lý của nhân viên(Dùng với attribute_type=forward_task)</br>
                                                                                                                                                        <code>forward_to_specific_staff</code>=Chuyển đến nhân viên cụ thể(Dùng với attribute_type=forward_task)</br>
@apiParam   (EXECUTE_MISSION node_config:)    {ArrayString}     missions.mission_config.RELATED_TO_TASK.attributes.specific_staff     Danh sách ID nhân viên cụ thể. <br/>Dùng với: <br/>attribute_option=forward_to_specific_staff
@apiParam   (EXECUTE_MISSION node_config:)         {String}        missions.mission_config.RELATED_TO_TASK.attributes.assign_id           ID nhân viên cụ thể. <br/>Dùng với: <br/>attribute_type=<code>forward_task</code>
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attributes.task_status     Trạng thái công việc. <br/>Dùng với: <br/>attribute_type=update_status_task
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attributes.priority_level     Mức độ ưu tiên công việc. <br/>Dùng với: <br/>attribute_type=update_priority_level_task
@apiParam   (EXECUTE_MISSION node_config:)    {ArrayString}     missions.mission_config.RELATED_TO_TASK.attributes.tags_id     Danh sách ID Tag phân loại công việc. <br/>Dùng với: <br/>attribute_type=add_tags_task
@apiParam   (EXECUTE_MISSION node_config:)    {Object}     missions.mission_config.RELATED_TO_TASK.attributes.add_comment     Bình luận công việc. <br/>Dùng với: <br/>attribute_type=add_comment_task
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attributes.add_comment.content     Nội dung bình luận
@apiParam   (EXECUTE_MISSION node_config:)    {ArrayObject}     missions.mission_config.RELATED_TO_TASK.attributes.add_comment.mentions     Thông tin người được nhắc tới trong bình luận
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attributes.add_comment.mentions.fe_id     ID trên giao diện người được nhắc đến trong bình luận
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attributes.add_comment.mentions.account_id     ID người được nhắc định trong bình luận
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attributes.parent_id     id công việc cha. <br/>Dùng với: <br/>attribute_type=update_parent_id_task
@apiParam   (EXECUTE_MISSION node_config:)    {Object}     missions.mission_config.RELATED_TO_TASK.attributes.deadline     Thời hạn hoàn thành. <br/>Dùng với: <br/>attribute_type=update_deadline
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attributes.deadline.key     Giá trị: <br/><code>days_after_active_wf</code>=Số ngày sau ngày kích hoạt workflow
                                                                                                                                    <br/><code>on_time_active_wf</code>=Vào ngày kích hoạt workflow
                                                                                                                                    <br/><code>specific_day</code>=Vào ngày cụ thể
                                                                                                                                    <br/><code>delete</code>=Xóa ngày
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attributes.deadline.value     <ul>
                                                                                                                                    <li>key :: <code>days_after_active_wf</code></li>
                                                                                                                                    <ul>
                                                                                                                                        <li>không cần truyền</li>
                                                                                                                                    </ul>
                                                                                                                                    <li>key :: <code>on_time_active_wf</code></li>
                                                                                                                                    <ul>
                                                                                                                                        <li>không cần truyền</li>
                                                                                                                                    </ul>
                                                                                                                                    <li>key :: <code>specific_day</code></li>
                                                                                                                                    <ul>
                                                                                                                                        <li><code>kiểu format</code>:%Y-%m-%d (UTC)</li>
                                                                                                                                    </ul>
                                                                                                                                    <li>key :: <code>delete</code></li>
                                                                                                                                    <ul>
                                                                                                                                        <li>không cần truyền</li>
                                                                                                                                    </ul>
                                                                                                                                </ul>
@apiParam   (EXECUTE_MISSION node_config:)         {int}   missions.mission_config.RELATED_TO_TASK.attributes.deadline.day_number      giá trị ngày sau khi kích hoạt wf (dùng với key là days_after_active_wf)
@apiParam   (EXECUTE_MISSION node_config:)    {Object}     missions.mission_config.RELATED_TO_TASK.attributes.planned_start_time     Thời gian dự kiến bắt đầu. <br/>Dùng với: <br/>attribute_type=update_planned_start_time
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attributes.planned_start_time.key     <ul>
                                                                                                                                        <li><code>days_after_active_wf</code>:Số ngày sau ngày kích hoạt workflow</li>
                                                                                                                                        <li><code>on_time_active_wf</code>:Vào ngày kích hoạt workflow</li>
                                                                                                                                        <li><code>specific_day</code>:Vào ngày cụ thể</li>
                                                                                                                                        <li><code>delete</code>:Xóa ngày</li>
                                                                                                                                    </ul>
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attributes.planned_start_time.value     <ul>
                                                                                                                                                <li>key :: <code>days_after_active_wf</code></li>
                                                                                                                                                <ul>
                                                                                                                                                    <li>không cần truyền</li>
                                                                                                                                                </ul>
                                                                                                                                                <li>key :: <code>on_time_active_wf</code></li>
                                                                                                                                                <ul>
                                                                                                                                                    <li>không cần truyền</li>
                                                                                                                                                </ul>
                                                                                                                                                <li>key :: <code>specific_day</code></li>
                                                                                                                                                <ul>
                                                                                                                                                    <li><code>kiểu format</code>:%Y-%m-%d (UTC)</li>
                                                                                                                                                </ul>
                                                                                                                                                <li>key :: <code>delete</code></li>
                                                                                                                                                <ul>
                                                                                                                                                    <li>không cần truyền</li>
                                                                                                                                                </ul>
                                                                                                                                            </ul>
@apiParam   (EXECUTE_MISSION node_config:)         {int}   missions.mission_config.RELATED_TO_TASK.attributes.planned_start_time.day_number      giá trị ngày sau khi kích hoạt wf (dùng với key là days_after_active_wf)
@apiParam   (EXECUTE_MISSION node_config:)    {Object}     missions.mission_config.RELATED_TO_TASK.attributes.dynamic_task     Trường tùy biến công việc. <br/>Dùng với: <br/>attribute_type=update_dynamic_task
@apiParam   (EXECUTE_MISSION node_config:)         {string}    missions.mission_config.RELATED_TO_TASK.attributes.dynamic_task.field_key        field_key của trường tùy biến
@apiParam   (EXECUTE_MISSION node_config:)         {string}    missions.mission_config.RELATED_TO_TASK.attributes.dynamic_task.key              key hành động
                                                                    key hành động của display_type datepicker
                                                                    <ul>
                                                                        <li><code>days_after_active_wf</code>:Số ngày sau ngày kích hoạt workflow</li>
                                                                        <li><code>on_time_active_wf</code>:Vào ngày kích hoạt workflow</li>
                                                                        <li><code>specific_day</code>:Vào ngày cụ thể</li>
                                                                        <li><code>delete</code>:Xóa ngày</li>
                                                                    </ul>      
                                                                    key hành động của display_type multiple-line text/dropdown multiple-select/checkbox   
                                                                    <ul>
                                                                        <li><code>add_values</code>:Thêm mới giá trị</li>
                                                                        <li><code>remove_values</code>:Xóa giá trị</li>
                                                                        <li><code>change_value</code>:Thêm giá trị mới và xóa giá trị cũ</li>
                                                                    </ul>
@apiParam   (EXECUTE_MISSION node_config:)         {string}    missions.mission_config.RELATED_TO_TASK.attributes.dynamic_task.date_value              giá trị datestring (tùy theo key, dùng cho display_type datepicker)
                                                                    <ul>
                                                                        <li>key :: <code>days_after_active_wf</code></li>
                                                                        <ul>
                                                                            <li>không cần truyền</li>
                                                                        </ul>
                                                                        <li>key :: <code>on_time_active_wf</code></li>
                                                                        <ul>
                                                                            <li>không cần truyền</li>
                                                                        </ul>
                                                                        <li>key :: <code>specific_day</code></li>
                                                                        <ul>
                                                                            <li><code>kiểu format</code>:%Y-%m-%d(UTC)</li>
                                                                        </ul>
                                                                        <li>key :: <code>delete</code></li>
                                                                        <ul>
                                                                            <li>không cần truyền</li>
                                                                        </ul>
                                                                    </ul>
@apiParam   (EXECUTE_MISSION node_config:)            {int}   missions.mission_config.RELATED_TO_TASK.attributes.dynamic_task.day_number      giá trị ngày sau khi kích hoạt wf (dùng với key là days_after_active_wf)
@apiParam   (EXECUTE_MISSION node_config:)           {array} missions.mission_config.RELATED_TO_TASK.attributes.dynamic_task.add_values      danh sách giá trị được thêm  (tùy theo key, dùng cho display_type multiple-line text/dropdown multiple-select/checkbox)
@apiParam   (EXECUTE_MISSION node_config:)            {object}    missions.mission_config.RELATED_TO_TASK.attributes.dynamic_task.remove_values      cấu hình giá trị xóa  (tùy theo key, dùng cho display_type multiple-line text/dropdown multiple-select/checkbox)
@apiParam   (EXECUTE_MISSION node_config:)            {string}    missions.mission_config.RELATED_TO_TASK.attributes.dynamic_task.remove_values.key      key hành động
                                                                    <ul>
                                                                        <li><code>all_current: </code> Tất cả giá trị đang có</li>
                                                                        <li><code>all_chosen: </code> Tất cả giá trị được chọn</li>
                                                                    </ul>
@apiParam   (EXECUTE_MISSION node_config:)            {array} missions.mission_config.RELATED_TO_TASK.attributes.dynamic_task.remove_values.values      danh sách giá trị xóa
@apiParam   (EXECUTE_MISSION node_config:)            {string}    missions.mission_config.RELATED_TO_TASK.attributes.dynamic_task.value          giá trị thay đổi (dùng cho display_type single-line)

@apiParam   (EXECUTE_MISSION node_config:)    {Object}     missions.mission_config.RELATED_TO_TASK.attribute_condition    Điều kiện thay đổi thuộc tính
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TASK.attribute_condition.audience_id    Mã audience



@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TICKET     Nhiệm vụ liên quan tới ticket
@apiParam   (EXECUTE_MISSION node_config:)    {ArrayObject}     missions.mission_config.RELATED_TO_TICKET.attributes     Danh sách thuộc tính
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TICKET.attributes.attribute_type     Thông tin thuộc tính. <br/>Giá trị: <br/><code>forward_ticket</code>=Chuyển tiếp ticket của User</br>
                                                                                                                                                        <code>add_ticket_supporter</code>=Gán User làm Oppty Supporter</br>
                                                                                                                                                        <code>remove_ticket_supporter</code>=Xóa vai trò supporter của User</br>
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TICKET.attributes.attribute_option     Thông tin thuộc tính. <br/>Giá trị: <br/><code>forward_to_leader</code>=Chuyển đến quản lý của nhân viên(Dùng với attribute_type=forward_ticket)</br>
                                                                                                                                                        <code>forward_to_wait_assignment</code>=Chuyển đến hàng chờ của ticket(Dùng với attribute_type=forward_ticket)</br>
                                                                                                                                                        <code>add_to_all_team_member</code>=Gán vào toàn bộ Ticket của các thành viên trong team(Dùng với attribute_type=add_ticket_supporter)</br>
                                                                                                                                                        <code>add_to_specific_staff</code>=Gán vào Ticket của thành viên cụ thể(Dùng với attribute_type=add_ticket_supporter)</br>
                                                                                                                                                        <code>remove_supporter_all_ticket</code>=Xóa vai trò Supporter trên toàn Ticket(Dùng với attribute_type=remove_ticket_supporter)</br>
                                                                                                                                                        <code>remove_supporter_specific_ticket_type</code>=Xóa vai trò Supporter ticket với kiểu ticket cụ thể(Dùng với attribute_type=remove_ticket_supporter)</br>
@apiParam   (EXECUTE_MISSION node_config:)    {ArrayString}     missions.mission_config.RELATED_TO_TICKET.attributes.specific_staff     Danh sách ID nhân viên cụ thể. <br/>Dùng với attribute_option=add_to_specific_staff
@apiParam   (EXECUTE_MISSION node_config:)    {ArrayString}     missions.mission_config.RELATED_TO_TICKET.attributes.type_ticket_id     Danh sách ID kiểu ticket. <br/>Dùng với attribute_option=remove_supporter_specific_ticket_type
@apiParam   (EXECUTE_MISSION node_config:)    {Object}     missions.mission_config.RELATED_TO_TICKET.attribute_condition    Điều kiện thay đổi thuộc tính
@apiParam   (EXECUTE_MISSION node_config:)    {String}     missions.mission_config.RELATED_TO_TICKET.attribute_condition.audience_id    Mã audience
@apiParamExample    {json}  EXECUTE_MISSION node_config:
{
    "missions": [
        {
            "mission_id": "dc95eff4-0b7c-11ef-a0ee-38d57a786a3e",
            "mission_name": "Nhiệm vụ 1",
            "mission_type": "RELATED_TO_ORDER",
            "mission_config": {
                "attributes": [
                    {
                        "attribute_type": "forward_deal/add_deal_supporter/remove_deal_supporter",
                        "attribute_option": "forward_to_leader/forward_to_team/add_all_deal_staff_in_team/add_deal_in_staff_detail/remove_deal_supporter_all_deal/remove_deal_supporter_all_deal_in_sale_process_detail"
                        "specific_staff": ["672295c0-0b7e-11ef-bff3-38d57a786a3e"],
                        "sale_process_id": ["877d713e-0b7e-11ef-ac1a-38d57a786a3e", "8d26a6ba-0b7e-11ef-91f6-38d57a786a3e"]
                    },
                    {
                        "attribute_type": "update_deal",
                        "field_key": "_dyn_single_line_text_1",
                        "modify_type": "update", // remove, add, update
                        "update_values": ["data1"]
                    }, // Đối với kiểu single_line_số, radio là tương tự
                    {
                        "attribute_type": "update_deal",
                        "field_key": "_dyn_multiple_text_1",
                        "modify_type": "add", // remove, add, update
                        "update_values": [],
                        "remove_values": [], // all: optional 
                        "remove_option": "all" // all: Tất cả giá trị đang có, values: Giá trị cụ thể 
                        "add_values": ["data1"]
                    }, 
                    {
                        "attribute_type": "update_deal",
                        "field_key": "_dyn_multiple_line_text_1",
                        "modify_type": "remove", // remove, add, update
                        "update_values": [],
                        "remove_values": ["data1"],
                        "add_values": []
                    },
                    {
                        "attribute_type": "update_deal",
                        "field_key": "_dyn_multiline_text_1",
                        "modify_type": "update", // remove, add, update
                        "update_values": [],
                        "remove_values": ["data1"],
                        "add_values": ["data2"]
                    }, // Đối với các kiểu multiple số, check box cũng tương tự
                    {
                        "attribute_type": "update_deal",
                        "field_key": "assignee_id", // Nhân viên phụ trách CHB.
                        "modify_type": "update"
                        "team_id": "323453dfsf-9ebe-458a-a730-4343454sdds", // Thông tin team
                        "update_values": ["7e709247-9ebe-458a-a730-2f8159dbbea0"]
                    },
                    {
                        "attribute_type": "update_deal",
                        "field_key": "supporter_ids", // Nhân viên hỗ trợ CHB
                        "modify_type": "add", // remove, add, update
                        "remove_values": ["7e709247-9ebe-458a-a730-2f8159dbbea0"], // all: optional"
                        "remove_options: "all" // all: Tất cả giá trị đang có, values: Giá trị cụ thể
                        "add_values": ["7e709247-9ebe-458a-a730-2f8159dbbea0"],
                        "update_values": ["006b34f6-ce75-44eb-834a-6d1b3eec1477"]
                    }
                    {
                        "attribute_type": "update_deal",
                        "field_key": "sale_process_id", // Quy trình bán hàng
                        "modify_type": "update"
                        "update_values": ["651b87ea0fd81b640d1dab16"],  // Quy trình bán hàng
                        "state_code": "X3J4BUWX" // Trạng thái CHB
                    },
                    {
                        "attribute_type": "update_deal",
                        "field_key": "product_info", // Dòng sản phẩm/Sản phẩm
                        "modify_type": "update", // Chỉ được phép update 
                        "customer_group": "", // Nhóm khách hàng
                        "product_line": [], // Dòng sản phẩm (truyền một phần tử dòng sản phẩm)
                        "product_types: [] // Loại sản phẩm
                        "products_bank": [] // Sản phẩm (Bank)
                    },
                    ... Các field base khác theo định dạng single line thì cứ theo cấu trúc dưới
                    {
                        "attribute_type": "update_deal",
                        "field_key": "field_base_1",
                        "modify_type": "update", // remove, add, update
                        "update_values": ["data1"]
                    }, // Đối với kiểu single_line số, chữ, radio là tương tự
                ],
                "attribute_condition": {
                    "audience_id": "b3d34b8a-0b7e-11ef-ab09-38d57a786a3e"
                }
            }
        },
        {
            "mission_id": "c74e040a-0b7e-11ef-bf3e-38d57a786a3e",
            "mission_name": "Nhiệm vụ 2",
            "mission_type": "RELATED_TO_TASK",
            "mission_config": {
                "attributes": [
                    {
                        "attribute_type": "forward_task/update_status_task",
                        "attribute_option": "forward_to_leader/forward_to_specific_staff"
                        "specific_staff": ["672295c0-0b7e-11ef-bff3-38d57a786a3e"],
                        "task_status": ""
                    }
                ],
                "attribute_condition": {
                    "audience_id": "b3d34b8a-0b7e-11ef-ab09-38d57a786a3e"
                }
            }
        },
        {
            "mission_id": "ca92089a-0b7e-11ef-ae5a-38d57a786a3e",
            "mission_name": "Nhiệm vụ 3",
            "mission_type": "RELATED_TO_TICKET",
            "mission_config": {
                "attributes": [
                    {
                        "attribute_type": "forward_ticket/add_ticket_supporter/remove_ticket_supporter",
                        "attribute_option": "forward_to_leader/forward_to_wait_assignment/add_to_all_team_member/add_to_specific_staff/remove_supporter_all_ticket/remove_supporter_specific_ticket_type"
                        "specific_staff": ["672295c0-0b7e-11ef-bff3-38d57a786a3e"],
                        "type_ticket_id": ["877d713e-0b7e-11ef-ac1a-38d57a786a3e", "8d26a6ba-0b7e-11ef-91f6-38d57a786a3e"]
                    }
                ],
                "attribute_condition": {
                    "audience_id": "b3d34b8a-0b7e-11ef-ab09-38d57a786a3e"
                }
            }
        }
    ]
}

@apiParam   (CONDITION_FILTER_LIST_TASK node_config:)    {String}     audience_id     ID audience bộ lọc
@apiParamExample    {json}  CONDITION_FILTER_LIST_TASK node_config:
{
    "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b"
}

@apiParam   (CHECK_CUSTOMER_NOTIFY_INTERACTION node_config:)    {String}    interaction_type  Kiểu tương tác. Giá trị: </br><code>SENT</code>=Đã nhận thông báo
                                                                            </br><code>OPENED</code>=Đã mở thông báo
                                                                            </br><code>CLICKED_LINK</code>=Đã bấm link
@apiParam   (CHECK_CUSTOMER_NOTIFY_INTERACTION node_config:)    {Object}    wait_config  Thông tin thời gian chờ
@apiParam   (CHECK_CUSTOMER_NOTIFY_INTERACTION node_config:)    {Number}    wait_config.days  Số ngày.
@apiParam   (CHECK_CUSTOMER_NOTIFY_INTERACTION node_config:)    {Number}    wait_config.hours  Số giờ.
@apiParam   (CHECK_CUSTOMER_NOTIFY_INTERACTION node_config:)    {Number}    wait_config.minutes  Số phút.
@apiParamExample    {json}  CHECK_CUSTOMER_NOTIFY_INTERACTION node_config:
{
    "interaction_type": "SENT/OPENED/CLICKED_LINK",
    "wait_config": {
        "days": 1,
        "hours": 1,
        "minutes": 1
    }
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công"
}
"""
********************************* Xóa Workflow *******************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {put} /workflow/delete Xóa workflow
@apiGroup Workflow
@apiVersion 1.0.0
@apiName DeleteWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    workflow_ids  Danh sách workflow cần xóa.

@apiParamExample    {json}  Body:
{   
    "workflow_ids": ["965217ca-c6f2-11ed-8fe8-c7a3239de083","23888a1a-c7d5-11ed-a58f-cd3807f6ef5e"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công"
}
"""
************************* Lấy thông tin chi tiết cấu hình Workflow ***********************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /workflow/config Lấy thông tin chi tiết cấu hình Workflow
@apiGroup Workflow
@apiVersion 1.0.0
@apiName GetConfigWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}    workflow_id  Mã workflow cần lấy cấu hình

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "workflow_id": "",
    "diagram": [],
    "nodes":[
        {
            "node_id": "72d95c56-c6f5-11ed-8fe8-c7a3239de083",
            "node_name": "Start"
            "node_type": "ROOT",
            "element_type": "OPERATION",
            "node_connection": {
                "next_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083",
                "previous_node_id": null
            }
        },
        {
            "node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083",
            "node_type": "TRIGGER_EVENT",
            "element_type": "TRIGGER",
            "node_name": "Ten khoi"
            "node_config": {
                "audience_id": "c830e338-e287-11ed-b545-8150b6abec2b"
            },
            "node_connection": {
                "next_node_id": "84497070-c6f5-11ed-8fe8-c7a3239de083",
                "previous_node_id": "72d95c56-c6f5-11ed-8fe8-c7a3239de083"
            }
        },
        {
            "node_id": "4506c31a-c7a2-11ed-a58f-cd3807f6ef5e",
            "node_type": "CONDITION_FILTER_PROFILE",
            "element_type": "SPECIFIC_CONDITION",
            "node_name": "Ten khoi",
            "node_config": {
                "audience_filter": [
                    {
                        "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b",
                        "position": 0,
                        "operator": null
                    }
                ]
            },
            "node_connection": [
                {
                    "result": "yes",
                    "next_node_id": "94d5413a-c6f5-11ed-8fe8-c7a3239de083",
                    "previous_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083"
                },
                {
                    "result": "no",
                    "next_node_id": "2d5db6d8-c7a2-11ed-a58f-cd3807f6ef5e",
                    "previous_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083"
                }
            ]
        },
        {
            "node_id": "4506c31a-c7a2-11ed-a58f-cd3807f6ef5e",
            "node_type": "CONDITION_FILTER_COMPANY",
            "element_type": "SPECIFIC_CONDITION",
            "node_name": "Ten khoi",
            "node_config": {
                "audience_filter": [
                    {
                        "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b",
                        "position": 0,
                        "operator": null
                    }
                ]
            },
            "node_connection": [
                {
                    "result": "yes",
                    "next_node_id": "94d5413a-c6f5-11ed-8fe8-c7a3239de083",
                    "previous_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083"
                },
                {
                    "result": "no",
                    "next_node_id": "2d5db6d8-c7a2-11ed-a58f-cd3807f6ef5e",
                    "previous_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083"
                }
            ]
        },
        {
            "node_id": "2d5db6d8-c7a2-11ed-a58f-cd3807f6ef5e",
            "node_type": "ACTION_CREATE_TICKET",
            "element_type": "MANAGEMENT_ACTION",
            "node_name": "Ten khoi",
            "config": {
                "name": "Ten ticket",
                "type_ticket_id": "Kieu ticket",
                "priority_level": "Muc do uu tien",
                "status_process_id": "Trang thai xu ly",
                "description": "Mo ta",
                "assign_information": {
                    "type_assign": "wait_assignment/staff",
                    "assign_id": "",
                    "type_staff_assign": "assign_staff/staff_charge_switchboard/profile_owner"
                }
            },
            "node_connection": {
                "next_node_id": "b28edc7c-c6f5-11ed-8fe8-c7a3239de083",
                "previous_node_id": "4506c31a-c7a2-11ed-a58f-cd3807f6ef5e"
            }
        },
        {
            "node_id": "b28edc7c-c6f5-11ed-8fe8-c7a3239de083",
            "node_name": "Thoat",
            "node_type": "EXIT",
            "element_type": "OPERATION",
            "node_connection": {
                "next_node_id": null,
                "previous_node_id": "94d5413a-c6f5-11ed-8fe8-c7a3239de083"
            }
        }
    ],
    "other_config": {
        "dynamic_field_with_node": [
            {
                "node_id": "2cbd3b52-48ca-11ef-8b7e-38d57a786a3e",
                "field_key": "_dyn__p__multiple_line_text_1721624983405"
            }    
        ]
    }
}
"""
************************* Lấy thông tin chi tiết Workflow ********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /workflow/detail Lấy thông tin chi tiết Workflow
@apiGroup Workflow
@apiVersion 1.0.0
@apiName DetailWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}    workflow_id  Mã workflow cần lấy thông tin chi tiết

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "created_time": "2023-06-02T09:51:15Z",
    "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "status": "DRAFT",
    "target_type": "USER",
    "updated_time": "2023-06-02T09:51:15Z",
    "updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "workflow_description": "",
    "workflow_id": "0425e570-012b-11ee-824a-38d57a786a3d",
    "workflow_name": "[HoaTest] Tạo ticket sau khi profile phát sinh cuộc gọi",
    "workflow_type": "EVENT"
}
"""
************************************ Lưu nháp Workflow ***********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /workflow/draft Lưu nháp workflow
@apiGroup Workflow
@apiVersion 1.0.0
@apiName DraftWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    workflow_id  Mã workflow cần cấu hình
@apiParam   (Body:)    {String}    [diagram]  Thông tin cấu hình dành cho Frontend sử dụng.
@apiParam   (Body:)    {ArrayObject}    nodes   Danh sách các thành phần chi tiết của workflow.
@apiParamExample    {json}  Body:
{
    "workflow_id": "146940fa-0476-11ee-824a-38d57a786a3d",
    "diagram": [],
    "nodes":[
        {
            "node_id": "72d95c56-c6f5-11ed-8fe8-c7a3239de083",
            "node_name": "Start"
            "node_type": "ROOT",
            "element_type": "OPERATION",
            "node_connection": {
                "next_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083",
                "previous_node_id": null
            }
        }
    ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
************************************ Sao chép Workflow ***********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /workflow/copy Sao chép workflow
@apiGroup Workflow
@apiVersion 1.0.0
@apiName CopyWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    copy_workflow_id  Mã workflow để sao chép
@apiParam   (Body:)    {String}    workflow_name  Tên workflow mới
@apiParam   (Body:)    {String}    [workflow_description]  Mô tả workflow
@apiParam   (Body:)    {String}    target_type  Đối tượng workflow. Giá trị: <br/>
                                                                                 <code>USER</code>=User<br/>
                                                                                 <code>MAILBOX</code>=User<br/>
                                                                                 <code>TASK</code>=Task<br/>
                                                                                 <code>PROFILE</code>=Profile<br/>
                                                                                 <code>OPPTY</code>=Oppty<br/>
@apiParam   (Body:)     {String}    workflow_type  Loại workflow. Giá trị: <br/><code>EVENT</code>=Khi phát sinh event
                                                                            <br/><code>SCHEDULE</code>=Lịch trình
@apiParamExample    {json}  Body:
{
    "copy_workflow_id": "146940fa-0476-11ee-824a-38d57a786a3d",
    "workflow_name": "Workflow copy",
    "workflow_description": "description",
    "workflow_type": "EVENT",
    "target_type": "USER"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "created_time": "2023-06-06T13:45:07Z",
        "created_user": null,
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "status": "DRAFT",
        "target_type": "USER",
        "updated_time": "2023-06-06T13:45:07Z",
        "updated_user": null,
        "workflow_description": "This is description of workflow",
        "workflow_id": "58fa7898-0470-11ee-824a-38d57a786a3d",
        "workflow_name": "Tên workflow mới",
        "workflow_type": "EVENT"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
************************************ Kích hoạt Workflow **********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /workflow/activate Kích hoạt workflow
@apiGroup Workflow
@apiVersion 1.0.0
@apiName ActivateWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    workflow_id  Mã workflow cần activate
@apiParamExample    {json}  Body:
{
    "workflow_id": "146940fa-0476-11ee-824a-38d57a786a3d"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
************************************ Gửi email thử nghiệm ********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /workflow/test/send-email Gửi email thử nghiệm
@apiGroup Workflow
@apiVersion 1.0.0
@apiName WorkflowTestSendEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    workflow_id  Mã workflow
@apiParam   (Body:)    {String}   email_type       Loại email. Giá trị: <br/><code>TRIGGER_EVENT_MAIL</code>=Là email phát sinh event
                                                                                                            <br/><code>OTHER_DOMAIN_MAIL</code>=Là email có domain bất kỳ
                                                                                                            <br/><code>WORKING_EMAIL</code>=Là e-mail working của doanh nghiệp
                                                                                                            <br/><code>WORKING_EMAIL_PERSONAL</code>=Là e-mail working của cá nhân
@apiParam   (Body:)    {String}   title       Tiêu đề email
@apiParam   (Body:)    {String}   content       Nội dung email
@apiParam   (Body:)     {Array}     personalizes  Thông tin cá nhân hóa
@apiParam   (Body:)    {Object}   sender_config  Thông tin cấu hình gửi
@apiParam   (Body:)    {String}   [sender_config.email_config_id]  config_id của email gửi được chọn. Áp dụng với email_type=<code>WORKING_EMAIL</code>/<code>WORKING_EMAIL_PERSONAL</code>
@apiParam   (Body:)    {String}   [sender_config.sender_type]  Loại người gửi. Áp dụng với email_type=<code>WORKING_EMAIL_PERSONAL</code>. <br/> Giá trị: </br><code>PROFILE_OWNER</code>=Profile owner
                                                                                                                                                              </br><code>WORKFLOW_CREATED_USER</code>=Người tạo workflow
@apiParam   (Body:)    {String}   sender_config.sender_id  Email người gửi
@apiParam   (Body:)    {String}   sender_config.sender_name  Tên người gửi
@apiParam   (Body:)    {String}   sender_config.domain     Tên miền gửi email
@apiParam   (Body:)    {Boolean}   sender_config.reply_to   Sử dụng làm email nhận phản hồi. Giá trị: <br/><code>True</code>/<code>False</code>
@apiParam   (Body:)    {String}   [sender_config.reply_email]   Email nhận phản hồi.
@apiParam   (Body:)    {String}   sender_config.send_to    Email nhận thông điệp. 
@apiParam   (Body:)    {ArrayObject}   [attachments]  Thông tin tệp đính kèm
@apiParam   (Body:)    {String}   attachments.url  Url file
@apiParam   (Body:)    {String}   attachments.local_path  Đường dẫn đến file trên server
@apiParam   (Body:)    {String}   attachments.format     Định dạng file
@apiParam   (Body:)    {String}   attachments.filename   Tên file
@apiParamExample    {json}  Body:
{
    "workflow_id": "146940fa-0476-11ee-824a-38d57a786a3d",
    "title": "Tieu de email",
    "content": "Noi dung email",
    "attachments": [
        {
            "url": "https://aaa.aaa/...",
            "local_path": "",
            "format": "",
            "filename": ""
        }  
    ],
    "personalizes": [
        {
            "field_name": "Đối tượng",
            "key": "WORKFLOW_TARGET",
            "replace": "*|WORKFLOW_TARGET|*",
            "source": "WORKFLOW"
        },
        {
            "field_name": "Tên workflow",
            "key": "WORKFLOW_NAME",
            "replace": "*|WORKFLOW_NAME|*",
            "source": "WORKFLOW"
        }
    ],
    "sender_config":{
      "sender_id": "<EMAIL>",
      "sender_name": "hoadq",
      "domain": "unsub.mobio.vn",
      "reply_to": True/False,
      "reply_email": "<EMAIL>",
      "send_to": "<EMAIL>"
    }
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
************************************ Gửi sms thử nghiệm **********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /workflow/test/send-sms Gửi sms thử nghiệm
@apiGroup Workflow
@apiVersion 1.0.0
@apiName WorkflowTestSendSMS

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    workflow_id  Mã workflow
@apiParam   (Body)    {String}   content       Nội dung tin nhắn
@apiParam   (Body)     {Array}     personalizes  Thông tin cá nhân hóa
@apiParam   (Body)     {Array}    links  Thông tin chèn link
@apiParam   (Body)     {String}    links.url  URL
@apiParam   (Body)     {String}    [links.domain_shorting_link]  Domain rút gọn URL
@apiParam   (Body)     {String}    links.key  Key link. Giá trị: <code>\*|LINK|\*</code>
@apiParam   (Body)    {Object}   sender_config  Thông tin cấu hình gửi
@apiParam   (Body)    {String}   sender_config.brand_name  SMS Brandname
@apiParam   (Body)    {ArrayString}   sender_config.send_to  Danh sách Số điện thoại nhận thông điệp
@apiParamExample    {json}  Body:
{   
    "workflow_id": "146940fa-0476-11ee-824a-38d57a786a3d",
    "content": "Noi dung sms",
    "personalizes": [
        {
            "field_name": "Đối tượng",
            "key": "WORKFLOW_TARGET",
            "replace": "*|WORKFLOW_TARGET|*",
            "source": "WORKFLOW"
        },
        {
            "field_name": "Tên workflow",
            "key": "WORKFLOW_NAME",
            "replace": "*|WORKFLOW_NAME|*",
            "source": "WORKFLOW"
        }
    ],
    "links": [
        {
            "url": "http://abc.com",
            "domain_shorting_link": "https://url.mobio.io/",
            "key": "*|LINK|*"
        }
    ],
    "sender_config":{
      "brand_name":"MOBIO",
      "send_to": ["0976358700"]
    }
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
************************************ Giải mã dữ liệu masking *****************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /decrypt Giải mã dữ liệu masking
@apiGroup Workflow
@apiVersion 1.0.0
@apiName DecryptData

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    field  Tên key cần giải mã
@apiParam   (Body:)    {ArrayString}   values       Giá trị cần giải mã
@apiParamExample    {json}  Body:
{   
    "field": "phone",
    "values": [
        "abcdefgh"
    ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
    "data": [
        {
            "value": "abcdefgh",
            "value_decrypt": "0987654321",
            "decrypt_error": null
        },
        {
            "value": "abcdefgh",
            "value_decrypt": null,
            "decrypt_error": "Decrypt error"
        }
    ]
}
"""

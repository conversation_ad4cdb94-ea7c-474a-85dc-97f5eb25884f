********************************* Dynamic Event Add Mapping Register Audience *********
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/dynamic_event/mapping_register_audience Add Mapping Register Audience.
@apiDescription API tạo mapping ELS, đăng ký audience 
@apiGroup Dynamic event
@apiVersion 1.0.0
@apiName  mapping_register_audience
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String}    _id       Id dyn_event
@apiParam   (Body:)    {String}    event_key       Tên event
@apiParam   (Body:)    {String}    mapping  Bao gồm dict key và kiểu dữ liệu
@apiParam   (Body:)    {String}    merchant_id   merchant_id 
@apiParam   (mapping:) {String}    key  các field key cần tạo và nhận giá trị là kiểu dữ liệu`

@apiParamExample  {json} Body request example
{
    "_id": "619c722d5a7b9e8eda0ea0f9",
    "event_key": "test_api_tao_dyn_event",
    "mapping": {
        "status": "string",
        "action_time": "date",
        "email": "string"
    },
    "merchant_id": "b758d486-4133-11ec-973a-0242ac130003"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "register_audience": true,
        "update_mapping": true
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
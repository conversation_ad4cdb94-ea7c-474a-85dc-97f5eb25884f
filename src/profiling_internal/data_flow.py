************************* Upsert Profile từ luồng DataFlow ****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {POST} [HOST]/profiling/internal/dataflow/v3.0/profile/upsert Upsert profile luồng dataflow từ module khác
@apiDescription Upsert profile luồng dataflow từ module khác; <code>key_host: pf-api-internal-dataflow-service-host</code>
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName ProfilingDataflowUpsertProfile

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {Object}   profile_data     Dữ liệu upsert profile
@apiParam      (Body:)     {String}   module       Module gọi sang: <code>Example: SALE, COMPANY, TICKET</code> 
@apiParam      (Body:)     {Object}   profile_connector_config      Thông tin cấu hình của connector
@apiParam      (Body:)     {Int}      connector_id    connector_id
@apiParam      (Body:)     {String}   merchant_id     Merchant ID
@apiParam      (Body:)     {String}   message_id  Message ID

@apiParamExample [json] Body example:
{
    "profile_data": {
        "name": "HienDT",
        "primary_email": "<EMAIL>",
        "source": "Core",
        "profile_identify": [
            {
                "identify_type": "citizen_identity",
                "identify_value": "cccd3466"
            }
        ],
        "cif": "cif_hien",
        "address_personal": [
            {
                "type": "contact_address",
                "detail": "test dia chi",
                "district": "Xã Hiếu Nghĩa",
                "city": "Huyện Vũng Liêm",
                "county": "Tỉnh Vĩnh Long",
                "country": "Việt Nam"
            }
        ]
    },
    "module": "COMPANY", // COMPANY, TICKET, SALE
    "profile_connector_config": {
        "id": 9,
        "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
        "name": "profiles",
        "fields_verify": [],
        "is_trust": true,
        "fields_replace": [],
        "fields_append": [],
        "fields_replace_ignore_empty": [
            "source",
            "cif",
            "name",
            "primary_phone",
            "primary_email",
            "profile_identify",
            "profile_owner",
            "address_personal"
        ],
        "unification_rules": {
            "operators": [
                {
                    "priority": 1,
                    "fields": {
                        "cif": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        },
                        "source": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        }
                    }
                }
            ],
            "consent": {
                "analytics_consent": "Có",
                "mkt_consent": "Có",
                "tracking_consent": "Có"
            }
        },
        "data_recording_rules": {
            "operators": [
                {
                    "priority": 1,
                    "fields": {
                        "cif": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        }
                    }
                }
            ],
            "consent": {
                "analytics_consent": "Có",
                "mkt_consent": "Có",
                "tracking_consent": "Có"
            }
        }
    },
    "connector_id": 9,
    "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
    "message_id": 1719393468459171
}

@apiSuccessExample  {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "state": "processed", // "processed"; "error"
    "profile": {
        "profile_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d688",
        "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687"
    },
    "action": "add", // "add"; "update"; null
    "reason": "Có thông tin nếu state=error"
}
"""


**************** Upsert Profile từ luồng AppMarketPlace => DataFlow *******************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} {topic_AppMarketPlace_tao} Upsert profile luồng dataflow từ module AppMarketPlace
@apiDescription Upsert profile luồng dataflow từ module AppMarketPlace
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName ProfilingDataflowUpsertProfileAppMarketPlace

@apiParam      (Input:)     {Object}   event_value.profile_data     Dữ liệu upsert profile
@apiParam      (Input:)     {Object}   profile_connector_config      Thông tin cấu hình của connector
@apiParam      (Input:)     {Int}      connector_id    connector_id
@apiParam      (Input:)     {String}   merchant_id     Merchant ID
@apiParam      (Input:)     {String}   message_id  Message ID
@apiParam      (Input:)     {Object}   callback  Thông tin callback trả về Orchestration

@apiParamExample [json] Input example:
{
    "event_value":
    {
        "profile_data": {
            "name": "HienDT",
            "primary_email": "<EMAIL>",
            "source": "Core",
            "profile_identify": [
                {
                    "identify_type": "citizen_identity",
                    "identify_value": "cccd3466"
                }
            ],
            "cif": "cif_hien",
            "address_personal": [
                {
                    "type": "contact_address",
                    "detail": "test dia chi",
                    "district": "Xã Hiếu Nghĩa",
                    "city": "Huyện Vũng Liêm",
                    "county": "Tỉnh Vĩnh Long",
                    "country": "Việt Nam"
                }
            ]
        },
        "company_data": {

        }
    },
    "primary_object": "profile",
    "connector_id": 20,
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "source_key": "api"
    "created_time": "2024-07-01 06:59:44.120",
    "session_id": 20240701066837,
    "state": "consume",
    "message_id": 1719817184120345,
    "profile_connector_config": {
        "name": "profiles",
        "fields_verify": [],
        "is_trust": true,
        "fields_replace": [],
        "fields_append": [],
        "fields_replace_ignore_empty": [
            "source",
            "cif",
            "name",
            "primary_phone",
            "primary_email",
            "profile_identify",
            "profile_owner",
            "address_personal"
        ],
        "unification_rules": {
            "operators": [
                {
                    "priority": 1,
                    "fields": {
                        "cif": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        },
                        "source": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        }
                    }
                }
            ],
            "consent": {
                "analytics_consent": "Có",
                "mkt_consent": "Có",
                "tracking_consent": "Có"
            }
        },
        "data_recording_rules": {
            "operators": [
                {
                    "priority": 1,
                    "fields": {
                        "cif": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        }
                    }
                }
            ],
            "consent": {
                "analytics_consent": "Có",
                "mkt_consent": "Có",
                "tracking_consent": "Có"
            }
        },
    },
    "company_connector_config": {
        ...
    },
    "callback":
    {
        "callback_type": "queue",
        "queue_config":
        {
            "target": "data-flow.1b99bdcf-d582-4f49-9715-1b61dfff3924.profiles.20240701066837_callback",
            "key": 1719817184120345
        },
        "data": {
            "session_id": 20240701066837,
            "connector_id": 20,
            "created_time": "2024-07-01 06:59:44.120",
            "state": "consume",
            "message_id": 1719817184120345,
            "source_key": "api"
        }
    },
    "version": 1
}

@apiSuccessExample  {json}  {json}  Callback Queue (Trả về theo yêu cầu của Orchestration để lên báo cáo):
{
    "session_id": 20240701066837,
    "connector_id": 20,
    "created_time": "2024-07-01 06:59:44.120", // created_time: datetime.utcnow()
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "result": {
        "profile": {
            "profile_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d688",
            "action": "add",
            "state": "processed"
        },
        "company": {
            "state": "error",
            "reason": "phone wrong"
        }
    },
    "source_key": "api",
    "state": "error",
    "message_id": 1719817184120345,
    "reason": "company required"
}
"""


************************* Update Profile từ luồng BACKEND_MOBILE_EKYC *****************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {POST} [HOST]/profiling/internal/dataflow/v3.0/profile/upsert Update profile từ luồng BACKEND_MOBILE_EKYC
@apiDescription Upsert profile luồng dataflow từ module khác; <code>key_host: pf-api-internal-dataflow-service-host</code>; <br>
<code>API Này thừa kế từ api Upsert profile luồng dataflow; Các luồng tương tự như này về sau này sẽ có kế hoạch hiển thị ra bên ngoài cấu hình được</code>
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName ProfilingBackendMobileUpdateProfile

@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {Object}   profile_data     Dữ liệu upsert profile
@apiParam      (Body:)     {String}   module       Module gọi sang: <code>Example: SALE, COMPANY, TICKET, BACKEND_MOBILE</code> 
@apiParam      (Body:)     {Object}   profile_connector_config      Thông tin cấu hình của connector
@apiParam      (Body:)     {String}   connector_id    connector_id: <code>BACKEND_MOBILE_EKYC</code>
@apiParam      (Body:)     {String}   merchant_id     Merchant ID
@apiParam      (Body:)     {String}   message_id  Message ID

@apiParamExample [json] Body example:
{
    "profile_data": {
        "profile_id": "1a801959-8a85-43fc-a06c-c7beb915ca3d",
        "cif": "cif_test_1"
    },
    "module": "MOBILE_BACKEND",
    "profile_connector_config": {
        "id": "BACKEND_MOBILE_EKYC",
        "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
        "name": "profiles",
        "fields_verify": [],
        "is_trust": true,
        "fields_replace": [],
        "fields_append": [
            "cif"
        ],
        "fields_replace_ignore_empty": [],
        "unification_rules": {
            "operators": [
                {
                    "priority": 1,
                    "fields": {
                        "profile_id": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        }
                    }
                }
            ]
        },
        "data_recording_rules": {
            "operators": [
                {
                    "priority": 1,
                    "fields": {
                        "profile_id": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        }
                    }
                }
            ]
        }
    },
    "connector_id": "BACKEND_MOBILE_EKYC",
    "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687"
}

@apiSuccessExample  {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "state": "processed", // "processed"; "error"
    "profile": {
        "profile_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d688",
        "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687"
    },
    "action": "add", // "add"; "update"; null
    "reason": "Có thông tin nếu state=error"
}
"""
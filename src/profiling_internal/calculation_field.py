********************** Queue calculation field ****************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} profiling-update-profile-cf [QUEUE] Profiling update profile calculation field
@apiDescription Profiling update profile calculation field
@apiGroup CalculationField
@apiVersion 1.0.0
@apiName ProfilingUpdateProfileCalculationField

@apiParam      (Input:)     {String}   merchant_id Merchant ID
@apiParam      (Input:)     {Array}    data danh sách dữ liệu profile + calculation field; <code>data max 200 profile</code>
@apiParam      (Input:)     {String}   action_time Thời gian đ<PERSON>y tin

@apiParamExample [json] Input example:
{
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "data": [
        {
            "profile_id": "1b99bdcf-d582-4f49-9715-1b61dfff3920",
            "_dyn_field_calculation_001": 10000
        }
    ]
    "callback": {
        "callback_type": "queue",
        "queue_config": {
          "target": "profiling-update-profile-cf",
          "key": "f9855ab9-d878-4eae-b805-2100bb45532c"
        },
        "data_callback": {
          "calculation_id": "67289a9f981b0c23f87914ad",
          "calculation_name": "Công thức 1",
          "session_id": 20240701066837
        }
    },
    "report_callback": {
        "callback_type": "queue",
        "queue_config": {
          "target": "calculationattribute-report-calculation",
          "key": "f9855ab9-d878-4eae-b805-2100bb45532c"
        },
        "data_callback": {
          "calculation_id": "67289a9f981b0c23f87914ad",
          "calculation_name": "Công thức 1",
          "session_id": 20240701066837
        }
    },
    "action_time": "2024-11-18 16:58:00.123"
}

@apiSuccessExample  {json}  Callback Queue topic report:
{
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "action_time": "2024-07-01 06:59:44.120",
    "type": "REPORT_DETAIL",
    "result": [
        {
          "object_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d688",
          "state": "processed",  # error
          "reason": "",
        }
    ],
    "data_callback": {
        "calculation_id": "67289a9f981b0c23f87914ad",
        "calculation_name": "Công thức 1",
        "session_id": 20240701066837
    }
}
"""
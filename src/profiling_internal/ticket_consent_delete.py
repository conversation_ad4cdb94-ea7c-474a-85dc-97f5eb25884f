************************* Queue Ticket consent delete *********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} profiling-ticket-consent-delete [QUEUE] Xóa profile từ luồng Ticket Consent
@apiDescription Xóa profile từ luồng Ticket Consent
@apiGroup TicketConsent
@apiVersion 1.0.0
@apiName TicketConsentDelete

@apiParam      (Input:)     {Object}   data_ticket     Thông tin Ticket
@apiParam      (Input:)     {String}   data_ticket.ticket_id     Ticket ID
@apiParam      (Input:)     {String}   data_ticket.ticket_name   Ticket Name
@apiParam      (Input:)     {String}   data_ticket.ticket_owner  Ticket Owner
@apiParam      (Input:)     {String}   data_ticket.merchant_id   Merchant ID
@apiParam      (Input:)     {String}   data_ticket.request_time  Thời gian request thực tế từ Customer
@apiParam      (Input:)     {String}   data_ticket.ticket_created_time Thời gian tạo ticket trên module Ticket
@apiParam      (Input:)     {Array}    profile_ids  Danh sách profile_id <code>limit: 10</code>


@apiParam      (Callback:)     {Object}   data_ticket Thông tin Ticket
@apiParam      (Callback:)     {Array}    data_profiles Danh sách profile
@apiParam      (Callback:)     {String}   data_profiles.profile_id  Profile ID
@apiParam      (Callback:)     {String}   data_profiles.name  Tên Profile trước khi xóa
@apiParam      (Callback:)     {String}   data_profiles.status  <code>SUCCESS</code>: Xóa thành công; <code>ERROR</code>: Xóa không thành công;
@apiParam      (Callback:)     {String}   data_profiles.reason  Lý do lỗi, Tồn tại khi data_profiles.status: <code>ERROR</code>
@apiParam      (Callback:)     {String}   data_profiles.reason_code  <code>01</code>: Không tìm thấy dữ liệu profile; <code>02</code> Profile đã từng bị xóa; <code>03</code>: Ticket Owner không có quyền xóa profile;

@apiParamExample [json] Input example:
{
    "data_ticket": {
        "ticket_id": "fa10d9c2-027d-47c8-9d32-814809c90f3c",
        "ticket_name": "Ticket Consent Delete",
        "ticket_owner": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        "merchant_id": "fa10d9c2-027d-47c8-9d32-814809c90f3d",
        "request_time": "2023-08-23T02:16:00.000Z",
        "ticket_created_time": "2023-08-23T02:16:00.000Z"
    },
    "profile_ids": [
        "fa10d9c2-027d-47c8-9d32-814809c90f3a", 
        "fa10d9c2-027d-47c8-9d32-814809c90f3b",
        "fa10d9c2-027d-47c8-9d32-814809c90f3e",
        "fa10d9c2-027d-47c8-9d32-814809c90f3f"
    ]
}

@apiSuccessExample  {json}  Callback Queue: ticket-profiling-consent-delete-callback
{
    "data_ticket": {
        "ticket_id": "fa10d9c2-027d-47c8-9d32-814809c90f3c",
        "ticket_name": "Ticket Consent Delete",
        "merchant_id": "fa10d9c2-027d-47c8-9d32-814809c90f3d",
        "request_time": "2023-08-23T02:16:00.000Z",
        "ticket_created_time": "2023-08-23T02:16:00.000Z"
    },
    "data_profiles": [
        {
            "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
            "name": "Profile A",
            "status": "SUCCESS"
        },
        {
            "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3b",
            "name": "Profile B",
            "status": "ERROR",
            "reason": "Không tìm thấy dữ liệu profile",
            "reason_code": "01"
        },
        {
            "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3e",
            "name": "Profile C",
            "status": "ERROR",
            "reason": "Profile đã từng bị xóa",
            "reason_code": "02"
        },
        {
            "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3f",
            "name": "Profile D",
            "status": "ERROR",
            "reason": "Ticket Owner không có quyền xóa profile",
            "reason_code": "03"
        ]
    ]
}
"""

*************************** Estimate Customer *********************************
* version: 1.0.0                                                              *
* version: 1.0.1                                                              *
*******************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/merchants/<merchant_id>/customers/actions/estimate [Done] Estimate số lượng khách hàng
@apiDescription Dịch vụ estimate số lượng khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.0
@apiName CustomerEstimate

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.
@apiParam      (Body:)     {Array}     [profile_group]        Danh sách id profile group cần lấy.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "profile_group": ['9b4c9373-f7ec-4d93-93e0-ac5497f4cec1', '8c9f0262-4a67-4042-96fe-520b8aa1d806'],
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Number}                                  estimate              Số lượng customer estimate.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
  "estimate": 5000
}
"""

"""
@api {post} [HOST]/profiling/v2.0/merchants/<merchant_id>/customers/actions/estimate [Done] Estimate số lượng khách hàng
@apiDescription Dịch vụ estimate số lượng khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.0
@apiName CustomerEstimate

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Number}                                  estimate              Số lượng customer estimate.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
  "estimate": 5000
}
"""


************************************ GET PROFILE ********************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/systems/<merchant_id>/profiles Lấy danh sách khách hàng(Truy vấn mongo).
@apiDescription API lấy danh sách khách hàng theo field
@apiGroup Customers
@apiVersion 1.0.0
@apiName GetProfileMongo
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Param:) {String} merchant_id                  ID merchant

@apiParam      (Body:)     {String}    field_key         Field cần truy v
Allowed values: <br/>
<li><code>cif</code>
<li><code>customer_id</code>
<li><code>email</code>
<li><code>phone_number</code>
<li><code>social_id</code>
<li><code>device_id</code>
@apiParam      (Body:)     {Array}     field_values      Giá trị tìm kiếm

@apiParam      (Body:)     {Array}     fields            Danh sách các field cần lấy.

@apiParamExample    {json}    Body example:
{
    "field_key": "cif",
    "field_values": ["123", "456"],
    "fields": ["name", "phone_number", "email", "cif"]
}

@apiSuccess       {Array}                                  data              Mảng danh sách thông tin user

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh.
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "birthday": "1981-07-28",
      "created_time": "2018-07-27T09:53:21Z",
      "name": "Vũ Thị thọ 123",
      "email": "['<EMAIL>', '<EMAIL>']",
      "gender": 3,
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "phone_number": ["+841215150001"],
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "updated_time": "2018-07-28T04:57:35Z",
      "source_type": 1,
      "source_id": "435ecfeb-e229-4076-806f-982580475e88"
    },
    ...
  ]
}

"""


**************************** CHECK PROFILE IN FILTER ****************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/customers/actions/check_in_filter Kiểm tra profile có thuộc bộ lọc?
@apiDescription Kiểm tra Profile có thuộc bộ lọc(Use Mongo)
@apiGroup Customers
@apiVersion 1.0.0
@apiName CheckProfileInFilter
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam      (Body:)     {String}    profile_id       Profile ID
@apiParam      (Body:)     {Array}     profile_filter    Danh sách bộ lọc
@apiParam      (Body:)     {Array}     fields            Danh sách các field cần lấy.

@apiParamExample    {json}    Body example:
{
    "profile_id": "228e9738-325c-41d0-802f-93a79309ea2f",
    "profile_filter": [
        {
            "criteria_key": "cri_is_non_profile",
            "operator_key": "op_is_in",
            "values": [
                true, false
            ]
        },
        {
            "criteria_key": "cri_last_social_sentiment",
            "operator_key": "op_is_in",
            "values": [
                "Positive"
            ]
        }
    ],
    "fields": ["merchant_id", "profile_id", "name", "phone_number", "email", "cif"]
}

@apiSuccess  {Array}     data              Mảng danh sách thông tin user

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh.
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "is_exist": true,
    "data": [
        {
            "address": "Tỉnh Thanh Hóa",
            "birthday": "1981-07-28",
            "created_time": "2018-07-27T09:53:21Z",
            "name": "Vũ Thị thọ 123",
            "email": "['<EMAIL>', '<EMAIL>']",
            "gender": 3,
            "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
            "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
            "phone_number": ["+841215150001"],
            "social_user": [
                {
                  "id_social": "2139374173003427",
                  "social_type": 1
                }
            ],
            "updated_time": "2018-07-28T04:57:35Z",
            "source_type": 1,
            "source_id": "435ecfeb-e229-4076-806f-982580475e88"
        }
    ]
}
"""

******************************* Get list profile by filter ****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {POST} [HOST]/profiling/internal/v3.0/profile_filter/register [API] Get profile by filter
@apiDescription Get profile by filter callback queue
@apiGroup Customers
@apiVersion 1.0.0
@apiName GetProfileByFilter

@apiParam      (Body:)     {Array}    merchant_ids      Merchant ID cần lấy.
@apiParam      (Body:)     {Array}    profile_filter       Danh sách điều kiện filter.
@apiParam      (Body:)     {Array}    fields      Danh sách field cần lấy
@apiParam      (Body:)     {Int}      [per_page]    Số profile trả về callback, <code>default: 10</code>
@apiParam      (Body:)     {Int}      [personalized]   1: Lấy field format cá nhân hóa; 0: Trả về field gốc; <code>default: 0</code>
@apiParam      (Body:)     {Object}   callback  cấu hình callback
@apiParam      (Body:)     {String}   callback.queue_name  Tên queue callback
@apiParam      (Body:)     {String}   [callback.queue_key]  queue_key callback, <code>default: random</code>
@apiParam      (Body:)     {Object}   [callback.data]  data trả về qua callback
@apiParam      (Body:)     {String}   [source]  Nguồn đăng kí; vd: <code>EVENT; FACTORY</code>

@apiUse critetia_key_body
@apiUse operator_key_body

@apiParamExample [json] Body example:
{
    "merchant_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
    "profile_filter": [
        {
            "criteria_key": "cri_email",
            "operator_key": "op_is_not_empty",
            "values": [
                "op_is_not_empty"
            ]
        },
        {
            "criteria_key": "cri_birthday_period",
            "operator_key": "op_is_between",
            "values": [
                "01-01",
                "01-01"
            ]
        }
    ],
    "fields": ["name", "email", "phone_number"],
    "per_page": 10, // default 10
    "personalized": 1, // Cho phep lay truong ca nhan hoa
    "callback": {
    	"queue_name": "jb-queue-callback",
    	"queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a", // Nếu không truyền hoặc giá trị null thì callback key random
    	"data": {
    		"field_1": "123",
    		"field_2": "321"
    	}
    },
    "audience_group": "GROUP_1",
    "source": "ADS"
}

@apiSuccessExample  {json}  Callback Queue:
{
    "code": 200,
    "data": [
        {
          "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
          "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
          "updated_time": "2018-07-28T04:57:35Z",
          "name": "Đoàn Bắc",
          ...........
        },
        ...
    ],
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    },
    "last_page": true // false
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công"
}
"""

**************************** CHECK PROFILE IN FILTER ****************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/customers/actions/profiles_in_filter Kiểm tra list profile_id có thuộc bộ lọc?
@apiDescription Kiểm tra List Profile có thuộc bộ lọc(Use Mongo)
@apiGroup Customers
@apiVersion 1.0.0
@apiName ProfilesInFilter
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam      (Body:)     {Array}    profile_ids        Danh sách profile_id; <code>Limit 10 profile_id</code>
@apiParam      (Body:)     {Array}     profile_filter    Danh sách bộ lọc
@apiParam      (Body:)     {Array}     fields            Danh sách các field cần lấy.

@apiParamExample    {json}    Body example:
{
    "profile_ids": ["228e9738-325c-41d0-802f-93a79309ea2f"],
    "profile_filter": [
        {
            "criteria_key": "cri_is_non_profile",
            "operator_key": "op_is_in",
            "values": [
                true, false
            ]
        },
        {
            "criteria_key": "cri_last_social_sentiment",
            "operator_key": "op_is_in",
            "values": [
                "Positive"
            ]
        }
    ],
    "fields": ["merchant_id", "profile_id", "name", "phone_number", "email", "cif"]
}

@apiSuccess  {Array}     data              Mảng danh sách thông tin user thỏa mãn bộ lọc

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh.
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "address": "Tỉnh Thanh Hóa",
            "birthday": "1981-07-28",
            "created_time": "2018-07-27T09:53:21Z",
            "name": "Vũ Thị thọ 123",
            "email": "['<EMAIL>', '<EMAIL>']",
            "gender": 3,
            "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
            "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
            "phone_number": ["+841215150001"],
            "social_user": [
                {
                  "id_social": "2139374173003427",
                  "social_type": 1
                }
            ],
            "updated_time": "2018-07-28T04:57:35Z",
            "source_type": 1,
            "source_id": "435ecfeb-e229-4076-806f-982580475e88"
        }
    ]
}
"""
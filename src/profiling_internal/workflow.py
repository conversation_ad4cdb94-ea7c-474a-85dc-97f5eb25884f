********************** Queue WF validate filter ***************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} profiling-wf-validate-filter [QUEUE] WF Validate filter
@apiDescription Validate filter
@apiGroup Workflow
@apiVersion 1.0.0
@apiName WFValidateFilter

@apiParam      (Input:)     {Array}    fields      Danh sách field cần lấy
@apiParam      (Input:)     {Int}      [personalized]   1: Lấy field format cá nhân hóa; 0: Trả về field gốc; <code>default: null</code>; Nếu giá trị là <code>null</code> thì không trả về thông tin profile
@apiParam      (Input:)     {Object}   callback  cấu hình callback
@apiParam      (Input:)     {String}   callback.queue_name  Tên queue callback
@apiParam      (Input:)     {String}   [callback.queue_key]  queue_key callback, <code>default: profile_id</code>
@apiParam      (Input:)     {Object}   [callback.data]  data trả về qua callback
@apiParam      (Input:)     {Object}   audiences_filter  Thông tin bộ lọc
@apiParam      (Input:)     {String}   source      Nguồn bên cần lấy dữ liệu: example: WORKFLOW
@apiParam      (Input:)     {Object}   setting     Setting config queue
@apiParam      (Input:)     {Int}      version Version của bộ lọc; <code>default: 1</code>; <code>2: Support bộ lọc phức hợp</code>

@apiParamExample [json] Input example:
{
    "merchant_id": "",
    "profile_id": "",
    "fields": ["name", "email", "phone_number"],
    "personalized": 1, // Cho phep lay truong ca nhan hoa
    "audiences_filter": [
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 0,
            "operator": null
        },
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 1,
            "operator": "and"
        },
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 2,
            "operator": "exclude"
        }
    ],
    "callback": {
    	"queue_name": "wf-queue-callback",
    	"queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a", // Nếu không truyền hoặc giá trị null thì callback key profile_id
    	"data": {
    		"field_1": "123",
    		"field_2": "321"
    	}
    },
    "source": "EVENT",
    "setting": {
        "setting_field_1": 1
    },
    "version": 2
}

@apiSuccessExample  {json}  Callback Queue:
{
    "code": 200,
    "status": True,
    "data_input": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": ""
    },
    "data": {
        "address": "Tỉnh Thanh Hóa",
        "birthday": "1981-07-28",
        "created_time": "2018-07-27T09:53:21Z",
        "name": "Vũ Thị thọ 123",
        "email": "['<EMAIL>', '<EMAIL>']",
        "gender": 3,
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": ["618261e0-dee6-4440-a744-89f67235b347"],
        "phone_number": ["+841215150001"],
        "social_user": [
            {
              "id_social": "2139374173003427",
              "social_type": 1
            }
        ],
        "updated_time": "2018-07-28T04:57:35Z",
        "source_type": 1,
        "source_id": "435ecfeb-e229-4076-806f-982580475e88",
        "is_non_profile": false
    },
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}

@apiSuccessExample  {json}  Callback Queue 404:
{
    "code": 404,
    "status": False,
    "data_input": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": "618261e0-dee6-4440-a744-89f67235b347"
    },
    "reason": "profile is not exist!",
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}
"""

******************************* Get list profile by filter ****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {Post} [HOST]/profiling/internal/v3.0/workflow/profile_filter/register [API] Get profile by filter
@apiDescription Get profile by filter callback queue
@apiGroup Workflow
@apiVersion 1.0.0
@apiName WFGetProfileByFilter

@apiParam      (Body:)     {Array}    audiences_filter       Danh sách nhóm audience
@apiParam      (Body:)     {Array}    audiences_filter.profile_filter       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Body:)     {Array}    fields      Danh sách field cần lấy
@apiParam      (Body:)     {Int}      [per_page]    Số profile trả về callback, <code>default: 10</code>
@apiParam      (Body:)     {Int}      [personalized]   1: Lấy field format cá nhân hóa; 0: Trả về field gốc; <code>default: 0</code>
@apiParam      (Body:)     {Object}   callback  cấu hình callback
@apiParam      (Body:)     {String}   callback.queue_name  Tên queue callback
@apiParam      (Body:)     {String}   [callback.queue_key]  queue_key callback, <code>default: random</code>
@apiParam      (Body:)     {Object}   [callback.data]  data trả về qua callback
@apiParam      (Body:)     {String}   workflow_id  Workflow ID
@apiParam      (Body:)     {String}   wf_start_time  Thời gian start workflow; <code>VD: 2018-07-27T09:53:21.000Z</code>
@apiParam      (Body:)     {Int}      version Version của bộ lọc; <code>default: 1</code>; <code>2: Support bộ lọc phức hợp</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample [json] Body example:
{
    "audiences_filter": [
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 0,
            "operator": null
        },
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 1,
            "operator": "and"
        },
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 2,
            "operator": "exclude"
        }
    ],
    "fields": ["name", "email", "phone_number"],
    "per_page": 10, // default 10
    "personalized": 1, // Cho phep lay truong ca nhan hoa
    "callback": {
    	"queue_name": "wf-queue-callback",
    	"queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a", // Nếu không truyền hoặc giá trị null thì callback key random
    	"data": {
    		"field_1": "123",
    		"field_2": "321"
    	}
    },
    "workflow_id": "7db1010b-b666-4573-ad04-43a21da66720",
    "wf_start_time": "2018-07-27T09:53:21.000Z",
    "version": 2
}

@apiSuccessExample  {json}  Callback Queue:
{
    "code": 200,
    "data": [
        {
          "address": "Tỉnh Thanh Hóa",
          "birthday": "1981-07-28",
          "created_time": "2018-07-27T09:53:21Z",
          "name": "Vũ Thị thọ 123",
          "email": "['<EMAIL>', '<EMAIL>']",
          "gender": 3,
          "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
          "merchant_id": ["618261e0-dee6-4440-a744-89f67235b347"],
          "phone_number": ["+841215150001"],
          "social_user": [
            {
              "id_social": "2139374173003427",
              "social_type": 1
            }
          ],
          "updated_time": "2018-07-28T04:57:35Z",
          "source_type": 1,
          "source_id": "435ecfeb-e229-4076-806f-982580475e88",
          "is_non_profile": false
        },
        ...
    ],
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    },
    "last_page": true,
    "total_profile": 1000
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "total_count": 5000
}
"""

******************************* Get check profile by filter ***************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} profiling-wf-check-profile-filter [QUEUE] Check profile by filter
@apiDescription Check profile by filter callback queue
@apiGroup Workflow
@apiVersion 1.0.0
@apiName WFCheckProfileByFilter

@apiParam      (Input:)     {String}   merchant_id       Merchant ID
@apiParam      (Input:)     {Array}    audiences_filter       Danh sách nhóm audience
@apiParam      (Input:)     {Array}    audiences_filter.profile_filter       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Input:)     {Array}    fields      Danh sách field cần lấy
@apiParam      (Input:)     {Int}      [per_page]    Số profile trả về callback, <code>default: 10</code>
@apiParam      (Input:)     {Int}      [personalized]   1: Lấy field format cá nhân hóa; 0: Trả về field gốc; <code>default: 0</code>
@apiParam      (Input:)     {Object}   callback  cấu hình callback
@apiParam      (Input:)     {String}   callback.callback_type <code>VALIDATE_FILTER; LIST_PROFILE<code>
@apiParam      (Input:)     {String}   callback.queue_name  Tên queue callback
@apiParam      (Input:)     {String}   [callback.queue_key]  queue_key callback, <code>default: random</code>
@apiParam      (Input:)     {Object}   [callback.data]  data trả về qua callback
@apiParam      (Input:)     {String}   workflow_id  Workflow ID
@apiParam      (Input:)     {String}   wf_start_time  Thời gian start workflow; <code>VD: 2018-07-27T09:53:21.000Z</code>
@apiParam      (Input:)     {Int}      version Version của bộ lọc; <code>default: 1</code>; <code>2: Support bộ lọc phức hợp</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample Input example:
{
    "merchant_id": "6a2e3424-5841-421f-b480-f36b29604063",
    "audiences_filter": [
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 0,
            "operator": null
        },
        // Cần WF build thêm, dưới đây là bộ lọc profile_owner
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_profile_owner",
                    "operator_key": "op_is_in",
                    "values": [
                        "6a2e3424-5841-421f-b480-f36b29604063" // Đây là user_id
                    ]
                }
            ],
            "position": 1,
            "operator": "and"
        }
    ],
    "fields": ["name", "email", "phone_number"],
    "per_page": 10, // default 10
    "personalized": 1, // Cho phep lay truong ca nhan hoa
    "callback": {
        "callback_type": "CHECK_VALIDATE",
    	"queue_name": "wf-queue-callback",
    	"queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a", // Nếu không truyền hoặc giá trị null thì callback key random
    	"data": {
    		"field_1": "123",
    		"field_2": "321"
    	}
    },
    "workflow_id": "7db1010b-b666-4573-ad04-43a21da66720",
    "wf_start_time": "2018-07-27T09:53:21.000Z",
    "version": 2
}

@apiSuccessExample Callback Queue LIST_PROFILE:
{
    "code": 200,
    "data": [
        {
          "address": "Tỉnh Thanh Hóa",
          "birthday": "1981-07-28",
          "created_time": "2018-07-27T09:53:21Z",
          "name": "Vũ Thị thọ 123",
          "email": "['<EMAIL>', '<EMAIL>']",
          "gender": 3,
          "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
          "merchant_id": ["618261e0-dee6-4440-a744-89f67235b347"],
          "phone_number": ["+841215150001"],
          "social_user": [
            {
              "id_social": "2139374173003427",
              "social_type": 1
            }
          ],
          "updated_time": "2018-07-28T04:57:35Z",
          "source_type": 1,
          "source_id": "435ecfeb-e229-4076-806f-982580475e88",
          "is_non_profile": false
        },
        ...
    ],
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    },
    "last_page": true,
    "total_profile": 1000
}

@apiSuccessExample Callback Queue CHECK_VALIDATE:
{
    "code": 200,
    "status": true, // false
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}
"""

********************** Queue WF validate filter with conditions ***********************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} profiling-wf-validate-filter-with-conditions [QUEUE] WF Validate filter with conditions
@apiDescription Validate filter with conditions
@apiGroup Workflow
@apiVersion 1.0.0
@apiName WFValidateFilterWithConditions

@apiParam      (Input:)     {Array}    fields      Danh sách field cần lấy
@apiParam      (Input:)     {Int}      [personalized]   1: Lấy field format cá nhân hóa; 0: Trả về field gốc; <code>default: null</code>; Nếu giá trị là <code>null</code> thì không trả về thông tin profile
@apiParam      (Input:)     {Object}   callback  cấu hình callback
@apiParam      (Input:)     {String}   callback.queue_name  Tên queue callback
@apiParam      (Input:)     {String}   [callback.queue_key]  queue_key callback, <code>default: profile_id</code>
@apiParam      (Input:)     {Object}   [callback.data]  data trả về qua callback
@apiParam      (Input:)     {Object}   audiences_filter  Thông tin bộ lọc
@apiParam      (Input:)     {String}   source      Nguồn bên cần lấy dữ liệu: example: WORKFLOW
@apiParam      (Input:)     {Int}      version Version của bộ lọc; <code>default: 1</code>; <code>2: Support bộ lọc phức hợp</code>
@apiParam      (Input:)     {Object}   audiences_filter  Thông tin bộ lọc
@apiParam      (Input:)     {Object}   conditions  cấu hình conditions
@apiParam      (Input:)     {String}   conditions.email  điều kiện email

@apiParamExample [json] Input example:
{
    "merchant_id": "",
    "workflow_id": "",
    "fields": ["name", "email", "phone_number"],
    "conditions": {
        "email": "<EMAIL>"
    },
    "personalized": 1, // Cho phep lay truong ca nhan hoa
    "audiences_filter": [
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 0,
            "operator": null
        },
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 1,
            "operator": "and"
        },
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 2,
            "operator": "exclude"
        }
    ],
    "callback": {
    	"queue_name": "wf-queue-callback",
    	"queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a", // Nếu không truyền hoặc giá trị null thì callback key profile_id
    	"data": {
    		"field_1": "123",
    		"field_2": "321"
    	}
    },
    "source": "WORKFLOW",
    "version": 2
}

@apiSuccessExample  {json}  Callback Queue:
{
    "code": 200,
    "status": True,
    "data_input": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": ""
    },
    "data": {
        "address": "Tỉnh Thanh Hóa",
        "birthday": "1981-07-28",
        "created_time": "2018-07-27T09:53:21Z",
        "name": "Vũ Thị thọ 123",
        "email": "['<EMAIL>', '<EMAIL>']",
        "gender": 3,
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": ["618261e0-dee6-4440-a744-89f67235b347"],
        "phone_number": ["+841215150001"],
        "social_user": [
            {
              "id_social": "2139374173003427",
              "social_type": 1
            }
        ],
        "updated_time": "2018-07-28T04:57:35Z",
        "source_type": 1,
        "source_id": "435ecfeb-e229-4076-806f-982580475e88",
        "is_non_profile": false
    },
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}

@apiSuccessExample  {json}  Callback Queue 404:
{
    "code": 404,
    "status": False,
    "data_input": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": "618261e0-dee6-4440-a744-89f67235b347"
    },
    "reason": "profile is not exist!",
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}
"""
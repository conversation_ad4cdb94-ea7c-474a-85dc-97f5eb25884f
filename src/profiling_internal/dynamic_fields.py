********************************* Merchant List Field **********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {get} [HOST]/profiling/internal/v3.0/merchant/field/list Merchant List Fields.
@apiDescription API lấy danh sách fields của Merchant
@apiGroup Merchant fields
@apiVersion 1.0.0
@apiName  MerchantListField
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse order_sort
@apiParam   (Query:)    {String}  [search]     Tìm kiếm fields theo tên.

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "group":"information",
      "fields":[]
    },
    {
      "group":"demographic",
      "fields":[]
    },
    {
      "group":"activity",
      "fields":[]
    },
    {
      "group":"loyalty",
      "fields":[]
    },
    {
      "group":"other",
      "fields":[]
    },
    {
      "group":"dynamic",
      "fields":[
        {
          "field_name": "field_1",
          "field_key": "_dyn_name_1558406233387",
          "field_property": 1,
          "display_type": "radio",
          "display_in_form": true,
          "order": 15,
          "group": "dynamic",
          "display_in_form_input": false,
          "disable_remove_form_input": false,
          "display_in_dashboard": false,
          "dashboard_order": 1,
          "disable_remove_dashboard": false,
          "required": false,
          "data_selected": [
            {
              "id": 1,
              "name": "data 1",
              "display_in_form": true
            },
            {
              "id": 1,
              "name": "data 2",
              "display_in_form": false
            }
          ],
          "is_base": false,
          "status": 1,
          "description": "mo ta ngan cua field",
          "created_time": "2019-03-27T17:52:46Z",
          "updated_time": "2019-03-27T17:52:46Z",
          "history": [
            {
              "created_time": "2019-03-27T17:52:46Z",
              "staff_id": "7df46d3b-98bd-4bf6-a8ac-1b7210297e54",
              "fullname": "MobioTest",
              "username": "admin@mobiotest"
            },
            {
              "created_time": "2019-03-28T17:52:46Z",
              "staff_id": "7df46d3b-98bd-4bf6-a8ac-1b7210297e54",
              "fullname": "MobioTest",
              "username": "admin@mobiotest"
            }
          ]
        }
      ]
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""


********************************* Child Add Field *************************************
* version: 1.0.0                                                                      *                                                                 *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/child/add/field Merchant Add New Field.
@apiDescription API hỗ trợ Merchant tạo thêm mới field ở product hold
@apiGroup Merchant fields
@apiVersion 1.0.0
@apiName  ChildAddField
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String}         field_name        Tên field mới
@apiParam   (Body:)    {String}         field_key        Tên field key
@apiParam   (Body:)     {Number=1:Int,2:String,3:DateTime}    field_property            Kiểu dữ liệu của field.
@apiParam   (Body:)     {String}    display_type            Kiểu hiển thị dữ liệu.
<li><code>single_line: Single Line</code></li>
<li><code>multi_line: Multi Line</code></li>
<li><code>dropdown_single_line: Dropdown Single Line</code></li>
<li><code>dropdown_multi_line: Dropdown Multi Line</code></li>
<li><code>radio: Radio</code></li>
<li><code>checkbox: Checkbox</code></li>
<li><code>date_picker: Date Picker</code></li>
@apiParam   (Body:)     {Array}    [data_selected]         Dữ liệu mẫu của field. Dữ liệu được thêm vào field phải nằm trong data_selected mới đươc đưa vào field. Dữ liệu này là bắt buộc với các kiểu hiển thị: dropdown_single_line, dropdown_multi_line, radio, checkbox.
@apiParam   (Body:)     {String='dd/mm','dd/mm/yyyy','dd/mm/yyyy hh:mm','dd-mm-yyyy','dd-mm-yyyy hh:mm','mm/dd/yyyy','mm/dd/yyyy hh:mm'}    [format]                 Data format. Dùng khi kiểu dữ liệu là datetime


@apiParamExample [json] Body example text single line:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "single_line"
}

@apiParamExample [json] Body example text multi line:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "multi_line"
}

@apiParamExample [json] Body example dropdown single line:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "dropdown_single_line",
  "data_selected": [
    {
      "id": 1,
      "value": "data 1",
      "enable": true
    },
    {
      "id": 2,
      "value": "data 2",
      "enable": false
    }
  ]
}

@apiParamExample [json] Body example dropdown multi line:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "dropdown_multi_line",
  "data_selected": [
    {
      "id": 1,
      "value": "data 1",
      "enable": true
    },
    {
      "id": 2,
      "value": "data 2",
      "enable": false
    }
  ]
}

@apiParamExample [json] Body example radio:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "radio",
  "data_selected": [
    {
      "id": 1,
      "value": "data 1",
      "enable": true
    },
    {
      "id": 2,
      "value": "data 2",
      "enable": false
    }
  ]
}

@apiParamExample [json] Body example checkbox:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "checkbox",
  "data_selected": [
    {
      "id": 1,
      "value": "data 1",
      "enable": true
    },
    {
      "id": 2,
      "value": "data 2",
      "enable": false
    }
  ]
}

@apiParamExample [json] Body example date_picker:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "date_picker",
  "format": "dd/mm/yyyy",
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "mapping_els": true,
        "register_audience": true
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

************************************ Assign Many Company To Profile ***********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/internal/v3.0/assign/<profile_id>/company Assign Many Company To Profile.
@apiDescription Assign Many Company To Profile
@apiGroup User
@apiVersion 1.0.0
@apiName  AssignManyCompanyToProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)    {related_to_company_v2}       related_to_company_v2        Mỗi liên hệ với công ty.
@apiParam   (related_to_company_v2:)    {job_title}        [job_title]       Chức Danh.
@apiParam   (related_to_company_v2:)    {job_title_group}       [job_title_group]        Nhóm chức danh.
@apiParam   (related_to_company_v2:)    {relationship}       [relationship]        Mối liên hệ.
@apiParam   (related_to_company_v2:)    {company_name}       [company_name]        Email phụ của Profile.
@apiParam   (related_to_company_v2:)    {action}       [action]        delete để gỡ công ty khỏi profile. nếu gán vào profile thì không  
@apiParam   (related_to_company_v2:)    {company_id}       [company_id]        Company id.


@apiParamExample Body example:
{
    "related_to_company_v2": [
        {
            "job_title": "JOBT1",
            "job_title_group": "C_LEVEL",
            "relationship": "OTHER",
            "company_name": "name1",
            "action": "delete",
            "company_id": "company_id"
        }
    ]
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "message": "request thành công.",
  "data": {
    "name": "Nguyen Van A",
    "profile_id": "aa571f87-1f73-4504-895d-417d6c417747",
    "merchant_id": ["d3b3aea1-0daf-4a48-8609-279497dc96e9"],
    "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      },
      "related_to_company_v2": [
        {
            "job_title": "JOBT1",
            "job_title_group": "C_LEVEL",
            "relationship": "OTHER",
            "company_name": "name1",
            "company_id": "company_id2"
        }
    ]
  }
}
"""

************************************ Assign Many Profile To Company ***********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/internal/v3.0/assign/<company_id>/profile Assign Many Profile To Company.
@apiDescription Assign Many Profile To Company
@apiGroup User
@apiVersion 1.0.0
@apiName  AssignManyProfileToCompany
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)    {data}       data        Mỗi liên hệ với công ty.
@apiParam   (data:)    {job_title}        [job_title]       Chức Danh.
@apiParam   (data:)    {job_title_group}       [job_title_group]        Nhóm chức danh.
@apiParam   (data:)    {relationship}       [relationship]        Mối liên hệ.
@apiParam   (data:)    {company_name}       [company_name]        Email phụ của Profile.
@apiParam   (data:)    {profile_id}       [profile_id]        profile_id.


@apiParamExample Body example:
{
    "data": [
        {
            "job_title": "JOBT1",
            "job_title_group": "C_LEVEL",
            "type_relationship": "OTHER",
            "company_name": "name1",
            "profile_id": "89da0ba3-f5a9-4a93-8528-1e17ff587cf6"
        },
        {
            "job_title": "JOBT1",
            "job_title_group": "C_LEVEL",
            "type_relationship": "OTHER",
            "company_name": "name1",
            "profile_id": "89da0ba3-f5a9-4a93-8528-1e17ff587cf6"
        }
    ]
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công.",
    "upsert_successful": true
}
"""

# ******************************** List types of identification ***********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /profiling/internal/v3.0/merchant/profile_identify/list_type List types of identification
@apiDescription Api List types of identification
@apiGroup Merchant Config
@apiVersion 1.0.0
@apiName  ListTypesOfIdentification
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "code": "citizen_identity",
            "multiple_value": true,
            "name": "CCCD",
            "translate_key": "i18n_citizen_identity"
        },
        {
            "code": "identity_card",
            "multiple_value": true,
            "name": "CMND",
            "translate_key": "i18n_identity_card"
        },
        {
            "code": "passport",
            "multiple_value": true,
            "name": "Hộ chiếu",
            "translate_key": "i18n_passport"
        },
        {
            "code": "driving_license",
            "multiple_value": true,
            "name": "Giấy phép lái xe",
            "translate_key": "i18n_driving_license"
        },
        {
            "code": "identity_card_army",
            "multiple_value": true,
            "name": "Chứng minh nhân dân quân đội",
            "translate_key": "i18n_identity_card_army"
        },
        {
            "code": "birth_certificate",
            "multiple_value": true,
            "name": "Giấy khai sinh",
            "translate_key": "i18n_birth_certificate"
        },
        {
            "code": "visa",
            "multiple_value": true,
            "name": "VISA",
            "translate_key": "i18n_VISA"
        },
        {
            "code": "temporary_residence_card",
            "multiple_value": true,
            "name": "Thẻ tạm trú",
            "translate_key": "i18n_temporary_residence_card"
        },
        {
            "code": "other",
            "multiple_value": true,
            "name": "Khác",
            "translate_key": "i18n_OTHER"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
# ******************************** List types of Address ***********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /profiling/internal/v3.0/merchant/address/list_address_type List types of Address
@apiDescription Api List types of address
@apiGroup Merchant Config
@apiVersion 1.0.0
@apiName  ListTypesOfAddress
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "code": "contact_address",
            "multiple_value": false,
            "name": "Địa chỉ liên hệ",
            "translate_key": "i18n_contact_address"
        },
        {
            "code": "permanent_address",
            "multiple_value": true,
            "name": "Địa chỉ thường trú",
            "translate_key": "i18n_permanent_address"
        },
        {
            "code": "temporary_address",
            "multiple_value": true,
            "name": "Địa chỉ tạm trú",
            "translate_key": "i18n_temporary_address"
        },
        {
            "code": "company_address",
            "multiple_value": true,
            "name": "Địa chỉ công ty",
            "translate_key": "i18n_company_address"
        },
        {
            "code": "other_address",
            "multiple_value": true,
            "name": "Địa chỉ khác",
            "translate_key": "i18n_address_other"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


********************************* Merchant List Field without group *******************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {get} [HOST]/profiling/internal/v3.0/merchant/field/list-without-group Merchant List Fields without group.
@apiDescription API lấy danh sách fields của Merchant; <code>internal_host: http://profiling-v4-app-internal-api-service.mobio</code>
@apiGroup Merchant fields
@apiVersion 1.0.0
@apiName  MerchantListFieldWithoutGroup
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Param:)    {String} [field_keys]    Danh sách field_key; Cách nhau bởi dấu <code>,</code>. Nếu không truyền thì mặc định lấy hết. VD: <code>&field_keys=name,birthday</code>
@apiParam   (Param:)    {String} [attributes]    Thuộc tính của field; Cách nhau bởi dấu <code>,</code>. Nếu không truyền thì mặc định lấy hết. VD: <code>&attributes=field_key,field_property,display_type,format</code>
@apiParam   (Param:)    {String} [merchant_ids]  list merchant_id(Support khi chưa tách merchant cho profile); Cách nhau bởi dấu <code>,</code>. Nếu không truyền thì mặc định lấy X-Merchant-ID ở header

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
        "merchant_id": "merchant_id_1",
        "display_type": "date_picker",
        "field_key": "birthday",
        "field_property": 3,
        "format": "dd/mm/yyyy"
    },
    {
        "merchant_id": "merchant_id_2",
        "display_type": "single_line",
        "field_key": "name",
        "field_property": 2,
        "format": null
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""
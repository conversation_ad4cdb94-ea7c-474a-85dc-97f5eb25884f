******************************* Get list profile by filter ****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {POST} [HOST]/profiling/internal/v3.0/merchant/special_day_broadcast/register [API] Register special day broadcast
@apiDescription Get profile by special day callback queue
@apiGroup SpecialDay
@apiVersion 1.0.0
@apiName MerchantSpecialDayBroadcastRegister

@apiParam      (Body:)     {String}    merchant_id      Merchant ID cần lấy.
@apiParam      (Body:)     {String}    trigger_id       Trigger ID.
@apiParam      (Body:)     {String}    journey_id       Journey ID.
@apiParam      (Body:)     {String}    [expired_date]   Ngày kết thúc journey VD: <code>2021-12-31T00:00:00.000Z</code>.
@apiParam      (Body:)     {Array}     [fields]         Danh sách field cần lấy
@apiParam      (Body:)     {Int}       [per_page]       Số profile trả về callback, <code>default: 10</code>
@apiParam      (Body:)     {Array}     profile_filter   Danh sách bộ lọc, trong đó có bộ lọc ngày đặc biệt
@apiParam      (Body:)     {Object}    callback         cấu hình callback
@apiParam      (Body:)     {String}    callback.queue_name  Tên queue callback
@apiParam      (Body:)     {String}    [callback.queue_key]  queue_key callback, <code>default: random</code>
@apiParam      (Body:)     {Object}    [callback.data]  data trả về qua callback
@apiParam      (Body:)     {String}    [source]  Nguồn đăng kí; vd: <code>EVENT; FACTORY</code>

@apiUse operator_key_body

@apiParamExample [json] Body example:
{
    "merchant_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
    "trigger_id": "fa10d9c2-027d-47c8-9d32-814809c90f3b",
    "journey_id": "fa10d9c2-027d-47c8-9d32-814809c90f3c",
    "expired_date": "2021-12-31T00:00:00.000Z",
    "profile_filter":[
        {
            "criteria_key": "cri_special_day_multiple",
            "operator_key": "op_is_multiple",
            "values": [{
              "criteria_key": "cri_special_day_key",
              "operator_key": "op_is_equal",
              "values": ["birthday"]
            },
            {
              "criteria_key": "cri_special_day_time",
              "operator_key": "op_is_equal",
              "values": ["current"] // ["before", 7], ["after", 7]
            }]
        }
    ],
    "fields": ["name", "email", "phone_number"],
    "per_page": 10, // default 10
    "callback": {
    	"queue_name": "key-queue-callback",
    	"queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a", // Nếu không truyền hoặc giá trị null thì callback key random
    	"data": {
    		"field_1": "123",
    		"field_2": "321"
    	}
    }
}

@apiSuccessExample  {json}  Callback Queue:
{
    "code": 200,
    "data": [
        {
          "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
          "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
          "updated_time": "2018-07-28T04:57:35Z",
          "name": "Đoàn Bắc",
          ...........
        },
        ...
    ],
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công"
}
"""
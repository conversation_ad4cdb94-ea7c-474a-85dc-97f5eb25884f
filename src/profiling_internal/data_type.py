*********************************** Upsert data type **********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/merchant/data_types/upsert Upsert data type
@apiDescription Upsert Data Type
@apiGroup Data Type
@apiVersion 1.0.0
@apiName UpsertDataType

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    data_type Ki<PERSON>u dữ liệu
@apiParam       (Body:)     {String}    value     Giá trị

@apiParamExample  {json} Body request example
{
    "data_type": "TRANS_TYPE",
    "value": "PAYMENT"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "61ea5f37a8772cd90c188528",
        "created_time": "2022-01-21T02:25:19.608000Z",
        "data_type": "TRANS_TYPE",
        "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
        "search": "payment",
        "status": 1,
        "updated_time": "2022-01-21T02:31:03.251000Z",
        "value": "PAYMENT"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
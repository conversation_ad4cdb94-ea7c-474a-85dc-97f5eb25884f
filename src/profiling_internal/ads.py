**************************************Ads Upload Excel*********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/ads/upload/excel Ads Upload excel.
@apiDescription Ads upload tập khách hàng
@apiGroup Ads
@apiVersion 1.0.0
@apiName AdsUploadExcel
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}    merchant_id Merchant Id

@apiParam   (Body:)    {File}         file             File Excel cần import.
@apiParam   (Body:)    {Object}       query            json chứa các tham số.
@apiParam   (query:)    {String}       session_id       Id của phiên làm việc
@apiParam   (query:)    {String}       webhook          url nhận thông báo khi tiến trình process hoàn thành.


@apiParamExample query example:
{
  "session_id": "332e0d69-2a73-486b-9775-439c1e33d25e",
  "webhook": "https://example.com/api/ads_webhook"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "message": "request thành công."
}
"""


**************************************Analyze Profile**********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/ads/analyze Analyze Profile.
@apiDescription Phân tích tập khách hàng theo audience_id.
@apiGroup Ads
@apiVersion 1.0.0
@apiName AdsAnalyzeProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    merchant_id Merchant Id

@apiParam   (Body:)    {String}         webhook            url nhận thông báo khi tiến trình process hoàn thành.
@apiParam   (Body:)    {String}       audience_id       Id được lưu trữ trên hệ thống Audience
@apiParam   (Body:)    {String}       session_id       Id của phiên làm việc

@apiParamExample Body example:
{
  "webhook": "https://example.com/api/ads_webhook",
  "audience_id": "992b1bff-df6e-493a-8e98-1ecdada5727a",
  "session_id": "992b1bff-df6e-493a-8e98-1ecdada5727a"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "message": "request thành công."
}
"""


************************************Ads Get List Profile*******************************
* version: 1.0.1                                                                      *
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {get} [HOST]/profiling/internal/v3.0/ads/profiles Get list profile by session_id.
@apiDescription Ads lấy danh sách Profile theo session_id.
@apiGroup Ads
@apiVersion 1.0.0
@apiName AdsGetProfileByAudience
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}    merchant_id Merchant Id
@apiParam   (Query:)    {String}    session_id Session Id
@apiParam   (Query:)    {Number}    [per_page] Số bản ghi trên 1 trang. default 15
@apiParam   (Query:)    {String}  [after_token]     Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)    {String}  [before_token]     Token để request lấy dữ liệu trang trước đó. Nếu trong danh sách tham số gửi lên có <code>after_token</code> thì ưu tiên xử lý <code>after_token</code>.

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi",
  "data": {
    "response_type": 1,
    "profiles": [
      {
        "profile_id": "126a1b55-64af-419c-a549-34d2465e02e7",
        "merchant_id": "4aad837e-9eb0-4015-a0b4-f4a5d12dcb4b",
        "email": "<EMAIL>",
        "phone_number": "0987777777"
        "social_user": [
          {
            "social_id": "1234567",
            "social_type": 1,
            "app_id": "123",
            "page_id": "789"
          }
        ]
      },
      {
        "profile_id": "94305062-5a35-4b16-a1a3-af814a533b1e",
        "merchant_id": "4aad837e-9eb0-4015-a0b4-f4a5d12dcb4b",
        "email": "<EMAIL>",
        "phone_number": "0978888888"
        "social_user": [
          {
            "social_id": "12345678",
            "social_type": 1,
            "app_id": "1234",
            "page_id": "6789"
          }
        ]
      }
    ]
  },
  "paging": {
    "cursors": {
        "after": "WzE1NDY5MjA3MDA3ODJd", 
        "before": ""
    }, 
    "page": 1, 
    "page_count": 623, 
    "per_page": 15, 
    "total_count": 9333
  },
  "message": "request thành công."
}
"""
"""
@api {get} [HOST]/profiling/internal/v3.1/ads/profiles Get list profile by session_id.
@apiDescription Ads lấy danh sách Profile theo session_id.
@apiGroup Ads
@apiVersion 1.0.1
@apiName AdsGetProfileByAudience
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam       (Param:)     {String}    [info_paging]      info_paging=True khi cần thông tin để chia page như total_count, page_count, mặc định False

@apiParam   (Query:)    {String}    merchant_id Merchant Id
@apiParam   (Query:)    {String}    session_id Session Id
@apiParam   (Query:)    {Number}    [per_page] Số bản ghi trên 1 trang. default 15
@apiParam   (Query:)    {String}  [after_token]     Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)    {String}  [before_token]     Token để request lấy dữ liệu trang trước đó. Nếu trong danh sách tham số gửi lên có <code>after_token</code> thì ưu tiên xử lý <code>after_token</code>.

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi",
  "data": {
    "response_type": 1,
    "profiles": [
      {
        "profile_id": "126a1b55-64af-419c-a549-34d2465e02e7",
        "merchant_id": "4aad837e-9eb0-4015-a0b4-f4a5d12dcb4b",
        "email": "<EMAIL>",
        "phone_number": "0987777777"
        "social_user": [
          {
            "social_id": "1234567",
            "social_type": 1,
            "app_id": "123",
            "page_id": "789"
          }
        ]
      },
      {
        "profile_id": "94305062-5a35-4b16-a1a3-af814a533b1e",
        "merchant_id": "4aad837e-9eb0-4015-a0b4-f4a5d12dcb4b",
        "email": "<EMAIL>",
        "phone_number": "0978888888"
        "social_user": [
          {
            "social_id": "12345678",
            "social_type": 1,
            "app_id": "1234",
            "page_id": "6789"
          }
        ]
      }
    ]
  },
  "paging": {
    "cursors": {
        "after": "WzE1NDY5MjA3MDA3ODJd", 
        "before": ""
    }, 
    "page": 1, 
    "page_count": 623, 
    "per_page": 15, 
    "total_count": 9333
  },
  "message": "request thành công."
}
"""

***********************************Ads Get Analyze Result******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {get} [HOST]/profiling/internal/v3.0/ads/analyze Get Analyze Result by session_id.
@apiDescription Ads kết quả phân tích theo session_id.
@apiGroup Ads
@apiVersion 1.0.0
@apiName AdsGetAnalyzeResult
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}    merchant_id Merchant Id
@apiParam   (Query:)    {String}    session_id Session Id

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi",
  "data": {
    "gender": [
      "male": 100000,
      "female": 20000,
      "undefined": 100
    ],
    "birth_year": [
      "1990_1995": 100000,
      "undefined": 200
    ],
    "marital_status": [
      "1": 100,
      "2": 120,
      "3": 80
    ],
    "province": [
      "1": 2000,
      "2": 1500
    ],
    "job": [
      "1": 200,
      "2", 150
    ]
  },
  "message": "request thành công."
}
"""

**************************************Start LookaLike*********************************
* version: 1.0.0
* Flow: - API khởi tạo tiến trình lookalike
***************************************************************************************
"""
@api {post} /profiling/internal/v3.0/ads/lal Start LookaLike process.
@apiDescription Ads kết quả phân tích theo session_id.
@apiGroup Ads
@apiVersion 1.0.0
@apiName AdsCreateLal
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Body:)    {string}     merchant_id             Merchant Id
@apiParam   (Body:)    {string}     origin_ss_id            Root session id 
@apiParam   (Body:)    {string}     child_ss_id             Process session id
@apiParam   (Body:)    {string}     webhook             Webhook của ADS mà profiling sẽ bắn profile về

@apiParamExample Body example:
{
  "merchant_id": "992b1bff-df6e-493a-8e98-1ecdada5727a",
  "origin_ss_id": "asdasdasdas",
  "child_ss_id": "asdasdasda",
  "webhook": "https://example.com/api/ads_webhook",
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi",
  "message": "request thành công."
}
"""

**************************************Find More LookAlike*********************************
* version: 1.0.0
* Flow: Sau khi profileFactory phân tích và bắn về cho ADS một lượng LookALike mà Factory có thể tìm thấy, ADS module sẽ xác định lại nhu cầu cần thêm bao nhiêu profiles và bắn lại cho Profiling để redirect cho Lake
***************************************************************************************
"""
@api {post} /profiling/internal/v3.0/ads/lal/find_more Find more profile look a like
@apiDescription Sau khi profileFactory phân tích và bắn về cho ADS một lượng LookALike mà Factory có thể tìm thấy, ADS module sẽ xác định lại nhu cầu cần thêm bao nhiêu profiles và bắn lại cho Profiling để redirect cho Lake
@apiGroup Ads
@apiVersion 1.0.0
@apiName FindMoreProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Body:)    {string}     merchant_id             Merchant Id
@apiParam   (Body:)    {string}     origin_ss_id            Root session id 
@apiParam   (Body:)    {string}     child_ss_id             Process session id
@apiParam   (Body:)    {string}     webhook             Webhook của ADS mà profiling sẽ bắn profile về
@apiParam   (Body:)    {int}        need_more               Số lượng profile mà ads cần thêm

@apiParamExample Body example:
{
  "merchant_id": "992b1bff-df6e-493a-8e98-1ecdada5727a",
  "origin_ss_id": "asdasdasdas",
  "child_ss_id": "asdasdasda",
  "webhook": "https://example.com/api/ads_webhook",
  "need_more": 200
  
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi",
  "message": "request thành công."
}
"""

**************************************Ads Upsert Profile*********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/ads/profile/upsert Ads upsert profile.
@apiDescription Ads upsert Profile
@apiGroup Ads
@apiVersion 1.0.0
@apiName AdsUpsertExcel
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}    merchant_id Merchant Id

@apiParam   (Body:)    {String}          [birthday]             Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (Body:)    {String}          [email]                email
@apiParam   (Body:)    {String}          [gender]                Giới tính.
<li><code>nam</code></li>
<li><code>male</code></li>
<li><code>nu</code></li>
<li><code>female</code></li>
@apiParam   (Body:)    {String}          name                Tên của profile.
@apiParam   (Body:)    {String}          [phone_number]                Số điện thoại.
@apiParam   (Body:)    {String}          [address]                địa chỉ.
@apiParam   (Body:)    {List}          [tags]                Các tags sẽ được gán vào profile.
@apiParam   (Body:)    {List}          [profile_group]                Các group sẽ được gán vào profile.
@apiParam   (Body:)    {List}          [remove_profile_group]                Các group sẽ được gỡ khỏi profile.


@apiParamExample body example:
{
  "birthday": "1989-12-21",
  "email": "<EMAIL>",
  "gender": "nam",
  "name": "GiangNTH",
  "phone_number": "0986123444",
  "address": "Duy tan Ha noi",
  "tags": ["tag_id1"],
  "profile_group": ["bb0a81e2-02ed-4c94-a81c-217aad9d8d74"],
  "remove_profile_group": ["72a93d45-a448-4a69-ad00-55ae9df89cd0"]
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi",
  "success": true,
  "data": {
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "address": "dia chi 1",
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ] 
      "answers": [], 
      "association": [], 
      "avatar": null, 
      "avg_monthly_consumption": null, 
      "avg_rate": null, 
      "birthday": null, 
      "created_account_type": 0, 
      "created_time": "2019-03-27T17:52:46Z", 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "gender": 2, 
      "hobby": [], 
      "is_non_profile": false, 
      "job": null, 
      "last_time_active": null, 
      "last_time_transaction": null, 
      "marital_status": null, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "Giang", 
      "people_id": null, 
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      }, 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "province_code": null, 
      "salary": null, 
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "social_user": [],
      "tags": [], 
      "updated_time": "2019-03-27T17:52:46Z"
    },
  "message": "request thành công."
}
"""
*********************************** Upsert Product ************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/bank/products_v2 Upsert Product BANK
@apiDescription Upsert Product BANK
@apiGroup BANK
@apiVersion 1.0.0
@apiName UpsertProduct

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    name              T<PERSON><PERSON> c<PERSON><PERSON> sả<PERSON> phẩ<PERSON>
@apiParam       (Body:)     {String}    term              <PERSON><PERSON><PERSON> <PERSON><PERSON> hạn

@apiParamExample  {json} Body request example
{
    "name": "IDC",
    "term": "7D"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "5e901a7afb403a80e29678e7",
        "created_time": "2020-03-24T11:54:22.000Z",
        "name": "IDC",
        "term": "7D",
        "status": 1,
        "updated_time": "2020-04-09T05:14:46.000Z",
        "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

*********************************** Upsert MCC     ************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/bank/mcc_v2 Upsert MCC BANK
@apiDescription Upsert MCC BANK
@apiGroup BANK
@apiVersion 1.0.0
@apiName UpsertMCC

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Integer}   code              Mã MCC của BANK
@apiParam       (Body:)     {String}    name              Tên của sản phẩm

@apiParamExample  {json} Body request example
{
    "code": 123,
    "name": "MCC 1"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "5e901a7afb403a80e29678e7",
        "created_time": "2020-03-24T11:54:22.000Z",
        "name": "MCC 1",
        "code": 123,
        "status": 1,
        "updated_time": "2020-04-09T05:14:46.000Z",
        "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

*********************************** Upsert Merchant ***********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/bank/merchants_v2 Upsert Merchant BANK
@apiDescription Upsert Merchant BANK
@apiGroup BANK
@apiVersion 1.0.0
@apiName UpsertMerchant

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Integer}    code             Mã của merchant
@apiParam       (Body:)     {String}    name              Tên của merchant

@apiParamExample  {json} Body request example
{
    "name": "Merchant 1",
    "code": 123
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "5e901a7afb403a80e29678e7",
        "created_time": "2020-03-24T11:54:22.000Z",
        "name": "Merchant 1",
        "status": 1,
        "updated_time": "2020-04-09T05:14:46.000Z",
        "code": 123,
        "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

*********************************** Upsert Card Status *************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/bank/upsert_card_status_v2 Upsert Card Status
@apiDescription Upsert Card Status
@apiGroup BANK
@apiVersion 1.0.0
@apiName UpsertCardStatus

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    name Trạng thái của thẻ 

@apiParamExample  {json} Body request example
{
    "name": "NORM"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
*********************************** Upsert Card Status *************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/bank/upsert_promotion Upsert Promotion
@apiDescription Upsert Promotion
@apiGroup BANK
@apiVersion 1.0.0
@apiName UpsertPromotion

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    promotion_name Tên ưu đãi

@apiParamExample  {json} Body request example
{
    "promotion_name": "test3"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công.",
    "name": "test3"
}
"""

# ********************************* Internal check rule ABAC event ********************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /profiling/internal/v3.0/<profile_id>/product_holding/check_rule_event Internal check rule ABAC event
@apiDescription Internal check rule ABAC event
@apiGroup Product Holding
@apiVersion 1.0.0
@apiName Internal check rule ABAC event
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apisuccess       {Array}                       success                                D/s product holding được phép xem evnet.

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [
    {
      "product_holding_id": "5c7ee512ad8e9c9172eadd37144a9083"
    }
  ]
  "lang": "vi", 
  "message": "request thành công."
}
"""
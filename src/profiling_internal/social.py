#!/usr/bin/python
# -*- coding: utf8 -*-

******************************** Social Upsert Profile ********************************
* version: 1.0.0       
* version: 1.0.1                                                                      *
* version: 1.0.2                                                                      *
* version: 1.0.3                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/profile/actions/social/upsert Social Upsert Profile.
@apiDescription Social Upsert Profile
@apiGroup Profile
@apiVersion 1.0.0
@apiName SocialUpsertProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.

@apiParam   (Body:)    {Object}         social_user        Thông tin xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)     {Number}    created_account_type                    Nguồn tạo tài khoản.
<li><code>2: FACEBOOK</code></li>
<li><code>6: ZALO</code></li>
<li><code>7: INSTAGRAM</code></li>
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (Body:)    {String}          [avatar]             Ảnh đại diện.
@apiParam   (Body:)    {Array}          [email]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {Array}          [phone_number]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {String}          [address]             Địa chỉ.
@apiParam   (Body:)    {String}          [birthday]             Ngày sinh.

@apiParam   (Social_User:)    {String}         social_id        Id của mạng mạng xã hội.
@apiParam   (Social_User:)    {String}          social_name             Tên Social của Profile.
@apiParam   (Social_User:)    {Int}            social_type      Mạng xã hội.
<li><code>1: FACEBOOK</code></li>
<li><code>2: ZALO</code></li>
<li><code>3: INSTAGRAM</code></li>
<li><code>4: YOUTUBE</code></li>
@apiParamExample {json} SocialUser-Example:
{"social_id": "*********", "social_type": 1, "social_name": "andrew"}

@apiParamExample [json] Body example:
{
  "created_account_type": 2,
  "gender": 2,
  "social_user": {"social_id": "*********", "social_type": 1, "social_name": "andrew"},
  "avatar: "https://fb.com/images/avatar.jpg"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "avatar": null, 
      "birthday": null, 
      "created_account_type": 2, 
      "created_time": "2019-03-27T17:52:46Z", 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "gender": 2, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "andrew",
      "social_name": [{"id":1,"name": "andrew"}],
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "social_user": [{"social_id": "*********", "social_type": 1}], 
      "updated_time": "2019-03-27T17:52:46Z", 
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

"""
@api {post} [HOST]/profiling/internal/v3.0/profile/actions/social/upsert Social Upsert Profile.
@apiDescription Social Upsert Profile
@apiGroup Profile
@apiVersion 1.0.1
@apiName SocialUpsertProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.

@apiParam   (Body:)    {String}         [merchant_id]      id của nhãn hàng.
@apiParam   (Body:)    {Object}         social_user        Thông tin xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)     {Number}    created_account_type                    Nguồn tạo tài khoản.
<li><code>2: FACEBOOK</code></li>
<li><code>6: ZALO</code></li>
<li><code>7: INSTAGRAM</code></li>
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (Body:)    {String}          [avatar]             Ảnh đại diện.
@apiParam   (Body:)    {Array}          [email]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {Array}          [phone_number]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {String}          [address]             Địa chỉ.
@apiParam   (Body:)    {String}          [birthday]             Ngày tháng, năm sinh.
@apiParam   (Body:)    {String}          [birth_date]             Ngày sinh.
@apiParam   (Body:)    {String}          [birth_year]             Năm sinh.
@apiParam   (Body:)    {Array}          [social_tags]             social tags.

@apiParam   (Social_User:)    {String}         social_id        Id của mạng mạng xã hội.
@apiParam   (Social_User:)    {String}          social_name             Tên Social của Profile.
@apiParam   (Social_User:)    {Number}            social_type      Mạng xã hội.
<li><code>1: FACEBOOK</code></li>
<li><code>2: ZALO</code></li>
<li><code>3: INSTAGRAM</code></li>
<li><code>4: YOUTUBE</code></li>
@apiParam   (Social_User:)    {Number=1:APP 2:GLOBAL}            social_id_type      Kiểu id trên mạng xã hội.
@apiParamExample {json} SocialUser-Example:
{"social_id": "*********", "social_type": 1, "social_id_type": 1, "social_name": "andrew"}

@apiParamExample [json] Body example:
{
  "created_account_type": 2,
  "gender": 2,
  "social_user": {"social_id": "*********", "social_type": 1, "social_id_type": 1, "social_name": "andrew"},
  "avatar: "https://fb.com/images/avatar.jpg"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "avatar": null, 
      "birthday": null, 
      "created_account_type": 2, 
      "created_time": "2019-03-27T17:52:46Z", 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "gender": 2, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "andrew",
      "social_name": [{"id":1, "name": "andrew"}],
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "social_user": [{"social_id": "*********", "social_type": 1, "social_id_type": 1}], 
      "updated_time": "2019-03-27T17:52:46Z", 
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

"""
@api {post} [HOST]/profiling/internal/v3.0/profile/actions/social/upsert Social Upsert Profile.
@apiDescription Social Upsert Profile
@apiGroup Profile
@apiVersion 1.0.2
@apiName SocialUpsertProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.
@apiParam   (Query:)    {String}  [group]     Chuỗi query nhận diện group của Merchant.

@apiParam   (Body:)    {String}         [merchant_id]      id của nhãn hàng.
@apiParam   (Body:)    {Object}         social_user        Thông tin xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)     {Number}    created_account_type                    Nguồn tạo tài khoản.
<li><code>2: FACEBOOK</code></li>
<li><code>6: ZALO</code></li>
<li><code>7: INSTAGRAM</code></li>
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (Body:)    {String}          [avatar]             Ảnh đại diện.
@apiParam   (Body:)    {Array}          [email]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {Array}          [phone_number]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {String}          [address]             Địa chỉ.
@apiParam   (Body:)    {String}          [birthday]             Ngày tháng, năm sinh.
@apiParam   (Body:)    {String}          [birth_date]             Ngày sinh.
@apiParam   (Body:)    {String}          [birth_year]             Năm sinh.
@apiParam   (Body:)    {Array}          [social_tags]             social tags.

@apiParam   (Social_User:)    {String}         social_id        Id của mạng mạng xã hội.
@apiParam   (Social_User:)    {String}          social_name             Tên Social của Profile.
@apiParam   (Social_User:)    {Number}            social_type      Mạng xã hội.
<li><code>1: FACEBOOK</code></li>
<li><code>2: ZALO</code></li>
<li><code>3: INSTAGRAM</code></li>
<li><code>4: YOUTUBE</code></li>
@apiParam   (Social_User:)    {Number=1:APP 2:GLOBAL}            social_id_type      Kiểu id trên mạng xã hội.
@apiParamExample {json} SocialUser-Example:
{"social_id": "*********", "social_type": 1, "social_id_type": 1, "social_name": "andrew"}

@apiParamExample [json] Body example:
{
  "created_account_type": 2,
  "gender": 2,
  "social_user": {"social_id": "*********", "social_type": 1, "social_id_type": 1, "social_name": "andrew"},
  "avatar: "https://fb.com/images/avatar.jpg"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "avatar": null, 
      "birthday": null, 
      "created_account_type": 2, 
      "created_time": "2019-03-27T17:52:46Z", 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "gender": 2, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "andrew",
      "social_name": [{"id":1, "name": "andrew"}],
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "social_user": [{"social_id": "*********", "social_type": 1, "social_id_type": 1}], 
      "updated_time": "2019-03-27T17:52:46Z", 
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

******************************** Social Update Profile ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/internal/v3.0/profile/actions/social/update/<profile_id> Social Update Profile.
@apiDescription Social Update Profile
@apiGroup Profile
@apiVersion 1.0.0
@apiName SocialUpdateProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.

@apiParam   (Body:)    {Array}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (Body:)    {Array}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (Body:)    {Object}         [social_user]        Thông tin xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (Body:)    {String}          [avatar]             Ảnh đại diện.
@apiParam   (Body:)    {Array}          [email]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {Array}          [phone_number]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {String}          [address]             Địa chỉ.
@apiParam   (Body:)    {String}          [birthday]             Ngày sinh.

@apiParamExample {json} SocialUser-Example:
{"social_id": "*********", "social_type": 1, "social_name": "andrew"}

@apiParamExample [json] Body example:
{
  "gender": 2,
  "phone_number": ["**********"],
  "email": ["<EMAIL>"],
  "social_user": {"social_id": "*********", "social_type": 1, "social_name": "andrew"},
  "avatar: "https://fb.com/images/avatar.jpg"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Cập nhật khách hàng thành công.", 
    "process_type": "update", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "avatar": null, 
      "birthday": null, 
      "created_account_type": 2, 
      "created_time": "2019-03-27T17:52:46Z", 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "gender": 2, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "andrew", 
      "social_name": [{"id":1,"name": "andrew"}],
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "social_user": [{"social_id": "*********", "social_type": 1}], 
      "updated_time": "2019-03-27T17:52:46Z", 
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

******************************** Social Update Profile ********************************
* version: 1.0.1                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/internal/v3.0/profile/actions/social/update/<profile_id> Social Update Profile.
@apiDescription Social Update Profile
@apiGroup Profile
@apiVersion 1.0.1
@apiName SocialUpdateProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.

@apiParam   (Body:)    {String}         [merchant_id]      id của nhãn hàng.
@apiParam   (Body:)    {Object}         social_user        Thông tin xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)     {Number}    created_account_type                    Nguồn tạo tài khoản.
<li><code>2: FACEBOOK</code></li>
<li><code>6: ZALO</code></li>
<li><code>7: INSTAGRAM</code></li>
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (Body:)    {String}          [avatar]             Ảnh đại diện.
@apiParam   (Body:)    {Array}          [email]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {Array}          [phone_number]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {String}          [address]             Địa chỉ.
@apiParam   (Body:)    {String}          [birthday]             Ngày tháng, năm sinh.
@apiParam   (Body:)    {String}          [birth_date]             Ngày sinh.
@apiParam   (Body:)    {String}          [birth_year]             Năm sinh.
@apiParam   (Body:)    {Array}          [social_tags]             social tags.

@apiParam   (Social_User:)    {String}         social_id        Id của mạng mạng xã hội.
@apiParam   (Social_User:)    {String}          social_name             Tên Social của Profile.
@apiParam   (Social_User:)    {Number}            social_type      Mạng xã hội.
<li><code>1: FACEBOOK</code></li>
<li><code>2: ZALO</code></li>
<li><code>3: INSTAGRAM</code></li>
<li><code>4: YOUTUBE</code></li>
@apiParam   (Social_User:)    {Number=1:APP 2:GLOBAL}            social_id_type      Kiểu id trên mạng xã hội.
@apiParamExample {json} SocialUser-Example:
{"social_id": "*********", "social_type": 1, "social_id_type": 1, "social_name": "andrew"}

@apiParamExample [json] Body example:
{
  "created_account_type": 2,
  "gender": 2,
  "social_user": {"social_id": "*********", "social_type": 1, "social_id_type": 1, "social_name": "andrew"},
  "avatar: "https://fb.com/images/avatar.jpg"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Cập nhật khách hàng thành công.", 
    "process_type": "update", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "avatar": null, 
      "birthday": null, 
      "created_account_type": 2, 
      "created_time": "2019-03-27T17:52:46Z", 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "gender": 2, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "andrew", 
      "social_name": [{"id":1, "name": "andrew"}],
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "social_user": [{"social_id": "*********", "social_type": 1, "social_id_type": 1}], 
      "updated_time": "2019-03-27T17:52:46Z", 
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""
######################### Filter Validate  ###########################
# version: 1.0.1
#################################################################
"""
@api {post} [HOST]/profiling/internal/v3.0/profile/validate/filter Profile Validate Filter
@apiDescription API kiểm tra profile có hợp lệ với bộ lọc hay không.
@apiVersion 1.0.1
@apiGroup Validate
@apiName Validate Filter

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)   {string}        merchant_id        merchant_id
@apiParam (Body:)   {string}        profile_id         profile_id 
@apiParam (Body:)   {array}         filter_info        Danh sách bộ lọc

@apiSuccess                   {int}                 code                                Mã response
@apiSuccess                   {text}                lang                                Ngôn ngữ hiển thị 
@apiSuccess                   {text}               message                             Thông tin từ hệ thống
@apiSuccess                   {int}                status                              Trạng thái kiểm tra profile có thỏa mãn điều kiện với bộ lọc hay không: <br><code>true</code>: Thỏa mãn<br><code>false</code>: không thỏa mãn



@apiParamExample    {json}      Body example:
{
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "profile_id": "97eb7f87-517b-4970-9f5f-36646f2b2976",
    "filter_info": [
        {
            "values" : [ 
                "e72144f2-aea4-45f6-bc12-603721542074"
            ],
            "operator_key" : "op_is_in",
            "criteria_key" : "cri_tags_ids"
        }
    ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "code": 200,
   "lang": "vi",
   "message": "request thành công",
   "status" : true // true; false
}
"""
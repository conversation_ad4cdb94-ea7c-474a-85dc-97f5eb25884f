**************************************Ads Upload Excel*********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/jb/device journey builder create device.
@apiDescription journey builder create device
@apiGroup JourneyBuilder
@apiVersion 1.0.0
@apiName JourneyBuilderCreateDevice
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)    {String}       client_ip            IP của client.
@apiParam   (Body:)    {String}       client_device_type       Kiêu device của client
@apiParam   (Body:)    {String}       client_device_name       Tên device của client 
@apiParam   (Body:)    {String}       client_source       nguồn ghi nhận của client. Có thể là website hoặc App khách hàng truy cập.
@apiParam   (Body:)    {String}       customer_id         id của client trên hệ thống 3rdparty.


@apiParamExample query example:
{
  "client_ip": "**************",
  "client_device_type": "Mobile",
  "client_device_name": "Iphone 8",
  "client_source": "https://mobio.vn",
  "customer_id": null
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "data": : {
	"profile_id": "886dbfd0-d6c8-4779-ab76-d4b90e358966",
	"device_id": "fd161cb7-496d-4198-b1e4-c05806c382ab",
	"merchant_id": ["72da8c45-5353-4c4e-bd1d-3c6776ea3a0a"]
  }
  "message": "request thành công."
}
"""

****************************************JB Add PushID**********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/jb/push_id journey builder add PushId.
@apiDescription journey builder add PushId
@apiGroup JourneyBuilder
@apiVersion 1.0.0
@apiName JourneyBuilderAddPushId
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)    {String}       push_id            		access_token của device.
@apiParam   (Body:)    {Number}       os_type       			Kiêu hệ điều hành.
<li><code>IOS = 1</code></li>
<li><code>ANDROID = 2</code></li>
<li><code>WEB_PUSH_FIREBASE = 5</code></li>
<li><code>WEB_PUSH_APNS = 6</code></li>
@apiParam   (Body:)    {String}       app_id       				ID của APP hoặc domain name 
@apiParam   (Body:)    {String}       device_id       			Id của thiết bị. (Cookie Id)


@apiParamExample query example:
{
  "push_id": "21a1a5f0-0b0f-4e82-9059-f51448f3b8c0",
  "os_type": 5,
  "app_id": "https://mobio.vn",
  "device_id": "df259e13-7bdf-43d7-8644-8abf0b226709",
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "success": : true,
  "message": "request thành công."
}
"""


***************************************JB Update PushID********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/jb/push_id/<old_push_id> journey builder update PushId.
@apiDescription journey builder update PushId
@apiGroup JourneyBuilder
@apiVersion 1.0.0
@apiName JourneyBuilderUpdatePushId
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)    {String}       push_id            		access_token của device.
@apiParam   (Body:)    {Number}       os_type       			Kiêu hệ điều hành.
<li><code>IOS = 1</code></li>
<li><code>ANDROID = 2</code></li>
<li><code>WEB_PUSH_FIREBASE = 5</code></li>
<li><code>WEB_PUSH_APNS = 6</code></li>
@apiParam   (Body:)    {String}       app_id       				ID của APP hoặc domain name 
@apiParam   (Body:)    {String}       device_id       			Id của thiết bị. (Cookie Id)
@apiParam   (Body:)    {Bool}        accept_push       			Đồng ý nhận push hay không?


@apiParamExample query example:
{
  "push_id": "21a1a5f0-0b0f-4e82-9059-f51448f3b8c0",
  "os_type": 5,
  "app_id": "https://mobio.vn",
  "device_id": "df259e13-7bdf-43d7-8644-8abf0b226709",
  "accept_push": true
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "success": : true,
  "message": "request thành công."
}
"""

******************************* Get profile estimate **********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {POST} [HOST]/profiling/internal/v3.0/journey/profile/estimate [API] Get profile estimate
@apiDescription Get profile estimate JB
@apiGroup JourneyBuilder
@apiVersion 1.0.0
@apiName GetJBProfileEstimate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {Array}     audiences_filter       Danh sách nhóm audience
@apiParam      (Body:)     {Array}     audiences_filter.profile_filter       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample [json] Body example:
{
  "audiences_filter": [
    {
      "profile_filter": [
          {
          "criteria_key": "cri_email",
          "operator_key": "op_is_not_empty",
          "values": [
            "op_is_not_empty"
          ]
        }
      ],
      "position": 0,
      "operator": null
    },
    {
      "profile_filter": [
        {
          "criteria_key": "cri_email",
          "operator_key": "op_is_not_empty",
          "values": [
            "op_is_not_empty"
          ]
        }
      ],
      "position": 1,
      "operator": "and"
    },
    {
      "profile_filter": [
        {
          "criteria_key": "cri_email",
          "operator_key": "op_is_not_empty",
          "values": [
            "op_is_not_empty"
          ]
        }
      ],
      "position": 2,
      "operator": "exclude"
    }
  ],
  "version": 2,
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "estimate": 5000
}
"""

******************************* Get list profile by filter ****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {Post} [HOST]/profiling/internal/v3.0/journey/profile_filter/register [API] Get profile by filter
@apiDescription Get profile by filter callback queue
@apiGroup JourneyBuilder
@apiVersion 1.0.0
@apiName JBGetProfileByFilter

@apiParam      (Body:)     {Array}    audiences_filter       Danh sách nhóm audience
@apiParam      (Body:)     {Array}    audiences_filter.profile_filter       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Body:)     {Array}    fields      Danh sách field cần lấy
@apiParam      (Body:)     {Int}      [per_page]    Số profile trả về callback, <code>default: 10</code>
@apiParam      (Body:)     {Int}      [personalized]   1: Lấy field format cá nhân hóa; 0: Trả về field gốc; <code>default: 0</code>
@apiParam      (Body:)     {Boolean}  [random]    true: Cho phép lấy profile random; false: Lấy profile theo danh sách. <code>default: false</code>
@apiParam      (Body:)     {Int}      [random_limit]  <code>required</code> trong trường hợp random: true; <code>max limit <=50000</code>
@apiParam      (Body:)     {String}   [seed]  Dùng trong trường hợp random: true
@apiParam      (Body:)     {Object}   callback  cấu hình callback
@apiParam      (Body:)     {String}   callback.queue_name  Tên queue callback
@apiParam      (Body:)     {String}   [callback.queue_key]  queue_key callback, <code>default: random</code>
@apiParam      (Body:)     {Object}   [callback.data]  data trả về qua callback
@apiParam      (Body:)     {String}   journey_id  Journey ID
@apiParam      (Body:)     {String}   jb_start_time  Thời gian start journey builder; <code>VD: 2018-07-27T09:53:21.000Z</code>
@apiParam      (Body:)     {Int}      version Version của bộ lọc; <code>default: 1</code>; <code>2: Support bộ lọc phức hợp</code>
@apiParam      (Body:)     {String}   [audience_group]  Phân tệp audience; Nhận 1 trong 2 giá trị: <code>GROUP_1</code>, <code>GROUP_2</code>; Default: <code>GROUP_1</code>; Có thể phân chia GROUP_1 là tệp lớn, GROUP_2 là tệp bé

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample [json] Body example:
{
    "audiences_filter": [
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 0,
            "operator": null
        },
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 1,
            "operator": "and"
        },
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 2,
            "operator": "exclude"
        }
    ],
    "fields": ["name", "email", "phone_number"],
    "per_page": 10, // default 10
    "personalized": 1, // Cho phep lay truong ca nhan hoa
    "random": true, // Boolean cho phép lấy dât random
    "seed": "123456789", // Dùng trong trường hợp random is true
    "callback": {
    	"queue_name": "jb-queue-callback",
    	"queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a", // Nếu không truyền hoặc giá trị null thì callback key random
    	"data": {
    		"field_1": "123",
    		"field_2": "321"
    	}
    },
    "journey_id": "7db1010b-b666-4573-ad04-43a21da66720",
    "jb_start_time": "2018-07-27T09:53:21.000Z",
    "version": 2
}

@apiSuccessExample  {json}  Callback Queue:
{
    "code": 200,
    "data": [
        {
          "address": "Tỉnh Thanh Hóa",
          "birthday": "1981-07-28",
          "created_time": "2018-07-27T09:53:21Z",
          "name": "Vũ Thị thọ 123",
          "email": "['<EMAIL>', '<EMAIL>']",
          "gender": 3,
          "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
          "merchant_id": ["618261e0-dee6-4440-a744-89f67235b347"],
          "phone_number": ["+841215150001"],
          "social_user": [
            {
              "id_social": "2139374173003427",
              "social_type": 1
            }
          ],
          "updated_time": "2018-07-28T04:57:35Z",
          "source_type": 1,
          "source_id": "435ecfeb-e229-4076-806f-982580475e88",
          "is_non_profile": false
        },
        ...
    ],
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "total_count": 5000
}
"""

********************** Queue Get profile detail by profile_id *************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} profiling-jb-get-profile-by-id [QUEUE] Get profile detail by profile_id
@apiDescription Get profile detail by profile_id
@apiGroup JourneyBuilder
@apiVersion 1.0.0
@apiName JBGetProfileDetailByID

@apiParam      (Input:)     {Array}    fields      Danh sách field cần lấy
@apiParam      (Input:)     {Int}      [personalized]   1: Lấy field format cá nhân hóa; 0: Trả về field gốc; <code>default: 0</code>
@apiParam      (Input:)     {Object}   callback  cấu hình callback
@apiParam      (Input:)     {String}   callback.queue_name  Tên queue callback
@apiParam      (Input:)     {String}   [callback.queue_key]  queue_key callback, <code>default: profile_id</code>
@apiParam      (Input:)     {Object}   [callback.data]  data trả về qua callback
@apiParam      (Input:)     {String}   source      Nguồn bên cần lấy dữ liệu: example: EVENT, JOURNEY_BUILDER
@apiParam      (Input:)     {Object}   setting     Setting config queue

@apiParam      (CallbackQueue:)     {Object}   data                     Dữ liệu Profile
@apiParam      (CallbackQueue:)     {Boolean}  data.consent_deleted     <code>true</code> Đã xóa; <code>false</code> Chưa xóa;
@apiParam      (CallbackQueue:)     {String}   data.mkt_consent         <code>Có</code> Cho phép MKT; <code>Không</code> Không cho phép MKT;
@apiParam      (CallbackQueue:)     {String}   data.analytics_consent   <code>Có</code> Cho phép phân tích; <code>Không</code> Không cho phép phân tích;
@apiParam      (CallbackQueue:)     {String}   data.tracking_consent    <code>Có</code> Cho phép tracking; <code>Không</code> Không cho phép tracking;

@apiParamExample [json] Input example:
{
    "merchant_id": "",
    "profile_id": "",
    "fields": ["name", "email", "phone_number"],
    "personalized": 1, // Cho phep lay truong ca nhan hoa
    "callback": {
    	"queue_name": "jb-queue-callback",
    	"queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a", // Nếu không truyền hoặc giá trị null thì callback key profile_id
    	"data": {
    		"field_1": "123",
    		"field_2": "321"
    	}
    },
    "source": "JOURNEY_BUILDER",
    "setting": {
        "setting_field_1": 1
    }
}

@apiSuccessExample  {json}  Callback Queue:
{
    "code": 200,
    "data_input": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": ""
    },
    "data": {
        "address": "Tỉnh Thanh Hóa",
        "birthday": "1981-07-28",
        "created_time": "2018-07-27T09:53:21Z",
        "name": "Vũ Thị thọ 123",
        "gender": 3,
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": ["618261e0-dee6-4440-a744-89f67235b347"],
        "phone_number": ["+841215150001"],
        "social_user": [
            {
              "id_social": "2139374173003427",
              "social_type": 1
            }
        ],
        "updated_time": "2018-07-28T04:57:35Z",
        "source_type": 1,
        "source_id": "435ecfeb-e229-4076-806f-982580475e88",
        "mkt_consent": "Có",
        "analytics_consent": "Không",
        "tracking_consent": "Có",
        "consent_deleted": false,
        "is_non_profile": false
    },
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}

@apiSuccessExample  {json}  Callback Queue 404:
{
    "code": 404,
    "data_input": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": "618261e0-dee6-4440-a744-89f67235b347"
    },
    "reason": "profile is merged!",
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}
"""


********************** Queue JB validate filter ***************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} profiling-jb-validate-filter [QUEUE] JB Validate filter
@apiDescription Validate filter
@apiGroup JourneyBuilder
@apiVersion 1.0.0
@apiName JBValidateFilter

@apiParam      (Input:)     {Array}    fields      Danh sách field cần lấy
@apiParam      (Input:)     {Int}      [personalized]   1: Lấy field format cá nhân hóa; 0: Trả về field gốc; <code>default: null</code>; Nếu giá trị là <code>null</code> thì không trả về thông tin profile
@apiParam      (Input:)     {Object}   callback  cấu hình callback
@apiParam      (Input:)     {String}   callback.queue_name  Tên queue callback
@apiParam      (Input:)     {String}   [callback.queue_key]  queue_key callback, <code>default: profile_id</code>
@apiParam      (Input:)     {Object}   [callback.data]  data trả về qua callback
@apiParam      (Input:)     {Object}   audiences_filter  Thông tin bộ lọc
@apiParam      (Input:)     {String}   source      Nguồn bên cần lấy dữ liệu: example: EVENT, JOURNEY_BUILDER
@apiParam      (Input:)     {Object}   setting     Setting config queue
@apiParam      (Input:)     {Int}      version Version của bộ lọc; <code>default: 1</code>; <code>2: Support bộ lọc phức hợp</code>

@apiParamExample [json] Input example:
{
    "merchant_id": "",
    "profile_id": "",
    "fields": ["name", "email", "phone_number"],
    "personalized": 1, // Cho phep lay truong ca nhan hoa
    "audiences_filter": [
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 0,
            "operator": null
        },
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 1,
            "operator": "and"
        },
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_email",
                    "operator_key": "op_is_not_empty",
                    "values": [
                        "op_is_not_empty"
                    ]
                }
            ],
            "position": 2,
            "operator": "exclude"
        }
    ],
    "callback": {
    	"queue_name": "jb-queue-callback",
    	"queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a", // Nếu không truyền hoặc giá trị null thì callback key profile_id
    	"data": {
    		"field_1": "123",
    		"field_2": "321"
    	}
    },
    "source": "EVENT",
    "setting": {
        "setting_field_1": 1
    },
    "version": 2
}

@apiSuccessExample  {json}  Callback Queue:
{
    "code": 200,
    "status": True,
    "data_input": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": ""
    },
    "data": {
        "address": "Tỉnh Thanh Hóa",
        "birthday": "1981-07-28",
        "created_time": "2018-07-27T09:53:21Z",
        "name": "Vũ Thị thọ 123",
        "email": "['<EMAIL>', '<EMAIL>']",
        "gender": 3,
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": ["618261e0-dee6-4440-a744-89f67235b347"],
        "phone_number": ["+841215150001"],
        "social_user": [
            {
              "id_social": "2139374173003427",
              "social_type": 1
            }
        ],
        "updated_time": "2018-07-28T04:57:35Z",
        "source_type": 1,
        "source_id": "435ecfeb-e229-4076-806f-982580475e88",
        "is_non_profile": false
    },
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}

@apiSuccessExample  {json}  Callback Queue 404:
{
    "code": 404,
    "status": False,
    "data_input": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": "618261e0-dee6-4440-a744-89f67235b347"
    },
    "reason": "profile is not exist!",
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}
"""


********************** Queue JB check multi split branch ******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} profiling-jb-check-multi-split-branch [QUEUE] JB check multi split branch
@apiDescription JB check multi split branch
@apiGroup JourneyBuilder
@apiVersion 1.0.0
@apiName JBCheckMultiSplitBranch

@apiParam      (Input:)     {String}   merchant_id  merchant_id
@apiParam      (Input:)     {String}   profile_id   profile_id
@apiParam      (Input:)     {Object}   callback  cấu hình callback
@apiParam      (Input:)     {String}   callback.queue_name  Tên queue callback
@apiParam      (Input:)     {String}   [callback.queue_key]  queue_key callback, <code>default: profile_id</code>
@apiParam      (Input:)     {Object}   [callback.data]  data trả về qua callback
@apiParam      (Input:)     {Array}    branches    Danh sách các nhánh cần kiểm tra    
@apiParam      (Input:)     {String}   source      Nguồn bên cần lấy dữ liệu: example: EVENT, JOURNEY_BUILDER
@apiParam      (Input:)     {Int}      version Version của bộ lọc; <code>default: 1</code>; <code>2: Support bộ lọc phức hợp</code>

@apiParamExample [json] Input example:
{
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
    "source": "JOURNEY_BUILDER",
    "callback": {
    	"queue_name": "jb-queue-callback",
    	"queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
    	"data": {
    		"field_1": "123",
    		"field_2": "321"
    	}
    },
    "branches": [
        {
            "audiences_filter" : [
                {
                    "profile_filter": [
                        {
                            "criteria_key": "cri_email",
                            "operator_key": "op_is_not_empty",
                            "values": [
                                "op_is_not_empty"
                            ]
                        }
                    ],
                    "position": 0,
                    "operator": null
                }
            ],
            "position": 0
        },
        {
            "audiences_filter" : [
                {
                    "profile_filter": [
                        {
                            "criteria_key": "cri_email",
                            "operator_key": "op_is_not_empty",
                            "values": [
                                "op_is_not_empty"
                            ]
                        }
                    ],
                    "position": 0,
                    "operator": null
                }
            ],
            "position": 1
        }
    ]   
}


@apiSuccessExample  {json}  Callback Queue thỏa mãn 1 nhánh:
{
    "code": 200,
    "position": 0,
    "status": true,
    "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}

@apiSuccessExample  {json}  Callback Queue không thỏa mãn nhánh nào:
{
    "code": 200,
    "status": false,
    "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}

@apiSuccessExample  {json}  Callback Queue 404:
{
    "code": 404,
    "status": False,
    "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
    "merchant_id": "618261e0-dee6-4440-a744-89f67235b347"
    "reason": "profile is not exist!",
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}
"""
********************** Queue Profile JB Personalize Filter ****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} profiling-personalize-filter [QUEUE] Queue Profile JB Personalize Filter
@apiDescription Nhận cấu trúc bộ lọc, fields cần lấy giá trị và trả về docs thỏa mãn bộ lọc chưa fields key 
@apiGroup Personalize
@apiVersion 1.0.0

@apiParam      (Input:)     {String}   merchant_id      Merchant ID
@apiParam      (Input:)     {String}   profile_id       Profile ID
@apiParam      (Input:)     {String}   source      Nguồn bên cần lấy dữ liệu: example: EVENT, JOURNEY_BUILDER
@apiParam      (Input:)     {Array}    profile_filter   Danh sách audience
@apiParam      (Input:)     {Array}    profile_filter.audiences_filter    Danh sách bộ lọc
@apiParam      (Input:)     {Array}    profile_filter.fields      Danh sách field cần lấy
@apiParam      (Input:)     {Array}    profile_filter.criteria_key   Danh sách keys bộ lọc cần validate và lấy dữ liệu
@apiParam      (Input:)     {String}   profile_filter.product_line  Giá trị dòng sản phẩm để validate và lấy dữ liệu
@apiParam      (Input:)     {Object}   callback  cấu hình callback
@apiParam      (Input:)     {String}   callback.queue_name  Tên queue callback
@apiParam      (Input:)     {String}   callback.queue_key  queue_key callback, <code>default: profile_id</code>
@apiParam      (Input:)     {Object}   callback.data  data trả về qua callback
@apiParamExample [json] Input example:
{
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "profile_id": "3442fc5b-a44e-485c-b0ab-b88b28eaad6d",
    "version": 2,
    "profile_filter": [
        {
            "audiences_filter": [
                {
                    "position": 0,
                    "operator": None,
                    "profile_filter": [
                        {
                            "criteria_key": "cri_product_holding_v1_total_category_product_multiple",
                            "operator_key": "op_is_multiple",
                            "values": [
                                {
                                    "criteria_key": "cri_product_holding_v1_product_line",
                                    "operator_key": "op_is_equal",
                                    "values": [
                                        "62c24d6a586b2a000f4e324d"
                                    ]
                                },
                                {
                                    "criteria_key": "cri_product_holding_v1_total_category_product",
                                    "operator_key": "op_is_greater_equal",
                                    "values": [
                                        1
                                    ]
                                }
                            ],
                            "display": []
                        },
                        {
                            "criteria_key": "cri_product_holding_v1_product_holding_multiple",
                            "operator_key": "op_is_multiple",
                            "values": [
                                {
                                    "criteria_key": "cri_product_holding_v1_product_line",
                                    "operator_key": "op_is_equal",
                                    "values": [
                                        "62c24d6a586b2a000f4e324d"
                                    ],
                                    "display": [
                                        {
                                            "key": "62c24d6a586b2a000f4e324d",
                                            "value": "Hà k xóa k sửa ahihi",
                                            "language": "vi"
                                        }
                                    ]
                                }
                            ],
                            "display": []
                        }
                    ]
                },
                {
                    "position": 1,
                    "operator": "and",
                    "profile_filter": [
                        {
                            "operator_key": "op_is_multiple",
                            "criteria_key": "cri_product_holding_v1_product_line_use_multiple",
                            "display": [],
                            "values": [
                                {
                                    "criteria_key": "cri_product_holding_v1_category_product_use_status",
                                    "operator_key": "op_is_in",
                                    "values": [
                                        "expired",
                                        "using"
                                    ]
                                },
                                {
                                    "criteria_key": "cri_product_holding_v1_product_line",
                                    "operator_key": "op_is_in",
                                    "values": [
                                        "op_is_least_one",
                                        "63315f75c5a5f8000e59a0e8",
                                        "62c24d6a586b2a000f4e324d"
                                    ]
                                }
                            ]
                        },
                        {
                            "operator_key": "op_is_not_empty",
                            "criteria_key": "cri_phone",
                            "display": [],
                            "values": [
                                "op_is_not_empty"
                            ],
                            "selectedItems": []
                        }
                    ]
                }
            ],
            "fields": [
                "open_day",
                "product_status"
            ],
            "criteria_key": [
                "cri_product_holding_v1_product_holding_multiple",
                "cri_product_holding_v1_product_line_use_multiple",
                "cri_product_holding_v1_total_category_product_multiple"
            ],
            "product_line": "62c24d6a586b2a000f4e324d"
        }
    ],
    "callback": {
        "queue_name": "jb-queue-callback",
        "queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "data": {
            "field_1": "123",
            "field_2": "321"
        }
    }
}

@apiSuccessExample  {json}  Callback Queue:
{
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "profile_id": "3442fc5b-a44e-485c-b0ab-b88b28eaad6d",
    "data": [
        {
            "data": {
                "open_day": "2022-06-05T00:00:00.000000Z",
                "product_status": "Active"
            },
            "product_line": "62c24d6a586b2a000f4e324d",
            "criteria_key": [
                "cri_product_holding_v1_product_holding_multiple",
                "cri_product_holding_v1_product_line_use_multiple",
                "cri_product_holding_v1_total_category_product_multiple"
            ]
        }
    ],
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}
"""
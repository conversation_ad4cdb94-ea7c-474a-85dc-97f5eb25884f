#!/usr/bin/python
# -*- coding: utf8 -*-

******************************* ChatTool Upsert Profile *******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/profile/actions/chattool/upsert ChatTool Upsert Profile.
@apiDescription ChatTool Upsert Profile
@apiGroup Profile
@apiVersion 1.0.2
@apiName ChatToolUpsertProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.
@apiParam   (Query:)    {String}  [group]     Chuỗi query nhận diện group của Merchant.
@apiParam   (Body:)     {Object}    device             Thiết bị sử dụng ChatTool.
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (Body:)    {String}          [avatar]             Ảnh đại diện.
@apiParam   (Body:)    {Array}          [email]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {Array}          [phone_number]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {String}          [address]             Địa chỉ.
@apiParam   (Body:)    {String}          [birthday]             Ngày tháng, năm sinh.


@apiParamExample [json] Body example:
{
  "gender": 2,
  "email": ["<EMAIL>", "<EMAIL>"]
  "device": {"device_id": "*********", "source": "https://mobio.vn", "device_name": "device 1"},
  "avatar: "https://fb.com/images/avatar.jpg"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "avatar": null, 
      "birthday": null, 
      "created_account_type": 2, 
      "created_time": "2019-03-27T17:52:46Z", 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "gender": 2, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "andrew",
      "social_name": [{"id":1, "name": "andrew"}],
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "social_user": [{"social_id": "*********", "social_type": 1, "social_id_type": 1}], 
      "updated_time": "2019-03-27T17:52:46Z", 
      "devices":[
        {"device_id": "*********", "source": "https://mobio.vn", "device_name": "device 1"}
      ]
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

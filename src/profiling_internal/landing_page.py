***********************************Landing Page Upsert Profile******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/landing/upsert_profile Landing Page Upsert Profile
@apiDescription Landing Page Upsert Profile
@apiGroup ProfilingLandingPage
@apiVersion 1.0.0
@apiName LandingPageUpsertProfile

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam       (Body:)     {String}    name              Tên
@apiParam       (Body:)     {String}    [primary_phone]            Số điện thoại
@apiParam       (Body:)     {String}    [primary_email]            Email
@apiParam       (Body:)     {Array}    [secondary_phones]            Danh sách số điện thoại phụ
@apiParam       (Body:)     {Array}    [secondary_emails]            Danh sách Email phụ
@apiParam       (Body:)     {String}    [address]           Địa chỉ
@apiParam       (Body:)     {String}    [birthday]             Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam       (Body:)     {Int}       [gender]             Giới tính.
<li><code>1: UNKNOWN</code></li>
<li><code>2: MALE</code></li>
<li><code>3: FEMALE</code></li>

@apiParam       (Query:)     {String}    [m_profile]              m_profile
@apiParam       (Query:)     {String}    [m_journey]              m_journey
@apiParam       (Query:)     {String}    [m_node]                 m_node
@apiParam       (Query:)     {String}    [m_instance]             m_instance
@apiParam       (Query:)     {String}    [m_tracking_id]          m_tracking_id

@apiParamExample  {json} Body request example
{
  "name": "Nguyen Van A",
  "primary_phone": "0987777777",
  "primary_email": "<EMAIL>",
  "address": "Ha Noi Viet Nam",
  "birthday": "1970-01-01",
  "gender": 2
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "profile_id": "1033568d-c853-4d5c-b55b-461d237eea1a",
        "profile_info": {
            "id": "1033568d-c853-4d5c-b55b-461d237eea1a",
            "name": "Nguyen Van A",
            "phone_number": "0987777777",
            "email": "<EMAIL>",
            "social_user": [{
            "social_id":"1234567",
            "social_type": 1
            }],
            "profile_identify": [
              {
                "identify_value": "123",
                "identify_type": "Passport",
                "is_verify": false,
                "date_verify": null,
                "verify_by": null
              }
            ],
            "avatar": "https://example.com/images/avatar.png"
        },
        "message": "Cập nhật khách hàng thành công.",
        "process_type": "update"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
#!/usr/bin/python
# -*- coding: utf8 -*-
******************************* Get profile by profile_id *****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/merchants/<merchant_id>/user/profile-id Get profile by profile_id.
@apiDescription Get list profile by list profile id
@apiGroup User
@apiVersion 1.0.0
@apiName GetProfileByProfileId

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String[]}    profile_ids       Mảng profile_id
@apiParam      (Body:)     {Array}     [fields]               Danh sách các thuộc t<PERSON>h cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Body:)     {Array}     [staff_id]               mã nhân viên 



@apiParamExample  {json} Body request example
{
    "profile_ids": ["27aeaa12-2d72-4441-b888-b7c26d6903a0", "27aeaa12-2d72-4441-b888-b7c26d6903a1"],
    "fields": ["profile_id","name","birthday"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": ['<EMAIL>', '<EMAIL>'],
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": ["+************"],
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ],
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+84832201234", 
        "status": 0
      },
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+84832206789", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      },
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ],
      "tags": [],
      "degree": 1,
      "income_family": 0,
      "number_childs": 0
    }
  ],
  "encrypt_data": [
        {
            "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a"
        }
    ]
}
"""
******************************* Get profile 'for' loyalty *******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/systems/loyalty/users Get profile for loyalty.
@apiDescription Get list profile for loyalty
@apiGroup User
@apiVersion 1.0.0
@apiName GetProfileForLoyalty

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    search_value        Giá trị tìm kiếm
@apiParam       (Body:)     {String}    search_field        Tên trường tìm kiếm. Giá trị: primary_phone, primary_email
@apiParam       (Body:)     {Array}     fields              Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]


@apiParamExample  {json} Body request example
{
    "search_value": "**********",
    "search_field": "primary_phone"
    "fields": ["profile_id","name","birthday"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": ['<EMAIL>', '<EMAIL>'],
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": ["+************"],
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ],
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+84832201234", 
        "status": 0
      },
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+84832206789", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      },
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ],
      "tags": [],
      "degree": 1,
      "income_family": 0,
      "number_childs": 0
    }
  ]
}
"""

*********************************** Add Card To Profile** *****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/merchants/<merchant-id>/profile/<profile_id>/card Add Card To Profile.
@apiDescription Thêm mới Card vào Profile
@apiGroup ProfilingLoyalty
@apiVersion 1.0.0
@apiName AddCardToProfile

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    card_id              ID của hạng thẻ
@apiParam       (Body:)     {String}    card_code            Mã thẻ
@apiParam       (Body:)     {String}    card_name            Tên thẻ

@apiParamExample  {json} Body request example
{
    "card_id": "6e8b53c9-3d32-4d6f-8844-e44ac5a65ba5",
    "card_code": "ABCD12345678",
    "card_name": "Thẻ vàng"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
      "id": "uid",
      "card_id": "6e8b53c9-3d32-4d6f-8844-e44ac5a65ba5",
      "card_code": "ABCD12345678",
      "card_name": "Thẻ vàng"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

*********************************** Add Card To Profile** *****************************
* version: 1.0.1                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/merchants/<merchant-id>/profile/<profile_id>/card Add Card To Profile.
@apiDescription Thêm mới Card vào Profile
@apiGroup ProfilingLoyalty
@apiVersion 1.0.1
@apiName AddCardToProfile

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    card_id              ID của hạng thẻ
@apiParam       (Body:)     {String}    card_code            Mã thẻ
@apiParam       (Body:)     {String}    card_name            Tên thẻ
@apiParam       (Body:)     {bool}    [is_primary]            Thẻ này có phải thẻ chính của Profile này không?
@apiParam       (Body:)     {Number}    [status]            Trạng thái của thẻ.
<code>
<li>0: "Khóa thẻ"</li>
<li>1: "Đã duyệt"</li>
<li>2: "Chờ duyệt"</li>
<li>3: "Hết hạn"</li>
<li>4: "Chưa có thẻ"</li>
<li>-1: "Hủy thẻ"</li>
</code>
@apiParam       (Body:)     {String}    expiry_time            Thời hạn sử dụng thẻ. FORMAT: 'yyyy-MM-dd hh:mm:ss'.

@apiParamExample  {json} Body request example
{
    "card_id": "6e8b53c9-3d32-4d6f-8844-e44ac5a65ba5",
    "card_code": "ABCD12345678",
    "card_name": "Thẻ vàng",
    "is_primary": false,
    "status": 1,
    "expiry_time": "2029-12-30 23:59:59"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
      "id": "uid",
      "card_id": "6e8b53c9-3d32-4d6f-8844-e44ac5a65ba5",
      "card_code": "ABCD12345678",
      "card_name": "Thẻ vàng",
      "is_primary": false,
      "status": 1,
      "expiry_time": "2029-12-30 23:59:59"
    },
    "lang": "vi",
    "message": "request thành công."
}

"""

********************************** Update Card To Profile******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/internal/v3.0/merchants/<merchant-id>/profile/<profile_id>/card/<id> Update Card To Profile.
@apiDescription Cập nhật hạng thẻ của Profile
@apiGroup ProfilingLoyalty
@apiVersion 1.0.0
@apiName UpdateCardToProfile

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    card_id              ID của hạng thẻ
@apiParam       (Body:)     {String}    card_code            Mã thẻ
@apiParam       (Body:)     {String}    card_name            Tên thẻ

@apiParamExample  {json} Body request example
{
  "card_code": "ABCD12345678",
  "card_id": "6e8b53c9-3d32-4d6f-8844-e44ac5a65ba5"
  "card_name": "Thẻ vàng"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
      "id": "uid",
      "card_id": "6e8b53c9-3d32-4d6f-8844-e44ac5a65ba5",
      "card_name": "Thẻ vàng"
      "card_code": "ABCD12345678"
    },
    "lang": "vi",
    "message": "request thành công."
}

"""

********************************** Update Card To Profile******************************
* version: 1.0.1                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/internal/v3.0/merchants/<merchant-id>/profile/<profile_id>/card/<id> Update Card To Profile.
@apiDescription Cập nhật hạng thẻ của Profile
@apiGroup ProfilingLoyalty
@apiVersion 1.0.1
@apiName UpdateCardToProfile

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    card_id              ID của hạng thẻ
@apiParam       (Body:)     {String}    card_code            Mã thẻ
@apiParam       (Body:)     {String}    card_name            Tên thẻ
@apiParam       (Body:)     {bool}    [is_primary]            Thẻ này có phải thẻ chính của Profile này không?
@apiParam       (Body:)     {Number}    [status]            Trạng thái của thẻ.
<code>
<li>0: "Khóa thẻ"</li>
<li>1: "Đã duyệt"</li>
<li>2: "Chờ duyệt"</li>
<li>3: "Hết hạn"</li>
<li>4: "Chưa có thẻ"</li>
<li>-1: "Hủy thẻ"</li>
</code>
@apiParam       (Body:)     {String}    expiry_time            Thời hạn sử dụng thẻ. FORMAT: 'yyyy-MM-dd hh:mm:ss'.

@apiParamExample  {json} Body request example
{
  "card_code": "ABCD12345678",
  "card_id": "6e8b53c9-3d32-4d6f-8844-e44ac5a65ba5"
  "card_name": "Thẻ vàng",
  "is_primary": false,
  "status": 1,
  "expiry_time": "2029-12-30 23:59:59"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
      "id": "uid",
      "card_id": "6e8b53c9-3d32-4d6f-8844-e44ac5a65ba5",
      "card_name": "Thẻ vàng"
      "card_code": "ABCD12345678",
      "is_primary": false,
      "status": 1,
      "expiry_time": "2029-12-30 23:59:59"
    },
    "lang": "vi",
    "message": "request thành công."
}

"""

***********************************Loyalty Update Profile******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/internal/v3.0/merchants/<merchant-id>/profile/<profile_id> Loyalty Update Profile.
@apiDescription Cập nhật thông tin Profile
@apiGroup ProfilingLoyalty
@apiVersion 1.0.0
@apiName LoyaltyUpdateProfile

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    [name]              Tên
@apiParam       (Body:)     {String}    [phone_number]            Số điện thoại
@apiParam       (Body:)     {String}    [email]            Email
@apiParam       (Body:)     {Object}    [social_user]            Thông tin Social
@apiParam       (Body:)     {Array}    [profile_identify]            Các thông tin đinh danh Profile
@apiParam       (Body:)     {String}    [avatar]            Ảnh đại diện
@apiParam       (Body:)     {String}    [address]           Địa chỉ
@apiParam       (Body:)     {String}    [birthday]             Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam       (Body:)     {Int}       [gender]             Giới tính.
<li><code>1: UNKNOWN</code></li>
<li><code>2: MALE</code></li>
<li><code>3: FEMALE</code></li>

@apiParam       (Social_User:)     {String}    social_id            Id của mạng xã hội
@apiParam       (Social_User:)     {String}    social_type          Kiểu mạng xã hội
@apiParam       (Social_User:)     {String}    [page_id]            Page Id.
@apiParam       (Social_User:)     {String}    [app_id]            App Id
<li><code>1: Facebook</code></li>
<li><code>2: Zalo</code></li>
<li><code>3: Instagram</code></li>
<li><code>4: Youtube</code></li>

@apiParam       (profile_identify:)     {String}    identify_value           Số giấy tờ.
@apiParam       (profile_identify:)     {String}    identify_type            Loại giấy tờ. (GPLX, Passport, CMND, ...)
@apiParam       (profile_identify:)     {Bool}    is_verify                  Đã được xác minh hay chưa?
@apiParam       (profile_identify:)     {DateTime}    date_verify            Ngày xác minh
@apiParam       (profile_identify:)     {String}    verify_by                Xác minh bởi?

@apiParamExample  {json} Body request example
{
  "name": "Nguyen Van A",
  "phone_number": "0987777777",
  "email": "<EMAIL>",
  "social_user": {
    "social_id":"1234567",
    "social_type": 1
  },
  "profile_identify": [
    {
      "identify_value": "123",
      "identify_type": "Passport",
      "is_verify": false,
      "date_verify": null,
      "verify_by": null
    }
  ],
  "avatar": "https://example.com/images/avatar.png"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "profile_id": "1033568d-c853-4d5c-b55b-461d237eea1a",
        "profile_info": {
            "id": "1033568d-c853-4d5c-b55b-461d237eea1a",
            "name": "Nguyen Van A",
            "phone_number": "0987777777",
            "email": "<EMAIL>",
            "social_user": [{
            "social_id":"1234567",
            "social_type": 1
            }],
            "profile_identify": [
              {
                "identify_value": "123",
                "identify_type": "Passport",
                "is_verify": false,
                "date_verify": null,
                "verify_by": null
              }
            ],
            "avatar": "https://example.com/images/avatar.png"
        },
        "message": "Cập nhật khách hàng thành công.",
        "process_type": "update"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

***********************************Loyalty Update Point******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/internal/v3.0/merchants/<merchant-id>/profile/<profile_id>/loyalty_point Loyalty Update Point.
@apiDescription Cập nhật Loyalty Point
@apiGroup ProfilingLoyalty
@apiVersion 1.0.0
@apiName LoyaltyUpdatePoint

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Object}    loyalty_point              Điểm Mobio Loyalty cần cập nhật

@apiParamExample  {json} Body request example
{
  "loyalty_point": {
    "point": 1000,
    "rank_point": 100
  }
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": true,
    "lang": "vi",
    "message": "request thành công."
}
"""

***********************************Loyalty Update Push ID******************************
* version: 1.0.0                                                                      *
* version: 1.0.1                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/internal/v3.0/loyalty/push_id Loyalty Update Push Id.
@apiDescription Cập nhật Loyalty PushID
@apiGroup ProfilingLoyalty
@apiVersion 1.0.0
@apiName LoyaltyUpdatePushId

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Array}    lst_profile              Danh sách các Profile cập nhật
@apiParam       (Profile:)     {String}    profile_id           ProfileId cần cập nhật
@apiParam       (Profile:)     {Array}    add                   Danh sách các PushID cần add vào Profile
@apiParam       (Profile:)     {Array}    remove                   Danh sách các PushID cần remove khỏi Profile

@apiParam       (Push_Id:)     {String}   push_id               PushId
@apiParam       (Push_Id:)     {Number}   os_type               Hệ điều hành
<li><code>1: IOS</code></li>
<li><code>2: Android</code></li>

@apiParamExample  {json} Body request example
{
  "lst_profile": [
    {
      "profile_id": "de89fead-02ee-4e2e-990c-86f0338fe480",
      "add": [
        {
          "push_id": "b65628cf-d9aa-4802-a4ad-09461c944709",
          "os_type": 1
        }
      ],
      "remove": [
        {
          "push_id": ""91fa75e1-294a-4c3c-a5fc-8354f5156c43"",
          "os_type": 2
        }
      ]
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": true,
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {put} [HOST]/profiling/internal/v3.0/loyalty/push_id Loyalty Update Push Id.
@apiDescription Cập nhật Loyalty PushID
@apiGroup ProfilingLoyalty
@apiVersion 1.0.1
@apiName LoyaltyUpdatePushId

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Array}    lst_profile              Danh sách các Profile cập nhật
@apiParam       (Profile:)     {String}    profile_id           ProfileId cần cập nhật
@apiParam       (Profile:)     {Array}    add                   Danh sách các Device cần add vào Profile
@apiParam       (Profile:)     {Array}    remove                   Danh sách các Device cần remove khỏi Profile

@apiParam       (Device:)     {String}   push_id               PushId
@apiParam       (Device:)     {String}   app_id               Id của app. VD: Sasuko App, Sunworld App
@apiParam       (Device:)     {String}   last_access               Lần cuối truy cập
@apiParam       (Device:)     {Number}   os_type               Hệ điều hành
<li><code>1: IOS</code></li>
<li><code>2: Android</code></li>

@apiParamExample  {json} Body request example
{
  "lst_profile": [
    {
      "profile_id": "de89fead-02ee-4e2e-990c-86f0338fe480",
      "add": [
        {
          "push_id": "b65628cf-d9aa-4802-a4ad-09461c944709",
          "app_id": "42269b26-edca-4c75-accc-a77f58eb799d",
          "last_access": "2020-02-02 23:59:59",
          "os_type": 1
        }
      ],
      "remove": [
        {
          "push_id": ""91fa75e1-294a-4c3c-a5fc-8354f5156c43"",
          "app_id": "ba6b64e2-cb85-4e34-b789-6f8ad43486bf",
          "last_access": "2029-12-30 23:59:59",
          "os_type": 2
        }
      ]
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": true,
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {put} [HOST]/profiling/internal/v3.0/loyalty/push_id Loyalty Update Push Id.
@apiDescription Cập nhật Loyalty PushID
@apiGroup ProfilingLoyalty
@apiVersion 1.0.2
@apiName LoyaltyUpdatePushId

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Array}    lst_profile              Danh sách các Profile cập nhật
@apiParam       (Profile:)     {String}    profile_id           ProfileId cần cập nhật
@apiParam       (Profile:)     {Array}    add                   Danh sách các Device cần add vào Profile
@apiParam       (Profile:)     {Array}    remove                   Danh sách các Device cần remove khỏi Profile

@apiParam       (Device:)     {String}   push_id               PushId
@apiParam       (Device:)     {Bool}   is_logged               Profile có đang đăng nhập hay không?
@apiParam       (Device:)     {String}   app_id               Id của app. VD: Sasuko App, Sunworld App
@apiParam       (Device:)     {String}   last_access               Lần cuối truy cập
@apiParam       (Device:)     {Number}   os_type               Hệ điều hành
<li><code>1: IOS</code></li>
<li><code>2: Android</code></li>

@apiParamExample  {json} Body request example
{
  "lst_profile": [
    {
      "profile_id": "de89fead-02ee-4e2e-990c-86f0338fe480",
      "add": [
        {
          "push_id": "b65628cf-d9aa-4802-a4ad-09461c944709",
          "app_id": "42269b26-edca-4c75-accc-a77f58eb799d",
          "is_logged": true,
          "last_access": "2020-02-02 23:59:59",
          "os_type": 1
        }
      ],
      "remove": [
        {
          "push_id": ""91fa75e1-294a-4c3c-a5fc-8354f5156c43"",
          "app_id": "ba6b64e2-cb85-4e34-b789-6f8ad43486bf",
          "is_logged": false,
          "last_access": "2029-12-30 23:59:59",
          "os_type": 2
        }
      ]
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": true,
    "lang": "vi",
    "message": "request thành công."
}
"""

*******************************Loyalty Add Merchant 2 Profile**************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/internal/v3.0/loyalty/add_merchant Add Merchant to Profile.
@apiDescription Api Add merchant vào profile khi có nghiệp vụ khách hàng tương tác ở sub-merchant
@apiGroup ProfilingLoyalty
@apiVersion 1.0.0
@apiName LoyaltyAddMerchant

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}   merchant_id               Merchant cần add vào Profile
@apiParam       (Body:)     {String}   profile_id                ProfileId

@apiParamExample  {json} Body request example
{
  "profile_id": "de89fead-02ee-4e2e-990c-86f0338fe480",
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": true,
    "lang": "vi",
    "message": "request thành công."
}
"""


********************** GET USER INFO BY FIELD AND FILTER ************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/systems/loyalty_strategy/users Lấy danh sách khách hàng.
@apiDescription API lấy danh sách khách hàng theo field và bộ lọc
@apiGroup Loyalty
@apiVersion 1.0.0
@apiName LoyaltyGetUsers

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse paging_tokens

@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} [business_case_id]                          ID business case
@apiParam (Query:) {Int} [personalized]                              <code>1</code> Cho phép lấy trường đã được cá nhân hóa

@apiParam      (Body:)     {String}    search                           Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>customer_id</code> hoặc <code>email</code>.

@apiParam      (Body:)     {Array}     fields                           Danh sách các field cần lấy. Gồm:<br/>
Allowed values: <br/>
<li><code>phone_number</code>
<li><code>email</code>
<li><code>name</code>
<li><code>social_user</code>
<li><code>gender</code>
<li><code>address</code>
<li><code>source_type</code>
<li><code>source_id</code>
<li><code>birthday</code>

@apiParam      (Body:)     {Array}     search_fields                           Danh sách các field cần tìm kiếm. Gồm:<br/>
Allowed values: <br/>
<li><code>phone_number</code>
<li><code>email</code>
<li><code>customer_id</code>
<li><code>cif</code>(Dành riêng cho Bank)

@apiUse critetia_key_body
@apiUse operator_key_body

@apiParam      (profile_filter:)     {Array}     values                  Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "fields": ["name", "phone_number", "email", "social_user", "gender", "address", "source_type", "source_id", "birthday"],
  "search_fields": ["email", "phone_number", "customer_id", "cif"],
  "search": "search_str",
  "profile_filter": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách thông tin user
@apiSuccess       {String}                                 signature         Chữ ký theo dữ liệu trả về

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "birthday": "1981-07-28",
      "created_time": "2018-07-27T09:53:21Z",
      "name": "Vũ Thị thọ 123",
      "email": "['<EMAIL>', '<EMAIL>']",
      "gender": 3,
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "phone_number": ["+************"],
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "updated_time": "2018-07-28T04:57:35Z",
      "source_type": 1,
      "source_id": "435ecfeb-e229-4076-806f-982580475e88"
    },
    ...
  ],
  "signature": "data signature",
}

"""
#!/usr/bin/python
# -*- coding: utf8 -*-
******************************* Get profile by profile_id without encrypt *************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/merchants/profile/profile-without-encrypt Lấy chi tiết profile không có mã hóa.
@apiDescription Lấy chi tiết profile không có mã hóa, đầu vào là danh sách profile_id
@apiGroup User
@apiVersion 1.0.0
@apiName GetProfileDetailWithoutEncrypt

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String[]}    profile_ids       Mảng profile_id
@apiParam      (Body:)     {Array}     [fields]        Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]

@apiParamExample  {json} Body request example
{
    "profile_ids": ["27aeaa12-2d72-4441-b888-b7c26d6903a0", "27aeaa12-2d72-4441-b888-b7c26d6903a1"],
    "fields": ["profile_id","name","birthday"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": ['<EMAIL>', '<EMAIL>'],
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": ["+************"],
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ],
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+84832201234", 
        "status": 0
      },
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+84832206789", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      },
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ],
      "tags": [],
      "degree": 1,
      "income_family": 0,
      "number_childs": 0
    }
  ]
}
"""
# -*- coding: utf-8 -*-
"""
Author: TruongNV
Company: MobioVN
Created Date: 31/07/2019
Describe:
"""


######################### Factory Trigger  ###########################
# version: 1.0.2
#################################################################
"""
@api {post} /profiling/internal/v3.0/factory/trigger/register Trigger - Register 
@apiDescription API dang ky trigger. 
<li>
<code>NOTE</code>: 
<li>Profiling chỉ lưu LOG trigger thời gian 3 tháng sau khi trigger được set "end_time", không thể traceback lại thông tin sau thời gian này</li>
<li>Đ<PERSON>i với <code>mobio-mkt</code>, trigger_id == campaign_id</li>
@apiVersion 1.0.2
@apiGroup Factory
@apiName Trigger Register

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)   {string}        merchant_id         UUID của merchant 
@apiParam (Body:)   {string}        trigger_id          ID unique cho trigger.
<li>
    Đối với <code>mobio-mkt</code>: trigger_id == campaign_id đối với MKT
</li>
@apiParam (Body:)   {string}        source              Nguồn đăng ký trigger
<li>
Allowed values: [<code>mobio-mkt</code>]
</li> 
@apiParam (Body:)   {string}        status              Trạng thái trigger
<li>
Allowed values: [<code>start</code>, <code>stop</code>]
</li> 
@apiParam (Body:)   {int}           start_time          Thời gian trigger bắt đầu thực hiện, timestamp lấy đến second 
@apiParam (Body:)   {int}           [end_time]          Thời gian trigger kết thúc, timestamp lấy đến second.
<li>
<code>NOTE:</code> nếu không truyền lên profiling sẽ set thời gian "end_time" = now + 10.000 year
</li> 
@apiParam (Body:)   {Object}        trigger_info        Thông tin cụ thể của trigger

@apiParam (Body:)   {Array[Object]}         trigger_info.filter_info                    Danh sách các filter lấy từ audience
@apiParam (Body:)   {Object}                trigger_info.call_back_info                 Thông tin để Factory có thể gửi profile call back trả về khi có action phù hợp
@apiParam (Body:)   {String}                trigger_info.call_back_info.web_hook        URL để profilingFactory call back khi có trigger event thoả mãn
<li>
<code>NOTE</code>: để thống nhất profiling chỉ gọi callback qua method <code>POST</code>
</li>
@apiParam (Body:)   {Object}    trigger_info.add_on_trigger                             Các điều kiện đặc biệt của trigger
@apiParam (Body:)   {String}    trigger_info.add_on_trigger.trigger_special_day         Thông tin về ngày cụ thể của trigger ngày đặc biệt. Format: <code>mm-dd</code>
@apiParam (Body:)   {Int}       trigger_info.add_on_trigger.trigger_birthday            Khoảng cách ngày trigger so với ngày sinh nhật, <code>trigger_day = birthday + trigger_birthday</code> 
@apiParam (Body:)   {Int}       trigger_info.add_on_trigger.trigger_wedding_day         Khoảng cách ngày so với ngày cưới, <code>trigger_day = wedding_day + trigger_wedding_day</code>
@apiParam (Body:)   {Int}       trigger_info.add_on_trigger.trigger_child_birthday      Khoảng cách ngày trigger so với ngày sinh con, <code>trigger_day = child_birthday + trigger_child_birthday</code>
@apiParam (Body:)   {String}    trigger_info.add_on_trigger.trigger_transaction_type    Loại trigger phát sinh giao dịch, in: <code>["first_transaction", "other_transaction", "all_transaction"]</code>
@apiParam (Body:)   {Array[String]}    trigger_info.add_on_trigger.trigger_transaction_trademark   Danh sách các IDs thương hiệu
@apiParam (Body:)   {Array[String]}    trigger_info.add_on_trigger.trigger_transaction_lst_category   Danh sách các IDs danh mục sản phẩm
@apiParam (Body:)   {Array[String]}    trigger_info.add_on_trigger.trigger_transaction_lst_item     Danh sách các IDs sản phầm
@apiParam (Body:)   {Float}    trigger_info.add_on_trigger.trigger_transaction_from_time     Thông tin về ngày bắt đầu tính trigger giao dịch
@apiParam (Body:)   {Float}    trigger_info.add_on_trigger.trigger_transaction_to_time      Thông tin về ngày cuối cùng tính trigger giao dịch
@apiParam (Body:)   {String}    trigger_info.add_on_trigger.trigger_social_like_channel     Kênh phát sinh lượt LIKE, in: <code>["Facebook", "Instagram", "Youtube"]</code>
@apiParam (Body:)   {Array[String]}    trigger_info.add_on_trigger.trigger_social_like_page     Danh sách IDs các page phát sinh lượt LIKE
@apiParam (Body:)   {String}    trigger_info.add_on_trigger.trigger_social_comment_channel      Kênh phát sinh lượt COMMENT, in: <code>["Facebook", "Instagram", "Youtube"]</code>
@apiParam (Body:)   {Array[String]}    trigger_info.add_on_trigger.trigger_social_comment_page  Danh sách IDs các page phát sinh lượt COMMENT
@apiParam (Body:)   {String}    trigger_info.add_on_trigger.trigger_social_msg_channel      Kênh phát sinh lượt MESSAGE, in: <code>["Facebook", "Zalo"]</code>
@apiParam (Body:)   {Array[String]}    trigger_info.add_on_trigger.trigger_social_msg_page  Danh sách IDs các page phát sinh lượt MESSAGE
@apiParam (Body:)   {Array[String]}    trigger_info.add_on_trigger.trigger_touch_tag    Danh sách Tags phát sinh tương tác
@apiParam (Body:)   {Int}       trigger_info.add_on_trigger.cycle_time      Thời gian một cycle hữu hiệu (second), <code>default=24*60*60</code>     

@apiParam (Body:)   {Object}        [profile_scan]          Thông tin cá nhân hoá
<li>DOC:
  <a href="https://dev.mobio.vn/docs/profiling_internal/#api-MKT-MKTGetUsers">
    https://dev.mobio.vn/docs/profiling_internal/#api-MKT-MKTGetUsers
  </a>
</li>  
@apiParam      (Body:)       {Array[Object]}     profile_scan.field_response_adding            Danh sách các field cần tính toán: Dành riêng cho MKT
@apiParam      (Body:)       {Array[Object]}     profile_scan.field_conditions                 Danh sách các field điều kiện
@apiParam      (Body:)       {String}    profile_scan.field_conditions.field_name    Tên trường cá nhân hóa
@apiParam      (Body:)       {String}    profile_scan.field_conditions.field_origin   Trường gốc
@apiParam      (Body:)       {Array[Object]}     profile_scan.field_conditions.filter          Filter tính ra giá trị của trường cá nhân hóa; Xem dữ liệu mô tả trong api <code>Profiling</code> > <code>VIB</code> > <code>Danh sách trường gốc</code>
@apiParam      (Body:)       {Array[Object]}     profile_scan.field_counts                     Danh sách các field cần tính toán
@apiParam      (Body:)       {String}    profile_scan.field_counts.field_name         Tên trường cá nhân hóa
@apiParam      (Body:)       {String}    profile_scan.field_counts.function_math      Biểu thức tính toán ra giá trị trường cá nhân hóa
@apiParam (Body:)   {Array[String]}        [need_adding_response]       Danh sách các field cơ bản của PROFILING cần thêm vào JSON callback 

@apiSuccess                   {int}                 code                                Mã response
@apiSuccess                   {text}                lang                                Ngôn ngữ hiển 
@apiSuccess                   {text}               message                             Thông tin từ hệ thống



@apiParamExample    {json}      Body example:
{
  "merchant_id": "xxxx-xxxx-xxxxx-xxxx",
  "start_time": 1564632921,
  "end_time": 1564632921,
  "status": "start",
  "trigger_info": {
    "call_back_info": {
        "web_hook": "https://test.com"
    },
    "filter_info": [
        {
          "criteria_key": "cri_gender",
          "operator_key": "op_is_in",
          "values": [
            "2",
            "3"
          ]
        },
        {
          "criteria_key": "cri_birthday_period",
          "operator_key": "op_is_equal",
          "values": [
            "1"
          ]
        }
    ],
    "add_on_trigger": {
        "trigger_special_day": 22,
        "trigger_special_month": 8,
        "cycle_time": 24
    }
  },
  "profile_scan" : {
    "field_response_adding": ["ten_truong_ca_nhan_hoa_1", "ten_truong_ca_nhan_hoa_2"],
    "field_conditions": [
        {
            "field_name": "ten_truong_ca_nhan_hoa_1",
            "field_origin": "tong_chi_tieu_tich_luy",
            "filter": [
                {
                  "criteria_key": "cri_vib_card_type",
                  "operator_key": "op_is_equal",
                  "values": ["dac00637-b1f7-4285-9b3f-1075d3058982"]
                }
            ]
        }
    ],
    "field_counts": [
        {
            "field_name": "ten_truong_ca_nhan_hoa_2",
            "function_math": "15 - ten_truong_ca_nhan_hoa_1"
        }
    ],
  },
  "need_adding_response": ["field_001", "field_002"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "code": 200,
   "lang": "vi",
   "message": "request thành công"
}
"""


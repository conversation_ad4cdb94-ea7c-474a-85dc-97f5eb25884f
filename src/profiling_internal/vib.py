*********************************** Upsert Product ************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/bank/products Upsert Product VIB
@apiDescription Upsert Product VIB
@apiGroup VIB
@apiVersion 1.0.0
@apiName UpsertProduct

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    name              Tê<PERSON> c<PERSON><PERSON> sản phẩm
@apiParam       (Body:)     {String}    term              Mứ<PERSON> k<PERSON> h<PERSON>n

@apiParamExample  {json} Body request example
{
    "name": "IDC",
    "term": "7D"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "5e901a7afb403a80e29678e7",
        "created_time": "2020-03-24T11:54:22.000Z",
        "name": "IDC",
        "term": "7D",
        "status": 1,
        "updated_time": "2020-04-09T05:14:46.000Z"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

*********************************** Upsert MCC     ************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/bank/mcc Upsert MCC VIB
@apiDescription Upsert MCC VIB
@apiGroup VIB
@apiVersion 1.0.0
@apiName UpsertMCC

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Integer}   code              Mã MCC của VIB
@apiParam       (Body:)     {String}    name              Tên của sản phẩm

@apiParamExample  {json} Body request example
{
    "code": 123,
    "name": "MCC 1"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "5e901a7afb403a80e29678e7",
        "created_time": "2020-03-24T11:54:22.000Z",
        "name": "MCC 1",
        "code": 123,
        "status": 1,
        "updated_time": "2020-04-09T05:14:46.000Z"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
*********************************** Upsert Instalment Plan     ************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/bank/upsert_installment_plan Upsert Instalment Plan VIB
@apiDescription Upsert Instalment Plan VIB
@apiGroup VIB
@apiVersion 1.0.0
@apiName UpsertInstalmentPlan

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Integer}   code              Mã Instalment Plan của VIB
@apiParam       (Body:)     {String}    name              Tên của sản phẩm

@apiParamExample  {json} Body request example
{
    "code": 123,
    "name": "installment_plan 1"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "5e901a7afb403a80e29678e7",
        "created_time": "2020-03-24T11:54:22.000Z",
        "name": "installment_plan 1",
        "code": 123,
        "status": 1,
        "updated_time": "2020-04-09T05:14:46.000Z"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
****************************** Upsert Average Quarter Spending     ********************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/bank/upsert_average_quarter_spending Upsert average quarter spending
@apiDescription Upsert Average Quarter Spending 
@apiGroup VIB
@apiVersion 1.0.0
@apiName UpsertAverageQuarterSpending

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}   card_name              Tên card 
@apiParam       (Body:)     {String}    card_type_id              Card ID
@apiParam       (Body:)     {json}    quarter              Quý


@apiParamExample  {json} Body request example
{
    "card_name" : "MC VIB not HAPPYyyyyy",
    "card_type_id" : "e4528b0b-4ce5-45bc-8279-1a5078079971",
    "quarter" : {
        "Q12021" : 111111,
        "Q22021" :111111,
        "Q32021" : 111111,
        "Q42021" : 111111,
        "Q12022" : 111111,
        "Q22022" : 111111,
        "Q22023" : 111111
    }
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 UPDATE OK
{
    "card_name": "MC VIB not HAPPYyyyyy",
    "card_type_id" : "e4528b0b-4ce5-45bc-8279-1a5078079971",
    "code": 200,
    "lang": "vi",
    "message": "request thành công.",
    "quarter": {
        "Q12021": 111111,
        "Q12022": 111111,
        "Q22021": 111111,
        "Q22022": 111111,
        "Q22023": 111111,
        "Q32021": 111111,
        "Q42021": 111111
    }
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 INSERT OK
{
    "card_name": "MC VIB not HAPPYyyyyy",
    "card_type_id" : "e4528b0b-4ce5-45bc-8279-1a5078079971",
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
*********************************** Upsert Merchant ***********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/bank/merchants Upsert Merchant VIB
@apiDescription Upsert Merchant VIB
@apiGroup VIB
@apiVersion 1.0.0
@apiName UpsertMerchant

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Integer}    code             Mã của merchant
@apiParam       (Body:)     {String}    name              Tên của merchant

@apiParamExample  {json} Body request example
{
    "name": "Merchant 1",
    "code": 123
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "5e901a7afb403a80e29678e7",
        "created_time": "2020-03-24T11:54:22.000Z",
        "name": "Merchant 1",
        "status": 1,
        "updated_time": "2020-04-09T05:14:46.000Z",
        "code": 123 
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

*********************************** Upsert Card Status *************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/bank/upsert_card_status Upsert Card Status
@apiDescription Upsert Card Status
@apiGroup VIB
@apiVersion 1.0.0
@apiName UpsertCardStatus

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    name Trạng thái của thẻ 

@apiParamExample  {json} Body request example
{
    "name": "NORM"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

*********************************** Upsert SMS Syntax *********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/bank/upsert_sms_syntax Upsert SMS Syntax
@apiDescription Upsert SMS Syntax
@apiGroup VIB
@apiVersion 1.0.0
@apiName UpsertSMSSyntax

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    content Cú pháp tin nhắn đăng ký

@apiParamExample  {json} Body request example
{
    "content": "DK 001"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
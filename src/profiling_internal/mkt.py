*********************************Get Profiles By Audience ID***************************
* version: 1.0.1                                                                      *
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/merchants/<merchant_id>/audience/<audience_id>/get_profiles Get Profiles By Audience ID.
@apiDescription L<PERSON><PERSON> danh s<PERSON>ch Profiles theo Audience
@apiGroup MKT
@apiVersion 1.0.0
@apiName GetProfilesByAudienceID

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String}  [after]     Chuỗi query after token
@apiParam   (Body:)    {Number}  [per_page]        Số bản ghi trên 1 trang. Max 1000, Default 200

@apiParamExample  {json} Body request example
{
  "after": "NWNmNzc5YTZiMWIzMzAwMzcwZmQ5MDg1",
  "per_page": 200
}



@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
      {
        "id": "profile_1",
        "name": "Nguyen Van A",
        "email": "<EMAIL>",
        "phone_number": "0987777777",
        "social_user": [
          {
            "social_id": "1234567",
            "social_type": 1
          }
        ],
        "avatar": "https://example.com/images/avatar,png"
      }
    ],
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh",
            "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 200,
        'page_count': 100,
        'total_count': 20000
    },
    "lang": "vi",
    "message": "request thành công."
}

"""
"""
@api {post} [HOST]/profiling/internal/v3.1/merchants/<merchant_id>/audience/<audience_id>/get_profiles Get Profiles By Audience ID.
@apiDescription Lấy danh sách Profiles theo Audience
@apiGroup MKT
@apiVersion 1.0.1
@apiName GetProfilesByAudienceID

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam       (Param:)     {String}    [info_paging]      info_paging=True khi cần thông tin để chia page như total_count, page_count, mặc định False

@apiParam   (Body:)    {String}  [after]     Chuỗi query after token
@apiParam   (Body:)    {Number}  [per_page]        Số bản ghi trên 1 trang. Max 1000, Default 200

@apiParamExample  {json} Body request example
{
  "after": "NWNmNzc5YTZiMWIzMzAwMzcwZmQ5MDg1",
  "per_page": 200
}



@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
      {
        "id": "profile_1",
        "name": "Nguyen Van A",
        "email": "<EMAIL>",
        "phone_number": "0987777777",
        "social_user": [
          {
            "social_id": "1234567",
            "social_type": 1
          }
        ],
        "avatar": "https://example.com/images/avatar,png"
      }
    ],
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh",
            "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 200,
        'page_count': 100,
        'total_count': 20000
    },
    "lang": "vi",
    "message": "request thành công."
}

"""

****************************** Criterial Key **********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@apiDefine critetia_key_body
@apiVersion 1.0.0
@apiParam   (profile_filter:) {String}    criteria_key    Các criteria key để filter khách hàng. Allowed values:<br/>
<li><code>cri_address</code>: Address
<li><code>cri_age</code>: Age
<li><code>cri_birthday</code>: Birthday
<li><code>cri_birthday_period</code>: Sinh nhật theo chu kỳ.
<li><code>cri_business_case</code>: Business case
<li><code>cri_card_level</code>: Loại thẻ
<li><code>cri_card_status</code>: Customer card status
<li><code>cri_city</code>: City
<li><code>cri_created_account_type</code>: Nguồn ghi nhận khách hàng
<li><code>cri_gender</code>: gender
<li><code>cri_hobby</code>: Hobby
<li><code>cri_job</code>: Job
<li><code>cri_marital_status</code>: Tình trạng hôn nhân
<li><code>cri_mkt_action_value</code>: Marketing action value
<li><code>cri_mkt_business_case_id</code>: MKT Business ID
<li><code>cri_mkt_campaign_id</code>: MKT Campaign ID
<li><code>cri_mkt_process_type</code>: Process type
<li><code>cri_mkt_root_process</code>: MKT root_process_id
<li><code>cri_mkt_step</code>: Loại khách hàng marketing
<li><code>cri_operation</code>: Lĩnh vực hoạt động
<li><code>cri_region</code>: Region
<li><code>cri_tags</code>: Lọc theo tags
<li><code>cri_created_time</code>: Lọc theo thời gian tạo profile
<li><code>cri_updated_time</code>: Lọc theo thời gian cập nhật profile
"""

****************************** Operator Key ***********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@apiDefine operator_key_body
@apiVersion 1.0.0
@apiParam   (profile_filter:) {String="op_is_between","op_is_greater_equal","op_is_in","op_is_equal","op_is_greater","op_is_has","op_is_has_not","op_is_less_equal","op_is_less","op_is_regex"}    operator_key    Các toán tử để filter khách hàng.
"""


****************************** Profile Scan ***********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@apiDefine profile_scan
@apiVersion 1.0.0
@apiParam      (Body:)     {Object}     [profile_scan]        Option dành riêng cho Marketing. Lưu ý khi có field này thì dữ liệu cần phải tính toán và trả profile qua luồng webhook. Xem chi tiết  Object<code>profiling_scan</code>

@apiParam      (profile_scan:)       {Array}     field_response_adding            Danh sách các field cần tính toán: Dành riêng cho MKT
@apiParam      (profile_scan:)       {Array}     field_conditions                 Danh sách các field điều kiện
@apiParam      (profile_scan:)       {String}     field_conditions.field_name    Tên trường cá nhân hóa
@apiParam      (profile_scan:)       {String}    field_conditions.field_origin   Trường gốc
@apiParam      (profile_scan:)       {Array}     field_conditions.filter          Filter tính ra giá trị của trường cá nhân hóa; Xem dữ liệu mô tả trong api <code>Profiling</code> > <code>VIB</code> > <code>Danh sách trường gốc</code>
@apiParam      (profile_scan:)       {Array}     field_counts                     Danh sách các field cần tính toán
@apiParam      (profile_scan:)       {String}    field_counts.field_name         Tên trường cá nhân hóa
@apiParam      (profile_scan:)       {String}    field_counts.function_math      Biểu thức tính toán ra giá trị trường cá nhân hóa
"""


********************** GET USER INFO BY FIELD AND FILTER ************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/systems/mkt_strategy/users Lấy danh sách khách hàng.
@apiDescription API lấy danh sách khách hàng theo field và bộ lọc
@apiGroup MKT
@apiVersion 1.0.0
@apiName MKTGetUsers

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse paging_tokens

@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} [business_case_id]                          ID business case
@apiParam (Query:) {Int} [personalized]                              <code>1</code> Cho phép lấy trường đã được cá nhân hóa

@apiParam      (Body:)     {String}    search                           Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]                 Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Body:)     {Array}     [profile_scan_filter]            Danh sách bộ lọc cần phải tính toán
@apiParam      (Body:)     {String}    [webhook]                        Webhook nhận dữ liệu sau khi tính toán xong. Bắt buộc khi có trường <code>profile_scan</code> hoặc <code>profile_scan_filter</code>
@apiParam      (Body:)     {String}    [campaign_id]                    Mã chiến dịch, required trong trường hợp có <code>profile_scan_filter</code> hoặc <code>profile_scan</code>
@apiParam      (Body:)     {String}    [profile_scan_unique]            Một thông điệp MKT khi gửi đi sẽ được <code>Unique</code> theo <code>profile_scan_unique</code> và <code>campaign_id</code>. Nếu không truyền <code>profile_scan_unique</code> thì cho phép gửi nhiều lần trên 1 campaign. 
                                                                        <br>Trường hợp khách hàng muốn nhận estimate số lượng profile thì <code>profile_scan_unique</code> là bắt buộc.

@apiParam      (Body:)     {Array}     fields                           Danh sách các field cần lấy. Gồm:<br/>
Allowed values: <br/>
<li><code>phone_number</code>
<li><code>email</code>
<li><code>name</code>
<li><code>social_user</code>
<li><code>gender</code>
<li><code>address</code>
<li><code>source_type</code>
<li><code>source_id</code>
<li><code>birthday</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiUse profile_scan

@apiParam      (profile_filter:)     {Array}     values                  Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "fields": ["name", "phone_number", "email", "social_user", "gender", "address", "source_type", "source_id", "birthday"],
  "search": "search_str",
  "profile_filter": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    }
  ],
  "profile_scan" : {
    "field_response_adding": ["ten_truong_ca_nhan_hoa_1", "ten_truong_ca_nhan_hoa_2"],
    "field_conditions": [
        {
            "field_name": "ten_truong_ca_nhan_hoa_1",
            // "field_type": "CONDITION",
            "field_origin": "tong_chi_tieu_tich_luy",
            "filter": [
                {
                  "criteria_key": "cri_vib_card_type",
                  "operator_key": "op_is_equal",
                  "values": ["dac00637-b1f7-4285-9b3f-1075d3058982"]
                }
            ]
        }
    ],
    "field_counts": [
        {
            "field_name": "ten_truong_ca_nhan_hoa_2",
            // "field_type": "COUNT",
            "function_math": "15 - ten_truong_ca_nhan_hoa_1"
        }
    ]
  },
  "profile_scan_filter": [],
  "profile_scan_unique": "dac00637-b1f7-4285-9b3f-1075d3058982",
  "webhook": "http://mkt"
}

@apiSuccess       {Array}                                  data              Mảng danh sách thông tin user
@apiSuccess       {String}                                 signature         Chữ ký theo dữ liệu trả về

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "birthday": "1981-07-28",
      "created_time": "2018-07-27T09:53:21Z",
      "name": "Vũ Thị thọ 123",
      "email": "['<EMAIL>', '<EMAIL>']",
      "gender": 3,
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "phone_number": ["+841215150001"],
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "updated_time": "2018-07-28T04:57:35Z",
      "source_type": 1,
      "source_id": "435ecfeb-e229-4076-806f-982580475e88"
    },
    ...
  ],
  "signature": "data signature",
}

"""


********************** GET USER INFO BY ID ************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/systems/mkt_strategy/users/<user_id> Lấy khách hàng theo id.
@apiDescription API lấy user theo id
@apiGroup MKT
@apiVersion 1.0.0
@apiName MKTGetUserInfo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} business_case_id                            ID business case
@apiParam (Query:) {Int} [personalized]                                 <code>1</code> Cho phép lấy trường đã được cá nhân hóa

@apiParam      (Body:)     {Array}     fields                           Danh sách các field cần lấy. Gồm:<br/>
Allowed values: <br/>
<li><code>phone_number</code>
<li><code>email</code>
<li><code>name</code>
<li><code>social_user</code>
<li><code>gender</code>
<li><code>address</code>
<li><code>source_type</code>
<li><code>source_id</code>
<li><code>birthday</code>

@apiParamExample    {json}    Body example:
{
  "fields": ["name", "phone_number", "email", "social_user", "gender", "address", "source_type", "source_id", "birthday"],
}

@apiSuccess       {Object}                                  data              Dữ liệu thông tin user
@apiSuccess       {String}                                 signature         Chữ ký theo dữ liệu trả về

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "address": "Tỉnh Thanh Hóa",
    "birthday": "1981-07-28",
    "created_time": "2018-07-27T09:53:21Z",
    "name": "Vũ Thị thọ 123",
    "email": "['<EMAIL>', '<EMAIL>']",
    "gender": 3,
    "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
    "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
    "phone_number": [
      "+841215150001"
    ],
    "social_user": [
      {
        "id_social": "2139374173003427",
        "social_type": 1
      }
    ],
    "updated_time": "2018-07-28T04:57:35Z",
    "source_type": 1,
    "source_id": "435ecfeb-e229-4076-806f-982580475e88"
  },
  "signature": "data signature"
}
"""

*************************** Estimate Customer *********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/merchants/<merchant_id>/customers/actions/estimate Estimate số lượng khách hàng
@apiDescription Dịch vụ estimate số lượng khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.0
@apiName CustomerEstimate

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Number}                                  estimate              Số lượng customer estimate.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
  "estimate": 5000
}
"""

********************* GET USER FOR MKT - MULTI PROFILE FILTER *******************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/systems/mkt_strategy/users_audiences Lấy danh sách khách hàng theo nhiều bộ lọc audiences
@apiDescription API lấy danh sách khách hàng theo nhiều bộ lọc audiences. Các biểu thức hỗ trợ: <code>Intersection (Int), Subtraction (Sub)</code>.
@apiGroup MKT
@apiVersion 1.0.0
@apiName MKTGetUsersAudiences

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse paging_tokens

@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} [business_case_id]                          ID business case
@apiParam (Query:) {Int} [personalized]                                 <code>1</code> Cho phép lấy trường đã được cá nhân hóa

@apiParam      (Body:)     {String}    search                           Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     fields                           Danh sách các field cần lấy. Gồm:<br/>
@apiParam      (Body:)     {String}     op                              Phép toán để tính toán trên tập hợp. Allowed values:<br/>
<li><code>int</code>: Phép giao, <b>không quan trọng thứ tự</b> của các tập hợp trong <code>profile_filters</code>.
<li><code>sub</code>: Phép trừ, api sẽ lấy phần từ đầu tiên trong <code>profile_filters</code> và trừ đi phần tử còn lại.
@apiParam      (Body:)     {Array}     profile_filters                  Danh sách các tập hợp điều kiện lọc. Các phần tử là mảng <code>profile_filter</code> như api <a href="#api-Customers-MKTGetUsers">Lấy danh sách khách hàng</a>
@apiParam      (Body:)     {Array}     [profile_scan_filter]            Danh sách bộ lọc cần phải tính toán
@apiParam      (Body:)     {String}    [webhook]                        Webhook nhận dữ liệu sau khi tính toán xong. Bắt buộc khi có trường <code>profile_scan</code> hoặc <code>profile_scan_filter</code>
@apiParam      (Body:)     {String}    [campaign_id]                    Mã chiến dịch, required trong trường hợp có <code>profile_scan_filter</code> hoặc <code>profile_scan</code>
@apiParam      (Body:)     {String}    [profile_scan_unique]            Một thông điệp MKT khi gửi đi sẽ được <code>Unique</code> theo <code>profile_scan_unique</code> và <code>campaign_id</code>. Nếu không truyền <code>profile_scan_unique</code> thì cho phép gửi nhiều lần trên 1 campaign.
                                                                        <br>Trường hợp khách hàng muốn nhận estimate số lượng profile thì <code>profile_scan_unique</code> là bắt buộc.

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                  Mảng các giá trị filter

@apiUse profile_scan

@apiParamExample    {json}    Body example:
{
  "fields": [
    "name",
    "phone_number",
    "email",
    "social_user",
    "gender",
    "address",
    "source_type",
    "source_id",
    "birthday"
  ],
  "search": "search_str",
  "profile_filters": [
    [
      {
        "criteria_key": "cri_age",
        "operator_key": "op_is_between",
        "values": [
          18,
          50
        ]
      }
    ],
    [
      {
        "criteria_key": "cri_age",
        "operator_key": "op_is_between",
        "values": [
          25,
          30
        ]
      }
    ]
  ],
  "op": "int",
  "profile_scan" : {
    "field_response_adding": ["ten_truong_ca_nhan_hoa_1", "ten_truong_ca_nhan_hoa_2"],
    "field_conditions": [
        {
            "field_name": "ten_truong_ca_nhan_hoa_1",
            // "field_type": "CONDITION",
            "field_origin": "tong_chi_tieu_tich_luy",
            "filter": [
                {
                  "criteria_key": "cri_vib_card_type",
                  "operator_key": "op_is_equal",
                  "values": ["dac00637-b1f7-4285-9b3f-1075d3058982"]
                }
            ]
        }
    ],
    "field_counts": [
        {
            "field_name": "ten_truong_ca_nhan_hoa_2",
            // "field_type": "COUNT",
            "function_math": "15 - ten_truong_ca_nhan_hoa_1"
        }
    ]
  },
  "profile_scan_filter": [],
  "profile_scan_unique": "dac00637-b1f7-4285-9b3f-1075d3058982",
  "webhook": "http://mkt"
}

@apiSuccess       {Array}                                  data              Mảng danh sách thông tin user

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "birthday": "1981-07-28",
      "created_time": "2018-07-27T09:53:21Z",
      "name": "Vũ Thị thọ 123",
      "email": "['<EMAIL>', '<EMAIL>']",
      "gender": 3,
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "phone_number": ["+841215150001"],
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "updated_time": "2018-07-28T04:57:35Z",
      "source_type": 1,
      "source_id": "435ecfeb-e229-4076-806f-982580475e88"
    },
    ...
  ]
}

"""
********************* ESTIMATE USER FOR MKT - MULTI AUDIENCE ********************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/systems/mkt_strategy/users_audiences/estimate Lấy số lượng khách hàng theo nhiều bộ lọc audiences
@apiDescription API lấy số lượng khách hàng theo nhiều bộ lọc audiences.
@apiGroup Customers
@apiVersion 1.0.0
@apiName MKTEstimateUsersAudiences

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} [business_case_id]                          ID business case

@apiParam      (Body:)     {String}    [search]                           Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     audience_ids                     Mảng các audience id cần lấy số lượng khách hàng

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "audience_ids": []
}

@apiSuccess       {Number}                                  estimate              Số lượng customer estimate.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
  "estimate": 5000
}

"""


********************** USER RANDOM FOR MKT - MULTI PROFILE FILTER ***************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/systems/mkt_strategy/random_users Lấy danh sách khách hàng ngẫu nhiên theo nhiều bộ lọc audiences
@apiDescription Lấy danh sách khách hàng ngẫu nhiên theo nhiều bộ lọc audiences. Các biểu thức hỗ trợ: <code>Intersection (Int), Subtraction (Sub), Union (Uni)</code>. Giới hạn lấy 50.000 bản ghi.
@apiGroup MKT
@apiVersion 1.0.0
@apiName MKTGetUsersRandomAudiences

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Query:)    {Number}    [per_page] Số phần tử trên một page.<br/> Example: <code>&per_page=5</code>. Mặc định per_page = 10
@apiParam   (Query:)    {String}    [after_token] Token để request lấy dữ liệu trang tiếp theo.
@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} [business_case_id]                          ID business case
@apiParam (Query:) {Int} [personalized]                                 <code>1</code> Cho phép lấy trường đã được cá nhân hóa

@apiParam      (Body:)     {String}    search                           Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     fields                           Danh sách các field cần lấy. Gồm:<br/>
@apiParam      (Body:)     {String}     op                              Phép toán để tính toán trên tập hợp. Allowed values:<br/>
<li><code>int</code>: Phép giao, <b>không quan trọng thứ tự</b> của các tập hợp trong <code>profile_filters</code>.
<li><code>sub</code>: Phép trừ, api sẽ lấy phần từ đầu tiên trong <code>profile_filters</code> và trừ đi phần tử còn lại.
<li><code>uni</code>: Phép hợp, <b>không quan trọng thứ tự</b> của các tập hợp trong <code>profile_filters</code>.
@apiParam      (Body:)     {Array}     profile_filters                  Danh sách các tập hợp điều kiện lọc. Các phần tử là mảng <code>profile_filter</code> như api <a href="#api-Customers-MKTGetUsers">Lấy danh sách khách hàng</a>
@apiParam      (Body:)     {Array}     seed                             Session trong random, khi mã seed thay đổi thì danh sách random thay đổi.



@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                  Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "fields": [
    "name",
    "phone_number",
    "email",
    "social_user",
    "gender",
    "address",
    "source_type",
    "source_id",
    "birthday"
  ],
  "search": "search_str",
  "profile_filters": [
    [
      {
        "criteria_key": "cri_age",
        "operator_key": "op_is_between",
        "values": [
          18,
          50
        ]
      }
    ],
    [
      {
        "criteria_key": "cri_age",
        "operator_key": "op_is_between",
        "values": [
          25,
          30
        ]
      }
    ]
  ],
  "op": "int",
  "seed": "fa10d9c2-027d-47c8-9d32-814809c90f3a"
}

@apiSuccess       {Array}                                  data              Mảng danh sách thông tin user

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "birthday": "1981-07-28",
      "created_time": "2018-07-27T09:53:21Z",
      "name": "Vũ Thị thọ 123",
      "email": "['<EMAIL>', '<EMAIL>']",
      "gender": 3,
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "phone_number": ["+841215150001"],
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "updated_time": "2018-07-28T04:57:35Z",
      "source_type": 1,
      "source_id": "435ecfeb-e229-4076-806f-982580475e88"
    },
    ...
  ]
}

"""


********************** USER RANDOM FOR MKT V2- MULTI PROFILE FILTER *************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/systems/mkt_strategy/webhook_random_users Lấy danh sách khách hàng ngẫu nhiên theo nhiều bộ lọc audiences trả về qua đường Webhook
@apiDescription Lấy danh sách khách hàng ngẫu nhiên theo nhiều bộ lọc audiences. Các biểu thức hỗ trợ: <code>Intersection (Int), Subtraction (Sub), Union (Uni)</code>. Giới hạn lấy 50.000 bản ghi.
@apiGroup MKT
@apiVersion 1.0.0
@apiName MKTWebhookUsersRandomAudiences

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Query:)    {Number}    [per_page] Số phần tử trên một page.<br/> Example: <code>&per_page=5</code>. Mặc định per_page = 10
@apiParam   (Query:)    {String}    [after_token] Token để request lấy dữ liệu trang tiếp theo.
@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} [business_case_id]                          ID business case
@apiParam (Query:) {Int} [personalized]                                 <code>1</code> Cho phép lấy trường đã được cá nhân hóa

@apiParam      (Body:)     {String}    search                           Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     fields                           Danh sách các field cần lấy. Gồm:<br/>
@apiParam      (Body:)     {String}     op                              Phép toán để tính toán trên tập hợp. Allowed values:<br/>
<li><code>int</code>: Phép giao, <b>không quan trọng thứ tự</b> của các tập hợp trong <code>profile_filters</code>.
<li><code>sub</code>: Phép trừ, api sẽ lấy phần từ đầu tiên trong <code>profile_filters</code> và trừ đi phần tử còn lại.
<li><code>uni</code>: Phép hợp, <b>không quan trọng thứ tự</b> của các tập hợp trong <code>profile_filters</code>.
@apiParam      (Body:)     {Array}     profile_filters                  Danh sách các tập hợp điều kiện lọc. Các phần tử là mảng <code>profile_filter</code> như api <a href="#api-Customers-MKTGetUsers">Lấy danh sách khách hàng</a>
@apiParam      (Body:)     {Array}     seed                             Session trong random, khi mã seed thay đổi thì danh sách random thay đổi.
@apiParam      (Body:)     {String}    webhook                          Đường dẫn nhận dữ liệu <code>required</code>.
@apiParam      (Body:)     {Number}    [total_profile]                  Số lượng profile muốn lấy. Nếu không có thì số lượng profile trả về bằng <code>per_page</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                  Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "fields": [
    "name",
    "phone_number",
    "email",
    "social_user",
    "gender",
    "address",
    "source_type",
    "source_id",
    "birthday"
  ],
  "search": "search_str",
  "profile_filters": [
    [
      {
        "criteria_key": "cri_age",
        "operator_key": "op_is_between",
        "values": [
          18,
          50
        ]
      }
    ],
    [
      {
        "criteria_key": "cri_age",
        "operator_key": "op_is_between",
        "values": [
          25,
          30
        ]
      }
    ]
  ],
  "op": "int",
  "seed": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
  "webhook": "https://mkt..."
}

@apiSuccess       {Array}                                  data              Mảng danh sách thông tin user

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_tyequirepe       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công!"
}
"""
******************************** Get profile exact cif ** *******************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/profile/search_exact_cif Search profile exact cif
@apiDescription Search profile exact cif
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName SearchProfileExactCif

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging


@apiParam      (Body:)        {String}    search                 Chuỗi tìm kiếm. Tìm theo <code>cif</code>.
@apiParam      (Body:)        {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Query:)       {String}    [sort]                 Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>


@apiParamExample    {json}    Body example:
{
  "search": "10123",
  "fields": ["profile_id","name","birthday"]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "c_data": [
        {
            "profile_id": "70a8fd6c-5609-438c-9cc3-fb571146dab9"
        }
    ],
    "code": 200,
    "data": [
        {
            "address": null,
            "created_time": "2022-12-05T07:03:40.719Z",
            "id": "70a8fd6c-5609-438c-9cc3-fb571146dab9",
            "cif": [
                "10123"
            ],
            "is_non_profile": false,
            "merchant_id": [
                "1da163aa-a831-46bb-9bbf-d62f553dcab5"
            ],
            "name": "test_123",
            "phone_number": [
                "+84334567891"
            ],
            "profile_address": [],
            "profile_id": "70a8fd6c-5609-438c-9cc3-fb571146dab9",
            "profile_identify": [],
            "profile_tags": [],
            "tags": [],
            "updated_time": "2022-12-07T07:20:22.298Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "",
            "before": ""
        },
        "page": 1,
        "page_count": 1,
        "per_page": 5,
        "total_count": 1
    }
}
"""

********************* Get profile by conditions ** **************************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/profile/find_by_conditions Tìm profile bằng với nhiều điều kiện và với nhau bằng mongo
@apiDescription Tìm profile bằng với nhiều điều kiện và với nhau bằng mongo
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName FindByConditions

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)        {array}    conditions                Danh sách các điều kiện tìm kiếm.
@apiParam      (conditions:)   {String}   [cif]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [email]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [phone_number]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [source]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [customer_id]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {Array}   [profile_identify]   Giá trị cần tìm kiếm.
@apiParam      (profile_identify:)   {String}   identify_type   driving_license, passport, identity_card, citizen_identity, identity_card_army, birth_certificate, visa, temporary_residence_card, other.
@apiParam      (profile_identify:)   {String}   identify_value   Giá trị cần tìm kiếm.
@apiParam      (Body:)        {Array}     fields               Danh sách các thuộc tính cần trả ra không có trả hết. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Body:)        {String}     source               call từ module nào

@apiParamExample    {json}    Body example:
{
    "conditions": [
        {
            "primary_email": "<EMAIL>",
            "cif": "20231173",
            "source": "Core",
            "profile_identify": [
                {
                    "identify_type": "citizen_identity",
                    "identify_value": "cccd123"
                }
            ]
        }
    ],
    "fields": [],
    "source": "Mobile"
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công.",
    "results": [
        {
            "request": {
                "cif": "20231173",
                "email": "<EMAIL>",
                "profile_identify": [
                    {
                        "identify_type": "citizen_identity",
                        "identify_value": "cccd123"
                    }
                ],
                "source": "Core"
            },
            "responses": [
                {
                    "cards": [],
                    "cif": [
                        "20231173"
                    ],
                    "created_time": "2023-12-20T07:39:13.044000Z",
                    "merchant_id": [
                        "57d559c1-39a1-4cee-b024-b953428b5ac8"
                    ],
                    "mkt_consent": "Có",
                    "name": "Hiendt371",
                    "predict": [
                        {
                            "confidence": 0.9929109216,
                            "key": "gender",
                            "value": "3"
                        }
                    ],
                    "primary_email": {
                        "detail": null,
                        "email": "<EMAIL>",
                        "last_check": "2023-12-20T07:39:13.043000Z",
                        "state": null,
                        "status": 0
                    },
                    "profile_group": [
                        "06b1b8b3-9bb5-4ba4-b70e-b2f5d472dc8f"
                    ],
                    "profile_id": "d7fdd2c6-e8b2-40bb-9322-6542e29b21fa",
                    "profile_identify": [
                        {
                            "date_verify": null,
                            "identify_type": "citizen_identity",
                            "identify_value": "cccd123",
                            "is_verify": false,
                            "verify_by": null
                        }
                    ],
                    "source": "Core",
                    "tracking_consent": "Có",
                    "updated_time": "2023-12-21T04:00:24.879000Z",
                }
            ]
        }
    ]
}
"""

********************* Get profile by satellite ** **************************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/profile/find_by_satellite Search profile by phone, email, customer_id,cif  By Mongo
@apiDescription search profile theo phone, email, customer_id, cif từ mongo 
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName SearchProfileByPhoneEmailCustomerIdCifName

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)        {dict}    condition                Điều kiện tìm kiếm.
@apiParam      (condition:)   {string}    find_by                Tìm theo <code>cif</code>, <code>phone_number</code> hoặc <code>customer_id</code> hoặc <code>email</code>.
@apiParam      (condition:)   {string}     value                 Mảng giá trị tối đa 5 giá trị
@apiParam      (Body:)        {Array}     [fields]               Danh sách các thuộc tính cần trả ra không có trả hết. Ví dụ: ["profile_id","name","birthday"]

@apiParamExample    {json}    Body example:
{
    "condition": {
        "find_by": "phone_number",
        "value": [
            "+84927024348","+84927024348","+84927024349","+84320000003"
        ]
    },
    "fields": [
        "_id", "name", "phone_number", "age", "profile_id", "cif", "email"
    ]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "_id": "65728d8631d4d34fcc2c8692",
            "address": null,
            "age": null,
            "cif": [
                "65656"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "9f78cca1-0a64-4b24-8ef0-1db4f65b3748",
            "name": "Trần Thị Lan 014",
            "phone_number": [
                "+84927024348"
            ],
            "profile_address": [],
            "profile_id": "9f78cca1-0a64-4b24-8ef0-1db4f65b3748",
            "profile_tags": [],
            "tags": []
        },
        {
            "_id": "65728d8631d4d34fcc2c8696",
            "address": null,
            "age": null,
            "cif": [
                "65655"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "a6cd9dd7-e6cf-4f9c-9b40-2bf64ba25efa",
            "name": "Trần Thị Lan 015",
            "phone_number": [
                "+84927024349"
            ],
            "profile_address": [],
            "profile_id": "a6cd9dd7-e6cf-4f9c-9b40-2bf64ba25efa",
            "profile_tags": [],
            "tags": []
        },
        {
            "_id": "655436190ee1877c7ab1c01e",
            "address": null,
            "age": null,
            "cif": [
                "65653"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "f8633fc3-18c6-401e-9ba6-df162ecfc8ae",
            "name": "Lypt 04",
            "phone_number": [
                "+84320000003"
            ],
            "profile_address": [],
            "profile_id": "f8633fc3-18c6-401e-9ba6-df162ecfc8ae",
            "profile_tags": [],
            "tags": []
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

********************* Get profile by multiple satellite ** **************************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/profile/find_by_multiple_satellite Search profile by multi condition in Mongo
@apiDescription search profile theo phone, email, customer_id, cif, profile_identify từ mongo 
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName SearchMultipleProfileByPhoneEmailCustomerIdCifName

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)        {Aray(dict)}    conditions                Điều kiện tìm kiếm.
@apiParam      (condition:)   {string}    find_by                Tìm theo 
                                                                    <ul>
                                                                        <li><code>phone</code></li>
                                                                        <li><code>email</code></li>
                                                                        <li><code>customer_id</code></li>
                                                                        <li><code>cif</code></li>
                                                                        <li><code>profile_identify</code></li>
                                                                        <li><code>source</code></li>
                                                                    </ul>
@apiParam      (condition:)   {Aray(string)}     value                 Mảng giá trị tối đa 5 giá trị
@apiParam      (condition:)   {Aray(string)}     type                  Loại giấy tờ định danh. Nếu có nhiều loại giấy tờ định danh sẽ or với nhau
                                                                    <ul>
                                                                      <li><code>citizen_identity: </code> CCCD </li>
                                                                      <li><code>identity_card: </code> CMND </li>
                                                                      <li><code>passport: </code> Hộ chiếu </li>
                                                                      <li><code>driving_license: </code> GPLX </li>
                                                                      <li><code>identity_card_army: </code> CMND Quân đội </li>
                                                                      <li><code>birth_certificate: </code> Giấy khai sinh </li>
                                                                      <li><code>visa: </code> VISA </li>
                                                                      <li><code>temporary_residence_card: </code> Thẻ tạm trú </li>
                                                                      <li><code>other: </code> Khác </li>
                                                                      <li><code>identity_card_police: </code> Giấy chứng minh Công an nhân dân </li>
                                                                      <li><code>resident_card: </code> Thẻ thường trú </li>
                                                                      <li><code>visa_exemption: </code> Giấy miễn thị thực </li>
                                                                    </ul>       
@apiParam      (Body:)        {Array}     [fields]               Danh sách các thuộc tính cần trả ra không có trả hết. Ví dụ: ["profile_id","name","birthday"]

@apiParamExample    {json}    Body example:
{
    "conditions": [
        {
            "find_by": "cif",
            "value": [
                "CIF1", "CIF2"
            ]
        },
        {
            "find_by": "profile_identify",
            "type": ["citizen_identity", "identity_card"]
            "value": [
                "CCCD1694", "CCCD1695"
            ]
        }
    ],
    "fields": [
        "_id", "name", "phone_number", "age", "profile_id", "cif", "email"
    ]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "_id": "65728d8631d4d34fcc2c8692",
            "address": null,
            "age": null,
            "cif": [
                "65656"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "9f78cca1-0a64-4b24-8ef0-1db4f65b3748",
            "name": "Trần Thị Lan 014",
            "phone_number": [
                "+84927024348"
            ],
            "profile_address": [],
            "profile_id": "9f78cca1-0a64-4b24-8ef0-1db4f65b3748",
            "profile_tags": [],
            "tags": []
        },
        {
            "_id": "65728d8631d4d34fcc2c8696",
            "address": null,
            "age": null,
            "cif": [
                "65655"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "a6cd9dd7-e6cf-4f9c-9b40-2bf64ba25efa",
            "name": "Trần Thị Lan 015",
            "phone_number": [
                "+84927024349"
            ],
            "profile_address": [],
            "profile_id": "a6cd9dd7-e6cf-4f9c-9b40-2bf64ba25efa",
            "profile_tags": [],
            "tags": []
        },
        {
            "_id": "655436190ee1877c7ab1c01e",
            "address": null,
            "age": null,
            "cif": [
                "65653"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "f8633fc3-18c6-401e-9ba6-df162ecfc8ae",
            "name": "Lypt 04",
            "phone_number": [
                "+84320000003"
            ],
            "profile_address": [],
            "profile_id": "f8633fc3-18c6-401e-9ba6-df162ecfc8ae",
            "profile_tags": [],
            "tags": []
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

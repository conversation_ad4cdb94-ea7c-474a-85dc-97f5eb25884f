***********************************Merchant Create Config******************************
* version: 1.0.0                                                                      *
* version: 1.0.1                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/merchants/<merchant_id>/config Merchant Create Config.
@apiDescription Merchant khởi tạo config
@apiGroup MerchantConfig
@apiVersion 1.0.0
@apiName MerchantCreateConfig

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Object}    business_case              Business Case
@apiParam       (Body:)     {String}    timezone          timezone
@apiParam       (Body:)     {Array}    parents            Danh sách các merchant Cha

@apiParam       (business_case:)     {String}    code            Code của Business Case
@apiParam       (business_case:)     {String}    name            Tên của Business Case
@apiParam       (business_case:)     {String}    [keyword]       Từ khóa của Business Case
@apiParam       (business_case:)     {String}    start           Thời gian bắt đầu của Business Case
@apiParam       (business_case:)     {String}    end             Thời gian kết thúc của Business Case
@apiParam       (business_case:)     {String}    is_default      Đặt Business Case làm mặc định

@apiParam       (parent:)     {String}    id            Id của merchant
@apiParam       (parent:)     {String}    name          Tên của merchant
@apiParam       (parent:)     {String}    avatar        Ảnh đại diện của merchant

@apiParamExample  {json} Body request example
{
  "business_case": {
    "name": "TEST",
    "code": "TEST"
    "start": "2019-03-31T10:30:50.980Z",
    "end": "2020-03-31T10:30:50.980Z",
    "is_default": True
  },
  "timezone": "Asia/Ho_Chi_Minh",
  "parents": [
    {
      "id": "1234567",
      "name": "parent merchant",
      "avatar": ""
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
                "business_case_id": inserted_business_case.id
            },
    "success": True,
    "lang": "vi",
    "message": "request thành công."
}

"""

"""
@api {post} [HOST]/profiling/internal/v3.0/merchants/<merchant_id>/config Merchant Create Config.
@apiDescription Merchant khởi tạo config
@apiGroup MerchantConfig
@apiVersion 1.0.1
@apiName MerchantCreateConfig

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    timezone          timezone
@apiParam       (Body:)     {Array}    parents            Danh sách các merchant Cha

@apiParam       (parent:)     {String}    id            Id của merchant
@apiParam       (parent:)     {String}    name          Tên của merchant
@apiParam       (parent:)     {String}    avatar        Ảnh đại diện của merchant

@apiParamExample  {json} Body request example
{
  "timezone": "Asia/Ho_Chi_Minh",
  "parents": [
    {
      "id": "1234567",
      "name": "parent merchant",
      "avatar": ""
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": True,
    "lang": "vi",
    "message": "request thành công."
}

"""

***********************************Merchant Update Config******************************
* version: 1.0.0                                                                      *
* version: 1.0.1                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/internal/v3.0/merchants/<merchant_id>/config Merchant Update Config.
@apiDescription Merchant cập nhật config
@apiGroup MerchantConfig
@apiVersion 1.0.0
@apiName MerchantUpdateConfig

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    timezone          timezone
@apiParam       (Body:)     {Array}    parents            Danh sách các merchant Cha

@apiParam       (parent:)     {String}    id            Id của merchant
@apiParam       (parent:)     {String}    name          Tên của merchant
@apiParam       (parent:)     {String}    avatar        Ảnh đại diện của merchant

@apiParamExample  {json} Body request example
{
  "timezone": "Asia/Ho_Chi_Minh",
  "parents": [
    {
      "id": "1234567",
      "name": "parent merchant",
      "avatar": ""
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": true,
    "lang": "vi",
    "message": "request thành công."
}

"""

"""
@api {put} [HOST]/profiling/internal/v3.0/merchants/<merchant_id>/config Merchant Update Config.
@apiDescription Merchant cập nhật config
@apiGroup MerchantConfig
@apiVersion 1.0.1
@apiName MerchantUpdateConfig

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    timezone          timezone
@apiParam       (Body:)     {Array}    parents            Danh sách các merchant Cha

@apiParam       (parent:)     {String}    id            Id của merchant
@apiParam       (parent:)     {String}    name          Tên của merchant
@apiParam       (parent:)     {String}    avatar        Ảnh đại diện của merchant

@apiParamExample  {json} Body request example
{
  "timezone": "Asia/Ho_Chi_Minh",
  "parents": [
    {
      "id": "1234567",
      "name": "parent merchant",
      "avatar": ""
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": true,
    "lang": "vi",
    "message": "request thành công."
}

"""


*******************************Merchant Update Enrich Fields***************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/internal/v3.0/merchants/<merchant_id>/config/enrich Merchant Update Enrich Fields.
@apiDescription System đăng ký những fields mà merchant này được enrich
@apiGroup MerchantConfig
@apiVersion 1.0.0
@apiName MerchantUpdateEnrichFields

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Array}    fields            Danh sách các fields merchant được Enrich

@apiParamExample  {json} Body request example
{
  "fields": [
    "gender",
    "birth_year",
    "hobby"
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": true,
    "lang": "vi",
    "message": "request thành công."
}
"""

*******************************Get List Criteria by Merchant***************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {get} [HOST]/profiling/internal/v3.0/merchants/criteria Get List Criteria by Merchant.
@apiDescription Lấy danh sách Criteria của Merchant
@apiGroup MerchantConfig
@apiVersion 1.0.0
@apiName GetCriteriaByMerchant

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": true,
    "lang": "vi",
    "message": "request thành công."
}
"""

********************************* Merchant List Group Field ***************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {get} [HOST]/profiling/internal/v3.0/merchant/groups/field Merchant List Group Field.
@apiDescription Api lấy danh sách các group field của Merchant.
@apiGroup MerchantConfig
@apiVersion 1.0.0
@apiName  MerchantListGroupField
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse order_sort
@apiParam   (Query:)    {String}  [search]     Tìm kiếm fields theo tên.

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
        "_id":"",
        "group_name": "Thông tin chung",
        "is_base": True,
        "group_index": 1,
        "merchant_id": "",
        "group_key": "information"
    },
    {
        "_id":"",
        "group_name": "Nhân khẩu học",
        "is_base": True,
        "group_index": 2,
        "merchant_id": "",
        "group_key": "demographic"
    },
    {
        "_id":"",
        "group_name": "group ba",
        "is_base": True,
        "group_index": 3,
        "merchant_id": "",
        "group_key": "group_ba"
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""


#!/usr/bin/python
# -*- coding: utf8 -*-

********************************* Merchant Add Field **********************************
                                                                                      *
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/merchant/field/add Merchant Add New Field.
@apiDescription API hỗ trợ Merchant tạo thêm mới field
@apiGroup Merchant Config
@apiVersion 1.0.0
@apiName  MerchantAddNewField
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String}         field_name        Tên field mới
@apiParam   (Body:)    {Number=1:Int,2:String,3:DateTime, 88:float_19}    field_property            Kiểu dữ liệu của field.
@apiParam   (Body:)    {String}         calculation_id      ID công thức module calculation field
@apiParam   (Body:)    {String}    format_value            Kiểu dữ liệu là số thì cần điền để xác định
 <li><code>positive_number: số dương</code></li>
<li><code>negative_number: số âm</code></li>
<li><code>all: cả âm dương</code></li>
@apiParam   (Body:)     {String}    display_type            Kiểu hiển thị dữ liệu.
<li><code>single_line: Single Line</code></li>
<li><code>multi_line: Multi Line</code></li>
<li><code>dropdown_single_line: Dropdown Single Line</code></li>
<li><code>dropdown_multi_line: Dropdown Multi Line</code></li>
<li><code>radio: Radio</code></li>
<li><code>checkbox: Checkbox</code></li>
<li><code>date_picker: Date Picker</code></li>
@apiParam   (Body:)     {Array}    [data_selected]         Dữ liệu mẫu của field. Dữ liệu được thêm vào field phải nằm trong data_selected mới đươc đưa vào field. Dữ liệu này là bắt buộc với các kiểu hiển thị: dropdown_single_line, dropdown_multi_line, radio, checkbox.
@apiParam   (Body:)     {String='dd/mm','dd/mm/yyyy','dd/mm/yyyy hh:mm','dd-mm-yyyy','dd-mm-yyyy hh:mm','mm/dd/yyyy','mm/dd/yyyy hh:mm'}    [format]                 Data format. Dùng khi kiểu dữ liệu là datetime
@apiParam   (Body:)     {String}    [translate_key]            key i18n.
@apiParam   (Body:)     {String}    [tooltip_i18n]            key i18n tooltip.
@apiParam   (Body:)     {String}    description            Mô tả ngắn.
@apiParam   (Body:)     {Bool}      display_detail         Hiển thị field này ở chi tiết profile.
@apiParam   (Body:)     {Bool}      display_in_form_add     Hiển thị field này ở popup cấu hình trường thông tin khi tạo
@apiParam   (Body:)     {Bool}      display_in_form_update  Hiển thị field này ở popup cấu hình trường thông tin khi sửa
@apiParam   (Body:)     {Bool}      display_in_import_file  Hiển thị field này ở popup tạo mới từ file
@apiParam   (Body:)     {Bool}      display_in_list_field   Hiển thị field này ở popup chọn thông tin hiển thị trong danh sách
@apiParam   (Body:)     {Bool}      display_in_filter       Hiển thị field này ở bộ lọc


@apiParamExample [json] Body example text single line:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "single_line",
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_import_file": true,
  "display_in_list_field": true,
  "display_in_filter": true,
  "description": "mo ta ngan cua field"
}

@apiParamExample [json] Body example text multi line:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "multi_line",
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_import_file": true,
  "display_in_list_field": true,
  "display_in_filter": true,
  "description": "mo ta ngan cua field"
}

@apiParamExample [json] Body example dropdown single line:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "dropdown_single_line",
  "data_selected": [
    {
      "id": 1,
      "name": "data 1",
      "display_in_form": true
    },
    {
      "id": 2,
      "value": "data 2",
      "display_in_form": false
    }
  ],
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_import_file": true,
  "display_in_list_field": true,
  "display_in_filter": true,
  "description": "mo ta ngan cua field"
}

@apiParamExample [json] Body example dropdown multi line:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "dropdown_multi_line",
  "data_selected": [
    {
      "id": 1,
      "name": "data 1",
      "display_in_form": true
    },
    {
      "id": 2,
      "name": "data 2",
      "display_in_form": false
    }
  ],
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_import_file": true,
  "display_in_list_field": true,
  "display_in_filter": true,
  "description": "mo ta ngan cua field"
}

@apiParamExample [json] Body example radio:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "radio",
  "data_selected": [
    {
      "id": 1,
      "name": "data 1",
      "display_in_form": true
    },
    {
      "id": 2,
      "name": "data 2",
      "display_in_form": false
    }
  ],
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_import_file": true,
  "display_in_list_field": true,
  "display_in_filter": true,
  "description": "mo ta ngan cua field"
}

@apiParamExample [json] Body example checkbox:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "checkbox",
  "data_selected": [
    {
      "id": 1,
      "name": "data 1",
      "display_in_form": true
    },
    {
      "id": 2,
      "name": "data 2",
      "display_in_form": false
    }
  ],
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_import_file": true,
  "display_in_list_field": true,
  "display_in_filter": true,
  "description": "mo ta ngan cua field"
}

@apiParamExample [json] Body example date_picker:
{
  "field_name": "field 1",
  "field_property": 1,
  "display_type": "date_picker",
  "format": "dd/mm/yyyy",
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_import_file": true,
  "display_in_list_field": true,
  "display_in_filter": true,
  "description": "mo ta ngan cua field"
}

@apiParam   (Response:)     {String}    field_name         Tên field.
@apiParam   (Response:)     {String}    field_key          Key định danh của field.
@apiParam   (Response:)     {String}    translate_key            key i18n.
@apiParam   (Response:)     {String}    tooltip_i18n            key i18n tooltip.
@apiParam   (Response:)     {Number}    field_property            Kiểu dữ liệu của field.
@apiParam   (Response:)     {Number}    order         thứ tự sắp xếp.
@apiParam   (Response:)     {String}    group         Nhóm của field.
@apiParam   (Response:)     {Bool}    is_base     Field này có phải field của hệ thống hay không?
@apiParam   (Response:)     {Bool}    display_detail                    Hiển thị field này ở chi tiết
@apiParam   (Response:)     {Bool}    display_in_form_add               Hiển thị field này ở popup cấu hình trường thông tin khi tạo
@apiParam   (Response:)     {Bool}    display_in_form_add_selected      Hiển thị field này ở popup tạo
@apiParam   (Response:)     {Number}    order_form_add                    Thứ tự hiển thị field trên form tạo
@apiParam   (Response:)     {Bool}    required_form_add                 Trường băt buộc trong form tạo
@apiParam   (Response:)     {Bool}    disable_remove_form_add           Không cho phép xóa ở form thêm
@apiParam   (Response:)     {Bool}    disable_required_form_add         Không cho phép required ở form thêm
@apiParam   (Response:)     {Bool}    display_in_form_update            Hiển thị field này ở popup cấu hình trường thông tin khi sửa
@apiParam   (Response:)     {Bool}    display_in_form_update_selected   Hiển thị field này ở popup sửa
@apiParam   (Response:)     {Bool}    order_form_update                 Thứ tự hiển thị field trên form sửa
@apiParam   (Response:)     {Bool}    required_form_update              Trường băt buộc trong form sửa
@apiParam   (Response:)     {Bool}    disable_remove_form_update        Không cho phép xóa ở form sửa
@apiParam   (Response:)     {Bool}    disable_required_form_update      Không cho phép required ở form sửa
@apiParam   (Response:)     {Bool}    display_in_import_file            Hiển thị field này ở popup tạo mới từ file
@apiParam   (Response:)     {Bool}    display_in_list_field             Hiển thị field này ở popup chọn thông tin hiển thị trong danh sách
@apiParam   (Response:)     {Bool}    display_in_list_field_selected    Hiển thị field này ở danh sách
@apiParam   (Response:)     {Bool}    order_list_field                  Thứ tự hiển thị field ở danh sách
@apiParam   (Response:)     {Bool}    disable_remove_list               Không cho phép xóa ở danh sách
@apiParam   (Response:)     {Bool}    display_in_filter                 Hiển thị field này ở bộ lọc
@apiParam   (Response:)     {Bool}    enable_export_file_profile_status                 Popup chọn thông tin hiển thị khi xuất file Profiles
@apiParam   (Response:)     {Bool}    enable_export_file_company_status                 Popup chọn thông tin hiển thị khi xuất file Công ty
@apiParam   (Response:)     {Bool}    enable_export_file_order_status                 Popup chọn thông tin hiển thị khi xuất file Cơ hội bán
@apiParam   (Response:)     {Bool}    enable_export_file_ticket_status                 Popup chọn thông tin hiển thị khi xuất file Ticket
@apiParam   (Response:)     {Bool}    is_change_group                  False là sẽ cố định group không chuyển
@apiParam   (Response:)     {Bool}    display_in_specific                  T/F Có hiển thị ở cấu hình cột 1 không
@apiParam   (Response:)     {Bool}    display_in_specific_selected                T/F - Có hiển thị ở cột 1 không
@apiParam   (Response:)     {Bool}    order_specific                 int - thứ tự hiển thị cột 1
@apiParam   (Response:)     {Bool}    sort_when_search                 False là không thể sắp xếp khi tìm kiếm
@apiParam   (Response:)     {Bool}    display_in_quickview                 T/F Có hiển thị ở cấu hình xem nhanh không (bên trái)
@apiParam   (Response:)     {Bool}    display_in_quickview_selected                 T/F - Có hiển thị ở xem nhanh không
@apiParam   (Response:)     {Bool}    order_quickview                 int - thứ tự hiển thị ở xem nhanh
@apiParam   (Response:)     {Bool}    display_in_abac_config                 T/F có xuất hiện ở config bộ lọc ABAC không
@apiParam   (Response:)     {Bool}    disable_abac_config                 T/F có xuất hiện ở config chức năng không


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "field_name": "field_1",
    "field_key": "_dyn_name_1558406233387",
    "field_property": 1,
    "display_type": "date_picker",
    "format": "dd/mm/yyyy",
    "order": 15,
    "group": "dynamic",
    "translate_key": "",
    "tooltip_i18n": "",
    "display_detail": true,
    "display_in_form_add": true,
    "display_in_form_add_selected": false,
    "order_form_add": 10,
    "required_form_add": false,
    "disable_remove_form_add": false,
    "disable_required_form_add": false,
    "display_in_form_update": true,
    "display_in_form_update_selected": false,
    "order_form_update": 10,
    "required_form_update": false,
    "disable_remove_form_update": false,
    "disable_required_form_update": false,
    "display_in_import_file": true,
    "display_in_list_field": true,
    "display_in_list_field_selected": false,
    "order_list_field": 10,
    "disable_remove_list": false,
    "display_in_filter": true,
    "is_base": false,
    "status": 1,
    "history": [
      {
        "created_time": "2019-03-27T17:52:46Z",
        "staff_id": "7df46d3b-98bd-4bf6-a8ac-1b7210297e54",
        "fullname": "MobioTest",
        "username": "admin@mobiotest"
      }
    ],
    "description": "mo ta ngan cua field",
    "created_time": "2019-03-27T17:52:46Z",
    "updated_time": "2019-03-27T17:52:46Z" 
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""
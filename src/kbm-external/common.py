
"""
@apiDefine KBMHeader
@apiHeader    (Headers)    {String}    Authorization     Chuỗi token hoặc API key để xác thực yêu cầu API.
@apiHeader    (Headers)    {String}    Path              Path domain call API.<br/>
                                                         Sử dụng để định danh site mà user đang truy cập. <br/>
                                                         <hr>
                                                         <PERSON><PERSON>u trúc chung:
                                                         <code>
                                                         <:domain>/<:language_key>/<:slug>/<:site_theme_key>/....
                                                         </code>
                                                         <br>
                                                         <PERSON>ô tả chi tiết params [tại đây](#api-KnowledgeBase-GetListKBM)


@apiHeaderExample Header Example
{
    "Path": "https://test28.kbm.io/vi/kb/default/articles/",
    "Authorization": "Basic 25630732-2976-444a-ba7a-3ca1b48fcc3a"
}
"""


"""
@apiDefine PagingNumberRequest

@apiParam   (Query:)      {Integer}            [page]              Vị trí page
@apiParam   (Query:)      {Integer}            [per_page]          Số bản ghi 1 page.
@apiParam   (Query:)      {String}             [order_by]          <PERSON><PERSON><PERSON> c<PERSON>u sắp xếp dữ liệu theo tiêu chí. Sử dụng: <code>&order_by=field_name</code>(sắp xếp dữ liệu theo <code>field_name</code>).
@apiParam   (Query:)      {String=asc,desc}    [order_type=asc]    Sắp xếp dữ liệu theo chiều tăng dần(<code>asc, A->Z</code>) hoặc theo chiều giảm dần(<code>desc, Z-A</code>)
"""


"""
@apiDefine PagingNumberResponse

@apiSuccess     {Object}    [paging]    					Thông tin phân trang.
@apiSuccess     {Int}    	[paging.page]    				Vị trí page request
@apiSuccess     {Int}    	[paging.per_page]    			Số lượng phần tử trên một page
@apiSuccess     {Int}    	[paging.total_item]    			Tổng số lượng phần tử


@apiSuccessExample {json} Paging example
{
    "paging": {
		"page": 1,
		"per_page": 1,
        "total_item": 15
    }
}
"""



# ================================================== ResponseCommonSuccessMessage ================================================== #
"""
@apiDefine APISuccessMessage
@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Object}            data                          Dữ liệu trả về
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
	"code": 200,
	"message": "request thành công.",
}
"""

# ================================================== ResponseCommonSuccess ================================================== #
"""
@apiDefine APISuccessCommon
@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Object}            data                          Dữ liệu trả về

"""


# ================================================== CommonApiSuccessExample ================================================== #
"""
@apiDefine APISuccessExampleCommon
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": ... // Xem Data Example,
}
"""

# ================================================== ResponseDetailArticle ================================================== #
"""
@apiDefine APISuccessDetailArticle

@apiSuccess     {String}            data.id                              <code>ID</code> của bài viết
@apiSuccess     {String}            data.folder_id                       <code>ID</code> thư mục đang chứa bài viết
@apiSuccess     {ArrayString}       [data.tag_ids]                       <code>ID</code> Tag
@apiSuccess     {ArrayObject}       [data.breadcrumb]                      Thông tin đường dẫn của thư mục bài viết
@apiSuccess     {String}            data.breadcrumb.id                   <code>ID</code> của thư mục
@apiSuccess     {String}            data.breadcrumb.title                Tên thư mục
@apiSuccess     {String}            data.breadcrumb.level                Level của thư mục. Thư mục gốc có <code>level=1</code>     
@apiSuccess     {Object}            [data.label]                         Nhãn hiển thị trên trang của người xem     
@apiSuccess     {String}            data.label.key                       Key của nhãn:
                                    		                                <ul>
                                    		                                  <li><code>new</code>: Mới</li>
                                    		                                  <li><code>new_update</code>: Vừa mới cập nhật</li>
                                    		                                </ul>
@apiSuccess     {Int}               data.label.expire_after_day           Số ngày nhãn được phép hiển thị trên trang của người đọc sau khi publish bài viết. Tối thiểu là <code>1</code> ngày
@apiSuccess     {String}            data.title                            Title của bài viết
@apiSuccess     {String}            data.slug                             Slug của bài viết
@apiSuccess     {Any}               data.raw_content                      Cấu trúc content của bài viết (do FE định nghĩa)
@apiSuccess     {String}            data.last_published_time              Thời gian bài viết được xuất bản
@apiSuccess     {String}            data.last_published_by                User xuất bản bài viết
"""

"""
@apiDefine APISuccessExampleDetailArticle
@apiSuccessExample  {json}  Data Example
{
	"data": {
		"id": "66ded37044ee506ef8663c75",
		"title": "13123",
		"slug": "13123",
		"tag_ids": [],
		"folder_id": "66cea2e3bacb857b6ef97182",
		"raw_content": null,
		"breadcrumb": [
			{
				"id": "66cea2e3bacb857b6ef97182",
				"title": "Thư mục 1",
				"level": 1
			}
		],
		"last_published_time": "2024-09-09T10:52:32.6Z",
		"last_published_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695"
	}
}
"""

# ================================================== SearchArticleRequest ================================================== #
"""
@apiDefine SearchArticleRequest
@apiParam       (Body:)     {String}            [search]                               Từ khóa tìm kiếm bài viết
@apiParam       (Body:)     {String}            [title]                                Từ khóa tìm kiếm bài viết theo title
@apiParam       (Body:)     {ArrayString}       [tag_ids]                              Danh sách <code>ID</code> tag bài viết cần tìm. Lấy danh sách [tại đây](#api-Tag-ListTag)


@apiParamExample    {json}  Body:
{
    "search": "Lorem",
}
"""

# ================================================== SearchArticleResponse ================================================== #
"""
@apiDefine SearchArticleResponse


@apiSuccess   	{String}         data.id                    						<code>ID</code> của bài viết.
@apiSuccess   	{String}         data.knowledge_base_id     					 	<code>ID</code> của knowledge base chứa bài viết.
@apiSuccess   	{String}         data.slug                  					 	Slug của bài viết.
@apiSuccess   	{String}         data.title                 					 	Tiêu đề của bài viết.
@apiSuccess   	{ArrayString}    data.tag_ids               					 	Danh sách <code>ID</code> của các tag được gán cho bài viết.
@apiSuccess   	{Object}         data.highlight             					 	Thông tin highlight cho các đoạn text tìm kiếm được.
@apiSuccess   	{ArrayString}    data.highlight.raw_text    					 	Danh sách các đoạn text được highlight theo từ khóa tìm kiếm.
@apiSuccess   	{ArrayString}    data.highlight.title       					 	Danh sách các đoạn tiêu đề được highlight theo từ khóa tìm kiếm.
@apiSuccess   	{Object}         data.highlight.published_attachments   		 	Thông tin highlight cho các tệp đính kèm đã xuất bản.
@apiSuccess   	{ArrayString}    data.highlight.published_attachments.filename   	Danh sách tên tệp đính kèm được highlight theo từ khóa tìm kiếm.



@apiSuccessExample    {json}  Data response:
{
    "data": {
        "id": "66e51b89b22f18e143163d59",
        "knowledge_base_id": "66e51b89b22f18e143163d59",
        "slug": "bai-viet-co-nhieu-noi-dung-tim-kiem",
        "tag_ids": [],
        "title": "Bai viết có nhiều nội dung tìm kiếm"
        "folder_id": "66e022fb870640dd8244269a",
        "breadcrumb": [{
            "id": "66e022fb870640dd8244269a",
            "title": "Thư mục 100",
            "level": 1
        }],
        "highlight": {
            "raw_text": [
                "Lịch sử tính toán: ghi nhận lịch sử các lần Refesh dữ liệu của <span class='highlight-kbm'>1</span> Segment.\n\n3."
            ]
        },
    }
}
"""
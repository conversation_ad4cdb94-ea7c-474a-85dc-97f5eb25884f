
# ---------- <PERSON><PERSON><PERSON> danh sách thư mục -----------
"""
@api {GET} {HOST}/knowledge-base/external/api/v1.0/folders  L<PERSON><PERSON> danh sách thư mục

@apiDescription API này trả về danh sách các thư mục dựa trên ID của thư mục cha và các tham số phân trang. Nếu không cung cấp ID của thư mục cha, API sẽ trả về danh sách tất cả các thư mục cấp đầu tiên.
@apiGroup Folder
@apiVersion 1.0.0
@apiName ListFolder

@apiUse   PagingNumberRequest

@apiParam   (Query:)  {String}   [parent_id]  <code>ID</code> của thư mục cha. Nếu muốn lấy danh sách thư mục gốc thì tru<PERSON>n <code>&parent_id=""</code>.

@apiSuccess   {Object}        data                   Dữ liệu trả về.
@apiSuccess   {ArrayObject}   data.results           Danh sách các thư mục.
@apiSuccess   {String}        data.results.id        <code>ID</code> của thư mục.
@apiSuccess   {String}        [data.results.parent_id] <code>ID</code> của thư mục cha. Nếu là thư mục gốc thì <code>parent_id=""</code>.
@apiSuccess   {String}        data.results.title     Tên của thư mục.
@apiSuccess   {String}        [data.results.description] Mô tả của thư mục.
@apiSuccess   {String}        data.results.language_key Mã ngôn ngữ của thư mục.
@apiSuccess   {Integer}       data.results.order     Vị trí của thư mục.
@apiSuccess   {Integer}       data.results.level     Cấp độ của thư mục tính từ thư mục gốc. Thư mục gốc có <code>level=1</code>.
@apiSuccess   {String}        [data.results.icon_url] URL của hình ảnh. Nếu không có, giá trị là chuỗi rỗng.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Request thành công",
  "data": {
    "results": [
      {
        "id": "66cea32bbacb857b6ef9718e",
        "parent_id": "66cea2e3bacb857b6ef97182",
        "title": "Thư mục 6",
        "description": "",
        "language_key": "vi",
        "order": 999,
        "level": 2,
        "icon_url": ""
      }
    ]
  },
  "paging": ... xem Paging example
}

@apiUse PagingNumberResponse


"""
# ---------- Chi tiết thư mục -----------
"""
@api {GET} {HOST}/knowledge-base/external/api/v1.0/folders/<folder_id>  Chi tiết thư mục
@apiDescription API lấy thông tin chi tiết thư mục
@apiGroup Folder
@apiVersion 1.0.0
@apiName DetailFolder

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess    {Number}               code                               Mã trạng thái HTTP của phản hồi.
@apiSuccess    {String}               message                            Thông điệp mô tả kết quả của yêu cầu.
@apiSuccess    {Object}               data                               Dữ liệu phản hồi.
@apiSuccess    {String}               data.id                            <code>ID</code> của thư mục.
@apiSuccess    {String}               [data.parent_id]                   <code>ID</code> của thư mục cha. Nếu là thư mục gốc thì <code>parent_id=""</code>.
@apiSuccess    {Object}               data                       Thông tin nội dung của thư mục.
@apiSuccess    {String}               data.title                 Tên của thư mục.
@apiSuccess    {String}               [data.description]         Mô tả của thư mục.
@apiSuccess    {String}               data.language_key          Mã ngôn ngữ của thư mục.
@apiSuccess    {Number}               data.order                         Thứ tự của thư mục.
@apiSuccess    {Number}               data.level                         Cấp độ của thư mục tính từ thư mục gốc.
@apiSuccess    {String}               [data.icon_url]                   URL của hình ảnh. Nếu không có, giá trị là chuỗi rỗng.
@apiSuccess    {ArrayObject}          data.breadcrumb                    Danh sách đường dẫn từ thư mục gốc đến thư mục cha.
@apiSuccess    {String}               data.breadcrumb.id                 <code>ID</code> của thư mục trong breadcrumb.
@apiSuccess    {String}               data.breadcrumb.title              Tên của thư mục trong breadcrumb.
@apiSuccess    {Number}               data.breadcrumb.level              Cấp độ của thư mục trong breadcrumb tính từ thư mục gốc.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "66cea32bbacb857b6ef9718e",
        "parent_id": "66cea2e3bacb857b6ef97182",
        "title": "Thư mục 6",
        "description": "",
        "language_key": "vi"
        "order": 999,
        "level": 2,
        "icon": {},
        "breadcrumb": [
            {
                "id": "66cea2e3bacb857b6ef97182",
                "title": "Thư mục 1",
                "level": 1
            }
        ]
    }
}

"""

# ---------- Lấy danh sách thư mục(tree) -----------
"""
@api {get} {HOST}/knowledge-base/external/api/v1.0/folders/actions/view-tree  Lấy danh sách thư mục (view tree)
@apiDescription API lấy danh sách tất cả thư mục có cùng thư mục cha.
<br/>Hiện tại, api sẽ trả về tất cả thư mục và không phân trang.

@apiGroup Folder
@apiVersion 1.0.0
@apiName ListFolderTree

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Query:)  {String}   parent_id  <code>ID</code> của thư mục cha. Nếu muốn lấy danh sách thư mục gốc thì truyền <code>&parent_id=""</code>.

@apiUse     APISuccessCommon

@apiSuccess   {String}           data.id                      <code>ID</code> của thư mục.
@apiSuccess   {String}           [data.parent_id]             <code>ID</code> của thư mục cha. Nếu là thư mục gốc thì <code>parent_id=""</code>.
@apiSuccess   {String}           data.title                   Tên của thư mục.
@apiSuccess   {String}           [data.description]           Mô tả của thư mục.
@apiSuccess   {String}           data.language_key            Mã ngôn ngữ của thư mục.
@apiSuccess   {Integer}          data.order                   Vị trí của thư mục.
@apiSuccess   {Integer}          data.level                   Cấp độ của thư mục tính từ thư mục gốc. Thư mục gốc có <code>level=1</code>.
@apiSuccess   {String}           [data.icon_url]              URL của hình ảnh. Nếu không có, giá trị là chuỗi rỗng.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "66cea2ffbacb857b6ef97184",
        "parent_id": "66cea2e3bacb857b6ef97182",
        "title": "Thư mục 2",
        "order": 999,
        "icon_url": "",
        "level": 2
    }
}

"""


# ---------- Lấy breadcrumb thư mục -----------
"""
@api {GET} {HOST}/knowledge-base/external/api/v1.0/folders/<folder_id>/breadcrumb  Lấy breadcrumb thư mục
@apiDescription API này trả về thông tin breadcrumb của một thư mục. Breadcrumb cung cấp thông tin về đường dẫn từ thư mục hiện tại đến thư mục gốc, giúp xác định vị trí của thư mục trong cấu trúc cây.
@apiGroup Folder
@apiVersion 1.0.0
@apiName GetBreadcrumbFolder

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess     {String}            message                         Mô tả phản hồi của API.
@apiSuccess     {Integer}           code                            Mã phản hồi của API.
@apiSuccess     {Object}            data                            Dữ liệu trả về từ API.
@apiSuccess     {ArrayObject}       data.breadcrumb                 Danh sách các thư mục trong đường dẫn breadcrumb.
@apiSuccess     {String}            data.breadcrumb.id              <code>ID</code> của mỗi thư mục trong breadcrumb.
@apiSuccess     {String}            data.breadcrumb.title           Tên của mỗi thư mục trong breadcrumb.
@apiSuccess     {Integer}           data.breadcrumb.level           Cấp độ của mỗi thư mục trong breadcrumb. Thư mục gốc có <code>level=1</code>.

@apiSuccessExample  {json}  Data Example
{
  "code": 200,
  "message": "Request thành công",
  "data": {
    "breadcrumb": [
      {
        "id": "2345cc4417a634e25f23",
        "title": "Tên cấp 2",
        "level": 2
      },
      {
        "id": "2345cc4417a634e25f24",
        "title": "Tên cấp 1",
        "level": 1
      }
    ]
  }
}
"""

# ---------- Lấy danh sách thư mục con hàng đầu và bài viết -----------
"""
@api {GET} {HOST}/knowledge-base/external/api/v1.0/folders/<folder_id>/top  Lấy danh sách top thư mục con và bài viết

@apiDescription API này trả về danh sách các thư mục con hàng đầu và bài viết trong một thư mục cụ thể. Nếu không cung cấp số lượng thư mục con hàng đầu, mặc định là 3.
@apiGroup Folder
@apiVersion 1.0.0
@apiName ListTopSubFolderAndArticle


@apiParam   {Integer}   [amount_top=3]  Số lượng thư mục, bài viết cần lấy. Nếu không cung cấp, mặc định là <code>3</code>. <code>Max=20</code>.

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiSuccess     {String}              message                       Mô tả phản hồi
@apiSuccess     {Integer}             code                          Mã phản hồi
@apiSuccess     {Object}              data                          Dữ liệu trả về
@apiSuccess     {ArrayObject}         data.folders                  Danh sách các thư mục con hàng đầu.
@apiSuccess     {String}              data.folders.id               <code>ID</code> của thư mục con.
@apiSuccess     {Integer}             data.folders.order            Vị trí của thư mục con.
@apiSuccess     {String}              data.folders.title            Tên của thư mục con.
@apiSuccess     {ArrayObject}         data.articles                 Danh sách các bài viết trong thư mục.
@apiSuccess     {String}              data.articles.id              <code>ID</code> của bài viết.
@apiSuccess     {String}              data.articles.title           Tiêu đề của bài viết.
@apiSuccess     {String}              data.articles.slug            Slug của bài viết.
@apiSuccess     {Integer}             data.total_folder             Tổng số thư mục con trong thư mục cha.
@apiSuccess     {Integer}             data.total_article            Tổng số bài viết trong thư mục.


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Request successful!!!",
  "data": {
    "folders": [
      {
        "id": "66cea2ffbacb857b6ef97184",
        "order": 999,
        "title": "Thư mục 2"
      },
      {
        "id": "66cea32bbacb857b6ef9718e",
        "order": 999,
        "title": "Thư mục 6"
      }
    ],
    "total_folder": 2,
    "articles": [
      {
        "id": "66e52ef9b22f18e143163d63",
        "slug": "nhung-bai-viet-lien-quan",
        "title": "Những bài viết liên quan"
      }
    ],
    "total_article": 7
  }
}

"""
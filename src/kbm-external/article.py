# ================================================== Get Detail article ================================================== #
"""
@api {GET} {domain}/knowledge-base/external/api/v1.0/articles/<article_id>/<article_slug>       Chi tiết bài viết
@apiGroup Article
@apiVersion 1.0.0
@apiName GetDetailArticle

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse APISuccessCommon
@apiUse APISuccessDetailArticle

@apiUse APISuccessExampleCommon
@apiUse APISuccessExampleDetailArticle
"""

# ================================================== List article ================================================== #
"""
@api {GET} {domain}/knowledge-base/external/api/v1.0/articles       Lấy danh sách bài viết
@apiGroup Article
@apiVersion 1.0.0
@apiName SearchListArticle

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse PagingNumberRequest

@apiParam   (Query:)  {String}   folder_id      <code>id</code> của thư mục chứa bài viết.

@apiUse APISuccessCommon
@apiSuccess     {String}       data.id                         <code>ID</code> của bài viết.
@apiSuccess     {String}       data.folder_id                  <code>ID</code> của thư mục chứa bài viết.
@apiSuccess     {String}       data.knowledge_base_id          <code>ID</code> của knowledge base chứa bài viết.
@apiSuccess     {Object}       [data.label]                     Nhãn hiện thị trên trang của người xem
@apiSuccess     {String}       data.label.key                   Key của nhãn:
                                                                <ul>
                                                                    <li><code>new</code>: Mới</li>
                                                                </ul>
                                                                <ul>
                                                                    <li><code>new_update</code>: Vừa mới cập nhật.</li>
                                                                </ul>
@apiSuccess     {Int}          data.label.expire_after_day      Số ngày nhãn được phép hiển thị trên trang của người đọc sau khi publish bài viết. Tối thiểu là <code>1</code> ngày
@apiSuccess     {String|null}  data.last_published_by          <code>ID</code> của người đã đăng bài viết lần cuối. Nếu không có, giá trị là <code>null</code>.
@apiSuccess     {String}       data.last_published_time        Thời gian bài viết được xuất bản. Định dạng: yyyy-MM-ddTHH:mm:ssZ
@apiSuccess     {String}       data.slug                       Slug của bài viết.
@apiSuccess     {ArrayString}  data.tag_ids                    Danh sách <code>ID</code> tag bài viết cần tìm. Lấy danh sách [tại đây](#api-Tag-ListTag)
@apiSuccess     {String}       data.title                      Tiêu đề của bài viết.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [{
            "folder_id": "66cea2ffbacb857b6ef97184",
            "id": "66e7d87944eb1f2548b446bc",
            "knowledge_base_id": "66e7d87944eb1f2548b446bc",
            "label": {
                "expire_after_day": 30,
                "key": "new_update"
            },
            "last_published_by": null,
            "last_published_time": "2024-09-16T07:10:43.173Z",
            "slug": "adasd",
            "tag_ids": [
                "66e52e97f187083c9787f0af",
                "66dec9d2bca5499b64e3fb47"
            ],
            "title": "adasd"
        },
        {
            "folder_id": "66cea2ffbacb857b6ef97184",
            "id": "66e7d795bf3dd6c295489ed5",
            "knowledge_base_id": "66e7d795bf3dd6c295489ed5",
            "label": {
                "expire_after_day": 30,
                "key": "new_update"
            },
            "last_published_by": null,
            "last_published_time": "2024-09-16T07:00:37.046Z",
            "slug": "adasd",
            "tag_ids": [
                "66e52e97f187083c9787f0af",
                "66dec9d2bca5499b64e3fb47"
            ],
            "title": "adasd"
        }
    ],
    "message": "Request successful!!!",
    "paging": {
        "page": 1,
        "per_page": 10,
        "total_item": 19
    }
}
"""

# ================================================== Search Article ================================================== #
"""
@api {POST} {domain}/knowledge-base/external/api/v1.0/articles/actions/search     Tìm kiếm bài viết
@apiGroup Article
@apiVersion 1.0.0
@apiName SearchArticle
@apiDescription Tìm kiếm bài viết

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse PagingNumberRequest
@apiUse SearchArticleRequest

@apiUse APISuccessCommon
@apiUse APISuccessExampleCommon
@apiUse SearchArticleResponse
@apiUse PagingNumberResponse

"""


# ================================================== Total article with search ================================================== #
"""
@api {POST} {domain}/knowledge-base/external/api/v1.0/articles/actions/total/search     Số lượng kết quả tìm kiếm bài viết
@apiGroup Article
@apiVersion 1.0.0
@apiName SearchTotalArticle
@apiDescription Số lượng kết quả tìm kiếm bài viết
@apiUse SearchArticleRequest


@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {Object}            data                            Dữ liệu trả về
@apiSuccess     {Int}               data.total                      Số lượng bài viết

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
	"code": 200,
	"message": "request thành công.",
    "data": {
        "total": 100
    }
}
"""
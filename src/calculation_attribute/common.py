#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 15/11/2024
"""

"""
@apiDefine calculation_attribute_order_sort
@apiParam   (Query:)    {String}    [order_by]  <PERSON><PERSON><PERSON> cầu sắp xếp dữ liệu theo tiêu chí. Sử dụng: <code>&sort=last_time_calculation</code>(sắp xếp dữ liệu theo <code>last_time_calculation</code>).
@apiParam   (Query:)    {String=asc,desc}    [order_type=asc]    Sắp xếp dữ liệu theo chiều tăng dần(<code>asc, A->Z</code>) hoặc theo chiều giảm dần(<code>desc, Z-A</code>)
"""
"""
@apiDefine filter_calculation_attribute
@apiParam   (Query:)    {String}    [order_by]  <PERSON><PERSON><PERSON> c<PERSON>u sắp xếp dữ liệu theo tiêu chí. <PERSON>ử dụng: <code>&sort=last_time_calculation</code>(sắp xếp dữ liệu theo <code>last_time_calculation</code>).
@apiParam   (Query:)    {String=asc,desc}    [order_type=asc]    Sắp xếp dữ liệu theo chiều tăng dần(<code>asc, A->Z</code>) hoặc theo chiều giảm dần(<code>desc, Z-A</code>)

@apiParam   (BODY:) {string}           [search]                         Từ khoá cần tìm kiếm
@apiParam   (BODY:) {array}            [list_status_connect]            Danh sách trạng thái kết nối của công thức.
                                                                        <ul>
                                                                            <li>ON: bật kết nối</li>
                                                                            <li>OFF: tắt kết nối</li>
                                                                        </ul>
                                                                        
@apiParam   (BODY:) {array}            [list_status_calculation]        Danh sách trạng thái xử lý của công thức.
                                                                        <ul>
                                                                            <li>PROCESSING: Đang xử lý</li>
                                                                            <li>FINISHED: Đã xử lý</li>
                                                                            <li>FAIL: không thành công</li>
                                                                        </ul>
@apiParam   (BODY:) {array}             [list_calculation_function]     Danh sách hàm tính toán.
                                                                        <ul>
                                                                            <li>SUM</li>
                                                                            <li>COUNT</li>
                                                                            <li>AVERAGE</li>
                                                                            <li>MIN</li>
                                                                            <li>MAX</li>
                                                                            <li>LAST</li>
                                                                            <li>FIRST</li>
                                                                        </ul>
@apiParam   (BODY:) {array}             [list_event_type]                Danh sách event_type.
                                                                        <ul>
                                                                            <li>DYNAMIC_EVENT</li>
                                                                            <li>BASE_EVENT</li>
                                                                            <li>TRACKING_EVENT</li>
                                                                        </ul>
@apiParam   (BODY:) {object}            [range_time_last_calculation]       Bộ lọc thời gian tính toán gần nhất
@apiParam   (BODY:) {string}            [range_time_last_calculation.start_time]          Thời gian bắt đầu. (%Y-%m-%dT%H:%MZ)
@apiParam   (BODY:) {string}            [range_time_last_calculation.end_time]            Thời gian kết thúc. (%Y-%m-%dT%H:%MZ)
@apiParam   (BODY:)  {object}           [formula_validity_period]                               Bộ lọc thời gian công thức có hiệu lực.
@apiParam   (BODY:)  {array(string)}           [formula_validity_period.type]                    Loại cấu hình thời gian có hiệu lực của công thức. Định dạng %Y-%m-%D
                                                                                                <ul>
                                                                                                    <li>UNLIMITED_DURATION: không có thời gian kết thúc</li>
                                                                                                    <li>LIMITED_DURATION: Có thời gian kết thúc</li>
                                                                                                </ul>
@apiParam   (BODY:)  {string}           [formula_validity_period.end_time]                      Thời gian kết thúc trong trường hợp có thời gian kết thúc. (%Y-%m-%dT%H:%MZ)
@apiParam   (BODY:)  {string}           [formula_validity_period.start_time]                    Thời gian bắt đầu trong trường hợp có thời gian kết thúc. (%Y-%m-%dT%H:%MZ)

@apiParamExample {json} Body:
{
    "search": "",
    "formula_validity_period": {
        "type": ["UNLIMITED_DURATION"],
    },
    "list_status_connect": [
        "ON",
        "OFF"
    ],
    "list_status_calculation": [
        "PROCESSING",
        "FINISHED",
        "FAIL"
    ],
    "list_calculation_function": [
        "SUM",
        "COUNT",
        "AVERAGE",
        "MIN",
        "MAX",
        "LAST",
        "FIRST"
    ],
    "list_event_type": [
        "DYNAMIC_EVENT",
        "BASE_EVENT",
        "TRACKING_EVENT"
    ],
    "range_time_last_calculation": {
        "start_time": "2021-01-01T00:00Z",
        "end_time": "2021-01-01T00:00Z"
    }
}
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 04/11/2024
"""
"""
@apiDefine DetailCalculation

@apiSuccess     {String}            message                         <PERSON><PERSON> tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {String}            data.id                         <code>ID</code> c<PERSON><PERSON> công thức
@apiSuccess     {string}            data.title                      Tên công thức
@apiSuccess     {String}            [data.description]              M<PERSON> tả công thức  
@apiSuccess     {String}            data.created_by                  ID người tạo
@apiSuccess     {String}            data.created_time                Thời gian tạo (định dạng ISO)
@apiSuccess     {String}            data.updated_by                  ID người cập nhật
@apiSuccess     {String}            data.updated_time                Thời gian cập nh<PERSON>t (định dạng ISO)
@apiSuccess     {string}            data.object_generated           Đ<PERSON>i tượng phát sinh (vd: profiles)
@apiSuccess     {string}            data.keyword                     Từ khóa tìm kiếm
@apiSuccess     {boolean}           data.is_stop_calculate_schedule  Trạng thái dừng tính toán theo lịch
@apiSuccess     {string}            [data.last_calculation_time]     Thời gian tính toán gần nhất
@apiSuccess     {string}            [data.last_session_id]           ID phiên tính toán gần nhất
@apiSuccess     {string}            data.merchant_id                 ID của merchant
@apiSuccess     {string}            data.next_process_time           Thời gian xử lý tiếp theo
@apiSuccess     {number}            data.number_of_consecutive_fail  Số lần tính toán thất bại liên tiếp
@apiSuccess     {string}            data.status_connect              Trạng thái kết nối (ON/OFF)
@apiSuccess     {Object}            data.config_filter              Cấu hình điều kiện Filter
@apiSuccess     {string}            data.config_filter.type          Kiểu filter
                                                                    <ul>
                                                                        <li><code>ALL</code>:: tất cả tập dữ liệu</li>
                                                                        <li><code>CUSTOM</code>:: chạy với tập segment cụ thể</li>
                                                                    </ul>
@apiSuccess     {Object}            data.config_filter.detail        Chi tiết của kiểu Filter
@apiSuccess     {string}            data.config_filter.detail.type   kiểu
                                                                <ul>
                                                                    <li><code>SEGMENT</code>: sử dụng segment</li>
                                                                </ul>
@apiSuccess     {string}            data.config_filter.detail.value  Giá trị của loại tương ứng. <b>trong trường hợp sử dụng segment thì tương ứng sẽ là lưu segment_id</b>
@apiSuccess     {string}            data.config_filter.detail.audience_id  Giá trị của audience_id tương ứng.
@apiSuccess     {string}            data.calculation_method          Phương thức tính toán
                                                                 <ul>
                                                                     <li><code>AGGREGATION</code>: tổng hợp</li>
                                                                     <li><code>COUNT_NUMBER_OF_EXECUTIONS</code>: đếm số lần thực hiện</li>
                                                                     <li><code>FREQUENCY</code>: tần suất</li>
                                                                     <li><code>CUSTOM</code>: công thức tuỳ chỉnh</li>
                                                                 </ul>
@apiSuccess     {object}            data.config_model                Cấu hình model
@apiSuccess     {string}            data.config_model.event_key      Định danh Key của event
@apiSuccess     {string}            data.config_model.event_device_type     Loại thiết bị event. Giá trị: <code>MOBILE</code>, <code>WEB</code>, <code>ALL</code>
@apiSuccess     {string}            data.config_model.event_type     Loại event. Giá trị: <code>DYNAMIC_EVENT</code>, <code>BASE_EVENT</code>, <code>TRACKING_EVENT</code>
@apiSuccess     {string}            data.config_model.audience_id    Định danh id của audience
@apiSuccess     {string}            data.config_model.function       Hàm sử dụng
                                                                    <ul>
                                                                        <li><code>SUM</code>: tổng</li>
                                                                        <li><code>AVERAGE</code>: bình quân</li>
                                                                        <li><code>MIN</code>: nhỏ nhất</li>
                                                                        <li><code>MAX</code>: lớn nhất</li>
                                                                        <li><code>COUNT</code>: đếm</li>
                                                                        <li><code>LAST</code>: giá trị cuối</li>
                                                                        <li><code>FIRST</code>: giá trị lần đầu</li>
                                                                    </ul>
@apiSuccess     {object}            data.config_model.field_attribute                            Thông tin field để tính toán
@apiSuccess     {string}            data.config_model.field_attribute.field_key                  Field key cần tính toán
@apiSuccess     {string}            data.config_model.field_attribute.field_name                 Tên field cần tính toán
@apiSuccess     {string}            data.config_model.field_attribute.field_property             Thuộc tính của field
@apiSuccess     {object}            data.config_model.condition_time                             Cấu hình thời gian tính toán của event
@apiSuccess     {string}            data.config_model.condition_time.type                        Loại cấu hình thời gian
                                                                                                <ul>
                                                                                                    <li><code>30_DAY_AGO</code>: 30 ngày trước</li>
                                                                                                    <li><code>90_DAY_AGO</code>: 90 ngày trước</li>
                                                                                                    <li><code>180_DAY_AGO</code>: 180 ngày trước</li>
                                                                                                    <li><code>270_DAY_AGO</code>: 270 ngày trước</li>
                                                                                                    <li><code>THIS_MONTH</code>: tháng này</li>
                                                                                                    <li><code>LAST_MONTH</code>: tháng trước</li>
                                                                                                    <li><code>THIS_YEAR</code>: năm nay</li>
                                                                                                    <li><code>LAST_YEAR</code>: năm trước</li>
                                                                                                    <li><code>CUSTOM</code>: tuỳ chỉnh thời gian</li>
                                                                                                </ul>

@apiSuccess     {string}            [data.config_model.condition_time.start_time]                Thời gian bắt đầu trong trường hợp type là tuỳ chỉnh thời gian. ("%Y-%m-%dT%H:%MZ")
@apiSuccess     {string}            [data.config_model.condition_time.end_time]                  Thời gian kết thúc trong trường hợp type là tuỳ chỉnh thời gian. ("%Y-%m-%dT%H:%MZ")
@apiSuccess     {object}            data.config_result                                           Cấu hình kết quả của hàm tính toán
@apiSuccess     {string}            data.config_result.display_type    Kiểu hiển thị (vd: single_line)
@apiSuccess     {string}            data.config_result.field_key      Định danh key của field kết quả
@apiSuccess     {string}            data.config_result.field_name     Tên field kết quả
@apiSuccess     {number}            data.config_result.field_property Thuộc tính của field
@apiSuccess     {string}            [data.config_result.format]       Định dạng hiển thị
@apiSuccess     {string}            data.config_result.format_value   Giá trị định dạng (vd: all)
@apiSuccess     {string}            data.config_result.object         Đối tượng lưu trữ kết quả
@apiSuccess     {object}            data.config_frequency                                        Cấu hình tuần suất tính toán
@apiSuccess     {string}            data.config_frequency.type                                   Kiểu tần suất tính toán
                                                                                                <ul>
                                                                                                    <li>MANUAL: tần suất tính toán thủ công</li>
                                                                                                    <li>PERIODIC: tần suất tính toán định kỳ</li>
                                                                                                </ul>
@apiSuccess     {object}            data.config_frequency.config                                 Cấu hình thời gian tính toán trong trường hợp sử dụng <code>định kỳ</code>
@apiSuccess     {string}            data.config_frequency.config.type                            Loại lặp định kỳ
                                                                                                <ul>
                                                                                                    <li><code>DAILY</code>: Hằng ngày</li>
                                                                                                    <li><code>WEEKLY</code>: Hằng tuần</li>
                                                                                                    <li><code>MONTHLY</code>: Hằng tháng</li>
                                                                                                </ul>
@apiSuccess     {string}            data.config_frequency.config.hour                            Thời gian lặp theo <code>định kỳ</code> (HH:MM). Example (16:49)
@apiSuccess     {array}             data.config_frequency.config.values                         Giá trị lặp trong trường hợp đặc biệt.
                                                                                                <ul>
                                                                                                    <li>Trong trường hợp <code>EVERY_WEEK</code>: 
                                                                                                        <ul>
                                                                                                            <li><code>MONDAY</code>: Thứ hai</li>
                                                                                                            <li><code>TUESDAY</code>: Thứ ba</li>
                                                                                                            <li><code>WEDNESDAY</code>: Thứ tư</li>
                                                                                                            <li><code>THURSDAY</code>: Thứ năm</li>
                                                                                                            <li><code>FRIDAY</code>: Thứ sáu</li>
                                                                                                            <li><code>SATURDAY</code>: Thứ bảy</li>
                                                                                                            <li><code>SUNDAY</code>: Chủ nhật</li>
                                                                                                        </ul>
                                                                                                    </li>
                                                                                                        <li>Trong trường hợp <code>EVERY_MONTH</code>: 
                                                                                                        <ul>
                                                                                                            <li><code>FIRST</code>: Ngày đầu tháng</li>
                                                                                                            <li><code>LAST</code>: Ngày cuối tháng</li>
                                                                                                            <li><code>1...30</code>: Các ngày trong tháng. Dạng int 1,2,3, ..., 30</li>
                                                                                                        </ul>
                                                                                                    </li>
                                                                                                </ul>
@apiSuccess     {string}            data.config_frequency.formula_validity_period.end_time      Thời gian kết thúc trong trường hợp có thời gian kết thúc. Định dạng <code>"%Y-%m-%dT%H:%MZ</code> 
@apiSuccess     {string}            data.config_frequency.formula_validity_period.type          Loại cấu hình thời gian có hiệu lực của công thức.
                                                                                                <ul>
                                                                                                    <li>UNLIMITED_DURATION: không có thời gian kết thúc</li>
                                                                                                    <li>LIMITED_DURATION: Có thời gian kết thúc</li>
                                                                                                </ul>
@apiSuccess     {int}               data.config_frequency.number_retry                          Số lần tính toán lại trong trường hợp gặp lỗi
@apiSuccess     {string}            data.status_calculation                                     Trạng thái của công thức tính toán
                                                                                                <ul>
                                                                                                    <li>INIT: Chưa tính toán</li>
                                                                                                    <li>PROCESSING: Đang xử lý</li>
                                                                                                    <li>FINISHED: Đã xử lý</li>
                                                                                                    <li>FAIL: Không thành công</li>
                                                                                                </ul>
@apiSuccess     {string}            data.last_calculation_time                                  Thời gian tính toán gần nhất. Định dạng: yyyy-MM-ddTHH:mmZ
@apiSuccess     {string}            data.status_connect                                         Trạng thái kết nối của công thức tính toán.
                                                                                                <ul>
                                                                                                    <li>ON: bật</li>
                                                                                                    <li>OFF: tắt</li>
                                                                                                </ul>
@apiSuccess     {string}            data.created_time                                           Thời gian tạo công thức tính toán. Định dạng: yyyy-MM-ddTHH:mmZ
@apiSuccess     {string}            data.updated_time                                           Thời gian cập nhật công thức tính toán. Định dạng: yyyy-MM-ddTHH:mmZ
@apiSuccess     {string}            data.created_by                                             Người tạo công thức tính toán
@apiSuccess     {string}            data.updated_by                                             Người cập nhật công thức tính toán

"""


# ---------- Khởi tạo công thức -----------
"""
@api {POST} {domain}/calculation-attribute/api/v1.0/calculations             Tạo công thức tính toán
@apiGroup Calculations
@apiVersion 1.0.0
@apiName AddCalculations

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}            title                       Tên công thức                                     
@apiParam   (BODY:) {string}            status_connect              Trạng thái kết nối của công thức.
                                                                    <ul>
                                                                        <li>ON: bật</li>
                                                                        <li>OFF: tắt</li>
                                                                    </ul>
@apiParam   (BODY:) {string}            [description]               Mô tả công thức  
@apiParam   (BODY:) {string}            object_generated            Đối tượng phát sinh

@apiParam   (BODY:) {Object}            config_filter               Cấu hình điều kiện Filter
@apiParam   (BODY:) {string}            config_filter.type          Kiểu filter
                                                                    <ul>
                                                                        <li><code>ALL</code>:: tất cả tập dữ liệu</li>
                                                                        <li><code>CUSTOM</code>:: chạy với tập segment cụ thể</li>
                                                                    </ul>
@apiParam   (BODY:) {Object}            config_filter.detail        Chi tiết của kiểu Filter
@apiParam   (BODY:) {String}            config_filter.detail.type   Kiểu
                                                                    <ul>
                                                                        <li><code>SEGMENT</code>: Sử dụng segment</li>
                                                                    </ul>
@apiParam   (BODY:) {String}            config_filter.detail.value  Giá trị của loại tương ứng. <b>Trong trường hợp sử dụng Segment thì tương ứng sẽ là lưu segment_id</b>
@apiParam   (BODY:) {string}            calculation_method          Phương thức tính toán
                                                                    <ul>
                                                                        <li><code>AGGREGATION</code>: Tổng hợp</li>
                                                                        <li><code>COUNT_NUMBER_OF_EXECUTIONS</code>: Đếm số lần thực hiện</li>
                                                                        <li><code>FREQUENCY</code>: Tần suất</li>
                                                                        <li><code>CUSTOM</code>: Công thức tuỳ chỉnh</li>
                                                                    </ul>
@apiParam   (BODY:) {object}            config_model                Cấu hình model
@apiParam   (BODY:) {string}            config_model.event_key      <code>Định danh Key của event</code>
@apiParam   (BODY:) {string}            config_model.event_device_type     Loại thiết bị event. Giá trị: <code>MOBILE</code>, <code>WEB</code>, <code>ALL</code>
@apiParam   (BODY:) {string}            config_model.event_type     Loại event. Giá trị: <code>DYNAMIC_EVENT</code>, <code>BASE_EVENT</code>, <code>TRACKING_EVENT</code>
@apiParam   (BODY:) {string}            config_model.audience_id    <code>Định danh ID của Audience</code>
@apiParam   (BODY:) {string}            config_model.function       Hàm sử dụng
                                                                    <ul>
                                                                        <li><code>SUM</code>: Tổng</li>
                                                                        <li><code>AVERAGE</code>: Bình quân</li>
                                                                        <li><code>MIN</code>: Nhỏ nhất</li>
                                                                        <li><code>MAX</code>: Lớn nhất</li>
                                                                        <li><code>COUNT</code>: Đếm</li>
                                                                        <li><code>LAST</code>: Giá trị cuối</li>
                                                                        <li><code>FIRST</code>: Giá trị lần đầu</li>
                                                                    </ul>
@apiParam   (BODY:) {object}            config_model.field_attribute                            Thông tin field để tính toán
@apiParam   (BODY:) {string}            config_model.field_attribute.field_key                  Field key cần tính toán
@apiParam   (BODY:) {string}            config_model.field_attribute.field_name                 Tên field cần tính toán
@apiParam   (BODY:) {string}            config_model.field_attribute.field_property             Thuộc tính của field

@apiParam   (BODY:) {object}            config_model.condition_time                             Cấu hình thời gian tính toán của Event
@apiParam   (BODY:) {string}            config_model.condition_time.type                        Loại cấu hình thời gian
                                                                                                <ul>
                                                                                                    <li><code>30_DAY_AGO</code>: 30 ngày trước</li>
                                                                                                    <li><code>90_DAY_AGO</code>: 90 ngày trước</li>
                                                                                                    <li><code>180_DAY_AGO</code>: 180 ngày trước</li>
                                                                                                    <li><code>270_DAY_AGO</code>: 270 ngày trước</li>
                                                                                                    <li><code>THIS_MONTH</code>: tháng này</li>
                                                                                                    <li><code>LAST_MONTH</code>: tháng trước</li>
                                                                                                    <li><code>THIS_YEAR</code>: năm nay</li>
                                                                                                    <li><code>LAST_YEAR</code>: năm trước</li>
                                                                                                    <li><code>CUSTOM</code>: tuỳ chỉnh thời gian</li>
                                                                                                </ul>
                                                                        
@apiParam   (BODY:) {string}            [config_model.condition_time.start_time]                   Thời gian bắt đầu trong trường hợp type là tuỳ chỉnh thời gian. ("%Y-%m-%dT%H:%MZ")
@apiParam   (BODY:) {string}            [config_model.condition_time.end_time]                     Thời gian kết thúc trong trường hợp type là tuỳ chỉnh thời gian. ("%Y-%m-%dT%H:%MZ")

@apiParam   (BODY:) {object}            config_result                                           Cấu hình kết quả của hàm tính toán
@apiParam   (BODY:) {string}            config_result.object                                    Đối tưởng thụ hưởng
@apiParam   (BODY:) {string}            config_result.field_key                                 Field thụ hưởng
                                                                         
@apiParam   (BODY:) {object}            config_frequency                                        Cấu hình tuần suất tính toán
@apiParam   (BODY:) {string}            config_frequency.type                                   Kiểu tần suất tính toán
                                                                                                <ul>
                                                                                                    <li>MANUAL: tần suất tính toán thủ công</li>
                                                                                                    <li>PERIODIC: tần suất tính toán định kỳ</li>
                                                                                                </ul>
@apiParam   (BODY:) {object}            config_frequency.config                                 Cấu hình thời gian tính toán trong trường hợp sử dụng <code>định kỳ</code>
@apiParam   (BODY:) {string}            config_frequency.config.type                            Loại lặp định kỳ
                                                                                                <ul>
                                                                                                    <li><code>DAILY</code>: Hằng ngày</li>
                                                                                                    <li><code>WEEKLY</code>: Hằng tuần</li>
                                                                                                    <li><code>MONTHLY</code>: Hằng tháng</li>
                                                                                                </ul>
@apiParam   (BODY:) {string}            config_frequency.config.hour                            Thời gian lặp theo <code>định kỳ</code> (HH:MM). Example (16:49)
@apiParam   (BODY:) {array}             config_frequency.config.values                          Giá trị lặp trong trường hợp đặc biệt.
                                                                                                <ul>
                                                                                                    <li>Trong trường hợp <code>WEEKLY</code>: 
                                                                                                        <ul>
                                                                                                            <li><code>MONDAY</code>: Thứ hai</li>
                                                                                                            <li><code>TUESDAY</code>: Thứ ba</li>
                                                                                                            <li><code>WEDNESDAY</code>: Thứ tư</li>
                                                                                                            <li><code>THURSDAY</code>: Thứ năm</li>
                                                                                                            <li><code>FRIDAY</code>: Thứ sáu</li>
                                                                                                            <li><code>SATURDAY</code>: Thứ bảy</li>
                                                                                                            <li><code>SUNDAY</code>: Chủ nhật</li>
                                                                                                        </ul>
                                                                                                    </li>
                                                                                                        <li>Trong trường hợp <code>MONTHLY</code>: 
                                                                                                        <ul>
                                                                                                            <li><code>FIRST</code>: Ngày đầu tháng</li>
                                                                                                            <li><code>LAST</code>: Ngày cuối tháng</li>
                                                                                                            <li><code>1...30</code>: Các ngày trong tháng. Dạng int 1,2,3, ..., 30</li>
                                                                                                        </ul>
                                                                                                    </li>
                                                                                                </ul>
@apiParam   (BODY:)  {object}            config_frequency.formula_validity_period               Cấu hình thời gian công thức có hiệu lực.
@apiParam   (BODY:)  {string}            config_frequency.formula_validity_period.end_time      Thời gian kết thúc trong trường hợp có thời gian kết thúc. Định dạng <code>"%Y-%m-%dT%H:%MZ</code> 
@apiParam   (BODY:)  {string}            config_frequency.formula_validity_period.type          Loại cấu hình thời gian có hiệu lực của công thức.
                                                                                                <ul>
                                                                                                    <li>UNLIMITED_DURATION: không có thời gian kết thúc</li>
                                                                                                    <li>LIMITED_DURATION: Có thời gian kết thúc</li>
                                                                                                </ul>
@apiParam   (BODY:)  {int}               config_frequency.number_retry=5                        Số lần tính toán lại trong trường hợp gặp lỗi
@apiParamExample {json} Body:
{
    "title": "Công thức tính toán cho event",
    "description": "Công thức tính toán cho event",
    "object_generated": "profile",
    "config_filter": {
        "type": "CUSTOM",
        "detail": {
            "type": "SEGMENT",
            "value": "5d725286-a322-11ef-bbcd-5e8b6caad7bb",
        }
    },
    "calculation_method": "AGGREGATION",
    "config_model": {
        "event_key": "5d725286-a322-11ef-bbcd-5e8b6caad7bb",
        "event_device_type": "ALL",
        "event_type": "dynamic_event",
        "audience_id": "5d725286-a322-11ef-bbcd-5e8b6caad7bb",
        "function": "SUM",
        "field_attribute": {
            "field_key": "tong_tien"
            "field_name": "TONG TIEN",
            "field_property": 3
        }
    },
    "config_result": {
        "object": "profile",
        "field_key": "_dyn_tinh_toan_16123131231"
    },
    "config_frequency": {
        "type": "MANUAL",
        "config": {
            "type": "DAILY",
            "hour": "HH:MM",
            "values": []
        }
    }
}

@apiSuccess     {Object}            data                            Dữ liệu trả về
@apiUse DetailCalculation

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data": {
        "id": "5e8f8b8e-fa6f-4b9e-8f0b-3c1d6a2f",
        "title": "test",
        "description": "test",
        "object_generated": "test",
        "config_filter": {
            "type": "CUSTOM",
            "detail": {
                "type": "SEGMENT",
                "value": "5d725286-a322-11ef-bbcd-5e8b6caad7bb",
                "audience_id": "5d725286-a322-11ef-bbcd-abcdefghigw"
            }
        },
        "config_model": {
            "condition_time": {
                "type": "30_DAY_AGO"
            }
        },
        "config_result": {
            "object": "profiles",
            "field_key": "test"
        },
        "config_frequency": {
            "type": "MANUAL"
        },
        "created_time": "2020-01-01T00:00Z",
        "updated_time": "2020-01-01T00:00Z",
        "created_by": "test",
        "updated_by": "test"
        
    }
}
"""


# ---------- Lấy danh sách công thức -----------
"""
@api {POST} {domain}/calculation-attribute/api/v1.0/calculations/actions/filter             Lấy danh sách công thức theo bộ lọc
@apiGroup Calculations
@apiVersion 1.0.0
@apiName ListCalculations

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging_tokens
@apiUse filter_calculation_attribute

@apiSuccess     {ArrayObject}            data                            Dữ liệu trả về
@apiUse DetailCalculation
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data": [
        {
            "calculation_method": "AGGREGATION",
            "config_filter": {
                "type": "ALL"
            },
            "config_frequency": {
                "config": {
                    "hour": "09:00",
                    "type": "DAILY",
                    "values": []
                },
                "formula_validity_period": {
                    "type": "UNLIMITED_DURATION"
                },
                "number_retry": 5,
                "type": "PERIODIC"
            },
            "config_model": {
                "audience_id": "a4bd16e1-faac-493f-8027-1630c64a67e0",
                "condition_time": {
                    "type": "30_DAY_AGO"
                },
                "event_key": "giao_dich_mua_hang_1720153612",
                "event_device_type": "ALL",
                "event_type": "dynamic_event",
                "field_attribute": {
                    "field_key": "ed_amount",
                    "field_name": "T\u1ed5ng s\u1ed1 ti\u1ec1n giao d\u1ecbch",
                    "field_property": 88
                },
                "function": "SUM"
            },
            "config_result": {
                "display_type": "single_line",
                "field_key": "_dyn_tong_so_lan_giao_dich_30_ngay_gan_nhat_1742442968232",
                "field_name": "T\u1ed5ng s\u1ed1 l\u1ea7n giao d\u1ecbch 30 ng\u00e0y g\u1ea7n nh\u1ea5t",
                "field_property": 88,
                "format": null,
                "format_value": "all",
                "object": "profiles"
            },
            "created_by": "0bd02211-fd47-4215-98ed-724914a96156",
            "created_time": "2025-03-20T03:56Z",
            "description": "",
            "id": "67db91d73b8cf43ad791f0d1",
            "is_stop_calculate_schedule": true,
            "keyword": "tong so lan giao dich",
            "last_calculation_time": null,
            "last_session_id": null,
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "next_process_time": "2025-03-21T02:00Z",
            "number_of_consecutive_fail": 0,
            "object_generated": "profiles",
            "status_calculation": "INIT",
            "status_connect": "OFF",
            "title": "T\u1ed5ng s\u1ed1 l\u1ea7n giao d\u1ecbch",
            "updated_by": "0bd02211-fd47-4215-98ed-724914a96156",
            "updated_time": "2025-03-20T03:56Z"
        }
    ]
}
"""

# ---------- Cập nhật công thức -----------
"""
@apiDescription Cập nhật công thức tính toán. <code>Cập nhật trường nào thì gửi lên trường đó.</code>
@apiGroup Calculations
@apiVersion 1.0.0
@apiName UpdateCalculations

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}            [title]                       Tên công thức                                     
@apiParam   (BODY:) {string}            [description]               Mô tả công thức  
@apiParam   (BODY:) {string}            [object_generated]            Đối tượng phát sinh

@apiParam   (BODY:) {Object}            [config_filter]               Cấu hình điều kiện Filter
@apiParam   (BODY:) {string}            [config_filter.type]          Kiểu filter
                                                                    <ul>
                                                                        <li><code>ALL</code>:: tất cả tập dữ liệu</li>
                                                                        <li><code>CUSTOM</code>:: chạy với tập segment cụ thể</li>
                                                                    </ul>
@apiParam   (BODY:) {Object}            [config_filter.detail]        Chi tiết của kiểu Filter
@apiParam   (BODY:) {string}            [config_filter.detail.type]     Kiểu
                                                                        <ul>
                                                                            <li><code>SEGMENT</code>: Sử dụng segment</li>
                                                                        </ul>
@apiParam   (BODY:) {string}            [config_filter.detail.value]  Giá trị của loại tương ứng. <b>Trong trường hợp sử dụng Segment thì tương ứng sẽ là lưu segment_id</b>
@apiParam   (BODY:) {string}            [calculation_method]            Phương thức tính toán
                                                                        <ul>
                                                                            <li><code>AGGREGATION</code>: Tổng hợp</li>
                                                                            <li><code>COUNT_NUMBER_OF_EXECUTIONS</code>: Đếm số lần thực hiện</li>
                                                                            <li><code>FREQUENCY</code>: Tần suất</li>
                                                                            <li><code>CUSTOM</code>: Công thức tuỳ chỉnh</li>
                                                                        </ul>
@apiParam   (BODY:) {object}            [config_model]                Cấu hình model
@apiParam   (BODY:) {string}            [config_model.event_key]       <code>Định danh Key của event</code>
@apiParam   (BODY:) {string}            [config_model.event_device_type]     Loại thiết bị event. Giá trị: <code>MOBILE</code>, <code>WEB</code>, <code>ALL</code>
@apiParam   (BODY:) {string}            [config_model.event_type]     Loại event. Giá trị: <code>DYNAMIC_EVENT</code>, <code>BASE_EVENT</code>, <code>TRACKING_EVENT</code>
@apiParam   (BODY:) {string}            [config_model.audience_id]    <code>Định danh ID của Audience</code>
@apiParam   (BODY:) {string}            [config_model.function]       Hàm sử dụng
                                                                    <ul>
                                                                        <li><code>SUM</code>: Tổng</li>
                                                                        <li><code>AVERAGE</code>: Bình quân</li>
                                                                        <li><code>MIN</code>: Nhỏ nhất</li>
                                                                        <li><code>MAX</code>: Lớn nhất</li>
                                                                        <li><code>COUNT</code>: Đếm</li>
                                                                        <li><code>LAST</code>: Giá trị cuối</li>
                                                                        <li><code>FIRST</code>: Giá trị lần đầu</li>
                                                                    </ul>
@apiParam   (BODY:) {object}            [config_model.field_attribute]                            Thông tin field để tính toán
@apiParam   (BODY:) {string}            [config_model.field_attribute.field_key]                  Field key cần tính toán
@apiParam   (BODY:) {string}            [config_model.field_attribute.field_name]                 Tên field cần tính toán
@apiParam   (BODY:) {string}            [config_model.field_attribute.field_property]             Thuộc tính của field

@apiParam   (BODY:) {object}            [config_model.condition_time]                             Cấu hình thời gian tính toán của Event
@apiParam   (BODY:) {string}            [config_model.condition_time.type]                        Loại cấu hình thời gian
                                                                                                <ul>
                                                                                                    <li><code>30_DAY_AGO</code>: 30 ngày trước</li>
                                                                                                    <li><code>90_DAY_AGO</code>: 90 ngày trước</li>
                                                                                                    <li><code>180_DAY_AGO</code>: 180 ngày trước</li>
                                                                                                    <li><code>270_DAY_AGO</code>: 270 ngày trước</li>
                                                                                                    <li><code>THIS_MONTH</code>: tháng này</li>
                                                                                                    <li><code>LAST_MONTH</code>: tháng trước</li>
                                                                                                    <li><code>THIS_YEAR</code>: năm nay</li>
                                                                                                    <li><code>LAST_YEAR</code>: năm trước</li>
                                                                                                    <li><code>CUSTOM</code>: tuỳ chỉnh thời gian</li>
                                                                                                </ul>
                                                                        
@apiParam   (BODY:) {string}            [config_model.condition_time.start_time]                   Thời gian bắt đầu trong trường hợp type là tuỳ chỉnh thời gian. ("%Y-%m-%dT%H:%MZ")
@apiParam   (BODY:) {string}            [config_model.condition_time.end_time]                     Thời gian kết thúc trong trường hợp type là tuỳ chỉnh thời gian. ("%Y-%m-%dT%H:%MZ")

@apiParam   (BODY:) {object}            [config_result]                                           Cấu hình kết quả của hàm tính toán
@apiParam   (BODY:) {string}            [config_result.object]                                    Đối tưởng thụ hưởng
@apiParam   (BODY:) {string}            [config_result.field_key]                                 Field thụ hưởng
@apiParam   (BODY:) {string}            [config_result.list_field_init_field]                     Danh sách field dùng để gửi sang phía module để khởi tạo field.
                                                                         
@apiParam   (BODY:) {object}            [config_frequency]                                        Cấu hình tuần suất tính toán
@apiParam   (BODY:) {string}            [config_frequency.type]                                   Kiểu tần suất tính toán
                                                                                                <ul>
                                                                                                    <li>MANUAL: tần suất tính toán thủ công</li>
                                                                                                    <li>PERIODIC: tần suất tính toán định kỳ</li>
                                                                                                </ul>
@apiParam   (BODY:) {object}            [config_frequency.config]                                 Cấu hình thời gian tính toán trong trường hợp sử dụng <code>định kỳ</code>
@apiParam   (BODY:) {string}            [config_frequency.config.type]                            Loại lặp định kỳ
                                                                                                <ul>
                                                                                                    <li><code>DAILY</code>: Hằng ngày</li>
                                                                                                    <li><code>WEEKLY</code>: Hằng tuần</li>
                                                                                                    <li><code>MONTHLY</code>: Hằng tháng</li>
                                                                                                </ul>
@apiParam   (BODY:) {string}            [config_frequency.config.hour]                            Thời gian lặp theo <code>định kỳ</code> (HH:MM). Example (16:49)
@apiParam   (BODY:) {array}             [config_frequency.config.values]                          Giá trị lặp trong trường hợp đặc biệt.
                                                                                                <ul>
                                                                                                    <li>Trong trường hợp <code>EVERY_WEEK</code>: 
                                                                                                        <ul>
                                                                                                            <li><code>MONDAY</code>: Thứ hai</li>
                                                                                                            <li><code>TUESDAY</code>: Thứ ba</li>
                                                                                                            <li><code>WEDNESDAY</code>: Thứ tư</li>
                                                                                                            <li><code>THURSDAY</code>: Thứ năm</li>
                                                                                                            <li><code>FRIDAY</code>: Thứ sáu</li>
                                                                                                            <li><code>SATURDAY</code>: Thứ bảy</li>
                                                                                                            <li><code>SUNDAY</code>: Chủ nhật</li>
                                                                                                        </ul>
                                                                                                    </li>
                                                                                                        <li>Trong trường hợp <code>EVERY_MONTH</code>: 
                                                                                                        <ul>
                                                                                                            <li><code>FIRST</code>: Ngày đầu tháng</li>
                                                                                                            <li><code>LAST</code>: Ngày cuối tháng</li>
                                                                                                            <li><code>1...30</code>: Các ngày trong tháng. Dạng int 1,2,3, ..., 30</li>
                                                                                                        </ul>
                                                                                                    </li>
                                                                                                </ul>
@apiParam   (BODY:) {string}            status_connect                                          Trạng thái kết nối của công thức.
                                                                                                <ul>
                                                                                                    <li>ON: bật</li>
                                                                                                    <li>OFF: tắt</li>
                                                                                                </ul>
@apiParam   (BODY:)  {object}            config_frequency.formula_validity_period               Cấu hình thời gian công thức có hiệu lực.
@apiParam   (BODY:)  {string}            config_frequency.formula_validity_period.end_time      Thời gian kết thúc trong trường hợp có thời gian kết thúc. Định dạng <code>"%Y-%m-%dT%H:%MZ</code> 
@apiParam   (BODY:)  {string}            config_frequency.formula_validity_period.type          Loại cấu hình thời gian có hiệu lực của công thức.
                                                                                                <ul>
                                                                                                    <li>UNLIMITED_DURATION: không có thời gian kết thúc</li>
                                                                                                    <li>LIMITED_DURATION: Có thời gian kết thúc</li>
                                                                                                </ul>
@apiParamExample {json} Body:
{
    "title": "Công thức tính toán cho event",
    "description": "Công thức tính toán cho event",
    "object_generated": "profile",
    "config_filter": {
        "type": "CUSTOM",
        "detail": {
            "type": "SEGMENT",
            "value": "5d725286-a322-11ef-bbcd-5e8b6caad7bb"
        }
    },
    "calculation_method": "AGGREGATION",
    "config_model": {
        "event_key": "5d725286-a322-11ef-bbcd-5e8b6caad7bb",
        "event_device_type": "ALL",
        "event_type": "dynamic_event",
        "audience_id": "5d725286-a322-11ef-bbcd-5e8b6caad7bb",
        "function": "SUM",
        "field_attribute": {
            "field_key": "tong_tien"
            "field_name": "TONG TIEN",
            "field_property": 3
        }
    },
    "config_result": {
        "object": "profile",
        "field_key": "_dyn_tinh_toan_16123131231",
        "field_property": 3,
        "field_name":"Tính toán"
    },
    "config_frequency": {
        "type": "MANUAL",
        "config": {
            "type": "DAILY",
            "hour": "HH:MM",
            "values": []
        }
    }
}

@apiSuccess     {Object}            data                            Dữ liệu trả về
@apiUse DetailCalculation

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data": {
        "id": "5e8f8b8e-fa6f-4b9e-8f0b-3c1d6a2f",
        "title": "test",
        "description": "test",
        "object_generated": "test",
        "config_filter": {
            "type": "ALL",
            "condition": {
                "field": "test",
                "operator": "EQUAL",
                "value": "test"
            }
        },
        "config_model": {
            "condition_time": {
                "type": "30_DAY_AGO"
            }
        },
        "config_result": {
            "object": "profiles",
            "field_key": "test"
        },
        "config_frequency": {
            "type": "MANUAL"
        },
        "created_time": "2020-01-01T00:00Z",
        "updated_time": "2020-01-01T00:00Z",
        "created_by": "test",
        "updated_by": "test"
    }
}
"""

# ---------- Chi tiết công thức -----------
"""
@api {GET} {domain}/calculation-attribute/api/v1.0/calculations/<calculation_id>             Lấy chi tiết công thức
@apiGroup Calculations
@apiVersion 1.0.0
@apiName DetailCalculations

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess     {Object}            data                            Dữ liệu trả về
@apiUse DetailCalculation

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data": {
        "id": "5e8f8b8e-fa6f-4b9e-8f0b-3c1d6a2f",
        "title": "test",
        "description": "test",
        "object_generated": "test",
        "config_filter": {
            "type": "ALL",
            "condition": {
                "field": "test",
                "operator": "EQUAL",
                "value": "test"
            }
        },
        "config_model": {
            "condition_time": {
                "type": "30_DAY_AGO"
            }
        },
        "config_result": {
            "object": "profiles",
            "field_key": "test"
        },
        "config_frequency": {
            "type": "MANUAL"
        },
        "created_time": "2020-01-01T00:00Z",
        "updated_time": "2020-01-01T00:00Z",
        "created_by": "test",
        "updated_by": "test"
    }
}
"""

# ---------- Xoá công thức -----------
"""
@api {POST} {domain}/calculation-attribute/api/v1.0/calculations/actions/delete             Xoá công thức
@apiGroup Calculations
@apiVersion 1.0.0
@apiName DeleteCalculations

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {array[String]}             calculation_ids                         Danh sách <code>ID</code> của công thức cần xoá

@apiParamExample {json} Body:
{
    "calculation_ids": ["5e8f8b8e-fa6f-4b9e-8f0b-3c1d6a2f"],
}    

@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {Object}            data                            Dữ liệu trả về

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        
    },
    "message": "Success"
}
"""

# ---------- Kiểm tra trạng thái công thức -----------
"""
@api {POST} {domain}/calculation-attribute/api/v1.0/calculations/calculation-status             Kiểm tra trạng thái công thức
@apiGroup Calculations
@apiVersion 1.0.0
@apiName GetCalculationsStatus

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {array[String]}             calculation_ids                         Danh sách <code>ID</code> của công thức cần kiểm tra trạng thái

@apiParamExample {json} Body:
{
    "calculation_ids": [
        "676e2044fbe335ed97008a16",
        "676e30e68f275be450c695a3",
        "676be11c3cf91a7f17fdff0b"
    ]
}    

@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {Object}            data                            Dữ liệu trả về

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        {
            "calculation_id": "676be11c3cf91a7f17fdff0b",
            "field_key": "_dyn_cal_timeout_1735123236804",
            "status": "ACTIVE"
        },
        {
            "calculation_id": "676e2044fbe335ed97008a16",
            "field_key": "_dyn_nam_test_pro_6_1735270474822",
            "status": "INACTIVE"
        },
        {
            "calculation_id": "676e30e68f275be450c695a3",
            "field_key": "_dyn_nam_test_9_pro_1735274731083",
            "status": "INACTIVE"
        }
    },
    "message": "Success"
}
"""


# ---------- Kiểm tra segment có được gắn vào công thức -----------
"""
@api {POST} {domain}/calculation-attribute/internal/api/v1.0/calculations/check-segments-exist             Kiểm tra segment có được gắn vào công thức
@apiGroup Calculations Internal
@apiVersion 1.0.0
@apiName checkSegmentExist

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {array[String]}             segment_ids                         Danh sách <code>segment_id</code> cần kiểm tra

@apiParamExample {json} Body:
{
    "segment_ids": ["396928666283544400", "396769982526001627"],
}    

@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {Object}            data                            Dữ liệu trả về

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "segment_id": "396928666283544400",
            "is_exist": true
        },
                {
            "segment_id": "396769982526001627",
            "is_exist": false
        }
    ],
    "message": "Success"
}
"""


# ---------- Lấy số lượng công thức theo bộ lọc -----------
"""
@api {POST} {domain}/calculation-attribute/api/v1.0/calculations/actions/count/by-filter             Lấy số lượng công thức theo bộ lọc
@apiGroup Calculations
@apiVersion 1.0.0
@apiName CountCalculationsByFilter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse filter_calculation_attribute


@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {Object}            data                            Dữ liệu trả về
@apiSuccess     {Int}               data.total                      Tổng số bản ghi

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "total": 1000
    },
    "message": "Success"
}
"""
# ---------- Thay đổi trạng thái kết nối -----------
"""
@api {POST} {domain}/calculation-attribute/api/v1.0/calculations/<calculation_id>/change-status-connect             Thay đổi trạng thái kết nối
@apiGroup Calculations
@apiVersion 1.0.0
@apiName ChangeStatusConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}            action                     Hành động thay đổi trạng thái kết nối
                                                                    <ul>
                                                                        <li>ON: bật</li>
                                                                        <li>OFF: tắt</li>
                                                                    </ul>
@apiParamExample {json} Body:
{
    "action": "ON"
}   
@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {Object}            data                            Dữ liệu trả về

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {},
    "message": "Success"
}
"""
# ---------- Tính toán thủ công -----------
"""
@api {POST} {domain}/calculation-attribute/api/v1.0/calculations/manual-calculation             Tính toán thủ công
@apiGroup Calculations
@apiVersion 1.0.0
@apiName ManualCalculation

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}            calculation_id                     ID cần tính toán

@apiParamExample {json} Body:
{
    "calculation_id": "5e8f8b8e-fa6f-4b9e-8f0b-3c1d6a2f"
}   
@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {Object}            data                            Dữ liệu trả về
@apiSuccess     {string}            data.status_calculation         Trạng thái tính toán của công thức
@apiSuccess     {string}            data.last_time_calculation      Thời gian tính toán gần nhất của công thức  

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "status_calculation": "PROCESSING",
        "last_time_calculation": "2020-01-01T00:00Z"
    },
    "message": "Success"
}
"""
# ---------- Lấy danh sách cấu hình thời gian tính toán -----------
"""
@api {GET} {domain}/calculation-attribute/api/v1.0/calculation-time-configurations             Lấy danh sách cấu hình thời gian tính toán
@apiGroup Calculations
@apiVersion 1.0.0
@apiName CalculationTimeConfigurations

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParamExample {json} Body:
{
}   
@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {Array}             data                            Dữ liệu trả về
@apiSuccess     {string}            data.title                      Tên của cấu hình thời gian
@apiSuccess     {string}            data.key                        Key của cấu hình.
                                                                    <ul>
                                                                        <li><code>30_DAY_AGO</code>: 30 ngày trước</li>
                                                                        <li><code>90_DAY_AGO</code>: 90 ngày trước</li>
                                                                        <li><code>180_DAY_AGO</code>: 180 ngày trước</li>
                                                                        <li><code>270_DAY_AGO</code>: 270 ngày trước</li>
                                                                        <li><code>THIS_MONTH</code>: tháng này</li>
                                                                        <li><code>LAST_MONTH</code>: tháng trước</li>
                                                                        <li><code>THIS_YEAR</code>: năm nay</li>
                                                                        <li><code>LAST_YEAR</code>: năm trước</li>
                                                                        <li><code>CUSTOM</code>: tuỳ chỉnh thời gian</li>
                                                                    </ul>
@apiSuccess     {int}               data.order                      Vị trí

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "title": "30 ngày trước",
            "key": "30_DAY_AGO",
            "order": 1
        },
        {
            "title": "90 ngày trước",
            "key": "90_DAY_AGO",
            "order": 2
        },
        {
            "title": "180 ngày trước",
            "key": "180_DAY_AGO",
            "order": 3
        },
        {
            "title": "270 ngày trước",
            "key": "270_DAY_AGO",
            "order": 4
        },
        {
            "title": "Tháng nay",
            "key": "THIS_MONTH",
            "order": 5
        },
        {
            "title": "Tháng trước",
            "key": "LAST_MONTH",
            "order": 6
        },
        {
            "title": "Năm nay",
            "key": "THIS_YEAR",
            "order": 7
        },
        {
            "title": "Năm trước",
            "key": "LAST_YEAR",
            "order": 8
        },
        {
            "title": "Tuỳ chỉnh thời gian",
            "key": "CUSTOM",
            "order": 9
        }
    ],
    "message": "Success"
}
"""
# ---------- Lấy lịch sử tính toán -----------
"""
@api {GET} {domain}/calculation-attribute/api/v1.0/calculations/<calculation_id>/sessions          Lấy các phiên tính toán của công thức
@apiDescription Lấy các phiên tính toán của công thức.
@apiGroup CalculationsSessions
@apiVersion 1.0.0
@apiName CS-GetList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging_tokens

@apiSuccess     {String}         message                      Mô tả phản hồi
@apiSuccess     {Integer}        code                         Mã phản hồi
@apiSuccess     {Array}          data                         Danh sách các phiên tính toán
@apiSuccess     {String}        data.id                       Id session
@apiSuccess     {String}         data.start_time_calculate   Thời gian bắt đầu tính toán <code>(%Y-%m-%dT%H:%MZ)</code>
@apiSuccess     {String}         data.end_time_calculate     Thời gian kết thúc tính toán <code>(%Y-%m-%dT%H:%MZ)</code>
@apiSuccess     {String}         data.finish_time  Thời gian hoàn thành tính toán <code>(%Y-%m-%dT%H:%MZ)</code> (sau 3 ngày từ thời điểm này sẽ dọn thông tin của session)
@apiSuccess     {String}         data.event_time               Thời gian phát sinh event <code>(%Y-%m-%dT%H:%MZ)</code>
@apiSuccess     {String}         data.status                   Trạng thái tính toán của session
                                                                <ul>
                                                                    <li><code>INIT</code>: Chưa tính toán</li>
                                                                    <li><code>PROCESSING</code>: Đang xử lý</li>
                                                                    <li><code>FINISHED</code>: Đã xử lý</li>
                                                                    <li><code>FAIL</code>: Không thành công</li>
                                                                </ul>
@apiSuccess     {Integer}        data.total_input         Số lượng bản ghi thoả mãn
@apiSuccess     {Integer}        data.total_success       Số lượng bản ghi được ghi dữ liệu
@apiSuccess     {Integer}        data.total_error         Số lượng bản ghi lỗi

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": [
        {
            "id": "20240701066837",
            "start_time_calculate": "2025-05-01T10:00:00Z",
            "end_time_calculate": "2025-05-01T10:05:00Z",
            "event_time": "2025-05-01T09:59:00Z",
            "finish_time": "2025-05-01T10:05:00Z",
            "status": "FINISHED",
            "total_input": 1200,
            "total_success": 1195,
            "total_error": 5
        },
        {
            "id": "20240701066838",
            "start_time_calculate": "2025-04-28T10:00:00Z",
            "end_time_calculate": "2025-04-28T10:04:00Z",
            "event_time": "2025-04-28T09:59:00Z",
            "finish_time": "2025-05-01T10:05:00Z",
            "status": "INIT",
            "total_input": 1180,
            "total_success": 1170,
            "total_error": 10
        }
    ]
}
"""

# ---------- Lấy logs phiên tính toán -----------
"""
@api {GET} {domain}/calculation-attribute/api/v1.0/calculations/<calculation_id>/sessions/<session_id>/logs          Lấy logs phiên tính toán của công thức
@apiDescription Lấy logs phiên tính toán của công thức.
@apiGroup CalculationsSessions
@apiVersion 1.0.0
@apiName CS-GetListLogs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging_tokens

@apiSuccess     {String}         message                      Mô tả phản hồi
@apiSuccess     {Integer}        code                         Mã phản hồi
@apiSuccess     {Array}          data                         Danh sách các phiên tính toán
@apiSuccess     {Integer}        data.id                      Id log
@apiSuccess     {String}         data.object_id               Id bản ghi tính toán chính
@apiSuccess     {Object}         data.object_data             Data của bản ghi đối tượng muốn hiển thị  (vd: đối tượng Profile sẽ là bản ghi của profile)
@apiSuccess     {String}         data.action_time             Thời gian hành động <code>(%Y-%m-%dT%H:%MZ)</code>
@apiSuccess     {String}         data.state                   Trạng thái hành động
                                                                <ul>
                                                                    <li><code>error</code>: Lỗi</li>
                                                                    <li><code>processed</code>: Xử lý thành công</li>
                                                                </ul>
@apiSuccess     {String}         data.reason                  Lý do hành động lỗi


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": [
        {
            "id": "681db6d1847b205bc3901110",
            "object_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d688",
            "state": "processed", 
            "action_time": "2020-01-01T00:00Z",
            "reason": "",
            "object_data": {
                "id": "1cb2a489-b34a-41b3-aa7d-f1efc580d688",
                "profile_name": "Nguyễn Văn A",
                "_dyn_nam_cal_pro_3_1735186111373": 12341
            }
        },
        {
            "id": "681db6d1847b205bc3901110",
            "object_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d688",
            "state": "error",
            "action_time": "2020-01-01T00:00Z",
            "reason": "reason error",
            "object_data": {
                "id": "1cb2a489-b34a-41b3-aa7d-f1efc580d688",
                "profile_name": "Nguyễn Thị Khách Hàng",
                "_dyn_nam_cal_pro_3_1735186111373": 7
            }
        }
    ]
}
"""
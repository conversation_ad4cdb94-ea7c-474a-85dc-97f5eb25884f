# *************************** Nhận báo c<PERSON>o kết quả tính toán ****************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {TOPIC} /calculationattribute-report-calculation Nhận báo cáo kết quả tính toán
@apiDescription Nhận báo cáo kết quả tính toán
@apiGroup Consumer
@apiVersion 1.0.0
@apiName ReportCalculation

@apiParam (Message:)     {String}               action_time                                     Thời điểm phát sinh hành động
@apiParam (Message:)     {String}               merchant_id                                     Merchant id
@apiParam (Message:)     {String}               type                                            Loại báo cáo. Nhận giá trị:
                                                                                                <li><code>REPORT_TOTAL</code> Báo cáo tổng số bản tin tính toán (nhận từ Compute Engine)</li>
                                                                                                <li><code>REPORT_DETAIL</code> Báo cáo chi tiết bản tin xử lý từ các module</li>
@apiParam (Message:)     {Object}               data_callback                                   Thông tin phiên tính toán
@apiParam (Message:)     {String}               data_callback.calculation_id                    Id công thức
@apiParam (Message:)     {String}               data_callback.calculation_name                  Tên công thức
@apiParam (Message:)     {Int}                  data_callback.session_id                        Id phiên tính toán

@apiParam (type=REPORT_TOTAL:)     {int}                  total_result                                     Tổng số bản tin tính toán

@apiParam (type=REPORT_DETAIL:)     {Array(Object)}        result                                          Danh sách kết quả tính toán của bản tin
@apiParam (type=REPORT_DETAIL:)     {String}               result.object_id                                Id đối tượng tính toán chính
@apiParam (type=REPORT_DETAIL:)     {String}               result.state                                    Trạng thái xử lý bản tin. Nhận giá trị:
                                                                                                            <li><code>processed</code> Xử lý thành công</li>
                                                                                                            <li><code>error</code> Xử lý thất bại</li>
@apiParam (type=REPORT_DETAIL:)     {String}               result.reason                                   Lý do lỗi
@apiParam (type=REPORT_DETAIL:)     {Object}               result.object_data                              Data của bản ghi muốn hiển thị

@apiParamExample {json} Báo cáo tổng số bản tin tính toán (nhận từ Compute Engine)
{
  "action_time": "2024-07-01 06:59:44.120",
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "type": "REPORT_TOTAL",
  "total_result": 1000,
  "data_callback": {
    "calculation_id": "67289a9f981b0c23f87914ad",
    "calculation_name": "Công thức 1",
    "session_id": 20240701066837
  }
}


@apiParamExample {json} Chi tiết thành công/lỗi của bản ghi đối tượng
{
  "action_time": "2024-07-01 06:59:44.120",
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "type": "REPORT_DETAIL",
  "result": [
    {
      "object_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d688",
      "state": "processed", # error
      "reason": "Lý do lỗi"
      "object_data": {
        "id": "1cb2a489-b34a-41b3-aa7d-f1efc580d688",
        "profile_name": "Tên khách hàng",
        "_dyn_nam_cal_pro_3_1735186111373": "Giá trị tính toán" # field _dyn_nam_cal_pro_3_1735186111373 là tên field tính toán
      }
    }
  ],
  "data_callback": {
    "calculation_id": "67289a9f981b0c23f87914ad",
    "calculation_name": "Công thức 1",
    "session_id": 20240701066837
  }
}
"""

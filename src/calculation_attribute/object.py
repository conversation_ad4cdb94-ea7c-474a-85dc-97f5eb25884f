#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 04/11/2024
"""

# ---------- L<PERSON><PERSON> danh sách đối tượng phát sinh -----------
"""
@api {GET} {domain}/calculation-attribute/api/v1.0/object-generated             L<PERSON>y danh sách đối tượng phát sinh
@apiGroup Object
@apiVersion 1.0.0
@apiName GetListObjectGenerated

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess     {Array}             data                                            Danh sách đối tượng xử lý
@apiSuccess     {string}            data.name                                       Tên đối tượng
@apiSuccess     {string}            data.key                                        Key của đối tượng
@apiSuccess     {int}               data.order                                      Vị trí
@apiSuccess     {int}               data.status                                     Trạng thái hỗ trợ
                                                                                    <ul>
                                                                                        <li>1: hỗ trợ</li>
                                                                                        <li>0: không hỗ trợ</li>
                                                                                    </ul>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "key": "profiles",
            "name": "Profile",
            "order": 1,
            "status": 1,
        },
        {
            "key": "company",
            "name": "Company",
            "order": 2,
            "status": 1
        },
        {
            "key": "sale",
            "name": "Sale",
            "order": 3,
            "status": 0
        },
        {
            "key": "ticket",
            "name": "Ticket",
            "order": 4,
            "status": 0
        }
    ],
    "message": "request thành công."
}
"""

# ---------- Lấy danh sách đối tượng thụ hưởng -----------
"""
@api {GET} {domain}/calculation-attribute/api/v1.0/object-destination             Lấy danh sách đối tượng thụ hưởng dữ liệu
@apiGroup Object
@apiVersion 1.0.0
@apiName GetListObjectDestination

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam       (QUERY:) {string}   object_generated                                Đối tượng phát sinh.

@apiSuccess     {Array}             data                                            Danh sách đối tượng xử lý
@apiSuccess     {string}            data.name                                       Tên đối tượng
@apiSuccess     {string}            data.key                                        Key của đối tượng
@apiSuccess     {int}               data.order                                      Vị trí
@apiSuccess     {int}               data.status                                     Trạng thái hỗ trợ
                                                                                    <ul>
                                                                                        <li>1: hỗ trợ</li>
                                                                                        <li>0: không hỗ trợ</li>
                                                                                    </ul>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "key": "profiles",
            "name": "Profile",
            "order": 1,
            "status": 1,
        },
        {
            "key": "company",
            "name": "Company",
            "order": 2,
            "status": 1
        },
        {
            "key": "sale",
            "name": "Sale",
            "order": 3,
            "status": 0
        },
        {
            "key": "ticket",
            "name": "Ticket",
            "order": 4,
            "status": 0
        }
    ],
    "message": "request thành công."
}
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 21/11/2024
"""
# ---------- <PERSON><PERSON> sách phương thức tính toán -----------
"""
@api {GET} {domain}/calculation-attribute/api/v1.0/calculation-methods             Danh sách phương thức tính toán.
@apiDescription     Danh sách phương thức tính toán
@apiGroup CalculationMethods
@apiVersion 1.0.0
@apiName CalculationMethods

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess     {String}            message                         M<PERSON> tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {Array}             data                            Dữ liệu trả về
@apiSuccess     {string}            data.key                        Key của phương thức
@apiSuccess     {string}            data.title                      Tên của phương thức
@apiSuccess     {string}            data.description                Mô tả của phương thức
@apiSuccess     {int}               data.order                      Vị trí của phương thức
@apiSuccess     {int}               data.status                     Trạng thái của phương thức
@apiSuccess     {Array}             data.functions                  Danh sách hàm sử dụng tương ứng của phương thức
@apiSuccess     {string}            data.functions.key              Key của hàm
@apiSuccess     {string}            data.functions.title            Tên của hàm

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "key": "AGGREGATION",
            "title": "Aggregation (Tổng hợp)",
            "description": "Sử dụng hàm SUM, ....",
            "order": 1,
            "status": 1,
            "functions": [
                {
                    "key": "SUM",
                    "title": "Tính tổng"
                }
            ] 
        }
    ],
    "message": "Success"
}
"""

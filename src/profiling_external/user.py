********************************** API Get Detail User ********************************
* version: 1.0.0                                                                      *
***************************************************************************************


"""
@api {get} /profiling/external/v3.0/merchants/<merchant_id>/customers/<customer_id>/info [Done] Lấy thông tin chi tiết User
@apiDescription API Lấy thông tin chi tiết 1 User. Bao gồm thông tin user và thẻ của user
@apiGroup Customers
@apiVersion 1.0.0
@apiName GetDetailCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng

@apiSuccess   (customer:)     {String}     id                               ID khách hàng
@apiSuccess   (customer:)     {String}    name                              Tên khách hàng
@apiSuccess   (customer:)     {Array}     phone_number                      Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Number}    created_account_type              Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (customer:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (customer:)     {Number}    gender                            Giới tính
@apiSuccess   (customer:)     {String}    address                           Địa chỉ
@apiSuccess   (customer:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (customer:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (customer:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (customer:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (customer:)     {String}    birthday                          Ngày sinh
@apiSuccess   (customer:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (customer:)     {Number}    income                            Mức thu nhập
@apiSuccess   (customer:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess   (customer:)     {DateTime}    created_time                      Thời gian tạo profile
@apiSuccess   (customer:)     {DateTime}    updated_time                      Thời gian cập nhật profile
@apiSuccess   (customer:)     {Array}     cards                             Dữ liệu thẻ khách hàng

@apiSuccess   (customer:cards:)     {String}     user_card_id                     Id khách hàng thẻ
@apiSuccess   (customer:cards:)     {String}     id                               Id thẻ
@apiSuccess   (customer:cards:)     {String}     code                             Mã thẻ
@apiSuccess   (customer:cards:)     {Number}     status                           Trạng thái thẻ
@apiSuccess   (customer:cards:)     {DateTime}   approved_time                    Thời điểm duyệt thẻ

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "customer": {
        "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
        "name": "andrew",
        "phone_number": ["+***********", "+***********"],
        "email": ["<EMAIL>", "<EMAIL>"],
        "created_account_type": 1,
        "avatar": "",
        "created_time": "2017-12-12T15:12:28Z",
        "updated_time": "2017-12-12T15:12:28Z",
        "gender": 2,
        "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
        "province_code": 1,
        "district_code": 1,
        "ward_code": 1,
        "marital_status": 1,
        "birthday": "1989-09-17",
        "religiousness": 1,
        "nation": 1,
        "job": 39,
        "operation": null,
        "hobby": "Giày dép;Phim;Chạy bộ",
        "operation": null,
        "income": null,
        "budget": null,
        "cards": [{
              "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
              "code": "124364",
              "status": 1,
              "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304",
              "approved_time": "2017-12-12T15:12:28Z"
        }]
    }
}
"""

******************** Update Consent many profile  many conditions **********************
* version: 1.0.0                                                                      *
*                                                                                     *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile/consent/conditions Update Consent with many profile, many conditions.
@apiDescription Update Consent with many profile, many conditions.
@apiGroup Customers
@apiVersion 1.0.0
@apiName  UpdateConsentWithManyProfileManyConditions
@apiHeader (Headers:) {String} Content-Type <code>application/form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam   (Body:)    {Files}         files       Danh sách các evidents.
@apiParam   (Body:)    {String}       mkt_consent             Marketing Consent.
<br/><br/>Allowed values:<br/>
<li><code>Có</code></li>
<li><code>Không</code></li>
@apiParam   (Body:)    {String}       analytics_consent       Analytics Consent.
<br/><br/>Allowed values:<br/>
<li><code>Có</code></li>
<li><code>Không</code></li>
@apiParam   (Body:)    {String}       tracking_consent        Tracking Consent.
<br/><br/>Allowed values:<br/>
<li><code>Có</code></li>
<li><code>Không</code></li>
@apiParam   (Body:)     {Array}       deleted_evidents        Danh sách các evident_id bị xóa.
@apiParam   (Body:)     {Array}       deleted_evidents        Danh sách các evident_id bị xóa.
@apiParam   (Body:)    {String}       mkt_consent             Marketing Consent.
@apiParam   (Body:)    {String}       updated_by              Update bởi ai module, service, VD: MSB.
@apiParam   (Body:)    {String}       body              Các điều kiện để tìm profile, mảng obj, điều kiện trong obj là và, điều kiện giữa các obj là hoặc.
field_key:cif,primary_email,primary_phone,customer_id,profile_identify
@apiParam      (conditions:)   {String}   [cif]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [primary_email]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [primary_phone]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [source]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [customer_id]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {Array}   [profile_identify]   Giá trị cần tìm kiếm.
@apiParam      (profile_identify:)   {String}   identify_type   driving_license, passport, identity_card, citizen_identity, identity_card_army, birth_certificate, visa, temporary_residence_card, other.
@apiParam      (profile_identify:)   {String}   identify_value   Giá trị cần tìm kiếm.



@apiParamExample Body data example:
{
  "mkt_consent": "Có",
  "analytics_consent": "Không",
  "tracking_consent": "Có",
  "deleted_evidents": ["abc", "def"],
  "updated_by": "MSB",
  "body": {
        "conditions": [
            {
                "profile_identify": [
                    {
                        "identify_type": "citizen_identity",
                        "identify_value": "cccd123"
                    }
                ],
                "primary_email": "<EMAIL>",
                "cif": "20231173",
                "source": "Core",
            },
            {
                "profile_identify": [
                    {
                        "identify_type": "identity_card",
                        "identify_value": "891919"
                    }
                ]
            }
        ]
    }
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

*************************************  Detail Product Holding  ************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/external/v3.0/profile/detail_product_holding Chi tiết product holding của profile
@apiDescription Chi tiết product holding của profile
@apiGroup ProductHolding
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName ProfileGetProductHolding

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam          (Param:)                     {String}        profile_id   Id của profile

@apiSuccess {Array}   data    Danh sách sản phẩm
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi


@apiSuccessExample {json} Response list card type example
{
    "code": 200,
    "data": [
        {
            "62a93c67a8772cd90c3b53b7": [
                {
                    "due_date": "2022-06-23T10:07:24.107Z",
                    "estimated_settlement_date": "2022-06-23T10:07:24.107Z",
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 1",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_holding_id": "e33a5aa07fdc88909df1076f83486384",
                    "product_line": "62a93c67a8772cd90c3b53b7",
                    "product_status": "OK",
                    "product_type": [
                        "62b185167fd598001059489f",
                        "62b185167fd598001059489d",
                        "62b185167fd598001059489c"
                    ],
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b52a06c67c3200124c608e",
                    "updated_time": "2022-06-24T03:05:42.432Z"
                },
                {
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 123",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_holding_id": "f435559a6c8d93cb633f7b0d14cda1f6",
                    "product_line": "62a93c67a8772cd90c3b53b7",
                    "product_status": "OK",
                    "product_type": [
                        "62b185167fd598001059489f",
                        "62b185167fd598001059489d",
                        "62b185167fd598001059489c"
                    ],
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b55f873d2aca000f0e8284",
                    "updated_time": "2022-06-24T06:54:00.622Z"
                }
            ],
            "product_line_code": "tien_gui_tiet_kiem_975feecc",
            "product_line_id": "62a93c67a8772cd90c3b53b7",
            "product_line_name": "TIỀN GỬI TIẾT KIỆM",
            "status": "expired",
            "total": {
                "amount_saving_each_passbook": 0,
                "total_savings": 0,
                "total_savings_book": 0
            }
        },
        {
            "62a93c67a8772cd90c3b53b1": [
                {
                    "due_date": "2022-06-23T10:07:24.107Z",
                    "estimated_settlement_date": "2022-06-23T10:07:24.107Z",
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 1",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_holding_id": "8",
                    "product_line": "62a93c67a8772cd90c3b53b1",
                    "product_type": [
                        "62b185167fd598001059489f",
                        "62b185167fd598001059489d",
                        "62b185167fd598001059489c"
                    ],
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b51e420ce781000eb6a477",
                    "updated_time": "2022-06-24T02:15:30.590Z"
                }
            ],
            "product_line_code": "tai_khoan_39fcdcd2",
            "product_line_id": "62a93c67a8772cd90c3b53b1",
            "product_line_name": "TÀI KHOẢN",
            "status": "expired",
            "total": {
                "current_balance_original_currency": 0,
                "current_balance_vnd": 0,
                "current_monthly_average_balance_original_currency": 0,
                "current_monthly_average_balance_vnd": 0,
                "total_current_balance_currency": 0,
                "total_current_balance_vnd": 0,
                "total_current_monthly_average_balance_currency": 0,
                "total_current_monthly_average_balance_vnd": 0,
                "total_product_available": 0
            }
        },
        {
            "62b175b13323cb000eada1c7": [
                {
                    "due_date": "2022-06-23T10:07:24.107Z",
                    "estimated_settlement_date": "2022-06-23T10:07:24.107Z",
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 1",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_code": "status3",
                    "product_holding_id": "99999999999",
                    "product_line": "62b175b13323cb000eada1c7",
                    "product_status": "status3",
                    "product_type": "id1",
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b51e420ce781000eb6a477",
                    "transaction_office": "status3",
                    "updated_time": "2022-06-24T02:15:30.590Z"
                }
            ],
            "product_line_code": "anh",
            "product_line_id": "62b175b13323cb000eada1c7",
            "product_line_name": "Ánh test 21/6",
            "status": "expired",
            "total": {
                "amount_saving_each_passbook": 0,
                "current_balance_original_currency": 0,
                "current_balance_vnd": 0,
                "current_monthly_average_balance_original_currency": 0,
                "current_monthly_average_balance_vnd": 0,
                "total_current_balance_vnd": 0,
                "total_current_monthly_average_balance_currency": 0,
                "total_current_monthly_average_balance_vnd": 0,
                "total_face_value": 0,
                "total_initial_loan_balance": 0,
                "total_loan_balance": 0,
                "total_number_loan": 0,
                "total_product_available": 0,
                "total_savings": 0,
                "total_savings_book": 0
            }
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
*************************************  Detail Product Holding From Other  ************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/external/v3.0/profile/detail/product_holding_from_other Chi tiết product holding của profile từ module khác
@apiDescription Chi tiết product holding của profile từ module khác
@apiGroup ProductHolding
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName ProfileGetProductHoldingFromOther

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam          (Param:)                     {String}        profile_id   Id của profile

@apiSuccess {Array}   data    Danh sách sản phẩm
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi


@apiSuccessExample {json} Response list card type example
{
    "code": 200,
    "data": [
        {
            "62a93c67a8772cd90c3b53b7": [
                {
                    "due_date": "2022-06-23T10:07:24.107Z",
                    "estimated_settlement_date": "2022-06-23T10:07:24.107Z",
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 1",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_holding_id": "e33a5aa07fdc88909df1076f83486384",
                    "product_line": "62a93c67a8772cd90c3b53b7",
                    "product_status": "OK",
                    "product_type": [
                        "62b185167fd598001059489f",
                        "62b185167fd598001059489d",
                        "62b185167fd598001059489c"
                    ],
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b52a06c67c3200124c608e",
                    "updated_time": "2022-06-24T03:05:42.432Z"
                },
                {
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 123",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_holding_id": "f435559a6c8d93cb633f7b0d14cda1f6",
                    "product_line": "62a93c67a8772cd90c3b53b7",
                    "product_status": "OK",
                    "product_type": [
                        "62b185167fd598001059489f",
                        "62b185167fd598001059489d",
                        "62b185167fd598001059489c"
                    ],
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b55f873d2aca000f0e8284",
                    "updated_time": "2022-06-24T06:54:00.622Z"
                }
            ],
            "product_line_code": "tien_gui_tiet_kiem_975feecc",
            "product_line_id": "62a93c67a8772cd90c3b53b7",
            "product_line_name": "TIỀN GỬI TIẾT KIỆM",
            "status": "expired",
            "total": {
                "amount_saving_each_passbook": 0,
                "total_savings": 0,
                "total_savings_book": 0
            }
        },
        {
            "62a93c67a8772cd90c3b53b1": [
                {
                    "due_date": "2022-06-23T10:07:24.107Z",
                    "estimated_settlement_date": "2022-06-23T10:07:24.107Z",
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 1",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_holding_id": "8",
                    "product_line": "62a93c67a8772cd90c3b53b1",
                    "product_type": [
                        "62b185167fd598001059489f",
                        "62b185167fd598001059489d",
                        "62b185167fd598001059489c"
                    ],
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b51e420ce781000eb6a477",
                    "updated_time": "2022-06-24T02:15:30.590Z"
                }
            ],
            "product_line_code": "tai_khoan_39fcdcd2",
            "product_line_id": "62a93c67a8772cd90c3b53b1",
            "product_line_name": "TÀI KHOẢN",
            "status": "expired",
            "total": {
                "current_balance_original_currency": 0,
                "current_balance_vnd": 0,
                "current_monthly_average_balance_original_currency": 0,
                "current_monthly_average_balance_vnd": 0,
                "total_current_balance_currency": 0,
                "total_current_balance_vnd": 0,
                "total_current_monthly_average_balance_currency": 0,
                "total_current_monthly_average_balance_vnd": 0,
                "total_product_available": 0
            }
        },
        {
            "62b175b13323cb000eada1c7": [
                {
                    "due_date": "2022-06-23T10:07:24.107Z",
                    "estimated_settlement_date": "2022-06-23T10:07:24.107Z",
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 1",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_code": "status3",
                    "product_holding_id": "99999999999",
                    "product_line": "62b175b13323cb000eada1c7",
                    "product_status": "status3",
                    "product_type": "id1",
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b51e420ce781000eb6a477",
                    "transaction_office": "status3",
                    "updated_time": "2022-06-24T02:15:30.590Z"
                }
            ],
            "product_line_code": "anh",
            "product_line_id": "62b175b13323cb000eada1c7",
            "product_line_name": "Ánh test 21/6",
            "status": "expired",
            "total": {
                "amount_saving_each_passbook": 0,
                "current_balance_original_currency": 0,
                "current_balance_vnd": 0,
                "current_monthly_average_balance_original_currency": 0,
                "current_monthly_average_balance_vnd": 0,
                "total_current_balance_vnd": 0,
                "total_current_monthly_average_balance_currency": 0,
                "total_current_monthly_average_balance_vnd": 0,
                "total_face_value": 0,
                "total_initial_loan_balance": 0,
                "total_loan_balance": 0,
                "total_number_loan": 0,
                "total_product_available": 0,
                "total_savings": 0,
                "total_savings_book": 0
            }
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

*************************************  List Product Holding  ************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************"""
@api {get} [HOST]/profiling/external/v3.0/profile/list/product_holding list product holding
@apiDescription list product holding
@apiGroup ProductHolding
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName ListProductHolding

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam          (Param:)                     {String}        profile_id   Id của profile

@apiSuccess {Array}   data    Danh sách sản phẩm
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response list card type example
{
    "code": 200,
    "data": [
        {
            "product_line_code": "tien_gui_tiet_kiem_975feecc",
            "product_line_id": "62a93c67a8772cd90c3b53b7",
            "product_line_name": "TIỀN GỬI TIẾT KIỆM",
            "system_product_line_status": "expired",
        },
        {
            "product_line_code": "tai_khoan_39fcdcd2",
            "product_line_id": "62a93c67a8772cd90c3b53b1",
            "product_line_name": "TÀI KHOẢN",
            "system_product_line_status": "expired",
        },
        {

            "product_line_code": "anh",
            "product_line_id": "62b175b13323cb000eada1c7",
            "product_line_name": "Ánh test 21/6", 
            "system_product_line_status": "expired",
        }
    ],
    "total": {
        "activate": 1,
        "expired": 1,
        "unused": 1
    }
    "lang": "vi",
    "message": "request thành công."
}
"""


***************************** Update Consent profile nfc ******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile/consent_nfc/<profile_id> Update Consent profile nfc
@apiDescription Update Consent profile nfc
@apiGroup Customers
@apiVersion 1.0.0
@apiName  UpdateConsentProfileNFC
@apiHeader (Headers:) {String} Content-Type <code>application/form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam   (Body:)    {Files}         files       Danh sách các evidents.
@apiParam   (Body:)    {String}       mkt_consent             Marketing Consent.
<br/><br/>Allowed values:<br/>
<li><code>Có</code></li>
<li><code>Không</code></li>
@apiParam   (Body:)    {String}       analytics_consent       Analytics Consent.
<br/><br/>Allowed values:<br/>
<li><code>Có</code></li>
<li><code>Không</code></li>
@apiParam   (Body:)    {String}       tracking_consent        Tracking Consent.
<br/><br/>Allowed values:<br/>
<li><code>Có</code></li>
<li><code>Không</code></li>
@apiParam   (Body:)     {Array}       deleted_evidents        Danh sách các evident_id bị xóa.
@apiParam   (Body:)     {Array}       deleted_evidents        Danh sách các evident_id bị xóa.
@apiParam   (Body:)    {String}       mkt_consent             Marketing Consent.
@apiParam   (Body:)    {String}       updated_by              Update bởi ai module, service, VD: MSB.
@apiParam   (Body:)    {String}       body              evident file created by NFC.
field_key:cif,primary_email,primary_phone,customer_id,profile_identify
@apiParam      (body:)   {String}   action_time   Thông tin evident.
@apiParam      (body:)   {int}   capacity   Thông tin evident.
@apiParam      (body:)   {String}   created_by   Thông tin evident.
@apiParam      (body:)   {String}   filename   Thông tin evident.
@apiParam      (body:)   {String}   id   Thông tin evident.
@apiParam      (body:)   {String}   local_path   Thông tin evident.
@apiParam      (body:)   {String}   merchant_id   Thông tin evident.
@apiParam      (body:)   {String}   objectId   Thông tin evident.
@apiParam      (body:)   {String}   url   Thông tin evident.



@apiParamExample Body data example:
{
  "mkt_consent": "Có",
  "analytics_consent": "Không",
  "tracking_consent": "Có",
  "deleted_evidents": ["abc", "def"],
  "updated_by": "MSB",
  "body": {
        "_id": "620dc147dfd20bf34ac6954f",
        "action_time": "2023-11-09T16:20:05.734Z",
        "capacity": 1082923,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "filename": "1645068615_0E0A7559.jpg",
        "format_file": "image/jpeg",
        "id": "620dc147dfd20bf34ac6954f",
        "local_path": "/Users/<USER>/workspace/mobio/Module-Ticket/ticket/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "objectId": None,
        "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg"
    }
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "message": "request thành công."
}
"""


*************************** search Unification rules by profile sources*************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {get} [HOST]/profiling/external/v3.0/unification/search search Unification rules.
@apiDescription search Unification rules.
@apiGroup Unification
@apiVersion 1.0.0
@apiName SearchUnificationRules

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang  
@apiParam   (query:)    {search}         search       search value.
@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "connector_config": {
            "data_recording_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "profile_identify": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            }
                        },
                        "priority": 1
                    }
                ]
            },
            "fields_append": [],
            "fields_replace": [],
            "fields_replace_ignore_empty": [],
            "fields_verify": [],
            "id": -1714034917253,
            "is_default": false,
            "is_trust": false,
            "name": "CCCD-NFC",
            "source": "CCCD-NFC",
            "unification_rules": {
                "consent": null,
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "profile_identify": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            }
                        },
                        "priority": 1
                    }
                ]
            }
        },
        "consent": {
            "analytics_consent": "Có",
            "mkt_consent": "Có",
            "tracking_consent": "Có"
        },
        "deletable": false,
        "disable": true,
        "editable": false,
        "id": "053i3gig6c6h",
        "is_default": false,
        "logical_operators": "or",
        "operators": [
            {
                "fields": {
                    "name": {
                        "match_type": "exact",
                        "normalized_type": "string"
                    },
                    "profile_identify": {
                        "match_type": "exact",
                        "normalized_type": "string"
                    }
                },
                "priority": 1
            }
        ],
        "show": true,
        "source": "CCCD-NFC",
        "status": 1
    },
    "lang": "vi",
    "message": "request thành công.",
    "success": true
}
"""


************************************ Detail Consent ***********************************
* version: 1.0.0                                                                      *
*                                                                                     *
***************************************************************************************
"""
@api {get} [HOST]/profiling/external/v3.0/profile/consent/<profile_id> Detail Consent.
@apiDescription Detail Consent
@apiGroup Customers
@apiVersion 1.0.0
@apiName  Detail Consent
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header



@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "data": {
    "staff_id": "uuid",
    "evident": [
      {
        "id": "abc",
        "url": "https://mobio.io/images/evident.png"
      }
    ],
    "created_time": "2023-08-22 11:52:23.123",
    "updated_time": "2023-08-22 11:52:23.123"
  },
  "code": 200, 
  "lang": "vi", 
  "message": "request thành công."
}
"""
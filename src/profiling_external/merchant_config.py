********************************* Merchant List Field **********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {get} [HOST]/profiling/external/v3.0/merchant/field/list Merchant List Fields.
@apiDescription API lấy danh sách fields của Merchant
@apiGroup Merchant fields
@apiVersion 1.0.0
@apiName  MerchantListField
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse order_sort
@apiParam   (Query:)    {String}  [search]     Tìm kiếm fields theo tên.

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "group":"information",
      "fields":[]
    },
    {
      "group":"demographic",
      "fields":[]
    },
    {
      "group":"activity",
      "fields":[]
    },
    {
      "group":"loyalty",
      "fields":[]
    },
    {
      "group":"other",
      "fields":[]
    },
    {
      "group":"dynamic",
      "fields":[
        {
          "field_name": "field_1",
          "field_key": "_dyn_name_1558406233387",
          "field_property": 1,
          "display_type": "radio",
          "display_in_form": true,
          "order": 15,
          "group": "dynamic",
          "display_in_form_input": false,
          "disable_remove_form_input": false,
          "display_in_dashboard": false,
          "dashboard_order": 1,
          "disable_remove_dashboard": false,
          "required": false,
          "data_selected": [
            {
              "id": 1,
              "name": "data 1",
              "display_in_form": true
            },
            {
              "id": 1,
              "name": "data 2",
              "display_in_form": false
            }
          ],
          "is_base": false,
          "status": 1,
          "description": "mo ta ngan cua field",
          "created_time": "2019-03-27T17:52:46Z",
          "updated_time": "2019-03-27T17:52:46Z",
          "history": [
            {
              "created_time": "2019-03-27T17:52:46Z",
              "staff_id": "7df46d3b-98bd-4bf6-a8ac-1b7210297e54",
              "fullname": "MobioTest",
              "username": "admin@mobiotest"
            },
            {
              "created_time": "2019-03-28T17:52:46Z",
              "staff_id": "7df46d3b-98bd-4bf6-a8ac-1b7210297e54",
              "fullname": "MobioTest",
              "username": "admin@mobiotest"
            }
          ]
        }
      ]
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""

# ******************************** List types of Address ***********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /profiling/external/v3.0/merchant/address/list_address_type List types of Address
@apiDescription Api List types of address
@apiGroup Merchant Config
@apiVersion 1.0.0
@apiName  ListTypesOfAddress
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "code": "contact_address",
            "multiple_value": false,
            "name": "Địa chỉ liên hệ",
            "translate_key": "i18n_contact_address"
        },
        {
            "code": "permanent_address",
            "multiple_value": true,
            "name": "Địa chỉ thường trú",
            "translate_key": "i18n_permanent_address"
        },
        {
            "code": "temporary_address",
            "multiple_value": true,
            "name": "Địa chỉ tạm trú",
            "translate_key": "i18n_temporary_address"
        },
        {
            "code": "company_address",
            "multiple_value": true,
            "name": "Địa chỉ công ty",
            "translate_key": "i18n_company_address"
        },
        {
            "code": "other_address",
            "multiple_value": true,
            "name": "Địa chỉ khác",
            "translate_key": "i18n_address_other"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
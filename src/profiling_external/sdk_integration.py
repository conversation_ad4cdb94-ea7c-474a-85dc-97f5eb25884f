#!/usr/bin/python
# -*- coding: utf8 -*-

****************************************** Init Device ********************************************
* version: 1.0.0                                                                        		  *
***************************************************************************************************
"""
@api {get} /profiling/external/v3.0/device/init Khởi tạo device_id và cấp phát jwt
@apiDescription Api khởi tạo device_id mới, chỉ call api này khi sdk lần đầu được chạy trên client hoặc sdk bắt được sự kiện người dùng được redirect đến site qua chiến dịch và trên params có chứa profile_id
@apiGroup SDK
@apiVersion 1.0.0
@apiName InitDevice

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam   (Query:)   {String}  [profile_id]		profile_id thu thập được từ parameter, nhằm assign trực tiếp device này cho profile cụ thể


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": {
    "device": {
        "device_id": "509557504208797953",
        "profile_id": "uuid",
        "session_id": "509968998733971713"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VfaWQiOjUwOTU1NzUwNDIwODc5Nzk1MywiaWF0IjoxNzEzMzg5NzM2LjgzNzk1NywiZXhwIjoxNzEzNzYzMjIyLjgzNzk1NywiaXNzIjoiRGlnaWVudHkifQ.CNl0AVLcc07nqd3jwX2zTm8AtJ_V9f2SLg8ZqpA5ang"
  },
  "code": 200,
  "message": "successful"
}
"""

************************************** Generate new token *****************************************
* version: 1.0.0                                                                        		  *
***************************************************************************************************
"""
@api {get} /profiling/external/v3.0/device/gen_token Cấp mới token
@apiDescription Khi token cũ gần hoặc đã hết hạn, cần cấp mới token để có thể giao tiếp cùng backend. 
@apiGroup SDK
@apiVersion 1.0.0
@apiName GenerateNewToken

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": {
    "device": {
        "device_id": "509557504208797953",
        "profile_id": "uuid",
        "session_id": "509968998733971713"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VfaWQiOjUwOTU1NzUwNDIwODc5Nzk1MywiaWF0IjoxNzEzMzg5NzM2LjgzNzk1NywiZXhwIjoxNzEzNzYzMjIyLjgzNzk1NywiaXNzIjoiRGlnaWVudHkifQ.CNl0AVLcc07nqd3jwX2zTm8AtJ_V9f2SLg8ZqpA5ang"
  },
  "code": 200,
  "message": "successful"
}
"""

****************************************** Sync Events ********************************************
* version: 1.0.0                                                                        		  *
***************************************************************************************************
"""
@api {post} /profiling/external/v3.0/device/sync Đồng bộ events
@apiDescription Api đồng bộ events thu thập được về cho hệ thống lưu trữ và xử lý.
@apiGroup SDK
@apiVersion 1.0.0
@apiName SyncEvents

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Data:)    {Object}   device_info         thông tin của device
@apiParam   (Data:)    {Object}   app_info         thông tin của device
@apiParam   (Data:)    {Object}   [app_info]         thông tin của mobile app
@apiParam   (Data:)    {Object}   [sdk]         thông tin của sdk
@apiParam   (Data:)    {String}   request_id         mỗi lượt request sẽ có id riêng, để tránh trùng lặp request
@apiParam   (Data:)    {Object}   [consent]         thông tin của device
@apiParam   (Data:)    {Object}   [profile_attributes]         thông tin của profile
@apiParam   (Data:)    {Object}   [omni_campaign]         thông tin mà device này được tiếp cận từ omni channel
@apiParam   (Data:)    {Array}   events          Danh sách các event được đồng bộ

@apiParamExample [json] Body example:
{
    "events": [
        {
            "properties": {
                "node_id": 10,
                "campaign_id" : 1,
                "tracking_id": "uuid",
                "popup_id": "uuid",
                "interacted_type ": "str",
                "interacted_position_id": "str",
                "interacted_time ": "2024-04-25 07:48:50.123"
            },
            "event_key": "click",
            "event_type": "onpage",
            "campaign": [{
                "campaign_id": 1,
                "node_id": 1
            }]
        },
        {
            "properties": {
                "plan": "Pro Annual",
                "accountType" : "Facebook",
                "action_time": "2024-04-25 07:48:50.123"            
            },
            "event_key": "user_registered",
            "event_type": "behavior_event",
            "campaign": [{
                "campaign_id": 1,
                "node_id": 1
            }]
        },
        {
            "properties": {
                "property_1": "String property",
                "property_5" : 1,
                "action_time": "2024-04-25 07:48:50.123"            
            },
            "event_key": "client_click",
            "event_type": "behavior_event"
        },
        {
            "properties": {
                "tag_name": "button|url",
                "tag_id": "id cua tag do form sinh ra",
                "tag_value": "text value cua tag",
                "action_time": "2024-04-25 07:48:50.123"            
            },
            "event_type": "click",
            "campaign": [{
                "campaign_id": 1,
                "node_id": 1
            }],
            "form": {
                "site_id": "str",
                "form_id": "str"
            }
        },
        {
            "properties": {
                "action_time": "2024-04-25 07:48:50.123",
                "name": "Name of the page",
                "referrer": "Previous page’s full URL. Equivalent to document.referrer from the DOM API.",
                "search": "Query string portion of the page’s URL. Equivalent to location.search from the DOM API.",
                "title": "Page’s title. Equivalent to document.title from the DOM API.",
                "url" : "Page’s full URL. Segment first looks for the canonical URL. If the canonical URL is not provided, Segment uses location.href from the DOM API.",
                "path": "Path portion of the page’s URL. Equivalent to canonical path which defaults to location.pathname from the DOM API.",
                "keywords": ["A list/array of keywords describing the page’s content. The keywords would most likely be the same as, or similar to, the keywords you would find in an HTML meta tag for SEO purposes. This property is mainly used by content publishers that rely heavily on pageview tracking. This isn’t automatically collected."]            
            },
            "event_type": "page",
            "campaign": [{
                "campaign_id": 1,
                "node_id": 1
            }]
        },
    ],
    "device_info": {},
    "app_info": {},
    "sdk": {
        "version": "str like 2.1.1, 2.1.2-rc1",
        "code": "ID của app được tạo ra từ AppMarketPlace"
    },
    "request_id": "uuid",
    "consent": {},
    "profile_attributes": {},
    "omni_campaign": {}
}

@apiParamExample   {json} Body DeviceInfo (JS):
{
    "screen_height": 1136,
    "screen_width": 640,
    "timezone_offset": -4,
    "http_header_user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 7_1 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Mobile/11D167 mParticle/2.5.0",
    "charset":"",
    "description": "",
    "language": "",
    "color": "",
    "viewport": "",
    "title": "",
    "url": "",
    "platforms": "tring / enum", # "iOS", "Android", "web", "tvOS", "roku", "out_of_band", "alexa", "smart_tv", "fire_tv", "xbox"
    "browser_platform": "Linux x86_64",
    "host": "document host",
    "path": "",
    "referrer": "document refferer",
    "finger_print": "finger print of current device, it cannot be null",
    "last_finger_print": "last finger print, it needed to check device change info"
}

@apiParamExample   {json} Body event (event-page):
{
    "properties": {
        "action_time": "2024-04-25 07:48:50.123",
        "name": "Name of the page",
        "referrer": "Previous page’s full URL. Equivalent to document.referrer from the DOM API.",
        "search": "Query string portion of the page’s URL. Equivalent to location.search from the DOM API.",
        "title": "Page’s title. Equivalent to document.title from the DOM API.",
        "url" : "Page’s full URL. Segment first looks for the canonical URL. If the canonical URL is not provided, Segment uses location.href from the DOM API.",
        "path": "Path portion of the page’s URL. Equivalent to canonical path which defaults to location.pathname from the DOM API.",
        "keywords": ["A list/array of keywords describing the page’s content. The keywords would most likely be the same as, or similar to, the keywords you would find in an HTML meta tag for SEO purposes. This property is mainly used by content publishers that rely heavily on pageview tracking. This isn’t automatically collected."]            
    },
    "event_type": "page",
    "campaign": [{
        "campaign_id": 1,
        "node_id": 1
    }]
}

@apiParamExample   {json} Body event (event-click):
{
    "properties": {
        "action_time": "2024-04-25 07:48:50.123",
        "tag_name": "button|url",
        "tag_id": "id cua tag do form sinh ra",
        "tag_value": "text value cua tag"            
    },
    "event_type": "click",
    "campaign": [{
        "campaign_id": 1,
        "node_id": 1
    }],
    "form": {
        "site_id": "str",
        "form_id": "str"
    }
}

@apiParamExample   {json} Body event (event-exit):
{
    "properties": {
        "action_time": "2024-04-25 07:48:50.123",
        "campaign_id": 1,
        "node_id": 2,
        "last_page": "string",
        "expire_time": "2024-04-25 07:48:50.123"            
    },
    "event_type": "exit"
}

@apiParamExample   {json} Body event (event-enable-notification):
{
    "properties": {
        "action_time": "2024-04-25 07:48:50.123",
        "status": "enable (enable|disable)",
        "push_id": "str",
        "push_type": 5 # WEB_PUSH_FIREBASE = 5, WEB_PUSH_APNS = 6      
    },
    "event_type": "notification"
}

@apiParamExample   {json} Body event (event-behaviour):
{
    "properties": {
        "property_1": "String property",
        "property_5" : 1,
        "action_time": "2024-04-25 07:48:50.123"
    },
    "event_key": "client_click",
    "event_type": "behavior_event"
}

@apiParamExample   {json} Body event (event-onpage event ra vào khối):
{
    "properties": {
        "node_id": 10,
        "campaign_id" : 1,
        "tracking_id": "uuid",
        "is_approach": bool,
        "is_start": bool,
        "entry_time": "2024-04-25 07:48:50.123",
        "is_finish": bool,
        "finish_time": "2024-04-25 07:48:50.123",
        "is_satisfied": bool
    },
    "event_key": "in-node",
    "event_type": "onpage"
}

@apiParamExample   {json} Body event (event-onpage event gửi):
{
    "properties": {
        "node_id": 10,
        "campaign_id" : 1,
        "tracking_id": "uuid",
        "popup_id": "uuid",
        "is_push": bool,
        "push_time": "2024-04-25 07:48:50.123"
        "push_error_reason": "str"
    },
    "event_key": "send",
    "event_type": "onpage"
}

@apiParamExample   {json} Body event (event-onpage click thông điệp):
{
    "properties": {
        "node_id": 10,
        "campaign_id" : 1,
        "tracking_id": "uuid",
        "popup_id": "uuid",
        "interacted_type ": "str",
        "interacted_position_id": "str",
        "interacted_time ": "2024-04-25 07:48:50.123"
        "interacted_action ": "str"
    },
    "event_key": "click",
    "event_type": "onpage"
}

@apiParamExample   {json} Body event (event-onpage thoát chiến dịch):
{
    "properties": {
        "campaign_id" : 1,
        "action_time ": "2024-04-25 07:48:50.123"
    },
    "event_key": "finish",
    "event_type": "onpage"
}


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": {
    "device": {
        "device_id": "509557504208797953",
        "profile_id": "uuid",
        "session_id": "509968998733971713"
    }
  },
  "code": 200,
  "message": "successful"
}
"""
****************************************** Sync Events ********************************************
* version: 1.0.1                                                                        		  *
***************************************************************************************************
"""
@api {post} /profiling/external/v3.0/device/sync Đồng bộ events
@apiDescription Api đồng bộ events thu thập được về cho hệ thống lưu trữ và xử lý.
@apiGroup SDK
@apiVersion 1.0.1
@apiName SyncEvents

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse json_header
@apiUse merchant_id_header
@apiHeader (Headers:)  {String}   app-id       name/id của channel sẽ nhận notification.

@apiParam   (Data:)    {Object}   device_info         thông tin của device
@apiParam   (Data:)    {Object}   app_info         thông tin của device
@apiParam   (Data:)    {Object}   [app_info]         thông tin của mobile app
@apiParam   (Data:)    {Object}   [sdk]         thông tin của sdk
@apiParam   (Data:)    {String}   request_id         mỗi lượt request sẽ có id riêng, để tránh trùng lặp request
@apiParam   (Data:)    {Object}   [consent]         thông tin của device
@apiParam   (Data:)    {Object}   [profile_attributes]         thông tin của profile
@apiParam   (Data:)    {Object}   [omni_campaign]         thông tin mà device này được tiếp cận từ omni channel
@apiParam   (Data:)    {Array}   events          Danh sách các event được đồng bộ

@apiParamExample [json] Body example:
{
    "events": [
        {
            "properties": {
                "node_id": 10,
                "campaign_id" : 1,
                "tracking_id": "uuid",
                "popup_id": "uuid",
                "interacted_type ": "str",
                "interacted_position_id": "str",
                "interacted_time ": "2024-04-25 07:48:50.123"
            },
            "event_key": "click",
            "event_type": "onpage",
            "campaign": [{
                "campaign_id": 1,
                "node_id": 1
            }]
        },
        {
            "properties": {
                "plan": "Pro Annual",
                "accountType" : "Facebook",
                "action_time": "2024-04-25 07:48:50.123"            
            },
            "event_key": "user_registered",
            "event_type": "behavior_event",
            "campaign": [{
                "campaign_id": 1,
                "node_id": 1
            }]
        },
        {
            "properties": {
                "property_1": "String property",
                "property_5" : 1,
                "action_time": "2024-04-25 07:48:50.123"            
            },
            "event_key": "client_click",
            "event_type": "behavior_event"
        },
        {
            "properties": {
                "tag_name": "button|url",
                "tag_id": "id cua tag do form sinh ra",
                "tag_value": "text value cua tag",
                "action_time": "2024-04-25 07:48:50.123"            
            },
            "event_type": "click",
            "campaign": [{
                "campaign_id": 1,
                "node_id": 1
            }],
            "form": {
                "site_id": "str",
                "form_id": "str"
            }
        },
        {
            "properties": {
                "action_time": "2024-04-25 07:48:50.123",
                "name": "Name of the page",
                "referrer": "Previous page’s full URL. Equivalent to document.referrer from the DOM API.",
                "search": "Query string portion of the page’s URL. Equivalent to location.search from the DOM API.",
                "title": "Page’s title. Equivalent to document.title from the DOM API.",
                "url" : "Page’s full URL. Segment first looks for the canonical URL. If the canonical URL is not provided, Segment uses location.href from the DOM API.",
                "path": "Path portion of the page’s URL. Equivalent to canonical path which defaults to location.pathname from the DOM API.",
                "keywords": ["A list/array of keywords describing the page’s content. The keywords would most likely be the same as, or similar to, the keywords you would find in an HTML meta tag for SEO purposes. This property is mainly used by content publishers that rely heavily on pageview tracking. This isn’t automatically collected."]            
            },
            "event_type": "page",
            "campaign": [{
                "campaign_id": 1,
                "node_id": 1
            }]
        },
    ],
    "device_info": {},
    "app_info": {},
    "sdk": {
        "version": "str like 2.1.1, 2.1.2-rc1",
        "code": "ID của app được tạo ra từ AppMarketPlace"
    },
    "request_id": "uuid",
    "consent": {},
    "profile_attributes": {},
    "omni_campaign": {}
}

@apiParamExample   {json} Body DeviceInfo (JS):
{
    "screen_height": 1136,
    "screen_width": 640,
    "timezone_offset": -4,
    "http_header_user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 7_1 like Mac OS X) AppleWebKit/537.51.2 (KHTML, like Gecko) Mobile/11D167 mParticle/2.5.0",
    "charset":"",
    "description": "",
    "language": "",
    "color": "",
    "viewport": "",
    "title": "",
    "url": "",
    "platforms": "tring / enum", # "iOS", "Android", "web", "tvOS", "roku", "out_of_band", "alexa", "smart_tv", "fire_tv", "xbox"
    "browser_platform": "Linux x86_64",
    "host": "document host",
    "path": "",
    "referrer": "document refferer",
    "finger_print": "finger print of current device, it cannot be null",
    "last_finger_print": "last finger print, it needed to check device change info"
}

@apiParamExample   {json} Body event (event-page):
{
    "properties": {
        "action_time": "2024-04-25 07:48:50.123",
        "name": "Name of the page",
        "referrer": "Previous page’s full URL. Equivalent to document.referrer from the DOM API.",
        "search": "Query string portion of the page’s URL. Equivalent to location.search from the DOM API.",
        "title": "Page’s title. Equivalent to document.title from the DOM API.",
        "url" : "Page’s full URL. Segment first looks for the canonical URL. If the canonical URL is not provided, Segment uses location.href from the DOM API.",
        "path": "Path portion of the page’s URL. Equivalent to canonical path which defaults to location.pathname from the DOM API.",
        "keywords": ["A list/array of keywords describing the page’s content. The keywords would most likely be the same as, or similar to, the keywords you would find in an HTML meta tag for SEO purposes. This property is mainly used by content publishers that rely heavily on pageview tracking. This isn’t automatically collected."]            
    },
    "event_type": "page",
    "campaign": [{
        "campaign_id": 1,
        "node_id": 1
    }]
}

@apiParamExample   {json} Body event (event-click):
{
    "properties": {
        "action_time": "2024-04-25 07:48:50.123",
        "tag_name": "button|url",
        "tag_id": "id cua tag do form sinh ra",
        "tag_value": "text value cua tag"            
    },
    "event_type": "click",
    "campaign": [{
        "campaign_id": 1,
        "node_id": 1
    }],
    "form": {
        "site_id": "str",
        "form_id": "str"
    }
}

@apiParamExample   {json} Body event (event-exit):
{
    "properties": {
        "action_time": "2024-04-25 07:48:50.123",
        "campaign_id": 1,
        "node_id": 2,
        "last_page": "string",
        "expire_time": "2024-04-25 07:48:50.123"            
    },
    "event_type": "exit"
}

@apiParamExample   {json} Body event (event-enable-notification):
{
    "properties": {
        "action_time": "2024-04-25 07:48:50.123",
        "status": "enable (enable|disable)",
        "push_id": "str",
        "push_type": 5 # WEB_PUSH_FIREBASE = 5, WEB_PUSH_APNS = 6      
    },
    "event_type": "notification"
}

@apiParamExample   {json} Body event (event-behaviour):
{
    "properties": {
        "property_1": "String property",
        "property_5" : 1,
        "action_time": "2024-04-25 07:48:50.123"
    },
    "event_key": "client_click",
    "event_type": "behavior_event"
}

@apiParamExample   {json} Body event (event-onpage event ra vào khối):
{
    "properties": {
        "node_id": 10,
        "campaign_id" : 1,
        "tracking_id": "uuid",
        "is_approach": bool,
        "is_start": bool,
        "entry_time": "2024-04-25 07:48:50.123",
        "is_finish": bool,
        "finish_time": "2024-04-25 07:48:50.123",
        "is_satisfied": bool
    },
    "event_key": "in-node",
    "event_type": "onpage"
}

@apiParamExample   {json} Body event (event-onpage event gửi):
{
    "properties": {
        "node_id": 10,
        "campaign_id" : 1,
        "tracking_id": "uuid",
        "popup_id": "uuid",
        "is_push": bool,
        "push_time": "2024-04-25 07:48:50.123"
        "push_error_reason": "str"
    },
    "event_key": "send",
    "event_type": "onpage"
}

@apiParamExample   {json} Body event (event-onpage click thông điệp):
{
    "properties": {
        "node_id": 10,
        "campaign_id" : 1,
        "tracking_id": "uuid",
        "popup_id": "uuid",
        "interacted_type ": "str",
        "interacted_position_id": "str",
        "interacted_time ": "2024-04-25 07:48:50.123"
        "interacted_action ": "str"
    },
    "event_key": "click",
    "event_type": "onpage"
}

@apiParamExample   {json} Body event (event-onpage thoát chiến dịch):
{
    "properties": {
        "campaign_id" : 1,
        "action_time ": "2024-04-25 07:48:50.123"
    },
    "event_key": "finish",
    "event_type": "onpage"
}


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": {
    "device": {
        "device_id": "509557504208797953",
        "profile_id": "uuid",
        "session_id": "509968998733971713"
    }
  },
  "code": 200,
  "message": "successful"
}
"""



**************************************** Collect Device ******************************************
* version: 1.0.0                                                                        		  *
***************************************************************************************************
"""
@api {get} /profiling/external/v3.0/device/collect Lấy thông tin profile/device
@apiDescription Api lấy thông tin profile/device
@apiGroup SDK
@apiVersion 1.0.0
@apiName CollectDevice

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)   {list}  keys		Danh sách các trường thông tin device/profile cần lấy và trả về cho client.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": {
    "profile": {
        "merchant_id": "uuid",
        "profile_id": "uuid",
        "name": "str",
        "district": int,
        "address": ["list"]
    },
    "device": {
        "device_id": "509557504208797953",
        "profile_id": "uuid",
        "session_id": "509968998733971713"
    },
  },
  
  "code": 200,
  "message": "successful"
}
"""
******************************* Get profile by profile owner *****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile_owner/actions/list [Done] L<PERSON>y danh sách theo profile owner
@apiDescription Dịch vụ lấy danh sách khách hàng theo profile owner
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens


@apiParam      (Body:)     {Array}     profile_filter       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam (Body:) {Array}      profile_group		        Danh sách các Group mà Staff này quản lý


@apiParam      (profile_filter:)     {Array}     values                           []
@apiParam      (profile_filter:)     {string}     criteria_key                     cri_profile_owner
@apiParam      (profile_filter:)     {string}     operator_key                     op_is_not_empty

@apiParamExample    {json}    Body example:
{
  "profile_filter": [
    {
      "operator_key": "op_is_not_empty",
      "criteria_key": "cri_profile_owner",
      "values": [
        ""
      ]
    }
  ],
  "profile_group": [
    "70a7422a-dc40-4937-a66b-f374f711b27a",
    "820a50a2-a6cc-4d41-ade7-820a04c2f0f2",
    "aa4ac4a5-9fa1-4225-98c4-53b0c2f6a1bf",
    "b19b95e3-93f6-4d37-b159-7b9aa2c224a2",
    "c2fa2c65-b221-47f8-8ccb-268b558d719c",
    "c4b8e92d-0b3a-463d-83a4-1eb9b3d546a9",
    "c512d944-de07-41e3-94d4-4b983a78842e",
    "cfb46e61-8c82-4fa4-a46d-1b502967a77d"
  ]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "created_account_type": 5,
            "created_time": "2021-10-20T09:25:01.059Z",
            "customer_created_time": null,
            "customer_id": "3C723A22-F0F1-4F5C-8297-1A0FA66288CA",
            "degree": null,
            "device_types": null,
            "devices": [
                {
                    "device_id": "C1FF297D-AE78-4C87-A534-15C1C88D5051",
                    "device_name": null,
                    "source": null
                }
            ],
            "id": "d6013579-3214-4653-803e-b3ed490c0e4c",
            "identify_code": [],
            "merchant_id": [
                "1b99bdcf-d582-4f49-9715-1b61dfff3924"
            ],
            "profile_group": [
                "541005c4-3dff-4063-8e77-66cc140015b8"
            ],
            "profile_id": "d6013579-3214-4653-803e-b3ed490c0e4c",
            "profile_identify": [],
            "profile_owner": "197320ce-adfc-4d47-bb7d-b3652e3d1521",
            "profile_tags": [],
            "province_code": -1,
            "push_id": [
                {
                    "accept_push": true,
                    "app_id": "IOS",
                    "count_fail": 0,
                    "device_id": null,
                    "is_logged": true,
                    "lang": "VI",
                    "last_access": "2021-10-21T04:03:51.000Z",
                    "os_type": 1,
                    "push_id": ""
                }
            ],
            "source": "APP",
            "updated_time": "2021-10-21T04:03:52.088Z",
            "ward_code": null
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "WzE2MzQ3ODQwMzIxMjAsICI1ZWVhZThlOC1hYTUyLTRjMmYtOWVmNi01YzNjNmRlODQzY2YiXQ==",
            "before": ""
        },
        "page": 1,
        "page_count": 13,
        "per_page": 10,
        "total_count": 123
    }
}
"""
******************************* Get user by profile_id ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/merchants/<merchant_id>/user/profile-id [Done] Lấy danh sách profile theo profile_ids
@apiDescription Dịch vụ lấy danh sách profile theo profile_ids
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName ListUser

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging


@apiParam   (Body:)    {String[]}    profile_ids       Mảng profile_id
@apiParam      (Body:)     {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]


@apiParamExample    {json}    Body example:
{
    "profile_ids": [
        "03f22509-7321-4f15-943a-d8c5b91e36b1",
        "27aeaa12-2d72-4441-b888-b7c26d6903a1"
    ],
    "fields": [
        "profile_id",
        "name",
        "birthday"
    ]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "birthday": null,
            "name": "Tái",
            "profile_id": "03f22509-7321-4f15-943a-d8c5b91e36b1"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
********************* Get profile by identification information ***********************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile/search_by_identify Search profile profile_identify
@apiDescription search profile_identify
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName SearchProfileByIdentify

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens


@apiParam      (Body:)        {String}    search                 Chuỗi tìm kiếm. Tìm theo <code>profile_identify</code>.
@apiParam      (Body:)        {Array}     profile_group          Danh sách các Group mà Staff này quản lý. VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']
@apiParam      (Body:)        {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Query:)       {String}    [sort]                 Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>


@apiParamExample    {json}    Body example:
{
  "search": "123",
  "fields": ["profile_id","name","birthday"],
  "profile_group": [
    "82448419-1888-44cb-bd59-3440153d66fc"
  ]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "c_data": [
        {
            "profile_id": "70a8fd6c-5609-438c-9cc3-fb571146dab9"
        }
    ],
    "code": 200,
    "data": [
        {
            "address": null,
            "created_time": "2022-12-05T07:03:40.719Z",
            "id": "70a8fd6c-5609-438c-9cc3-fb571146dab9",
            "is_non_profile": false,
            "merchant_id": [
                "1da163aa-a831-46bb-9bbf-d62f553dcab5"
            ],
            "name": "test_123",
            "phone_number": [
                "+84334567891"
            ],
            "profile_address": [],
            "profile_id": "70a8fd6c-5609-438c-9cc3-fb571146dab9",
            "profile_identify": [],
            "profile_tags": [],
            "tags": [],
            "updated_time": "2022-12-07T07:20:22.298Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "",
            "before": ""
        },
        "page": 1,
        "page_count": 1,
        "per_page": 5,
        "total_count": 1
    }
}
"""
******************************** Get profile by phone *********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile/search_by_phone Search profile by phone
@apiDescription search profile theo phone
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName SearchProfileByPhone

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens


@apiParam      (Body:)        {String}    search                 Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>.
@apiParam      (Body:)        {Array}     profile_group          Danh sách các Group mà Staff này quản lý. VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']
@apiParam      (Body:)        {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Query:)       {String}    [sort]                 Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>


@apiParamExample    {json}    Body example:
{
  "search": "0334567891",
  "fields": ["profile_id","name","birthday"],
  "profile_group": [
    "82448419-1888-44cb-bd59-3440153d66fc"
  ]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "c_data": [
        {
            "profile_id": "70a8fd6c-5609-438c-9cc3-fb571146dab9"
        }
    ],
    "code": 200,
    "data": [
        {
            "address": null,
            "created_time": "2022-12-05T07:03:40.719Z",
            "id": "70a8fd6c-5609-438c-9cc3-fb571146dab9",
            "is_non_profile": false,
            "merchant_id": [
                "1da163aa-a831-46bb-9bbf-d62f553dcab5"
            ],
            "name": "test_123",
            "phone_number": [
                "+84334567891"
            ],
            "profile_address": [],
            "profile_id": "70a8fd6c-5609-438c-9cc3-fb571146dab9",
            "profile_identify": [],
            "profile_tags": [],
            "tags": [],
            "updated_time": "2022-12-07T07:20:22.298Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "",
            "before": ""
        },
        "page": 1,
        "page_count": 1,
        "per_page": 5,
        "total_count": 1
    }
}
"""
******************************** Create Profile *********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/df/profile Create profile
@apiDescription Đầu external tạo profile
@apiGroup User
@apiVersion 1.0.0
@apiName CreateProfile

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Body:)    {String}       name        Tên đầy đủ của Profile.
@apiParam   (Body:)    {String}        [email_1]       Email chính của Profile.
@apiParam   (Body:)    {Array}       [email_2]]        Email phụ của Profile.

@apiParam      (Query:)     {int=-1->24}    source  Nguồn tạo profile 
<li><code>-1</code>:Khác </li>
<li><code>0</code>:Nhập Thủ Công </li>
<li><code>1</code>:Số Điện Thoại </li>
<li><code>2</code>:FaceBook </li>
<li><code>3</code>:G+ </li>
<li><code>4</code>:Landing Page </li>
<li><code>5</code>:Nhập Từ File </li>
<li><code>6</code>:Zalo </li>
<li><code>7</code>:Instagram </li>
<li><code>8</code>:Mobile App </li>
<li><code>9</code>:Youtube </li>
<li><code>10</code>:Zalo </li>
<li><code>11</code>:Smart Wifi </li>
<li><code>12</code>:Call Center </li>
<li><code>13</code>:Social </li>
<li><code>15</code>:Phong Vũ Odoo</li>
<li><code>16</code>:Phong Vũ Magento</li>
<li><code>17</code>:Phong Vũ Inhouse</li>
<li><code>18</code>:Phong Vũ Asia</li>
<li><code>19</code>:Face ID </li>
<li><code>20</code>:Line </li>
<li><code>21</code>:Website </li>
<li><code>22</code>:POS </li>
<li><code>23</code>:Core </li>
<li><code>24</code>:Sale </li>


@apiParamExample Body example:
{
  "name": "Nguyen Van A",
  "email_1": "<EMAIL>",
  "email_2": ["<EMAIL>", "<EMAIL>"]
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "message": "request thành công.",
  "data": {
    "name": "Nguyen Van A",
    "profile_id": "aa571f87-1f73-4504-895d-417d6c417747",
    "merchant_id": ["d3b3aea1-0daf-4a48-8609-279497dc96e9"],
    "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      },
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
  }
}
"""

************************************ Update Profile ***********************************
* version: 1.0.0                                                                       *
***************************************************************************************
"""
@api {put} [HOST]/profiling/external/v3.0/df/profile/<profile_id> Update Profile.
@apiDescription  Update Profile
@apiGroup User
@apiVersion 1.0.0
@apiName  ExternalUpdateProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)    {String}       name        Tên đầy đủ của Profile.
@apiParam   (Body:)    {String}        [primary_phone]       SDT chính của Profile.
@apiParam   (Body:)    {String}        [profile_owner]       profile_owner.


@apiParamExample Body example:
{
  "name": "Nguyen Van A",
    "primary_phone": "+84833666555",
    "profile_owner": "f8aa3926-7a74-4fcf-8135-73fad9c967a7",
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "message": "request thành công.",
  "data": {
    "name": "Nguyen Van A",
    "profile_id": "aa571f87-1f73-4504-895d-417d6c417747",
    "merchant_id": ["d3b3aea1-0daf-4a48-8609-279497dc96e9"],
    "primary_phone": {
        "last_verify": "2023-06-12T04:02:21.826Z",
        "phone_number": "+84833666555",
        "status": 0
    },
    "profile_owner": "f8aa3926-7a74-4fcf-8135-73fad9c967a7"
  }
}
"""

******************************** Get profile by name *********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile/search_by_name Search profile by name
@apiDescription search profile theo name
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName SearchProfileByName

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens


@apiParam      (Body:)        {String}    search                 Chuỗi tìm kiếm. Tìm theo <code>name</code>.
@apiParam      (Body:)        {Array}     profile_group          Danh sách các Group mà Staff này quản lý. VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']
@apiParam      (Body:)        {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Query:)       {String}    [sort]                 Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>


@apiParamExample    {json}    Body example:
{
  "search": "test_123",
  "fields": ["profile_id","name","birthday"],
  "profile_group": [
    "82448419-1888-44cb-bd59-3440153d66fc"
  ]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "c_data": [
        {
            "profile_id": "70a8fd6c-5609-438c-9cc3-fb571146dab9"
        }
    ],
    "code": 200,
    "data": [
        {
            "address": null,
            "created_time": "2022-12-05T07:03:40.719Z",
            "id": "70a8fd6c-5609-438c-9cc3-fb571146dab9",
            "is_non_profile": false,
            "merchant_id": [
                "1da163aa-a831-46bb-9bbf-d62f553dcab5"
            ],
            "name": "test_123",
            "phone_number": [
                "+84334567891"
            ],
            "profile_address": [],
            "profile_id": "70a8fd6c-5609-438c-9cc3-fb571146dab9",
            "profile_identify": [],
            "profile_tags": [],
            "tags": [],
            "updated_time": "2022-12-07T07:20:22.298Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "",
            "before": ""
        },
        "page": 1,
        "page_count": 1,
        "per_page": 5,
        "total_count": 1
    }
}
"""

******************************** Get profile exact cif *********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile/search_exact_cif Search profile exact cif
@apiDescription Search profile exact cif
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName SearchProfileExactCif

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens


@apiParam      (Body:)        {String}    search                 Chuỗi tìm kiếm. Tìm theo <code>cif</code>.
@apiParam      (Body:)        {Array}     profile_group          Danh sách các Group mà Staff này quản lý. VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']
@apiParam      (Body:)        {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Query:)       {String}    [sort]                 Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>


@apiParamExample    {json}    Body example:
{
  "search": "10123",
  "fields": ["profile_id","name","birthday"],
  "profile_group": [
    "82448419-1888-44cb-bd59-3440153d66fc"
  ]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "c_data": [
        {
            "profile_id": "70a8fd6c-5609-438c-9cc3-fb571146dab9"
        }
    ],
    "code": 200,
    "data": [
        {
            "address": null,
            "created_time": "2022-12-05T07:03:40.719Z",
            "id": "70a8fd6c-5609-438c-9cc3-fb571146dab9",
            "cif": [
                "10123"
            ],
            "is_non_profile": false,
            "merchant_id": [
                "1da163aa-a831-46bb-9bbf-d62f553dcab5"
            ],
            "name": "test_123",
            "phone_number": [
                "+84334567891"
            ],
            "profile_address": [],
            "profile_id": "70a8fd6c-5609-438c-9cc3-fb571146dab9",
            "profile_identify": [],
            "profile_tags": [],
            "tags": [],
            "updated_time": "2022-12-07T07:20:22.298Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "",
            "before": ""
        },
        "page": 1,
        "page_count": 1,
        "per_page": 5,
        "total_count": 1
    }
}
"""
********************************* Media Create ********************************
*version: 1.0.0                                                                      *
*************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/merchant/profile/<profile_id>/media CreateMedia.
@apiDescription CreateMedia
@apiGroup Media
@apiVersion 1.0.0
@apiName  CreateMedia
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Query:)	 {String} type	Loại media viết cách nhau bởi dấu phẩy VD: OTHER,IDENTIFICATION_INFO OTHER: Khác , IDENTIFICATION_INFO: Thông tin định danh
@apiParam	(Form:)	  {File}	      file	 File data


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "64abc565b96612ff39f4b580",
            "profile_id": "64a515b5ed4d95b66c434e38",
        "created_time": "2023-07-10T08:46:29.1688978789Z",
        "deleted_time": null,
        "format_file": "image/jpeg",
        "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
        "status": "ACTIVE",
        "title": "5b0e7a9de497d96c4414ebd8dd47bddf.jpg",
        "type": [
            "IDENTIFICATION_INFO",
            "OTHER"
        ],
        "updated_time": "2023-07-10T08:46:29.1688978789Z",
        "url": "https://t1.mobio.vn/static/10350f12-1bd7-4369-9750-46d35c416a46/upload/1688978786_5b0e7a9de497d96c4414ebd8dd47bddf.jpg"
    },
    "lang": "vi",
    "message": "request th\u00e0nh c\u00f4ng."
}
"""

********************************* Media Update ********************************
* version: 1.0.0                                                                      *
* *************************************************************************************
"""
@api {put} [HOST]/profiling/external/v3.0/merchant/profile/<profile_id>/media/<media_id> Update Media.
@apiDescription Update Media
@apiGroup Media
@apiVersion 1.0.0
@apiName  Update Media
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam	(Query:)			{String}	            type			          Danh sách loại media, cách nhau bằng dấu ","
                                                                        <code>
                                                                          <ul>
                                                                            <li>IDENTIFICATION_INFO: Thông tin định danh</li>
                                                                            <li>OTHER: Khác</li>
                                                                          </ul>
                                                                        </code>
@apiParam	(Query:)			{String}	                    display		          Kiểu hiển thị
@apiParam	(Form:)			{File}	                    file		            File data

@apiParamExample {json} Body example
{
    "file": <File>
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "64abc611d8f7fc8c68c414d3",
        "profile_id": "649a997a8290b2722f7db87f",
        "created_time": "2023-07-10T08:49:21.1688953761Z",
        "deleted_time": null,
        "format_file": "image/png",
        "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
        "status": "ACTIVE",
        "title": "encode2.png",
        "type": [
            "OTHER",
            "IDENTIFICATION_INFO"
        ],
        "updated_time": "2023-07-10T08:53:01.1688953981Z",
        "url": "https://t1.mobio.vn/static/10350f12-1bd7-4369-9750-46d35c416a46/upload/encode2.png"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

********************************* Media Delete ********************************
*version: 1.0.0                                                                      *
*************************************************************************************
"""
@api {delete} [HOST]/profiling/external/v3.0/merchant/profile/<profile_id>/media/<media_id> Delete Media.
@apiDescription Delete Media
@apiGroup Media
@apiVersion 1.0.0
@apiName  Delete Media
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": null
  "lang": "vi", 
  "message": "request thành công."
}
"""

********************************* Media List ********************************
*version: 1.0.0                                                                      *
*************************************************************************************
"""
@api {get} [HOST]/profiling/external/v3.0/merchant/profile/<profile_id>/media List Media.
@apiDescription List Media
@apiGroup Media
@apiVersion 1.0.0
@apiName  List Media
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Param:)			{String}	          type			Loại media, OTHER: Khác , IDENTIFICATION_INFO: Thông tin định danh

@apiParamExample {json} Param example
{
    "type": "IDENTIFICATION_INFO;OTHER",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "_id": "64abc565b96612ff39f4b580",
            "profile_id": "64a515b5ed4d95b66c434e38",
            "created_time": "2023-07-10T08:46:29.1688978789Z",
            "deleted_time": null,
            "format_file": "image/jpeg",
            "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
            "status": "ACTIVE",
            "title": "5b0e7a9de497d96c4414ebd8dd47bddf.jpg",
            "type": [
                "IDENTIFICATION_INFO",
                "OTHER"
            ],
            "updated_time": "2023-07-10T08:46:29.1688978789Z",
            "url": "https://t1.mobio.vn/static/10350f12-1bd7-4369-9750-46d35c416a46/upload/1688978786_5b0e7a9de497d96c4414ebd8dd47bddf.jpg"
        }
    ],
    "lang": "vi",
    "message": "request th\u00e0nh c\u00f4ng.",
    "paging": {
        "page": -1,
        "per_page": 10,
        "total_count": 1
    }
}
"""
************************************ External Upsert Profile ***********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/customers/actions/upsert Upsert Profile.
@apiDescription Upsert Profile
@apiGroup Customers
@apiVersion 1.0.0
@apiName ExternalUpsertProfile
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [group]     Chuỗi query nhận diện group của Merchant.


@apiParam   (Body:)    {String}          name             Tên đầy đủ.

@apiParam   (Body:)    {String}      [primary_email]              Email chính của khách hàng.
@apiParam   (Body:)    {String}      [primary_phone]       Số điện thoại chính của khách hàng.
@apiParam   (Body:)    {String}      [customer_id]         ID của khách hàng.
@apiParam   (Body:)    {String[]}      [secondary_emails]                     Các email phụ của khách hàng.
@apiParam   (Body:)    {String[]}      [secondary_phones]                     Các số điện thoại phụ của khách hàng.
@apiParam   (Body:)    {String}      [ewallet_id]           ID của ví.
@apiParam   (Body:)    {String}      [nationality]          Quốc gia của khách hàng.
@apiParam   (Body:)    {String}      [gender]               Giới tính của khách hàng. 
<li><code>UNKNOWN</code></li>
<li><code>MALE</code></li>
<li><code>FEMALE</code></li>
@apiParam   (Body:)    {String}      [birthday]             Ngày sinh của khách hàng. Format <code>YYYY-mm-DD</code>.
@apiParam   (Body:)    {String}      [province_code]        Quận/Huyện của khách hàng.
@apiParam   (Body:)    {String}      [source]               Nguồn tạo tài khoản của khách hàng.
@apiParam   (Body:)     {Int}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>


@apiParamExample [json] Body example:
{ 
  "name": "andrew",
  "primary_phone": "0914123456",
  "primary_email": "<EMAIL>",
  "secondary_phones": ["0904123456"],
  "secondary_emails": ["<EMAIL>"],
  "address": "Cầu Giấy, Hà Nội",
  "gender": "MALE",
  "birthday": "1989-01-22",
  "marital_status":1,
  "province_code":"Hà Nội",
  "_dyn_product_12_1579107600": ["laptop asus"],
  "_dyn_last_login_1579107600": "2020-02-12 10:55:51",
  "_dyn_total_amount_1579107600": 10000000
}


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
  "profile_info": {
    "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
    "merchant_id": "4a9f6012-5627-4239-b281-4adb85c00827",
    "address": "",
    "avatar": "",
    "birthday": "1989-01-22T00:00:00",
    "birth_year": 1989,
    "email": ["<EMAIL>", "<EMAIL>"],
    "fax": "",
    "gender": 1,
    "hobby": [""],
    "job": [""],
    "marital_status": 1,
    "name": "andrew",
    "nation": [""],
    "phone_number": ["+84914123456", "+***********"],
    "province_code": [""],
    "tax_code": "",
    "ward_code": [""],
    "workplace": ""
  },
  "message": "Tạo mới profile thành công",
  "process_type": "ADD",
}
"""

************************************ External Profile Check Data ***********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/customers/actions/check_in_data Profile Check Data.
@apiDescription Profile Check Data
@apiGroup Customers
@apiVersion 1.0.0
@apiName ExternalProfileCheckData
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {Object}     data_profile       Các điều kiện để search profile 
@apiParam (Body:) {Object}      data_check		        Các điều kiện để check profile

@apiParamExample    {json}    Body example:
{
  "data_profile": {
    "profile_id": "70a7422a-dc40-4937-a66b-f374f711b27a",
    "primary_email": "<EMAIL>",
    "name": "eceC",
    "source": "NUTIFOOD"
  },
  "data_check": {
    "_dyn_test_key_mapping_1692868024703": "70a7422a-dc40-4937-a66b-f374f711b27a",
    "gender": 1
  }
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "exists_data": True  
    "lang": "vi",
    "message": "request thành công."
}
"""


********************* Get profile by cif name phone identification information ***********************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile/search_by_cnpi Search profile by cif, name, phone, profile_identify By ELS
@apiDescription search profile theo cif, name, phone, profile_identify
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName SearchProfileByCifNamePhoneIdentify

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens


@apiParam      (Body:)        {String}    search                 Chuỗi tìm kiếm. Tìm theo <code>cif</code>, <code>name</code><code>phone_number</code> hoặc <code>profile_identify</code>.
@apiParam      (Body:)        {Array}     profile_group          Danh sách các Group mà Staff này quản lý. VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']
@apiParam      (Body:)        {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Query:)       {String}    [sort]                 Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>


@apiParamExample    {json}    Body example:
{
  "search": "123",
  "fields": ["profile_id","name","birthday"],
  "profile_group": [
    "82448419-1888-44cb-bd59-3440153d66fc"
  ]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "c_data": [
        {
            "profile_id": "70a8fd6c-5609-438c-9cc3-fb571146dab9"
        }
    ],
    "code": 200,
    "data": [
        {
            "address": null,
            "created_time": "2022-12-05T07:03:40.719Z",
            "id": "70a8fd6c-5609-438c-9cc3-fb571146dab9",
            "is_non_profile": false,
            "merchant_id": [
                "1da163aa-a831-46bb-9bbf-d62f553dcab5"
            ],
            "name": "test_123",
            "phone_number": [
                "+84334567891"
            ],
            "profile_address": [],
            "profile_id": "70a8fd6c-5609-438c-9cc3-fb571146dab9",
            "profile_identify": [],
            "profile_tags": [],
            "tags": [],
            "updated_time": "2022-12-07T07:20:22.298Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "",
            "before": ""
        },
        "page": 1,
        "page_count": 1,
        "per_page": 5,
        "total_count": 1
    }
}
"""


********************* Get profile by satellite ****************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile/find_by_satellite Search profile by phone, email, customer_id,cif  By Mongo
@apiDescription search profile theo phone, email, customer_id, cif từ mongo 
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName SearchProfileByPhoneEmailCustomerIdCifName

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)        {dict}    condition                Điều kiện tìm kiếm.
@apiParam      (condition:)   {string}    find_by                Tìm theo <code>cif</code>, <code>phone_number</code> hoặc <code>customer_id</code> hoặc <code>email</code>.
@apiParam      (condition:)   {string}     value                 Mảng giá trị tối đa 5 giá trị
@apiParam      (Body:)        {Array}     [fields]               Danh sách các thuộc tính cần trả ra không có trả hết. Ví dụ: ["profile_id","name","birthday"]

@apiParamExample    {json}    Body example:
{
    "condition": {
        "find_by": "phone_number",
        "value": [
            "+84927024348","+84927024348","+84927024349","+84320000003"
        ]
    },
    "fields": [
        "_id", "name", "phone_number", "age", "profile_id", "cif", "email"
    ]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "_id": "65728d8631d4d34fcc2c8692",
            "address": null,
            "age": null,
            "cif": [
                "65656"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "9f78cca1-0a64-4b24-8ef0-1db4f65b3748",
            "name": "Trần Thị Lan 014",
            "phone_number": [
                "+84927024348"
            ],
            "profile_address": [],
            "profile_id": "9f78cca1-0a64-4b24-8ef0-1db4f65b3748",
            "profile_tags": [],
            "tags": []
        },
        {
            "_id": "65728d8631d4d34fcc2c8696",
            "address": null,
            "age": null,
            "cif": [
                "65655"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "a6cd9dd7-e6cf-4f9c-9b40-2bf64ba25efa",
            "name": "Trần Thị Lan 015",
            "phone_number": [
                "+84927024349"
            ],
            "profile_address": [],
            "profile_id": "a6cd9dd7-e6cf-4f9c-9b40-2bf64ba25efa",
            "profile_tags": [],
            "tags": []
        },
        {
            "_id": "655436190ee1877c7ab1c01e",
            "address": null,
            "age": null,
            "cif": [
                "65653"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "f8633fc3-18c6-401e-9ba6-df162ecfc8ae",
            "name": "Lypt 04",
            "phone_number": [
                "+84320000003"
            ],
            "profile_address": [],
            "profile_id": "f8633fc3-18c6-401e-9ba6-df162ecfc8ae",
            "profile_tags": [],
            "tags": []
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
********************* Get profile by conditions ****************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile/find_by_conditions Tìm profile bằng với nhiều điều kiện và với nhau bằng mongo
@apiDescription Tìm profile bằng với nhiều điều kiện và với nhau bằng mongo
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName FindByConditions

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)        {array}    conditions                Danh sách các điều kiện tìm kiếm.
@apiParam      (conditions:)   {String}   [cif]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [email]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [phone_number]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [source]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [customer_id]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {Array}   [profile_identify]   Giá trị cần tìm kiếm.
@apiParam      (profile_identify:)   {String}   identify_type   driving_license, passport, identity_card, citizen_identity, identity_card_army, birth_certificate, visa, temporary_residence_card, other.
@apiParam      (profile_identify:)   {String}   identify_value   Giá trị cần tìm kiếm.
@apiParam      (Body:)        {Array}     fields               Danh sách các thuộc tính cần trả ra không có trả hết. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Body:)        {String}     source               call từ module nào

@apiParamExample    {json}    Body example:
{
    "conditions": [
        {
            "primary_email": "<EMAIL>",
            "cif": "20231173",
            "source": "Core",
            "profile_identify": [
                {
                    "identify_type": "citizen_identity",
                    "identify_value": "cccd123"
                }
            ]
        }
    ],
    "fields": [],
    "source": "Mobile"
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công.",
    "results": [
        {
            "request": {
                "cif": "20231173",
                "email": "<EMAIL>",
                "profile_identify": [
                    {
                        "identify_type": "citizen_identity",
                        "identify_value": "cccd123"
                    }
                ],
                "source": "Core"
            },
            "responses": [
                {
                    "cards": [],
                    "cif": [
                        "20231173"
                    ],
                    "created_time": "2023-12-20T07:39:13.044000Z",
                    "merchant_id": [
                        "57d559c1-39a1-4cee-b024-b953428b5ac8"
                    ],
                    "mkt_consent": "Có",
                    "name": "Hiendt371",
                    "predict": [
                        {
                            "confidence": 0.9929109216,
                            "key": "gender",
                            "value": "3"
                        }
                    ],
                    "primary_email": {
                        "detail": null,
                        "email": "<EMAIL>",
                        "last_check": "2023-12-20T07:39:13.043000Z",
                        "state": null,
                        "status": 0
                    },
                    "profile_group": [
                        "06b1b8b3-9bb5-4ba4-b70e-b2f5d472dc8f"
                    ],
                    "profile_id": "d7fdd2c6-e8b2-40bb-9322-6542e29b21fa",
                    "profile_identify": [
                        {
                            "date_verify": null,
                            "identify_type": "citizen_identity",
                            "identify_value": "cccd123",
                            "is_verify": false,
                            "verify_by": null
                        }
                    ],
                    "source": "Core",
                    "tracking_consent": "Có",
                    "updated_time": "2023-12-21T04:00:24.879000Z",
                }
            ]
        }
    ]
}
"""
*************************** Get list profile by ids without ABAC ***********************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile/search_by_profile_ids/limit_field Get list profile by ids limit field without ABAC.
@apiDescription Get list profile by profile ids limit field  without ABAC
@apiGroup ABAC
@apiVersion 1.0.0
@apiName GetProfileByIdsLimitField

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Array}    profile_ids              Danh sách các Profile Ids. Max = 100

@apiParamExample  {json} Body request example
{
    "profile_ids": [
        "4b1d793d-e999-4066-9614-6f0bf4fd3c9e"
    ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
        {
            "avatar": "https://graph.facebook.com/737323523274672/picture?type=large&access_token=EAAENpMUscdQBAHmFJwuZASXrdCBOOcH4CWKglGTduP9S5OuRdckZCSqbtz5Aw2XnsZCoCMaMzHeWVdZAktop2FfHBopl4fs0NySUhZCdGD5oxFKiI2yNAsd0sgjXqx3sHx4fhQEUuY3kWp9lOocvkfiNZCl1HbLX8iJCx5aYDjkgZDZD",
            "name": "Nguyễn Thị Thùy",
            "profile_id": "4b1d793d-e999-4066-9614-6f0bf4fd3c9e"
        }
    ]
}
"""
*************************************  API danh sách profile từ module khác (cột 3)*****************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/merchants/<merchant_id>/customers/actions/list_assign Danh sách profile khi chọn gắn profile ở cột 3
@apiDescription Danh sách profile khi chọn gắn profile ở cột 3
@apiGroup ABAC
@apiVersion 1.0.0
@apiName ListAssign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {String}    ignore_profile_id                  Danh sach profile_id can loai tru 
@apiParam      (Body:)     {Array}    profile_group                       Danh sách các Group mà Staff này quản lý. VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']
@apiParam      (Body:)     {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id", "name", "email", "phone_number", "cif", "primary_email", "secondary_emails",
                      "primary_phone", "secondary_phones", "social_user"]
@apiParam	   (Body:)			{Array}	[search_with_fields]							  Danh sách field search với value search_text [name , email, phone_number, tags, devices, customer_id, cif, social_name, profile_identify]
@apiSuccess {Array}   data    Danh sách profile 
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiParamExample    {json}    Body example:
{
  "search": "search_str"
}
@apiSuccessExample {json} Response example
{
    "c_data": [
        {
            "profile_id": "acf22893-7eee-4c7f-bdc3-172e5eda588f"
        }
    ],
    "code": 200,
    "data": [
        {
            "address": null,
            "cif": [],
            "created_time": "2023-07-19T15:06:35.384Z",
            "email": [
                "<EMAIL>"
            ],
            "id": "acf22893-7eee-4c7f-bdc3-172e5eda588f",
            "merchant_id": [
                "972e6e1d-8891-4fdb-9d02-8a7855393298"
            ],
            "name": "Ho\u00e0i ABAC Note Task",
            "phone_number": [
                "+84940492039"
            ],
            "primary_email": {
                "detail": null,
                "email": "<EMAIL>",
                "last_check": "2023-07-19T15:06:35.381Z",
                "state": null,
                "status": 0
            },
            "primary_phone": {
                "last_verify": "2023-07-19T15:06:35.382Z",
                "phone_number": "+84940492039",
                "status": 0
            },
            "profile_address": [],
            "profile_id": "acf22893-7eee-4c7f-bdc3-172e5eda588f",
            "profile_tags": [],
            "secondary_emails": {
                "secondary": [],
                "secondary_size": 0
            },
            "secondary_phones": {
                "secondary": [],
                "secondary_size": 0
            },
            "social_user": [],
            "tags": [],
            "updated_time": "2023-07-19T15:06:36.553Z"
        }
        
    ],
    "lang": "vi",
    "message": "request th\u00e0nh c\u00f4ng.",
    "paging": {
        "cursors": {
            "after": "WzE2ODg1NDgyODIzMjksICI5MWExYTQwMC1jNGI4LTRlMWYtODQyYS0yNTAzMjZmYjgwY2YiXQ==",
            "before": ""
        },
        "page": 1,
        "page_count": 3117,
        "per_page": 10,
        "total_count": 31169
    }
}
"""

************************************* API chi tiết profile từ CDP **********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/external/v3.0/merchants/<merchant_id>/cdp_view_profile/<profile_id>/info Chi tiết profile từ CDP
@apiDescription Chi tiết profile từ CDP
@apiGroup ABAC
@apiVersion 1.0.0
@apiName CDPViewDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.
@apiParam      (Resource:)    {String}    profile_id                             ID của profile.

@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng

@apiSuccess   (customer:)     {String}     id                               ID khách hàng
@apiSuccess   (customer:)     {String}    name                              Tên khách hàng
@apiSuccess   (customer:)     {Array}     phone_number                      Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Number}    created_account_type              Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (customer:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (customer:)     {Number}    gender                            Giới tính
@apiSuccess   (customer:)     {String}    address                           Địa chỉ
@apiSuccess   (customer:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (customer:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (customer:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (customer:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (customer:)     {String}    birthday                          Ngày sinh
@apiSuccess   (customer:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (customer:)     {Number}    income                            Mức thu nhập
@apiSuccess   (customer:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess   (customer:)     {DateTime}    created_time                      Thời gian tạo profile
@apiSuccess   (customer:)     {DateTime}    updated_time                      Thời gian cập nhật profile
@apiSuccess   (customer:)     {Array}     cards                             Dữ liệu thẻ khách hàng

@apiSuccess   (customer:cards:)     {String}     user_card_id                     Id khách hàng thẻ
@apiSuccess   (customer:cards:)     {String}     id                               Id thẻ
@apiSuccess   (customer:cards:)     {String}     code                             Mã thẻ
@apiSuccess   (customer:cards:)     {Number}     status                           Trạng thái thẻ
@apiSuccess   (customer:cards:)     {DateTime}   approved_time                    Thời điểm duyệt thẻ

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "profile": {
        "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
        "name": "andrew",
        "phone_number": ["+***********", "+***********"],
        "email": ["<EMAIL>", "<EMAIL>"],
        "created_account_type": 1,
        "avatar": "",
        "created_time": "2017-12-12T15:12:28Z",
        "updated_time": "2017-12-12T15:12:28Z",
        "gender": 2,
        "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
        "province_code": 1,
        "district_code": 1,
        "ward_code": 1,
        "marital_status": 1,
        "birthday": "1989-09-17",
        "religiousness": 1,
        "nation": 1,
        "job": 39,
        "operation": null,
        "hobby": "Giày dép;Phim;Chạy bộ",
        "operation": null,
        "income": null,
        "budget": null,
        "cards": [{
              "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
              "code": "124364",
              "status": 1,
              "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304",
              "approved_time": "2017-12-12T15:12:28Z"
        }]
    }
}
"""
********************* Check profile by conditions ****************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile/check_by_conditions Check profile by condition
@apiDescription Kiểm tra profile bằng với nhiều điều kiện và với nhau bằng mongo
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName CheckByConditions

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)        {array}    conditions                Danh sách các điều kiện tìm kiếm.
@apiParam      (conditions:)   {String}   [cif]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [email]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [phone_number]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [source]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {String}   [customer_id]   Giá trị cần tìm kiếm.
@apiParam      (conditions:)   {Array}   [profile_identify]   Giá trị cần tìm kiếm.
@apiParam      (profile_identify:)   {String}   identify_type   driving_license, passport, identity_card, citizen_identity, identity_card_army, birth_certificate, visa, temporary_residence_card, other.
@apiParam      (profile_identify:)   {String}   identify_value   Giá trị cần tìm kiếm.
@apiParam      (Body:)        {Array}     fields               Danh sách các thuộc tính cần trả ra không có trả hết. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Body:)        {String}     source               call từ module nào

@apiParamExample    {json}    Body example:
{
    "conditions": [
        {
            "primary_email": "<EMAIL>",
            "cif": "20231173",
            "source": "Core",
            "profile_identify": [
                {
                    "identify_type": "citizen_identity",
                    "identify_value": "cccd123"
                }
            ]
        }
    ],
    "source": "Mobile"
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": true,
    "lang": "vi",
    "message": "request thành công."
}
"""

********************************* Media Update ********************************
* version: 1.0.0                                                                      *
* *************************************************************************************
"""
@api {put} [HOST]/profiling/external/v3.0/merchant/profile/<profile_id>/modify_media/<media_id> Modify Media.
@apiDescription Modify Media
@apiGroup Media
@apiVersion 1.0.0
@apiName  Modify Media
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (BODY:) {string}                         data            <code>data</code> dữ liệu modify  
@apiParam   (data:) {string}                         title            <code>data</code> trường dữ liệu cần update 

@apiParamExample {json} Body example
    "data":{
        "title": "test2"
    }
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "64abc611d8f7fc8c68c414d3",
        "profile_id": "649a997a8290b2722f7db87f",
        "created_time": "2023-07-10T08:49:21.1688953761Z",
        "deleted_time": null,
        "format_file": "image/png",
        "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
        "status": "ACTIVE",
        "title": "encode2.png",
        "type": [
            "OTHER",
            "IDENTIFICATION_INFO"
        ],
        "updated_time": "2023-07-10T08:53:01.1688953981Z",
        "url": "https://t1.mobio.vn/static/10350f12-1bd7-4369-9750-46d35c416a46/upload/encode2.png"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

********************* Get profile by multiple satellite ** ****************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {post} [HOST]/profiling/external/v3.0/profile/find_by_multiple_satellite Search profile by multi condition in Mongo
@apiDescription search profile theo phone, email, customer_id, cif, profile_identify từ mongo 
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName SearchMultipleProfileByPhoneEmailCustomerIdCifName

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)        {Aray(dict)}    conditions                Điều kiện tìm kiếm.
@apiParam      (condition:)   {string}    find_by                Tìm theo 
                                                                    <ul>
                                                                        <li><code>phone</code></li>
                                                                        <li><code>email</code></li>
                                                                        <li><code>customer_id</code></li>
                                                                        <li><code>cif</code></li>
                                                                        <li><code>profile_identify</code></li>
                                                                        <li><code>source</code></li>
                                                                    </ul>
@apiParam      (condition:)   {Aray(string)}     value                 Mảng giá trị tối đa 5 giá trị
@apiParam      (condition:)   {Aray(string)}     type                  Loại giấy tờ định danh. Nếu có nhiều loại giấy tờ định danh sẽ or với nhau
                                                                    <ul>
                                                                      <li><code>citizen_identity: </code> CCCD </li>
                                                                      <li><code>identity_card: </code> CMND </li>
                                                                      <li><code>passport: </code> Hộ chiếu </li>
                                                                      <li><code>driving_license: </code> GPLX </li>
                                                                      <li><code>identity_card_army: </code> CMND Quân đội </li>
                                                                      <li><code>birth_certificate: </code> Giấy khai sinh </li>
                                                                      <li><code>visa: </code> VISA </li>
                                                                      <li><code>temporary_residence_card: </code> Thẻ tạm trú </li>
                                                                      <li><code>other: </code> Khác </li>
                                                                      <li><code>identity_card_police: </code> Giấy chứng minh Công an nhân dân </li>
                                                                      <li><code>resident_card: </code> Thẻ thường trú </li>
                                                                      <li><code>visa_exemption: </code> Giấy miễn thị thực </li>
                                                                    </ul>       
@apiParam      (Body:)        {Array}     [fields]               Danh sách các thuộc tính cần trả ra không có trả hết. Ví dụ: ["profile_id","name","birthday"]

@apiParamExample    {json}    Body example:
{
    "conditions": [
        {
            "find_by": "cif",
            "value": [
                "CIF1", "CIF2"
            ]
        },
        {
            "find_by": "profile_identify",
            "type": ["citizen_identity", "identity_card"]
            "value": [
                "CCCD1694", "CCCD1695"
            ]
        }
    ],
    "fields": [
        "_id", "name", "phone_number", "age", "profile_id", "cif", "email"
    ]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "_id": "65728d8631d4d34fcc2c8692",
            "address": null,
            "age": null,
            "cif": [
                "65656"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "9f78cca1-0a64-4b24-8ef0-1db4f65b3748",
            "name": "Trần Thị Lan 014",
            "phone_number": [
                "+84927024348"
            ],
            "profile_address": [],
            "profile_id": "9f78cca1-0a64-4b24-8ef0-1db4f65b3748",
            "profile_tags": [],
            "tags": []
        },
        {
            "_id": "65728d8631d4d34fcc2c8696",
            "address": null,
            "age": null,
            "cif": [
                "65655"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "a6cd9dd7-e6cf-4f9c-9b40-2bf64ba25efa",
            "name": "Trần Thị Lan 015",
            "phone_number": [
                "+84927024349"
            ],
            "profile_address": [],
            "profile_id": "a6cd9dd7-e6cf-4f9c-9b40-2bf64ba25efa",
            "profile_tags": [],
            "tags": []
        },
        {
            "_id": "655436190ee1877c7ab1c01e",
            "address": null,
            "age": null,
            "cif": [
                "65653"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "f8633fc3-18c6-401e-9ba6-df162ecfc8ae",
            "name": "Lypt 04",
            "phone_number": [
                "+84320000003"
            ],
            "profile_address": [],
            "profile_id": "f8633fc3-18c6-401e-9ba6-df162ecfc8ae",
            "profile_tags": [],
            "tags": []
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
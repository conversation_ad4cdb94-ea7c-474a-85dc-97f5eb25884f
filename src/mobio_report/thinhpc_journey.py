******************************* ReportOpenClickLink ******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/open-click-link Báo cáo chung - Báo cáo tỉ lệ mở và bấm link (Done)
@apiDescription Báo cáo tỉ lệ mở và bấm link
@apiGroup Journey
@apiVersion 1.0.0
@apiName ReportOpenClickLink

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccess (data) {int} total_opended Lượt mở 
@apiSuccess (data) {int} ratio_opened Tỉ lệ mở. Đơn vị %
@apiSuccess (data) {int} total_clicked Lượt bấm link
@apiSuccess (data) {int} ratio_clicked Tỉ lệ bấm link. Đơn vị %
@apiSuccess (data) {int} ratio_ctor CTOR. Đơn vị %

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "total_opened": 10000,
        "ratio_opened": 30,
        "total_clicked": 6782,
        "ratio_clicked": 10,
        "ratio_ctor": 10
    }
}
"""

******************************* ReportOpenClickLink ******************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/journey/open-click-link Báo cáo chung - Báo cáo tỉ lệ mở và bấm link (Done)
@apiDescription Báo cáo tỉ lệ mở và bấm link
@apiGroup Journey
@apiVersion 2.0.0
@apiName ReportOpenClickLink

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccess (data) {int} total_opended Lượt mở 
@apiSuccess (data) {int} ratio_opened Tỉ lệ mở. Đơn vị %
@apiSuccess (data) {int} total_clicked Lượt bấm link
@apiSuccess (data) {int} ratio_clicked Tỉ lệ bấm link. Đơn vị %
@apiSuccess (data) {int} ratio_ctor CTOR. Đơn vị %

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "total_opened": 10000,
        "ratio_opened": 30,
        "total_clicked": 6782,
        "ratio_clicked": 10,
        "ratio_ctor": 10
    }
}
"""

******************************* ReportLinkByChannel ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/link Báo cáo khác - Báo cáo link (Done)
@apiDescription báo cáo link
@apiGroup Journey
@apiVersion 1.0.0
@apiName ReportLinkByChannel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [channel_code]     Kênh <code> Mặc định là Tất cả các kênh</code>


@apiSuccess (data) {int} link link
@apiSuccess (data) {int} notification Thông điêp
@apiSuccess (data) {int} total_clicked Lượt bấm link

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [{
        "link": "https://app.mobio.vn/",
        "notification": "E-mail",
        "total_clicked": 10,
        "channel_code" : "WEB_PUSH",
        "num_order": 0
    },
    {
        "link": "https://app.mobio.vn/",
        "notification": "E-mail",
        "total_clicked": 10,
        "channel_code": "ZALO",
        "num_order": 1
    },
    {
        "link": "https://app.mobio.vn/",
        "notification": "E-mail",
        "total_clicked": 10,
        "channel_code": "FB_MESSENGER",
        "num_order": 2
    }]
    
}
"""
******************************* ReportLinkByChannel ********************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/journey/link Báo cáo khác - Báo cáo link (Done)
@apiDescription báo cáo link
@apiGroup Journey
@apiVersion 2.0.0
@apiName ReportLinkByChannel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [channel_code]     Kênh <code> Mặc định là Tất cả các kênh</code>


@apiSuccess (data) {int} link link
@apiSuccess (data) {int} notification Thông điêp
@apiSuccess (data) {int} total_clicked Lượt bấm link

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [{
        "link": "https://app.mobio.vn/",
        "notification": "E-mail",
        "total_clicked": 10,
        "channel_code" : "WEB_PUSH",
        "num_order": 0
    },
    {
        "link": "https://app.mobio.vn/",
        "notification": "E-mail",
        "total_clicked": 10,
        "channel_code": "ZALO",
        "num_order": 1
    },
    {
        "link": "https://app.mobio.vn/",
        "notification": "E-mail",
        "total_clicked": 10,
        "channel_code": "FB_MESSENGER",
        "num_order": 2
    }]
    
}
"""

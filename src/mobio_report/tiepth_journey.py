#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" Author: tiep
    Company: MobioVN
    Date created:  19/05/2021
"""

******************************* Tổng số lượt gửi không thành công ******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {get}  /api/v1.0/journey/total-sent-fail Total sent fail
@apiDescription API  Tổng lượt gửi lỗi
@apiVersion 1.0.0
@apiGroup ReportSendSuccess
@apiName TotalSentFail

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:)   {string}       node_id                   UUID id của node
@apiParam (Query:)   {string}      lang                        Ngôn ngữ. VD: &lang=vi Default value: vi

@apiSuccess (data:)   {int}           total                   Tổng số message failed
@apiSuccess (data:)   {string}        reason_type             Kiểu lý do
@apiSuccess (data:)   {string}        reason                  Lý do
@apiSuccess (data:)   {string}        reason_key              i18n key

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công.",
    "update_time": "2019-07-18T07:51:01Z"
    "data": [
        {
            "reason_type": 1,
            "reason": "Email ko tồn tại",
            "reason_key": "email_not_exit",
            "total": 600
        },

    ]
}
"""

******************************* Tổng số lượt gửi không thành công ******************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET}  /api/v2.0/journey/total-sent-fail Total sent fail
@apiDescription API  Tổng lượt gửi lỗi
@apiVersion 2.0.0
@apiGroup ReportSendSuccess
@apiName TotalSentFail

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:)   {string}       node_id                   UUID id của node
@apiParam (Query:)   {string}      lang                        Ngôn ngữ. VD: &lang=vi Default value: vi

@apiSuccess (data:)   {int}           total                   Tổng số message failed
@apiSuccess (data:)   {string}        reason_type             Kiểu lý do
@apiSuccess (data:)   {string}        reason                  Lý do
@apiSuccess (data:)   {string}        reason_key              i18n key

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công.",
    "update_time": "2019-07-18T07:51:01Z"
    "data": [
        {
            "reason_type": 1,
            "reason": "Email ko tồn tại",
            "reason_key": "email_not_exit",
            "total": 600
        },

    ]
}
"""

******************************* Lấy danh sách profile tiếp cận Lỗig ******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {get}  /api/v1.0/journey/profile-sent-fail Chi tiết profile gửi lỗi
@apiDescription API Chi tiết profile gửi lỗi
@apiVersion 1.0.0
@apiName ProfileSentFail
@apiGroup ReportSendSuccess

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse search
@apiUse paging

@apiParam (Query:)   {string}       node_id                 UUID id của node
@apiParam (Query:)   {string}       search                  search theo name hoặc address 
@apiParam (Query:)   {string}      lang                        Ngôn ngữ. VD: &lang=vi Default value: vi

@apiSuccess (profile:)   {string}        profile_id              Profile id 
@apiSuccess (profile:)   {string}        reason_type             Kiểu lý do
@apiSuccess (profile:)   {string}        reason                  Lý do
@apiSuccess (profile:)   {string}        name                    Tên profile
@apiSuccess (profile:)   {string}        address                 Thông tin liên lạc

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công.",
    "profile": [
        {
            "profile_id": "2184e3a4-ba50-11e8-9912-c0389680f4e2",
            "reason_type": 1,
            "reason": "Email ko tồn tại",
            "address": "<EMAIL>",
            "name": "example",
            "created_time": "2021-05-11T13:25:39.528000Z"
        }
    ]
}
"""

******************************* Lấy danh sách profile tiếp cận Lỗig ******************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {get}  /api/v2.0/journey/profile-sent-fail Chi tiết profile gửi lỗi
@apiDescription API Chi tiết profile gửi lỗi
@apiVersion 2.0.0
@apiName ProfileSentFail
@apiGroup ReportSendSuccess

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse search
@apiUse paging

@apiParam (Query:)   {string}       node_id                 UUID id của node
@apiParam (Query:)   {string}       search                  search theo name hoặc address 
@apiParam (Query:)   {string}      lang                        Ngôn ngữ. VD: &lang=vi Default value: vi

@apiSuccess (profile:)   {string}        profile_id              Profile id 
@apiSuccess (profile:)   {string}        reason_type             Kiểu lý do
@apiSuccess (profile:)   {string}        reason                  Lý do
@apiSuccess (profile:)   {string}        name                    Tên profile
@apiSuccess (profile:)   {string}        address                 Thông tin liên lạc

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công.",
    "profile": [
        {
            "profile_id": "2184e3a4-ba50-11e8-9912-c0389680f4e2",
            "reason_type": 1,
            "reason": "Email ko tồn tại",
            "address": "<EMAIL>",
            "name": "example",
            "created_time": "2021-05-11T13:25:39.528000Z"
        }
    ]
}
"""


******************************* Báo cáo event chuyển đổi  ******************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {get}  /api/v2.0/journey/conversion_rate/<journey_id> Báo cáo event chuyển đổi 
@apiDescription API Báo cáo event chuyển đổi
@apiVersion 2.0.0
@apiName ReportJourneyConversionRate
@apiGroup Journey

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:)   {string}       start_time                thời gian bắt đầu: 2009-05-30T00:00:00.000Z
@apiParam (Query:)   {string}       end_time                  thời gian kết thúc: 2009-06-02T00:00:00.000Z
@apiParam (Query:)   {string}      lang                        Ngôn ngữ. VD: &lang=vi Default value: vi

@apiSuccess (profile:)   {string}        journey_id              journey_id
@apiSuccess (profile:)   {array}        report_date              Mảng báo cáo theo ngày của từng event
@apiSuccess (profile:)   {dict}        summary                   Báo cáo tổng quan

@apiSuccess (summary:)   {array}        event                   Báo cáo tổng quan theo từng event
@apiSuccess (summary:)   {int}        profile                   Báo cáo theo profile
@apiSuccess (summary:)   {int}        turn_profile                   Báo cáo theo lượt profile

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "journey_id": "2fa3d91b-ca21-454f-bc12-e02f2f2d370e",
    "lang": "vi",
    "message": "request thành công.",
    "report_date": [
        {
            "date": "30-05-2009",
            "event": [
                {
                    "date": "30-05-2009",
                    "event_id": 1,
                    "sum_profile_cr": 0,
                    "sum_turn_cr": 0
                },
                {
                    "date": "30-05-2009",
                    "event_id": 2,
                    "sum_profile_cr": 0,
                    "sum_turn_cr": 0
                },
                {
                    "date": "30-05-2009",
                    "event_id": 3,
                    "sum_profile_cr": 0,
                    "sum_turn_cr": 0
                }
            ]
        },
        {
            "date": "02-06-2009",
            "event": [
                {
                    "date": "02-06-2009",
                    "event_id": 1,
                    "sum_profile_cr": 0,
                    "sum_turn_cr": 0
                },
                {
                    "date": "02-06-2009",
                    "event_id": 2,
                    "sum_profile_cr": 0,
                    "sum_turn_cr": 0
                },
                {
                    "date": "02-06-2009",
                    "event_id": 3,
                    "sum_profile_cr": 0,
                    "sum_turn_cr": 0
                }
            ]
        }
    ],
    "summary": {
        "event": [
            {
                "event_id": 1,
                "event_name": "a",
                "sum_profile_cr": 400,
                "sum_turn_cr": 400
            },
            {
                "event_id": 2,
                "event_name": "b",
                "sum_profile_cr": 400,
                "sum_turn_cr": 400
            },
            {
                "event_id": 3,
                "event_name": "c",
                "sum_profile_cr": 400,
                "sum_turn_cr": 400
            }
        ],
        "profile": 600,
        "turn_profile": 1200
    }
}
"""

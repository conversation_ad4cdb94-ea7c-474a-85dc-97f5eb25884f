********************************* ProfileJoinJourneyInMasterCampaign ******************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/master-campaign/profile-join-journey Báo cáo chung: Profile tham gia các journey trong Master Campaign (Done)
@apiDescription Báo cáo chung: Profile tham gia các journey trong Master Campaign
@apiGroup Master Campaign
@apiVersion 1.0.0
@apiName ProfileJoinJourneyInMasterCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_id        Master Campaign ID <code>required</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "master_campaign_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "profile_reach": 1000,
        "turn_start_journey": 600,
        "turn_exit_journey": 400
    }
}
"""
********************************* ProfileJoinJourneyInMasterCampaign ******************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/master-campaign/profile-join-journey Báo cáo chung: Profile tham gia các journey trong Master Campaign (Done)
@apiDescription Báo cáo chung: Profile tham gia các journey trong Master Campaign
@apiGroup Master Campaign
@apiVersion 2.0.0
@apiName ProfileJoinJourneyInMasterCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_id        Master Campaign ID <code>required</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "master_campaign_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "profile_reach": 1000,
        "turn_start_journey": 600,
        "turn_exit_journey": 400
    }
}
"""

********************************* NumberNotificationSent ******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/master-campaign/number-notification-sent Báo cáo chung: Số lượng thông điệp đã gửi (Done)
@apiDescription Báo cáo chung: Số lượng thông điệp đã gửi
@apiGroup Master Campaign
@apiVersion 1.0.0
@apiName NumberNotificationSent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_id        Master Campaign ID <code>required</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "master_campaign_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "total_sent": 18000,
        "sent_successfully": 16200,
        "sent_unsuccessfully": 1800
    }
}
"""
********************************* NumberNotificationSent ******************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/master-campaign/number-notification-sent Báo cáo chung: Số lượng thông điệp đã gửi (Done)
@apiDescription Báo cáo chung: Số lượng thông điệp đã gửi
@apiGroup Master Campaign
@apiVersion 2.0.0
@apiName NumberNotificationSent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_id        Master Campaign ID <code>required</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "master_campaign_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "total_sent": 18000,
        "sent_successfully": 16200,
        "sent_unsuccessfully": 1800
    }
}
"""

********************************* OpenClickLink ***************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/master-campaign/open-click-link Báo cáo chung: Tỉ lệ mở và bấm link (Done)
@apiDescription Báo cáo chung: Tỉ lệ mở và bấm link 
@apiGroup Master Campaign
@apiVersion 1.0.0
@apiName OpenClickLink

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_id        Master Campaign ID <code>required</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "master_campaign_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "total_opened": 10000,
        "total_clicked": 6782,
        "ratio_opened": 30,
        "ratio_clicked": 10,
        "ratio_ctor": 10
    }
}
"""
********************************* OpenClickLink ***************************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/master-campaign/open-click-link Báo cáo chung: Tỉ lệ mở và bấm link (Done)
@apiDescription Báo cáo chung: Tỉ lệ mở và bấm link 
@apiGroup Master Campaign
@apiVersion 2.0.0
@apiName OpenClickLink

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_id        Master Campaign ID <code>required</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "master_campaign_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "total_opened": 10000,
        "total_clicked": 6782,
        "ratio_opened": 30,
        "ratio_clicked": 10,
        "ratio_ctor": 10
    }
}
"""
********************************* ConversionRate **************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/master-campaign/conversion-rate Báo cáo chung: Tỉ lệ chuyển đổi (Done)

@apiDescription Báo cáo chung: Tỉ lệ chuyển đổi 
@apiGroup Master Campaign
@apiVersion 1.0.0
@apiName ConversionRate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_id        Master Campaign ID <code>required</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "master_campaign_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "perform_cta": 0,
        "profile_reach": 1,
        "purchased": 0,
        "total_sent": 1,
        "turn_granted_voucher": 0,
        "turn_reach": 1,
        "turn_spends": 0,
        "used_voucher": 0
    }
}
"""
********************************* ConversionRate **************************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/master-campaign/conversion-rate Báo cáo chung: Tỉ lệ chuyển đổi (Done)

@apiDescription Báo cáo chung: Tỉ lệ chuyển đổi 
@apiGroup Master Campaign
@apiVersion 2.0.0
@apiName ConversionRate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_id        Master Campaign ID <code>required</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "master_campaign_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "perform_cta": 0,
        "profile_reach": 1,
        "purchased": 0,
        "total_sent": 1,
        "turn_granted_voucher": 0,
        "turn_reach": 1,
        "turn_spends": 0,
        "used_voucher": 0
    }
}
"""

******************************* ExportReport ******************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/master-campaign/export-report Tải báo cáo 
@apiDescription Tải báo cáo 
@apiGroup Master Campaign
@apiVersion 1.0.0
@apiName ExportReport

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_id           Master Campaign ID <code>required</code>
@apiParam      (Param:)    {string}    master_campaign_name         Tên Master Campaign <code>required</code>
@apiParam      (Param:)    {String}    [emails]                     Danh Sách email nhận báo cáo cách, nhau bởi dấu , 
                                                                        <code>required</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "Message ": "Mail file bao cao se duoc gui den mail cua ban"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
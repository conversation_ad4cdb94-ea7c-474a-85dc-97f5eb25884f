******************************* NumberNotificationSent ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/number-notification-sent Báo cáo lượt số lượng thông điệp đã gửi (Done)
@apiDescription Báo cáo số lượng thông điệp đã gửi
@apiGroup Journey
@apiVersion 1.0.0
@apiName NumberNotificationSent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "total_sent": 18000,
        "sent_successfully": 16200,
        "sent_unsuccessfully": 1800
    }
}
"""
******************************* NumberNotificationSent ********************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/journey/number-notification-sent Báo cáo lượt số lượng thông điệp đã gửi (Done)
@apiDescription Báo cáo số lượng thông điệp đã gửi
@apiGroup Journey
@apiVersion 2.0.0
@apiName NumberNotificationSent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "total_sent": 18000,
        "sent_successfully": 16200,
        "sent_unsuccessfully": 1800
    }
}
"""
******************************* AmountProfileJoinJB ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/amount-profile-join-journey Báo cáo số lượng profile tham gia JB (Done)
@apiDescription Báo cáo số lượng profile tham gia JB 
@apiGroup Journey
@apiVersion 1.0.0
@apiName AmountProfileJoinJB

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
 
@apiSuccess (Data Response) {Integer} profile_reach            Tổng profile tiếp cận -> (mới đổi JB Phase 2 - Sprint 5) đổi sang Profile đã gửi thành công 
@apiSuccess (Data Response) {Integer} profile_reachable        Profile có khẳn năng tiếp cận 
@apiSuccess (Data Response) {Integer} profile_start_journey    Profile bắt đầu journey 
@apiSuccess (Data Response) {Integer} profile_target           Đối tượng mục tiêu
@apiSuccess (Data Response) {Integer} profile_sent             Profile đã gửi (mới có JB Phase 2 - Sprint 5)


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "profile_reach": 0,
        "profile_reachable": 0,
        "profile_start_journey": 1,
        "profile_target": 0,
        "profile_sent": 3,
    }
}
"""
******************************* AmountProfileJoinJB ********************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/journey/amount-profile-join-journey Báo cáo số lượng profile tham gia JB (Done)
@apiDescription Báo cáo số lượng profile tham gia JB 
@apiGroup Journey
@apiVersion 2.0.0
@apiName AmountProfileJoinJB

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
 
@apiSuccess (Data Response) {Integer} profile_reach            Tổng profile tiếp cận -> (mới đổi JB Phase 2 - Sprint 5) đổi sang Profile đã gửi thành công 
@apiSuccess (Data Response) {Integer} profile_reachable        Profile có khẳn năng tiếp cận 
@apiSuccess (Data Response) {Integer} profile_start_journey    Profile bắt đầu journey 
@apiSuccess (Data Response) {Integer} profile_target           Đối tượng mục tiêu
@apiSuccess (Data Response) {Integer} profile_sent             Profile đã gửi (mới có JB Phase 2 - Sprint 5)


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "profile_reach": 0,
        "profile_reachable": 0,
        "profile_start_journey": 1,
        "profile_target": 0,
        "profile_sent": 3,
    }
}
"""
******************************* ConversionRate ****************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/conversion-rate Báo cáo tỷ lệ chuyển đổi (Done)
@apiDescription Báo cáo tỷ lệ chuyển đổi
@apiGroup Journey
@apiVersion 1.0.0
@apiName ConversionRate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "perform_cta": 0,
    "profile_reach": 1,
    "purchased": 0,
    "total_sent": 1,
    "turn_granted_voucher": 0,
    "turn_reach": 1,
    "turn_spends": 0,
    "used_voucher": 0
  },
  "lang": "vi",
  "message": "request thành công",
}
"""
******************************* ConversionRate ****************************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/journey/conversion-rate Báo cáo tỷ lệ chuyển đổi (Done)
@apiDescription Báo cáo tỷ lệ chuyển đổi
@apiGroup Journey
@apiVersion 2.0.0
@apiName ConversionRate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "perform_cta": 0,
    "profile_reach": 1,
    "purchased": 0,
    "total_sent": 1,
    "turn_granted_voucher": 0,
    "turn_reach": 1,
    "turn_spends": 0,
    "used_voucher": 0
  },
  "lang": "vi",
  "message": "request thành công",
}
"""
******************************* ExportReport ******************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/export-report Xuất báo cáo excel 
@apiDescription Xuất báo cáo excel 
@apiGroup Journey
@apiVersion 1.0.0
@apiName ExportReport

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id         Journey ID <code>required</code>
@apiParam      (Param:)    {string}    journey_name       Tên Journey <code>required</code>
@apiParam      (Param:)    {String}    [channel_code]     Kênh <code> Mặc định là Tất cả các kênh</code>
@apiParam      (Param:)    {String}    [emails]           Danh Sách email nhận báo cáo ở cuối journey, và mail ở tài khoản 
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "Message ": "Mail file bao cao se duoc gui den mail cua ban"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


******************************* ExportDetailError *************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/export-detail-error Xuất báo cáo chi tiết lỗi
@apiDescription Xuất báo cáo chi tiết lỗi 
@apiGroup Journey
@apiVersion 1.0.0
@apiName ExportDetailError

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)   {String}    journey_id         Journey ID <code>required</code>
@apiParam      (Param:)   {string}    node_name          Tên Node <code>required</code>
@apiParam      (Param:)   {string}    journey_name       Tên Journey <code>required</code>
@apiParam      (Param:)   {String}    [emails]           Danh Sách email nhận báo cáo ở cuối journey, và mail ở tài khoản 
@apiParam      (Param:)   {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)   {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>



@apiParam      (Param:)   {string}    node_id          UUID id của node
@apiParam      (Param:)   {string}    search           earch theo name hoặc address 
@apiParam      (Param:)   {string}    lang             Ngôn ngữ. VD: &lang=vi Default value: vi
@apiParam      (Param:)   {string}    [page]           Vị trí page cần lấy dữ liệu. Set page=-1 nếu muốn lấy tất cả dữ liệu.
                                                            MIN_VALUE=1
                                                            Example: &page=2
                                                            Default value: 1
@apiParam      (Param:)   {Number}    [page]           Vị trí page cần lấy dữ liệu. Set page=-1 nếu muốn lấy tất cả dữ liệu.
@apiParam      (Param:)   {Number}    [per_page]       Số phần tử trên một page.Example: &per_page=5



@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "Message ": "Mail file bao cao chi tiet loi se duoc gui den mail cua ban"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
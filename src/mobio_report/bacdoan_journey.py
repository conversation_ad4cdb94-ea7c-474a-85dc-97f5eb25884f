******************************* ReportProfileJoin<PERSON><PERSON><PERSON> ******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/profile-join-journey Báo cáo lượt profiles tham gia journey (Done)
@apiDescription Báo cáo lượt profiles tham gia journey
@apiGroup Journey
@apiVersion 1.0.0
@apiName ReportProfileJoinJourney

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id         Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "start_journey": 1000,
        "in_journey": 600,
        "exit_journey": 400
    }
}
"""
******************************* ReportProfileJoinJourney ******************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/journey/profile-join-journey Báo cáo lượt profiles tham gia journey (Done)
@apiDescription Báo cáo lượt profiles tham gia journey
@apiGroup Journey
@apiVersion 2.0.0
@apiName ReportProfileJoinJourney

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id         Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "start_journey": 1000,
        "in_journey": 600,
        "exit_journey": 400
    }
}
"""

******************************* ReportEffectiveChannel ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/effective-channel Báo cáo hiệu quả tiếp cận theo từng kênh (Done)
@apiDescription Báo cáo hiệu quả tiếp cận theo từng kênh
@apiGroup Journey
@apiVersion 1.0.0
@apiName ReportEffectiveChannel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [
        {
            "channel_code": "WEB_PUSH",
            "sent": 1000,
            "success": 500,
            "failed": 500 
        },
        {
            "channel_code": "ZALO",
            "sent": 1000,
            "success": 500,
            "failed": 500 
        }
    ]
}
"""
******************************* ReportEffectiveChannel ********************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/journey/effective-channel Báo cáo hiệu quả tiếp cận theo từng kênh (Done)
@apiDescription Báo cáo hiệu quả tiếp cận theo từng kênh
@apiGroup Journey
@apiVersion 2.0.0
@apiName ReportEffectiveChannel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [
        {
            "channel_code": "WEB_PUSH",
            "sent": 1000,
            "success": 500,
            "failed": 500 
        },
        {
            "channel_code": "ZALO",
            "sent": 1000,
            "success": 500,
            "failed": 500 
        }
    ]
}
"""

******************************* InteractionRateByChannel ******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/interaction-rate-by-channel Tỷ lệ tương tác theo từng kênh (Done)
@apiDescription Tỷ lệ tương tác theo từng kênh
@apiGroup Journey
@apiVersion 1.0.0
@apiName InteractionRateByChannel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [
        {
            "channel_code": "WEB_PUSH",
            "total_sent": 100,
            "turn_sent_success": 100,
            "turn_reach": 100,
            "opened": 100,
            "clicked": 85,
            "clicked_cta": 20,
            "granted_voucher": 20,
            "used_voucher": 20,
            "purchased": 20,
            "unsubscribed": 0
        },
        {
            "channel_code": "ZALO",
            "total_sent": 100,
            "turn_sent_success": 100,
            "turn_reach": 100,
            "opened": 100,
            "clicked": 85,
            "clicked_cta": 20,
            "used_voucher": 20,
            "granted_voucher": 20,
            "purchased": 20,
            "unsubscribed": 0
        }
    ]
}
"""

******************************* InteractionRateByChannel ******************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/journey/interaction-rate-by-channel Tỷ lệ tương tác theo từng kênh (Done)
@apiDescription Tỷ lệ tương tác theo từng kênh
@apiGroup Journey
@apiVersion 2.0.0
@apiName InteractionRateByChannel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [
        {
            "channel_code": "WEB_PUSH",
            "total_sent": 100,
            "turn_sent_success": 100,
            "turn_reach": 100,
            "opened": 100,
            "clicked": 85,
            "clicked_cta": 20,
            "granted_voucher": 20,
            "used_voucher": 20,
            "purchased": 20,
            "unsubscribed": 0
        },
        {
            "channel_code": "ZALO",
            "total_sent": 100,
            "turn_sent_success": 100,
            "turn_reach": 100,
            "opened": 100,
            "clicked": 85,
            "clicked_cta": 20,
            "used_voucher": 20,
            "granted_voucher": 20,
            "purchased": 20,
            "unsubscribed": 0
        }
    ]
}
"""

******************************* EffectiveMessage **************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/effective-message Báo cáo hiệu quả theo thông điệp (Done)
@apiDescription Báo cáo hiệu quả theo thông điệp
@apiGroup Journey
@apiVersion 1.0.0
@apiName EffectiveMessage

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id         Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [channel_code]     Kênh <code> Mặc định là Tất cả các kênh</code>
@apiParam      (Param:)    {String}    type               <code>interactive</code>: Hiệu quả tương tác;<br>
                                                          <code>reach</code>: Hiệu quả tiếp cận;
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccess (Data Response Reach) {String} name                     Thông điệp  
@apiSuccess (Data Response Reach) {Integer} profile_in_node    Profile vào khối
@apiSuccess (Data Response Reach) {Integer} profile_sent             Profile được gửi -> profile đã gửi (mới đổi JB Phase 2 - Sprint 5)
@apiSuccess (Data Response Reach) {Integer} profile_reach            Tổng profile tiếp cận -> sang Profile đã gửi thành công (mới đổi JB Phase 2 - Sprint 5) 
@apiSuccess (Data Response Reach) {Integer} turn_in_node        Lượt profile vào khối
@apiSuccess (Data Response Reach) {Integer} turn_sent    Lượt gửi
@apiSuccess (Data Response Reach) {Integer} turn_sent_success           Lượt gửi thành công 
@apiSuccess (Data Response Reach) {Boolean} reason           True hiện lý do gửi không thành công 


@apiSuccessExample  {json}  Response interactive: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [
        {
            "id": "1",
            "name": "channel web push",
            "channel_code": "WEB_PUSH",
            "turn_sent": 100, 
            "turn_sent_success": 85,
            "turn_reach": 85,
            "turn_opened": 20,
            "turn_clicked": 20,
            "turn_cta": 20,
            "turn_granted_voucher": 0,
            "turn_used_voucher": 0,
            "turn_bought_product": 0,
            "turn_unsubscribed": 0
        },
        {
            "id": "2",
            "name": "Khối MI",
            "channel_code": "MI",
            "turn_sent": 100, 
            "turn_sent_success": 85,
            "turn_reach": 85,
            "turn_opened": 20,
            "turn_clicked": 20,
            "turn_cta": 20,
            "turn_granted_voucher": 0,
            "turn_used_voucher": 0,
            "turn_bought_product": 0,
            "turn_unsubscribed": 0,
            "report_detail": [
                {
                    "id": "3",
                    "name": "Email",
                    "channel_code": "EMAIL",
                    "turn_sent": 100, 
                    "turn_sent_success": 85,
                    "turn_reach": 85,
                    "turn_opened": 20,
                    "turn_clicked": 20,
                    "turn_cta": 20,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0,
                    "turn_unsubscribed": 0
                },
                {
                    "id": "4",
                    "name": "Zalo",
                    "channel_code": "ZALO",
                    "turn_sent": 100, 
                    "turn_sent_success": 85,
                    "turn_reach": 85,
                    "turn_opened": 20,
                    "turn_clicked": 20,
                    "turn_cta": 20,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0,
                    "turn_unsubscribed": 0,
                    "report_detail": [
                        {
                            "id": 5,
                            "turn_sent": 100, 
                            "turn_sent_success": 85,
                            "turn_reach": 85,
                            "turn_opened": 20,
                            "turn_clicked": 20,
                            "turn_cta": 20,
                            "turn_granted_voucher": 0,
                            "turn_used_voucher": 0,
                            "turn_bought_product": 0,
                            "turn_unsubscribed": 0,
                            "page_id": "3973519755073535314"
                        },
                        {
                            "id": 6,
                            "turn_sent": 100, 
                            "turn_sent_success": 85,
                            "turn_reach": 85,
                            "turn_opened": 20,
                            "turn_clicked": 20,
                            "turn_cta": 20,
                            "turn_granted_voucher": 0,
                            "turn_used_voucher": 0,
                            "turn_bought_product": 0,
                            "turn_unsubscribed": 0,
                            "page_id": "3973519755073535315"
                        }
                    ]
                }
            ]
        }
    ]
}

@apiSuccessExample  {json}  Response reach: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [
        {
            "id": "1",
            "name": "channel web push",
            "channel_code": "WEB_PUSH",
            "profile_in_node": 100,
            "profile_sent": 85,
            "profile_reach": 20,
            "turn_in_node": 20,
            "turn_sent": 20,
            "turn_sent_success": 0
        },
        {
            "id": "2",
            "name": "channel zalo",
            "channel_code": "Zalo",
            "profile_in_node": 100,
            "profile_sent": 85,
            "profile_reach": 20,
            "turn_in_node": 20,
            "turn_sent": 20,
            "turn_sent_success": 0
        }
    ]
}

"""
******************************* EffectiveMessage **************************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/journey/effective-message Báo cáo hiệu quả theo thông điệp (Done)
@apiDescription Báo cáo hiệu quả theo thông điệp
@apiGroup Journey
@apiVersion 2.0.0
@apiName EffectiveMessage

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id         Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [channel_code]     Kênh <code> Mặc định là Tất cả các kênh</code>
@apiParam      (Param:)    {String}    type               <code>interactive</code>: Hiệu quả tương tác;<br>
                                                          <code>reach</code>: Hiệu quả tiếp cận;
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccess (Data Response Reach) {String} name                     Thông điệp  
@apiSuccess (Data Response Reach) {Integer} profile_in_node    Profile vào khối
@apiSuccess (Data Response Reach) {Integer} profile_sent             Profile được gửi -> profile đã gửi (mới đổi JB Phase 2 - Sprint 5)
@apiSuccess (Data Response Reach) {Integer} profile_reach            Tổng profile tiếp cận -> sang Profile đã gửi thành công (mới đổi JB Phase 2 - Sprint 5) 
@apiSuccess (Data Response Reach) {Integer} turn_in_node        Lượt profile vào khối
@apiSuccess (Data Response Reach) {Integer} turn_sent    Lượt gửi
@apiSuccess (Data Response Reach) {Integer} turn_sent_success           Lượt gửi thành công 
@apiSuccess (Data Response Reach) {Boolean} reason           True hiện lý do gửi không thành công 


@apiSuccessExample  {json}  Response interactive: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [
        {
            "id": "1",
            "name": "channel web push",
            "channel_code": "WEB_PUSH",
            "turn_sent": 100, 
            "turn_sent_success": 85,
            "turn_reach": 85,
            "turn_opened": 20,
            "turn_clicked": 20,
            "turn_cta": 20,
            "turn_granted_voucher": 0,
            "turn_used_voucher": 0,
            "turn_bought_product": 0,
            "turn_unsubscribed": 0
        },
        {
            "id": "2",
            "name": "Khối MI",
            "channel_code": "MI",
            "turn_sent": 100, 
            "turn_sent_success": 85,
            "turn_reach": 85,
            "turn_opened": 20,
            "turn_clicked": 20,
            "turn_cta": 20,
            "turn_granted_voucher": 0,
            "turn_used_voucher": 0,
            "turn_bought_product": 0,
            "turn_unsubscribed": 0,
            "report_detail": [
                {
                    "id": "3",
                    "name": "Email",
                    "channel_code": "EMAIL",
                    "turn_sent": 100, 
                    "turn_sent_success": 85,
                    "turn_reach": 85,
                    "turn_opened": 20,
                    "turn_clicked": 20,
                    "turn_cta": 20,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0,
                    "turn_unsubscribed": 0
                },
                {
                    "id": "4",
                    "name": "Zalo",
                    "channel_code": "ZALO",
                    "turn_sent": 100, 
                    "turn_sent_success": 85,
                    "turn_reach": 85,
                    "turn_opened": 20,
                    "turn_clicked": 20,
                    "turn_cta": 20,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0,
                    "turn_unsubscribed": 0,
                    "report_detail": [
                        {
                            "id": 5,
                            "turn_sent": 100, 
                            "turn_sent_success": 85,
                            "turn_reach": 85,
                            "turn_opened": 20,
                            "turn_clicked": 20,
                            "turn_cta": 20,
                            "turn_granted_voucher": 0,
                            "turn_used_voucher": 0,
                            "turn_bought_product": 0,
                            "turn_unsubscribed": 0,
                            "page_id": "3973519755073535314"
                        },
                        {
                            "id": 6,
                            "turn_sent": 100, 
                            "turn_sent_success": 85,
                            "turn_reach": 85,
                            "turn_opened": 20,
                            "turn_clicked": 20,
                            "turn_cta": 20,
                            "turn_granted_voucher": 0,
                            "turn_used_voucher": 0,
                            "turn_bought_product": 0,
                            "turn_unsubscribed": 0,
                            "page_id": "3973519755073535315"
                        }
                    ]
                }
            ]
        }
    ]
}

@apiSuccessExample  {json}  Response reach: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [
        {
            "id": "1",
            "name": "channel web push",
            "channel_code": "WEB_PUSH",
            "profile_in_node": 100,
            "profile_sent": 85,
            "profile_reach": 20,
            "turn_in_node": 20,
            "turn_sent": 20,
            "turn_sent_success": 0
        },
        {
            "id": "2",
            "name": "channel zalo",
            "channel_code": "Zalo",
            "profile_in_node": 100,
            "profile_sent": 85,
            "profile_reach": 20,
            "turn_in_node": 20,
            "turn_sent": 20,
            "turn_sent_success": 0
        }
    ]
}

"""
************************************ ReportJourneyDetail ******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/journey-detail Báo cáo chi tiết journey (Done)
@apiDescription Báo cáo chi tiết journey
@apiGroup Journey
@apiVersion 1.0.0
@apiName ReportJourneyDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "process": [
            {
                "id": "1",
                "report": {
                    "profile_in_node": 100,
                    "turn_in_node": 150,
                    "turn_sent": 0,
                    "turn_sent_success": 0,
                    "turn_reach": 0,
                    "turn_opened": 0,
                    "turn_clicked": 0,
                    "turn_cta": 0,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0
                }
            },
            {
                "id": "2",
                "report": {
                    "profile_in_node": 100,
                    "turn_in_node": 150,
                    "turn_sent": 0,
                    "turn_sent_success": 0,
                    "turn_reach": 0,
                    "turn_opened": 0,
                    "turn_clicked": 0,
                    "turn_cta": 0,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0
                },
                "branches": [
                    {
                        "position": 0,
                        "next_id": "3",
                        "percent": 50
                    },
                    {
                        "position": 1,
                        "next_id": "4",
                        "percent": 50
                    }
                ]
            },
            {
                "id": "3",
                "report": {
                    "profile_in_node": 100,
                    "turn_in_node": 150,
                    "turn_sent": 0,
                    "turn_sent_success": 0,
                    "turn_reach": 0,
                    "turn_opened": 0,
                    "turn_clicked": 0,
                    "turn_cta": 0,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0
                }
            },
            {
                "id": "4",
                "report": {
                    "profile_in_node": 100,
                    "turn_in_node": 150,
                    "turn_sent": 0,
                    "turn_sent_success": 0,
                    "turn_reach": 0,
                    "turn_opened": 0,
                    "turn_clicked": 0,
                    "turn_cta": 0,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0
                }
            },
            {
                "id": "5",
                "report": {
                    "profile_in_node": 100,
                    "turn_in_node": 150,
                    "turn_sent": 0,
                    "turn_sent_success": 0,
                    "turn_reach": 0,
                    "turn_opened": 0,
                    "turn_clicked": 0,
                    "turn_cta": 0,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0
                }
            }
        ],
        "diagram": // data FE
    }
}
"""
************************************ ReportJourneyDetail ******************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/journey/journey-detail Báo cáo chi tiết journey (Done)
@apiDescription Báo cáo chi tiết journey
@apiGroup Journey
@apiVersion 2.0.0
@apiName ReportJourneyDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id       Journey ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccess (conversion_rate:)   {array}        event                   Báo cáo tổng quan theo từng event
@apiSuccess (conversion_rate:)   {int}        profile                   Báo cáo theo profile
@apiSuccess (conversion_rate:)   {int}        turn_profile                   Báo cáo theo lượt profile

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "process": [
            {
                "id": "1",
                "report": {
                    "profile_in_node": 100,
                    "turn_in_node": 150,
                    "turn_sent": 0,
                    "turn_sent_success": 0,
                    "turn_reach": 0,
                    "turn_opened": 0,
                    "turn_clicked": 0,
                    "turn_cta": 0,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0,
                    "conversion_rate":{
                        "event": [
                            {
                                "event_id": 1,
                                "event_name": "a",
                                "sum_profile_cr": 400,
                                "sum_turn_cr": 400
                            },
                            {
                                "event_id": 2,
                                "event_name": "b",
                                "sum_profile_cr": 400,
                                "sum_turn_cr": 400
                            },
                            {
                                "event_id": 3,
                                "event_name": "c",
                                "sum_profile_cr": 400,
                                "sum_turn_cr": 400
                            }
                        ],
                        "profile": 600,
                        "turn_profile": 1200
                    }
                }
            },
            {
                "id": "2",
                "report": {
                    "profile_in_node": 100,
                    "turn_in_node": 150,
                    "turn_sent": 0,
                    "turn_sent_success": 0,
                    "turn_reach": 0,
                    "turn_opened": 0,
                    "turn_clicked": 0,
                    "turn_cta": 0,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0,
                    "conversion_rate":{
                        "event": [
                            {
                                "event_id": 1,
                                "event_name": "a",
                                "sum_profile_cr": 400,
                                "sum_turn_cr": 400
                            },
                            {
                                "event_id": 2,
                                "event_name": "b",
                                "sum_profile_cr": 400,
                                "sum_turn_cr": 400
                            },
                            {
                                "event_id": 3,
                                "event_name": "c",
                                "sum_profile_cr": 400,
                                "sum_turn_cr": 400
                            }
                        ],
                        "profile": 600,
                        "turn_profile": 1200
                    }
                },
                "branches": [
                    {
                        "position": 0,
                        "next_id": "3",
                        "percent": 50
                    },
                    {
                        "position": 1,
                        "next_id": "4",
                        "percent": 50
                    }
                ]
            },
            {
                "id": "3",
                "report": {
                    "profile_in_node": 100,
                    "turn_in_node": 150,
                    "turn_sent": 0,
                    "turn_sent_success": 0,
                    "turn_reach": 0,
                    "turn_opened": 0,
                    "turn_clicked": 0,
                    "turn_cta": 0,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0,
                    "conversion_rate":{
                        "event": [
                            {
                                "event_id": 1,
                                "event_name": "a",
                                "sum_profile_cr": 400,
                                "sum_turn_cr": 400
                            },
                            {
                                "event_id": 2,
                                "event_name": "b",
                                "sum_profile_cr": 400,
                                "sum_turn_cr": 400
                            },
                            {
                                "event_id": 3,
                                "event_name": "c",
                                "sum_profile_cr": 400,
                                "sum_turn_cr": 400
                            }
                        ],
                        "profile": 600,
                        "turn_profile": 1200
                    }
                }
            },
            {
                "id": "4",
                "report": {
                    "profile_in_node": 100,
                    "turn_in_node": 150,
                    "turn_sent": 0,
                    "turn_sent_success": 0,
                    "turn_reach": 0,
                    "turn_opened": 0,
                    "turn_clicked": 0,
                    "turn_cta": 0,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0
                }
            },
            {
                "id": "5",
                "report": {
                    "profile_in_node": 100,
                    "turn_in_node": 150,
                    "turn_sent": 0,
                    "turn_sent_success": 0,
                    "turn_reach": 0,
                    "turn_opened": 0,
                    "turn_clicked": 0,
                    "turn_cta": 0,
                    "turn_granted_voucher": 0,
                    "turn_used_voucher": 0,
                    "turn_bought_product": 0
                }
            }
        ],
        "diagram": // data FE
    }
}
"""

*************************************** NodeDetail ************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/node-detail Báo cáo chi tiết node (Done)
@apiDescription Báo cáo chi tiết node
@apiGroup Journey
@apiVersion 1.0.0
@apiName NodeDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id         Journey ID <code>required</code>
@apiParam      (Param:)    {String}    node_id            Node ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccess (Data Response) {String}  id                         node_id
@apiSuccess (Data Response) {String}  code                       Tên node
@apiSuccess (Data Response) {Object}  config                     Cấu trúc Config của node
@apiSuccess (Data Response) {Integer} profile_in_node            Tổng profile vào khối
@apiSuccess (Data Response) {Integer} turn_in_node               Tổng lượt vào khối
@apiSuccess (Data Response) {Integer} profile_sent               Tổng profile đã gửi
@apiSuccess (Data Response) {Integer} turn_sent                  Tổng lượt đã gửi
@apiSuccess (Data Response) {Integer} profile_reach              Tổng profile tiếp cận
@apiSuccess (Data Response) {Integer} turn_reach                 Tổng lượt tiếp cận
@apiSuccess (Data Response) {Integer} turn_sent_success          Tổng lượt gửi thành công
@apiSuccess (Data Response) {Integer} profile_opened             Tổng profile đã mở thông điệp
@apiSuccess (Data Response) {Integer} turn_opened                Tổng lượt mở thông điệp
@apiSuccess (Data Response) {Integer} profile_clicked            Tổng profile click link
@apiSuccess (Data Response) {Integer} turn_clicked               Tổng lượt click link
@apiSuccess (Data Response) {Integer} profile_granted_voucher    Tổng profile nhận voucher(<code>Pending</code>)
@apiSuccess (Data Response) {Integer} turn_granted_voucher       Tổng lượt nhận voucher(<code>Pending</code>)
@apiSuccess (Data Response) {Integer} profile_used_voucher       Tổng profile sử dụng voucher
@apiSuccess (Data Response) {Integer} turn_used_voucher          Tổng lượt sử dụng voucher
@apiSuccess (Data Response) {Integer} turn_unsubscribed          Tổng lượt unsubscribed(<code>Pending</code>)

@apiSuccess (Data Response) {Object}  data_ab_test               Dữ liệu báo cáo node AB Test, dữ liệu gọi bên journey <https://dev.mobio.vn/docs/journey/#api-Report-ReportAbtest>
                                                                <code>Pending</code>


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "id": "node_1",
        "element_type": "WORKFLOW",
        "code": "MESSAGE_INTERACTION",
        "name": "Kiểm tra tương tác với thông điệp 1",
        "config": {},
        "profile_in_node": 100,
        "turn_in_node": 120,
        "profile_sent": 100,
        "turn_sent": 120,
        "profile_reach": 100,
        "turn_sent_success": 120,
        "profile_opened": 100,
        "turn_opened": 120,
        "profile_granted_voucher": 5,
        "turn_granted_voucher": 5,
        "profile_used_voucher": 5,
        "turn_used_voucher": 5,
        "data_ab_test": {
            "number_test": 1010,
            "time_test": 1093,
            "criteria": "PUSH",
            ...
        }
    }
}
"""
*************************************** NodeDetail ************************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/journey/node-detail Báo cáo chi tiết node (Done)
@apiDescription Báo cáo chi tiết node
@apiGroup Journey
@apiVersion 2.0.0
@apiName NodeDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_id         Journey ID <code>required</code>
@apiParam      (Param:)    {String}    node_id            Node ID <code>required</code>
@apiParam      (Param:)    {String}    [start_time]       start_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>
@apiParam      (Param:)    {String}    [end_time]         end_time, time UTC: vd: <code>2021-04-21T11:08:42.775Z</code>

@apiSuccess (Data Response) {String}  id                         node_id
@apiSuccess (Data Response) {String}  code                       Tên node
@apiSuccess (Data Response) {Object}  config                     Cấu trúc Config của node
@apiSuccess (Data Response) {Integer} profile_in_node            Tổng profile vào khối
@apiSuccess (Data Response) {Integer} turn_in_node               Tổng lượt vào khối
@apiSuccess (Data Response) {Integer} profile_sent               Tổng profile đã gửi
@apiSuccess (Data Response) {Integer} turn_sent                  Tổng lượt đã gửi
@apiSuccess (Data Response) {Integer} profile_reach              Tổng profile tiếp cận
@apiSuccess (Data Response) {Integer} turn_reach                 Tổng lượt tiếp cận
@apiSuccess (Data Response) {Integer} turn_sent_success          Tổng lượt gửi thành công
@apiSuccess (Data Response) {Integer} profile_opened             Tổng profile đã mở thông điệp
@apiSuccess (Data Response) {Integer} turn_opened                Tổng lượt mở thông điệp
@apiSuccess (Data Response) {Integer} profile_clicked            Tổng profile click link
@apiSuccess (Data Response) {Integer} turn_clicked               Tổng lượt click link
@apiSuccess (Data Response) {Integer} profile_granted_voucher    Tổng profile nhận voucher(<code>Pending</code>)
@apiSuccess (Data Response) {Integer} turn_granted_voucher       Tổng lượt nhận voucher(<code>Pending</code>)
@apiSuccess (Data Response) {Integer} profile_used_voucher       Tổng profile sử dụng voucher
@apiSuccess (Data Response) {Integer} turn_used_voucher          Tổng lượt sử dụng voucher
@apiSuccess (Data Response) {Object}  conversion_rate            Báo cáo tỷ lệ chuyển đổi
@apiSuccess (Data Response) {Integer} turn_unsubscribed          Tổng lượt unsubscribed(<code>Pending</code>)

@apiSuccess (Data Response) {Object}  data_ab_test               Dữ liệu báo cáo node AB Test, dữ liệu gọi bên journey <https://dev.mobio.vn/docs/journey/#api-Report-ReportAbtest>
                                                                <code>Pending</code>
                                                                
@apiSuccess (conversion_rate:)   {array}        event                   Báo cáo tổng quan theo từng event
@apiSuccess (conversion_rate:)   {int}        profile                   Báo cáo theo profile
@apiSuccess (conversion_rate:)   {int}        turn_profile                   Báo cáo theo lượt profile


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": {
        "id": "node_1",
        "element_type": "WORKFLOW",
        "code": "MESSAGE_INTERACTION",
        "name": "Kiểm tra tương tác với thông điệp 1",
        "config": {},
        "profile_in_node": 100,
        "turn_in_node": 120,
        "profile_sent": 100,
        "turn_sent": 120,
        "profile_reach": 100,
        "turn_sent_success": 120,
        "profile_opened": 100,
        "turn_opened": 120,
        "profile_granted_voucher": 5,
        "turn_granted_voucher": 5,
        "profile_used_voucher": 5,
        "turn_used_voucher": 5,
        "conversion_rate":{
            "event": [
                {
                    "event_id": 1,
                    "event_name": "a",
                    "sum_profile_cr": 400,
                    "sum_turn_cr": 400
                },
                {
                    "event_id": 2,
                    "event_name": "b",
                    "sum_profile_cr": 400,
                    "sum_turn_cr": 400
                },
                {
                    "event_id": 3,
                    "event_name": "c",
                    "sum_profile_cr": 400,
                    "sum_turn_cr": 400
                }
            ],
            "profile": 600,
            "turn_profile": 1200
        },
        "data_ab_test": {
            "number_test": 1010,
            "time_test": 1093,
            "criteria": "PUSH",
            ...
        }
    }
}
"""

********************************* ReportListJourney ***********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/report-list-journey Báo cáo trên danh sách journey
@apiDescription Báo cáo trên danh sách journey
@apiGroup Journey
@apiVersion 1.0.0
@apiName ReportByJourneyIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_ids        Danh sách Journey ID; Mỗi journey_id cách nhau bởi dấu <code>,</code>
                                                          <br> vd: journey_ids=id_1,id_2 
@apiParam      (Param:)    {Boolean}    [cache_mcr]          True thì lấy data phục vụ cho việc cache data Master campaign report, 
                                                            không thì vẫn giữ chức năng 
@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [{
        "journey_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "turn_sent": 2000,
        "turn_sent_success": 2000,
        "profile_reach": 1000,
        "turn_reach": 1200,
        "turn_opened": 200,
        "turn_clicked": 200 
    }]
}
@apiSuccessExample Response: HTTP/1.1 200 OK with cache_mcr = True  {json}  
{
    "code": 200,
    "data": [
        {
            "journey_id": "698e4b15-6023-47e9-a23d-3fcb3dd57fd2",
            "name": "[P]CP voucher 1",
            "profile_reach": 3,
            "status": "paused",
            "turn_bought_product": 0,
            "turn_clicked": 0,
            "turn_cta": 0,
            "turn_granted_voucher": 3,
            "turn_opened": 3,
            "turn_reach": 3,
            "turn_sent": 11,
            "turn_sent_success": 3,
            "turn_used_voucher": 0
        },
        {
            "journey_id": "4dcab847-ed7a-464c-aa65-1f95442dca19",
            "name": "[LAN] Giỏ hàng bỏ quên",
            "profile_reach": 1,
            "status": "running",
            "turn_bought_product": 0,
            "turn_clicked": 0,
            "turn_cta": 0,
            "turn_granted_voucher": 0,
            "turn_opened": 1,
            "turn_reach": 1,
            "turn_sent": 1,
            "turn_sent_success": 1,
            "turn_used_voucher": 0
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
********************************* ReportListJourney ***********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/journey/report-list-journey Báo cáo trên danh sách journey
@apiDescription Báo cáo trên danh sách journey
@apiGroup Journey
@apiVersion 1.0.0
@apiName ReportByJourneyIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_ids        Danh sách Journey ID; Mỗi journey_id cách nhau bởi dấu <code>,</code>
                                                          <br> vd: journey_ids=id_1,id_2 
@apiParam      (Param:)    {Boolean}    [cache_mcr]          True thì lấy data phục vụ cho việc cache data Master campaign report, 
                                                            không thì vẫn giữ chức năng 
@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [{
        "journey_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "turn_sent": 2000,
        "turn_sent_success": 2000,
        "profile_reach": 1000,
        "turn_reach": 1200,
        "turn_opened": 200,
        "turn_clicked": 200 
    }]
}
@apiSuccessExample Response: HTTP/1.1 200 OK with cache_mcr = True  {json}  
{
    "code": 200,
    "data": [
        {
            "journey_id": "698e4b15-6023-47e9-a23d-3fcb3dd57fd2",
            "name": "[P]CP voucher 1",
            "profile_reach": 3,
            "status": "paused",
            "turn_bought_product": 0,
            "turn_clicked": 0,
            "turn_cta": 0,
            "turn_granted_voucher": 3,
            "turn_opened": 3,
            "turn_reach": 3,
            "turn_sent": 11,
            "turn_sent_success": 3,
            "turn_used_voucher": 0
        },
        {
            "journey_id": "4dcab847-ed7a-464c-aa65-1f95442dca19",
            "name": "[LAN] Giỏ hàng bỏ quên",
            "profile_reach": 1,
            "status": "running",
            "turn_bought_product": 0,
            "turn_clicked": 0,
            "turn_cta": 0,
            "turn_granted_voucher": 0,
            "turn_opened": 1,
            "turn_reach": 1,
            "turn_sent": 1,
            "turn_sent_success": 1,
            "turn_used_voucher": 0
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
********************************* ReportListJourney ***********************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/journey/report-list-journey Báo cáo trên danh sách journey
@apiDescription Báo cáo trên danh sách journey
@apiGroup Journey
@apiVersion 2.0.0
@apiName ReportByJourneyIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    journey_ids        Danh sách Journey ID; Mỗi journey_id cách nhau bởi dấu <code>,</code>
                                                          <br> vd: journey_ids=id_1,id_2 
@apiParam      (Param:)    {Boolean}    [cache_mcr]          True thì lấy data phục vụ cho việc cache data Master campaign report, 
                                                            không thì vẫn giữ chức năng 
@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [{
        "journey_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "turn_sent": 2000,
        "turn_sent_success": 2000,
        "profile_reach": 1000,
        "turn_reach": 1200,
        "turn_opened": 200,
        "turn_clicked": 200 
    }]
}
@apiSuccessExample Response: HTTP/1.1 200 OK with cache_mcr = True  {json}  
{
    "code": 200,
    "data": [
        {
            "journey_id": "698e4b15-6023-47e9-a23d-3fcb3dd57fd2",
            "name": "[P]CP voucher 1",
            "profile_reach": 3,
            "status": "paused",
            "turn_bought_product": 0,
            "turn_clicked": 0,
            "turn_cta": 0,
            "turn_granted_voucher": 3,
            "turn_opened": 3,
            "turn_reach": 3,
            "turn_sent": 11,
            "turn_sent_success": 3,
            "turn_used_voucher": 0
        },
        {
            "journey_id": "4dcab847-ed7a-464c-aa65-1f95442dca19",
            "name": "[LAN] Giỏ hàng bỏ quên",
            "profile_reach": 1,
            "status": "running",
            "turn_bought_product": 0,
            "turn_clicked": 0,
            "turn_cta": 0,
            "turn_granted_voucher": 0,
            "turn_opened": 1,
            "turn_reach": 1,
            "turn_sent": 1,
            "turn_sent_success": 1,
            "turn_used_voucher": 0
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
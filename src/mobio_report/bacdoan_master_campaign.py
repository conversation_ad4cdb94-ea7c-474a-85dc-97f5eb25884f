********************************* ReportByListMasterCampaign **************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/master-campaign/list Báo cáo danh sách Master Campaign (Done)
@apiDescription Báo cáo danh sách master campaign
@apiGroup Master Campaign
@apiVersion 1.0.0
@apiName ReportByListMasterCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_ids        Danh sách master_campaign_id; Mỗi master_campaign_id 
                                                                  cách nhau bởi dấu <code>,</code>
                                                                  <br> vd: master_campaign_ids=id_1,id_2 

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [{
        "master_campaign_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "turn_start_journey": 10,
        "profile_reach": 10,
        "turn_sent_success": 10,
        "turn_reach": 1200,
        "turn_opened": 200,
        "turn_clicked": 200 
    }]
}
"""
********************************* ReportByListMasterCampaign **************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/master-campaign/list Báo cáo danh sách Master Campaign (Done)
@apiDescription Báo cáo danh sách master campaign
@apiGroup Master Campaign
@apiVersion 2.0.0
@apiName ReportByListMasterCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_ids        Danh sách master_campaign_id; Mỗi master_campaign_id 
                                                                  cách nhau bởi dấu <code>,</code>
                                                                  <br> vd: master_campaign_ids=id_1,id_2 

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [{
        "master_campaign_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "turn_start_journey": 10,
        "profile_reach": 10,
        "turn_sent_success": 10,
        "turn_reach": 1200,
        "turn_opened": 200,
        "turn_clicked": 200 
    }]
}
"""

********************************* ReportListJourney ***********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/master-campaign/report-list-journey Báo cáo master campaign trên danh sách journey (Done)
@apiDescription Báo cáo trên danh sách journey
@apiGroup Master Campaign
@apiVersion 1.0.0
@apiName ReportByMasterCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_id    master_campaign_id
@apiParam      (Param:)    {String}    page    page
@apiParam      (Param:)    {String}    per_page    per_page

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [{
        "journey_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "name": "journey 1",
        "status": "running", // running, scheduled, waiting_approve, paused, done, cancelled
        "turn_sent": 2000,
        "turn_sent_success": 2000,
        "profile_reach": 1000,
        "turn_reach": 1200,
        "turn_opened": 200,
        "turn_clicked": 200 ,
        "turn_cta": 200,
        "turn_granted_voucher": 0,
        "turn_used_voucher": 0,
        "turn_bought_product": 0
    }],
    "paging": {
        "page": 0,
        "per_page": 1,
        "total_items": 100,
        "total_page": 100
    }
}
"""
********************************* ReportListJourney ***********************************
* version: 2.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v2.0/master-campaign/report-list-journey Báo cáo master campaign trên danh sách journey (Done)
@apiDescription Báo cáo trên danh sách journey
@apiGroup Master Campaign
@apiVersion 2.0.0
@apiName ReportByMasterCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    master_campaign_id    master_campaign_id
@apiParam      (Param:)    {String}    page    page
@apiParam      (Param:)    {String}    per_page    per_page

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công",
    "data": [{
        "journey_id": "877c992f-b2bc-421b-b691-b423209e4c7e",
        "name": "journey 1",
        "status": "running", // running, scheduled, waiting_approve, paused, done, cancelled
        "turn_sent": 2000,
        "turn_sent_success": 2000,
        "profile_reach": 1000,
        "turn_reach": 1200,
        "turn_opened": 200,
        "turn_clicked": 200 ,
        "turn_cta": 200,
        "turn_granted_voucher": 0,
        "turn_used_voucher": 0,
        "turn_bought_product": 0
    }],
    "paging": {
        "page": 0,
        "per_page": 1,
        "total_items": 100,
        "total_page": 100
    }
}
"""
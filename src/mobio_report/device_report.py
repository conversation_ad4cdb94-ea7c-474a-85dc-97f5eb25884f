**************************************ReportDevice*************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/device/report-by-site Báo cáo theo site
@apiDescription Báo cáo chuyên sâu theo site_id
@apiGroup Device
@apiVersion 1.0.0
@apiName ReportDeviceBySite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    site_id            Id của site cần báo cáo <code>required</code>
@apiParam      (Param:)    {String}    report             Các loại báo báo
<ul>
<li><code> visitor = Báo cáo Site sesions/ Unique Visitors/ Avg sesion duration</code></li>
<li><code> device-type = Sesions by devices</code></li>
<li><code> new-return = New vs Returning visitor</code></li>
<li><code> scroll = Scroll page</code></li>
<li><code> traffic-source = Top traffic source by session</code></li>
<li><code> conversion-profile = Conversion rate/Profile</code></li>
<li><code> conversion-deal = Conversion rate/Deal</code></li>
</ul>
@apiParam      (Param:)    {String}    start_time       start_time, time UTC: vd: <code>2021-04-21 11:08:42.775</code>
@apiParam      (Param:)    {String}    end_time         end_time, time UTC: vd: <code>2021-04-21 11:08:42.775</code>

@apiSuccessExample visitor  {json}  Response: HTTP/1.1 200 OK
{
	"code": 200,
	"message": "request thành công",
	"data": {
		"site_sessions": {
			"current": 123456,
			"previous": 123321
		},
		"unique_visitors": {
			"current": 123456,
			"previous": 123321
		},
		"avg_session_duration": {
			"current": 18.6,
			"previous": 23.3
		}
	}
}

@apiSuccessExample device-type  {json}  Response: HTTP/1.1 200 OK
{
	"code": 200,
	"message": "request thành công",
	"data": {
		"Destop": 123,
		"Mobile": 456,
		"Tablet": 111,
		"Other": 12
	}
}

@apiSuccessExample new-return  {json}  Response: HTTP/1.1 200 OK
{
	"code": 200,
	"message": "request thành công",
	"data": {
		"Unique": 123,
		"New": 23,
		"Returning": 80
	}
}

@apiSuccessExample scroll  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "rate": {
            "current": 0.01670378619153675,
            "previous": 0
        },
        "site_sessions": {
        	"5": 20,
            "10": 15,
            "100": 6,
            "15": 15,
            "20": 15,
            "25": 12,
            "30": 12,
            "35": 12,
            "40": 12,
            "45": 12,
            "50": 11,
            "55": 11,
            "60": 11,
            "65": 11,
            "70": 11,
            "75": 11,
            "80": 9,
            "85": 9,
            "90": 9,
            "95": 6
        }
    },
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccessExample traffic-source  {json}  Response: HTTP/1.1 200 OK
{
	"code": 200,
	"message": "request thành công",
	"data": {
		"Facebook": {
			"current": 123456,
			"previous": 123321
		},
		"Google": {
			"current": 123456,
			"previous": 123321
		},
		....
	}
}

@apiSuccessExample conversion-profile  {json}  Response: HTTP/1.1 200 OK
{
	"code": 200,
	"message": "request thành công",
	"data": {
		"rate": {
			"current": 4.1,
			"previous": 3.9
		},
		"timeline": {
			"13/01/2023": {
				"sessions": 123,
				"profile": 10
			},
			"14/01/2023": {
				"sessions": 456,
				"profile": 20
			}
		}
	}
}

@apiSuccessExample conversion-deal  {json}  Response: HTTP/1.1 200 OK
{
	"code": 200,
	"message": "request thành công",
	"data": {
		"rate": {
			"current": 4.1,
			"previous": 3.9
		},
		"timeline": {
			"13/01/2023": {
				"sessions": 123,
				"deal": 10
			},
			"14/01/2023": {
				"sessions": 456,
				"deal": 20
			}
		}
	}
}
"""
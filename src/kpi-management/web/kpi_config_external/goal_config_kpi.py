####################################################################################################
# Danh sách mục tiêu kinh doanh
# version: 1.0.0                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/kpi-management/external/api/v1.0/kpi/goal-configs  Danh sách mục tiêu kinh doanh
@apiGroup KpiGoalConfigExternal
@apiVersion 1.0.0
@apiName GetListGoalConfigKPI

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam	(Body:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả.
                                                              VD: ["name","code"]. Trường hợp không có fields thì sẽ
                                                              trả về tất cả thuộc tính
@apiParam  (Body:)     {String}   [start_time_create]         Thời gian bắt đầu tạo mới (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {String}   [end_time_create]           Thời gian kết thúc tạo mới (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {String}   [start_time_update]         Thời gian bắt đầu cập nhật (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {String}   [end_time_update]           Thời gian kết thúc cập nhật (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {Boolean}  [status]                    Trạng thái mục tiêu, default: <code>false</code>
@apiParam  (Body:)     {Array}    [created_by_ids]             <code>ID</code> Danh sách người tạo
@apiParam  (Body:)     {Array}    [updated_by_ids]             <code>ID</code> Danh sách nười cập nhật
@apiParam  (Body:)     {Array}    [list_formula]              Danh sách ID Công thức </br>
                                                              <code>sum</code> Tổng </br>
                                                              <code>average</code> Bình quân</br>
@apiParam	(Body:)	    {String}	 [search]			      Tìm kiếm

@apiParam	(Query:)	    {Int}	     [get_all]			  <code>-1</code> Lấy tất cả
@apiParam	(Query:)		{String=asc,desc}	[order]   	  Kiểu sắp xếp:
                                                              <ul>
                                                                <li><code>asc: </code> sắp xếp tăng dần</li>
                                                                <li><code>desc: </code> sắp xếp giảm dần</li>
                                                              </ul>

@apiParam	(Query:)	    {String}	 [sort]		          Field key cần sort, default là: <code>updated_time</code>
@apiUse paging_tokens


@apiParamExample {json} Body example
{
    "fields": ["_id", "name", "code"],
    "status": true,
    "created_by_ids": ["account_id1", "account2"],
    "updated_by_ids": ["account_id1", "account2"],
    "start_time_create": "2023-10-09",
    "end_time_create": "2023-10-09",
    "start_time_update": "2023-10-09",
    "end_time_update": "2023-10-09"
}

@apiSuccess {Array}             data                          Danh sách mục tiêu
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id": "6520ca0ed3ba586dd41f8940",
            "code": "13923",
            "created_by": "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737",
            "created_time": "2023-10-07T03:01:34.000Z",
            "description": "o day ko co gi vui ca",
            "id": "6520ca0ed3ba586dd41f8940",
            "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
            "name": "muc tieu dai han",
            "status": true,
            "unit_id": "651fe4984cfc668931658e1d",
            "updated_by": null,
            "updated_time": "2023-10-07T03:01:34.000Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "WyIyMDIzLTEwLTA3VDAzOjAxOjM0LjAwMFoiLCAiNjUyMGNhMGVkM2JhNTg2ZGQ0MWY4OTQwIl0=",
            "before": ""
        },
        "per_page": 10
    }
}
"""

####################################################################################################
# Số lượng mục tiêu kinh doanh theo trạng thái
# version: 1.0.0                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/kpi-management/external/api/v1.0/kpi/goal-config/total  Số lượng mục tiêu kinh doanh theo trạng thái
@apiGroup KpiGoalConfigExternal
@apiVersion 1.0.0
@apiName TotalGoalConfigKPI

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [start_time_create]         Thời gian bắt đầu tạo mới (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {String}   [end_time_create]           Thời gian kết thúc tạo mới (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {String}   [start_time_update]         Thời gian bắt đầu cập nhật (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {String}   [end_time_update]           Thời gian kết thúc cập nhật (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {Array}    [created_by_ids]             <code>ID</code> Danh sách người tạo
@apiParam  (Body:)     {Array}    [updated_by_ids]             <code>ID</code> Danh sách nười cập nhật
@apiParam	(Body:)	   {String}	  [search]			          Tìm kiếm


@apiParamExample {json} Body example
{
    "created_by_ids": ["account_id1", "account2"],
    "updated_by_ids": ["account_id1", "account2"],
    "start_time_create": "2023-10-09",
    "end_time_create": "2023-10-09",
    "start_time_update": "2023-10-09",
    "end_time_update": "2023-10-09"
}

@apiSuccess {Object}            data                          Danh sách mục tiêu
@apiSuccess {Int}               data.total_active             Số lượng đang áp dụng   
@apiSuccess {Int}               data.total_not_active         Số lượng không áp dụng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "total_active": 2,
        "total_not_active": 1
    }
}
"""

####################################################################################################
# Thêm mới mục tiêu
# version: 1.0.0                                                                                   #
####################################################################################################
"""
@api {POST} {domain}/kpi-management/external/api/v1.0/kpi/goal-config Thêm mới mục tiêu kinh doanh
@apiGroup KpiGoalConfigExternal
@apiVersion 1.0.0
@apiName AddKpiGoalConfigExternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam	(Body:)			{String}   name             Tên mục tiêu
@apiParam	(Body:)			{String}   code             Mã mục tiêu
@apiParam	(Body:)			{String}   formula          Công thức </br>
                                                        <code>sum</code> Tổng </br>
                                                        <code>average</code> Bình quân</br>
@apiParam	(Body:)			{String}   [description]    Mô tả
@apiParam	(Body:)			{String}   [unit_id]        <code>ID</code> đơn vị tính
@apiParam	(Body:)			{Object}   [unit_info]      Thông tin đơn vị tính ở lựa chọn <code>other</code>
@apiParam	(Body:)			{String}   unit_info.name      Tên đơn vị tính khi chọn ở option khác <code>other</code>
@apiParam	(Body:)			{Boolean}   status          Trạng thái mục tiêu

@apiSuccess         {Object}        data                    Dữ liệu trả về
@apiSuccess         {Object}        data.code               Mã mục tiêu
@apiSuccess         {Object}        data.created_by         ID người tạo
@apiSuccess         {Object}        data.description        Mô tả
@apiSuccess         {Object}        data.name               Tên mục tiêu
@apiSuccess         {Object}        data.status             Trạng thái mục tiêu
@apiSuccess         {Object}        data.unit_id            ID đơn vị tính
@apiSuccess         {Object}        data.unit_info          Thông tin đơn vị tính ở lựa chọn <code>other</code>
@apiSuccess         {Object}        data.status             Trạng thái mục tiêu
@apiSuccess         {Object}        data.updated_by         ID người cập nhật
@apiSuccess         {Object}        data.updated_time       Thời gian cập nhật


@apiParamExample {json} Body example
{
    "name": "muc tieu dai han",
    "code": "13923",
    "description": "o day ko co gi vui ca",
    "unit_id": "651fe4984cfc668931658e1d",
    "status": true,
    "unit_info": {
        "code": "other"
        "name": "Khách hàng"
    }
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "_id": "6520ca0ed3ba586dd41f8940",
        "code": "13923",
        "created_by": "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737",
        "created_time": "2023-10-07T03:01:34.000Z",
        "description": "o day ko co gi vui ca",
        "id": "6520ca0ed3ba586dd41f8940",
        "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
        "name": "muc tieu dai han",
        "status": true,
        "unit_id": "651fe4984cfc668931658e1d",
        "updated_by": null,
        "updated_time": "2023-10-07T03:01:34.000Z"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

####################################################################################################
# Cập nhật mục tiêu kinh doanh
# version: 1.0.0                                                                                   #
####################################################################################################
"""
@api {PUT} {domain}/kpi-management/external/api/v1.0/kpi/goal-config/<goal_config_id> Cập nhật mục tiêu kinh doanh
@apiGroup KpiGoalConfigExternal
@apiVersion 1.0.0
@apiName UpdateKpiGoalConfigExternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam	(Param:)	    {String}	 goal_config_id   ID mục tiêu kinh doanh (<code>id</code>)

@apiParam	(Body:)			{String}   name             Tên mục tiêu
@apiParam	(Body:)			{String}   [description]    Mô tả
@apiParam	(Body:)			{String}   [formula]          Công thức </br>
                                                        <code>sum</code> Tổng </br>
                                                        <code>average</code> Bình quân</br>
@apiParam	(Body:)			{String}   [unit_id]          <code>ID</code> đơn vị tính
@apiParam	(Body:)			{Object}   [unit_info]      Thông tin đơn vị tính ở lựa chọn <code>other</code>
@apiParam	(Body:)			{String}   unit_info.name      Tên đơn vị tính khi chọn ở option khác <code>other</code>
@apiParam	(Body:)			{Boolean}   status          Trạng thái mục tiêu

@apiSuccess         {Object}        data                    Dữ liệu trả về
@apiSuccess         {Object}        data.code               Mã mục tiêu
@apiSuccess         {Object}        data.created_by         ID người tạo
@apiSuccess         {Object}        data.description        Mô tả
@apiSuccess         {Object}        data.name               Tên mục tiêu
@apiSuccess         {Object}        data.status             Trạng thái mục tiêu
@apiSuccess         {Object}        data.unit_id            ID đơn vị tính
@apiSuccess         {Object}        data.status             Trạng thái mục tiêu
@apiSuccess         {Object}        data.updated_by         ID người cập nhật
@apiSuccess         {Object}        data.updated_time       Thời gian cập nhật


@apiParamExample {json} Body example
{
    "name": "muc tieu dai han",
    "code": "13923",
    "description": "o day ko co gi vui ca",
    "unit_id": "651fe4984cfc668931658e1d",
    "status": true
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "_id": "6520ca0ed3ba586dd41f8940",
        "code": "13923",
        "created_by": "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737",
        "created_time": "2023-10-07T03:01:34.000Z",
        "description": "o day ko co gi vui ca",
        "id": "6520ca0ed3ba586dd41f8940",
        "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
        "name": "muc tieu dai han",
        "status": true,
        "unit_id": "651fe4984cfc668931658e1d",
        "updated_by": null,
        "updated_time": "2023-10-07T03:01:34.000Z"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

####################################################################################################
# Cập nhật trạng thái nhiều mục tiêu kinh doanh
# version: 1.0.0                                                                                   #
####################################################################################################
"""
@api {PUT} {domain}/kpi-management/external/api/v1.0/kpi/goal-configs/update-status Cập nhật trạng thái nhiều mục tiêu kinh doanh
@apiGroup KpiGoalConfigExternal
@apiVersion 1.0.0
@apiName UpdateStatusKpiGoalConfigExternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam	(Body:)	        {Array}	 goal_config_ids    Danh sách ID mục tiêu kinh doanh (<code>id</code>)
@apiParam	(Body:)			{Boolean}   status          Trạng thái mục tiêu

@apiSuccess         {Object}        data                    Dữ liệu trả về
@apiSuccess         {Array}        data.goal_config_ids    Danh sách ID mục tiêu được cập nhật


@apiParamExample {json} Body example
{
    "goal_config_ids": ["6520ca0ed3ba586dd41f8940"],
    "status": true
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "goal_config_ids": [
            "6520ca0ed3ba586dd41f8940"
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} {domain}/kpi-management/external/api/v1.0/kpi/goal-config/staff/<staff_id>  Danh sách mục tiêu kinh doanh theo nhân viên
@apiGroup KpiGoalConfigExternal
@apiVersion 1.0.0
@apiName GetListGoalConfigKPIStaff

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_id                   ID Nhân viên 
@apiParam  (Param:)     {String}   year                       Năm thiết lập mục tiêu


@apiSuccess {Array}             data                          Danh sách mục tiêu
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id": "6520ca0ed3ba586dd41f8940",
            "code": "13923",
            "description": "o day ko co gi vui ca",
            "id": "6520ca0ed3ba586dd41f8940",
            "name": "muc tieu dai han",
            "unit_id": "651fe4984cfc668931658e1d",
            "formula": ""
        }
    ],
    "lang": "vi",
    "message": "request thành công."
    }
}
"""


"""
@api {POST} {domain}/kpi-management/external/api/v1.0/kpi/goal-config-by-codes  Danh sách mục tiêu kinh doanh theo mã mục tiêu
@apiGroup KpiGoalConfigExternal
@apiVersion 1.0.0
@apiName GetListGoalConfigKPIByGoalCodes

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {Array}   goal_codes                   Danh sách mã mục tiêu


@apiSuccess {Array}             data                          Danh sách mục tiêu
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id": "6520ca0ed3ba586dd41f8940",
            "code": "13923",
            "description": "o day ko co gi vui ca",
            "id": "6520ca0ed3ba586dd41f8940",
            "name": "muc tieu dai han",
            "unit_id": "651fe4984cfc668931658e1d",
            "formula": ""
        }
    ],
    "lang": "vi",
    "message": "request thành công."
    }
}
"""

"""
@api {POST} {domain}/kpi-management/external/api/v1.0/export/monthly/forecast Export báo c<PERSON><PERSON> mục tiê<PERSON> - <PERSON><PERSON>, lu<PERSON> kế thực hiện trong tháng
@apiGroup ExportDashboardKpiExternal
@apiVersion 1.0.0
@apiName ExportReportCumulativeStaffsExternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {List}    team_ids       Danh sách ID team
@apiParam  (Body:)     {List}    staff_ids      Danh sách ID nhân viên

@apiParamExample {json} Body example
{
    "team_ids": ["e72306aa-fc45-11ec-bda9-f94ce838542e"],
    "staff_ids": ["9eee7fe6-c21d-408b-8f5a-77337e19f673", "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737"]
}

@apiSuccess (data)   {Number}   type            Kiểu xuất file.
                                                <ul>
                                                    <li><code>1</code>: download</li>
                                                    <li><code>2</code>: sent mail</li>
                                                </ul>
@apiSuccess (data)   {String}   [link]          Link download file excel.
@apiSuccess (data)   {Number}   [limit]         Giới hạn bản ghi chuyển sang kiểu xuất file qua mail.

@apiSuccessExample Response link:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "type": 1,
        "link": "https://api-p.test1.mobio.vn/sale/static/export_excel/Bao_cao_20210921_0832AM.xlsx"
    }
}

@apiSuccessExample Response sent mail:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "type": 2,
        "limit": 1000
    }
}
"""

"""
@api {POST} {domain}/kpi-management/external/api/v1.0/export/monthly/performance-staff-goals  Export kết quả thực hiện mục tiêu của nhân viên theo tháng
@apiGroup ExportDashboardKpiExternal
@apiVersion 1.0.0
@apiName ExportDashboardMonthlyPerformanceStaffGoalExternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {List}    team_ids       Danh sách ID team
@apiParam  (Body:)     {List}    staff_ids      Danh sách ID nhân viên
@apiParam  (Body:)     {String}    goal_code     Danh sách mã mục tiêu
@apiParam  (Body:)     {String}    year        Năm

@apiParamExample {json} Body example
{
    "team_ids": ["e72306aa-fc45-11ec-bda9-f94ce838542e"],
    "staff_ids": ["9eee7fe6-c21d-408b-8f5a-77337e19f673", "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737"],
    "goal_code": "LINH20",
    "year": "2023"
}

@apiSuccess (data)   {Number}   type            Kiểu xuất file.
                                                <ul>
                                                    <li><code>1</code>: download</li> 
                                                    <li><code>2</code>: sent mail</li> 
                                                </ul>
@apiSuccess (data)   {String}   [link]          Link download file excel.
@apiSuccess (data)   {Number}   [limit]         Giới hạn bản ghi chuyển sang kiểu xuất file qua mail.

@apiSuccessExample Response link:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "type": 1,
        "link": "https://api-p.test1.mobio.vn/sale/static/export_excel/Bao_cao_20210921_0832AM.xlsx"
    }
}

@apiSuccessExample Response sent mail:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "type": 2,
        "limit": 1000
    }
}
"""

"""
@api {POST} {domain}/kpi-management/external/api/v1.0/export/staff/monthly/target-compare-forecast Export  Kết quả thực hiện mục tiêu trong tháng của nhân viên so với dự báo
@apiGroup ExportDashboardKpiExternal
@apiVersion 1.0.0
@apiName ExportDashboardStaffTargetCompareForecastExternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {List}    team_ids       Danh sách ID team
@apiParam  (Body:)     {List}    staff_ids      Danh sách ID nhân viên
@apiParam  (Body:)     {String}    goal_code     Danh sách mã mục tiêu
@apiParam  (Body:)     {String}    year      Năm
@apiParam  (Body:)     {String}    month     Tháng

@apiParamExample {json} Body example
{
    "team_ids": ["e72306aa-fc45-11ec-bda9-f94ce838542e"],
    "staff_ids": ["9eee7fe6-c21d-408b-8f5a-77337e19f673", "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737"],
    "goal_code": "LINH20",
    "year": "2023",
    "month": "03"
}

@apiSuccess (data)   {Number}   type            Kiểu xuất file.
                                                <ul>
                                                    <li><code>1</code>: download</li> 
                                                    <li><code>2</code>: sent mail</li> 
                                                </ul>
@apiSuccess (data)   {String}   [link]          Link download file excel.
@apiSuccess (data)   {Number}   [limit]         Giới hạn bản ghi chuyển sang kiểu xuất file qua mail.

@apiSuccessExample Response link:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "type": 1,
        "link": "https://api-p.test1.mobio.vn/sale/static/export_excel/Bao_cao_20210921_0832AM.xlsx"
    }
}

@apiSuccessExample Response sent mail:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "type": 2,
        "limit": 1000
    }
}
"""


"""
@api {POST} {domain}/kpi-management/external/api/v1.0/export/kpi/report/goals  Export kế hoạch, dự báo kết quả thực hiện trong tháng hiện tại
@apiGroup ExportDashboardKpiExternal
@apiVersion 1.0.0
@apiName ExportDashboardKpiNumberOrForecastExternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {List}    team_ids       Danh sách ID team
@apiParam  (Body:)     {List}    staff_ids      Danh sách ID nhân viên
@apiParam  (Body:)     {String}    type         Loại báo cáo </br>
                                                <code>actual</code>: Kế hoạch </br>
                                                <code>forecast</code>: Dự báo </br>

@apiParamExample {json} Body example
{
    "team_ids": ["e72306aa-fc45-11ec-bda9-f94ce838542e"],
    "staff_ids": ["9eee7fe6-c21d-408b-8f5a-77337e19f673", "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737"],
    "type": "actual"
}

@apiSuccess (data)   {Number}   type            Kiểu xuất file.
                                                <ul>
                                                    <li><code>1</code>: download</li> 
                                                    <li><code>2</code>: sent mail</li> 
                                                </ul>
@apiSuccess (data)   {String}   [link]          Link download file excel.
@apiSuccess (data)   {Number}   [limit]         Giới hạn bản ghi chuyển sang kiểu xuất file qua mail.

@apiSuccessExample Response link:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "type": 1,
        "link": "https://api-p.test1.mobio.vn/sale/static/export_excel/Bao_cao_20210921_0832AM.xlsx"
    }
}

@apiSuccessExample Response sent mail:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "type": 2,
        "limit": 1000
    }
}
"""


"""
@api {POST} {domain}/kpi-management/external/api/v1.0/kpi/sync/record-forecast/staff  Đ<PERSON><PERSON> bộ thông tin dự báo và cập nhật kết quả KPI theo nhân viên
@apiGroup KpiSyncExternal
@apiVersion 1.0.0
@apiName KpiSyncRecordAndForecast

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {String}    dsp_eb_company_id        ID Công ty
@apiParam  (Body:)     {String}    goal_code                Mã mục tiêu
@apiParam  (Body:)     {Number}    type                     Cách tính
                                                            <ul>
                                                                <li><code>1: </code> Tính bình quân</li>
                                                                <li><code>2: </code> Tính tổng</li>
                                                            </ul>
@apiParam  (Body:)     {String}     unit                     Đơn vị tính
@apiParam  (Body:)     {String}     staff_code               Mã nhân viên
@apiParam  (Body:)     {String}     year                     Năm thực hiện
@apiParam  (Body:)     {String}     month                    Tháng thực hiện
@apiParam  (Body:)     {Number}     kpi_number               Số chỉ tiêu tháng được giao
@apiParam  (Body:)     {Number}     forecast_number          Số dự báo
@apiParam  (Body:)     {Number}     actual_number            Số thực hiện
@apiParam  (Body:)     {String}     created_time             Ngày tạo (Format: <code>yyyy-mm-dd hh:mm</code>  ex: 2023-05-10 20:05)
@apiParam  (Body:)     {String}     updated_time             Ngày cập nhật <Format: <code>yyyy-mm-dd hh:mm</code>  ex: 2023-05-10 20:05)

@apiParamExample {json} Body example
{
    "dsp_eb_company_id": "651f78450a8585989462b929",
    "goal_code": "LINH23",
    "type": 2,
    "unit": "Khách hàng",
    "staff_code": "123213",
    "year": "2023",
    "month": "08",
    "kpi_number": 323424,
    "forecast_number": 4343545,
    "actual_number": 434354,
    "created_time": "2023-01-10 19:03",
    "updated_time": "2023-05-10 20:05"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {String}            tracking_code                 Mã tracking request
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin dự báo
@apiSuccess {Object}            data.id                       Id bản ghi KPI

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "id": "653723e015bf8a792cbdfe5c"
    },
    "lang": "vi",
    "message": "request thành công.",
    "tracking_code": "0a74e109-4672-4fc7-a4a6-ef6eef16976a"
}
"""

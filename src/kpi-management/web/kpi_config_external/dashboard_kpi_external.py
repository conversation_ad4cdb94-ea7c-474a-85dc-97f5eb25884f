"""
@api {POST} {domain}/kpi-management/external/api/v1.0/monthly/forecast/staffs  <PERSON>h sách nhân viên theo b<PERSON>o c<PERSON><PERSON> lu<PERSON> kế
@apiGroup DashboardKpiExternal
@apiVersion 1.0.0
@apiName DashboardReportCumulativeStaffsExternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {List}    team_ids       Danh sách ID team
@apiParam  (Body:)     {List}    staff_ids      Danh sách ID nhân viên

@apiUse paging_tokens

@apiParamExample {json} Body example
{
    "team_ids": ["e72306aa-fc45-11ec-bda9-f94ce838542e"],
    "staff_ids": ["9eee7fe6-c21d-408b-8f5a-77337e19f673", "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737"]
}

@apiSuccess {String}            message                       <PERSON>ô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {List}              data                          Thông tin dự báo
@apiSuccess {String}            data.staff_id                 ID nhân viên

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "staff_id": "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737"
        },
        {
            "staff_id": "9eee7fe6-c21d-408b-8f5a-77337e19f673"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paginate": {
        "page": 1,
        "page_count": 1,
        "per_page": 15,
        "total_count": 2
    }
}
"""

"""
@api {POST} {domain}/kpi-management/external/api/v1.0/monthly/forecast/detail Chi tiết báo cáo Luỹ kế theo từng nhân viên
@apiGroup DashboardKpiExternal
@apiVersion 1.0.0
@apiName DashboardReportCumulativeDetailByStaffExternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {String}    staff_ids      Danh sách ID nhân viên

@apiUse paging_tokens

@apiParamExample {json} Body example
{
    "staff_id": "9eee7fe6-c21d-408b-8f5a-77337e19f673"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {List}              data                          Thông tin dự báo
@apiSuccess {String}            data.goal_code                Mã mục tiêu
@apiSuccess {Number}            data.accumulation             Luỹ kế tới tháng T-1
@apiSuccess {Number}            data.kpi_number               Chỉ tiêu năm
@apiSuccess {Number}            data.forecast                 Dự báo tháng hiện tại
@apiSuccess {Number}            data.formula                  Công thức
@apiSuccess {String}            data.unit_id                  ID đơn vị tính
@apiSuccess {String}            data.name                     Tên mục tiêu

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "accumulation": 50,
            "code": "LINH22",
            "forecast": 0,
            "formula": "sum",
            "goal_code": "LINH22",
            "kpi_number": 200,
            "name": "linh22",
            "unit_id": "6524d35f01352ef3e3bb984e",
            "updated_time": 1698191507.549
        },
        {
            "accumulation": 320,
            "code": "LINH20",
            "forecast": 0,
            "formula": "sum",
            "goal_code": "LINH20",
            "kpi_number": 5000500,
            "name": "linh 20",
            "unit_id": "6524d35f01352ef3e3bb984f",
            "updated_time": 1698191507.549
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paginate": {
        "page": 1,
        "page_count": 1,
        "per_page": 15,
        "total_count": 1
    }
}
"""

"""
@api {POST} {domain}/kpi-management/external/api/v1.0/monthly/performance-staff-goals  Kết quả thực hiện mục tiêu của nhân viên theo tháng
@apiGroup DashboardKpiExternal
@apiVersion 1.0.0
@apiName DashboardMonthlyPerformanceStaffGoalExternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {List}    team_ids       Danh sách ID team
@apiParam  (Body:)     {List}    staff_ids      Danh sách ID nhân viên
@apiParam  (Body:)     {String}    goal_code      Mã mục tiêu kinh doanh
@apiParam  (Body:)     {String}    year        Năm

@apiUse paging_tokens

@apiParamExample {json} Body example
{
    "team_ids": ["e72306aa-fc45-11ec-bda9-f94ce838542e"],
    "staff_ids": ["9eee7fe6-c21d-408b-8f5a-77337e19f673", "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737"],
    "goal_code":  "LINH20",
    "year": "2023"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {List}              data                          Thông tin kết quả
@apiSuccess {String}           data.staff_id                 ID nhân viên
@apiSuccess {Float}            data.january                  Kết quả tháng 1
@apiSuccess {Float}            data.february                 Kết quả tháng 2
@apiSuccess {Float}            data.march                    Kết quả tháng 3
@apiSuccess {Float}            data.april                    Kết quả tháng 4
@apiSuccess {Float}            data.may                      Kết quả tháng 5
@apiSuccess {Float}            data.june                     Kết quả tháng 6
@apiSuccess {Float}            data.july                     Kết quả tháng 7
@apiSuccess {Float}            data.august                   Kết quả tháng 8
@apiSuccess {Float}            data.september                Kết quả tháng 9
@apiSuccess {Float}            data.october                  Kết quả tháng 10
@apiSuccess {Float}            data.november                 Kết quả tháng 11
@apiSuccess {Float}            data.december                 Kết quả tháng 12
@apiSuccess {Float}            data.accumulation             Luỹ kế tới tháng t-1
@apiSuccess {Float}            data.accumulation_kpi_number  Lũy kế mục tiêu tời thời điểm T-1
@apiSuccess {Float}            data.kpi_number               Chỉ tiêu năm

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "accumulation": 320,
            "april": 0,
            "august": 0,
            "december": 0,
            "february": 10,
            "january": 300,
            "july": 0,
            "june": 0,
            "kpi_number": 5000500,
            "march": 10,
            "may": 0,
            "november": 0,
            "october": 0,
            "september": 0,
            "staff_id": "9eee7fe6-c21d-408b-8f5a-77337e19f673"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paginate": {
        "page": 1,
        "page_count": 2,
        "per_page": 1,
        "total_count": 2
    }
}
"""

"""
@api {POST} {domain}/kpi-management/external/api/v1.0/staff/monthly/target-compare-forecast  Kết quả thực hiện mục tiêu trong tháng của nhân viên so với dự báo
@apiGroup DashboardKpiExternal
@apiVersion 1.0.0
@apiName DashboardStaffTargetCompareForecastExternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {List}    team_ids       Danh sách ID team
@apiParam  (Body:)     {List}    staff_ids      Danh sách ID nhân viên
@apiParam  (Body:)     {String}    goal_code     Danh sách mã mục tiêu
@apiParam  (Body:)     {String}    year      Năm
@apiParam  (Body:)     {String}    month     Tháng

@apiUse paging_tokens

@apiParamExample {json} Body example
{
    "team_ids": ["e72306aa-fc45-11ec-bda9-f94ce838542e"],
    "staff_ids": ["9eee7fe6-c21d-408b-8f5a-77337e19f673", "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737"],
    "goal_code": "LINH20",
    "year": "2023",
    "month": "03"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {List}              data                          Thông tin kết quả 
@apiSuccess {String}           data.staff_id                    ID nhân viên
@apiSuccess {Float}            data.actual_number               Thực tế thực hiện
@apiSuccess {Float}            data.forecast_number             Dự báo

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "actual_number": 10,
            "forecast_number": 300000,
            "staff_id": "9eee7fe6-c21d-408b-8f5a-77337e19f673"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paginate": {
        "page": 0,
        "page_count": 1,
        "per_page": 15,
        "total_count": 1
    }
}
"""


"""
@api {POST} {domain}/kpi-management/external/api/v1.0/kpi/report/goals  Kế hoạch, dự báo kết quả thực hiện trong tháng hiện tại
@apiGroup DashboardKpiExternal
@apiVersion 1.0.0
@apiName DashboardKpiNumberOrForecastExternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {List}    team_ids       Danh sách ID team
@apiParam  (Body:)     {List}    staff_ids      Danh sách ID nhân viên
@apiParam  (Body:)     {String}    type         Loại báo cáo </br>
                                                <code>actual</code>: Kế hoạch </br>
                                                <code>forecast</code>: Dự báo </br>

@apiUse paging_tokens

@apiParamExample {json} Body example
{
    "team_ids": ["e72306aa-fc45-11ec-bda9-f94ce838542e"],
    "staff_ids": ["9eee7fe6-c21d-408b-8f5a-77337e19f673", "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737"],
    "type": "actual"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {List}              data                          Thông tin kết quả 
@apiSuccess {String}           data.staff_id                    ID nhân viên
@apiSuccess {Float}            data.actual_number               Thực tế thực hiện
@apiSuccess {Float}            data.kpi_number                  Chi tiêu tháng
@apiSuccess {Float}            data.forecast_number             Dự báo tháng

@apiSuccessExample {json} Response đối với type = "actual"
{
    "code": 200,
    "data": [
        {
            "kpi_number": 500000,
            "actual_number": 300000,
            "goal_code": "LINH20"
        },
        {
            "kpi_number": 1000,
            "actual_number": 500,
            "goal_code": "LINH22"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paginate": {
        "page": 0,
        "page_count": 1,
        "per_page": 15,
        "total_count": 1
    }
}

@apiSuccessExample {json} Response đối với type = "forecast"
{
    "code": 200,
    "data": [
        {
            "forecast_number": 500000,
            "actual_number": 300000,
            "goal_code": "LINH20"
        },
        {
            "forecast_number": 700000,
            "actual_number": 30000,
            "goal_code": "LINH22"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paginate": {
        "page": 0,
        "page_count": 1,
        "per_page": 15,
        "total_count": 1
    }
}
"""


####################################################################################################
# Lấy cấu hình bộ lọc dashboard KPI
# version: 1.0.0                                                                                   #
####################################################################################################
"""
@api {GET} {domain}/kpi-management/api/v1.0/dashboard/config/filter   L<PERSON>y cấu hình bộ lọc dashboard KPI
@apiGroup DashboardKpiConfigFilter
@apiVersion 1.0.0
@apiName GetDashboardKpiConfigFilter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header


@apiSuccess         {Object}         data                    Dữ liệu trả về


@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "report_date": "",
        "report_month": "",
        "report_year": "",
        "area_ids": [],
        "group_position_ids": [],
        "business_unit": "",
        "leader_group_business": "",
        "account_view": "",
        "group_position_exclude": "",
        "sol_ids": "",
        "account_ids": "",
        "staff_title": "",
        "department_id": "",
        "region": "",
        "staff_id": ""
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

####################################################################################################
# Cập nhật cấu hình bộ lọc dashboard KPI
# version: 1.0.0                                                                                   #
####################################################################################################
"""
@api {POST} {domain}/kpi-management/api/v1.0/dashboard/config/filter   Cập nhật cấu hình bộ lọc dashboard KPI
@apiGroup DashboardKpiConfigFilter
@apiVersion 1.0.0
@apiName UpdateDashboardKpiConfigFilter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header


@apiParamExample {json} Body example
{
    "report_date": "",
    "report_month": "",
    "report_year": "",
    "area_ids": [],
    "group_position_ids": [],
    "business_unit": "",
    "leader_group_business": "",
    "account_view": "",
    "group_position_exclude": "",
    "sol_ids": "",
    "account_ids": "",
    "staff_title": "",
    "department_id": "",
    "region": "",
    "staff_id": ""
}

@apiSuccess         {Object}         data                    Dữ liệu trả về


@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "report_date": "",
        "report_month": "",
        "report_year": "",
        "area_ids": [],
        "group_position_ids": [],
        "business_unit": "",
        "leader_group_business": "",
        "account_view": "",
        "group_position_exclude": "",
        "sol_ids": "",
        "account_ids": "",
        "staff_title": "",
        "department_id": "",
        "region": "",
        "staff_id": ""
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: nguyenthong
    Company: M O B I O
    Date Created: 13/11/2023
"""
# ---------------------------------------------
# Danh sách nhân viên trong nhóm quản lý
# version: 1.0.0
# ---------------------------------------------

"""
@api {post} {domain}/kpi-management/api/v1.0/staff-manager      <PERSON><PERSON> sách nhân viên trong nhóm quản lý
@apiDescription Danh sách ID nhân viên thuộc quản lý của SRBO, SRM
@apiGroup StaffConfig
@apiVersion 1.0.0
@apiName StaffConfigStaffManager

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
                                                              
@apiParamExample {json} Body example
{
    "report_month": "2023-10"
}

@apiSuccess {Object}        data                          Dữ liệu trả về
@apiSuccess {Array}        data.staff_ids                Danh sách ID nhân viên


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "staff_ids": [
            "d085b48b-5b1b-4c20-a2e5-2ae94b866292"
        ]  
    }   
}
"""

# ---------------------------------------------
# ID quản lý theo nhân viên
# version: 1.0.0
# ---------------------------------------------

"""
@api {post} {domain}/kpi-management/api/v1.0/manager-staff     Id quản lý theo nhân viên
@apiDescription ID SRM, SRBO theo nhân viên
@apiGroup StaffConfig
@apiVersion 1.0.0
@apiName StaffConfigManagerStaff

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_month]         Kỳ báo cáo tháng (Format: <code>%Y-%m</code>) (Trường hợp không có thì lấy theo tháng hiện tại)
@apiParam  (Body:)     {String}   [staff_id]             ID nhân viên (trường hợp không có thì lấy ID theo token)

@apiParamExample {json} Body example
{
    "report_month": "2023-11",
    "staff_id": "519f7913-6c01-4d70-9470-aad75e707ee1"
}

@apiSuccess {Object}        data                         Dữ liệu trả về
@apiSuccess {Array}        data.manager_id               ID quản lý


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "manager_id": "9f7e6739-ac06-4f75-8078-f1cf210540e3"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Danh sách cấp độ theo chức danh
# version: 1.0.0
# ---------------------------------------------

"""
@api {GET} {domain}/kpi-management/api/v1.0/staff-level/<staff_title>      Danh sách cấp độ theo chức danh
@apiDescription Danh sách cấp độ theo chức danh RM, SRM, SM, SRBO
@apiGroup StaffConfig
@apiVersion 1.0.0
@apiName StaffLevelByStaffTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                Mã chức danh (<code>RM, SRM, SRBO, RBO</code>

@apiSuccess {Array[Object]}        data                          Danh sách cấp độ
@apiSuccess {String}               data.staff_level              Mã cấp độ
@apiSuccess {String}               data.staff_level_name         Tên cấp độ


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "staff_level": "CD109",
            "staff_level_name": "RM cấp 01"
        },
        {
            "staff_level": "CD110",
            "staff_level_name": "RM cấp 02"
        },
        {
            "staff_level": "CD111",
            "staff_level_name": "RM cấp 03"
        },
        {
            "staff_level": "CD112",
            "staff_level_name": "RM cấp 04"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

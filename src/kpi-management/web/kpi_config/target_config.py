#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: thong
    Company: M O B I O
    Date Created: 13/06/2024
"""

####################################################################################################
# Thêm chỉ tiêu
# version: 1.0.1                                                                                   #
# version: 1.0.0                                                                                   #
####################################################################################################

# version: 1.0.1
"""
@api {POST} {domain}/kpi-management/api/v1.0/target  Thêm chỉ tiêu
@apiGroup Target Config
@apiVersion 1.0.1
@apiName TargetAdd

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   name                        Tên chỉ tiêu
@apiParam  (Body:)     {String}   [code]                      Mã chỉ tiêu
@apiParam  (Body:)     {String}   staff_title                 Áp dụng cho nhóm chức danh
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRM</code></li>
                                                                <li><code>CSO</code></li>
                                                                <li><code>SM</code></li>
                                                                <li><code>GDV</code></li>
                                                                <li><code>KSV</code></li>
                                                                <li><code>RM0</code></li>
                                                              </ul>

@apiParam  (Body:)     {Array}    apply                       Cấu hình thông tin áp dụng
@apiParam  (Body:)     {Number}   apply.type                  Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiParam  (Body:)     {StringDate}   apply.start_time        Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m</code>. 
                                                              Mặc định: <code>current_month</code>)
@apiParam  (Body:)     {StringDate}   [apply.end_time]              Thời gian kết thúc áp dụng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}       [apply.parent_code]           Mã chỉ tiêu cha
@apiParam  (Body:)     {String}       [apply.id]              ID khối (Support FE)
                                                              
@apiParam  (Body:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiParam  (Body:)     {String}       [unit]                  Đơn vị chỉ tiêu

@apiParam  (Body:)     {ListObject}   [config_index]          Mapping dữ liệu chỉ tiêu
@apiParam  (Body:)     {string}       config_index.field_key  Key trường thông tin đồng bộ
@apiParam  (Body:)     {string}       config_index.type       Chỉ số đồng bộ
                                                              <ul>
                                                                <li><code>sales_plan</code>: Sales plan (điểm chỉ tiêu giao)</li>
                                                                <li><code>sales_plan_exchange_revenue</code>: Doanh số chỉ tiêu giao</li>
                                                                <li><code>exchange</code>: Điểm thực hiện chỉ tiêu</li>
                                                                <li><code>revenue</code>: Doanh số thực hiện</li>
                                                                <li><code>completion_rate</code>: Tỷ lệ hoàn thành chỉ tiêu</li>
                                                                <li><code>comparison_period</code>: Giá trị kỳ so sánh</li>
                                                                <li><code>net_increase</code>: Tăng ròng</li>
                                                                <li><code>revenue_last_month</code>: Doanh số tháng trước</li>
                                                              </ul>
@apiParam  (Body:)     {string}       config_index.data_type  Kiểu dữ liệu 
                                                              <ul>
                                                                <li><code>Float64</code>: Số thực</li>
                                                              </ul>
@apiParam  (Body:)     {ArrayString}       config_index.table_name Bảng dữ liệu đồng bộ
                                                                <ul>
                                                                    <li><code> CSO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> CSO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> RBO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> RBO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> RM_SRM_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> RM_SRM_SALESPLAN_THANG </code></li>
                                                                    <li><code> SRBO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> SRBO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> SM_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> GDV_KSV_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                </ul>
@apiParam  (Body:)     {Number}       config_index.status       Trạng thái mapping
                                                                  <ul>
                                                                    <li><code>0</code>: OFF</li>
                                                                    <li><code>1</code>: ON (Mặc định)</li>
                                                                  </ul>                                                                                                                                                                            


@apiParamExample {json} Body example
{
    "name": "Doanh số giải ngân trung dài hạn",
    "code": "dsgn_tdh_1",
    "staff_title": "RBO",
    "apply": [
        {
            "type": 1,
            "start_time": "2024-06"    
        }
    ],
    "status": 1,
    "unit": "%",
    "config_index": [
        {
            "field_key": "dsgn_tdh_1",
            "type": "revenue",
            "data_type": "Float64",
            "table_name": ["RBO_BAO_CAO_TONG_HOP_THEO_NGAY"],
            "status": 1
        },
        {
            "field_key": "dsgn_tdh_1",
            "type": "sales_plan",
            "data_type": "Float64",
            "table_name": ["RBO_BAO_CAO_TONG_HOP_THEO_NGAY"],
            "status": 1
        }
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin chỉ tiêu
@apiSuccess (data:)      {String}   id                        ID chỉ tiêu
@apiSuccess  (data:)     {String}   name                      Tên chỉ tiêu
@apiSuccess  (data:)     {String}   code                      Mã chỉ tiêu
@apiSuccess  (data:)     {String}   staff_title               Áp dụng cho nhóm chức danh

@apiSuccess  (data:)     {Array}    apply                     Cấu hình thông tin áp dụng
@apiSuccess  (data:)     {Number}   apply.type                Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiSuccess  (data:)     {StringDate}   apply.start_time        Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m</code>. 
                                                              Mặc định: <code>current_month</code>)
@apiSuccess  (data:)     {StringDate}   [apply.end_time]      Thời gian kết thúc áp dụng (Format: <code>%Y-%m</code>)
@apiSuccess  (data:)     {String}       [apply.parent_code]   Mã chỉ tiêu cha

@apiSuccess  (data:)     {Object}       current_apply         Cấu hình thông tin áp dụng hiện tại
@apiSuccess  (data:)     {Number}       current_apply.type                Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiSuccess  (data:)     {StringDate}   current_apply.start_time        Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m</code>. 
                                                              Mặc định: <code>current_month</code>)
@apiSuccess  (data:)     {StringDate}   [current_apply.end_time]      Thời gian kết thúc áp dụng (Format: <code>%Y-%m</code>)
@apiSuccess  (data:)     {String}       [current_apply.parent_code]   Mã chỉ tiêu cha

@apiSuccess  (data:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiSuccess  (data:)     {String}       [unit]                  Đơn vị chỉ tiêu

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "id": "656d96ea11940c3d98df36d0",
        "name": "Doanh số giải ngân trung dài hạn",
        "code": "dsgn_tdh_1",
        "staff_title": "RBO",
        "apply": [
            {
                "type": 1,
                "start_time": "2024-06"    
            }
        ],
        "current_apply": {
            "type": 1,
            "start_time": "2024-06"    
        },
        "status": 1,
        "unit": "%",
        "created_by": "0315a587-3840-4e56-a15b-35103a31b532",
        "updated_by": null,
        "created_time": "2024-06-13T00:00:00.000Z",
        "updated_time": null,
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


# version: 1.0.0
"""
@api {POST} {domain}/kpi-management/api/v1.0/target  Thêm chỉ tiêu
@apiGroup Target Config
@apiVersion 1.0.0
@apiName TargetAdd

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   name                        Tên chỉ tiêu
@apiParam  (Body:)     {String}   [code]                      Mã chỉ tiêu
@apiParam  (Body:)     {String}   staff_title                 Áp dụng cho nhóm chức danh
@apiParam  (Body:)     {Number}   type                        Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiParam  (Body:)     {StringDate}   [start_time]            Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m-%d</code>. 
                                                              Mặc định: <code>current_date</code>)
@apiParam  (Body:)     {StringDate}   [end_time]              Thời gian kết thúc áp dụng (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiParam  (Body:)     {String}       [unit]                  Đơn vị chỉ tiêu
@apiParam  (Body:)     {String}       [parent_code]           Mã chỉ tiêu cha
@apiParam  (Body:)     {ListObject}   [config_index]            Mapping dữ liệu chỉ tiêu
@apiParam  (Body:)     {string}       config_index.field_key  Key trường thông tin đồng bộ
@apiParam  (Body:)     {string}       config_index.type       Chỉ số đồng bộ
                                                              <ul>
                                                                <li><code>sales_plan</code>: Sales plan (điểm chỉ tiêu giao)</li>
                                                                <li><code>sales_plan_exchange_revenue</code>: Doanh số chỉ tiêu giao</li>
                                                                <li><code>exchange</code>: Điểm thực hiện chỉ tiêu</li>
                                                                <li><code>revenue</code>: Doanh số thực hiện</li>
                                                                <li><code>completion_rate</code>: Tỷ lệ hoàn thành chỉ tiêu</li>
                                                                <li><code>comparison_period</code>: Giá trị kỳ so sánh</li>
                                                                <li><code>net_increase</code>: Tăng ròng</li>
                                                                <li><code>revenue_last_month</code>: Doanh số tháng trước</li>
                                                              </ul>
@apiParam  (Body:)     {string}       config_index.data_type  Kiểu dữ liệu 
                                                              <ul>
                                                                <li><code>Float64</code>: Số thực</li>
                                                              </ul>
@apiParam  (Body:)     {ArrayString}       config_index.table_name Bảng dữ liệu đồng bộ
                                                                <ul>
                                                                    <li><code> CSO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> CSO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> RBO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> RBO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> RM_SRM_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> RM_SRM_SALESPLAN_THANG </code></li>
                                                                    <li><code> SRBO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> SRBO_SALESPLAN_KPIs </code></li>
                                                                </ul>
@apiParam  (Body:)     {Number}       config_index.status       Trạng thái mapping
                                                                  <ul>
                                                                    <li><code>0</code>: OFF</li>
                                                                    <li><code>1</code>: ON (Mặc định)</li>
                                                                  </ul>                                                                                                                                                                            
                                                              

@apiParamExample {json} Body example
{
    "name": "Doanh số giải ngân trung dài hạn",
    "code": "dsgn_tdh_1",
    "staff_title": "RBO",
    "type": 1,
    "start_time": "2024-06-13",
    "status": 1,
    "unit": "%",
    "config_index": [
        {
            "field_key": "dsgn_tdh_1",
            "type": "revenue",
            "data_type": "Float64",
            "table_name": ["RBO_BAO_CAO_TONG_HOP_THEO_NGAY"],
            "status": 1
        },
        {
            "field_key": "dsgn_tdh_1",
            "type": "sales_plan",
            "data_type": "Float64",
            "table_name": ["RBO_BAO_CAO_TONG_HOP_THEO_NGAY"],
            "status": 1
        }
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin chỉ tiêu
@apiSuccess {String}            data.id                       ID chỉ tiêu

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "id": "6670ffcb5076032d8d44df52"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

####################################################################################################
# Cập nhật chỉ tiêu
# version: 1.0.1                                                                                   #
# version: 1.0.0                                                                                   #
####################################################################################################

# version: 1.0.1
"""
@api {PUT} {domain}/kpi-management/api/v1.0/target/<target_id>/update  Cập nhật chỉ tiêu
@apiGroup Target Config
@apiVersion 1.0.1
@apiName TargetUpdate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [name]                      Tên chỉ tiêu
@apiParam  (Body:)     {String}   [code]                      Mã chỉ tiêu
@apiParam  (Body:)     {String}   [staff_title]               Áp dụng cho nhóm chức danh
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRM</code></li>
                                                                <li><code>CSO</code></li>
                                                                <li><code>SM</code></li>
                                                                <li><code>GDV</code></li>
                                                                <li><code>KSV</code></li>
                                                                <li><code>RM0</code></li>
                                                              </ul>
@apiParam  (Body:)     {Array}    apply                       Cấu hình thông tin áp dụng
@apiParam  (Body:)     {Number}   apply.type                  Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiParam  (Body:)     {StringDate}   apply.start_time        Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m</code>. 
                                                              Mặc định: <code>current_month</code>)
@apiParam  (Body:)     {StringDate}   [apply.end_time]              Thời gian kết thúc áp dụng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}       [apply.parent_code]           Mã chỉ tiêu cha

@apiParam  (Body:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiParam  (Body:)     {String}       [unit]                  Đơn vị chỉ tiêu
                                                              
@apiParam  (Body:)     {ListObject}   [config_index]            Mapping dữ liệu chỉ tiêu
@apiParam  (Body:)     {string}       [config_index.field_key]  Key trường thông tin đồng bộ
@apiParam  (Body:)     {string}       config_index.type       Chỉ số đồng bộ
                                                              <ul>
                                                                <li><code>sales_plan</code>: Sales plan (điểm chỉ tiêu giao)</li>
                                                                <li><code>sales_plan_exchange_revenue</code>: Doanh số chỉ tiêu giao</li>
                                                                <li><code>exchange</code>: Điểm thực hiện chỉ tiêu</li>
                                                                <li><code>revenue</code>: Doanh số thực hiện</li>
                                                                <li><code>completion_rate</code>: Tỷ lệ hoàn thành chỉ tiêu</li>
                                                                <li><code>comparison_period</code>: Giá trị kỳ so sánh</li>
                                                                <li><code>net_increase</code>: Tăng ròng</li>
                                                                <li><code>revenue_last_month</code>: Doanh số tháng trước</li>
                                                              </ul>
@apiParam  (Body:)     {string}       config_index.data_type  Kiểu dữ liệu 
                                                              <ul>
                                                                <li><code>Float64</code>: Số thực</li>
                                                              </ul>
@apiParam  (Body:)     {ArrayString}  [config_index.table_name] Bảng dữ liệu đồng bộ
                                                                <ul>
                                                                    <li><code> CSO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> CSO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> RBO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> RBO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> RM_SRM_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> RM_SRM_SALESPLAN_THANG </code></li>
                                                                    <li><code> SRBO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> SRBO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> SM_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> GDV_KSV_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                </ul>
@apiParam  (Body:)     {Number}       config_index.status       Trạng thái mapping
                                                                  <ul>
                                                                    <li><code>0</code>: OFF</li>
                                                                    <li><code>1</code>: ON (Mặc định)</li>
                                                                  </ul>                                                                                                                                                                            


@apiParamExample {json} Body example
{
    "name": "Doanh số giải ngân trung dài hạn",
    "code": "dsgn_tdh_1",
    "staff_title": "RBO",
    "apply": [
        {
            "type": 1,
            "start_time": "2024-06"    
        }
    ],
    "status": 1,
    "unit": "%",
    "config_index": [
        {
            "field_key": "dsgn_tdh_1",
            "type": "revenue",
            "data_type": "Float64",
            "table_name": ["RBO_BAO_CAO_TONG_HOP_THEO_NGAY"],
            "status": 1
        },
        {
            "field_key": "dsgn_tdh_1",
            "type": "sales_plan",
            "data_type": "Float64",
            "table_name": ["RBO_BAO_CAO_TONG_HOP_THEO_NGAY"],
            "status": 1
        }
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess (data:)      {String}   id                        ID chỉ tiêu
@apiSuccess  (data:)     {String}   name                      Tên chỉ tiêu
@apiSuccess  (data:)     {String}   code                      Mã chỉ tiêu
@apiSuccess  (data:)     {String}   staff_title               Áp dụng cho nhóm chức danh

@apiSuccess  (data:)     {Array}    apply                     Cấu hình thông tin áp dụng
@apiSuccess  (data:)     {Number}   apply.type                Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiSuccess  (data:)     {StringDate}   apply.start_time        Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m</code>. 
                                                              Mặc định: <code>current_month</code>)
@apiSuccess  (data:)     {StringDate}   [apply.end_time]      Thời gian kết thúc áp dụng (Format: <code>%Y-%m</code>)
@apiSuccess  (data:)     {String}       [apply.parent_code]   Mã chỉ tiêu cha

@apiSuccess  (data:)     {Object}       current_apply         Cấu hình thông tin áp dụng hiện tại
@apiSuccess  (data:)     {Number}       current_apply.type                Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiSuccess  (data:)     {StringDate}   current_apply.start_time        Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m</code>. 
                                                              Mặc định: <code>current_month</code>)
@apiSuccess  (data:)     {StringDate}   [current_apply.end_time]      Thời gian kết thúc áp dụng (Format: <code>%Y-%m</code>)
@apiSuccess  (data:)     {String}       [current_apply.parent_code]   Mã chỉ tiêu cha

@apiSuccess  (data:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiSuccess  (data:)     {String}       [unit]                  Đơn vị chỉ tiêu

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "656d96ea11940c3d98df36d0",
        "name": "Doanh số giải ngân trung dài hạn",
        "code": "dsgn_tdh_1",
        "staff_title": "RBO",
        "apply": [
            {
                "type": 1,
                "start_time": "2024-06"    
            }
        ],
        "current_apply": {
            "type": 1,
            "start_time": "2024-06"    
        },
        "status": 1,
        "unit": "%",
        "created_by": "0315a587-3840-4e56-a15b-35103a31b532",
        "updated_by": "0315a587-3840-4e56-a15b-35103a31b532",
        "created_time": "2024-06-13T00:00:00.000Z",
        "updated_time": "2024-06-13T00:00:00.000Z",
    }
}
"""

# version: 1.0.0
"""
@api {PUT} {domain}/kpi-management/api/v1.0/target/<target_id>/update  Cập nhật chỉ tiêu
@apiGroup Target Config
@apiVersion 1.0.0
@apiName TargetUpdate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [name]                      Tên chỉ tiêu
@apiParam  (Body:)     {String}   [code]                      Mã chỉ tiêu
@apiParam  (Body:)     {String}   [staff_title]               Áp dụng cho nhóm chức danh
@apiParam  (Body:)     {Number}   [type]                        Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiParam  (Body:)     {StringDate}   [start_time]            Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m-%d</code>. 
                                                              Mặc định: <code>current_date</code>)
@apiParam  (Body:)     {StringDate}   [end_time]              Thời gian kết thúc áp dụng (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiParam  (Body:)     {String}       [unit]                  Đơn vị chỉ tiêu
@apiParam  (Body:)     {String}       [parent_code]           Mã chỉ tiêu cha                                                              
@apiParam  (Body:)     {ListObject}   [config_index]            Mapping dữ liệu chỉ tiêu
@apiParam  (Body:)     {string}       [config_index.field_key]  Key trường thông tin đồng bộ
@apiParam  (Body:)     {string}       config_index.type       Chỉ số đồng bộ
                                                              <ul>
                                                                <li><code>sales_plan</code>: Sales plan (điểm chỉ tiêu giao)</li>
                                                                <li><code>sales_plan_exchange_revenue</code>: Doanh số chỉ tiêu giao</li>
                                                                <li><code>exchange</code>: Điểm thực hiện chỉ tiêu</li>
                                                                <li><code>revenue</code>: Doanh số thực hiện</li>
                                                                <li><code>completion_rate</code>: Tỷ lệ hoàn thành chỉ tiêu</li>
                                                                <li><code>comparison_period</code>: Giá trị kỳ so sánh</li>
                                                                <li><code>net_increase</code>: Tăng ròng</li>
                                                                <li><code>revenue_last_month</code>: Doanh số tháng trước</li>
                                                              </ul>
@apiParam  (Body:)     {string}       config_index.data_type  Kiểu dữ liệu 
                                                              <ul>
                                                                <li><code>Float64</code>: Số thực</li>
                                                              </ul>
@apiParam  (Body:)     {ArrayString}  [config_index.table_name] Bảng dữ liệu đồng bộ
                                                                <ul>
                                                                    <li><code> CSO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> CSO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> RBO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> RBO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> RM_SRM_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> RM_SRM_SALESPLAN_THANG </code></li>
                                                                    <li><code> SRBO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> SRBO_SALESPLAN_KPIs </code></li>
                                                                </ul>
@apiParam  (Body:)     {Number}       config_index.status       Trạng thái mapping
                                                                  <ul>
                                                                    <li><code>0</code>: OFF</li>
                                                                    <li><code>1</code>: ON (Mặc định)</li>
                                                                  </ul>                                                                                                                                                                            


@apiParamExample {json} Body example
{
    "name": "Doanh số giải ngân trung dài hạn",
    "code": "dsgn_tdh_1",
    "staff_title": "RBO",
    "type": 1,
    "start_time": "2024-06-13",
    "status": 1,
    "unit": "%",
    "config_index": [
        {
            "field_key": "dsgn_tdh_1",
            "type": "revenue",
            "data_type": "Float64",
            "table_name": ["RBO_BAO_CAO_TONG_HOP_THEO_NGAY"],
            "status": 1
        },
        {
            "field_key": "dsgn_tdh_1",
            "type": "sales_plan",
            "data_type": "Float64",
            "table_name": ["RBO_BAO_CAO_TONG_HOP_THEO_NGAY"],
            "status": 1
        }
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# On / OFF chỉ tiêu
# version: 1.0.0                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/kpi-management/api/v1.0/target/action/on-off  Bật tắt chỉ tiêu
@apiGroup Target Config
@apiVersion 1.0.0
@apiName TargetActionOnOff

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {ArrayString}   target_ids             Danh sách ID chỉ tiêu                                                     
@apiParam  (Body:)     {Number}        status                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>

@apiParamExample {json} Body example
{
    "target_ids": ["656d96ea11940c3d98df36d0"]
    "status": 1
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# Danh sách chỉ tiêu
# version: 1.0.1                                                                                   #
# version: 1.0.0                                                                                   #
####################################################################################################

# version: 1.0.1
"""
@api {POST} {domain}/kpi-management/api/v1.0/target/list     Danh sách chỉ tiêu
@apiGroup Target Config
@apiVersion 1.0.1
@apiName TargetList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam  (Query:)    {String}       [order_by]              Sắp xếp kết quả theo trường thông tin
                                                              <ul>
                                                                <li><code>name</code> : Tên chỉ tiêu</li>
                                                                <li><code>created_time</code> : Thời gian tạo</li>
                                                                <li><code>updated_time</code> : Thời gian cập nhật</li>
                                                              </uL>
@apiParam  (Query:)    {String}       [order_type]            Kiểu sắp xếp
                                                              <ul>
                                                                <li><code>asc</code>: Sắp xếp tăng dần</li>
                                                                <li><code>desc</code>: Sắp xếp giảm dần</li>
                                                              </ul>    


@apiParam  (Body:)     {String}       [staff_title]           Nhóm chức danh                                                           
@apiParam  (Body:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiParam  (Body:)     {String}       [search_text]           Tìm theo tên chỉ tiêu                                                              
@apiParam  (Body:)     {ArrayNumber}  [type]                  Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiParam  (Body:)     {ArrayNumber}  [current_type]          Loại chỉ tiêu hiện tại
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>                                                              
@apiParam  (Body:)     {StringDate}  [created_time_start]    Thời gian tạo từ  (Format: <code>%Y-%m-%d</code>)                                                             
@apiParam  (Body:)     {StringDate}  [created_time_end]      Thời gian tạo đến (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {StringDate}  [updated_time_start]    Thời gian cập nhật từ  (Format: <code>%Y-%m-%d</code>)                                                             
@apiParam  (Body:)     {StringDate}  [updated_time_end]      Thời gian cập nhật đến (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {StringDate}  [apply_time_start]      Thời gian áp dụng từ (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {StringDate}  [apply_time_end]        Thời gian áp dụng đến (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {String}      [created_by]            ID người tạo
@apiParam  (Body:)     {String}      [updated_by]            ID người cập nhật                                                                                   
@apiParam  (Body:)     {ArrayString}      [parent_code]           Mã chỉ tiêu cha (Trường hợp parent_code = null là chỉ tiêu cha)                                                                                   
@apiParam  (Body:)     {ArrayString}      [code]                  Mã chỉ tiêu                                                                                   


@apiParamExample {json} Body example
{
    "staff_title": "RBO",
    "status": 1
}


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {ArrayObject}       data                          Danh sách chỉ tiêu

@apiSuccess  (data:)     {String}   id                        ID chỉ tiêu
@apiSuccess  (data:)     {String}   name                        Tên chỉ tiêu
@apiSuccess  (data:)     {String}   code                      Mã chỉ tiêu
@apiSuccess  (data:)     {String}   staff_title                Áp dụng cho nhóm chức danh

@apiSuccess  (data:)     {Array}    apply                     Cấu hình thông tin áp dụng
@apiSuccess  (data:)     {Number}   apply.type                Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiSuccess  (data:)     {StringDate}   apply.start_time        Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m</code>. 
                                                              Mặc định: <code>current_month</code>)
@apiSuccess  (data:)     {StringDate}   [apply.end_time]      Thời gian kết thúc áp dụng (Format: <code>%Y-%m</code>)
@apiSuccess  (data:)     {String}       [apply.parent_code]   Mã chỉ tiêu cha

@apiSuccess  (data:)     {Object}       current_apply         Cấu hình thông tin áp dụng hiện tại
@apiSuccess  (data:)     {Number}       current_apply.type                Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiSuccess  (data:)     {StringDate}   current_apply.start_time        Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m</code>. 
                                                              Mặc định: <code>current_month</code>)
@apiSuccess  (data:)     {StringDate}   [current_apply.end_time]      Thời gian kết thúc áp dụng (Format: <code>%Y-%m</code>)
@apiSuccess  (data:)     {String}       [current_apply.parent_code]   Mã chỉ tiêu cha                                                              
                                                              
@apiSuccess  (data:)     {Number}       [status]               Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiSuccess  (data:)     {String}       [unit]                  Đơn vị chỉ tiêu
                                                              
@apiSuccess  (data:)     {string}       [created_by]               ID người tạo                                                                              
@apiSuccess  (data:)     {string}       [updated_by]               ID người cập nhật                                                                              
@apiSuccess  (data:)     {stringDatetime}   created_time         Thời gian tạo (Format: %Y-%m-%dT%H:%M:%S.%fZ)                                                                              
@apiSuccess  (data:)     {stringDatetime}   [updated_time]         Thời gian cập nhật (Format: %Y-%m-%dT%H:%M:%S.%fZ)                                                                              
@apiSuccess  (data:)     {Boolean}   [is_index_target]         Chỉ tiêu có cấu hình mapping field hay không (True: có, False: Không)                                                                      



@apiSuccessExample {json} Response Example
{
    "data": [
        {
            "id": "656d96ea11940c3d98df36d0",
            "name": "Doanh số giải ngân trung dài hạn",
            "code": "dsgn_tdh_1",
            "staff_title": "RBO",
            "apply": [
                {
                    "type": 1,
                    "start_time": "2024-06"    
                }
            ],
            "current_apply": {
                "type": 1,
                "start_time": "2024-06"    
            },
            "status": 1,
            "unit": "%",
            "created_by": "0315a587-3840-4e56-a15b-35103a31b532",
            "updated_by": "0315a587-3840-4e56-a15b-35103a31b532",
            "created_time": "2024-06-13T00:00:00.000Z",
            "updated_time": "2024-06-13T00:00:00.000Z",
            "is_index_target": true
        }
    ]
    "code": 200,
    "message": "request thành công."
}
"""

# version: 1.0.0
"""
@api {POST} {domain}/kpi-management/api/v1.0/target/list     Danh sách chỉ tiêu
@apiGroup Target Config
@apiVersion 1.0.0
@apiName TargetList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam  (Query:)    {String}       [order_by]              Sắp xếp kết quả theo trường thông tin
                                                              <ul>
                                                                <li><code>name</code> : Tên chỉ tiêu</li>
                                                                <li><code>created_time</code> : Thời gian tạo</li>
                                                                <li><code>updated_time</code> : Thời gian cập nhật</li>
                                                              </uL>
@apiParam  (Query:)    {String}       [order_type]            Kiểu sắp xếp
                                                              <ul>
                                                                <li><code>asc</code>: Sắp xếp tăng dần</li>
                                                                <li><code>desc</code>: Sắp xếp giảm dần</li>
                                                              </ul>    


@apiParam  (Body:)     {String}       [staff_title]           Nhóm chức danh                                                           
@apiParam  (Body:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiParam  (Body:)     {String}       [search_text]           Tìm theo tên chỉ tiêu                                                              
@apiParam  (Body:)     {ArrayNumber}  [type]                  Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiParam  (Body:)     {StringDate}  [created_time_start]    Thời gian tạo từ  (Format: <code>%Y-%m-%d</code>)                                                             
@apiParam  (Body:)     {StringDate}  [created_time_end]      Thời gian tạo đến (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {StringDate}  [updated_time_start]    Thời gian cập nhật từ  (Format: <code>%Y-%m-%d</code>)                                                             
@apiParam  (Body:)     {StringDate}  [updated_time_end]      Thời gian cập nhật đến (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {StringDate}  [apply_time_start]      Thời gian áp dụng từ (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {StringDate}  [apply_time_end]        Thời gian áp dụng đến (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {String}      [created_by]            ID người tạo
@apiParam  (Body:)     {String}      [updated_by]            ID người cập nhật                                                                                   
@apiParam  (Body:)     {ArrayString}      [parent_code]           Mã chỉ tiêu cha (Trường hợp parent_code = null là chỉ tiêu cha)                                                                                   
@apiParam  (Body:)     {ArrayString}      [code]                  Mã chỉ tiêu                                                                                   
                                                                                                                            

@apiParamExample {json} Body example
{
    "staff_title": "RBO",
    "status": 1
}


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {ArrayObject}       data                          Danh sách chỉ tiêu

@apiSuccess  (data:)     {String}   id                        ID chỉ tiêu
@apiSuccess  (data:)     {String}   name                        Tên chỉ tiêu
@apiSuccess  (data:)     {String}   code                      Mã chỉ tiêu
@apiSuccess  (data:)     {String}   staff_title                Áp dụng cho nhóm chức danh
@apiSuccess  (data:)     {Number}   type                        Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiSuccess  (data:)     {StringDatetime}   [start_time]            Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m-%dT%H:%M:%S.%fZ</code>. 
                                                              Mặc định: <code>current_date</code>)
@apiSuccess  (data:)     {StringDatetime}   [end_time]              Thời gian kết thúc áp dụng (Format: <code>%Y-%m-%dT%H:%M:%S.%fZ</code>)                                                              
@apiSuccess  (data:)     {Number}       [status]               Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiSuccess  (data:)     {String}       [unit]                  Đơn vị chỉ tiêu
@apiSuccess  (data:)     {String}       [parent_code]           Mã chỉ tiêu cha                                                              

@apiSuccess  (data:)     {string}       [created_by]               ID người tạo                                                                              
@apiSuccess  (data:)     {string}       [updated_by]               ID người cập nhật                                                                              
@apiSuccess  (data:)     {stringDatetime}   created_time         Thời gian tạo (Format: %Y-%m-%dT%H:%M:%S.%fZ)                                                                              
@apiSuccess  (data:)     {stringDatetime}   [updated_time]         Thời gian cập nhật (Format: %Y-%m-%dT%H:%M:%S.%fZ)                                                                              
                                                                    


@apiSuccessExample {json} Response Example
{
    "data": [
        {
            "id": "656d96ea11940c3d98df36d0",
            "name": "Doanh số giải ngân trung dài hạn",
            "code": "dsgn_tdh_1",
            "staff_title": "RBO",
            "type": 1,
            "start_time": "2024-06-13T00:00:00.000Z",
            "status": 1,
            "unit": "%",
            "created_by": "0315a587-3840-4e56-a15b-35103a31b532",
            "updated_by": "0315a587-3840-4e56-a15b-35103a31b532",
            "created_time": "2024-06-13T00:00:00.000Z",
            "updated_time": "2024-06-13T00:00:00.000Z",
        }
    ]
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# Số lượng chỉ tiêu theo trạng thái
# version: 1.0.0                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/kpi-management/api/v1.0/target/statistic/status     Số lượng chỉ tiêu theo trạng thái
@apiGroup Target Config
@apiVersion 1.0.0
@apiName TargetStatisticStatus

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam  (Body:)     {String}       [staff_title]           Nhóm chức danh                                                           
@apiParam  (Body:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiParam  (Body:)     {String}       [search_text]           Tìm theo tên chỉ tiêu                                                              
@apiParam  (Body:)     {ArrayNumber}  [type]                  Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiParam  (Body:)     {ArrayNumber}  [current_type]          Loại chỉ tiêu hiện tại
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>                                                                
@apiParam  (Body:)     {StringDate}  [created_time_start]    Thời gian tạo từ  (Format: <code>%Y-%m-%d</code>)                                                             
@apiParam  (Body:)     {StringDate}  [created_time_end]      Thời gian tạo đến (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {StringDate}  [updated_time_start]    Thời gian cập nhật từ  (Format: <code>%Y-%m-%d</code>)                                                             
@apiParam  (Body:)     {StringDate}  [updated_time_end]      Thời gian cập nhật đến (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {StringDate}  [apply_time_start]      Thời gian áp dụng từ (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {StringDate}  [apply_time_end]        Thời gian áp dụng đến (Format: <code>%Y-%m-%d</code>)                                                                       
@apiParam  (Body:)     {String}      [created_by]            ID người tạo
@apiParam  (Body:)     {String}      [updated_by]            ID người cập nhật
@apiParam  (Body:)     {ArrayString}      [parent_code]           Mã chỉ tiêu cha (Trường hợp parent_code = null là chỉ tiêu cha)                                                                                   
@apiParam  (Body:)     {ArrayString}      [code]                  Mã chỉ tiêu                                                                                      


@apiParamExample {json} Body example
{
    "staff_title": "RBO",
    "status": 1
}


@apiSuccess {String}                message                       Mô tả phản hồi
@apiSuccess {Integer}               code                          Mã phản hồi
@apiSuccess {Object}                 data                          Thông tin chỉ tiêu

@apiSuccess  (data:)     {Number}   total                         Tổng số chỉ tiêu
@apiSuccess  (data:)     {Number}   active                        Số chỉ tiêu đang hoạt động
@apiSuccess  (data:)     {Number}   inactive                      Số chỉ tiêu ngừng hoạt động




@apiSuccessExample {json} Response Example
{
    "data": {
        "total": 10,
        "active": 4,
        "inactive": 6,
    },
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# Chi tiết chỉ tiêu
# version: 1.0.1                                                                                   #
# version: 1.0.0                                                                                   #
####################################################################################################

# version: 1.0.1
"""
@api {GET} {domain}/kpi-management/api/v1.0/target/<target_id>/detail     Chi tiết chỉ tiêu
@apiGroup Target Config
@apiVersion 1.0.1
@apiName TargetDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}       data                               Thông tin chỉ tiêu

@apiSuccess  (data:)     {String}   id                        ID chỉ tiêu
@apiSuccess  (data:)     {String}   name                       Tên chỉ tiêu
@apiSuccess  (data:)     {String}   code                      Mã chỉ tiêu
@apiSuccess  (data:)     {String}   staff_title                 Áp dụng cho nhóm chức danh

@apiSuccess  (data:)     {Array}    apply                       Cấu hình thông tin áp dụng
@apiSuccess  (data:)     {Number}   apply.type                  Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiSuccess  (data:)     {StringDate}   apply.start_time        Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m</code>. 
                                                              Mặc định: <code>current_month</code>)
@apiSuccess  (data:)     {StringDate}   [apply.end_time]      Thời gian kết thúc áp dụng (Format: <code>%Y-%m</code>)
@apiSuccess  (data:)     {String}       [apply.parent_code]   Mã chỉ tiêu cha

@apiSuccess  (data:)     {Object}       current_apply         Cấu hình thông tin áp dụng hiện tại
@apiSuccess  (data:)     {Number}       current_apply.type                Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiSuccess  (data:)     {StringDate}   current_apply.start_time        Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m</code>. 
                                                              Mặc định: <code>current_month</code>)
@apiSuccess  (data:)     {StringDate}   [current_apply.end_time]      Thời gian kết thúc áp dụng (Format: <code>%Y-%m</code>)
@apiSuccess  (data:)     {String}       [current_apply.parent_code]   Mã chỉ tiêu cha

@apiSuccess  (data:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiSuccess  (data:)     {String}       [unit]                  Đơn vị chỉ tiêu
                                                              
@apiSuccess  (data:)     {ListObject}   config_index            Mapping dữ liệu chỉ tiêu
@apiSuccess  (data:)     {string}       [config_index.field_key]  Key trường thông tin đồng bộ
@apiSuccess  (data:)     {string}       config_index.type       Chỉ số đồng bộ
                                                              <ul>
                                                                <li><code>sales_plan</code>: Sales plan (điểm chỉ tiêu giao)</li>
                                                                <li><code>sales_plan_exchange_revenue</code>: Doanh số chỉ tiêu giao</li>
                                                                <li><code>exchange</code>: Điểm thực hiện chỉ tiêu</li>
                                                                <li><code>revenue</code>: Doanh số thực hiện</li>
                                                                <li><code>completion_rate</code>: Tỷ lệ hoàn thành chỉ tiêu</li>
                                                                <li><code>comparison_period</code>: Giá trị kỳ so sánh</li>
                                                                <li><code>net_increase</code>: Tăng ròng</li>
                                                                <li><code>revenue_last_month</code>: Doanh số tháng trước</li>
                                                              </ul>
@apiSuccess  (data:)     {string}       config_index.data_type  Kiểu dữ liệu 
                                                              <ul>
                                                                <li><code>Float64</code>: Số thực</li>
                                                              </ul>
@apiSuccess  (data:)     {ArrayString}       config_index.table_name Bảng dữ liệu đồng bộ
                                                                <ul>
                                                                    <li><code> CSO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> CSO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> RBO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> RBO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> RM_SRM_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> RM_SRM_SALESPLAN_THANG </code></li>
                                                                    <li><code> SRBO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> SRBO_SALESPLAN_KPIs </code></li>
                                                                </ul>
@apiSuccess  (data:)     {Number}       config_index.status       Trạng thái mapping
                                                                  <ul>
                                                                    <li><code>0</code>: OFF</li>
                                                                    <li><code>1</code>: ON (Mặc định)</li>
                                                                  </ul> 
@apiSuccess  (data:)     {string}       [created_by]               ID người tạo                                                                              
@apiSuccess  (data:)     {string}       [updated_by]               ID người cập nhật                                                                              
@apiSuccess  (data:)     {stringDatetime}   created_time         Thời gian tạo (Format: %Y-%m-%dT%H:%M:%S.%fZ)                                                                              
@apiSuccess  (data:)     {stringDatetime}   [updated_time]         Thời gian cập nhật (Format: %Y-%m-%dT%H:%M:%S.%fZ)                                                                              



@apiSuccessExample {json} Response Example
{
    "data": {
            "id": "656d96ea11940c3d98df36d0",
            "name": "Doanh số giải ngân trung dài hạn",
            "code": "dsgn_tdh_1",
            "staff_title": "RBO",
            "apply": [
                {
                    "type": 1,
                    "start_time": "2024-06"    
                }
            ],
            "current_apply": {
                "type": 1,
                "start_time": "2024-06"    
            },
            "status": 1,
            "unit": "%",
            "config_index": [
                {
                    "field_key": "dsgn_tdh_1",
                    "type": "revenue",
                    "data_type": "Float64",
                    "table_name": ["RBO_BAO_CAO_TONG_HOP_THEO_NGAY"],
                    "status": 1
                },
                {
                    "field_key": "dsgn_tdh_1",
                    "type": "sales_plan",
                    "data_type": "Float64",
                    "table_name": ["RBO_BAO_CAO_TONG_HOP_THEO_NGAY"],
                    "status": 1
                }
            ],
            "created_by": "0315a587-3840-4e56-a15b-35103a31b532",
            "updated_by": "0315a587-3840-4e56-a15b-35103a31b532",
            "created_time": "2024-06-13T00:00:00.000Z",
            "updated_time": "2024-06-13T00:00:00.000Z",
    },
    "code": 200,
    "message": "request thành công."
}
"""

# version: 1.0.0
"""
@api {GET} {domain}/kpi-management/api/v1.0/target/<target_id>/detail     Chi tiết chỉ tiêu
@apiGroup Target Config
@apiVersion 1.0.0
@apiName TargetDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}       data                               Thông tin chỉ tiêu

@apiSuccess  (data:)     {String}   id                        ID chỉ tiêu
@apiSuccess  (data:)     {String}   name                       Tên chỉ tiêu
@apiSuccess  (data:)     {String}   code                      Mã chỉ tiêu
@apiSuccess  (data:)     {String}   staff_title                 Áp dụng cho nhóm chức danh
@apiSuccess  (data:)     {Number}   type                        Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiSuccess  (data:)     {StringDatetime}   [start_time]            Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m-%dT%H:%M:%S.%fZ</code>. 
                                                              Mặc định: <code>current_date</code>)
@apiSuccess  (data:)     {StringDatetime}   [end_time]              Thời gian kết thúc áp dụng (Format: <code>%Y-%m-%dT%H:%M:%S.%fZ</code>)                                                              
@apiSuccess  (data:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiSuccess  (data:)     {String}       [unit]                  Đơn vị chỉ tiêu
@apiSuccess  (data:)     {String}       [parent_code]           Mã chỉ tiêu cha                                                              
@apiSuccess  (data:)     {ListObject}   config_index            Mapping dữ liệu chỉ tiêu
@apiSuccess  (data:)     {string}       [config_index.field_key]  Key trường thông tin đồng bộ
@apiSuccess  (data:)     {string}       config_index.type       Chỉ số đồng bộ
                                                              <ul>
                                                                <li><code>sales_plan</code>: Sales plan (điểm chỉ tiêu giao)</li>
                                                                <li><code>sales_plan_exchange_revenue</code>: Doanh số chỉ tiêu giao</li>
                                                                <li><code>exchange</code>: Điểm thực hiện chỉ tiêu</li>
                                                                <li><code>revenue</code>: Doanh số thực hiện</li>
                                                                <li><code>completion_rate</code>: Tỷ lệ hoàn thành chỉ tiêu</li>
                                                                <li><code>comparison_period</code>: Giá trị kỳ so sánh</li>
                                                                <li><code>net_increase</code>: Tăng ròng</li>
                                                                <li><code>revenue_last_month</code>: Doanh số tháng trước</li>
                                                              </ul>
@apiSuccess  (data:)     {string}       config_index.data_type  Kiểu dữ liệu 
                                                              <ul>
                                                                <li><code>Float64</code>: Số thực</li>
                                                              </ul>
@apiSuccess  (data:)     {ArrayString}       config_index.table_name Bảng dữ liệu đồng bộ
                                                                <ul>
                                                                    <li><code> CSO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> CSO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> RBO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> RBO_SALESPLAN_KPIs </code></li>
                                                                    <li><code> RM_SRM_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> RM_SRM_SALESPLAN_THANG </code></li>
                                                                    <li><code> SRBO_BAO_CAO_TONG_HOP_THEO_NGAY </code></li>
                                                                    <li><code> SRBO_SALESPLAN_KPIs </code></li>
                                                                </ul>
@apiSuccess  (data:)     {Number}       config_index.status       Trạng thái mapping
                                                                  <ul>
                                                                    <li><code>0</code>: OFF</li>
                                                                    <li><code>1</code>: ON (Mặc định)</li>
                                                                  </ul> 
@apiSuccess  (data:)     {string}       [created_by]               ID người tạo                                                                              
@apiSuccess  (data:)     {string}       [updated_by]               ID người cập nhật                                                                              
@apiSuccess  (data:)     {stringDatetime}   created_time         Thời gian tạo (Format: %Y-%m-%dT%H:%M:%S.%fZ)                                                                              
@apiSuccess  (data:)     {stringDatetime}   [updated_time]         Thời gian cập nhật (Format: %Y-%m-%dT%H:%M:%S.%fZ)                                                                              



@apiSuccessExample {json} Response Example
{
    "data": {
            "id": "656d96ea11940c3d98df36d0",
            "name": "Doanh số giải ngân trung dài hạn",
            "code": "dsgn_tdh_1",
            "staff_title": "RBO",
            "type": 1,
            "start_time": "2024-06-13T00:00:00.000Z",
            "status": 1,
            "unit": "%",
            "config_index": [
                {
                    "field_key": "dsgn_tdh_1",
                    "type": "revenue",
                    "data_type": "Float64",
                    "table_name": ["RBO_BAO_CAO_TONG_HOP_THEO_NGAY"],
                    "status": 1
                },
                {
                    "field_key": "dsgn_tdh_1",
                    "type": "sales_plan",
                    "data_type": "Float64",
                    "table_name": ["RBO_BAO_CAO_TONG_HOP_THEO_NGAY"],
                    "status": 1
                }
            ],
            "created_by": "0315a587-3840-4e56-a15b-35103a31b532",
            "updated_by": "0315a587-3840-4e56-a15b-35103a31b532",
            "created_time": "2024-06-13T00:00:00.000Z",
            "updated_time": "2024-06-13T00:00:00.000Z",
    },
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# Số lượng chỉ tiêu theo chức danh
# version: 1.0.0                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/kpi-management/api/v1.0/target/statistic  Số lượng chỉ tiêu theo chức danh
@apiGroup Target Config
@apiVersion 1.0.0
@apiName TargetStatistic

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccess {String}                message                       Mô tả phản hồi
@apiSuccess {Integer}               code                          Mã phản hồi
@apiSuccess {Array}                data                           Thông tin chỉ tiêu

@apiSuccess  (data:)     {String}   staff_title                   Chức danh
@apiSuccess  (data:)     {Number}   total                         Tổng số chỉ tiêu
@apiSuccess  (data:)     {Number}   active                        Số chỉ tiêu đang hoạt động
@apiSuccess  (data:)     {Number}   inactive                      Số chỉ tiêu ngừng hoạt động

@apiSuccessExample {json} Response Example
{
    "data": [
        {
            "staff_title": "RBO",
            "total": 10,
            "active": 4,
            "inactive": 6,
        }
    ]
    "code": 200,
    "message": "request thành công."
}

"""

####################################################################################################
# Danh sách bảng mapping chỉ tiêu
# version: 1.0.0                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/kpi-management/api/v1.0/mapping-table  Danh sách bảng mapping chỉ tiêu
@apiGroup Target Config
@apiVersion 1.0.0
@apiName TargetMappingTable

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Query:)     {String}       [staff_title]           Nhóm chức danh

@apiSuccess {String}                message                       Mô tả phản hồi
@apiSuccess {Integer}               code                          Mã phản hồi
@apiSuccess {Array}                 data                          Thông tin chỉ tiêu

@apiSuccess {ArrayString}           data.mapping_table            Danh sách bảng mapping chỉ tiêu

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "mapping_table": [
            "CSO_BAO_CAO_TONG_HOP_THEO_NGAY",
            "CSO_SALESPLAN_KPIs",
            "RBO_BAO_CAO_TONG_HOP_THEO_NGAY",
            "RBO_SALESPLAN_KPIs",
            "RM_SRM_BAO_CAO_TONG_HOP_THEO_NGAY",
            "RM_SRM_SALESPLAN_THANG",
            "SRBO_BAO_CAO_TONG_HOP_THEO_NGAY",
            "SRBO_SALESPLAN_KPIs"
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}

"""


####################################################################################################
# Xóa chỉ tiêu
# version: 1.0.0                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/kpi-management/api/v1.0/target/delete  Xóa danh sách chỉ tiêu
@apiGroup Target Config
@apiVersion 1.0.0
@apiName TargetDelete

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (BODY:)     {Array}      target_id                    Danh sách ID chỉ tiêu

@apiParamExample {json} Body example
{
    "target_id": [
        "67035872135639d1e74b8883"
    ]
}

@apiSuccess {String}                message                       Mô tả phản hồi
@apiSuccess {Integer}               code                          Mã phản hồi
@apiSuccess {Object}                data                          Thông tin thực hiện
@apiSuccess {Array}                 deleted_ids                   Danh sách ID chỉ tiêu được xóa thành công


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "deleted_ids": [
            "67035872135639d1e74b8883"
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}

"""

# version: 1.0.0
"""
@api {POST} {domain}/kpi-management/api/v1.0/target/list-by-codes     Danh sách chỉ tiêu theo danh sách mã chỉ tiêu
@apiGroup Target Config
@apiVersion 1.0.0
@apiName TargetListByCodes

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam  (Body:)     {String}         staff_title           Chức danh                                                          
@apiParam  (Body:)     {ArrayString}    target_codes          Danh sách mã chỉ tiêu                                                                     


@apiParamExample {json} Body example
{
    "target_codes": ["ttqt", "du_no_khdnl_01"],
    "staff_title": "RM"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {ArrayObject}       data                          Danh sách chỉ tiêu

@apiSuccess  (data:)     {String}   name                      Tên chỉ tiêu
@apiSuccess  (data:)     {String}   code                      Mã chỉ tiêu


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "code": "du_no_khdnl_01",
            "name": "Dư nợ BQ KHDN lớn"
        },
        {
            "code": "ttqt",
            "name": "Thanh toán quốc tế (USD)"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
####################################################################################################
# Mẫu import file excel
# version: 1.0.0                                                                                   #
####################################################################################################
"""
@api {GET} {domain}/kpi-management/api/v1.0/kpi/kpi-config/template-import-excel   Mẫu import file excel KPI
@apiGroup KpiConfig
@apiVersion 1.0.0
@apiName KpiConfigTemplateImport

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header


@apiSuccess         {Object}         data                    Dữ liệu trả về
@apiSuccess         {String}         data.url                Link file mẫu


@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "url": url
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {POST} {domain}/kpi-management/api/v1.0/kpi/kpi-config/import-excel  Thiết lập KPI bằng file excel
@apiGroup ImportKpiConfig
@apiVersion 1.0.0
@apiName ImportKpiConfigExcel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(Form:)			{File}	    file		            File excel cần import
@apiParam	(Form:)			{Int}	    year		            Năm thiết lập

@apiParamExample {json} Form example
file: (binary)

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

"""
@api {POST} {domain}/kpi-management/api/v1.0/kpi/kpi-configs  Danh sách kpi
@apiGroup KpiConfig
@apiVersion 1.0.0
@apiName ListKpiConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {Array}    [staff_ids]             <code>ID</code> Nhân viên
@apiParam  (Body:)     {Array}    [goal_codes]            <code>code</code> Danh sách mã mục tiêu
@apiParam  (Body:)     {Array}    [scope_codes]           Danh sách mã cấp quản lý
@apiParam  (Body:)     {String}    year                   Năm
@apiParam  (Body:)     {String}    [month]                Tháng

@apiParamExample {json} Body example
{
    "year": "2023",
    "staff_ids": ["ce5b23cc-c962-4d02-bbfb-8f3a3eec1737"],
    "goal_codes": ["JFFJND"],
    "scope_codes": ["HANOI#NAMTULIEM"],
    "month": "01"
}


@apiUse paging_tokens

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccess {Array}             data                          Thông tin KPI
@apiSuccess {String}            data.staff_id                 ID nhân viên
@apiSuccess {String}            data.JFFJND                   <code>JFFJND</code> Là mã mục tiêu </br>
                                                              <code>37777</code> Giá trị KPI của mục tiêu đó

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "JFFJND": 37777, // JFFJND là mã mục tiêu kinh doanh. 37777 là giá trị kpi mục tiêu
            "staff_id": "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paginate": {
        "page": 1,
        "page_count": 1,
        "per_page": 15,
        "total_count": 1
    }
}
"""


"""
@api {POST} {domain}/kpi-management/api/v1.0/kpi/kpi-config/total Tổng kpi tất cả nhân viên theo mục tiêu
@apiGroup KpiConfig
@apiVersion 1.0.0
@apiName TotalKpiConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {Array}    [staff_ids]             <code>ID</code> Nhân viên
@apiParam  (Body:)     {Array}    [goal_codes]            <code>code</code> Danh sách mã mục tiêu
@apiParam  (Body:)     {String}    year                   Năm
@apiParam  (Body:)     {String}    [month]                Tháng

@apiParamExample {json} Body example
{
    "year": "2023",
    "staff_ids": ["ce5b23cc-c962-4d02-bbfb-8f3a3eec1737"],
    "goal_codes": ["JFFJND"],
    "month": "01"
}

@apiUse paging_tokens

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin tổng KPI theo từng mã mục tiêu

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "DFDSFD": 9000000.0,
        "OKE": 9000000.0,
        "QEEWR": 9000000.0
    },
    "lang": "vi",
    "message": "request thành công."
}
"""



"""
@api {POST} {domain}/kpi-management/api/v1.0/kpi/kpi-config Chi tiết KPI theo nhân viên
@apiGroup KpiConfig
@apiVersion 1.0.0
@apiName DetailKpiConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {String}    year                   Năm
@apiParam  (Body:)     {String}    [goal_code]              Mã mục tiêu
@apiParam  (Body:)     {String}    staff_id               <code>ID</code> Nhân viên
@apiParam  (Body:)     {String}    [start_month]            Tháng bắt đầu
@apiParam  (Body:)     {String}    [end_month]              Tháng kết thúc
@apiParam  (Body:)     {String}    [search]                 Tìm kiếm theo mã mục tiêu, tên mục tiêu
@apiParam  (Body:)     {Boolean}   [ignore_goal_config_not_value]  Ẩn mục tiêu kinh doanh không có dữ liệu

@apiParamExample {json} Body example
{
    "year": "2023",
    "start_month": "01",
    "end_month": "10",
    "staff_id": "1daa5225-4283-4911-8ace-cd261c3ef6eb",
    "search": "DFDSFD",
    "ignore_goal_config_not_value": false
}

@apiUse paging_tokens

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách KPI theo mục tiêu
@apiSuccess {Float}            data.code                      Mã mục tiêu
@apiSuccess {Float}            data.unit_id                   ID đơn vị tính
@apiSuccess {Float}            data.name                      Tên mục tiêu
@apiSuccess {Float}            data.formula                   Công thức
@apiSuccess {Float}            data.january                  Mục tiêu tháng 1
@apiSuccess {Float}            data.february                  Mục tiêu tháng 2
@apiSuccess {Float}            data.march                  Mục tiêu tháng 3
@apiSuccess {Float}            data.april                  Mục tiêu tháng 4
@apiSuccess {Float}            data.may                  Mục tiêu tháng 5
@apiSuccess {Float}            data.june                  Mục tiêu tháng 6
@apiSuccess {Float}            data.july                  Mục tiêu tháng 7
@apiSuccess {Float}            data.august                  Mục tiêu tháng 8
@apiSuccess {Float}            data.september                  Mục tiêu tháng 9
@apiSuccess {Float}            data.october                  Mục tiêu tháng 10
@apiSuccess {Float}            data.november                  Mục tiêu tháng 11
@apiSuccess {Float}            data.december                  Mục tiêu tháng 12

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "april": 9000000.0,
            "august": 9000000.0,
            "code": "DFDSFD",
            "december": null,
            "february": 9000000.0,
            "formula": null,
            "january": 9000000.0,
            "july": 9000000.0,
            "june": 9000000.0,
            "march": 9000000.0,
            "may": 9000000.0,
            "name": "mcu tiêu 200",
            "november": null,
            "october": 9000000.0,
            "september": 9000000.0,
            "unit_id": "6524d35f01352ef3e3bb984e"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paginate": {
        "page": 1,
        "page_count": 1,
        "per_page": 15,
        "total_count": 10
    }
}
"""


"""
@api {POST} {domain}/kpi-management/api/v1.0/kpi/kpi-config/staff Chi tiết KPI theo danh sách ID nhân viên
@apiGroup KpiConfig
@apiVersion 1.0.0
@apiName DetailKpiConfigByStaff

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {String}    year                             Năm
@apiParam  (Body:)     {Array[String]}    [goal_codes]              Danh sách Mã mục tiêu
@apiParam  (Body:)     {Array[String]}    staff_ids                 Danh sách <code>ID</code> Nhân viên
@apiParam  (Body:)     {String}    [start_month]                    Tháng bắt đầu
@apiParam  (Body:)     {String}    [end_month]                      Tháng kết thúc
@apiParam  (Body:)     {String}    [search]                         Tìm kiếm theo mã mục tiêu, tên mục tiêu
@apiParam  (Body:)     {Boolean}   [ignore_goal_config_not_value]   Ẩn mục tiêu kinh doanh không có dữ liệu

@apiParamExample {json} Body example
{
    "year": "2023",
    "goal_codes": ["MUCTIEU25", "LINH22", "LINH22"],
    "start_month": "01",
    "end_month": "10",
    "staff_ids": ["3354a8d4-55b2-4a04-80b5-95bd608229c7", "bbf201e9-1c5d-4f47-8582-659cd2a0db02", "c7b46ba0-6990-4a4f-8d34-3fd5421b6f2a"]
    "search": "MUCTIEU25",
    "ignore_goal_config_not_value": false
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Thông tin KPI
@apiSuccess {String}            data.staff_id                 ID Nhân viên
@apiSuccess {Array[Object]}     data.kpi_configs              Danh sách KPI theo mục tiêu
@apiSuccess {Float}            data.kpi_configs.code                      Mã mục tiêu
@apiSuccess {Float}            data.kpi_configs.unit_id                   ID đơn vị tính
@apiSuccess {Float}            data.kpi_configs.name                      Tên mục tiêu
@apiSuccess {Float}            data.kpi_configs.formula                   Công thức
@apiSuccess {Float}            data.kpi_configs.january                  Mục tiêu tháng 1
@apiSuccess {Float}            data.kpi_configs.february                  Mục tiêu tháng 2
@apiSuccess {Float}            data.kpi_configs.march                  Mục tiêu tháng 3
@apiSuccess {Float}            data.kpi_configs.april                  Mục tiêu tháng 4
@apiSuccess {Float}            data.kpi_configs.may                  Mục tiêu tháng 5
@apiSuccess {Float}            data.kpi_configs.june                  Mục tiêu tháng 6
@apiSuccess {Float}            data.kpi_configs.july                  Mục tiêu tháng 7
@apiSuccess {Float}            data.kpi_configs.august                  Mục tiêu tháng 8
@apiSuccess {Float}            data.kpi_configs.september                  Mục tiêu tháng 9
@apiSuccess {Float}            data.kpi_configs.october                  Mục tiêu tháng 10
@apiSuccess {Float}            data.kpi_configs.november                  Mục tiêu tháng 11
@apiSuccess {Float}            data.kpi_configs.december                  Mục tiêu tháng 12

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "kpi_configs": [
                {
                    "april": null,
                    "august": null,
                    "code": "LINH22",
                    "december": null,
                    "february": null,
                    "formula": "sum",
                    "january": null,
                    "july": null,
                    "june": null,
                    "march": null,
                    "may": null,
                    "name": "linh22",
                    "november": null,
                    "october": null,
                    "september": null,
                    "unit_id": "6524d35f01352ef3e3bb984e",
                    "updated_time": 1698191507.549
                }
            ],
            "staff_id": "c7b46ba0-6990-4a4f-8d34-3fd5421b6f2a"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {PUT} {domain}/kpi-management/api/v1.0/kpi/kpi-config Cập nhật KPI theo nhân viên
@apiGroup KpiConfig
@apiVersion 1.0.0
@apiName UpdateKpiConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {Int}       year                           Năm
@apiParam  (Body:)     {String}    goal_code                      Mã mục tiêu
@apiParam  (Body:)     {String}    staff_id                       <code>ID</code> Nhân viên
@apiParam  (Body:)     {Array}     kpi_configs                    Danh sách kpi theo tháng
@apiParam  (Body:)     {String}    kpi_configs.month              Tháng 
@apiParam  (Body:)     {Float}     kpi_configs.target_value       Mục tiêu

@apiParamExample {json} Body example
{
    "year": 2023,
    "staff_id": "1daa5225-4283-4911-8ace-cd261c3ef6eb",
    "goal_code": "DFDSFD",
    "kpi_configs": [
        {
            "month": "october",
            "target_value": 60000
        }
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin tổng KPI theo từng mã mục tiêu

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {},
    "lang": "vi",
    "message": "request thành công."
}
"""
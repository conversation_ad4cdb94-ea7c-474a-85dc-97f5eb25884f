####################################################################################################
# Danh sách đơn vị tính của mục tiêu kinh doanh
# version: 1.0.0                                                                                   #
####################################################################################################
"""
@api {GET} {domain}/kpi-management/api/v1.0/kpi/goal-config/units   Danh sách đơn vị tính của mục tiêu kinh doanh
@apiGroup UnitGoalConfig
@apiVersion 1.0.0
@apiName ListUnitGoalConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header


@apiSuccess         {Array}         data                    Dữ liệu trả về
@apiSuccess         {String}        data.id                 <code>ID</code> Đơn vị tính
@apiSuccess         {String}        data.code               Mã đơn vị tính
@apiSuccess         {String}        data.name               Tên đơn vị tính


@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "_id": "651fe4984cfc668931658e1d",
            "code": "vnd",
            "id": "651fe4984cfc668931658e1d",
            "name": "VNĐ"
        },
        {
            "_id": "651fe4984cfc668931658e1e",
            "code": "customer",
            "id": "651fe4984cfc668931658e1e",
            "name": "Khách hàng"
        },
        {
            "_id": "651fe4984cfc668931658e1f",
            "code": "other",
            "id": "651fe4984cfc668931658e1f",
            "name": "Khác"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
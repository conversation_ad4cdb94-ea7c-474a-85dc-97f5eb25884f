####################################################################################################
# Danh sách cấu hình hiển thị mục tiêu
# version: 1.0.0                                                                                   #
####################################################################################################
"""
@api {GET} {domain}/kpi-management/api/v1.0/kpi/staff/display/goal-config   Danh sách cấu hình hiển thị mục tiêu
@apiGroup DisplayGoalConfig
@apiVersion 1.0.0
@apiName ListDisplayGoalConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header


@apiSuccess         {Object}       data                         Dữ liệu trả về
@apiSuccess         {Array}        data.goal_configs            Danh sách mục tiêu cấu hình
@apiSuccess         {String}       data.goal_configs.goal_code  Mã mục tiêu
@apiSuccess         {Int}          data.goal_configs.index      Thứ tự chọn


@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "goal_configs": [
            {
                "goal_code": "JFFJND",
                "index": 1
            },
            {
                "goal_code": "KDKSKJ",
                "index": 2
            }
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {POST} {domain}/kpi-management/api/v1.0/kpi/staff/display/goal-config  Cập nhật cấu hình hiển thị mục tiêu
@apiGroup DisplayGoalConfig
@apiVersion 1.0.0
@apiName UpdateDisplayGoalConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam  (Body:)     {Array}    goal_configs                Danh sách mục tiêu cấu hình
@apiParam  (Body:)     {String}   goal_configs.goal_code      Mã mục tiêu
@apiParam  (Body:)     {Int}      goal_configs.index          Thứ tự chọn

@apiSuccess         {Array}        data                        Dữ liệu trả về
@apiSuccess         {Array}        data.goal_configs            Danh sách mục tiêu cấu hình

@apiParamExample {json} Form example
{
    "goal_configs": [
        {
            "goal_code": "JFFJND",
            "index": 1
        },
        {
            "goal_code": "KDKSKJ",
            "index": 2
        }
    ]
}


@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "goal_configs": [
            {
                "goal_code": "JFFJND",
                "index": 1
            },
            {
                "goal_code": "KDKSKJ",
                "index": 2
            }
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
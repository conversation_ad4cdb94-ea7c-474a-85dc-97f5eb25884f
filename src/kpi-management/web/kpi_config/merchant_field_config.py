#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: thong
    Company: M O B I O
    Date Created: 08/07/2024
"""

####################################################################################################
# S<PERSON> lượng chỉ tiêu theo chức danh
# version: 1.0.0                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/kpi-management/api/v1.0/merchant-field/personalize/list  Danh sách field cá nhân hóa
@apiGroup Merchant Config
@apiVersion 1.0.0
@apiName MerchantFieldPersonalizeList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiSuccess {String}               message                       Mô tả phản hồi
@apiSuccess {Integer}              code                          Mã phản hồi
@apiSuccess {Array}                data                          Thông tin chỉ tiêu
@apiSuccess {String}               data.field_name               Tên Group
@apiSuccess {String}               data.key                      Group key
@apiSuccess {Object}               data.fields                   Danh sách fields
@apiSuccess {Array}                [data.fields.data_selected]          Dữ liệu hiển thị
@apiSuccess {String}               data.fields.display_type             Kiểu hiển thị dữ liệu.
@apiSuccess {String}               [data.fields.field_property]         Kiểu dữ liệu của field
@apiSuccess {String}               [data.fields.format]                 Data format.
@apiSuccess {String}               data.fields.field_name               Tên Field
@apiSuccess {String}               data.fields.key                      Field Key
@apiSuccess {String}               data.fields.source                   Nguồn (Default: "KPI")
@apiSuccess {String}               data.fields.replace                  Field cá nhân hóa


@apiSuccessExample {json} Response Example
{
    "data": [
        {
            "field_name": "TT KPI",
            "key": "kpi_info"
            "fields": [
                {
                    "data_selected": null,
                    "display_type": "date_picker",
                    "field_name": "Tháng gần nhất",
                    "field_property": "datetime",
                    "format": "%m/%Y",
                    "key": "month_nearest",
                    "replace": "*|THANG_GAN_NHAT|*",
                    "source": "KPI"
                }
            ]
        }
    ],
    "code": 200,
    "message": "request thành công."
}
"""

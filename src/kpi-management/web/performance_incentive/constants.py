#!/usr/bin/python
# -*- coding: utf8 -*-

"""
@apiDefine staff_title_query
@apiVersion 1.0.0
@apiParam   (Query:)     {String}   staff_title  Chức danh của nhân viên. Ví dụ: <code>rbo,srbo,cso,rm,srm,sm,gdv_ksv,rm0</code>
"""

"""
@apiDefine staff_title_success
@apiVersion 1.0.0
@apiSuccess   (Config)  {String}  staff_title  Chức danh của nhân viên. Ví dụ: <code>rbo,srbo,cso,rm,srm,sm,gdv_ksv,rm0</code>
"""

"""
@apiDefine staff_title_body
@apiVersion 1.0.0
@apiSuccess   (Body:)  {String}  staff_title  Chức danh của nhân viên. Ví dụ: <code>rbo,srbo,cso,rm,srm,sm,gdv_ksv,rm0</code>
"""

"""
@apiDefine condition_obj_success
@apiVersion 1.0.0
@apiSuccess   (Condition object)  {String}  id  Định danh của condition.
@apiSuccess   (Condition object)  {String}  key   Mã chỉ tiêu. Ứng với mỗi chức danh có danh sách mã chỉ tiêu khác nhau. Lấy danh sách mã chỉ tiêu theo chức danh (#)
@apiSuccess   (Condition object)  {String}  operator  Toán tử so sánh. Ví dụ: <code>&gt;,&lt;,&gt;=,&lt;=,between</code>
@apiSuccess   (Condition object)  {Object}  value   Dữ liệu toán hạng. Tùy thuộc vào toán tử sẽ có số lượng toán tử khác nhau.<br/>
<li>Với toán tử 1 ngôi có 1 toán hạng <code>first</code>.</li>
<li>Với toán tử 2 ngôi có 2 toán hạng <code>first, second</code></li>
"""

"""
@apiDefine condition_obj_body
@apiVersion 1.0.0
@apiParam   (Condition object)  {String}  id  Định danh của condition.
@apiParam   (Condition object)  {String}  key   Mã chỉ tiêu. Ứng với mỗi chức danh có danh sách mã chỉ tiêu khác nhau. Lấy danh sách mã chỉ tiêu theo chức danh (#)
@apiParam   (Condition object)  {String}  operator  Toán tử so sánh. Ví dụ: <code>&gt;,&lt;,&gt;=,&lt;=,between</code>
@apiParam   (Condition object)  {Object}  value   Dữ liệu toán hạng. Tùy thuộc vào toán tử sẽ có số lượng toán tử khác nhau.<br/>
<li>Với toán tử 1 ngôi có 1 toán hạng <code>first</code>.</li>
<li>Với toán tử 2 ngôi có 2 toán hạng <code>first, second</code></li>
"""
# ---------------------------------------------
# L<PERSON>y cấu hình lương vị trí theo staff title
# version: 1.0.0
# ---------------------------------------------

"""
@api {GET} {domain}/kpi-management/api/v1.0/position-salary/<staff_title>      L<PERSON>y cấu hình lương vị trí
@apiDescription Lấy cấu hình lương vị trí
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName GetPositionSalary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                Mã chỉ tiêu (Ex: <code>SRBO</code>)

@apiSuccess {Object}        data                          Dữ liệu trả về
@apiSuccess {Number}        data.position_salary          Lương vị trí
@apiSuccess {Number}        data.staff_title              Mã chỉ chiêu


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "position_salary": 124000,
        "staff_title": "SRBO",
    }
}
"""

# ---------------------------------------------
# Update lương vị trí theo staff title
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/api/v1.0/position-salary/<staff_title>      Cập nhật cấu hình lương vị trí
@apiDescription Cập nhật cấu hình lương vị trí
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName UpdatePositionSalary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                Mã chỉ tiêu (Ex: <code>SRBO</code>)

@apiParamExample {json} Body example
{
    "position_salary": 124000
}

@apiSuccess {Object}        data                          Dữ liệu trả về
@apiSuccess {Number}        data.position_salary          Lương vị trí
@apiSuccess {Number}        data.staff_title              Mã chỉ chiêu


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "position_salary": 124000,
        "staff_title": "SRBO",
    }
}
"""

# ---------------------------------------------
# Lấy cấu hình lương kinh doanh tối đa
# version: 1.0.0
# ---------------------------------------------

"""
@api {GET} {domain}/kpi-management/api/v1.0/salary-max-with-level/<staff_title>      Lấy cấu hình lương kinh doanh tối đa theo cấp độ
@apiDescription Lấy cấu hình lương kinh doanh tối đa theo cấp độ
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName GetSalaryMaxWithLevel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                Mã chỉ tiêu (Ex: <code>SRBO</code>)

@apiSuccess {Array}        data                          Dữ liệu trả về
@apiSuccess {String}        data.staff_level             Cấp độ
@apiSuccess {Number}        data.position_salary         Lương vị trí
@apiSuccess {Number}        data.value                   Lương kinh doanh tối đa


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_level": "RM1",
            "position_salary": 1240000,
            "value": 240000
        },
        {
            "staff_level": "RM2",
            "position_salary": 1240000,
            "value": 240000
        },
         {
            "staff_level": "RM3",
            "position_salary": 1240000,
            "value": 240000
        },
        {
            "staff_level": "RM4",
            "position_salary": 1240000,
            "value": 240000
        }
    ]
}
"""

# ---------------------------------------------
# Update cấu hình lương kinh doanh tối đa
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/api/v1.0/salary-max-with-level/<staff_title>      Update cấu hình lương kinh doanh tối đa theo cấp độ
@apiDescription Update cấu hình lương kinh doanh tối đa theo cấp độ
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName UpdateSalaryMaxWithLevel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                Mã chỉ tiêu (Ex: <code>SRBO</code>)

@apiParam  (Body:)     {Array}   data                         Danh sách cấu hình
@apiParam  (Body:)     {String}   data.staff_level                 Cấp độ
@apiParam  (Body:)     {Number}   data.position_salary             Lương vị trí
@apiParam  (Body:)     {Number}   data.value                     Lương kinh doanh tối đa

@apiParamExample {json} Body example
{
    "data": [
        {
            "staff_level": "RM1",
            "position_salary": 309494,
            "value": 3434346
        },
        {
            "staff_level": "RM2",
            "position_salary": 434,
            "value": 434
        },
        {
            "staff_level": "RM3",
            "position_salary": 7643,
            "value": 4465
        }
    ]
}


@apiSuccess {Array}         data                          Dữ liệu trả về
@apiSuccess {String}        data.staff_level             Cấp độ
@apiSuccess {Number}        data.position_salary         Lương vị trí
@apiSuccess {Number}        data.value                   Lương kinh doanh tối đa


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_level": "RM1",
            "position_salary": 1240000,
            "value": 240000
        },
        {
            "staff_level": "RM2",
            "position_salary": 1240000,
            "value": 240000
        },
         {
            "staff_level": "RM3",
            "position_salary": 1240000,
            "value": 240000
        },
        {
            "staff_level": "RM4",
            "position_salary": 1240000,
            "value": 240000
        }
    ]
}
"""

# ---------------------------------------------
# Lấy cấu điểm sale plan theo cấp độ
# version: 1.0.0
# ---------------------------------------------

"""
@api {GET} {domain}/kpi-management/api/v1.0/sale-plan-with-level/<staff_title>      Lấy cấu điểm sale plan theo cấp độ
@apiDescription Lấy cấu điểm sale plan theo cấp độ
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName GetSalePlanWithLevel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                Mã chỉ tiêu (Ex: <code>RBO</code>)

@apiSuccess {Array}        data                          Dữ liệu trả về
@apiSuccess {String}       data.staff_level              Cấp độ
@apiSuccess {Number}       data.value                    Tổng điểm Sale plan chuẩn


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_level": "RBO1",
            "value": 43545
        },
        {
            "staff_level": "RBO2",
            "value": 65644
        },
         {
            "staff_level": "RBO3",
            "value": 32454
        },
        {
            "staff_level": "RBO4",
            "value": 656565
        }
    ]
}
"""

# ---------------------------------------------
# Update cấu điểm sale plan theo cấp độ
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/api/v1.0/sale-plan-with-level/<staff_title>      Update cấu điểm sale plan theo cấp độ
@apiDescription Update cấu điểm sale plan theo cấp độ
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName UpdateSalePlanWithLevel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                Mã chỉ tiêu (Ex: <code>RBO</code>)

@apiParam  (Body:)     {Array}    data                         Danh sách cấu hình
@apiParam  (Body:)     {String}   data.staff_level             Cấp độ
@apiParam  (Body:)     {Number}   data.value                   Tổng điểm Sale plan chuẩn

@apiParamExample {json} Body example
{
    "data": [
        {
            "staff_level": "RBO1",
            "value": 2322
        },
        {
            "staff_level": "RBO2",
            "value": 2243
        },
        {
            "staff_level": "RBO3",
            "value": 43455
        },
        {
            "staff_level": "RBO4",
            "value": 43
        },
    ]
}

@apiSuccess {Array}        data                          Dữ liệu trả về
@apiSuccess {String}       data.staff_level              Cấp độ
@apiSuccess {Number}       data.value                    Tổng điểm Sale plan chuẩn


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_level": "RBO1",
            "value": 2322
        },
        {
            "staff_level": "RBO2",
            "value": 2243
        },
        {
            "staff_level": "RBO3",
            "value": 43455
        },
        {
            "staff_level": "RBO4",
            "value": 43
        }
    ]
}
"""

# ---------------------------------------------
# Danh sách cấu hình hệ số
# version: 1.0.0
# ---------------------------------------------

"""
@api {GET} {domain}/kpi-management/api/v1.0/management-coefficient/<staff_title>      Cấu hình hệ số theo chức danh
@apiDescription Cấu hình hệ số theo chức danh
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName GetManagementCoefficient

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                Mã chỉ tiêu (Ex: <code>RBO</code>)

@apiSuccess {Array}        data                             Dữ liệu trả về
@apiSuccess {Number}       data.total_members               Số lượng nhân viên
@apiSuccess {Number}       data.value                       Hệ số
@apiSuccess {String}       data.operator                    Toán tử </br>
                                                            <li><code>equal</code>: Bằng</li>
                                                            <li><code>less_than_or_equal</code>: bé hơn hoặc bằng</li>
                                                            <li><code>greater_than_or_equal</code>: Lớn hơn hoặc bằng</li>
                                                            <li><code>not_equal</code>: Không bằng</li>
                                                            <li><code>less_than</code>: Bé hơn</li>
                                                            <li><code>greater_than</code>: Lớn hơn</li>


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "total_members": 4,
            "value": 0.9,
            "operator": "equal"
        },
         {
            "total_members": 5,
            "value": 0.9,
            "operator": "equal"
        }
    ]
}
"""

# ---------------------------------------------
# Update cấu điểm sale plan theo cấp độ
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/api/v1.0/management-coefficient/<staff_title>      Cập nhật cấu hình hệ số theo chức danh
@apiDescription Cập nhật cấu hình hệ số theo chức danh
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName UpdateManagementCoefficient

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                Mã chỉ tiêu (Ex: <code>RBO</code>)

@apiParam  (Body:)     {Array}    data                         Danh sách cấu hình
@apiParam  (Body:)     {String}   data.total_members           Số lượng nhân viên
@apiParam  (Body:)     {Number}   data.value                   Hệ số
@apiParam  (Body:)     {Number}   data.operator                    Toán tử </br>
                                                            <li><code>equal</code>: Bằng</li>
                                                            <li><code>less_than_or_equal</code>: bé hơn hoặc bằng</li>
                                                            <li><code>greater_than_or_equal</code>: Lớn hơn hoặc bằng</li>
                                                            <li><code>not_equal</code>: Không bằng</li>
                                                            <li><code>less_than</code>: Bé hơn</li>
                                                            <li><code>greater_than</code>: Lớn hơn</li>

@apiParamExample {json} Body example
{
    "data": [
        {
            "total_members": 4,
            "value": 0.9,
            "operator": "equal"
        },
         {
            "total_members": 5,
            "value": 0.9,
            "operator": "equal"
        }
    ]
}

@apiSuccess {Array}        data                             Dữ liệu trả về
@apiSuccess {Number}       data.total_members               Số lượng nhân viên
@apiSuccess {Number}       data.value                       Hệ số
@apiSuccess {String}       data.operator                    Toán tử </br>
                                                            <li><code>equal</code>: Bằng</li>
                                                            <li><code>less_than_or_equal</code>: bé hơn hoặc bằng</li>
                                                            <li><code>greater_than_or_equal</code>: Lớn hơn hoặc bằng</li>
                                                            <li><code>not_equal</code>: Không bằng</li>
                                                            <li><code>less_than</code>: Bé hơn</li>
                                                            <li><code>greater_than</code>: Lớn hơn</li>


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
       {
            "total_members": 4,
            "value": 0.9,
            "operator": "equal"
        },
         {
            "total_members": 5,
            "value": 0.9,
            "operator": "equal"
        }
    ]
}
"""

"""
@api {post} {domain}/kpi-management/api/v1.0/api/v1.0/salary-deduction/<staff_title> Cấu hình chính sách giảm trừ lương kinh doanh
@apiDescription  Dịch vụ cấu hình chính sách giảm trừ lương kinh doanh theo chức danh
@apiGroup SalaryDeduction
@apiVersion 1.0.0
@apiName UpdateSalaryDeduction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiUse staff_title_query

@apiParam  (Body:)    {Array}      data  Danh sách cấu hình. Xem object <code>Config</code>
@apiParam   (Config)  {String}    id  Định danh cấu hình. (<b>Rule cấu hình </b>) <br>
                                    <ul>
                                        <li>Nếu data.id truyền lên = null thì thẻ điều kiện đó được xem là thẻ thêm mới </li>
                                        <li>Nếu data.id tồn tại thì sẽ được xem là cập nhật
                                        <li>Nếu muốn xoá cấu hình thẻ điều kiện thì vui lòng không truyền thẻ muốn xoá lên trên BE, BE sẽ tự kiểm tra và xoá thẻ đó khỏi hệ thống </li>
                                    </ul>
@apiParam   (Config)  {String}    target_code  Mã chỉ tiêu giảm trừ lương kinh doanh (Mặc định là: <code>total_point_performance_credit</code>: Chỉ tiêu tín dụng)
@apiParam   (Config)  {Number}    business_salary_deduction_percent  Lương kinh doanh bị giảm trừ (đơn vị: <code>%</code>)
@apiParam   (Config)  {Object[]}  condition_groups    Group các điều kiện thỏa mãn để nhân viên được nhận lương kinh doanh. Các group điều kiện sử dụng toán tử <code>or(hoặc)</code> để kiểm tra thỏa mãn.
@apiParam   (Config)  {String}    condition_groups..id    Định danh của group.
@apiParam   (Config)  {Object[]}  condition_groups..conditions  Danh sách các điều kiện. Các điều kiện sử dụng toán tử <code>and(và)</code> để kiểm tra thỏa mãn. Chi tiết xem object <code>condition</code>
@apiUse condition_obj_body

@apiParamExample {json} Body example
{
    "data": [
        {   
            "id": "667e6c8821832cb3c74ff49a",
            "conditions_groups": [
                {
                    "conditions": [
                        {
                            "id": "cb2f7778-eec3-4a75-b805-5920599417b4",
                            "key": "non_performing_loan_ratio",
                            "operator": ">=",
                            "value": {
                                "first": 2.25
                            }
                        },
                        {
                            "id": "ccf8b1b6-cacf-4690-b25a-f05780b0f533",
                            "key": "individual_customer_loan_portfolio_ratio",
                            "operator": ">",
                            "value": {
                                "first": 0
                            }
                        }
                    ],
                    "id": "fe571de7-8900-41e2-8a67-16032eff6e6d"
                },
                {
                    "conditions": [
                        {
                            "id": "",
                            "key": "non_performing_loan_ratio",
                            "operator": ">=",
                            "value": {
                                "first": 1.75
                            }
                        }
                    ],
                    "id": "8729fa63-4b10-4064-8b8f-b1d29da2dc00"
                }
            ],
            "target_code": "total_point_performance_credit",
            "business_salary_deduction_percent": 50
        }
    ]
}

@apiParamExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {post} {domain}/kpi-management/api/v1.0/api/v1.0/salary-deduction/<staff_title> Cấu hình chính sách giảm trừ lương kinh doanh
@apiDescription  Dịch vụ cấu hình chính sách giảm trừ lương kinh doanh theo chức danh
@apiGroup SalaryDeduction
@apiVersion 1.0.1
@apiName UpdateSalaryDeduction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiUse staff_title_query

@apiParam  (Body:)   {Array}  data  Danh sách cấu hình. Xem object <code>Config</code>
@apiParam   (Config)  {String}  id  Định danh cấu hình. (<b>Rule cấu hình </b>) <br>
                                                        <ul>
                                                            <li>Nếu data.id truyền lên = null thì thẻ điều kiện đó được xem là thẻ thêm mới </li>
                                                            <li>Nếu data.id tồn tại thì sẽ được xem là cập nhật
                                                            <li>Nếu muốn xoá cấu hình thẻ điều kiện thì vui lòng không truyền thẻ muốn xoá lên trên BE, BE sẽ tự kiểm tra và xoá thẻ đó khỏi hệ thống </li>
                                                        </ul>
@apiParam   (Config)  {String}  target_code  Mã chỉ tiêu giảm trừ lương kinh doanh <br>
                                             <li><code>RBO</code>: <code>total_point_performance_credit</code>: Chỉ tiêu tín dụng</li>
                                             <li><code>CSO</code>: <code>total_point_capital_mobilization</code>: Chỉ tiêu quy mô huy động vốn</li>
@apiParam   (Config)  {Number}  business_salary_deduction_percent  Lương kinh doanh bị giảm trừ (đơn vị: <code>%</code>)
@apiParam   (Config)  {Object[]}  condition_groups    Group các điều kiện thỏa mãn để nhân viên được nhận lương kinh doanh. Các group điều kiện sử dụng toán tử <code>or(hoặc)</code> để kiểm tra thỏa mãn.
@apiParam   (Config)  {String}  condition_groups..id    Định danh của group.
@apiParam   (Config)  {Object[]}  condition_groups..conditions  Danh sách các điều kiện. Các điều kiện sử dụng toán tử <code>and(và)</code> để kiểm tra thỏa mãn. Chi tiết xem object <code>condition</code>
@apiUse condition_obj_body

@apiParamExample {json} Body example
{
    "data": [
        {   
            "id": "667e6c8821832cb3c74ff49a",
            "conditions_groups": [
                {
                    "conditions": [
                        {
                            "id": "cb2f7778-eec3-4a75-b805-5920599417b4",
                            "key": "non_performing_loan_ratio",
                            "operator": ">=",
                            "value": {
                                "first": 2.25
                            }
                        },
                        {
                            "id": "ccf8b1b6-cacf-4690-b25a-f05780b0f533",
                            "key": "individual_customer_loan_portfolio_ratio",
                            "operator": ">",
                            "value": {
                                "first": 0
                            }
                        }
                    ],
                    "id": "fe571de7-8900-41e2-8a67-16032eff6e6d"
                },
                {
                    "conditions": [
                        {
                            "id": "",
                            "key": "non_performing_loan_ratio",
                            "operator": ">=",
                            "value": {
                                "first": 1.75
                            }
                        }
                    ],
                    "id": "8729fa63-4b10-4064-8b8f-b1d29da2dc00"
                }
            ],
            "target_code": "total_point_performance_credit",
            "business_salary_deduction_percent": 50
        }
    ]
}

@apiParamExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {post} {domain}/kpi-management/api/v1.0/api/v1.0/salary-deduction/<staff_title> Cấu hình chính sách giảm trừ lương kinh doanh
@apiDescription  Dịch vụ cấu hình chính sách giảm trừ lương kinh doanh theo chức danh
@apiGroup SalaryDeduction
@apiVersion 1.0.1
@apiName UpdateSalaryDeduction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiUse staff_title_query

@apiParam  (Body:)   {Array}  data  Danh sách cấu hình. Xem object <code>Config</code>
@apiParam   (Config)  {String}  id  Định danh cấu hình. (<b>Rule cấu hình </b>) <br>
                                                        <ul>
                                                            <li>Nếu data.id truyền lên = null thì thẻ điều kiện đó được xem là thẻ thêm mới </li>
                                                            <li>Nếu data.id tồn tại thì sẽ được xem là cập nhật
                                                            <li>Nếu muốn xoá cấu hình thẻ điều kiện thì vui lòng không truyền thẻ muốn xoá lên trên BE, BE sẽ tự kiểm tra và xoá thẻ đó khỏi hệ thống </li>
                                                        </ul>
@apiParam   (Config)  {String}  target_code  Mã chỉ tiêu giảm trừ lương kinh doanh <br>
                                             <li><code>RBO</code>: <code>total_point_performance_credit</code>: Chỉ tiêu tín dụng</li>
                                             <li><code>CSO</code>: <code>total_point_capital_mobilization</code>: Chỉ tiêu quy mô huy động vốn</li>
                                             <li><code>SM </code>: <code>total_point_performance_credit</code>: Chỉ tiêu tín dụng</li>
@apiParam   (Config)  {Number}  business_salary_deduction_percent  Lương kinh doanh bị giảm trừ (đơn vị: <code>%</code>)
@apiParam   (Config)  {Object[]}  condition_groups    Group các điều kiện thỏa mãn để nhân viên được nhận lương kinh doanh. Các group điều kiện sử dụng toán tử <code>or(hoặc)</code> để kiểm tra thỏa mãn.
@apiParam   (Config)  {String}  condition_groups..id    Định danh của group.
@apiParam   (Config)  {Object[]}  condition_groups..conditions  Danh sách các điều kiện. Các điều kiện sử dụng toán tử <code>and(và)</code> để kiểm tra thỏa mãn. Chi tiết xem object <code>condition</code>
@apiUse condition_obj_body

@apiParamExample {json} Body example
{
    "data": [
        {   
            "id": "667e6c8821832cb3c74ff49a",
            "conditions_groups": [
                {
                    "conditions": [
                        {
                            "id": "cb2f7778-eec3-4a75-b805-5920599417b4",
                            "key": "non_performing_loan_ratio",
                            "operator": ">=",
                            "value": {
                                "first": 2.25
                            }
                        },
                        {
                            "id": "ccf8b1b6-cacf-4690-b25a-f05780b0f533",
                            "key": "individual_customer_loan_portfolio_ratio",
                            "operator": ">",
                            "value": {
                                "first": 0
                            }
                        }
                    ],
                    "id": "fe571de7-8900-41e2-8a67-16032eff6e6d"
                },
                {
                    "conditions": [
                        {
                            "id": "",
                            "key": "non_performing_loan_ratio",
                            "operator": ">=",
                            "value": {
                                "first": 1.75
                            }
                        }
                    ],
                    "id": "8729fa63-4b10-4064-8b8f-b1d29da2dc00"
                }
            ],
            "target_code": "total_point_performance_credit",
            "business_salary_deduction_percent": 50
        }
    ]
}

@apiParamExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {get} {domain}/kpi-management/api/v1.0/salary-deduction/:staff_title Lấy thông tin cấu hình giảm trừ lương kinh doanh
@apiVersion 1.0.0
@apiName GetSalaryDeduction
@apiGroup SalaryDeduction

@apiUse json_header
@apiUse merchant_id_header

@apiParam {String} staff_title Chức vụ của nhân viên (vd: rbo, srbo, cso, rm, srm, sm, gdv_ksv, rm0).

@apiSuccess {Number} code Mã trạng thái của phản hồi.
@apiSuccess {Object[]} data Danh sách thông tin giảm trừ lương.
@apiSuccess {String} data.id Mã ID thẻ giảm trừ lương.
@apiSuccess {Number} data.business_salary_deduction_percent Phần trăm giảm từ lương.
@apiSuccess {String} data.condition_expression Biểu thức điều kiện.
@apiSuccess {Object[]} data.conditions_groups Nhóm điều kiện.
@apiSuccess {Object[]} data.conditions_groups.conditions Các điều kiện trong nhóm.
@apiSuccess {String} data.conditions_groups.conditions.id ID của điều kiện.
@apiSuccess {String} data.conditions_groups.conditions.key Tên khóa điều kiện.
@apiSuccess {String} data.conditions_groups.conditions.operator Toán tử so sánh.
@apiSuccess {Object} data.conditions_groups.conditions.value Giá trị của điều kiện.
@apiSuccess {Number} data.conditions_groups.conditions.value.first Giá trị đầu tiên của điều kiện.
@apiSuccess {String} data.conditions_groups.id ID của nhóm điều kiện.
@apiSuccess {String} data.staff_title Chức danh.
@apiSuccess {String} data.target_code Mã chỉ tiêu
@apiSuccess {String} message Thông báo của phản hồi.
@apiUse   condition_obj_success

@apiSuccessExample {json} Success-Response:
    HTTP/1.1 200 OK
    {
      "code": 200,
      "data": [
        {
          "_id": "667e6c8821832cb3c74ff49a",
          "business_salary_deduction_percent": 50,
          "condition_expression": "(({non_performing_loan_ratio}>=2.25)and({individual_customer_loan_portfolio_ratio}>0))or(({non_performing_loan_ratio}>=1.75))",
          "conditions_groups": [
            {
              "conditions": [
                {
                  "id": "cb2f7778-eec3-4a75-b805-5920599417b4",
                  "key": "non_performing_loan_ratio",
                  "operator": ">=",
                  "value": {
                    "first": 2.25
                  }
                },
                {
                  "id": "ccf8b1b6-cacf-4690-b25a-f05780b0f533",
                  "key": "individual_customer_loan_portfolio_ratio",
                  "operator": ">",
                  "value": {
                    "first": 0
                  }
                }
              ],
              "id": "fe571de7-8900-41e2-8a67-16032eff6e6d"
            },
            {
              "conditions": [
                {
                  "id": "",
                  "key": "non_performing_loan_ratio",
                  "operator": ">=",
                  "value": {
                    "first": 1.75
                  }
                }
              ],
              "id": "8729fa63-4b10-4064-8b8f-b1d29da2dc00"
            }
          ],
          "id": "667e6c8821832cb3c74ff49a",
          "staff_title": "rbo",
          "target_code": "total_point_performance_credit",
          "variables": [
            "non_performing_loan_ratio",
            "individual_customer_loan_portfolio_ratio"
          ]
        }
      ],
      "lang": "vi",
      "message": "request thành công."
    }
"""

# ---------------------------------------------
# Update cấu hình lương kinh doanh tối đa
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/api/v1.0/salary-max-with-level/<staff_title>      Update cấu hình lương kinh doanh tối đa theo cấp độ
@apiDescription Update cấu hình lương kinh doanh tối đa theo cấp độ
@apiGroup PerformanceIncentive
@apiVersion 1.0.1
@apiName UpdateSalaryMaxWithLevel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                Mã chỉ tiêu (Ex: <code>SRBO</code>)

@apiParam  (Body:)     {Array}   data                         Danh sách cấu hình
@apiParam  (Body:)     {String}   data.staff_level                 Cấp độ
@apiParam  (Body:)     {Number}   data.value_salary_additional_target      Lương kinh doanh chỉ tiêu bổ sung
@apiParam  (Body:)     {Number}   data.position_salary             Lương vị trí
@apiParam  (Body:)     {Number}   data.value                     Lương kinh doanh tối đa

@apiParamExample {json} Body example
{
    "data": [
        {
            "staff_level": "RM1",
            "position_salary": 309494,
            "value_salary_additional_target": 5000000,
            "value": 2000000
        },
        {
            "staff_level": "RM2",
            "position_salary": 434,
            "value_salary_additional_target": 5000000,
            "value": 2000000
        },
        {
            "staff_level": "RM3",
            "position_salary": 7643,
            "value_salary_additional_target": 5000000,
            "value": 2000000
        }
    ]
}


@apiSuccess {Array}         data                          Dữ liệu trả về
@apiSuccess {String}        data.staff_level             Cấp độ
@apiSuccess {Number}        data.position_salary         Lương vị trí
@apiSuccess {Number}        data.value_salary_additional_target         Lương kinh doanh chỉ tiêu bổ sung
@apiSuccess {Number}        data.value                   Lương kinh doanh tối đa


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_level": "RM1",
            "position_salary": 1240000,
            "value": 240000,
            "value_salary_additional_target": 5000000,
        },
        {
            "staff_level": "RM2",
            "position_salary": 1240000,
            "value": 240000,
            "value_salary_additional_target": 5000000,
        },
         {
            "staff_level": "RM3",
            "position_salary": 1240000,
            "value": 240000,
            "value_salary_additional_target": 5000000,
        },
        {
            "staff_level": "RM4",
            "position_salary": 1240000,
            "value": 240000,
            "value_salary_additional_target": 5000000,
        }
    ]
}
"""

# ---------------------------------------------
# Lấy cấu hình lương kinh doanh tối đa
# version: 1.0.0
# ---------------------------------------------

"""
@api {GET} {domain}/kpi-management/api/v1.0/salary-max-with-level/<staff_title>      Lấy cấu hình lương kinh doanh tối đa theo cấp độ
@apiDescription Lấy cấu hình lương kinh doanh tối đa theo cấp độ
@apiGroup PerformanceIncentive
@apiVersion 1.0.1
@apiName GetSalaryMaxWithLevel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)         {String}   staff_title                Mã chỉ tiêu (Ex: <code>SRBO</code>)

@apiSuccess {Array}         data                          Dữ liệu trả về
@apiSuccess {String}        data.staff_level             Cấp độ
@apiSuccess {Number}        data.position_salary         Lương vị trí
@apiSuccess {Number}        data.value_salary_additional_target       Lương kinh doanh chỉ  tiêu bổ sung
@apiSuccess {Number}        data.value                   Lương kinh doanh tối đa


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_level": "RM1",
            "position_salary": 1240000,
            "value": 240000,
            "value_salary_additional_target": 5000000
        },
        {
            "staff_level": "RM2",
            "position_salary": 1240000,
            "value": 240000,
            "value_salary_additional_target": 5000000
        },
         {
            "staff_level": "RM3",
            "position_salary": 1240000,
            "value": 240000,
            "value_salary_additional_target": 5000000
        },
        {
            "staff_level": "RM4",
            "position_salary": 1240000,
            "value": 240000,
            "value_salary_additional_target": 5000000
        }
    ]
}
"""

# ---------------------------------------------
# Lấy cấu hình lương vị trí theo staff title
# version: 1.0.0
# ---------------------------------------------

"""
@api {GET} {domain}/kpi-management/api/v1.0/position-salary/<staff_title>      Lấy cấu hình lương vị trí
@apiDescription Lấy cấu hình lương vị trí
@apiGroup PerformanceIncentive
@apiVersion 1.0.1
@apiName GetPositionSalary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                Mã chỉ tiêu (Ex: <code>SRBO</code>)

@apiSuccess {Object}        data                          Dữ liệu trả về
@apiSuccess {Number}        data.position_salary          Lương vị trí
@apiSuccess {Number}        data.position_salary_percent  Tỉ lệ X theo lương vị trí
@apiSuccess {Number}        data.staff_title              Mã chỉ chiêu


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "position_salary": 124000,
        "position_salary_percent": 40,
        "staff_title": "SRBO",
    }
}
"""

# ---------------------------------------------
# Update lương vị trí theo staff title
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/api/v1.0/position-salary/<staff_title>      Cập nhật cấu hình lương vị trí
@apiDescription Cập nhật cấu hình lương vị trí
@apiGroup PerformanceIncentive
@apiVersion 1.0.1
@apiName UpdatePositionSalary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                Mã chỉ tiêu (Ex: <code>SRBO</code>)

@apiParamExample {json} Body example
{
    "position_salary": 124000,
    "position_salary_percent": 40
}

@apiSuccess {Object}        data                          Dữ liệu trả về
@apiSuccess {Number}        data.position_salary          Lương vị trí
@apiSuccess {Number}        data.position_salary_percent  Tỉ lệ X theo lương vị trí
@apiSuccess {Number}        data.staff_title              Mã chỉ chiêu


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "position_salary": 124000,
        "position_salary_percent": 40,
        "staff_title": "SRBO",
    }
}
"""

# ---------------------------------------------
# Lấy cấu hình lương kinh doanh tối đa
# version: 1.0.2
# ---------------------------------------------

"""
@api {GET} {domain}/kpi-management/api/v1.0/salary-max-with-level/<staff_title>      Lấy cấu hình lương kinh doanh tối đa theo cấp độ
@apiDescription Lấy cấu hình lương kinh doanh tối đa theo cấp độ
@apiGroup PerformanceIncentive
@apiVersion 1.0.2
@apiName GetSalaryMaxWithLevel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)         {String}   staff_title                  Mã chỉ tiêu (Ex: <code>SRBO</code>)
@apiSuccess {Array}         data                                    Dữ liệu trả về
@apiSuccess {String}        data.staff_level                        Cấp độ
@apiSuccess {Number}        data.position_salary                    Lương vị trí
@apiSuccess {Number}        data.value_salary_additional_target     Lương kinh doanh chỉ  tiêu bổ sung
@apiSuccess {Number}        data.max_salary_from_funding            Lương kinh doanh tối đa từ chỉ tiêu Huy động vốn
@apiSuccess {Number}        data.max_salary_from_credit             Lương kinh doanh tối đa từ chỉ tiêu tín dụng
@apiSuccess {Number}        data.value                              Lương kinh doanh tối đa


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_level": "RM1",
            "position_salary": 1240000,
            "max_salary_from_funding": 10000000,
            "max_salary_from_credit": 10000000,
            "value": 240000,
            "value_salary_additional_target": 5000000
        },
        {
            "staff_level": "RM2",
            "position_salary": 1240000,
            "max_salary_from_funding": 10000000,
            "max_salary_from_credit": 10000000,
            "value": 240000,
            "value_salary_additional_target": 5000000
        },
         {
            "staff_level": "RM3",
            "position_salary": 1240000,
            "max_salary_from_funding": 10000000,
            "max_salary_from_credit": 10000000,
            "value": 240000,
            "value_salary_additional_target": 5000000
        },
        {
            "staff_level": "RM4",
            "position_salary": 1240000,
            "max_salary_from_funding": 10000000,
            "max_salary_from_credit": 10000000,
            "value": 240000,
            "value_salary_additional_target": 5000000
        }
    ]
}
"""

# ---------------------------------------------
# Update cấu hình lương kinh doanh tối đa
# version: 1.0.2
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/api/v1.0/salary-max-with-level/<staff_title>      Update cấu hình lương kinh doanh tối đa theo cấp độ
@apiDescription Update cấu hình lương kinh doanh tối đa theo cấp độ
@apiGroup PerformanceIncentive
@apiVersion 1.0.2
@apiName UpdateSalaryMaxWithLevel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                              Mã chỉ tiêu (Ex: <code>SRBO</code>)
@apiParam  (Body:)     {Array}   data                                       Danh sách cấu hình
@apiParam  (Body:)     {String}   data.staff_level                          Cấp độ
@apiParam  (Body:)     {Number}   data.value_salary_additional_target       Lương kinh doanh chỉ tiêu bổ sung
@apiParam  (Body:)     {Number}   [data.max_salary_from_funding]            Lương kinh doanh tối đa từ chỉ tiêu Huy động vốn
@apiParam  (Body:)     {Number}   [data.max_salary_from_credit]             Lương kinh doanh tối đa từ chỉ tiêu tín dụng
@apiParam  (Body:)     {Number}   data.position_salary                      Lương vị trí
@apiParam  (Body:)     {Number}   data.value                                Lương kinh doanh tối đa

@apiParamExample {json} Body example
{
    "data": [
        {
            "staff_level": "RM1",
            "position_salary": 309494,
            "value_salary_additional_target": 5000000,
            "max_salary_from_funding": 10000000,
            "value": 2000000
        },
        {
            "staff_level": "RM2",
            "position_salary": 434,
            "value_salary_additional_target": 5000000,
            "max_salary_from_funding": 10000000,
            "value": 2000000
        },
        {
            "staff_level": "RM3",
            "position_salary": 7643,
            "value_salary_additional_target": 5000000,
            "max_salary_from_funding": 10000000,
            "value": 2000000
        }
    ]
}


@apiSuccess {Array}         data                                        Dữ liệu trả về
@apiSuccess {String}        data.staff_level                            Cấp độ
@apiSuccess {Number}        data.position_salary                        Lương vị trí
@apiSuccess {Number}        data.value_salary_additional_target         Lương kinh doanh chỉ tiêu bổ sung
@apiSuccess {Number}        data.max_salary_from_funding                Lương kinh doanh tối đa từ chỉ tiêu Huy động vốn
@apiSuccess {Number}        data.max_salary_from_credit                 Lương kinh doanh tối đa từ chỉ tiêu tín dụng
@apiSuccess {Number}        data.value                                  Lương kinh doanh tối đa


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_level": "RM1",
            "position_salary": 1240000,
            "value": 240000,
            "value_salary_additional_target": 5000000,
            "max_salary_from_funding": 10000000,
        },
        {
            "staff_level": "RM2",
            "position_salary": 1240000,
            "value": 240000,
            "value_salary_additional_target": 5000000,
            "max_salary_from_funding": 10000000,
        },
         {
            "staff_level": "RM3",
            "position_salary": 1240000,
            "value": 240000,
            "value_salary_additional_target": 5000000,
            "max_salary_from_funding": 10000000,
        },
        {
            "staff_level": "RM4",
            "position_salary": 1240000,
            "value": 240000,
            "value_salary_additional_target": 5000000,
            "max_salary_from_funding": 10000000,
        }
    ]
}
"""


# ---------------------------------------------
# Update cấu hình lương kinh doanh tối đa theo điều kiện
# version: 1.0.1
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/api/v1.0/salary-max-by-condition/<staff_title>      Update cấu hình lương kinh doanh tối đa theo điều kiện
@apiDescription  Update cấu hình lương kinh doanh tối đa theo điều kiện
@apiGroup PerformanceIncentive
@apiVersion 1.0.1
@apiName UpdateSalaryMaxByCondition

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   staff_title                              Mã chỉ tiêu (Ex: <code>RM</code>)
@apiParam  (Body:)     {Array}      data                                       Danh sách cấu hình
@apiParam   (Config)  {String}      id  Định danh cấu hình. (<b>Rule cấu hình </b>) <br>
                                    <ul>
                                        <li>Nếu data.id truyền lên = null thì thẻ điều kiện đó được xem là thẻ thêm mới </li>
                                        <li>Nếu data.id tồn tại thì sẽ được xem là cập nhật
                                        <li>Nếu muốn xoá cấu hình thẻ điều kiện thì vui lòng không truyền thẻ muốn xoá lên trên BE, BE sẽ tự kiểm tra và xoá thẻ đó khỏi hệ thống </li>
                                    </ul>
@apiParam   (Config)  {Object[]}  condition_groups    Group các điều kiện thỏa mãn để nhân viên được nhận lương kinh doanh. Các group điều kiện sử dụng toán tử <code>or(hoặc)</code> để kiểm tra thỏa mãn.
@apiParam   (Config)  {String}    condition_groups..id    Định danh của group.
@apiParam   (Config)  {Object[]}  condition_groups..conditions  Danh sách các điều kiện. Các điều kiện sử dụng toán tử <code>and(và)</code> để kiểm tra thỏa mãn. Chi tiết xem object <code>condition</code>
@apiUse condition_obj_body
@apiParam  (Body:)     {Number}   data.value                                Lương kinh doanh tối đa

@apiParamExample {json} Body example
{
    "data": [
        {   
            "id": "667e6c8821832cb3c74ff49a",
            "conditions_groups": [
                {
                    "conditions": [
                        {
                            "id": "cb2f7778-eec3-4a75-b805-5920599417b4",
                            "key": "non_performing_loan_ratio",
                            "operator": ">=",
                            "value": {
                                "first": 2.25
                            }
                        },
                        {
                            "id": "ccf8b1b6-cacf-4690-b25a-f05780b0f533",
                            "key": "individual_customer_loan_portfolio_ratio",
                            "operator": ">",
                            "value": {
                                "first": 0
                            }
                        }
                    ],
                    "id": "fe571de7-8900-41e2-8a67-16032eff6e6d"
                },
                {
                    "conditions": [
                        {
                            "id": "",
                            "key": "non_performing_loan_ratio",
                            "operator": ">=",
                            "value": {
                                "first": 1.75
                            }
                        }
                    ],
                    "id": "8729fa63-4b10-4064-8b8f-b1d29da2dc00"
                }
            ],
            "value": 10000000
        }
    ]
}

@apiParamExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Lấy cấu hình đơn giá chuẩn
# version: 1.0.1
# ---------------------------------------------

"""
@api {GET} {domain}/kpi-management/api/v1.0/standard-unit-price/<staff_title>      Lấy cấu hình đơn giá chuẩn
@apiDescription Lấy cấu hình đơn giá chuẩn
@apiGroup PerformanceIncentive
@apiVersion 1.0.1
@apiName GetStandardUnitPrice

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Param:)         {String}   staff_title                  Mã chỉ tiêu (Ex: <code>RM0</code>)
@apiSuccess {Object}        data                                    Cấu hình
@apiSuccess {String}        data.value                              Cấp độ

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "value": 100000
    }
}
"""

# ---------------------------------------------
# Update cấu hình đơn giá chuẩn
# version: 1.0.1
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/api/v1.0/standard-unit-price/<staff_title>     Cập nhật cấu hình đơn giá chuẩn theo chức danh
@apiDescription Cập nhật cấu hình đơn giá chuẩn theo chức danh
@apiGroup PerformanceIncentive
@apiVersion 1.0.1
@apiName UpdateStandardUnitPrice

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)    {String}  staff_title                               Mã chỉ tiêu (Ex: <code>RM0</code>)
@apiParam  (Body:)     {Object}   data                                      Cấu hình
@apiParam  (Body:)     {Number}   data.value                                Đơn giá chuẩn

@apiParamExample {json} Body example
{
    "value": 100000
}


@apiSuccess {Object}         data                                        Dữ liệu trả về
@apiSuccess {Number}         data.value                                   Đơn giá chuẩn


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "value": 100000
    }
}
"""


# ---------------------------------------------
# Lấy cấu hình lương kinh doanh chuẩn
# version: 1.0.1
# ---------------------------------------------

"""
@api {GET} {domain}/kpi-management/api/v1.0/standard-business-salary/<staff_title>    Lấy cấu hình lương kinh doanh chuẩn
@apiDescription Lấy cấu hình lương kinh doanh chuẩn
@apiGroup PerformanceIncentive
@apiVersion 1.0.1
@apiName GetStandardBusinessSalary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Param:)        {String}   staff_title                  Mã chỉ tiêu (Ex: <code>RM0</code>)
@apiSuccess {Object}        data                                    Cấu hình
@apiSuccess {String}        data.value                              Cấp độ

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "value": 100000
    }
}
"""

# ---------------------------------------------
# Cập nhật cấu hình lương kinh doanh chuẩn
# version: 1.0.1
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/api/v1.0/standard-business-salary/<staff_title>   Cập nhật cấu hình lương kinh doanh chuẩn
@apiDescription  Cập nhật cấu hình lương kinh doanh chuẩn
@apiGroup PerformanceIncentive
@apiVersion 1.0.1
@apiName UpdateStandardBusinessSalary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)    {String}   staff_title                               Mã chỉ tiêu (Ex: <code>RM0</code>)
@apiParam  (Body:)     {Object}   data                                      Cấu hình
@apiParam  (Body:)     {Number}   data.value                                Đơn giá chuẩn

@apiParamExample {json} Body example
{
    "value": 100000
}


@apiSuccess {Object}         data                                        Dữ liệu trả về
@apiSuccess {Number}         data.value                                   Đơn giá chuẩn


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "value": 100000
    }
}
"""


# ---------------------------------------------
# Lấy cấu hình lương kinh doanh tối đa theo điều kiện
# version: 1.0.1
# ---------------------------------------------

"""
@api {GET} {domain}/kpi-management/api/v1.0/salary-max-by-condition/<staff_title>     Lấy cấu hình lương kinh doanh tối đa theo điều kiện
@apiDescription Lấy cấu hình lương kinh doanh tối đa theo điều kiện
@apiGroup PerformanceIncentive
@apiVersion 1.0.1
@apiName GetSalaryByCondition

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)         {String}   staff_title                  Mã chỉ tiêu (Ex: <code>SRBO</code>)
@apiSuccess {Array}         data                                    Dữ liệu trả về
@apiSuccess {Number}        data.value                              Lương kinh doanh tối đa
@apiSuccess {String}        data.id Mã ID thẻ giảm trừ lương.
@apiSuccess {String}        data.condition_expression Biểu thức điều kiện.
@apiSuccess {Object[]}      data.conditions_groups Nhóm điều kiện.
@apiSuccess {Object[]}      data.conditions_groups.conditions Các điều kiện trong nhóm.
@apiSuccess {String}        data.conditions_groups.conditions.id ID của điều kiện.
@apiSuccess {String}        data.conditions_groups.conditions.key Tên khóa điều kiện.
@apiSuccess {String}        data.conditions_groups.conditions.operator Toán tử so sánh.
@apiSuccess {Object}        data.conditions_groups.conditions.value Giá trị của điều kiện.
@apiSuccess {Number}        data.conditions_groups.conditions.value.first Giá trị đầu tiên của điều kiện.
@apiSuccess {String}        data.conditions_groups.id ID của nhóm điều kiện.
@apiSuccess {String}        data.staff_title Chức danh.


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
          "_id": "667e6c8821832cb3c74ff49a",
          "value": 100000,
          "conditions_groups": [
            {
              "conditions": [
                {
                  "id": "cb2f7778-eec3-4a75-b805-5920599417b4",
                  "key": "non_performing_loan_ratio",
                  "operator": ">=",
                  "value": {
                    "first": 2.25
                  }
                },
                {
                  "id": "ccf8b1b6-cacf-4690-b25a-f05780b0f533",
                  "key": "individual_customer_loan_portfolio_ratio",
                  "operator": ">",
                  "value": {
                    "first": 0
                  }
                }
              ],
              "id": "fe571de7-8900-41e2-8a67-16032eff6e6d"
            },
            {
              "conditions": [
                {
                  "id": "",
                  "key": "non_performing_loan_ratio",
                  "operator": ">=",
                  "value": {
                    "first": 1.75
                  }
                }
              ],
              "id": "8729fa63-4b10-4064-8b8f-b1d29da2dc00"
            }
          ],
          "id": "667e6c8821832cb3c74ff49a",
          "staff_title": "rbo",
          "variables": [
            "non_performing_loan_ratio",
            "individual_customer_loan_portfolio_ratio"
          ]
        }
    ]
}
"""




# ---------------------------------------------
# Lấy cấu hình điểm chỉ tiêu dùng tính lương tối đa
# version: 1.0.0
# ---------------------------------------------

"""
@api {GET} {domain}/kpi-management/api/v1.0/max-performance-points-salary/<staff_title>    Lấy cấu hình điểm chỉ tiêu dùng tính lương tối đa
@apiDescription Lấy cấu hình điểm chỉ tiêu dùng tính lương tối đa
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName GetMaxPerformancePointsSalary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Param:)            {String}   staff_title                  Mã chỉ tiêu (Ex: <code>RM0</code>)
@apiSuccess {Array[Object]}      data                                    Cấu hình
@apiSuccess {String}             data.target_code                          Mã chỉ tiêu
@apiSuccess {Array[Object]}      data.configs                              Danh sách Điểm chỉ tiêu dùng tính lương tối đa theo từng level
@apiSuccess {String}             data.configs.staff_level                  Cấp độ chức danh
@apiSuccess {Number}             data.configs.value                        Điểm chỉ tiêu chuẩn
@apiSuccess {Number}             data.points_percentage                    Điểm chỉ tiêu dùng tính lương

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id": "673d832eb029ae59dd87b125",
            "configs": [
                {
                    "staff_level": "CD109",
                    "value": 180
                },
                {
                    "staff_level": "CD110",
                    "value": 180
                },
                {
                    "staff_level": "CD111",
                    "value": 180
                },
                {
                    "staff_level": "CD112",
                    "value": 180
                }
            ],
            "id": "673d832eb029ae59dd87b125",
            "points_percentage": 150,
            "target_code": "loan"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Cập nhật cấu hình Điểm chỉ tiêu dùng tính lương tối đa
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/api/v1.0/max-performance-points-salary/<staff_title>   Cập nhật cấu Điểm chỉ tiêu dùng tính lương tối đa
@apiDescription  Cập nhật cấu hình Điểm chỉ tiêu dùng tính lương tối đa
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName UpdateMaxPerformancePointsSalary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)    {String}             staff_title                               Mã chỉ tiêu (Ex: <code>RM0</code>)
@apiParam  (Body:)     {Array[Object]}      data                                      Danh sách cấu hình theo từng chỉ tiêu
@apiParam  (Body:)     {String}             data.target_code                          Mã chỉ tiêu
@apiParam  (Body:)     {Array[Object]}      data.configs                              Danh sách Điểm chỉ tiêu dùng tính lương tối đa theo từng level
@apiParam  (Body:)     {String}             data.configs.staff_level                  Cấp độ chức danh
@apiParam  (Body:)     {Number}             data.configs.value                        Điểm chỉ tiêu chuẩn
@apiParam  (Body:)     {Number}             data.points_percentage                    Điểm chỉ tiêu dùng tính lương

@apiParamExample {json} Body example
{
    "data": [
        {
            "target_code": "loan",
            "configs": [
                {
                    "staff_level": "CD109",
                    "value": 180
                },
                {
                    "staff_level": "CD110",
                    "value": 180
                },
                {
                    "staff_level": "CD111",
                    "value": 180
                },
                {
                    "staff_level": "CD112",
                    "value": 180
                }
            ],
            "points_percentage": 150
        }
    ]
}

@apiSuccess {Object}         data                                        Dữ liệu trả về
@apiSuccess {Number}         data.value                                   Đơn giá chuẩn


@apiSuccessExample {json} Response Example
{
    "body": {
        "data": [
            {
                "configs": [
                    {
                        "staff_level": "CD109",
                        "value": 180
                    },
                    {
                        "staff_level": "CD110",
                        "value": 180
                    },
                    {
                        "staff_level": "CD111",
                        "value": 180
                    },
                    {
                        "staff_level": "CD112",
                        "value": 180
                    }
                ],
                "points_percentage": 150,
                "target_code": "loan",
                "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
                "updated_time": "2024-11-20T07:16:02.000Z"
            }
        ]
    },
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

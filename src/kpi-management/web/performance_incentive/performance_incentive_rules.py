#!/usr/bin/python
# -*- coding: utf8 -*-
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Set
rules
config ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {post} {domain}/kpi-management/api/v1.0/performance-incentive/rules   Cấu hình chính sách
@apiDescription  Dịch vụ cấu hình chính sách theo chức danh.
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName SetRulesPI

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam  (Body:)   {Array}  rules  Danh sách cấu hình. Xem object <code>Config</code>
@apiParam  (Param:)   {String}  staff_title  Chức danh

@apiParam   (Config)  {String}  id  Định danh cấu hình.
@apiUse staff_title_body
@apiParam   (Config)  {Number}  [unit_price]  Đơn giá. Chỉ <code>required</code> với các chức danh: <code>rbo, cso, rm</code>
@apiParam   (Config)  {String}  max_value_expression_type   Loại cấu hình lương kinh doanh tối đa.<br/>
<li><code>standard_sale_plan_total_by_staff_level</code>: Điểm chuẩn Saleplan theo cấp độ</li>
<li><code>max_performance_incentive_by_staff_level</code>: Lương kinh doanh tối đa theo cấp độ</li>
@apiParam   (Config)  {Object[]}  condition_groups    Group các điều kiện thỏa mãn để nhân viên được nhận lương kinh doanh. Các group điều kiện sử dụng toán tử <code>or(hoặc)</code> để kiểm tra thỏa mãn.
@apiParam   (Config)  {String}  condition_groups..id    Định danh của group.
@apiParam   (Config)  {Object[]}  condition_groups..conditions  Danh sách các điều kiện. Các điều kiện sử dụng toán tử <code>and(và)</code> để kiểm tra thỏa mãn. Chi tiết xem object <code>condition</code>
@apiUse condition_obj_body

@apiParamExample   {json}  Response: HTTP/1.1 200 OK
{
  "rules": [
    {
      "id": "642fc65e2af4e1dbe42ea896",
      "staff_title": "RBO",
      "unit_price": 3000,
      "max_value_expression_type": "standard_sale_plan_total_by_staff_level",
      "conditions_groups": [
        {
          "id": "7830f766-786f-491c-9c8f-a3fb2cb11904",
          "conditions": [
            {
              "id": "ca304779-d2a4-4dc8-8613-08f068b63ce5",
              "key": "kpi_processed_amount",
              "operator": ">=",
              "value": {
                "first": 3
              }
            },
            {
              "id": "ef70ce34-dc51-4fec-9a12-fb3c00464324",
              "key": "sale_plan_completion_rate",
              "operator": ">=",
              "value": {
                "first": 70
              }
            }
          ]
        }
      ]
    }
  ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Get
rules
config ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {get} {domain}/kpi-management/api/v1.0/performance-incentive/rules   Lấy cấu hình chính sách
@apiDescription  Dịch vụ lấy cấu hình chính sách theo chức danh.
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName GetRulesPI

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiUse staff_title_query

@apiSuccess   {Array}  data  Danh sách cấu hình. Xem object <code>Config</code>

@apiSuccess   (Config)  {String}  id  Định danh cấu hình.
@apiUse staff_title_success
@apiSuccess   (Config)  {Number}  [unit_price]  Đơn giá. Chỉ <code>required</code> với các chức danh: <code>rbo, cso, rm</code>
@apiSuccess   (Config)  {String}  max_value_expression_type   Loại cấu hình lương kinh doanh tối đa.<br/>
<li><code>standard_sale_plan_total_by_staff_level</code>: Điểm chuẩn Saleplan theo cấp độ</li>
<li><code>max_performance_incentive_by_staff_level</code>: Lương kinh doanh tối đa theo cấp độ</li>
@apiSuccess   (Config)  {Object[]}  condition_groups    Group các điều kiện thỏa mãn để nhân viên được nhận lương kinh doanh. Các group điều kiện sử dụng toán tử <code>or(hoặc)</code> để kiểm tra thỏa mãn.
@apiSuccess   (Config)  {String}  condition_groups..id    Định danh của group.
@apiSuccess   (Config)  {Object[]}  condition_groups..conditions  Danh sách các điều kiện. Các điều kiện sử dụng toán tử <code>and(và)</code> để kiểm tra thỏa mãn. Chi tiết xem object <code>condition</code>
@apiUse   condition_obj_success


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
  "data": [
    {
      "id": "642fc65e2af4e1dbe42ea896",
      "staff_title": "RBO",
      "unit_price": 3000,
      "max_value_expression_type": "standard_sale_plan_total_by_staff_level",
      "conditions_groups": [
        {
          "id": "7830f766-786f-491c-9c8f-a3fb2cb11904",
          "conditions": [
            {
              "id": "ca304779-d2a4-4dc8-8613-08f068b63ce5",
              "key": "kpi_processed_amount",
              "operator": ">=",
              "value": {
                "first": 3
              }
            },
            {
              "id": "ef70ce34-dc51-4fec-9a12-fb3c00464324",
              "key": "sale_plan_completion_rate",
              "operator": ">=",
              "value": {
                "first": 70
              }
            }
          ]
        }
      ]
    }
  ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *DELETE
rules
config ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {DELETE} {domain}/kpi-management/api/v1.0/performance-incentive/rules/actions/clear   Xoá cấu hình chính sách theo chức danh
@apiDescription  Xoá cấu hình chính sách theo chức danh.
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName DeleteRulesPI

@apiParam  (Param:)   {String}  staff_title  Chức danh

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công"
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *History Performance Incentive ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {GET} {domain}/kpi-management/api/v1.0/performance-incentive/history   Lịch sử thay đổi theo danh sách chức danh
@apiDescription  Lịch sử thay đổi theo danh sách chức danh
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName HistoryPerformanceIncentive

@apiParam  (Param:)   {String}  staff_title  Chức danh (Ex: <code>'srbo,rbo,srm,cso,rm,cso'</code>)

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccess   {Array}  data  Danh sách lịch sử thay đổi
@apiSuccess   {String}  data.user_id  Mã nhân viên
@apiSuccess   {String}  data.staff_title  Mã chức danh
@apiSuccess   {String}  data.created_time  Thời gian cập nhật

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "action": "remove_performance_incentive_rule",
            "created_time": "2023-12-15T11:59:24.000Z",
            "staff_title": "SRBO",
            "user_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *History Performance Incentive ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.1 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {GET} {domain}/kpi-management/api/v1.0/performance-incentive/history   Lịch sử thay đổi theo danh sách chức danh
@apiDescription  Lịch sử thay đổi theo danh sách chức danh
@apiGroup PerformanceIncentive
@apiVersion 1.0.1
@apiName HistoryPerformanceIncentive

@apiParam  (Param:)   {String}  staff_title  Chức danh (Ex: <code>'srbo,rbo,srm,cso,rm,cso,sm,gdv_ksv,rm0'</code>)

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccess   {Array}  data  Danh sách lịch sử thay đổi
@apiSuccess   {String}  data.user_id  Mã nhân viên
@apiSuccess   {String}  data.staff_title  Mã chức danh
@apiSuccess   {String}  data.created_time  Thời gian cập nhật

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "action": "remove_performance_incentive_rule",
            "created_time": "2023-12-15T11:59:24.000Z",
            "staff_title": "SRBO",
            "user_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

#!/usr/bin/python
# -*- coding: utf8 -*-
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** List
variables ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {get} {domain}/kpi-management/api/v1.0/variables  List variables
@apiDescription  API lấy dánh sách chỉ tiêu theo chức danh nhân viên
@apiGroup PerformanceIncentive
@apiVersion 1.0.0
@apiName ListVariables

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiUse staff_title_query

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "656e165ff4c905f279f31df1",
      "key": "kpi_processed_amount",
      "label": "Số lượng chỉ tiêu thực hiện trong tháng báo cáo (không bao gồm chỉ tiêu cộng thêm)"
    }
  ]
}
"""

# !/usr/bin/python
# -*- coding: utf8 -*-
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** List
variables ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.1                                             *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {get} {domain}/kpi-management/api/v1.0/variables  List variables
@apiDescription  API lấy dánh sách chỉ tiêu theo chức danh nhân viên
@apiGroup PerformanceIncentive
@apiVersion 1.0.1
@apiName ListVariables

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiUse staff_title_query
@apiParam   (Query:)     {String}   [variable_type]  Kiểu chỉ tiêu theo từng cấu hình (<code>Bắt buộc với cấu hình giảm trừ lương kinh doanh</code>) <br>
                                                    <ul>
                                                        <li><code>condition_salary_deduction</code>: Variable điều kiện cấu hình giảm trừ lương</li>
                                                        <li><code>target_salary_deduction</code>: Chỉ tiêu bị giảm trừ lương</li>
                                                    </ul>

@apiSuccess {List}          data                          Dữ liệu trả về
@apiSuccess {String}        data.group_key                Mã nhóm
@apiSuccess {String}        data.group_name               Tên nhóm
@apiSuccess {String}        data.key                      Mã variables
@apiSuccess {String}        data.label                    Tên variable
@apiSuccess {String}        data.unit                     Đơn vị

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
     {
        "group_key": "i18n_general_target",
        "group_name": "Chỉ tiêu chung",
        "id": "656e165ff4c905f279f31df1",
        "key": "kpi_processed_amount",
        "label": "Số lượng chỉ tiêu chính được thực hiện",
        "unit": null
     }
  ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** List
variables ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.2                                             *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {get} {domain}/kpi-management/api/v1.0/variables  List variables
@apiDescription  API lấy dánh sách chỉ tiêu theo chức danh nhân viên
@apiGroup PerformanceIncentive
@apiVersion 1.0.2
@apiName ListVariables

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiUse staff_title_query
@apiParam   (Query:)     {String}   [variable_type]  Kiểu chỉ tiêu theo từng cấu hình (<code>Bắt buộc với cấu hình giảm trừ lương kinh doanh, điều kiện lương kinh doanh tối đa</code>) <br>
                                                    <ul>
                                                        <li><code>condition_salary_deduction</code>: Variable điều kiện cấu hình giảm trừ lương</li>
                                                        <li><code>target_salary_deduction</code>: Chỉ tiêu bị giảm trừ lương</li>
                                                        <li><code>condition_salary</code>: Variable điều kiện tính lương kinh doanh tối đa</li>
                                                    </ul>

@apiSuccess {List}          data                          Dữ liệu trả về
@apiSuccess {String}        data.group_key                Mã nhóm
@apiSuccess {String}        data.group_name               Tên nhóm
@apiSuccess {String}        data.key                      Mã variables
@apiSuccess {String}        data.label                    Tên variable
@apiSuccess {String}        data.unit                     Đơn vị

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
     {
        "group_key": "i18n_general_target",
        "group_name": "Chỉ tiêu chung",
        "id": "656e165ff4c905f279f31df1",
        "key": "kpi_processed_amount",
        "label": "Số lượng chỉ tiêu chính được thực hiện",
        "unit": null
     }
  ]
}
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: thong
    Company: M O B I O
    Date Created: 06/06/2024
"""

# -------------------------------------------
# Kiểm tra userCBBH thỏa mãn điều kiện kpi
# version: 1.0.0
# -------------------------------------------
"""
@api {Queue} kpi-management-check-user-condition  [QUEUE] Kiểm tra CBBH thỏa mãn điều kiện kpi
@apiDescription Kiểm tra user thỏa mãn điều kiện kpi
@apiGroup Integrate Workflow
@apiVersion 1.0.0
@apiName IntegrateWFCheckUserCondition

@apiParam	(Input:)	    {String}	user_id			            ID nhân viên
@apiParam	(Input:)	    {String}	merchant_id			        ID Merchant
@apiParam	(Input:)	    {List}	    conditions			        Danh sách điều kiện cần kiểm tra

@apiParam	(Input:)  {Array}	        conditions.values		    Giá trị cần so sánh
@apiParam	(Input:)  {String}	        conditions.criteria_key		Các key để kiểm tra
                                                                    Alow values:
                                                                    <ul>
                                                                        <li><code>cri_completion_rate </code>: Tỷ lệ hoàn thành</li>
                                                                        <li><code>cri_completion_rate_staff_multiple </code>: Tỷ lệ cán bộ hoàn thành KPI</li>
                                                                    </ul>

@apiParam	(Input:)  {String}	        conditions.operator_key		Các toán tử
                                                                    Alow value:
                                                                    <code> 
                                                                        "op_is_between", "op_is_greater_equal", "op_is_in",
                                                                         "op_is_equal", "op_is_greater", "op_is_has", 
                                                                         "op_is_has_not", "op_is_less_equal", "op_is_less",
                                                                         "op_is_has_in" 
                                                                    </code>       
                                                                    <ul>
                                                                        <li><code>op_is_between</code>: Thỏa mãn khoảng giá trị </li>
                                                                        <li><code>op_is_greater_equal</code>: Lớn hơn hoặc bằng giá trị values[0]</li>
                                                                        <li><code>op_is_in</code>: Bằng các giá trị nằm trong values</li>
                                                                        <li><code>op_is_equal</code>: Bằng các giá trị values[0]</li>
                                                                        <li><code>op_is_greater</code>: lớn hơn giá trị values[0]</li>
                                                                        <li><code>op_is_has</code>: So sánh contains chuỗi với giá trị values[0]</li>
                                                                        <li><code>op_is_less_equal</code>: nhỏ hơn hoặc bằng giá trị values[0]</li>
                                                                        <li><code>op_is_less</code>: nhỏ hơn giá trị values[0]</li>
                                                                        <li><code>op_is_has_in</code>: So sánh contains chuỗi với các giá trị nằm trong values</li>
                                                                        <li><code>op_is_multiple</code>: Kiểm tra tất cả các điều kiện của giá trị trong values</li>
                                                                    </ul>       
@apiParam	(Input:)	    {Object}	callback			        Thông tin callback                                                                      
@apiParam	(Input:)	    {String}	callback.queue_name			Tên Topic                                                      
@apiParam	(Input:)	    {Object}	callback.data			    Data cần bắn callback                                                 

@apiParamExample {json} Input example
{
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"user_id": "bcf2fe46-aca6-4ab6-9cdc-2b2dd170f20b",
	"conditions": [
	    {
	        "criteria_key": "cri_completion_rate",
            "operator_key": "op_is_greater",
            "values": [0.5]
	    },
	    {
            "operator_key": "op_is_multiple",
            "criteria_key": "cri_completion_rate_staff_multiple",
            "display": [],
            "values": [{
                "criteria_key": "cri_scope",
                "operator_key": "op_is_equal",
                "values": [
                    "system" //  system: Toàn hệ thống - region:Khu vực - department: Đơn vị kinh doanh
                ],
                "display": []
            }, {
                "criteria_key": "cri_completion_rate",
                "operator_key": "op_is_greater",
                "values": [0.5],
                "display": []
            }]
        }
	],
	"callback": {
		"queue_name": "topic-callback",
		"data": {
			"field1": "value1"
		}
	}
}

@apisuccess	(Callback:)	    {String}	status			            Kết quả kiểm tra
                                                                    <ul>
                                                                        <li><code>success</code>: Thỏa mãn điều kiện</li>
                                                                        <li><code>fail</code>: Không thỏa mãn điều kiện</li>
                                                                    </ul>
@apisuccess	(Callback:)	    {Object}	data_callback			    Thông tin data callback được nhận ở input

@apiSuccessExample {json} Callback Example
{
    "status": "success",
    "data_callback": {
        "field1": "value1"
    }
}
"""

# -------------------------------------------
# Lấy thông tin kpi của CBBH
# version: 1.0.0
# -------------------------------------------
"""
@api {Queue} kpi-management-get-kpi-user  [QUEUE] Lấy thông tin kpi của CBBH
@apiDescription Lấy thông tin KPI của CBBH
@apiGroup Integrate Workflow
@apiVersion 1.0.0
@apiName IntegrateWFGetKpiUser

@apiParam	(Input:)	    {String}	user_id			            ID nhân viên
@apiParam	(Input:)	    {String}	merchant_id			        ID Merchant
@apiParam	(Input:)	    {String}	[report_month]			    Tháng báo cáo (Format: %Y-%m). Mặc định là tháng hiện tại
       
@apiParam	(Input:)	    {Object}	callback			        Thông tin callback                                                                      
@apiParam	(Input:)	    {String}	callback.queue_name			Tên Topic                                                      
@apiParam	(Input:)	    {Object}	callback.data			    Data cần bắn callback                                                 

@apiParamExample {json} Input example
{
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"user_id": "bcf2fe46-aca6-4ab6-9cdc-2b2dd170f20b",
	"callback": {
		"queue_name": "topic-callback",
		"data": {
			"field1": "value1"
		}
	}
}

@apisuccess	(Callback:)	    {Object}	data			            Thông tin kpi của CBBH
@apisuccess	(Callback:)	    {String}	data.month_nearest          Tháng gần nhất (Format: <code>%m/%Y</code>)
@apisuccess	(Callback:)	    {String}	data.day_nearest            Ngày gần nhất (Format: <code>%d/%m/%Y</code>)
@apisuccess	(Callback:)	    {String}	data.link_report            Link báo cáo
@apisuccess	(Callback:)	    {Number}	data.number_staff           Tổng CBBH quản lý
@apisuccess	(Callback:)	    {Number}	data.number_staff_complete  Tổng CBBH quản lý hoàn thành kpi
@apisuccess	(Callback:)	    {Number}	data.number_staff_incomplete  Tổng CBBH quản lý chưa hoàn thành kpi
@apisuccess	(Callback:)	    {Number}	data.number_rbo_managed     Số lượng RBO quản lý
@apisuccess	(Callback:)	    {Number}	data.number_rbo_managed_incomplete     Số lượng RBO chưa hoàn thành kpi
@apisuccess	(Callback:)	    {Number}	data.completion_rate_rbo_managed_incomplete     Tỷ lệ RBO chưa hoàn thành kpi

@apisuccess	(Callback:)	    {Number}	data.number_srbo_managed     Số lượng SRBO quản lý
@apisuccess	(Callback:)	    {Number}	data.number_srbo_managed_incomplete     Số lượng SRBO chưa hoàn thành kpi
@apisuccess	(Callback:)	    {Number}	data.completion_rate_srbo_managed_incomplete     Tỷ lệ SRBO chưa hoàn thành kpi

@apisuccess	(Callback:)	    {Number}	data.number_cso_managed     Số lượng CSO quản lý
@apisuccess	(Callback:)	    {Number}	data.number_cso_managed_incomplete     Số lượng CSO chưa hoàn thành kpi
@apisuccess	(Callback:)	    {Number}	data.completion_rate_cso_managed_incomplete     Tỷ lệ CSO chưa hoàn thành kpi

@apisuccess	(Callback:)	    {Number}	data.number_rm_managed     Số lượng RM quản lý
@apisuccess	(Callback:)	    {Number}	data.number_rm_managed_incomplete     Số lượng RM chưa hoàn thành kpi
@apisuccess	(Callback:)	    {Number}	data.completion_rate_rm_managed_incomplete     Tỷ lệ RM chưa hoàn thành kpi

@apisuccess	(Callback:)	    {Number}	data.number_srm_managed     Số lượng SRM quản lý
@apisuccess	(Callback:)	    {Number}	data.number_srm_managed_incomplete     Số lượng SRM chưa hoàn thành kpi
@apisuccess	(Callback:)	    {Number}	data.completion_rate_srm_managed_incomplete     Tỷ lệ SRM chưa hoàn thành kpi

@apisuccess	(Callback:)	    {Object}	data_callback			    Thông tin data callback được nhận ở input

@apiSuccessExample {json} Callback Example
{
    "data": {
        "fullname": "Nguyễn Văn A",
        "username": "HA001",
        "staff_title": "RBO"
    },
    "data_callback": {
        "field1": "value1"
    }
}
"""
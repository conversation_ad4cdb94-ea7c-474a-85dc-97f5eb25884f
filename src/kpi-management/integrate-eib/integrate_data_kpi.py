#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: nguyenthong
    Company: M O B I O
    Date Created: 10/10/2023
"""

# -------------------------------------------
# Config KPI Targets
# version: 1.0.0
# -------------------------------------------
"""
@api {Queue} kpi-management-targets  [QUEUE] Cấu hình chỉ tiêu
@apiDescription Thông tin chỉ tiêu
@apiGroup Kpi Etl Data
@apiVersion 1.0.0
@apiName kpi-management-targets

@apiParam	(Input:)	    {String}	business_code			    Mã nghiệp vụ. Mặc định:<code>targets</code>
@apiParam	(Input:)	    {String}	code			            Mã chỉ tiêu
@apiParam	(Input:)	    {String}	name			            Tên chỉ tiêu
@apiParam	(Input:)	    {Array}	    staff_title			        Danh sách chức danh áp dụng chỉ tiêu
@apiParam	(Input:)		{String}	data_type			        Kiểm dữ liệu của chỉ tiêu (Example: String, Integer, ...)
@apiParam	(Input:)		{Integer}	type		                Loại chỉ tiêu
                                                                    <ul>
                                                                        <li><code>0</code> : Điểm trừ</li>
                                                                        <li><code>1</code> : Chỉ tiêu chính</li>
                                                                        <li><code>2</code> : Chỉ tiêu cộng thêm</li>
                                                                    </ul>
@apiParam	(Input:)		{String}	merchant_id		            ID merchant_id
@apiParam	(Input:)		{Datetime}	action_time		            thời gian cập nhật. (Format: <code>%Y-%m-%d %H:%M:%S</code>)
@apiParam	(Input:)	    {Object}	callback			                Thông tin callback                                                                      
@apiParam	(Input:)	    {String}	callback.queue_name			        Tên Topic                                                      
@apiParam	(Input:)	    {Object}	callback.data			            Data cần bắn callback                                                 


@apiParamExample {json} Input example
{
	"business_code": "targets",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"code": "DSGN_TDH",
	"name": "Doanh số giải ngân trung dài hạn",
	"staff_title": ["RBO"],
	"data_type": "Float64",
	"type": 1,
	"action_time": "2023-10-10 10:10:10",
	"callback": {
		"queue_name": "topic-callback",
		"data": {
			"field1": "value1"
		}
	}
}
"""

# -------------------------------------------
# Config PointConvert
# version: 1.0.0
# -------------------------------------------
"""
@api {Queue} kpi-management-point-convert  [QUEUE] Quy đổi điểm
@apiDescription Thông tin quy đổi điểm của chỉ tiêu
@apiGroup Kpi Etl Data
@apiVersion 1.0.0
@apiName kpi-point-convert

@apiParam	(Input:)	    {String}	business_code			    Mã nghiệp vụ. Mặc định:<code>point_convert</code>
@apiParam	(Input:)	    {String}	target_code			        Mã chỉ tiêu
@apiParam	(Input:)	    {String}	unit			            Đơn vị tính
@apiParam	(Input:)	    {String}	staff_title			        Chức danh nhân viên
@apiParam	(Input:)	    {Float}	    [converted_sale_min]		Doanh số quy đổi min
@apiParam	(Input:)	    {Float}	    [converted_sale_max]		Doanh số quy đổi max
@apiParam	(Input:)	    {Float}	    [point]			            Số điểm quy đổi
@apiParam	(Input:)		{Integer}	type		                Loại điểm quy đổi
                                                                    <ul>
                                                                        <li><code>0</code> : Điểm trừ</li>
                                                                        <li><code>1</code> : Chỉ tiêu chính</li>
                                                                        <li><code>2</code> : Chỉ tiêu cộng thêm</li>
                                                                    </ul>
@apiParam	(Input:)		{String}	merchant_id		            ID merchant_id
@apiParam	(Input:)		{Datetime}	action_time		            thời gian cập nhật. (Format: <code>%Y-%m-%d %H:%M:%S</code>)
@apiParam	(Input:)	    {Object}	callback			                Thông tin callback                                                                      
@apiParam	(Input:)	    {String}	callback.queue_name			        Tên Topic                                                      
@apiParam	(Input:)	    {Object}	callback.data			            Data cần bắn callback

@apiParamExample {json} Input example
{
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"business_code": "point_convert",
	"target_code": "toi_khdnl_tr",
	"unit": "%",
	"staff_title": "RBO",
	"converted_sale_min": 5,
	"converted_sale_max": 10,
	"point": 100,
	"type": 1,
	"action_time": "2023-10-10 10:10:10",
    "callback": {
		"queue_name": "topic-callback",
		"data": {
			"field1": "value1"
		}
	}
}
"""


# -------------------------------------------
# Config Staff Manager
# version: 1.0.0
# -------------------------------------------
"""
@api {Queue} kpi-management-staff-manager  [QUEUE] Nhân viên - Quản lý
@apiDescription Mối quan hệ giữa SRBO vả RBO, giữa RM và SRM
@apiGroup Kpi Etl Data
@apiVersion 1.0.0
@apiName StaffManager

@apiParam	(Input:)	    {String}	business_code			    Mã nghiệp vụ. Mặc định:<code>staff_manager</code>
@apiParam	(Input:)	    {String}	report_date			        Thời gian báo cáo. (Format: <code>%Y-%m-%d</code>)
@apiParam	(Input:)	    {String}	report_month			    Tháng báo cáo. (Format: <code>%Y-%m</code>)
@apiParam	(Input:)	    {Integer}	type			            Loại mối quan hệ
                                                                    <ul>
                                                                        <li><code>1</code> : RBO - SRBO</li>
                                                                        <li><code>2</code> : RM - SRM</li>
                                                                    </ul>
@apiParam	(Input:)	    {String}	staff_code			        Mã nhân viên
@apiParam	(Input:)	    {String}	staff_name			        Tên nhân viên
@apiParam	(Input:)	    {String}	level			            Cấp độ nhân viên
@apiParam	(Input:)	    {String}	manager_code			    Mã nhân viên quản lý
@apiParam	(Input:)	    {String}	manager_name			    Tên nhân viên quản lý
@apiParam	(Input:)		{String}	merchant_id		            ID merchant_id
@apiParam	(Input:)		{Datetime}	action_time		            thời gian cập nhật. (Format: <code>%Y-%m-%d %H:%M:%S</code>)
@apiParam	(Input:)	    {Object}	callback			                Thông tin callback                                                                      
@apiParam	(Input:)	    {String}	callback.queue_name			        Tên Topic                                                      
@apiParam	(Input:)	    {Object}	callback.data			            Data cần bắn callback

@apiParamExample {json} Input example
{
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"business_code": "staff_manager",
	"report_date": "2023-10-10",
	"report_month": "2023-10",
	"type": 1,
	"staff_code": "210201844",
	"staff_name": "Thao Nguyễn Xuân",
	"level": "RBO1",
	"manager_code": "900000385",
	"manager_name": "Luân Nguyễn Đức",
	"action_time": "2023-10-10 10:10:10",
    "callback": {
		"queue_name": "topic-callback",
		"data": {
			"field1": "value1"
		}
	}
}
"""

# -------------------------------------------
# Config SalesPlan
# version: 1.0.0
# -------------------------------------------
"""
@api {Queue} kpi-management-staff-sales-plan  [QUEUE] Chỉ tiêu nhân viên
@apiDescription Ghi nhận chỉ tiêu của nhân viên (RBO,SRBO,RM,SRM, ..)
@apiGroup Kpi Etl Data
@apiVersion 1.0.0
@apiName SalesPlan

@apiParam	(Input:)	    {String}	report_date			        Thời gian báo cáo. (Format: <code>%Y-%m-%d</code>)
@apiParam	(Input:)	    {String}	report_month			    Chỉ tiêu cho tháng. (Format: <code>%Y-%m</code>)
@apiParam	(Input:)	    {String}	staff_code			        Mã nhân viên
@apiParam	(Input:)	    {String}	staff_title			        Chức danh nhân viên
@apiParam	(Input:)	    {String}	level			            Cấp bậc
@apiParam	(Input:)	    {String}	region			            Khu vực
@apiParam	(Input:)	    {String}	branch_code			        Mã ĐVKD, phòng giao dịch
@apiParam	(Input:)	    {String}	date_of_join			    Ngày hiệu lực. (Format: <code>%Y-%m-%d</code>)
@apiParam	(Input:)	    {String}	date_of_month			    Ngày vào làm việc trong tháng. (Format: <code>%Y-%m-%d</code>)
@apiParam	(Input:)	    {Integer}	actual_number_day			Số ngày làm việc thực tế
@apiParam	(Input:)	    {Float}	    adjustment_coefficient		Hệ số điều chỉnh
@apiParam	(Input:)	    {Float}	    management_coefficient		Hệ số quản lý
@apiParam	(Input:)	    {Float}	    sales_plan		            Tổng điểm chỉ tiêu giao
@apiParam	(Input:)	    {Array}	    targets		                Chỉ tiêu tháng
@apiParam	(Input:)	    {String}	targets.code		        Mã chỉ tiêu
@apiParam	(Input:)	    {Float}	    targets.value		        Giá trị chỉ tiêu giao
@apiParam	(Input:)		{String}	merchant_id		            ID merchant_id
@apiParam	(Input:)		{Datetime}	action_time		            thời gian cập nhật. (Format: <code>%Y-%m-%d %H:%M:%S</code>)

@apiParamExample {json} Input example
{
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"report_date": "2023-10-10",
	"report_month": "2023-10",
	"staff_code": "900002954",
	"staff_title": "RBO",
	"level": "RBO 4",
	"region": "Miền Bắc",
	"branch_code": "1001",
	"date_of_join": "2023-10-10",
	"date_of_month": "2023-10-10",
	"actual_number_day": 10,
	"adjustment_coefficient": 1,
	"management_coefficient": 1,
	"sales_plan": 1,
	"targets": [
	    {
	        "code": "dsgn_tdh",
	        "value": 420
	    },
	    {
	        "code": "trdn_nhbq",
	        "value": 180
	    },
	    {
	        "code": "the_td",
	        "value": 36
	    }
	],
	"action_time": "2023-10-10 10:10:10"
}
"""

# -------------------------------------------
# TargetsPerformance
# version: 1.0.0
# -------------------------------------------
"""
@api {Queue} kpi-management-targets-performance  [QUEUE] Kết quả thực hiện theo ngày
@apiDescription Ghi nhận báo cáo kết quả thực hiện của nhân viên theo ngày
@apiGroup Kpi Etl Data
@apiVersion 1.0.0
@apiName TargetsPerformance

@apiParam	(Input:)	    {String}	report_date			        Ngày báo cáo. (Format: <code>%Y-%m-%d</code>)
@apiParam	(Input:)	    {String}	staff_code			        Mã nhân viên
@apiParam	(Input:)	    {String}	staff_title			        Chức danh nhân viên
@apiParam	(Input:)	    {String}	level			            Cấp bậc
@apiParam	(Input:)	    {String}	region			            Khu vực
@apiParam	(Input:)	    {String}	branch_code			        Mã ĐVKD, phòng giao dịch
@apiParam	(Input:)	    {String}	date_of_join			    Ngày hiệu lực. (Format: <code>%Y-%m-%d</code>)
@apiParam	(Input:)	    {String}	date_of_month			    Ngày vào làm việc trong tháng. (Format: <code>%Y-%m-%d</code>)
@apiParam	(Input:)	    {Integer}	actual_number_day			Số ngày làm việc thực tế
@apiParam	(Input:)	    {Float}	    total_point_performance		Tổng điểm thực hiện
@apiParam	(Input:)	    {Float}	    sales_plan			        Tổng điểm chỉ tiêu giao
@apiParam	(Input:)	    {Float}	    completion_rate			    Tỷ lệ hoàn thành
@apiParam	(Input:)	    {Array}	    targets		                Kết quả thực hiện chỉ tiêu
@apiParam	(Input:)	    {String}	targets.code		        Mã chỉ tiêu
@apiParam	(Input:)	    {Float}	    targets.value		        Kết quả thực hiện
@apiParam	(Input:)		{String}	merchant_id		            ID merchant_id
@apiParam	(Input:)		{Datetime}	action_time		            thời gian cập nhật. (Format: <code>%Y-%m-%d %H:%M:%S</code>)

@apiParamExample {json} Input example
{
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"report_date": "2023-10-10",
	"staff_code": "900002954",
	"staff_title": "RBO",
	"level": "RBO 4",
	"region": "Miền Bắc",
	"branch_code": "1001",
	"date_of_join": "2023-10-10",
	"date_of_month": "2023-10-10",
	"actual_number_day": 10,
	"total_point_performance": 10,
	"sales_plan": 10,
	"completion_rate": 10,
	"targets": [
	    {
	        "code": "ds_gntdh",
	        "value": 420
	    },
	    {
	        "code": "qd_dsgntdh",
	        "value": 0
	    },
	    {
	        "code": "dsgn_tdh",
	        "value": 0
	    }
	],
	"action_time": "2023-10-10 10:10:10"
}
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: nguyenthong
    Company: M O B I O
    Date Created: 03/11/2023
"""

# -------------- Xuất file data raw --------------------

# @apiVersion 1.0.0

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/export/data-raw  Xuất file báo cáo KPI
@apiDescription Xuất báo cáo data raw KPI
@apiGroup Export EIB
@apiVersion 1.0.0
@apiName ExportDataRaw

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   business_unit               Khối kinh doanh
                                                              <ul>
                                                                <li><code>KHCN</code> : <PERSON>h<PERSON><PERSON> hàng cá nhân</li>
                                                                <li><code>KHDN</code> : <PERSON><PERSON><PERSON><PERSON> hàng doanh nghiệp</li>
                                                              </ul>
@apiParam  (Body:)     {String}   [area]                      Khu vực
@apiParam  (Body:)     {String}   staff_title                 Chức danh
@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo (Format: %Y-%m)
@apiParam  (Body:)     {Array}    email                       Danh sách Email nhận kết quả

@apiParamExample {json} Body example
{
    "staff_title": "RBO",
    "business_unit": "KHCN",
    "report_month": "2023-10",
    "email": ["<EMAIL>"]
}

@apiSuccess {Object}            data                          Dữ liệu trả về 
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công."
}
"""

# @apiVersion 1.0.1

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/export/data-raw  Xuất file báo cáo KPI
@apiDescription Xuất báo cáo data raw KPI
@apiGroup Export EIB
@apiVersion 1.0.1
@apiName ExportDataRaw

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   business_unit               Khối kinh doanh
                                                              <ul>
                                                                <li><code>KHCN</code> : Khách hàng cá nhân</li>
                                                                <li><code>KHDN</code> : Khách hàng doanh nghiệp</li>
                                                              </ul>
@apiParam  (Body:)     {String}   [area]                      Khu vực
@apiParam  (Body:)     {String}   staff_title                 Chức danh
                                                              <ul>
                                                                <li><code>RBO</code> : RBO</li>
                                                                <li><code>SRBO</code> : SRBO</li>
                                                                <li><code>CSO</code> : CSO</li>
                                                                <li><code>RM</code> : RM </li>
                                                                <li><code>SRM</code> : SRM </li>
                                                                <li><code>GDV_KSV</code> : GDV_KSV </li>
                                                                <li><code>SM</code> : SM </li>
                                                              </ul>
@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo (Format: %Y-%m)
@apiParam  (Body:)     {Array}    email                       Danh sách Email nhận kết quả

@apiParamExample {json} Body example
{
    "staff_title": "RBO",
    "business_unit": "KHCN",
    "report_month": "2023-10",
    "email": ["<EMAIL>"]
}

@apiSuccess {Object}            data                          Dữ liệu trả về 
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công."
}
"""
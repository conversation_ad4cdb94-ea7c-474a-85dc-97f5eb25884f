#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: nguyenthong
    Company: M O B I O
    Date Created: 31/10/2023
"""
# ---------------------------------------------
# B<PERSON><PERSON> cáo tỷ lệ hoàn thành
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/completion-rate  Tỷ lệ hoàn thành
@apiDescription Báo cáo tỷ lệ hoàn thành theo cá nhân từng chức danh
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardCompletionRate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>CSO</code></li>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRM</code></li>
                                                              </ul>

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO"
}

@apiSuccess {Object}            data                          Dữ liệu trả về 
@apiSuccess {Float}             data.completion_rate          Tỷ lệ % hoàn thành   
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "completion_rate": 50.3,
    }
}
"""

# ---------------------------------------------
# Báo cáo tổng quan kết quả KPI theo điểm thực hiện
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/performance-score/overview  KPI theo điểm thực hiện
@apiDescription Báo cáo tổng quan kết quả KPI theo điểm thực hiện
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardPerformanceOverview

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   [report_date]               Ngày báo cáo (Format: <code>%Y-%m-%d</code>) (Default: Ngày hiện tại)
@apiParam  (Body:)     {String}   [staff_title]               Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>CSO</code></li>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRM</code></li>
                                                              </ul>
@apiParam  (Body:)     {String}   [staff_id]                  ID nhân viên                                                              

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO",
    "staff_id": "c6c37896-940f-4c97-8978-13f8f9ff9704"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Float}             data.target_main              Chỉ tiêu chính
@apiSuccess {Float}             data.target_secondary         Chỉ tiêu phụ   
@apiSuccess {Float}             data.target_additional        Chỉ tiêu cộng thêm   
@apiSuccess {Float}             data.sub_point                Điểm trừ   
@apiSuccess {Float}             data.total                    Tổng điểm thực hiện (Chỉ tiêu chính + chỉ tiêu cộng thêm - điểm trừ)   
@apiSuccess {Float}             data.completion_rate          Tỷ lệ hoàn thành   
@apiSuccess {Float}             data.gap_point                Điểm gap
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "target_main": 50,
        "target_secondary": 10,
        "target_additional": 32,
        "sub_point": 10,
        "total": 82,
        "gap_point": 10,
        "completion_rate": 82
    }
}
"""

# ---------------------------------------------
# Báo cáo Đơn giá dự kiến
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/estimate-price  Đơn giá dự kiến
@apiDescription Đơn giá dự kiến
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardEstimatePrice

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>CSO</code></li>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRM</code></li>
                                                              </ul>

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Float}             data.estimate_price           Đơn giá dự kiến
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "estimate_price": 50.2,
    }
}
"""

# ---------------------------------------------
# Báo cáo Lương kinh doanh dự kiến
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/expected-salary  Lương kinh doanh dự kiến
@apiDescription Mức lương kinh doanh dự kiến theo chức danh
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardExpectedSalary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>CSO</code></li>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRM</code></li>
                                                              </ul>

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Float}             data.expected_salary          Mức lương kinh doanh dự kiến
@apiSuccess {Float}             [data.max_salary]             Lương kinh doanh tối đa
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "max_salary": 50.2,
        "expected_salary": 50.2,
    }
}
"""

# ---------------------------------------------
# Kết quả hoàn thành theo tổng và từng chỉ tiêu
# version: 1.0.0
# ---------------------------------------------
# version: 1.0.1
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/result-complete-detail  Kết quả hoàn thành chi tiết
@apiDescription Tiến độ hoàn thành chỉ tiêu kinh doanh (Cá nhân)
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardResultCompleteDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   [report_date]               Ngày báo cáo (Format: <code>%Y-%m-%d</code>) (Default: Ngày hiện tại)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>CSO</code></li>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRM</code></li>
                                                                <li><code>KHCN</code></li>
                                                                <li><code>DVKH</code></li>
                                                                <li><code>KHDN</code></li>
                                                                <li><code>GD-DVKD</code></li>
                                                              </ul>
@apiParam  (Body:)     {String}   [staff_id]                  ID nhân viên                                                              

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO",
    "staff_id": "c00a00b2-8a7e-4cbc-b184-dd94930cb07e"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Array}             data.detail            Kết quả chi tiết theo chỉ tiêu
@apiSuccess {Object}            data.total                    Kết quả chi tiết theo chỉ tiêu
@apiSuccess {Float}             data.total.sales_plan         Tổng điểm chỉ tiêu
@apiSuccess {Float}             data.total.total_point_performance         Tổng điểm thực hiện
@apiSuccess {Float}             data.total.completion_rate    Tỷ lệ hoàn thành
@apiSuccess (detail)  {String}   group_target           Nhóm chỉ tiêu
                                                              <ul>
                                                                <li><code>main</code> : Chỉ tiêu chính</li>
                                                                <li><code>additional</code> : Chỉ tiêu ghi nhận thêm</li>
                                                                <li><code>sub</code> : Điểm trừ</li>
                                                              </ul>
@apiSuccess (detail)  {Array}   targets                Thông tin chi tiết từng chỉ tiêu                                                              
@apiSuccess (detail)  {String}  targets.code           Mã chỉ tiêu                                                              
@apiSuccess (detail)  {String}  targets.name           Tên chỉ tiêu                                                              
@apiSuccess (detail)  {String}  targets.unit           Đơn vị tính                                                              
@apiSuccess (detail)  {Float}   targets.sales_plan     Chỉ tiêu theo điểm                                                              
@apiSuccess (detail)  {Float}   targets.revenue        Chỉ tiêu theo doanh số                                                              
@apiSuccess (detail)  {Float}   targets.result_sales_plan        Kết quả thực hiện theo điểm                                                              
@apiSuccess (detail)  {Float}   targets.result_revenue Kết quả thực hiện theo doanh số                                                              
@apiSuccess (detail)  {Float}   targets.comparative_sales Doanh số kỳ so sánh                                                              
@apiSuccess (detail)  {Float}   targets.net_increase      Tăng ròng                                                              
@apiSuccess (detail)  {Float}   targets.completion_rate   Tỷ lệ hoàn thành                                                              
@apiSuccess (detail)  {ListObject}   targets.target_sub      Thông tin chỉ tiêu con (Các thông tin tương tự chỉ tiêu cha)

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "group_target": "main",
             "targets": [
                {
                    "code": "AB01",
                    "name": "Doanh số giải ngân TDH từ KHCN",
                    "unit": "VND",
                    "sales_plan": 120,
                    "revenue": 12000000,
                    "result_sales_plan": 10,
                    "result_revenue": 10000000,
                    "completion_rate": 80,
                    "comparative_sales": 70,
                    "net_increase": 0.1,
                    "target_sub": [
                        {
                            "code": "doanh_so_bao_hiem_cn",
                            "comparative_sales": null,
                            "completion_rate": null,
                            "name": "PHÍ BHNT",
                            "net_increase": null,
                            "parent_code": "dt_bh",
                            "result_revenue": 0.0,
                            "result_sales_plan": 0.0,
                            "revenue": null,
                            "sales_plan": null,
                            "unit": "VNĐ"
                        },
                        {
                            "code": "phi_bao_hiem_phi_nhan_tho",
                            "comparative_sales": null,
                            "completion_rate": null,
                            "name": "PHÍ BH PHI NT",
                            "net_increase": null,
                            "parent_code": "dt_bh",
                            "result_revenue": 0.0,
                            "result_sales_plan": 0.0,
                            "revenue": null,
                            "sales_plan": null,
                            "unit": "VNĐ"
                        }
                    ]
                }
             ],
             "total": {
                 "sales_plan": 300,
                "total_point_performance": 320,
                "completion_rate": 89
            }    
        },
        {
            "group_target": "additional",
             "targets": [
                {
                    "code": "AB01",
                    "name": "Doanh số giải ngân TDH từ KHCN",
                    "add_point": 120, # Điểm cộng
                    "result_revenue": 10000000 # Doanh số thực hiện
                }
             ],
             "total": {
                 "add_point": 300, # Tổng điểm cộng
                 "total_revenue": 3200000 # Tổng doanh số thực hiện
            }    
        },
        {
            "group_target": "sub",
             "targets": [
                {
                    "code": "AB01",
                    "name": "Doanh số giải ngân TDH từ KHCN",
                    "sub_point": 120, # Điểm trừ
                    "result_revenue": 100000 # Doanh số bị trừ
                }
             ],
             "total": {
                 "sub_point": 300, # D
                 "total_revenue": 320
            }    
        }
    ]
}
"""

# version: 1.0.0
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/result-complete-detail  Kết quả hoàn thành chi tiết
@apiDescription Tiến độ hoàn thành chỉ tiêu kinh doanh (Cá nhân)
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardResultCompleteDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   [report_date]               Ngày báo cáo (Format: <code>%Y-%m-%d</code>) (Default: Ngày hiện tại)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>CSO</code></li>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRM</code></li>
                                                                <li><code>KHCN</code></li>
                                                                <li><code>DVKH</code></li>
                                                                <li><code>KHDN</code></li>
                                                                <li><code>GD-DVKD</code></li>
                                                              </ul>
@apiParam  (Body:)     {String}   [staff_id]                  ID nhân viên                                                              

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO",
    "staff_id": "c00a00b2-8a7e-4cbc-b184-dd94930cb07e"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Array}             data.detail            Kết quả chi tiết theo chỉ tiêu
@apiSuccess {Object}            data.total                    Kết quả chi tiết theo chỉ tiêu
@apiSuccess {Float}             data.total.sales_plan         Tổng điểm chỉ tiêu
@apiSuccess {Float}             data.total.total_point_performance         Tổng điểm thực hiện
@apiSuccess {Float}             data.total.completion_rate    Tỷ lệ hoàn thành
@apiSuccess (detail)  {String}   group_target           Nhóm chỉ tiêu
                                                              <ul>
                                                                <li><code>main</code> : Chỉ tiêu chính</li>
                                                                <li><code>additional</code> : Chỉ tiêu ghi nhận thêm</li>
                                                                <li><code>sub</code> : Điểm trừ</li>
                                                              </ul>
@apiSuccess (detail)  {Array}   targets                Thông tin chi tiết từng chỉ tiêu                                                              
@apiSuccess (detail)  {String}  targets.code           Mã chỉ tiêu                                                              
@apiSuccess (detail)  {String}  targets.name           Tên chỉ tiêu                                                              
@apiSuccess (detail)  {String}  targets.unit           Đơn vị tính                                                              
@apiSuccess (detail)  {Float}   targets.sales_plan     Chỉ tiêu theo điểm                                                              
@apiSuccess (detail)  {Float}   targets.revenue        Chỉ tiêu theo doanh số                                                              
@apiSuccess (detail)  {Float}   targets.result_sales_plan        Kết quả thực hiện theo điểm                                                              
@apiSuccess (detail)  {Float}   targets.result_revenue Kết quả thực hiện theo doanh số                                                              
@apiSuccess (detail)  {Float}   targets.comparative_sales Doanh số kỳ so sánh                                                              
@apiSuccess (detail)  {Float}   targets.net_increase      Tăng ròng                                                              
@apiSuccess (detail)  {Float}   targets.completion_rate   Tỷ lệ hoàn thành                                                              
            
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "group_target": "main",
             "targets": [
                {
                    "code": "AB01",
                    "name": "Doanh số giải ngân TDH từ KHCN",
                    "unit": "VND",
                    "sales_plan": 120,
                    "revenue": 12000000,
                    "result_sales_plan": 10,
                    "result_revenue": 10000000,
                    "completion_rate": 80,
                    "comparative_sales": 70,
                    "net_increase": 0.1
                }
             ],
             "total": {
                 "sales_plan": 300,
                "total_point_performance": 320,
                "completion_rate": 89
            }    
        },
        {
            "group_target": "additional",
             "targets": [
                {
                    "code": "AB01",
                    "name": "Doanh số giải ngân TDH từ KHCN",
                    "add_point": 120, # Điểm cộng
                    "result_revenue": 10000000 # Doanh số thực hiện
                }
             ],
             "total": {
                 "add_point": 300, # Tổng điểm cộng
                 "total_revenue": 3200000 # Tổng doanh số thực hiện
            }    
        },
        {
            "group_target": "sub",
             "targets": [
                {
                    "code": "AB01",
                    "name": "Doanh số giải ngân TDH từ KHCN",
                    "sub_point": 120, # Điểm trừ
                    "result_revenue": 100000 # Doanh số bị trừ
                }
             ],
             "total": {
                 "sub_point": 300, # D
                 "total_revenue": 320
            }    
        }
    ]
}
"""

# ---------------------------------------------
# GAP theo tổng và từng chỉ tiêu
# version: 1.0.0
# ---------------------------------------------

# version: 1.0.1
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/gap-targets  Gap theo tổng và từng chỉ tiêu
@apiDescription Báo cáo kết quả hoàn thành theo tổng và từng chỉ tiêu
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardGapTargets

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   [report_date]               Ngày báo cáo (Format: <code>%Y-%m-%d</code>) (Default: Ngày hiện tại)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>CSO</code></li>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRM</code></li>
                                                                <li><code>KHCN</code></li>
                                                                <li><code>DVKH</code></li>
                                                                <li><code>KHDN</code></li>
                                                                <li><code>GD-DVKD</code></li>
                                                              </ul>
@apiParam  (Body:)     {String}   [staff_id]                  ID nhân viên                                                              

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO",
    "staff_id": "c00a00b2-8a7e-4cbc-b184-dd94930cb07e"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Array}             data.detail            Kết quả chi tiết theo chỉ tiêu
@apiSuccess {Object}            data.total                    Kết quả chi tiết theo chỉ tiêu
@apiSuccess {Float}             data.total.sales_plan         Tổng điểm chỉ tiêu
@apiSuccess {Float}             data.total.total_point_performance         Tổng điểm thực hiện
@apiSuccess {Float}             data.total.completion_rate    Tỷ lệ hoàn thành
@apiSuccess (detail)  {String}   group_target           Nhóm chỉ tiêu
                                                              <ul>
                                                                <li><code>main</code> : Chỉ tiêu chính</li>
                                                                <li><code>additional</code> : Chỉ tiêu ghi nhận thêm</li>
                                                                <li><code>sub</code> : Điểm trừ</li>
                                                              </ul>
@apiSuccess (detail)  {Array}   targets                Thông tin chi tiết từng chỉ tiêu                                                              
@apiSuccess (detail)  {String}  targets.code           Mã chỉ tiêu                                                              
@apiSuccess (detail)  {String}  targets.name           Tên chỉ tiêu                                                              
@apiSuccess (detail)  {Float}   targets.gap_sales_plan Gap kế hoạch theo điểm                                                              
@apiSuccess (detail)  {Float}   targets.gap_revenue    Gap kế hoạch theo doanh số                                                              
@apiSuccess (detail)  {Float}   targets.incomplete_rate   Tỷ lệ chưa hoàn thành
@apiSuccess (detail)  {ListObject}   targets.target_sub      Thông tin chỉ tiêu con (Các thông tin tương tự chỉ tiêu cha)                                                              

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "group_target": "main",
             "targets": [
                {
                    "code": "AB01",
                    "name": "Doanh số giải ngân TDH từ KHCN",
                    "gap_sales_plan": 2,
                    "gap_revenue": 200000,
                    "incomplete_rate": 80,
                    "target_sub": [
                        {
                            "code": "doanh_so_bao_hiem_cn",
                            "gap_revenue": null,
                            "gap_sales_plan": null,
                            "incomplete_rate": null,
                            "name": "PHÍ BHNT",
                            "parent_code": "dt_bh",
                            "unit": "VNĐ"
                        },
                        {
                            "code": "phi_bao_hiem_phi_nhan_tho",
                            "gap_revenue": null,
                            "gap_sales_plan": null,
                            "incomplete_rate": null,
                            "name": "PHÍ BH PHI NT",
                            "parent_code": "dt_bh",
                            "unit": "VNĐ"
                        }
                    ]
                }
             ],
            "total": {
                "gap_sales_plan": 300,
                "gap_revenue": 320,
                "incomplete_rate": 89
            }    
        }
    ]
}
"""

# version: 1.0.0
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/gap-targets  Gap theo tổng và từng chỉ tiêu
@apiDescription Báo cáo kết quả hoàn thành theo tổng và từng chỉ tiêu
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardGapTargets

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   [report_date]               Ngày báo cáo (Format: <code>%Y-%m-%d</code>) (Default: Ngày hiện tại)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>CSO</code></li>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRM</code></li>
                                                                <li><code>KHCN</code></li>
                                                                <li><code>DVKH</code></li>
                                                                <li><code>KHDN</code></li>
                                                                <li><code>GD-DVKD</code></li>
                                                              </ul>
@apiParam  (Body:)     {String}   [staff_id]                  ID nhân viên                                                              

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO",
    "staff_id": "c00a00b2-8a7e-4cbc-b184-dd94930cb07e"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Array}             data.detail            Kết quả chi tiết theo chỉ tiêu
@apiSuccess {Object}            data.total                    Kết quả chi tiết theo chỉ tiêu
@apiSuccess {Float}             data.total.sales_plan         Tổng điểm chỉ tiêu
@apiSuccess {Float}             data.total.total_point_performance         Tổng điểm thực hiện
@apiSuccess {Float}             data.total.completion_rate    Tỷ lệ hoàn thành
@apiSuccess (detail)  {String}   group_target           Nhóm chỉ tiêu
                                                              <ul>
                                                                <li><code>main</code> : Chỉ tiêu chính</li>
                                                                <li><code>additional</code> : Chỉ tiêu ghi nhận thêm</li>
                                                                <li><code>sub</code> : Điểm trừ</li>
                                                              </ul>
@apiSuccess (detail)  {Array}   targets                Thông tin chi tiết từng chỉ tiêu                                                              
@apiSuccess (detail)  {String}  targets.code           Mã chỉ tiêu                                                              
@apiSuccess (detail)  {String}  targets.name           Tên chỉ tiêu                                                              
@apiSuccess (detail)  {Float}   targets.gap_sales_plan Gap kế hoạch theo điểm                                                              
@apiSuccess (detail)  {Float}   targets.gap_revenue    Gap kế hoạch theo doanh số                                                              
@apiSuccess (detail)  {Float}   targets.incomplete_rate   Tỷ lệ chưa hoàn thành                                                              

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "group_target": "main",
             "targets": [
                {
                    "code": "AB01",
                    "name": "Doanh số giải ngân TDH từ KHCN",
                    "gap_sales_plan": 2,
                    "gap_revenue": 200000,
                    "incomplete_rate": 80,
                }
             ],
            "total": {
                "gap_sales_plan": 300,
                "gap_revenue": 320,
                "incomplete_rate": 89
            }    
        }
    ]
}
"""

# ---------------------------------------------
# Xếp hạng theo điểm thực hiện
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/rank-point-performance  Xếp hạng theo điểm thực hiện
@apiDescription Xếp hạng theo điểm thực hiện
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardRankPointPerformance

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   [report_date]               Ngày báo cáo (Format: <code>%Y-%m-%d</code>) (Default: Ngày hiện tại)
@apiParam  (Body:)     {String}   [staff_id]                  ID nhân viên
@apiParam  (Body:)     {String}   [staff_title]                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>CSO</code></li>
                                                              </ul>
@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO"
}

@apiSuccess {Array}          data                          Dữ liệu trả về
@apiSuccess {Int}            data.type                     Chỉ số
                                                              <ul>
                                                                <li><code>1</code> : Hệ thống</li>
                                                                <li><code>2</code> : Khu vực</li>
                                                                <li><code>3</code> : Đơn vị kinh doanh</li>
                                                                <li><code>4</code> : Tier</li>
                                                              </ul>
@apiSuccess {Int}          data.rank                    Xếp hạng cá nhân
@apiSuccess {Int}          data.total                   Tổng số
@apiSuccess {String}          [data.name]                  Tên khu vực
@apiSuccess {String}          [data.code]                  Mã khu vực
@apiSuccess {String}          [data.staff_title]           Chức danh

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "type": 1,
            "rank": 20,
            "total": 100,
            "staff_title": "RM"
        },
        {
            "type": 2,
            "rank": 15,
            "total": 50,
            "name": "Khu vực miền bắc",
            "code": "KVMB",
            "staff_title": "RM"
        },
        {
            "type": 3,
            "rank": 10,
            "total": 20,
            "name": "EIB Thủ Đức",
            "code": "000012",
            "staff_title": "RM"
        },
        {
            "type": 4,
            "rank": 5,
            "total": 10,
            "name": "Tier 1",
            "code": "Tier 1",
            "staff_title": "RM"
        }
    ]
}
"""

# ---------------------------------------------
# Xếp hạng theo tỷ lệ hoàn thành
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/rank-completion-rate  Xếp hạng theo tỷ lệ hoàn thành
@apiDescription Xếp hạng theo tỷ lệ hoàn thành
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardRankCompletionRate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   [report_date]               Ngày báo cáo (Format: <code>%Y-%m-%d</code>) (Default: Ngày hiện tại)
@apiParam  (Body:)     {String}   [staff_id]                  ID nhân viên
@apiParam  (Body:)     {String}   [staff_title]                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>SRM</code></li>
                                                              </ul>

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO"
}

@apiSuccess {Array}          data                          Dữ liệu trả về
@apiSuccess {Int}            data.type                     Chỉ số
                                                              <ul>
                                                                <li><code>1</code> : Hệ thống</li>
                                                                <li><code>2</code> : Khu vực</li>
                                                                <li><code>3</code> : Đơn vị kinh doanh</li>
                                                              </ul>
@apiSuccess {Int}          data.rank                    Xếp hạng cá nhân
@apiSuccess {Int}          data.total                   Tổng số
@apiSuccess {String}          [data.name]                  Tên khu vực
@apiSuccess {String}          [data.code]                  Mã khu vực
@apiSuccess {String}          [data.staff_title]           Chức danh

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "type": 1,
            "rank": 20,
            "total": 100,
            "staff_title": "RM"
        },
        {
            "type": 2,
            "rank": 15,
            "total": 50,
            "name": "Khu vực miền bắc",
            "code": "KVMB",
            "staff_title": "RM"
        },
        {
            "type": 3,
            "rank": 10,
            "total": 20,
            "name": "EIB Thủ Đức",
            "code": "000012",
            "staff_title": "RM"
        }
    ]
}
"""

# ---------------------------------------------
# Kết quả tỷ lệ CBBH hoàn thành theo chức danh dành cho SRBO, SRM
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/completion-rate/staff-title  Tỷ lệ CBBH hoàn thành theo chức danh
@apiDescription Kết quả tỷ lệ CBBH hoàn thành theo chức danh dành cho SRBO, SRM
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardCompletionRateStaffTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   start_time                Thời gian bắt đầu (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {String}   end_time                  Thời gian kết thúc (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>SRM</code></li>
                                                              </ul>
@apiParamExample {json} Body example
{
    "start_time": "2023-10-01",
    "end_time": "2023-10-30",
    "staff_title": "SRBO"
}

@apiSuccess {Array}          data                          Dữ liệu trả về
@apiSuccess {String}         data.target_code              Mã chỉ tiêu
@apiSuccess {String}         data.target_name              Tên chỉ tiêu
@apiSuccess {Float}          data.completion_rate          Tỷ lệ hoàn thành
@apiSuccess {Int}            data.number_staff             Số lượng nhân viên
@apiSuccess {Int}            data.number_staff_complete    Số lượng hoàn thành chỉ tiêu
@apiSuccess {Int}            data.number_staff_uncomplete  Số lượng hoàn thành chỉ tiêu

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "target_code": "AB1",
            "target_name": "Tỷ lệ RBO hoàn thành chỉ tiêu saleplan",
            "completion_rate": 35.5,
            "number_staff": 90,
            "number_staff_complete": 32,
            "number_staff_uncomplete": 58,
        }
    ]
}
"""

# ---------------------------------------------
# BC Kết quả tổng hợp từng RBO/ RM trong nhóm quản lý
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/synthetic/group-manager  Kết quả tổng hợp trong nhóm quản lý
@apiDescription 14. Kết quả thực hiện KPIs của CBBH trong nhóm quản lý
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardSyntheticGroupManager

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   start_time                Thời gian bắt đầu (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {String}   end_time                  Thời gian kết thúc (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>SRM</code></li>
                                                              </ul>
@apiParam  (Body:)     {Array}   [staff_ids]                  ID nhân viên                                                              
@apiParam  (Body:)     {Array}   [department_id]              ID phòng kinh doanh
@apiParam  (Body:)     {Array}    [business_unit]             Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}    [region]              	  Danh sách khu vực
                                                              
@apiParamExample {json} Body example
{
    "start_time": "2023-10-01",
    "end_time": "2023-10-20",
    "staff_ids": ["944b6eef-fae1-4a51-a06a-fb3a2a191390"],
    "department_id": ["KHCN"]
}

@apiSuccess {Array}          data                          Dữ liệu trả về
@apiSuccess {Object}         data.staff_info               Thông tin nhân viên
@apiSuccess {String}         data.staff_info.staff_id      ID nhân viên
@apiSuccess {String}         data.staff_info.name          Tên nhân viên
@apiSuccess {String}         data.staff_info.staff_code    Mã nhân viên
@apiSuccess {String}         data.staff_info.level         Cấp độ nhân viên
@apiSuccess {String}         data.staff_info.date_of_join  Ngày hiệu lực chức danh (Format: %Y-%m-%d)
@apiSuccess {Float}          data.sales_plan         sales_plan
@apiSuccess {Float}          data.exchange           Điểm thực hiện
@apiSuccess {Float}          data.gap_sales_plan     Gap kế hoạch
@apiSuccess {Float}          data.completion_rate    Tỷ lệ hoàn thành
@apiSuccess {Array}          data.performance_target       Thông tin thực hiện theo chỉ tiêu
@apiSuccess {String}         data.performance_target.code  Mã chỉ tiêu
@apiSuccess {String}         data.performance_target.name  Tên chỉ tiêu
@apiSuccess {Float}          data.performance_target.value              Kết quả theo chỉ tiêu


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_info": {
                "staff_code": "KHA10212",
                "name": "Nguyễn Văn Bi",
                "level": "RBO 1",
                "date_of_join": "2023-10-10",
            },
            "sales_plan": 100,
            "gap_sales_plan": 10,
            "exchange": 90,
            "completion_rate": 90,
            "performance_target": [
                {
                    "code": "SLQL",
                    "name": "SL KHCN QLCBQ",
                    "value": 100
                },
                {
                    "code": "DNQLBQ",
                    "name": "Dư nợ KHCN QLBQ",
                    "value": 100
                }
            ]
        }
    ]
}
"""

# ---------------------------------------------
# Kết quả chi tiết RBO/RM thực hiện theo từng chỉ tiêu
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/target-performance/detail-staff Kết quả chi tiết RBO/RM thực hiện
@apiDescription Kết quả chi tiết RBO/RM thực hiện theo từng chỉ tiêu 
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardTargetPerformanceDetailStaff

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   [report_date]               Ngày báo cáo (Format: <code>%Y-%m-%d</code>) (Default: Ngày hiện tại)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>SRM</code></li>
                                                              </ul>
@apiParam  (Body:)     {Array}   staff_ids                    ID nhân viên     
@apiParam  (Body:)     {Array}   [department_id]              ID phòng kinh doanh
@apiParam  (Body:)     {Array}    [business_unit]             Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}    [region]              	  Danh sách khu vực
@apiParam  (Body:)     {Array}    [title]              Chức danh
                                                       <ul>
                                                        <li><code> RBO </code></li>
                                                        <li><code> SRBO </code></li>
                                                        <li><code> CSO </code></li>
                                                        <li><code> RM </code></li>                                            
                                                        <li><code> SRM </code></li>                                            
                                                        <li><code> GDV </code></li>                                            
                                                        <li><code> KSV </code></li>                                            
                                                      </ul>
                                                         
@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "SRBO",
    "staff_ids": ["944b6eef-fae1-4a51-a06a-fb3a2a191390"],
    "department_id": ["KHCN"]
}

@apiSuccess {Array}          data                          Dữ liệu trả về
@apiSuccess {Object}         data.target_info              Thông tin chỉ tiêu
@apiSuccess {String}         data.target_info.code  Mã chỉ tiêu
@apiSuccess {String}         data.target_info.name  Tên chỉ tiêu

@apiSuccess {Array}          data.performance_target       Thông tin thực hiện theo chỉ tiêu theo nhân viên
@apiSuccess {Object}         data.performance_target.staff_info               Thông tin nhân viên
@apiSuccess {String}         data.performance_target.staff_info.name          Tên nhân viên
@apiSuccess {String}         data.performance_target.staff_info.staff_id      ID nhân viên
@apiSuccess {String}         data.performance_target.staff_info.staff_code    Mã nhân viên
@apiSuccess {String}         data.performance_target.staff_info.level         Cấp độ nhân viên

@apiSuccess {Float}          data.performance_target.sales_plan         sales_plan
@apiSuccess {Float}          data.performance_target.result_sales_plan	         Điểm thực hiện
@apiSuccess {Float}          data.performance_target.gap_sales_plan     Gap kế hoạch
@apiSuccess {Float}          data.performance_target.completion_rate    Tỷ lệ hoàn thành
           

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "target_info": {
                "code": "SLQL",
                "name": "SL KHCN QLCBQ",
            }
            "performance_target": [
                {
                    "staff_info": {
                        "staff_id": "d085b48b-5b1b-4c20-a2e5-2ae94b866292",
                        "staff_code": "KHA10212",
                        "name": "Nguyễn Văn Bi",
                        "level": "RBO 1",
                        "date_of_join": "2023-10-10",
                    },
                    "result_sales_plan": 90,
                    "sales_plan": 100,
                    "gap_sales_plan": 10,
                    "completion_rate": 90
                }
            ]
        }
    ]
}
"""

# ---------------------------------------------
# Kết quả thực hiện KPI trong năm báo cáo theo cá nhân
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/target-performance/year Kết quả thực hiện KPI trong năm
@apiDescription Kết quả thực hiện KPIs trong Năm báo cáo theo cá nhân
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardTargetPerformanceYear

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_year                 Năm báo cáo
@apiParam  (Body:)     {String}   staff_title                Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRM</code></li>
                                                                <li><code>CSO</code></li>
                                                                <li><code>KHDN</code></li>
                                                              </ul>
@apiParam  (Body:)     {String}   [staff_id]                    ID nhân viên                                                              
@apiParamExample {json} Body example
{
    "report_year": "2023",
    "staff_title": "SRBO"
}

@apiSuccess {Object}        data                          Dữ liệu trả về
@apiSuccess {String}        data.staff_title              Chức danh
@apiSuccess {Array}         data.detail                   Kết quả thực hiện chi tiết theo tháng
@apiSuccess {Int}           data.detail.month             Tháng báo cáo
@apiSuccess {Float}         data.detail.sales_plan        Điểm sales plan
@apiSuccess {Float}         data.detail.performance_target      Điểm thực hiện
@apiSuccess {Float}         data.detail.completion_rate   Tỷ lệ hoàn thành
@apiSuccess {Float}         [data.detail.srm_completion_rate]   Tỷ lệ hoàn thành cá nhân (đối với SRM)
@apiSuccess {Object}        data.average                  Bình quân
@apiSuccess {Float}        data.average.sales_plan       Bình quân điểm sales plan
@apiSuccess {Float}        data.average.performance_target       Bình quân điểm thực hiện
@apiSuccess {Float}        data.average.completion_rate       Bình quân tỷ lệ hoàn thành
@apiSuccess {Float}        [data.average.srm_completion_rate]       Bình quân tỷ lệ hoàn thành cá nhân (đối với SRM)


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "staff_title": "RBO",         
        "detail": [
            {
                "month": 1,
                "sales_plan": 100,
                "performance_target": 80,
                "completion_rate": 80
            },
            {
                "month": 2,
                "sales_plan": 100,
                "performance_target": 80,
                "completion_rate": 80
            },
            {
                "month": 3,
                "sales_plan": 200,
                "performance_target": 80,
                "completion_rate": 40
            },
            {
                "month": 4,
                "sales_plan": 200,
                "performance_target": 100,
                "completion_rate": 50
            }
        ],
        "average": {
            "sales_plan": 150,
            "performance_target": 85,
            "completion_rate": 62.5
        }     
    }   
}
"""

# ---------------------------------------------
# Báo cáo số lượng nhân sự quản lý cấp bậc
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/management/level-staff Báo cáo số lượng quản lý cấp bậc
@apiDescription BC Số lượng nhân sự quản lý cấp bậc 
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardManagementStaffLevel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {Int}    [report_month]              Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   staff_title               Chức danh nhân viên
                                                              <ul>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>SRM</code></li>
                                                                <li><code>KHCN</code></li>
                                                                <li><code>DVKH</code></li>
                                                              </ul>
@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "SRBO"
}

@apiSuccess {Array}         data                          Dữ liệu trả về
@apiSuccess {String}        data.staff_title              Chức danh
@apiSuccess {Int}           data.quantity                 Số lượng nhân viên tương ứng với chức danh



@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_title": "RBO",
            "quantity": 10         
        }
    ]
}
"""

# ---------------------------------------------
# Báo cáo Điểm dùng tính lương
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/point-used-salary  Điểm dùng tính lương
@apiDescription Báo cáo điểm dùng tính lương
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardPointUsedSalary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_month                Kỳ báo cáo tháng (Format: <code>%Y-%m</code>)
@apiParam  (Body:)     {String}   [report_date]               Ngày báo cáo (Format: <code>%Y-%m-%d</code>) (Default: Ngày hiện tại)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>SRBO</code></li>
                                                                <li><code>CSO</code></li>
                                                                <li><code>RM</code></li>
                                                                <li><code>SRM</code></li>
                                                              </ul>

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Float}             data.point_salary             Điểm dùng tính lương   
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "point_salary": 120
    }
}
"""

# ---------------------------------------------
# Báo cáo Tỷ lệ hoàn thành KPI theo DVKD
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/completion-rate/business-unit  Tỷ lệ hoàn thành KPIs theo ĐVKD
@apiDescription 17. Tỉ lệ hoàn thành KPIs theo ĐVKD
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardCompletionRateBusinessUnit

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_year                 Năm báo cáo
@apiParam  (Body:)     {Array}   [staff_ids]                   Danh sách ID trưởng nhóm
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>KHCN</code></li>
                                                                <li><code>KHDN</code></li>
                                                                <li><code>DVKH</code></li>                                            
                                                              </ul>
@apiParam  (Body:)     {Array}   [department_id]              ID phòng kinh doanh
@apiParam  (Body:)     {Array}    [business_unit]             Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}    [region]              	  Danh sách khu vực                                                          

@apiParamExample {json} Body example
{
    "report_year": "2023",
    "staff_title": "RBO",
    "staff_ids": ["dc3f4650-8d9b-4f0c-8e06-38264f9a9ef0"],
    "department_id": ["KHCN"]
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Array}             data.detail                   Chi tiết tỷ lệ hoàn thành theo tháng   
@apiSuccess {String}            data.detail.month             Tháng báo cáo   
@apiSuccess {Float}             data.detail.completion_rate   Tỷ lệ hoàn thành   
@apiSuccess {Float}             data.detail.number_complete   Số lượng nhân viên hoàn thành   
@apiSuccess {Float}             data.detail.total             Số lượng nhân viên   
@apiSuccess {Float}             data.avg_completion_rate      Tỷ lệ hoàn thành bình quân   
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "detail": [
            {
                "month": "1",
                "completion_rate": 82.2,
                "number_complete": 822,
                "total": 1000,
            },
            {
                "month": "2",
                "completion_rate": 91,
                "number_complete": 91,
                "total": 100,
            }
        ],
        "avg_completion_rate": 86.6
    }
}
"""

# ---------------------------------------------
# Báo cáo Tỷ lệ hoàn thành KPI theo DVKD - Theo loại chart báo cáo
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/completion-rate/business-unit/title  Tỷ lệ hoàn thành KPIs theo ĐVKD (Theo chức danh)
@apiDescription 17. Tỉ lệ hoàn thành KPIs theo ĐVKD theo từng loại chức danh
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardCompletionRateBusinessUnitTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   card_title          Chức danh cho card báo cáo
                                                      <ul>
                                                         <li><code>GDV_KSV</code>: GDV/KSV</li>
                                                      </ul>

@apiParam  (Body:)     {String}   report_year          Năm báo cáo
@apiParam  (Body:)     {Array}    [department_id]      ID phòng kinh doanh
@apiParam  (Body:)     {Array}    [business_unit]      Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}    [region]             Danh sách khu vực                                                          
@apiParam  (Body:)     {Array}    [title]              Chức danh
                                                       <ul>
                                                        <li><code> RBO </code></li>
                                                        <li><code> SRBO </code></li>
                                                        <li><code> CSO </code></li>
                                                        <li><code> RM </code></li>                                            
                                                        <li><code> SRM </code></li>                                            
                                                        <li><code> GDV </code></li>                                            
                                                        <li><code> KSV </code></li>                                            
                                                      </ul>
@apiParam  (Body:)     {Array}    [staff_ids]          Danh sách ID trưởng nhóm



@apiParamExample {json} Body example
{
    "card_title": "GDV_KSV",
    "title": ["KSV"],
    "report_year": "2023",
    "title": ["RBO", "GDV"],
    "staff_ids": ["dc3f4650-8d9b-4f0c-8e06-38264f9a9ef0"],
    "department_id": ["KHCN"]
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Array}             data.detail                   Chi tiết tỷ lệ hoàn thành theo tháng   
@apiSuccess {String}            data.detail.month             Tháng báo cáo   
@apiSuccess {Float}             data.detail.completion_rate   Tỷ lệ hoàn thành   
@apiSuccess {Float}             data.detail.number_complete   Số lượng nhân viên hoàn thành   
@apiSuccess {Float}             data.detail.total             Số lượng nhân viên   
@apiSuccess {Float}             data.avg_completion_rate      Tỷ lệ hoàn thành bình quân   
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "detail": [
            {
                "month": "1",
                "completion_rate": 82.2,
                "number_complete": 822,
                "total": 1000,
            },
            {
                "month": "2",
                "completion_rate": 91,
                "number_complete": 91,
                "total": 100,
            }
        ],
        "avg_completion_rate": 86.6
    }
}
"""

# ---------------------------------------------
# Báo cáo Tỷ lệ hoàn thành KPI theo DVKD
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/rank-completion-rate/business-department  Xếp hạng tỷ lệ hoàn thành KPIs theo ĐVKD
@apiDescription 18. Xếp hạng tỉ lệ hoàn thành của Phòng kinh doanh 
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardRankCompletionRateBusinessDepartment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_year                 Năm báo cáo
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>KHCN</code></li>
                                                                <li><code>KHDN</code></li>
                                                                <li><code>DVKH</code></li>                                            
                                                              </ul>

@apiParamExample {json} Body example
{
    "report_year": "2023",
    "staff_title": "RBO"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Array}             data.detail                   Chi tiết tỷ lệ hoàn thành theo tháng   
@apiSuccess {String}            data.detail.month             Tháng báo cáo   
@apiSuccess {Array}             data.detail.rank_detail       Thông tin xếp hạng theo tháng   
@apiSuccess {String}            data.detail.rank_detail.type  Loại   
                                                              <ul>
                                                                <li><code>system</code> : Hệ thống</li>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>tier</code> : Tier</li>
                                                              </ul>
@apiSuccess {Int}               data.detail.rank_detail.name  Tên loại  
@apiSuccess {Int}               data.detail.rank_detail.rank  Xếp hạng   
@apiSuccess {Int}               data.detail.rank_detail.total Tổng số   
@apiSuccess {Object}            data.avg_completion_rate      Xếp hạng tỷ lệ hoàn thành bình quân   
@apiSuccess {String}            data.avg_completion_rate.type Loại
                                                              <ul>
                                                                <li><code>system</code> : Hệ thống</li>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>tier</code> : Tier</li>
                                                              </ul>
@apiSuccess {Int}            data.avg_completion_rate.rank    Xếp hạng                                                              
@apiSuccess {Int}            data.avg_completion_rate.total   Tổng số                                                              
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "detail": [
            {
                "month": "1",
                "rank_detail": [
                    {
                        "type": "system",
                        "rank": 2,
                        "total": 10
                    },
                    {
                        "type": "region",
                        "rank": 2,
                        "total": 10
                    },
                    {
                        "type": "tier",
                        "rank": 2,
                        "total": 10
                    }
                ]
            }
        ],
        "avg_completion_rate": [
            {
                "type": "system",
                "rank": 2,
                "total": 10
            },
            {
                "name": "Khu vực TPHCM",
                "type": "region",
                "rank": 2,
                "total": 10
            },
            {
                "name": "Tier 01",
                "type": "tier",
                "rank": 2,
                "total": 10
            }
        ]
    }
}
"""

# ---------------------------------------------
# Báo cáo Tỷ lệ hoàn thành KPI theo DVKD (theo chức danh)
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/rank-completion-rate/business-department/title  Xếp hạng tỷ lệ hoàn thành KPIs theo ĐVKD (Theo chức danh)
@apiDescription 18. Xếp hạng tỉ lệ hoàn thành của Phòng kinh doanh theo từng loại chức danh
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardRankCompletionRateBusinessDepartmentTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam  (Body:)     {String}   card_title          Chức danh cho card báo cáo
                                                      <ul>
                                                         <li><code>GDV_KSV</code>: GDV/KSV</li>
                                                      </ul>

@apiParam  (Body:)     {String}   report_year          Năm báo cáo
@apiParam  (Body:)     {Array}    [title]              Chức danh
                                                       <ul>
                                                        <li><code> RBO </code></li>
                                                        <li><code> SRBO </code></li>
                                                        <li><code> CSO </code></li>
                                                        <li><code> RM </code></li>                                            
                                                        <li><code> SRM </code></li>                                            
                                                        <li><code> GDV </code></li>                                            
                                                        <li><code> KSV </code></li>                                            
                                                      </ul>

                                                              

@apiParamExample {json} Body example
{
    "card_title": "GDV_KSV",
    "title": ["KSV"],
    "report_year": "2023",
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Array}             data.detail                   Chi tiết tỷ lệ hoàn thành theo tháng   
@apiSuccess {String}            data.detail.month             Tháng báo cáo   
@apiSuccess {Array}             data.detail.rank_detail       Thông tin xếp hạng theo tháng   
@apiSuccess {String}            data.detail.rank_detail.type  Loại   
                                                              <ul>
                                                                <li><code>system</code> : Hệ thống</li>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>tier</code> : Tier</li>
                                                              </ul>
@apiSuccess {Int}               data.detail.rank_detail.name  Tên loại  
@apiSuccess {Int}               data.detail.rank_detail.rank  Xếp hạng   
@apiSuccess {Int}               data.detail.rank_detail.total Tổng số   
@apiSuccess {Object}            data.avg_completion_rate      Xếp hạng tỷ lệ hoàn thành bình quân   
@apiSuccess {String}            data.avg_completion_rate.type Loại
                                                              <ul>
                                                                <li><code>system</code> : Hệ thống</li>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>tier</code> : Tier</li>
                                                              </ul>
@apiSuccess {Int}            data.avg_completion_rate.rank    Xếp hạng                                                              
@apiSuccess {Int}            data.avg_completion_rate.total   Tổng số                                                              
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "detail": [
            {
                "month": "1",
                "rank_detail": [
                    {
                        "type": "system",
                        "rank": 2,
                        "total": 10
                    },
                    {
                        "type": "region",
                        "rank": 2,
                        "total": 10
                    },
                    {
                        "type": "tier",
                        "rank": 2,
                        "total": 10
                    }
                ]
            }
        ],
        "avg_completion_rate": [
            {
                "type": "system",
                "rank": 2,
                "total": 10
            },
            {
                "name": "Khu vực TPHCM",
                "type": "region",
                "rank": 2,
                "total": 10
            },
            {
                "name": "Tier 01",
                "type": "tier",
                "rank": 2,
                "total": 10
            }
        ]
    }
}
"""

# ---------------------------------------------
# 2.4.6 Số lượng RBO/CSO hoàn thành từng chỉ tiêu theo ĐVKD (#28)
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/complete-target/staff/department Số lượng nhân sự hoàn thành chỉ tiêu theo phòng kinh doanh
@apiDescription 28. Số lượng nhân sự hoàn thành chỉ tiêu theo phòng kinh doanh 
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardCompleteTargetStaffDepartment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_date                 Năm báo cáo (Format: %Y-%m-%d)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>KHCN</code></li>
                                                                <li><code>KHDN</code></li>
                                                                <li><code>DVKH</code></li>                                            
                                                              </ul>
@apiParam  (Body:)     {Array}   [staff_ids]                  Danh sách ID trưởng nhóm
@apiParam  (Body:)     {Array}   [department_id]              ID phòng kinh doanh
@apiParam  (Body:)     {Array}    [business_unit]             Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}    [region]              	  Danh sách khu vực

@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "staff_title": "KHCN",
    "department_id": ["KHCN"]
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Array}             data.detail                   Chi tiết kết quả hoàn thành 
@apiSuccess {String}            data.detail.target_code       Mã chỉ tiêu   
@apiSuccess {String}            data.detail.target_name       Tên chỉ tiêu   
@apiSuccess {Number}            data.detail.completed         Số lượng nhân viên hoàn thành chỉ tiêu  
@apiSuccess {Number}            data.detail.completing        Số lượng nhân viên đang hoàn thành 
@apiSuccess {Number}            data.detail.unfulfilled       Số lượng nhân viên chưa thực hiện                                                     
@apiSuccess {Int}               data.number_assigned          Số lượng nhân viên được giao                                                              
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "detail": [
            {
                "target_code": "dsgntdh",
                "target_name": "Doanh số giải ngân trung dài hạn",
                "completed": 8,
                "completing": 2,
                "unfulfilled": 2
            }
        ],
        "number_assigned": 12
    }
}
"""

# ---------------------------------------------
# 2.4.6 Số lượng RBO/CSO hoàn thành từng chỉ tiêu theo ĐVKD (#28)
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/complete-target/staff/department/title Số lượng nhân sự hoàn thành chỉ tiêu theo phòng kinh doanh (Theo chức danh)
@apiDescription 28. Số lượng nhân sự hoàn thành chỉ tiêu theo phòng kinh doanh (Theo chức danh: GDV/KSV, ..) 
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardCompleteTargetStaffDepartmentTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   card_title          Chức danh cho card báo cáo
                                                      <ul>
                                                         <li><code>GDV_KSV</code>: GDV/KSV</li>
                                                      </ul>

@apiParam  (Body:)     {String}   report_date          Ngày báo cáo (Format: %Y-%m-%d)
@apiParam  (Body:)     {Array}    [department_id]      ID phòng kinh doanh
@apiParam  (Body:)     {Array}    [business_unit]      Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}    [region]             Danh sách khu vực                                                          
@apiParam  (Body:)     {Array}    [title]              Chức danh
                                                       <ul>
                                                        <li><code> RBO </code></li>
                                                        <li><code> SRBO </code></li>
                                                        <li><code> CSO </code></li>
                                                        <li><code> RM </code></li>                                            
                                                        <li><code> SRM </code></li>                                            
                                                        <li><code> GDV </code></li>                                            
                                                        <li><code> KSV </code></li>                                            
                                                      </ul>
@apiParam  (Body:)     {Array}    [staff_ids]          Danh sách ID trưởng nhóm


@apiParamExample {json} Body example
{
    "card_title": "GDV_KSV",
    "title": ["KSV"],
    "report_date": "2023-10-30",
    "title": ["RBO", "GDV"],
    "staff_ids": ["dc3f4650-8d9b-4f0c-8e06-38264f9a9ef0"],
    "department_id": ["KHCN"]
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Array}             data.detail                   Chi tiết kết quả hoàn thành 
@apiSuccess {String}            data.detail.target_code       Mã chỉ tiêu   
@apiSuccess {String}            data.detail.target_name       Tên chỉ tiêu   
@apiSuccess {Number}            data.detail.completed         Số lượng nhân viên hoàn thành chỉ tiêu  
@apiSuccess {Number}            data.detail.completing        Số lượng nhân viên đang hoàn thành 
@apiSuccess {Number}            data.detail.unfulfilled       Số lượng nhân viên chưa thực hiện                                                     
@apiSuccess {Int}               data.number_assigned          Số lượng nhân viên được giao                                                              
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "detail": [
            {
                "target_code": "dsgntdh",
                "target_name": "Doanh số giải ngân trung dài hạn",
                "completed": 8,
                "completing": 2,
                "unfulfilled": 2
            }
        ],
        "number_assigned": 12
    }
}
"""

# ---------------------------------------------
# Báo cáo Doanh số bình quân trên RBO
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/average-revenue/department Doanh số bình quân
@apiDescription 29. Doanh số bình quân trên RBO 
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardAverageRevenueDepartment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_date                 Năm báo cáo (Format: %Y-%m-%d)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>KHCN</code></li>
                                                                <li><code>DVKH</code></li>                                            
                                                              </ul>
@apiParam  (Body:)     {Array}   [staff_ids]                   Danh sách ID trưởng nhóm                                                              
@apiParam  (Body:)     {Array}   [department_id]              ID phòng kinh doanh
@apiParam  (Body:)     {Array}    [business_unit]             Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}    [region]              	  Danh sách khu vực

@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "staff_title": "KHCN",
    "department_id": ["KHCN"]
}

@apiSuccess {Array}             data                          Dữ liệu trả về
@apiSuccess {String}            data.target_code              Mã chỉ tiêu   
@apiSuccess {String}            data.target_name              Tên chỉ tiêu   
@apiSuccess {String}            data.unit                     Đơn vị tính   
@apiSuccess {Object}            data.index_type               Doanh số theo loại   
@apiSuccess {Object}            data.index_type.{type}        Loại doanh số
                                                              <ul>
                                                                <li><code>department</code> : Đơn vị kinh doanh</li>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>tier</code> : Tier</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                              </ul>
@apiSuccess {Number}            data.index_type.type.total_revenue         Tổng doanh số 
@apiSuccess {Number}            data.index_type.type.number_staff        Số lượng nhân viên 
@apiSuccess {Number}            data.index_type.type.average_revenue       Doanh số bình quân 
                                                                                              
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "target_code": "dsgntdh",
            "target_name": "Doanh số giải ngân trung dài hạn",
            "unit": "VND",
            "index_type": {
                "department": {
                    "total_revenue": 10000000,
                    "number_staff": 10,
                    "average_revenue": 1000000
                },
                "region": {
                    "total_revenue": 10000000,
                    "number_staff": 10,
                    "average_revenue": 1000000
                },
                "tier": {
                    "total_revenue": 10000000,
                    "number_staff": 10,
                    "average_revenue": 1000000
                },
                "system": {
                    "total_revenue": 10000000,
                    "number_staff": 10,
                    "average_revenue": 1000000
                }
            }
        }
    ]
}
"""

# ---------------------------------------------
# Báo cáo Doanh số bình quân trên chức danh
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/average-revenue/department/title Doanh số bình quân (Theo chức danh)
@apiDescription 29. Doanh số bình quân (Theo chức danh) GDV/KSV
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardAverageRevenueDepartmentTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   card_title            Chức danh cho card báo cáo
                                                        <ul>
                                                            <li><code>GDV_KSV</code>: GDV/KSV</li>
                                                        </ul>

@apiParam  (Body:)     {String}   report_date          Ngày báo cáo (Format: %Y-%m-%d)
@apiParam  (Body:)     {Array}    [department_id]      ID phòng kinh doanh
@apiParam  (Body:)     {Array}    [business_unit]      Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}    [region]             Danh sách khu vực                                                          
@apiParam  (Body:)     {Array}    [title]              Chức danh
                                                       <ul>
                                                        <li><code> RBO </code></li>
                                                        <li><code> SRBO </code></li>
                                                        <li><code> CSO </code></li>
                                                        <li><code> RM </code></li>                                            
                                                        <li><code> SRM </code></li>                                            
                                                        <li><code> GDV </code></li>                                            
                                                        <li><code> KSV </code></li>                                            
                                                      </ul>
@apiParam  (Body:)     {Array}    [staff_ids]          Danh sách ID trưởng nhóm

@apiParamExample {json} Body example
{
    "card_title": "GDV_KSV",
    "title": ["KSV"],
    "report_date": "2023-11-20",
    "title": ["RBO", "GDV"],
    "staff_ids": ["dc3f4650-8d9b-4f0c-8e06-38264f9a9ef0"],
    "department_id": ["KHCN"]
}

@apiSuccess {Array}             data                          Dữ liệu trả về
@apiSuccess {String}            data.target_code              Mã chỉ tiêu   
@apiSuccess {String}            data.target_name              Tên chỉ tiêu   
@apiSuccess {String}            data.unit                     Đơn vị tính   
@apiSuccess {Object}            data.index_type               Doanh số theo loại   
@apiSuccess {Object}            data.index_type.{type}        Loại doanh số
                                                              <ul>
                                                                <li><code>department</code> : Đơn vị kinh doanh</li>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>tier</code> : Tier</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                              </ul>
@apiSuccess {Number}            data.index_type.type.total_revenue         Tổng doanh số 
@apiSuccess {Number}            data.index_type.type.number_staff        Số lượng nhân viên 
@apiSuccess {Number}            data.index_type.type.average_revenue       Doanh số bình quân 

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "target_code": "dsgntdh",
            "target_name": "Doanh số giải ngân trung dài hạn",
            "unit": "VND",
            "index_type": {
                "department": {
                    "total_revenue": 10000000,
                    "number_staff": 10,
                    "average_revenue": 1000000
                },
                "region": {
                    "total_revenue": 10000000,
                    "number_staff": 10,
                    "average_revenue": 1000000
                },
                "tier": {
                    "total_revenue": 10000000,
                    "number_staff": 10,
                    "average_revenue": 1000000
                },
                "system": {
                    "total_revenue": 10000000,
                    "number_staff": 10,
                    "average_revenue": 1000000
                }
            }
        }
    ]
}
"""

# ---------------------------------------------
# Kết quả thực hiện KPIs của SRM/RM theo nhóm
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/target-performance/rm Kết quả thực hiện KPIs của SRM/RM theo nhóm
@apiDescription 30. Kết quả thực hiện KPIs của SRM/RM theo nhóm  
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardTargetPerformanceRM

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam (Query:) {string} [order_by]                       Sắp xếp theo thuộc tính nào. 
                                                             <ul>
                                                                <li><code>completion_rate</code> : Tỷ lệ hoàn thành</li>
                                                                <li><code>sales_plan</code> : Điểm sales plan</li>
                                                                <li><code>total_point_performance</code> : Điểm thực hiện</li>
                                                             </ul>
@apiParam (Query:) {string} [order_type]                    Kiểu sắp xếp
                                                            <ul>
                                                                <li><code>asc</code>: Tăng dần</li>
                                                                <li><code>desc</code>: Giảm dần</li>
                                                            </ul>      
                                                            
@apiParam  (Body:)     {String}   report_date                Ngày báo cáo (Format: %Y-%m-%d)
@apiParam  (Body:)     {String}   staff_title                Chức danh nhân viên
@apiParam  (Body:)     {Array}   [staff_titles]              Danh sách chức danh nhân viên ở bộ lọc chung
@apiParam  (Body:)     {Array}    [staff_ids]                Danh sách ID nhân viên
@apiParam  (Body:)     {String}   [manage_id]                ID quản lý (ID SRM)
                                                            <br>
                                                             Trường hợp manage_id = <code>no_group</code> lấy danh sách SRBO không có quản lý
@apiParam  (Body:)     {Array}   [department_id]              ID phòng kinh doanh
@apiParam  (Body:)     {Array}    [business_unit]             Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}    [region]              	  Danh sách khu vực                                                        

@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "department_id": ["KHDN"]
}

@apiSuccess {Array}             data                          Dữ liệu trả về
@apiSuccess {String}            data.staff_id                 ID nhân viên   
@apiSuccess {String}            data.staff_title              Chức danh nhân viên   
@apiSuccess {Float}             data.completion_rate          Tỷ lệ hoàn thành
@apiSuccess {Float}             data.sales_plan               Điểm sales plan
@apiSuccess {Float}             data.total_point_performance  Điểm thực hiện
@apiSuccess {Float}             data.gap                      Điểm Gap

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_id": "7e48aedd-f5e0-44c9-a75f-23bbe491d101",
            "staff_title": "SRM",
            "completion_rate": 50.2,
            "sales_plan": 1000,
            "total_point_performance": 502,
            "gap": 498,
        }
    ]
}
"""

# ---------------------------------------------
# Kết quả thực hiện KPI của RBO/SRBO theo Nhóm kinh doanh trong PhòngKD
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/target-performance/rbo/department Kết quả thực hiện KPI của RBO/SRBO theo Nhóm kinh doanh trong PhòngKD
@apiDescription 27. Kết quả thực hiện KPI của RBO/SRBO theo Nhóm kinh doanh trong PhòngKD  
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardTargetPerformanceRboDepartment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam (Query:) {string} [order_by]                       Sắp xếp theo tiêu chí nào. (Key truyền vào là mã tiêu chí)
                                                             Ví dụ:
                                                             RBO   
                                                              <ul>
                                                                <li><code>dsgn_tdh</code>: Doanh số giải ngân TDH từ KHCN</li>
                                                                <li><code>trdn_nhbq</code>: Dư nợ NH bình quân tăng ròng từ KHCN </li>
                                                                <li><code>the_td</code>: Thẻ tín dụng </li>
                                                                <li><code>hd_khcn</code>: Huy động mới từ KHCN </li>
                                                                <li><code>combo</code>: Tài khoản Combo </li>
                                                                <li><code>dt_bh</code>: Doanh thu phí bảo hiểm </li>
                                                                <li><code>sl_khcn_new_active</code>: Số lượng KHCN acitve mới </li>
                                                              </ul>
                                                              CSO
                                                              <ul>
                                                                <li><code>trhdv_khcn</code>: Tăng ròng HĐV</li>
                                                                <li><code>hdckh_qlbq</code>: HĐV KHCN có kỳ hạn quản lý bình quân </li>
                                                                <li><code>dt_bh</code>: Doanh thu phí bảo hiểm </li>
                                                                <li><code>combo</code>: Chỉ tiêu combo </li>
                                                                <li><code>the_gnqt</code>: Chỉ tiêu thẻ ghi nợ quốc tế</li>
                                                                <li><code>the_td</code>: Chỉ tiêu thẻ tín dụng</li>
                                                                <li><code>sl_khcn_new_active</code>: Số lượng KHCN active mới  </li>
                                                              </ul>
@apiParam (Query:) {string} [order_type]                    Kiểu sắp xếp
                                                            <ul>
                                                                <li><code>asc</code>: Tăng dần</li>
                                                                <li><code>desc</code>: Giảm dần</li>
                                                            </ul>      

@apiParam  (Body:)     {String}   report_date                Ngày báo cáo (Format: %Y-%m-%d)
@apiParam  (Body:)     {String}   staff_title                Chức danh nhân viên
@apiParam  (Body:)     {Array}   [staff_titles]              Danh sách chức danh nhân viên ở bộ lọc chung
@apiParam  (Body:)     {Array}    [staff_ids]                Danh sách ID nhân viên
@apiParam  (Body:)     {String}   [manage_id]                ID quản lý (SRBO)
                                                             <br>
                                                             Trường hợp manage_id = <code>no_group</code> lấy danh sách RBO không có quản lý
@apiParam  (Body:)     {Array}   [department_id]              ID phòng kinh doanh
@apiParam  (Body:)     {Array}   [business_unit]            Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}   [region]              		Danh sách khu vực

@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "staff_title": "KHCN",
    "manage_id": "7e48aedd-f5e0-44c9-a75f-23bbe491d101",
    "department_id": ["KHCN"]
}

@apiSuccess {Array}             data                          Dữ liệu trả về
@apiSuccess {String}            data.staff_id                 ID nhân viên   
@apiSuccess {String}            data.staff_title              Chức danh nhân viên   
@apiSuccess {String}            data.staff_level              Tên cấp bậc nhân viên   
@apiSuccess {Array}             data.target_detail            Chi tiết kết quả thực hiện   
@apiSuccess {String}            data.target_detail.target_code      Mã chỉ tiêu
                                                              RBO   
                                                              <ul>
                                                                <li><code>dsgn_tdh</code>: Doanh số giải ngân TDH từ KHCN</li>
                                                                <li><code>trdn_nhbq</code>: Dư nợ NH bình quân tăng ròng từ KHCN </li>
                                                                <li><code>the_td</code>: Thẻ tín dụng </li>
                                                                <li><code>hd_khcn</code>: Huy động mới từ KHCN </li>
                                                                <li><code>combo</code>: Tài khoản Combo </li>
                                                                <li><code>dt_bh</code>: Doanh thu phí bảo hiểm </li>
                                                                <li><code>sl_khcn_new_active</code>: Số lượng KHCN acitve mới </li>
                                                              </ul>
                                                              CSO
                                                              <ul>
                                                                <li><code>trhdv_khcn</code>: Tăng ròng HĐV</li>
                                                                <li><code>hdckh_qlbq</code>: HĐV KHCN có kỳ hạn quản lý bình quân </li>
                                                                <li><code>dt_bh</code>: Doanh thu phí bảo hiểm </li>
                                                                <li><code>combo</code>: Chỉ tiêu combo </li>
                                                                <li><code>the_gnqt</code>: Chỉ tiêu thẻ ghi nợ quốc tế</li>
                                                                <li><code>the_td</code>: Chỉ tiêu thẻ tín dụng</li>
                                                                <li><code>sl_khcn_new_active</code>: Số lượng KHCN active mới  </li>
                                                              </ul>
                                                              
@apiSuccess {Float}             data.target_detail.revenue    Doanh số thực hiện   
@apiSuccess {Float}             data.target_detail.completion_rate Tỷ lệ hoàn thành  


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_id": "7e48aedd-f5e0-44c9-a75f-23bbe491d101",
            "staff_title": "SRBO",
            "staff_level": "SRBO",
            "target_detail": [
                {
                    "target_code": "dsgntdh",
                    "revenue": 200000000,
                    "completion_rate": 82.1
                }
            ]
        }
    ]
}
"""

# ---------------------------------------------
# ******* Kết quả thực hiện từng chỉ tiêu của CBBH theo Phòng DVKH
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/target-performance/target/department/title Kết quả thực hiện từng chỉ tiêu của CBBH theo Phòng DVKH (theo chức danh)
@apiDescription ******* Kết quả thực hiện từng chỉ tiêu của CBBH theo Phòng DVKH (GDV/KSV)  
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardTargetPerformanceTargetDepartmentTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam (Query:) {string} [order_by]                       Sắp xếp theo tiêu chí nào. (Key truyền vào là mã tiêu chí)
                                                             Ví dụ:
                                                             GDV   
                                                              <ul>
                                                                <li><code>dsgn_tdh</code>: Doanh số giải ngân TDH từ KHCN</li>
                                                                <li><code>trdn_nhbq</code>: Dư nợ NH bình quân tăng ròng từ KHCN </li>
                                                                <li><code>the_td</code>: Thẻ tín dụng </li>
                                                                <li><code>hd_khcn</code>: Huy động mới từ KHCN </li>
                                                                <li><code>combo</code>: Tài khoản Combo </li>
                                                                <li><code>dt_bh</code>: Doanh thu phí bảo hiểm </li>
                                                                <li><code>sl_khcn_new_active</code>: Số lượng KHCN acitve mới </li>
                                                              </ul>
@apiParam (Query:) {string} [order_type]                    Kiểu sắp xếp
                                                            <ul>
                                                                <li><code>asc</code>: Tăng dần</li>
                                                                <li><code>desc</code>: Giảm dần</li>
                                                            </ul>      

@apiParam  (Body:)     {String}   report_date                Ngày báo cáo (Format: %Y-%m-%d)
@apiParam  (Body:)     {String}   card_title                Chức danh cho card báo cáo
                                                            <ul>
                                                                <li><code>GDV_KSV</code>: GDV/KSV</li>
                                                            </ul>                                                
@apiParam  (Body:)     {Array}    [title]                   Danh sách chức danh nhân viên ở bộ lọc chung
                                                            <ul>
                                                                <li><code> RBO </code></li>
                                                                <li><code> SRBO </code></li>
                                                                <li><code> CSO </code></li>
                                                                <li><code> RM </code></li>                                            
                                                                <li><code> SRM </code></li>                                            
                                                                <li><code> GDV </code></li>                                            
                                                                <li><code> KSV </code></li>                                            
                                                            </ul>
@apiParam  (Body:)     {Array}    [staff_ids]                Danh sách ID nhân viên
@apiParam  (Body:)     {String}   [manage_id]                ID trưởng nhóm
@apiParam  (Body:)     {Array}   [department_id]              ID phòng kinh doanh
@apiParam  (Body:)     {Array}   [business_unit]            Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}   [region]              		Danh sách khu vực

@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "card_title": "GDV_KSV"
    "manage_id": "7e48aedd-f5e0-44c9-a75f-23bbe491d101",
    "department_id": ["KHCN"]
}

@apiSuccess {Array}             data                          Dữ liệu trả về
@apiSuccess {String}            data.staff_id                 ID nhân viên   
@apiSuccess {String}            data.staff_title              Chức danh nhân viên   
@apiSuccess {String}            data.staff_level              Tên cấp bậc nhân viên   
@apiSuccess {Array}             data.target_detail            Chi tiết kết quả thực hiện   
@apiSuccess {String}            data.target_detail.target_code      Mã chỉ tiêu
                                                              RBO   
                                                              <ul>
                                                                <li><code>dsgn_tdh</code>: Doanh số giải ngân TDH từ KHCN</li>
                                                                <li><code>trdn_nhbq</code>: Dư nợ NH bình quân tăng ròng từ KHCN </li>
                                                                <li><code>the_td</code>: Thẻ tín dụng </li>
                                                                <li><code>hd_khcn</code>: Huy động mới từ KHCN </li>
                                                                <li><code>combo</code>: Tài khoản Combo </li>
                                                                <li><code>dt_bh</code>: Doanh thu phí bảo hiểm </li>
                                                                <li><code>sl_khcn_new_active</code>: Số lượng KHCN acitve mới </li>
                                                              </ul>

@apiSuccess {Float}             data.target_detail.revenue    Doanh số thực hiện   
@apiSuccess {Float}             data.target_detail.completion_rate Tỷ lệ hoàn thành  


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_id": "7e48aedd-f5e0-44c9-a75f-23bbe491d101",
            "staff_title": "GDV",
            "staff_level": "GDV",
            "target_detail": [
                {
                    "target_code": "dsgntdh",
                    "revenue": 200000000,
                    "completion_rate": 82.1
                }
            ]
        }
    ]
}
"""

# ---------------------------------------------
# Kết quả thực hiện KPIs của SRBO/ RBO theo nhóm
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/target-performance/rbo Kết quả thực hiện KPIs của SRBO/RBO theo nhóm
@apiDescription 31. Kết quả thực hiện KPIs của SRBO/ RBO theo nhóm   
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardTargetPerformanceRBO

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam (Query:) {string} [order_by]                       Sắp xếp theo thuộc tính nào. 
                                                             <ul>
                                                                <li><code>completion_rate</code> : Tỷ lệ hoàn thành</li>
                                                                <li><code>sales_plan</code> : Điểm sales plan</li>
                                                                <li><code>total_point_performance</code> : Điểm thực hiện</li>
                                                             </ul>
@apiParam (Query:) {string} [order_type]                    Kiểu sắp xếp
                                                            <ul>
                                                                <li><code>asc</code>: Tăng dần</li>
                                                                <li><code>desc</code>: Giảm dần</li>
                                                            </ul>      

@apiParam  (Body:)     {String}   report_date                 Ngày báo cáo (Format: %Y-%m-%d)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
@apiParam  (Body:)     {Array}   [staff_titles]                Danh sách chức danh nhân viên ở bộ lọc chung
@apiParam  (Body:)     {Array}    [staff_ids]                 Danh sách ID nhân viên
@apiParam  (Body:)     {String}   [manage_id]                ID quản lý (ID SRBO)
                                                            <br>
                                                             Trường hợp manage_id = <code>no_group</code> lấy danh sách SRBO không có quản lý
@apiParam  (Body:)     {Array}   [department_id]              ID phòng kinh doanh
@apiParam  (Body:)     {Array}    [business_unit]             Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}    [region]              	  Danh sách khu vực


@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "staff_title": "KHCN"
}

@apiSuccess {Array}             data                          Dữ liệu trả về
@apiSuccess {String}            data.staff_id                 ID nhân viên   
@apiSuccess {String}            data.staff_title              Chức danh nhân viên   
@apiSuccess {Float}             data.completion_rate          Tỷ lệ hoàn thành
@apiSuccess {Float}             data.number_manager_customer  Số lượng khách hàng quản lý
@apiSuccess {Float}             [data.outstanding_debt]         Dư nợ quản lý
@apiSuccess {Float}             [data.mobilize_capital]         Huy động vốn quản lý

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_id": "7e48aedd-f5e0-44c9-a75f-23bbe491d101",
            "staff_title": "SRBO",
            "completion_rate": 50.2,
            "number_manager_customer": 12,
            "outstanding_debt": 121235.2,
            "mobilize_capital": 100001.2
        }
    ]
}
"""

# ---------------------------------------------
# Tỷ lệ RBO/CSO hoàn thành chỉ tiêu theo phân khúc
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/completion-rate/segment Tỷ lệ RBO/CSO hoàn thành chỉ tiêu theo phân khúc
@apiDescription Tỷ lệ RBO/CSO hoàn thành chỉ tiêu theo phân khúc   
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardCompletionRateSegment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_date                 Ngày báo cáo (Format: %Y-%m-%d)
@apiParam  (Body:)     {String}   staff_title                 Chức danh nhân viên
                                                              <ul>
                                                                <li><code>RBO</code></li>
                                                                <li><code>CSO</code></li>
                                                              </ul>
@apiParam  (Body:)     {Array}   [staff_titles]               Danh sách chức danh nhân viên ở bộ lọc chung
@apiParam  (Body:)     {Array}   [department_id]              ID phòng kinh doanh
@apiParam  (Body:)     {Array}   [manage_id]                  ID trưởng nhóm
@apiParam  (Body:)     {Array}   [business_unit]              Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}   [region]              	      Danh sách khu vực


@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "staff_title": "RBO"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Number}            data.total                    Tổng số lượng nhân viên
@apiSuccess {Array}             data.detail                   Chi tiết tỉ lệ hoàn thành theo từng khoảng
@apiSuccess {String}            data.detail.type              Khoảng dữ liệu
                                                              <ul>
                                                                <li><code>lt70</code> : Hoàn thành dưới 70 %</li>
                                                                <li><code>gte70_lt100</code> : Từ 70 - 100</li>
                                                                <li><code>gte100</code> : Lớn hơn 100</li>
                                                              </ul>
@apiSuccess {Number}            data.detail.number_completion Số lượng nhân viên hoàn thành theo tỷ lệ                                                              
@apiSuccess {Number}            data.detail.rate              Chiếm tỷ lệ                                                              


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "total": 527,
        "detail": [
            {
                "type": "lt70",
                "number_completion": 100,
                "rate": 19
            },
            {
                "type": "gte70_lt100",
                "number_completion": 300,
                "rate": 57
            },
            {
                "type": "gte100",
                "number_completion": 127,
                "rate": 24
            }
        ]
    }
}
"""

# ---------------------------------------------
# Tỷ lệ RBO/CSO hoàn thành chỉ tiêu theo phân khúc
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/completion-rate/segment/title Tỷ lệ CBBH hoàn thành chỉ tiêu theo phân khúc (theo chức danh)
@apiDescription Tỷ lệ CBBH hoàn thành chỉ tiêu theo phân khúc (Theo chức danh: GDV/KSV, ...)   
@apiGroup Dashboard EIB
@apiVersion 1.0.0
@apiName DashboardCompletionRateSegmentTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   report_date            Ngày báo cáo (Format: %Y-%m-%d) (Mặc định lấy theo ngày hiện tại)
@apiParam  (Body:)     {String}   card_title          Chức danh cho card báo cáo
                                                      <ul>
                                                         <li><code>GDV_KSV</code>: GDV/KSV</li>
                                                      </ul>                                                
@apiParam  (Body:)     {Array}    [title]              Chức danh
                                                       <ul>
                                                        <li><code> RBO </code></li>
                                                        <li><code> SRBO </code></li>
                                                        <li><code> CSO </code></li>
                                                        <li><code> RM </code></li>                                            
                                                        <li><code> SRM </code></li>                                            
                                                        <li><code> GDV </code></li>                                            
                                                        <li><code> KSV </code></li>                                            
                                                      </ul>
@apiParam  (Body:)     {Array}   [department_id]              ID phòng kinh doanh
@apiParam  (Body:)     {Array}   [manage_id]                  ID trưởng nhóm
@apiParam  (Body:)     {Array}   [business_unit]              Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}   [region]              	      Danh sách khu vực



@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "card_title": "GDV_KSV"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Number}            data.total                    Tổng số lượng nhân viên
@apiSuccess {Array}             data.detail                   Chi tiết tỉ lệ hoàn thành theo từng khoảng
@apiSuccess {String}            data.detail.type              Khoảng dữ liệu
                                                              <ul>
                                                                <li><code>lt70</code> : Hoàn thành dưới 70 %</li>
                                                                <li><code>gte70_lt100</code> : Từ 70 - 100</li>
                                                                <li><code>gte100</code> : Lớn hơn 100</li>
                                                              </ul>
@apiSuccess {Number}            data.detail.number_completion Số lượng nhân viên hoàn thành theo tỷ lệ                                                              
@apiSuccess {Number}            data.detail.rate              Chiếm tỷ lệ                                                              


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "total": 527,
        "detail": [
            {
                "type": "lt70",
                "number_completion": 100,
                "rate": 19
            },
            {
                "type": "gte70_lt100",
                "number_completion": 300,
                "rate": 57
            },
            {
                "type": "gte100",
                "number_completion": 127,
                "rate": 24
            }
        ]
    }
}
"""

# --------------- LEVEL 3 ----------------

# ---------------------------------------------
# Tỉ lệ CBBH hoàn thành KPIs (tổng các chức danh)
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level3/completion-rate Tỉ lệ CBBH hoàn thành KPIs (tổng các chức danh)
@apiDescription 23. Tỉ lệ CBBH hoàn thành KPIs (tổng các chức danh)
@apiGroup Dashboard EIB - Level3
@apiVersion 1.0.0
@apiName DashboardLevel3CompletionRate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam  (Body:)     {String}   [report_month]              Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {String}   type                        <ul>
                                                                <li><code>current</code> : Tháng hiện tại</li>
                                                                <li><code>cumulative</code> : Luỹ kế từ đầu năm</li>
                                                              <ul>
@apiParam  (Body:)     {Array}    [business_unit]             Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}    [region]              	  Danh sách khu vực                                                              

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "type": "current"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Object}            data.completion_rate               Thông số tỷ lệ hoàn thành
@apiSuccess {Object}            data.completion_rate.type        Loại
                                                              <ul>
                                                                <li><code>department</code> : Đơn vị kinh doanh</li>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                              </ul>
@apiSuccess {Number}            data.completion_rate.type.total         Tổng số CBBH
@apiSuccess {Number}            data.completion_rate.type.complete      Số lượng CBBH hoàn thành KPI 
@apiSuccess {Number}            data.completion_rate.type.completion_rate  Tỷ lệ hoàn CBBH hoàn thành KPI 

@apiSuccess {Object}            data.rank                   Thông số xếp hạng
@apiSuccess {Object}            data.rank.type        Loại
                                                              <ul>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                              </ul>
@apiSuccess {Number}            data.rank.type.total         Tổng số ĐVKD
@apiSuccess {Number}            data.rank.type.point          Xếp hạng của ĐVKD
@apiSuccess {String}            data.rank.type.name          Tên loại

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "completion_rate": {
            "department": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            },
            "region": {
                "total": 100,
                "complete": 50,
                "completion_rate": 50
            },
            "system": {
                "total": 1000,
                "complete": 825,
                "completion_rate": 82.5
            }
        },
        "rank": {
            "region": {
                "name": "Khu vực TPHCM",
                "total": 49,
                "point": 19
            },
            "system": {
                "total": 207,
                "point": 93
            }
        }
        
    }
}
"""

# ---------------------------------------------
# Tỉ lệ CBBH hoàn thành KPIs (theo chức danh)
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level3/completion-rate/staff-title Tỉ lệ CBBH hoàn thành KPIs (theo chức danh)
@apiDescription 24. Tỉ lệ CBBH hoàn thành KPIs (theo chức danh)
@apiGroup Dashboard EIB - Level3
@apiVersion 1.0.0
@apiName DashboardLevel3CompletionRateStaffTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam  (Body:)     {String}   [report_month]              Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {String}   type                        <ul>
                                                                <li><code>current</code> : Tháng hiện tại</li>
                                                                <li><code>cumulative</code> : Luỹ kế từ đầu năm</li>
                                                              <ul>
@apiParam  (Body:)     {Array}   [department_id]             ID phòng kinh doanh
@apiParam  (Body:)     {Array}   [manage_id]                 ID trưởng nhóm
@apiParam  (Body:)     {Array}   [business_unit]             Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}   [region]              	  Danh sách khu vực

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "type": "current",
    "department_id": ["656c3b77-1657-43b9-bc55-3e941e659de9"],
    "manage_id": ["17b465e3-20ef-4abe-8b48-b65574919f9e"]
}

@apiSuccess {Array}             data                          Dữ liệu trả về

@apiSuccess {String}            data.staff_title        Chức danh
@apiSuccess {Number}            data.total         Tổng số CBBH
@apiSuccess {Number}            data.complete      Số lượng CBBH hoàn thành KPI 
@apiSuccess {Number}            data.completion_rate  Tỷ lệ hoàn CBBH hoàn thành KPI 

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_title": "RBO",
            "total": 10,
            "complete": 6,
            "completion_rate": 60
        },
        {
            "staff_title": "SRBO",
            "total": 10,
            "complete": 6,
            "completion_rate": 60
        },
        {
            "staff_title": "RM",
            "total": 10,
            "complete": 6,
            "completion_rate": 60
        },
        {
            "staff_title": "SRM",
            "total": 10,
            "complete": 6,
            "completion_rate": 60
        },
        {
            "staff_title": "CSO",
            "total": 10,
            "complete": 6,
            "completion_rate": 60
        }
    ]
}
"""

# ---------------------------------------------
# Xếp hạng CBBH hoàn thành KPIs theo chức danh
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level3/rank/completion-rate Xếp hạng CBBH hoàn thành KPIs theo chức danh
@apiDescription 32. Xếp hạng CBBH hoàn thành KPIs theo chức danh
@apiGroup Dashboard EIB - Level3
@apiVersion 1.0.0
@apiName DashboardLevel3RankCompletionRate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_month]              Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {Array}    [business_unit]             Danh sách đơn vị kinh doanh
@apiParam  (Body:)     {Array}    [region]              	  Danh sách khu vực
@apiParam  (Body:)     {Array}    [staff_title]               Danh sách chức danh

@apiParamExample {json} Body example
{
    "report_month": "2023-10"
}

@apiSuccess {Object}            data                       Dữ liệu trả về

@apiSuccess {Array}             data.header                Thông tin header
@apiSuccess {String}            data.header.type           Loại
                                                            <ul>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>tier</code> : Tier</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                              </ul>
@apiSuccess {String}            data.header.name           Tên

@apiSuccess {Array}             data.rank_info       Dữ liệu xếp hạng
@apiSuccess {String}            data.rank_info.staff_title            Chức danh
@apiSuccess {Object}            data.rank_info.index_type             Xếp hạng theo loại
@apiSuccess {Object}            data.rank_info.index_type.type        Loại
                                                              <ul>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>tier</code> : Tier</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                              </ul>

@apiSuccess {Number}            data.rank_info.index_type..total         Tổng số ĐVKD
@apiSuccess {Number}            data.rank_info.index_type..point        Xếp hạng ĐVKD 

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "header": [
            {
                "name": "Tier 01",
                "type": "tier"
            },
            {
                "name": "Khu vực miền Đông Nam Bộ",
                "type": "region"
            },
            {
                "name": "system",
                "type": "system"
            }
        ],
        "rank_info": [
            {
                "index_type": {
                    "region": {
                        "point": 2,
                        "total": 19
                    },
                    "system": {
                        "point": 6,
                        "total": 80
                    },
                    "tier": {
                        "point": 1,
                        "total": 8
                    }
                },
                "staff_title": "SRBO"
            },
            {
                "index_type": {
                    "region": {
                        "point": 4,
                        "total": 21
                    },
                    "system": {
                        "point": 18,
                        "total": 215
                    },
                    "tier": {
                        "point": 2,
                        "total": 10
                    }
                },
                "staff_title": "RBO"
            },
            {
                "index_type": {
                    "region": {
                        "point": 7,
                        "total": 18
                    },
                    "system": {
                        "point": 40,
                        "total": 136
                    },
                    "tier": {
                        "point": 9,
                        "total": 11
                    }
                },
                "staff_title": "RM"
            },
            {
                "index_type": {
                    "region": {
                        "point": 1,
                        "total": 4
                    },
                    "system": {
                        "point": 1,
                        "total": 27
                    },
                    "tier": {
                        "point": 1,
                        "total": 11
                    }
                },
                "staff_title": "SRM"
            },
            {
                "index_type": {
                    "region": {
                        "point": 6,
                        "total": 15
                    },
                    "system": {
                        "point": 59,
                        "total": 197
                    },
                    "tier": {
                        "point": 3,
                        "total": 10
                    }
                },
                "staff_title": "CSO"
            }
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Năng suất bình quân của CBBH theo chỉ tiêu
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level3/average-productivity/target Năng suất bình quân của CBBH theo chỉ tiêu
@apiDescription 25. Năng suất thực hiện theo từng chỉ tiêu
@apiGroup Dashboard EIB - Level3
@apiVersion 1.0.0
@apiName DashboardLevel3AverageProductivityTarget

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_date]            Ngày báo cáo (Format: %Y-%m-%d) (Mặc định lấy theo ngày hiện tại)
@apiParam  (Body:)     {String}   staff_title           Chức danh
                                                        <ul>
                                                            <li>RBO</li>
                                                            <li>CSO</li>
                                                            <li>RM</li>
                                                            <li>SRM</li>
                                                        </ul>

@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "staff_title": "RBO"
}

@apiSuccess {Array}            data                       Dữ liệu trả về

@apiSuccess {String}            data.target_code              Mã chỉ tiêu   
@apiSuccess {String}            data.target_name              Tên chỉ tiêu   
@apiSuccess {String}            data.unit                     Đơn vị tính   
@apiSuccess {Object}            data.index_type               Doanh số theo loại   
@apiSuccess {Object}            data.index_type.{type}        Loại doanh số
                                                              <ul>
                                                                <li><code>department</code> : Đơn vị kinh doanh</li>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                              </ul>
@apiSuccess {Number}            data.index_type.type.total_revenue         Tổng doanh số 
@apiSuccess {Number}            data.index_type.type.number_staff        Số lượng nhân viên 
@apiSuccess {Number}            data.index_type.type.average_revenue       Doanh số bình quân 

@apiSuccess {String}            message                   Mô tả phản hồi
@apiSuccess {Integer}           code                      Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "index_type": {
                "department": {
                    "average_revenue": 0.0,
                    "number_staff": 3,
                    "total_revenue": 0.0
                },
                "region": {
                    "average_revenue": 0.0,
                    "number_staff": 67,
                    "total_revenue": 0.0
                },
                "system": {
                    "average_revenue": 0.006450730688935281,
                    "number_staff": 479,
                    "total_revenue": 3.0898999999999996
                }
            },
            "target_code": "dsgn_tdh",
            "target_name": "Doanh số giải ngân trung dài hạn",
            "unit": "VNĐ"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Năng suất bình quân của CBBH theo chỉ tiêu
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level3/average-productivity/target/title Năng suất bình quân của CBBH theo chỉ tiêu (theo chức danh)
@apiDescription 25. Năng suất thực hiện theo từng chỉ tiêu theo từng loại chức danh
@apiGroup Dashboard EIB - Level3
@apiVersion 1.0.0
@apiName DashboardLevel3AverageProductivityTargetTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_date]            Ngày báo cáo (Format: %Y-%m-%d) (Mặc định lấy theo ngày hiện tại)
@apiParam  (Body:)     {String}   card_title          Chức danh cho card báo cáo
                                                      <ul>
                                                         <li><code>GDV_KSV</code>: GDV/KSV</li>
                                                      </ul>                                                
@apiParam  (Body:)     {Array}    [title]              Chức danh
                                                       <ul>
                                                        <li><code> RBO </code></li>
                                                        <li><code> SRBO </code></li>
                                                        <li><code> CSO </code></li>
                                                        <li><code> RM </code></li>                                            
                                                        <li><code> SRM </code></li>                                            
                                                        <li><code> GDV </code></li>                                            
                                                        <li><code> KSV </code></li>                                            
                                                      </ul>

@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "card_title": "GDV_KSV"
}

@apiSuccess {Array}            data                       Dữ liệu trả về

@apiSuccess {String}            data.target_code              Mã chỉ tiêu   
@apiSuccess {String}            data.target_name              Tên chỉ tiêu   
@apiSuccess {String}            data.unit                     Đơn vị tính   
@apiSuccess {Object}            data.index_type               Doanh số theo loại   
@apiSuccess {Object}            data.index_type.{type}        Loại doanh số
                                                              <ul>
                                                                <li><code>department</code> : Đơn vị kinh doanh</li>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                              </ul>
@apiSuccess {Number}            data.index_type.type.total_revenue         Tổng doanh số 
@apiSuccess {Number}            data.index_type.type.number_staff        Số lượng nhân viên 
@apiSuccess {Number}            data.index_type.type.average_revenue       Doanh số bình quân 

@apiSuccess {String}            message                   Mô tả phản hồi
@apiSuccess {Integer}           code                      Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "index_type": {
                "department": {
                    "average_revenue": 0.0,
                    "number_staff": 3,
                    "total_revenue": 0.0
                },
                "region": {
                    "average_revenue": 0.0,
                    "number_staff": 67,
                    "total_revenue": 0.0
                },
                "system": {
                    "average_revenue": 0.006450730688935281,
                    "number_staff": 479,
                    "total_revenue": 3.0898999999999996
                }
            },
            "target_code": "dsgn_tdh",
            "target_name": "Doanh số giải ngân trung dài hạn",
            "unit": "VNĐ"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# --------------- END LEVEL 3 ----------------

# --------------- LEVEL 2 ----------------

# ---------------------------------------------
# TOP 5 ĐVKD có hiệu suất cao nhất và thấp nhất theo tỉ lệ hoàn thành chỉ tiêu
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level2/top5/department/target Top5 ĐVKD có hiệu suất cao nhất và thấp nhất theo tỉ lệ hoàn thành chỉ tiêu
@apiDescription 33. Top 5 ĐVKD có hiệu suất cao nhất và thấp nhất theo tỉ lệ hoàn thành chỉ tiêu 
@apiGroup Dashboard EIB - Level2
@apiVersion 1.0.0
@apiName DashboardLevel2Top5DepartmentTarget

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_month]        Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {String}   staff_title           Chức danh
                                                        <ul>
                                                            <li>RBO</li>
                                                            <li>CSO</li>
                                                        </ul>

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO"
}

@apiSuccess {Array}            data                       Dữ liệu trả về

@apiSuccess {String}            data.target_code              Mã chỉ tiêu   
@apiSuccess {String}            data.target_name              Tên chỉ tiêu   
@apiSuccess {String}            data.unit                     Đơn vị tính   
@apiSuccess {Object}            data.rank_info                Xếp hạng đơn vị
@apiSuccess {Array}             data.rank_info.type           Loại
                                                              <ul>
                                                                <li><code>highest</code> : Cao nhất</li>
                                                                <li><code>lowest</code> : Thấp nhất</li>
                                                              <ul>
@apiSuccess {String}            data.rank_info.type.branch_code Mã đơn vị kinh doanh               
@apiSuccess {Number}            data.rank_info.type.rank      Xếp hạng               
@apiSuccess {Number}            data.rank_info.type.revenue   Doanh số               
@apiSuccess {Number}            data.rank_info.type.number_staff   Số lượng nhân viên theo chức danh          
@apiSuccess {Number}            data.rank_info.type.avg_revenue   Doanh số bình quân               

@apiSuccess {String}            message                   Mô tả phản hồi
@apiSuccess {Integer}           code                      Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "rank_info": {
                "highest": [
                    {
                        "branch_code": "1903",
                        "rank": 1,
                        "revenue": 12,
                        "number_staff": 1,
                        "avg_revenue": 12
                    },
                    {
                        "branch_code": "1624",
                        "rank": 2,
                        "revenue": 10,
                        "number_staff": 1,
                        "avg_revenue": 10
                    }
                ],
                "lowest": [
                    {
                        "branch_code": "1212",
                        "rank": 1,
                        "revenue": 1,
                        "number_staff": 1,
                        "avg_revenue": 1
                    },
                    {
                        "branch_code": "1905",
                        "rank": 2,
                        "revenue": 2,
                        "number_staff": 1,
                        "avg_revenue": 2
                    }
                ]
            },
            "target_code": "dsgn_tdh",
            "target_name": "Doanh số giải ngân trung dài hạn",
            "unit": "Tỷ đồng"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# TOP 5 ĐVKD có hiệu suất cao nhất và thấp nhất theo tỉ lệ hoàn thành chỉ tiêu
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level2/top5/department/target/title Top5 ĐVKD có hiệu suất cao nhất và thấp nhất theo tỉ lệ hoàn thành chỉ tiêu (Theo chức danh)
@apiDescription 33. Top 5 ĐVKD có hiệu suất cao nhất và thấp nhất theo tỉ lệ hoàn thành chỉ tiêu (Theo chức danh GDV/KSV, ...)
@apiGroup Dashboard EIB - Level2
@apiVersion 1.0.0
@apiName DashboardLevel2Top5DepartmentTargetTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_month]        Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {String}   card_title          Chức danh cho card báo cáo
                                                      <ul>
                                                         <li><code>GDV_KSV</code>: GDV/KSV</li>
                                                      </ul>                                                
@apiParam  (Body:)     {Array}    [title]              Chức danh
                                                       <ul>
                                                        <li><code> RBO </code></li>
                                                        <li><code> SRBO </code></li>
                                                        <li><code> CSO </code></li>
                                                        <li><code> RM </code></li>                                            
                                                        <li><code> SRM </code></li>                                            
                                                        <li><code> GDV </code></li>                                            
                                                        <li><code> KSV </code></li>                                            
                                                      </ul>                                                        

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "card_title": "GDV_KSV"
}

@apiSuccess {Array}            data                       Dữ liệu trả về

@apiSuccess {String}            data.target_code              Mã chỉ tiêu   
@apiSuccess {String}            data.target_name              Tên chỉ tiêu   
@apiSuccess {String}            data.unit                     Đơn vị tính   
@apiSuccess {Object}            data.rank_info                Xếp hạng đơn vị
@apiSuccess {Array}             data.rank_info.type           Loại
                                                              <ul>
                                                                <li><code>highest</code> : Cao nhất</li>
                                                                <li><code>lowest</code> : Thấp nhất</li>
                                                              <ul>
@apiSuccess {String}            data.rank_info.type.branch_code Mã đơn vị kinh doanh               
@apiSuccess {Number}            data.rank_info.type.rank      Xếp hạng               
@apiSuccess {Number}            data.rank_info.type.revenue   Doanh số               
@apiSuccess {Number}            data.rank_info.type.number_staff   Số lượng nhân viên theo chức danh          
@apiSuccess {Number}            data.rank_info.type.avg_revenue   Doanh số bình quân               

@apiSuccess {String}            message                   Mô tả phản hồi
@apiSuccess {Integer}           code                      Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "rank_info": {
                "highest": [
                    {
                        "branch_code": "1903",
                        "rank": 1,
                        "revenue": 12,
                        "number_staff": 1,
                        "avg_revenue": 12
                    },
                    {
                        "branch_code": "1624",
                        "rank": 2,
                        "revenue": 10,
                        "number_staff": 1,
                        "avg_revenue": 10
                    }
                ],
                "lowest": [
                    {
                        "branch_code": "1212",
                        "rank": 1,
                        "revenue": 1,
                        "number_staff": 1,
                        "avg_revenue": 1
                    },
                    {
                        "branch_code": "1905",
                        "rank": 2,
                        "revenue": 2,
                        "number_staff": 1,
                        "avg_revenue": 2
                    }
                ]
            },
            "target_code": "dsgn_tdh",
            "target_name": "Doanh số giải ngân trung dài hạn",
            "unit": "Tỷ đồng"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# TOP 5 ĐVKD có tỷ lệ RM hoàn thành KPIs cao nhất và thấp nhât
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level2/top5/rm Top 5 ĐVKD có tỉ lệ RM hoàn thành KPis cao nhất và thấp nhất
@apiDescription 33. Top 5 ĐVKD có tỉ ệ RM hoàn thành KPIs cao nhất và thấp nhất 
@apiGroup Dashboard EIB - Level2
@apiVersion 1.0.0
@apiName DashboardLevel2Top5RM

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_month]         Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {Array}   [staff_title]                  Danh sách chức danh

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
}

@apiSuccess {Array}             data                       Dữ liệu trả về

@apiSuccess {Object}            data.rank_info                Xếp hạng đơn vị
@apiSuccess {Array}             data.rank_info.type           Loại
                                                              <ul>
                                                                <li><code>highest</code> : Cao nhất</li>
                                                                <li><code>lowest</code> : Thấp nhất</li>
                                                              <ul>
@apiSuccess {String}            data.rank_info.type.branch_code Mã đơn vị kinh doanh               
@apiSuccess {Number}            data.rank_info.type.rank      Xếp hạng               
@apiSuccess {Number}            data.rank_info.type.completion_rate   Tỷ lệ hoàn thành               

@apiSuccess {String}            message                   Mô tả phản hồi
@apiSuccess {Integer}           code                      Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "rank_info": {
                "highest": [
                    {
                        "branch_code": "1903",
                        "rank": 1,
                        "completion_rate": 100
                    },
                    {
                        "branch_code": "1624",
                        "rank": 2,
                        "completion_rate": 96.78
                    }
                ],
                "lowest": [
                    {
                        "branch_code": "1212",
                        "rank": 1,
                        "completion_rate": 1.08
                    },
                    {
                        "branch_code": "1905",
                        "rank": 2,
                        "completion_rate": 8
                    }
                ]
            }
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Tỷ lệ CBBH hoàn thành KPIs (tổng các chức danh)
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level2/completion-rate Tỉ lệ CBBH hoàn thành KPIs (tổng các chức danh)
@apiDescription 23. Tỉ lệ CBBH hoàn thành KPIs (tổng các chức danh)
@apiGroup Dashboard EIB - Level2
@apiVersion 1.0.0
@apiName DashboardLevel2CompletionRate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam  (Body:)     {String}   [report_month]              Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {String}   type                        <ul>
                                                                <li><code>current</code> : Tháng hiện tại</li>
                                                                <li><code>cumulative</code> : Luỹ kế từ đầu năm</li>
                                                              <ul>
@apiParam  (Body:)     {Array}   [business_unit]              Danh sách đơn vị kinh doanh                                                              

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "type": "current"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Object}            data.completion_rate          Thông số tỷ lệ hoàn thành
@apiSuccess {Object}            data.completion_rate.type     Loại
                                                              <ul>
                                                                <li><code>department</code> : Đơn vị kinh doanh</li>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                              </ul>
@apiSuccess {Number}            data.completion_rate.type.total         Tổng số CBBH
@apiSuccess {Number}            data.completion_rate.type.complete      Số lượng CBBH hoàn thành KPI 
@apiSuccess {Number}            data.completion_rate.type.completion_rate  Tỷ lệ hoàn CBBH hoàn thành KPI 

@apiSuccess {Object}            data.rank                   Thông số xếp hạng
@apiSuccess {Object}            data.rank.type              Loại
                                                              <ul>
                                                                <li><code>region_system</code> : Khu vực trên toàn hệ thống</li>
                                                                <li><code>department_region</code> : ĐVKD trong khu vực</li>
                                                                <li><code>department_system</code> : ĐVKD trên toàn hệ thống</li>
                                                              </ul>
@apiSuccess {Number}            data.rank.type.total         Tổng số ĐVKD
@apiSuccess {Number}            data.rank.type.point         Xếp hạng của ĐVKD
@apiSuccess {String}            data.rank.type.name          Tên loại

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "completion_rate": {
            "region": {
                "total": 100,
                "complete": 50,
                "completion_rate": 50
            },
            "system": {
                "total": 1000,
                "complete": 825,
                "completion_rate": 82.5
            }
        },
        "rank": {
            "region": {
                "total": 49,
                "point": 19
            }
        }
        
    }
}
"""

# ---------------------------------------------
# Tỉ lệ CBBH hoàn thành KPIs (theo chức danh)
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level2/completion-rate/staff-title Tỉ lệ CBBH hoàn thành KPIs (theo chức danh)
@apiDescription 24. Tỉ lệ CBBH hoàn thành KPIs (theo chức danh)
@apiGroup Dashboard EIB - Level2
@apiVersion 1.0.0
@apiName DashboardLevel2CompletionRateStaffTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam  (Body:)     {String}   [report_month]              Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {String}   type                        <ul>
                                                                <li><code>current</code> : Tháng hiện tại</li>
                                                                <li><code>cumulative</code> : Luỹ kế từ đầu năm</li>
                                                              <ul>
@apiParam  (Body:)     {Array}   [business_unit]               Danh sách đơn vị kinh doanh                                                              
@apiParam  (Body:)     {Array}   [staff_title]                  Danh sách chức danh                                                              

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "type": "current"
}

@apiSuccess {Array}             data                          Dữ liệu trả về

@apiSuccess {String}            data.staff_title        Chức danh
@apiSuccess {Object}            data.index_type.{type}        Loại
                                                              <ul>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                              </ul>
@apiSuccess {Number}            data.index_type.total         Tổng số CBBH
@apiSuccess {Number}            data.index_type.complete      Số lượng CBBH hoàn thành KPI 
@apiSuccess {Number}            data.index_type.completion_rate  Tỷ lệ hoàn CBBH hoàn thành KPI 

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_title": "RBO",
            "system": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            },
            "region": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            }
            
        },
        {
            "staff_title": "SRBO",
            "system": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            },
            "region": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            }
        },
        {
            "staff_title": "RM",
            "system": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            },
            "region": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            }
        },
        {
            "staff_title": "SRM",
            "system": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            },
            "region": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            }
        },
        {
            "staff_title": "CSO",
            "system": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            },
            "region": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            }
        }
    ]
}
"""

# ---------------------------------------------
# Tỷ lệ RM/SRM hoàn thành KPIs theo ĐVKD
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level2/completion-rate/rm/business-unit Tỷ lệ RM/SRM hoàn thành chỉ tiêu theo DVKD
@apiDescription 19. Tỷ lệ RM/SRM hoàn thành KPIs theo ĐVKD
@apiGroup Dashboard EIB - Level2
@apiVersion 1.0.0
@apiName DashboardLevel2CompletionRateRMDVKD

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam  (Body:)     {String}   [report_month]              Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {Array}   [business_unit]              Danh sách đơn vị kinh doanh                                                              
@apiParam  (Body:)     {Array}   [staff_title]                Danh sách chức danh                                                              

@apiParamExample {json} Body example
{
    "report_month": "2023-10"
}

@apiSuccess {Object}             data                          Dữ liệu trả về

@apiSuccess {Object}            data.region                   Dữ liệu tổng khu vực
@apiSuccess {Number}            data.region.total             Tổng số lượng RM theo khu vực
@apiSuccess {Array}             data.region.detail            Chi tiết tỉ lệ hoàn thành theo từng khoảng
@apiSuccess {String}            data.region.detail.type              Khoảng dữ liệu
                                                              <ul>
                                                                <li><code>lt40</code> : Nhỏ hơn 40 %</li>
                                                                <li><code>gte40_lt70</code> : Từ 40 - 70</li>
                                                                <li><code>gte79_lte100</code> : Từ 70 - 100</li>
                                                                <li><code>gt100</code> : Lớn hơn 100</li>
                                                              </ul>
@apiSuccess {Number}            data.region.detail.number_completion Số lượng nhân viên hoàn thành theo tỷ lệ                                                              
@apiSuccess {Number}            data.region.detail.rate              Chiếm tỷ lệ                                                              

@apiSuccess {Array}             data.business_unit            Dữ liệu theo đơn vị kinh doanh
@apiSuccess {String}            data.business_unit.branch_code   Mã ĐVKD
@apiSuccess {String}            data.business_unit.total      Tổng số RM
@apiSuccess {Array}             data.business_unit.detail     Chi tiết tỉ lệ hoàn thành theo từng khoảng
@apiSuccess {String}            data.business_unit.detail.type              Khoảng dữ liệu
                                                              <ul>
                                                                <li><code>lt40</code> : Nhỏ hơn 40 %</li>
                                                                <li><code>gte40_lt70</code> : Từ 40 - 70</li>
                                                                <li><code>gte79_lte100</code> : Từ 70 - 100</li>
                                                                <li><code>gt100</code> : Lớn hơn 100</li>
                                                              </ul>
@apiSuccess {Number}            data.business_unit.detail.number_completion Số lượng nhân viên hoàn thành theo tỷ lệ                                                              
@apiSuccess {Number}            data.business_unit.detail.rate              Chiếm tỷ lệ             

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "region": {
            "total": 4800,
            "detail": [
                {
                    "type": "lt40",
                    "number_completion": 2400,
                    "rate": 40
                }
            ]
        },
        "business_unit": [
            {
                "branch_code": "1400",
                "total": 1200,
                "detail": [
                    {
                        "type": "lt40",
                        "number_completion": 600,
                        "rate": 40
                    }
                ]
            }
        ]
    }
}
"""

# ---------------------------------------------
# Tỉ lệ RM/SRM hoàn thành từng chỉ tiêu KPIs
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level2/completion-rate/rm/target Tỉ lệ RM/SRM hoàn thành từng chỉ tiêu KPIs
@apiDescription 21. Tỉ lệ RM hoàn thành từng chỉ tiêu KPIs
@apiGroup Dashboard EIB - Level2
@apiVersion 1.0.0
@apiName DashboardLevel2CompletionRateRMTarget

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam  (Body:)     {String}  [report_month]               Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {String}  type                         View Type
                                                              <ul>
                                                                <li><code>month</code>: Tháng hiện tại</li>
                                                                <li><code>average_year</code>: Bình quân năm</li>
                                                              </ul> 
@apiParam  (Body:)     {Array}   [business_unit]              Danh sách đơn vị kinh doanh                                                              
@apiParam  (Body:)     {Array}   [staff_title]                Danh sách chức danh                                                              

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "type": "month",
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Array}             data.target                   Thông tin tỷ lệ hoàn thành theo chỉ tiêu
@apiSuccess {String}            data.target.target_code              Mã chỉ tiêu
@apiSuccess {String}            data.target.target_name              Tên chỉ tiêu
@apiSuccess {Number}            data.target.completion_rate          Tỷ lệ CBBH hoàn thành chỉ tiêu
@apiSuccess {Number}            data.target.number_complete          Số lượng CBBH hoàn thành chỉ tiêu
@apiSuccess {Number}            data.target.total                    Tổng số CBBH
@apiSuccess {Number}            data.average                   Trung bình
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "target": [
            {
                "target_code": "AB1",
                "target_name": "Tỷ lệ RBO hoàn thành chỉ tiêu saleplan",
                "completion_rate": 35.5,
                "total": 90,
                "number_complete": 32,
            }
        ],
        "average": 50
    }
}
"""

# ---------------------------------------------
# Năng suất bình quân của CBBH theo chỉ tiêu
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level2/average-productivity/target Năng suất bình quân của CBBH theo chỉ tiêu
@apiDescription 25. Năng suất thực hiện theo từng chỉ tiêu
@apiGroup Dashboard EIB - Level2
@apiVersion 1.0.0
@apiName DashboardLevel2AverageProductivityTarget

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_date]            Ngày báo cáo (Format: %Y-%m-%d) (Mặc định lấy theo ngày hiện tại)
@apiParam  (Body:)     {String}   staff_title              Chức danh
                                                        <ul>
                                                            <li>RBO</li>
                                                            <li>CSO</li>
                                                            <li>RM</li>
                                                            <li>SRM</li>
                                                        </ul>
@apiParam  (Body:)     {String}   [business_unit]           Mã đơn vị kinh doanh                                                        

@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "staff_title": "RBO"
}

@apiSuccess {Array}            data                       Dữ liệu trả về

@apiSuccess {String}            data.target_code              Mã chỉ tiêu   
@apiSuccess {String}            data.target_name              Tên chỉ tiêu   
@apiSuccess {String}            data.unit                     Đơn vị tính   
@apiSuccess {Object}            data.index_type               Doanh số theo loại   
@apiSuccess {Object}            data.index_type.{type}        Loại doanh số
                                                              <ul>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                                <li><code>department</code> : Đơn vị kinh doanh</li>
                                                              </ul>
@apiSuccess {Number}            data.index_type.type.total_revenue         Tổng doanh số 
@apiSuccess {Number}            data.index_type.type.number_staff        Số lượng nhân viên 
@apiSuccess {Number}            data.index_type.type.average_revenue       Doanh số bình quân 

@apiSuccess {String}            message                   Mô tả phản hồi
@apiSuccess {Integer}           code                      Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "index_type": {
                "region": {
                    "average_revenue": 0.0,
                    "number_staff": 67,
                    "total_revenue": 0.0
                },
                "system": {
                    "average_revenue": 0.006450730688935281,
                    "number_staff": 479,
                    "total_revenue": 3.0898999999999996
                }
            },
            "target_code": "dsgn_tdh",
            "target_name": "Doanh số giải ngân trung dài hạn",
            "unit": "VNĐ"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Năng suất bình quân của CBBH theo chỉ tiêu
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level2/average-productivity/target/title Năng suất bình quân của CBBH theo chỉ tiêu (theo chức danh)
@apiDescription 25. Năng suất thực hiện theo từng chỉ tiêu theo từng loại chức danh
@apiGroup Dashboard EIB - Level2
@apiVersion 1.0.0
@apiName DashboardLevel2AverageProductivityTargetTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_date]       Ngày báo cáo (Format: %Y-%m-%d) (Mặc định lấy theo ngày hiện tại)
@apiParam  (Body:)     {String}   card_title          Chức danh cho card báo cáo
                                                      <ul>
                                                         <li><code>GDV_KSV</code>: GDV/KSV</li>
                                                      </ul>                                                
@apiParam  (Body:)     {Array}    [title]              Chức danh
                                                       <ul>
                                                        <li><code> RBO </code></li>
                                                        <li><code> SRBO </code></li>
                                                        <li><code> CSO </code></li>
                                                        <li><code> RM </code></li>                                            
                                                        <li><code> SRM </code></li>                                            
                                                        <li><code> GDV </code></li>                                            
                                                        <li><code> KSV </code></li>                                            
                                                      </ul>
@apiParam  (Body:)     {String}   [business_unit]           Mã đơn vị kinh doanh                                                     

@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "card_title": "GDV_KSV"
}

@apiSuccess {Array}            data                       Dữ liệu trả về

@apiSuccess {String}            data.target_code              Mã chỉ tiêu   
@apiSuccess {String}            data.target_name              Tên chỉ tiêu   
@apiSuccess {String}            data.unit                     Đơn vị tính   
@apiSuccess {Object}            data.index_type               Doanh số theo loại   
@apiSuccess {Object}            data.index_type.{type}        Loại doanh số
                                                              <ul>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                                <li><code>department</code> : Đơn vị kinh doanh</li>
                                                              </ul>
@apiSuccess {Number}            data.index_type.type.total_revenue         Tổng doanh số 
@apiSuccess {Number}            data.index_type.type.number_staff        Số lượng nhân viên 
@apiSuccess {Number}            data.index_type.type.average_revenue       Doanh số bình quân 

@apiSuccess {String}            message                   Mô tả phản hồi
@apiSuccess {Integer}           code                      Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "index_type": {
                "region": {
                    "average_revenue": 0.0,
                    "number_staff": 67,
                    "total_revenue": 0.0
                },
                "system": {
                    "average_revenue": 0.006450730688935281,
                    "number_staff": 479,
                    "total_revenue": 3.0898999999999996
                }
            },
            "target_code": "dsgn_tdh",
            "target_name": "Doanh số giải ngân trung dài hạn",
            "unit": "VNĐ"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# --------------- END LEVEL 2 ----------------

# --------------- LEVEL 1  -------------------
# ---------------------------------------------
# Tỉ lệ CBBH hoàn thành KPIs (theo chức danh)
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level1/completion-rate/staff-title Tỉ lệ CBBH hoàn thành KPIs (theo chức danh)
@apiDescription 24. Tỉ lệ CBBH hoàn thành KPIs (theo chức danh)
@apiGroup Dashboard EIB - Level1
@apiVersion 1.0.0
@apiName DashboardLevel1CompletionRateStaffTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam  (Body:)     {String}   [report_month]              Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {String}   type                        <ul>
                                                                <li><code>current</code> : Tháng hiện tại</li>
                                                                <li><code>cumulative</code> : Luỹ kế từ đầu năm</li>
                                                              <ul>
@apiParam  (Body:)     {Array}   [business_unit]              Danh sách đơn vị kinh doanh                                                              
@apiParam  (Body:)     {Array}   [staff_title]                Danh sách chức danh                                                              
@apiParam  (Body:)     {String}  region                       Mã khu vực                                                              

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "type": "current",
    "region": "KVMB"
}

@apiSuccess {Array}             data                          Dữ liệu trả về

@apiSuccess {String}            data.staff_title        Chức danh
                                                        <ul>
                                                            <li>RBO</li>
                                                            <li>SRBO</li>
                                                            <li>SRM</li>
                                                            <li>RM</li>
                                                            <li>CSO</li>
                                                            <li>ALL_TITLE : Tổng CBBH</li>
                                                        </ul>
@apiSuccess {Object}            data.index_type.{type}        Loại
                                                              <ul>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                              </ul>
@apiSuccess {Number}            data.index_type.total         Tổng số CBBH
@apiSuccess {Number}            data.index_type.complete      Số lượng CBBH hoàn thành KPI 
@apiSuccess {Number}            data.index_type.completion_rate  Tỷ lệ hoàn CBBH hoàn thành KPI 

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "staff_title": "RBO",
            "system": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            },
            "region": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            }

        },
        {
            "staff_title": "SRBO",
            "system": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            },
            "region": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            }
        },
        {
            "staff_title": "RM",
            "system": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            },
            "region": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            }
        },
        {
            "staff_title": "SRM",
            "system": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            },
            "region": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            }
        },
        {
            "staff_title": "CSO",
            "system": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            },
            "region": {
                "total": 10,
                "complete": 6,
                "completion_rate": 60
            }
        }
    ]
}
"""

# ---------------------------------------------
# Năng suất bình quân của CBBH theo chỉ tiêu
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level1/average-productivity/target Năng suất bình quân của CBBH theo chỉ tiêu
@apiDescription 25. Năng suất thực hiện theo từng chỉ tiêu
@apiGroup Dashboard EIB - Level1
@apiVersion 1.0.0
@apiName DashboardLevel1AverageProductivityTarget

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_date]            Ngày báo cáo (Format: %Y-%m-%d) (Mặc định lấy theo ngày hiện tại)
@apiParam  (Body:)     {String}   staff_title              Chức danh
                                                        <ul>
                                                            <li>RBO</li>
                                                            <li>CSO</li>
                                                            <li>RM</li>
                                                            <li>SRM</li>
                                                        </ul>
@apiParam  (Body:)     {String}   [region]               Mã khu vực             
@apiParam  (Body:)     {String}   [business_unit]           Mã đơn vị kinh doanh                                                 

@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "staff_title": "RBO"
}

@apiSuccess {Array}            data                       Dữ liệu trả về

@apiSuccess {String}            data.target_code              Mã chỉ tiêu   
@apiSuccess {String}            data.target_name              Tên chỉ tiêu   
@apiSuccess {String}            data.unit                     Đơn vị tính   
@apiSuccess {Object}            data.index_type               Doanh số theo loại   
@apiSuccess {Object}            data.index_type.{type}        Loại doanh số
                                                              <ul>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                                 <li><code>department</code> : Đơn vị kinh doanh</li>
                                                              </ul>
@apiSuccess {Number}            data.index_type.type.total_revenue         Tổng doanh số 
@apiSuccess {Number}            data.index_type.type.number_staff        Số lượng nhân viên 
@apiSuccess {Number}            data.index_type.type.average_revenue       Doanh số bình quân 

@apiSuccess {String}            message                   Mô tả phản hồi
@apiSuccess {Integer}           code                      Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "index_type": {
                "region": {
                    "average_revenue": 0.0,
                    "number_staff": 67,
                    "total_revenue": 0.0
                },
                "system": {
                    "average_revenue": 0.006450730688935281,
                    "number_staff": 479,
                    "total_revenue": 3.0898999999999996
                }
            },
            "target_code": "dsgn_tdh",
            "target_name": "Doanh số giải ngân trung dài hạn",
            "unit": "VNĐ"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Năng suất bình quân của CBBH theo chỉ tiêu
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level1/average-productivity/target/title Năng suất bình quân của CBBH theo chỉ tiêu (theo chức danh)
@apiDescription 25. Năng suất thực hiện theo từng chỉ tiêu theo chức danh
@apiGroup Dashboard EIB - Level1
@apiVersion 1.0.0
@apiName DashboardLevel1AverageProductivityTargetTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_date]            Ngày báo cáo (Format: %Y-%m-%d) (Mặc định lấy theo ngày hiện tại)
@apiParam  (Body:)     {String}   card_title          Chức danh cho card báo cáo
                                                      <ul>
                                                         <li><code>GDV_KSV</code>: GDV/KSV</li>
                                                      </ul>                                                
@apiParam  (Body:)     {Array}    [title]              Chức danh
                                                       <ul>
                                                        <li><code> RBO </code></li>
                                                        <li><code> SRBO </code></li>
                                                        <li><code> CSO </code></li>
                                                        <li><code> RM </code></li>                                            
                                                        <li><code> SRM </code></li>                                            
                                                        <li><code> GDV </code></li>                                            
                                                        <li><code> KSV </code></li>                                            
                                                      </ul>
@apiParam  (Body:)     {String}   [region]               Mã khu vực                                                      
@apiParam  (Body:)     {String}   [business_unit]           Mã đơn vị kinh doanh                                                      
                                     

@apiParamExample {json} Body example
{
    "report_date": "2023-10-30",
    "staff_title": "RBO"
}

@apiSuccess {Array}            data                       Dữ liệu trả về

@apiSuccess {String}            data.target_code              Mã chỉ tiêu   
@apiSuccess {String}            data.target_name              Tên chỉ tiêu   
@apiSuccess {String}            data.unit                     Đơn vị tính   
@apiSuccess {Object}            data.index_type               Doanh số theo loại   
@apiSuccess {Object}            data.index_type.{type}        Loại doanh số
                                                              <ul>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                                 <li><code>department</code> : Đơn vị kinh doanh</li>
                                                              </ul>
@apiSuccess {Number}            data.index_type.type.total_revenue         Tổng doanh số 
@apiSuccess {Number}            data.index_type.type.number_staff        Số lượng nhân viên 
@apiSuccess {Number}            data.index_type.type.average_revenue       Doanh số bình quân 

@apiSuccess {String}            message                   Mô tả phản hồi
@apiSuccess {Integer}           code                      Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "index_type": {
                "region": {
                    "average_revenue": 0.0,
                    "number_staff": 67,
                    "total_revenue": 0.0
                },
                "system": {
                    "average_revenue": 0.006450730688935281,
                    "number_staff": 479,
                    "total_revenue": 3.0898999999999996
                }
            },
            "target_code": "dsgn_tdh",
            "target_name": "Doanh số giải ngân trung dài hạn",
            "unit": "VNĐ"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Tỷ lệ RM/SRM hoàn thành KPIs theo ĐVKD
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level1/completion-rate/rm/business-unit Tỷ lệ RM/SRM hoàn thành chỉ tiêu theo DVKD
@apiDescription 19. Tỷ lệ RM/SRM hoàn thành KPIs theo ĐVKD
@apiGroup Dashboard EIB - Level1
@apiVersion 1.0.0
@apiName DashboardLevel1CompletionRateRMDVKD

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam  (Body:)     {String}  [report_month]               Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {Array}   [region]                     Danh sách khu vực                                                              
@apiParam  (Body:)     {Array}   [business_unit]              Danh sách đơn vị kinh doanh                                                              
@apiParam  (Body:)     {Array}   [staff_title]                Danh sách chức danh                                                              

@apiParamExample {json} Body examplek
{
    "report_month": "2023-10"
}

@apiSuccess {Object}             data                          Dữ liệu trả về

@apiSuccess {Object}            data.summary                   Dữ liệu tổng hệ thống
@apiSuccess {Number}            data.summary.total             Tổng số lượng RM theo khu vực
@apiSuccess {Array}             data.summary.detail            Chi tiết tỉ lệ hoàn thành theo từng khoảng
@apiSuccess {String}            data.summary.detail.type              Khoảng dữ liệu
                                                              <ul>
                                                                <li><code>lt40</code> : Nhỏ hơn 40 %</li>
                                                                <li><code>gte40_lt70</code> : Từ 40 - 70</li>
                                                                <li><code>gte79_lte100</code> : Từ 70 - 100</li>
                                                                <li><code>gt100</code> : Lớn hơn 100</li>
                                                              </ul>
@apiSuccess {Number}            data.summary.detail.number_completion Số lượng nhân viên hoàn thành theo tỷ lệ                                                              
@apiSuccess {Number}            data.summary.detail.rate              Chiếm tỷ lệ                                                              

@apiSuccess {Array}             data.business_unit            Dữ liệu theo đơn vị kinh doanh
@apiSuccess {String}            data.business_unit.branch_code   Mã ĐVKD
@apiSuccess {String}            data.business_unit.total      Tổng số RM
@apiSuccess {Array}             data.business_unit.detail     Chi tiết tỉ lệ hoàn thành theo từng khoảng
@apiSuccess {String}            data.business_unit.detail.type              Khoảng dữ liệu
                                                              <ul>
                                                                <li><code>lt40</code> : Nhỏ hơn 40 %</li>
                                                                <li><code>gte40_lt70</code> : Từ 40 - 70</li>
                                                                <li><code>gte79_lte100</code> : Từ 70 - 100</li>
                                                                <li><code>gt100</code> : Lớn hơn 100</li>
                                                              </ul>
@apiSuccess {Number}            data.business_unit.detail.number_completion Số lượng nhân viên hoàn thành theo tỷ lệ                                                              
@apiSuccess {Number}            data.business_unit.detail.rate              Chiếm tỷ lệ             

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "summary": {
            "total": 4800,
            "detail": [
                {
                    "type": "lt40",
                    "number_completion": 2400,
                    "rate": 40
                }
            ]
        },
        "business_unit": [
            {
                "branch_code": "1400",
                "total": 1200,
                "detail": [
                    {
                        "type": "lt40",
                        "number_completion": 600,
                        "rate": 40
                    }
                ]
            }
        ]
    }
}
"""

# ---------------------------------------------
# TOP 5 ĐVKD có tỷ lệ RM hoàn thành KPIs cao nhất và thấp nhât
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level1/top5/rm Top 5 ĐVKD có tỉ lệ RM hoàn thành KPis cao nhất và thấp nhất
@apiDescription 33. Top 5 ĐVKD có tỉ lệ RM hoàn thành KPIs cao nhất và thấp nhất 
@apiGroup Dashboard EIB - Level1
@apiVersion 1.0.0
@apiName DashboardLevel1Top5RM

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_month]         Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {Array}    [region]                     Danh sách khu vực
@apiParam  (Body:)     {Array}   [staff_title]                  Danh sách chức danh

@apiParamExample {json} Body example
{
    "report_month": "2023-10"
}

@apiSuccess {Array}             data                       Dữ liệu trả về

@apiSuccess {Object}            data.rank_info                Xếp hạng đơn vị
@apiSuccess {Array}             data.rank_info.type           Loại
                                                              <ul>
                                                                <li><code>highest</code> : Cao nhất</li>
                                                                <li><code>lowest</code> : Thấp nhất</li>
                                                              <ul>
@apiSuccess {String}            data.rank_info.type.branch_code Mã đơn vị kinh doanh               
@apiSuccess {Number}            data.rank_info.type.rank      Xếp hạng               
@apiSuccess {Number}            data.rank_info.type.completion_rate   Tỷ lệ hoàn thành               

@apiSuccess {String}            message                   Mô tả phản hồi
@apiSuccess {Integer}           code                      Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "rank_info": {
                "highest": [
                    {
                        "branch_code": "1903",
                        "rank": 1,
                        "completion_rate": 100
                    },
                    {
                        "branch_code": "1624",
                        "rank": 2,
                        "completion_rate": 96.78
                    }
                ],
                "lowest": [
                    {
                        "branch_code": "1212",
                        "rank": 1,
                        "completion_rate": 1.08
                    },
                    {
                        "branch_code": "1905",
                        "rank": 2,
                        "completion_rate": 8
                    }
                ]
            }
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# TOP 5 ĐVKD có hiệu suất cao nhất và thấp nhất theo tỉ lệ hoàn thành chỉ tiêu
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level1/top5/department/target Top5 ĐVKD có hiệu suất cao nhất và thấp nhất theo tỉ lệ hoàn thành chỉ tiêu
@apiDescription 33. Top 5 ĐVKD có hiệu suất cao nhất và thấp nhất theo tỉ lệ hoàn thành chỉ tiêu 
@apiGroup Dashboard EIB - Level1
@apiVersion 1.0.0
@apiName DashboardLevel1Top5DepartmentTarget

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_month]        Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {Array}    [region]              Danh sách khu vực
@apiParam  (Body:)     {String}   staff_title           Chức danh
                                                        <ul>
                                                            <li>RBO</li>
                                                            <li>CSO</li>
                                                        </ul>

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "staff_title": "RBO"
}

@apiSuccess {Array}             data                       Dữ liệu trả về

@apiSuccess {String}            data.target_code              Mã chỉ tiêu   
@apiSuccess {String}            data.target_name              Tên chỉ tiêu   
@apiSuccess {String}            data.unit                     Đơn vị tính   
@apiSuccess {Object}            data.rank_info                Xếp hạng đơn vị
@apiSuccess {Array}             data.rank_info.type           Loại
                                                              <ul>
                                                                <li><code>highest</code> : Cao nhất</li>
                                                                <li><code>lowest</code> : Thấp nhất</li>
                                                              <ul>
@apiSuccess {String}            data.rank_info.type.branch_code Mã đơn vị kinh doanh               
@apiSuccess {Number}            data.rank_info.type.rank      Xếp hạng               
@apiSuccess {Number}            data.rank_info.type.revenue   Doanh số               
@apiSuccess {Number}            data.rank_info.type.number_staff   Số lượng nhân viên theo chức danh          
@apiSuccess {Number}            data.rank_info.type.avg_revenue   Doanh số bình quân               

@apiSuccess {String}            message                   Mô tả phản hồi
@apiSuccess {Integer}           code                      Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "rank_info": {
                "highest": [
                    {
                        "branch_code": "1903",
                        "rank": 1,
                        "revenue": 12,
                        "number_staff": 1,
                        "avg_revenue": 12
                    },
                    {
                        "branch_code": "1624",
                        "rank": 2,
                        "revenue": 10,
                        "number_staff": 1,
                        "avg_revenue": 10
                    }
                ],
                "lowest": [
                    {
                        "branch_code": "1212",
                        "rank": 1,
                        "revenue": 1,
                        "number_staff": 1,
                        "avg_revenue": 1
                    },
                    {
                        "branch_code": "1905",
                        "rank": 2,
                        "revenue": 2,
                        "number_staff": 1,
                        "avg_revenue": 2
                    }
                ]
            },
            "target_code": "dsgn_tdh",
            "target_name": "Doanh số giải ngân trung dài hạn",
            "unit": "Tỷ đồng"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# TOP 5 ĐVKD có hiệu suất cao nhất và thấp nhất theo tỉ lệ hoàn thành chỉ tiêu
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level1/top5/department/target/title Top5 ĐVKD có hiệu suất cao nhất và thấp nhất theo tỉ lệ hoàn thành chỉ tiêu (theo chức danh)
@apiDescription 33. Top 5 ĐVKD có hiệu suất cao nhất và thấp nhất theo tỉ lệ hoàn thành chỉ tiêu (Theo chức danh: GDV/KSV, ..)
@apiGroup Dashboard EIB - Level1
@apiVersion 1.0.0
@apiName DashboardLevel1Top5DepartmentTargetTitle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   [report_month]        Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {Array}    [region]              Danh sách khu vực
@apiParam  (Body:)     {String}   card_title          Chức danh cho card báo cáo
                                                      <ul>
                                                         <li><code>GDV_KSV</code>: GDV/KSV</li>
                                                      </ul>                                                
@apiParam  (Body:)     {Array}    [title]              Chức danh
                                                       <ul>
                                                        <li><code> RBO </code></li>
                                                        <li><code> SRBO </code></li>
                                                        <li><code> CSO </code></li>
                                                        <li><code> RM </code></li>                                            
                                                        <li><code> SRM </code></li>                                            
                                                        <li><code> GDV </code></li>                                            
                                                        <li><code> KSV </code></li>                                            
                                                      </ul>                                                        

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "card_title": "GDV_KSV"
}

@apiSuccess {Array}             data                       Dữ liệu trả về

@apiSuccess {String}            data.target_code              Mã chỉ tiêu   
@apiSuccess {String}            data.target_name              Tên chỉ tiêu   
@apiSuccess {String}            data.unit                     Đơn vị tính   
@apiSuccess {Object}            data.rank_info                Xếp hạng đơn vị
@apiSuccess {Array}             data.rank_info.type           Loại
                                                              <ul>
                                                                <li><code>highest</code> : Cao nhất</li>
                                                                <li><code>lowest</code> : Thấp nhất</li>
                                                              <ul>
@apiSuccess {String}            data.rank_info.type.branch_code Mã đơn vị kinh doanh               
@apiSuccess {Number}            data.rank_info.type.rank      Xếp hạng               
@apiSuccess {Number}            data.rank_info.type.revenue   Doanh số               
@apiSuccess {Number}            data.rank_info.type.number_staff   Số lượng nhân viên theo chức danh          
@apiSuccess {Number}            data.rank_info.type.avg_revenue   Doanh số bình quân               

@apiSuccess {String}            message                   Mô tả phản hồi
@apiSuccess {Integer}           code                      Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "rank_info": {
                "highest": [
                    {
                        "branch_code": "1903",
                        "rank": 1,
                        "revenue": 12,
                        "number_staff": 1,
                        "avg_revenue": 12
                    },
                    {
                        "branch_code": "1624",
                        "rank": 2,
                        "revenue": 10,
                        "number_staff": 1,
                        "avg_revenue": 10
                    }
                ],
                "lowest": [
                    {
                        "branch_code": "1212",
                        "rank": 1,
                        "revenue": 1,
                        "number_staff": 1,
                        "avg_revenue": 1
                    },
                    {
                        "branch_code": "1905",
                        "rank": 2,
                        "revenue": 2,
                        "number_staff": 1,
                        "avg_revenue": 2
                    }
                ]
            },
            "target_code": "dsgn_tdh",
            "target_name": "Doanh số giải ngân trung dài hạn",
            "unit": "Tỷ đồng"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Tỉ lệ RM/SRM hoàn thành từng chỉ tiêu KPIs
# version: 1.0.0
# ---------------------------------------------
"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level1/completion-rate/rm/target Tỉ lệ RM/SRM hoàn thành từng chỉ tiêu KPIs
@apiDescription 21. Tỉ lệ RM hoàn thành từng chỉ tiêu KPIs
@apiGroup Dashboard EIB - Level1
@apiVersion 1.0.0
@apiName DashboardLevel1CompletionRateRMTarget

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam  (Body:)     {String}  [report_month]               Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {String}  type                         View Type
                                                              <ul>
                                                                <li><code>month</code>: Tháng hiện tại</li>
                                                                <li><code>average_year</code>: Bình quân năm</li>
                                                              </ul> 
@apiParam  (Body:)     {Array}   [region]              Danh sách khu vực                                                              
@apiParam  (Body:)     {Array}   [business_unit]              Danh sách đơn vị kinh doanh                                                              
@apiParam  (Body:)     {Array}   [staff_title]                Danh sách chức danh                                                              

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "type": "month",
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Array}             data.target                   Thông tin tỷ lệ hoàn thành theo chỉ tiêu
@apiSuccess {String}            data.target.target_code              Mã chỉ tiêu
@apiSuccess {String}            data.target.target_name              Tên chỉ tiêu
@apiSuccess {Number}            data.target.completion_rate          Tỷ lệ CBBH hoàn thành chỉ tiêu
@apiSuccess {Number}            data.target.number_complete          Số lượng CBBH hoàn thành chỉ tiêu
@apiSuccess {Number}            data.target.total                    Tổng số CBBH
@apiSuccess {Number}            data.average                   Trung bình
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "target": [
            {
                "target_code": "AB1",
                "target_name": "Tỷ lệ RBO hoàn thành chỉ tiêu saleplan",
                "completion_rate": 35.5,
                "total": 90,
                "number_complete": 32,
            }
        ],
        "average": 50
    }
}
"""

# ---------------------------------------------
# Tỷ lệ CBBH hoàn thành KPIs (tổng các chức danh)
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/level1/completion-rate Tỉ lệ CBBH hoàn thành KPIs (tổng các chức danh)
@apiDescription 23. Tỉ lệ CBBH hoàn thành KPIs (tổng các chức danh)
@apiGroup Dashboard EIB - Level1
@apiVersion 1.0.0
@apiName DashboardLevel1CompletionRate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam  (Body:)     {String}   [report_month]              Tháng báo cáo (Format: %Y-%m) (Mặc định lấy theo tháng hiện tại)
@apiParam  (Body:)     {String}   [region]                     Mã khu vực
@apiParam  (Body:)     {String}   type                        <ul>
                                                                <li><code>current</code> : Tháng hiện tại</li>
                                                                <li><code>cumulative</code> : Luỹ kế từ đầu năm</li>
                                                              <ul>
@apiParam  (Body:)     {Array}   [business_unit]              Danh sách đơn vị kinh doanh                                                                

@apiParamExample {json} Body example
{
    "report_month": "2023-10",
    "region": "KVMB",
    "type": "current"
}

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Object}            data.completion_rate          Thông số tỷ lệ hoàn thành
@apiSuccess {Object}            data.completion_rate.type     Loại
                                                              <ul>
                                                                <li><code>department</code> : Đơn vị kinh doanh</li>
                                                                <li><code>region</code> : Khu vực</li>
                                                                <li><code>system</code> : Hệ thống</li>
                                                              </ul>
@apiSuccess {Number}            data.completion_rate.type.total         Tổng số CBBH
@apiSuccess {Number}            data.completion_rate.type.complete      Số lượng CBBH hoàn thành KPI 
@apiSuccess {Number}            data.completion_rate.type.completion_rate  Tỷ lệ hoàn CBBH hoàn thành KPI 

@apiSuccess {Object}            data.rank                   Thông số xếp hạng
@apiSuccess {Object}            data.rank.type              Loại
                                                              <ul>
                                                                <li><code>region_system</code> : Khu vực trên toàn hệ thống</li>
                                                                <li><code>department_region</code> : ĐVKD trong khu vực</li>
                                                                <li><code>department_system</code> : ĐVKD trên toàn hệ thống</li>
                                                              </ul>
@apiSuccess {Number}            data.rank.type.total         Tổng số ĐVKD
@apiSuccess {Number}            data.rank.type.point         Xếp hạng của ĐVKD
@apiSuccess {String}            data.rank.type.name          Tên loại

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "completion_rate": {
            "region": {
                "total": 100,
                "complete": 50,
                "completion_rate": 50
            },
            "system": {
                "total": 1000,
                "complete": 825,
                "completion_rate": 82.5
            }
        },
        "rank": {
            "region_system": {
                "total": 49,
                "point": 19
            }
        }

    }
}
"""

# --------------- END LEVEL 1 ----------------

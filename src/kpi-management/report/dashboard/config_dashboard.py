#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: nguyenthong
    Company: M O B I O
    Date Created: 13/11/2023
"""

# ---------------------------------------------
# C<PERSON>u hình hiển thị chỉ tiêu theo dashboard
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/targets/config-display Cấu hình hiển thị chỉ tiêu
@apiDescription Lưu thông tin cấu hình ẩn hiện chỉ tiêu 
@apiGroup Dashboard EIB Config
@apiVersion 1.0.0
@apiName DashboardConfigTargetDisplay

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   dashboard_code              Mã dashboard
                                                              <ul>
                                                                <li><code>result-complete-detail</code>: Kết quả hoàn thành chi tiết</li>
                                                              </ul>   
@apiParam  (Body:)     {String}   [report_month]           Tháng báo cáo (Format: %Y-%m)
@apiParam  (Body:)     {String}   [staff_title]            Chức danh
                                                            <ul>
                                                                <li>RBO</li>
                                                                <li>SRBO</li>
                                                                <li>CSO</li>
                                                                <li>RM</li>
                                                                <li>SRM</li>
                                                            </ul>                                                               
@apiParam  (Body:)     {Array}    configs                     Cấu hình    
@apiParam  (Body:)     {String}   configs.target_code         Mã chỉ tiêu    
@apiParam  (Body:)     {Boolean}  configs.is_display          Hiển thị? (True: hiển thị, False: ẩn)    
@apiParam  (Body:)     {Number}   configs.order               Thứ tự sắp xếp    
                                                              
@apiParamExample {json} Body example
{
    "dashboard_code": "result-complete-detail",
    "configs": [
        {
            "target_code": "ds_gntdh",
            "is_display": true,
            "order": 1
        },
        {
            "target_code": "combo",
            "is_display": false,
            "order": 2
        }
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Lấy cấu hình hiển thị chỉ tiêu theo dashboard
# version: 1.0.0
# ---------------------------------------------


"""
@api {get} {domain}/kpi-management/report/api/v1.0/dashboard/targets/config-display Lấy cấu hình hiển thị chỉ tiêu
@apiDescription Lấy thông tin cấu hình ẩn hiện chỉ tiêu 
@apiGroup Dashboard EIB Config
@apiVersion 1.0.0
@apiName DashboardGetConfigTargetDisplay

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   dashboard_code             Mã dashboard
                                                              <ul>
                                                                <li><code>result-complete-detail</code>: Kết quả hoàn thành chi tiết</li>
                                                              </ul>
@apiParam  (Param:)     {String}    [report_month]           Tháng báo cáo (Format: %Y-%m)                                                              
@apiParam  (Param:)     {String}   [staff_title]            Chức danh cần lấy chỉ tiêu (Trường hợp k có thì sẽ lấy theo chức danh của tài khoản đăng nhập)
                                                            <ul>
                                                                <li>RBO</li>
                                                                <li>SRBO</li>
                                                                <li>CSO</li>
                                                                <li>RM</li>
                                                                <li>SRM</li>
                                                            </ul>

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {String}            data.dashboard_code           Mã dashboard
@apiSuccess {Array}             data.configs                  Thông tin cấu hình
@apiSuccess {Number}             data.configs.type            Loại chỉ tiêu
                                                            <ul>
                                                                <li><code>0</code> : Điểm trừ</li>
                                                                <li><code>1</code> : Chỉ tiêu chính</li>
                                                                <li><code>2</code> : Chỉ tiêu ghi nhận thêm</li>
                                                              </ul>

@apiSuccess {Array}             data.configs.targets          Thông tin chỉ tiêu
@apiSuccess {String}             data.configs.targets.target_code         Mã chỉ tiêu
@apiSuccess {String}             data.configs.targets.target_name         Tên chỉ tiêu
@apiSuccess {Number}             [data.configs.targets.order]         Thứ tự hiển thị
@apiSuccess {Boolean}            data.configs.targets.is_display         Hiển thị? (True: hiển thị, False: ẩn)
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "configs": [
            {
                "targets": [
                    {
                        "is_display": true,
                        "order": null,
                        "target_code": "baohiemhuy",
                        "target_name": "HĐ Bảo hiểm hủy ",
                        "type": 0
                    }
                ],
                "type": 0
            },
            {
                "targets": [
                    {
                        "is_display": true,
                        "order": null,
                        "target_code": "trdn_nhbq",
                        "target_name": "Tăng ròng dư nợ ngắn hạn bình quân",
                        "type": 1
                    },
                    
                ],
                "type": 1
            },
            {
                "targets": [
                    {
                        "is_display": true,
                        "order": null,
                        "target_code": "tham_dinh_ho_so",
                        "target_name": "Công tác thẩm định, xử lý hồ sơ  thẻ tín dụng do bộ phận khác giới thiệu",
                        "type": 2
                    }
                ],
                "type": 2
            }
        ],
        "dashboard_code": "result-complete-detail"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Thời gian cập nhật dữ liệu lần cuối
# version: 1.0.0
# ---------------------------------------------


"""
@api {get} {domain}/kpi-management/report/api/v1.0/dashboard/last-report-date Thời gian cập nhật báo cáo gần nhất
@apiDescription Lấy thông tin thời gian báo cáo được cập nhật gần nhất
@apiGroup Dashboard EIB Config
@apiVersion 1.0.0
@apiName DashboardLastReportDate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {String}            data.last_report_date         Thời gian cập nhật lần cuối (FORMAT: <code>%Y-%m-%d</code>)
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
    "last_report_date": "2023-12-01",
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Cấu hình hiển thị các cột theo dashboard
# version: 1.0.0
# ---------------------------------------------

"""
@api {POST} {domain}/kpi-management/report/api/v1.0/dashboard/column/config-display Cấu hình hiển thị dashboard
@apiDescription Lưu thông tin cấu hình ẩn hiện danh sách các cột trên dashboard 
@apiGroup Dashboard EIB Config
@apiVersion 1.0.0
@apiName DashboardConfigColumnDisplay

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}   dashboard_code              Mã dashboard
                                                              <ul>
                                                                <li><code>result-complete-detail</code>: Kết quả hoàn thành chi tiết</li>
                                                              </ul>    
@apiParam  (Body:)     {String}   [staff_title]                 Mã chức danh    
@apiParam  (Body:)     {Array}    configs                     Cấu hình    
@apiParam  (Body:)     {String}   configs.code               Mã cột    
@apiParam  (Body:)     {String}   [configs.name]               Tên cột    
@apiParam  (Body:)     {Boolean}  configs.is_display         Hiển thị? (True: hiển thị, False: ẩn)    
@apiParam  (Body:)     {Number}   [configs.order]              Thứ tự cột    

@apiParamExample {json} Body example
{
    "dashboard_code": "result-complete-detail",
    "staff_title": "RBO",
    "configs": [
        {
            "code": "staff_name",
            "name": "Họ và tên RBO",
            "is_display": true,
            "order": 1
        },
        {
            "code": "staff_code",
            "name": "Mã RBO",
            "is_display": false,
            "order": 2
        }
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "message": "request thành công."
}
"""

# ---------------------------------------------
# Lấy cấu hình hiển thị danh sách cột theo dashboard
# version: 1.0.0
# ---------------------------------------------


"""
@api {get} {domain}/kpi-management/report/api/v1.0/dashboard/column/config-display Lấy cấu hình hiển thị dashboard
@apiDescription Lấy thông tin cấu hình ẩn hiện danh sách cột của dashboard
@apiGroup Dashboard EIB Config
@apiVersion 1.0.0
@apiName DashboardGetConfigColumnDisplay

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Param:)     {String}   dashboard_code             Mã dashboard
                                                              <ul>
                                                                <li><code>result-complete-detail</code>: Kết quả hoàn thành chi tiết</li>
                                                              </ul>
@apiParam  (Param:)     {String}   [staff_title]                Mã chức danh                                                                  

@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {String}            data.dashboard_code           Mã dashboard
@apiSuccess {Array}             data.configs                  Thông tin cấu hình
@apiSuccess {String}            data.configs.code             Mã cột
@apiSuccess {String}            data.configs.name             Tên cột
@apiSuccess {Boolean}           data.configs.is_display       Hiển thị? (True: hiển thị, False: ẩn)
@apiSuccess {Number}            data.configs.order            Thứ tự cột

@apiSuccess {Array}            [data.config_default]         Thông tin các cột được extend theo chức danh
@apiSuccess {String}            data.config_default.code     Mã cột
@apiSuccess {String}            data.config_default.name     Tên cột



@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "dashboard_code": "result-complete-detail",
        "staff_title": "RBO",
        "configs": [
            {
                "code": "staff_name",
                "name": "Họ và tên RBO",
                "is_display": true,
                "order": 1
            },
            {
                "code": "staff_code",
                "name": "Mã RBO",
                "is_display": false,
                "order": 2
            }
        ],
        "config_default": [
            {
                "code": "SLQL",
                "name": "Số lượng KHCN QLBQ"
            },
            {
                "code": "DNQLBQ",
                "name": "Dư nợ KHCN QLBQ"
            }
        ],
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: thong
    Company: M O B I O
    Date Created: 10/07/2024
"""
####################################################################################################
# Danh sách chỉ tiêu
# version: 1.0.1                                                                                   #
# version: 1.0.0                                                                                   #
####################################################################################################

# version: 1.0.1
"""
@api {POST} {domain}/kpi-management/mobile/api/v1.0/target/list     Danh sách chỉ tiêu
@apiGroup Target Config mobile
@apiVersion 1.0.0
@apiName TargetListMobile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam  (Query:)    {String}       [order_by]              Sắp xếp kết quả theo trường thông tin
                                                              <ul>
                                                                <li><code>name</code> : Tên chỉ tiêu</li>
                                                                <li><code>created_time</code> : Thời gian tạo</li>
                                                                <li><code>updated_time</code> : Thời gian cập nhật</li>
                                                              </uL>
@apiParam  (Query:)    {String}       [order_type]            Kiểu sắp xếp
                                                              <ul>
                                                                <li><code>asc</code>: Sắp xếp tăng dần</li>
                                                                <li><code>desc</code>: Sắp xếp giảm dần</li>
                                                              </ul>    


@apiParam  (Body:)     {String}       [staff_title]           Nhóm chức danh                                                           
@apiParam  (Body:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiParam  (Body:)     {String}       [search_text]           Tìm theo tên chỉ tiêu                                                              
@apiParam  (Body:)     {ArrayNumber}  [type]                  Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiParam  (Body:)     {StringDate}  [created_time_start]    Thời gian tạo từ  (Format: <code>%Y-%m-%d</code>)                                                             
@apiParam  (Body:)     {StringDate}  [created_time_end]      Thời gian tạo đến (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {StringDate}  [updated_time_start]    Thời gian cập nhật từ  (Format: <code>%Y-%m-%d</code>)                                                             
@apiParam  (Body:)     {StringDate}  [updated_time_end]      Thời gian cập nhật đến (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {StringDate}  [apply_time_start]      Thời gian áp dụng từ (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {StringDate}  [apply_time_end]        Thời gian áp dụng đến (Format: <code>%Y-%m-%d</code>)                                                                       
@apiParam  (Body:)     {String}      [created_by]            ID người tạo
@apiParam  (Body:)     {String}      [updated_by]            ID người cập nhật                                                                                   
@apiParam  (Body:)     {ArrayString}      [parent_code]           Mã chỉ tiêu cha (Trường hợp parent_code = null là chỉ tiêu cha)                                                                                   
@apiParam  (Body:)     {ArrayString}      [code]                  Mã chỉ tiêu                                                                                   


@apiParamExample {json} Body example
{
    "staff_title": "RBO",
    "status": 1
}


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {ArrayObject}       data                          Danh sách chỉ tiêu

@apiSuccess  (data:)     {String}   id                        ID chỉ tiêu
@apiSuccess  (data:)     {String}   name                        Tên chỉ tiêu
@apiSuccess  (data:)     {String}   code                      Mã chỉ tiêu
@apiSuccess  (data:)     {String}   staff_title               Áp dụng cho nhóm chức danh

@apiSuccess  (data:)     {Array}    apply                     Cấu hình thông tin áp dụng
@apiSuccess  (data:)     {Number}   apply.type                Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiSuccess  (data:)     {StringDate}   apply.start_time        Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m-%d</code>. 
                                                              Mặc định: <code>current_date</code>)
@apiSuccess  (data:)     {StringDate}   [apply.end_time]      Thời gian kết thúc áp dụng (Format: <code>%Y-%m-%d</code>)
@apiSuccess  (data:)     {String}       [apply.parent_code]   Mã chỉ tiêu cha

@apiSuccess  (data:)     {Number}       [status]               Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiSuccess  (data:)     {String}       [unit]                  Đơn vị chỉ tiêu

@apiSuccess  (data:)     {string}       [created_by]               ID người tạo                                                                              
@apiSuccess  (data:)     {string}       [updated_by]               ID người cập nhật                                                                              
@apiSuccess  (data:)     {stringDatetime}   created_time         Thời gian tạo (Format: %Y-%m-%dT%H:%M:%S.%fZ)                                                                              
@apiSuccess  (data:)     {stringDatetime}   [updated_time]         Thời gian cập nhật (Format: %Y-%m-%dT%H:%M:%S.%fZ)                                                                              



@apiSuccessExample {json} Response Example
{
    "data": [
        {
            "id": "656d96ea11940c3d98df36d0",
            "name": "Doanh số giải ngân trung dài hạn",
            "code": "dsgn_tdh_1",
            "staff_title": "RBO",
            "apply": [
                {
                    "type": 1,
                    "start_time": "2024-06-13"    
                }
            ],
            "status": 1,
            "unit": "%",
            "created_by": "0315a587-3840-4e56-a15b-35103a31b532",
            "updated_by": "0315a587-3840-4e56-a15b-35103a31b532",
            "created_time": "2024-06-13T00:00:00.000Z",
            "updated_time": "2024-06-13T00:00:00.000Z",
        }
    ]
    "code": 200,
    "message": "request thành công."
}
"""

# version: 1.0.0
"""
@api {POST} {domain}/kpi-management/mobile/api/v1.0/target/list     Danh sách chỉ tiêu
@apiGroup Target Config mobile
@apiVersion 1.0.0
@apiName TargetListMobile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse merchant_id_header

@apiParam  (Query:)    {String}       [order_by]              Sắp xếp kết quả theo trường thông tin
                                                              <ul>
                                                                <li><code>name</code> : Tên chỉ tiêu</li>
                                                                <li><code>created_time</code> : Thời gian tạo</li>
                                                                <li><code>updated_time</code> : Thời gian cập nhật</li>
                                                              </uL>
@apiParam  (Query:)    {String}       [order_type]            Kiểu sắp xếp
                                                              <ul>
                                                                <li><code>asc</code>: Sắp xếp tăng dần</li>
                                                                <li><code>desc</code>: Sắp xếp giảm dần</li>
                                                              </ul>    


@apiParam  (Body:)     {String}       [staff_title]           Nhóm chức danh                                                           
@apiParam  (Body:)     {Number}       [status]                Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiParam  (Body:)     {String}       [search_text]           Tìm theo tên chỉ tiêu                                                              
@apiParam  (Body:)     {ArrayNumber}  [type]                  Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiParam  (Body:)     {StringDate}  [created_time_start]    Thời gian tạo từ  (Format: <code>%Y-%m-%d</code>)                                                             
@apiParam  (Body:)     {StringDate}  [created_time_end]      Thời gian tạo đến (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {StringDate}  [updated_time_start]    Thời gian cập nhật từ  (Format: <code>%Y-%m-%d</code>)                                                             
@apiParam  (Body:)     {StringDate}  [updated_time_end]      Thời gian cập nhật đến (Format: <code>%Y-%m-%d</code>)
@apiParam  (Body:)     {StringDate}  [apply_time_start]      Thời gian áp dụng từ (Format: <code>%Y-%m-%d</code>)                                                              
@apiParam  (Body:)     {StringDate}  [apply_time_end]        Thời gian áp dụng đến (Format: <code>%Y-%m-%d</code>)                                                                       
@apiParam  (Body:)     {String}      [created_by]            ID người tạo
@apiParam  (Body:)     {String}      [updated_by]            ID người cập nhật                                                                                   
@apiParam  (Body:)     {ArrayString}      [parent_code]           Mã chỉ tiêu cha (Trường hợp parent_code = null là chỉ tiêu cha)                                                                                   
@apiParam  (Body:)     {ArrayString}      [code]                  Mã chỉ tiêu                                                                                   


@apiParamExample {json} Body example
{
    "staff_title": "RBO",
    "status": 1
}


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {ArrayObject}       data                          Danh sách chỉ tiêu

@apiSuccess  (data:)     {String}   id                        ID chỉ tiêu
@apiSuccess  (data:)     {String}   name                        Tên chỉ tiêu
@apiSuccess  (data:)     {String}   code                      Mã chỉ tiêu
@apiSuccess  (data:)     {String}   staff_title                Áp dụng cho nhóm chức danh
@apiSuccess  (data:)     {Number}   type                        Loại chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: Chỉ tiêu trừ</li>
                                                                <li><code>1</code>: Chỉ tiêu chính</li>
                                                                <li><code>2</code>: Chỉ tiêu cộng thêm</li>
                                                              </ul>
@apiSuccess  (data:)     {StringDatetime}   [start_time]            Thời gian bắt đầu áp dụng 
                                                              (Format: <code>%Y-%m-%dT%H:%M:%S.%fZ</code>. 
                                                              Mặc định: <code>current_date</code>)
@apiSuccess  (data:)     {StringDatetime}   [end_time]              Thời gian kết thúc áp dụng (Format: <code>%Y-%m-%dT%H:%M:%S.%fZ</code>)                                                              
@apiSuccess  (data:)     {Number}       [status]               Trạng thái chỉ tiêu
                                                              <ul>
                                                                <li><code>0</code>: OFF</li>
                                                                <li><code>1</code>: ON (Mặc định)</li>
                                                              </ul>
@apiSuccess  (data:)     {String}       [unit]                  Đơn vị chỉ tiêu
@apiSuccess  (data:)     {String}       [parent_code]           Mã chỉ tiêu cha                                                              

@apiSuccess  (data:)     {string}       [created_by]               ID người tạo                                                                              
@apiSuccess  (data:)     {string}       [updated_by]               ID người cập nhật                                                                              
@apiSuccess  (data:)     {stringDatetime}   created_time         Thời gian tạo (Format: %Y-%m-%dT%H:%M:%S.%fZ)                                                                              
@apiSuccess  (data:)     {stringDatetime}   [updated_time]         Thời gian cập nhật (Format: %Y-%m-%dT%H:%M:%S.%fZ)                                                                              



@apiSuccessExample {json} Response Example
{
    "data": [
        {
            "id": "656d96ea11940c3d98df36d0",
            "name": "Doanh số giải ngân trung dài hạn",
            "code": "dsgn_tdh_1",
            "staff_title": "RBO",
            "type": 1,
            "start_time": "2024-06-13T00:00:00.000Z",
            "status": 1,
            "unit": "%",
            "created_by": "0315a587-3840-4e56-a15b-35103a31b532",
            "updated_by": "0315a587-3840-4e56-a15b-35103a31b532",
            "created_time": "2024-06-13T00:00:00.000Z",
            "updated_time": "2024-06-13T00:00:00.000Z",
        }
    ]
    "code": 200,
    "message": "request thành công."
}
"""

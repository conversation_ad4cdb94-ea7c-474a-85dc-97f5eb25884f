*************************************  Chạy thử chính sách  ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************


"""
@api {get} /data-mgt/api/v1.0/retention/dryrun/:id/run Dry-run in Retention Policy
@apiName RunRetentionDryRun
@apiGroup RetentionDryRun
@apiVersion 1.0.0
@apiDescription Gửi yêu cầu chạy dry-run cho chính sách có ID cụ thể.

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (URL Path) {Number} id ID của retention policy cần chạy dry-run.
@apiParam (Query String) {String="en","vi"} lang Ngôn ngữ phản hồi (ví dụ: `en` hoặc `vi`, default: `vi`).

@apiSuccess {Number} code Mã trạng thái (200 nếu thành công).
@apiSuccess {String} message Thông báo từ hệ thống.

@apiSuccessExample {json} Success-Response:
{
    "code": 200,
    "message": "request successful."
}
"""

************************************  Lấy kết quả chạy thử  ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************


"""
@api {get} /data-mgt/api/v1.0/retention/dryrun/:id/detail Get detail Dry-run Retention
@apiName DetailRetentionDryRun
@apiGroup RetentionDryRun
@apiVersion 1.0.0
@apiDescription Lấy thông tin chi tiết của một dry-run retention dựa trên ID.

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (URL Path) {Number} id ID của retention policy cần lấy thông tin dry-run.
@apiParam (Query String) {String="en","vi"} lang Ngôn ngữ phản hồi (ví dụ: `en` hoặc `vi`, default: `vi`)

@apiSuccess {Number} code Mã trạng thái (200 nếu thành công).
@apiSuccess {Object} data Dữ liệu chi tiết dry-run.
@apiSuccess {String} data._id ID của dry-run.
@apiSuccess {String} data.retention_object Đối tượng áp dụng retention (`profile`, `event`,...).
@apiSuccess {Number} data.retention_config_id ID cấu hình retention.
@apiSuccess {String} data.data_type Loại dữ liệu (`event`, `profile`,...).
@apiSuccess {Number} data.count_data Số lượng dữ liệu bị ảnh hưởng.
@apiSuccess {String} data.status Trạng thái dry-run (`pending`, `processing`,`success`).
@apiSuccess {String} data.created_time Thời gian tạo dry-run (định dạng ISO hoặc GMT).
@apiSuccess {String} data.updated_time Thời gian cập nhật dry-run.
@apiSuccess {String} data.tenant_id ID tenant.
@apiSuccess {Object[]} data.datas Danh sách dữ liệu dry-run (nếu có, tối đã 100 phần tử, mỗi bảng sẽ có định dạng khác nhau).
@apiSuccess {String} message Thông điệp phản hồi (nếu có).

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "count_data": 0,
        "created_time": "Mon, 02 Jun 2025 02:02:13 GMT",
        "data_type": "event",
        "datas": [],
        "_id": "683d06fd91a138106da37bd6",
        "retention_config_id": 419259998783144193,
        "retention_object": "profile",
        "status": "pending",
        "tenant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "updated_time": "Mon, 02 Jun 2025 02:02:13 GMT"
    },
    "message": ""
}

@apiSuccessExample {json} Device Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "683d471e817462066678d89b",
        "count_data": 730,
        "created_time": "Mon, 02 Jun 2025 06:39:22 GMT",
        "data_type": "device",
        "datas": [
            {
                "allow_notification": 0,
                "convert_to_profile": 0,
                "created_time": "Mon, 06 May 2024 10:02:31 GMT",
                "device_id": 362883383744791497,
                "finger_print": null,
                "init_from_camp": 1,
                "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
                "profile_id": "2ca940f5-8262-47a6-ba1d-b3630ac3dff3",
                "push_id": null,
                "session_id": 362883383761568713,
                "site_id": "367fe208-48b1-4ba1-b1c2-c768b05f0ed2",
                "updated_time": "Mon, 06 May 2024 10:02:31 GMT"
            },
            {
                "allow_notification": 0,
                "convert_to_profile": 0,
                "created_time": "Wed, 08 May 2024 04:34:38 GMT",
                "device_id": 363140287716920977,
                "finger_print": null,
                "init_from_camp": 1,
                "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
                "profile_id": "fc5b9719-8ac7-4716-b0e0-63b18920d1fa",
                "push_id": null,
                "session_id": 363140287784029841,
                "site_id": "367fe208-48b1-4ba1-b1c2-c768b05f0ed2",
                "updated_time": "Wed, 08 May 2024 04:34:40 GMT"
            }
        ],
        "retention_config_id": 419675709657579777,
        "retention_object": "profile",
        "status": "success",
        "tenant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "updated_time": "Mon, 02 Jun 2025 09:23:40 GMT"
    },
    "message": ""
}

@apiSuccessExample {json} Event Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "683e5f959f662e3b9c3f71fd",
        "count_data": 12227,
        "created_time": "Tue, 03 Jun 2025 02:35:44 GMT",
        "data_type": "event",
        "datas": [
            {
                "_hash_id": -1695259177,
                "_id": "5a30bed6-7eb9-4ebf-874d-590dab8aafef::1593593292.0",
                "action_time": "Wed, 01 Jul 2020 08:48:12 GMT",
                "agent": null,
                "block_id": "a9b4ed26-952d-4220-91f9-1bddf9217ccf",
                "body": "{\"activity\": [], \"demographic\": [], \"dynamic\": [], \"information\": [{\"add\": [\"c4b8e92d-0b3a-463d-83a4-1eb9b3d546a9\", \"c2fa2c65-b221-47f8-8ccb-268b558d719c\", \"aa4ac4a5-9fa1-4225-98c4-53b0c2f6a1bf\", \"70a7422a-dc40-4937-a66b-f374f711b27a\", \"c512d944-de07-41e3-94d4-4b983a78842e\", \"b19b95e3-93f6-4d37-b159-7b9aa2c224a2\", \"820a50a2-a6cc-4d41-ade7-820a04c2f0f2\"], \"change\": [], \"field_name\": \"profile_group\", \"field_property\": 2, \"key\": \"profile_group\", \"remove\": [], \"translate_key\": \"i18n_profiles_group\"}, {\"add\": [], \"change\": [{\"from\": \"Nhung Trâm\", \"to\": \"Tuyết Nhung\"}], \"field_name\": \"name\", \"field_property\": 2, \"key\": \"name\", \"remove\": [], \"translate_key\": \"i18n_call_center_table_fullname\"}], \"loyalty\": [], \"other\": []}",
                "created_time": "Wed, 01 Jul 2020 08:48:13 GMT",
                "group_configs": null,
                "init_time": null,
                "lst_display_type": null,
                "lst_field_key": null,
                "profile_id": "5a30bed6-7eb9-4ebf-874d-590dab8aafef",
                "staff_id": null,
                "updated_time": "Wed, 01 Jul 2020 08:48:13 GMT"
            },
            {
                "_hash_id": 683056713,
                "_id": "52aa9743-abb9-426a-9e9d-ffc62ca3d619::1591263038.0",
                "action_time": "Thu, 04 Jun 2020 09:30:38 GMT",
                "agent": null,
                "block_id": "eb297f35-2fa3-484d-9195-5847fd9e486f",
                "body": "{\"activity\": [], \"demographic\": [], \"dynamic\": [], \"information\": [{\"add\": [], \"change\": [{\"from\": \"PHAN THÚC ĐỊNH\", \"to\": \"Linh\"}], \"field_name\": \"name\", \"field_property\": 2, \"key\": \"name\", \"remove\": [], \"translate_key\": \"i18n_call_center_table_fullname\"}, {\"add\": [], \"change\": [{\"from\": \"+84962565010\", \"to\": \"+84985333333\"}], \"field_name\": \"primary_phone\", \"field_property\": 7, \"key\": \"primary_phone\", \"remove\": [], \"translate_key\": \"i18n_phone_number\"}, {\"add\": [\"+84962565010\"], \"change\": [], \"field_name\": \"secondary_phones\", \"field_property\": 7, \"key\": \"secondary_phones\", \"remove\": [], \"translate_key\": \"i18n_phone_number_other\"}], \"loyalty\": [], \"other\": []}",
                "created_time": "Thu, 04 Jun 2020 09:30:39 GMT",
                "group_configs": null,
                "init_time": null,
                "lst_display_type": null,
                "lst_field_key": null,
                "profile_id": "52aa9743-abb9-426a-9e9d-ffc62ca3d619",
                "staff_id": null,
                "updated_time": "Thu, 04 Jun 2020 09:30:39 GMT"
            }
        ],
        "retention_config_id": 419804773475156225,
        "retention_object": "profile",
        "status": "success",
        "tenant_id": "0ff54084-a607-46f7-aeb4-8854ab8e6292",
        "updated_time": "Tue, 03 Jun 2025 02:36:43 GMT"
    },
    "message": ""
}
"""

**************************  B<PERSON>o cáo biến động thay đổi giữa hai chu kỳ *****************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/report/tables/pop Report tables period-over-period
@apiDescription Report tables period-over-period
@apiGroup ReportTables
@apiVersion 1.0.0
@apiName ReportTablesPop

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} obj  Object/Đối tượng bảng cần lấy báo cáo ['profile', 'sale', 'ticket', 'company']
@apiParam (Query:) {String} start_time  Ngày bắt đầu báo cáo
@apiParam (Query:) {String} end_time  Ngày kết thúc báo cáo

@apiSuccess {Dict}   data    Dữ liệu phản hồi
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	previous	    Số bản tin xóa ở chu kỳ trước
@apiSuccess (data) {String} 	now		Số bản tin xóa ở chu kỳ này

@apiSuccessExample {json} Response example
{
  "data": {
    "previous" : 150,
    "now": 200
  },
  "code": 200,
  "message": "request thành công"
}
"""

************************  Báo cáo số lượng bản tin đã xóa theo time range **************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/report/tables/time Report data retention by time range
@apiDescription Report data retention by time range
@apiGroup ReportTables
@apiVersion 1.0.0
@apiName ReportTablesTimeRange

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} obj  Object/Đối tượng bảng cần lấy báo cáo ['profile', 'sale', 'ticket', 'company']
@apiParam (Query:) {String} start_time  Ngày bắt đầu báo cáo
@apiParam (Query:) {String} end_time  Ngày kết thúc báo cáo
@apiParam (Query:) {String} [grouped_by]  Kiểu group by time ['day', 'week', 'month', 'quarter']. Default grouped_by day.

@apiSuccess {Dict}   data    Dữ liệu phản hồi
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
  "data": {
    "2025-03-03" : 150,
    "2025-03-04": 200
  },
  "code": 200,
  "message": "request thành công"
}
"""

**********************************  Báo cáo tổng quan **********************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/report/tables/overview Report data overview
@apiDescription Report data overview
@apiGroup ReportTables
@apiVersion 1.0.0
@apiName ReportTablesOverview

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} obj  Object/Đối tượng bảng cần lấy báo cáo ['profile', 'sale', 'ticket', 'company']

@apiSuccess {Dict}   data    Dữ liệu phản hồi
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	events	    Tổng số lượng events đang sử dụng
@apiSuccess (data) {String} 	rows		Tổng số dòng dữ liệu (events) đang có
@apiSuccess (data) {String} 	size		Tổng số dung lượng đang sử dụng (đơn vị byte)  

@apiSuccessExample {json} Response example
{
  "data": {
    "events": 1,
    "rows": 821,
    "size": 39659141
  },
  "code": 200,
  "message": "request thành công"
}
"""
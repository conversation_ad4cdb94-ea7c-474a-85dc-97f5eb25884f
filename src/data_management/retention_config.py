**************************  <PERSON><PERSON>o cáo biến động thay đổi giữa hai chu kỳ *****************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/retention/tables/config get all retention config
@apiDescription Api get all retention config of tenant
@apiGroup RetentionConfig
@apiVersion 1.0.0
@apiName GetRetentionConfig

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} lang ngôn ngữ (en,vi)

@apiSuccessExample {json} Response example
{
    "code": 200,
    "data": [
        {
            "_id": 410553032791556353,
            "count_event": 13,
            "created_time": "2025-03-31 06:37:46",
            "data_type": "event",
            "is_default": true,
            "retention_archive": 7,
            "retention_description": "Đây là chính sách mặc định của hệ thống, được tạo ra để giải phóng tài nguyên vận hành, sau một khoảng thời gian kể từ khi phát sinh dữ liệu. Thời gian thực hiện sẽ được cấu hình theo gói dịch vụ đã đăng ký.",
            "retention_description_i18n": null,
            "retention_name": "Chính sách dọn dữ liệu Basic",
            "retention_object": "profiles",
            "retention_period": 180,
            "retention_status": null,
            "tenant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "updated_time": "2025-03-31 06:37:46"
        }
    ],
    "message": "request success"
}
"""

****************************  Lấy danh sách Event thuộc tenant đó **********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************

"""
@api {get} /api/v1.0/retention/event/list Get list event
@apiDescription Lấy danh sách event thuộc tenant đó. Nhằm mục đích cho người dùng chọn các event theo chính sách người dùng cấu hình.
@apiGroup RetentionConfig
@apiVersion 1.0.0
@apiName RetentionConfigGetEvent

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {Integer} per_page Số bản tin trên 1 trang; `default: 50`. per_page phải là số `nguyên dương` max per_page: 200.
@apiParam (Query:) {Integer} after_token Token để lấy dữ liệu trang tiếp theo `default: 1`
@apiParam (Query String) {String="en","vi"} lang Ngôn ngữ phản hồi (ví dụ: `en` hoặc `vi`, default: `vi`)

@apiSuccess {Number} code Mã trạng thái (200 nếu thành công).
@apiSuccess {Object[]} data Danh sách các events.
@apiSuccess {Number} data._id ID của event.
@apiSuccess {String} data.fact_name Tên của event.
@apiSuccess {String} data.policy_name Tên chính sách đang cấu hình event đó (nếu có).
@apiSuccess {String}     message     Mô tả phản hồi
@apiSuccess {Object} paging Thông tin phân trang.
@apiSuccess {Object} paging.cursors Con trỏ phân trang.
@apiSuccess {Number} paging.cursors.after Cursor sau.
@apiSuccess {Number} paging.cursors.before Cursor trước.
@apiSuccess {Number} paging.per_page Số phần tử trên mỗi trang.
@apiSuccess {Number} paging.total_items Tổng số phần tử.

@apiSuccessExample {json} Response 200
{
    "code": 200,
    "data": [
        {
            "_id": 405781835495768321,
            "fact_name": "dyn_event_1b99bdcf_d582_4f49_9715_1b61dfff3924_event_3101_number_int",
            "policy_name": ""
        }
    ],
    "message": "",
    "paging": {
        "cursors": {
            "after": 9,
            "before": 7
        },
        "per_page": 1,
        "total_items": 1508
    }
}
"""



**********************************  Tạo chính sách theo Event  *************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************

"""
@api {post} /api/v1.0/retention/event/policy Create policy by event
@apiDescription Api tạo chính sách theo Event
@apiGroup RetentionConfig
@apiVersion 1.0.0
@apiName RetentionConfigCreateConfigEvent

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query String) {String="en","vi"} lang Ngôn ngữ phản hồi (ví dụ: `en` hoặc `vi`, default: `vi`)

@apiParam   (Body:)   {String}  retention_name		Tên chính sách
@apiParam   (Body:)   {String}  retention_description       Mô tả cho chính sách 
@apiParam   (Body:)   {Integer}  retention_period   Số ngày giữ lại dữ liệu (tính bằng ngày).
@apiParam   (Body:)   {Number[]} policy_ids  Danh sách ID chính sách (kiểu số nguyên).


@apiParamExample {json} Body example
{
    "retention_name" : "Tên Chính sách",
    "retention_description" : "Mô tả chính sách này xóa 3 Event",
    "retention_period" : 30 ,
    "policy_ids" : [******************, ******************, ******************]
}

@apiSuccess {Number} code Mã trạng thái HTTP.
@apiSuccess {Object} data Đối tượng thông tin chính sách.
@apiSuccess {Number} data._id ID chính sách.
@apiSuccess {String} data.account_id ID tài khoản.
@apiSuccess {String} data.created_time Thời gian tạo.
@apiSuccess {String} data.updated_time Thời gian cập nhật.
@apiSuccess {String} data.data_type Loại dữ liệu áp dụng ("event", "log", v.v.).
@apiSuccess {Boolean} data.is_default Có phải mặc định không.
@apiSuccess {Number[]} data.policy_ids Danh sách ID chính sách.
@apiSuccess {Number} data.retention_archive Số ngày lưu trữ.
@apiSuccess {String} data.retention_description Mô tả chính sách.
@apiSuccess {String} data.retention_name Tên chính sách.
@apiSuccess {String} data.retention_object Đối tượng áp dụng ("profile", "transaction", v.v.).
@apiSuccess {Number} data.retention_period Số ngày giữ lại dữ liệu.
@apiSuccess {Number} data.retention_status Trạng thái chính sách (1: hoạt động, 0: ngừng).
@apiSuccess {String} data.tenant_id ID tenant.
@apiSuccess {String} message Thông báo kết quả thao tác.

@apiSuccessExample {json} Response 200
{
    "code": 200,
    "data": {
        "_id": 419276321621803265,
        "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "Fri, 30 May 2025 10:56:02 GMT",
        "data_type": "event",
        "is_default": false,
        "policy_ids": [
            ******************,
            ******************,
            ******************
        ],
        "retention_archive": 7,
        "retention_description": "Mô tả chính sách này xóa 3 Event",
        "retention_name": "Tên Chính sách",
        "retention_object": "profile",
        "retention_period": 30,
        "retention_status": 0,
        "tenant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "updated_time": "Fri, 30 May 2025 10:56:02 GMT"
    },
    "message": "The events ['behavior_event_1b99bdcf_d582_4f49_9715_1b61dfff3924_1_1724923272', 'dyn_event_1b99bdcf_d582_4f49_9715_1b61dfff3924_tam_event', 'dyn_event_1b99bdcf_d582_4f49_9715_1b61dfff3924_tien_te'] have been moved"
}
"""
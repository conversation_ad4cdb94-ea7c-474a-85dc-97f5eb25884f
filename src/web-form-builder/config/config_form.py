#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: nguyenthong
    Company: M O B I O
    Date Created: 31/10/24
"""

"""
@apiDefine status_json_body
@apiVersion 1.0.0
@apiParam  (Body:)     {String}    status                     Trạng thái form
                                                            <li><code>draft</code>: Nháp</li>
                                                            <li><code>published</code>: <PERSON>ang sử dụng</li>
                                                            <li><code>stopped</code>: Không sử dụng</li>
"""


"""
@apiDefine params_response_form
@apiVersion 1.0.0

@apiSuccess {String}            message                 Mô tả phản hồi
@apiSuccess {Integer}           code                    Mã phản hồi
@apiSuccess {Object}            data                    Thông tin form được tạo
@apiSuccess {string}            data.id                 ID định danh form
@apiSuccess {String}            data.name               Tên form
@apiSuccess {Array}             data.target_object      <PERSON><PERSON><PERSON> tượng sử dụng form
@apiSuccess {Array}             data.related_object     Đ<PERSON>i tượng liên quan đến form
@apiSuccess {Array}             data.product_line       Dòng sản phẩm
@apiSuccess {String}            data.status             Trạng thái của form
@apiSuccess {String}            data.category_id        ID thư mục chứa form
@apiSuccess {String}            data.created_by         ID nhân viên tạo form
@apiSuccess {String}            data.updated_by         ID nhân viên sửa thông tin form gần nhất
@apiSuccess {String}            data.created_time       Thời gian tạo form
@apiSuccess {String}            data.updated_time       Thời gian cập nhật form

"""

"""
@apiDefine example_response_form
@apiVersion 1.0.0
@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "id": "6724429fbc1d709c1da7be5b",
        "name": "Biểu mẫu 1",
        "category_id": "67243ff9d254aab2abf83de0",
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2024-11-01T02:00:00Z",
        "updated_time": "2024-11-01T02:00:00Z",
        "status": "pending",
        "target_object": ["sale"],
        "related_object": ["sale"],
        "product_line": []
    }
    "message": "request thành công."
}
"""

# Tạo form

"""
@api {POST} {domain}/wfb/api/v1.0/forms    Tạo form mới
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName CreateForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam  (Body:)     {String}    name                 Tên form cần tạo
@apiParam  (Body:)     {String}    category_id          ID thư mục chứa form
@apiParam  (Body:)     {String}    [description]        Mô tả
@apiParam  (Body:)     {Array}     target_object        Đối tượng sử dụng form
                                                        <li><code>sale</code>: Cơ hội bán</li>
                                                        <li><code>profile</code>: Profile</li>
                                                        <li><code>company</code>: Công ty</li>

@apiParam  (Body:)     {Array}    [product_line]        Danh sách dòng sản phẩm, <code>required khi target_object có đối tượng sale</code>

@apiParamExample {json} Body example
{
    "name": "Biểu mẫu 1",
    "category_id": "67243ff9d254aab2abf83de0",
    "target_object": ["sale"],
    "related_object": ["sale"],
    "product_line": ["cad9cc6e-286b-4dd1-b734-afdd81b69a73"],
}

@apiUse params_response_form
@apiUse example_response_form
"""


# Sửa form
"""
@api {PUT} {domain}/wfb/api/v1.0/forms/<form_id>    Cập nhật thông tin form
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName UpdateForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam  (Body:)     {String}    name                 Tên form
@apiParam  (Body:)     {String}    category_id          ID thư mục chứa form
@apiParam  (Body:)     {String}    [description]        Mô tả
@apiParam  (Body:)     {Array}     target_object        Đối tượng sử dụng form
@apiParam  (Body:)     {Array}     related_object       Đối tượng liên quan của form
@apiParam  (Body:)     {Array}     [product_line]       Danh sách dòng sản phẩm <code>required khi target_object có đối tượng sale</code>

@apiParamExample {json} Body example
{
    "name": "Biểu mẫu 1",
    "category_id": "67243ff9d254aab2abf83de0",
    "target_object": ["sale"],
    "related_object": ["sale"],
    "product_line": ["cad9cc6e-286b-4dd1-b734-afdd81b69a73"],
}

@apiUse params_response_form
@apiUse example_response_form
"""


# Xoá form
"""
@api {POST} {domain}/wfb/api/v1.0/forms/delete         Xoá form
@apiDescription  xóa nhiều id  
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName DeleteForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {array}    ids                 Danh sách ID của form cần xoá

@apiParamExample {json} Body example
{
    "ids": ["6724429fbc1d709c1da7be5b"]
}

@apiSuccess {String}            message                 Mô tả phản hồi
@apiSuccess {Integer}           code                    Mã phản hồi
@apiSuccess {Object}            data                    Thông tin phản hồi
@apiSuccess {Object}            data.ids_success        Danh sách id form xoá thành công
@apiSuccess {Object}            data.ids_error          Danh sách id form xoá thất bại

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "ids_success": ["6724429fbc1d709c1da7be5b"],
        "ids_error": []
    }
    "message": "request thành công."
}
"""


# Danh sách form
"""
@api {POST} {domain}/wfb/api/v1.0/forms/filters         Danh sách form
@apiDescription tìm theo bộ lọc, phân trang 
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName ListForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging


@apiUse status_json_body

@apiParam  (Body:)     {String}        [search]                Giá trị muốn tìm kiếm trong form
@apiParam  (Body:)     {String}        [category_id]           ID thư mục chứa form
@apiParam  (Body:)     {Array}         [selected_field]        Danh sách các field cần lấy thông tin (name, status, ...)
@apiParam  (Body:)     {String}        [target_object]         Lọc theo đối tượng sử dụng form <code>profile, sale, company</code>

@apiParam  (Query: )    {String}        [order_by]              Sắp xếp theo field nào (Chỉ hỗ trợ sort theo 1 filed).
                                                                <li>Nhận giá trị <code>name</code>: Tên form</li>
                                                                <li>Nhận giá trị <code>created_time</code>: Thời gian tạo form</li>
                                                                <li>Nhận giá trị <code>updated_time</code>: Thời gian cập nhật form</li>
                                                                <li>Nếu không truyền gì thì mặc định sắp xếp theo: <code>updated_time</code></li>
                                                                
@apiParam  (Query: )    {String}        [order_type]            Thứ tự cần sắp xếp
                                                                <li>Nhận giá trị <code>desc</code>, <code>asc</code></li>
                                                                <li><code>asc</code>: tăng dần</li>
                                                                <li><code>desc</code>: giảm dần</li>
                                                                <li>Mặc định <code>desc</code></li>

@apiUse params_response_form

@apiSuccess {String}            message                 Mô tả phản hồi
@apiSuccess {Integer}           code                    Mã phản hồi
@apiSuccess {Array}             data                    Danh sách form
@apiSuccess {Int}               data.submitted_total    Số lượt submit form

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "id": "6724429fbc1d709c1da7be5b",
            "name": "Biểu mẫu 1",
            "category_id": "67243ff9d254aab2abf83de0",
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2024-11-01T02:00:00Z",
            "updated_time": "2024-11-01T02:00:00Z",
            "status": "draft",
            "target_object": ["sale"],
            "related_object": ["sale"],
            "submitted_total": 100
        }
    ]
    "message": "request thành công."
}
"""


"""
@api {POST} {domain}/wfb/api/v1.0/forms/count/status       Tổng số form theo trạng thái
@apiDescription  đếm theo từ khóa 
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName CountTotalFormWithStatus

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {String}        [search]         Giá trị muốn tìm kiếm trong form
@apiParam  (Body:)     {String}        [category_id]    Id danh mục

@apiSuccess {String}            message                 Mô tả phản hồi
@apiSuccess {Integer}           code                    Mã phản hồi
@apiSuccess {Array}             data                    Danh sách form
@apiSuccess {Int}               data.status             Trạng thái
@apiSuccess {Int}               data.total              Tổng số form

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "status": "draft",
            "total": 5
        },
        {
            "status": "published",
            "total": 10
        },
        {
            "status": "stopped",
            "total": 20
        }
    ]
    "message": "request thành công."
}
"""


"""
@api {POST} {domain}/wfb/api/v1.0/forms/update-status      Cập nhật trạng thái của form
@apiDescription  Cập nhật trạng thái cho nhiều form, hiện chỉ hỗ trợ cập nhật trạng thái từ <code>published</code>: Đang sử dụng -> <code>deleted</code>: Không sử dụng
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName FormUpdateStatus

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {Array}    ids                   Danh sách ID của form cập nhật trạng thái
@apiParam  (Body:)     {String}   status                Trạng thái được thay đổi
                                                        <li><code>stopped</code> Ngừng sử dụng</li>

@apiSuccess {String}            message                 Mô tả phản hồi
@apiSuccess {Integer}           code                    Mã phản hồi
@apiSuccess {Object}            data                    Thông tin phản hồi
@apiSuccess {Object}            data.ids_success        Danh sách id form xoá thành công
@apiSuccess {Object}            data.ids_error          Danh sách id form xoá thất bại

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "ids_success": ["6724429fbc1d709c1da7be5b"],
        "ids_error": []
    }
    "message": "request thành công."
}
"""



"""
@api {post} {domain}/wfb/api/v1.0/forms/<form_id>/config-content    Cập nhật nội dung form 
@apiDescription  chỉnh sửa nội dung form 
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName UpdateConfigContent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam  (Body:)     {String}    [status]          trạng thái: published
@apiParam  (Body:)     {Object}    config        nội dung cấu hình form 
@apiParam  (Body:)     {String}    version         Phiên bản chỉnh sửa hiện tại của form


@apiParamExample {json} Body example
{
    "id": "67243ff9d254aab2abf83de0",
    "status": "published",
    "config": { ... },
    "version": "67243ff9d254aab2abf83e10"
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "version": "67243ff9d254aab2abf83de1",
    }
    "message": "request thành công."
}

"""



"""
@api {GET} {domain}/wfb/api/v1.0/forms/<form_id>       Chi tiết form 
@apiDescription  Mobile <b>{domain}/wfb/mobile/api/v1.0/forms/<form_id></b>
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName DetailForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Query: )    {String}        [selected_field]              Field cần lấy thông tin, các field cách nhau bởi dấu <code>,</code>. VD <code>name,status</code>


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "name": "form 1",
        "status": "published",
        "id": "67243ff9d254aab2abf83de0",
        "config": { ... },
        "submited_total": 10,
        "target_object": ["sale", "profile", "company"],
        "related_object": ["sale", "profile", "company"],
        "product_line": ["cad9cc6e-286b-4dd1-b734-afdd81b69a73"],
        "version": "67243ff9d254aab2abf83de1",
        "created_time": "2024-11-01T02:00:00Z",
        "updated_time": "2024-11-01T02:00:00Z",
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
    }
    "message": "request thành công."
}

"""


"""
@api {GET} {domain}/wfb/api/v1.0/forms/object     thông tin đối tượng 
@apiDescription  đối tượng sử dụng, đối tượng liên quan tới 
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName DetailObject

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "target_object": ["sale", "profile", "company"],
        "related_object": ["sale", "profile", "company"],
    }
    "message": "request thành công."
}

"""




"""
@api {GET} {domain}/wfb/api/v1.0/forms/fields     Danh sách field
@apiDescription Danh sách field kéo thả theo type
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName ListFieldType

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "tab_type":"basic",
            "group":[
                {
                    "group_type":"text",
                    "fields":[
                        {
                            "field_type":"title",
                            "field_name":"Nhan đề",
                            "allow_config_api":false,
                            "allow_config_calculation":false
                        },
                        {
                            "field_type":"heading",
                            "field_name":"Tiêu đề",
                            "allow_config_api":false,
                            "allow_config_calculation":false
                        },
                        {
                            "field_type":"content",
                            "field_name":"Nội dung",
                            "allow_config_api":false,
                            "allow_config_calculation":false
                        }
                    ]
                },
                {
                    "group_type":"button",
                    "fields":[
                        {
                            "field_type":"primary_button",
                            "field_name":"Nút chính",
                            "allow_config_api":false,
                            "allow_config_calculation":false
                        },
                        {
                            "field_type":"secondary_button",
                            "field_name":"Nút phụ",
                            "allow_config_api":false,
                            "allow_config_calculation":false
                        }
                    ]
                },
                {
                    "group_type":"media",
                    "fields":[
                        {
                            "field_type":"image",
                            "field_name":"Ảnh",
                            "allow_config_api":false,
                            "allow_config_calculation":false
                        }
                    ]
                },
                {
                    "group_type":"field",
                    "fields":[
                        {
                            "field_type":"single_text",
                            "field_name":"Single line text",
                            "allow_config_api":true,
                            "allow_config_calculation":false
                        },
                        {
                            "field_type":"multiple_text",
                            "field_name":"Multiple line text",
                            "allow_config_api":true,
                            "allow_config_calculation":false
                        },
                        {
                            "field_type":"number",
                            "field_name":"Number",
                            "allow_config_api":true,
                            "allow_config_calculation":true
                        },
                        {
                            "field_type":"radio",
                            "field_name":"Radio",
                            "allow_config_api":true,
                            "allow_config_calculation":false
                        },
                        {
                            "field_type":"checkbox",
                            "field_name":"Checkbox",
                            "allow_config_api":true,
                            "allow_config_calculation":false
                        }
                    ]
                }
            ]
        }
    ],
    "message": "request thành công."
}

"""


"""
@api {GET} {domain}/wfb/api/v1.0/currency-unit       Danh sách đơn vị tiền tệ
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName CurrencyUnit

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "code": "THB",
            "name": "Baht Thái Lan"
        },
        {
            "code": "USD",
            "name": "Đô la Mỹ"
        },
        {
            "code": "VND",
            "name": "Đồng Việt Nam"
        }
    ],
    "message": "request thành công."
}

"""



"""
@api {GET} {domain}/wfb/api/v1.0/forms/<form_id>/setting/general    Chi tiết cài đặt form
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName FormGeneralSettingDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccess {Object}            data                                    Thông tin cài đặt
@apiSuccess {String}            data.version                            Phiên bản chỉnh sửa hiện tại của form

@apiSuccess {Object}            data.confirm_setting                    Cài đặt xác nhận hoàn thành form
@apiSuccess {String}            data.confirm_setting.status             Trạng thái sử dụng
                                                                        <li><code>active</code>: Sử dụng</li>
                                                                        <li><code>inactive</code>: Không sử dụng</li>
                                                                        <li>default: <code>inactive</code></li>
@apiSuccess {String}            data.confirm_setting.title              Tiêu đề thông báo
@apiSuccess {String}            data.confirm_setting.content            Nội dung thông báo

@apiSuccess {Object}            data.edit_setting                       Cài đặt chỉnh sửa sau khi hoàn thành
@apiSuccess {String}            data.edit_setting.status                Trạng thái sử dụng
                                                                        <li><code>active</code>: Sử dụng</li>
                                                                        <li><code>inactive</code>: Không sử dụng</li>
                                                                        <li>default: <code>inactive</code></li>

@apiSuccess {String}            data.edit_setting.type                  Phạm vi chỉnh sửa
                                                                        <li><code>all</code>: Tất cả các trường thông tin</li>
                                                                        <li><code>custom</code>: Không sử dụng</li>
                                                                        <li>default: <code>all</code></li>
@apiSuccess {Array}             data.edit_setting.fields                Danh sách key_field được phép chỉnh sửa

@apiSuccess {Object}            data.complete_setting                   Cài đặt hành động sau khi submit form
@apiSuccess {String}            data.complete_setting.status            Trạng thái sử dụng
                                                                        <li><code>active</code>: Sử dụng</li>
                                                                        <li><code>inactive</code>: Không sử dụng</li>
                                                                        <li>default: <code>active</code></li>

@apiSuccess {String}            data.complete_setting.type              Hành động diễn ra
                                                                        <li><code>thanks_page</code>: Trang cảm ơn</li>
                                                                        <li><code>redirect</code>: Redirect thông tin</li>
                                                                        <li>default: <code>thanks_page</code></li>
@apiSuccess {String}            data.complete_setting.content           Nội dung trang cảm ơn
@apiSuccess {Object}            data.complete_setting.config            Cấu hình redirect thông tin 


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "version": "67243ff9d254aab2abf83de1",
        "confirm_setting": {
            "status": "inactive",
            "title": "",
            "content": ""
        },
        "edit_setting": {
            "status": "inactive",
            "type": "all",
            "fields": []
        },
        "complete_setting": {
            "status": "active",
            "type": "thanks_page",
            "content": "<div>Cảm ơn bạn đã tham gia điền form.</div>",
            "config": {}
        }
    }
    "message": "request thành công."
}

"""


"""
@api {POST} {domain}/wfb/api/v1.0/forms/<form_id>/setting/general    Cập nhật cài đặt form
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName FormGeneralSetting

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParamExample {json} Body example
{
    "version": "67243ff9d254aab2abf83de1",
    "confirm_setting": {
        "status": "active",
        "title": "Xác nhận hoàn thành biểu mẫu",
        "content": "Vui lòng kiểm tra thông tin trong biểu mẫu trước khi xác nhận hoàn thành. Sau khi gửi biểu mẫu có thể không được phép sửa."
    },
    "edit_setting": {
        "status": "active",
        "type": "all",
        "fields": []
    },
    "complete_setting": {
        "status": "active",
        "type": "thanks_page",
        "content": "<div>Cảm ơn bạn đã tham gia điền form</div>",
        "config": {}
    }
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "version": "67243ff9d254aab2abf83de1",
        "confirm_setting": {
            "status": "active",
            "title": "Xác nhận hoàn thành biểu mẫu",
            "content": "Vui lòng kiểm tra thông tin trong biểu mẫu trước khi xác nhận hoàn thành. Sau khi gửi biểu mẫu có thể không được phép sửa."
        },
        "edit_setting": {
            "status": "active",
            "type": "all",
            "fields": []
        },
        "complete_setting": {
            "status": "active",
            "type": "thanks_page",
            "content": "<div>Cảm ơn bạn đã tham gia điền form</div>",
            "config": {}
        }
    }
    "message": "request thành công."
}

"""


"""
@api {GET} {domain}/wfb/api/v1.0/forms/fields-exclude       Danh sách field loại trừ của các module
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName FieldModuleExclude

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "name": "Nhà mạng",
            "field_module": "profile",
            "field_key": "isp"
        }
    ],
    "message": "request thành công."
}

"""


"""
@api {GET} {domain}/wfb/api/v1.0/forms/object/abac       Danh sách đối tượng cho abac 
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName FieldObjectABAC 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "name": "Profile",
            "key": "profile",
        }
    ],
    "message": "request thành công."
}

"""



"""
@api {GET} {domain}/wfb/api/v1.0/forms/fields/abac       Danh sách field của form cho abac 
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName FieldFormABAC 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "fields": [
                {
                    "display_in_abac_config": true,
                    "display_type": "multi_line",
                    "field_key": "target_object",
                    "field_name": "Đối tượng sử dụng",
                    "field_property": 2,
                },
                
            ],
            "group_key": "information",
            "group_name": "Nhóm mặc định",
            "is_base": true
            "group_index": 0
        }
    ],
    "message": "request thành công."
}

"""


"""
@api {GET} {domain}/wfb/api/v1.0/forms/<form_id>/logic       Danh sách logic trong form
@apiDescription Mobile <b>/wfb/mobile/api/v1.0/forms/<form_id>/logic</b>
@apiGroup LogicForm
@apiVersion 1.0.0
@apiName LogicForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Query:)         {String}    [status]                        Trạng thái logic
                                                                        <li><code>active</code>: Đang sử dụng</li>
                                                                        <li><code>inactive</code>: Không sử dụng</li>

@apiSuccess {Array}             data                                    Danh sách logic form
@apiSuccess {String}            data.id                                 Id định danh logic form
@apiSuccess {String}            data.name                               Tên logic
@apiSuccess {String}            data.logic_type                         Loại logic
                                                                        <li><code>update_calculate_field</code>: Tính toán lại giá trị của field</li>
                                                                        <li><code>change_state_field</code>: Thay đổi trạng thái của field</li>
@apiSuccess {Array}             data.conditions                         Điều kiện logic
@apiSuccess {Int}               data.conditions.position                Thứ tự của khối logic trong danh sách conditions
@apiSuccess {String}            data.conditions.operator_key            Toán tử liên kết giữa các điều kiện <code>and</code>, <code>or</code>
@apiSuccess {Array}             data.conditions.filters                 Danh sách diều kiện trong khối điều kiện
@apiSuccess {String}            data.conditions.filters.field           ID field trong form
@apiSuccess {String}            data.conditions.filters.operator_key    Toán tử điều kiện, xem <a target="_blank" href="https://mobiojsc.sg.larksuite.com/wiki/RStWwqaphixwOtk1AZ0l8s92gHe?fromScene=spaceOverview#share-LniTdQvrtoehn2xoD7MlCclYgqe">tại đây</a>
@apiSuccess {String}            data.conditions.filters.target          Đối tượng so sánh là giá trị nhập vào hay là giá trị của field khác
                                                                        <li><code>value</code>: Giá trị nhập vào của field hiện tại</li>
                                                                        <li><code>field</code>: Giá trị của field khác</li>
@apiSuccess {Array}             data.conditions.filters.value           Giá trị của field hiện tại nếu target là <code>value</code>, field id nếu target là <code>field</code>

@apiSuccess {Array}             data.actions                            Hành động logic khi conditions thoả mãn
@apiSuccess {String}            data.actions.action                     Loại hành động, xem <a target="_blank" href="https://mobiojsc.sg.larksuite.com/wiki/RStWwqaphixwOtk1AZ0l8s92gHe?fromScene=spaceOverview#share-KdG6dMbt1o1y5Hx3m4nl0XqmgSc">tại đây</a>
@apiSuccess {Array}             data.actions.value                      Danh sách ID field/page cần thực hiện hành động hoặc danh sách công thức tính toán được cấu hình
@apiSuccess {String}            [data.actions.summary]                  Field nhận kết quả khi action là <code>calculate</code>
@apiSuccess {String}            [data.actions.message]                  Message cảnh báo khi action là <code>warning</code>

@apiSuccess {Int}               data.position                           Vị trí của logic trong danh sách
@apiSuccess {String}            data.status                             Trạng thái logic
                                                                        <li><code>active</code>: Đang sử dụng</li>
                                                                        <li><code>inactive</code>: Không sử dụng</li>
@apiSuccess {String}            data.updated_time                       Thời gian cập nhật logic


@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "actions": [
                {
                    "action": "calculate",
                    "summary": "b75ffd05-e1c9-4816-8518-6736fdc27704",
                    "value": [
                        {
                            "data": "1d3406f8-60b8-443b-9923-1371419f44e1",
                            "index": 1,
                            "type": "field"
                        },
                        {
                            "data": "+",
                            "index": 2,
                            "type": "operation"
                        },
                        {
                            "data": "14cecd75-c8ee-4558-9afc-8838b654f893",
                            "index": 3,
                            "type": "field"
                        }
                    ]
                }
            ],
            "conditions": [
                {
                    "filters": [
                        {
                            "field": "3dc8bc1d-a9d8-4e8b-bd35-f59d964e9603",
                            "operator_key": "in_value",
                            "target": "value",
                            "value": [
                                "dasdad",
                                "ffff"
                            ]
                        }
                    ],
                    "operator_key": "or",
                    "position": 0
                }
            ],
            "id": "67f74569bc7273ca1788f304",
            "logic_type": "update_calculate_field",
            "name": "logic 2",
            "position": 1,
            "status": "active",
            "updated_time": "2025-04-10T04:13:11Z"
        },
        {
            "actions": [
                {
                    "action": "hide",
                    "value": [
                        "668240af-6ca6-4b26-91dc-ad6162afd0cc"
                    ]
                }
            ],
            "conditions": [
                {
                    "filters": [
                        {
                            "field": "3dc8bc1d-a9d8-4e8b-bd35-f59d964e9603",
                            "operator_key": "not_contains",
                            "target": "value",
                            "value": [
                                "dasdad",
                                "ffff"
                            ]
                        }
                    ],
                    "operator_key": "or",
                    "position": 0
                }
            ],
            "id": "67f74598bc7273ca1788f306",
            "logic_type": "change_state_field",
            "name": "dadasdsada",
            "position": 2,
            "status": "active",
            "updated_time": "2025-04-10T04:14:12Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}

"""



"""
@api {POST} {domain}/wfb/api/v1.0/forms/<form_id>/logic       Tạo logic form mới
@apiGroup LogicForm
@apiVersion 1.0.0
@apiName CreateLogicForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {String}    name                                             Tên logic

@apiParam  (Body:)     {Array}     conditions                                       Điều kiện logic
@apiParam  (Body:)     {Int}       conditions.position                              Thứ tự của khối logic trong danh sách conditions 
@apiParam  (Body:)     {String}    conditions.operator_key                          Toán tử liên kết giữa các điều kiện <code>and</code>, <code>or</code>
@apiParam  (Body:)     {Array}     conditions.filters                               Danh sách diều kiện trong khối điều kiện
@apiParam  (Body:)     {String}    conditions.filters.field                         ID field trong form
@apiParam  (Body:)     {String}    conditions.filters.operator_key                  Toán tử điều kiện, xem <a target="_blank" href="https://mobiojsc.sg.larksuite.com/wiki/RStWwqaphixwOtk1AZ0l8s92gHe?fromScene=spaceOverview#share-LniTdQvrtoehn2xoD7MlCclYgqe">tại đây</a>
@apiParam  (Body:)     {String}    conditions.filters.target                        Đối tượng so sánh là giá trị nhập vào hay là giá trị của field khác
                                                                                    <li><code>value</code>: Giá trị nhập vào của field hiện tại</li>
                                                                                    <li><code>field</code>: Giá trị của field khác</li>
@apiParam  (Body:)     {Array}     conditions.filters.value                         Giá trị của field hiện tại nếu target là <code>value</code>, field id nếu target là <code>field</code>


@apiParam  (Body:)     {Array}     actions                                          Hành động logic khi conditions thoả mãn
@apiParam  (Body:)     {String}    actions.action                                   Loại hành động, xem <a target="_blank" href="https://mobiojsc.sg.larksuite.com/wiki/RStWwqaphixwOtk1AZ0l8s92gHe?fromScene=spaceOverview#share-KdG6dMbt1o1y5Hx3m4nl0XqmgSc">tại đây</a>
@apiParam  (Body:)     {Array}     actions.value                                    Danh sách ID field/page cần thực hiện hành động hoặc danh sách công thức tính toán được cấu hình
@apiParam  (Body:)     {String}    [actions.summary]                                Field nhận kết quả khi action là <code>calculate</code>
@apiParam  (Body:)     {String}    [actions.message]                                Message cảnh báo khi action là <code>warning</code>

@apiParam  (Body:)     {Int}       position                                         Vị trí của logic trong danh sách, vị trí này không được trùng với vị trí đã có sẵn
@apiParam  (Body:)     {String}    logic_type                                       Loại logic
                                                                                    <li><code>update_calculate_field</code>: Tính toán lại giá trị của field</li>
                                                                                    <li><code>change_state_field</code>: Thay đổi trạng thái của field</li>

@apiParamExample {json} Body example change_state_field
{
    "logic_type": "change_state_field",
    "conditions": [
        {
            "position": 1,
            "operator_key": "or",
            "filters": [
                {
                    "value": [
                        "dasdad",
                        "ffff"
                    ],
                    "target": "value",
                    "field": "3dc8bc1d-a9d8-4e8b-bd35-f59d964e9603",
                    "operator_key": "in_value"
                }
            ]
        }
    ],
    "actions": [
        {
            "value": [
                "668240af-6ca6-4b26-91dc-ad6162afd0cc"
            ],
            "action": "hide"
        }
    ],
    "name": "dadasdsada",
    "position": 1
}

@apiParamExample {json} Body example update_calculate_field
{
    "logic_type": "update_calculate_field",
    "conditions": [
        {
            "position": 0,
            "operator_key": "or",
            "filters": [
                {
                    "value": [
                        "dasdad",
                        "ffff"
                    ],
                    "target": "value",
                    "field": "3dc8bc1d-a9d8-4e8b-bd35-f59d964e9603",
                    "operator_key": "in_value"
                }
            ]
        }
    ],
    "actions": [
        {
            "action": "calculate",
            "value": [
                {
                    "type": "field",
                    "data": "1d3406f8-60b8-443b-9923-1371419f44e1",
                    "index": 1
                },
                {
                    "type": "operator",
                    "data": "+",
                    "index": 2
                },
                {
                    "type": "field",
                    "data": "14cecd75-c8ee-4558-9afc-8838b654f893",
                    "index": 3
                }
            ],
            "summary": "b75ffd05-e1c9-4816-8518-6736fdc27704"
        }
    ],
    "name": "dadasdsada",
    "position": 1
}


@apiSuccessExample {json} Response change_state_field
{
    "code": 200,
    "data": {
        "actions": [
            {
                "action": "hide",
                "value": [
                    "668240af-6ca6-4b26-91dc-ad6162afd0cc"
                ]
            }
        ],
        "conditions": [
            {
                "filters": [
                    {
                        "field": "3dc8bc1d-a9d8-4e8b-bd35-f59d964e9603",
                        "operator_key": "in_value",
                        "target": "value",
                        "value": [
                            "dasdad",
                            "ffff"
                        ]
                    }
                ],
                "operator_key": "or",
                "position": 0
            }
        ],
        "id": "67f741a1c8e1744fe453644f",
        "position": 1,
        "status": "active",
        "updated_time": "2025-04-10T03:57:21Z"
    },
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccessExample {json} Response update_calculate_field
{
    "code": 200,
    "data": {
        "logic_type": "update_calculate_field",
        "conditions": [
            {
                "position": 0,
                "operator_key": "or",
                "filters": [
                    {
                        "value": [
                            "dasdad",
                            "ffff"
                        ],
                        "target": "value",
                        "field": "3dc8bc1d-a9d8-4e8b-bd35-f59d964e9603",
                        "operator_key": "in_value"
                    }
                ]
            }
        ],
        "actions": [
            {
                "action": "calculate",
                "value": [
                    {
                        "type": "field",
                        "data": "1d3406f8-60b8-443b-9923-1371419f44e1",
                        "index": 1
                    },
                    {
                        "type": "operator",
                        "data": "+",
                        "index": 2
                    },
                    {
                        "type": "field",
                        "data": "14cecd75-c8ee-4558-9afc-8838b654f893",
                        "index": 3
                    }
                ],
                "summary": "b75ffd05-e1c9-4816-8518-6736fdc27704"
            }
        ],
        "name": "dadasdsada",
        "id": "67f741a1c8e1744fe453674f",
        "position": 1,
        "status": "active",
        "updated_time": "2025-04-10T03:57:21Z"
    },
    "lang": "vi",
    "message": "request thành công."
}

"""


"""
@api {PATCH} {domain}/wfb/api/v1.0/forms/<form_id>/logic/<logic_id>/status       Cập nhật trạng thái logic form
@apiGroup LogicForm
@apiVersion 1.0.0
@apiName StatusLogicForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {String}    status                       Trạng thái logic
                                                                <li><code>active</code>: Đang sử dụng</li>
                                                                <li><code>inactive</code>: Không sử dụng</li>
@apiParam  (Body:)     {String}    [position]                   Vị trí của logic trong danh sách, <code>required</code> khi chuyển trạng thái từ <code>inactive</code> sang <code>active</code>

@apiParamExample {json} Body example
{
    "status": "active"
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}

"""


"""
@api {PATCH} {domain}/wfb/api/v1.0/forms/<form_id>/logic/<logic_id>       Cập nhật cấu hình logic form
@apiGroup LogicForm
@apiVersion 1.0.0
@apiName UpdateLogicForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParamExample {json} Body example
{
    "logic_type": "change_state_field",
    "conditions": [
        {
            "position": 0,
            "operator_key": "or",
            "filters": [
                {
                    "value": [
                        "dasdad",
                        "ffff"
                    ],
                    "target": "value",
                    "field": "3dc8bc1d-a9d8-4e8b-bd35-f59d964e9603",
                    "operator_key": "not_contains"
                }
            ]
        }
    ],
    "actions": [
        {
            "value": [
                "668240af-6ca6-4b26-91dc-ad6162afd0cc"
            ],
            "action": "hide"
        }
    ],
    "name": "dadasdsada",
    "position": 2
}


@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "actions": [
            {
                "action": "hide",
                "value": [
                    "668240af-6ca6-4b26-91dc-ad6162afd0cc"
                ]
            }
        ],
        "conditions": [
            {
                "filters": [
                    {
                        "field": "3dc8bc1d-a9d8-4e8b-bd35-f59d964e9603",
                        "operator_key": "not_contains",
                        "target": "value",
                        "value": [
                            "dasdad",
                            "ffff"
                        ]
                    }
                ],
                "operator_key": "or",
                "position": 0
            }
        ],
        "id": "67f74598bc7273ca1788f306",
        "logic_type": "change_state_field",
        "name": "dadasdsada",
        "position": 2,
        "status": "active",
        "updated_time": "2025-04-10T04:14:12Z"
    },
    "lang": "vi",
    "message": "request thành công."
}

"""



"""
@api {DELETE} {domain}/wfb/api/v1.0/forms/<form_id>/logic/<logic_id>       Xoá cấu hình logic form
@apiGroup LogicForm
@apiVersion 1.0.0
@apiName DeleteLogicForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}

"""



"""
@api {POST} {domain}/wfb/api/v1.0/forms/<form_id>/logic/position       Thay đổi vị trí của các logic đang hoạt động
@apiDescription API thay đổi thứ tự của logic, khi thay đổi vị trí phải gửi toàn bộ thứ tự của các logic có trạng thái active để cập nhật
@apiGroup LogicForm
@apiVersion 1.0.0
@apiName ChangePositionLogicForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {Array}    config                                  Danh sách cấu hình vị trí của logic
@apiParam  (Body:)     {String}   config.id                               Id logic
@apiParam  (Body:)     {Int}      config.position                         Vị trí logic trong danh sách


@apiParamExample {json} Body example
{
    "config": [
        {
            "id": "67ea0197c84d8c35c1bc039b",
            "position": 1
        },
        {
            "id": "67ea0197c84d8c35c1bc03ac",
            "position": 2
        }
    ]
}



@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "id": "67ea0197c84d8c35c1bc039b",
            "position": 1
        },
        {
            "id": "67ea0197c84d8c35c1bc03ac",
            "position": 2
        }
    ]
    "message": "request thành công."
}

"""



"""
@api {GET} {domain}/wfb/api/v1.0/forms/<form_id>/logic/count       Số lượng logic trong form theo trạng thái
@apiGroup LogicForm
@apiVersion 1.0.0
@apiName CountLogicForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess       (data)   {Int}      active                     Số lượng logic đang hoạt động
@apiSuccess       (data)   {Int}      inactive                   Số lượng logic không hoạt động
@apiSuccess       (data)   {Int}      max_limit                  Số lượng logic tối đa trong form


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "active": 10,
        "inactive": 0,
        "max_limit": 10
    },
    "message": "request thành công."
}

"""


"""
@api {GET} {domain}/wfb/api/v1.0/forms/status      Danh sách trạng thái form
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName FormStatus

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {"key": "draft", "name": "Chưa hoàn thành"},
        {"key": "complete", "name": "Hoàn Thành"}
    ],
    "message": "request thành công."
}

"""


"""
@api {GET} {domain}/wfb/api/v1.0/forms/config/page-complete     lấy cấu hình hành động sau khi hoàn thành form 
@apiGroup FormManagement
@apiVersion 1.0.0
@apiName ConfigPageComplete 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "type": "thanks_page",
            "name": "Trang cảm ơn"
        },
        {
            "type": "redirect",
            "name": "Redirect thông tin"
        },
        {
            "type": "confirm_page",
            "name": "Trang xác nhận",
            "pages": [
                {
                    "type": "quick_sales",
                    "name": "Quick sales"
                },
                {
                    "type": "credit_card_online",
                    "name": "Pre-approved thẻ tín dụng online"
                }
            ]
        },
    ],
    "message": "request thành công."
}

"""




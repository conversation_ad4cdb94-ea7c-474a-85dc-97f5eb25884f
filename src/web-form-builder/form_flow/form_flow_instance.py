#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: thong
    Company: M O B I O
    Date Created: 16/04/2025
"""
# ------------------------------------------------------------------------------
# Thực hiện hành động trong flow
# ------------------------------------------------------------------------------

"""
@api {POST} {domain}/wfb/api/v1.0/flow/action/next-step   Cập nhật hành động luồng xử lý
@apiDescription Xử lý chuyển trạng thái l<PERSON>t submit trong flow
@apiVersion 1.0.0
@apiGroup FormFlowInstance
@apiName FlowActionNextStep

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Body: ) {String}                     submission_id    ID lượt xử lý 
@apiParam (Body: ) {String}                     flow_id          Id luồng xử lý 
@apiParam (Body: ) {String}                     action           ID hành động (ID button)
@apiParam (Body: ) {Object}                     [result_data]    Kết quả xử lý (Đối với các button nhận kết quả từ 3RD 
                                                                 thì định nghĩa cấu trúc phù hợp với node tiếp)

@apiParamExample {json} Body example
{
    "submission_id": "67d8eab62c84c6e6e44458ba",
    "flow_id": "65445cdfd8cec35c5cf8c5e0",
    "action": "ACCEPT",
    "result_data": {
        ""
    }
}

@apiSuccess {String}             message                       Mô tả phản hồi
@apiSuccess {Integer}            code                          Mã phản hồi


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}

"""


# ------------------------------------------------------------------------------
# Danh sách button tương ứng với trạng thái FLow
# ------------------------------------------------------------------------------

"""
@api {POST} {domain}/wfb/api/v1.0/flow/get/action   danh sách hành động theo trạng thái flow
@apiDescription Lấy danh sách action  tiếp theo tương ứng với trạng thái flow của lượt submit
@apiVersion 1.0.0
@apiGroup FormFlowInstance
@apiName FlowActionGetAction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Body: ) {String}                     flow_id          Id luồng xử lý 
@apiParam (Body: ) {String}                     node_id          ID khối hành động flow

@apiParamExample {json} Body example
{
    "flow_id": "65445cdfd8cec35c5cf8c5e0",
    "node_id": "1252c368-325e-4c87-8eaa-e1a0f38e189d",
}

@apiSuccess {String}             message                       Mô tả phản hồi
@apiSuccess {Integer}            code                          Mã phản hồi
@apiSuccess {ArrayObject}        data                          Danh sách Action
@apiSuccess {String}             data.id                       ID hành động
@apiSuccess {String}             data.name                     Tên hành động


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "65445cdfd8cec35c5cf8c5e0",
            "name": "Trình duyệt"
        },
        {
            "id": "c35c5cf8c5e065445cdfd8ce",
            "name": "Yêu cầu bổ sung"
        }
    
    ],
    "lang": "vi",
    "message": "request thành công."
}

"""
# Cấu hình form

"""
@api {POST} {domain}/wfb/api/v1.0/form-flow-config T<PERSON>o mới cấu hình luồng xử lý form
@apiDescription T<PERSON>o cấu hình luồng xử lý form
@apiVersion 1.0.0
@apiGroup FormFlowConfig
@apiName CreateFormFlowConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Body: ) {String}                     form_id         ID Form
@apiParam (Body: ) {Array[Object]}              process         Thông tin cấu hình
@apiParam (Body: ) {String}                     process.id      ID định danh node xử lý
@apiParam (Body: ) {String}                     process.element_type      Loại node
@apiParam (Body: ) {String}                     process.code      Mã định danh node xử lý
@apiParam (Body: ) {Object}                     process.config      <PERSON><PERSON><PERSON> hình của node
@apiParam (Body: ) {Array[Object]}              process.next_config      <PERSON><PERSON>u hình của nhánh tiếp theo
@apiParam (Body: ) {String}                     process.next_config.next_node      ID định danh node xử lý tiếp theo
@apiParam (Body: ) {String}                     process.next_config.id      ID định danh nhánh
@apiParam (Body: ) {String}                     process.next_config.name      Tên nhánh
@apiParam (Body: ) {Object}                     process.next_config.config      Cấu hình của nhánh
@apiParam (Body: ) {Int}                        process.next_config.position      Vị trí nhánh
@apiParam (Body: ) {Int}                        process.next_config.outcome_result      Giá trị xử lý của node
@apiParam (Body: ) {Object}                     process.next_config.condition      Điều kiện để mapping với outcome_result (Dùng cho luồng tự động, cái nào config dc như CALL API thì config. Còn cái nào fix code thì dễ rồi)
@apiParam (Body: ) {Array[String]}              process.prev_ids      Danh sách ID các khối trước đó "UID"
@apiParam (Body: ) {String}                     process.name      Tên Trạng thái hiện tại của Form
@apiParam (Body: ) {Object}                     process.permission      Cấu hình quyền của field form ở trạng thái hiện tại
@apiParam (Body: ) {Object}                     process.permission.form_field      Cấu hình quyền của field form
@apiParam (Body: ) {Object}                     process.permission.set_approvers      Cấu hình quyền có thể chuyển cho ai ?


@apiParamExample {json} Body example
{
    "form_id": "67d8eab62c84c6e6e44458ba",
    "process": [{
        "id": "1252c368-325e-4c87-8eaa-e1a0f38e189d",
        "element_type": "BEGIN",
        "code": "BEGIN",
        "config": {
          # cấu hình của Node - > Xử lý nghiệp vụ gì
        },
        "next_config": [{
          "next_node": "", #ID khối kế tiếp
          "id": "", # ID của nhánh
          "name": "", # Tên nhánh(Tên Button lúc approval)
          "config": {
            # Cấu hình thêm cái gì thì để vào đây
          },
          "position": 0, # Vị trí nhánh
          "outcome_result": 1 # Đối với các luồng tự động config giá trị xử lý của node tương ứng với outcome để điều hướng
          "condition": {

          }, # Điều kiện để mapping với outcome_result (Dùng cho luồng tự động, cái nào config dc như CALL API thì config. Còn cái nào fix code thì dễ rồi)
        }],
        "prev_ids": [ # Danh sách ID các khối trước đó "UID"
        ],
        "name": "Bắt đầu", # Tên Trạng thái hiện tại của Form
        "permission": { # Cấu hình quyền của field form ở trạng thái hiện tại
           "form_field": {
            "field1": ["read", "edit"]
          },
          "set_approvers": {
            # Cấu hình quyền có thể chuyển cho ai ?
          }
        }
      }]
}

@apiSuccess {String}             message                       Mô tả phản hồi
@apiSuccess {Integer}            code                          Mã phản hồi


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":{},
    "lang": "vi",
    "message": "request thành công."
}

"""


"""
@api {PUT} {domain}/wfb/api/v1.0/form-flow-config/<form_flow_config_id> Cập nhật cấu hình luồng xử lý form
@apiDescription Cập nhật cấu hình luồng xử lý form
@apiVersion 1.0.0
@apiGroup FormFlowConfig
@apiName UpdateFormFlowConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
    
@apiParam (Body: ) {String}                     form_id         ID Form
@apiParam (Body: ) {Array[Object]}              process         Thông tin cấu hình
@apiParam (Body: ) {String}                     process.id      ID định danh node xử lý
@apiParam (Body: ) {String}                     process.element_type      Loại node
@apiParam (Body: ) {String}                     process.code      Mã định danh node xử lý
@apiParam (Body: ) {Object}                     process.config      Cấu hình của node
@apiParam (Body: ) {Array[Object]}              process.next_config      Cấu hình của nhánh tiếp theo
@apiParam (Body: ) {String}                     process.next_config.next_node      ID định danh node xử lý tiếp theo
@apiParam (Body: ) {String}                     process.next_config.id      ID định danh nhánh
@apiParam (Body: ) {String}                     process.next_config.name      Tên nhánh
@apiParam (Body: ) {Object}                     process.next_config.config      Cấu hình của nhánh
@apiParam (Body: ) {Int}                        process.next_config.position      Vị trí nhánh
@apiParam (Body: ) {Int}                        process.next_config.outcome_result      Giá trị xử lý của node
@apiParam (Body: ) {Object}                     process.next_config.condition      Điều kiện để mapping với outcome_result (Dùng cho luồng tự động, cái nào config dc như CALL API thì config. Còn cái nào fix code thì dễ rồi)
@apiParam (Body: ) {Array[String]}              process.prev_ids      Danh sách ID các khối trước đó "UID"
@apiParam (Body: ) {String}                     process.name      Tên Trạng thái hiện tại của Form
@apiParam (Body: ) {Object}                     process.permission      Cấu hình quyền của field form ở trạng thái hiện tại
@apiParam (Body: ) {Object}                     process.permission.form_field      Cấu hình quyền của field form
@apiParam (Body: ) {Object}                     process.permission.set_approvers      Cấu hình quyền có thể chuyển cho ai ?
@apiParam (Body: ) {String}                     status      Trạng thái cấu hình </br>
                                                <ul>
                                                    <li><code>enable</code>: Bật</li>
                                                    <li><code>disable</code>: Tắt</li>
                                                </ul>



@apiParamExample {json} Body example
{   
    "form_id": "67d8eab62c84c6e6e44458ba",
    "process": [{
        "id": "1252c368-325e-4c87-8eaa-e1a0f38e189d",
        "element_type": "BEGIN",
        "code": "BEGIN",
        "config": {
          # cấu hình của Node - > Xử lý nghiệp vụ gì
        },
        "next_config": [{
          "next_node": "", #ID khối kế tiếp
          "id": "", # ID của nhánh
          "name": "", # Tên nhánh(Tên Button lúc approval)
          "config": {
            # Cấu hình thêm cái gì thì để vào đây
          },
          "position": 0, # Vị trí nhánh
          "outcome_result": 1 # Đối với các luồng tự động config giá trị xử lý của node tương ứng với outcome để điều hướng
          "condition": {

          }, # Điều kiện để mapping với outcome_result (Dùng cho luồng tự động, cái nào config dc như CALL API thì config. Còn cái nào fix code thì dễ rồi)
        }],
        "prev_ids": [ # Danh sách ID các khối trước đó "UID"
        ],
        "name": "Bắt đầu", # Tên Trạng thái hiện tại của Form
        "permission": { # Cấu hình quyền của field form ở trạng thái hiện tại
           "form_field": {
            "field1": ["read", "edit"]
          },
          "set_approvers": {
            # Cấu hình quyền có thể chuyển cho ai ?
          }
        }
      }]
}

@apiSuccess {String}             message                       Mô tả phản hồi
@apiSuccess {Integer}            code                          Mã phản hồi


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":{},
    "lang": "vi",
    "message": "request thành công."
}

"""

# Lấy danh sách cấu hình xử lý form

"""
@api {GET} {domain}/wfb/api/v1.0/form-flow-configs  Lấy danh sách cấu hình luồng xử lý form
@apiDescription Lấy danh sách cấu hình xử lý form
@apiVersion 1.0.0
@apiGroup FormFlowConfig
@apiName ListFormFlowConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Query:) {String}  [status]      status      Trạng thái cấu hình </br>
                                            <ul>
                                                <li><code>enable</code>: Bật</li>
                                                <li><code>disable</code>: Tắt</li>
                                            </ul>

@apiSuccess {String}             message                       Mô tả phản hồi
@apiSuccess {Integer}            code                          Mã phản hồi
@apiSuccess {Array[Object]}      data                          Danh sách cấu hình xử lý form

@apiSuccess {String}    data.form_id                         ID Form
@apiSuccess {Array[Object]}    data.process                            Thông tin cấu hình
@apiSuccess {String}    data.process.id                       ID định danh node xử lý
@apiSuccess {String}    data.process.element_type              Loại node
@apiSuccess {String}    data.process.code                      Mã định danh node xử lý
@apiSuccess {Object}    data.process.config                    Cấu hình của node
@apiSuccess {Array[Object]}    data.process.next_config        Cấu hình của nhánh tiếp theo
@apiSuccess {String}    data.process.next_config.next_node    ID định danh node xử lý tiếp theo
@apiSuccess {String}    data.process.next_config.id           ID định danh nhánh    
@apiSuccess {String}    data.process.next_config.name         Tên nhánh
@apiSuccess {Object}    data.process.next_config.config       Cấu hình của nhánh
@apiSuccess {Number}    data.process.next_config.position     Vị trí nhánh
@apiSuccess {Number}    data.process.next_config.outcome_result Giá trị xử lý của node
@apiSuccess {Object}    data.process.next_config.condition    Điều kiện để mapping với outcome_result (Dùng cho luồng tự động, cái nào config dc như CALL API thì config. Còn cái nào fix code thì dễ rồi)
@apiSuccess {Array[String]}    data.process.prev_ids           Danh sách ID các khối trước đó "UID"
@apiSuccess {String}    data.process.name                     Tên Trạng thái hiện tại của Form
@apiSuccess {Object}    data.process.permission             Cấu hình quyền của field form ở trạng thái hiện tại
@apiSuccess {Object}    data.process.permission.form_field    Cấu hình quyền của field form
@apiSuccess {Object}    data.process.permission.set_approvers    Cấu hình quyền có thể chuyển cho ai ?
@apiSuccess {String}    data.status                         Trạng thái cấu hình </br>
                                                            <ul>
                                                                <li><code>enable</code>: Bật</li>
                                                                <li><code>disable</code>: Tắt</li>
                                                            </ul>
@apiSuccess {Object}    data.created_time                   Thời gian khởi tạo
@apiSuccess {Object}    data.updated_time                   Thời gian cập nhật mới nhất
@apiSuccess {Object}    data.created_by                     ID Người tạo
@apiSuccess {Object}    data.updated_by                     ID Người cập nhật

@apiSuccessExample:
{
    "code": 200,
    "data": [
        {
            "form_id": "67d8eab62c84c6e6e44458ba",
            "process": [{
            "id": "1252c368-325e-4c87-8eaa-e1a0f38e189d",
            "element_type": "BEGIN",
            "code": "BEGIN",
            "config": {
              # cấu hình của Node - > Xử lý nghiệp vụ gì
            },
            "next_config": [{
              "next_node": "", #ID khối kế tiếp
              "id": "", # ID của nhánh
              "name": "", # Tên nhánh(Tên Button lúc approval)
              "config": {
                # Cấu hình thêm cái gì thì để vào đây
              },
              "position": 0, # Vị trí nhánh
              "outcome_result": 1 # Đối với các luồng tự động config giá trị xử lý của node tương ứng với outcome để điều hướng
              "condition": {
    
              }, # Điều kiện để mapping với outcome_result (Dùng cho luồng tự động, cái nào config dc như CALL API thì config. Còn cái nào fix code thì dễ rồi)
            }],
            "prev_ids": [ # Danh sách ID các khối trước đó "UID"
            ],
            "name": "Bắt đầu", # Tên Trạng thái hiện tại của Form
            "permission": { # Cấu hình quyền của field form ở trạng thái hiện tại
               "form_field": {
                "field1": ["read", "edit"]
              },
              "set_approvers": {
                # Cấu hình quyền có thể chuyển cho ai ?
              }
            }
          }],
            "status": "enable", // disable
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {DELETE} {domain}/wfb/api/v1.0/form-flow-config/<form_flow_config_id> Xoá cấu hình luồng xử lý form
@apiDescription Xoá cấu hình luồng xử lý form
@apiVersion 1.0.0
@apiGroup FormFlowConfig
@apiName DeleteFormFlowConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiSuccess {String}             message                       Mô tả phản hồi
@apiSuccess {Integer}            code                          Mã phản hồi


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":{},
    "lang": "vi",
    "message": "request thành công."
}

"""

"""
@api {GET} {domain}/wfb/api/v1.0/form-flow-config/<form_flow_config_id> Lấy chi tiết cấu hình luồng xử lý form
@apiDescription Lấy chi tiết cấu hình luồng xử lý form
@apiVersion 1.0.0
@apiGroup FormFlowConfig
@apiName DetailFormFlowConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiSuccess {String}             message                       Mô tả phản hồi
@apiSuccess {Integer}            code                          Mã phản hồi
@apiSuccess {Object}             data                          Thông tin cấu hình


@apiSuccess {Number}    data.form_id                           ID Form
@apiSuccess {Array[Object]}    data.process                    Thông tin cấu hình
@apiSuccess {String}    data.process.id                        ID định danh node xử lý
@apiSuccess {String}    data.process.element_type              Loại node
@apiSuccess {String}    data.process.code                      Mã định danh node xử lý
@apiSuccess {Object}    data.process.config                    Cấu hình của node
@apiSuccess {Array[Object]}    data.process.next_config        Cấu hình của nhánh tiếp theo
@apiSuccess {String}    data.process.next_config.next_node    ID định danh node xử lý tiếp theo
@apiSuccess {String}    data.process.next_config.id           ID định danh nhánh    
@apiSuccess {String}    data.process.next_config.name         Tên nhánh
@apiSuccess {Object}    data.process.next_config.config       Cấu hình của nhánh
@apiSuccess {Number}    data.process.next_config.position     Vị trí nhánh
@apiSuccess {Number}    data.process.next_config.outcome_result Giá trị xử lý của node
@apiSuccess {Object}    data.process.next_config.condition    Điều kiện để mapping với outcome_result (Dùng cho luồng tự động, cái nào config dc như CALL API thì config. Còn cái nào fix code thì dễ rồi)
@apiSuccess {Array[String]}    data.process.prev_ids           Danh sách ID các khối trước đó "UID"
@apiSuccess {String}    data.process.name                     Tên Trạng thái hiện tại của Form
@apiSuccess {Object}    data.process.permission             Cấu hình quyền của field form ở trạng thái hiện tại
@apiSuccess {Object}    data.process.permission.form_field    Cấu hình quyền của field form
@apiSuccess {Object}    data.process.permission.set_approvers    Cấu hình quyền có thể chuyển cho ai ?
@apiSuccess {String}    data.status                         Trạng thái cấu hình </br>
                                                            <ul>
                                                                <li><code>enable</code>: Bật</li>
                                                                <li><code>disable</code>: Tắt</li>
                                                            </ul>
@apiSuccess {Object}    data.created_time                   Thời gian khởi tạo
@apiSuccess {Object}    data.updated_time                   Thời gian cập nhật mới nhất
@apiSuccess {Object}    data.created_by                     ID Người tạo
@apiSuccess {Object}    data.updated_by                     ID Người cập nhật

@apiSuccessExample:
{
    "code": 200,
    "data": {
        "form_id": "67d8eab62c84c6e6e44458ba",
        "process": [{
        "id": "1252c368-325e-4c87-8eaa-e1a0f38e189d",
        "element_type": "BEGIN",
        "code": "BEGIN",
        "config": {
          # cấu hình của Node - > Xử lý nghiệp vụ gì
        },
        "next_config": [{
          "next_node": "", #ID khối kế tiếp
          "id": "", # ID của nhánh
          "name": "", # Tên nhánh(Tên Button lúc approval)
          "config": {
            # Cấu hình thêm cái gì thì để vào đây
          },
          "position": 0, # Vị trí nhánh
          "outcome_result": 1 # Đối với các luồng tự động config giá trị xử lý của node tương ứng với outcome để điều hướng
          "condition": {

          }, # Điều kiện để mapping với outcome_result (Dùng cho luồng tự động, cái nào config dc như CALL API thì config. Còn cái nào fix code thì dễ rồi)
        }],
        "prev_ids": [ # Danh sách ID các khối trước đó "UID"
        ],
        "name": "Bắt đầu", # Tên Trạng thái hiện tại của Form
        "permission": { # Cấu hình quyền của field form ở trạng thái hiện tại
           "form_field": {
            "field1": ["read", "edit"]
          },
          "set_approvers": {
            # Cấu hình quyền có thể chuyển cho ai ?
          }
        }
      }],
        "status": "enable", // disable
     },
    "lang": "vi",
    "message": "request thành công."
}

"""


"""
@api {POST} {domain}/wfb/api/v1.0/form-flow-config/<form_flow_config_id>/status Cập nhật trạng thái cấu hình luồng xử lý form
@apiDescription  Cập nhật trạng thái cấu hình luồng xử lý form
@apiVersion 1.0.0
@apiGroup FormFlowConfig
@apiName UpdateStatusFormFlowConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam  (Body:)     {String}    status          Trạng thái của cấu hình luồng xử lý form
                                                    <li><code>published</code>: Bật</li>
                                                    <li><code>stopped</code>: Tắt</li>

@apiSuccess {String}             message                       Mô tả phản hồi
@apiSuccess {Integer}            code                          Mã phản hồi


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":{},
    "lang": "vi",
    "message": "request thành công."
}

"""
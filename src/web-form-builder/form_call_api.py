
"""
@apiDefine params_call_api_example
@apiVersion 1.0.0

@apiParam  (Body:)     {String}    url                     api url 
@apiParam  (Body:)     {String}    method                  phương thức request: 'GET', 'POST', 'PUT', 'PATCH', 'DELETE'
@apiParam  (Body:)     {Object}    [authorization]         thông tin xác thực 
@apiParam  (Body:)     {Object}    [headers]               thông tin header  
@apiParam  (Body:)     {Object}    [params]                 các tham số trên url 
@apiParam  (Body:)     {Object}    [body]                   dữ liệu cần gửi
@apiParam  (Body:)     {Object}    account_id               id account thưc hiện call api
@apiParam  (Body:)     {Object}    merchant_id              id tenant
@apiParam  (Body:)     {Object}    object_id                Id đối tượng sử dụng form
@apiParam  (Body:)     {Object}    object_type              Loại đối tượng sử dụng form

@apiParamExample {json} Body example
{
    "url": "https://...",
    "method": "POST",
    "authorization": {
        "Authorization": "Basic xxxxxx"
    },
    "params": {
        "search": "123"
    },
    "headers": {
        "Content-Type": "application/json"
    },
    "body": {},
}

"""

"""
@api {POST} {domain}/wfb/get-data/api/v1.0/forms/call-api   Field gọi api lấy dữ liệu (Web)
@apiDescription gửi cấu hình call api, api này đóng vai trò là forward nên dữ liệu trả về lấy từ response của api cấu hình 
@apiGroup FormCallApi
@apiVersion 1.0.0
@apiName WebCallApiField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiUse params_call_api_example

@apiSuccessExample {json} Response
{
    // response từ api trả về không có mẫu chung 
}
"""


"""
@api {POST} {domain}/pbuser/wfb/mobile/get-data/api/v1.0/forms/call-api   Field gọi api lấy dữ liệu (Mobile)
@apiDescription gửi cấu hình call api, api này đóng vai trò là forward nên dữ liệu trả về lấy từ response của api cấu hình 
@apiGroup FormCallApi
@apiVersion 1.0.0
@apiName MobileCallApiField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiUse params_call_api_example

@apiSuccessExample {json} Response
{
    // response từ api trả về không có mẫu chung 
}
"""


"""
@api {POST} {domain}/wfb/get-data/api/v1.0/forms/config/call-api   Test call api trong quá trình cấu hình form
@apiDescription gửi cấu hình call api, api này đóng vai trò là forward nên dữ liệu trả về lấy từ response của api cấu hình 
@apiGroup FormCallApi
@apiVersion 1.0.0
@apiName CallTestApiField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam  (Body:)     {String}    url                     api url 
@apiParam  (Body:)     {String}    method                  phương thức request: 'GET', 'POST', 'PUT', 'PATCH', 'DELETE'
@apiParam  (Body:)     {Object}    [authorization]         thông tin xác thực 
@apiParam  (Body:)     {Object}    [headers]               thông tin header  
@apiParam  (Body:)     {Object}    [params]                 các tham số trên url 
@apiParam  (Body:)     {Object}    [body]                   dữ liệu cần gửi

@apiSuccessExample {json} Response
{
    // response từ api trả về không có mẫu chung 
}
"""


"""
@api {get} {domain}/pbuser/wfb/mobile/get-data/api/v1.0/form/savings-account   danh sách tài khoản tiết kiệm 
@apiGroup EIBCallAPI
@apiVersion 1.0.0
@apiName ListTKTietKiem

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Query:)     {String}    cif          mã cif khách hàng 

@apiSuccess {String}            account_number                         số tài khoản 
@apiSuccess {Number}            account_value                          giá trị tài khoản       
@apiSuccess {String}            maturity_date                         ngày đáo hạn "dd/mm/yyyy"
@apiSuccess {String}            cif                                     mã cif khách hàng
@apiSuccess {Number}            use_value                          giá trị tài sản sử dụng   
@apiSuccess {Number}            secure_value                          giá trị tài sản đảm bảo   

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "account_number": "*********",
            "account_value": *********,
            "maturity_date": "15/12/2026",
            "cif": "123456",
            "use_value": *********,
            "secure_value": *********,
        }
    ]
}

"""



"""
@api {get} {domain}/pbuser/wfb/mobile/get-data/api/v1.0/form/identify-doc  Lấy thông tin giấy tờ định danh 
@apiGroup EIBCallAPI
@apiVersion 1.0.0
@apiName GetIdentifyDoc 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Query:)     {String}    cif          mã cif khách hàng 
@apiParam  (Query:)     {String}    doc_type      loại giấy tờ 

@apiSuccess {String}            doc_type                        loại giấy tờ 
@apiSuccess {String}            identify_value                   mã giấy tờ       
@apiSuccess {String}            create_date                      ngày cấp
@apiSuccess {String}            cif                              mã cif khách hàng
@apiSuccess {String}            country_code                      mã quốc gia  
@apiSuccess {String}            country                          tên quốc gia  
@apiSuccess {String}            create_plate                     nơi cấp 

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "doc_type": "cccd",
        "identify_value": "017209876",
        "create_date": "15/12/2006",
        "cif": "anhnt",
        "country_code": "VN",
        "country": "Việt Nam",
        "create_plate": "Nha Trang",
    }
    
}

"""


"""
@api {GET} {domain}/pbuser/wfb/mobile/get-data/api/v1.0/forms/constant-data       Lấy danh sách các constant data của form theo data_type
@apiDescription web {domain}/wfb/get-data/api/v1.0/forms/constant-data
@apiGroup FormCallApi
@apiVersion 1.0.0
@apiName MerchantConstantData

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header



@apiParam  (Query:)     {String}    data_type           Loại data cần lấy danh sách
                                                        <li><code>DOC_ATTACHMENTS</code>: Danh sách nhóm hồ sơ tài liệu đính kèm EIB</li>
                                                        <li><code>TYPES_ATTACHMENTS</code>: Danh sách loại hồ sơ tài liệu đính kèm EIB</li>
                                                        <li><code>PHONE_COUNTRY_CODE</code>: Mã số điện thoại/quốc kì theo quốc gia</li>
                                                        <li><code>KYHAN</code>: Kỳ hạn vay/thanh toán...</li>
                                                        <li><code>LOAIID</code>: Loại giấy tờ định danh cá nhân</li>
                                                        <li><code>TINHTRANGVENHACUA</code>: Tình trạng nhà cửa</li>
                                                        <li><code>THUNHAP</code>: Loại thu nhập</li>
                                                        <li><code>NGUOITHAMCHIEU</code>: Người tham chiếu</li>
                                                        <li><code>TINHTRANGHONNHAN</code>: Tình trạng hôn nhân</li>
                                                        <li><code>DOMAIN_EMAIL_INVALID</code>: Danh sách domain email không hợp lệ</li>
                                                        <li><code>KHACHHANGVIP</code>: Hạng khách hàng</li>
                                                        <li><code>GIOITINH</code>: Giới tính</li>


@apiParam  (Query:)     {String}    [parent_id]         key định danh của nhóm hồ sơ tài liệu đính kèm EIB, <code>required</code> khi data_type là <code>TYPES_ATTACHMENTS</code>

@apiParam  (Query:)     {String}    [search_key]        Danh sách data cần tìm kiếm, cách nhau bằng dấu <code>,</code>, VD với loại PHONE_COUNTRY_CODE <code>VN,US,CN</code>


@apiSuccess {String}            data.key                                key data
@apiSuccess {String}            data.name                               Tên data
@apiSuccess {String}            [data.dial_code]                        Mã số điện thoại
@apiSuccess {String}            [data.flag]                             Quốc kỳ
@apiSuccess {String}            [data.name]                             Tên data


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "key": "GHS_01",
            "name": "Hồ sơ pháp lý"
        },
        {
            "key": "GHS_03",
            "name": "Hồ sơ tài chính"
        }
    ],
    "message": "request thành công."
}

@apiSuccessExample {json}  Phone Code Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "dial_code": "+376",
            "flag": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAABmJLR0QA/wD/AP+gvaeTAAACw0lEQVR4nO3aS0hUURzH8e+ZO5OOo3ObqKTCaCG9JKHQfEC2yp0UCAaGy2hf6zZtrZ20CMRoF24iWtUiTAs0JCLLMknCSCWhxtHxgffeFpcim5nOBOOci/4/MAycuXPvf36c+Z8zDxBCCCGEEEIIIYQQ290g4G3T2+DfYagsAXn5pLiFbcgknOuogxfuFvSqn+50F/R8786tF/R8ta8/ZB0PFfQqW5AEpCEBaUhAGhKQhgSkkXOZL4apj4qxUcXkhGJ+TpFK+puQcht2V3pUH/Y4UedxqNrc1sxIQG9fKR7eDzHzJXOf6gGpJKSSiqkJxZNHsL8K2jocak4WPygjAfX1WKytgp3wqGv2OFLjsa8K4jv9ABZ+KGam4f2YYvSF4uu0oq/H4mZvYTeH+TASUOdlh7U1RX2zi5WlAjvhYSfgaK1HWwe8fK7YUVL8OsFQQKcaf3021LPC0HjWXA+SVUzDyAy6cS1MKgkV8fyOTy1A3Pa4fsvZ3MKyMBLQ/Jx/v7qS/3O+rWT7ZmbzGd0Hdc6vZowduJrOGOu+lyhGOVkZDSjuZDbfXbZroJLcpElrGJ1BfxqPWoyUR7B6S2htWqL+eObbz4RAzKDlEDyLR1gOwWJa8eBpjMW0mab8N6MzaM8lfxmbTVl4w6W/x11X8bk/yt6w+X5kNKCyY/6+pmrdITHu8n3Bn9AVjkd0UpH2LP/ASlMVBqQHRcJwpT3J8JtSkgMRatIOVkB+fApEDwKwy11am9KcXlwn5gYkHQIUUFBJQBpGAiqN+m+h6Vl9C5yN+CWWxTa1pJyMNOmGMx4DjxW3++3MB3OsWA0tZpZ8IwGd73RBKUaGFMtL/z42GoPGFpe2i9sooHAY2rsc2rtMXP3/SJPWkIA0JCANCUhDAtKQgDTkT5yZNmSSbQYNFamQINrOr10IIYQQQgghhBBCAD8BqwrmrQWey0MAAAAASUVORK5CYII=",
            "key": "AD",
            "name": "Andorra"
        }
    ],
    "message": "request thành công."
}

"""


"""
@api {POST} {domain}/wfb/api/v1.0/forms/<form_id>/config-api       Tạo cấu hình api
@apiGroup FormConfigApi
@apiVersion 1.0.0
@apiName CreateConfigApi

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam   (Body:)     {String}        name                                Tên API
@apiParam   (Body:)     {String}        mapping_response_type               Loại mapping dữ liệu trả về
                                                                            <li><code>no_mapping</code>: Không sử dụng kết quả response</li>
                                                                            <li><code>mapping_field_value</code>: Lấy giá trị từ kết quả api đưa vào giá trị các field trên form </li>
                                                                            <li><code>mapping_select</code>: Sử dụng kiểu mapping cho field dropdow lựa chọn</li>
@apiParam   (Body:)     {String}        url                                 Url api
@apiParam   (Body:)     {String}        method                              Phương thức request: GET, POST, PUT, PATCH, DELETE
@apiParam   (Body:)     {Array}         authorization                       Phương thức xác thực
@apiParam   (Body:)     {String}        authorization.key                   Key xác thực
@apiParam   (Body:)     {String}        authorization.value                 Giá trị xác thực
@apiParam   (Body:)     {String}        authorization.type                  Phương thức điền giá trị xác thực
                                                                            <li><code>field</code>: Lấy giá trị từ field</li>
                                                                            <li><code>enter_content</code>: Nhập liệu</li>
@apiParam   (Body:)     {Array}         params                              Paramenter data
@apiParam   (Body:)     {String}        params.key                          Key params
@apiParam   (Body:)     {String}        params.value                        Giá trị
@apiParam   (Body:)     {String}        params.type                         Phương thức điền giá
                                                                            <li><code>field</code>: Lấy giá trị từ field</li>
                                                                            <li><code>enter_content</code>: Nhập liệu</li>
@apiParam   (Body:)     {Array}         headers                             Headers
@apiParam   (Body:)     {String}        headers.key                         Key headers
@apiParam   (Body:)     {String}        headers.value                       Giá trị
@apiParam   (Body:)     {String}        headers.type                        Phương thức điền giá
                                                                            <li><code>field</code>: Lấy giá trị từ field</li>
                                                                            <li><code>enter_content</code>: Nhập liệu</li>

@apiParam   (Body:)     {Object}        body                                Body request api
@apiParam   (Body:)     {String}        body.type                           Kiểu dữ liệu của body
                                                                            <li><code>none</code>: Không có body</li>
                                                                            <li><code>raw</code></li>
                                                                            <li><code>form_data</code></li>
                                                                            <li><code>form_urlencoded</code></li>
@apiParam   (Body:)     {String}        [body.raw]                          Giá trị body khi type là <code>raw</code>

@apiParam   (Body:)     {Array}         [body.form_data]                    Giá trị body khi type là <code>form_data</code>
@apiParam   (Body:)     {String}        body.form_data.key                  Key
@apiParam   (Body:)     {String}        body.form_data.value                Giá trị
@apiParam   (Body:)     {String}        body.form_data.type                 Phương thức điền giá
                                                                            <li><code>field</code>: Lấy giá trị từ field</li>
                                                                            <li><code>enter_content</code>: Nhập liệu</li>

@apiParam   (Body:)     {Array}         [body.form_urlencoded]              Giá trị body khi type là <code>form_urlencoded</code>
@apiParam   (Body:)     {String}        body.form_urlencoded.key            Key
@apiParam   (Body:)     {String}        body.form_urlencoded.value          Giá trị
@apiParam   (Body:)     {String}        body.form_urlencoded.type           Phương thức điền giá
                                                                            <li><code>field</code>: Lấy giá trị từ field</li>
                                                                            <li><code>enter_content</code>: Nhập liệu</li>

@apiParam   (Body:)     {Object}        mapping_response                    Mapping dữ liệu trả về
@apiParam   (Body:)     {String}        mapping_response.type               Phương thức mapping dữ liệu trả về
                                                                            <li><code>none</code>: không mapping</li>
                                                                            <li><code>text</code>: Mapping text</li>
                                                                            <li><code>json</code>: Json data</li>
@apiParam   (Body:)     {String}        mapping_response.data_json_example  Mẫu dữ liệu trả về
@apiParam   (Body:)     {Array}        mapping_response.fields              Mapping field
@apiParam   (Body:)     {Array}        mapping_response.fields.value        Giá trị
@apiParam   (Body:)     {Array}        mapping_response.fields.field_id     Field id được mapping

@apiSuccess {String}            data.id                                Id định danh cấu hình api
@apiSuccess {String}            data....                 Toàn bộ thông tin cấu hình api đã gửi lên

@apiSuccessExample {json}  Response
{
    "code": 200,
    "lang": "vi",
    "data": {   
        "id": "68071815bc160eb674370586"
        "name": "API test"
        "method" : "GET",
        "authorization" : [
            {
                "key" : "Authorization",
                "type" : "enter_content",
                "value" : "4OUnQVaneedFE2CJWw7XZzGhY6luX4Qb0Fo7qcaoDaAPVNjwVMeW3Nmfg_ayZLJiXRvA-ZZoNF3hF6_fixsbDqvfQV8VKVYEVfUqhcxFpGE="
            }
        ],
        "params" : [

        ],
        "headers" : [

        ],
        "body" : {
            "type" : "none"
        },
        "mapping_response" : {
            "type" : "JSON",
            "single_text" : {
                "root_path_object" : "$",
                "paths" : [
                    "$.data.username"
                ]
            },
            "data_json_example" : "{\n    \"data\": {\n        \"email\": \"<EMAIL>\",\n        \"name\": \" \",\n        \"user_id\": \"5867897316444191080\",\n        \"username\": \"abc123\"\n    },\n    \"status\": 1\n}"
        },
        "url" : "https://abcdzxxx.com/api/v1.0/user/get-user-info",
    }
    "message": "request thành công."
}
"""


"""
@api {PATCH} {domain}/wfb/api/v1.0/forms/<form_id>/config-api/<config_id>       Update cấu hình api
@apiGroup FormConfigApi
@apiVersion 1.0.0
@apiName UpdateConfigApi

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json}  Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "method" : "GET",
        "authorization" : [
            {
                "key" : "Authorization",
                "type" : "enter_content",
                "value" : "4OUnQVaneedFE2CJWw7XZzGhY6luX4Qb0Fo7qcaoDaAPVNjwVMeW3Nmfg_ayZLJiXRvA-ZZoNF3hF6_fixsbDqvfQV8VKVYEVfUqhcxFpGE="
            }
        ],
        "params" : [

        ],
        "headers" : [

        ],
        "body" : {
            "type" : "none"
        },
        "mapping_response" : {
            "type" : "JSON",
            "single_text" : {
                "root_path_object" : "$",
                "paths" : [
                    "$.data.username"
                ]
            },
            "data_json_example" : "{\n    \"data\": {\n        \"email\": \"<EMAIL>\",\n        \"name\": \" \",\n        \"user_id\": \"5867897316444191080\",\n        \"username\": \"abc123\"\n    },\n    \"status\": 1\n}"
        },
        "url" : "https://abcdzxxx.com/api/v1.0/user/get-user-info",
    }
    "message": "request thành công."
}
"""




"""
@api {GET} {domain}/wfb/api/v1.0/forms/<form_id>/config-api/<config_id>       Chi tiết cấu hình api
@apiDescription  Mobile <code>{domain}/pbuser/wfb/mobile/api/v1.0/forms/<form_id>/config-api/<config_id></code>
@apiGroup FormConfigApi
@apiVersion 1.0.0
@apiName DetailConfigApi

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json}  Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "method" : "GET",
        "authorization" : [
            {
                "key" : "Authorization",
                "type" : "enter_content",
                "value" : "4OUnQVaneedFE2CJWw7XZzGhY6luX4Qb0Fo7qcaoDaAPVNjwVMeW3Nmfg_ayZLJiXRvA-ZZoNF3hF6_fixsbDqvfQV8VKVYEVfUqhcxFpGE="
            }
        ],
        "params" : [

        ],
        "headers" : [

        ],
        "body" : {
            "type" : "none"
        },
        "mapping_response" : {
            "type" : "JSON",
            "single_text" : {
                "root_path_object" : "$",
                "paths" : [
                    "$.data.username"
                ]
            },
            "data_json_example" : "{\n    \"data\": {\n        \"email\": \"<EMAIL>\",\n        \"name\": \" \",\n        \"user_id\": \"5867897316444191080\",\n        \"username\": \"abc123\"\n    },\n    \"status\": 1\n}"
        },
        "url" : "https://abcdzxxx.com/api/v1.0/user/get-user-info",
    }
    "message": "request thành công."
}
"""



"""
@api {GET} {domain}/wfb/api/v1.0/forms/<form_id>/config-api              Danh sách cấu hình api
@apiGroup FormConfigApi
@apiVersion 1.0.0
@apiName ListConfigApi

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json}  Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "method" : "GET",
            "authorization" : [
                {
                    "key" : "Authorization",
                    "type" : "enter_content",
                    "value" : "4OUnQVaneedFE2CJWw7XZzGhY6luX4Qb0Fo7qcaoDaAPVNjwVMeW3Nmfg_ayZLJiXRvA-ZZoNF3hF6_fixsbDqvfQV8VKVYEVfUqhcxFpGE="
                }
            ],
            "params" : [

            ],
            "headers" : [

            ],
            "body" : {
                "type" : "none"
            },
            "mapping_response" : {
                "type" : "JSON",
                "single_text" : {
                    "root_path_object" : "$",
                    "paths" : [
                        "$.data.username"
                    ]
                },
                "data_json_example" : "{\n    \"data\": {\n        \"email\": \"<EMAIL>\",\n        \"name\": \" \",\n        \"user_id\": \"5867897316444191080\",\n        \"username\": \"abc123\"\n    },\n    \"status\": 1\n}"
            },
            "url" : "https://abcdzxxx.com/api/v1.0/user/get-user-info",
        }
    ],
    "message": "request thành công."
}
"""


"""
@api {DELETE} {domain}/wfb/api/v1.0/forms/<form_id>/config-api/<config_id>       Xoá cấu hình api
@apiGroup FormConfigApi
@apiVersion 1.0.0
@apiName UpdateConfigApi

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json}  Response
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
"""
@apiDefine ObjectUsedForm

@apiParam  (Body:)     {String}    object_type          Đ<PERSON><PERSON> tượng sử dụng form
                                                        <li><code>sale</code>: <PERSON><PERSON> hội bán</li>
                                                        <li><code>profile</code>: Profile</li>
                                                        <li><code>company</code>: Công ty</li>
@apiParam  (Body:)     {String}    object_id            ID Đối tượng sử dụng form
@apiParam  (Body:)     {String}    form_id              Id form
"""
"""
@apiDefine ProcessStatusForm

@apiSuccess {String}            [data.process_status]                       Trạng thái xử lý trong form
@apiSuccess {String}            [data.process_status.status]                Trạng thái
                                                                            <li><code>cif_exists</code>: Cif đã tồn tại</li>
                                                                            <li><code>ekyc_success</code>: EKYC thành công</li>
                                                                            <li><code>ekyc_fail</code>: EKY<PERSON> thất bại</li>
                                                                            <li><code>success</code>: Th<PERSON>nh công</li>
                                                                            <li><code>landing_page_cancel</code>: Cancel trên landing page</li>
                                                                            <li><code>landing_page_accept</code>: hoàn thành bước OTP tại landing page</li>
                                                                            <li><code>cif_create_error</code>: Tạo cif thất bại</li>
                                                                            <li><code>account_create_error</code>: Tạo TKTT lỗi</li>
                                                                            <li><code>edigi_create_error</code>: Tạo tài khoản Edigi lỗi</li>
                                                                            <li><code>fail</code>: thất bại</li>
                                                                            <li>Tất cả các status khác <code>success</code> được coi là chưa hoàn thành</li>
@apiSuccess {String}            [data.process_status.reason]                Lý do lỗi
@apiSuccess {String}            [data.process_status.confirm_page_status]    Trạng thái tại landing page, với luồng pre-approved qua OTP thì lần sửa form tiếp theo không hiển thị QR 
                                                                            <li><code>landing_page_cancel</code>: Cancel trên landing page</li>
                                                                            <li><code>landing_page_accept</code>: hoàn thành bước OTP tại landing page</li>
@apiSuccess {String}            [data.process_status.external_status]    Trạng thái hồ sơ tại hệ thống ngoài, dùng api khác để từ mã lấy ra tên 

"""



"""
@api {POST} {domain}/wfb/mobile/api/v1.0/forms/used       Danh sách form được sử dụng bởi đối tượng
@apiGroup MobileFormSubmission
@apiVersion 1.0.0
@apiName ObjectUsedForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam  (Body:)     {String}    target_object         Đối tượng sử dụng form
@apiParam  (Body:)     {Array}     [product_line]        Danh sách dòng sản phẩm <code>required</code> khi target_object có giá trị là <code>sale</code>
@apiParam  (Body:)     {String}    [form_code]           Mã lấy danh sách form cho các form tuỳ chỉnh, nhận các giá trị:
                                                         <li><code>quick_sales</code>: Form quick sale EIB</li>

@apiSuccess {String}            data.id                                     Id form
@apiSuccess {String}            data.name                                   Têm form
@apiSuccess {Object}            data.config                                 Cấu hình form
@apiSuccess {String}            data.target_object                          Đối tượng sử dụng form
@apiUse ProcessStatusForm

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "name": "form 1",
            "id": "67243ff9d254aab2abf83de0",
            "config": { ... },
            "target_object": ["sale", "profile", "company"],
        }
    ]
    "message": "request thành công."
}

"""



"""
@api {POST} {domain}/wfb/mobile/api/v1.0/forms/submission       Submit form
@apiGroup MobileFormSubmission
@apiVersion 1.0.0
@apiName SubmitForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiUse ObjectUsedForm
@apiParam  (Body:)     {Object}    submit_data                              Dữ liệu submit của form, tham khảo <a href="https://mobiojsc.sg.larksuite.com/wiki/CZx2w7p22ilThSkcEEslJzEfgOd?fromScene=spaceOverview#share-DMd6dKWxAojsKqxjodjlVu9BgLc">tại đây</a>
@apiParam  (Body:)     {String}    version                                  Version submit form
@apiParam  (Body:)     {String}    version_config                           Version của cấu hình form hiện tại
@apiParam  (Body:)     {Object}    [process_status]                         Trạng thái xử lý trong form
@apiParam  (Body:)     {String}    [process_status.status]                  Trạng thái
                                                                            <li><code>cif_exists</code>: Cif đã tồn tại</li>
                                                                            <li><code>ekyc_success</code>: EKYC thành công</li>
                                                                            <li><code>ekyc_fail</code>: EKYC thất bại</li>
@apiParam  (Body:)     {String}    [process_status.reason]                  Lý do lỗi
@apiParam  (Body:)     {String}    [action]                                 Action submit form
                                                                            <li><code>submit</code>: Người dùng chọn submit button</li>
                                                                            <li><code>draft</code>: Auto save trong quá trình điền form</li>
                                                                            <li>Default: <code>draft</code></li>

@apiParamExample {json} Body example
{
    "form_id": "673ef959e2590c5e63ce93c1",
    "object_type": "sale",
    "object_id": "c9d79c5e-a7e8-11ef-8cdb-276bd2b69d37",
    "submit_data": {...},
    "version": "",
    "version_config": ""
    "action": "draft"
}


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "form_id": "673ef959e2590c5e63ce93c1",
        "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "object_type": "sale",
        "object_id": "c9d79c5e-a7e8-11ef-8cdb-276bd2b69d37",
        "submit_data": {...},
        "version": "673efc3fe2590c5e63ce93c3",
        "created_time": "2024-11-21T00:00:00Z",
        "name": "form 1",

    },
    "message": "request thành công."
}

"""


"""
@api {GET} {domain}/wfb/mobile/api/v1.0/forms/submission/records       Danh sách form đã được điền
@apiGroup MobileFormSubmission
@apiVersion 1.0.0
@apiName ListFormRecorded

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging


@apiParam  (Query:)     {String}    object_type          Đối tượng sử dụng form
@apiParam  (Query:)     {String}    object_id            ID Đối tượng sử dụng form

@apiSuccess {String}            data.form_id            Id form
@apiSuccess {String}            data.account_id         Id account submit form
@apiSuccess {String}            data.name               Tên form
@apiSuccess {Object}            data.submit_data        Dữ liệu submit form
@apiSuccess {String}            data.action             Trạng thái của bản ghi submit (draft/sunmit)
@apiSuccess {String}            data.created_time       Thời gian submit được ghi nhận
@apiSuccess {Object}             [data.related_to]       Đối tượng liên quan tới 
@apiSuccess {Object}             [data.data_extra]       Dữ liệu ghi nhận thêm cho submit form, có thể từ hệ thống khác 
@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "form_id": "673ef959e2590c5e63ce93c1",
            "name": "form 1",
            "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "submit_data": {...},
            "action": "submit",
            "created_time": "2024-11-21T00:00:00Z",
            "data_extra": {
                "trans_code": "ma ho so", "doc_id": [""], "ecm_data": []
            },
            "related_to": {
                "deal_id": "682d580ea531b96fb6c1ba2e",
                "profile_id": "f182d312-210c-4f83-abbc-d6d32655a98c",
                "company_id": ""
            }
        }
    ],
    "message": "request thành công."
}

"""



"""
@api {POST} {domain}/wfb/internal/api/v1.0/forms/process-status    Cập nhật trạng thái xử lý của form
@apiGroup Internal
@apiVersion 1.0.0
@apiName FormSubmitProcessStatus

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse ObjectUsedForm
@apiParam  (Body:)     {String}    status               Trạng thái xử lý form
                                                        <li><code>success</code>: Thành công</li>
                                                        <li><code>landing_page_cancel</code>: Cancel trên landing page</li>
                                                        <li><code>landing_page_accept</code>: hoàn thành bước OTP tại landing page</li>
                                                        <li><code>cif_create_error</code>: Tạo cif thất bại</li>
                                                        <li><code>account_create_error</code>: Tạo TKTT lỗi</li>
                                                        <li><code>edigi_create_error</code>: Tạo tài khoản Edigi lỗi</li>
@apiParam  (Body:)     {String}    [reason]             Lý do hủy

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}

"""



"""
@api {get} {domain}/wfb/mobile/api/v1.0/forms/process-status   Lấy trạng thái quá trình xử lý quick sales 
@apiGroup MobileFormSubmission
@apiVersion 1.0.0
@apiName ProcessStatusForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Query:)     {String}    target_object          Đối tượng sử dụng form: profile, sale, company
@apiParam  (Query:)     {String}    object_id            ID Đối tượng sử dụng form
@apiParam  (Query:)     {String}    [form_id]            ID form nếu có, ưu tiên tìm theo key này, nếu không thì sử dụng form_code 
@apiParam  (Query:)     {String}    [form_code]           Mã lấy danh sách form cho các form tuỳ chỉnh, nhận các giá trị:
                                                         <li><code>quick_sales</code>: Form quick sale EIB</li>

@apiSuccess {String}            id                                Id form
@apiSuccess {String}            name                                   tên form        
@apiSuccess {String}            permissions                            quyền tạo submit form: 
                                                                           <li><code>allow</code>: có quyền </li>
                                                                            <li><code>deny</code>: không có quyền</li>
@apiSuccess {Object}            [process_status]                       Trạng thái xử lý trong form, fe xử lý tình huống response không có key này 
@apiSuccess {String}            [process_status.status]                Trạng thái
                                                                            <li><code>not_submit</code>: chưa có bản ghi submit nào</li>
                                                                            <li><code>cif_exists</code>: Cif đã tồn tại</li>
                                                                            <li><code>ekyc_success</code>: EKYC thành công</li>
                                                                            <li><code>ekyc_fail</code>: EKYC thất bại</li>
                                                                            <li><code>success</code>: Thành công</li>
                                                                            <li><code>landing_page_cancel</code>: Cancel trên landing page</li>
                                                                            <li><code>landing_page_accept</code>: hoàn thành bước OTP tại landing page</li>
                                                                            <li><code>cif_create_error</code>: Tạo cif thất bại</li>
                                                                            <li><code>account_create_error</code>: Tạo TKTT lỗi</li>
                                                                            <li><code>edigi_create_error</code>: Tạo tài khoản Edigi lỗi</li>
                                                                            <li>Tất cả các status khác <code>success</code> được coi là chưa hoàn thành</li>
@apiSuccess {String}            [process_status.reason]                Lý do lỗi

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "id": "673ef959e2590c5e63ce93c1",
        "name": "form 1",
        "process_status": {
            "status": "cif_exists",
        },
        "permissions": "allow"
    }
}

"""


"""
@api {get} {domain}/wfb/mobile/api/v1.0/forms/submission/permissions-add   kiểm tra quyền tạo form quick sales 
@apiGroup MobileFormSubmission
@apiVersion 1.0.0
@apiName CheckPermissionAddSubmit

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam  (Query:)     {String}    [form_id]            ID form nếu có, ưu tiên tìm theo key này, nếu không thì sử dụng form_code 
@apiParam  (Query:)     {String}    [form_code]           Mã lấy danh sách form cho các form tuỳ chỉnh, nhận các giá trị:
                                                         <li><code>quick_sales</code>: Form quick sale EIB</li>

@apiSuccess {String}            id                                Id form
@apiSuccess {String}            name                                   tên form        
@apiSuccess {String}            permissions                            quyền tạo submit form: 
                                                                           <li><code>allow</code>: có quyền </li>
                                                                            <li><code>deny</code>: không có quyền</li>

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "id": "673ef959e2590c5e63ce93c1",
        "name": "form 1",
       "permissions": "allow"
    }
}

"""



"""
@api {GET} {domain}/wfb/mobile/api/v1.0/forms/submission-info       Thông tin lần submit form gần nhất, kèm cấu hình, logic form
@apiGroup MobileFormSubmission
@apiVersion 1.0.0
@apiName DetailSubmissionInfo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam  (Query:)     {String}    target_object          Đối tượng sử dụng form: profile, sale, company
@apiParam  (Query:)     {String}    object_id            ID Đối tượng sử dụng form
@apiParam  (Query:)     {String}    form_id            ID form 


@apiSuccess {String}            data.form_id                                Id form
@apiSuccess {String}            data.name                                   tên form
@apiSuccess {Object}            data.config                                 Cấu hình form
@apiSuccess {String}            data.account_id                             Id account submit form
@apiSuccess {String}            data.object_type                            Đối tượng sử dụng form
@apiSuccess {String}            data.object_id                              Id đối tượng sử dụng form
@apiSuccess {Object}            data.submit_data                            Dữ liệu submit form
@apiSuccess {Object}            data.action                                 Action submit form
                                                                            <li><code>submit</code>: Form đã hoàn thành</li>
                                                                            <li><code>draft</code>: Form chưa hoàn thành</li>
@apiSuccess {String}            data.version                                Submit version, nếu chưa có lượt submit/save draft nào thì version=""
@apiSuccess {String}            data.version_config                         form config version, dùng để gửi lên backend kiểm tra form đang điền có là version mới nhất ko 
@apiUse ProcessStatusForm
@apiSuccess {Array}             [data.logic]                                Logic được cấu hình trong form
@apiSuccess {Array}             [data.condition_fill_form]                  luồng kiểm tra điều kiện trước khi điền form(áp dụng luồng pre-approved)
@apiSuccess {Object}             [data.flow_check_form]                     chứa thông tin kết quả kiểm tra "condition_fill_form"
@apiSuccess {Object}             [data.data_extra]                          Dữ liệu ghi nhận thêm cho submit form, có thể từ hệ thống khác 

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "form_id": "673ef959e2590c5e63ce93c1",
        "name": "form 1",
        "config": {},
        "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "object_type": "sale",
        "object_id": "c9d79c5e-a7e8-11ef-8cdb-276bd2b69d37",
        "submit_data": {},
        "action": "draft",
        "version": "",
        "version_config": "",
        "process_status": {
            "status": "ekyc_success"
        },
        "logic": [
            {
                "logic_type": "update_calculate_field",
                "conditions": [
                    {
                        "position": 1,
                        "operator_key": "or",
                        "filters": [
                            {
                                "value": [
                                    "dasdad",
                                    "ffff"
                                ],
                                "target": "value",
                                "field": "3dc8bc1d-a9d8-4e8b-bd35-f59d964e9603",
                                "operator_key": "in_value"
                            }
                        ]
                    }
                ],
                "actions": [
                    {
                        "action": "calculate",
                        "value": [
                            {
                                "type": "field",
                                "data": "1d3406f8-60b8-443b-9923-1371419f44e1",
                                "index": 1
                            },
                            {
                                "type": "operation",
                                "data": "+",
                                "index": 2
                            },
                            {
                                "type": "field",
                                "data": "14cecd75-c8ee-4558-9afc-8838b654f893",
                                "index": 3
                            }
                        ],
                        "summary": "b75ffd05-e1c9-4816-8518-6736fdc27704"
                    }
                ],
                "position": 1
            }
        ],
        "data_extra": {
            "trans_code": "ma ho so", "doc_id": [""], "ecm_data": []
        },
        "flow_check_form": {
            "node_id": "6", "node_code": "ACCEPT_FORM"  // trước khi chạy vào condition_fill_form thì kiểm tra có key này thì cho tiếp tục điền form 
        },
        "condition_fill_form": []
    },
    "message": "request thành công."
}

"""




"""
@api {POST} {domain}/wfb/mobile/api/v1.0/forms/upload-attachments       Upload file đính kèm trong form
@apiDescription Upload file đính kèm. Dịch vụ gửi lên request dạng <code>form-data</code>  
@apiGroup MobileFormSubmission
@apiVersion 1.0.0
@apiName FormUploadAttachments

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam       (Body:)     {File}      attachments                          Danh sách file cần upload
@apiParam       (Body:)     {String}    data                                 Thông tin bổ sung, dữ liệu dạng String object json được mô tả bên dưới

@apiParam       (data)      {String}    version                              Submit version, nếu chưa có lượt submit/save draft nào thì version=""
@apiParam       (data)      {String}    form_id                              ID Form hiện tại
@apiParam       (data)      {String}    version_config                       Form config version, dùng để gửi lên backend kiểm tra form đang điền có là version mới nhất hay không

@apiParam       (data)      {Array}     attachments                          Danh sách các file đính kèm đã upload trước đó, nếu không có gửi danh sách rỗng []
@apiParam       (data)      {String}    attachments.url                      Link file
@apiParam       (data)      {String}    attachments.local_path               Path file
@apiParam       (data)      {String}    attachments.filename                 Tên file
@apiParam       (data)      {String}    attachments.format                   Loại file
@apiParam       (data)      {String}    attachments.capacity                 Dung lượng file
@apiParam       (data)      {Number}    attachments.origin_capacity          Dung lượng file gốc (bytes)
@apiParam       (data)      {String}    attachments.source                   Nguồn upload file, mặc định <code>web_form</code>
@apiParam       (data)      {String}    [attachments.group_type]             Mã nhóm hồ sơ, required với field <code>attached_document (Tài liệu đính kèm)</code>
@apiParam       (data)      {String}    [attachments.group_name]             Tên nhóm hồ sơ, required với field <code>attached_document (Tài liệu đính kèm)</code>
@apiParam       (data)      {String}    [attachments.doc_type]               Mã loại hồ sơ, required với field <code>attached_document (Tài liệu đính kèm)</code>
@apiParam       (data)      {String}    [attachments.doc_name]               Tên loại hồ sơ, required với field <code>attached_document (Tài liệu đính kèm)</code>
@apiParam       (data)      {String}    [attachments.is_new]               Tên loại hồ sơ, required với field <code>attached_document (Tài liệu đính kèm)</code>

@apiParam       (data)      {String}    [group_type]                         Mã nhóm hồ sơ, required với field <code>attached_document (Tài liệu đính kèm)</code>
@apiParam       (data)      {String}    [group_name]                         Tên nhóm hồ sơ, required với field <code>attached_document (Tài liệu đính kèm)</code>
@apiParam       (data)      {String}    [doc_id]                               ID Mã loại hồ sơ, dùng để gom nhóm tài liệu khi doc_type bị xoá
@apiParam       (data)      {String}    [doc_type]                           Mã loại hồ sơ, required với field <code>attached_document (Tài liệu đính kèm)</code>
@apiParam       (data)      {String}    [doc_name]                           Tên loại hồ sơ, required với field <code>attached_document (Tài liệu đính kèm)</code>
@apiParam       (data)      {String}    object_type                          Đối tượng sử dụng form
                                                                             <li><code>sale</code>: Cơ hội bán</li>
                                                                             <li><code>profile</code>: Profile</li>
                                                                             <li><code>company</code>: Công ty</li>
@apiParam       (data)      {String}    object_id                            ID Đối tượng sử dụng form
@apiParam       (data)      {Array}    [urls_delete]                         danh sách link media muốn xóa  


@apiParamExample {json} data example
{
  "form_id": "67eb8fe5a137285848f26b14",
  "version": "",
  "attachments": [
    {
      "url": "https://t1.mobio.vn/static/mobio/upload/AddAccountBank.xlsx",
      "local_path": "/media/data/public_resources/static/mobio/upload/AddAccountBank.xlsx",
      "filename": "AddAccountBank.xlsx",
      "format": "application/zip",
      "capacity": "10 KB",
      "origin_capacity": 10240,
      "source": "web_form",
      "group_type": "legal_doc",
      "group_name": "Hồ sơ pháp lý",
      "doc_type": "personal_legal_doc",
      "doc_name": "Hồ sơ pháp lý cá nhân"
    }
  ],
  "version_config": "67eb93a0a137285848f26b1a",
  "field_id": "4c7580c0-2496-48b6-98c1-a85897d2d19a",
  "group_type": "legal_doc",
  "group_name": "Hồ sơ pháp lý",
  "doc_type": "personal_legal_doc",
  "doc_name": "Hồ sơ pháp lý cá nhân",
  "object_type": "sale",
  "object_id": "**************",
  "doc_id": "4c7580c0-2496-asas-09aj-a85897d2d19a",
  "urls_delete": [https://t1.mobio.vn/static/mobio/upload/ho_so.jpg]
}


@apiSuccessExample {json} Response

{
    "code": 200,
    "data": {
        "account_id": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "action": "draft",
        "created_time": "2025-04-03T04:09:51Z",
        "form_id": "67eb8fe5a137285848f26b14",
        "name": "test attachments",
        "object_id": "**************",
        "object_type": "sale",
        "submit_data": {
            "4c7580c0-2496-48b6-98c1-a85897d2d19a": {
                "value": [
                    {
                        "capacity": "10 KB",
                        "origin_capacity": 10240,
                        "doc_name":"Hồ sơ pháp lý cá nhân",
                        "doc_type": "personal_legal_doc",
                        "filename": "AddAccountBank.xlsx",
                        "format": "application/zip",
                        "group_name":"Hồ sơ pháp lý",
                        "group_type": "legal_doc",
                        "local_path": "/media/data/public_resources/static/mobio/upload/AddAccountBank.xlsx",
                        "source": "web_form",
                        "url": "https://t1.mobio.vn/static/mobio/upload/AddAccountBank.xlsx"
                    },
                    {
                        "capacity": "10.0 KB",
                        "origin_capacity": 10240,
                        "doc_name":"Hồ sơ pháp lý cá nhân",
                        "doc_type": "personal_legal_doc",
                        "expire_time": null,
                        "filename": "UpdateAccountBank.xlsx",
                        "format": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        "group_name":"Hồ sơ pháp lý",
                        "group_type": "legal_doc",
                        "local_path": "/home/<USER>/Mobio/logs/media/data/public_resources/static/57d559c1-39a1-4cee-b024-b953428b5ac8/upload/UpdateAccountBank.xlsx",
                        "source": "web_form",
                        "url": "https://t1.mobio.vn/static/57d559c1-39a1-4cee-b024-b953428b5ac8/upload/UpdateAccountBank.xlsx"
                    },
                ]
            }
        },
        "version": "67ee07a6d1f9c027395229e7"
    },
    "lang": "vi",
    "message": "request thành công."
}

"""
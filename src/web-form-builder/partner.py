"""
@api {POST} {public-domain}/partner/wfb/external/api/v1.0/form/status/by-type   C<PERSON><PERSON> nhật trạng thá<PERSON> hồ sơ 
@apiGroup Partner 
@apiVersion 1.0.0
@apiName FormUpdateStatus 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {String}    trans_code          M<PERSON> hồ sơ 
@apiParam  (Body:)     {String}    request_type        <PERSON><PERSON><PERSON> hồ sơ 
                                                        <li><code>PRE_APPROVED_CREDIT_CARD</code>: thẻ tín dụng </li>
@apiParam  (Body:)     {String}   status              Trạng thái hồ sơ 
                                                        <li>với loại hồ sơ: PRE_APPROVED_CREDIT_CARD</li>
                                                        <li><code>STA12</code>: Hủy</li>
                                                        <li><code>STA03</code>: Khởi tạo</li>
                                                        <li><code>STA04</code>: <PERSON><PERSON> sung</li>
                                                        <li><code>STA15</code>: <PERSON><PERSON><PERSON><PERSON> liệu</li>
                                                        <li><code>STA05</code>: Kiểm soát</li>
                                                        <li><code>STA11</code>: Hoàn thành</li>
                                                        <li><code>CARD_SUCCESS</code>: phát hành thẻ thành công </li>
                                                        <li><code>ACTIVE_CARD_SUCCESS</code>: kích hoạt thẻ thành công </li>
                                                        <li><code>CARD_SPENT</code>: thẻ đã phát sinh giao dịch </li>
                                                        
@apiParam  (Body:)     {Object}   [data]            dữ liệu bổ sung
@apiParam  (data:)     {String}   [dateOpen]        Ngày đẩy way4 thành công
@apiParam  (data:)     {String}   [losStatus]     Trạng thái RLOS
@apiParam  (data:)     {String}   [extraRs]         CardID (Sốmasking)
@apiParam  (data:)     {String}   [cardExpiry]     Ngày hết hiệu lực yymm
@apiParam  (data:)     {String}   [cardTransCode]     Mã DVT
@apiParam  (data:)     {String}   [detailCancelReason]     Chi tiết mã lỗi
@apiParam  (data:)     {String}   [addrInfo04]     Số tài khoản thanh toán thẻ
@apiParam  (data:)     {String}   [contractNumber]     Số Masking 
@apiParam  (data:)     {String}   [creApprovalCode]     Mã LOS
@apiParam  (data:)     {String}   [cancelReason]     Lý do hủy

@apiParamExample {json} Body example
{
    "trans_code": "1717.KT.25.007815",
    "request_type": "PRE_APPROVED_CREDIT_CARD",
    "status": "STA11",
    "data": {
        "dateOpen":"",
        "losStatus":"STA11",
        "extraRs":"",
        "cardTransStatus":"",
        "cardExpiry":"",
        "cardTransCode":"1717.DV.25.003815",
        "detailCancelReason":"",
        "addrInfo04":"",
        "contractNumber":"",
        "creApprovalCode":"1717.KT.25.007815",
        "cancelReason":""
    }
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}


"""

"""
@api {POST} {public-domain}/partner/wfb/external/api/v1.0/master-data  Cập nhật dữ liệu master data    
@apiGroup Partner 
@apiVersion 1.0.0
@apiName FormUpdateMasterData 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {Array}   data                   danh sách nhóm dữ liệu master data 
@apiParam  (Body:)     {String}   data.group_code       mã nhóm 
@apiParam  (Body:)     {Array}   data.data             danh sách key theo nhóm 



@apiParamExample {json} Body example
{
    "data": [
        {
            "group_code": "LOAIKHACHHANG",
            "data": [
                {
                    "key": "CN",
                    "name": "Cá nhân",
                },
                {
                    "key": "DN",
                    "name": "Pháp nhân",
                },
            ]
        }
    ]
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}


"""




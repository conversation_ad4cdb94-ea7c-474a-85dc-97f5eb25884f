
"""
@apiDefine response_create_update_category
@apiVersion 1.0.0

@apiSuccess {String}            message                 <PERSON><PERSON>ả phản hồi
@apiSuccess {Integer}           code                    M<PERSON> phản hồi
@apiSuccess {Object}            data                    Thông tin thư mục đ<PERSON>
@apiSuccess {String}            data.name               T<PERSON><PERSON> thư mục
@apiSuccess {String}            data.id                 ID thư mục
@apiSuccess {String}            data.created_time       Thời gian t<PERSON>
@apiSuccess {String}            data.updated_time       Thời gian cập nhật

"""

"""
@api {POST} {domain}/wfb/api/v1.0/categories    T<PERSON><PERSON> thư mục mới
@apiGroup Categories
@apiVersion 1.0.0
@apiName CreateCategory

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParamExample {json} Body example
{
    "name": "Th<PERSON> mục 1",
}
@apiUse response_create_update_category
@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "name": "Th<PERSON> mục 1",
        "id": "67243ff9d254aab2abf83de0",
        "created_time": "2024-11-01T02:00:00Z",
        "updated_time": "2024-11-01T02:00:00Z",
    }
    "message": "request thành công."
}
"""


# Sửa thư mục
"""
@api {PATCH} {domain}/wfb/api/v1.0/categories/<category_id>    Cập nhật thông tin thư mục
@apiGroup Categories
@apiVersion 1.0.0
@apiName UpdateCategory

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam  (Body:)     {String}    [name]                 Tên thư mục

@apiParamExample {json} Body example
{
    "name": "Thư mục 1",
}

@apiUse response_create_update_category
@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "name": "Thư mục 1",
        "id": "67243ff9d254aab2abf83de0",
        "created_time": "2024-11-01T02:00:00Z",
        "updated_time": "2024-11-01T02:00:00Z",
    }
    "message": "request thành công."
}
"""


# Xoá thư mục
"""
@api {POST} {domain}/wfb/api/v1.0/categories/delete    Xoá thư mục
@apiGroup Categories
@apiVersion 1.0.0
@apiName DeleteCategory

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {String}    category_id         ID thư mục cần xoá
@apiParam  (Body:)     {String}    target_category     ID thư mục sẽ nhận lại form từ thư mục cần xoá

@apiSuccess {String}            message                 Mô tả phản hồi
@apiSuccess {Integer}           code                    Mã phản hồi

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}

"""


"""
@api {GET} {domain}/wfb/api/v1.0/categories    Danh sách thư mục
@apiGroup Categories
@apiVersion 1.0.0
@apiName ListCategory

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header



@apiSuccess {String}            message                 Mô tả phản hồi
@apiSuccess {Integer}           code                    Mã phản hồi
@apiSuccess {Object}            data                    Thông tin thư mục được tạo
@apiSuccess {String}            data.name               Tên thư mục
@apiSuccess {String}            data.id                 ID thư mục
@apiSuccess {String}            data.created_time       Thời gian tạo
@apiSuccess {String}            data.updated_time       Thời gian cập nhật

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "name": "Thư mục 1",
            "id": "67243ff9d254aab2abf83de0",
            "created_time": "2024-11-01T02:00:00Z",
            "updated_time": "2024-11-01T02:00:00Z",
        }
    ]
    "message": "request thành công."
}
"""


"""
@api {GET} {domain}/wfb/api/v1.0/categories/<category_id>       Chi tiết thư mục
@apiGroup Categories
@apiVersion 1.0.0
@apiName CategoryDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse response_create_update_category

@apiSuccess {String}            data.created_by         Người tạo
@apiSuccess {String}            data.updated_by         Người cập nhật gần nhất

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "name": "Thư mục 1",
        "id": "67243ff9d254aab2abf83de0",
        "created_time": "2024-11-01T02:00:00Z",
        "updated_time": "2024-11-01T02:00:00Z",
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"

    }
    "message": "request thành công."
}

"""



"""
@api {POST} {domain}/wfb/api/v1.0/categories/count/form       Đếm số lượng form trong thư mục theo filter form
@apiGroup Categories
@apiVersion 1.0.0
@apiName CountCategoryByForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse status_json_body
@apiParam  (Body:)     {String}    [form_name]                 Tên thư mục cần tìm

@apiSuccess {String}            message                 Mô tả phản hồi
@apiSuccess {Integer}           code                    Mã phản hồi
@apiSuccess {Array}             data                    Thông tin thư mục được tạo

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "name": "Thư mục 1",
            "id": "67243ff9d254aab2abf83de0",
            "created_time": "2024-11-01T02:00:00Z",
            "updated_time": "2024-11-01T02:00:00Z",
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            "total": "10"
        }
    ],
    "message": "request thành công."
}
"""
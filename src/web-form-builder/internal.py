"""
@api {POST} {domain}/wfb/internal/api/v1.0/forms/check-submit   kiểm tra form đã submit cho đối tượng chưa  
@apiGroup Internal
@apiVersion 1.0.0
@apiName FormCheckSubmit

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)     {String}    object_type          Đ<PERSON>i tượng sử dụng form
                                                        <li><code>sale</code>: <PERSON><PERSON> hội bán</li>
                                                        <li><code>profile</code>: Profile</li>
                                                        <li><code>company</code>: Công ty</li>
@apiParam  (Body:)     {Array}    object_ids            danh sách ID Đối tượng sử dụng form
@apiParam  (Body:)     {Array}    form_ids              danh sách Id form
@apiParam  (Body:)     {String}   [status]              Trạng thái của form khi đư<PERSON><PERSON> sử dụng
                                                        <li><code>darft</code>: <PERSON><PERSON><PERSON><PERSON> th<PERSON></li>
                                                        <li><code>complete</code>: <PERSON><PERSON><PERSON> thành</li>
                                                        <li><code>all</code>: Tất cả</li>

@apiParamExample {json} Body example
{
    "form_ids": ["673ef959e2590c5e63ce93c1"],
    "object_type": "sale",
    "object_ids": ["c9d79c5e-a7e8-11ef-8cdb-276bd2b69d37"],
    
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "form_id": "673ef959e2590c5e63ce93c1",
            "object_id": "c9d79c5e-a7e8-11ef-8cdb-276bd2b69d37",
        }
    ]
    "lang": "vi",
    "message": "request thành công."
}


"""
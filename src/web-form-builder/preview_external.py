
"""
@api {GET} {domain}/wfb/mobile/api/v1.0/forms/link-preview     Lấy thông tin link preview 
@apiGroup MobilePreview
@apiVersion 1.0.0
@apiName GetLinkPreview

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam  (Query:)     {String}    object_type          Đ<PERSON>i tượng sử dụng form
@apiParam  (Query:)     {String}    object_id            ID Đối tượng sử dụng form
@apiParam  (Query:)     {String}    form_id            ID Đ<PERSON>i tượng sử dụng form

@apiSuccess {String}            link_preview          link landing page preview thông tin submit
@apiSuccess {String}            expire_time         thời điểm hết hạn của link preview, nếu không có key thì không có hạn   

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "link_preview": "https://....",
        "expire_time": "2024-11-21T00:00:00Z"
    },
    "message": "request thành công."
}

"""

"""
@api {GET} {domain}/wfb/mobile/api/v1.0/forms/link-preview/reload     Khởi tạo lại link preview 
@apiGroup MobilePreview
@apiVersion 1.0.0
@apiName ReloadLinkPreview

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam  (Query:)     {String}    object_type          Đối tượng sử dụng form
@apiParam  (Query:)     {String}    object_id            ID Đối tượng sử dụng form
@apiParam  (Query:)     {String}    form_id            ID Đối tượng sử dụng form

@apiSuccess {String}            link_preview          link landing page preview thông tin submit
@apiSuccess {String}            expire_time         thời điểm hết hạn của link preview, nếu không có key thì không có hạn   

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "link_preview": "https://....",
        "expire_time": "2024-11-21T00:00:00Z"
    },
    "message": "request thành công."
}

"""



"""
@api {get} {domain}/wfb/internal/api/v1.0/forms/decrypt-code    Giải mã code preview của form
@apiGroup Internal
@apiVersion 1.0.0
@apiName FormDecryptCode

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  (Query:)     {String}    code         mã code từ link preview

@apiSuccess {String}            object_id          id đối tượng form: profile, sale, company 
@apiSuccess {String}            merchant_id          id merchant 
@apiSuccess {String}            form_id          id form 
@apiSuccess {String}            object_type       đối tượng sử dụng form: profile, sale, company 
@apiSuccess {String}            staff_id          id nhân viên nhập liệu form 
@apiSuccess {String}            expire_time         thời điểm hết hạn của code, nếu giá trị rỗng thì không có hạn   

@apiSuccess {String}            [error_code]      có lỗi thì trả về mã lỗi sau: 
                                                <li><code>code_invalid</code>: code không hợp lệ</li>
                                                <li><code>code_expire</code>: code hết hạn</li>

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "object_id": "aa6c36ef-2b68-453c-a516-f88b39d9a9ce",
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "form_id": "123123",
        "object_type": "profile",
        "expire_time": "2025-02-22 10:00:00.000",
        "staff_id": "",
    },
    "lang": "vi",
    "message": "request thành công."
}

"""


"""
@api {GET} {domain}/guest/wfb/external/api/v1.0/forms/submission/external       Thông tin submit form 
@apiGroup PreviewExternal
@apiVersion 1.0.0
@apiName DetailSubmission

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam  (Query:)     {String}    code          mã code từ link preview 
@apiParam  (Query:)     {String}    [filter]      lấy thêm dữ liệu: config - lấy dữ liệu config builder  


@apiSuccess {Object}            data.submit_data                            Dữ liệu submit form
@apiSuccess {Object}            data.config                            Dữ liệu config builder, có nếu filter=config 
@apiSuccess {String}            [error_code]      có lỗi thì trả về mã lỗi sau: 
                                                <li><code>code_invalid</code>: code không hợp lệ</li>
                                                <li><code>code_expire</code>: code hết hạn</li>
@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "submit_data": {
            ...
        },
        "config": {
            ...
        },
    },
    "message": "request thành công."
}

"""



"""
@api {GET} {domain}/guest/wfb/external/api/v1.0/forms/config/external       Thông tin cấu hình builder form 
@apiGroup PreviewExternal
@apiVersion 1.0.0
@apiName GetConfigBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam  (Query:)     {String}    code          mã code từ link preview 


@apiSuccess {Object}            data.config                            Dữ liệu submit form
@apiSuccess {String}            [error_code]      có lỗi thì trả về mã lỗi sau: 
                                                <li><code>code_invalid</code>: code không hợp lệ</li>
                                                <li><code>code_expire</code>: code hết hạn</li>

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        "config": {
            ...
        },
        
    },
    "message": "request thành công."
}

"""

"""
@api {post} {domain}/wfb/internal/api/v1.0/forms/submit/transform-module    Lấy dữ liệu submit theo module
@apiGroup Internal
@apiVersion 1.0.0
@apiName TransformSubmitData

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam      (Body:)     {String}    object_type          Đối tượng sử dụng form
@apiParam      (Body:)     {String}    object_id            ID Đối tượng sử dụng form
@apiParam      (Body:)     {String}    form_id            ID Đối tượng sử dụng form
@apiParam      (Body:)     {Object}     [filter_module]     bộ lọc theo module, nếu không có thì lấy tất cả
 @apiParam      (filter_module:)     {Array}     [profile] danh sách field key module profile muốn trả về, mảng rỗng là lấy tất cả   
 @apiParam      (filter_module:)     {Array}     [sale] danh sách field key module sale muốn trả về, mảng rỗng là lấy tất cả 
 @apiParam      (filter_module:)     {Array}     [company] danh sách field key module company muốn trả về, mảng rỗng là lấy tất cả 
 @apiParam      (filter_module:)     {Array}     [web_form] danh sách field key form muốn trả về, mảng rỗng là lấy tất cả 

@apiParamExample {json} Info example
{
    "form_id": "67c7c5ff0b4decaeb478e5cf",
    "object_id": "bc5f37dc-3a37-4649-8691-6ec0ab7daa23",
    "object_type": "profile",
    "filter_module": {
        "profile": ["name", "primary_phone", "primary_email", "profile_identify"]
    }
 }


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {
        'profile': {
            'name': 'Nguyen Ba Hung', 
            'primary_email': '<EMAIL>', 
            'primary_phone': '0358999999', 
            'profile_identify': [{'identify_type': 'citizen_identity', 'identify_value': 'CCCD222'}]
        }
    },
    "message": "request thành công."
}

"""


"""
@api {POST} {domain}/guest/wfb/external/api/v1.0/forms/confirm-page/otp   Lấy OTP để confirm landing page
@apiDescription         Lấy OTP để confirm landing page. Lấy OTP chỉ áp dụng trong thời gian link truy cập form còn tồn tại. Chỉ bấm được gửi mã OTP sau 1 phút.
@apiName LandingPageOtp
@apiGroup PreviewExternal
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apiParam (BODY:)        {String}    form_token     Token access form

@apiParamExample {json} Body example
{
    "form_token": "c29tZV9zYW1wbGVfdmFsdWU="
}

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
}
"""


"""
@api {POST} {domain}/guest/wfb/external/api/v1.0/forms/confirm-page/otp/verify       Kiểm tra OTP confirm landing page có hợp lệ hay không
@apiDescription         Kiểm tra OTP confirm landing page có hợp lệ hay không?
@apiName LandingPageOtpVerify
@apiGroup PreviewExternal 
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apiParam (BODY:)        {String}    form_token     Token access form
@apiParam (BODY:)        {String}    otp            OTP

@apiParamExample {json} Body example
{
    "form_token": "c29tZV9zYW1wbGVfdmFsdWU=",
    "otp": "123456"
}

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
}
"""

"""
@api {POST} {domain}/guest/wfb/external/api/v1.0/forms/confirm-page/cancel       Cancel form landing page
@apiDescription         Cancel form landing page
@apiName LandingPageSubmit
@apiGroup PreviewExternal
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apiParam (BODY:)        {String}    form_token     Token access form
@apiParam (BODY:)        {String}    reason      Lý do hủy bỏ


@apiParamExample {json} Body example
{
    "form_token": "c29tZV9zYW1wbGVfdmFsdWU=",
    "reason": "Lý do hủy bỏ",
    
}


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
}
"""


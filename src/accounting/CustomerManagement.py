*!/usr/bin/python
* -*- coding: utf8 -*-

******************************** Feedback Lead ********************************
* version: 1.0.4                                                              *
* version: 1.0.3                                                              *
* version: 1.0.2                                                              *
* version: 1.0.1                                                              *
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} /profiling/v1.0/feedback/leads     Feedback lead
@apiDescription Dịch vụ để hệ thống CRM update lại thông tin của lead sau khi sale.
@apiGroup Customer
@apiVersion 1.0.4
@apiName FeedbackLead

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam     (Body:)     {Object[]}    leads       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>Lead</code>

@apiParam     (Lead)      {String}      id          Định danh của khách hàng trên hệ thống DMP-MOBIO.
@apiParam     (Lead)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (Lead)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam  (UserInfo)      {String}        [display_name]                Tên khách hàng
@apiParam     (UserInfo)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (UserInfo)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (UserInfo)      {String}        [email]                       Email của khách hàng
@apiParam  (UserInfo)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (UserInfo)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (UserInfo)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (UserInfo)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (UserInfo)       {String}        [address]                      Địa chỉ
@apiParam  (UserInfo)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (UserInfo)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (UserInfo)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (UserInfo)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (UserInfo)       {Number}        [district_code]                Mã quận huyện
@apiParam  (UserInfo)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (UserInfo)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (UserInfo)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (UserInfo)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam  (UserInfo)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (UserInfo)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (UserInfo)      {Number}        [religiousness]                Tôn giáo
@apiParam  (UserInfo)      {Number}        [nation]                       Dân tộc.
@apiParam  (UserInfo)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (UserInfo)      {String}        [location]                     Vị trí.
@apiParam  (UserInfo)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam   (UserInfo)         {Array}     answers         Danh sách câu trả lời của khách hàng.
@apiParam   (answers)       {Number}    question_id     ID Câu hỏi.
<li><code>1: Có hợp đồng bảo hiểm trên 1 năm không?</code></li>
<li><code>2: Có sở hữu ô tô không?</code></li>
<li><code>3: Có sở hữu Bất động sản không?</code></li>
@apiParam   (answers)       {Number}    int_result      Câu trả lời dạng number.
@apiParam   (answers)       {String}    string_result   Câu trả lời dạng text.
@apiParam   (UserInfo)    {Object}    [third_party_info]    Thông tin bổ sung của third party. Thông tin này sẽ được gửi lại cho third party sau khi phân tích user.

@apiParam     (Feedback)    {Array}  [status]   Cập nhật tình trạng lead.
@apiParam     (Feedback)    {Number}  [status..value]   Trạng thái lead.<br/>
<li><code>1: Chưa liên hệ</code></li>
<li><code>2: KH không nghe máy</code></li>
<li><code>3: KH hẹn gọi lại sau</code></li>
<li><code>4: KH tắt máy hoặc sai số</code></li>
<li><code>5: KH từ chối</code></li>
<li><code>6: KH không đủ điều kiện</code></li>
<li><code>7: KH đã sử dụng sản phẩm</code></li>
<li><code>8: KH đồng ý</code></li>
<li><code>9: Khác</code></li>
<li><code>10: KH nộp hồ sơ</code></li>
<li><code>11: KH mở thẻ thành công</code></li>
<li><code>12: KH đủ điều kiện nhưng ở xa không hỗ trợ</code></li>
@apiParam     (Feedback)    {Number}  [status..updated_time]   Thời điểm cập nhật trạng thái. Đơn vị tính timestamp. Ví dụ: <code>"updated_time": 1528881775138</code><br/>

@apiParamExample    {json}      Body example:
{
    "leads":[
        {
            "id":"9035b72d-4b8e-471d-935d-e73f73063246",
            "user_info": {
              "display_name": "AnNV",
              "phone_number": "+***********",
              "email": "<EMAIL>",
              "status": 1,
              "avatar": "",
              "birthday": "29/11/1989",
              "gender": 2,
              "address": "Cầu Giấy, Hà Nội",
              "phone_number2": "",
              "national_phone_code": "84",
              "province_code": 1,
              "district_code": 1,
              "ward_code": 1,
              "marital_status": 1,
              "monthly_income": 0,
              "income_unit": 1,
              "income_type": 1,
              "religiousness": 1,
              "nation": 1,
              "job": 39,
              "location": "",
              "people_id": "1234567890",
              "frequently_demands": [1, 2, 3],
              "answers": [
                {
                    "question_id": 1,
                    "int_result": 1,
                    "string_result": "Có"
                },
                {
                    "question_id": 2,
                    "int_result": 0,
                    "string_result": "Không"
                }
              ],
              "third_party_info":{
                "id": "f79a7c11-31ae-4637-9a09-a0fce3184dd4"
              }
            },
            "feedback":{
                "status":[
                  {
                    "value": 1,
                    "updated_time": 1528861712428
                  },
                  {
                    "value": 8,
                    "updated_time": 1528861712428
                  }
                ]
            }

        }
    ]
}

@apiSuccess     {String[]}      success     Danh sách id của khách hàng đã update thành công.
@apiSuccess     {Object[]}      error       Danh sách khách hàng update không thành công.
@apiSuccess     {String}      error..id   Id của khách hàng trên hệ thống DMP-MOBIO.
@apiSuccess     {String}      error..message   Nguyên nhân lỗi không update được thông tin khách hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "success":[
        "0755ee00-8610-424f-ba4b-b264f64c3b46",
        "ccca9bfb-0f21-47e2-8f90-8d590e094b77"
    ],
    "error":[
        {
            "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",
            "message": "..."
        },
        {
            "id": "a33bf63e-5f0e-4023-8bb4-f6793666ddcf",
            "message": "..."
        }
    ]
}
"""
****************
"""
@api {post} /profiling/v1.0/feedback/leads     Feedback lead
@apiDescription Dịch vụ để hệ thống CRM update lại thông tin của lead sau khi sale.
@apiGroup Customer
@apiVersion 1.0.3
@apiName FeedbackLead

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam     (Body:)     {Object[]}    leads       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>Lead</code>

@apiParam     (Lead)      {String}      id          Định danh của khách hàng trên hệ thống DMP-MOBIO.
@apiParam     (Lead)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (Lead)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam  (UserInfo)      {String}        [display_name]                Tên khách hàng
@apiParam     (UserInfo)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (UserInfo)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (UserInfo)      {String}        [email]                       Email của khách hàng
@apiParam  (UserInfo)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (UserInfo)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (UserInfo)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (UserInfo)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (UserInfo)       {String}        [address]                      Địa chỉ
@apiParam  (UserInfo)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (UserInfo)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (UserInfo)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (UserInfo)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (UserInfo)       {Number}        [district_code]                Mã quận huyện
@apiParam  (UserInfo)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (UserInfo)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (UserInfo)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (UserInfo)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam  (UserInfo)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (UserInfo)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (UserInfo)      {Number}        [religiousness]                Tôn giáo
@apiParam  (UserInfo)      {Number}        [nation]                       Dân tộc.
@apiParam  (UserInfo)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (UserInfo)      {String}        [location]                     Vị trí.
@apiParam  (UserInfo)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam   (UserInfo)         {Array}     answers         Danh sách câu trả lời của khách hàng.
@apiParam   (answers)       {Number}    question_id     ID Câu hỏi.
<li><code>1: Có hợp đồng bảo hiểm trên 1 năm không?</code></li>
<li><code>2: Có sở hữu ô tô không?</code></li>
<li><code>3: Có sở hữu Bất động sản không?</code></li>
@apiParam   (answers)       {Number}    int_result      Câu trả lời dạng number.
@apiParam   (answers)       {String}    string_result   Câu trả lời dạng text.

@apiParam     (Feedback)    {Array}  [status]   Cập nhật tình trạng lead.
@apiParam     (Feedback)    {Number}  [status..value]   Trạng thái lead.<br/>
<li><code>1: Chưa liên hệ</code></li>
<li><code>2: KH không nghe máy</code></li>
<li><code>3: KH hẹn gọi lại sau</code></li>
<li><code>4: KH tắt máy hoặc sai số</code></li>
<li><code>5: KH từ chối</code></li>
<li><code>6: KH không đủ điều kiện</code></li>
<li><code>7: KH đã sử dụng sản phẩm</code></li>
<li><code>8: KH đồng ý</code></li>
<li><code>9: Khác</code></li>
<li><code>10: KH nộp hồ sơ</code></li>
<li><code>11: KH mở thẻ thành công</code></li>
<li><code>12: KH đủ điều kiện nhưng ở xa không hỗ trợ</code></li>
@apiParam     (Feedback)    {Number}  [status..updated_time]   Thời điểm cập nhật trạng thái. Đơn vị tính timestamp. Ví dụ: <code>"updated_time": 1528881775138</code><br/>

@apiParamExample    {json}      Body example:
{
    "leads":[
        {
            "id":"9035b72d-4b8e-471d-935d-e73f73063246",
            "user_info": {
              "display_name": "AnNV",
              "phone_number": "+***********",
              "email": "<EMAIL>",
              "status": 1,
              "avatar": "",
              "birthday": "29/11/1989",
              "gender": 2,
              "address": "Cầu Giấy, Hà Nội",
              "phone_number2": "",
              "national_phone_code": "84",
              "province_code": 1,
              "district_code": 1,
              "ward_code": 1,
              "marital_status": 1,
              "monthly_income": 0,
              "income_unit": 1,
              "income_type": 1,
              "religiousness": 1,
              "nation": 1,
              "job": 39,
              "location": "",
              "people_id": "1234567890",
              "frequently_demands": [1, 2, 3],
              "answers": [
                {
                    "question_id": 1,
                    "int_result": 1,
                    "string_result": "Có"
                },
                {
                    "question_id": 2,
                    "int_result": 0,
                    "string_result": "Không"
                }
              ]
            },
            "feedback":{
                "status":[
                  {
                    "value": 1,
                    "updated_time": 1528861712428
                  },
                  {
                    "value": 8,
                    "updated_time": 1528861712428
                  }
                ]
            }

        }
    ]
}

@apiSuccess     {String[]}      success     Danh sách id của khách hàng đã update thành công.
@apiSuccess     {Object[]}      error       Danh sách khách hàng update không thành công.
@apiSuccess     {String}      error..id   Id của khách hàng trên hệ thống DMP-MOBIO.
@apiSuccess     {String}      error..message   Nguyên nhân lỗi không update được thông tin khách hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "success":[
        "0755ee00-8610-424f-ba4b-b264f64c3b46",
        "ccca9bfb-0f21-47e2-8f90-8d590e094b77"
    ],
    "error":[
        {
            "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",
            "message": "..."
        },
        {
            "id": "a33bf63e-5f0e-4023-8bb4-f6793666ddcf",
            "message": "..."
        }
    ]
}
"""
****************
"""
@api {post} /profiling/v1.0/feedback/leads     Feedback lead
@apiDescription Dịch vụ để hệ thống CRM update lại thông tin của lead sau khi sale.
@apiGroup Customer
@apiVersion 1.0.2
@apiName FeedbackLead

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam     (Body:)     {Object[]}    leads       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>Lead</code>

@apiParam     (Lead)      {String}      id          Định danh của khách hàng trên hệ thống DMP-MOBIO.
@apiParam     (Lead)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (Lead)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam  (UserInfo)      {String}        [display_name]                Tên khách hàng
@apiParam     (UserInfo)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (UserInfo)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (UserInfo)      {String}        [email]                       Email của khách hàng
@apiParam  (UserInfo)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (UserInfo)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (UserInfo)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (UserInfo)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (UserInfo)       {String}        [address]                      Địa chỉ
@apiParam  (UserInfo)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (UserInfo)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (UserInfo)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (UserInfo)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (UserInfo)       {Number}        [district_code]                Mã quận huyện
@apiParam  (UserInfo)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (UserInfo)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (UserInfo)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (UserInfo)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam  (UserInfo)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (UserInfo)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (UserInfo)      {Number}        [religiousness]                Tôn giáo
@apiParam  (UserInfo)      {Number}        [nation]                       Dân tộc.
@apiParam  (UserInfo)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (UserInfo)      {String}        [location]                     Vị trí.
@apiParam  (UserInfo)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam   (UserInfo)         {Array}     answers         Danh sách câu trả lời của khách hàng.
@apiParam   (answers)       {Number}    question_id     ID Câu hỏi.
<li><code>1: Có hợp đồng bảo hiểm trên 1 năm không?</code></li>
<li><code>2: Có sở hữu ô tô không?</code></li>
<li><code>3: Có sở hữu Bất động sản không?</code></li>
@apiParam   (answers)       {Number}    int_result      Câu trả lời dạng number.
@apiParam   (answers)       {String}    string_result   Câu trả lời dạng text.

@apiParam     (Feedback)    {Number}  [trusted]   Kết quả đánh giá trusted profile.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Profile không trusted;</li>
<li><code>1</code>: Profile có trusted.</li>
@apiParam     (Feedback)    {Number}  [saled]   Kết quả sale.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Sale thất bại;</li>
<li><code>1</code>: Sale thành công.</li>
@apiParam     (Feedback)    {Number}  [demand]   Kết quả đánh giá nhu cầu của khách hàng.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: KH không có nhu cầu;</li>
<li><code>1</code>: KH có nhu cầu.</li>

@apiParamExample    {json}      Body example:
{
    "leads":[
        {
            "id":"9035b72d-4b8e-471d-935d-e73f73063246",
            "user_info": {
              "display_name": "AnNV",
              "phone_number": "+***********",
              "email": "<EMAIL>",
              "status": 1,
              "avatar": "",
              "birthday": "29/11/1989",
              "gender": 2,
              "address": "Cầu Giấy, Hà Nội",
              "phone_number2": "",
              "national_phone_code": "84",
              "province_code": 1,
              "district_code": 1,
              "ward_code": 1,
              "marital_status": 1,
              "monthly_income": 0,
              "income_unit": 1,
              "income_type": 1,
              "religiousness": 1,
              "nation": 1,
              "job": 39,
              "location": "",
              "people_id": "1234567890",
              "frequently_demands": [1, 2, 3],
              "answers": [
                {
                    "question_id": 1,
                    "int_result": 1,
                    "string_result": "Có"
                },
                {
                    "question_id": 2,
                    "int_result": 0,
                    "string_result": "Không"
                }
              ]
            },
            "feedback":{
                "trusted": 1,
                "saled": 1,
                "demand": 1
            }

        }
    ]
}

@apiSuccess     {String[]}      success     Danh sách id của khách hàng đã update thành công.
@apiSuccess     {Object[]}      error       Danh sách khách hàng update không thành công.
@apiSuccess     {String}      error..id   Id của khách hàng trên hệ thống DMP-MOBIO.
@apiSuccess     {String}      error..message   Nguyên nhân lỗi không update được thông tin khách hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "success":[
        "0755ee00-8610-424f-ba4b-b264f64c3b46",
        "ccca9bfb-0f21-47e2-8f90-8d590e094b77"
    ],
    "error":[
        {
            "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",
            "message": "..."
        },
        {
            "id": "a33bf63e-5f0e-4023-8bb4-f6793666ddcf",
            "message": "..."
        }
    ]
}
"""
****************
"""
@api {post} /profiling/v1.0/feedback/leads     Feedback lead
@apiDescription Dịch vụ để hệ thống CRM update lại thông tin của lead sau khi sale.
@apiGroup Customer
@apiVersion 1.0.1
@apiName FeedbackLead

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam     (Body:)     {Object[]}    leads       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>Lead</code>

@apiParam     (Lead)      {String}      id          Định danh của khách hàng trên hệ thống DMP-MOBIO.
@apiParam     (Lead)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (Lead)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam  (UserInfo)      {String}        [display_name]                Tên khách hàng
@apiParam     (UserInfo)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (UserInfo)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (UserInfo)      {String}        [email]                       Email của khách hàng
@apiParam  (UserInfo)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (UserInfo)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (UserInfo)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (UserInfo)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (UserInfo)       {String}        [address]                      Địa chỉ
@apiParam  (UserInfo)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (UserInfo)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (UserInfo)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (UserInfo)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (UserInfo)       {Number}        [district_code]                Mã quận huyện
@apiParam  (UserInfo)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (UserInfo)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam  (UserInfo)      {Number}        [monthly_income]         Thu nhập hàng tháng
@apiParam  (UserInfo)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (UserInfo)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (UserInfo)      {Number}        [religiousness]                Tôn giáo
@apiParam  (UserInfo)      {Number}        [nation]                       Dân tộc.
@apiParam  (UserInfo)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (UserInfo)      {String}        [location]                     Vị trí.
@apiParam  (UserInfo)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam     (Feedback)    {Number}  [trusted]   Kết quả đánh giá trusted profile.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Profile không trusted;</li>
<li><code>1</code>: Profile có trusted.</li>
@apiParam     (Feedback)    {Number}  [saled]   Kết quả sale.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Sale thất bại;</li>
<li><code>1</code>: Sale thành công.</li>
@apiParam     (Feedback)    {Number}  [demand]   Kết quả đánh giá nhu cầu của khách hàng.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: KH không có nhu cầu;</li>
<li><code>1</code>: KH có nhu cầu.</li>

@apiParamExample    {json}      Body example:
{
    "leads":[
        {
            "id":"9035b72d-4b8e-471d-935d-e73f73063246",
            "user_info": {
                "display_name": "AnNV",
                "phone_number": "+***********",
                "email": "<EMAIL>",
                "status": 1,
                "avatar": "",
                "birthday": "29/11/1989",
                "gender": 2,
                "address": "Cầu Giấy, Hà Nội",
                "phone_number2": "",
                "national_phone_code": "84",
                "province_code": 1,
                "district_code": 1,
                "ward_code": 1,
                "marital_status": 1,
                "monthly_income": 0,
                "income_unit": 1,
                "income_type": 1,
                "religiousness": 1,
                "nation": 1,
                "job": 39,
                "location": "",
                "people_id": "1234567890",
                "frequently_demands": [1, 2, 3]
            },
            "feedback":{
                "trusted": 1,
                "saled": 1,
                "demand": 1
            }

        }
    ]
}

@apiSuccess     {String[]}      success     Danh sách id của khách hàng đã update thành công.
@apiSuccess     {Object[]}      error       Danh sách khách hàng update không thành công.
@apiSuccess     {String}      error..id   Id của khách hàng trên hệ thống DMP-MOBIO.
@apiSuccess     {String}      error..message   Nguyên nhân lỗi không update được thông tin khách hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "success":[
        "0755ee00-8610-424f-ba4b-b264f64c3b46",
        "ccca9bfb-0f21-47e2-8f90-8d590e094b77"
    ],
    "error":[
        {
            "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",
            "message": "..."
        },
        {
            "id": "a33bf63e-5f0e-4023-8bb4-f6793666ddcf",
            "message": "..."
        }
    ]
}
"""
****************
"""
@api {post} /profiling/v1.0/feedback/leads     Feedback lead
@apiDescription Dịch vụ để hệ thống CRM update lại thông tin của lead sau khi sale.
@apiGroup Customer
@apiVersion 1.0.0
@apiName FeedbackLead

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam     (Body:)     {Object[]}    leads       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>Lead</code>

@apiParam     (Lead)      {String}      id          Định danh của khách hàng trên hệ thống DMP-MOBIO.
@apiParam     (Lead)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (Lead)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam  (UserInfo)      {String}        [display_name]                Tên khách hàng
@apiParam     (UserInfo)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (UserInfo)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (UserInfo)      {String}        [email]                       Email của khách hàng
@apiParam  (UserInfo)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (UserInfo)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (UserInfo)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (UserInfo)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (UserInfo)       {String}        [address]                      Địa chỉ
@apiParam  (UserInfo)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (UserInfo)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (UserInfo)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (UserInfo)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (UserInfo)       {Number}        [district_code]                Mã quận huyện
@apiParam  (UserInfo)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (UserInfo)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam  (UserInfo)      {Number}        [monthly_income]         Thu nhập hàng tháng
@apiParam  (UserInfo)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (UserInfo)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (UserInfo)      {Number}        [religiousness]                Tôn giáo
@apiParam  (UserInfo)      {Number}        [nation]                       Dân tộc.
@apiParam  (UserInfo)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (UserInfo)      {String}        [hobby]                        Sở thích.
@apiParam  (UserInfo)      {String}        [location]                     Vị trí.
@apiParam  (UserInfo)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>
@apiParam     (Feedback)    {Number}  [trusted]   Kết quả đánh giá trusted profile.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Profile không trusted;</li>
<li><code>1</code>: Profile có trusted.</li>
@apiParam     (Feedback)    {Number}  [saled]   Kết quả sale.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Sale thất bại;</li>
<li><code>1</code>: Sale thành công.</li>
@apiParam     (Feedback)    {Number}  [demand]   Kết quả đánh giá nhu cầu của khách hàng.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: KH không có nhu cầu;</li>
<li><code>1</code>: KH có nhu cầu.</li>

@apiParamExample    {json}      Body example:
{
    "leads":[
        {
            "id":"9035b72d-4b8e-471d-935d-e73f73063246",
            "user_info": {
                "display_name": "AnNV",
                "phone_number": "+***********",
                "email": "<EMAIL>",
                "status": 1,
                "avatar": "",
                "birthday": "29/11/1989",
                "gender": 2,
                "address": "Cầu Giấy, Hà Nội",
                "phone_number2": "",
                "national_phone_code": "84",
                "province_code": 1,
                "district_code": 1,
                "ward_code": 1,
                "marital_status": 1,
                "monthly_income": 0,
                "income_unit": 1,
                "income_type": 1,
                "religiousness": 1,
                "nation": 1,
                "job": 39,
                "hobby": "",
                "location": "",
                "people_id": "1234567890",
                "frequently_demands": [1, 2, 3]
            },
            "feedback":{
                "trusted": 1,
                "saled": 1,
                "demand": 1
            }

        }
    ]
}

@apiSuccess     {String[]}      success     Danh sách id của khách hàng đã update thành công.
@apiSuccess     {Object[]}      error       Danh sách khách hàng update không thành công.
@apiSuccess     {String}      error..id   Id của khách hàng trên hệ thống DMP-MOBIO.
@apiSuccess     {String}      error..message   Nguyên nhân lỗi không update được thông tin khách hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "success":[
        "0755ee00-8610-424f-ba4b-b264f64c3b46",
        "ccca9bfb-0f21-47e2-8f90-8d590e094b77"
    ],
    "error":[
        {
            "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",
            "message": "..."
        },
        {
            "id": "a33bf63e-5f0e-4023-8bb4-f6793666ddcf",
            "message": "..."
        }
    ]
}
"""

*********************************** Batches ***********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} /batches/import/users Batch import users
@apiDescription Dịch vụ tạo/update user.
@apiGroup BatchOperations
@apiVersion 1.0.0
@apiName BatchImportUsers

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}    business_case_id    Id của chương trình campaign.
@apiParam   (Body:)   {User[]}    users   Danh sách các user cần import vào hệ thống.

@apiParam  (User)      {String}        [display_name]                Tên khách hàng
@apiParam     (User)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam   (User)    {String}    [people_id]     Chứng mình thư nhân dân hoặc thẻ căn cước công dân.
@apiParam  (User)      {String}        [email]                       Email của khách hàng
@apiParam  (User)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (User)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (User)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (User)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (User)       {String}        [address]                      Địa chỉ
@apiParam  (User)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (User)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (User)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (User)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (User)       {Number}        [district_code]                Mã quận huyện
@apiParam  (User)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (User)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (User)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (User)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam  (User)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (User)      {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam  (User)      {Number}        [religiousness]                Tôn giáo
@apiParam  (User)      {Number}        [nation]                       Dân tộc.
@apiParam  (User)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (User)      {String}        [location]                     Vị trí.
@apiParam  (User)     {Array}         [frequently_demands]                    Mảng id nhu cầu thường xuyên.
<li><code>1: Đi du lịch</code></li>
<li><code>2: Mua sắm online</code></li>
<li><code>3: Mua sắm siêu thị</code></li>
<li><code>4: Ăn uống nhà hàng</code></li>
<li><code>5: Giải trí</code></li>
<li><code>6: Đóng tiền học phí</code></li>

@apiParamExample   {json}  Body example
{
  "business_case_id":"3ec01a58-e8dc-4e99-925c-0ee094b5e92e",
  "users": [
    {
      "display_name": "andrew",
      "phone_number": "+***********",
      "email": "<EMAIL>",
      "social_user": {
          "id_social": "3456789",
          "social_type": 1,
      },
      "full_name": "Andrew Nguyễn",
      "phone_number2": "",
      "phone_number3": "",
      "gender": 2,
      "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
      "national_phone_code": "84",
      "province_code": 1,
      "district_code": 5,
      "ward_code": 167,
      "marital_status": 1,
      "birthday": "********",
      "income_low_threshold": 0,
      "income_high_threshold": 0,
      "income_unit": 1,
      "income_type": 1,
      "religiousness": 1,
      "nation": 1,
      "job": 39,
      "location": "",
      "people_id": "",
      "frequently_demands": [
          1,
          2,
          3
      ]
    }
  ]
}
"""

******************************** Feedback User ********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} /feedback/users     Feedback User
@apiDescription Dịch vụ update lại thông tin của user sau khi sale.
@apiGroup Customer
@apiVersion 1.0.0
@apiName FeedbackUser

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam     (Body:)     {Object[]}    users       Danh sách khách hàng feedback. Xem chi tiết đối tượng <code>User</code>

@apiParam     (User)      {String}      id          Định danh của khách hàng trên hệ thống.
@apiParam     (User)      {Object}      user_info   Thông tin user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>UserInfo</code>
@apiParam     (User)      {Object}      feedback    Thông tin feedback về user có được sau nghiệp vụ sale. Xem chi tiết đối tượng <code>Feedback</code>

@apiParam  (User)      {String}        [display_name]                Tên khách hàng
@apiParam     (User)    {String}   [phone_number]  Số điện thoại của khách hàng. Chuẩn hoá theo chuẩn quốc tế.
@apiParam  (User)      {String}        [email]                       Email của khách hàng
@apiParam  (User)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiParam  (User)       {String}        [avatar]                      Đường dẫn ảnh đại diện của khách hàng
@apiParam  (User)       {String}        [birthday]                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiParam  (User)       {Number}        [gender]                       Giới tính</br>
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiParam  (User)       {String}        [address]                      Địa chỉ
@apiParam  (User)       {String}        [full_name]                    Tên đầy đủ của khách hàng.
@apiParam  (User)       {String}        [phone_number2]                Số điện thoại thứ 2 của khách hàng
@apiParam  (User)       {String}        [national_phone_code]          Mã điện thoại quốc gia
@apiParam  (User)       {Number}        [province_code]                Mã tỉnh thành
@apiParam  (User)       {Number}        [district_code]                Mã quận huyện
@apiParam  (User)       {Number}        [ward_code]                   Mã phường xã
@apiParam  (User)       {Number}        [marital_status]               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam  (User)      {Number}        [monthly_income]         Thu nhập hàng tháng
@apiParam  (User)      {Number=1:VNĐ 2:USD}    [income_unit]          Loại tiền.
@apiParam  (User)      {Number}        [religiousness]                Tôn giáo
@apiParam  (User)      {Number}        [nation]                       Dân tộc.
@apiParam  (User)      {Number}        [job]                          Nghề nghiệp.
@apiParam  (User)      {String}        [hobby]                        Sở thích.
@apiParam  (User)      {String}        [location]                     Vị trí.
@apiParam     (Feedback)    {Number}  [trusted]   Kết quả đánh giá trusted profile.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Profile không trusted;</li>
<li><code>1</code>: Profile có trusted.</li>
@apiParam     (Feedback)    {Number}  [saled]   Kết quả sale.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: Sale thất bại;</li>
<li><code>1</code>: Sale thành công.</li>
@apiParam     (Feedback)    {Number}  [demand]   Kết quả đánh giá nhu cầu của khách hàng.</br>
<li><code>-1</code>: Chưa có thông tin;</li>
<li><code>0</code>: KH không có nhu cầu;</li>
<li><code>1</code>: KH có nhu cầu.</li>

@apiParamExample    {json}      Body example:
{
    "users":[
        {
            "id":"9035b72d-4b8e-471d-935d-e73f73063246",
            "user_info": {
                "display_name": "AnNV",
                "phone_number": "+***********",
                "email": "<EMAIL>",
                "status": 1,
                "avatar": "",
                "birthday": "29/11/1989",
                "gender": 2,
                "address": "Cầu Giấy, Hà Nội",
                "phone_number2": "",
                "national_phone_code": "84",
                "province_code": 1,
                "district_code": 1,
                "ward_code": 1,
                "marital_status": 1,
                "monthly_income": 0,
                "income_unit": 1,
                "religiousness": 1,
                "nation": 1,
                "job": 39,
                "hobby": "",
                "location": ""
            },
            "feedback":{
                "trusted": 1,
                "saled": 1,
                "demand": 1
            }

        }
    ]
}

@apiSuccess     {String[]}      success     Danh sách id của khách hàng đã update thành công.
@apiSuccess     {Object[]}      error       Danh sách khách hàng update không thành công.
@apiSuccess     {String}      error..id   Id của khách hàng trên hệ thống.
@apiSuccess     {String}      error..message   Nguyên nhân lỗi không update được thông tin khách hàng.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "success":[
        "0755ee00-8610-424f-ba4b-b264f64c3b46",
        "ccca9bfb-0f21-47e2-8f90-8d590e094b77"
    ],
    "error":[
        {
            "id": "8caf14f9-361b-4942-bf06-d4ee06ac8ed8",
            "message": "..."
        },
        {
            "id": "a33bf63e-5f0e-4023-8bb4-f6793666ddcf",
            "message": "..."
        }
    ]
}
"""

************************************************************************************************************
************************************* API Create User ******************************************************
************************************************************************************************************
"""
@api {post} /users/<state> Tạo user
@apiDescription API tạo mới 1 user. <br/>Lưu ý: Body được gửi lên theo cấu trúc <code>form-data</code>
@apiGroup Customer
@apiVersion 1.0.0
@apiName CreateCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {Number}    state        Tình trạng của khách hàng. <br/>
<li><code>1: Khách hàng chính thức của hệ thống</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiParam   (Body:)     {String}    [password]                              Mật khẩu
@apiParam   (Body:)     {String}    display_name                            Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng. Bắt buộc khi <code>state=1</code> và <code>created_account_type=1</code>
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.Bắt buộc khi <code>state=1</code> và <code>created_account_type=3</code>.
@apiParam   (Body:)     {String}    [created_account_type]                   Kiểu tạo tài khoản. Bắt buộc khi <code>state=1</code><br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiParam   (Body:)     {file}    [avatar]                                  Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {Object}    [social_user]                           Thông tin chi tiết của user theo mạng xã hội. Bắt buộc khi <code>created_account_type=2 hoặc 3</code>.<br/>
<table>
    <thead>
        <tr>
          <th style="width: 40%">Field</th>
          <th style="width: 10%">Type</th>
          <th style="width: 50%">Description</th>
        </tr>
    </thead>
    <tr>
        <td class="code">id_social</td>
        <td>String</td>
        <td><p>ID của social</p></td>
    </tr>
    <tr>
        <td class="code">social_type</td>
        <td>Number</td>
        <td><p>Loại social</p><p class="type-size">Allowed values:<li><code>1:Facebook</code></li><li><code>2:GooglePlus</code></li> <li><code>3:Instagram</code></li> <li><code>4:Zalo</code></li></p></td>
    </tr>
    <tr>
        <td class="code">access_token <span class="label label-optional">optional</span></td>
        <td>String</td>
        <td><p>Access token để truy cập social (nếu có).</p></td>
    </tr>
</table>
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYYmmDD</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [hobby]                                 Sở thích.
@apiParam   (Body:)     {String}    [location]                              Vị trí.


@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess       {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess       {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess       {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess       {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess       {String}        personal_code                Mã cá nhân của khách hàng.
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        hobby                        Sở thích.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Phone Response: HTTP/1.1 200 OK
//response cho trường hợp tạo user với created_account_type=1
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "created_account_type": 1,
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "mpoint": 0,
  "state": 1,
  "level": 1,
  "verify_contact_info": 0,
  "code_verify_contact_info": "0678",
  "personal_code": "ZAGEJHN",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": ""
}

@apiSuccessExample     {json}    Social Response: HTTP/1.1 200 OK
//response cho trường hợp tạo user với created_account_type=2 hoặc 3
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "created_account_type": 2,
  "social_user": {
    "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
    "social_type": 1,
    "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
  },
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "mpoint": 0,
  "state": 1,
  "level": 1,
  "verify_contact_info": 0,
  "code_verify_contact_info": "0678",
  "personal_code": "ZAGEJHN",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": ""
}
"""

************************************************************************************************************
************************************* API Update User ******************************************************
************************************************************************************************************
"""
@api {patch} /users/<user_id> Update user
@apiDescription API cập nhật 1 user. <br/>Lưu ý: Body được gửi lên theo cấu trúc <code>form-data</code>
@apiGroup Customer
@apiVersion 1.0.0
@apiName UpdateCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {String}    user_id                             ID của user.
@apiParam   (Body:)     {String}    [display_name]                          Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng. Bắt buộc khi <code>state=1</code> và <code>created_account_type=1</code>
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.Bắt buộc khi <code>state=1</code> và <code>created_account_type=3</code>.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>yyyyMMdd</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [hobby]                                 Sở thích.
@apiParam   (Body:)     {String}    [location]                              Vị trí.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess       {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess       {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess       {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess       {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess       {String}        personal_code                Mã cá nhân của khách hàng.
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        hobby                        Sở thích.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "created_account_type": 2,
  "social_user": {
    "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
    "social_type": 1,
    "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
  },
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "mpoint": 0,
  "state": 1,
  "level": 1,
  "verify_contact_info": 0,
  "code_verify_contact_info": "0678",
  "personal_code": "ZAGEJHN",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": ""
}
"""

************************************************************************************************************
********************************* API Change User Password *************************************************
************************************************************************************************************
"""
@api {patch} /users/<user_id>/pwd User đổi mật khẩu
@apiDescription API đổi mật khẩu của 1 user.
@apiGroup Customer
@apiVersion 1.0.0
@apiName CustomerChangePwd

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {String}    user_id                             ID của user.
@apiParam   (Body:)         {String}    password                            Mật khẩu mới.
@apiParam   (Body:)         {String}    old_password                        Mật khẩu cũ.
@apiParam   (Body:)         {String}    verify_password                     Mật khẩu mới nhập lại.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess       {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess       {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess       {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess       {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess       {String}        personal_code                Mã cá nhân của khách hàng.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "created_account_type": 2,
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "mpoint": 0,
  "state": 1,
  "level": 1,
  "verify_contact_info": 0,
  "code_verify_contact_info": "0678",
  "personal_code": "ZAGEJHN"
}
"""

************************************************************************************************************
************************************* API Delete User ******************************************************
************************************************************************************************************
"""
@api {delete} /users/<user_id> Delete user
@apiDescription API Delete 1 user. Api update user status=2 table Customer.
@apiGroup Customer
@apiVersion 1.0.0
@apiName DeleteCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {String}    user_id                             ID của user.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess       {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess       {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess       {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess       {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess       {String}        personal_code                Mã cá nhân của khách hàng.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "created_account_type": 1,
  "status": 2,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "mpoint": 0,
  "state": 1,
  "level": 1,
  "verify_contact_info": 0,
  "code_verify_contact_info": "0678",
  "personal_code": "ZAGEJHN"
}
"""

************************************************************************************************************
********************************** API Get Detail User *****************************************************
************************************************************************************************************
"""
@api {get} /users/<user_id>/info Lấy thông tin chi tiết User
@apiDescription API Lấy thông tin chi tiết 1 User. API lấy thông tin từ bảng Customer + Info + Social.
@apiGroup Customer
@apiVersion 1.0.0
@apiName GetDetailCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {String}    user_id                             ID của user.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess       {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess       {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess       {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess       {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess       {String}        personal_code                Mã cá nhân của khách hàng.
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        hobby                        Sở thích.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "created_account_type": 2,
  "social_user": [
    {
      "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
      "social_type": 1,
      "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
    }
  ],
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "mpoint": 0,
  "state": 1,
  "level": 1,
  "verify_contact_info": 0,
  "code_verify_contact_info": "0678",
  "personal_code": "ZAGEJHN",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": ""
}
"""

************************************************************************************************************
************************************* API Get User *********************************************************
************************************************************************************************************
"""
@api {get} /users/<user_id> Lấy chi tiết User
@apiDescription API Lấy thông tin 1 User. API lấy tất cả thông tin từ bảng Customer.
@apiGroup Customer
@apiVersion 1.0.0
@apiName GetCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {String}    user_id                             ID của user.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess       {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess       {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess       {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess       {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess       {String}        personal_code                Mã cá nhân của khách hàng.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "created_account_type": 1,
  "status": 2,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "mpoint": 0,
  "state": 1,
  "level": 1,
  "verify_contact_info": 0,
  "code_verify_contact_info": "0678",
  "personal_code": "ZAGEJHN"
}
"""

***************************************** API Get List Users *************************************************
* version: 1.0.2                                                                                             *
* version: 1.0.1                                                                                             *
* version: 1.0.0                                                                                             *
**************************************************************************************************************
"""
@api {get} /users Lấy danh sách user
@apiDescription API Lấy Danh sách user. API hỗ trợ tìm kiếm, sắp xếp, filter, paging danh sách user.
@apiGroup Customer
@apiVersion 1.0.2
@apiName GetListCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)     {Number}     [state]                                 Filter theo tình trạng tài khoản của user.
@apiParam   (Query:)     {String}     [search]                                Chuỗi tìm kiếm. Tìm kiếm theo tên user.
@apiParam   (Query:)     {Date}       [from]     Mốc thời điểm đầu lọc user theo thời điểm cuối cập nhật. Example: <code>&from=1509433200</code>.
@apiParam   (Query:)     {Date}       [to]       Mốc thời điểm cuối lọc user theo thời điểm cuối cập nhật. Example: <code>&to=1517126400</code>. Nếu client không gửi thì lấy thời điểm hiện tại.


@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {String}        display_name                Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess  (data)      {String}        email                       Email của khách hàng
@apiSuccess  (data)      {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess  (data)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess  (data)      {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)      {DateTime}      created_time                Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)      {DateTime}      updated_time                Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)      {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess  (data)      {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess  (data)      {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess  (data)      {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess  (data)      {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess  (data)      {String}        personal_code                Mã cá nhân của khách hàng.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
      "display_name": "andrew",
      "phone_number": "+***********",
      "email": "<EMAIL>",
      "created_account_type": 2,
      "status": 1,
      "avatar": "",
      "created_time": "2017-12-12T15:12:28Z",
      "updated_time": "2017-12-12T15:12:28Z",
      "mpoint": 0,
      "state": 1,
      "level": 1,
      "verify_contact_info": 0,
      "code_verify_contact_info": "0678",
      "personal_code": "ZAGEJHN"
    },
    ...
  ],
  "paging":{
    ...
  }
}
"""
********************
"""
@api {get} /users Lấy danh sách user
@apiDescription API Lấy Danh sách user. API hỗ trợ tìm kiếm, sắp xếp, filter, paging danh sách user.
@apiGroup Customer
@apiVersion 1.0.1
@apiName GetListCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)     {Number}    [state]                                 Filter theo tình trạng tài khoản của user.
@apiParam   (Query:)     {String}     [search]                                Chuỗi tìm kiếm. Tìm kiếm theo tên user.


@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {String}        display_name                Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess  (data)      {String}        email                       Email của khách hàng
@apiSuccess  (data)      {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess  (data)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess  (data)      {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)      {DateTime}      created_time                Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)      {DateTime}      updated_time                Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)      {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess  (data)      {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess  (data)      {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess  (data)      {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess  (data)      {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess  (data)      {String}        personal_code                Mã cá nhân của khách hàng.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
      "display_name": "andrew",
      "phone_number": "+***********",
      "email": "<EMAIL>",
      "created_account_type": 2,
      "status": 1,
      "avatar": "",
      "created_time": "2017-12-12T15:12:28Z",
      "updated_time": "2017-12-12T15:12:28Z",
      "mpoint": 0,
      "state": 1,
      "level": 1,
      "verify_contact_info": 0,
      "code_verify_contact_info": "0678",
      "personal_code": "ZAGEJHN"
    },
    ...
  ],
  "paging":{
    ...
  }
}
"""
******************
"""
@api {get} /users Lấy danh sách user
@apiDescription API Lấy Danh sách user. API hỗ trợ tìm kiếm, sắp xếp, filter, paging danh sách user.
@apiGroup Customer
@apiVersion 1.0.0
@apiName GetListCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)     {Number}    [state]                                 Filter theo tình trạng tài khoản của user.
@apiParam   (Query:)     {String}     [search]                                Chuỗi tìm kiếm. Tìm kiếm theo tên user.


@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {String}        display_name                Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess  (data)      {String}        email                       Email của khách hàng
@apiSuccess  (data)      {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess  (data)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess  (data)      {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)      {DateTime}      created_time                Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)      {DateTime}      updated_time                Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)      {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess  (data)      {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess  (data)      {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess  (data)      {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess  (data)      {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess  (data)      {String}        personal_code                Mã cá nhân của khách hàng.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
      "display_name": "andrew",
      "phone_number": "+***********",
      "email": "<EMAIL>",
      "created_account_type": 2,
      "status": 1,
      "avatar": "",
      "created_time": "2017-12-12T15:12:28Z",
      "updated_time": "2017-12-12T15:12:28Z",
      "mpoint": 0,
      "state": 1,
      "level": 1,
      "verify_contact_info": 0,
      "code_verify_contact_info": "0678",
      "personal_code": "ZAGEJHN"
    },
    ...
  ],
  "paging":{
    ...
  }
}
"""

************************************************************************************************************
********************************** API Add User Device *****************************************************
************************************************************************************************************
"""
@api {put} /users/<user_id>/devices Add thiết bị cho user
@apiDescription API add thiết bị cho user khi user sử dụng phần mềm trên nền tảng Android và iOS.
@apiGroup Customer
@apiVersion 1.0.0
@apiName AddUserDevice

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Resource:)     {String}    user_id                             ID của user.
@apiParam   (Body:)         {String}    device_id                           Mã thiết bị, do client gửi lên. <br/>
<li>Với nền tảng Android thì lấy <code>AndroidID</code></li>
<li>Với nền tảng iOS thì lấy <code>IDFV</code></li>
@apiParam   (Body:)         {String}    [operating_system]                  Hệ điều hành.<br/>
Allowed values:
<li><code>ANDROID</code></li>
<li><code>IOS</code></li>
<li><code>WP</code></li>
@apiParam   (Body:)         {String}    [operating_system_version]          Phiên bản hệ điều hành.
@apiParam   (Body:)         {String}    [manufactory]                       Hãng sản xuất thiết bị.
@apiParam   (Body:)         {String}    [model]                             Dòng thiết bị. VD: <code>IPHONE8,1;SM-G955F ...</code>
@apiParam   (Body:)         {Number}    [screen_width]                      Kích thước chiều rộng màn hình.
@apiParam   (Body:)         {Number}    [screen_height]                     Kích thước chiều cao màn hình.
@apiParam   (Body:)         {String}    [imei1]                             IMEI 1
@apiParam   (Body:)         {String}    [imei2]                             IMEI 2
@apiParam   (Body:)         {String}    [serial]                            Serial thiết bị. (Sử dụng trong trường hợp thiết bị không có imei).
@apiParam   (Body:)         {String}    [mac_address]                       Địa chỉ MAC của thiết bị.


@apiSuccess              {String}        id                                 Id user thiết bị
@apiSuccess              {String}        user_id                            Id của user
@apiSuccess              {String}        device_id                          Id của thiết bị
@apiSuccess              {String}        operating_system                   Hệ điều hành
@apiSuccess              {String}        operating_system_version           Phiên bản hệ điều hành
@apiSuccess              {String}        manufactory                        Hãng sản xuất thiết bị
@apiSuccess              {String}        model                              Model của thiết bị
@apiSuccess              {Number}        screen_width                       Cỡ màn hình ngang
@apiSuccess              {Number}        screen_height                      Cỡ màn hình dọc
@apiSuccess              {String}        imei1                              Imei1 của thiết bị
@apiSuccess              {String}        imei2                              Imei2 của thiết bị
@apiSuccess              {String}        serial                             Serial của thiết bị. (Dùng trong trường hợp thiết bị ko có imei)
@apiSuccess              {String}        mac_address                        Địa chỉ MAC của thiết bị.
@apiSuccess              {Number}        status                             Trạng thái của thiết bị.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code></li>
<li><code>2: Disable</code></li>
@apiUse created_time
@apiUse updated_time

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "eb6fe390-c2d1-47cd-b924-0e5ec82bc291",
  "user_id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "device_id": "af91192c-abc1-4a7a-b95c-858b3788f296",
  "operating_system": "IOS",
  "operating_system_version": "10.2",
  "manufactory": "APPLE",
  "model": "IPHONE8,1",
  "screen_width": 375,
  "screen_height": 667,
  "imei1": "",
  "imei2": "",
  "serial": "",
  "mac_address": "00:25:96:FF:FE:12:34:56",
  "status": 1,
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
}
"""

************************************************************************************************************
********************************** API User Register *******************************************************
************************************************************************************************************

"""
@api {post} /users/register User đăng ký mới
@apiDescription API đăng ký cho user vào hệ thống.
@apiGroup Customer
@apiVersion 1.0.0
@apiName RegisterUser

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)         {String}    [password]                          Password
@apiParam   (Body:)         {String}    display_name                        Tên hiển thị. Nếu <code>created_account_type=1</code> thì lấy <code>phone_number</code> và thay thế 3 số cuối <code>= ***</code>
@apiParam   (Body:)         {String}    [phone_number]                      Số điện thoại. Bắt buộc khi <code>created_account_type=1</code>.
@apiParam   (Body:)         {String}    [email]                             Địa chỉ email.
@apiParam   (Body:)         {String}    created_account_type                 Kiểu tạo tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiParam   (Body:)         {String}    [id_social]                         ID Social. Bắt buộc khi <code>created_account_type=2 hoặc 3</code>. 
@apiParam   (Body:)         {String}    [access_token]                      Mã truy cập social.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess       {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess       {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess       {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess       {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess       {String}        personal_code                Mã cá nhân của khách hàng.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "created_account_type": 2,
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "mpoint": 0,
  "state": 1,
  "level": 1,
  "verify_contact_info": 0,
  "code_verify_contact_info": "0678",
  "personal_code": "ZAGEJHN"
}

"""

************************************************************************************************************
*************************** API Chuyển user tiềm năng sang chính thức **************************************
************************************************************************************************************
"""
@api {patch} /users/<user_id>/offical Chuyển user tiềm năng lên user chính thức
@apiDescription API cập nhật state=1 của user.
@apiGroup Customer
@apiVersion 1.0.1
@apiName ChangeStateCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {String}    user_id                             ID của user.

@apiParam   (Body:)     {String}    [display_name]                          Tên khách hàng
@apiParam   (Body:)     {String}    phone_number                            Số điện thoại của khách hàng. Bắt buộc khi chuyển từ tiềm năng sang chính thức.
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>yyyyMMdd</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [hobby]                                 Sở thích.
@apiParam   (Body:)     {String}    [location]                              Vị trí.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess       {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess       {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess       {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess       {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess       {String}        personal_code                Mã cá nhân của khách hàng.
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        hobby                        Sở thích.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "created_account_type": 1,
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "mpoint": 0,
  "state": 1,
  "level": 1,
  "verify_contact_info": 0,
  "code_verify_contact_info": "0678",
  "personal_code": "ZAGEJHN",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": ""
}

"""


"""
@api {patch} /users/<user_id>/offical Chuyển user tiềm năng lên user chính thức
@apiDescription API cập nhật state=1 của user.
@apiGroup Customer
@apiVersion 1.0.0
@apiName ChangeStateCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {String}    user_id                             ID của user.

@apiParam   (Body:)     {String}    [display_name]                          Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng. Bắt buộc khi <code>created_account_type=1</code>
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.Bắt buộc khi <code>created_account_type=3</code>.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>yyyyMMdd</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [hobby]                                 Sở thích.
@apiParam   (Body:)     {String}    [location]                              Vị trí.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess       {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess       {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess       {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess       {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess       {String}        personal_code                Mã cá nhân của khách hàng.
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        hobby                        Sở thích.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "created_account_type": 2,
  "social_user": {
    "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
    "social_type": 1,
    "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
  },
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "mpoint": 0,
  "state": 1,
  "level": 1,
  "verify_contact_info": 0,
  "code_verify_contact_info": "0678",
  "personal_code": "ZAGEJHN",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": ""
}

"""


************************************************************************************************************
***************************************** API User Login ***************************************************
************************************************************************************************************
"""
@api {post} /users/login User login
@apiDescription API Login cho user
@apiGroup Customer
@apiVersion 1.0.0
@apiName UserLogin

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParamExample {json} Body
{
  "username": "0904123456",
  "password": "123456"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "jwt": "Thông tin user từ bảng User."
}

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess       {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess       {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess       {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess       {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess       {String}        personal_code                Mã cá nhân của khách hàng.


"""


############################################################################################################
###################################### API Search user by social ###########################################
############################################################################################################
"""
@api {get} /search-users Tìm kiếm khách hàng.
@apiDescription Tìm kiếm khách hàng của một nhãn hàng. Nếu có đủ các tham số, thứ tự ưu tiên sẽ lần lượt search từ trên xuống:
<li>1. Nếu tìm kiếm theo <code>social</code> thì bắt buộc gửi tham số <code>social_type</code> và <code>social_id</code>.</li>
<li>2. Nếu tìm kiếm theo <code>chuỗi tìm kiếm</code> thì bắt buộc gửi tham số <code>query</code>. Tìm kiếm theo số điện thoại và email của khách hàng.</li>
@apiGroup Customer
@apiVersion 1.0.0
@apiName SearchCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    [query] Chuỗi tìm kiếm khách hàng theo email, số điện thoại
@apiParam   (Query:)    {String}    [social_id] ID của khách hàng trên mạng xã hội.
@apiParam   (Query:)    {Number=1-FACEBOOK;2-Zalo}    [social_type] Loại mạng xã hội. Bắt buộc khi search social.

@apiSuccess       {Array}         customers                   Mảng danh sách khách hàng tìm kiếm.

@apiSuccess (customers)       {String}        id                          Id của user
@apiSuccess (customers)       {String}        display_name                Tên khách hàng
@apiSuccess (customers)       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess (customers)       {String}        email                       Email của khách hàng
@apiSuccess (customers)       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess (customers)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess (customers)      {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiSuccess (customers)      {DateTime}      created_time                Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess (customers)      {DateTime}      updated_time                Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess (customers)      {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess (customers)      {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess (customers)      {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess (customers)      {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess (customers)      {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess (customers)      {String}        personal_code                Mã cá nhân của khách hàng.
@apiSuccess (customers)      {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess (customers)      {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess (customers)      {String}        phone_number3                Số điện thoại thứ 2 của khách hàng
@apiSuccess (customers)      {Number}        gender                       Giới tính
@apiSuccess (customers)      {String}        address                      Địa chỉ
@apiSuccess (customers)      {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess (customers)      {String}        province_code                Mã tỉnh thành
@apiSuccess (customers)      {String}        district_code                Mã quận huyện
@apiSuccess (customers)      {String}        ward_code                   Mã phường xã
@apiSuccess (customers)      {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess (customers)      {String}        birthday                     Ngày sinh. Format: <code> dd/MM/yyyy </code>
@apiSuccess (customers)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess (customers)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess (customers)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess (customers)      {Number}        religiousness                Tôn giáo
@apiSuccess (customers)      {Number}        nation                       Dân tộc.
@apiSuccess (customers)      {Number}        job                          Nghề nghiệp.
@apiSuccess (customers)      {String}        hobby                        Sở thích.
@apiSuccess (customers)      {String}        location                     Vị trí.
@apiSuccess (customers)      {Array}        social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "customers": [
    {
      "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
      "display_name": "andrew",
      "phone_number": "+***********",
      "email": "<EMAIL>",
      "created_account_type": 2,
      "social_user": [{
              "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
              "social_type": 1,
              "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
            }],
      "status": 1,
      "avatar": "",
      "created_time": "2017-12-12T15:12:28Z",
      "updated_time": "2017-12-12T15:12:28Z",
      "mpoint": 0,
      "state": 1,
      "level": 1,
      "verify_contact_info": 0,
      "code_verify_contact_info": "0678",
      "personal_code": "ZAGEJHN",
      "full_name": "Andrew Nguyễn",
      "phone_number2": "",
      "phone_number3": "",
      "gender": 2,
      "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
      "national_phone_code": "84",
      "province_code": "HANOI",
      "district_code": "CAUGIAY",
      "ward_code": "",
      "marital_status": 1,
      "birthday": "20/11/1989",
      "income_low_threshold": 0,
      "income_high_threshold": 0,
      "income_unit": 1,
      "religiousness": 1,
      "nation": 1,
      "job": 39,
      "hobby": "",
      "location": ""
    },
    ...
  ]
}
"""


############################################################################################################
###################################### API Get Danh sách customer ###########################################
############################################################################################################
"""
@api {get} /users-info Lấy danh sách thông tin user
@apiDescription API Lấy Danh sách thông tin user, gồm social info và các thông tin cơ bản. API hỗ trợ tìm kiếm, sắp xếp, filter, paging danh sách user.
@apiGroup Customer
@apiVersion 1.0.0
@apiName GetListUserInfo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)     {Number}    [state]                                 Filter theo tình trạng tài khoản của user.
@apiParam   (Query:)     {String}     [search]                                Chuỗi tìm kiếm. Tìm kiếm theo tên user.


@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {String}        display_name                Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess  (data)      {String}        email                       Email của khách hàng
@apiSuccess  (data)      {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess  (data)      {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess  (data)      {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)      {DateTime}      created_time                Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)      {DateTime}      updated_time                Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)      {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess  (data)      {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess  (data)      {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess  (data)      {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess  (data)      {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess  (data)      {String}        personal_code                Mã cá nhân của khách hàng.
@apiSuccess  (data)      {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess (data)      {Number}        gender                       Giới tính
@apiSuccess (data)      {String}        address                      Địa chỉ
@apiSuccess (data)       {Array}        social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
      "display_name": "andrew",
      "phone_number": "+***********",
      "email": "<EMAIL>",
      "created_account_type": 2,
      "status": 1,
      "avatar": "",
      "created_time": "2017-12-12T15:12:28Z",
      "updated_time": "2017-12-12T15:12:28Z",
      "mpoint": 0,
      "state": 1,
      "level": 1,
      "verify_contact_info": 0,
      "code_verify_contact_info": "0678",
      "personal_code": "ZAGEJHN",
      "social_user": [{
              "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
              "social_type": 1,
              "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
            }],
      "birthday": "29/11/1989",
      "gender": 2,
      "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
    },
    ...
  ],
  "paging":{
    ...
  }
}
"""
"""
************************************************************************************************************
********************************** API Get Detail User For verify ******************************************
#   v2.0.0
#   v1.0.0
************************************************************************************************************
"""
@api {GET} /profiling/v2.0/merchants/<merchant_id>/users/actions/verify Lấy thông tin chi tiết User sau khi verify
@apiDescription API Lấy thông tin chi tiết 1 User. API lấy thông tin từ bảng Profile
@apiGroup VerifyCustomer
@apiVersion 2.0.0
@apiName GetDetailCustomerForVerify

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader      (Headers:)      {String}        Content-Type       application/json

@apiParam       (Query:)         {String}       lmbolc      Chuỗi mã hoá dùng để verify                            

@apiSuccess       {String}        merchant_id                   UUID Merchant ID của khách hàng 
@apiSuccess       {String}        profile_id                    UUID Profile ID của khách hàng
@apiSuccess       {Array}         phone_number                  Tập các số điện thoại của khách hàng
@apiSuccess       {Array}         email                         Tập các địa chỉ email của khách hàng      
@apiSuccess       {String}        name                          Tên khách hàng
@apiSuccess       {String}        people_id                     CMND của khách hàng
@apiSuccess       {Array}         social_user                   Thông tin social của khách hàng

@apiSuccess       {Int}           created_account_type          Nguồn khởi tạo tài khoản khách hàng. Allowed values: </br>
<li><code>-1: Other</code></li>
<li><code>0: WebCem</code></li>
<li><code>1: By_Phone_Number</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google_Plus</code></li>
<li><code>4: Landing_Page</code></li>
<li><code>5: Import_File</code></li>
<li><code>6: Zalo</code></li>
<li><code>7: Instagram</code></li>
<li><code>8: Mobile_App</code></li>
<li><code>9: Youtube</code></li>
<li><code>10: Zalo_campaign_09171217</code></li>
<li><code>11: Corena_captive_wifi</code></li>
<li><code>12: Call_center</code></li>

@apiSuccess       {String}          avatar                      Link avatar của khách hàng
@apiSuccess       {Int}             gender                      Giới tính của khách hàng. Allowed values: </br>
<li><code>1: Unknown</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nu</code></li>
@apiSuccess       {String}          address                     Địa chỉ khách hàng
@apiSuccess       {Object}          district_code               Code quận của khách hàng. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListDistrict</code> 
@apiSuccess       {Object}          province_code               Code tỉnh của khách hàng. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListProvinces</code>
@apiSuccess       {Object}          ward_code                   Code phường của khách hàng. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListWard</code>
@apiSuccess       {Object}          operation                   Lĩnh vực công tác của khách hàng. Plz check </br><code>API: https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListOperation</code>
@apiSuccess       {Object}          job                         Nghề nghiệp của khách hàng. plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListJob<</code>
@apiSuccess       {Object}          religiousness               Tôn giáo. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListReligiousness</code>
@apiSuccess       {Object}          nation                      Dân tộc. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListNation</code>
@apiSuccess       {Object}          marital_status              Tình trạng hôn nhân. Plz </br><code>check API: https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListMaritalStatus</code>
@apiSuccess       {String}          birthday                    Ngày sinh . %Y-%m-%d

@apiSuccess       {Int}             budget_low_threshold        Mức tiêp dùng ngưỡng thấp
@apiSuccess       {Int}             budget_high_threshold       Mức tiêu dùng ngưỡng cao
@apiSuccess       {Int}             income_low_threshold        Thu nhập ngưỡng thấp
@apiSuccess       {Int}             income_high_threshold       Thu nhập ngưỡng cao

@apiSuccess       {Int}             salary                      Lương 
@apiSuccess       {Int}             income_type                 Loại thu nhập. Allowed values: </br>
<li><code>1: ChuyenKhoan</code></li>
<li><code>2: TienMat</code></li>
<li><code>3: TuDo</code></li>

@apiSuccess       {String}          created_time                Thời gian khởi tạo tài khoản ('%Y-%m-%dT%TZ')
@apiSuccess       {String}          updated_time                Thời gian update thông tin tài khoản ('%Y-%m-%dT%TZ')
@apiSuccess       {Array}           hobby                       Danh sách sở thích khách hàng. Plz check API: https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListHobby

@apiSuccess (social_user)       {String}    id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Int}       social_type       Loại mạng xã hội. Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Youtube</code></li>
@apiSuccess (social_user)       {String}    access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "merchant_id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "profile_id": "andrew",
  "phone_number": ["+***********", "+***********"],
  "email": "<EMAIL>",
  "name": "Ngụ Thị Quỳnh Phương",
  "people_id": "*********",
  "social_user": [
    {
      "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
      "social_type": 1,
      "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
    }
  ],
  "created_account_type": 2,
  "avatar": "https://storage.googleapis.com/test_xloyalty1/images/comments/00001d10-86fe-11e7-94df-0242ac16000c?*************",
  "gender": 3,
  "address": "Thành phố Hồ Chí Minh",
  "district_code": {
    "id": 1,
    "name": "Thành phố Hà Nội"
  },
  "province_code": {
    "id": 8,
    "name": "TUYENQUANG"
  },
  "ward_code": {
    "id": 1,
    "name": "Phường Phúc Xá"
  },
  "operation": {
    "id": 1,
    "name": "An ninh - Bảo vệ"
  },
  "job": {
    "id": 1,
    "name": "Làm ruộng"
  },
  "religiousness": {
    "id": 1,
    "name": "Lương giáo"
  },
  "nation": {
    "id": 1,
    "name": "Kinh"
  },
  "marital_status": {
    "id": 2,
    "name": "Đã đính hôn"
  },
  "birthday": "1999-02-23",
  "budget_low_threshold": 1000,
  "budget_high_threshold": 1000,
  "income_low_threshold": 1000,
  "income_high_threshold": 1000,
  "salary": 1000,
  "income_type": 2,
  "created_time": '2018-10-10T10:19:06Z',
  "updated_time": '2018-10-10T10:19:06Z',
  "hobby": [
    {
      "id": 1,
      "name": "Công nghệ"
    },
    {
      "id": 1,
      "name": "Công nghệ"
    }
  ]
}
"""

#############################
# Version 1.0.0
#############################

"""
@api {GET} /users/actions/verify Lấy thông tin chi tiết User để verify
@apiDescription API Lấy thông tin chi tiết 1 User. API lấy thông tin từ bảng Customer + Info + Social.
@apiGroup VerifyCustomer
@apiVersion 1.0.0
@apiName GetDetailCustomerForVerify

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)     {String}    id                             ID for verify.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess       {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess       {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess       {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess       {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess       {String}        personal_code                Mã cá nhân của khách hàng.
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        hobby                        Sở thích.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "created_account_type": 2,
  "social_user": [
    {
      "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
      "social_type": 1,
      "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
    }
  ],
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "mpoint": 0,
  "state": 1,
  "level": 1,
  "verify_contact_info": 0,
  "code_verify_contact_info": "0678",
  "personal_code": "ZAGEJHN",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": ""
}
"""


************************************************************************************************************
*************************************** API Update verify info *********************************************
* v2.0.0
************************************************************************************************************
"""
@api {PATCH} /profiling/v2.0/merchants/<merchant_id>/users/actions/verify Update thông tin chi tiết User để verify
@apiDescription API Lấy thông tin chi tiết 1 User. API lấy thông tin từ bảng Profile
@apiGroup VerifyCustomer
@apiVersion 2.0.0
@apiName UpdateCustomerVerifyInfo


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader      (Headers:)      {String}        Content-Type       application/json

@apiParam       (Query:)         {String}       lmbolc      Chuỗi mã hoá dùng để verify 
   
@apiParam       (Body:)     {String}        [profile_id]                    UUID Profile ID của khách hàng
@apiParam       (Body:)     {Array}         [phone_number]                  Tập các số điện thoại của khách hàng
@apiParam       (Body:)     {Array}         [email]                         Tập các địa chỉ email của khách hàng      
@apiParam       (Body:)     {String}        [name]                          Tên khách hàng
@apiParam       (Body:)     {String}        [people_id]                     CMND của khách hàng
@apiParam       (Body:)     {Array}         [social_user]                   Thông tin social của khách hàng

@apiParam       (Body:)     {Int}           [created_account_type]          Nguồn khởi tạo tài khoản khách hàng. Allowed values: </br>
<li><code>-1: Other</code></li>
<li><code>0: WebCem</code></li>
<li><code>1: By_Phone_Number</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google_Plus</code></li>
<li><code>4: Landing_Page</code></li>
<li><code>5: Import_File</code></li>
<li><code>6: Zalo</code></li>
<li><code>7: Instagram</code></li>
<li><code>8: Mobile_App</code></li>
<li><code>9: Youtube</code></li>
<li><code>10: Zalo_campaign_09171217</code></li>
<li><code>11: Corena_captive_wifi</code></li>
<li><code>12: Call_center</code></li>

@apiParam       (Body:)     {String}          [avatar]                      Link avatar của khách hàng
@apiParam       (Body:)     {Int}             [gender]                      Giới tính của khách hàng. Allowed values: </br>
<li><code>1: Unknown</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nu</code></li>
@apiParam       (Body:)     {String}          [address]                     Địa chỉ khách hàng
@apiParam       (Body:)     {Object}          [district_code]               Code quận của khách hàng. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListDistrict</code> 
@apiParam       (Body:)     {Object}          [province_code]               Code tỉnh của khách hàng. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListProvinces</code>
@apiParam       (Body:)     {Object}          [ward_code]                   Code phường của khách hàng. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListWard</code>
@apiParam       (Body:)     {Object}          [operation]                   Lĩnh vực công tác của khách hàng. Plz check </br><code>API: https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListOperation</code>
@apiParam       (Body:)     {Object}          [job]                         Nghề nghiệp của khách hàng. plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListJob<</code>
@apiParam       (Body:)     {Object}          [religiousness]               Tôn giáo. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListReligiousness</code>
@apiParam       (Body:)     {Object}          [nation]                      Dân tộc. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListNation</code>
@apiParam       (Body:)     {Object}          [marital_status]              Tình trạng hôn nhân. Plz </br><code>check API: https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListMaritalStatus</code>
@apiParam       (Body:)     {String}          [birthday]                    Ngày sinh . %Y-%m-%d

@apiParam       (Body:)     {Int}             [budget_low_threshold]        Mức tiêp dùng ngưỡng thấp
@apiParam       (Body:)     {Int}             [budget_high_threshold]       Mức tiêu dùng ngưỡng cao
@apiParam       (Body:)     {Int}             [income_low_threshold]        Thu nhập ngưỡng thấp
@apiParam       (Body:)     {Int}             [income_high_threshold]       Thu nhập ngưỡng cao

@apiParam       (Body:)     {Int}             [salary]                      Lương 
@apiParam       (Body:)     {Int}             [income_type]                 Loại thu nhập. Allowed values: </br>
<li><code>1: ChuyenKhoan</code></li>
<li><code>2: TienMat</code></li>
<li><code>3: TuDo</code></li>

@apiParam       (Body:)     {String}          [created_time]                Thời gian khởi tạo tài khoản ('%Y-%m-%dT%TZ')
@apiParam       (Body:)     {String}          [updated_time]                Thời gian update thông tin tài khoản ('%Y-%m-%dT%TZ')
@apiParam       (Body:)     {Array}           [hobby]                       Danh sách sở thích khách hàng. Plz check API: https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListHobby


@apiSuccess       {String}        merchant_id                   UUID Merchant ID của khách hàng 
@apiSuccess       {String}        profile_id                    UUID Profile ID của khách hàng
@apiSuccess       {Array}         phone_number                  Tập các số điện thoại của khách hàng
@apiSuccess       {Array}         email                         Tập các địa chỉ email của khách hàng      
@apiSuccess       {String}        name                          Tên khách hàng
@apiSuccess       {String}        people_id                     CMND của khách hàng
@apiSuccess       {Array}         social_user                   Thông tin social của khách hàng

@apiParamExample    {json}      Body example:
{
  "merchant_id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "profile_id": "andrew",
  "phone_number": ["+***********", "+***********"],
  "email": "<EMAIL>",
  "name": "Ngụ Thị Quỳnh Phương",
  "people_id": "*********",
  "social_user": [
    {
      "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
      "social_type": 1,
      "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
    }
  ],
  "created_account_type": 2,
  "avatar": "https://storage.googleapis.com/test_xloyalty1/images/comments/00001d10-86fe-11e7-94df-0242ac16000c?*************",
  "gender": 3,
  "address": "Thành phố Hồ Chí Minh"
}

@apiSuccess       {Int}           created_account_type          Nguồn khởi tạo tài khoản khách hàng. Allowed values: </br>
<li><code>-1: Other</code></li>
<li><code>0: WebCem</code></li>
<li><code>1: By_Phone_Number</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google_Plus</code></li>
<li><code>4: Landing_Page</code></li>
<li><code>5: Import_File</code></li>
<li><code>6: Zalo</code></li>
<li><code>7: Instagram</code></li>
<li><code>8: Mobile_App</code></li>
<li><code>9: Youtube</code></li>
<li><code>10: Zalo_campaign_09171217</code></li>
<li><code>11: Corena_captive_wifi</code></li>
<li><code>12: Call_center</code></li>

@apiSuccess       {String}          avatar                      Link avatar của khách hàng
@apiSuccess       {Int}             gender                      Giới tính của khách hàng. Allowed values: </br>
<li><code>1: Unknown</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nu</code></li>
@apiSuccess       {String}          address                     Địa chỉ khách hàng
@apiSuccess       {Object}          district_code               Code quận của khách hàng. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListDistrict</code> 
@apiSuccess       {Object}          province_code               Code tỉnh của khách hàng. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListProvinces</code>
@apiSuccess       {Object}          ward_code                   Code phường của khách hàng. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListWard</code>
@apiSuccess       {Object}          operation                   Lĩnh vực công tác của khách hàng. Plz check </br><code>API: https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListOperation</code>
@apiSuccess       {Object}          job                         Nghề nghiệp của khách hàng. plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListJob<</code>
@apiSuccess       {Object}          religiousness               Tôn giáo. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListReligiousness</code>
@apiSuccess       {Object}          nation                      Dân tộc. Plz check API: </br><code>https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListNation</code>
@apiSuccess       {Object}          marital_status              Tình trạng hôn nhân. Plz </br><code>check API: https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListMaritalStatus</code>
@apiSuccess       {String}          birthday                    Ngày sinh . %Y-%m-%d

@apiSuccess       {Int}             budget_low_threshold        Mức tiêp dùng ngưỡng thấp
@apiSuccess       {Int}             budget_high_threshold       Mức tiêu dùng ngưỡng cao
@apiSuccess       {Int}             income_low_threshold        Thu nhập ngưỡng thấp
@apiSuccess       {Int}             income_high_threshold       Thu nhập ngưỡng cao

@apiSuccess       {Int}             salary                      Lương 
@apiSuccess       {Int}             income_type                 Loại thu nhập. Allowed values: </br>
<li><code>1: ChuyenKhoan</code></li>
<li><code>2: TienMat</code></li>
<li><code>3: TuDo</code></li>

@apiSuccess       {String}          created_time                Thời gian khởi tạo tài khoản ('%Y-%m-%dT%TZ')
@apiSuccess       {String}          updated_time                Thời gian update thông tin tài khoản ('%Y-%m-%dT%TZ')
@apiSuccess       {Array}           hobby                       Danh sách sở thích khách hàng. Plz check API: https://dev.mobio.vn/docs/mobio-v2/#api-MobioSystemData-ListHobby

@apiSuccess (social_user)       {String}    id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Int}       social_type       Loại mạng xã hội. Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Youtube</code></li>
@apiSuccess (social_user)       {String}    access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "merchant_id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "profile_id": "andrew",
  "phone_number": ["+***********", "+***********"],
  "email": "<EMAIL>",
  "name": "Ngụ Thị Quỳnh Phương",
  "people_id": "*********",
  "social_user": [
    {
      "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
      "social_type": 1,
      "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
    }
  ],
  "created_account_type": 2,
  "avatar": "https://storage.googleapis.com/test_xloyalty1/images/comments/00001d10-86fe-11e7-94df-0242ac16000c?*************",
  "gender": 3,
  "address": "Thành phố Hồ Chí Minh",
  "district_code": {
    "id": 1,
    "name": "Thành phố Hà Nội"
  },
  "province_code": {
    "id": 8,
    "name": "TUYENQUANG"
  },
  "ward_code": {
    "id": 1,
    "name": "Phường Phúc Xá"
  },
  "operation": {
    "id": 1,
    "name": "An ninh - Bảo vệ"
  },
  "job": {
    "id": 1,
    "name": "Làm ruộng"
  },
  "religiousness": {
    "id": 1,
    "name": "Lương giáo"
  },
  "nation": {
    "id": 1,
    "name": "Kinh"
  },
  "marital_status": {
    "id": 2,
    "name": "Đã đính hôn"
  },
  "birthday": "1999-02-23",
  "budget_low_threshold": 1000,
  "budget_high_threshold": 1000,
  "income_low_threshold": 1000,
  "income_high_threshold": 1000,
  "salary": 1000,
  "income_type": 2,
  "created_time": '2018-10-10T10:19:06Z',
  "updated_time": '2018-10-10T10:19:06Z',
  "hobby": [
    {
      "id": 1,
      "name": "Công nghệ"
    },
    {
      "id": 1,
      "name": "Công nghệ"
    }
  ]
}
"""

******** v1.0.0
"""
@api {patch} /users/actions/verify Update thông tin user verify
@apiDescription API Lấy thông tin chi tiết 1 User. API lấy thông tin từ bảng Customer + Info + Social.
@apiGroup VerifyCustomer
@apiVersion 1.0.0
@apiName UpdateCustomerVerifyInfo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    id                                     ID for verify.

@apiParam   (Body:)     {String}    [display_name]                          Tên khách hàng
@apiParam   (Body:)     {String}    [phone_number]                          Số điện thoại của khách hàng. Bắt buộc khi <code>state=1</code> và <code>created_account_type=1</code>
@apiParam   (Body:)     {String}    [email]                                 Email của khách hàng.Bắt buộc khi <code>state=1</code> và <code>created_account_type=3</code>.
@apiParam   (Body:)     {file}      [avatar]                                Ảnh đại diện của khách hàng.
@apiParam   (Body:)     {String}    [full_name]                             Tên đầy đủ của khách hàng.
@apiParam   (Body:)     {String}    [phone_number2]                         Số điện thoại thứ 2 của khách hàng (nếu có).
@apiParam   (Body:)     {String}    [phone_number3]                         Số điện thoại thứ 3 của khách hàng (nếu có).
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]           Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [national_phone_code]                   Mã điện thoại quốc gia.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>yyyyMMdd</code>.
@apiParam   (Body:)     {Number}    [income_low_threshold]                  Ngưỡng thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                 Ngưỡng thu nhập cao.
@apiParam   (Body:)     {Number=1:VNĐ 2:USD}    [income_unit]               Loại tiền.
@apiParam   (Body:)     {Number}    [religiousness]                         Tôn giáo
@apiParam   (Body:)     {Number}    [nation]                                Dân tộc.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {String}    [hobby]                                 Sở thích.
@apiParam   (Body:)     {String}    [location]                              Vị trí.

@apiSuccess       {String}        id                          Id của user
@apiSuccess       {String}        display_name                Tên khách hàng
@apiSuccess       {String}        phone_number                Số điện thoại của khách hàng
@apiSuccess       {String}        email                       Email của khách hàng
@apiSuccess       {Number}        created_account_type        Kiểu tạo tài khoản <br/>
Allowed values:<br/>
<li><code>1: Số điện thoại</code></li>
<li><code>2: Facebook</code></li>
<li><code>3: Google Plus</code></li>
@apiSuccess       {Number}        status                      Trạng thái của tài khoản.<br/>
Allowed values:<br/>
<li><code>1: Enable.</code> Tài khoản còn hiệu lực</li>
<li><code>2: Disable</code> Tài khoản đã bị xoá</li>
@apiSuccess       {String}        avatar                      Đường dẫn ảnh đại diện của khách hàng
@apiUse created_time
@apiUse updated_time
@apiSuccess       {Number}        mpoint                      Số điểm mpoint hiện tại của tài khoản
@apiSuccess       {Number}        state                       Tình trạng của khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng chính thức của hệ thống.</code></li>
<li><code>2: Khách hàng tiềm năng</code></li>
@apiSuccess       {Number}        level                       Mức độ đánh giá của hệ thống với khách hàng.<br/>
Allowed values:<br/>
<li><code>1: Khách hàng mới.</code></li>
<li><code>2: Khách hàng tích cực</code></li>
<li><code>3: Khách hàng trung thành</code></li>
<li><code>4: Khách hàng hạng vàng</code></li>
<li><code>5: Khách hàng thiên thần</code></li>
@apiSuccess       {Number}        verify_contact_info         Trạng thái đã xác thực số điện thoại hoặc email.<br/>
Allowed values:<br/>
<li><code>0: Chưa xác thực.</code></li>
<li><code>1: Đã xác thực phone</code></li>
<li><code>2: Đã xác thực email</code></li>
<li><code>3: Đã xác thực phone & email</code></li>
@apiSuccess       {String}        code_verify_contact_info     Mã xác thực tài khoản.
@apiSuccess       {String}        personal_code                Mã cá nhân của khách hàng.
@apiSuccess       {String}        full_name                    Tên đầy đủ của khách hàng.
@apiSuccess       {String}        phone_number2                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {String}        phone_number3                Số điện thoại thứ 2 của khách hàng
@apiSuccess       {Number}        gender                       Giới tính
@apiSuccess       {String}        address                      Địa chỉ
@apiSuccess       {String}        national_phone_code          Mã điện thoại quốc gia
@apiSuccess       {String}        province_code                Mã tỉnh thành
@apiSuccess       {String}        district_code                Mã quận huyện
@apiSuccess       {String}        ward_code                   Mã phường xã
@apiSuccess       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess       {String}        birthday                     Ngày sinh
@apiSuccess       {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess       {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess       {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess       {Number}        religiousness                Tôn giáo
@apiSuccess       {Number}        nation                       Dân tộc.
@apiSuccess       {Number}        job                          Nghề nghiệp.
@apiSuccess       {String}        hobby                        Sở thích.
@apiSuccess       {String}        location                     Vị trí.
@apiSuccess       {Object}        social_user                  Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "display_name": "andrew",
  "phone_number": "+***********",
  "email": "<EMAIL>",
  "created_account_type": 2,
  "social_user": {
    "id_social": "5c68eb00-eaca-49a2-bd5f-c32be3d51078",
    "social_type": 1,
    "access_token": "EAAENpMUscdQBAPXUJUgUgK6ZANZBi0DUyum7Hmf4TJ7hROcjfbZCMPSWQH6lj0EbOYeT3QBrZBMuzv5h7abUH6i6oMHBC76mWuMKcVoE3Uc1YvCpZBD2yHBeGV79m5HyZAFre95l4ZBXAXUdNqPFHfqe8nVrf9ZB5h3PIuAZBP7eGyAZDZD"
  },
  "status": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "mpoint": 0,
  "state": 1,
  "level": 1,
  "verify_contact_info": 0,
  "code_verify_contact_info": "0678",
  "personal_code": "ZAGEJHN",
  "full_name": "Andrew Nguyễn",
  "phone_number2": "",
  "phone_number3": "",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "national_phone_code": "84",
  "province_code": "HANOI",
  "district_code": "CAUGIAY",
  "ward_code": "",
  "marital_status": 1,
  "birthday": "********",
  "income_low_threshold": 0,
  "income_high_threshold": 0,
  "income_unit": 1,
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "hobby": "",
  "location": ""
}
"""


************************************************************************************************************
*************************************** API Get redirect url ***********************************************
************************************************************************************************************
"""
@api {get} /users/actions/support User Need support
@apiDescription API Lưu thông tin user cần hỗ trợ vào DB và trả về redirect link.
@apiGroup VerifyCustomer
@apiVersion 1.0.0
@apiName UserNeedSupport

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)     {String}    id                             ID for .

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "redirect_link": "https://vpbank.com.vn"
}
"""
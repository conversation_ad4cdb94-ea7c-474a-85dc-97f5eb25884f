************************ <PERSON><PERSON><PERSON><PERSON> đồ trạng thái cảm xúc ***************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {get} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/reports/daily_sentiment [Done] Daily sentiment
@apiDescription Dịch vụ lấy chi tiết trạng thái cảm xúc của khách hàng ở các thời điểm khách hàng tương tác với hệ thống.
@apiGroup Customers
@apiVersion 1.0.0
@apiName DailySentiment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse new_since_until_query
@apiUse business_case_query

@apiParam   (Query:)  {String}  [customer_id]   Định danh của khách hàng. Nếu không gửi thì lấy toàn hệ thống.
@apiParam   (Query:)    {String[]}    metrics   Danh sách các field cần lấy dữ liệu thống kê.<br/>
<li><code>interact_source</code>: Kênh phát sinh tương tác</li>

@apiSuccess       {Array}        data                    Mảng dữ liệu trạng thái cảm xúc.

@apiSuccess (data)       {String}        name                    Tên kiểu thống kê. Trả về theo metrics gửi lên.
@apiSuccess (data)       {Array}         values                  Mảng dữ liệu thống kê.

@apiSuccess (values)       {Number}        value                  Giá trị thống kê. Miền giá trị từ 1-5. Với <code>1</code> là tiêu cực, <code>3</code> là trung lập, <code>5</code> là tích cực
@apiSuccess (values)       {Number}        type                   Kênh tương tác.<br/><br/>
Allowed values:<br/>
<li><code>1: Mạng xã hội</code></li>
<li><code>2: Website</code></li>
<li><code>3: Chi nhánh</code></li>
<li><code>4: Khác</code></li>
@apiSuccess (values)       {Datetime}        end_time                   Thời điểm tương tác. Chính xác đến <code>giây</code>.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "name":"interact_source",
      "values":[
        {
          "value": 2.6,
          "type": 1
          "end_time": "2018-07-25T04:57:35Z"
        },
        {
          "value": 3.6,
          "type": 2
          "end_time": "2018-07-25T04:59:35Z"
        },
        {
          "value": 3.6,
          "type": 2
          "end_time": "2018-07-24T04:57:35Z"
        },
        {
          "value": 0.5,
          "type": 3
          "end_time": "2018-07-23T04:57:35Z"
        },
        {
          "value": 0.5,
          "type": 3
          "end_time": "2018-07-22T04:57:35Z"
        }
      ]
    }
}
"""


********************************* Huỷ thẻ *************************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} https://dev.mobio.vn/crm/api/v2.0/merchants/<merchant_id>/cards/<card_id>/actions/delete [Done] Huỷ thẻ thành viên
@apiDescription Dịch vụ để nhãn hàng từ chối tạo hoặc xoá thẻ thành viên của một khách hàng.
@apiGroup Customers
@apiVersion 1.0.0
@apiName DeleteCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParamExample  {json}  Body example:
{
  "reason": "Khách hàng không đủ điều kiện"
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "id": "f72c41c3-1532-4887-9043-b4319bb5fd8b"
}
"""

****************************** List Customer **********************************
* version: 1.0.3                                                              *
* version: 1.0.2                                                              *
* version: 1.0.1                                                              *
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.3
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=1:Pending 2:Approval 3:Cancelled}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": "['<EMAIL>', '<EMAIL>']",
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": "+************",
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "****************",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ]
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
*********
"""
@api {post} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.2
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=1:Pending 2:Approval 3:Cancelled}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": "['<EMAIL>', '<EMAIL>']",
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": "+************",
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "****************",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ]
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
*********
"""
@api {post} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.1
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=1:Pending 2:Approval 3:Cancelled}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": "['<EMAIL>', '<EMAIL>']",
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": "+************",
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "****************",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ]
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
************

"""
@api {post} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.0
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": "['<EMAIL>', '<EMAIL>']",
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": "+************",
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "****************",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""

*************************** Estimate Customer *********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/customers/actions/estimate [Done] Estimate số lượng khách hàng
@apiDescription Dịch vụ estimate số lượng khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.0
@apiName CustomerEstimate

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Number}                                  estimate              Số lượng customer estimate.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
  "estimate": 5000
}
"""

*************************** Create Customer ***********************************
* version: 1.0.0                                                              *
* version: 1.0.1                                                              *
* version: 1.0.2                                                              *
*******************************************************************************

"""
@api {post} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/customers/actions/create [Done] Tạo khách hàng
@apiDescription API Tạo khách hàng.
@apiGroup Customers
@apiVersion 1.0.2
@apiName CreateCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam   (Body:)     {Object}    customer                            Thông tin khách hàng
@apiParam   (Body:)     {Object}    cards                               Thông tin thẻ khách hàng.

@apiParam   (customer:)     {String}    name                            Tên khách hàng
@apiParam   (customer:)     {String}    phone_number_1                          Số điện thoại của khách hàng.
@apiParam   (customer:)     {String}    email                                   Email của khách hàng.
@apiParam   (customer:)     {String}    [phone_number_2]                        Số điện thoại 2 của khách hàng.
@apiParam   (customer:)     {String}    [email_2]                               Email 2 của khách hàng.
@apiParam   (customer:)     {Number=1:Unknown 2:Nam 3:Nữ}    gender             Giới tính.
@apiParam   (customer:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (customer:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (customer:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (customer:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (customer:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (customer:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (customer:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (customer:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (customer:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (customer:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (customer:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (customer:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (customer:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.
@apiParam   (customer:)     {Object}    social_user                             Thông tin người dùng trên mạng xã hội

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.

@apiParam   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiParam   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube 

@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    cards        Dữ liệu thẻ khách hàng

@apiSuccess (customer:)     {String}     id                               ID khách hàng
@apiSuccess   (customer:)     {String}      name                            Tên khách hàng
@apiSuccess   (customer:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (customer:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (customer:)     {Number}    gender                            Giới tính
@apiSuccess   (customer:)     {String}    address                           Địa chỉ
@apiSuccess   (customer:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (customer:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (customer:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (customer:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (customer:)     {String}    birthday                          Ngày sinh
@apiSuccess   (customer:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (customer:)     {Number}    income                            Mức thu nhập
@apiSuccess   (customer:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess   (customer:)     {Object}    social_user                             Thông tin người dùng trên mạng xã hội

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccess   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiSuccess   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube 

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "customer": {
    "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
    "name": "andrew",
    "phone_number": ["+***********", "+***********"],
    "email": ["<EMAIL>", "<EMAIL>"],
    "created_account_type": 1,
    "avatar": "",
    "created_time": "2017-12-12T15:12:28Z",
    "updated_time": "2017-12-12T15:12:28Z",
    "gender": 2,
    "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
    "province_code": 1,
    "district_code": 1,
    "ward_code": 1,
    "marital_status": 1,
    "birthday": "1989-09-17",
    "religiousness": 1,
    "nation": 1,
    "job": 39,
    "operation": null,
    "hobby": "Giày dép;Phim;Chạy bộ",
    "operation": null,
    "income": null,
    "budget": null,
    "social_user": {
      "id_social": "",
      "social_type": 4
    }
  },
  "cards": [{
      "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
      "code": "124364",
      "status": 1
    }]
}
"""

*********

"""
@api {post} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/customers/actions/create [Done] Tạo khách hàng
@apiDescription API Tạo khách hàng.
@apiGroup Customers
@apiVersion 1.0.1
@apiName CreateCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam   (Body:)     {Object}    customer                            Thông tin khách hàng
@apiParam   (Body:)     {Object}    cards                               Thông tin thẻ khách hàng.

@apiParam   (customer:)     {String}    name                            Tên khách hàng
@apiParam   (customer:)     {String}    phone_number_1                          Số điện thoại của khách hàng.
@apiParam   (customer:)     {String}    email                                   Email của khách hàng.
@apiParam   (customer:)     {String}    [phone_number_2]                        Số điện thoại 2 của khách hàng.
@apiParam   (customer:)     {String}    [email_2]                               Email 2 của khách hàng.
@apiParam   (customer:)     {Number=1:Unknown 2:Nam 3:Nữ}    gender             Giới tính.
@apiParam   (customer:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (customer:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (customer:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (customer:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (customer:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (customer:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (customer:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (customer:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (customer:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (customer:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (customer:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (customer:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (customer:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.

@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    cards        Dữ liệu thẻ khách hàng

@apiSuccess (customer:)     {String}     id                               ID khách hàng
@apiSuccess   (customer:)     {String}      name                            Tên khách hàng
@apiSuccess   (customer:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (customer:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (customer:)     {Number}    gender                            Giới tính
@apiSuccess   (customer:)     {String}    address                           Địa chỉ
@apiSuccess   (customer:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (customer:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (customer:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (customer:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (customer:)     {String}    birthday                          Ngày sinh
@apiSuccess   (customer:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (customer:)     {Number}    income                            Mức thu nhập
@apiSuccess   (customer:)     {Number}    budget                            Mức tiêu dùng

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "customer": {
    "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
    "name": "andrew",
    "phone_number": ["+***********", "+***********"],
    "email": ["<EMAIL>", "<EMAIL>"],
    "created_account_type": 1,
    "avatar": "",
    "created_time": "2017-12-12T15:12:28Z",
    "updated_time": "2017-12-12T15:12:28Z",
    "gender": 2,
    "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
    "province_code": 1,
    "district_code": 1,
    "ward_code": 1,
    "marital_status": 1,
    "birthday": "1989-09-17",
    "religiousness": 1,
    "nation": 1,
    "job": 39,
    "operation": null,
    "hobby": "Giày dép;Phim;Chạy bộ",
    "operation": null,
    "income": null,
    "budget": null
    },
  "cards": [{
      "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
      "code": "124364",
      "status": 1
    }]
}
"""

*********


"""
@api {post} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/customers/actions/create [Done] Tạo khách hàng
@apiDescription API Tạo khách hàng.
@apiGroup Customers
@apiVersion 1.0.0
@apiName CreateCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam   (Body:)     {Object}    customer                            Thông tin khách hàng
@apiParam   (Body:)     {Object}    cards                               Thông tin thẻ khách hàng.

@apiParam   (customer:)     {String}    name                            Tên khách hàng
@apiParam   (customer:)     {String}    phone_number_1                          Số điện thoại của khách hàng.
@apiParam   (customer:)     {String}    email                                   Email của khách hàng.
@apiParam   (customer:)     {String}    [phone_number_2]                        Số điện thoại 2 của khách hàng.
@apiParam   (customer:)     {String}    [email_2]                               Email 2 của khách hàng.
@apiParam   (customer:)     {Number=1:Unknown 2:Nam 3:Nữ}    gender             Giới tính.
@apiParam   (customer:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (customer:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (customer:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (customer:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (customer:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (customer:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (customer:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (customer:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (customer:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (customer:)     {String}    [income]                                Mức thu nhập hàng tháng.
@apiParam   (customer:)     {String}    [budget]                                Mức tiêu dùng hàng tháng.

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.

@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    cards        Dữ liệu thẻ khách hàng

@apiSuccess (customer:)     {String}     id                               ID khách hàng
@apiSuccess   (customer:)     {String}      name                            Tên khách hàng
@apiSuccess   (customer:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (customer:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (customer:)     {Number}    gender                            Giới tính
@apiSuccess   (customer:)     {String}    address                           Địa chỉ
@apiSuccess   (customer:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (customer:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (customer:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (customer:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (customer:)     {String}    birthday                          Ngày sinh
@apiSuccess   (customer:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (customer:)     {Number}    income                            Mức thu nhập
@apiSuccess   (customer:)     {Number}    budget                            Mức tiêu dùng

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "customer": {
    "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
    "name": "andrew",
    "phone_number": ["+***********", "+***********"],
    "email": ["<EMAIL>", "<EMAIL>"],
    "created_account_type": 1,
    "avatar": "",
    "created_time": "2017-12-12T15:12:28Z",
    "updated_time": "2017-12-12T15:12:28Z",
    "gender": 2,
    "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
    "province_code": 1,
    "district_code": 1,
    "ward_code": 1,
    "marital_status": 1,
    "birthday": "1989-09-17",
    "religiousness": 1,
    "nation": 1,
    "job": 39,
    "operation": null,
    "hobby": "Giày dép;Phim;Chạy bộ",
    "operation": null,
    "income": null,
    "budget": null
    },
  "cards": [{
      "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
      "code": "124364",
      "status": 1
    }]
}
"""


*************************** Update Customer ***********************************
* version: 1.0.0                                                              *
* version: 1.0.1                                                              *
*******************************************************************************
"""
@api {patch} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/customers/actions/update/<customer_id> [Done] Cập nhật khách hàng
@apiDescription API update thông tin khách hàng.
@apiGroup Customers
@apiVersion 1.0.0
@apiName UpdateCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.
@apiParam      (Resource:)    {String}    customer_id                             ID của khách hàng.

@apiParam   (Body:)     {Object}    [customer]                            Thông tin khách hàng
@apiParam   (Body:)     {Object}    [cards]                               Thông tin thẻ khách hàng.

@apiParam   (customer:)     {String}    [name]                            Tên khách hàng
@apiParam   (customer:)     {String}    [phone_number_1]                          Số điện thoại của khách hàng.
@apiParam   (customer:)     {String}    [email]                                   Email của khách hàng.
@apiParam   (customer:)     {String}    [phone_number_2]                        Số điện thoại 2 của khách hàng.
@apiParam   (customer:)     {String}    [email_2]                               Email 2 của khách hàng.
@apiParam   (customer:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (customer:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (customer:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (customer:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (customer:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (customer:)     {Number}    [marital_status]                        Tình trạng hôn nhân
@apiParam   (customer:)     {String}    [tags]                        Nhãn gợi ý tìm kiếm. Format: "tags1;tags2"
@apiParam   (customer:)     {String}    [company]                        Công ty.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (customer:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (customer:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (customer:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (customer:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (customer:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (customer:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (customer:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (customer:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.

@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng
@apiSuccess {Array}    cards        Dữ liệu thẻ khách hàng

@apiSuccess (customer:)     {String}     id                               ID khách hàng
@apiSuccess   (customer:)     {String}      name                            Tên khách hàng
@apiSuccess   (customer:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (customer:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (customer:)     {Number}    gender                            Giới tính
@apiSuccess   (customer:)     {String}    address                           Địa chỉ
@apiSuccess   (customer:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (customer:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (customer:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (customer:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (customer:)     {String}    birthday                          Ngày sinh
@apiSuccess   (customer:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (customer:)     {Number}    income                            Mức thu nhập
@apiSuccess   (customer:)     {Number}    budget                            Mức tiêu dùng

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     user_card_id                     Id khách hàng thẻ
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "customer": {
    "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
    "name": "andrew",
    "phone_number": ["+***********", "+***********"],
    "email": ["<EMAIL>", "<EMAIL>"],
    "created_account_type": 1,
    "avatar": "",
    "created_time": "2017-12-12T15:12:28Z",
    "updated_time": "2017-12-12T15:12:28Z",
    "gender": 2,
    "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
    "province_code": 1,
    "district_code": 1,
    "ward_code": 1,
    "marital_status": 1,
    "birthday": "1989-09-17",
    "religiousness": 1,
    "nation": 1,
    "job": 39,
    "operation": null,
    "hobby": "Giày dép;Phim;Chạy bộ",
    "operation": null,
    "income": null,
    "budget": null
    },
  "cards": [{
      "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
      "code": "124364",
      "status": 1,
      "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
    }]
}
"""


******************

"""
@api {patch} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/customers/actions/update/<customer_id> [Done] Cập nhật khách hàng
@apiDescription API update thông tin khách hàng.
@apiGroup Customers
@apiVersion 1.0.0
@apiName UpdateCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.
@apiParam      (Resource:)    {String}    customer_id                             ID của khách hàng.

@apiParam   (Body:)     {Object}    [customer]                            Thông tin khách hàng
@apiParam   (Body:)     {Object}    [cards]                               Thông tin thẻ khách hàng.

@apiParam   (customer:)     {String}    [name]                            Tên khách hàng
@apiParam   (customer:)     {String}    [phone_number_1]                          Số điện thoại của khách hàng.
@apiParam   (customer:)     {String}    [email]                                   Email của khách hàng.
@apiParam   (customer:)     {String}    [phone_number_2]                        Số điện thoại 2 của khách hàng.
@apiParam   (customer:)     {String}    [email_2]                               Email 2 của khách hàng.
@apiParam   (customer:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (customer:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (customer:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (customer:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (customer:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (customer:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (customer:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (customer:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (customer:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (customer:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (customer:)     {String}    [income]                                Mức thu nhập hàng tháng.
@apiParam   (customer:)     {String}    [budget]                                Mức tiêu dùng hàng tháng.

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.

@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng
@apiSuccess {Array}    cards        Dữ liệu thẻ khách hàng

@apiSuccess (customer:)     {String}     id                               ID khách hàng
@apiSuccess   (customer:)     {String}      name                            Tên khách hàng
@apiSuccess   (customer:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (customer:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (customer:)     {Number}    gender                            Giới tính
@apiSuccess   (customer:)     {String}    address                           Địa chỉ
@apiSuccess   (customer:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (customer:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (customer:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (customer:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (customer:)     {String}    birthday                          Ngày sinh
@apiSuccess   (customer:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (customer:)     {Number}    income                            Mức thu nhập
@apiSuccess   (customer:)     {Number}    budget                            Mức tiêu dùng

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     user_card_id                     Id khách hàng thẻ
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "customer": {
    "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
    "name": "andrew",
    "phone_number": ["+***********", "+***********"],
    "email": ["<EMAIL>", "<EMAIL>"],
    "created_account_type": 1,
    "avatar": "",
    "created_time": "2017-12-12T15:12:28Z",
    "updated_time": "2017-12-12T15:12:28Z",
    "gender": 2,
    "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
    "province_code": 1,
    "district_code": 1,
    "ward_code": 1,
    "marital_status": 1,
    "birthday": "1989-09-17",
    "religiousness": 1,
    "nation": 1,
    "job": 39,
    "operation": null,
    "hobby": "Giày dép;Phim;Chạy bộ",
    "operation": null,
    "income": null,
    "budget": null
    },
  "cards": [{
      "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
      "code": "124364",
      "status": 1,
      "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
    }]
}
"""



************************************************************************************************************
********************************** API Get Detail User *****************************************************
************************************************************************************************************


"""
@api {get} https://dev.mobio.vn/profiling/v3.0/merchants/<merchant_id>/customers/<customer_id>/info [Done] Lấy thông tin chi tiết User
@apiDescription API Lấy thông tin chi tiết 1 User. Bao gồm thông tin user và thẻ của user
@apiGroup Customers
@apiVersion 1.0.2
@apiName GetDetailCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng

@apiSuccess   (customer:)     {String}     id                               ID khách hàng
@apiSuccess   (customer:)     {String}    name                              Tên khách hàng
@apiSuccess   (customer:)     {Array}     phone_number                      Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Number}    created_account_type              Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (customer:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (customer:)     {Number}    gender                            Giới tính
@apiSuccess   (customer:)     {String}    address                           Địa chỉ
@apiSuccess   (customer:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (customer:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (customer:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (customer:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (customer:)     {String}    birthday                          Ngày sinh
@apiSuccess   (customer:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (customer:)     {Number}    income                            Mức thu nhập
@apiSuccess   (customer:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess   (customer:)     {DateTime}    created_time                      Thời gian tạo profile
@apiSuccess   (customer:)     {DateTime}    updated_time                      Thời gian cập nhật profile
@apiSuccess   (customer:)     {Array}     cards                             Dữ liệu thẻ khách hàng

@apiSuccess   (customer:cards:)     {String}     user_card_id                     Id khách hàng thẻ
@apiSuccess   (customer:cards:)     {String}     id                               Id thẻ
@apiSuccess   (customer:cards:)     {String}     code                             Mã thẻ
@apiSuccess   (customer:cards:)     {Number}     status                           Trạng thái thẻ
@apiSuccess   (customer:cards:)     {DateTime}   approved_time                    Thời điểm duyệt thẻ

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "customer": {
        "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
        "name": "andrew",
        "phone_number": ["+***********", "+***********"],
        "email": ["<EMAIL>", "<EMAIL>"],
        "created_account_type": 1,
        "avatar": "",
        "created_time": "2017-12-12T15:12:28Z",
        "updated_time": "2017-12-12T15:12:28Z",
        "gender": 2,
        "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
        "province_code": 1,
        "district_code": 1,
        "ward_code": 1,
        "marital_status": 1,
        "birthday": "1989-09-17",
        "religiousness": 1,
        "nation": 1,
        "job": 39,
        "operation": null,
        "hobby": "Giày dép;Phim;Chạy bộ",
        "operation": null,
        "income": null,
        "budget": null,
        "cards": [{
              "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
              "code": "124364",
              "status": 1,
              "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304",
              "approved_time": "2017-12-12T15:12:28Z"
        }]
    }
}
"""

****************

"""
@api {get} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/customers/<customer_id>/info [Done] Lấy thông tin chi tiết User
@apiDescription API Lấy thông tin chi tiết 1 User. Bao gồm thông tin user và thẻ của user
@apiGroup Customers
@apiVersion 1.0.1
@apiName GetDetailCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng
@apiSuccess {Array}    cards        Dữ liệu thẻ khách hàng

@apiSuccess (customer:)     {String}     id                               ID khách hàng
@apiSuccess   (customer:)     {String}      name                            Tên khách hàng
@apiSuccess   (customer:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (customer:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (customer:)     {Number}    gender                            Giới tính
@apiSuccess   (customer:)     {String}    address                           Địa chỉ
@apiSuccess   (customer:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (customer:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (customer:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (customer:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (customer:)     {String}    birthday                          Ngày sinh
@apiSuccess   (customer:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (customer:)     {Number}    income                            Mức thu nhập
@apiSuccess   (customer:)     {Number}    budget                            Mức tiêu dùng

@apiSuccess   (cards:)     {String}     user_card_id                     Id khách hàng thẻ
@apiSuccess   (cards:)     {String}     id                               Id thẻ
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ
@apiSuccess   (cards:)     {DateTime}   approved_time                    Thời điểm duyệt thẻ

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "customer": {
    "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
    "name": "andrew",
    "phone_number": ["+***********", "+***********"],
    "email": ["<EMAIL>", "<EMAIL>"],
    "created_account_type": 1,
    "avatar": "",
    "created_time": "2017-12-12T15:12:28Z",
    "updated_time": "2017-12-12T15:12:28Z",
    "gender": 2,
    "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
    "province_code": 1,
    "district_code": 1,
    "ward_code": 1,
    "marital_status": 1,
    "birthday": "1989-09-17",
    "religiousness": 1,
    "nation": 1,
    "job": 39,
    "operation": null,
    "hobby": "Giày dép;Phim;Chạy bộ",
    "operation": null,
    "income": null,
    "budget": null
    },
  "cards": [{
      "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
      "code": "124364",
      "status": 1,
      "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304",
      "approved_time": "2017-12-12T15:12:28Z"
    }]
}
"""

****************

"""
@api {get} https://dev.mobio.vn/profiling/v2.0/merchants/<merchant_id>/customers/<customer_id>/info [Done] Lấy thông tin chi tiết User
@apiDescription API Lấy thông tin chi tiết 1 User. Bao gồm thông tin user và thẻ của user
@apiGroup Customers
@apiVersion 1.0.0
@apiName GetDetailCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng
@apiSuccess {Array}    cards        Dữ liệu thẻ khách hàng

@apiSuccess (customer:)     {String}     id                               ID khách hàng
@apiSuccess   (customer:)     {String}      name                            Tên khách hàng
@apiSuccess   (customer:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (customer:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (customer:)     {Number}    gender                            Giới tính
@apiSuccess   (customer:)     {String}    address                           Địa chỉ
@apiSuccess   (customer:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (customer:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (customer:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (customer:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (customer:)     {String}    birthday                          Ngày sinh
@apiSuccess   (customer:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (customer:)     {Number}    income                            Mức thu nhập
@apiSuccess   (customer:)     {Number}    budget                            Mức tiêu dùng

@apiSuccess   (cards:)     {String}     user_card_id                     Id khách hàng thẻ
@apiSuccess   (cards:)     {String}     id                               Id thẻ
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "customer": {
    "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
    "name": "andrew",
    "phone_number": ["+***********", "+***********"],
    "email": ["<EMAIL>", "<EMAIL>"],
    "created_account_type": 1,
    "avatar": "",
    "created_time": "2017-12-12T15:12:28Z",
    "updated_time": "2017-12-12T15:12:28Z",
    "gender": 2,
    "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
    "province_code": 1,
    "district_code": 1,
    "ward_code": 1,
    "marital_status": 1,
    "birthday": "1989-09-17",
    "religiousness": 1,
    "nation": 1,
    "job": 39,
    "operation": null,
    "hobby": "Giày dép;Phim;Chạy bộ",
    "operation": null,
    "income": null,
    "budget": null
    },
  "cards": [{
      "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
      "code": "124364",
      "status": 1,
      "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
    }]
}
"""


************************************************************************************************************
******************************* API Search user by social **************************************************
************************************************************************************************************
"""
@api {get} https://dev.mobio.vn/profiling/v2.0/search-users [Done] Tìm kiếm khách hàng.
@apiDescription Tìm kiếm khách hàng của một nhãn hàng. Nếu có đủ các tham số, thứ tự ưu tiên sẽ lần lượt search từ trên xuống:
<li>1. Nếu tìm kiếm theo <code>social</code> thì bắt buộc gửi tham số <code>social_type</code> và <code>social_id</code>.</li>
<li>2. Nếu tìm kiếm theo <code>chuỗi tìm kiếm</code> thì bắt buộc gửi tham số <code>query</code>. Tìm kiếm theo số điện thoại và email của khách hàng.</li>
@apiGroup Customers
@apiVersion 1.0.0
@apiName SearchCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    [query] Chuỗi tìm kiếm khách hàng theo email, số điện thoại
@apiParam   (Query:)    {String}    [social_id] ID của khách hàng trên mạng xã hội.
@apiParam   (Query:)    {Number=1-FACEBOOK;2-Zalo}    [social_type] Loại mạng xã hội. Bắt buộc khi search social.

@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng

@apiSuccess (customer:)     {String}     id                               ID khách hàng
@apiSuccess   (customer:)     {String}      name                            Tên khách hàng
@apiSuccess   (customer:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (customer:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (customer:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (customer:)     {Number}    gender                            Giới tính
@apiSuccess   (customer:)     {String}    address                           Địa chỉ
@apiSuccess   (customer:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (customer:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (customer:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (customer:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (customer:)     {String}    birthday                          Ngày sinh
@apiSuccess   (customer:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (customer:)     {Number}    income                            Mức thu nhập
@apiSuccess   (customer:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>

@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "customer": [{
    "id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
    "name": "andrew",
    "phone_number": ["+***********", "+***********"],
    "email": ["<EMAIL>", "<EMAIL>"],
    "created_account_type": 1,
    "avatar": "",
    "created_time": "2017-12-12T15:12:28Z",
    "updated_time": "2017-12-12T15:12:28Z",
    "gender": 2,
    "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
    "province_code": 1,
    "district_code": 1,
    "ward_code": 1,
    "marital_status": 1,
    "birthday": "1989-09-17",
    "religiousness": 1,
    "nation": 1,
    "job": 39,
    "operation": null,
    "hobby": "Giày dép;Phim;Chạy bộ",
    "operation": null,
    "income": null,
    "budget": null,
    "social_user": [
        {
          "id_social": "****************",
          "social_type": 1
        }
      ],
    },
    ...
  ]
}
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 19/08/2022
"""

# --------- <PERSON><PERSON><PERSON> danh sách comment của note -------------
"""
@api {POST} {domain}/note/api/v1.0/note/comment/action/filter               L<PERSON><PERSON> s<PERSON>ch comment của note
@apiGroup Comment
@apiVersion 1.0.0

@apiName ListCommentNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (BODY:) {string}                         object_id            <code>ID</code> định danh đối tượng                                        

@apiParamExample {json} Body example
{
    "object_id": "82f95839-5be5-4a1b-bf20-a1f3a14025e3"
}

@apiSuccess {String}            message                       <PERSON><PERSON> tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách các comment trong note.

@apiSuccess {String}            data.id                       <code>ID</code> định danh của Comment
@apiSuccess {String}            data.object_id                  <code>ID</code> định danh của đối tượng
@apiSuccess {String}            data.merchant_id              <code>ID</code> định danh của Merchant
@apiSuccess {String}            data.description              Nội dung của Comment                                                        
@apiSuccess {Array}             data.file_attachments         Danh sách file đính kèm của comment.                                                        
@apiSuccess {Array}             data.file_attachment_ids      Danh sách <code>ID</code> file đính kèm của comment.                                                        
@apiSuccess {String}            data.status                   Trạng thái của comment
                                                             <ul>
                                                                <li><code>created</code>: Tạo</li>
                                                                <li><code>updated</code>: Đã sửa</li>
                                                             </ul>                                                        
@apiSuccess {String}            data.created_by               Người tạo comment                                                        
@apiSuccess {String}            data.updated_by               Người cập nhật comment                                                        
@apiSuccess {String}            data.created_time             Thời gian tạo comment                                                        
@apiSuccess {String}            data.updated_time             Thời gian cập nhật comment                                                                                                                
@apiSuccess {String}            data.file_attachments         Danh sách file đính kèm của comment.                                                        
@apiSuccess {Array}             [data.mentions]                             Danh sách người được mentions
@apiSuccess {String}            [data.mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiSuccess {String}            [data.mentions.account_id]                  <code>ID</code> của người được mentions


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "id": "",
            "object_id": "",
            "merchant_id": "",
            "description": "",
            "file_attachments": [],
            "file_attachment_ids": [],
            "status": "created",
            "mentions": [],
            "created_by": "",
            "updated_by": "",
            "created_time": "",
            "updated_time": "",
        },
        {
            "id": "",
            "object_id": "",
            "merchant_id": "",
            "description": "",
            "file_attachments": [],
            "file_attachment_ids": [],
            "status": "created",
            "mentions": [],
            "created_by": "",
            "updated_by": "",
            "created_time": "",
            "updated_time": "",
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
}
"""
# --------- Thêm comment của note -------------
"""
@api {POST} {domain}/note/api/v1.0/note/comment               Tạo comment cho note
@apiGroup Comment
@apiVersion 1.0.0

@apiName AddComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
                               
@apiParam   (BODY:) {String}            object_id                  <code>ID</code> định danh của đối tượng
@apiParam   (BODY:) {String}            description              Nội dung của Comment                                                                                                           
@apiParam   (BODY:) {Array}             file_attachment_ids      Danh sách <code>ID</code> file đính kèm                                                                                                                                                                                                                                                                           
@apiParam   (BODY:) {Array}             [mentions]                             Danh sách người được mentions
@apiParam   (BODY:) {String}            [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiParam   (BODY:) {String}            [mentions.account_id]                  <code>ID</code> của người được mentions

@apiParamExample {json} Body example
{
    "object_id": "",
    "description": "",
    "mentions": [
        {
            "fe_id": "",
            "account_id": ""
        }
    ],
    "file_attachment_ids": [],
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}             data                         Chi tiết comment

@apiSuccess (data)  {String}            id                       <code>ID</code> định danh của Comment
@apiSuccess (data)  {String}            object_id                  <code>ID</code> định danh của Đối tượng
@apiSuccess (data)  {String}            merchant_id              <code>ID</code> định danh của Merchant
@apiSuccess (data)  {String}            description              Nội dung của Comment                                                        
@apiSuccess (data)  {Array}             file_attachments         Danh sách file đính kèm của comment.                                                        
@apiSuccess (data)  {Array}             file_attachment_ids      Danh sách <code>ID</code> file đính kèm                                                        
@apiSuccess (data)  {String}            status                   Trạng thái của comment
                                                                 <ul>
                                                                    <li><code>created</code>: Tạo</li>
                                                                    <li><code>updated</code>: Đã sửa</li>
                                                                 </ul>                                                        
@apiSuccess (data)  {String}            created_by               Người tạo comment                                                        
@apiSuccess (data)  {String}            updated_by               Người cập nhật comment                                                        
@apiSuccess (data)  {String}            created_time             Thời gian tạo comment                                                        
@apiSuccess (data)  {String}            updated_time             Thời gian cập nhật comment                                                                                                                                                                
@apiSuccess (data)  {Array}             [mentions]                             Danh sách người được mentions
@apiSuccess (data)  {String}            [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiSuccess (data)  {String}            [mentions.account_id]                  <code>ID</code> của người được mentions


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": 
        {
            "id": "",
            "object_id": "",
            "merchant_id": "",
            "description": "",
            "file_attachments": [],
            "file_attachment_ids": [],
            "status": "created",
            "mentions": [],
            "created_by": "",
            "updated_by": "",
            "created_time": "",
            "updated_time": "",
        },
    "lang": "vi",
    "message": "request thành công.",
}
"""
# --------- Cập nhật comment của note -------------
"""
@api {PUT} {domain}/note/api/v1.0/note/comment/<comment_id>               Cập nhật comment
@apiGroup Comment
@apiVersion 1.0.0

@apiName UpdateComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}                         [description]            Nội dung của comment                                       
@apiParam   (BODY:) {string}                         [file_attachment_ids]    Danh sách <code>ID</code> file đính kèm                                       
@apiParam   (BODY:) {Array}                          [mentions]               Danh sách người được mentions                                  
@apiParam   (BODY:) {string}                         [mentions.fe_id]         <code>ID</code> của fe                                  
@apiParam   (BODY:) {string}                         [mentions.account_id] <code>Account          <code>ID</code> account                                  

@apiParamExample {json} Body example
{
    "description": "",
    "mentions": [],
    "file_attachment_ids": [],
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}             data                         Chi tiết comment

@apiSuccess (data)  {String}            id                       <code>ID</code> định danh của Comment
@apiSuccess (data)  {String}            object_id                  <code>ID</code> định danh của Đối tượng
@apiSuccess (data)  {String}            merchant_id              <code>ID</code> định danh của Merchant
@apiSuccess (data)  {String}            description              Nội dung của Comment                                                        
@apiSuccess (data)  {Array}             file_attachments         Danh sách file đính kèm của comment.                                                        
@apiSuccess (data)  {Array}             file_attachment_ids      Danh sách <code>ID</code> file đính kèm                                                        
@apiSuccess (data)  {String}            status                   Trạng thái của comment
                                                             <ul>
                                                                <li><code>created</code>: Tạo</li>
                                                                <li><code>updated</code>: Đã sửa</li>
                                                             </ul>                                                        
@apiSuccess (data)  {String}            created_by               Người tạo comment                                                        
@apiSuccess (data)  {String}            updated_by               Người cập nhật comment                                                        
@apiSuccess (data)  {String}            created_time             Thời gian tạo comment                                                        
@apiSuccess (data)  {String}            updated_time             Thời gian cập nhật comment                                                                                                                                                                
@apiSuccess (data)  {Array}             [mentions]                             Danh sách người được mentions
@apiSuccess (data)  {String}            [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiSuccess (data)  {String}            [mentions.account_id]                  <code>ID</code> của người được mentions


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": 
        {
            "id": "",
            "object_id": "",
            "merchant_id": "",
            "description": "",
            "file_attachments": [],
            "file_attachment_ids": [],
            "status": "created",
            "mentions": [],
            "created_by": "",
            "updated_by": "",
            "created_time": "",
            "updated_time": "",
        },
    "lang": "vi",
    "message": "request thành công.",
}
"""
# --------- Delete comment của note -------------
"""
@api {DELETE} {domain}/note/api/v1.0/note/comment               Xoá comment
@apiGroup Comment
@apiVersion 1.0.0

@apiName DeleteComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string}              ids          Danh sách <code>ID</code> của comment. Trong trường hợp nhiều thì sẽ ngăn cách bởi dấu ,                                      
                                  
                                 

@apiParamExample {json} Query example
{
    ?ids=18d389fc-210a-11ed-8d38-acde48001122,18d389fc-210a-11ed-8d38-acde48001123
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}             data                         Dữ liệu xoá comment

@apiSuccess {Int}             data.number_delete_success      Số comment xoá thành công
@apiSuccess {Int}             data.number_delete_fail      Số comment xoá thất bại



@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": 
        {
            "number_delete_success": 0,
            "number_delete_fail": 1
        },
    "lang": "vi",
    "message": "request thành công.",
}
"""

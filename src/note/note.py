#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TungDD
    Company: Mobio
    Date Created: 11/10/2021
"""
"""
@apiDefine ResponseDetailNoteNew

@apiSuccess {String}            data.id                       <code>ID</code> định danh của Note
@apiSuccess {String}            data.source                   Đ<PERSON><PERSON> danh nguồn tạo Note
@apiSuccess {String}            data.object_id                Định danh của phần tạo tác. Ví dụ ở SALE thì sẽ là định danh của đơn hàng.
@apiSuccess {String}            data.description              Nội dung của Note 
@apiSuccess {Object}            data.relate_to                Liên quan tới
@apiSuccess {String}            data.relate_to.company_ids    Danh sách các <code>ID</code> công ty liên quan tới
@apiSuccess {String}            data.relate_to.profile_ids    Danh sách các <code>ID</code> profile liên quan tới
@apiSuccess {String}            data.relate_to.order_ids       Danh sách các <code>ID</code> deal liên quan tới
@apiSuccess {String}            data.relate_to.ticket_ids     Danh sách các <code>ID</code> ticket liên quan tới
@apiSuccess {String}            data.information              Thông tin chung của Note
@apiSuccess {String}            data.information.type         Loại Note
@apiSuccess {String}            [data.information.profiles_contact]   Danh sách profile đã liên hệ
@apiSuccess {String}            [data.information.time_contact]       Thời gian liên hệ
@apiSuccess {String}            [data.information.result]             Kết quả cuộc gọi tương ứng với <code>type=CALL, MEETING</code>
@apiSuccess {Object}            data.relate_to                Liên quan tới
@apiSuccess {String}            data.relate_to.company_ids    Danh sách các <code>ID</code> công ty liên quan tới
@apiSuccess {String}            data.relate_to.profile_ids    Danh sách các <code>ID</code> profile liên quan tới
@apiSuccess {String}            data.relate_to.order_ids       Danh sách các <code>ID</code> deal liên quan tới
@apiSuccess {String}            data.relate_to.ticket_ids     Danh sách các <code>ID</code> ticket liên quan tới
@apiSuccess {String}            data.follow_task              Thông tin tạo công việc tiếp nối
@apiSuccess {String}            data.follow_task.status       Trạng thái tạo công việc tiếp nối
@apiSuccess {Int}               data.comment_count                             Số lượng comment có trong task
@apiSuccess {String}            [data.follow_task.time_success]       Thời gian hoàn thành của Task. Tương ứng khi <code>status=1</code>
@apiSuccess {Array}             [data.mentions]                             Danh sách người được mentions
@apiSuccess {String}            [data.mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiSuccess {String}            [data.mentions.account_id]                  <code>ID</code> của người được mentions
@apiSuccess {Array}             [data.description_attachment_ids]              Danh sách <code>ID</code> các file được đính kèm trong nội dung của Ghi chú
@apiSuccess {Array}               data.account_mentions   Danh sách người được mentions trong comment và nội dung note
@apiSuccess {String}            data.created_by      Được tạo bởi ai
@apiSuccess {String}            data.created_time    Thời gian tạo. Định dạng <code>%Y-%m-%dT%H:%MZ</code>
@apiSuccess {String}            data.updated_time    Thời gian cập nhật. Định dạng <code>%Y-%m-%dT%H:%MZ</code>
@apiSuccess {Boolean}            data.pin    Trạng thái ghi của note
                                            <ul>
                                                <li>true: Được ghim</li>
                                                <li>false: Không được ghim</li>
                                            </ul>

"""

"""
@apiDefine DetailNoteResponse

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                           Danh sách Note

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
            "source": "SALE",
            "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
            "description": "Nội dung ghi chú",
            "information": {
                "type": "GENERAL"
            },
            "related_to": {
                "company_ids": [],
                "ticket_ids": [],
                "profile_ids": [],
                "order_ids": []
            },
            "related_to_not_delete": {
                "company_ids": [],
                "ticket_ids": [],
                "profile_ids": [],
                "order_ids": []
            },
            "attachment_ids":[],
            "description_attachment_ids":[],
            "follow_task": {
                "status": 0
            },
            "mentions": [
                {
                    "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
                    "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
                }
            ],
            "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
            "created_time": "2021-10-03T08:10Z",
            "updated_time": "2021-10-03T08:10Z",
        }
}
"""

"""
@apiDefine GetListNoteBody

@apiParam   (BODY:)   {array}   account_mentions   Danh sách <code>ID</code> người được mentions
@apiParam   (BODY:)   {array}   brand_ids   Danh sách <code>ID</code> thương hiệu
@apiParam   (BODY:)   {array}   assignee_ids   Danh sách <code>ID</code> nhân viên tương tức
@apiParam   (BODY:)   {array}   type   Kiểu ghi chú
@apiParam   (BODY:)   {string}  source    Nguồn cần lấy Note
@apiParam   (BODY:)   {string}   object_id   Định danh thành phần cần lấy Note
@apiParam   (BODY:)   {string}   [start_time]  Thời gian bắt đầu tìm kiếm. Định dang: <code>%Y-%m-%dT%H:%MZ</code>
@apiParam   (BODY:)   {string}   [end_time]   Thời gian kết thúc tìm Kiếm. Định dang: <code>%Y-%m-%dT%H:%MZ</code>

@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "account_mentions": [],
    "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
    "type": [
        "GENERAL",
        "CALL",
        "EMAIL"
    ],
    "brand_ids": [],
    "assignee_ids": [],
    "start_time": "2021-11-01T01:52Z",
    "end_time": "2021-11-01T01:52Z"
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}            data                           Danh sách Note

"""

"""

@apiDefine GetListNoteResponse

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "source": "SALE",
            "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
            "description": "Nội dung ghi chú",
            "information": {
                "type": "GENERAL"
            },
            "related_to": {
                "company_ids": [],
                "ticket_ids": [],
                "profile_ids": [],
                "order_ids": []
            },
            "related_to_not_delete": {
                "company_ids": [],
                "ticket_ids": [],
                "profile_ids": [],
                "order_ids": []
            },
            "attachment_ids":[],
            "description_attachment_ids":[],
            "follow_task": {
                "status": 0
            },
            "mentions": [
                {
                    "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
                    "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
                }
            ],
            "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
            "created_time": "2021-10-03T08:10Z"
        }
    ]
}
"""

# --------------- Thêm note -----------------
"""
@api {POST} {domain}/note/api/v1.0/notes                    Thêm Notes
@apiGroup Note
@apiVersion 1.0.0
@apiName AddNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    description                         Nội dung của Note
@apiParam   (BODY:) {object}    information                         Thông tin chung của Note
@apiParam   (BODY:) {string=GENERAL,EMAIL,CALL, MEETING}    information.type                         Loại ghi chú
@apiParam   (BODY:) {Array}     attachment_ids                      Danh sách <code>ID</code> file đính kèm           
@apiParam   (BODY:) {String=SALE,PROFILE,TICKET,COMPANY}    source                              Nguồn tạo. Tức là đang đứng ở Module nào thì sẽ cần truyền vào Module đó. Ví dụ: <code>SALE</code>
@apiParam   (BODY:) {String}    object_id                           Định danh của thành phân cần lấy danh sách task. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {Object}    relate_to                           Liên quan tới phần nào.
@apiParam   (BODY:) {Array}    relate_to.company_ids                Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.ticket_ids                 Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.order_ids                   Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.profile_ids                Danh sách profile liên quan tới.


@apiParam   (BODY:) {Object}    relate_to_not_delete                           Liên quan tới phần chính nào.
@apiParam   (BODY:) {Array}    relate_to_not_delete.company_ids                Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.ticket_ids                 Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.order_ids                   Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.profile_ids                Danh sách profile liên quan tới.

@apiParam   (BODY:) {Object}   follow_task                          Tạo công việc tiếp nối.
@apiParam   (BODY:) {String}  follow_task.title                          Tiêu đề của công việc tiếp nối
@apiParam   (BODY:) {int}  follow_task.status                                   Trạng thái tạo công việc tiếp nối.
                                                                    <ul>
                                                                        <li><code>0:</code> không tự động tạo công việc tiếp nối</li>
                                                                        <li><code>1:</code> tự động tạo công việc tiếp nối</li>
                                                                    </ul>
@apiParam   (BODY:) {string}    follow_task.deadline                 Thời hạn hoàn thành của Task trong trường hợp <code>status:1</code>
@apiParam   (BODY:) {string}    follow_task.deadline.type            Loại thời hạn hoàn thành của công việc tiếp nối
                                                                        <ul>
                                                                            <li><code>time_slot</code>: Khung giờ</li>
                                                                            <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                        </ul>
@apiParam   (BODY:)  {string}   follow_task.deadline.start_time      Thời hạn hoàn thành công việc.
                                                                    <ul>
                                                                        <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                        <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                    </ul>
@apiParam   (BODY:)  {string}   follow_task.deadline.end_time        Thời hạn hoàn thành công việc.
                                                                    <ul>
                                                                        <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                        <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                    </ul>
@apiParam   (BODY:) {Array}    [mentions]                             Danh sách người được mentions
@apiParam   (BODY:) {String}   [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiParam   (BODY:) {String}   [mentions.account_id]                  <code>ID</code> của người được mentions
@apiParam   (BODY:) {String}   [description_attachment_ids]              Danh sách <code>ID</code> của file được đính kèm trong nội dung ghi chú.



@apiParam   (Information type: EMAIL)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: EMAIL)  {string}   time_contact   Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>  


@apiParam   (Information type: CALL)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: CALL)  {string}   time_contact       Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>
@apiParam   (Information type: CALL)  {string=HEARD,REFUSE_ANSWER,MISSED_CALL,BUSY,WRONG_PHONE_NUMBER,PROFILE_CARE,PROFILE_CALL_BACK,ALREADY_MADE_APPOINTMENT,NO_ALREADY_MADE_APPOINTMENT}   result     Kết quả cuộc gọi 
                                                                                                                    <ul>
                                                                                                                        <li><code>HEARD:</code> Profile đã nghe</li>
                                                                                                                        <li><code>REFUSE_ANSWER:</code> Profile từ chối nghe</li>
                                                                                                                        <li><code>MISSED_CALL:</code> Gọi nhỡ</li>
                                                                                                                        <li><code>BUSY:</code> Profile máy bận/không nghe máy/không liên lạc được</li>
                                                                                                                        <li><code>WRONG_PHONE_NUMBER:</code> Sai số điện thoại</li>
                                                                                                                        <li><code>PROFILE_CARE:</code> Profile quan tâm</li>
                                                                                                                        <li><code>PROFILE_CALL_BACK:</code> Profile hẹn gọi lại</li>
                                                                                                                        <li><code>ALREADY_MADE_APPOINTMENT:</code> Đặt hẹn được</li>
                                                                                                                        <li><code>NO_ALREADY_MADE_APPOINTMENT:</code> Không đặt hẹn được</li>
                                                                                                                    </ul>
                                                                                                                    
@apiParam   (Information type: MEETING)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: MEETING)  {string}   time_contact       Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>
@apiParam   (Information type: MEETING)  {string=MET,CHANGE_APPOINTMENT_TIME,PROFILES_NOT_COMING,MEETING_CANCELED,PROFILES_AGREE_COLLECT_RECORDS,PROFILES_NOT_AGREE_COLLECT_RECORDS }   result     Kết quả cuộc gặp 
                                                                                                                    <ul>
                                                                                                                        <li><code>MET:</code> Đã gặp</li>
                                                                                                                        <li><code>CHANGE_APPOINTMENT_TIME:</code> Đổi thời gian hẹn gặp</li>
                                                                                                                        <li><code>PROFILES_NOT_COMING:</code> Profiles không tới cuộc gặp</li>
                                                                                                                        <li><code>MEETING_CANCELED:</code> Cuộc gặp bị huỷ</li>
                                                                                                                        <li><code>PROFILES_AGREE_COLLECT_RECORDS:</code>Profiles đồng ý thu thập hồ sơ</li>
                                                                                                                        <li><code>PROFILES_NOT_AGREE_COLLECT_RECORDS:</code>Profiles không đồng ý thu thập hồ sơ</li>
                                                                                                                    </ul>


@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
    "description": "Nội dung ghi chú",
    "mentions": [
        {
            "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
            "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
        }
    ],
    "information": {
        "type": "GENERAL"
    },
    "related_to": {
        "company_ids": [],
        "ticket_ids": [],
        "profile_ids": [],
        "order_ids": []
    },
    "related_to_not_delete": {
        "company_ids": [],
        "ticket_ids": [],
        "profile_ids": [],
        "order_ids": []
    },
    "mentions": [
        {
            "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
            "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
        }
    ], 
    "attachment_ids":[],
    "description_attachment_ids":[],
    "follow_task": {
        "status": 0
    }
}                                       

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin file Upload

@apiUse ResponseDetailNoteNew

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "source": "SALE",
        "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
        "description": "Nội dung ghi chú",
        "information": {
            "type": "GENERAL"
        },
        "related_to": {
            "company_ids": [],
            "ticket_ids": [],
            "profile_ids": [],
            "order_ids": []
        },
        "related_to_not_delete": {
            "company_ids": [],
            "ticket_ids": [],
            "profile_ids": [],
            "order_ids": []
        },
        "attachment_ids":[],
        "description_attachment_ids":[],
        "follow_task": {
            "status": 0
        },
        "mentions": [
            {
                "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
                "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
            }
        ],
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""
# -----------------Lấy danh sách Note-------------------------
"""
@api {POST} {domain}/note/api/v1.0/notes/action/filter                    Lấy danh sách Note
@apiGroup Note
@apiVersion 1.0.0
@apiName ListNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:) {string}    sort                   field sort 
@apiParam   (Query:) {string}    order                  thu tu sort (asc/desc)

@apiUse GetListNoteBody
@apiUse ResponseDetailNoteNew
@apiUse GetListNoteResponse
@apiUse paging
"""
# ------------------------- Lấy chi tiết của Note -------------
"""
@api {GET} {domain}/note/api/v1.0/note/<note_id>                  Chi tiết Note
@apiGroup Note
@apiVersion 1.0.0
@apiName    DetailNote
@apiParam   (Query:) {string}    [fields]                         Danh sách field cần lấy ngăn cách nhau bởiđâ phẩy.
                                                                <code>Example: fields=id,description</code>

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse ResponseDetailNoteNew
@apiUse DetailNoteResponse
"""
# ------------------------- Xoá Note ----------------
"""
@api {DELETE} {domain}/note/api/v1.0/notes                   Xoá Note
@apiGroup Note
@apiVersion 1.0.0
@apiName DeleteNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam       (Query:)    {string}        ids         Chuỗi ID của Note cần xoá, ngăn cách nhau bởi dấu phẩy.

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""


# ------------------------Cập nhật thông của Note----------------
"""
@api {PUT} {domain}/note/api/v1.0/note/<note_id>                   Cập nhật thông tin của Note
@apiGroup Note
@apiVersion 1.0.0
@apiName UpdateNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    description                         Nội dung của Note
@apiParam   (BODY:) {object}    information                         Thông tin chung của Note
@apiParam   (BODY:) {string=GENERAL,EMAIL,CALL}    information.type                         Loại ghi chú
@apiParam   (BODY:) {Array}     attachment_ids                      Danh sách <code>ID</code> file đính kèm           
@apiParam   (BODY:) {String}    source                              Nguồn tạo. Tức là đang đứng ở Module nào thì sẽ cần truyền vào Module đó. Ví dụ: <code>SALE</code>
@apiParam   (BODY:) {String}    object_id                           Định danh của thành phân cần lấy danh sách task. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {Object}    relate_to                           Liên quan tới phần nào.
@apiParam   (BODY:) {Array}    relate_to.company_ids                Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.ticket_ids                 Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.order_ids                   Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.profile_ids                Danh sách profile liên quan tới.

@apiParam   (BODY:) {Object}    relate_to_not_delete                           Liên quan tới phần chính nào.
@apiParam   (BODY:) {Array}    relate_to_not_delete.company_ids                Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.ticket_ids                 Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.order_ids                   Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.profile_ids                Danh sách profile liên quan tới.
@apiParam   (BODY:) {Object}   follow_task                          Tạo công việc tiếp nối.
@apiParam   (BODY:) {Array}    [mentions]                             Danh sách người được mentions
@apiParam   (BODY:) {String}   [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiParam   (BODY:) {String}   [mentions.account_id]                  <code>ID</code> của người được mentions
@apiParam   (BODY:) {String}   [description_attachment_ids]         Danh sách <code>ID</code> của file đính kèm trong nội dung ghi chú
@apiParam   (BODY:) {int}  status                                   Trạng thái tạo công việc tiếp nối.
                                                                    <ul>
                                                                        <li><code>0:</code> không tự động tạo công việc tiếp nối</li>
                                                                        <li><code>1:</code> tự động tạo công việc tiếp nối</li>
                                                                    </ul>
@apiParam   (BODY:) {string}   time_success                         Thời gian hoàn thành của Task trong trường hợp <code>status:1</code>


@apiParam   (Information type: EMAIL)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: EMAIL)  {string}   time_contact   Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>  


@apiParam   (Information type: CALL)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: CALL)  {string}   time_contact       Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>
@apiParam   (Information type: CALL)  {string=HEARD,REFUSE_ANSWER,MISSED_CALL,BUSY,WRONG_PHONE_NUMBER,ALREADY_MADE_APPOINTMENT,NO_ALREADY_MADE_APPOINTMENT}   result     Kết quả cuộc gọi 
                                                                                                                    <ul>
                                                                                                                        <li><code>HEARD:</code> Profile đã nghe</li>
                                                                                                                        <li><code>REFUSE_ANSWER:</code> Profile từ chối nghe</li>
                                                                                                                        <li><code>MISSED_CALL:</code> Gọi nhỡ</li>
                                                                                                                        <li><code>BUSY:</code> Profile máy bận/không nghe máy/không liên lạc được</li>
                                                                                                                        <li><code>WRONG_PHONE_NUMBER:</code> Sai số điện thoại</li>
                                                                                                                        <li><code>PROFILE_CARE:</code> Profile quan tâm</li>
                                                                                                                        <li><code>PROFILE_CALL_BACK:</code> Profile hẹn gọi lại</li>
                                                                                                                        <li><code>ALREADY_MADE_APPOINTMENT:</code> Đặt hẹn được</li>
                                                                                                                        <li><code>NO_ALREADY_MADE_APPOINTMENT:</code> Không đặt hẹn được</li>
                                                                                                                    </ul>
                                                                                                                    
@apiParam   (Information type: MEETING)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: MEETING)  {string}   time_contact       Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>
@apiParam   (Information type: MEETING)  {string=MET,CHANGE_APPOINTMENT_TIME,PROFILES_NOT_COMING,MEETING_CANCELED,PROFILES_AGREE_COLLECT_RECORDS,PROFILES_NOT_AGREE_COLLECT_RECORDS,}   result     Kết quả cuộc gặp 
                                                                                                                    <ul>
                                                                                                                        <li><code>MET:</code> Đã gặp</li>
                                                                                                                        <li><code>CHANGE_APPOINTMENT_TIME:</code> Đổi thời gian hẹn gặp</li>
                                                                                                                        <li><code>PROFILES_NOT_COMING:</code> Profiles không tới cuộc gặp</li>
                                                                                                                        <li><code>MEETING_CANCELED:</code> Cuộc gặp bị huỷ</li>
                                                                                                                        <li><code>PROFILES_AGREE_COLLECT_RECORDS:</code>Profiles đồng ý thu thập hồ sơ</li>
                                                                                                                        <li><code>PROFILES_NOT_AGREE_COLLECT_RECORDS:</code>Profiles không đồng ý thu thập hồ sơ</li>
                                                                                                                    </ul>                                                                                                                    


@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
    "description": "Nội dung ghi chú",
    "information": {
        "type": "GENERAL"
    },
    "related_to": {
        "company_ids": [],
        "ticket_ids": [],
        "profile_ids": [],
        "order_ids": []
    },
    "related_to_not_delete": {
        "company_ids": [],
        "ticket_ids": [],
        "profile_ids": [],
        "order_ids": []
    },
    "attachment_ids":[],
    "description_attachment_ids":[],
    "follow_task": {
        "status": 0
    },
    "mentions":[
        {
            "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
            "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
        }
    ]
}                                       

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin file Upload

@apiUse ResponseDetailNoteNew

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "source": "SALE",
        "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
        "description": "Nội dung ghi chú",
        "information": {
            "type": "GENERAL"
        },
        "related_to": {
            "company_ids": [],
            "ticket_ids": [],
            "profile_ids": [],
            "order_ids": []
        },
        "related_to_not_delete": {
            "company_ids": [],
            "ticket_ids": [],
            "profile_ids": [],
            "order_ids": []
        },
        "attachment_ids":[],
        "description_attachment_ids":[],
        "follow_task": {
            "status": 0
        },
        "mentions":[
            {
                "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
                "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
            }
        ],
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""
# Lấy danh sách trạng thái kết quả cuộc gọi
"""
@api {GET} {domain}/note/api/v1.0/notes/result-call                  Lấy danh sách trạng thái kết quả cuộc gọi 
@apiGroup Note
@apiVersion 1.0.0
@apiName    GetResultCallNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}            data                           Danh sách Note

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "code": "HEARD",
            "value": {
                "en": "Profile listened",
                "vi": "Profile đã nghe"
            }
        },
        {
            "code": "REFUSE_ANSWER",
            "value": {
                "en": "Profile refuses to listen",
                "vi": "Profile từ chối nghe"
            }
        },
        {
            "code": "MISSED_CALL",
            "value": {
                "en": "Profile missed call",
                "vi": "Gọi nhỡ"
            }
        },
        {
            "code": "BUSY",
            "value": {
                "en": "Busy",
                "vi": "Máy bận"
            }
        },
        {
            "code": "WRONG_PHONE_NUMBER",
            "value": {
                "en": "Incorrect telephone number",
                "vi": "Sai số điện thoại"
            }
        }
    ]
}
"""

# Ghim note
"""
@api {PUT} {domain}/note/api/v1.0/notes/actions/pin        Ghim ghi chú                               
@apiGroup PinNote
@apiVersion 1.0.0
@apiName    PinNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {String}    source                              Nguồn tạo. Tức là đang đứng ở Module nào thì sẽ cần truyền vào Module đó. Ví dụ: <code>SALE</code>
@apiParam   (BODY:) {String}    object_id                           Định danh của thành phân cần lấy danh sách task. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {string}    note_id                       <code>ID</code> ghi chú được ghim
@apiParamExample    {json}      BODY:
{
    "note_id": "1287312kjj81928",
    "source": "TICKET",
    "object_id": "1287312kjj81928",
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""
# Bỏ ghim note
"""
@api {PUT} {domain}/note/api/v1.0/notes/actions/unpin        Bỏ ghim ghi chú                               
@apiGroup PinNote
@apiVersion 1.0.0
@apiName    UnPinNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    note_id                       <code>ID</code> ghi chú bỏ ghim
@apiParamExample    {json}      BODY:
{
    "note_id": "1287312kjj81928"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

"""
@api {GET} {domain}/note/api/v1.0/notes/type     Lấy danh sách kiểu ghi chú                            
@apiGroup TypeNote
@apiVersion 1.0.0
@apiName    ListTypeNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {List}           data                          Danh sách type của note

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "code": "GENERAL",
            "is_default": True,
            "value": {
                "vi": "chung",
                "en": "general"
            }
        },
        {
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "code": "CALL",
            "is_default": False,
            "value": {
                "vi": "cuộc gọi",
                "en": "call"
            }
        },
        {
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "code": "EMAIL",
            "is_default": False,
            "value": {
                "vi": "email",
                "en": "email"
            }
        },
        {
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "code": "MEETING",
            "is_default": False,
            "value": {
                "vi": "cuộc gặp",
                "en": "meeting"
            }
        },
    ]
}
"""

# ------------------------Cập nhật thông của Note theo field----------------
"""
@api {PUT} {domain}/note/api/v1.0/note/<note_id>/only_update_field_send                   Cập nhật thông tin của Note theo field
@apiDescription Cập nhật thông tin của Note theo các field FE truyền lên. 
@apiGroup Note
@apiVersion 1.0.0
@apiName UpdateNoteOnlyUpdateField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    [description]                         Nội dung của Note
@apiParam   (BODY:) {object}    [information]                         Thông tin chung của Note
@apiParam   (BODY:) {object}    [description_attachment_ids]          Danh sách <code>ID</code> file đính kèm ở nội dung của ghi chú.
@apiParam   (BODY:) {string=GENERAL,EMAIL,CALL}    [information.type]                         Loại ghi chú
@apiParam   (BODY:) {Array}     [attachment_ids]                      Danh sách <code>ID</code> file đính kèm           
@apiParam   (BODY:) {String}    [source]                              Nguồn tạo. Tức là đang đứng ở Module nào thì sẽ cần truyền vào Module đó. Ví dụ: <code>SALE</code>
@apiParam   (BODY:) {String}    [object_id]                           Định danh của thành phân cần lấy danh sách task. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {Object}    [relate_to]                           Liên quan tới phần nào.
@apiParam   (BODY:) {Array}    [relate_to.company_ids]                Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    [relate_to.ticket_ids]                 Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    [relate_to.order_ids]                   Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    [relate_to.profile_ids]                Danh sách profile liên quan tới.

@apiParam   (BODY:) {Object}    [relate_to_not_delete]                           Liên quan tới phần chính nào.
@apiParam   (BODY:) {Array}    [relate_to_not_delete.company_ids]                Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    [relate_to_not_delete.ticket_ids]                 Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    [relate_to_not_delete.order_ids]                   Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    [relate_to_not_delete.profile_ids]                Danh sách profile liên quan tới.
@apiParam   (BODY:) {Object}   [follow_task]                          Tạo công việc tiếp nối.
@apiParam   (BODY:) {Array}    [mentions]                             Danh sách người được mentions
@apiParam   (BODY:) {String}   [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiParam   (BODY:) {String}   [mentions.account_id]                  <code>ID</code> của người được mentions
@apiParam   (BODY:) {String}   [description_attachment_ids]         Danh sách <code>ID</code> của file đính kèm trong nội dung ghi chú


@apiParam   (Information type: EMAIL)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: EMAIL)  {string}   time_contact   Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>  


@apiParam   (Information type: CALL)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: CALL)  {string}   time_contact       Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>
@apiParam   (Information type: CALL)  {string=HEARD,REFUSE_ANSWER,MISSED_CALL,BUSY,WRONG_PHONE_NUMBER,ALREADY_MADE_APPOINTMENT,NO_ALREADY_MADE_APPOINTMENT}   result     Kết quả cuộc gọi 
                                                                                                                    <ul>
                                                                                                                        <li><code>HEARD:</code> Profile đã nghe</li>
                                                                                                                        <li><code>REFUSE_ANSWER:</code> Profile từ chối nghe</li>
                                                                                                                        <li><code>MISSED_CALL:</code> Gọi nhỡ</li>
                                                                                                                        <li><code>BUSY:</code> Profile máy bận/không nghe máy/không liên lạc được</li>
                                                                                                                        <li><code>WRONG_PHONE_NUMBER:</code> Sai số điện thoại</li>
                                                                                                                        <li><code>PROFILE_CARE:</code> Profile quan tâm</li>
                                                                                                                        <li><code>PROFILE_CALL_BACK:</code> Profile hẹn gọi lại</li>
                                                                                                                        <li><code>ALREADY_MADE_APPOINTMENT:</code> Đặt hẹn được</li>
                                                                                                                        <li><code>NO_ALREADY_MADE_APPOINTMENT:</code> Không đặt hẹn được</li>
                                                                                                                    </ul>

@apiParam   (Information type: MEETING)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: MEETING)  {string}   time_contact       Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>
@apiParam   (Information type: MEETING)  {string=MET,CHANGE_APPOINTMENT_TIME,PROFILES_NOT_COMING,MEETING_CANCELED,PROFILES_AGREE_COLLECT_RECORDS,PROFILES_NOT_AGREE_COLLECT_RECORDS,}   result     Kết quả cuộc gặp 
                                                                                                                    <ul>
                                                                                                                        <li><code>MET:</code> Đã gặp</li>
                                                                                                                        <li><code>CHANGE_APPOINTMENT_TIME:</code> Đổi thời gian hẹn gặp</li>
                                                                                                                        <li><code>PROFILES_NOT_COMING:</code> Profiles không tới cuộc gặp</li>
                                                                                                                        <li><code>MEETING_CANCELED:</code> Cuộc gặp bị huỷ</li>
                                                                                                                        <li><code>PROFILES_AGREE_COLLECT_RECORDS:</code>Profiles đồng ý thu thập hồ sơ</li>
                                                                                                                        <li><code>PROFILES_NOT_AGREE_COLLECT_RECORDS:</code>Profiles không đồng ý thu thập hồ sơ</li>
                                                                                                                    </ul>                                                                                                                    


@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
    "description": "Nội dung ghi chú",
    "information": {
        "type": "GENERAL"
    },
    "related_to": {
        "company_ids": [],
        "ticket_ids": [],
        "profile_ids": [],
        "order_ids": []
    },
    "related_to_not_delete": {
        "company_ids": [],
        "ticket_ids": [],
        "profile_ids": [],
        "order_ids": []
    },
    "attachment_ids":[],
    "description_attachment_ids":[],
    "mentions":[
        {
            "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
            "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
        }
    ]
}                                       

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin của ghi chú sau khi cập nhật

@apiUse ResponseDetailNoteNew

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "source": "SALE",
        "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
        "description": "Nội dung ghi chú",
        "information": {
            "type": "GENERAL"
        },
        "related_to": {
            "company_ids": [],
            "ticket_ids": [],
            "profile_ids": [],
            "order_ids": []
        },
        "related_to_not_delete": {
            "company_ids": [],
            "ticket_ids": [],
            "profile_ids": [],
            "order_ids": []
        },
        "attachment_ids":[],
        "description_attachment_ids":[],
        "mentions":[
            {
                "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
                "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
            }
        ],
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 27/05/2023
"""
# ---------- Socket nhận khi có người nhắc đến bạn trong 1 ghi chú -----------
"""
@api {socket}   None              Socket nhận khi có người nhân nhắc đến bạn trong 1 ghi chú
@apiGroup Socket
@apiVersion 1.0.0
@apiName SocketNoteMentionMe

@apiSuccessExample {json} Response 
{
    {
      "from": {
        "status": 1,
        "source": "note",
        "sender_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "sender_type": 2
      },
      "to": {
        "receiver_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "receiver_ids": [],
        "receiver_type": 3,
        "channel": "MOBIO_PARTNER_CEM"
      },
      "body": {
        "title": "<PERSON><PERSON><PERSON> đư<PERSON> nhắc tên trong một ghi chú",
        "content": null,
        "information": {
          "type": "GENERAL"
        },
        "object_id": "646dd5c0277ba3000ff31a01",
        "socket_type": "note_mention_me",
        "note_type": "Chung",
        "user_name": "Admin pingcomshop(admin@pingcomshop)",
        "source": "TICKET",
        "module_name": "ticket",
        "name_info": "TH2",
        "note_content": "<div>3333 <span class=\"mo-quill-mention\" id=\"4f3e947c-7b2a-49e7-9bb7-f4decf75a9bd\" feid=\"2b0583d6-7604-4035-b47f-3dce4e81a99c\" value=\"@ thuhauser1\" style=\"font-weight: 600;-moz-osx-font-smoothing: grayscale;-webkit-font-smoothing: antialiased\">﻿<span contenteditable=\"false\">﻿<span contenteditable=\"false\">@ thuhauser1</span>﻿</span>﻿</span> <span class=\"mo-quill-mention\" id=\"7fc0a33c-baf5-11e7-a7c2-0242ac180003\" feid=\"bb050740-32dd-405b-84b7-6a31a9d3dc7b\" value=\"@Admin pingcomshop\" style=\"font-weight: 600;-moz-osx-font-smoothing: grayscale;-webkit-font-smoothing: antialiased\">﻿<span contenteditable=\"false\">@Admin pingcomshop</span>﻿</span> </div>",
        "email_sender_name": "Mobio Alert"
      },
      "notify_id": "e50ce717-5012-47e5-baba-9f908ef2618b"
    }
}
"""
# ---------- Socket nhận khi có người nhắc đến bạn trong 1 bình luận ghi chú -----------
"""
@api {socket}   None              Socket nhận khi có người nhân nhắc đến bạn trong 1 ghi chú
@apiGroup Socket
@apiVersion 1.0.0
@apiName SocketCommentNoteMentionMe

@apiSuccessExample {json} Response 
{
  "from": {
    "status": 1,
    "source": "note",
    "sender_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "sender_type": 2
  },
  "to": {
    "receiver_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "receiver_ids": [],
    "receiver_type": 3,
    "channel": "MOBIO_PARTNER_CEM"
  },
  "body": {
    "title": "Bạn được nhắc tên trong một Bình luận thuộc Ghi chú",
    "content": null,
    "object_id": "646dd5c0277ba3000ff31a01",
    "socket_type": "note_mention_me_comment",
    "note_name": null,
    "information": {
      "type": "GENERAL"
    },
    "username": "admin@pingcomshop",
    "user_name": "admin@pingcomshop",
    "module_name": "Ticket",
    "note_type": "Chung",
    "source": "TICKET",
    "full_name": "Admin pingcomshop",
    "name_info": "TH2",
    "comment_content": "333 <span class=\"mo-lib-shared-mention\" style=\"font-weight: 600;-moz-osx-font-smoothing: grayscale;-webkit-font-smoothing: antialiased\" contenteditable=\"false\" id=\"7fc0a33c-baf5-11e7-a7c2-0242ac180003\" feid=\"39723274-5a25-4fdc-9454-44af6524c8c7\" value=\"@Admin pingcomshop\">@Admin pingcomshop</span>&nbsp;",
    "email_sender_name": "Mobio Alert"
  },
  "notify_id": "d41482a2-f8ff-499b-bb89-ed1e82bfeb50"
}
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 21/08/2022
"""
# ---------- <PERSON><PERSON> sách File theo ID -----------
"""
@api {GET} {domain}/note/api/v1.0/note/comment/files              L<PERSON>y danh sách các file theo Comment ID
@apiGroup FileComment
@apiVersion 1.0.0
@apiName ListFileComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string}  comment_ids      List <code> Comment ID </code> cần lấy file.


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách tương ứng các file.

@apiSuccess {String}            data.id                       <code>ID</code> của file upload lên hệ thống
@apiSuccess {String}            data.object_id                <code>ID</code> của đối tượng
@apiSuccess {String}            data.title                    Tên file upload
@apiSuccess {String}            data.format_file              Định dạng của file
@apiSuccess {String}            data.url                      URL file


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "id": "9a9f73fc-c24d-4be7-a1dd-019ace63b283",
            "object_id" : "9a9f73fc-c24d-4be7-a1dd-019ace63b283",
            "title": "cmnd.png",
            "format_file": "image/png",
            "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/cmnd.png"
        },
        {
            "id": "9a9f73fc-c24d-4be7-a1dd-019ace63b298",
            "object_id" : "9a9f73fc-c24d-4be7-a1dd-019ace63b283"
            "title": "video.mp4",
            "format_file": "video/mp4",
            "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/video.mp4"
        },
    ]
}
"""

# ---------- Thêm file trong comment-----------
"""
@api {POST} {domain}/note/api/v1.0/note/comment/files                    Upload multi file comment
@apiGroup FileComment
@apiVersion 1.0.0
@apiName UploadFileComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(Form:)			{File}	    file		            Danh sách file cần upload 

@apiParamExample {json} Form example
file: (binary)

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin file Upload
@apiSuccess {Object}            data.success                  Danh sách file upload thành công
@apiSuccess {Object}            data.fail                     Danh sách file upload thất bại
@apiSuccess {Int}               data.success.number           Số file upload thành công
@apiSuccess {Int}               data.fail.number              Số file upload thất bại
@apiSuccess {Array}             data.success.list             Thông tin file upload thành công


@apiSuccess (data)      {String}         success.list.id                       <code>ID</code> file upload lên hệ thống
@apiSuccess (data)      {Array}          success.list.type_media               Loại Media
@apiSuccess (data)      {String}         success.list.title                    Tên file upload
@apiSuccess (data)      {String}         success.list.format_file              Định dạng của file
@apiSuccess (data)      {String}         success.list.url                      URL file
@apiSuccess (data)      {String}         success.list.capacity                 Dung lượng file
@apiSuccess (data)      {String}         success.list.local_path               Đường dẫn vậy lý của file
@apiSuccess (data)      {String}         success.list.merchant_id              Định danh tenant upload file
@apiSuccess (data)      {String}         success.list.comment_id                Định danh Comment

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "success": {
            "number": 2,
            "list": [
                {
                    "_id": "620dc147dfd20bf34ac6954e",
                    "action_time": 1645043415.566902,
                    "capacity": 1185524,
                    "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "filename": "1645068615_0E0A7556.jpg",
                    "format_file": "image/jpeg",
                    "id": "620dc147dfd20bf34ac6954e",
                    "local_path": "/Users/<USER>/workspace/mobio/Module-Ticket/ticket/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7556.jpg",
                    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
                    "ticket_id": null,
                    "type_media": [
                        "other"
                    ],
                    "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7556.jpg"
                },
                {
                    "_id": "620dc147dfd20bf34ac6954f",
                    "action_time": 1645043415.593171,
                    "capacity": 1082923,
                    "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "filename": "1645068615_0E0A7559.jpg",
                    "format_file": "image/jpeg",
                    "id": "620dc147dfd20bf34ac6954f",
                    "local_path": "/Users/<USER>/workspace/mobio/Module-Ticket/ticket/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg",
                    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
                    "ticket_id": null,
                    "type_media": [
                        "other"
                    ],
                    "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg"
                }
            ]
        },
        "fail": {
            "number": 0
        }
        
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------- Xóa file trong Comment -----------
"""
@api {DELETE} {domain}/note/api/v1.0/note/comment/files           Xóa file trong Comment
@apiGroup FileComment
@apiVersion 1.0.0
@apiName DeleteFileComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(Param:)			{String}     ids             Danh sách các <code>ID</code> file cần gỡ. Cách nhau bởi dấu phẩy

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TungDD
    Company: <PERSON>bio
    Date Created: 11/10/2021
"""
# ---------- <PERSON><PERSON> sách File theo ID -----------
"""
@api {GET} {domain}/note/api/v1.0/files              L<PERSON>y danh sách các file theo Note ID
@apiGroup File
@apiVersion 1.0.0
@apiName ListFile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string}  note_ids      List <code> Task ID </code> cần lấy file.


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách tương ứng các file.

@apiSuccess {String}            data.id                       <code>ID</code> của file upload lên hệ thống
@apiSuccess {String}            data.note_id                  <code>ID</code> của note
@apiSuccess {String}            data.title                    Tên file upload
@apiSuccess {String}            data.format_file              Định dạng của file
@apiSuccess {String}            data.url                      URL file


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "id": "9a9f73fc-c24d-4be7-a1dd-019ace63b283",
            "note_id": "9a9f73fc-c24d-4be7-a1dd-019ace63b283",
            "title": "cmnd.png",
            "format_file": "image/png",
            "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/cmnd.png"
        },
        {
            "id": "9a9f73fc-c24d-4be7-a1dd-019ace63b298",
            "note_id" : "9a9f73fc-c24d-4be7-a1dd-019ace63b283"
            "title": "video.mp4",
            "format_file": "video/mp4",
            "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/video.mp4"
        },
    ]
}
"""

# ---------- Thêm file trong note-----------
"""
@api {POST} {domain}/note/api/v1.0/files                    Upload file
@apiGroup File
@apiVersion 1.0.0
@apiName UploadFile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(Form:)			{File}	    file		            File cần upload

@apiParamExample {json} Form example
file: (binary)

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin file Upload

@apiSuccess {String}            data.id                       <code>ID</code> của file upload lên hệ thống
@apiSuccess {String}            data.title                    Tên file upload
@apiSuccess {String}            data.format_file              Định dạng của file
@apiSuccess {String}            data.url                      URL file

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "id": "9a9f73fc-c24d-4be7-a1dd-019ace63b283",
        "title": "cmnd.png",
        "format_file": "image/png",
        "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/cmnd.png"
    },
    "message": "request thành công."
}
"""

# ---------- Xóa file trong note -----------
"""
@api {DELETE} {domain}/note/api/v1.0/files           Xóa file trong Note
@apiGroup File
@apiVersion 1.0.0
@apiName DeleteFile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(Param:)			{String}     ids             Danh sách các <code>ID</code> file cần gỡ. Cách nhau bởi dấu phẩy

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""
# ---------- Thêm file trong note-----------
"""
@api {POST} {domain}/note/api/v1.0/description/attachments                    Upload file trong description của Module Note
@apiGroup FileDescription
@apiVersion 1.0.0
@apiName UploadFileDescription

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(Form:)			{File}	    files		            Danh sách file cần upload

@apiParamExample {json} Form example
file: (binary)

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}            data                           Thông tin file Upload

@apiSuccess {String}            data.id                       <code>ID</code> của file upload lên hệ thống
@apiSuccess {String}            data.title                    Tên file upload
@apiSuccess {String}            data.format_file              Định dạng của file
@apiSuccess {String}            data.url                      URL file
@apiSuccess {Object}            data.size                     Kích thước của ảnh 
@apiSuccess {float}             data.size.width               Độ rộng của ảnh                 
@apiSuccess {float}             data.size.height              Chiều cao của ảnh                 

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "id": "9a9f73fc-c24d-4be7-a1dd-019ace63b283",
            "title": "cmnd.png",
            "format_file": "image/png",
            "size": {
                "width": "",
                "height": ""
            }
            "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/cmnd.png",
        }
    ],
    "message": "request thành công."
}
"""
# ---------- Thêm thông tin file theo danh sách IDS-----------
"""
@api {GET} {domain}/note/api/v1.0/attachments                    Lấy danh sách file theo IDS
@apiGroup Attachments
@apiVersion 1.0.0
@apiName ListAttachment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(Query:)	{String}    attachment_ids	  	    Danh sách id cần lấy thông tin, được ngăn cách nhau bởi dấu ,


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Thông tin file

@apiSuccess {String}            data.id                       <code>ID</code> của file upload lên hệ thống
@apiSuccess {String}            data.title                    Tên file upload
@apiSuccess {String}            data.format_file              Định dạng của file
@apiSuccess {String}            data.url                      URL file
@apiSuccess {Object}            data.size                     Kích thước của ảnh 
@apiSuccess {float}             data.size.width               Độ rộng của ảnh                 
@apiSuccess {float}             data.size.height              Chiều cao của ảnh                 

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "id": "9a9f73fc-c24d-4be7-a1dd-019ace63b283",
            "title": "cmnd.png",
            "format_file": "image/png",
            "size": {
                "width": "",
                "height": ""
            }
            "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/cmnd.png",
        }
    ],
    "message": "request thành công."
}
"""

# ---------- Zip multi file đính kèm của Note-----------
"""
@api {GET} {domain}/note/api/v1.0/note/<note_id>/attachments/zip                    Zip tất cả các file của note_id gửi lên
@apiGroup Attachments
@apiVersion 1.0.0
@apiName ZipAllAttachmentByNoteID

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Thông tin file

@apiSuccess {String}            data.link                       <code>Link</code> file zip              

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "link": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/note_id.zip",
    },
    "message": "request thành công."
}
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" 
    Author: tungdd
    Company: MobioVN
    Date created: 18/10/2023
"""
"""
@api {POST} {domain}/note/api/v1.0/notes/validate/data-add                    API validate dữ liệu tạo note
@apiGroup ValidateData
@apiVersion 1.0.0
@apiName ValidateDataAddNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    description                         Nội dung của Note
@apiParam   (BODY:) {object}    information                         Thông tin chung của Note
@apiParam   (BODY:) {string=GENERAL,EMAIL,CALL, MEETING}    information.type                         Loại ghi chú
@apiParam   (BODY:) {Array}     attachment_ids                      Danh sách <code>ID</code> file đính kèm           
@apiParam   (BODY:) {String=SALE,PROFILE,TICKET,COMPANY}    source                              <PERSON><PERSON>ồn tạo. Tứ<PERSON> là đang đứng ở Module nào thì sẽ cần truyền vào Module đó. Ví dụ: <code>SALE</code>
@apiParam   (BODY:) {String}    object_id                           Định danh của thành phân cần lấy danh sách task. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {Object}    relate_to                           Liên quan tới phần nào.
@apiParam   (BODY:) {Array}    relate_to.company_ids                Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.ticket_ids                 Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.order_ids                   Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.profile_ids                Danh sách profile liên quan tới.


@apiParam   (BODY:) {Object}    relate_to_not_delete                           Liên quan tới phần chính nào.
@apiParam   (BODY:) {Array}    relate_to_not_delete.company_ids                Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.ticket_ids                 Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.order_ids                   Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.profile_ids                Danh sách profile liên quan tới.

@apiParam   (BODY:) {Object}   follow_task                          Tạo công việc tiếp nối.
@apiParam   (BODY:) {String}  follow_task.title                          Tiêu đề của công việc tiếp nối
@apiParam   (BODY:) {int}  follow_task.status                                   Trạng thái tạo công việc tiếp nối.
                                                                    <ul>
                                                                        <li><code>0:</code> không tự động tạo công việc tiếp nối</li>
                                                                        <li><code>1:</code> tự động tạo công việc tiếp nối</li>
                                                                    </ul>
@apiParam   (BODY:) {string}    follow_task.deadline                 Thời hạn hoàn thành của Task trong trường hợp <code>status:1</code>
@apiParam   (BODY:) {string}    follow_task.deadline.type            Loại thời hạn hoàn thành của công việc tiếp nối
                                                                        <ul>
                                                                            <li><code>time_slot</code>: Khung giờ</li>
                                                                            <li><code>unspecified_time</code>: Không xác định thời gian</li>
                                                                        </ul>
@apiParam   (BODY:)  {string}   follow_task.deadline.start_time      Thời hạn hoàn thành công việc.
                                                                    <ul>
                                                                        <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                        <li>Trong trường hợp type=<code>unspecified_time</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d"</code></li>
                                                                    </ul>
@apiParam   (BODY:)  {string}   follow_task.deadline.end_time        Thời hạn hoàn thành công việc.
                                                                    <ul>
                                                                        <li>Trong trường hợp type=<code>time_slot</code> thì cần truyền giá trị này. Định dạng: <code>"%Y-%m-%d %H:%m"</code></li>
                                                                        <li>Trong trường hợp type=<code>unspecified_time</code> thì không cần truyền giá trị này.</li>
                                                                    </ul>
@apiParam   (BODY:) {Array}    [mentions]                             Danh sách người được mentions
@apiParam   (BODY:) {String}   [mentions.fe_id]                       <code>ID</code> của người được mentions được lưu ở FE
@apiParam   (BODY:) {String}   [mentions.account_id]                  <code>ID</code> của người được mentions
@apiParam   (BODY:) {String}   [description_attachment_ids]              Danh sách <code>ID</code> của file được đính kèm trong nội dung ghi chú.



@apiParam   (Information type: EMAIL)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: EMAIL)  {string}   time_contact   Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>  


@apiParam   (Information type: CALL)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: CALL)  {string}   time_contact       Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>
@apiParam   (Information type: CALL)  {string=HEARD,REFUSE_ANSWER,MISSED_CALL,BUSY,WRONG_PHONE_NUMBER,PROFILE_CARE,PROFILE_CALL_BACK,ALREADY_MADE_APPOINTMENT,NO_ALREADY_MADE_APPOINTMENT}   result     Kết quả cuộc gọi 
                                                                                                                    <ul>
                                                                                                                        <li><code>HEARD:</code> Profile đã nghe</li>
                                                                                                                        <li><code>REFUSE_ANSWER:</code> Profile từ chối nghe</li>
                                                                                                                        <li><code>MISSED_CALL:</code> Gọi nhỡ</li>
                                                                                                                        <li><code>BUSY:</code> Profile máy bận/không nghe máy/không liên lạc được</li>
                                                                                                                        <li><code>WRONG_PHONE_NUMBER:</code> Sai số điện thoại</li>
                                                                                                                        <li><code>PROFILE_CARE:</code> Profile quan tâm</li>
                                                                                                                        <li><code>PROFILE_CALL_BACK:</code> Profile hẹn gọi lại</li>
                                                                                                                        <li><code>ALREADY_MADE_APPOINTMENT:</code> Đặt hẹn được</li>
                                                                                                                        <li><code>NO_ALREADY_MADE_APPOINTMENT:</code> Không đặt hẹn được</li>
                                                                                                                    </ul>

@apiParam   (Information type: MEETING)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: MEETING)  {string}   time_contact       Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>
@apiParam   (Information type: MEETING)  {string=MET,CHANGE_APPOINTMENT_TIME,PROFILES_NOT_COMING,MEETING_CANCELED,PROFILES_AGREE_COLLECT_RECORDS,PROFILES_NOT_AGREE_COLLECT_RECORDS }   result     Kết quả cuộc gặp 
                                                                                                                    <ul>
                                                                                                                        <li><code>MET:</code> Đã gặp</li>
                                                                                                                        <li><code>CHANGE_APPOINTMENT_TIME:</code> Đổi thời gian hẹn gặp</li>
                                                                                                                        <li><code>PROFILES_NOT_COMING:</code> Profiles không tới cuộc gặp</li>
                                                                                                                        <li><code>MEETING_CANCELED:</code> Cuộc gặp bị huỷ</li>
                                                                                                                        <li><code>PROFILES_AGREE_COLLECT_RECORDS:</code>Profiles đồng ý thu thập hồ sơ</li>
                                                                                                                        <li><code>PROFILES_NOT_AGREE_COLLECT_RECORDS:</code>Profiles không đồng ý thu thập hồ sơ</li>
                                                                                                                    </ul>


@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
    "description": "Nội dung ghi chú",
    "mentions": [
        {
            "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
            "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
        }
    ],
    "information": {
        "type": "GENERAL"
    },
    "related_to": {
        "company_ids": [],
        "ticket_ids": [],
        "profile_ids": [],
        "order_ids": []
    },
    "related_to_not_delete": {
        "company_ids": [],
        "ticket_ids": [],
        "profile_ids": [],
        "order_ids": []
    },
    "mentions": [
        {
            "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
            "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
        }
    ], 
    "attachment_ids":[],
    "description_attachment_ids":[],
    "follow_task": {
        "status": 0
    }
}                                       

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

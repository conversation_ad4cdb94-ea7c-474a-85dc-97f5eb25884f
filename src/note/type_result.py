#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 18/05/2022
"""

# -----------------<PERSON><PERSON><PERSON> danh sách kết quả của Note theo loại-------------------------
"""
@api {GET} {domain}/note/api/v1.0/notes/type-result                    L<PERSON><PERSON> danh sách loại kết quả
@apiGroup TypeResult
@apiVersion 1.0.0
@apiName ListTypeResultNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:)   {string}   type              Mã loại kết quả
                                                    <ul>
                                                        <li><code>CALL</code> <PERSON><PERSON><PERSON> đi<PERSON>n</li>
                                                        <li><code>MEETING</code> <PERSON><PERSON><PERSON><PERSON> gặp</li>
                                                    </ul>                                           
    
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                           Danh sách Note

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "code": "HEARD",
            "type": "CALL",
            "value": {
                "en": "Profile listened",
                "vi": "Profile đã nghe"
            }
        },
        {
            "code": "REFUSE_ANSWER",
            "type": "CALL",
            "value": {
                "en": "Profile refuses to listen",
                "vi": "Profile từ chối nghe"
            }
        },
        {
            "code": "MISSED_CALL",
            "type": "CALL",
            "value": {
                "en": "Profile missed call",
                "vi": "Gọi nhỡ"
            }
        },
        {
            "code": "BUSY",
            "type": "CALL",
            "value": {
                "en": "Busy",
                "vi": "Máy bận"
            }
        },
        {
            "code": "WRONG_PHONE_NUMBER",
            "type": "CALL",
            "value": {
                "en": "Incorrect telephone number",
                "vi": "Sai số điện thoại"
            }
        }
    ]
}
"""

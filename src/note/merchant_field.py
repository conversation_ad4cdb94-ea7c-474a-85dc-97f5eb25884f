#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 22/05/2023
"""

# ********************************  List Field **********************************
# version: 1.0.0                                                                      *
# **************************************************************************************
"""
@api {get} {domain}/note/api/v1.0/merchant/field/list           Lấy danh sách field của merchant
@apiDescription Merchant List Field
@apiGroup MerchantConfigField
@apiVersion 1.0.0
@apiName  MerchantConfigField
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
        "fields": [
            {
                "display_type": "tags",
                "field_key": "mentions",
                "field_name": "Thông tin người được nhắc đến trong ghi chú",
                "field_property": 2
            },
            {
                "display_type": "single_line",
                "field_key": "create_by",
                "field_name": "Thông tin người tạo",
                "field_property": 2
            },
            {
                "display_type": "single_line",
                "field_key": "time_contact",
                "field_name": "Thời gian liên hệ",
                "field_property": 2
            },
            {
                "display_type": "single_line",
                "field_key": "time_contact",
                "field_name": "Thời gian liên hệ",
                "field_property": 2
            },
            {
                "display_type": "dropdown_single_line",
                "field_key": "information_type",
                "field_name": "Loại ghi chú",
                "field_property": 2
            }
        ],
        "group_key": "information",
        "group_name": "Thông tin chung",
        "is_base": true
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""

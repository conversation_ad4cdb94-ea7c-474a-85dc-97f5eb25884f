#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 02/12/2021
"""
# -----------------<PERSON><PERSON><PERSON> danh sách Note -------------------------
"""
@api {POST} {domain}/note/api/v1.0/notes/action/filter_internal                    Lấy danh sách Note
@apiGroup Internal
@apiVersion 1.0.0
@apiName ListNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)   {string}   sort=created_time Field sort
@apiParam   (Query:)   {string=asc,desc}   order=desc   Sắp xếp theo thứ tự
@apiParam   (BODY:)   {string}  source    Nguồn cần lấy Note
@apiParam   (BODY:)   {array}   object_ids   Danh sách  id cần lấy
@apiParam   (BODY:)   {array}   fields   <PERSON>h sách các field c<PERSON>n l<PERSON>y dữ li<PERSON>u

@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "object_id": ["5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0"]
    "fields": []

}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}            data                           Danh sách Note

@apiUse ResponseDetailNote

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "_id": "61a744ad8d863d630781740f",
            "attachment_ids": [
                "61a74490a3697fe2ed994fb2",
                "61a744a721c2475cdd1953cb"
            ],
            "attachments": [
                {
                    "created_time": "2021-12-01 09:46",
                    "format_file": "image/jpeg",
                    "id": "61a74490a3697fe2ed994fb2",
                    "title": "1638352013_1.jpg",
                    "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1638352013_1.jpg"
                },
                {
                    "created_time": "2021-12-01 09:47",
                    "format_file": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "id": "61a744a721c2475cdd1953cb",
                    "title": "1638352037_dropdown.xlsx",
                    "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1638352037_dropdown.xlsx"
                }
            ],
            "create_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2021-12-01T16:47Z",
            "description": "<div>Trước thông tin Hà Nội tạm dừng tiêm vắc xin phòng&nbsp;<a href=\"https://thanhnien.vn/covid-19/\" target=\"_blank color: rgb(0, 115, 159);\">Covid-19</a>, sáng 1.12, lãnh đạo Sở Y tế Hà Nội khẳng định, Hà Nội đang rà soát lại 2 lô vắc xin Pfizer gia hạn.</div><div>Sáng nay 1.12, một trường THCS trên địa bàn Q.Đống Đa (Hà Nội) đã nhắn cho phụ huynh học sinh \"tạm dừng phương án tiêm chủng ngày 2 và 3.12 cho đến khi có chỉ đạo mới\".</div><div>Trao đổi với báo chí, bà Trần Thị Nhị Hà, Giám đốc&nbsp;<a href=\"https://thanhnien.vn/bao-phu-vac-xin-covid-19-giam-f0-nang-tu-vong-post1406820.html\" target=\"_blank color: rgb(0, 115, 159);\">Sở Y tế Hà Nội</a>, cho biết Sở đã nhận được nhiều ý kiến đóng góp của phụ huynh về những lo ngại khi cho con em mình tiêm vắc xin.</div><div>Sở Y tế Hà Nội đã tiếp thu và tiến hành rà soát lại tất cả các khâu trong tiêm chủng, gồm các điểm tiêm, các dây chuyền tiêm chủng về điều kiện cơ sở vật chất, trang thiết bị và nguồn nhân lực.</div><div><img style=\"max-width: 100%\" src=\"https://image.thanhnien.vn/w2048/Uploaded/2021/sgozna/2021_11_29/70977e6c3c74f72aae65-2823.jpg\" alt=\"Hà Nội tạm dừng tiêm 2 lô vắc xin Pfizer gia hạn cho học sinh - ảnh 1\"></div>",
            "follow_task": {
                "status": 1,
                "time_success": "2021-12-01T09:47Z"
            },
            "id": "61a744ad8d863d630781740f",
            "information": {
                "type": "GENERAL"
            },
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "object_id": "61932d4d1e660855a668b8c6",
            "related_to": {
                "company_ids": [
                    "61932d4d1e660855a668b8c6"
                ],
                "order_ids": [
                    "61a5e85652299f9db03dce56"
                ],
                "profile_ids": [
                    "66d14396-70ea-494e-aee8-33d7278c989b"
                ],
                "ticket_ids": [
                    "e79dc433-dfc4-405d-a4b7-4e81d417729b"
                ]
            },
            "related_to_not_delete": {},
            "source": "COMPANY"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

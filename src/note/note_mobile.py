#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TungDD
    Company: Mobio
    Date Created: 11/10/2021
"""
"""
@apiDefine ResponseDetailNote

@apiSuccess {String}            data.id                       <code>ID</code> đ<PERSON><PERSON> danh của Note
@apiSuccess {String}            data.source                   <PERSON><PERSON><PERSON> danh nguồn tạo Note
@apiSuccess {String}            data.object_id                Định danh của phần tạo tác. Ví dụ ở SALE thì sẽ là định danh của đơn hàng.
@apiSuccess {String}            data.description              Nội dung của Note 
@apiSuccess {Object}            data.relate_to                Liên quan tới
@apiSuccess {String}            data.relate_to.company_ids    Danh sách các <code>ID</code> công ty liên quan tới
@apiSuccess {String}            data.relate_to.profile_ids    Danh sách các <code>ID</code> profile liên quan tới
@apiSuccess {String}            data.relate_to.order_ids       Danh sách các <code>ID</code> deal liên quan tới
@apiSuccess {String}            data.relate_to.ticket_ids     Danh sách các <code>ID</code> ticket liên quan tới
@apiSuccess {String}            data.information              Thông tin chung của Note
@apiSuccess {String}            data.information.type         Loại Note
@apiSuccess {String}            [data.information.profiles_contact]   Danh sách profile đã liên hệ
@apiSuccess {String}            [data.information.time_contact]       Thời gian liên hệ
@apiSuccess {String}            [data.information.result]             Kết quả cuộc gọi tương ứng với <code>type=CALL</code>
@apiSuccess {Object}            data.relate_to                Liên quan tới
@apiSuccess {String}            data.relate_to.company_ids    Danh sách các <code>ID</code> công ty liên quan tới
@apiSuccess {String}            data.relate_to.profile_ids    Danh sách các <code>ID</code> profile liên quan tới
@apiSuccess {String}            data.relate_to.order_ids       Danh sách các <code>ID</code> deal liên quan tới
@apiSuccess {String}            data.relate_to.ticket_ids     Danh sách các <code>ID</code> ticket liên quan tới
@apiSuccess {String}            data.follow_task              Thông tin tạo công việc tiếp nối
@apiSuccess {String}            data.follow_task.status       Trạng thái tạo công việc tiếp nối
@apiSuccess {String}            [data.follow_task.time_success]       Thời gian hoàn thành của Task. Tương ứng khi <code>status=1</code>

@apiSuccess {String}            data.created_by      Được tạo bởi ai
@apiSuccess {String}            data.created_time    Thời gian tạo

"""

"""
@apiDefine BodyGetListNote

@apiParam   (BODY:)   {array}   brand_ids   Danh sách <code>ID</code> thương hiệu
@apiParam   (BODY:)   {array}   profile_ids   Danh sách <code>ID</code> profile tương tức
@apiParam   (BODY:)   {array}   type   Kiểu ghi chú
@apiParam   (BODY:)   {string}  source    Nguồn cần lấy Note
@apiParam   (BODY:)   {string}   object_id   Định danh thành phần cần lấy Note
@apiParam   (BODY:)   {string}   start_time  Thời gian bắt đầu tìm kiếm. Định dang: <code>%Y-%m-%dT%H:%MZ</code>
@apiParam   (BODY:)   {string}   end_time   Thời gian kết thúc tìm Kiếm. Định dang: <code>%Y-%m-%dT%H:%MZ</code>

@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
    "type": [
        "GENERAL",
        "CALL",
        "EMAIL"
    ],
    "brand_ids": [],
    "profile_ids": [],
    "start_time": "2021-11-01T01:52Z",
    "end_time": "2021-11-01T01:52Z"
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}            data                           Danh sách Note

"""

"""
@apiDefine ResponseGetListNote

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "source": "SALE",
            "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
            "description": "Nội dung ghi chú",
            "information": {
                "type": "GENERAL"
            },
            "related_to": {
                "company_ids": [],
                "ticket_ids": [],
                "profile_ids": [],
                "order_ids": []
            },
            "related_to_not_delete": {
                "company_ids": [],
                "ticket_ids": [],
                "profile_ids": [],
                "order_ids": []
            },
            "attachment_ids":[],
            "follow_task": {
                "status": 0
            },
            "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
            "created_time": "2021-10-03T08:10Z"
        }
    ]
}
"""

"""
@apiDefine ResponseDetailNoteExample

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                           Danh sách Note

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
            "source": "SALE",
            "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
            "description": "Nội dung ghi chú",
            "information": {
                "type": "GENERAL"
            },
            "related_to": {
                "company_ids": [],
                "ticket_ids": [],
                "profile_ids": [],
                "order_ids": []
            },
            "related_to_not_delete": {
                "company_ids": [],
                "ticket_ids": [],
                "profile_ids": [],
                "order_ids": []
            },
            "attachment_ids":[],
            "follow_task": {
                "status": 0
            },
            "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
            "created_time": "2021-10-03T08:10Z"
        }
}
"""

# --------------- Thêm note -----------------
"""
@api {POST} {domain}/note/api/v1.0/notes                    Thêm Notes
@apiGroup Note
@apiVersion 1.0.0
@apiName AddNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    description                         Nội dung của Note
@apiParam   (BODY:) {object}    information                         Thông tin chung của Note
@apiParam   (BODY:) {string=GENERAL,EMAIL,CALL}    information.type                         Loại ghi chú
@apiParam   (BODY:) {Array}     attachment_ids                      Danh sách <code>ID</code> file đính kèm           
@apiParam   (BODY:) {String}    source                              Nguồn tạo. Tức là đang đứng ở Module nào thì sẽ cần truyền vào Module đó. Ví dụ: <code>SALE</code>
@apiParam   (BODY:) {String}    object_id                           Định danh của thành phân cần lấy danh sách task. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {Object}    relate_to                           Liên quan tới phần nào.
@apiParam   (BODY:) {Array}    relate_to.company_ids                Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.ticket_ids                 Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.order_ids                   Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.profile_ids                Danh sách profile liên quan tới.


@apiParam   (BODY:) {Object}    relate_to_not_delete                           Liên quan tới phần chính nào.
@apiParam   (BODY:) {Array}    relate_to_not_delete.company_ids                Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.ticket_ids                 Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.order_ids                   Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.profile_ids                Danh sách profile liên quan tới.

@apiParam   (BODY:) {Object}   follow_task                          Tạo công việc tiếp nối.
@apiParam   (BODY:) {int}  status                                   Trạng thái tạo công việc tiếp nối.
                                                                    <ul>
                                                                        <li><code>0:</code> không tự động tạo công việc tiếp nối</li>
                                                                        <li><code>1:</code> tự động tạo công việc tiếp nối</li>
                                                                    </ul>
@apiParam   (BODY:) {string}   time_success                         Thời gian hoàn thành của Task trong trường hợp <code>status:1</code>


@apiParam   (Information type: EMAIL)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: EMAIL)  {string}   time_contact   Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>  


@apiParam   (Information type: CALL)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: CALL)  {string}   time_contact       Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>
@apiParam   (Information type: CALL)  {string=HEARD,REFUSE_ANSWER,MISSED_CALL,BUSY,WRONG_PHONE_NUMBER}   result     Kết quả cuộc gọi 
                                                                                                                    <ul>
                                                                                                                        <li><code>HEARD:</code> Profile đã nghe</li>
                                                                                                                        <li><code>REFUSE_ANSWER:</code> Profile từ chối nghe</li>
                                                                                                                        <li><code>MISSED_CALL:</code> Gọi nhỡ</li>
                                                                                                                        <li><code>BUSY:</code> Máy bận</li>
                                                                                                                        <li><code>WRONG_PHONE_NUMBER:</code> Số điện thoại không chính xác</li>
                                                                                                                    </ul>


@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
    "description": "Nội dung ghi chú",
    "information": {
        "type": "GENERAL"
    },
    "related_to": {
        "company_ids": [],
        "ticket_ids": [],
        "profile_ids": [],
        "order_ids": []
    },
    "related_to_not_delete": {
        "company_ids": [],
        "ticket_ids": [],
        "profile_ids": [],
        "order_ids": []
    },
    "attachment_ids":[],
    "follow_task": {
        "status": 0
    }
}                                       

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin file Upload

@apiUse ResponseDetailNote

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "source": "SALE",
        "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
        "description": "Nội dung ghi chú",
        "information": {
            "type": "GENERAL"
        },
        "related_to": {
            "company_ids": [],
            "ticket_ids": [],
            "profile_ids": [],
            "order_ids": []
        },
        "related_to_not_delete": {
            "company_ids": [],
            "ticket_ids": [],
            "profile_ids": [],
            "order_ids": []
        },
        "attachment_ids":[],
        "follow_task": {
            "status": 0
        },
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""
# -----------------Lấy danh sách Note-------------------------
"""
@api {POST} {domain}/note/api/v1.0/notes/action/filter                    Lấy danh sách Note
@apiGroup Note
@apiVersion 1.0.0
@apiName ListNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiUse BodyGetListNote
@apiUse ResponseDetailNote
@apiUse ResponseGetListNote
"""
# ------------------------- Lấy chi tiết của Note -------------
"""
@api {GET} {domain}/note/api/v1.0/note/<note_id>                  Chi tiết Note
@apiGroup Note
@apiVersion 1.0.0
@apiName    DetailNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse ResponseDetailNote
@apiUse ResponseDetailNoteExample
"""
# ------------------------- Xoá Note ----------------
"""
@api {DELETE} {domain}/note/api/v1.0/notes                   Xoá Note
@apiGroup Note
@apiVersion 1.0.0
@apiName DeleteNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam       (Query:)    {string}        ids         Chuỗi ID của Note cần xoá, ngăn cách nhau bởi dấu phẩy.

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

# ------------------------Cập nhật thông của Note----------------
"""
@api {PUT} {domain}/note/api/v1.0/note/<note_id>                   Cập nhật thông tin của Note
@apiGroup Note
@apiVersion 1.0.0
@apiName UpdateNote

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    description                         Nội dung của Note
@apiParam   (BODY:) {object}    information                         Thông tin chung của Note
@apiParam   (BODY:) {string=GENERAL,EMAIL,CALL}    information.type                         Loại ghi chú
@apiParam   (BODY:) {Array}     attachment_ids                      Danh sách <code>ID</code> file đính kèm           
@apiParam   (BODY:) {String}    source                              Nguồn tạo. Tức là đang đứng ở Module nào thì sẽ cần truyền vào Module đó. Ví dụ: <code>SALE</code>
@apiParam   (BODY:) {String}    object_id                           Định danh của thành phân cần lấy danh sách task. Ví dụ: Đối với module Sale thì định danh sẽ cần truyền vào là ID của đơn hàng.
@apiParam   (BODY:) {Object}    relate_to                           Liên quan tới phần nào.
@apiParam   (BODY:) {Array}    relate_to.company_ids                Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.ticket_ids                 Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.order_ids                   Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to.profile_ids                Danh sách profile liên quan tới.

@apiParam   (BODY:) {Object}    relate_to_not_delete                           Liên quan tới phần chính nào.
@apiParam   (BODY:) {Array}    relate_to_not_delete.company_ids                Danh sách company liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.ticket_ids                 Danh sách ticket liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.order_ids                   Danh sách đơn hàng liên quan tới.
@apiParam   (BODY:) {Array}    relate_to_not_delete.profile_ids                Danh sách profile liên quan tới.
@apiParam   (BODY:) {Object}   follow_task                          Tạo công việc tiếp nối.
@apiParam   (BODY:) {int}  status                                   Trạng thái tạo công việc tiếp nối.
                                                                    <ul>
                                                                        <li><code>0:</code> không tự động tạo công việc tiếp nối</li>
                                                                        <li><code>1:</code> tự động tạo công việc tiếp nối</li>
                                                                    </ul>
@apiParam   (BODY:) {string}   time_success                         Thời gian hoàn thành của Task trong trường hợp <code>status:1</code>


@apiParam   (Information type: EMAIL)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: EMAIL)  {string}   time_contact   Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>  


@apiParam   (Information type: CALL)  {string}   profiles_contact   Danh sách profile đã liên hệ 
@apiParam   (Information type: CALL)  {string}   time_contact       Thời gian liên hệ. Định dạng: <code>%Y-%m-%d %H:%M</code>
@apiParam   (Information type: CALL)  {string=HEARD,REFUSE_ANSWER,MISSED_CALL,BUSY,WRONG_PHONE_NUMBER}   result     Kết quả cuộc gọi 
                                                                                                                    <ul>
                                                                                                                        <li><code>HEARD:</code> Profile đã nghe</li>
                                                                                                                        <li><code>REFUSE_ANSWER:</code> Profile từ chối nghe</li>
                                                                                                                        <li><code>MISSED_CALL:</code> Gọi nhỡ</li>
                                                                                                                        <li><code>BUSY:</code> Máy bận</li>
                                                                                                                        <li><code>WRONG_PHONE_NUMBER:</code> Số điện thoại không chính xác</li>
                                                                                                                    </ul>


@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
    "description": "Nội dung ghi chú",
    "information": {
        "type": "GENERAL"
    },
    "related_to": {
        "company_ids": [],
        "ticket_ids": [],
        "profile_ids": [],
        "order_ids": []
    },
    "related_to_not_delete": {
        "company_ids": [],
        "ticket_ids": [],
        "profile_ids": [],
        "order_ids": []
    },
    "attachment_ids":[],
    "follow_task": {
        "status": 0
    }
}                                       

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin file Upload

@apiUse ResponseDetailNote

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "source": "SALE",
        "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0",
        "description": "Nội dung ghi chú",
        "information": {
            "type": "GENERAL"
        },
        "related_to": {
            "company_ids": [],
            "ticket_ids": [],
            "profile_ids": [],
            "order_ids": []
        },
        "related_to_not_delete": {
            "company_ids": [],
            "ticket_ids": [],
            "profile_ids": [],
            "order_ids": []
        },
        "attachment_ids":[],
        "follow_task": {
            "status": 0
        },
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""
# --------------- Lấy số lượng của ghi chú -----------------
"""
@api {POST} {domain}/note/api/v1.0/count                    Lấy số lượng ghi chú
@apiGroup NoteMobile
@apiVersion 1.0.0
@apiName CountNoteMobile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}    source                              Định danh đối tượng cần lấy dữ liệu. Ví dụ: SALE, TICKET,....
@apiParam   (BODY:) {string}    object_id                           <code>ID</code> định danh đối tượng cần lấy dữ liệu


@apiParamExample    {json}      BODY:
{
    "source": "SALE",
    "object_id": "5c0bb70d-0db4-4024-a6ab-c8d102d1bbe0"
}                                       

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin file Upload

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "number": 100
    }
}
"""


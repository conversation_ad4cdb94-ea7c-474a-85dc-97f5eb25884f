# ****************************** Criterial Key **********************************
# * version: 1.0.0                                                              *
# *******************************************************************************
"""
@apiDefine critetia_key_body
@apiVersion 1.0.0
@apiParam   (profile_filter:) {String}    criteria_key    Các criteria key để filter khách hàng. Allowed values:<br/>
<li><code>cri_address</code>: Address
<li><code>cri_age</code>: Age
<li><code>cri_gender</code>:  gender
<li><code>cri_birthday</code>:    Birthday
<li><code>cri_member_join</code>: Member join
<li><code>cri_membership_level</code>:    Membership Level
<li><code>cri_region</code>:  Region
<li><code>cri_city</code>:    City
<li><code>cri_job</code>: Job
<li><code>cri_operation</code>:   Lĩnh vực hoạt động
<li><code>cri_hobby</code>:   Hobby
<li><code>cri_sentiment</code>:   Sentiment
<li><code>cri_interactive_frequency</code>:   Tần suất tương tác
<li><code>cri_purchase_frequency</code>:  Mức tiêu dùng
<li><code>cri_created_account_type</code>:    Nguồn ghi nhận khách hàng
<li><code>cri_card_level</code>:  Loại thẻ
<li><code>cri_card_status</code>: Customer card status
<li><code>cri_last_time_acitve</code>:    Lần cuối tương tác
<li><code>cri_last_time_transaction</code>:   Lần cuối giao dịch
<li><code>cri_business_case</code>:   Business case
<li><code>cri_mkt_step</code>:    Loại khách hàng marketing
<li><code>cri_sale_status</code>: Tình trạng saled
<li><code>cri_marital_status</code>:  Tình trạng hôn nhân
<li><code>...</code>:  Others criteria
"""

# ****************************** Operator Key ***********************************
# * version: 1.0.0                                                              *
# *******************************************************************************
"""
@apiDefine operator_key_body
@apiVersion 1.0.0
@apiParam   (profile_filter:) {String="op_is_between","op_is_greater_equal","op_is_in","op_is_equal","op_is_greater","op_is_has","op_is_has_not","op_is_less_equal","op_is_less", "op_is_in_and"}    operator_key    Các toán tử để filter khách hàng.
"""


# ****************************** Create Audience ********************************
# * version: 1.0.0                                                              *
# *******************************************************************************
"""
@api {post} /api/v1.0/merchants/<merchant_id>/audiences Tạo tập khách hàng
@apiDescription Dịch vụ tạo tập khách hàng theo các điều kiện filter
@apiGroup Audience
@apiVersion 1.0.0
@apiName CreateAudienceV2

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    name                                       Tên tập khách hàng
@apiParam      (Body:)     {Number}    [display_status]                           Trạng thái hiển thị của tập khách hàng trên giao diện. Allowed values:<br/>
<li><code>1: Yes. Có hiển thị</code></li>
<li><code>2: No. Không hiển thị</code></li>
<br/>
Default: <code> 1. Yes</code>
@apiParam      (Body:)     {Object}    [external_data]            Dữ liệu thêm.
@apiParam      (Body:)     {Array}     [profile_scan_filter]      Dữ liệu thêm về profile scan.
@apiParam      (Body:)     {String}    audience_type      Kiểu bộ lọc. Giá trị: <code>PROFILE_FILTER,TRIGGER</code>
@apiParam      (Body:)     {Array}     nodes              List các bộ lọc.
@apiParam      (Body:)     {Int}       version            version tệp khách hàng. Default: version=2

@apiParam      (nodes:)     {Number}   position              Thứ tự của node.
@apiParam      (nodes:)     {String}  operator        Toán tử tập hợp <code>and</code>,<code>or</code>,<code>exclude</code>
@apiParam      (nodes:)     {Array}    profile_filter     Danh sách bộ lọc của node

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter
@apiParam      (profile_filter:)     {String}     [filter_type]                      Khi sử dụng bộ lọc dynamic event: <li><code>must</code>: phát sinh event</li>
                                                                                        <li><code>must_not</code>: không phát sinh event</li>


@apiParamExample    {json}    Body example:
{
  "name": "Tập khách hàng mới",
  "nodes": [
    {
      "profile_filter": [
        {
          "criteria_key": "cri_age",
          "operator_key": "op_is_between",
          "values": [
            18,
            50
          ],
          "filter_type": "must_not"
        },
        {
          "criteria_key": "cri_member_join",
          "operator_key": "op_is_between",
          "values": [
            "2017-01-01",
            "2017-09-30"
          ],
          "filter_type": "must"
        }
      ],
      "position": 0,
      "operator": null
    },
    {
      "profile_filter": [
        {
          "criteria_key": "cri_age",
          "operator_key": "op_is_between",
          "values": [
            18,
            50
          ],
          "filter_type": "must_not"
        },
        {
          "criteria_key": "cri_member_join",
          "operator_key": "op_is_between",
          "values": [
            "2017-01-01",
            "2017-09-30"
          ],
          "filter_type": "must"
        }
      ],
      "position": 1,
      "operator": "and"
    }
  ],
  "display_status": 1,
  "external_data": {},
  "audience_type": "PROFILE_FILTER,TRIGGER",
  "profile_scan_filter": {},
  "version": 2
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công"
}
"""

# ****************************** Get Audience By ID******************************
# * version: 1.0.0                                                              *
# *******************************************************************************
"""
@api {get} /api/v1.0/merchants/<merchant_id>/audiences/<audience_id> Lấy tập khách hàng theo id
@apiDescription Dịch vụ Lấy tập khách hàng theo id
@apiGroup Audience
@apiVersion 1.0.0
@apiName GetAudienceByID

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.
@apiParam      (Resource:)    {String}    audience_id                             ID của audience.


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "display_status": 1,
  "id": "00262c4c-fee5-4b81-be40-6644b0a4ec60",
  "lang": "vi",
  "message": "request thành công.",
  "nodes": [
    {
      "profile_filter": [
        {
          "criteria_key": "cri_age",
          "operator_key": "op_is_between",
          "values": [
            18,
            50
          ]
        },
        {
          "criteria_key": "cri_member_join",
          "operator_key": "op_is_between",
          "values": [
            "2017-01-01",
            "2017-09-30"
          ]
        }
      ],
      "position": 0,
      "operator": null
    },
    {
      "profile_filter": [
        {
          "criteria_key": "cri_age",
          "operator_key": "op_is_between",
          "values": [
            18,
            50
          ]
        },
        {
          "criteria_key": "cri_member_join",
          "operator_key": "op_is_between",
          "values": [
            "2017-01-01",
            "2017-09-30"
          ]
        }
      ],
      "position": 1,
      "operator": "and"
    }
  ],
  "external_data": {},
  "audience_type": "PROFILE_FILTER,TRIGGER",
  "profile_scan_filter": {}
}
"""


# ****************************** Criteria Key ***********************************
# * version: 1.0.0                                                              *
# *******************************************************************************
"""
@api {post} /internal/api/v1.0/merchants/<merchant_id>/audiences/criteria Tạo điều kiện lọc mới
@apiDescription Dịch vụ Tạo điều kiện lọc cho dynamic fields theo merchant
@apiGroup Audience
@apiVersion 1.0.0
@apiName CreateNewCriteria

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)    {Array}    new_criteria                       Danh sách criteria cần tạo

@apiParam      (new_criteria:)    {String}    key                             Criteria key mới
@apiParam      (new_criteria:)    {Array}     ops                             Mảng danh sách các operator tương ứng với criteria. <br/>
Allowed values: <code> op_is_between, op_is_empty, op_is_equal, op_is_greater, op_is_greater_equal, op_is_has, op_is_has_not, op_is_in, op_is_less, op_is_less_equal, op_is_not_empty, op_is_not_in, op_is_top, op_is_not_equal</code>
@apiParam      (new_criteria:)    {String}    name                            Criteria name
@apiParam      (new_criteria:)    {Integer}   value_type                      Loại value. Allow values:<br/>
<li><code>1- INT</code></li>
<li><code>2- FLOAT</code></li>
<li><code>3- STRING</code></li>
<li><code>4- DATE</code></li>
<li><code>5- DATETIME</code></li>
<li><code>6- TIME</code></li>
<li><code>7- OBJECT</code></li>

@apiParamExample    {json}    Body example:
{
    "new_criteria": [
      {
        "key": "cri_dynamic_field_A",
        "ops": ["op_is_in", "op_is_not_in"],
        "name": "field A",
        "value_type": 1,

      },
      {
        "key": "cri_dynamic_field_B",
        "ops": ["op_is_greater", "op_is_less"],
        "name": "field B",
        "value_type": 2,
      }
    ]

}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "tạo criteria thành công"
}

"""


# ****************************** Search Audience by filter **********************
# * version: 1.0.0                                                              *
# *******************************************************************************
"""
@api {get} /api/v1.0/merchants/<merchant_id>/audiences/get_by_ids Lấy danh sách audience bằng audience_ids
@apiDescription Lấy danh sách audience bằng audience_ids
@apiGroup Audience
@apiVersion 1.0.0
@apiName GetAudienceByIds

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Param:)    {String}    audience_ids   Danh sách audience_id, audience_id cách nhau bởi dấu <code>,</code>


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "audiences": [
    {
        "audience_type": "PROFILE_FILTER",
        "display_status": 1,
        "estimate_customers": 0,
        "id": "6c3089f3-70df-4eaa-9893-505248dc9abd",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name": "mail hợp lệ - có số điên thoại",
        "profile_filter": [
            {
                "criteria_key": "cri_email",
                "operator_key": "op_is_not_empty",
                "values": [
                    "op_is_not_empty",
                    1
                ]
            },
            {
                "criteria_key": "cri_phone",
                "operator_key": "op_is_not_empty",
                "values": [
                    "op_is_not_empty"
                ]
            }
        ],
        "type": 1
    }
  ],
  "code": 200,
  "lang": "vi",
  "message": "request thành công."
}

"""

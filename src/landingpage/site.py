#!/usr/bin/python
# -*- coding: utf8 -*-

********************************** Sync used domain *********************************
* version: 1.0.0                                                                    *
*************************************************************************************
"""
@api {post} {HOST}/landingpage/api/v2.0/sites/actions/sync-used-domain  Sync used domain
@apiDescription  API dùng để đồng bộ các domain đang được sử dụng sang module quản lý domain
@apiGroup Site
@apiVersion 1.0.0
@apiName SyncUsedDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)  {StringArray}  [site_ids]  Danh sách id của site cần sync thông tin. Nếu không có sẽ scan toàn bộ site trong hệ thống. Trường hợp scan toàn hệ thống, API sẽ cần nhiều thời gian để xử lý.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""

********************************** List site by ids *********************************
* version: 1.0.0                                                                    *
*************************************************************************************
"""
@api {post} {HOST}/landingpage/api/v2.0/site-by-ids  Lấy thông tin nhiều site theo ids
@apiDescription  API dùng để lấy thông tin nhiều site theo id. Tối đa 20 sites 1 lần request.
@apiGroup Site
@apiVersion 1.0.0
@apiName ListSiteByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse order_sort
@apiUse paging_tokens

@apiParam   (Body:)  {StringArray}  [site_ids]  Danh sách id của site. Tối đa <code>20 ids/request</code>
@apiParamExample  {json}  Example:
{
  "site_ids": [
    "6406b81ae2cdc5378xfb1",
    "6406b81ae2cdc5378xfb2"
  ]
}

@apiSuccess   {String}  id  Định danh của site.
@apiSuccess   {String}  name  Tên của site.
@apiSuccess   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   {String}  [category]  Định danh của category.
@apiSuccess   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).
@apiSuccess   {Timestamp}   [published_time]  Thời điểm publish mới nhất của site (theo giờ UTC).
@apiSuccess   {String}   created_user  Định danh user tạo site.
@apiSuccess   {String}   updated_user  Định danh của user thao tác chỉnh sửa cuối cùng trên site.
@apiSuccess   {Boolean}   published  Trạng thái xác định site đã được publish hay chưa.
@apiSuccess   {Boolean}   [connected_custom_domain]  Trạng thái để xác định xem site đã được connect tới custom domain hay chưa.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
    {
      "id": "6406b81ae2cdc5378xfb1",
      "name": "Site 1",
      "thumbnail": "https://...",
      "category": "6406c41ab81e2cdc53avxfb1",
      "created_time": 123456789678,
      "updated_time": 123456789678,
      "created_user": "ccc98ada-da3c-4fda-b210-b2361b29ab68",
      "updated_user": "43f09dac-238b-40ee-910e-1a1f019f2bb0",
      "published_time": 123456789678,
      "published": true,
      "connected_custom_domain": false
    },
    {
      "id": "6406b81ae2cdc5378xfb2",
      "name": "Site 1",
      "thumbnail": "https://...",
      "category": "6406c41ab81e2cdc53avxfb1",
      "created_time": 123456789678,
      "updated_time": 123456789678,
      "created_user": "ccc98ada-da3c-4fda-b210-b2361b29ab68",
      "updated_user": "43f09dac-238b-40ee-910e-1a1f019f2bb0",
      "published_time": 123456789678,
      "published": true,
      "connected_custom_domain": false
    }
  ]
}
"""
********************************* List version using Form ********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} {HOST}/api/sites/actions/list-version-using-form  Lấy danh sách version đang sử dụng Form
@apiDescription Lấy danh sách version của Landing page đang sử dụng form.
@apiGroup Site
@apiVersion 1.0.0
@apiName ListVerUsingForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  form_id   Định danh của form cần kiểm tra.
@apiParam   (Body:)   {StringArray}  site_ids   Danh sách định danh site cần check. Mỗi lần check tối đa 5 sites.
@apiParamExample    {json}  Body example:
{
  "form_id": "234dao70fa1sdkfkashd",
  "site_ids": [
    "642f85314d4356e983dd7415",
    "642fc65e2af4e1dbe42ea896",
    "6433c0959870fa17a0219613",
    "643ccc4417a634e25f59fe93"
  ]
}

@apiSuccess   (Site info)   {String}  id  Định danh của site.
@apiSuccess   (Site info)   {String}  name  Tên của site.
@apiSuccess   (Site info)   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   (Site info)   {String}  [category]  Định danh của category.
@apiSuccess   (Site info)   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   [published_time]  Thời điểm publish mới nhất của site (theo giờ UTC).
@apiSuccess   (Site info)   {String}   created_user  Định danh user tạo site.
@apiSuccess   (Site info)   {String}   updated_user  Định danh của user thao tác chỉnh sửa cuối cùng trên site.
@apiSuccess   (Site info)   {Boolean}   published  Trạng thái xác định site đã được publish hay chưa.
@apiSuccess   (Site info)   {Boolean}   [connected_custom_domain]  Trạng thái để xác định xem site đã được connect tới custom domain hay chưa.


@apiSuccess   (Site version)   {String}   editor_version  Phiên bản chỉnh sửa của site, được sử dụng để tránh conflict khi 2 users đang cùng tương tác chỉnh sửa.
@apiSuccess   (Site version)   {Timestamp}   editor_saved_time  Thời điểm lưu chỉnh sửa content site cuối cùng(theo giờ UTC).
@apiSuccess   (Site version)   {String}   id  Định danh của phiên bản chỉnh sửa.
@apiSuccess   (Site version)   {String=draft,versioning,published}   version_type  Loại version đang sử dụng form.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "info": {
        "id": "642f85314d4356e983dd7415",
        "name": "Site 1",
        "thumbnail": "https://...",
        "category": "6406c41ab81e2cdc53avxfb1",
        "created_time": 123456789678,
        "updated_time": 123456789678,
        "created_user": "ccc98ada-da3c-4fda-b210-b2361b29ab68",
        "updated_user": "43f09dac-238b-40ee-910e-1a1f019f2bb0",
        "published_time": 123456789678,
        "published": true,
        "connected_custom_domain": false
      }
      "versions": [
        {
          "editor_saved_time": 1681429450,
          "editor_version": "6438f63a4ab346c0bdfcd6a7",
          "id": "6438f63a4ab346c0bdfcd6a8",
          "version_type": ""
        }
      ]
    }
  ]
}
"""

********************************* Check Form Used ********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {post} {HOST}/api/sites/actions/check-form-in-used  Kiểm tra dánh sách Site đang sử dụng form.
@apiDescription Kiểm tra danh sách Landing page đang được publish có đang sử dụng form. Với đầu vào là id của Form và danh sách site, kiểm tra trong version published của các site này có đang sử dụng form kia không và trả về kết quả.
@apiGroup Site
@apiVersion 1.0.0
@apiName CheckFormInUse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  form_id   Định danh của form cần kiểm tra.
@apiParam   (Body:)   {StringArray}  site_ids   Danh sách định danh site cần check. Mỗi lần check tối đa 20 sites.
@apiParamExample    {json}  Body example:
{
  "form_id": "234dao70fa1sdkfkashd",
  "site_ids": [
    "642f85314d4356e983dd7415",
    "642fc65e2af4e1dbe42ea896",
    "6433c0959870fa17a0219613",
    "643ccc4417a634e25f59fe93",
    "64466ef018de64c2f6acd234",
    "644698d918de64c2f6acd240"
  ]
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "642f85314d4356e983dd7415",
      "result": true
    },
    {
      "id": "642fc65e2af4e1dbe42ea896",
      "result": false
    },
    {
      "id": "6433c0959870fa17a0219613",
      "result": true
    },
    {
      "id": "643ccc4417a634e25f59fe93",
      "result": true
    },
    {
      "id": "64466ef018de64c2f6acd234",
      "result": true
    },
    {
      "id": "644698d918de64c2f6acd240",
      "result": true
    }
  ]
}
"""

*********************************** Restore Site *********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {post} {HOST}/api/v2.0/sites/<site_id>/actions/restore  Restore site
@apiDescription  API khôi phục site đã xóa
@apiGroup Site
@apiVersion 1.0.0
@apiName RestoreSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của site. Các site không được phép trùng tên.
@apiParam   (Body:)   {String}  category  Định danh category để lưu site.<br/>
Một site sẽ thuộc ít nhất 1 category (default thuộc category <code>Tất cả</code>) và tối đa 2 categories (<code>Tất cả</code>, other category - nếu config)

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""
*********************************** Delete Sites *********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {post} {HOST}/landingpage/api/v2.0/sites/actions/delete  Delete sites
@apiDescription   API xóa nhiều site cùng 1 lúc.
@apiGroup Site
@apiVersion 1.0.0
@apiName DeleteSites

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse include_exclude

@apiUse json_header
@apiUse merchant_id_header


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""
*********************************** Count site *********************************
* version: 1.0.2                                                               *
* version: 1.0.1                                                               *
* version: 1.0.0                                                               *
********************************************************************************
"""
@api {post} {HOST}/landingpage/api/v2.0/sites/actions/count  Count sites
@apiDescription API đếm số lượng site trước khi thực hiện hành động (di chuyển, xóa).
@apiGroup Site
@apiVersion 1.0.2
@apiName CountSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  category  Định danh thư mục mới.
@apiParam   (Body:)   {String=published,unpublished,stopped}  [publish_status]  Lọc danh sách site theo trạng thái. Ex: <code>"publish_status": "unpublished"</code>
@apiParam   (Body:)   {Object}  match  Thông tin để match danh sách site.
@apiParam   (Body:)   {Object}  [match..include]  Thông tin bao gồm, dùng để lọc site phù hợp. Danh sách định danh site chỉ định được ưu tiên xử lý so với dùng search.
@apiParam   (Body:)   {Object}  [match..include..ids]  Danh sách định danh site cần chuyển thự mục.<br>Trong trường hợp người dùng chọn cụ thể site để chuyển.
@apiParam   (Body:)   {Object}  [match..include..category]  Định danh thư mục cần lọc.
@apiParam   (Body:)   {Object}  [match..include..search]  Tìm kiếm theo tên site. Truyền vào <code>"search": "*"</code> nếu muốn chuyển toàn bộ site trên hệ thống.
@apiParam   (Body:)   {Object}  [match..exclude]  Danh sách định danh site cần loại trừ không thực hiện chuyển thư mục.

@apiParamExample  {json}  Body example:
{
  "category": "64661306335a9ec854d0ea01",
  "publish_status": "published",
  "match": {
    "include": {
      "ids": [
          "642f85314d4356e983dd7415",
          "642fc65e2af4e1dbe42ea896",
          "6433c0959870fa17a0219613",
          "643ccc4417a634e25f59fe93",
          "64466ef018de64c2f6acd234",
          "644698d918de64c2f6acd240"
      ],
      "search": "site"
    },
    "exclude": [
      "642f85314d4356e983dd7415",
      "642fc65e2af4e1dbe42ea896"
    ]
  }
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "count": 12000
}
"""
******************
"""
@api {post} {HOST}/landingpage/api/v2.0/sites/actions/count  Count sites
@apiDescription API đếm số lượng site trước khi thực hiện hành động (di chuyển, xóa).
@apiGroup Site
@apiVersion 1.0.1
@apiName CountSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  category  Định danh thư mục mới.
@apiParam   (Body:)   {Object}  match  Thông tin để match danh sách site.
@apiParam   (Body:)   {Object}  [match..include]  Thông tin bao gồm, dùng để lọc site phù hợp. Danh sách định danh site chỉ định được ưu tiên xử lý so với dùng search.
@apiParam   (Body:)   {Object}  [match..include..ids]  Danh sách định danh site cần chuyển thự mục.<br>Trong trường hợp người dùng chọn cụ thể site để chuyển.
@apiParam   (Body:)   {Object}  [match..include..category]  Định danh thư mục cần lọc.
@apiParam   (Body:)   {Object}  [match..include..search]  Tìm kiếm theo tên site. Truyền vào <code>"search": "*"</code> nếu muốn chuyển toàn bộ site trên hệ thống.
@apiParam   (Body:)   {Object}  [match..exclude]  Danh sách định danh site cần loại trừ không thực hiện chuyển thư mục.

@apiParamExample  {json}  Body example:
{
  "category": "64661306335a9ec854d0ea01",
  "match": {
    "include": {
      "ids": [
          "642f85314d4356e983dd7415",
          "642fc65e2af4e1dbe42ea896",
          "6433c0959870fa17a0219613",
          "643ccc4417a634e25f59fe93",
          "64466ef018de64c2f6acd234",
          "644698d918de64c2f6acd240"
      ],
      "search": "site"
    },
    "exclude": [
      "642f85314d4356e983dd7415",
      "642fc65e2af4e1dbe42ea896"
    ]
  }
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "count": 12000
}
"""
*******************
"""
@api {post} {HOST}/landingpage/api/v2.0/sites/actions/count  Count sites
@apiDescription API đếm số lượng site trước khi thực hiện hành động (di chuyển, xóa).
@apiGroup Site
@apiVersion 1.0.0
@apiName CountSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  category  Định danh thư mục mới.
@apiParam   (Body:)   {Object}  match  Thông tin để match danh sách site.
@apiParam   (Body:)   {Object}  match..include  Thông tin bao gồm, dùng để lọc site phù hợp. Danh sách định danh site chỉ định được ưu tiên xử lý so với dùng search.
@apiParam   (Body:)   {Object}  match..include..sites  Danh sách định danh site cần chuyển thự mục.<br>Trong trường hợp người dùng chọn cụ thể site để chuyển.
@apiParam   (Body:)   {Object}  match..include..search  Tìm kiếm theo tên site.
@apiParam   (Body:)   {Object}  match..exclude  Danh sách định danh site cần loại trừ không thực hiện chuyển thư mục.

@apiParamExample  {json}  Body example:
{
  "category": "64661306335a9ec854d0ea01",
  "match": {
    "include": {
      "sites": [
          "642f85314d4356e983dd7415",
          "642fc65e2af4e1dbe42ea896",
          "6433c0959870fa17a0219613",
          "643ccc4417a634e25f59fe93",
          "64466ef018de64c2f6acd234",
          "644698d918de64c2f6acd240"
      ],
      "search": "site"
    },
    "exclude": {
      "sites": [
        "642f85314d4356e983dd7415",
        "642fc65e2af4e1dbe42ea896"
      ]
    }
  }
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "count": 12000
}
"""

*********************************** Move 1 site *********************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} {HOST}/landingpage/api/v2.0/sites/<site_id>/actions/move  Move 1 site
@apiDescription API chuyển thư mục của 1 site.
@apiGroup Site
@apiVersion 1.0.0
@apiName Moving1Site

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  category  Định danh thư mục mới.

@apiParamExample  {json}  Body example:
{
  "category": "646611bc335a9ec854d0ea00
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

*********************************** Move many site *********************************
* version: 1.0.1                                                                   *
* version: 1.0.0                                                                   *
************************************************************************************
"""
@api {post} {HOST}/landingpage/api/v2.0/sites/actions/move  Move sites
@apiDescription API chuyển thư mục của nhiều site. Hỗ match site theo pattern.
@apiGroup Site
@apiVersion 1.0.1
@apiName MovingSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  category  Định danh thư mục mới.
@apiParam   (Body:)   {Object}  match  Thông tin để match danh sách site.
@apiParam   (Body:)   {Object}  [match..include]  Thông tin bao gồm, dùng để lọc site phù hợp. Danh sách định danh site chỉ định được ưu tiên xử lý so với dùng search.
@apiParam   (Body:)   {Object}  [match..include..ids]  Danh sách định danh site cần chuyển thự mục.<br>Trong trường hợp người dùng chọn cụ thể site để chuyển.
@apiParam   (Body:)   {Object}  [match..include..category]  Định danh thư mục cần lọc.
@apiParam   (Body:)   {Object}  [match..include..search]  Tìm kiếm theo tên site.
@apiParam   (Body:)   {StringArray}  [match..exclude]  Danh sách định danh site cần loại trừ không thực hiện chuyển thư mục.

@apiParamExample  {json}  Body example:
{
  "category": "64661306335a9ec854d0ea01",
  "match": {
    "include": {
      "ids": [
          "642f85314d4356e983dd7415",
          "642fc65e2af4e1dbe42ea896",
          "6433c0959870fa17a0219613",
          "643ccc4417a634e25f59fe93",
          "64466ef018de64c2f6acd234",
          "644698d918de64c2f6acd240"
      ],
      "category": "64661306335a9ec854d0ea02",
      "search": "site"
    },
    "exclude": [
      "642f85314d4356e983dd7415",
      "642fc65e2af4e1dbe42ea896"
    ]
  }
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""
******************
"""
@api {post} {HOST}/landingpage/api/v2.0/sites/actions/move  Move sites
@apiDescription API chuyển thư mục của nhiều site. Hỗ match site theo pattern.
@apiGroup Site
@apiVersion 1.0.0
@apiName MovingSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  category  Định danh thư mục mới.
@apiParam   (Body:)   {Object}  match  Thông tin để match danh sách site.
@apiParam   (Body:)   {Object}  match..include  Thông tin bao gồm, dùng để lọc site phù hợp. Danh sách định danh site chỉ định được ưu tiên xử lý so với dùng search.
@apiParam   (Body:)   {Object}  match..include..sites  Danh sách định danh site cần chuyển thự mục.<br>Trong trường hợp người dùng chọn cụ thể site để chuyển.
@apiParam   (Body:)   {Object}  match..include..search  Tìm kiếm theo tên site.
@apiParam   (Body:)   {Object}  match..exclude  Danh sách định danh site cần loại trừ không thực hiện chuyển thư mục.

@apiParamExample  {json}  Body example:
{
  "category": "64661306335a9ec854d0ea01",
  "match": {
    "include": {
      "sites": [
          "642f85314d4356e983dd7415",
          "642fc65e2af4e1dbe42ea896",
          "6433c0959870fa17a0219613",
          "643ccc4417a634e25f59fe93",
          "64466ef018de64c2f6acd234",
          "644698d918de64c2f6acd240"
      ],
      "search": "site"
    },
    "exclude": {
      "sites": [
          "642f85314d4356e983dd7415",
          "642fc65e2af4e1dbe42ea896"
      ]
    }
  }
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Export site **********************************
* version: 1.0.1                                                                *
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} {HOST}/landingpage/api/v2.0/sites/<site_id>/actions/export  Export site
@apiDescription  API dùng để export bản editor của site (.mopage), sau đó có thể import lại.
@apiGroup Site
@apiVersion 1.0.0
@apiName ExportSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {Boolean=true,false}   [with_attachment]  Tham số bật/tắt export site có kèm các resources: images,...

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""
*******************
"""
@api {get} {HOST}/landingpage/api/v2.0/sites/<site_id>/actions/export  Export site
@apiDescription  API dùng để export bản editor của site (.mopage), sau đó có thể import lại.
@apiGroup Site
@apiVersion 1.0.0
@apiName ExportSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header



@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Import site **********************************
* version: 1.0.1                                                                *
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} {HOST}/landingpage/api/v2.0/sites/actions/import  Import site
@apiDescription  API tạo site từ file editor (.mopage).
@apiGroup Site
@apiVersion 1.0.1
@apiName ImportSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse form_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của site mới. Các site không được phép trùng tên.
@apiParam   (Body:)   {String}  category  Định danh category để lưu site.<br/>
@apiParam   (Body:)   {File}  file  File data.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""
******************
"""
@api {post} {HOST}/landingpage/api/v2.0/sites/actions/import  Import site
@apiDescription  API tạo site từ file editor (.mopage).
@apiGroup Site
@apiVersion 1.0.0
@apiName ImportSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse form_header
@apiUse merchant_id_header

@apiParam   (Body:)   {File}  file  File data.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Duplicate site **********************************
* version: 1.0.0                                                                   *
************************************************************************************
"""
@api {post} {HOST}/landingpage/api/v2.0/sites/actions/duplicate  Duplicate site
@apiDescription  API dùng để clone 1 site.
@apiGroup Site
@apiVersion 1.0.0
@apiName DuplicateSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của site mới. Các site không được phép trùng tên.
@apiParam   (Body:)   {String}  [category]  Định danh của category.
@apiParam   (Body:)   {String}  [site_id]  Định danh của site nguồn để duplicate.
@apiParamExample {json} Body example
{
  "name": "New site 1",
  "thumbnail": "https://...",
  "category": "6406c41ab81e2cdc53a9jdoe",
  "site_id": "6406b81ae2cdc5378xfb1"
}

@apiSuccess   (Site info)   {String}  id  Định danh của site.
@apiSuccess   (Site info)   {String}  name  Tên của site.
@apiSuccess   (Site info)   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   (Site info)   {String}  [category]  Định danh của category.
@apiSuccess   (Site info)   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   [published_time]  Thời điểm publish mới nhất của site (theo giờ UTC).
@apiSuccess   (Site info)   {String}   created_user  Định danh user tạo site.
@apiSuccess   (Site info)   {String}   updated_user  Định danh của user thao tác chỉnh sửa cuối cùng trên site.
@apiSuccess   (Site info)   {Boolean}   published  Trạng thái xác định site đã được publish hay chưa.
@apiSuccess   (Site info)   {Boolean}   [connected_custom_domain]  Trạng thái để xác định xem site đã được connect tới custom domain hay chưa.

@apiSuccess   (Site insights)   {Number}   [visit_amount]  Số lượt visit site.
@apiSuccess   (Site insights)   {Number}   [conversion_rate]  Tỷ lệ chuyển đổi của profile visit site.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "site": {
    "info": {
      "id": "6406b81ae2cdc5378xfb1",
      "name": "New site 1",
      "thumbnail": "https://...",
      "category": "6406c41ab81e2cdc53avxfb1",
      "created_time": 123456789678,
      "updated_time": 123456789678,
      "created_user": "ccc98ada-da3c-4fda-b210-b2361b29ab68",
      "updated_user": "43f09dac-238b-40ee-910e-1a1f019f2bb0",
      "published_time": 123456789678,
      "published": true,
      "connected_custom_domain": false
    },
    "insights": {
      "visit_amount": 0,
      "conversion_rate": 0
    },
  },
}
"""

********************************** Update site **********************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {patch} {HOST}/landingpage/api/v2.0/sites/<site_id>   Cập nhật site
@apiDescription API dùng để cập nhật thông tin site
@apiGroup Site
@apiVersion 1.0.0
@apiName UpdateSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  [name]  Tên của site. Các site không được phép trùng tên.
@apiParam   (Body:)   {String}  [thumbnail]   Link ảnh thumbnail của site.
@apiParam   (Body:)   {String}  [category]  Định danh category để lưu site.<br/>
Một site sẽ thuộc ít nhất 1 category (default thuộc category <code>Tất cả</code>) và tối đa 2 categories (<code>Tất cả</code>, other category - nếu config).
@apiParamExample {json} Body example
{
  "name": "Site 1",
  "thumbnail": "https://...",
  "category": "6406c41ab81ae2cdc53avxfb1"
}

@apiSuccess   (Site info)   {String}  id  Định danh của site.
@apiSuccess   (Site info)   {String}  name  Tên của site.
@apiSuccess   (Site info)   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   (Site info)   {String}  [category]  Định danh của category.
@apiSuccess   (Site info)   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   [published_time]  Thời điểm publish mới nhất của site (theo giờ UTC).
@apiSuccess   (Site info)   {String}   created_user  Định danh user tạo site.
@apiSuccess   (Site info)   {String}   updated_user  Định danh của user thao tác chỉnh sửa cuối cùng trên site.
@apiSuccess   (Site info)   {Boolean}   published  Trạng thái xác định site đã được publish hay chưa.
@apiSuccess   (Site info)   {Boolean}   [connected_custom_domain]  Trạng thái để xác định xem site đã được connect tới custom domain hay chưa.

@apiSuccess   (Site insights)   {Number}   [visit_amount]  Số lượt visit site.
@apiSuccess   (Site insights)   {Number}   [conversion_rate]  Tỷ lệ chuyển đổi của profile visit site.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "site": {
    "info": {
      "id": "6406b81ae2cdc5378xfb1",
      "name": "Site 1",
      "thumbnail": "https://...",
      "category": "6406c41ab81e2cdc53avxfb1",
      "created_time": 123456789678,
      "updated_time": 123456789678,
      "created_user": "ccc98ada-da3c-4fda-b210-b2361b29ab68",
      "updated_user": "43f09dac-238b-40ee-910e-1a1f019f2bb0",
      "published_time": 123456789678,
      "published": true,
      "connected_custom_domain": false
    },
    "insights": {
      "visit_amount": 0,
      "conversion_rate": 0
    },
  },
}
"""

********************************** Detail site **********************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {get} {HOST}/landingpage/api/v2.0/sites/<site_id>  Lấy chi tiết site
@apiDescription  API dùng để lấy dữ liệu chi tiết của 1 site.
@apiGroup Site
@apiVersion 1.0.0
@apiName Read1Site

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccess   (Site info)   {String}  id  Định danh của site.
@apiSuccess   (Site info)   {String}  name  Tên của site.
@apiSuccess   (Site info)   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   (Site info)   {String}  [category]  Định danh của category.
@apiSuccess   (Site info)   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   [published_time]  Thời điểm publish mới nhất của site (theo giờ UTC).
@apiSuccess   (Site info)   {String}   created_user  Định danh user tạo site.
@apiSuccess   (Site info)   {String}   updated_user  Định danh của user thao tác chỉnh sửa cuối cùng trên site.
@apiSuccess   (Site info)   {Boolean}   published  Trạng thái xác định site đã được publish hay chưa.
@apiSuccess   (Site info)   {Boolean}   [connected_custom_domain]  Trạng thái để xác định xem site đã được connect tới custom domain hay chưa.

@apiSuccess   (Site insights)   {Number}   [visit_amount]  Số lượt visit site.
@apiSuccess   (Site insights)   {Number}   [conversion_rate]  Tỷ lệ chuyển đổi của profile visit site.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "site": {
    "info": {
      "id": "6406b81ae2cdc5378xfb1",
      "name": "Site 1",
      "thumbnail": "https://...",
      "category": "6406c41ab81e2cdc53avxfb1",
      "created_time": 123456789678,
      "updated_time": 123456789678,
      "created_user": "ccc98ada-da3c-4fda-b210-b2361b29ab68",
      "updated_user": "43f09dac-238b-40ee-910e-1a1f019f2bb0",
      "published_time": 123456789678,
      "published": true,
      "connected_custom_domain": false
    },
    "insights": {
      "visit_amount": 0,
      "conversion_rate": 0
    }
  }
}
"""

********************************** List deleted sites *********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {get} {HOST}/landingpage/api/v2.0/sites/categories/recycle-bin  Lấy danh sách site chờ xóa
@apiDescription  API dùng để lấy danh sách các site trong thùng rác<br/>
Hỗ trợ tìm kiếm theo tên site. Sắp xếp theo số lượt visit.
@apiGroup Site
@apiVersion 1.0.0
@apiName ListRecycleBin

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse order_sort
@apiUse paging_tokens

@apiParam   (Query:)  {String}  [search]  Tìm kiếm theo tên site. Trả về danh sách site có tên chứa từ cần tìm.

@apiSuccess   (Site info)   {String}  id  Định danh của site.
@apiSuccess   (Site info)   {String}  name  Tên của site.
@apiSuccess   (Site info)   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   (Site info)   {String}  [category]  Định danh của category.
@apiSuccess   (Site info)   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   [published_time]  Thời điểm publish mới nhất của site (theo giờ UTC).
@apiSuccess   (Site info)   {String}   created_user  Định danh user tạo site.
@apiSuccess   (Site info)   {String}   updated_user  Định danh của user thao tác chỉnh sửa cuối cùng trên site.
@apiSuccess   (Site info)   {Boolean}   published  Trạng thái xác định site đã được publish hay chưa.
@apiSuccess   (Site info)   {Boolean}   [connected_custom_domain]  Trạng thái để xác định xem site đã được connect tới custom domain hay chưa.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
    {
      "info": {
        "id": "6406b81ae2cdc5378xfb1",
        "name": "Site 1",
        "thumbnail": "https://...",
        "category": "6406c41ab81e2cdc53avxfb1",
        "created_time": 123456789678,
        "updated_time": 123456789678,
        "created_user": "ccc98ada-da3c-4fda-b210-b2361b29ab68",
        "updated_user": "43f09dac-238b-40ee-910e-1a1f019f2bb0",
        "published_time": 123456789678,
        "published": true,
        "connected_custom_domain": false
      }
    }
  ]
}
"""

********************************** List sites *********************************
* version: 1.0.2                                                              *
* version: 1.0.1                                                              *
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {get} {HOST}/landingpage/api/v2.0/sites  Lấy danh sách site
@apiDescription  API dùng để lấy danh sách site.<br/>
Hỗ trợ tìm kiếm theo tên site. Sắp xếp theo số lượt visit.
@apiGroup Site
@apiVersion 1.0.2
@apiName ListSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse order_sort
@apiUse paging_tokens

@apiParam   (Query:)  {String}  [search]  Tìm kiếm theo tên site. Trả về danh sách site có tên chứa từ cần tìm.
@apiParam   (Query:)  {String}  [category]  Lọc danh sách site theo thư mục. Ex: <code>&category=6406c41ab81a23456ad</code>
@apiParam   (Query:)  {String=published,unpublished,stopped}  [publish_status]  Lọc danh sách site theo trạng thái. Ex: <code>&publish_status=unpublished</code>

@apiSuccess   (Site info)   {String}  id  Định danh của site.
@apiSuccess   (Site info)   {String}  name  Tên của site.
@apiSuccess   (Site info)   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   (Site info)   {String}  [category]  Định danh của category.
@apiSuccess   (Site info)   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   [published_time]  Thời điểm publish mới nhất của site (theo giờ UTC).
@apiSuccess   (Site info)   {String}   created_user  Định danh user tạo site.
@apiSuccess   (Site info)   {String}   updated_user  Định danh của user thao tác chỉnh sửa cuối cùng trên site.
@apiSuccess   (Site info)   {Boolean}   published  Trạng thái xác định site đã được publish hay chưa.
@apiSuccess   (Site info)   {Boolean}   [connected_custom_domain]  Trạng thái để xác định xem site đã được connect tới custom domain hay chưa.

@apiSuccess   (Site insights)   {Number}   [visit_amount]  Số lượt visit site.
@apiSuccess   (Site insights)   {Number}   [conversion_rate]  Tỷ lệ chuyển đổi của profile visit site.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
    {
      "info": {
        "id": "6406b81ae2cdc5378xfb1",
        "name": "Site 1",
        "thumbnail": "https://...",
        "category": "6406c41ab81e2cdc53avxfb1",
        "created_time": 123456789678,
        "updated_time": 123456789678,
        "created_user": "ccc98ada-da3c-4fda-b210-b2361b29ab68",
        "updated_user": "43f09dac-238b-40ee-910e-1a1f019f2bb0",
        "published_time": 123456789678,
        "published": true,
        "connected_custom_domain": false
      },
      "insights": {
        "visit_amount": 0,
        "conversion_rate": 0
      },
    }
  ]
}
"""
*******************
"""
@api {get} {HOST}/landingpage/api/v2.0/sites  Lấy danh sách site
@apiDescription  API dùng để lấy danh sách site.<br/>
Hỗ trợ tìm kiếm theo tên site. Sắp xếp theo số lượt visit.
@apiGroup Site
@apiVersion 1.0.1
@apiName ListSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse order_sort
@apiUse paging_tokens

@apiParam   (Query:)  {String}  [search]  Tìm kiếm theo tên site. Trả về danh sách site có tên chứa từ cần tìm.
@apiParam   (Query:)  {String}  [category]  Lọc danh sách site theo thư mục. Ex: <code>&category=6406c41ab81a23456ad</code>

@apiSuccess   (Site info)   {String}  id  Định danh của site.
@apiSuccess   (Site info)   {String}  name  Tên của site.
@apiSuccess   (Site info)   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   (Site info)   {String}  [category]  Định danh của category.
@apiSuccess   (Site info)   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   [published_time]  Thời điểm publish mới nhất của site (theo giờ UTC).
@apiSuccess   (Site info)   {String}   created_user  Định danh user tạo site.
@apiSuccess   (Site info)   {String}   updated_user  Định danh của user thao tác chỉnh sửa cuối cùng trên site.
@apiSuccess   (Site info)   {Boolean}   published  Trạng thái xác định site đã được publish hay chưa.
@apiSuccess   (Site info)   {Boolean}   [connected_custom_domain]  Trạng thái để xác định xem site đã được connect tới custom domain hay chưa.

@apiSuccess   (Site insights)   {Number}   [visit_amount]  Số lượt visit site.
@apiSuccess   (Site insights)   {Number}   [conversion_rate]  Tỷ lệ chuyển đổi của profile visit site.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
    {
      "info": {
        "id": "6406b81ae2cdc5378xfb1",
        "name": "Site 1",
        "thumbnail": "https://...",
        "category": "6406c41ab81e2cdc53avxfb1",
        "created_time": 123456789678,
        "updated_time": 123456789678,
        "created_user": "ccc98ada-da3c-4fda-b210-b2361b29ab68",
        "updated_user": "43f09dac-238b-40ee-910e-1a1f019f2bb0",
        "published_time": 123456789678,
        "published": true,
        "connected_custom_domain": false
      },
      "insights": {
        "visit_amount": 0,
        "conversion_rate": 0
      },
    }
  ]
}
"""
*******************
"""
@api {get} {HOST}/landingpage/api/v2.0/sites  Lấy danh sách site
@apiDescription  API dùng để lấy danh sách site.<br/>
Hỗ trợ tìm kiếm theo tên site. Sắp xếp theo số lượt visit.
@apiGroup Site
@apiVersion 1.0.0
@apiName ListSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse order_sort
@apiUse paging_tokens

@apiParam   (Query:)  {String}  [search]  Tìm kiếm theo tên site. Trả về danh sách site có tên chứa từ cần tìm.

@apiSuccess   (Site info)   {String}  id  Định danh của site.
@apiSuccess   (Site info)   {String}  name  Tên của site.
@apiSuccess   (Site info)   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   (Site info)   {String}  [category]  Định danh của category.
@apiSuccess   (Site info)   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   [published_time]  Thời điểm publish mới nhất của site (theo giờ UTC).
@apiSuccess   (Site info)   {String}   created_user  Định danh user tạo site.
@apiSuccess   (Site info)   {String}   updated_user  Định danh của user thao tác chỉnh sửa cuối cùng trên site.
@apiSuccess   (Site info)   {Boolean}   published  Trạng thái xác định site đã được publish hay chưa.
@apiSuccess   (Site info)   {Boolean}   [connected_custom_domain]  Trạng thái để xác định xem site đã được connect tới custom domain hay chưa.

@apiSuccess   (Site insights)   {Number}   [visit_amount]  Số lượt visit site.
@apiSuccess   (Site insights)   {Number}   [conversion_rate]  Tỷ lệ chuyển đổi của profile visit site.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
    {
      "info": {
        "id": "6406b81ae2cdc5378xfb1",
        "name": "Site 1",
        "thumbnail": "https://...",
        "category": "6406c41ab81e2cdc53avxfb1",
        "created_time": 123456789678,
        "updated_time": 123456789678,
        "created_user": "ccc98ada-da3c-4fda-b210-b2361b29ab68",
        "updated_user": "43f09dac-238b-40ee-910e-1a1f019f2bb0",
        "published_time": 123456789678,
        "published": true,
        "connected_custom_domain": false
      },
      "insights": {
        "visit_amount": 0,
        "conversion_rate": 0
      },
    }
  ]
}
"""
********************************** Delete 1 site *********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {delete} {HOST}/landingpage/api/v2.0/api/sites/<site_id>  Xóa 1 site
@apiDescription  API dùng để xóa 1 site cụ thể.
@apiGroup Site
@apiVersion 1.0.0
@apiName Delete1Site

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)  {Integer=1,0}   [permanent]   Tham số bật/tắt cơ chế xóa site vĩnh viễn.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Create site *********************************
* version: 1.0.1                                                               *
* version: 1.0.0                                                               *
********************************************************************************
"""
@api {post} {HOST}/landingpage/api/v2.0/api/sites   Tạo site mới
@apiDescription API dùng để tạo site mới
@apiGroup Site
@apiVersion 1.0.1
@apiName CreateSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của site. Các site không được phép trùng tên.
@apiParam   (Body:)   {String}  [category]  Định danh category để lưu site.<br/>
Một site sẽ thuộc ít nhất 1 category (default thuộc category <code>Tất cả</code>) và tối đa 2 categories (<code>Tất cả</code>, other category - nếu config)
@apiParam   (Body:)   {String}  [template_id]   Định danh template dùng để generate site.
@apiParamExample {json} Body example
{
  "name": "Site 1",
  "thumbnail": "https://...",
  "category": "6406c41ab81ae2cdc53avxfb1",
  "template_id": "6406c41ab81ae2cdc53avaldj"
}

@apiSuccess   (Site info)   {String}  id  Định danh của site.
@apiSuccess   (Site info)   {String}  name  Tên của site.
@apiSuccess   (Site info)   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   (Site info)   {String}  [category]  Định danh của category.
@apiSuccess   (Site info)   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   [published_time]  Thời điểm publish mới nhất của site (theo giờ UTC).
@apiSuccess   (Site info)   {String}   created_user  Định danh user tạo site.
@apiSuccess   (Site info)   {String}   updated_user  Định danh của user thao tác chỉnh sửa cuối cùng trên site.
@apiSuccess   (Site info)   {Boolean}   published  Trạng thái xác định site đã được publish hay chưa.
@apiSuccess   (Site info)   {Boolean}   [connected_custom_domain]  Trạng thái để xác định xem site đã được connect tới custom domain hay chưa.

@apiSuccess   (Site insights)   {Number}   [visit_amount]  Số lượt visit site.
@apiSuccess   (Site insights)   {Number}   [conversion_rate]  Tỷ lệ chuyển đổi của profile visit site.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "site": {
    "info": {
      "id": "6406b81ae2cdc5378xfb1",
      "name": "Site 1",
      "thumbnail": "https://...",
      "category": "6406c41ab81e2cdc53avxfb1",
      "created_time": 123456789678,
      "updated_time": 123456789678,
      "created_user": "ccc98ada-da3c-4fda-b210-b2361b29ab68",
      "updated_user": "43f09dac-238b-40ee-910e-1a1f019f2bb0",
      "published_time": 123456789678,
      "published": true,
      "connected_custom_domain": false
    },
    "insights": {
      "visit_amount": 0,
      "conversion_rate": 0
    },
  },
}
"""
******************
"""
@api {post} {HOST}/landingpage/api/v2.0/api/sites   Tạo site mới
@apiDescription API dùng để tạo site mới
@apiGroup Site
@apiVersion 1.0.0
@apiName CreateSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của site. Các site không được phép trùng tên.
@apiParam   (Body:)   {String}  [category]  Định danh category để lưu site.<br/>
Một site sẽ thuộc ít nhất 1 category (default thuộc category <code>Tất cả</code>) và tối đa 2 categories (<code>Tất cả</code>, other category - nếu config).
@apiParamExample {json} Body example
{
  "name": "Site 1",
  "thumbnail": "https://...",
  "category": "6406c41ab81ae2cdc53avxfb1"
}

@apiSuccess   (Site info)   {String}  id  Định danh của site.
@apiSuccess   (Site info)   {String}  name  Tên của site.
@apiSuccess   (Site info)   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   (Site info)   {String}  [category]  Định danh của category.
@apiSuccess   (Site info)   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   [published_time]  Thời điểm publish mới nhất của site (theo giờ UTC).
@apiSuccess   (Site info)   {String}   created_user  Định danh user tạo site.
@apiSuccess   (Site info)   {String}   updated_user  Định danh của user thao tác chỉnh sửa cuối cùng trên site.
@apiSuccess   (Site info)   {Boolean}   published  Trạng thái xác định site đã được publish hay chưa.
@apiSuccess   (Site info)   {Boolean}   [connected_custom_domain]  Trạng thái để xác định xem site đã được connect tới custom domain hay chưa.

@apiSuccess   (Site insights)   {Number}   [visit_amount]  Số lượt visit site.
@apiSuccess   (Site insights)   {Number}   [conversion_rate]  Tỷ lệ chuyển đổi của profile visit site.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "site": {
    "info": {
      "id": "6406b81ae2cdc5378xfb1",
      "name": "Site 1",
      "thumbnail": "https://...",
      "category": "6406c41ab81e2cdc53avxfb1",
      "created_time": 123456789678,
      "updated_time": 123456789678,
      "created_user": "ccc98ada-da3c-4fda-b210-b2361b29ab68",
      "updated_user": "43f09dac-238b-40ee-910e-1a1f019f2bb0",
      "published_time": 123456789678,
      "published": true,
      "connected_custom_domain": false
    },
    "insights": {
      "visit_amount": 0,
      "conversion_rate": 0
    },
  },
}
"""

********************************** Publish site *********************************
* version: 1.0.1                                                                *
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} {HOST}/landingpage/api/v2.0/api/sites/<site_id>/actions/publish   Xuất bản 1 site
@apiDescription API dùng để xuất bản 1 site
@apiGroup Site
@apiVersion 1.0.1
@apiName PublishSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}      body_html           Body của trang site cần xuất bản.
@apiParam   (Body:)   {String}      name_url            Tên của URL.
@apiParam   (Body:)   {String}      short_domain        Domain của shorten link.
@apiParam   (Body:)   {String}      protocol_domain     Giao thức mạng + domain.
@apiParam   (Body:)   {String}      short_url           Chuỗi ký tự phía sau domain, đại diện cho url gốc.
@apiParam   (Body:)   {String}      tracking_code       Mã tracking event của site.


@apiParamExample {json} Body example
{
    "body_html": "html.html",
    "name_url": "mobio",
    "short_domain": "url.mobio.io/",
    "protocol_domain": "http://url.mobio.io/",
    "short_url": "kt",
    "tracking_code": "A97B3J8M"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xuất bản 1 site

@apiSuccess {String}            data.url                      Link sau khi đã publish và rút ngọn

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "url": "http://..."
  }
}
"""
*******************
"""
@api {post} {HOST}/landingpage/api/v2.0/api/sites/<site_id>/actions/publish   Xuất bản 1 site
@apiDescription API dùng để xuất bản 1 site
@apiGroup Site
@apiVersion 1.0.0
@apiName PublishSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}      body_html           Body của trang site cần xuất bản.
@apiParam   (Body:)   {String}      name_url            Tên của URL.
@apiParam   (Body:)   {String}      short_domain        Domain của shorten link.
@apiParam   (Body:)   {String}      protocol_domain     Giao thức mạng + domain.
@apiParam   (Body:)   {String}      short_url           Chuỗi ký tự phía sau domain, đại diện cho url gốc.


@apiParamExample {json} Body example
{
    "body_html": "html.html",
    "name_url": "mobio",
    "short_domain": "url.mobio.io/",
    "protocol_domain": "http://url.mobio.io/",
    "short_url": "kt"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xuất bản 1 site

@apiSuccess {String}            data.url                      Link sau khi đã publish và rút ngọn

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "url": "http://..."
  }
}
"""

********************************** Un_publish site ******************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} {HOST}/landingpage/api/v2.0/api/sites/<site_id>/actions/un-publish   Bỏ xuất bản 1 site
@apiDescription API dùng để bỏ xuất bản 1 site đã xuất bản trước đó 
@apiGroup Site
@apiVersion 1.0.0
@apiName UnPublishSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Bỏ xuất bản 1 site

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""
#!/usr/bin/python
# -*- coding: utf8 -*-

******************************** Lock tenant ********************************
* version: 1.0.0                                                            *
*****************************************************************************
"""
@api {post} /tenants/actions/approve Duyệt tenant
@apiDescription Dịch vụ duyệt tài khoản tenant
@apiGroup Tenant
@apiVersion 1.0.0
@apiName ApproveTenant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {StringArray}   tenants   Danh sách các tenant sẽ được duyệt.
@apiParamExample  {json}  Example:
{
  "tenants": [
    "2b3b865c-4388-4047-997d-6d85d121ac94",
    "9556c91e-7e95-49bd-b72d-72b7c0b19ef2"
  ]
}

@apiSuccess   approved_tenants  Danh sách các tenant được duyệt.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "lock success",
  "approved_tenants": [
    "2b3b865c-4388-4047-997d-6d85d121ac94",
    "9556c91e-7e95-49bd-b72d-72b7c0b19ef2"
  ]
}
"""

********************************* Search tenant *********************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {get} /tenants/actions/search Tìm kiếm tenant
@apiDescription Dịch vụ tìm kiếm tenant trên hệ thống
@apiGroup Tenant
@apiVersion 1.0.0
@apiName SearchTenant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}  [name]  Tên cần tìm kiếm

@apiSuccess   {String}  merchant_id   Định danh tenant
@apiSuccess   {String}  merchant_name   Tên tenant
@apiSuccess   {Number}  status   Trạng thái tenant
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "merchant_id": "5a098628-40ec-4a70-a373-56128cc9e361",
    "merchant_name": "ABC",
    "status": 1
  }
}
"""

******************************** Register tenant ********************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} /tenants/actions/register Đăng ký tenant
@apiDescription Dịch vụ để đăng ký tenant
@apiGroup Tenant
@apiVersion 1.0.0
@apiName RegisterTenant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}  name  Tên của tenant
@apiParam   (Body:)   {String}  email  Địa chỉ email liên lạc
@apiParam   (Body:)   {String}  [phone_number]  Số điện thoại liên lạc
@apiParam   (Body:)   {String}  [category_id]   Định danh category
@apiParam   (Body:)   {String}  [referer]  Mã nhân viên giới thiệu
@apiParam   (Body:)   {String}  package_code  Mã license package
@apiParam   (Body:)   {String=mobio_product}  [server_code]  Mã server quản lý tenant.
@apiParamExample  {json}  Example:
{
  "name": "Mobio Shop",
  "email": "",
  "phone_number": "",
  "category_id": "",
  "referer": "",
  "package_code": "",
  "server_code": ""
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Đã nhận yêu cầu đăng ký thành công."
}
"""

******************************** Deactive tenant ********************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} /tenants/actions/deactive Ngừng dịch vụ với tenant
@apiDescription Dịch vụ chuyển trạng thái tenant sang ngừng hoạt động
@apiGroup Tenant
@apiVersion 1.0.0
@apiName DeactiveTenant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {StringArray}   tenants   Danh sách các tenant cần ngừng hoạt động.
@apiParamExample  {json}  Example:
{
  "tenants": [
    "2b3b865c-4388-4047-997d-6d85d121ac94",
    "9556c91e-7e95-49bd-b72d-72b7c0b19ef2"
  ]
}

@apiSuccess   deactived_tenants  Danh sách các tenant đã bị ngừng hoạt động.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Deactived success"
  "deactived_tenants": [
    "2b3b865c-4388-4047-997d-6d85d121ac94",
    "9556c91e-7e95-49bd-b72d-72b7c0b19ef2"
  ]
}
"""

******************************** Log Activity ********************************
* version: 1.0.0                                                             *
******************************************************************************
"""
@api {get} /tenants/<merchant_id>/activities Lịch sử hoạt động
@apiDescription Dịch vụ lấy danh sách lịch sử hoạt động
@apiGroup Activity
@apiVersion 1.0.0
@apiName ListActivity

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse since_until_query

@apiSuccess   {String}  id  Định danh activity
@apiSuccess   {String}  action_time   Thời gian thực hiện action
@apiSuccess   {String}  updated_user  Định danh user thực hiện
@apiSuccess   {String}  action_type   Hành động <br />
<li><code>register</code>: đăng ký</li>
<li><code>approved</code>: được duyệt</li>
<li><code>locked</code>: bị khoá</li>
<li><code>unlock</code>: mở khoá</li>
<li><code>expired_package</code>: hết hạn gói dịch vụ</li>
<li><code>buy_new_package</code>: mua thêm gói dịch vụ</li>
<li><code>change_package</code>: đổi gói dịch vụ</li>
<li><code>stopped</code>: ngừng hoạt động</li>

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "10e4c94e-b3c5-45d9-8d62-3cc54c035989",
      "action_time": "2019-12-31T12:00:00Z",
      "updated_user": "bac7574d-075b-45ba-8f0f-d6262fcac19f",
      "action_type": "register",
      "action_group": "transaction",
      "extend": {
        "transaction_code": "AW3DHAOS"
      }
    }
  ]
}
"""

******************************** Download license ********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {get} /tenants/actions/download-license Tải license hiện tại của tenant
@apiDescription Dịch vụ lấy license hiện tại của 1 tenant
@apiGroup Tenant
@apiVersion 1.0.0
@apiName DownloadLicense
@apiIgnore

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
"""

******************************** Unlock tenant ********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} /tenants/actions/unlock Mở khoá tenant
@apiDescription Dịch vụ mở khoá tài khoản tenant.
@apiGroup Tenant
@apiVersion 1.0.0
@apiName UnlockTenant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {StringArray}   tenants   Danh sách các tenant cần mở khoá.
@apiParamExample  {json}  Example:
{
  "tenants": [
    "2b3b865c-4388-4047-997d-6d85d121ac94",
    "9556c91e-7e95-49bd-b72d-72b7c0b19ef2"
  ]
}

@apiSuccess   unlocked_tenants  Danh sách các tenant đã được mở khoá.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "unlock success"
  "unlocked_tenants": [
    "2b3b865c-4388-4047-997d-6d85d121ac94",
    "9556c91e-7e95-49bd-b72d-72b7c0b19ef2"
  ]
}
"""

******************************** Lock tenant ********************************
* version: 1.0.0                                                            *
*****************************************************************************
"""
@api {post} /tenants/actions/lock Khoá tenant
@apiDescription Dịch vụ khoá tài khoản tenant
@apiGroup Tenant
@apiVersion 1.0.0
@apiName LockTenant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {StringArray}   tenants   Danh sách các tenant sẽ bị khoá.
@apiParamExample  {json}  Example:
{
  "tenants": [
    "2b3b865c-4388-4047-997d-6d85d121ac94",
    "9556c91e-7e95-49bd-b72d-72b7c0b19ef2"
  ]
}

@apiSuccess   locked_tenants  Danh sách các tenant đã khoá.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "lock success",
  "locked_tenants": [
    "2b3b865c-4388-4047-997d-6d85d121ac94",
    "9556c91e-7e95-49bd-b72d-72b7c0b19ef2"
  ]
}
"""

************************************ List Tenant ************************************
* version: 1.0.4                                                                    *
* version: 1.0.3                                                                    *
* version: 1.0.2                                                                    *
* version: 1.0.1                                                                    *
* version: 1.0.0                                                                    *
*************************************************************************************
"""
@api {post} /tenants/actions/get-list Lấy danh sách tenant
@apiDescription Dịch vụ lấy danh sách các tenant
@apiGroup Tenant
@apiVersion 1.0.4
@apiName ListTenant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse json_header

@apiParam   (Query:)   {String}  [search]   Chuỗi tìm kiếm

@apiParam   (Body:)  {Array}  packages  List package cần lọc.
<li><code>trial_package</code>: gói Trial</li>
<li><code>pay_as_you_go_package</code>: gói Pay As You Go</li>
<li><code>sme_enterprise_package</code>: gói SME / enterprise</li>
<li><code>corporate_package</code>: gói Corporate</li>
@apiParam   (Body:)  {Array}  status  Danh sách status cần lọc
@apiParam   (Body:)   {Object}  expired_day   Thông tin bộ lọc số ngày còn lại
@apiParam   (Body:)   {String}  expired_day..operator_key   Mã toán tử<br />
<li><code>op_is_equal</code>: So sánh bằng</li>
<li><code>op_is_greater_equal</code>: Lớn hơn hoặc bằng</li>
<li><code>op_is_greater</code>: Lớn hơn</li>
<li><code>op_is_between</code>: Giữa</li>
<li><code>op_is_less_equal</code>: Nhỏ hơn hoặc bằng</li>
<li><code>op_is_less</code>: Nhỏ hơn</li>
@apiParam   (Body:)   {String}  expired_day..values   Danh sách giá trị
@apiParamExample  {json}  Body example
{
  "packages": [
    "trial_package",
    "pay_as_you_go_package",
    "sme_enterprise_package"
  ],
  "status": [
    -1, 0
  ],
  "expired_day": {
    "operator_key": "op_is_equal",
    "values": [15]
  }
}

@apiSuccess   {String}  merchant_id   Định danh của tenant.
@apiSuccess   {String}  merchant_name   Tên của tenant.
@apiSuccess   {Object}  category  Thông tin danh mục
@apiSuccess   {String}  category..id    Định danh danh mục
@apiSuccess   {String}  category..name    Tên danh mục
@apiSuccess   {Object}  [contact_info]  Thông tin liên hệ
@apiSuccess   {String}  contact_info..email   Thư điện tử liên hệ
@apiSuccess   {String}  contact_info..phone_number   Số điện thoại liên hệ
@apiSuccess   {String}  package_code  Mã gói tenant đang sử dụng.
@apiSuccess   {String}  expire_time   Thời điểm hết hạn sử dụng của tenant.
@apiSuccess   {Number}  expired_day   Số ngày còn lại của license.
@apiSuccess   {Number=-1: STOPPED; 0: LOCKED; 1: PENDING; 2: AVAILABLE}  status  Trạng thái sử dụng của tenant<br />
<li><code>-1: STOPPED</code>: ngưng hoạt động</li>
<li><code>0: LOCKED</code>: đã bị khoá</li>
<li><code>1: PENDING</code>: chờ duyệt</li>
<li><code>2: AVAILABLE</code>: đang hoạt động</li>
@apiSuccess   {Number}  [current_profile_number]  Số lượng profile hiện tại của tenant.
@apiSuccess   {Number}  [max_profile_number]  Số lượng profile tối đa của tenant.
@apiSuccess   {Number}  [current_email_sent_number]  Số lượng email đã gửi của tenant.
@apiSuccess   {Number}  [max_email_sent_number]   Số lượng email tối đa được gửi trong tháng.
@apiSuccess   {Number}  [current_user_number]   Số lượng user hiện tại.
@apiSuccess   {Number}  [max_user_number]   Số lượng user tối đa.
@apiSuccess   {Number}  [current_page_social_number]   Số lượng trang mạng xã hội đã được tích hợp.
@apiSuccess   {Number}  [max_page_social_number]   Số lượng trang mạng xã hội tối đa được tích hợp.
@apiSuccess   {Array}  [partner_info]  Thông tin partner quản lý tenant
@apiSuccess   {Object}  partner_info..id  Định danh partner
@apiSuccess   {Object}  partner_info..name  Tên của partner
@apiSuccess   {Array}   [extend_features]   Danh sách các tính năng bổ sung tenant đăng ký.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "merchant_id": "76121c48-10b1-48fc-9a18-4a4eebe6a291",
      "merchant_name": "Mobio",
      "category": {
        "id": "608aba2f-eba5-4f7e-b4d3-bf7e3747bedf",
        "name": "i18n_travel_hotel"
      },
      "contact_info": {
        "email": "",
        "phone_number": ""
      },
      "package_code": "sme_enterprise_package",
      "expire_time": "2019-12-31T12:00:00Z",
      "status": 1,
      "current_profile_number": 525000,
      "max_profile_number": 1050000,
      "current_email_sent_number": 2100000,
      "max_email_sent_number": 3150000,
      "current_user_number": 5,
      "max_user_number": 10,
      "current_page_social_number": 5,
      "max_page_social_number": 10,
      "partner_info": [
        {
          "name": "Mobio",
          "id": "be243a97-2cd9-4260-b432-476883911cb3",
          "user_id": "06634328-57e0-4ee4-a453-7df61695b726"
        }
      ],
      "extend_features": [
        {
          "key" : "extend_feature_loyalty_management"
          "value": 1
        },
        {
          "key" : "extend_feature_mobile_app"
          "value": 1
        }
      ]
    }
  ]
}
"""
****************
"""
@api {post} /tenants/actions/get-list Lấy danh sách tenant
@apiDescription Dịch vụ lấy danh sách các tenant
@apiGroup Tenant
@apiVersion 1.0.3
@apiName ListTenant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse json_header

@apiParam   (Query:)   {String}  [search]   Chuỗi tìm kiếm

@apiParam   (Body:)  {Array}  packages  List package cần lọc.
<li><code>trial_package</code>: gói Trial</li>
<li><code>pay_as_you_go_package</code>: gói Pay As You Go</li>
<li><code>sme_enterprise_package</code>: gói SME / enterprise</li>
<li><code>corporate_package</code>: gói Corporate</li>
@apiParam   (Body:)  {Array}  status  Danh sách status cần lọc
@apiParam   (Body:)   {Object}  expired_day   Thông tin bộ lọc số ngày còn lại
@apiParam   (Body:)   {String}  expired_day..operator_key   Mã toán tử<br />
<li><code>op_is_equal</code>: So sánh bằng</li>
<li><code>op_is_greater_equal</code>: Lớn hơn hoặc bằng</li>
<li><code>op_is_greater</code>: Lớn hơn</li>
<li><code>op_is_between</code>: Giữa</li>
<li><code>op_is_less_equal</code>: Nhỏ hơn hoặc bằng</li>
<li><code>op_is_less</code>: Nhỏ hơn</li>
@apiParam   (Body:)   {String}  expired_day..values   Danh sách giá trị
@apiParamExample  {json}  Body example
{
  "packages": [
    "trial_package",
    "pay_as_you_go_package",
    "sme_enterprise_package"
  ],
  "status": [
    -1, 0
  ],
  "expired_day": {
    "operator_key": "op_is_equal",
    "values": [15]
  }
}

@apiSuccess   {String}  merchant_id   Định danh của tenant.
@apiSuccess   {String}  merchant_name   Tên của tenant.
@apiSuccess   {Object}  category  Thông tin danh mục
@apiSuccess   {String}  category..id    Định danh danh mục
@apiSuccess   {String}  category..name    Tên danh mục
@apiSuccess   {Object}  [contact_info]  Thông tin liên hệ
@apiSuccess   {String}  contact_info..email   Thư điện tử liên hệ
@apiSuccess   {String}  contact_info..phone_number   Số điện thoại liên hệ
@apiSuccess   {String}  package_code  Mã gói tenant đang sử dụng.
@apiSuccess   {String}  expire_time   Thời điểm hết hạn sử dụng của tenant.
@apiSuccess   {Number}  expired_day   Số ngày còn lại của license.
@apiSuccess   {Number=-1: STOPPED; 0: LOCKED; 1: PENDING; 2: AVAILABLE}  status  Trạng thái sử dụng của tenant<br />
<li><code>-1: STOPPED</code>: ngưng hoạt động</li>
<li><code>0: LOCKED</code>: đã bị khoá</li>
<li><code>1: PENDING</code>: chờ duyệt</li>
<li><code>2: AVAILABLE</code>: đang hoạt động</li>
@apiSuccess   {Number}  [current_profile_number]  Số lượng profile hiện tại của tenant.
@apiSuccess   {Number}  [max_profile_number]  Số lượng profile tối đa của tenant.
@apiSuccess   {Number}  [current_email_sent_number]  Số lượng email đã gửi của tenant.
@apiSuccess   {Number}  [max_email_sent_number]   Số lượng email tối đa được gửi trong tháng.
@apiSuccess   {Number}  [current_user_number]   Số lượng user hiện tại.
@apiSuccess   {Number}  [max_user_number]   Số lượng user tối đa.
@apiSuccess   {Number}  [current_page_social_number]   Số lượng trang mạng xã hội đã được tích hợp.
@apiSuccess   {Number}  [max_page_social_number]   Số lượng trang mạng xã hội tối đa được tích hợp.
@apiSuccess   {Object}  [partner_info]  Thông tin partner quản lý tenant
@apiSuccess   {Object}  partner_info..id  Định danh partner
@apiSuccess   {Object}  partner_info..name  Tên của partner
@apiSuccess   {Array}   [extend_features]   Danh sách các tính năng bổ sung tenant đăng ký.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "merchant_id": "76121c48-10b1-48fc-9a18-4a4eebe6a291",
      "merchant_name": "Mobio",
      "category": {
        "id": "608aba2f-eba5-4f7e-b4d3-bf7e3747bedf",
        "name": "i18n_travel_hotel"
      },
      "contact_info": {
        "email": "",
        "phone_number": ""
      },
      "package_code": "sme_enterprise_package",
      "expire_time": "2019-12-31T12:00:00Z",
      "status": 1,
      "current_profile_number": 525000,
      "max_profile_number": 1050000,
      "current_email_sent_number": 2100000,
      "max_email_sent_number": 3150000,
      "current_user_number": 5,
      "max_user_number": 10,
      "current_page_social_number": 5,
      "max_page_social_number": 10,
      "partner_info": {
        "name": "Mobio",
        "id": "be243a97-2cd9-4260-b432-476883911cb3",
        "user_id": "06634328-57e0-4ee4-a453-7df61695b726"
      },
      "extend_features": [
        {
          "key" : "extend_feature_loyalty_management"
          "value": 1
        },
        {
          "key" : "extend_feature_mobile_app"
          "value": 1
        }
      ]
    }
  ]
}
"""
*****************
"""
@api {post} /tenants/actions/get-list Lấy danh sách tenant
@apiDescription Dịch vụ lấy danh sách các tenant
@apiGroup Tenant
@apiVersion 1.0.2
@apiName ListTenant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse json_header

@apiParam   (Body:)  {Array}  packages  List package cần lọc.
<li><code>trial_package</code>: gói Trial</li>
<li><code>pay_as_you_go_package</code>: gói Pay As You Go</li>
<li><code>sme_enterprise_package</code>: gói SME / enterprise</li>
<li><code>corporate_package</code>: gói Corporate</li>
@apiParam   (Body:)  {Array}  status  Danh sách status cần lọc
@apiParam   (Body:)   {Object}  expired_day   Thông tin bộ lọc số ngày còn lại
@apiParam   (Body:)   {String}  expired_day..operator_key   Mã toán tử<br />
<li><code>op_is_equal</code>: So sánh bằng</li>
<li><code>op_is_greater_equal</code>: Lớn hơn hoặc bằng</li>
<li><code>op_is_greater</code>: Lớn hơn</li>
<li><code>op_is_between</code>: Giữa</li>
<li><code>op_is_less_equal</code>: Nhỏ hơn hoặc bằng</li>
<li><code>op_is_less</code>: Nhỏ hơn</li>

@apiParam   (Body:)   {String}  expired_day..values   Danh sách giá trị
@apiParamExample  {json}  Body example
{
  "packages": [
    "trial_package",
    "pay_as_you_go_package",
    "sme_enterprise_package"
  ],
  "status": [
    -1, 0
  ],
  "expired_day": {
    "operator_key": "op_is_equal",
    "values": [15]
  }
}

@apiSuccess   {String}  merchant_id   Định danh của tenant.
@apiSuccess   {String}  merchant_name   Tên của tenant.
@apiSuccess   {Object}  category  Thông tin danh mục
@apiSuccess   {String}  category..id    Định danh danh mục
@apiSuccess   {String}  category..name    Tên danh mục
@apiSuccess   {Object}  [contact_info]  Thông tin liên hệ
@apiSuccess   {String}  contact_info..email   Thư điện tử liên hệ
@apiSuccess   {String}  contact_info..phone_number   Số điện thoại liên hệ
@apiSuccess   {String}  package_code  Mã gói tenant đang sử dụng.
@apiSuccess   {String}  expire_time   Thời điểm hết hạn sử dụng của tenant.
@apiSuccess   {Number}  expired_day   Số ngày còn lại của license.
@apiSuccess   {Number=-1: STOPPED; 0: LOCKED; 1: PENDING; 2: AVAILABLE}  status  Trạng thái sử dụng của tenant<br />
<li><code>-1: STOPPED</code>: ngưng hoạt động</li>
<li><code>0: LOCKED</code>: đã bị khoá</li>
<li><code>1: PENDING</code>: chờ duyệt</li>
<li><code>2: AVAILABLE</code>: đang hoạt động</li>
@apiSuccess   {Number}  [current_profile_number]  Số lượng profile hiện tại của tenant.
@apiSuccess   {Number}  [max_profile_number]  Số lượng profile tối đa của tenant.
@apiSuccess   {Number}  [current_email_sent_number]  Số lượng email đã gửi của tenant.
@apiSuccess   {Number}  [max_email_sent_number]   Số lượng email tối đa được gửi trong tháng.
@apiSuccess   {Number}  [current_user_number]   Số lượng user hiện tại.
@apiSuccess   {Number}  [max_user_number]   Số lượng user tối đa.
@apiSuccess   {Number}  [current_page_social_number]   Số lượng trang mạng xã hội đã được tích hợp.
@apiSuccess   {Number}  [max_page_social_number]   Số lượng trang mạng xã hội tối đa được tích hợp.
@apiSuccess   {Object}  [partner_info]  Thông tin partner quản lý tenant
@apiSuccess   {Object}  partner_info..id  Định danh partner
@apiSuccess   {Object}  partner_info..name  Tên của partner
@apiSuccess   {Array}   [extend_features]   Danh sách các tính năng bổ sung tenant đăng ký.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "merchant_id": "76121c48-10b1-48fc-9a18-4a4eebe6a291",
      "merchant_name": "Mobio",
      "category": {
        "id": "608aba2f-eba5-4f7e-b4d3-bf7e3747bedf",
        "name": "i18n_travel_hotel"
      },
      "contact_info": {
        "email": "",
        "phone_number": ""
      },
      "package_code": "sme_enterprise_package",
      "expire_time": "2019-12-31T12:00:00Z",
      "status": 1,
      "current_profile_number": 525000,
      "max_profile_number": 1050000,
      "current_email_sent_number": 2100000,
      "max_email_sent_number": 3150000,
      "current_user_number": 5,
      "max_user_number": 10,
      "current_page_social_number": 5,
      "max_page_social_number": 10,
      "partner_info": {
        "name": "Mobio",
        "id": "be243a97-2cd9-4260-b432-476883911cb3",
        "user_id": "06634328-57e0-4ee4-a453-7df61695b726"
      },
      "extend_features": [
        {
          "key" : "extend_feature_loyalty_management"
          "value": 1
        },
        {
          "key" : "extend_feature_mobile_app"
          "value": 1
        }
      ]
    }
  ]
}
"""
****************
"""
@api {post} /tenants/actions/get-list Lấy danh sách tenant
@apiDescription Dịch vụ lấy danh sách các tenant
@apiGroup Tenant
@apiVersion 1.0.1
@apiName ListTenant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse json_header

@apiParam   (Body:)  {Array}  packages  List package cần lọc phân cách bởi dấu phẩy(,). Ex: <code>&packages=trial,sme...</code><br />
<li><code>trial_package</code>: gói Trial</li>
<li><code>pay_as_you_go_package</code>: gói Pay As You Go</li>
<li><code>sme_enterprise_package</code>: gói SME / enterprise</li>
<li><code>corp_package</code>: gói Corporate</li>
@apiParam   (Body:)  {Array}  status  Danh sách status cần lọc
@apiParam   (Body:)   {Object}  expired_day   Thông tin bộ lọc số ngày còn lại
@apiParam   (Body:)   {String}  expired_day..operator_key   Mã toán tử<br />
<li><code>op_is_equal</code>: So sánh bằng</li>
<li><code>op_is_greater_equal</code>: Lớn hơn hoặc bằng</li>
<li><code>op_is_greater</code>: Lớn hơn</li>
<li><code>op_is_between</code>: Giữa</li>
<li><code>op_is_less_equal</code>: Nhỏ hơn hoặc bằng</li>
<li><code>op_is_less</code>: Nhỏ hơn</li>

@apiParam   (Body:)   {String}  expired_day..values   Danh sách giá trị
@apiParamExample  {json}  Body example
{
  "packages": [
    "trial_package",
    "pay_as_you_go_package",
    "sme_enterprise_package"
  ],
  "status": [
    -1, 0
  ],
  "expired_day": {
    "operator_key": "op_is_equal",
    "values": [15]
  }
}

@apiSuccess   {String}  merchant_id   Định danh của tenant.
@apiSuccess   {String}  merchant_name   Tên của tenant.
@apiSuccess   {Object}  category  Thông tin danh mục
@apiSuccess   {String}  category..id    Định danh danh mục
@apiSuccess   {String}  category..name    Tên danh mục
@apiSuccess   {Object}  contact_info  Thông tin liên hệ
@apiSuccess   {String}  contact_info..email   Thư điện tử liên hệ
@apiSuccess   {String}  contact_info..phone_number   Số điện thoại liên hệ
@apiSuccess   {String}  package_code  Mã gói tenant đang sử dụng.
@apiSuccess   {String}  expire_time   Thời điểm hết hạn sử dụng của tenant.
@apiSuccess   {Number}  expired_day   Số ngày còn lại của license.
@apiSuccess   {Number=-1: STOPPED; 0: LOCKED; 1: PENDING; 2: AVAILABLE}  status  Trạng thái sử dụng của tenant<br />
<li><code>-1: STOPPED</code>: ngưng hoạt động</li>
<li><code>0: LOCKED</code>: đã bị khoá</li>
<li><code>1: PENDING</code>: chờ duyệt</li>
<li><code>2: AVAILABLE</code>: đang hoạt động</li>
@apiSuccess   {Number}  current_profile_number  Số lượng profile hiện tại của tenant.
@apiSuccess   {Number}  max_profile_number  Số lượng profile tối đa của tenant.
@apiSuccess   {Number}  current_email_sent_number  Số lượng email đã gửi của tenant.
@apiSuccess   {Number}  max_email_sent_number   Số lượng email tối đa được gửi trong tháng.
@apiSuccess   {Number}  [current_user_number]   Số lượng user hiện tại.
@apiSuccess   {Number}  [max_user_number]   Số lượng user tối đa.
@apiSuccess   {Number}  [current_page_social_number]   Số lượng trang mạng xã hội đã được tích hợp.
@apiSuccess   {Number}  [max_page_social_number]   Số lượng trang mạng xã hội tối đa được tích hợp.
@apiSuccess   {Object}  partner_info  Thông tin partner quản lý tenant
@apiSuccess   {Object}  partner_info..id  Định danh partner
@apiSuccess   {Object}  partner_info..name  Tên của partner
@apiSuccess   {Array}   extend_features   Danh sách các tính năng bổ sung tenant đăng ký.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "merchant_id": "76121c48-10b1-48fc-9a18-4a4eebe6a291",
      "merchant_name": "Mobio",
      "category": {
        "id": "608aba2f-eba5-4f7e-b4d3-bf7e3747bedf",
        "name": "i18n_travel_hotel"
      },
      "contact_info": {
        "email": "",
        "phone_number": ""
      },
      "referer": "88d4e8be-715b-405a-b446-dd2c6ca925ea",
      "package_code": "sme_enterprise_package",
      "expire_time": "2019-12-31T12:00:00Z",
      "status": 1,
      "current_profile_number": 525000,
      "max_profile_number": 1050000,
      "current_email_sent_number": 2100000,
      "max_email_sent_number": 3150000,
      "current_user_number": 5,
      "max_user_number": 10,
      "current_page_social_number": 5,
      "max_page_social_number": 10,
      "partner_info": {
        "name": "Mobio",
        "id": "be243a97-2cd9-4260-b432-476883911cb3",
        "user_id": "06634328-57e0-4ee4-a453-7df61695b726"
      },
      "extend_features": [
        {
          "key" : "extend_feature_loyalty_management"
          "value": 1
        },
        {
          "key" : "extend_feature_mobile_app"
          "value": 1
        }
      ]
    }
  ]
}
"""
********************
"""
@api {get} /tenants Get tenant package
@apiDescription Lấy thông tin license của các tenant
@apiGroup Tenant
@apiVersion 1.0.0
@apiName ListTenant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)  {String}  merchant_id   Định danh của tenant cần lọc.

@apiSuccess   {String}  merchant_id   Định danh của tenant.
@apiSuccess   {String}  merchant_name Tên của tenant.
@apiSuccess   {String}  package_code  Mã gói tenant đang sử dụng.
@apiSuccess   {String}  expire_time   Thời điểm hết hạn sử dụng của tenant.
@apiSuccess   {Number=-1-STOPPED; 0-LOCKED; 1-AVAILABLE; 2-WARNING_EXPIRED}  status  Trạng thái sử dụng của tenant
@apiSuccess   {Number}  current_profile_number  Số lượng profile hiện tại của tenant.
@apiSuccess   {Number}  max_profile_number  Số lượng profile tối đa của tenant.
@apiSuccess   {Number}  current_email_sent_number  Số lượng email đã gửi của tenant.
@apiSuccess   {Number}  max_email_sent_number   Số lượng email tối đa được gửi trong tháng.
@apiSuccess   {Number}  [current_user_number]   Số lượng user hiện tại.
@apiSuccess   {Number}  [max_user_number]   Số lượng user tối đa.
@apiSuccess   {Number}  [current_page_social_number]   Số lượng trang mạng xã hội đã được tích hợp.
@apiSuccess   {Number}  [max_page_social_number]   Số lượng trang mạng xã hội tối đa được tích hợp.
@apiSuccess   {Object}  partner_info  Thông tin partner quản lý tenant
@apiSuccess   {Object}  partner_info..id  Định danh partner
@apiSuccess   {Object}  partner_info..name  Tên của partner
@apiSuccess   {Array}   extend_features   Danh sách các tính năng bổ sung tenant đăng ký.

@apiSuccessExample 	{json}	Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "merchant_id": "76121c48-10b1-48fc-9a18-4a4eebe6a291",
      "merchant_name": "Mobio",
      "package_code": "sme_enterprise_package",
      "expire_time": "2019-12-31T12:00:00Z",
      "status": 1,
      "current_profile_number": 525000,
      "max_profile_number": 1050000,
      "current_email_sent_number": 2100000,
      "max_email_sent_number": 3150000,
      "current_user_number": 5,
      "max_user_number": 10,
      "current_page_social_number": 5,
      "max_page_social_number": 10,
      "partner_info": {
        "name": "Mobio",
        "id": "be243a97-2cd9-4260-b432-476883911cb3"
      },
      "extend_features": [
        {
          "key" : "extend_feature_loyalty_management"
          "value": 1
        },
        {
          "key" : "extend_feature_mobile_app"
          "value": 1
        }
      ]
    }
  ]
}
"""
#!/usr/bin/python
# -*- coding: utf8 -*-

******************************** Logout ********************************
* version: 1.0.0                                                      *
***********************************************************************
"""
@api {post} /logout Đăng xuất
@apiDescription Dịch vụ logout hệ thống
@apiGroup User
@apiVersion 1.0.0
@apiName Logout

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "login successful!!!"
}
"""

******************************** Login ********************************
* version: 1.0.0                                                      *
***********************************************************************
"""
@api {post} /login Đăng nhập
@apiDescription Dịch vụ login hệ thống
@apiGroup User
@apiVersion 1.0.0
@apiName Login

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header

@apiParam   (Body:)   {String}  username   Tài khoản login
@apiParam   (Body:)   {String}  password  Mật khẩu

@apiParamExample  {json}  Example:
{
  "username": "<EMAIL>",
  "password": "asdlakjsd"
  
}

@apiSuccess   {String}  jwt   Token sử dụng.
@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "login successful!!!",
  "jwt": ""
}
"""

******************************** List User **********************************
* version: 1.0.1                                                            *
* version: 1.0.0                                                            *
*****************************************************************************
"""
@api {get} /users List user
@apiDescription Dịch vụ lấy danh sách user
@apiGroup User
@apiVersion 1.0.1
@apiName ListUser

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header

@apiParam   (Query:)   {String}  [partner_ids]   Danh sách định danh partner. Ex: <code>partner_ids=c86b1e94-a6b3-45b5-aeac-994315619202,617ddffb...</code>

@apiSuccess   {String}  id  Định danh của user
@apiSuccess   {String}  role  Quyền của user
@apiSuccess   {String}  name  Tên của user
@apiSuccess   {String}  username  Username đăng nhập
@apiSuccess   {String}  email  Thư điện tử
@apiSuccess   {String}  code  Mã cá nhân
@apiSuccess   {String}  partner_id  Định danh của partner
@apiSuccessExample  {json}  Example:
{
  "data": [
    {
      "id": "e1a9f74e-24fb-4354-b280-bdfbc368222b",
      "role": "admin",
      "name": "Nguyễn Văn An",
      "username": "<EMAIL>",
      "email": "",
      "code": "HFDADP"
      "partner_id": "8ba315a5-b244-4e9b-91e3-ae08582e3e13"
    },
    {
      "id": "e1a9f74e-24fb-4354-b280-bdfbc368222b",
      "role": "staff",
      "name": "Nguyễn Văn B",
      "username": "<EMAIL>",
      "email": "",
      "code": "PKDWVR"
      "partner_id": "3cad833f-9902-427d-a8ce-ca469d0e8c1a"
    }
  ]
}
"""
****************
"""
@api {get} /users List user
@apiDescription Dịch vụ lấy danh sách user
@apiGroup User
@apiVersion 1.0.0
@apiName ListUser

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header

@apiParam   (Query:)   {String}  [partner_id]   Định danh partner

@apiSuccessExample  {json}  Example:
{
  "data": [
    {
      "name": "Nguyễn Văn An",
      "username": "<EMAIL>",
      "email": "",
      "code": "HFDADP"
      "partner_id": "8ba315a5-b244-4e9b-91e3-ae08582e3e13"
    },
    {
      "name": "Nguyễn Văn B",
      "username": "<EMAIL>",
      "email": "",
      "code": "PKDWVR"
      "partner_id": "3cad833f-9902-427d-a8ce-ca469d0e8c1a"
    }
  ]
}
"""

******************************** Create User ********************************
* version: 1.0.0                                                            *
*****************************************************************************
"""
@api {post} /users Create user
@apiDescription Dịch vụ tạo tài khoản trang quản trị
@apiGroup User
@apiVersion 1.0.0
@apiName CreateUser

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header

@apiParam   (Body:)   {String}  username   Tài khoản login
@apiParam   (Body:)   {String}  name  Tên tài khoản
@apiParam   (Body:)   {String}  email   Thư điện tử
@apiParam   (Body:)   {String}  partner_id  Định danh partner

@apiParamExample  {json}  Example:
{
  "name": "Nguyễn Văn An",
  "username": "<EMAIL>",
  "email": "",
  "partner_id": ""
  
}

@apiSuccessExample 	{json}	Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "register successful!!!"
}
"""
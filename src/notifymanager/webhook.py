
#
# ==================================================================================================================================
# ************************************************** Webhook icheck  **************************************************
# * v2.0.0                                                                                                                         *
# **********************************************************************************************************************************
"""
@api {POST} /webhook/api/v2.0/webhook/channel/<channel> Webhook gửi tin
@apiVersion v2.0.0
@apiName Webhook sent message
@apiGroup Webhook

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Body:)   {string}       id                      id của message
@apiParam (Body:)   {string}       type                    Loại trạng thái message
<ul>
<li><b><code>sent</code></b> sent </li>
<li><b><code>open</code></b> open </li>
</ul>
@apiParam (Body:)   {int}       status                    Loại trạng thái message
<ul>
<li><b><code>0</code></b> Không thành công </li>
<li><b><code>1</code></b> Thành công </li>
</ul>
@apiParam (Body:)   {string}       reason                  Lý do không gửi được
@apiParam (Body:)   {string}       channel                 Kênh gửi
<ul>
<li><b><code>app</code></b> Notify </li>
<li><b><code>sms</code></b> Sms </li>
</ul>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message":"request successful."
}
"""

#
# ==================================================================================================================================
# ************************************************** Webhook MyVIB **************************************************
# * v2.0.0                                                                                                                         *
# **********************************************************************************************************************************
"""
@api {POST} /webhook/api/v2.0/webhook/myvib     Webhook gửi tin MyVIB
@apiVersion v2.0.0
@apiName Webhook myvib
@apiGroup Webhook 

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Body:)   {string}       id                      id của message
@apiParam (Body:)   {string}       type                    Loại trạng thái message
<ul>
<li><b><code>sent</code></b> sent </li>
<li><b><code>open</code></b> open </li>
</ul>
@apiParam (Body:)   {int}       status                    Loại trạng thái message
<ul>
<li><b><code>0</code></b> Không thành công </li>
<li><b><code>1</code></b> Thành công </li>
</ul>
@apiParam (Body:)   {string}       [reason]                  Lý do không gửi được tương ứng status = 0

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message":"request successful."
}
"""


#
# ==================================================================================================================================
# ************************************************** Webhook MyVIB **************************************************
# * v2.0.0                                                                                                                         *
# **********************************************************************************************************************************
"""
@api {POST} /webhook/api/v2.0/webhook/chanel/sms     Webhook gửi tin nhắn
@apiVersion v2.0.0
@apiName Webhook sms
@apiGroup Webhook 

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:)   {string}          id                     id của message

@apiParam (Body:)   {int}           status                    Loại trạng thái message
<ul>
<li><b><code>0</code></b> Không thành công </li>
<li><b><code>1</code></b> Thành công </li>
</ul>
@apiParam (Body:)   {string}       [reason]                  Lý do không gửi được tương ứng status = 0

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message":"request successful."
}
"""



#
# ==================================================================================================================================
# ************************************************** Webhook batch  **************************************************
# * v2.0.0                                                                                                                         *
# **********************************************************************************************************************************
"""
@api {POST} /webhook/api/v2.0/webhook/channel/<channel>/batch Webhook gửi tin batch
@apiVersion v2.0.0
@apiName Webhook sent message batch
@apiGroup Webhook

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Body:)   {list}       data     
                
@apiParam (data:)   {string}       id                      id của message
@apiParam (data:)   {string}       type                    Loại trạng thái message
<ul>
<li><b><code>sent</code></b> sent </li>
<li><b><code>open</code></b> open </li>
</ul>
@apiParam (data:)   {int}       status                    Loại trạng thái message
<ul>
<li><b><code>0</code></b> Không thành công </li>
<li><b><code>1</code></b> Thành công </li>
</ul>
@apiParam (data:)   {string}       reason                  Lý do không gửi được
@apiParam (data:)   {string}       channel                 Kênh gửi
<ul>
<li><b><code>app</code></b> Notify </li>
<li><b><code>sms</code></b> Sms </li>
</ul>

@apiParamExample    {json}      Body example:
{
    "data": [
        {
            "id": "1bf177d8-f938-4adb-b62d-743db142bd9f",
            "reason": "success",
            "status": 1,
            "type": "sent"
        }
    ]
}


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message":"request successful."
}
"""
# ==================================================================================================================================
# *********************************************** YÊU CẦU MESSAGE THEO BLOCK  ******************************************************
# * v2.0.0                                                                                                                         *
# **********************************************************************************************************************************
"""
@api {POST} /api/v2.0/message/block/sms Yêu cầu gửi nhiều thông điệp theo block quảng cáo
@apiDescription API chỉ cho phép IP đã được đăng ký gửi request.
@apiVersion v2.0.0
@apiName SendBlockMsg
@apiGroup Message

@apiUse 401
@apiUse 500

@apiParam (Body:) {Object} from Thông tin nguồn gửi
@apiParam (Body:) {Array} to Danh sách các địa chỉ gửi message

@apiParam (from) {string=call-center retail social-crm marketing email-marketing loyalty profiling admin} source Nguồn yêu cầu gửi tin nhắn.
@apiParam (from) {string} sender_id UUID người gửi
@apiParam (from) {string} merchant_id UUID merchant yêu cầu gửi tin nhắn
@apiParam (from) {string} business_id UUID master campaign
@apiParam (from) {string} campaign_id UUID campaign chạy chiến dịch
@apiParam (from) {string} brandname Brand name gửi sms
@apiParam (from) {Boolean} anti_spam Trạng thái chống spam
@apiParam (from) {string} root_process_id ID root process

@apiParam (to) {string} phone_number Số điện thoại người nhận
@apiParam (to) {string} profile_id Profile id người nhận
@apiParam (to) {string} sent_webhook Link webhook for sent msg
@apiParam (to) {string} open_webhook Link webhook for open msg
@apiParam (to) {Object} body Object nội dung message

@apiParam (to-body) {string} content Nội dung message



@apiParamExample {json} SMS
{
    "from": {
        "source": "marketing",
        "sender_id": "cb6fd302-bffe-48e4-8390-3fd1ecec9808",
        "merchant_id": "b06234d0-209b-4070-88f5-2a8b76707119", /* uuid merchant yêu cầu gửi tin nhắn */
        "business_id": "590dd961-b514-4a65-ae6c-e68c12677370", /* UUID master campaign */
        "anti_spam": false, /* Trạng thái chống spam */
        "campaign_id": "c1c10139-e7a3-4169-ad7f-43ade8bb57bd", /* UUID campaign chạy chiến dịch */
        "brandname": "TALKING",
        "root_process_id": "10759310-269c-42c4-b6ae-b52aa6f7a119"
    },
    "to": [
        {
            "phone_number": "0363335020",
            "body": {
                "content": "CHAOBAN h48 *|VC_VALID_TIME|*" /* nội dung tin SMS */
            },
            "profile_id": "8a375eeb-7a61-4175-a9d1-15334bcd2e49",
            "sent_webhook": "https://...",  /* link webhook nhận thông báo trạng thái gửi SMS */
            "open_webhook": "http://...",  /* link webhook nhận thông báo khi SMS được open */
        }
    ]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message":"Request successful."
}
"""
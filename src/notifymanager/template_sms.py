# Get List Template Sms
# version: 2.0
############################################################################################
"""
@api {GET} /api/v2.0/template_sms
Get List Template Sms
@apiDescription API Lấy danh sách các template
@apiVersion 2.0.0
@apiGroup TemplateSms
@apiName Get Template Sms

@apiHeader (Headers:) {String} Content-Type 
@apiHeader (Headers:) {String} X-Merchant-ID 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse order_sort

@apiParam (Query:)   {string}       [search]                     Search by name or content
@apiParam (Query:)   {int}          page                         Số page tìm kiếm, -1 lấy all phần tử
@apiParam (Query:)   {int}          per_page                     Số item trên một page.
@apiParam (Query:)   {int}          status                       Trạng thái (ẩn / hiện)
<li> Ẩn </li>
<li> Hiện</li>

@apiParam (Query:)   {string}          type                       Loai template
<li> QC </li>
<li> CSKH</li>

@apiParam (Query:)   {string}       brand_name                          brand_name
@apiParam (Query:)   {string}       updated_time                        updated_time: ["2024-02-29T14:24:48Z", "2024-04-29T15:24:48Z"]
@apiParam (Query:)   {string}       created_time                        created_time: ["2024-02-29T14:24:48Z", "2024-04-29T15:24:48Z"]
@apiParam (Query:)   {string}       account_updated                     account_updated: "0f5be325-bd84-420b-9e03-8a7161404233"
@apiParam (Query:)   {string}       account_created                     account_created: "0f5be325-bd84-420b-9e03-8a7161404233"

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK:
{
 "data": [
   {
            "account_created_info": {
                "account_id": "0f5be325-bd84-420b-9e03-8a7161404233",
                "email": "<EMAIL>",
                "username": "admin@msb"
            },
            "account_updated_info": {
                "account_id": "0f5be325-bd84-420b-9e03-8a7161404233",
                "email": "<EMAIL>",
                "username": "admin@msb"
            },
            "brand_name": "MOBIO",
            "content_display": "Nội dung hiển thị trên MOBIO 1",
            "content_register": "Nội dung đăng ký với nhà mạng",
            "created_time": "2024-02-29T14:25:51Z",
            "id": "7bf84c41-3c0d-480d-b68e-f1f9658346e7",
            "name": "Test V4x 2",
            "status": 1,
            "type": "CSKH",
            "updated_time": "2024-02-29T08:03:24Z"
    }
 ],
  "page": 1,
  "per_page":20,  
  "code": 200,
  "message": "request thành công."
}
"""

# Get List Count Template Sms
# version: 2.0
############################################################################################
"""
@api {GET} /api/v2.0/template_sms/count
Get List Count Template Sms
@apiDescription API Lấy danh sách số lượng các Template
@apiVersion 2.0.0
@apiGroup TemplateSms
@apiName Get Template Sms Count

@apiHeader (Headers:) {String} Content-Type 
@apiHeader (Headers:) {String} X-Merchant-ID 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam (Query:)   {string}       [search]                     Search by name or content
@apiParam (Query:)   {int}          page                         Số page tìm kiếm, -1 lấy all phần tử
@apiParam (Query:)   {int}          per_page                     Số item trên một page.
@apiParam (Query:)   {int}          status                       Trạng thái (ẩn / hiện)
<li> Ẩn </li>
<li> Hiện</li>

@apiParam (Query:)   {string}          type                       Loai template
<li> QC </li>
<li> CSKH</li>

@apiParam (Query:)   {string}       brand_name                          brand_name
@apiParam (Query:)   {string}       updated_time                        updated_time: ["2024-02-29T14:24:48Z", "2024-04-29T15:24:48Z"]
@apiParam (Query:)   {string}       created_time                        created_time: ["2024-02-29T14:24:48Z", "2024-04-29T15:24:48Z"]
@apiParam (Query:)   {string}       account_updated                     account_updated: "0f5be325-bd84-420b-9e03-8a7161404233"
@apiParam (Query:)   {string}       account_created                     account_created: "0f5be325-bd84-420b-9e03-8a7161404233"

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK:
{
 "data": [
   {
      "status": 1,
      "count": 1
    }
 ],
  "code": 200,
  "message": "request thành công."
}
"""

# Get List Count Brandname Template Sms
# version: 2.0
############################################################################################
"""
@api {GET} /api/v2.0/template_sms/brand_name/count
Get List Count Template Sms  brand_name
@apiDescription API Lấy danh sách số lượng các Template của brand_name
@apiVersion 2.0.0
@apiGroup TemplateSms
@apiName Get Template Sms Count brand_name

@apiHeader (Headers:) {String} Content-Type 
@apiHeader (Headers:) {String} X-Merchant-ID 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:)   {string}       [search]                     Search by name or content
@apiParam (Query:)   {int}          page                         Số page tìm kiếm, -1 lấy all phần tử
@apiParam (Query:)   {int}          per_page                     Số item trên một page.
@apiParam (Query:)   {int}          status                       Trạng thái (ẩn / hiện)
<li> Ẩn </li>
<li> Hiện</li>

@apiParam (Query:)   {string}          type                       Loai template
<li> QC </li>
<li> CSKH</li>

@apiParam (Query:)   {string}       brand_name                          brand_name
@apiParam (Query:)   {string}       updated_time                        updated_time: ["2024-02-29T14:24:48Z", "2024-04-29T15:24:48Z"]
@apiParam (Query:)   {string}       created_time                        created_time: ["2024-02-29T14:24:48Z", "2024-04-29T15:24:48Z"]
@apiParam (Query:)   {string}       account_updated                     account_updated: "0f5be325-bd84-420b-9e03-8a7161404233"
@apiParam (Query:)   {string}       account_created                     account_created: "0f5be325-bd84-420b-9e03-8a7161404233"

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK:
{
 "data": [
   {
      "brand_name": "Mobio",
      "count": 1
    }
 ],
  "code": 200,
  "message": "request thành công."
}
"""

# Insert Template Sms
# version: 2.0
############################################################################################
"""
@api {POST} /api/v2.0/template_sms
Insert Template Sms
@apiDescription API Tạo Template Sms
@apiVersion 2.0.0
@apiGroup TemplateSms
@apiName Insert TemplateSms

@apiHeader (Headers:) {String} Content-Type 
@apiHeader (Headers:) {String} X-Merchant-ID 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {String}                name                     Template Sms
@apiParam      (Body:)     {int}                   status                    Trạng thái của trường cá nhân
<li> Ẩn </li>
<li> Hiện</li>
@apiParam      (Body:)     {String}                content_display                   Content Template Sms
@apiParam      (Body:)     {String}                content_register          Content đăng ký với nhà mạng
@apiParam      (Body:)     {String}                brand_name                     brand_name
@apiParam (Query:)   {string}          type                       Loai template
<li> QC </li>
<li> CSKH</li>

@apiParamExample    {json}      Body example:
{
      "name": "Mau sms 1",
      "status": 1,
      "type": "CSKH",
      "content_display": "Mau sms",
      "content_register":"Mau sms",
      "brand":"brand"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
      "id": "3b42058a-7352-4c02-9b66-41c5e7f329b5",
      "name": "Mau sms 1",
      "status": 1,
      "type": "CSKH",
      "content": "Mau sms",
      "brand":"brand"
    },
  "lang": "vi",
  "message": "request thành công."
}
"""

# Update Template Sms
# version: 2.0
############################################################################################
"""
@api {PUT} /api/v2.0/template_sms/<template_sms_id> 
Update Template Sms
@apiDescription API Update Template Sms
@apiVersion 2.0.0
@apiGroup TemplateSms
@apiName Update Template Sms

@apiHeader (Headers:) {String} Content-Type 
@apiHeader (Headers:) {String} X-Merchant-ID 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {String}                name                     Template Sms
@apiParam      (Body:)     {int}                   status                    Trạng thái của trường cá nhân
<li> Ẩn </li>
<li> Hiện</li>
@apiParam      (Body:)     {String}                content                   Content Template Sms
@apiParam      (Body:)     {Boolean}                input_content_register   Nhập nội dung khai báo với nhà mạng: True/False
@apiParam      (Body:)     {String}                content_register          Content đăng ký với nhà mạng
@apiParam (Query:)   {string}          type                       Loai template
<li> QC </li>
<li> CSKH</li>

@apiParamExample    {json}      Body example:
{
      "name": "Mau sms 1",
      "status": 1,
      "type": "CSKH",
      "content": "Mau sms",
      "brand_name": "brand",
      "content_register":"Mau sms",
      "content_display":"Mau sms",
      "input_content_register": true
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
      "id": "3b42058a-7352-4c02-9b66-41c5e7f329b5",
      "name": "Mau sms 1",
      "status": 1,
      "type": "CSKH",
      "content": "Mau sms",
      "brand_name": "brand",
      "content_register":"Mau sms",
      "content_display":"Mau sms",
    },
  "lang": "vi",
  "message": "request thành công."
}
"""

# Xóa Template Sms
# version: 2.0
############################################################################################
"""
@api {DELETE} /api/v2.0/template_sms 
Xóa trường Template Sms
@apiDescription Xóa trường Template Sms
@apiVersion 2.0.0
@apiGroup TemplateSms
@apiName Delete TemplateSms

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:)   {string}       ids        Tập hợp các UUID của trường cá nhân hóa cách ','

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK:
{
  "code": 200,
  "message": "request thành công."
}
"""

# Update status Template Sms
# version: 2.0
############################################################################################
"""
@api {PUT} /api/v2.0/template_sms/status
Update trạng thái danh Template Sms
@apiDescription Update trạng thái danh Template Sms
@apiVersion 2.0.0
@apiGroup TemplateSms
@apiName Update status Template Sms

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {Array}    ids      Tập hợp các UUID của trường cá nhân hóa
@apiParam      (Body:)     {Int}    status   Trang thái

@apiParamExample {json} Body example
{
	"ids": [
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703"
	],
	"status": 1
}
"""

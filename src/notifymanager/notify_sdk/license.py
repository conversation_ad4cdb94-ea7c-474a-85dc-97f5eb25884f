"""
@api {GET} /api/v1.0/license/config <PERSON><PERSON><PERSON> c<PERSON><PERSON> h<PERSON>nh license theo code
@apiDescription L<PERSON><PERSON> c<PERSON><PERSON> hình license theo code
@apiVersion v1.0.0
@apiName GetConfigLicenseByCode
@apiGroup NMLicense

@apiUse 401
@apiUse 500

@apiParam  (Params:)    {String}  code                       Mã code cấu hình license cần lấy

@apiSuccess {Array}         data                              Dữ liệu trả về
@apiSuccess {Boolean}       data.is_on_premise                Là máy on premise hay máy Cloud
@apiSuccess {String}        data.is_allowed                   Trạng thái license</br>
                                                             <ul>
                                                                <li><code>true</code>: <PERSON><PERSON><PERSON><PERSON> phép</li>
                                                                <li><code>false</code>: Không được phép</li>
                                                             </ul>

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "is_on_premise": true,
        "is_allowed": true,
        "values": [
            "cdp"
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

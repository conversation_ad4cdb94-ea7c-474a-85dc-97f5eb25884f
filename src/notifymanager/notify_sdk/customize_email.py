"""
@api {GET} /api/v1.0/customize-email/configs-general <PERSON><PERSON><PERSON> thông tin cấu hình chung cài đặt thông báo qua Email
@apiDescription Lấy thông tin cấu hình chung cài đặt thông báo qua Email
@apiVersion v1.0.0
@apiName GetCustomizeEmailConfigGeneral
@apiGroup CustomizeEmailSDK

@apiUse 401
@apiUse 500


@apiSuccess {Array}         data                              Dữ liệu trả về
@apiSuccess {String}        data.id                           ID cấu hình chung
@apiSuccess {String}        data.email_sending_domain         Domain cấu hình gửi
@apiSuccess {Boolean}       data.is_default                   Cấu hình Mặc định hay không
@apiSuccess {String}        data.modules_code                 Danh sách đối tượng (<b>Nếu là mặc định thì áp dụng toàn bộ đối tượng *</b>)
@apiSuccess {String}        data.sender_email                 Email người gửi
@apiSuccess {String}        data.sender_name_mail_alert       Tên người gửi email thông báo
@apiSuccess {String}        data.sender_name_mail_reporter    Tên người gửi email báo cáo

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "_id": "6618e8abb8eccd21a3f40a3a",
            "email_sending_domain": "mobio.vn",
            "is_default": true,
            "modules_code": [
                "*"
            ],
            "sender_email": "<EMAIL>",
            "sender_name_mail_alert": "Mobio Alert",
            "sender_name_mail_reporter": "Email Report",
            "status": 1
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {POST} /api/v1.0/customize-email/config-general Cập nhật cấu hình chung phần cài đặt Email
@apiDescription Cập nhật cấu hình chung phần cài đặt Email
@apiVersion v1.0.0
@apiName AddCustomizeEmailConfigGeneral
@apiGroup CustomizeEmailSDK

@apiUse 401
@apiUse 500


@apiParam  (Body:)     {List}     data                                     Tên miền người gửi
@apiParam  (Body:)     {String}   [data._id]                               ID bản ghi muốn cập nhật (Nếu không gửi lên mặc định là thêm mới)
@apiParam  (Body:)     {String}   data.email_sending_domain                Tên miền người gửi
@apiParam  (Body:)     {Array}    data.modules_code                        Danh sách modules
@apiParam  (Body:)     {String}   data.sender_email                        Email người gửi
@apiParam  (Body:)     {String}   data.sender_name_mail_alert              Tên người gửi email thông báo
@apiParam  (Body:)     {String}   data.sender_name_mail_reporter                Tên người gửi email báo cáo
                                                              
@apiParamExample {json} Body example
{
    "data": [
        {
            "_id": "6625cfbd283b739bce732dea",
            "email_sending_domain": "unsub.mailu.mobio.vn",
            "is_default": false,
            "modules_code": [
                "profile",
                "company",
                "jb",
                "landing_page"
            ],
            "sender_email": "<EMAIL>",
            "sender_name_mail_alert": "111",
            "sender_name_mail_reporter": "111"
        },
        {
            "_id": "6625d15a8f348f2b2a814a76",
            "email_sending_domain": "mailu.mobio.vn",
            "is_default": false,
            "modules_code": ["sale"],
            "sender_email": "<EMAIL>",
            "sender_name_mail_alert": "77",
            "sender_name_mail_reporter": "777"
        }
    ]
}

@apiSuccess {Object}        data                              Dữ liệu trả về
@apiSuccess {String}        data.id                           ID cấu hình chung
@apiSuccess {String}        data.email_sending_domain         Tên miền người gửi
@apiSuccess {Boolean}       data.is_default                   Cấu hình Mặc định hay không
@apiSuccess {String}        data.modules_code                 Danh sách đối tượng (<b>Nếu là mặc định thì áp dụng toàn bộ đối tượng *</b>)
@apiSuccess {String}        data.sender_email                 Email người gửi
@apiSuccess {String}        data.sender_name_mail_alert       Tên người gửi email thông báo
@apiSuccess {String}        data.sender_name_mail_reporter    Tên người gửi email báo cáo

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
[
    {
        "_id": "6625cfbd283b739bce732dea",
        "email_sending_domain": "unsub.mailu.mobio.vn",
        "is_default": false,
        "modules_code": [
            "profile",
            "company",
            "jb",
            "landing_page"
        ],
        "sender_email": "<EMAIL>",
        "sender_name_mail_alert": "111",
        "sender_name_mail_reporter": "111"
    },
    {
        "_id": "6625d15a8f348f2b2a814a76",
        "email_sending_domain": "mailu.mobio.vn",
        "is_default": false,
        "modules_code": ["sale"],
        "sender_email": "<EMAIL>",
        "sender_name_mail_alert": "77",
        "sender_name_mail_reporter": "777"
    }
]
"""

"""
@api {GET} /api/v1.0/customize-email/domain/list Lấy danh sách Module cấu hình tên miền gửi email cài đặt thông báo
@apiDescription Lấy danh sách Module cấu hình tên miền gửi email cài đặt thông báo
@apiVersion v1.0.0
@apiName GetListConfigDomain
@apiGroup CustomizeEmailSDK

@apiUse 401
@apiUse 500


@apiParam  (Params:)        {String}  [sender_domain]            Domain gửi Email

@apiSuccess {Array}         data                             Dữ liệu trả về
@apiSuccess {String}        data.sender_domain               Domain gửi email

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "sender_domain": "mobio.vn"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} /api/v1.0/customize-email/email-sending/list Lấy danh sách cấu hình Email Gửi cấu hình cài đặt thông báo
@apiDescription Lấy danh sách cấu hình Email Gửi cấu hình cài đặt thông báo
@apiVersion v1.0.0
@apiName GetListConfigEmail
@apiGroup CustomizeEmailSDK

@apiUse 401
@apiUse 500


@apiParam  (Params:)        {String}  [sender_email]         Email gửi

@apiSuccess {Array}         data                             Dữ liệu trả về
@apiSuccess {String}        data.sender_email                Email gửi

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "sender_email": "<EMAIL>"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} /api/v1.0/customize-email/email-sender-name/list Lấy danh sách tên người gửi Email cấu hình thông báo
@apiDescription Lấy danh sách tên người gửi Email cấu hình thông báo
@apiVersion v1.0.0
@apiName GetListConfigEmailSenderName
@apiGroup CustomizeEmailSDK

@apiUse 401
@apiUse 500


@apiParam  (Params:)        {String}  [sender_name]          Tên người gửi
@apiParam  (Params:)        {String}  sender_type           Loại người gửi </br>
                                                            <ul>
                                                                <li><code>alert</code>: Thông báo</li>
                                                                <li><code>report</code>: Báo cáo</li>
                                                            </ul>

@apiSuccess {Array}         data                             Dữ liệu trả về
@apiSuccess {String}        data.sender_email                Email gửi

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "sender_name": "Mobio Test",
            "sender_type": "alert"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {PUT} /api/v1.0/customize-email/config-notify Lấy thông tin cấu hình giao diện email
@apiDescription Lấy thông tin cấu hình giao diện email
@apiVersion v1.0.0
@apiName GetCustomizeEmailConfigNotify
@apiGroup CustomizeEmailSDK

@apiUse 401
@apiUse 500


@apiSuccess {Object}         data                              Dữ liệu trả về
@apiSuccess {String}        data.background_color_code        mã màu nền
@apiSuccess {String}        data.contact_email                Thông tin Email liên hệ 
@apiSuccess {String}        data.contact_phone                Thông tin Số điện thoại liên hệ
@apiSuccess {String}        data.website_url                  Thông tin website url
@apiSuccess {String}        data.content_heading_color_code   Màu chữ đầu mục nội dung
@apiSuccess {String}        data.facebook_url                 Thông tin Facebook url
@apiSuccess {String}        data.footer_content               Nội dung footer
@apiSuccess {String}        data.icon_color_code              Mã màu icon
@apiSuccess {String}        data.id                           ID Cấu hình
@apiSuccess {String}        data.logo_navigation_link         Link điều hướng logo
@apiSuccess {String}        data.logo_option                  Kiểu cấu hình logo </br>
                                                              <ul>
                                                                <li><code>library</code>: Thư viện</li>
                                                                <li><code>url</code>: Đường link</li>
                                                              </ul>
@apiSuccess {String}        data.logo_url                     Link logo
@apiSuccess {String}        data.navigation_button_font_color_code   màu nút bấm điều hướng
@apiSuccess {String}        data.navigation_button_text_color_code   màu chữ nút bấm điều hướng
@apiSuccess {String}        data.title_font_color_code   màu chữ tiêu đề

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "background_color_code": "#f6f6f6",
        "contact_email": "<EMAIL>",
        "contact_phone": "************",
        "contact_website_url": "https://mobio.io",
        "content_heading_color_code": "#4caad8",
        "created_time": "2024-04-15T04:05:58.000Z",
        "facebook_url": "",
        "footer_content": "Copyright **YEAR_COPYRIGHT** MOBIO, All rights reserved.",
        "icon_color_code": "#226ff5",
        "id": "661ca7a673a6aa95369d714c",
        "is_default": true,
        "logo_navigation_link": "https://mobio.io",
        "logo_option": "url",
        "logo_url": "https://mobio.io/resources/img/iconmobio_2.png",
        "navigation_button_font_color_code": "#009cdb",
        "navigation_button_text_color_code": "#226ff5",
        "title_font_color_code": "#0a4869",
        "updated_time": "2024-04-15T04:05:58.000Z"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {PUT} /api/v1.0/customize-email/config-notify/<config_id> Cập nhật thông tin cấu hình giao diện email
@apiDescription Cập nhật thông tin cấu hình giao diện email
@apiVersion v1.0.0
@apiName UpdateCustomizeEmailConfigNotify
@apiGroup CustomizeEmailSDK

@apiUse 401
@apiUse 500


@apiParam  (Params:)   {String}   config_id                           ID cấu hình

@apiParam (Body:) {String}        background_color_code        mã màu nền
@apiParam (Body:) {String}        [contact_email]                Thông tin Email liên hệ 
@apiParam (Body:) {String}        [contact_phone]                Thông tin Số điện thoại liên hệ
@apiParam (Body:) {String}        [website_url]                  Thông tin website url
@apiParam (Body:) {String}        content_heading_color_code   Màu chữ đầu mục nội dung
@apiParam (Body:) {String}        [facebook_url]                 Thông tin Facebook url
@apiParam (Body:) {String}        footer_content               Nội dung footer
@apiParam (Body:) {String}        icon_color_code              Mã màu icon
@apiParam (Body:) {String}        logo_navigation_link         Link điều hướng logo
@apiParam (Body:) {String}        logo_option                  Kiểu cấu hình logo </br>
                                                              <ul>
                                                                <li><code>library</code>: Thư viện</li>
                                                                <li><code>url</code>: Đường link</li>
                                                              </ul>
@apiParam (Body:) {String}        logo_url                     Link logo
@apiParam (Body:) {String}        navigation_button_font_color_code   màu nút bấm điều hướng
@apiParam (Body:) {String}        navigation_button_text_color_code   màu chữ nút bấm điều hướng
@apiParam (Body:) {String}        title_font_color_code   màu chữ tiêu đề

@apiParamExample {json} Body example
{
    "background_color_code": "#f6f6f6",
    "contact_email": "<EMAIL>",
    "contact_phone": "0903439982",
    "website_url": "https://mobio.io",
    "content_heading_color_code": "#4caad8",
    "facebook_url": "",
    "footer_content": "Copyright **YEAR_COPYRIGHT** MOBIO, All rights reserved.",
    "icon_color_code": "#226ff5",
    "logo_navigation_link": "https://mobio.io",
    "logo_option": "url",
    "logo_url": "https://mobio.io/resources/img/iconmobio_2.png",
    "navigation_button_font_color_code": "#009cdb",
    "navigation_button_text_color_code": "#226ff5",
    "title_font_color_code": "#0a4869"
}


@apiSuccess {Array}         data                              Dữ liệu trả về
@apiSuccess {String}        data.background_color_code        mã màu nền
@apiSuccess {String}        data.contact_email                Thông tin Email liên hệ 
@apiSuccess {String}        data.contact_phone                Thông tin Số điện thoại liên hệ
@apiSuccess {String}        data.website_url                  Thông tin website url
@apiSuccess {String}        data.content_heading_color_code   Màu chữ đầu mục nội dung
@apiSuccess {String}        data.facebook_url                 Thông tin Facebook url
@apiSuccess {String}        data.footer_content               Nội dung footer
@apiSuccess {String}        data.icon_color_code              Mã màu icon
@apiSuccess {String}        data.id                           ID Cấu hình
@apiSuccess {String}        data.logo_navigation_link         Link điều hướng logo
@apiSuccess {String}        data.logo_option                  Kiểu cấu hình logo </br>
                                                              <ul>
                                                                <li><code>library</code>: Thư viện</li>
                                                                <li><code>url</code>: Đường link</li>
                                                              </ul>
@apiSuccess {String}        data.logo_url                     Link logo
@apiSuccess {String}        data.navigation_button_font_color_code   màu nút bấm điều hướng
@apiSuccess {String}        data.navigation_button_text_color_code   màu chữ nút bấm điều hướng
@apiSuccess {String}        data.title_font_color_code   màu chữ tiêu đề

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "background_color_code": "#f6f6f6",
        "contact_email": "<EMAIL>",
        "contact_phone": "************",
        "contact_website_url": "https://mobio.io",
        "content_heading_color_code": "#4caad8",
        "created_time": "2024-04-15T04:05:58.000Z",
        "facebook_url": "",
        "footer_content": "Copyright **YEAR_COPYRIGHT** MOBIO, All rights reserved.",
        "icon_color_code": "#226ff5",
        "id": "661ca7a673a6aa95369d714c",
        "is_default": true,
        "logo_navigation_link": "https://mobio.io",
        "logo_option": "url",
        "logo_url": "https://mobio.io/resources/img/iconmobio_2.png",
        "navigation_button_font_color_code": "#009cdb",
        "navigation_button_text_color_code": "#226ff5",
        "title_font_color_code": "#0a4869",
        "updated_time": "2024-04-15T04:05:58.000Z"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} /api/v1.0/customize-email/module/module-by-domain Lấy danh sách module theo domain
@apiDescription Lấy danh sách module đang cấu hình theo domain
@apiVersion v1.0.0
@apiName GetListConfigModuleByDomain
@apiGroup CustomizeEmailSDK

@apiUse 401
@apiUse 500


@apiParam  (Params:)        {String}  domain                 

@apiSuccess {Array}         data                             Dữ liệu trả về
@apiSuccess {String}        data.email_sending_domain        Domain
@apiSuccess {Array}        data.modules_code                 Thông tin module
@apiSuccess {Array}        data.sender_email                 Email cấu hình theo domain
@apiSuccess {Array}        data.modules_code.code            Mã module
@apiSuccess {Array}        data.modules_code.name            Tên module

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "_id": "6645cc1de337a065e38e4967",
            "email_sending_domain": "mailu.mobio.vn",
            "modules_code": [
                {
                    "code": "work",
                    "name": "Công việc"
                }
            ],
            "sender_email": "<EMAIL>"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} /api/v1.0/customize-email/template/email-frame-sampling Lấy mẫu khung email theo merchant
@apiDescription Lấy mẫu khung email theo merchant
@apiVersion v1.0.0
@apiName GetEmailFrameSampling
@apiGroup CustomizeEmailSDK

@apiUse 401
@apiUse 500

@apiSuccess {String}         data                             Nội dung khung email

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": "<!doctype html>\n<html xmlns=\"http://www.w3.org/1999/xhtml\" xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\">\n\n<head>\n    <!--[if gte mso 15]><xml><o:OfficeDocumentSettings><o:AllowPNG/>...more"
    "lang": "vi",
    "message": "request thành công."
}
"""

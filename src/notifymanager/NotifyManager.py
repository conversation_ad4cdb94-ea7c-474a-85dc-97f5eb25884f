# ==================================================================================================================================
# ************************************************** YÊU CẦU GỬI NHIỀU THÔNG ĐIỆP **************************************************
# * v2.0.0                                                                                                                         *
# **********************************************************************************************************************************
"""
@api {POST} /api/v2.0/message/multiple/<channel> Yêu cầu gửi nhiều thông điệp theo từng channel
@apiDescription API chỉ cho phép IP đã được đăng ký gửi request.
@apiVersion v2.0.0
@apiName PostMultipleMessage
@apiGroup Message

@apiUse 401
@apiUse 500

@apiParam (Resources) {string=email sms facebook zalo mobile socket} channel Kênh gửi tin

@apiParam (from) {string=call-center retail social-crm marketing email-marketing loyalty profiling admin} source Nguồn yêu cầu gửi tin nhắn.
@apiParam (from) {string} sender_id UUID người gửi

@apiParamExample {json} Web socket
{
    "from": {
        "source": "marketing", /* Nguồn yêu cầu gửi tin */
        "sender_id": "0c214f46-a6d6-47b7-b9ea-282144d0a52c"
    },
    "to": [
    {
        "sent_webhook": "https://...",  /* link webhook nhận thông báo trạng thái gửi mail */
        "receiver_id": "2cf390b5-8953-4c56-b96c-906a1191a9a3",
        "receiver_type": 3,
        "channel": "NOTIFY",
        "notify_type": 1,
        "body": {
            "id": "09f8ee23-713f-424b-ae2e-a839db65c120",
            "page_social_id": "195992607246285",
            "social_type": 1,
            "status": 3,
            "token_name": "Mẹ và bé",
            "title": "Đã ngắt kết nối đến page Facebook: Mẹ và bé",
            "description": "",
            "action": 1000
        }
    }
  ]
}

@apiParam (Email-from) {string} [sender_id] UUID người gửi. Nếu không gửi sẽ lấy default là <code>noreply@domain_đăng_ký</code>
@apiParam (Email-from) {string} merchant_id UUID merchant gửi
@apiParam (Email-from) {string} [sender_name] Tên người gửi
@apiParam (Email-from) {string} sender_domain Domain muốn gửi
@apiParam (Email-from) {string=BROAD_CAST TRANSACTION} sender_type=BROAD_CAST Kiểu gửi email
@apiParam (Email-from) {boolean} [is_fake_response] Fake dữ liệu callback trả về 
@apiParam (Email-from) {array} [cc] cc người gửi
@apiParam (Email-from) {array} [bcc] bcc người gửi
@apiParam (Email-from) {string} [Reply-To] Reply-To người gửi
@apiParamExample {json} Email
{
    "from": {
        "source": "marketing", /* Nguồn yêu cầu gửi tin */
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
        "sender_id": "<EMAIL>", /* email người gửi */
        "sender_name": "Support Mobio", /* tên người gửi */
        "sender_domain": "mobio.vn",    /* domain muốn gửi email */
        "campaign_id": "04354484-71c8-4b93-bb6b-8ad9a0d4cde9",  /* UUID campaign chạy chiến dịch */
        "business_id": "15c7b89a-02ad-41ee-903a-bf664bac4702",  /* UUID master campaign *?
        "sender_type": "BROAD_CAST",
        "anti_spam": true /* Trạng thái chống spam */,
        "bcc": ["<EMAIL>"],
        "cc": ["<EMAIL>"]
        "Reply-To": "<EMAIL>"
        
    },
    "to": [
    {
        "sent_webhook": "https://...",  /* link webhook nhận thông báo trạng thái gửi mail */
        "open_webhook": "http://...",  /* link webhook nhận thông báo khi mail được open */
        "profile_id": "48ebedfb-22af-45b3-b7cc-22ef533b6de5",
        "to": [
            { "email": "<EMAIL>", "name": "Lộc lấp lánh" },
            { "email": "<EMAIL>", "name": "Chung chinh chiến" }
        ]
        "body": {
            "subject": "Day la subject",    /* Subject của email */
            "content": "Day la html thì phải"   /* nội dung email */
        }
    },
    ...
  ]
}

@apiParamExample {json} SMS
{
    "from": {
        "source": "marketing", /* Nguồn yêu cầu gửi tin */
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
        "brandname": "MOBIO",
        "campaign_id": "04354484-71c8-4b93-bb6b-8ad9a0d4cde9",  /* UUID campaign chạy chiến dịch */
        "business_id": "15c7b89a-02ad-41ee-903a-bf664bac4702",  /* UUID master campaign *?
        "anti_spam": true /* Trạng thái chống spam */
    },
    "to": [
    {
        "sent_webhook": "https://...",  /* link webhook nhận thông báo trạng thái gửi SMS */
        "open_webhook": "http://...",  /* link webhook nhận thông báo khi SMS được open */
        "phone_number": "083xxxxxxxx"
        "profile_id": "48ebedfb-22af-45b3-b7cc-22ef533b6de5",
        "body": {
            "content": "Day la message" /* nội dung tin SMS */
        }
    },
    ...
  ]
}

@apiParamExample {json} Social
{
    "from": {
        "source": "marketing", /* Nguồn yêu cầu gửi tin */
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
        "social_type": 1 /* kiểu mạng xã hội: 1: Facebook, 2: Zalo; 3: Instagram; 4: Youtube*/
        "campaign_id": "04354484-71c8-4b93-bb6b-8ad9a0d4cde9",  /* UUID campaign chạy chiến dịch */
        "business_id": "15c7b89a-02ad-41ee-903a-bf664bac4702",  /* UUID master campaign *?
        "anti_spam": true /* Trạng thái chống spam */
    },
    "to":[
    {
        "page_social_id": "", /* ID page mạng xã hội */
        "sent_webhook": "https://...",  /* link webhook nhận thông báo trạng thái gửi */
        "open_webhook": "http://...",  /* link webhook nhận thông báo khi tin được open */
        "profile_id": "48ebedfb-22af-45b3-b7cc-22ef533b6de5",
        "user_social_ids": [
            "2129012930129",
            "2019949502910"
        ], /* danh sách id mạng xã hội của 1 user / 1 mạng xã hội (do FB có nhiều ID / user)*/
        "body": {
            "content": "Day la html thì phải"   /* nội dung tin nhắn */
        }
    },
    ...
  ]
}

@apiParamExample {json} App Mobile
{
    "from": {
        "source": "marketing",  /* Nguồn yêu cầu gửi tin */
        "merchant_id": "659a...1297c",  /* uuid merchant yêu cầu gửi tin nhắn */
        "app_code": "MOBIO",  /* ĐỊnh danh cho app trên mỗi merchant */
        "anti_spam": false, /* Trạng thái chống spam */
        "status": true, /* Lưu thông báo hay không */
    },
    "to": [
    {
        "push_webhook": "https://...",    /* link webhook đã gửi thông báo trạng thái gửi */,
        "sent_webhook": "https://...",    /* link webhook nhận thông báo trạng thái gửi */,
        "open_webhook": "http://...",  /* link webhook nhận thông báo khi mail được open */,
        "profile_id": "48ebedfb-22af-45b3-b7cc-22ef533b6de5",
        "to": [
            {"os_push_id": "ANDROID", "push_id": "ALDKKAWIELASDLJFDF29ADKASL0K32L32KLDSAK210"},
            {"os_push_id": "IOS", "push_id": "ALDKKAWIELASDLJFDF29ADKASL0K32L32KLDSAK210"},
            {
              "app_id": "BVBANK_MOBILE",    /* app_id */
              "os_push_id": "BVBANK_MOBILE",    /* os_push_id: 'ANDROID','IOS','VIB','ICHECK','MYVIB','MSB_PLUS','MSB_MBANK','HDB_MB','VNPAY_OTT','EIB_MOBILE','SHB_MOBILE','SHB_SAHA_MOBILE','SHB_SAHA_15_MOBILE','MSB_DIGI_SALE','VNPT_MOBILE','BVBANK_MOBILE','MOBIO_DEV','DLVN_MOBILE'*/
              "push_id": "********",
              "lang": "VI",
              "device_id": null
            }
        ],
        "body": {
            "alert": {
                "title": "title",     <===> key 'alert'
                "body": "Day la push duoc gui tu Notify Manager abc xyz",      /*Nội dung hiển thị*/
                "content_type": "text",  /*Định dạng text/html/popup*/
            },
            "badge": 10,
            "data": {  /* Thông tin kèm theo */
              "deal_name": "Sale"
            }
        }
    }
  ]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message":"Request successful."
}
"""


# ==================================================================================================================================
# *********************************************** LẤY DANH SÁCH CẤU HÌNH CHANNEL ***************************************************
# * v2.0.0                                                                                                                         *
# **********************************************************************************************************************************
"""
@api {GET} /api/v2.0/merchant/channel/info Lấy danh sách cấu hình các kênh gửi
@apiDescription API trả về danh sách cấu hình của các kênh email, sms, mobile
@apiVersion 2.0.0
@apiName GetMerchantChannel
@apiGroup Config

@apiUse 401
@apiUse 405

@apiParam (Query) {string} merchant_id UUID merchant muốn lấy thông tin channel

@apiSuccess (data) {int=1-SMS 2-Email 3-MobileApp} type Kiểu channel
@apiSuccess (data) {string} name Tên channel
@apiSuccessExample {json} Response 200
{
  "code": "200",
  "message": "Request successful.",
  "data": [
    {
      "type": 1,
      "name": "MOBIO"
    },
    {
      "type": 2,
      "name": "api.mobio.vn"
    },
    {
      "type": 3,
      "name": "SAKUKO"
    }
  ]
}
"""

# ==================================================================================================================================
# ************************************************ YÊU CẦU GỬI EMAIL TỪ ON-PREMISE *************************************************
# * v2.0.0
# **********************************************************************************************************************************
"""
@api {POST} /api/v2.0/message/onpremise/email Yêu cầu gửi email từ on-premise
@apiDescription API này được cài đặt trên hệ thống on cloud của Mobio
@apiVersion 2.0.0
@apiName PostOnPremiseEmail
@apiGroup Message

@apiUse 401
@apiUse 405

@apiHeader {string} X-Merchant-ID UUID merchant request

@apiParam (from) {string} [sender_id] UUID người gửi. Nếu không gửi sẽ lấy default là <code>noreply@domain_đăng_ký</code>
@apiParam (from) {string} merchant_id UUID merchant gửi
@apiParam (from) {string} [sender_name] Tên người gửi
@apiParam (from) {string} sender_domain Domain muốn gửi
@apiParam (from) {string=BROAD_CAST TRANSACTION} sender_type=BROAD_CAST Kiểu gửi email
@apiParamExample {json} Example
{
    "from": {
        "source": "marketing", /* Nguồn yêu cầu gửi tin */
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
        "sender_id": "<EMAIL>", /* email người gửi */
        "sender_name": "Support Mobio", /* tên người gửi */
        "sender_domain": "mobio.vn",    /* domain muốn gửi email */
        "campaign_id": "04354484-71c8-4b93-bb6b-8ad9a0d4cde9",  /* UUID campaign chạy chiến dịch */
        "business_id": "15c7b89a-02ad-41ee-903a-bf664bac4702",  /* UUID master campaign *?
        "sender_type": "BROAD_CAST",
        "anti_spam": true /* Trạng thái chống spam */
    },
    "to": [
    {
        "sent_webhook": "https://...",  /* link webhook nhận thông báo trạng thái gửi mail */
        "open_webhook": "http://...",  /* link webhook nhận thông báo khi mail được open */
        "profile_id": "48ebedfb-22af-45b3-b7cc-22ef533b6de5",
        "to": [
            { "email": "<EMAIL>", "name": "Lộc lấp lánh" },
            { "email": "<EMAIL>", "name": "Chung chinh chiến" }
        ]
        "body": {
            "subject": "Day la subject",    /* Subject của email */
            "content": "Day la html thì phải"   /* nội dung email */
        }
    },
    ...
  ]
}
"""


# ==================================================================================================================================
# *********************************************** Merchant gửi tin nhắn ***************************************************
# * v2.0.0                                                                                                                         *
# **********************************************************************************************************************************
"""
@api {POST} /api/v2.0/merchant/send/sms Merchant gửi tin nhắn 
@apiDescription API Merchant gửi tin nhắn web
@apiVersion 2.0.0
@apiName MerchantSendSms
@apiGroup Message

@apiUse 401
@apiUse 405


@apiParam      (Body:)     {String}                brand_name            brand_name
@apiParam      (Body:)     {String}                phone_number          Content
@apiParam      (Body:)     {String}                content               Nội dung tin nhắn


@apiParamExample    {json}      Body example:
{
      "brand_name": "MOBIO",
      "phone_number": '0363335020',
      "content": "Chao ban",
}
"""

# ==================================================================================================================================
# *********************************************** Merchant gửi tin ZNS ***************************************************
# * v2.0.0                                                                                                                         *
# **********************************************************************************************************************************
"""
@api {POST} /api/v2.0/merchant/send/zns Merchant gửi tin ZNS 
@apiDescription API Merchant gửi tin zns
@apiVersion 2.0.0
@apiName MerchantSendZns
@apiGroup Message

@apiUse 401
@apiUse 405

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam      (Body:)     {String}                page_social_id        Page_social_id
@apiParam      (Body:)     {String}                template_id           Template_id
@apiParam      (Body:)     {String}                phone_number          Phone_number
@apiParam      (Body:)     {Dict}                  template_data         Key-value tương ứng với teamplate zns


@apiParamExample    {json}      Body example:
{
  "page_social_id": "671089420100339247",
  "template_id": "3158203",
  "phone_number": "0363335020",
  "template_data": {
    "otp": "631168"
  }
}

"""

# ==================================================================================================================================
# ************************************************** YÊU CẦU GỬI NHIỀU THÔNG ĐIỆP **************************************************
# * v2.0.0                                                                                                                         *
# **********************************************************************************************************************************
"""
@api {POST} /api/v2.0/message/multiple/<channel>/transaction Yêu cầu gửi nhiều thông điệp transaction theo từng channel 
@apiDescription API chỉ cho phép IP đã được đăng ký gửi request.
@apiVersion v2.0.0
@apiName PostMultipleMessageTransaction
@apiGroup Message

@apiUse 401
@apiUse 500

@apiParam (Resources) {string=email sms} channel Kênh gửi tin

@apiParam (from) {string=call-center retail social-crm marketing email-marketing loyalty profiling admin} source Nguồn yêu cầu gửi tin nhắn.
@apiParam (from) {string} sender_id UUID người gửi


@apiParam (Email-from) {string} [sender_id] UUID người gửi. Nếu không gửi sẽ lấy default là <code>noreply@domain_đăng_ký</code>
@apiParam (Email-from) {string} merchant_id UUID merchant gửi
@apiParam (Email-from) {string} [sender_name] Tên người gửi
@apiParam (Email-from) {string} [sender_config_id] UUID  config_id config gửi email personal
@apiParam (Email-from) {boolean} [push_event] Push event
@apiParam (Email-from) {string} sender_domain Domain muốn gửi
@apiParam (Email-from) {string=BROAD_CAST TRANSACTION PERSONAL} sender_type=BROAD_CAST Kiểu gửi email
@apiParam (Email-from) {boolean} [is_fake_response] Fake dữ liệu callback trả về 
@apiParam (Email-from) {array} [cc] cc người gửi
@apiParam (Email-from) {array} [bcc] bcc người gửi
@apiParam (Email-from) {string} [Reply-To] Reply-To người gửi

@apiParam (SMS-from) {string} [staff_id] Nhân viên gửi
@apiParam (SMS-from) {string} [push_event] Đẩy dữ liệu sang event


@apiParamExample {json} Email
{
    "from": {
        "source": "marketing", /* Nguồn yêu cầu gửi tin */
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
        "sender_id": "<EMAIL>", /* email người gửi */
        "sender_name": "Support Mobio", /* tên người gửi */
        "sender_domain": "mobio.vn",    /* domain muốn gửi email */
        "campaign_id": "04354484-71c8-4b93-bb6b-8ad9a0d4cde9",  /* UUID campaign chạy chiến dịch */
        "business_id": "15c7b89a-02ad-41ee-903a-bf664bac4702",  /* UUID master campaign *?
        "sender_type": "BROAD_CAST",
        "anti_spam": false /* Trạng thái chống spam */,
        "bcc": ["<EMAIL>"],
        "cc": ["<EMAIL>"]
        "Reply-To": "<EMAIL>",
        "push_event": true

    },
    "to": [
    {
        "sent_webhook": "https://...",  /* link webhook nhận thông báo trạng thái gửi mail */
        "open_webhook": "http://...",  /* link webhook nhận thông báo khi mail được open */
        "profile_id": "48ebedfb-22af-45b3-b7cc-22ef533b6de5",
        "to": [
            { "email": "<EMAIL>", "name": "Lộc lấp lánh" },
            { "email": "<EMAIL>", "name": "Chung chinh chiến" }
        ]
        "body": {
            "subject": "Day la subject",    /* Subject của email */
            "content": "Day la html thì phải",   /* nội dung email */
            "attachments": ["https://api-test1.mobio.vn//emk/images/9487be34-ca24-4a85-acdd-9824d6d41240.jpg"]   /* link public media upload lên media */
        }
    },
    ...
  ]
}

@apiParamExample {json} SMS
{
    "from": {
        "source": "marketing", /* Nguồn yêu cầu gửi tin */
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
        "brandname": "MOBIO",
        "campaign_id": "04354484-71c8-4b93-bb6b-8ad9a0d4cde9",  /* UUID campaign chạy chiến dịch */
        "business_id": "15c7b89a-02ad-41ee-903a-bf664bac4702",  /* UUID master campaign *?
        "anti_spam": true, /* Trạng thái chống spam */,
        "staff_id": "15c7b89a-02ad-41ee-903a-bf664bac4702",
        "push_event": true
    },
    "to": [
    {
        "sent_webhook": "https://...",  /* link webhook nhận thông báo trạng thái gửi SMS */
        "open_webhook": "http://...",  /* link webhook nhận thông báo khi SMS được open */
        "phone_number": "083xxxxxxxx"
        "profile_id": "48ebedfb-22af-45b3-b7cc-22ef533b6de5",
        "body": {
            "content": "Day la message" /* nội dung tin SMS */
        }
    },
    ...
  ]
}

@apiParamExample {json} ZALO_ZNS
{
    "from": {
        "source": "marketing", /* Nguồn yêu cầu gửi tin */
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",  /* uuid merchant yêu cầu gửi tin nhắn */
        "brandname": "MOBIO",
        "campaign_id": "04354484-71c8-4b93-bb6b-8ad9a0d4cde9",  /* UUID campaign chạy chiến dịch */
        "business_id": "15c7b89a-02ad-41ee-903a-bf664bac4702",  /* UUID master campaign */
        "staff_id": "15c7b89a-02ad-41ee-903a-bf664bac4702",
        "push_event": true,
        "social_type": 2,           /* social_type: FACEBOOK = 1, ZALO = 2, INSTAGRAM = 3, YOUTUBE = 4, INFOBIP = 50 */
        "page_social_id": "154064374335884244",   /* page_social */
        "supplier": "zalo"           /* nhà cung cấp ZALO = "zalo", INFOBIP = "infobip", VIETGUYS = "vietguys" */
    },
    "to": [
    {
        "sent_webhook": "https://...",  /* link webhook nhận thông báo trạng thái gửi ZNS */
        "open_webhook": "http://...",  /* link webhook nhận thông báo khi ZNS được open */
        "profile_id": "48ebedfb-22af-45b3-b7cc-22ef533b6de5",
        "body": {
             "phone": "03633xxxxx",      /* Số điện thoại */
             "template_id": "205769",     /* Template id của zns, Đăng ký trên zalo, khuyến cáo nên để kiểu dữ liệu là string */
             "template_data": {         /* Data của tempalte zns dưới dạng key-value: key là field name trong listParams của template   */
                  "otp": "506508"
            }
        }
    },
    ...
  ]
}

@apiParamExample {json} App Mobile
{
    "from": {
        "source": "marketing",  /* Nguồn yêu cầu gửi tin */
        "merchant_id": "659a...1297c",  /* uuid merchant yêu cầu gửi tin nhắn */
        "app_code": "MOBIO",
        "anti_spam": false, /* Trạng thái chống spam */
        "status": true, /* Lưu thông báo hay không */
    },
    "to": [
    {
        "push_webhook": "https://...",    /* link webhook đã gửi thông báo trạng thái gửi */,
        "sent_webhook": "https://...",    /* link webhook nhận thông báo trạng thái gửi */,
        "open_webhook": "http://...",  /* link webhook nhận thông báo khi mail được open */,       
        "profile_id": "48ebedfb-22af-45b3-b7cc-22ef533b6de5",
        "to": [
            {"os_push_id": "ANDROID", "push_id": "ALDKKAWIELASDLJFDF29ADKASL0K32L32KLDSAK210"},
            {"os_push_id": "IOS", "push_id": "ALDKKAWIELASDLJFDF29ADKASL0K32L32KLDSAK210"},
            {
              "app_id": "BVBANK_MOBILE",    /* app_id */
              "os_push_id": "BVBANK_MOBILE",    /* os_push_id: 'ANDROID','IOS','VIB','ICHECK','MYVIB','MSB_PLUS','MSB_MBANK','HDB_MB','VNPAY_OTT','EIB_MOBILE','SHB_MOBILE','SHB_SAHA_MOBILE','SHB_SAHA_15_MOBILE','MSB_DIGI_SALE','VNPT_MOBILE','BVBANK_MOBILE','MOBIO_DEV','DLVN_MOBILE'*/
              "push_id": "********",
              "lang": "VI",
              "device_id": null
            }
        ],
        "body": {
            "alert": {
                "title": "title",     <===> key 'alert'
                "body": "Day la push duoc gui tu Notify Manager abc xyz",      /*Nội dung hiển thị*/
                "content_type": "text",  /*Định dạng text/html/popup*/
            },
            "badge": 10,
            "data": {  /* Thông tin kèm theo */
              "deal_name": "Sale"
            }
        }
    }
  ]
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message":"Request successful."
}
"""

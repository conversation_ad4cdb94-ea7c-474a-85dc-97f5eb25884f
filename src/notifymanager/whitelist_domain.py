# Get List Template Sms
# version: 2.0
############################################################################################
"""
@api {GET} /api/v2.0/whitelist_domain/list
Get List domain whitelist
@apiDescription API Lấy danh sách domain
@apiVersion 2.0.0
@apiGroup whitelist_domain
@apiName Get list domain

@apiHeader (Headers:) {String} Content-Type
@apiHeader (Headers:) {String} X-Merchant-ID

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:)   {string}       [search]                     Search by name or content
@apiParam (Query:)   {int}          page                         Số page tìm kiếm, -1 lấy all phần tử
@apiParam (Query:)   {int}          per_page                     Số item trên một page.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK:
{
    "code": 200,
    "data": [
        {
            "id": "154064374335884244",
            "domain": "mobio.vn",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 0,
        "per_page": 15,
        "total_count": 5,
        "total_page": 1
    }
}
"""

# Update domain
# version: 2.0.0
###########################################################################################
"""
@api {PUT} /api/v2.0/whitelist_domain/<domain_id> API Update domain
@apiDescription API Update domain
@apiVersion 2.0.0
@apiGroup whitelist_domain
@apiName Update domain

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header

@apiParam (Query:)   {string}      lang                        Ngôn ngữ. VD: &lang=vi Default value: vi

@apiParam (Body:)   {string}       domain                      domain

@apiParamExample    {json}      Body example:
{
  "domain": "mobio.vn"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "code": 200,
   "lang": "vi",
   "message": "request thành công."
}
"""

# Delete domain
# version: 2.0.0
###########################################################################################
"""
@api {DELETE} /api/v2.0/whitelist_domain/<domain_id> API Delete domain
@apiDescription API Delete domain
@apiVersion 2.0.0
@apiGroup whitelist_domain
@apiName Delete domain

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header

@apiParam (Query:)   {string}      lang                        Ngôn ngữ. VD: &lang=vi Default value: vi

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "code": 200,
   "lang": "vi",
   "message": "request thành công."
}
"""

# insert domain
# version: 2.0.0
###########################################################################################
"""
@api {POST} /api/v2.0/whitelist_domain API insert domain
@apiDescription API insert domain
@apiVersion 2.0.0
@apiGroup whitelist_domain
@apiName insert domain

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header

@apiParam (Query:)   {string}      lang                        Ngôn ngữ. VD: &lang=vi Default value: vi

@apiParam (Body:)   {string}       domain                      domain

@apiParamExample    {json}      Body example:
{
  "domain": ["mobio.vn"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "code": 200,
   "lang": "vi",
   "message": "request thành công."
}
"""

# import domain
# version: 2.0.0
###########################################################################################
"""
@api {POST} /api/v2.0/whitelist_domain/import API import domain
@apiDescription API import domain
@apiVersion 2.0.0
@apiGroup whitelist_domain
@apiName import domain

@apiHeader (Headers:) {String} Content-Type <code>application/form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header

@apiParam   (Body:)    {File}         file                    File Excel cần import.

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "success": true,
  "message": "request thành công."
}
"""


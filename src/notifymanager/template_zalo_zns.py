# Get List Template Sms
# version: 2.0
############################################################################################
"""
@api {GET} /api/v2.0/template/zalo_zns
Get List Template Zalo Zns
@apiDescription API Lấy danh sách các template Zns
@apiVersion 2.0.0
@apiGroup TemplateZaloZns
@apiName Get Template Zalo Zns

@apiHeader (Headers:) {String} Content-Type 
@apiHeader (Headers:) {String} X-Merchant-ID 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:)   {string}       [search]                     Search by name or content
@apiParam (Query:)   {int}          page                         Số page tìm kiếm, -1 l<PERSON>y all phần tử
@apiParam (Query:)   {int}          per_page                     Số item trên một page.
@apiParam (Query:)   {string}       page_social_id               page_social_id
@apiParam (Query:)   {string}       type                         Loại tin nhắn: OTP --> Tin OPT, TVB --> Tin văn bản,
TDB --> Tin dạng bảng, mặc đinh lấy TVB
@apiParam (Query:)   {string}       [template_id]                  template_id

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK:
{
    "code": 200,
    "data": [
        {
            "dynamic_fields": [
                {
                    "field": "otp",
                    "replace": "otp"
                }
            ],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "page_social_id": "154064374335884244",
            "status": "ENABLE",
            "template_id": 205355,
            "template_name": "Mẫu tin OTP"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 0,
        "per_page": 15,
        "total_count": 5,
        "total_page": 1
    }
}
"""

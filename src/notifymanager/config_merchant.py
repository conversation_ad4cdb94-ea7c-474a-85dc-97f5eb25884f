
# Upsert configs theo merchant
# version: 2.0
############################################################################################
"""
@api {PUT} /api/v2.0/merchants/<merchant_id>/configs Upsert configs sender by merchant
@apiDescription API Upsert configs, Configs field tham khảo file exec: ProviderConfig Define
@apiVersion 2.0.0
@apiGroup Config
@apiName Upsert configs

@apiHeader (Headers:) {String} Content-Type
@apiHeader (Headers:) {String} X-Merchant-ID
@apiHeader (Headers:) {String} Authorization: Basic <string>

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {String}                auth_name                     user_name củ<PERSON> tà<PERSON> kho<PERSON>n
@apiParam      (Body:)     {String}                auth_pass                     user_pass của tài khoản
@apiParam      (Body:)     {String}                provider_type                 provider_type định nghĩa đầu gửi, tham khảo ở file excel
@apiParam      (Body:)     {String}                auth_attachment               Brand_name, domain, website tùy cấu hình của kênh gửi
@apiParam      (Body:)     {String}                provider_api                  Api call sang các bên
@apiParam      (Body:)     {Int}                   status                        Trạng thái của cấu hình 1: enable, 2: disable
@apiParam      (Body:)     {JSOn}                  others                        Configs mở rộng tùy vào đầu cấu hình
@apiParam      (Body:)     {String}                type                         CSKH/QC/APP_USER/APP_PROFILE

@apiParamExample    {json}      Body example:
{
    "auth_name": "auth_name",
    "auth_pass": "auth_pass",
    "provider_type": 115,
    "auth_attachment": "GUARDIAN",
    "provider_api": "http://mkt.vivas.vn:9380/SMSBNAPINEW/sendsms",
    "others": {},
    "status": 1,
    "type":"CSKH"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công."
}
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 24/03/2024
"""
"""
@apiDefine 401Custom
@apiVersion 1.0.0
@apiHeader (Headers:) {String} Authorization Token/api-key để sử dụng api. Nếu typically là:
<li><code>Basic</code> thì Authenticate là API-Key</li>
@apiHeaderExample Basic Token Example:
{
    "Authorization":"Basic 25630732-2976-444a-ba7a-3ca1b48fcc3a"
}
@apiError (Error 4xx) 401-Unauthorized token/api-key hết hạn hoặc sai định dạng. Yêu cầu login lại.
<li><code>code:</code> 401</li>
<li><code>message:</code> Mô tả lỗi.</li>
<br/>
@apiErrorExample    {json}  HTTP/1.1 401
HTTP/1.1 401 Unauthorized
{
    "code": 401,
    "message":"Token is invalid or is expired. Please login again."
}
"""
"""
@api {POST} {domain}/partner/mobilebackend/api/v1.0/sms/receiver                   Đầu ghi nhận kết quả SMS từ phía EIB
@apiDescription         Đầu ghi nhận kết quả SMS từ phía EIB
@apiName ReceiverSMS
@apiGroup SMS
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {string}    client_id             Mã định danh ứng dụng gửi kết quả SMS
                                                        <ul>
                                                            <li>eib_sms</li>
                                                        </ul>                                                            
@apiParam   (Body:)   {string}    type_uses               Mục đích sử dụng của SMS
                                                        <ul>
                                                            <li><code>tracking_consent</code>: Tương ứng với tin nhắn dùng để tracking consent</li>
                                                        </ul>
@apiParam   (Body:)   {string(11)}    phone_number        <code>Số điện thoại của Khách hàng</code>
@apiParam   (Body:)   {string}    reference_id            <code>ID</code> của request (Tương ứng với RequestID)
@apiParam   (Body:)   {string}    action_time             Thời gian nhận được tin nhắn (Format: yyyy-MM-dd HH:mm:ss). <code>Example: "2024-03-25 14:22:52"</code>
@apiParam   (Body:)   {string}    message_content         Nội dung tin nhắn được khách hàng trả lời
@apiParam   (Body:)   {string}    signature                 Chữ ký sử dụng thuật toán HMAC-SHA256 để tạo chuỗi HEX String với chuỗi plan text được cộng từ các giá trị của:  client_id, type_uses, phone_number, reference_id, action_time, message_content.
                                                            <code>plan_text=client_id+type_uses+phone_number+reference_id+action_time+message_content</code>
                                                            signature = HMAC-SHA256(plainText,secretKey);


@apiParamExample {json} Body example
{
    "client_id": "eib_sms",
    "type_uses": "tracking_consent",
    "phone_number": "01234567890",
    "reference_id": "b59e9e88-e9af-11ee-bba4-acde48001122",
    "action_time": "2024-03-25 14:22:52",
    "message_content": "EIB 0333022200333003 YES",
    "signature": "ca8d28b780a6f53de1c0314dfdf8777b26476caf4d715c258e790fb4af13ccb2"
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
}
"""

"""
@api {POST} {domain}/mobilebackend/api/v1.0/sms/request/confirm-consent                   API nhận thông tin gửi SMS để xác nhận consent
@apiDescription         API nhận thông tin gửi SMS để xác nhận consent
@apiName RequestSendSMS
@apiGroup SMS
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)   {string}    session          Phiên làm việc
@apiParam   (Query:)   {string}    flow_name            Luồng đang thao tác
                                                        <ul>
                                                            <li><code>teller_app</code> :: Luồng teller app</li>
                                                            <li><code>cbbh_add_profile</code> :: Luồng cán bộ bán hàng</li>
                                                        </ul>

@apiParam   (Body:)   {string}    profile_name          Họ tên khách hàng (MaxLength: <code>27</code>)                                                         
@apiParam   (Body:)   {string}    phone_number          Số điện thoại gửi tin nhắn


@apiParamExample {json} Body example
{
    "profile_name": "Nguyễn Văn A",
    "phone_number": "09832411312"
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {object}    data        Dữ liệu
@apiSuccess {string}     data.log_id        Log ID

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "log_id": "0a6707bd-83da-481f-89f3-5201d6b54d47"
    }
}
"""

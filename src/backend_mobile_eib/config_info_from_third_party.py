#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 11/06/2024
"""

"""
@apiDefine 401Custom
@apiVersion 1.0.0
@apiHeader (Headers:) {String} Authorization Token/api-key để sử dụng api. Nếu typically là:
<li><code>Basic</code> thì Authenticate là API-Key</li>
@apiHeaderExample Basic Token Example:
{
    "Authorization":"Basic 25630732-2976-444a-ba7a-3ca1b48fcc3a"
}
@apiError (Error 4xx) 401-Unauthorized token/api-key hết hạn hoặc sai định dạng. Yêu cầu login lại.
<li><code>code:</code> 401</li>
<li><code>message:</code> Mô tả lỗi.</li>
<br/>
@apiErrorExample    {json}  HTTP/1.1 401
HTTP/1.1 401 Unauthorized
{
    "code": 401,
    "message":"Token is invalid or is expired. Please login again."
}
"""
"""
@api {POST} {domain}/partner/mobilebackend/api/v1.0/information/config/third-party/api                   Đầu cấu hình thông tin khi call API kết nối với ThirdParty
@apiDescription         Đầu cấu hình thông tin khi call API kết nối với ThirdParty
@apiName InformationConfigAPI
@apiGroup InformationConfig
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {string}    client_id             Mã định danh third party
                                                        <ul>
                                                            <li>eib</li>
                                                        </ul>                                                            
@apiParam   (Body:)   {string}    type_config           Kiểu cấu hình
                                                        <ul>
                                                            <li><code>basic_username_password</code>: Cấu hình username, password để tạo basic token khi call API</li>
                                                        </ul>
                                                        
@apiParam   (Body:)   {Object}    configuration                 Dữ liệu cấu hình
@apiParam   (Body:)   {string}    configuration.username        Username cấu hình đối với type config là <code>basic_username_password</code>
@apiParam   (Body:)   {string}    configuration.password        Password cấu hình đối với type config là <code>basic_username_password</code>



@apiParamExample {json} Body example
{
    "client_id": "eib",
    "type_config": "basic_username_password",
    "configuration": {
        "username": "eib",
        "password": "eib"
    }
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
}
"""

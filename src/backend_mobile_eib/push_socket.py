#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 11/04/2024
"""

"""
@api NONE Socket nhận khi có phản hồi của khách hàng khi trả lời tin nhắn.
@apiDescription Socket nhận khi có phản hồi của khách hàng khi trả lời tin nhắn.
@apiGroup Socket
@apiVersion 1.0.0
@apiName SocketProfileReplySMSConfirmConsent

@apiSuccess {string=mobilebackend_profile_reply_sms_confirm_consent}    socket_type=mobilebackend_profile_reply_sms_confirm_consent        Kiểu socket
                                          
@apiSuccess {String}    log_id     Định danh log
@apiSuccess {String}    profile_name     Tên khách hàng
@apiSuccess {String}    phone_number     Số điện thoại gửi tin nhắn
@apiSuccess {String}    merchant_id     Merchant id
@apiSuccess {String}    account_id      Tên nhân viên
@apiSuccess {String}    merchant_id     Merchant id
@apiSuccess {boolean}   is_confirm_consent     Trạng thái xác nhận Consent
                                                <ul>
                                                    <li><code>true</code>: đồng ý xác nhận consent</li>
                                                    <li><code>false</code>: không đồng ý xác nhận consent</li>
                                                </ul>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "socket_type":  "mobilebackend_profile_reply_sms_confirm_consent",
    "profile_name": "Nguyễn Văn A",
    "phone_number": "***********",
    "merchant_id": "",
    "account_id": "",
    "is_confirm_consent": true,
    "log_id": ""
}
"""

"""
@api NONE Socket nhận khi giải mã căn cước công dân thành công.
@apiDescription Socket nhận khi giải mã căn cước công dân thành công.
@apiGroup Socket
@apiVersion 1.0.0
@apiName SocketSuccessfulDecodedCitizenID

@apiSuccess {string=mobilebackend_successful_decoded_citizen_id}    socket_type=mobilebackend_successful_decoded_citizen_id        Kiểu socket
                                          
@apiSuccess {object}    data        Dữ liệu encryption
@apiSuccess {string}    data.status         Trạng thái giải mã
                                            <ul>
                                                <li><code>success</code>: Thành công</li>
                                                <li><code>fail</code>: Thất bại</li>
                                            </ul>
@apiSuccess {string}    [data.reason]       Lý do trong trường hợp fail.
@apiSuccess {string}    data.log_id       <code>ID</code> log
@apiSuccess {string}    [data.cif]        Dữ liệu cif nếu có
@apiSuccess {boolean}    [data.isAuthInformation]       Thông tin có xác thực hay không?
                                                        <ul>
                                                            <li><code>True</code>: có xác thực</li>
                                                            <li><code>False</code>: không xác thực</li>
                                                        </ul>
@apiSuccess {boolean}    [data.isAuthTemporary]       Thông tin có xác thực hay không? So sánh với database của bên thứ 3.
                                                        <ul>
                                                            <li><code>True</code>: có xác thực</li>
                                                            <li><code>False</code>: không xác thực</li>
                                                        </ul>
@apiSuccess {boolean}    [data.isAuthC06]               Thông tin xác thực với C06
                                                        <ul>
                                                            <li><code>True</code>: có xác thực</li>
                                                            <li><code>False</code>: không xác thực</li>
                                                        </ul>                                                            
@apiSuccess {boolean}    [data.isNextAuthFace]          Có cần chuyển sang bước xác thực khuôn mặt không?
                                                        <ul>
                                                            <li><code>True</code>: có</li>
                                                            <li><code>False</code>: không</li>
                                                        </ul> 
@apiSuccess {boolean}    [data.isSameCardNoRequest]     Số CCCD trong giải mã có giống với số CCCD check CIF hay không?
                                                        <ul>
                                                            <li><code>True</code>: có</li>
                                                            <li><code>False</code>: không</li>
                                                        </ul> 
@apiSuccess {string}    [data.idCardRequestCheckCif]   Số CCCD request check CIF
@apiSuccess {object}    data.cardInformation        Thông tin card

@apiSuccess {string}    data.cardInformation.deviceType         Loại thiết bị
@apiSuccess {string}    data.cardInformation.dateOfExpiry       Ngày hết hạn
@apiSuccess {string}    data.cardInformation.fatherName         Họ và tên bố
@apiSuccess {string}    data.cardInformation.ethnic             Dân tộc
@apiSuccess {string}    data.cardInformation.address            Địa chỉ
@apiSuccess {string}    data.cardInformation.gender             Giới tính
@apiSuccess {string}    data.cardInformation.idCard             Số giấy tờ
@apiSuccess {string}    data.cardInformation.placeOfResidence   Nơi thường trú
@apiSuccess {string}    data.cardInformation.motherName         Họ và tên mẹ
@apiSuccess {string}    data.cardInformation.dateOfBirth        Ngày tháng năm sinh
@apiSuccess {string}    data.cardInformation.personalSpecificIdentification     Đặc điểm nhận dạng
@apiSuccess {string}    data.cardInformation.dateOfIssuance                     Ngày hiệu lực
@apiSuccess {string}    data.cardInformation.religion                           Tôn giáo
@apiSuccess {string}    data.cardInformation.nationality                        Quốc tịch
@apiSuccess {string}    data.cardInformation.oldIdCardNo                        Số giấy tờ cũ
@apiSuccess {string}    data.cardInformation.placeOfOrigin                      Quê quán
@apiSuccess {string}    data.cardInformation.name                               Họ và tên trên giấy tờ
@apiSuccess {string}    data.cardInformation.spouseName                         Họ và tên vợ/chồng
@apiSuccess {string}    data.cardInformation.cardImage                          Chuỗi Base64 của ảnh CCCD
@apiSuccess {string}    data.cardInformation.typeCard                           Loại giấy tờ
                                                                                <ul>
                                                                                    <li><code>CCCD</code>: Loại CCCD</li>
                                                                                    <li><code>CCUOC</code>: Loại CCUOC</li>
                                                                                </ul>
@apiSuccess {string}    data.cardInformation.placeOfIssuance                      Nơi cấp phát
@apiSuccess {string}    data.cardInformation.codePlaceOfIssuance                  Mã nơi cấp phát

                                                                                

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "socket_type":  "mobilebackend_successful_decoded_citizen_id",
    "data": {
        "log_id": "",
        "status": "success",
        "cif": "",
        "isAuthInformation": False,
        "isAuthTemporary": False,
        "isAuthC06": False,
        "isNextAuthFace": False,
        "isSameCardNoRequest": True,
        "idCardRequestCheckCif": "056095011725",
        "cardInformation": {
            "deviceType": "TEST_DEVICE",
            "dateOfExpiry": "10/09/2035",
            "fatherName": "",
            "ethnic": "Kinh",
            "address": "HANOI",
            "gender": "Nam",
            "idCard": "056095011725",
            "placeOfResidence": "Lô 15, Ô 5, Thôn Đất Lành, Vĩnh Thái, Nha Trang, Khánh Hòa",
            "motherName": "",
            "dateOfBirth": "10/09/1995",
            "personalSpecificIdentification": "Sẹo chấm C:1cm5 sau cánh mũi trái",
            "dateOfIssuance": "12/09/2022",
            "religion": "Phật giáo",
            "nationality": "Việt Nam",
            "oldIdCardNo": "225576873",
            "placeOfOrigin": "Nha Trang, Khánh Hòa",
            "name": "Nguyễn Hồ Phi Long",
            "spouseName": "",
            "cardImage": "",
            "typeCard": "CCCD",
            "placeOfIssuance": "Cục trưởng cục CS ĐK QLCT và Du liệu QG về dân cước",
            "codePlaceOfIssuance": "01075"
        }
    }
}
"""
"""
@api NONE Socket nhận khi xác thực khuôn mặt của khách hàng.
@apiDescription Socket nhận khi xác thực khuôn mặt của khách hàng
@apiGroup Socket
@apiVersion 1.0.0
@apiName CustomerFaceAuthenticationSocket

@apiSuccess {string=mobilebackend_customer_face_authentication}    socket_type=mobilebackend_customer_face_authentication        Kiểu socket
                                          
@apiSuccess {object}    data        Dữ liệu encryption
@apiSuccess {string}    data.log_id <code>ID</code> log

@apiSuccess {bool}      data.isSaveCustomer         Trạng thái lưu customer
@apiSuccess {bool}      data.existEdigi             Trạng thái tồn tại thông tin khách hàng trong DB Edigi
@apiSuccess {string}    data.cif                    Thông tin CIF của khách hàng
@apiSuccess {int}       [data.remaining_attempts_verify] Số lượt còn lại để quét mặt tương ứng với trường hợp <code> trạng thái là Fail</code>
@apiSuccess {string}    data.status         Trạng thái giải mã
                                            <ul>
                                                <li><code>success</code>: Thành công</li>
                                                <li><code>fail</code>: Thất bại</li>
                                            </ul>
@apiSuccess {string}    [data.reason]       Lý do trong trường hợp fail.
@apiSuccess {string}    [data.state]        Bước bị fail.
                                            <ul>
                                                <li>check_face:: Bước kiểm tra khuôn mặt bị lỗi</li>
                                                <li>verify_image:: Bước verify image bị lỗi</li>
                                            </ul>
@apiSuccess {object}    [data.data_face]    Dữ liệu khuôn mặt
@apiSuccess {float}     [data.data_face.faceliveness]        Hình ảnh live của khuôn mặt
@apiSuccess {float}     [data.data_face.matching]        Tỉ lệ khớp khuôn mặt
@apiSuccess {boolean}   [data.data_face.iveness]        Khuông mặt trong hình ảnh có phải người thật hay không
@apiSuccess {boolean}   [data.data_face.faceMasked]        Khuông mặt trong hình ảnh có bị che hay không
@apiSuccess {object}    [data.data_face.features]        
@apiSuccess {boolean}    [data.data_face.eyesOpen]        Khuôn mặt trong hình ảnh có mởmắt hay không
@apiSuccess {boolean}    [data.data_face.blurFace]        Hình ảnh có bị mờ hay không


@apiSuccessExample Response success:
{
    "socket_type":  "mobilebackend_successful_decoded_citizen_id",
    "data": {
        "status": "success",
        "isSaveCustomer": true,
        "remaining_attempts_verify": 10,
        "data_face": {
            "faceliveness": ""
            "matching": 10.3,
            "iveness": true,
            "faceMasked": true,
            "features": {},
            "eyesOpen": true,
            "blurFace": true,
        }
    }
}
@apiSuccessExample Response Fail:
{
    "socket_type":  "mobilebackend_successful_decoded_citizen_id",
    "data": {
        "status": "fail",
        "state": "verify_image",
        "reason": "Message error",
        "remaining_attempts_verify": 10,
        "data_face": {
            "faceliveness": ""
            "matching": 10.3,
            "iveness": true,
            "faceMasked": true,
            "features": {},
            "eyesOpen": true,
            "blurFace": true,
        }
    }
}
"""

"""
@api NONE Notify nhận khi có phản hồi của khách hàng về việc tra cứu mã CIC.
@apiDescription Notify nhận khi có phản hồi của khách hàng về việc tra cứu mã CIC.
@apiGroup Socket
@apiVersion 1.0.0
@apiName NotifyReplyRequestSearchCIC

@apiSuccess {string=mobilebackend_notify_push_in_app_reply_request_search_cic}    notify_type=mobilebackend_notify_push_in_app_reply_request_search_cic        Kiểu notify push in app
                                          
@apiSuccess {object}    body        Thông tin notify
@apiSuccess {object}    body.filter      Cấu hình bộ lọc
@apiSuccess {string}    body.filter.customer_type                               Loại khách hàng
                                                                                        <ul>
                                                                                            <li>KHCN: Khách hàng cá nhân</li>
                                                                                            <li>KHDN: Khách hàng doanh nghiệp</li>
                                                                                        <ul>
                                                                                        
@apiSuccess {string}    body.filter.[start_time]                                Thời gian bắt đầu tìm kiếm thông tin. Theo định dạng: %Y-%m-%dT%H:%MZ. <code>Đối với khách hàng CN và DN</code>
@apiSuccess {string}    body.filter.[end_time]                                  Thời gian kết thúc tìm kiếm thông tin. Theo định dạng: %Y-%m-%dT%H:%MZ. <code>Đối với khách hàng CN và DN</code>
@apiSuccess {Object}    body.filter.profile_identify                            Thông tin giấy tờ cần tìm kiếm. <code>Đối với khách hàng CN</code>
@apiSuccess {string}    body.filter.profile_identify.identify_type              Loại giấy tờ
                                                                                        <ul>
                                                                                            <li><code>identity_card</code>: Căn cước công dân</li>
                                                                                        </ul>
@apiSuccess {string}    body.filter.profile_identify.identify_value             Giá trị cần tìm kiếm của loại giấy tờ

@apiSuccess {string}    body.filter.profile_name                                Tên của khách hàng cần tìm kiếm. <code>Đối với khách hàng CN</c
@apiSuccess {string}    body.filter.company_tax_code                            Mã số thuế của công ty. <code>Đối với khách hàng DN</code>
@apiSuccess {string}    body.filter.company_name                                Tên công ty. <code>Đối với khách hàng DN</code>
@apiSuccess {string}    body.filter.business_registration                       Giấy phép kinh doanh. <code>Đối với khách hàng DN</code>

@apiSuccess {string}    body.filter.cic_code                                    Mã CIC. <code>Đối với khách hàng CN và DN</code>
@apiSuccess {array}     body.filter.list_product_code                           Danh sách mã sản phẩm. <code>Đối với khách hàng CN và DN</code>

                                                                                                                                                                
@apiSuccess {Object}    body.data                            Thông tin chi tiết của báo cáo
@apiSuccess {string}    body.data.cic_code                   Mã CIC
@apiSuccess {string}    body.data.profile_name               Tên khách hàng. <code>Trong trường hợp là CN</code>
@apiSuccess {string}    body.data.company_name               Tên công ty. <code>Trong trường hợp là DN</code>
@apiSuccess {string}    body.data.company_tax_code           Mã số thuế của công ty. <code>Trong trường hợp là DN</code>
@apiSuccess {string}    body.data.business_registration      Số giấy phép kinh doanh. <code>Trong trường hợp là DN</code>
@apiSuccess {string}    body.data.product_code               Mã sản phẩm. <code>Trong trường hợp là CN và DN</code>
@apiSuccess {string}    body.data.report_status              Trạng thái báo cáo. <code>Trong trường hợp là CN và DN</code>
@apiSuccess {string}    body.data.requested_date             Ngày tra cứu. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiSuccess {string}    body.data.received_date              Ngày nhận. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiSuccess {string}    body.data.report_doc_id            Doc ID của PDF đã được upload lên ECM
@apiSuccess {string}    body.data.report_doc_name          Tên của PDF được upload lên ECM.
@apiSuccess {string}    body.data.sheet_number             Mã số phiếu

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "notify_type":  "mobilebackend_reply_request_search_cic",
    "body": {
        "filter": {
            "profile_identify": {
                "identify_type": "identity_card",
                "identify_value": "*********"
            },
            "profile_name": "Nguyen Van A",
            "cic_code": "*********",
            "start_time": "2020-01-01T00:00Z",
            "end_time": "2020-01-01T00:00Z",
            "list_product_code": ["S111"],
            "force_new_flag": false
        },
        "data": {
            "cic_code": "*********",
            "profile_name": "Nguyen Van A",
            "company_tax_code": "*********",
            "business_registration": "*********",
            "product_code": "S111",
            "report_status": "1",
            "requested_date": "2020-01-01T00:00Z",
            "received_date": "2020-01-01T00:00Z",
            "report_doc_id": "*********",
            "report_doc_name": "Nguyen Van A",
            "sheet_number": "*********"
        }
        
    }
}
"""
"""
@api NONE Notify nhận khi có phản hồi về đăng ký sản phẩm dịch vụ luồng Quick Sales.
@apiDescription Notify nhận khi có phản hồi về đăng ký sản phẩm dịch vụ luồng Quick Sales.
@apiGroup Socket
@apiVersion 1.0.0
@apiName NotifyReplyRequestQuickSales

@apiSuccess {string=mobilebackend_result_quick_sales}    notify_type=mobilebackend_result_quick_sales        Kiểu notify push in app
                                          
@apiSuccess {object}    body        Thông tin notify
@apiSuccess {string}    body.merchant_id      Merchant id
@apiSuccess {string}    body.staff_id      Staff id
@apiSuccess {string}    body.form_id      Form id
@apiSuccess {string}    body.status             Status
                                                <ul>
                                                    <li><code>success</code>: Thành công</li>
                                                    <li><code>fail</code>: Thất bại</li>
                                                </ul>
@apiSuccess {string}    body.object_id      Object id
@apiSuccess {string}    body.object_type      Object type
@apiSuccess {string}    body.object_name      Tên của đối tượng
@apiSuccess {string}    [body.reason]      Lý do thất bại

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "notify_type":  "mobilebackend_result_quick_sales",
    "body": {
        "merchant_id": "*********",
        "staff_id": "*********",
        "form_id": "*********",
        "status": "success",
        "object_id": "*********",
        "object_type": "*********",
        "object_name": "Profile mở tài khoản",
    }
}
"""

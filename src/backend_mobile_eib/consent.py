#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 11/04/2024
"""

"""
@api {GET} {domain}/mobilebackend/api/v1.0/consent/profile/get-by-phone L<PERSON><PERSON> thông tin consent của profile bằng số điện thoại
@apiDescription         Lấy thông tin consent của profile bằng số điện thoại
@apiGroup        Consent
@apiName ProfileConfirmConsentByPhone

@apiParam   (Query:)   {string}    phone_number          Số điện thoại của profile

@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {object}    data        Dữ liệu file confirm consent
@apiSuccess {object}    data.link   Link file consent

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "link": "https://t1.mobio.vn/static/file-consent.docx"
    }
}

"""

"""
@api {GET} {domain}/mobilebackend/api/v1.0/evident-consent-by-log-id            L<PERSON><PERSON> thông tin file consent của profile bằng log-id
@apiDescription          L<PERSON>y thông tin file consent của profile bằng log-id
@apiGroup        Consent
@apiName EvidentConsentByLogID

@apiParam   (Query:)   {string}    log_id          Phần này được trả về trong socket <code>mobilebackend_profile_reply_sms_confirm_consent</code>

@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {object}    data        Dữ liệu file confirm consent
@apiSuccess (data)      {String}         _id                      <code>ID</code> file upload lên hệ thống
@apiSuccess (data)      {Array}          type_media               Loại Media
@apiSuccess (data)      {String}         title                    Tên file upload
@apiSuccess (data)      {String}         format_file              Định dạng của file
@apiSuccess (data)      {String}         url                      Link file
@apiSuccess (data)      {String}         capacity                 Dung lượng file
@apiSuccess (data)      {String}         local_path               Đường dẫn vậy lý của file
@apiSuccess (data)      {String}         merchant_id              ID của merchant
@apiSuccess (data)      {String}         objectId                 ID của doi tuong (mặc định là None)


@apiSuccess {Array}             data                          Thông tin file upload
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "620dc147dfd20bf34ac6954f",
        "action_time": 1645043415.593171,
        "capacity": 1082923,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "filename": "1645068615_0E0A7559.jpg",
        "format_file": "image/jpeg",
        "id": "620dc147dfd20bf34ac6954f",
        "local_path": "/Users/<USER>/workspace/mobio/Module-Ticket/ticket/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "objectId": None,
        "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/11/2024
"""
"""
@api {GET} {domain}/mobilebackend/api/v1.0/los/status                   Lấy danh sách trạng thái của hồ sơ.
@apiDescription         Lấy danh sách trạng thái của hồ sơ.
@apiName GetListStatus
@apiGroup LOS
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {Array}     data        Dữ liệu
@apiSuccess {string}    data.title  Tiêu đề của trạng thái
@apiSuccess {string}    data.key    Key của trạng thái
@apiSuccess {string}    data.color  Mã màu của trạng thái
@apiSuccess {int}       data.order  Vị trí của trạng thái




@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "title": "Khởi tạo",
            "key": "init",
            "color": "#FFA500",
            "order": 1
        }
        
    ]
}
"""
"""
@api {GET} {domain}/mobilebackend/api/v1.0/los/merchant/config/fields                   Lấy danh sách field cấu hình của LOS
@apiDescription         Lấy danh sách field cấu hình của LOS
@apiName LosFields
@apiGroup LOS
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess   {Number}    code        Response status
@apiSuccess   {String}    message     Response message
@apiSuccess   {Array}     data        Dữ liệu
@apiSuccess   (Response:)     {String}      field_name         Tên field.
@apiSuccess   (Response:)     {String}      field_key          Key định danh của field.
@apiSuccess   (Response:)     {Number}      field_property            Kiểu dữ liệu của field.
@apiSuccess   (Response:)     {Number}      order         thứ tự sắp xếp.
@apiSuccess   (Response:)     {Bool}        disable_required_form_add     Không cho phép required ở form thêm
@apiSuccess   (Response:)     {Bool}        display_in_list               Trạng thái hiển thị ở danh sách LOS (True là hiển thị, False là không hiển thị)
@apiSuccess   (Response:)     {Bool}        display_in_detail             Trạng thái hiển thị ở chi tiết LOS (True là hiển thị, False là không hiển thị)
@apiSuccess   (Response:)     {Int}         section_in_list               Phần hiển thị ở danh sách
@apiSuccess   (Response:)     {Int}         section_in_detail             Phần hiển thị trong detail
@apiSuccess   (Response:)     {String}      format                        Định dạng

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "field_name": "field_name_1",
            "field_key": "field_key_1",
            "field_property": 1,
            "order": 1,
            "disable_required_form_add": true,
            "display_in_list": true,
            "display_in_detail": true,
            "section_in_list": 1,
            "section_in_detail": 1,
            "format": "YYYY-mm-DD HH:mm"
        }
    ]
}
"""
"""
@api {POST} {domain}/mobilebackend/api/v1.0/los/actions/filter                   Lấy danh sách hồ sơ theo bộ lọc
@apiDescription         Lấy danh sách hồ sơ theo bộ lọc
@apiName LosActionsFilter
@apiGroup LOS
@apiVersion 1.0.0

@apiUse paging

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {string}    assignee_id           Nhân viên phụ trách
@apiParam   (Body:)   {string}    sol_id                Mã đơn vị kinh doanh của nhân viên
@apiParam   (Body:)   {string}    username              User name của nhân viên
@apiParam   (Body:)   {object}    initialization_time   Thời gian khởi tạo
@apiParam   (Body:)   {string}    initialization_time.start_time    Thời gian bắt đầu. Theo định dạng: %Y-%m-%dT%H:%MZ
@apiParam   (Body:)   {string}    initialization_time.end_time      Thời gian kết thúc. Theo định dạng: %Y-%m-%dT%H:%MZ
@apiParam   (Body:)   {string}    initialization_time.type          Kiểu lọc
                                                                    <ul>
                                                                        <li>31_day_ago: 31 ngày trước</li>
                                                                        <li>custom_time: tuỳ chỉnh thời gian</li>
                                                                    </ul>
@apiParam   (Body:)   {array}     list_status        Danh sách trạng thái cần lọc của hồ sơ
@apiParam   (Body:)   {string}    customer_type      Loại khách hàng
                                                    <ul>
                                                        <li>CN: Khách hàng cá nhân</li>
                                                        <li>DN: Khách hàng doanh nghiệp</li>
                                                    </ul>

@apiParam   (Body:)   {string}    cif               Thông tin CIF
@apiParam   (Body:)   {string}    [no_cccd]         Số CCCD đối với Khách hàng CN
@apiParam   (Body:)   {string}    [tax_code]        Mã số thuế với Khách hàng DN



@apiParamExample {json} Body example
{
    "assignee_id": "0a6707bd-83da-481f-89f3-5201d6b54d47",
    "sol_id": "1234567890",
    "username": "admin",
    "initialization_time": {
        "start_time": "2024-11-01 00:00",
        "end_time": "2024-11-30 23:59",
        "type": "custom_time"
    },
    "list_status": ["init", "pending", "processing", "completed"],
    "customer_type": "CN",
    "cif": "1234567890",
    "no_cccd": "1234567890"
}

@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {Array}     data        Dữ liệu
@apiSuccess {string}    data.name   Tên của khách hàng
@apiSuccess {string}    data.cif    Số CIF của khách hàng
@apiSuccess {string}    data.customer_type    Loại khách hàng
@apiSuccess {string}    data.status_code    Mã trạng thái
@apiSuccess {string}    data.no_cccd   Số CCCD (Trong trường hợp khách hàng là KHCN)
@apiSuccess {string}    data.tax_code  Mã số thuế (Trong trường hợp khách hàng là KHDN)
@apiSuccess {float}     data.total_money    Tổng số tiền quy đổi.  

@apiSuccess {Array}     total_by_status    Tổng số item theo từng trạng thái
@apiSuccess {string}    total_by_status.status_code    Mã trạng thái
@apiSuccess {Number}    total_by_status.total_item    Tổng số item theo trạng thái


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "name": "John Doe",
            "cif": "1234567890",
            "customer_type": "CN",
            "status_code": "pending",
            "no_cccd": "",
            "tax_code": "",
            "total_money": 100000.00
        }
    ],
    "total_by_status": [
        {
            "status_code": "pending",
            "total_item": 10
        }
    ]
}
"""

"""
@api {GET} {domain}/mobilebackend/api/v1.0/los/detail-by-code                   Chi tiết hồ sơ theo mã hồ sơ
@apiDescription         Chi tiết hồ sơ theo mã hồ sơ
@apiName DetailLos
@apiGroup LOS
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:)    {string}            code                       Mã hồ sơ

@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {Object}     data        Dữ liệu
@apiSuccess {string}    data.code   Mã của hồ sơ
@apiSuccess {string}    data.process    Quy trình LOS
@apiSuccess {string}    data.step_code    Mã bước
@apiSuccess {string}    data.step_name    Tên bước
@apiSuccess {string}    data.processing_user    Người đang xử lý hồ sơ
@apiSuccess {string}    data.status_code    Mã trạng thái
@apiSuccess {string}    data.cancel_code    Mã lý do huỷ
@apiSuccess {string}    data.cancel_reason  Lý do huỷ
@apiSuccess {string}    data.cancel_reason_detail    Chi tiết lý do huỷ
@apiSuccess {string}    data.reject_reason  Lý do từ chối hồ sơ CTD
@apiSuccess {string}    data.updated_time   Ngày cập nhật
@apiSuccess {string}    data.eoffice_user   User xử lý trên Eoffice
@apiSuccess {string}    data.relationship_manager_username   User name của cán bộ quan hệ KH
@apiSuccess {Array}     data.product_informations       Thông tin sản phẩm
@apiSuccess {string}    data.product_informations.deal_code       Mã CHB
@apiSuccess {string}    data.product_informations.product_code    Mã sản phẩm/ chính sách
@apiSuccess {string}    data.product_informations.product_group   Nhóm sản phẩm
@apiSuccess {string}    data.product_informations.product         Sản phẩm
@apiSuccess {string}    data.product_informations.proposal_type   Loại đề nghị
@apiSuccess {float}     data.product_informations.proposed_amount_foreign_currency   Số tiền CTD đề xuất (nguyên tệ)
@apiSuccess {string}    data.product_informations.proposed_currency_foreign   Loại tiền
@apiSuccess {float}    data.product_informations.proposed_amount_converted    Số tiền CTD đề xuất (Quy đổi VND)
@apiSuccess {float}    data.product_informations.approved_amount_foreign_currency    Số tiền CTD phê duyệt (nguyên tệ)
@apiSuccess {string}    data.product_informations.approved_currency_foreign    Loại tiền CTD phê duyệt (nguyên tệ)
@apiSuccess {float}    data.product_informations.approved_amount_converted    Số tiền CTD phê duyệt (Quy đổi VND)




@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "code": "HD123456",
        "process": "LOS",
        "step_code": "001",
        "step_name": "Đăng ký",
        "processing_user": "admin",
        "status_code": "001",
        "status_name": "Đang xử lý",
        "cancel_code": "",
        "cancel_reason": "",
        "cancel_reason_detail": "",
        "reject_reason": "",
        "updated_time": "2024-11-01 10:30:00",
        "eoffice_user": "admin",
        "relationship_manager_username": "admin",
        "product_informations": [
            {
                "deal_code": "CTD123456",
                "product_code": "SP123456",
                "product_group: "SP123456",
                "product": "Sản phẩm 1",
                "proposal_type": "Đề xuất",
                "proposed_amount_foreign_currency": 1000000,
                "proposed_currency_foreign": "VND",
                "proposed_amount_converted": 1000000,
                "approved_amount_foreign_currency": 1000000,
                "approved_currency_foreign": "VND",
                "approved_amount_converted": 1000000,
            }
        ]
    }
}
"""

"""
@api {POST} {domain}/mobilebackend/api/v1.0/los/status/actions/upsert                   Upsert trạng thái hồ sơ
@apiDescription         Upsert trạng thái hồ sơ. Trong trường hợp <code>mã trạng thái</code> không tồn tại thì sẽ được thêm mới. Còn trong trường hợp <code>mã trạng thái</code> đã tồn tại thì sẽ update thông tin.
@apiName UpsertStatusLos
@apiGroup LOS
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {array[object]}    data           Lô dữ liệu cần được cập nhật hoặc bổ sung
@apiParam   (Body:)   {string}    data.status_code           Mã trạng thái
@apiParam   (Body:)   {string}    data.title                 Tên của trạng thái
@apiParam   (Body:)   {string}    [data.color]               Mã màu <code>HEX</code> của trạng thái. <code>Tham số này không bắt buộc. Trong trường hợp không gửi sang thì sẽ lấy mặc định là ""</code>
                                                            <br>Có thể lấy mã màu hex <a href="https://htmlcolorcodes.com/">tại đây</a>
@apiParam   (Body:)   {string}    data.status               Định nghĩa trạng thái sử dụng của trạng thái hồ sơ.
                                                            <ul>
                                                                <li><code>inactive</code>: Không hoạt động</li>
                                                                <li><code>active</code>: Hoạt động</li>
                                                                <li><code>delete</code>: Xoá</li>
                                                            </ul>

@apiParamExample {json} Body example
{
    "data": [
        {
            "status_code": "001",
            "title": "Khởi tạo",
            "color": "#FFA500",
            "status": "active"
        }
    ]
}

@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {array[object]}    data         Dữ liệu trả về
@apiSuccess {string}    data.title          Tiêu đề của trạng thái
@apiSuccess {string}    data.status_code    Mã của trạng thái
@apiSuccess {string}    data.color          Mã màu của trạng thái
@apiSuccess {string}    data.status         Trạng thái của trạng thái hồ sơ

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "status_code": "001",
            "title": "Khởi tạo",
            "color": "#FFA500",
            "status": "active"
        }
    ]
}
"""

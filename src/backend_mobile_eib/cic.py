#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 19/11/2024
"""
"""
@apiDefine 401Custom
@apiVersion 1.0.0
@apiHeader (Headers:) {String} Authorization Token/api-key để sử dụng api. Nếu typically là:
<li><code>Basic</code> thì Authenticate là API-Key</li>
@apiHeaderExample Basic Token Example:
{
    "Authorization":"Basic 25630732-2976-444a-ba7a-3ca1b48fcc3a"
}
@apiError (Error 4xx) 401-Unauthorized token/api-key hết hạn hoặc sai định dạng. Yêu cầu login lại.
<li><code>code:</code> 401</li>
<li><code>message:</code> Mô tả lỗi.</li>
<br/>
@apiErrorExample    {json}  HTTP/1.1 401
HTTP/1.1 401 Unauthorized
{
    "code": 401,
    "message":"Token is invalid or is expired. Please login again."
}
"""
"""
@api {POST} {domain}/mobilebackend/api/v1.0/cic/actions/get-code                   Lấy danh sách mã CIC của khách hàng
@apiDescription         Lấy danh sách mã CIC của khách hàng
@apiName GetCodeCIC
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {string}             customer_type           Loại khách hàng
                                                                    <ul>
                                                                        <li>KHCN: Khách hàng cá nhân</li>
                                                                        <li>KHDN: Khách hàng doanh nghiệp</li>
                                                                    <ul>
@apiParam   (Body:)     {Object}            profile_identify                            Thông tin giấy tờ cần tìm kiếm. <code>Đối với khách hàng CN</code>
@apiParam   (Body:)     {string}            profile_identify.customer_id                Thông tin định danh của KHCN

@apiParam   (Body:)     {string}            profile_name                                Tên của khách hàng cần tìm kiếm. <code>Đối với khách hàng CN</code>


@apiParam   (Body:)     {string}            company_tax_code                            Mã số thuế của công ty. <code>Đối với khách hàng DN</code>
@apiParam   (Body:)     {string}            company_name                                Tên công ty. <code>Đối với khách hàng DN</code>
@apiParam   (Body:)     {string}            business_registration                       Giấy phép kinh doanh. <code>Đối với khách hàng DN</code>

@apiParam   (Body:)     {string}            cic_code                                    Mã CIC. <code>Đối với khách hàng CN và DN</code>


@apiParamExample {json} Body KHCN:
{
    "profile_identify": {
        "customer_id": "*********"
    },
    "profile_name": "Nguyen Van A",
    "cic_code": "*********"
}

@apiParamExample {json} Body KHDN:
{
    "company_tax_code": "*********",
    "company_name": "Nguyen Van A",
    "business_registration": "*********",
    "cic_code": "*********"
}


@apiSuccess {Number}    code                Response status
@apiSuccess {String}    message             Response message
@apiSuccess {Array}     data                Dữ liệu
@apiSuccess {int}       data.order          Vị trí
@apiSuccess {string}    data.cic_code       Mã CIC
@apiSuccess {string}    data.profile_name   Tên khách hàng. <code>Trong trường hợp là CN</code>
@apiSuccess {string}    data.company_tax_code   Mã số thuế của công ty. <code>Trong trường hợp là DN</code>
@apiSuccess {string}    data.business_registration   Số giấy phép kinh doanh. <code>Trong trường hợp là DN</code>


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "order": 1,
            "cic_code": "*********",
            "profile_name": "Nguyen Van A",
            "company_tax_code": "*********",
            "business_registration": "*********"
        },
        {
            "order": 2,
            "cic_code": "*********",
            "profile_name": "Nguyen Van A",
            "company_tax_code": "*********",
            "business_registration": "*********"
        }
    ]
}
"""
"""
@api {POST} {domain}/mobilebackend/api/v1.0/cic/actions/get-history                   Lấy danh sách lịch sử tra cứu
@apiDescription         Lấy danh sách lịch sử tra cứu. Trong trường hợp không truyền start_time, end_time thì sẽ lấy mặc định 30 ngày kể từ thời điểm hiện tại.
@apiName GetHistoryFindCIC
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {string}             customer_type                               Loại khách hàng
                                                                                        <ul>
                                                                                            <li>KHCN: Khách hàng cá nhân</li>
                                                                                            <li>KHDN: Khách hàng doanh nghiệp</li>
                                                                                        <ul>
                                                                                        
@apiParam   (Body:)     {string}            [start_time]                                Thời gian bắt đầu tìm kiếm thông tin. Theo định dạng: %Y-%m-%dT%H:%MZ. <code>Đối với khách hàng CN và DN</code>
@apiParam   (Body:)     {string}            [end_time]                                  Thời gian kết thúc tìm kiếm thông tin. Theo định dạng: %Y-%m-%dT%H:%MZ. <code>Đối với khách hàng CN và DN</code>
@apiParam   (Body:)     {Object}            profile_identify                            Thông tin giấy tờ cần tìm kiếm. <code>Đối với khách hàng CN</code>
@apiParam   (Body:)     {string}            profile_identify.customer_id                Thông tin định danh của KHCN

@apiParam   (Body:)     {string}            profile_name                                Tên của khách hàng cần tìm kiếm. <code>Đối với khách hàng CN</code>

@apiParam   (Body:)     {string}            company_tax_code                            Mã số thuế của công ty. <code>Đối với khách hàng DN</code>
@apiParam   (Body:)     {string}            company_name                                Tên công ty. <code>Đối với khách hàng DN</code>
@apiParam   (Body:)     {string}            business_registration                       Giấy phép kinh doanh. <code>Đối với khách hàng DN</code>

@apiParam   (Body:)     {string}            cic_code                                    Mã CIC. <code>Đối với khách hàng CN và DN</code>
@apiParam   (Body:)     {array}             list_product_code                           Danh sách mã sản phẩm. <code>Đối với khách hàng CN và DN</code>


@apiParamExample {json} Body KHCN:
{
    "profile_identify": {
        "customer_id": "*********"
    },
    "profile_name": "Nguyen Van A",
    "cic_code": "*********",
    "start_time": "2020-01-01T00:00Z",
    "end_time": "2020-01-01T00:00Z",
    "list_product_code": ["S111"]
}

@apiParamExample {json} Body KHDN:
{
    "company_tax_code": "*********",
    "company_name": "Nguyen Van A",
    "business_registration": "*********",
    "cic_code": "*********",
    "start_time": "2020-01-01T00:00Z",
    "end_time": "2020-01-01T00:00Z",
    "list_product_code": ["S111"]
}


@apiSuccess {Number}    code                            Response status
@apiSuccess {String}    message                         Response message
@apiSuccess {int}       number_inquiry_remain           Số lượt tra cứu còn lại trong tháng
                                                        <ul>
                                                            <li>Trong trường hợp -1 thì là unlimited</li>
                                                        </ul>
@apiSuccess {Array}     data                            Dữ liệu
@apiSuccess {int}       data.order                      Vị trí
@apiSuccess {string}    data.cic_code                   Mã CIC
@apiSuccess {string}    data.profile_name               Tên khách hàng. <code>Trong trường hợp là CN</code>
@apiSuccess {string}    data.profile_customer_id        Số CCCD của khách hàng. <code>Trong trường hợp là CN</code>
@apiSuccess {string}    data.profile_customer_id        Số CCCD của khách hàng. <code>Trong trường hợp là CN</code>
@apiSuccess {string}    data.address                    Địa chỉ.
@apiSuccess {string}    data.company_name               Tên công ty. <code>Trong trường hợp là DN</code>
@apiSuccess {string}    data.company_tax_code           Mã số thuế của công ty. <code>Trong trường hợp là DN</code>
@apiSuccess {string}    data.business_registration      Số giấy phép kinh doanh. <code>Trong trường hợp là DN</code>
@apiSuccess {string}    data.product_code               Mã sản phẩm. <code>Trong trường hợp là CN và DN</code>
@apiSuccess {string}    data.report_status              Trạng thái báo cáo. <code>Trong trường hợp là CN và DN</code>
@apiSuccess {string}    data.requested_date             Ngày tra cứu. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiSuccess {string}    data.requested_user             Thông tin người tra cứu đầu tiên.
@apiSuccess {boolean}   data.reuse_flag                 Trạng thái có thể tái sử dụng hay không?
                                                        <ul>
                                                            <li><code>true</code>: Có thể tài sử dụng</li>
                                                            <li><code>false</code>: hỏi mới hoặc kho data trả về null</li>
                                                        </ul>

@apiSuccess {string}    data.received_date              Ngày nhận. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiSuccess {string}    data.status                     Trạng thái của bản tin
                                                        <ul>
                                                            <li>fail: thất bại</li>
                                                            <li>success: thành công</li>
                                                        </ul>
@apiSuccess {string}    [data.report_doc_id]            Doc ID của PDF đã được upload lên ECM
@apiSuccess {string}    [data.report_doc_name]          Tên của PDF được upload lên ECM.
@apiSuccess {string}    [data.sheet_number]             Mã số phiếu



@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "order": 1,
            "cic_code": "*********",
            "profile_name": "Nguyen Van A",
            "company_tax_code": "*********",
            "business_registration": "*********",
            "product_code": "S111",
            "report_status": "1",
            "requested_date": "2020-01-01T00:00Z",
            "received_date": "2020-01-01T00:00Z",
            "report_doc_id": "*********",
            "report_doc_name": "Nguyen Van A",
            "sheet_number": "*********",
            "reuse_flag": true,
            "requested_user": "Nguyen Van A",
            "status": "fail"
        }
    ]
}
"""
"""
@api {GET} {domain}/mobilebackend/api/v1.0/cic/detail-by-sheet-number/<sheet_number>                   Lấy chi tiết CIC dựa theo Sheet Number
@apiDescription         Lấy chi tiết CIC dựa theo Sheet Number
@apiName DetailBySheetNumber
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {Number}    code                            Response status
@apiSuccess {String}    message                         Response message
@apiSuccess {int}       number_inquiry_remain           Số lượt tra cứu còn lại trong tháng
                                                        <ul>
                                                            <li>Trong trường hợp -1 thì là unlimited</li>
                                                        </ul>
@apiSuccess {Object}    data                            Dữ liệu
@apiSuccess {int}       data.order                      Vị trí
@apiSuccess {string}    data.cic_code                   Mã CIC
@apiSuccess {string}    data.profile_name               Tên khách hàng. <code>Trong trường hợp là CN</code>
@apiSuccess {string}    data.profile_customer_id        Số CCCD của khách hàng. <code>Trong trường hợp là CN</code>
@apiSuccess {string}    data.address                    Địa chỉ.
@apiSuccess {string}    data.company_name               Tên công ty. <code>Trong trường hợp là DN</code>
@apiSuccess {string}    data.company_tax_code           Mã số thuế của công ty. <code>Trong trường hợp là DN</code>
@apiSuccess {string}    data.business_registration      Số giấy phép kinh doanh. <code>Trong trường hợp là DN</code>
@apiSuccess {string}    data.product_code               Mã sản phẩm. <code>Trong trường hợp là CN và DN</code>
@apiSuccess {string}    data.report_status              Trạng thái báo cáo. <code>Trong trường hợp là CN và DN</code>
@apiSuccess {string}    data.requested_date             Ngày tra cứu. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiSuccess {string}    data.received_date              Ngày nhận. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiSuccess {string}    data.status                     Trạng thái của bản tin
                                                        <ul>
                                                            <li>fail: thất bại</li>
                                                            <li>success: thành công</li>
                                                        </ul>
@apiSuccess {string}    [data.report_doc_id]            Doc ID của PDF đã được upload lên ECM
@apiSuccess {string}    [data.report_doc_name]          Tên của PDF được upload lên ECM.
@apiSuccess {string}    [data.sheet_number]             Mã số phiếu



@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "number_inquiry_remain": 5,
    "data": {
        "order": 1,
        "cic_code": "*********",
        "profile_name": "Nguyen Van A",
        "company_tax_code": "*********",
        "business_registration": "*********",
        "product_code": "S111",
        "report_status": "1",
        "requested_date": "2020-01-01T00:00Z",
        "received_date": "2020-01-01T00:00Z",
        "report_doc_id": "*********",
        "report_doc_name": "Nguyen Van A",
        "sheet_number": "*********",
        "status": "fail"
    }
}
"""
"""
@api {GET} {domain}/mobilebackend/api/v1.0/cic/products                   Lấy danh sách sản phẩm tra cứu.
@apiDescription         Lấy danh sách sản phẩm tra cứu dựa theo loại khách hàng.
@apiName GetProductCIC
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)   {string}     [search]                Từ khoá tìm kiếm
@apiParam   (Query:)   {string}     customer_type           Loại khách hàng
                                                            <ul>
                                                                <li>KHCN: Khách hàng cá nhân</li>
                                                                <li>KHDN: Khách hàng doanh nghiệp</li>
                                                            <ul>

@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {Array}     data        Dữ liệu
@apiSuccess {string}    data.name   Tên sản phẩm
@apiSuccess {string}    data.code   Mã sản phẩm
@apiSuccess {int}       data.order  Vị trí của sản phẩm


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "name": "SP chi tiết KH vay thế nhân có thông tin",
            "key": "S11A",
            "order": 1
        },
        {
            "name": "SP thông tin chủ thẻ tín dụng có thông tin",
            "key": "R14",
            "order": 1
        }
    ]
}
"""
"""
@api {GET} {domain}/mobilebackend/api/v1.0/cic/report-status                   Lấy danh sách trạng thái của báo cáo
@apiDescription         Lấy danh sách trạng thái của báo cáo
@apiName GetReportStatusCIC
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {Array}     data        Dữ liệu
@apiSuccess {string}    data.name   Tên trạng thái
@apiSuccess {string}    data.code   Mã trạng thái
@apiSuccess {string}    data.background_color   Mã màu nền


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "name": "Hỏi tin sang CIC thành công",
            "code": "1",
            "background_color": "#FF0000"
        }
    ]
}
"""
"""
@api {POST} {domain}/mobilebackend/api/v1.0/cic/actions/search-new                   Thực hiện tra cứu mới
@apiDescription         Thực hiện tra cứu mới
@apiName SearchNewCIC
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {string}             customer_type                               Loại khách hàng
                                                                                        <ul>
                                                                                            <li>KHCN: Khách hàng cá nhân</li>
                                                                                            <li>KHDN: Khách hàng doanh nghiệp</li>
                                                                                        <ul>
                                                                                        
@apiParam   (Body:)     {string}            [start_time]                                Thời gian bắt đầu tìm kiếm thông tin. Theo định dạng: %Y-%m-%dT%H:%MZ. <code>Đối với khách hàng CN và DN</code>
@apiParam   (Body:)     {string}            [end_time]                                  Thời gian kết thúc tìm kiếm thông tin. Theo định dạng: %Y-%m-%dT%H:%MZ. <code>Đối với khách hàng CN và DN</code>
@apiParam   (Body:)     {Object}            profile_identify                            Thông tin giấy tờ cần tìm kiếm. <code>Đối với khách hàng CN</code>
@apiParam   (Body:)     {string}            profile_identify.customer_id                Thông tin định danh của KHCN

@apiParam   (Body:)     {string}            profile_name                                Tên của khách hàng cần tìm kiếm. <code>Đối với khách hàng CN</code>

@apiParam   (Body:)     {string}            company_tax_code                            Mã số thuế của công ty. <code>Đối với khách hàng DN</code>
@apiParam   (Body:)     {string}            company_name                                Tên công ty. <code>Đối với khách hàng DN</code>
@apiParam   (Body:)     {string}            business_registration                       Giấy phép kinh doanh. <code>Đối với khách hàng DN</code>

@apiParam   (Body:)     {string}            cic_code                                    Mã CIC. <code>Đối với khách hàng CN và DN</code>
@apiParam   (Body:)     {array}             list_product_code                           Danh sách mã sản phẩm. <code>Đối với khách hàng CN và DN</code>
@apiParam   (Body:)     {boolean}           force_new_flag=false                        Cờ xác định việc hỏi mới hay không?
                                                                                        Trong trường hợp lần đầu gọi thì sẽ mặc định là false không hỏi mới.





@apiParamExample {json} Body KHCN:
{
    "profile_identify": {
        "customer_id": "*********"
    },
    "profile_name": "Nguyen Van A",
    "cic_code": "*********",
    "start_time": "2020-01-01T00:00Z",
    "end_time": "2020-01-01T00:00Z",
    "list_product_code": ["S111"],
    "force_new_flag": false
}

@apiParamExample {json} Body KHDN:
{
    "company_tax_code": "*********",
    "company_name": "Nguyen Van A",
    "business_registration": "*********",
    "cic_code": "*********",
    "start_time": "2020-01-01T00:00Z",
    "end_time": "2020-01-01T00:00Z",
    "list_product_code": ["S111"],
    "force_new_flag": false
}


@apiSuccess {Number}    code                            Response status
@apiSuccess {String}    message                         Response message
@apiSuccess {int}       number_inquiry_remain           Số lượt tra cứu còn lại trong tháng. Phần này sẽ được bên CICH2H trả về theo role của user tra cứu.
                                                        <ul>
                                                            <li>Trong trường hợp -1 thì là unlimited</li>
                                                        </ul>
@apiSuccess {Array}     data                            Dữ liệu
@apiSuccess {string}    data.cic_code                   Mã CIC
@apiSuccess {string}    data.product_code               Mã sản phẩm. <code>Trong trường hợp là CN và DN</code>
@apiSuccess {string}    data.report_status              Trạng thái báo cáo. <code>Trong trường hợp là CN và DN</code>
@apiSuccess {string}    data.requested_date             Ngày tra cứu. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiSuccess {string}    data.received_date              Ngày nhận. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiSuccess {string}    data.status                     Trạng thái của bản tin
                                                        <ul>
                                                            <li>fail: thất bại</li>
                                                            <li>success: thành công</li>
                                                        </ul>
@apiSuccess {boolean}   data.reuse_flag                 Trạng thái có thể tái sử dụng hay không?
                                                        <ul>
                                                            <li><code>true</code>: Có thể tài sử dụng</li>
                                                            <li><code>false</code>: hỏi mới hoặc kho data trả về null</li>
                                                        </ul>
@apiSuccess {string}    data.sheet_number               Mã số phiếu



@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "number_inquiry_remain": 5,
    "data": [
        {
            "cic_code": "*********",
            "product_code": "S111",
            "report_status": "1",
            "requested_date": "2020-01-01T00:00Z",
            "received_date": "2020-01-01T00:00Z",
            "sheet_number": "*********",
            "status": "success"
        }
    ]

}
"""
"""
@api {GET} {domain}/mobilebackend/api/v1.0/cic/actions/get-file-report/<report_id>                   Lấy file report by report-id
@apiDescription         Lấy link file report by report-id
@apiName GetFileReport
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {Number}    code                            Response status
@apiSuccess {String}    message                         Response message
@apiSuccess {Object}    data                            Dữ liệu
@apiSuccess {string}    data.url                        Link truy cập file report.



@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "url": "https://example.com/file.pdf"
    }

}
"""
"""
@api {POST} {domain}/mobilebackend/api/v1.0/cic/reports/history/list                   Báo cáo lịch sử tra cứu
@apiDescription         Báo cáo lịch sử tra cứu
@apiName CicReportHistory
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)    {string}            type                                        Kiểu lấy danh sách báo cáo
                                                                                        <ul>
                                                                                            <li>user: Báo cáo theo User</li>
                                                                                            <li>number_of_searches: Báo cáo theo số lượt tra cứu</li>
                                                                                        </ul>
                                                                                        
@apiParam   (Body:)     {string}            start_time                                  Thời gian bắt đầu tìm kiếm thông tin. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiParam   (Body:)     {string}            end_time                                    Thời gian kết thúc tìm kiếm thông tin. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiParam   (Body:)     {array}             area_codes                                  Danh sách <code>mã</code> khu vực.
@apiParam   (Body:)     {array}             sol_ids                                     Danh sách <code>mã</code> ĐVKD.

@apiParamExample {json} Body:
{
    "start_time": "2020-01-01T00:00Z",
    "end_time": "2020-01-01T00:00Z",
    "area_codes": [],
    "sol_ids": []
}


@apiSuccess {Number}    code                            Response status
@apiSuccess {String}    message                         Response message
@apiSuccess {Array}     data                            Dữ liệu
@apiSuccess {string}    data.date                       Ngày báo cáo. Theo định dạng: %Y-%m-%d.
@apiSuccess {int}       data.number_new_searches        Số lượt tra cứu mới.
@apiSuccess {int}       data.number_success_searches    Số lượt tra cứu Thành công.
@apiSuccess {string}    data.number_fail_searches       Số lượt tra cứu không thành công.



@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "date": "2020-01-01",
            "number_new_searches": 5,
            "number_success_searches": 5,
            "number_fail_searches": 5
        },
        {
            "date": "2020-01-02",
            "number_new_searches": 5,
            "number_success_searches": 5,
            "number_fail_searches": 5
        },
        {
            "date": "2020-01-03",
            "number_new_searches": 5,
            "number_success_searches": 5,
            "number_fail_searches": 5
        }
    ]

}
"""
"""
@api {POST} {domain}/mobilebackend/api/v1.0/cic/reports/history/total                   Báo cáo tổng lịch sử tra cứu
@apiDescription         Báo cáo tổng lịch sử tra cứu
@apiName CicTotalReportHistory
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)    {string}            type                                        Kiểu lấy danh sách báo cáo
                                                                                        <ul>
                                                                                            <li>user: Báo cáo theo User</li>
                                                                                            <li>number_of_searches: Báo cáo theo số lượt tra cứu</li>
                                                                                        </ul>
                                                                                        
@apiParam   (Body:)     {string}            start_time                                  Thời gian bắt đầu tìm kiếm thông tin. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiParam   (Body:)     {string}            end_time                                    Thời gian kết thúc tìm kiếm thông tin. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiParam   (Body:)     {array}             area_codes                                  Danh sách <code>mã</code> khu vực.
@apiParam   (Body:)     {array}             sol_ids                                     Danh sách <code>mã</code> ĐVKD.

@apiParamExample {json} Body:
{
    "start_time": "2020-01-01T00:00Z",
    "end_time": "2020-01-01T00:00Z",
    "area_codes": [],
    "sol_ids": []
}


@apiSuccess {Number}    code                            Response status
@apiSuccess {String}    message                         Response message
@apiSuccess {Object}     data                            Dữ liệu
@apiSuccess {int}       data.total_number_searches      Tổng số lượt tra cứu
@apiSuccess {int}       data.total_number_new_searches     Tổng số lượt tra cứu thành công



@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "total_number_searches": 5,
        "total_number_new_searches": 5
    }

}
"""
"""
@api {POST} {domain}/mobilebackend/api/v1.0/cic/reports/history/exports                   Xuất báo cáo lịch sử tra cứu
@apiDescription         Xuất báo cáo lịch sử tra cứu
@apiName CicExportReportHistory
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


                                                                                        
@apiParam   (Body:)     {string}            start_time                                  Thời gian bắt đầu tìm kiếm thông tin. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiParam   (Body:)     {string}            end_time                                    Thời gian kết thúc tìm kiếm thông tin. Theo định dạng: %Y-%m-%dT%H:%MZ.
@apiParam   (Body:)     {array}             [area_codes]                                Danh sách <code>mã</code> khu vực.
@apiParam   (Body:)     {array}             [sol_ids]                                   Danh sách <code>mã</code> ĐVKD.
@apiParam   (Body:)     {array}             emails                                      Danh sách email nhận kết quả export

@apiParamExample {json} Body:
{
    "start_time": "2020-01-01T00:00Z",
    "end_time": "2020-01-01T00:00Z",
    "area_codes": [],
    "sol_ids": [],
    "emails": ["<EMAIL>"]
}


@apiSuccess {Number}    code                            Response status
@apiSuccess {String}    message                         Response message



@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
}
"""

"""
@api {POST} {domain}/mobilebackend/api/v1.0/cic/actions/upsert/save-view                   Lưu lại trạng thái bộ lọc gần nhất CIC
@apiDescription         Lưu lại trạng thái bộ lọc gần nhất CIC
@apiName CicActionsUpsertSaveView
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
                                                                                        
@apiParam   (Body:)     {string}            area_code                                       Mã khu vực
                                                                                            <ul>
                                                                                                <li>HOME: Trang chủ</li>
                                                                                                <li>DETAIL_PROFILE: Chi tiết khách hàng</li>
                                                                                                <li>DETAIL_COMPANY: Chi tiết công ty</li>
                                                                                            </ul>
@apiParam   (Body:)     {string}            [object_id]                                     Định danh đối tượng.
                                                                                            Trong trường area_code là DETAIL_PROFILE, DETAIL_COMPANY thì object_id sẽ là id của đối tượng.
@apiParam   (Body:)     {object}            filters                                         Cấu hình thông tin bộ lọc. Phần này FE tự định nghĩa. FE truyền như nào BE sẽ ghi nhận.
                                                                                            

@apiParamExample {json} Body:
{
    "area_code": "HOME",
    "object_id": "",
    "filters": {
        "key1": "value1",
        "key2": "value2"
    }
}


@apiSuccess {Number}    code                            Response status
@apiSuccess {String}    message                         Response message



@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
}
"""

"""
@api {POST} {domain}/mobilebackend/api/v1.0/cic/actions/filters/save-view      Lấy save view bộ lọc theo khu vực
@apiDescription         Lấy save view bộ lọc theo khu vực
@apiName CicActionsFiltersSaveView
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
                                                                                        
@apiParam   (Body:)     {string}            area_code                                       Mã khu vực
                                                                                            <ul>
                                                                                                <li>HOME: Trang chủ</li>
                                                                                                <li>DETAIL_PROFILE: Chi tiết khách hàng</li>
                                                                                                <li>DETAIL_COMPANY: Chi tiết công ty</li>
                                                                                            </ul>
@apiParam   (Body:)     {string}            [object_id]                                     Định danh đối tượng.
                                                                                            Trong trường area_code là DETAIL_PROFILE, DETAIL_COMPANY thì object_id sẽ là id của đối tượng.
                                                                                            

@apiParamExample {json} Body:
{
    "area_code": "HOME",
    "object_id": "",
}


@apiSuccess {Number}    code                            Response status
@apiSuccess {String}    message                         Response message
@apiSuccess {Object}    data                            Dữ liệu trả về
@apiSuccess {String}    data.area_code                  Mã khu vực
@apiSuccess {String}    data.object_id                  Định danh đối tượng
@apiSuccess {Object}    data.filters                    Bộ lọc tương ứng



@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "area_code": "HOME",
        "object_id": "",
        "filters": {
            "key1": "value1",
            "key2": "value2"
        }
    }
}
"""
"""
@api {POST} {domain}/mobilebackend/api/v1.0/cic/receiver/search-results              Api nhận kết quả tra cứu bản tin CIC mới.
@apiDescription      Api báo có kết quả bản tin hỏi CIC, CICH2H sẽ gọi API này của CRM để thông báo kết quả.
@apiName CicReceiverSearchReults
@apiGroup CIC
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse json_header

@apiHeader (Headers:)   {String}            RequestID                                       Định danh của request. Không được trùng giữa các request
@apiHeader (Headers:)   {String}            ClientID                                        Định danh của Client request. Phần này sẽ do CRM cung cấp.
                                                                                        
@apiParam   (Body:)     {array}             data                                            Danh sách dữ liệu. Tối đa 10 phần tử.
@apiParam   (Body:)     {string}            data.sheetNumber                                     Mã số phiếu.
@apiParam   (Body:)     {string}            data.statusCode                                      Mã trạng thái mã số phiếu.
@apiParam   (Body:)     {string}            data.statusMessage                                   Trạng thái mã số phiếu.
@apiParam   (Body:)     {string}            data.pdfDocId                                        DocID của file PDF đã upload lên ECM.
@apiParam   (Body:)     {string}            data.pdfFileName                                     Tên báo cáo PDF (Mã số phiếu + “.pdf”)
@apiParam   (Body:)     {string}            data.requester                                       Người hỏi bản tin
@apiParam   (Body:)     {string}            data.requestedDate                                   Ngày hỏi bản tin. Định dạng Date unix timestamp.
@apiParam   (Body:)     {string}            data.receivedDate                                    Ngày nhận kết quả từ CIC. Định dạng Date unix timestamp.
                                                                                            

@apiParamExample {json} Body:
{
    "data": [
        {
            "sheetNumber": "EIBS11T202407030000000788",
            "statusCode": "2",
            "statusMessage": "Bản hỏi tin có báo cáo trả lời",
            "pdfDocId": "7Q8W9ERTY",
            "pdfFilename": "20230209102036S10AX992900020101616001803.pdf",
            "requester": "maker1",
            "requestedDate": "1677726189510",
            "receivedDate": "1677726189510"
        }
    ]
}


@apiSuccess {Number}    code                            Response status
@apiSuccess {String}    message                         Response message

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
}
"""
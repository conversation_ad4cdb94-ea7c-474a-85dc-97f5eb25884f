#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 11/04/2024
"""
"""
@apiDefine flow_name
@apiParam   (Query:)   {string}    flow_name            Luồng đang thao tác
                                                        <ul>
                                                            <li><code>teller_app</code> :: Luồng teller app</li>
                                                            <li><code>cbbh_add_profile</code> :: <PERSON><PERSON><PERSON> cán bộ bán hàng</li>
                                                            <li><code>quick_sales</code> :: Luồng quick sales</li>
                                                            <li><code>pre_approval</code> :: <PERSON><PERSON><PERSON> pre approval</li>
                                                        </ul>
"""


"""
@api {POST} {domain}/mobilebackend/api/v1.0/identification-card/decryption                   API nhận thông tin giải mã căn cước công dân
@apiDescription         API nhận thông tin giải mã căn cước công dân
@apiName IdentificationCardDecryption
@apiGroup Decryption
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)   {string}    [session]            Phiên làm việc
@apiUse flow_name
@apiParam   (Body:)   {string}    data_decryption          Dữ liệu mã hoá
@apiParam   (Body:)   {string}    sdk_request_round          Base64 số vòng mã hóa (Thông tin SDK trả về)
@apiParam   (Body:)   {string}    sdk_request_session          Chuỗi uuid dùng để mã hoá dữ liệu(Thông tin do SDK trả về)
@apiParam   (Body:)   {int}    request_timestamp          Timestamp tại thời diểm request


@apiParamExample {json} Body example
{
    "data_decryption": "",
    "sdk_request_session": "0a6707bd-83da-481f-89f3-5201d6b54d47",
    "sdk_request_round": "0a6707bd-83da-481f-89f3-5201d6b54d48",
    "request_timestamp": 1713254096
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {object}    data        Dữ liệu
@apiSuccess {string}     data.log_id        Log ID


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "log_id": "2f831383-9698-4088-85f5-c7365b8ed363"
    }
}
"""
"""
@api {POST} {domain}/mobilebackend/api/v1.0/face/check                   API nhận thông tin đối chiếu hình ảnh khuôn mặt
@apiDescription         API nhận thông tin đối chiếu hình ảnh khuôn mặt
@apiName CheckFae
@apiGroup Decryption
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)   {string}    [session]          Phiên làm việc
@apiUse flow_name

@apiParam   (Body:)   {string}    data_decryption          Dữ liệu mã hoá
@apiParam   (Body:)   {string}    sdk_request_session          Chuỗi uuid dùng để mã hoá dữ liệu(Thông tin do SDK trả về)
@apiParam   (Body:)   {string}    sdk_request_round          Base64 số vòng mã hóa (Thông tin SDK trả về)
@apiParam   (Body:)   {string}    raw_img_1                 Base64 ảnh 1 (Bỏ qua 100 ký tự đầu)
@apiParam   (Body:)   {string}    raw_img_2                 Base64 ảnh 2 (Bỏ qua 100 ký tự đầu)
@apiParam   (Body:)   {string}    image_verify              Hình ảnh đối chiếu. Dùng để call API VerifyImage. Lấy phần tử cuối cùng của mảng <code>images</code> do SDK trả về
@apiParam   (Body:)   {int}       request_timestamp          Timestamp tại thời diểm request




@apiParamExample {json} Body example
{
    "data_decryption": "",
    "sdk_request_session": "0a6707bd-83da-481f-89f3-5201d6b54d47",
    "sdk_request_round": "0a6707bd-83da-481f-89f3-5201d6b54d47",
    "raw_img_1": "0a6707bd-83da-481f-89f3-5201d6b54d47",
    "raw_img_2": "0a6707bd-83da-481f-89f3-5201d6b54d47",
    "request_timestamp": 1713254096,
    "image_verify": "0a6707bd-83da-481f-89f3-5201d6b54d47"
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {object}    data        Dữ liệu encryption
@apiSuccess {string}     data.log_id        Log ID
@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "log_id": "0a6707bd-83da-481f-89f3-5201d6b54d47"
    }
}
"""

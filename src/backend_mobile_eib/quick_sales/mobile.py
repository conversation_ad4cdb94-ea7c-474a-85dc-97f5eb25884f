#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 13/02/2025
"""

"""
@apiDefine flow_name
@apiParam   (Query:)   {string}    flow_name            Luồng đang thao tác
                                                        <ul>
                                                            <li><code>quick_sales</code> :: Luồng quick sales</li>
                                                            <li><code>pre_approved</code> :: Luồng pre approved</li>
                                                        </ul>
"""

"""
@api {POST}     {domain}/mobilebackend/api/v1.0/quick-sales/check/cif                   API kiểm tra CIF
@apiDescription         API kiểm tra CIF
@apiName CheckCIF
@apiGroup QuickSales
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)     {string}        [profile_name]                Tên khách hàng
@apiParam   (Body:)     {string}        phone_number                Số điện thoại
@apiParam   (Body:)     {object}        identify                    Thông tin giấy tờ cần tìm kiếm. <code>Đối với khách hàng CN</code>
@apiParam   (Body:)     {string}        identify.identify_type      Loại giấy tờ
                                                                    <ul>
                                                                        <li><code>citizen_identity</code>: Căn cước công dân</li>
                                                                    </ul>
@apiParam   (Body:)     {string}         identify.identify_value    Giá trị cần tìm kiếm


@apiParamExample {json} Body example
{
    "profile_name": "Nguyen Van A",
    "phone_number": "0909090909",
    "identify": {
        "identify_type": "citizen_identity",
        "identify_value": "**********"
    }
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {object}    data        Dữ liệu
@apiSuccess {boolean}   data.is_exist           Trạng thái tồn tại
@apiSuccess {boolean}   data.is_aml           Trạng thái kiểm tra AML
                                                <ul>
                                                    <li><code>true</code>: Không tồn tại nghi ngờ AML</li>
                                                    <li><code>false</code>: Tồn tại nghi ngờ AML</li>
                                                </ul>
@apiSuccess {string}    data.session_id         Định danh của phiên làm việc. (Cần truyền vào tham số của <code>session</code> của các API liên quan đến Xác thực khuôn mặt!)
@apiSuccess {string}    [data.cif]        Thông tin CIF của Profile trong trường hợp tồn tại


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "is_exist": true,
        "is_aml": true,
        "cif": "**********",
        "session_id": "**********"
    }
}
"""
"""
@api {GET}     {domain}/mobilebackend/api/v1.0/quick-sales/premium-account-lengths                   API lấy danh sách độ dài tài khoản
@apiDescription         API lấy danh sách độ dài tài khoản
@apiName PremiumAccountLengths
@apiGroup QuickSales
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {Array}     data        Dữ liệu
@apiSuccess {String}    data.name   Tên
@apiSuccess {String}    data.code   Mã
@apiSuccess {String}    data.fee    Chi phí


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "name": "Tài khoản 8 số",
            "code": "Y090804KHCN",
            "fee": 10000
        },
        {
            "name": "Tài khoản 9 số",
            "code": "Y090904KHCN",
            "fee": 10000
        },
        {
            "name": "Tài khoản 10 số",
            "code": "Y091004KHCN",
            "fee": 10000
        },
        {
            "name": "Tài khoản 12 số",
            "code": "Y091204KHCN",
            "fee": 10000
        }
    ]
}
"""
"""
@api {GET}     {domain}/mobilebackend/api/v1.0/quick-sales/premium-account-types                   API lấy danh sách loại tài khoản
@apiDescription         API lấy danh sách loại tài khoản
@apiName PremiumAccountTypes
@apiGroup QuickSales
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {Array}     data        Dữ liệu
@apiSuccess {String}    data.name   Tên
@apiSuccess {String}    data.code   Mã
@apiSuccess {String}    data.price_type    Loại chi phí
                                            <ul>
                                                <li><code>free</code>: Miễn phí</li>
                                                <li><code>charges</code>: Có phí</li>
                                            </ul>
@apiSuccess {Array}     data.next_element   Bước tiếp theo


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "name": "Số tài khoản ngân hàng tự tạo",
            "code": "STK_CUSTOM",
            "price_type": "free",
            "next_element": ["account_number"]
        },
        {
            "name": "Tài khoản theo số CCCD",
            "code": "STK_ID_CARD",
            "price_type": "free",
            "next_element": ["account_number"]
        },
        {
            "name": "Tài khoản theo số điện thoại",
            "code": "STK_PHONE_NUMBER",
            "price_type": "free",
            "next_element": ["account_number"]
        },
        {
            "name": "Tài khoản số đẹp",
            "code": "STK_PREMIUM_NUMBER",
            "price_type": "charges",
            "next_element": ["beauti_number_type", "account_number"]
        }
    ]
}
"""
"""
@api {GET}     {domain}/mobilebackend/api/v1.0/quick-sales/premium-number-types                   API lấy danh sách mã loại số đẹp
@apiDescription         API lấy danh sách mã loại số đẹp
@apiName PremiumNumberTypes
@apiGroup QuickSales
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {Array}     data        Dữ liệu
@apiSuccess {String}    data.name                Tên loại số đẹp
@apiSuccess {String}    data.code                Mã loại số đẹp

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "name": "Số giống nhau",
            "code": "01"
        },
        {
            "name": "Số phát lộc 6,8",
            "code": "02"
        },
        {
            "name": "Số phát tài 39,79",
            "code": "03"
        },
        {
            "name": "Số tiến",
            "code": "04"
        },
        {
            "name": "Số lục, ngũ, tứ, tam hoa",
            "code": "05"
        },
        {
            "name": "Số phát lộc 368, 386",
            "code": "06"
        },
        {
            "name": "Số lặp lại",
            "code": "07"
        },
        {
            "name": "Số soi gương",
            "code": "08"
        },
        {
            "name": "Số ngũ, tứ, tam hoa nhụy giữa",
            "code": "10"
        },
        {
            "name": "Số ngũ, tứ, tam hoa không nhụy",
            "code": "11"
        },
        {
            "name": "Số ngũ, tứtứ, tam hoa bằng nhau",
            "code": "12"
        },
        {
            "name": "Số ngũ, tứtứ, tam hoa khác nhau",
            "code": "13"
        },
        {
            "name": "Số lưu ý đặc biệt",
            "code": "14"
        },
        {
            "name": "Số tiến căp",
            "code": "15"
        },
        {
            "name": "Số hỗn hợp",
            "code": "19"
        }
    ]
}
"""
"""
@api {POST}     {domain}/mobilebackend/api/v1.0/quick-sales/premium-number-types-fee                   API lấy danh sách mã loại số đẹp và phí
@apiDescription         API lấy danh sách mã loại số đẹp và phí
@apiName PremiumNumberTypesFee
@apiGroup QuickSales
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)     {string}        premium_number_type_code                Mã loại số đẹp
@apiParam   (Body:)     {string}        premium_account_length_code                Mã độ dài tài khoản


@apiParamExample {json} Body example
{
    "premium_number_type_code": "01",
    "premium_account_length_code": "08"
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {Integer}   total       Tổng số dữ liệu

@apiSuccess {Array}     data        Dữ liệu
@apiSuccess {String}    data.premium_number_type_name                Tên loại số đẹp
@apiSuccess {String}    data.premium_number_type_code                Mã loại số đẹp
@apiSuccess {String}    data.fee                Phí


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "total": 2,
    "data": [
        {
            "premium_number_type_name": "3 So phat loc 6,8",
            "premium_number_type_code": "Y090302KHCN",
            "fee": 10000
        },
        {
            "premium_number_type_name": "3 So phat loc 368,386",
            "premium_number_type_code": "Y090306KHCN",
            "fee": 10000
        }
    ]
}
"""
"""
@api {POST}     {domain}/mobilebackend/api/v1.0/quick-sales/premium-accounts/actions/filter                   API lấy danh sách tài khoản số đẹp
@apiDescription         API lấy danh sách tài khoản số đẹp
@apiName PremiumAccountsFilter
@apiGroup QuickSales
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)     {string}        premium_account_type_code                   Mã loại tài khoản
@apiParam   (Body:)     {string}        premium_account_length_code                 Mã độ dài tài khoản
@apiParam   (Body:)     {string}        [number_customer_select]                    Số khách hàng chọn. <code>Sẽ tìm kiếm danh sách tài khoản dựa theo số khách hàng điền.</code>


@apiParamExample {json} Body example
{
    "premium_account_type_code": "01",
    "premium_account_length_code": "Y090306KHCN",
    "number_customer_select": ""
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {Integer}   total       Tổng số dữ liệu

@apiSuccess {Array}     data        Dữ liệu
@apiSuccess {String}    data.premium_account_number                Số tài khoản
@apiSuccess {String}    data.premium_account_type_code             Mã loại tài khoản
@apiSuccess {String}    data.premium_account_type_name             Tên loại tài khoản
@apiSuccess {String}    data.fee                                   Phí


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "total": 2,
    "data": [
        {
            "premium_account_number": "**********",
            "premium_account_type_code": "Y090302KHCN",
            "premium_account_type_name": "3 So phat loc 6,8",
            "fee": 10000
        },
        {
            "premium_account_type_name": "3 So phat loc 368,386",
            "premium_account_type_code": "Y090306KHCN",
            "premium_account_number": "**********",
            "fee": 10000
        }
    ]
}
"""
"""
@api {POST}     {domain}/mobilebackend/api/v1.0/quick-sales/accounts/actions/filter-by-type                   API lấy danh sách tài khoản theo loại
@apiDescription         API lấy danh sách tài khoản theo loại. Ví dụ: CCCD, SĐT
@apiName PremiumAccountsFilterByType
@apiGroup QuickSales
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)     {string}        type                    Loại tài khoản
                                                                <ul>
                                                                    <li><code>citizen_identity</code>: CCCD</li>
                                                                    <li><code>phone_number</code>: SĐT</li>
                                                                </ul>
@apiParam   (Body:)     {string}        value                   Giá trị


@apiParamExample {json} Body example
{
    "type": "citizen_identity",
    "value": "**********"
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {Integer}   total       Tổng số dữ liệu

@apiSuccess {Array}     data        Dữ liệu
@apiSuccess {String}    data.premium_account_number                Số tài khoản
@apiSuccess {String}    data.premium_account_type_code             Mã loại tài khoản
@apiSuccess {String}    data.premium_account_type_name             Tên loại tài khoản
@apiSuccess {String}    data.fee                                   Phí


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "total": 2,
    "data": [
        {
            "premium_account_number": "**********",
            "premium_account_type_code": "Y090302KHCN",
            "premium_account_type_name": "3 So phat loc 6,8",
            "fee": 10000
        },
        {
            "premium_account_type_name": "3 So phat loc 368,386",
            "premium_account_type_code": "Y090306KHCN",
            "premium_account_number": "**********",
            "fee": 10000
        }
    ]
}
"""
"""
@api {POST}     {domain}/mobilebackend/api/v1.0/quick-sales/premium-accounts/lock-unlock                   API khóa/mở khóa tài khoản số đẹp
@apiDescription         API khóa/mở khóa tài khoản số đẹp
@apiName PremiumAccountsLockUnlock
@apiGroup QuickSales
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)     {string}        account_number                   Số tài khoản
@apiParam   (Body:)     {string}        action                           Hành động
                                            <ul>
                                                <li><code>lock</code>: Khóa tài khoản</li>
                                                <li><code>unlock</code>: Mở khóa tài khoản</li>
                                            </ul>



@apiParamExample {json} Body example
{
    "account_number": "**********",
    "action": "lock"
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message



@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",

}
"""
"""
@api {POST} {domain}/mobilebackend/api/v1.0/citizen-ids/read-only-information                   API chỉ đọc thông tin căn cước công dân
@apiDescription         API chỉ đọc thông tin căn cước công dân
@apiName CitizenIdsReadOnlyInformation
@apiGroup QuickSales
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse flow_name

@apiParam   (Query:)   {string}    [session]            Phiên làm việc
@apiParam   (Body:)   {string}    data_decryption          Dữ liệu mã hoá
@apiParam   (Body:)   {string}    sdk_request_round          Base64 số vòng mã hóa (Thông tin SDK trả về)
@apiParam   (Body:)   {string}    sdk_request_session          Chuỗi uuid dùng để mã hoá dữ liệu(Thông tin do SDK trả về)
@apiParam   (Body:)   {int}    request_timestamp          Timestamp tại thời diểm request


@apiParamExample {json} Body example
{
    "data_decryption": "",
    "sdk_request_session": "0a6707bd-83da-481f-89f3-5201d6b54d47",
    "sdk_request_round": "0a6707bd-83da-481f-89f3-5201d6b54d48",
    "request_timestamp": 1713254096
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {object}    data        Dữ liệu
@apiSuccess {string}     data.log_id        Log ID


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "log_id": "2f831383-9698-4088-85f5-c7365b8ed363"
    }
}
"""

"""
@api {GET} {domain}/mobilebackend/api/v1.0/citizen-ids/validate                   API kiểm tra căn cước công dân có hợp lệ không?
@apiDescription         API kiểm tra căn cước công dân có hợp lệ không? (Check thông tin căn cước công dân với C06)
@apiName CitizenIdsValidate
@apiGroup QuickSales
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse flow_name

@apiParam   (Query:)   {string}    session            Phiên làm việc

@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {object}    data        Dữ liệu
@apiSuccess {Boolean}    data.is_auth_c06        Thông tin xác thực căn cước công dân với C06


@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "is_auth_c06": true
    }
}
"""

"""
@api {POST} {domain}/mobilebackend/api/v1.0/face-recognition/verify                   API nhận thông tin đối chiếu hình ảnh khuôn mặt
@apiDescription         API nhận thông tin đối chiếu hình ảnh khuôn mặt
@apiName FaceRecognitionVerify
@apiGroup QuickSales
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse flow_name

@apiParam   (Query:)   {string}   session          Phiên làm việc


@apiParam   (Body:)   {string}    data_decryption          Dữ liệu mã hoá
@apiParam   (Body:)   {string}    sdk_request_session          Chuỗi uuid dùng để mã hoá dữ liệu(Thông tin do SDK trả về)
@apiParam   (Body:)   {string}    sdk_request_round          Base64 số vòng mã hóa (Thông tin SDK trả về)
@apiParam   (Body:)   {string}    raw_img_1                 Base64 ảnh 1 (Bỏ qua 100 ký tự đầu)
@apiParam   (Body:)   {string}    raw_img_2                 Base64 ảnh 2 (Bỏ qua 100 ký tự đầu)
@apiParam   (Body:)   {string}    image_verify              Hình ảnh đối chiếu. Dùng để call API VerifyImage. Lấy phần tử cuối cùng của mảng <code>images</code> do SDK trả về
@apiParam   (Body:)   {int}       request_timestamp          Timestamp tại thời diểm request




@apiParamExample {json} Body example
{
    "data_decryption": "",
    "sdk_request_session": "0a6707bd-83da-481f-89f3-5201d6b54d47",
    "sdk_request_round": "0a6707bd-83da-481f-89f3-5201d6b54d47",
    "raw_img_1": "0a6707bd-83da-481f-89f3-5201d6b54d47",
    "raw_img_2": "0a6707bd-83da-481f-89f3-5201d6b54d47",
    "request_timestamp": 1713254096,
    "image_verify": "0a6707bd-83da-481f-89f3-5201d6b54d47"
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {object}    data        Dữ liệu encryption
@apiSuccess {string}     data.log_id        Log ID
@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "log_id": "0a6707bd-83da-481f-89f3-5201d6b54d47"
    }
}
"""

"""
@api {POST} {domain}/mobilebackend/api/v1.0/quick-sales/actions/retry                   API retry luồng Quick Sales
@apiDescription         API retry luồng Quick Sales
@apiName RetryQuickSales
@apiGroup QuickSales
@apiVersion 1.0.0

@apiUse 404
@apiUse 401Custom
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {string}    form_id          ID của form
@apiParam   (Body:)   {string}    object_id          ID của đối tượng
@apiParam   (Body:)   {string}    object_type          Loại đối tượng 
                                                        <ul>
                                                            <li><code>profile</code>: Profile</li>
                                                        </ul>





@apiParamExample {json} Body example
{
    "form_id": "**********",
    "object_id": "**********",
    "object_type": "profile"
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {object}    data        Dữ liệu
@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
    
    }
}
"""

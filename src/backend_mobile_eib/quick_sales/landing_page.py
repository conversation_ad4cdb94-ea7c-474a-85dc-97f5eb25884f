#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 20/02/2025
"""

"""
@api {POST}     {domain}/guest/mobilebackend/external/api/v1.0/quick-sales/landing-page/otp                   Lấy OTP để confirm landing page
@apiDescription         Lấy OTP để confirm landing page. L<PERSON>y OTP chỉ áp dụng trong thời gian link truy cập form còn tồn tại. Chỉ bấm được gửi mã OTP sau 1 phút.
@apiName LandingPageOtp
@apiGroup QuickSalesLandingPage
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apiParam (BODY:)        {String}    form_token     Token access form

@apiParamExample {json} Body example
{
    "form_token": "c29tZV9zYW1wbGVfdmFsdWU="
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
}
"""
"""
@api {POST}     {domain}/guest/mobilebackend/external/api/v1.0/quick-sales/landing-page/otp/verify                   Kiểm tra OTP confirm landing page có hợp lệ hay không?
@apiDescription         Kiểm tra OTP confirm landing page có hợp lệ hay không?
@apiName LandingPageOtpCheck
@apiGroup QuickSalesLandingPage 
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apiParam (BODY:)        {String}    form_token     Token access form
@apiParam (BODY:)        {String}    otp            OTP

@apiParamExample {json} Body example
{
    "form_token": "c29tZV9zYW1wbGVfdmFsdWU=",
    "otp": "123456"
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
}
"""

"""
@api {POST}     {domain}/guest/mobilebackend/external/api/v1.0/quick-sales/landing-page/submit                   Submit form landing page
@apiDescription         Submit form landing page
@apiName LandingPageSubmit
@apiGroup QuickSalesLandingPage
@apiVersion 1.0.0

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apiParam (BODY:)        {String}    form_token     Token access form
@apiParam (BODY:)        {String}    [otp]          OTP
@apiParam (BODY:)        {String}    status         Trạng thái submit form
                                                    <ul>
                                                        <li><code>confirm</code>: Xác nhận</li>
                                                        <li><code>cancel</code>: Hủy bỏ</li>
                                                    </ul>
@apiParam (BODY:)        {String}    [reason]       Lý do trong trường hợp hủy bỏ
@apiParam (BODY:)        {Array}     [extra_data]   Dữ liệu thêm
@apiParam (BODY:)        {String}    [extra_data.key]   Mã thông tin
@apiParam (BODY:)        {String}    [extra_data.value]   Giá trị thông tin
@apiParam (BODY:)        {Int}       [extra_data.status]   Trạng thái thông tin
                                                            <ul>
                                                                <li><code>1</code>: Xác nhận</li>
                                                                <li><code>0</code>: Không xác nhận</li>
                                                            </ul>






@apiParamExample {json} Body example
{
    "form_token": "c29tZV9zYW1wbGVfdmFsdWU=",
    "otp": "123456",
    "status": "confirm",
    "reason": "Lý do hủy bỏ",
    "extra_data": [
        {
            "key": "US_CITIZEN_OR_RESIDENT",
            "value": "Là công dân Hoa kỳ hoặc đối tượng cư trú tại Hoa Kỳ",
            "status": 1
        }
    ]
}


@apiSuccess {Number}    code        Response status
@apiSuccess {String}    message     Response message
@apiSuccess {Object}    data        Dữ liệu

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
}
"""

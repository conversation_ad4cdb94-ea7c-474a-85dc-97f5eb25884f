"""
         /\_/\
        ( o.o )
         > ^ <
        / /------\ \
    ~^~^~^~^~^~^~^~^~^~^~
    Author: Toandd
    Company: M O B I O
    Date Created: 04/12/23

"""

# Define 
"""
@apiDefine estimate_identity
@apiParam   (estimate_identity_object:)       {Integer}                 None                     ước tính danh tính

@apiSuccessExample {Integer} estimate_identity_object
18
"""

"""
@apiDefine report_by_gender
@apiParam   (report_by_gender_object:)       {String}                      name                Lo<PERSON><PERSON> giới t<PERSON>h
@apiParam   (report_by_gender_object:)       {Integer}                     total               Tổng số profile theo giới tính

@apiSuccessExample {json} report_by_gender_object
{
  "name": "Nam",
  "total": 16080
}
"""


"""
@apiDefine report_by_source
@apiParam   (report_by_source_object:)       {String}                      name                Nguồn ghi nhận thông tin
@apiParam   (report_by_source_object:)       {Integer}                     total               Tổng số profile

@apiSuccessExample {json} report_by_source_object
{
    "name": "Sale",
    "total": 20974
}
"""

"""
@apiDefine contact_infor
@apiParam   (contact_infor_object:)       {String}                      name                Nguồn ghi nhận thông tin
@apiParam   (contact_infor_object:)       {Integer}                     total               Tổng số profile

@apiSuccessExample {json} contact_infor_object
{
    "name": "primary_phone",
    "total": 20974
}
"""

"""
@apiDefine chart_total_member_by_week
@apiParam   (chart_total_member_by_week_object:)       {ArrayObject}                        data                      mảng dữ liệu phân tích theo ngày
@apiParam   (chart_total_member_by_week_object:)       {String}                       data.key                  "Hà Nội", "Hồ Chí Minh","Khu vực khác"
@apiParam   (chart_total_member_by_week_object:)       {Integer}                     data.value                Tổng số profile
@apiParam   (chart_total_member_by_week_object:)       {Datetime}                     time                      thời gian theo ngày
@apiParam   (chart_total_member_by_week_object:)       {ArrayObject}                        data_total                Mảng dữ liệu tổng hợp tổng số profile
@apiParam   (chart_total_member_by_week_object:)       {String}                       data_total.key                  "Hà Nội", "Hồ Chí Minh","Khu vực khác"
@apiParam   (chart_total_member_by_week_object:)       {Integer}                     data_total.value                Tổng số profile

@apiSuccessExample {json} chart_total_member_by_week_object
"data_total": [
        {
            "key": "Hà Nội",
            "value": 209
        },
        {
            "key": "Hồ Chí Minh",
            "value": 92
        },
        {
            "key": "Khu vực khác",
            "value": 143
        }
    ],
"data_total": [
      {
          "key": "Hà Nội",
          "value": 209
      },
      {
          "key": "Hồ Chí Minh",
          "value": 92
      },
      {
          "key": "Khu vực khác",
          "value": 143
      }
  ]

"""


"""
@apiDefine chart_total_member_by_card_id
@apiParam   (chart_total_member_by_card_id_object:)       {Integer}                      doc_count            Tổng số profile
@apiParam   (chart_total_member_by_card_id_object:)       {String}                       key                  key card id
@apiParam   (chart_total_member_by_card_id_object:)       {Float}                        percent              Phần trăm trên tổng số


@apiSuccessExample {json} chart_total_member_by_card_id_object
  {
      "doc_count": 76,
      "key": "09cc0dd7-73f7-41c6-8441-ee667b14619b",
      "percent": 100.0
  }
  
"""


"""
@apiDefine chart_total_member_by_region
@apiParam   (chart_total_member_by_region_object:)       {Integer}                      doc_count            Tổng số profile
@apiParam   (chart_total_member_by_region_object:)       {String}                       key                  "Hà Nội", "Hồ Chí Minh","Khu vực khác"
@apiParam   (chart_total_member_by_region_object:)       {Float}                        percent              Phần trăm trên tổng số


@apiSuccessExample {json} chart_total_member_by_region_object
  {
      "doc_count": 315,
      "key": "Hà Nội",
      "percent": 48.4
  },
  {
      "doc_count": 114,
      "key": "Hồ Chí Minh",
      "percent": 17.5
  },
  {
      "doc_count": 222,
      "key": "Khu vực khác",
      "percent": 34.1
  }
"""



"""
@apiDefine chart_total_profile_interest_category
@apiParam   (chart_total_profile_interest_category_object:)       {Integer}                      doc_count            Tổng số profile
@apiParam   (chart_total_profile_interest_category_object:)       {String}                       key                  Key loại sở thích
@apiParam   (chart_total_profile_interest_category_object:)       {String}                        key_i18n            key il18n


@apiSuccessExample {json} chart_total_profile_interest_category_object
{
  "doc_count": 200,
  "key": "Sport",
  "key_i18n": "i18n_sport"
}
"""


"""
@apiDefine chart_sapporo_profile_transaction

@apiParam   (chart_sapporo_profile_transaction_object:)       {ArrayObject}                  data         
@apiParam   (chart_sapporo_profile_transaction_object:)       {String}                       data.key                  Loại member
@apiParam   (chart_sapporo_profile_transaction_object:)       {Integer}                      data.value                Giá trị
@apiParam   (chart_sapporo_profile_transaction_object:)       {String}                       data.key                  Loại non-member
@apiParam   (chart_sapporo_profile_transaction_object:)       {Integer}                      data.value                Giá trị
@apiParam   (chart_sapporo_profile_transaction_object:)       {DateTime}                     time                      thời gian theo từng ngày


@apiSuccessExample {json} chart_sapporo_profile_transaction_object
 {
  "data": [
      {
          "key": "member",
          "value": 0
      },
      {
          "key": "non-member",
          "value": 0
      }
  ],
  "time": "2023-11-13"
},
"""


"""
@apiDefine overview_profiles_3
@apiParam   (overview_profiles_3_object:)       {String}                      name                      key
@apiParam   (overview_profiles_3_object:)       {Float}                       value                     giá trị


@apiSuccessExample {json} overview_profiles_3_object
{
  "name": "profiles_amount",
  "value": 0
}
"""

"""
@apiDefine chart_ages
@apiParam   (chart_ages_object:)       {String}                      doc_count                Tổng
@apiParam   (chart_ages_object:)       {Float}                       key                      Độ tuổi
@apiParam   (chart_ages_object:)       {Float}                       percent                  Tỉ lệ phần trăm


@apiSuccessExample {json} chart_ages_object
 {
    "doc_count": 2,
    "key": "18-24",
    "percent": 0.9
}
"""

"""
@apiDefine customer_classification
@apiParam   (customer_classification_object:)       {ArrrayObject}                      female                          Key giới tính
@apiParam   (customer_classification_object:)       {Integer}                          female.doc_count                Tổng
@apiParam   (customer_classification_object:)       {string}                            female.key                      key phân loaị khách hàng


@apiSuccessExample {json} customer_classification_object
 "female": [
      {
          "doc_count": 1,
          "key": "diamond"
      }
  ],
"""



# ********************************* Báo cáo theo giới tính ****************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} profiling-report/api/v1.0/report/gender Reported By Gender
@apiDescription Reported By Gender
@apiGroup Profiling Report
@apiVersion 1.0.0
@apiName  Reported By Gender


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse report_by_gender

@apisuccess       {ArrayObject}                                 data                                              Mảng data theo giới tính
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [report_by_gender], 
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ********************************* Báo cáo Licensed Profiles *************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} profiling-report/api/v1.0/report/profile/estimate-identity Reported Estimate Identity
@apiDescription Reported Estimate Identity
@apiGroup Profiling Report
@apiVersion 1.0.0
@apiName  Reported Estimate Identity


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse estimate_identity

@apisuccess       {ArrayObject}                                 data                                              estimate indentity
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": estimate_identity_object, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

# ************************* Báo cáo theo nguồn ghi nhận thông tin *********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} profiling-report/api/v1.0/report/created-account-type Report According to The source
@apiDescription Report According to The source of Information Recorded
@apiGroup Profiling Report
@apiVersion 1.0.0
@apiName  Report According to The source of Information Recorded


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse report_by_source

@apisuccess       {Integer}                                 data                                              Mảng gồm tổng số profile theo nguồn
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [report_by_source], 
  "lang": "vi", 
  "message": "request thành công."
}
"""

# ************************* Báo cáo Profiles theo thông tin thu thập *********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} profiling-report/api/v1.0/report/contact-info Report Contact Info
@apiDescription Report Contact Info
@apiGroup Profiling Report
@apiVersion 1.0.0
@apiName  Report Contact Info


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse contact_infor

@apiParam(Query:) {String}                                      [fields]                                            nguồn thu thập thông tin. VD primary_phone,primary_email,social_user                          

@apisuccess       {ArrayObject}                                 data                                              Mảng gồm tổng số profile theo nguồn
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [contact_infor], 
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ************************* Báo cáo thành viên mới theo thời gian *********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} profiling-report/api/v1.0/report/chart Report New members by week
@apiDescription Report New members by week
@apiGroup Profiling Report
@apiVersion 1.0.0
@apiName  Report New members by week


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse chart_total_member_by_week

@apiParam(Query:) {String}                                      key_chart                                         key báo cáo: chart_total_member_by_week                        
@apiParam(Query:) {String}                                      start_time                                       Thời gian bắt đầu dạng "%Y-%m-%dT%H:%M:%S.%fZ"                         
@apiParam(Query:) {String}                                      end_time                                          Thời gian kết thúc dạng "%Y-%m-%dT%H:%M:%S.%fZ"                                    

@apisuccess       {ArrayObject}                                 data                                              Mảng gồm tổng số profile theo nguồn
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [chart_total_member_by_week], 
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ************************* Báo cáo Tổng quan về Profiles *********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} profiling-report/api/v1.0/report/insights/profiles_amount Report Overview Profiles 3
@apiDescription Report Overview Profiles 3
@apiGroup Profiling Report
@apiVersion 1.0.0
@apiName  Report Overview Profiles 3


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse overview_profiles_3

@apiParam(Query:) {String}                                      profile_group                                    Profile group                    

@apisuccess       {Object}                                 overview_profiles_3                                   
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  overview_profiles_3, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

# ************************* Báo cáo Thành viên theo hạng *********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} profiling-report/api/v1.0/report/chart?key_chart=chart_total_member_by_card_id Report Members by rank
@apiGroup Profiling Report
@apiVersion 1.0.0
@apiName  Report Members by rank


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse chart_total_member_by_card_id

@apiParam(Query:) {String}                                      key_chart                                         key báo cáo: chart_total_member_by_card_id
@apiParam(Query:) {String}                                      card_ids                                          danh sách key card id, VD 09cc0dd7-73f7-41c6-8441-ee667b14619b, b995bfb5-4460-4fd3-9e4f-1c31822cd636

@apisuccess       {ArrayObject}                                 data                                              Mảng gồm tổng số profile theo rank
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [chart_total_member_by_card_id_object]
  "lang": "vi", 
  "message": "request thành công."
}
"""

# ************************* Báo cáo Thành viên khu vực *********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} profiling-report/api/v1.0/report/chart?key_chart=chart_total_member_by_region Report Regional members
@apiGroup Profiling Report
@apiVersion 1.0.0
@apiName  Report Regional members


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse chart_total_member_by_region

@apiParam(Query:) {String}                                      key_chart                                         key báo cáo: chart_total_member_by_region

@apisuccess       {ArrayObject}                                 data                                              Mảng gồm tổng số profile theo nguồn
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [chart_total_member_by_region_object]
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ************************* Báo cáo Theo độ tuổi *********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} profiling-report/api/v1.0/report/age Report Ages
@apiGroup Profiling Report
@apiVersion 1.0.0
@apiName  Report Ages


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse chart_ages

@apisuccess       {ArrayObject}                                 data                                              Mảng gồm tổng số profile theo nguồn
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [chart_ages_object]
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ************************* Báo cáo Thành viên khu vực *********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} profiling-report/api/v1.0/report/chart?key_chart=chart_total_profile_interest_category Report Profiles by interest groups
@apiGroup Profiling Report
@apiVersion 1.0.0
@apiName  Report Profiles by interest groups


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse chart_total_profile_interest_category

@apiParam(Query:) {String}                                      key_chart                                         key báo cáo: chart_total_profile_interest_category

@apisuccess       {ArrayObject}                                 data                                              Mảng gồm tổng số profile theo nguồn
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [chart_total_profile_interest_category_object]
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ************************* Báo cáo Profiles mua hàng *********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} profiling-report/api/v1.0/report/chart?key_chart=chart_sapporo_profile_transaction Report Purchase profiles
@apiGroup Profiling Report
@apiVersion 1.0.0
@apiName  Report Purchase profiles


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse chart_sapporo_profile_transaction

@apiParam(Query:) {String}                                      key_chart                                         key báo cáo: chart_sapporo_profile_transaction
@apiParam(Query:) {String}                                      start_time                                       Thời gian bắt đầu dạng "%Y-%m-%dT%H:%M:%S.%fZ" 
@apiParam(Query:) {String}                                      end_time                                        Thời gian kết thúc dạng "%Y-%m-%dT%H:%M:%S.%fZ"

@apisuccess       {ArrayObject}                                 data                                              Mảng gồm tổng số profile theo nguồn
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [chart_sapporo_profile_transaction_object]
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ************************* Báo cáo phân hạng khách hàng *********************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {post} profiling-report/api/v1.0/report/gender_customer_classification Report Customer Classification
@apiGroup Profiling Report
@apiVersion 1.0.0
@apiName  Report Customer Classification

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse customer_classification

@apiParam(Body:) {String}                                           aggregation                                        Loại phân hạng, VD gender, customer_classification
@apiParam(Body:) {ArrayObject}                                      profile_filter                                     filter
@apiParam(Body:) {string}                                           profile_filter.operator_key                        key "operator_key"
@apiParam(Body:) {string}                                           profile_filter.criteria_key                        key "criteria_key"
@apiParam(Body:) {Array}                                            profile_filter.values                              mảng dữ liệu profile owner



@apiParamExample {json} Body example
{
    "aggregation": "gender",
    "profile_filter": [
        {
            "operator_key": "op_is_in",
            "criteria_key": "cri_profile_owner",
            "values": [
                "070a6046-1283-4ba9-b555-c4777adda42a",
                "070e582c-8615-4cbb-9dde-662f5f81771d",
            ]
        }
    ]
}


@apisuccess       {ArrayObject}                                 data                                              Mảng gồm tổng số profile theo hạng khách hàng
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [customer_classification_object]
  "lang": "vi", 
  "message": "request thành công."
}
"""

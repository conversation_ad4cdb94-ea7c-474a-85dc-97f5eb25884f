# Define
"""
@apiDefine common_request_license
@apiParam   (body:)       {String}             start_time     Format "%Y-%m-%dT%H:%M:%S"                     
@apiParam   (body:)       {String}             end_time       Format "%Y-%m-%dT%H:%M:%S"    

"""

# Count MTP
"""
@apiDefine count_mtp_response
@apisuccess       {Object}                                      data                                              Data
@apisuccess       {Integer}                                     data.total                                        Tổng số lượng MTP
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "total": 20
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

# Count MTP by day
"""
@apiDefine count_mtp_by_day_response
@apisuccess       {ArrayObject}                                 data                                              Data
@apisuccess       {Integer}                                     data.total                                        Tổng số lượng MTP
@apisuccess       {String}                                      data.day                                          Ngày đếm MTP
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {Integer}                                     total                                             Tổng các ngày
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [
    {
      "day": "2023-02-26""
      "total": 20
    }
  ], 
  "total": 20
  "lang": "vi", 
  "message": "request thành công."
}
"""

# Count message send
"""
@apiDefine count_message_send_response
@apisuccess       {Object}                                      data                                              Data
@apisuccess       {Integer}                                     data.total                                        Tổng số lượng MTP
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "total": 20
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

# Count message send by day
"""
@apiDefine count_message_send_by_day_response
@apisuccess       {ArrayObject}                                 data                                              Data
@apisuccess       {Integer}                                     data.total                                        Tổng số lượng MTP
@apisuccess       {String}                                      data.day                                          Ngày đếm MTP
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {Integer}                                     total                                             Tống các ngày
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [
    {
      "day": "2023-02-26""
      "total": 20
    }
  ],
  "total": 20
  "lang": "vi", 
  "message": "request thành công."
}
"""

# ********************************* Báo cáo count MTP *************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {post} profiling-report/api/v1.0/report/count_mtp Report Count MTP
@apiDescription Report Count MTP
@apiGroup License Report
@apiVersion 1.0.0
@apiName  Report Count MTP


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse common_request_license
@apiUse count_mtp_response
"""

# ********************************* Báo cáo count MTP by day *************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {post} profiling-report/api/v1.0/report/count_mtp_by_day Report Count MTP By day
@apiDescription Report Count MTP By day
@apiGroup License Report
@apiVersion 1.0.0
@apiName  Report Count MTP By day


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse common_request_license
@apiUse count_mtp_by_day_response
"""

# ********************************* Báo cáo count message send *************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {post} profiling-report/api/v1.0/report/count_message_send Report Count Message Send
@apiDescription Report Message Send
@apiGroup License Report
@apiVersion 1.0.0
@apiName  Report Message Send


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse common_request_license
@apiUse count_message_send_response
"""

# ********************************* Báo cáo count message send by day *************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {post} profiling-report/api/v1.0/report/count_message_send_by_day Report Count Message Send By Day
@apiDescription Report Count Message Send By Day
@apiGroup License Report
@apiVersion 1.0.0
@apiName  Report Count Message Send By Day


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse common_request_license
@apiUse count_message_send_by_day_response
"""

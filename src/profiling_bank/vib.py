#!/usr/bin/python
# -*- coding: utf8 -*-

*************************************  Danh sách bank products  ************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/get_products Danh sách sản phẩm
@apiDescription Danh sách sản phẩm của Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankGetProduct

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam          (Query:)                     {String}        [search]                                                             Chuỗi tìm kiếm

@apiSuccess {Array}   data    Danh sách sản phẩm
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String}     _id		                Mã sản phẩm
@apiSuccess (data) {String} 	name				    Tên sản phẩm
@apiSuccess (data) {String} 	description				Mô tả sản phẩm

@apiSuccessExample {json} Response list card type example
{
  "data": [
    {
      "_id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000c",
      "created_time": "Tue, 24 Mar 2020 11:54:22 GMT",
      "name": "IDC",
      "status": 1,
      "updated_time": "Tue, 24 Mar 2020 11:54:22 GMT"
    }
  ],
  "code": 200,
  "message": "request thành công"
}
"""
*************************************  Danh sách bank instalment plan  *************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/get_installment_plans Danh sách chương trình trả góp
@apiDescription Danh sách chương trình trả góp của Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankGetInstalmentPlan

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    [search]       Từ khóa tìm kiếm
@apiParam   (Query:)    {Number}    [per_page]     Số bản ghỉ trên 1 trang. Mặc định không truyền là lấy tất cả.
@apiParam   (Query:)    {String}    [after]         Param gửi lên để lấy trang kế tiếp

@apiSuccess {Array}   data    Danh sách mcc_id
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
    "code": 200,
    "data": [
        {
            "installment_plan_id": "123",
            "installment_plan_name": "installment_plan_name 1"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "NjFkYmIxY2E5YTEzNmM5MmEzYmRhOTkxXzIwMjItMDEtMTAgMDQ6MTA6NTAuMzk2MDAw",
            "before": "NjFkYmIxY2E5YTEzNmM5MmEzYmRhOTkxXzIwMjItMDEtMTAgMDQ6MTA6NTAuMzk2MDAw"
        },
        "per_page": 1,
        "total_count": 2
    }
}
"""
***************************  Danh sách bank average quarter spending product  **********************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/average_quarter_spending Danh sách mức chi tiêu trung bình quý của từng sản phẩm
@apiDescription Danh sách mức chi tiêu trung bình của từng sản phẩm
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName AverageQuarterSpendingProduct

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    [search]       Từ khóa tìm kiếm
@apiParam   (Query:)    {Number}    [per_page]     Số bản ghỉ trên 1 trang. Mặc định không truyền là lấy tất cả.
@apiParam   (Query:)    {String}    [after]         Param gửi lên để lấy trang kế tiếp
@apiParam   (Query:)    {String}    from_quarter    Từ quý VD: Q32021
@apiParam   (Query:)    {String}    to_quarter      Đến quý VD: Q12022

@apiSuccess {Array}   data    Danh sách mức chi tiêu trung bình quý của từng sản phẩm
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
    "code": 200,
    "data": [
        {
            "card_name": "MC VIB HAPPYyyyyy",
            "card_type_id": "e4528b0b-4ce5-45bc-8279-1a4078079973",
            "quarter": {
                "Q12022": 123
            }
        },
        {
            "card_name": "MC VIB HAPPY DRIVE",
            "card_type_id": "e4528b0b-4ce5-45bc-8279-1a4078079971",
            "quarter": {
                "Q12022": 5
            }
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": null,
            "before": "NjFlOTMwMGZjOGM3NzI4NGQzNmFmNDYzXzIwMjItMDEtMjAgMDk6NDk6NTMuMzg2MDAw"
        },
        "per_page": -1,
        "total_count": 2
    }
}
"""
*************************************  Danh sách bank instalment plan by ids  **********************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/get_installment_plan_by_ids Lấy chi tiết danh sách chương trình trả góp bằng ids
@apiDescription Lấy chi tiết chương trình trả góp bằng danh sách id của Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankGetInstalmentPlanByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    [ids]       Danh sách Id cách nhau bởi đấu , VD: 123,456

@apiSuccess {Array}   data    Danh sách merchant_id
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
    "code": 200,
    "data": [
        {
            "installment_plan_id": "123",
            "installment_plan_name": "installment_plan_name 1"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
*************************************  Danh sách bank mcc  *****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/get_list_mcc Danh sách MCC
@apiDescription Danh sách MCC Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankGetMCC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam          (Query:)                     {String}        [search]                                                             Chuỗi tìm kiếm

@apiSuccess {Array}   data    Danh sách MCC
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String}     _id		                Mã MCC
@apiSuccess (data) {String} 	name				    Tên MCC

@apiSuccessExample {json} Response example
{
  "data": [
    {
      "_id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000c",
      "created_time": "Tue, 24 Mar 2020 11:54:22 GMT",
      "name": "MCC 1",
      "status": 1,
      "updated_time": "Tue, 24 Mar 2020 11:54:22 GMT"
    }
  ],
  "code": 200,
  "message": "request thành công"
}
"""

*************************************  Danh sách bank mcc_ids  *************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/get_mcc_ids Danh sách mcc_id
@apiDescription Danh sách mcc_id của Bank, mã ngành hàng
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankGetMCCID

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    [search]       Từ khóa tìm kiếm
@apiParam   (Query:)    {Number}    [per_page]     Số bản ghỉ trên 1 trang. Mặc định không truyền là lấy tất cả.
@apiParam   (Query:)    {String}    [after]         Param gửi lên để lấy trang kế tiếp

@apiSuccess {Array}   data    Danh sách mcc_id
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
  "data": [
    "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000c", 
    "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000d"
  ],
  "code": 200,
  "message": "request thành công"
}
"""

*************************************  Danh sách bank mcc_by_ids  **********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/bank/get_mcc_details Lấy chi tiết mcc bằng danh sách mcc_id
@apiDescription Lấy chi tiết MCC bằng danh sách mcc_id của Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankGetMCCDetailByIDs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body) {String} mcc_ids Danh sách mcc_id

@apiParamExample {Json}  Json example:
{
    "mcc_ids" : [
        "5f7e5854-1fe9-4bdf-8b34-278389636d7c", 
        "5f7e5854-1fe9-4bdf-8b34-278389636d7d"
    ]
}

@apiSuccess {Array}   data    Danh sách merchant_id
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
  "data": [
    {
      "id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000c",
      "name": "MCC 1",
      "code": "123"
    },
    {
      "id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000d",
      "name": "MCC 2",
      "code": "234"
    }
  ],
  "code": 200,
  "message": "request thành công"
}
"""

*************************************  Danh sách bank merchants  ***********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/get_merchants Danh sách merchant
@apiDescription Danh sách merchant của Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankGetMerchant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam          (Query:)                     {String}        [search]                                                             Chuỗi tìm kiếm

@apiSuccess {Array}   data    Danh sách merchant
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String}     _id		                Mã merchant bank
@apiSuccess (data) {String} 	name				    Tên merchant

@apiSuccessExample {json} Response example
{
  "data": [
    {
      "_id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000c",
      "created_time": "Tue, 24 Mar 2020 11:54:22 GMT",
      "name": "Merchant 1",
      "status": 1,
      "updated_time": "Tue, 24 Mar 2020 11:54:22 GMT"
    }
  ],
  "code": 200,
  "message": "request thành công"
}
"""

*************************************  Danh sách bank merchant_ids  ********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/get_merchant_ids Danh sách merchant_id
@apiDescription Danh sách merchant_id của Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankGetMerchantID

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    [search]       Từ khóa tìm kiếm
@apiParam   (Query:)    {Number}    [per_page]     Số bản ghỉ trên 1 trang. Mặc định không truyền là lấy tất cả.
@apiParam   (Query:)    {String}    [after]        Param gửi lên để lấy trang kế tiếp

@apiSuccess {Array}   data    Danh sách merchant_id
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
  "data": [
    "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000c", 
    "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000d"
  ],
  "code": 200,
  "message": "request thành công"
}
"""

*************************************  Danh sách bank merchant_by_ids  *****************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/bank/get_merchant_details Lấy chi tiết merchant bằng danh sách merchant_id
@apiDescription Lấy chi tiết merchant bằng danh sách merchant_id của Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankGetMerchantDetailByIDs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body) {String} merchant_ids Danh sách merchant_id

@apiParamExample {Json}  Json example:
{
    "merchant_ids" : [
        "5f7e5854-1fe9-4bdf-8b34-278389636d7c", 
        "5f7e5854-1fe9-4bdf-8b34-278389636d7d"
    ]
}

@apiSuccess {Array}   data    Danh sách merchant_id
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
  "data": [
    {
      "id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000c",
      "name": "Merchant 1",
      "code": "123"
    },
    {
      "id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000d",
      "name": "Merchant 2",
      "code": "234"
    }
  ],
  "code": 200,
  "message": "request thành công"
}
"""


*************************************  Danh sách bank Activities  ***********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/activities Danh sách Activities
@apiDescription Danh sách setting Activities của Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankGetActivities

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Array}   data    Danh sách Activities
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String}   _id		                Activity id
@apiSuccess (data) {Number} 	activity_type				  Kiểu Activity. 1 tháng, 3 tháng, 6 tháng
@apiSuccess (data) {Dict}     no_spending           Các giá trị trong phạm vi No Spending
@apiSuccess (data) {Dict}     low_spending           Các giá trị trong phạm vi Low Spending
@apiSuccess (data) {Dict}     medium_spending           Các giá trị trong phạm vi Medium Spending
@apiSuccess (data) {Dict}     high_spending           Các giá trị trong phạm vi High Spending

@apiSuccess (spending) {Number}     from         Giá trị bắt đầu của spending
@apiSuccess (spending) {Number}     to           Giá trị kết thúc của spending

@apiSuccessExample {json} Response example
{
  "data": [
    {
      "_id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000c",
      "created_time": "Tue, 24 Mar 2020 11:54:22 GMT",
      "updated_time": "Tue, 24 Mar 2020 11:54:22 GMT",
      "activity_type": 3,
      "no_spending": {
        "from": 0,
        "to": 99999
      },
      "low_spending": {
        "from": 100000,
        "to": 9999999
      },
      "medium_spending": {
        "from": ********,
        "to": ********
      },
      "high_spending": {
        "from": ********,
        "to": **************
      }
    }
  ],
  "code": 200,
  "message": "request thành công"
}
"""

*****************************************  Tạo bank Activity  ***************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/bank/activity Tạo bank Activity
@apiDescription Tạo setting Activities của Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankCreateActivity

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (data) {Number}   activity_type         Kiểu Activity. 1 tháng, 3 tháng, 6 tháng
@apiParam (data) {Dict}     no_spending           Các giá trị trong phạm vi No Spending
@apiParam (data) {Dict}     low_spending           Các giá trị trong phạm vi Low Spending
@apiParam (data) {Dict}     medium_spending           Các giá trị trong phạm vi Medium Spending
@apiParam (data) {Dict}     high_spending           Các giá trị trong phạm vi High Spending
@apiParam (spending) {Number}     from         Giá trị bắt đầu của spending
@apiParam (spending) {Number}     to           Giá trị kết thúc của spending

@apiSuccess (data) {String}   _id                   Activity id
@apiSuccess (data) {Number}   activity_type         Kiểu Activity. 1 tháng, 3 tháng, 6 tháng
@apiSuccess (data) {Dict}     no_spending           Các giá trị trong phạm vi No Spending
@apiSuccess (data) {Dict}     low_spending           Các giá trị trong phạm vi Low Spending
@apiSuccess (data) {Dict}     medium_spending           Các giá trị trong phạm vi Medium Spending
@apiSuccess (data) {Dict}     high_spending           Các giá trị trong phạm vi High Spending

@apiSuccess (spending) {Number}     from         Giá trị bắt đầu của spending
@apiSuccess (spending) {Number}     to           Giá trị kết thúc của spending

@apiSuccessExample {json} Response example
{
  "data":{
    "_id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000c",
    "created_time": "Tue, 24 Mar 2020 11:54:22 GMT",
    "updated_time": "Tue, 24 Mar 2020 11:54:22 GMT",
    "activity_type": 3,
    "no_spending": {
      "from": 0,
      "to": 99999
    },
    "low_spending": {
      "from": 100000,
      "to": 9999999
    },
    "medium_spending": {
      "from": ********,
      "to": ********
    },
    "high_spending": {
      "from": ********,
      "to": **************
    }
  },
  "code": 200,
  "message": "request thành công"
}
"""

*****************************************  Cập nhật bank Activity  ***************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {put} [HOST]/profiling/v3.0/bank/activity Cập nhật bank Activity
@apiDescription Cập nhật setting Activities của Bank 
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankUpdateActivity

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (data) {Number}   activity_type         Kiểu Activity. 1 tháng, 3 tháng, 6 tháng
@apiParam (data) {Dict}     no_spending           Các giá trị trong phạm vi No Spending
@apiParam (data) {Dict}     low_spending           Các giá trị trong phạm vi Low Spending
@apiParam (data) {Dict}     medium_spending           Các giá trị trong phạm vi Medium Spending
@apiParam (data) {Dict}     high_spending           Các giá trị trong phạm vi High Spending
@apiParam (spending) {Number}     from         Giá trị bắt đầu của spending
@apiParam (spending) {Number}     to           Giá trị kết thúc của spending

@apiSuccess (data) {String}   _id                   Activity id
@apiSuccess (data) {Number}   activity_type         Kiểu Activity. 1 tháng, 3 tháng, 6 tháng
@apiSuccess (data) {Dict}     no_spending           Các giá trị trong phạm vi No Spending
@apiSuccess (data) {Dict}     low_spending           Các giá trị trong phạm vi Low Spending
@apiSuccess (data) {Dict}     medium_spending           Các giá trị trong phạm vi Medium Spending
@apiSuccess (data) {Dict}     high_spending           Các giá trị trong phạm vi High Spending

@apiSuccess (spending) {Number}     from         Giá trị bắt đầu của spending
@apiSuccess (spending) {Number}     to           Giá trị kết thúc của spending


@apiParamExample  {json} Body request example
{
    "data": [
        {
            "activity_type": 3,
            "created_time": "2021-07-12T09:53:03.490000Z",
            "high_spending": {
                "from": 3,
                "to": 985
            },
            "low_spending": {
                "from": 1,
                "to": 1
            },
            "medium_spending": {
                "from": 2,
                "to": 2
            },
            "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
            "no_spending": {
                "from": 0,
                "to": 0
            },
            "updated_time": "2021-07-12T09:53:03.490000Z"
        },
        {
            "_id": "60ed0a48b71a2e0a02a71c22",
            "activity_type": 6,
            "created_time": "2021-07-12T09:53:03.490000Z",
            "high_spending": {
                "from": 3,
                "to": 9898989
            },
            "low_spending": {
                "from": 1,
                "to": 1
            },
            "medium_spending": {
                "from": 2,
                "to": 2
            },
            "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
            "no_spending": {
                "from": 0,
                "to": 0
            },
            "updated_time": "2021-07-12T09:53:03.490000Z"
        }
    ]
}
@apiSuccessExample {json} Response example
{
    "code": 200,
    "data": [
        {
            "_id": "60ed0b1bb71a2e0a02a71c28",
            "activity_type": 3,
            "high_spending": {
                "from": 3,
                "to": 123
            },
            "low_spending": {
                "from": 1,
                "to": 1
            },
            "medium_spending": {
                "from": 2,
                "to": 2
            },
            "no_spending": {
                "from": 0,
                "to": 0
            }
        },
        {
            "_id": "60eeadcfb71a2e71a7e28dbe",
            "activity_type": 1,
            "high_spending": {
                "from": 3,
                "to": 54
            },
            "low_spending": {
                "from": 1,
                "to": 1
            },
            "medium_spending": {
                "from": 2,
                "to": 2
            },
            "no_spending": {
                "from": 0,
                "to": 0
            }
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "success": true
}
"""


*************************************  Danh sách trường gốc  ***************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/get_field_origins Danh sách trường gốc
@apiDescription Danh sách trường gốc của Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiName BankGetFieldOrigin

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Array}   data    Danh sách trường gốc
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	name				    Tên trường gốc
@apiSuccess (data) {String} 	key				        Khóa trường gốc: Unique
@apiSuccess (data) {String} 	value				    Giá trị trường gốc <code>number</code>;<code>string</code>

@apiSuccessExample {json} Response example
{
  "data": [
        {
            "name": "Tổng chi tiêu tích lũy",
            "key": "tong_chi_tieu_tich_luy",
            "status": 1,
            "value": "number",
            "filter": [
                {
                  "criteria_key": "cri_bank_card_type",
                  "operator_key": "op_is_in",
                  //"values": [1] or [2, ["id_the_1", "id_the_2"]]
                },
                {
                  "criteria_key": "cri_bank_behavior_time",
                  "operator_key": "op_is_between",
                  //"values": [1] or [2, ["start_time", "end_time"]] or [3, ["day", 30]] or [3, ["month", 5]] or [4, "end_time"] or [5, ["day", 2019, 5]] or [5, ["month", 2019, 5]] or [6, [2019, "end_time"]]
                },
                {
                  "criteria_key": "cri_bank_behavior_channel",
                  "operator_key": "op_is_in",
                  //"values": [2, ["POS-SALE", "ECOM", "CASH", "IB"]],
                  "data_displays": [
                    {
                        "name": "Máy POS",
                        "id": "POS-SALE"
                    },
                    {
                        "name": "E-commerce",
                        "id": "ECOM"
                    },
                    {
                        "name": "CASH",
                        "id": "CASH"
                    },
                    {
                        "name": "IB",
                        "id": "IB"
                    }
                  ]
                },
                {
                  "criteria_key": "cri_bank_behavior_area",
                  "operator_key": "op_is_in",
                  //"values": [2, ["OVERSEA", "DOMESTIC"]]
                  "data_displays": [
                    {
                        "name": "Ngoài nước",
                        "id": "OVERSEA"
                    },
                    {
                        "name": "Trong nước",
                        "id": "DOMESTIC"
                    }
                  ]
                },
                {
                  "criteria_key": "cri_bank_behavior_day_of_week",
                  "operator_key": "op_is_in",
                  //"values": [2, ["MONDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY","SUNDAY"]]
                },
                {
                  "criteria_key": "cri_bank_behavior_mcc",
                  "operator_key": "op_is_in",
                  //"values": [2, ["id_1", "id_2"]]
                },
                {
                  "criteria_key": "cri_bank_behavior_merchant",
                  "operator_key": "op_is_in",
                  //"values": [2, ["id_1", "id_2"]]
                }
            ]
        },
        {
            "name": "Hạn mức thẻ",
            "key": "han_muc_the",
            "value": "number",
            "status": 1,
            "filter": [
                {
                  "criteria_key": "cri_bank_card_type",
                  "operator_key": "op_is_equal",
                  //"values": ["dac00637-b1f7-4285-9b3f-1075d3058982"]
                }
            ]
        },
        {
            "name": "Loại thẻ",
            "key": "loai_the",
            "value": "string",
            "status": 1,
            "filter": [
                {
                  "criteria_key": "cri_bank_card_type",
                  "operator_key": "op_is_equal",
                  //"values": ["dac00637-b1f7-4285-9b3f-1075d3058982"]
                }
            ]
        },
        {
            "name": "Số thẻ",
            "key": "so_the",
            "value": "string",
            "status": 1,
            "filter": [
                {
                  "criteria_key": "cri_bank_card_type",
                  "operator_key": "op_is_equal",
                  //"values": ["dac00637-b1f7-4285-9b3f-1075d3058982"]
                }
            ]
        },
        {
            "name": "Số tài khoản",
            "key": "so_tai_khoan",
            "value": "string",
            "status": 1,
            "filter": [
                {
                  "criteria_key": "cri_bank_card_type",
                  "operator_key": "op_is_equal",
                  //"values": ["dac00637-b1f7-4285-9b3f-1075d3058982"]
                }
            ]
        },
        {
            "name": "Trạng thái thẻ",
            "key": "trang_thai_the",
            "value": "string",
            "status": 1,
            "filter": [
                {
                  "criteria_key": "cri_bank_card_type",
                  "operator_key": "op_is_equal",
                  //"values": ["dac00637-b1f7-4285-9b3f-1075d3058982"]
                }
            ]
        },
        {
            "name": "Dư nợ thẻ",
            "key": "du_no_the",
            "value": "number",
            "status": 1,
            "filter": [
                {
                  "criteria_key": "cri_bank_card_type",
                  "operator_key": "op_is_equal",
                  //"values": ["dac00637-b1f7-4285-9b3f-1075d3058982"]
                }
            ]
        }
  ],
  "code": 200,
  "message": "request thành công"
}
"""
*****************************************  Xóa bank Activity  ***************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {delete} [HOST]/profiling/v3.0/bank/activity/<activity_id> Xóa bank Activity
@apiDescription Xóa setting Activities của Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankDeleteActivity

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiSuccessExample {json} Response example
{
  "success": true,
  "code": 200,
  "message": "request thành công"
}
"""

************************************* Import Offer ************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/bank/upload/offer Bank Import Offer.
@apiDescription Bank import danh sách khách hàng nhận offer 
@apiGroup BANK
@apiVersion 1.0.0
@apiName  ImportOffer
@apiHeader (Headers:) {String} Content-Type <code>application/form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)    {File}         file                    File Excel cần import.
@apiParam   (Body:)    {Array}        [tags]                  Danh sách ids các tags sẽ gán vào profile.
@apiParam   (Body:)    {String}       master_campaign_id      master campaign id.

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "success": true,
  "message": "request thành công."
}
"""

*************************************  Danh sách channel  ******************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/channels Danh sách kênh
@apiDescription Danh sách kênh Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiName BankGetChannel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Array}   data    Danh sách channel
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer} code    Mã phản hồi

@apiSuccess (data) {String} 	name				    Tên kênh
@apiSuccess (data) {String} 	key				        Mã kênh: Unique

@apiSuccessExample {json} Response example
{
    "code": 200,
    "data": [
        {
            "_id": "5eb25ce9fb403a80e2a3099d",
            "created_time": "2020-05-06T05:14:46.000Z",
            "key": "CASH",
            "name": "CASH",
            "status": 1,
            "updated_time": "2020-05-06T05:14:46.000Z"
        },
        {
            "_id": "5eb25cd6fb403a80e2a30294",
            "created_time": "2020-05-06T05:14:46.000Z",
            "key": "ECOM",
            "name": "E commerce",
            "status": 1,
            "updated_time": "2020-05-06T05:14:46.000Z"
        },
        {
            "_id": "5eb25d02fb403a80e2a3128b",
            "created_time": "2020-05-06T05:14:46.000Z",
            "key": "IB",
            "name": "IB",
            "status": 1,
            "updated_time": "2020-05-06T05:14:46.000Z"
        },
        {
            "_id": "5eb25c6afb403a80e2a2dbd7",
            "created_time": "2020-05-06T05:14:46.000Z",
            "key": "POS-SALE",
            "name": "Máy POS",
            "status": 1,
            "updated_time": "2020-05-06T05:14:46.000Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

*************************************  Danh sách sms syntax  ***************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/sms_syntax Danh sách nội dung tin nhắn đăng kí tham gia campaign
@apiDescription Danh sách nội dung tin nhắn đăng kí tham gia campaign
@apiGroup BANK
@apiVersion 1.0.0
@apiName BankGetSMSSyntax

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    [search]       Từ khóa tìm kiếm
@apiParam   (Query:)    {Number}    [per_page]     Số bản ghỉ trên 1 trang. Mặc định 15.
@apiParam   (Query:)    {String}    [after]       Param gửi lên để lấy trang kế tiếp

@apiSuccess {Array}   data    Danh sách nội dung tin nhắn
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer} code    Mã phản hồi

@apiSuccess (data) {String} 	content	Nội dung tin nhắn

@apiSuccessExample {json} Response example
{
    "code": 200,
    "data": [
        {
            "content": "DK 001",
        },
        {
            "content": "DK 002",
        }
    ],
    "paging ": {
        "per_page": 15,
        "total_count": 40,
        "cursors": {
            'after': 'MTIzMw==',
            'before': n
        }
    },
    "message": "Request thành công"
}
"""

******************************** Upsert sms syntax ************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/bank/upsert_sms_syntax Upsert SMS Syntax.
@apiDescription Bank Upsert SMS Syntax
@apiGroup BANK
@apiVersion 1.0.0
@apiName  BankUpsertSMSSyntax
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)    {Array}       content      Nội dung sms.

@apiParamExample {json} Body request example
{
  "content": ["DK 001", "DK 002"]
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

******************************** get sms by contents **********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/bank/get_sms_by_contents Get SMS by contents.
@apiDescription Bank Get sms syntax by contents
@apiGroup BANK
@apiVersion 1.0.0
@apiName  BankGetSMSByContents
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)    {Array}       content      Nội dung sms.

@apiParamExample {json} Body request example
{
  "content": ["DK 001", "DK 002"]
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "data": [
    {
        "content": "DK 001"
    }
  ],
  "message": "request thành công."
}
"""


*************************************  Danh sách product term  *************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/get_product_terms Danh sách kì hạn 
@apiDescription Danh sách kì hạn
@apiGroup BANK
@apiVersion 1.0.0
@apiName BankGetProductTerms

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Array}   data    Danh sách kì hạn
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer} code    Mã phản hồi

@apiSuccess (data) {String} 	name				    Tên kì hạn
@apiSuccess (data) {String} 	key				        Mã kì hạn: Unique

@apiSuccessExample {json} Response example
{
    "code": 200,
    "data": [
        {
            "name": "Không kì hạn",
            "key": "None"
        },
        {
            "name": "7D",
            "key": "7D"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


**********************************Create Export Report ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/bank/export/report/create Bank create report template.
@apiDescription Bank tạo template báo cáo.
@apiGroup BANK
@apiVersion 1.0.0
@apiName  BankCreatetReportTemplate
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (body:)    {Bool}       is_draft                  Lưu nháp.
@apiParam   (body:)    {Object}     data                  Data.


@apiParam   (data:)    {Object}       common                  Thông tin chung.
@apiParam   (data:)    {Object}       time                    Các mốc thời gian báo cáo.
@apiParam   (data:)    {Object}       index                   Chỉ số báo cáo.


@apiParam   (common:)    {String}       name                  Tên báo cáo.
@apiParam   (common:)    {String=spending,card}       type                  Kiểu báo cáo.
@apiParam   (common:)    {String}       master_campaign_id                  Master Campaign Id.

@apiParam   (time:)    {Array}       [before]                  Danh sách các khoảng thời gian báo cáo trước khi chạy chiến dịch. Required với type==spending.
@apiParam   (time:)    {Array}       within                    Danh sách các khoảng thời gian báo cáo sau khi chạy chiến dịch.

@apiParam   (index spending:)    {Object}       register                Số Profiles đã nhắn tin đăng ký tham gia nhận ưu đãi.
@apiParam   (index spending:)    {Object}       spending_vnd             Tổng số tiền đã chi tiêu của tập Profiles đã nhắn tin đăng ký.
@apiParam   (index spending:)    {Object}       spending_client         Số Profiles đã đăng ký và có chi tiêu.
@apiParam   (index spending:)    {Object}       spending_per_client_vnd Giá trị chi tiêu trên mỗi Profile.
@apiParam   (index spending:)    {Object}       spending_txn            Tổng số giao dịch của tập Profiles đã nhắn tin đăng ký.
@apiParam   (index spending:)    {Object}       spending_per_txn        Giá trị chi tiêu trung bình của mỗi giao dịch.
@apiParam   (index spending:)    {Object}       budget                  Ngân sách.

@apiParam   (index card:)    {Object}       total_base                  Tổng số Profiles mục tiêu của chiến dịch.
@apiParam   (index card:)    {Object}       total_successful            Tổng số Profiles nâng cấp/ mở thêm thẻ thành công.
@apiParam   (index card:)    {Object}       total_successful_have_spend Tổng số Profiles nâng cấp/ mở thêm thẻ thành công và có chi tiêu theo quy định.
@apiParam   (index card:)    {Object}       total_successful_qualifier     Tổng số Profiles nâng cấp/ mở thêm thẻ thành công và nhận được ưu đãi.
@apiParam   (index card:)    {Object}       total_spend_in_all_group    Tổng chi tiêu của tập Profiles nâng cấp/ mở thẻ thành công và có chi tiêu theo quy định.
@apiParam   (index card:)    {Object}       total_spend_in_qualifiers_group   Tổng chi tiêu của Profiles nâng cấp/ mở thêm thẻ thành công và nhận được ưu đãi.
@apiParam   (index card:)    {Object}       spend_qualifiers             Chi tiêu trung bình của mỗi Profiles nâng cấp/ mở thêm thẻ thành công và nhận được ưu đãi.

# SPENDING DATA
@apiParam   (register:)    {Int}       [target]                  Mục tiêu.
@apiParam   (register:)    {Array}        sms_syntax              Danh sách các syntax mà khách hàng nhắn tin tới tổng đài.

@apiParam   (spending_vnd:)    {Int}       [target]                  Mục tiêu.
@apiParam   (spending_vnd:)    {Object}    sub_index            Chỉ số con.
@apiParam   (spending_vnd:)    {Object}    filters              Điều kiện xác định giá trị.

@apiParam   (sub_index:)    {String=channel,bank_card,domestic,day_in_week,mcc,merchant,spending_range}      type      Kiểu filter xác định chỉ số con.
@apiParam   (sub_index:)    {Array}      values                Giá trị xác định chỉ số con.

@apiParam   (filters spending_vnd:)    {Array}      [bank_card]                Danh sách loại thẻ.
@apiParam   (filters spending_vnd:)    {Array}      [primary_card]            Thẻ chính, thẻ phụ.
@apiParam   (filters spending_vnd:)    {Array}      [channel]                 Danh sách kênh chi tiêu.
@apiParam   (filters spending_vnd:)    {Array}      [domestic]                Danh sách khu vực chi tiêu.
@apiParam   (filters spending_vnd:)    {Array}      [day_in_week]             Danh sách các ngày chi tiêu trong tuần.
@apiParam   (filters spending_vnd:)    {Array}      [mcc]                     Danh sách các MCC.
@apiParam   (filters spending_vnd:)    {Array}      [merchant]                Danh sách các merchant.

#CARD DATA
@apiParam   (total_base:)    {Int}       [target]                  Mục tiêu.
@apiParam   (total_base:)    {Object}       sub_index            Chỉ số con.

@apiParam   (total_successful:)    {Int}       target                  Mục tiêu.
@apiParam   (total_successful:)    {Object}       sub_index            Chỉ số con.

@apiParam   (total_successful_have_spend:)    {Int}       [target]                  Mục tiêu.
@apiParam   (total_successful_have_spend:)    {Object}       filters                  Điều kiện.


@apiParam   (sub_index total_base:)    {String=bank_card}       type            Chỉ số con.
@apiParam   (sub_index total_base:)    {Array}       values          Danh sách các loại thẻ.

@apiParam   (sub_index total_successful:)    {String=bank_card}       type            Chỉ số con.
@apiParam   (sub_index total_successful:)    {Array}       values          Danh sách các loại thẻ.
@apiParam   (sub_index total_successful:)    {Array}       filter          Danh sách bộ lọc.

@apiParam   (total_successful_qualifier:)    {Int}       [target]            Mục tiêu.
@apiParam   (total_successful_qualifier:)    {Array}     marketing_ids     Danh sách các chiến dịch marketing.


@apiParam   (filter total_successful:)    {String}       target_card          Thẻ mục tiêu.
@apiParam   (filter total_successful:)    {Array}       target_card_status     Trạng thái của thẻ mục tiêu.
@apiParam   (filter total_successful:)    {Array}       origin_card           Danh sác các loại thẻ gốc.
@apiParam   (filter total_successful:)    {Array}       origin_card_status           Danh sác trạng thái của thẻ gốc.

@apiParam   (filters total_successful_have_spend:)    {Array}       [primary_card]           Thẻ chính, thẻ phụ.
@apiParam   (filters total_successful_have_spend:)    {Array}       [channel]                Danh sách kênh chi tiêu.
@apiParam   (filters total_successful_have_spend:)    {Array}       [domestic]               Danh sách khu vực chi tiêu.
@apiParam   (filters total_successful_have_spend:)    {Array}       [day_in_week]            Danh sách các ngày chi tiêu trong tuần.
@apiParam   (filters total_successful_have_spend:)    {Array}      [mcc]                     Danh sách các MCC.
@apiParam   (filters total_successful_have_spend:)    {Array}      [merchant]                Danh sách các merchant.


@apiParamExample [json] Spending example:
{
    "is_draft": true,
    "data": {
      "common": {
      "name": "Report 1",
      "type": "spending|card",
      "master_campaign_id": "10c0d6f6-6955-451a-bc78-621e418c21f0"
      },
      "time": {
        "before": [
          [
            "date_from_1",
            "date_util_1"
          ],
          [
            "date_from_2",
            "date_util_2"
          ]
        ],
        "within": [
          [
            "date_from_1",
            "date_util_1"
          ],
          [
            "date_from_2",
            "date_util_2"
          ]
        ]
      },
      "index": {
        "register": {
          "target": 5000,
          "tags": [
            "tag_id_1",
            "tag_id_2"
          ]
        },
        "spending_vnd": {
          "target": 5000,
          "sub_index": {
            "type": "channel|bank_card|domestic|day_in_week|mcc|merchant|spending_range",
            "values": []
          },
          "filters": {
            "primary_card": [
              "PRIMARY",
              "SUB"
            ],
            "channel": [
              "sale",
              "e-commerce",
              "cash",
              "ebank"
            ],
            "domestic": [
              "DOMESTIC",
              "OVERSEA"
            ],
            "day_in_week": [
              "monday",
              "tuesday",
              "thursday",
              "friday",
              "saturday",
              "sunday"
            ],
            "mcc": [
              "mcc_id1",
              "mcc_id2"
            ],
            "merchant": [
              "merchant_id1",
              "merchant_id2"
            ]
          }
        },
        "spending_client": {
          "target": 5000
        },
        "spending_per_client_vnd": {
          "target": 5000
        },
        "spending_txn": {
          "target": 5000
        },
        "spending_per_txn" : {
           "target": 5000
        },
        "budget": {
          "target": 5000,
          "actual": 5000
        }
      }
    }
  }

@apiParamExample [json] Card example:
{
    "is_draft": false,
    "data": {
      "common": {
      "name": "Report 2",
      "type": "card",
      "master_campaign_id": "10c0d6f6-6955-451a-bc78-621e418c21f0"
      },
      "time": {
        "within": [
          [
            "date_from_1",
            "date_util_1"
          ],
          [
            "date_from_2",
            "date_util_2"
          ]
        ]
      },
      "index": {
        "total_base": {
          "target": 5000,
          "sub_index": {
            "type": "bank_card",
            "values": []
          }
        },
        "total_successful": {
          "target": 5000,
          "sub_index": {
            "type": "bank_card",
            "values": [
              "",
              ""
            ],
            "filters": [
              {
                "target_card": "514b0396-9457-4022-bd08-6c5fb2ee0db2",
                "target_card_status": [
                  "norm",
                  "lost"
                ],
                "origin_card": [
                  "21999c80-09a0-40cd-aa08-7bb4ba5498ba",
                  "78a32cbf-d4d5-414f-b18c-05519fc58079"
                ],
                "origin_card_status": [
                  "closed",
                  "lost"
                ]
              }
            ]
          }
        },
        "total_successful_have_spend": {
          "target": 5000,
          "filters": {
            "primary_card": [
              "PRIMARY",
              "SUB"
            ],
            "channel": [
              "sale",
              "e-commerce",
              "cash",
              "ebank"
            ],
            "domestic": [
              "DOMESTIC",
              "OVERSEA"
            ],
            "day_in_week": [
              "monday",
              "tuesday",
              "thursday",
              "friday",
              "saturday",
              "sunday"
            ],
            "mcc": [
              "mcc_id1",
              "mcc_id2"
            ],
            "merchant": [
              "merchant_id1",
              "merchant_id2"
            ]
          }
        },
        "total_successful_qualifier": {
          "target": 5000,
          "marketing_ids": [
            "1a88c7ea-bf9b-49a8-bb93-d79f977d40af",
            "ea9eb65d-7b2d-4009-a8d1-e088f40940e0"
          ]
        },
        "total_spend_in_all_group": {
          "target": 5000
        },
        "total_spend_in_qualifiers_group": {
          "target": 5000
        },
        "spend_qualifiers": {
          "target": 5000
        }
      }
    }
  }


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "_id" : ObjectId("5ece419e52ac5913d43ee60f"),
    "is_draft" : true,
    "merchant_id" : LUUID("1b99bdcf-d582-4f49-9715-1b61dfff3924"),
    "updated_time" : ISODate("2020-05-28T03:54:59.440Z"),
    "name" : "Report 2.2",
    "data" : {
        "index" : {},
        "time" : {
            "within" : [],
            "before" : []
        },
        "common" : {
            "name" : "Report 2.2",
            "type" : "spending",
            "master_campaign_id" : "10c0d6f6-6955-451a-bc78-621e418c21f0"
        }
    },
    "created_time" : ISODate("2020-05-27T10:31:58.878Z"),
    "master_campaign_id" : "10c0d6f6-6955-451a-bc78-621e418c21f0",
    "report_type" : "spending"
  },
  "lang": "vi", 
  "success": true,
  "message": "request thành công."
}
"""

**********************************Update Export Report ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/v3.0/bank/export/report/update/<report_id> Bank update report template.
@apiDescription Bank cập nhật template báo cáo.
@apiGroup BANK
@apiVersion 1.0.0
@apiName  BankUpdateReportTemplate
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (body:)    {Bool}       is_draft                  Lưu nháp.
@apiParam   (body:)    {Object}     data                  Data.


@apiParam   (data:)    {Object}       common                  Thông tin chung.
@apiParam   (data:)    {Object}       time                    Các mốc thời gian báo cáo.
@apiParam   (data:)    {Object}       index                   Chỉ số báo cáo.


@apiParam   (common:)    {String}       name                  Tên báo cáo.
@apiParam   (common:)    {String=spending,card}       type                  Kiểu báo cáo.
@apiParam   (common:)    {String}       master_campaign_id                  Master Campaign Id.

@apiParam   (time:)    {Array}       [before]                  Danh sách các khoảng thời gian báo cáo trước khi chạy chiến dịch. Required với type==spending.
@apiParam   (time:)    {Array}       within                    Danh sách các khoảng thời gian báo cáo sau khi chạy chiến dịch.

@apiParam   (index spending:)    {Object}       register                Số Profiles đã nhắn tin đăng ký tham gia nhận ưu đãi.
@apiParam   (index spending:)    {Object}       spending_vnd             Tổng số tiền đã chi tiêu của tập Profiles đã nhắn tin đăng ký.
@apiParam   (index spending:)    {Object}       spending_client         Số Profiles đã đăng ký và có chi tiêu.
@apiParam   (index spending:)    {Object}       spending_per_client_vnd Giá trị chi tiêu trên mỗi Profile.
@apiParam   (index spending:)    {Object}       spending_txn            Tổng số giao dịch của tập Profiles đã nhắn tin đăng ký.
@apiParam   (index spending:)    {Object}       spending_per_txn        Giá trị chi tiêu trung bình của mỗi giao dịch.
@apiParam   (index spending:)    {Object}       budget                  Ngân sách.

@apiParam   (index card:)    {Object}       total_base                  Tổng số Profiles mục tiêu của chiến dịch.
@apiParam   (index card:)    {Object}       total_successful            Tổng số Profiles nâng cấp/ mở thêm thẻ thành công.
@apiParam   (index card:)    {Object}       total_successful_have_spend Tổng số Profiles nâng cấp/ mở thêm thẻ thành công và có chi tiêu theo quy định.
@apiParam   (index card:)    {Object}       total_successful_qualifier     Tổng số Profiles nâng cấp/ mở thêm thẻ thành công và nhận được ưu đãi.
@apiParam   (index card:)    {Object}       total_spend_in_all_group    Tổng chi tiêu của tập Profiles nâng cấp/ mở thẻ thành công và có chi tiêu theo quy định.
@apiParam   (index card:)    {Object}       total_spend_in_qualifiers_group   Tổng chi tiêu của Profiles nâng cấp/ mở thêm thẻ thành công và nhận được ưu đãi.
@apiParam   (index card:)    {Object}       spend_qualifiers             Chi tiêu trung bình của mỗi Profiles nâng cấp/ mở thêm thẻ thành công và nhận được ưu đãi.

# SPENDING DATA
@apiParam   (register:)    {Int}       [target]                  Mục tiêu.
@apiParam   (register:)    {Array}        sms_syntax              Danh sách các syntax mà khách hàng nhắn tin tới tổng đài.

@apiParam   (spending_vnd:)    {Int}       [target]                  Mục tiêu.
@apiParam   (spending_vnd:)    {Object}    sub_index            Chỉ số con.
@apiParam   (spending_vnd:)    {Object}    filters              Điều kiện xác định giá trị.

@apiParam   (sub_index:)    {String=channel,bank_card,domestic,day_in_week,mcc,merchant,spending_range}      type      Kiểu filter xác định chỉ số con.
@apiParam   (sub_index:)    {Array}      values                Giá trị xác định chỉ số con.

@apiParam   (filters spending_vnd:)    {Array}      [bank_card]                Danh sách loại thẻ.
@apiParam   (filters spending_vnd:)    {Array}      [primary_card]            Thẻ chính, thẻ phụ.
@apiParam   (filters spending_vnd:)    {Array}      [channel]                 Danh sách kênh chi tiêu.
@apiParam   (filters spending_vnd:)    {Array}      [domestic]                Danh sách khu vực chi tiêu.
@apiParam   (filters spending_vnd:)    {Array}      [day_in_week]             Danh sách các ngày chi tiêu trong tuần.
@apiParam   (filters spending_vnd:)    {Array}      [mcc]                     Danh sách các MCC.
@apiParam   (filters spending_vnd:)    {Array}      [merchant]                Danh sách các merchant.

#CARD DATA
@apiParam   (total_base:)    {Int}       [target]                  Mục tiêu.
@apiParam   (total_base:)    {Object}       sub_index            Chỉ số con.

@apiParam   (total_successful:)    {Int}       target                  Mục tiêu.
@apiParam   (total_successful:)    {Object}       sub_index            Chỉ số con.

@apiParam   (total_successful_have_spend:)    {Int}       [target]                  Mục tiêu.
@apiParam   (total_successful_have_spend:)    {Object}       filters                  Điều kiện.


@apiParam   (sub_index total_base:)    {String=bank_card}       type            Chỉ số con.
@apiParam   (sub_index total_base:)    {Array}       values          Danh sách các loại thẻ.

@apiParam   (sub_index total_successful:)    {String=bank_card}       type            Chỉ số con.
@apiParam   (sub_index total_successful:)    {Array}       values          Danh sách các loại thẻ.
@apiParam   (sub_index total_successful:)    {Array}       filter          Danh sách bộ lọc.

@apiParam   (total_successful_qualifier:)    {Int}       [target]            Mục tiêu.
@apiParam   (total_successful_qualifier:)    {Array}     marketing_ids     Danh sách các chiến dịch marketing.


@apiParam   (filter total_successful:)    {String}       target_card          Thẻ mục tiêu.
@apiParam   (filter total_successful:)    {Array}       target_card_status     Trạng thái của thẻ mục tiêu.
@apiParam   (filter total_successful:)    {Array}       origin_card           Danh sác các loại thẻ gốc.
@apiParam   (filter total_successful:)    {Array}       origin_card_status           Danh sác trạng thái của thẻ gốc.

@apiParam   (filters total_successful_have_spend:)    {Array}       [primary_card]           Thẻ chính, thẻ phụ.
@apiParam   (filters total_successful_have_spend:)    {Array}       [channel]                Danh sách kênh chi tiêu.
@apiParam   (filters total_successful_have_spend:)    {Array}       [domestic]               Danh sách khu vực chi tiêu.
@apiParam   (filters total_successful_have_spend:)    {Array}       [day_in_week]            Danh sách các ngày chi tiêu trong tuần.
@apiParam   (filters total_successful_have_spend:)    {Array}      [mcc]                     Danh sách các MCC.
@apiParam   (filters total_successful_have_spend:)    {Array}      [merchant]                Danh sách các merchant.


@apiParamExample [json] Spending example:
{
    "is_draft": true,
    "data": {
      "common": {
      "name": "Report 1",
      "type": "spending|card",
      "master_campaign_id": "10c0d6f6-6955-451a-bc78-621e418c21f0"
      },
      "time": {
        "before": [
          [
            "date_from_1",
            "date_util_1"
          ],
          [
            "date_from_2",
            "date_util_2"
          ]
        ],
        "within": [
          [
            "date_from_1",
            "date_util_1"
          ],
          [
            "date_from_2",
            "date_util_2"
          ]
        ]
      },
      "index": {
        "register": {
          "target": 5000,
          "tags": [
            "tag_id_1",
            "tag_id_2"
          ]
        },
        "spending_vnd": {
          "target": 5000,
          "sub_index": {
            "type": "channel|bank_card|domestic|day_in_week|mcc|merchant|spending_range",
            "values": []
          },
          "filters": {
            "primary_card": [
              "PRIMARY",
              "SUB"
            ],
            "channel": [
              "sale",
              "e-commerce",
              "cash",
              "ebank"
            ],
            "domestic": [
              "DOMESTIC",
              "OVERSEA"
            ],
            "day_in_week": [
              "monday",
              "tuesday",
              "thursday",
              "friday",
              "saturday",
              "sunday"
            ],
            "mcc": [
              "mcc_id1",
              "mcc_id2"
            ],
            "merchant": [
              "merchant_id1",
              "merchant_id2"
            ]
          }
        },
        "spending_client": {
          "target": 5000
        },
        "spending_per_client_vnd": {
          "target": 5000
        },
        "spending_txn": {
          "target": 5000
        },
        "spending_per_txn" : {
           "target": 5000
        },
        "budget": {
          "target": 5000,
          "actual": 5000
        }
      }
    }
  }

@apiParamExample [json] Card example:
{
    "is_draft": false,
    "data": {
      "common": {
      "name": "Report 2",
      "type": "card",
      "master_campaign_id": "10c0d6f6-6955-451a-bc78-621e418c21f0"
      },
      "time": {
        "within": [
          [
            "date_from_1",
            "date_util_1"
          ],
          [
            "date_from_2",
            "date_util_2"
          ]
        ]
      },
      "index": {
        "total_base": {
          "target": 5000,
          "sub_index": {
            "type": "bank_card",
            "values": []
          }
        },
        "total_successful": {
          "target": 5000,
          "sub_index": {
            "type": "bank_card",
            "values": [
              "",
              ""
            ],
            "filters": [
              {
                "target_card": "514b0396-9457-4022-bd08-6c5fb2ee0db2",
                "target_card_status": [
                  "norm",
                  "lost"
                ],
                "origin_card": [
                  "21999c80-09a0-40cd-aa08-7bb4ba5498ba",
                  "78a32cbf-d4d5-414f-b18c-05519fc58079"
                ],
                "origin_card_status": [
                  "closed",
                  "lost"
                ]
              }
            ]
          }
        },
        "total_successful_have_spend": {
          "target": 5000,
          "filters": {
            "primary_card": [
              "PRIMARY",
              "SUB"
            ],
            "channel": [
              "sale",
              "e-commerce",
              "cash",
              "ebank"
            ],
            "domestic": [
              "DOMESTIC",
              "OVERSEA"
            ],
            "day_in_week": [
              "monday",
              "tuesday",
              "thursday",
              "friday",
              "saturday",
              "sunday"
            ],
            "mcc": [
              "mcc_id1",
              "mcc_id2"
            ],
            "merchant": [
              "merchant_id1",
              "merchant_id2"
            ]
          }
        },
        "total_successful_qualifier": {
          "target": 5000,
          "marketing_ids": [
            "1a88c7ea-bf9b-49a8-bb93-d79f977d40af",
            "ea9eb65d-7b2d-4009-a8d1-e088f40940e0"
          ]
        },
        "total_spend_in_all_group": {
          "target": 5000
        },
        "total_spend_in_qualifiers_group": {
          "target": 5000
        },
        "spend_qualifiers": {
          "target": 5000
        }
      }
    }
  }


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "_id" : ObjectId("5ece419e52ac5913d43ee60f"),
    "is_draft" : true,
    "merchant_id" : LUUID("1b99bdcf-d582-4f49-9715-1b61dfff3924"),
    "updated_time" : ISODate("2020-05-28T03:54:59.440Z"),
    "name" : "Report 2.2",
    "data" : {
        "index" : {},
        "time" : {
            "within" : [],
            "before" : []
        },
        "common" : {
            "name" : "Report 2.2",
            "type" : "spending",
            "master_campaign_id" : "10c0d6f6-6955-451a-bc78-621e418c21f0"
        }
    },
    "created_time" : ISODate("2020-05-27T10:31:58.878Z"),
    "master_campaign_id" : "10c0d6f6-6955-451a-bc78-621e418c21f0",
    "report_type" : "spending"
  },
  "lang": "vi", 
  "success": true,
  "message": "request thành công."
}
"""

*********************************** List Export Report ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/bank/export/report/list Bank get list report template.
@apiDescription Bank lấy danh sách template báo cáo.
@apiGroup BANK
@apiVersion 1.0.0
@apiName  BankGetListReportTemplate
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiParam       (Query:)     {Integer}   is_draft             Lấy loại báo cáo.
@apiParam       (Query:)     {Integer}   [per_page]          SỐ bản ghi trên 1 trang
@apiParam       (Query:)     {String}    [after_token]         Token để lấy dữ liệu trang tiếp theo.
@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp
<br />
<li><code>created_time</code>: Ngày tạo</li>
<li><code>name</code>: Tên báo cáo</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiParam   (body:)    {String}       [search]              chuỗi tìm kiếm.
@apiParam   (body:)    {Array=card,spending}     [type]                  tìm kiếm theo kiểu báo cáo.
@apiParam   (body:)    {Array}     [master_campaign_id]                  danh sách các master campaign.


@apiParamExample [json] example:
{
  "search": "report",
  "master_campaign_id": ["10c0d6f6-6955-451a-bc78-621e418c21f0"],
  "type": ["card", "spending"]
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "_id": "5ece419e52ac5913d43ee60f",
            "created_time": "2020-05-27T10:31:58.000Z",
            "data": {
                "common": {
                    "master_campaign_id": "10c0d6f6-6955-451a-bc78-621e418c21f0",
                    "name": "Report 2.2",
                    "type": "spending"
                },
                "index": {},
                "time": {
                    "before": [],
                    "within": []
                }
            },
            "is_draft": true,
            "master_campaign_id": "10c0d6f6-6955-451a-bc78-621e418c21f0",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "Report 2.2",
            "report_type": "spending",
            "updated_time": "2020-05-28T03:54:59.000Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "NWVjZTQxOWU1MmFjNTkxM2Q0M2VlNjBmXzIwMjAtMDUtMjdUMTA6MzE6NTguMDAwWg==",
            "before": "NWVjZTQxOWU1MmFjNTkxM2Q0M2VlNjBmXzIwMjAtMDUtMjdUMTA6MzE6NTguMDAwWg=="
        },
        "per_page": 1,
        "total_count": 1
    }
}
"""
*********************************** Count Export Report ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/bank/export/report/count Bank count report template.
@apiDescription Bank count danh sách template báo cáo.
@apiGroup BANK
@apiVersion 1.0.0
@apiName  BankCountReportTemplate
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (body:)    {String}                  [search]                 chuỗi tìm kiếm.
@apiParam   (body:)    {Array=card,spending}     [type]                   tìm kiếm theo kiểu báo cáo.
@apiParam   (body:)    {Array}                   [master_campaign_id]     danh sách các master campaign.


@apiParamExample [json] example:
{
    "type": [
        "card",
        "spending"
    ],
    "master_campaign_id": [
        "ff4471ad-dd88-4400-b9af-063de2abb876",
        "f7cded69-a82d-4263-83ab-b2f9b915eb23"
    ],
    "search": "mcc"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "count": 0,
            "is_draft": true
        },
        {
            "count": 3,
            "is_draft": false
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

*********************************** Delete Export Report ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {patch} [HOST]/profiling/v3.0/bank/export/report/delete Bank delete list report template.
@apiDescription Bank xóa danh sách template báo cáo.
@apiGroup BANK
@apiVersion 1.0.0
@apiName  BankDeleteListReportTemplate
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (body:)    {Array}       report_ids              Danh sách các template muốn xóa.


@apiParamExample [json] example:
{
  "report_ids": ["10c0d6f6-6955-451a-bc78-621e418c21f0"]
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": true,
    "data": {
      "deleted": 1
    }
}
"""

********************************** Process Export Report ******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/bank/export/report/process/<report_id> Export Report.
@apiDescription Bank Xuất file báo cáo chiến dịch marketing.
@apiGroup BANK
@apiVersion 1.0.0
@apiName  BankExportReport
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (body:)    {Array}       emails              Danh sách các email muốn nhận báo cáo.


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": true,
}
"""

********************************** Detail Export Report ******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/export/report/detail/<report_id> Detail Report.
@apiDescription Bank Xem chi tiết cấu hình báo cáo.
@apiGroup BANK
@apiVersion 1.0.0
@apiName  BankDetailReport
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (body:)    {Array}       emails              Danh sách các email muốn nhận báo cáo.


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "success": true,
    "data": {
            "_id": "5ece419e52ac5913d43ee60f",
            "created_time": "2020-05-27T10:31:58.000Z",
            "data": {
                "common": {
                    "master_campaign_id": "10c0d6f6-6955-451a-bc78-621e418c21f0",
                    "name": "Report 2.2",
                    "type": "spending"
                },
                "index": {},
                "time": {
                    "before": [],
                    "within": []
                }
            },
            "is_draft": true,
            "master_campaign_id": "10c0d6f6-6955-451a-bc78-621e418c21f0",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "Report 2.2",
            "report_type": "spending",
            "updated_time": "2020-05-28T03:54:59.000Z"
        }
}
"""

*************************************  Bank  Estimate campaign  *************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/mkt/<campaign_id>/estimate Estimate campaign bộ lọc cần phải tính toán
@apiDescription Lấy estimate campaign bộ lọc cần phải tính toán, trường hợp chưa Estimate xong thì không có dữ liệu và sẽ trả về 404
@apiGroup BANK
@apiVersion 1.0.0
@apiName BankGetEstimateCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Object}   data    Dữ liệu estimate của campaign
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer} code    Mã phản hồi

@apiSuccess (data) {String} 	campaign_id				ID của chiến dịch
@apiSuccess (data) {String} 	estimate		        Estimate của chiến dịch

@apiSuccessExample {json} Response example
{
    "code": 200,
    "data": {
        "campaign_id": "043e93d7-9836-4f76-abee-0b004e7b7971",
        "estimate": 99
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

*************************************  Danh sách bank products  ************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/profile/detail_product_holding Chi tiết product holding của profile
@apiDescription Chi tiết product holding của profile
@apiGroup MSB
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName ProfileGetProductHolding

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam          (Param:)                     {String}        profile_id   Id của profile

@apiSuccess {Array}   data    Danh sách sản phẩm
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi


@apiSuccessExample {json} Response list card type example
{
    "code": 200,
    "data": [
        {
            "62a93c67a8772cd90c3b53b7": [
                {
                    "due_date": "2022-06-23T10:07:24.107Z",
                    "estimated_settlement_date": "2022-06-23T10:07:24.107Z",
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 1",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_holding_id": "e33a5aa07fdc88909df1076f83486384",
                    "product_line": "62a93c67a8772cd90c3b53b7",
                    "product_status": "OK",
                    "product_type": [
                        "62b185167fd598001059489f",
                        "62b185167fd598001059489d",
                        "62b185167fd598001059489c"
                    ],
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b52a06c67c3200124c608e",
                    "updated_time": "2022-06-24T03:05:42.432Z"
                },
                {
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 123",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_holding_id": "f435559a6c8d93cb633f7b0d14cda1f6",
                    "product_line": "62a93c67a8772cd90c3b53b7",
                    "product_status": "OK",
                    "product_type": [
                        "62b185167fd598001059489f",
                        "62b185167fd598001059489d",
                        "62b185167fd598001059489c"
                    ],
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b55f873d2aca000f0e8284",
                    "updated_time": "2022-06-24T06:54:00.622Z"
                }
            ],
            "product_line_code": "tien_gui_tiet_kiem_975feecc",
            "product_line_id": "62a93c67a8772cd90c3b53b7",
            "product_line_name": "TIỀN GỬI TIẾT KIỆM",
            "status": "expired",
            "total": {
                "amount_saving_each_passbook": 0,
                "total_savings": 0,
                "total_savings_book": 0
            }
        },
        {
            "62a93c67a8772cd90c3b53b1": [
                {
                    "due_date": "2022-06-23T10:07:24.107Z",
                    "estimated_settlement_date": "2022-06-23T10:07:24.107Z",
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 1",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_holding_id": "8",
                    "product_line": "62a93c67a8772cd90c3b53b1",
                    "product_type": [
                        "62b185167fd598001059489f",
                        "62b185167fd598001059489d",
                        "62b185167fd598001059489c"
                    ],
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b51e420ce781000eb6a477",
                    "updated_time": "2022-06-24T02:15:30.590Z"
                }
            ],
            "product_line_code": "tai_khoan_39fcdcd2",
            "product_line_id": "62a93c67a8772cd90c3b53b1",
            "product_line_name": "TÀI KHOẢN",
            "status": "expired",
            "total": {
                "current_balance_original_currency": 0,
                "current_balance_vnd": 0,
                "current_monthly_average_balance_original_currency": 0,
                "current_monthly_average_balance_vnd": 0,
                "total_current_balance_currency": 0,
                "total_current_balance_vnd": 0,
                "total_current_monthly_average_balance_currency": 0,
                "total_current_monthly_average_balance_vnd": 0,
                "total_product_available": 0
            }
        },
        {
            "62b175b13323cb000eada1c7": [
                {
                    "due_date": "2022-06-23T10:07:24.107Z",
                    "estimated_settlement_date": "2022-06-23T10:07:24.107Z",
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 1",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_code": "status3",
                    "product_holding_id": "99999999999",
                    "product_line": "62b175b13323cb000eada1c7",
                    "product_status": "status3",
                    "product_type": "id1",
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b51e420ce781000eb6a477",
                    "transaction_office": "status3",
                    "updated_time": "2022-06-24T02:15:30.590Z"
                }
            ],
            "product_line_code": "anh",
            "product_line_id": "62b175b13323cb000eada1c7",
            "product_line_name": "Ánh test 21/6",
            "status": "expired",
            "total": {
                "amount_saving_each_passbook": 0,
                "current_balance_original_currency": 0,
                "current_balance_vnd": 0,
                "current_monthly_average_balance_original_currency": 0,
                "current_monthly_average_balance_vnd": 0,
                "total_current_balance_vnd": 0,
                "total_current_monthly_average_balance_currency": 0,
                "total_current_monthly_average_balance_vnd": 0,
                "total_face_value": 0,
                "total_initial_loan_balance": 0,
                "total_loan_balance": 0,
                "total_number_loan": 0,
                "total_product_available": 0,
                "total_savings": 0,
                "total_savings_book": 0
            }
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
*************************************  Detail Product Holding From Other  ************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/profile/detail/product_holding_from_other Chi tiết product holding của profile từ module khác
@apiDescription Chi tiết product holding của profile từ module khác
@apiGroup MSB
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName ProfileGetProductHoldingFromOther

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam          (Param:)                     {String}        profile_id   Id của profile

@apiSuccess {Array}   data    Danh sách sản phẩm
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi


@apiSuccessExample {json} Response list card type example
{
    "code": 200,
    "data": [
        {
            "62a93c67a8772cd90c3b53b7": [
                {
                    "due_date": "2022-06-23T10:07:24.107Z",
                    "estimated_settlement_date": "2022-06-23T10:07:24.107Z",
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 1",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_holding_id": "e33a5aa07fdc88909df1076f83486384",
                    "product_line": "62a93c67a8772cd90c3b53b7",
                    "product_status": "OK",
                    "product_type": [
                        "62b185167fd598001059489f",
                        "62b185167fd598001059489d",
                        "62b185167fd598001059489c"
                    ],
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b52a06c67c3200124c608e",
                    "updated_time": "2022-06-24T03:05:42.432Z"
                },
                {
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 123",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_holding_id": "f435559a6c8d93cb633f7b0d14cda1f6",
                    "product_line": "62a93c67a8772cd90c3b53b7",
                    "product_status": "OK",
                    "product_type": [
                        "62b185167fd598001059489f",
                        "62b185167fd598001059489d",
                        "62b185167fd598001059489c"
                    ],
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b55f873d2aca000f0e8284",
                    "updated_time": "2022-06-24T06:54:00.622Z"
                }
            ],
            "product_line_code": "tien_gui_tiet_kiem_975feecc",
            "product_line_id": "62a93c67a8772cd90c3b53b7",
            "product_line_name": "TIỀN GỬI TIẾT KIỆM",
            "status": "expired",
            "total": {
                "amount_saving_each_passbook": 0,
                "total_savings": 0,
                "total_savings_book": 0
            }
        },
        {
            "62a93c67a8772cd90c3b53b1": [
                {
                    "due_date": "2022-06-23T10:07:24.107Z",
                    "estimated_settlement_date": "2022-06-23T10:07:24.107Z",
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 1",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_holding_id": "8",
                    "product_line": "62a93c67a8772cd90c3b53b1",
                    "product_type": [
                        "62b185167fd598001059489f",
                        "62b185167fd598001059489d",
                        "62b185167fd598001059489c"
                    ],
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b51e420ce781000eb6a477",
                    "updated_time": "2022-06-24T02:15:30.590Z"
                }
            ],
            "product_line_code": "tai_khoan_39fcdcd2",
            "product_line_id": "62a93c67a8772cd90c3b53b1",
            "product_line_name": "TÀI KHOẢN",
            "status": "expired",
            "total": {
                "current_balance_original_currency": 0,
                "current_balance_vnd": 0,
                "current_monthly_average_balance_original_currency": 0,
                "current_monthly_average_balance_vnd": 0,
                "total_current_balance_currency": 0,
                "total_current_balance_vnd": 0,
                "total_current_monthly_average_balance_currency": 0,
                "total_current_monthly_average_balance_vnd": 0,
                "total_product_available": 0
            }
        },
        {
            "62b175b13323cb000eada1c7": [
                {
                    "due_date": "2022-06-23T10:07:24.107Z",
                    "estimated_settlement_date": "2022-06-23T10:07:24.107Z",
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "name 1",
                    "parent_id": "00190b50-12c1-4fcb-9742-2ae99264ecf1",
                    "product_code": "status3",
                    "product_holding_id": "99999999999",
                    "product_line": "62b175b13323cb000eada1c7",
                    "product_status": "status3",
                    "product_type": "id1",
                    "profile_event_relations": {
                        "name": "event",
                        "parent": "00190b50-12c1-4fcb-9742-2ae99264ecf1"
                    },
                    "tracking_code": "62b51e420ce781000eb6a477",
                    "transaction_office": "status3",
                    "updated_time": "2022-06-24T02:15:30.590Z"
                }
            ],
            "product_line_code": "anh",
            "product_line_id": "62b175b13323cb000eada1c7",
            "product_line_name": "Ánh test 21/6",
            "status": "expired",
            "total": {
                "amount_saving_each_passbook": 0,
                "current_balance_original_currency": 0,
                "current_balance_vnd": 0,
                "current_monthly_average_balance_original_currency": 0,
                "current_monthly_average_balance_vnd": 0,
                "total_current_balance_vnd": 0,
                "total_current_monthly_average_balance_currency": 0,
                "total_current_monthly_average_balance_vnd": 0,
                "total_face_value": 0,
                "total_initial_loan_balance": 0,
                "total_loan_balance": 0,
                "total_number_loan": 0,
                "total_product_available": 0,
                "total_savings": 0,
                "total_savings_book": 0
            }
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

*************************************  List Product Holding  ************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/profile/list/product_holding list product holding
@apiDescription list product holding
@apiGroup Product Holding
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName ListProductHolding

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam          (Param:)                     {String}        profile_id   Id của profile

@apiSuccess {Array}   data    Danh sách sản phẩm
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi


@apiSuccessExample {json} Response list card type example
@apiSuccessExample {json} Response list card type example
{
    "code": 200,
    "data": [
        {
            "product_line_code": "tien_gui_tiet_kiem_975feecc",
            "product_line_id": "62a93c67a8772cd90c3b53b7",
            "product_line_name": "TIỀN GỬI TIẾT KIỆM",
            "system_product_line_status": "expired",
        },
        {
            "product_line_code": "tai_khoan_39fcdcd2",
            "product_line_id": "62a93c67a8772cd90c3b53b1",
            "product_line_name": "TÀI KHOẢN",
            "system_product_line_status": "expired",
        },
        {
            
            "product_line_code": "anh",
            "product_line_id": "62b175b13323cb000eada1c7",
            "product_line_name": "Ánh test 21/6", 
            "system_product_line_status": "expired",
        }
    ],
    "total": {
        "activate": 1,
        "expired": 1,
        "unused": 1
    }
    "lang": "vi",
    "message": "request thành công."
}
"""

*************************************  Danh sách bank promotion  *************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/bank/get_promotion Danh sách promotion
@apiDescription Danh sách promotion của Bank
@apiGroup BANK
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName BankGetPromotion

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    [search]       Từ khóa tìm kiếm

@apiSuccess {Array}   data    Danh sách Promotion
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
    "code": 200,
    "data": [
        "test1",
        "test2",
        "test3"
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
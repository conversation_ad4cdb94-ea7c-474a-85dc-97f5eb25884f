#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: THONGNV
    Company: M O B I O
    Date Created: 8/20/20
"""

# ****   Đánh giá APP ****

# * Version 1.0.0
"""
@api {POST} /rating_app   Đánh giá ứng dụng
@apiDescription Profile Đánh giá ứng dụng. Kết quả đánh giá sẽ được lấy theo lần mới nhất
@apiName ProfileRatingApp
@apiGroup ProfileRating
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam   (Query:)     {string}    app_code    Mã app

@apiParam   (body:)  {Number}   rate_number     Số điểm đánh giá
@apiParam   (body:)  {String}   content           Nội dung đánh giá

@apiParamExample  {json}  Example
{
    "rate_number": 4,
    "content": "Nội dung nhạt nhẽo ad"
}

@apiSuccess {Object} data                      Kết quả đánh giá
@apiSuccess (data) {String}   app_code         Mã app
@apiSuccess (data) {String}   content          Nội dung đánh giá
@apiSuccess (data) {String}   rate_number      Số điểm đánh giá
@apiSuccess (data) {String}   id               ID đánh giá


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "app_code": "VENESA_APP",
    "content": "Nội dung nhạt nhẽo ad",
    "id": "6a2c50d8-a30d-44e6-841b-6d434cebd3b3",
    "rate_number": 4
  },
  "message": "request thành công."
}
"""


# ****   Lấy thông tin đánh giá ****

# * Version 1.0.0
"""
@api {GET} /rating_app/profile  Lấy thông tin đánh giá của profile
@apiDescription Thông tin đánh giá của ứng dụng của profile
@apiName GetProfileRatingApp
@apiGroup ProfileRating
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam   (Query:)     {string}    app_code    Mã app

@apiSuccess {Object} data                      Kết quả đánh giá
@apiSuccess (data) {String}   app_code         Mã app
@apiSuccess (data) {String}   content          Nội dung đánh giá
@apiSuccess (data) {String}   rate_number      Số điểm đánh giá
@apiSuccess (data) {String}   id               ID đánh giá


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "app_code": "VENESA_APP",
    "content": "Nội dung nhạt nhẽo ad",
    "id": "6a2c50d8-a30d-44e6-841b-6d434cebd3b3",
    "rate_number": 4
  },
  "message": "request thành công."
}
"""

# ****   Lấy danh sách lý do đánh giá thấp ****

# * Version 1.0.0
"""
@api {GET} /underestimate  Lấy danh sách lý do đánh giá thấp
@apiDescription Danh sách lý do đánh giá thấp
@apiName GetUnderestimate
@apiGroup ProfileRating
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header


@apiSuccess {Object} data                      Danh sách lý do đánh giá
@apiSuccess (data) {Number}   threshold        Số điểm giới hạn đánh giá
@apiSuccess (data) {Number}   title            Tiêu đề đánh giá
@apiSuccess (data) {Array}    reason           Danh sách lý do đánh giá thấp
@apiSuccess (data) {String}   reason.id        Id lý do
@apiSuccess (data) {String}   reason.name      Tên lý do đánh giá thấp


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "reason": [
      {
        "id": "b5a2584f-a3b0-41b2-9ad6-5800ef2f395c",
        "name": "Chất lượng dịch vụ kém"
      },
      {
        "id": "e4ddcd22-a119-4956-8ad5-6c5badfcb118",
        "name": "Thái độ nhân viên không nhiệt tình"
      },
      {
        "id": "da02d7ba-8c7a-428b-9a84-e2e3b7325825",
        "name": "Cơ sở vật chất tồi tàn"
      }
    ],
    "status": "UNDERESTIMATE",
    "threshold": 2,
    "title": "Vui lòng cho biết lí do đánh giá thấp"
  },
  "message": "request thành công."
}
"""

# ****   Đánh giá buổi trị liệu ****
# * Version 1.0.0
"""
@api {POST} /rating/therapy  Đánh giá buổi trị liệu
@apiDescription Đánh giá buổi trị liệu
@apiName RatingTherapy
@apiGroup ProfileRating
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam   (body:)  {String}   obj_id          Id buổi trị liệu (ref_id gửi về notification)
@apiParam   (body:)  {String}   reason_id       ID lí do đánh giá thấp
@apiParam   (body:)  {Number}   rate            Số điểm đánh giá
@apiParam   (body:)  {String}   note            Ghi chú đánh giá

@apiParamExample    {Body}      Body example:
{
    "obj_id":"17ee029a-e07e-4f68-93e2-14a9a8400d21",
    "reason_id":"b5a2584f-a3b0-41b2-9ad6-5800ef2f395c",
    "rate":1,
    "note": "Delay giờ hẹn quá lâu  "
}


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""

# ****   Đánh giá buổi trị liệu ****
# * Version 1.0.0
"""
@api {POST} /rating/therapy/check  Kiểm tra buổi trị liệu đã đánh giá?
@apiDescription Kiểm tra buổi trị liệu đã được đánh giá hay chưa
@apiName RatingTherapyCheck
@apiGroup ProfileRating
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam   (body:)  {String}   obj_id          Id buổi trị liệu (ref_id gửi về notification)

@apiParamExample    {Body}      Body example:
{
    "obj_id":"17ee029a-e07e-4f68-93e2-14a9a8400d21"
}


@apiSuccess {Object} data                      Kết quả kiểm tra
@apiSuccess (data) {String}   id               Id buổi trị liệu
@apiSuccess (data) {Number}   status           Trạng thái đánh giá
                                                <ul>
                                                    <li><code>0</code>: Chưa đánh giá </li>
                                                    <li><code>1</code>: Đã đánh giá </li>
                                                </ul>

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "id": "17ee029a-e07e-4f68-93e2-14a9a8400d21",
    "status": 1
  },
  "message": "request thành công."
}
"""
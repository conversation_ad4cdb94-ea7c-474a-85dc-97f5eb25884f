# ********************************************************************
# ********************** L<PERSON>y danh sách tin tức **********************
# ********************************************************************
"""
@api {post} /news Lấy danh sách tin tức
@apiDescription Lấy danh sách tin tức
@apiName GetNewsList
@apiGroup News
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging

@apiParam (Body:) {string}     [title]              <PERSON><PERSON><PERSON> theo tiêu đề của tin tức
@apiParam (Body:) {string}     [start_time]         Thời gian bắt đầu hiển thị
@apiParam (Body:) {string}     [end_time]           Thời gian kết thúc hiển thị
@apiParam (Body:) {Integer}    [order_by]	        Sắp xếp theo thuộc tính nào. Giá trị:
 <li><code>1</code>Sắp xếp theo tiêu đề của tin tức </li>
 <li><code>2</code>Sắp xếp theo tên của nhà cung cấp </li>
 <li><code>3</code>Thời gian băt đầu hiển thị </li>

@apiParam (Body:) {Integer}	[order_type]            Kiểu sắp xếp: Giá trị: 1=Tăng dần, 2=Giảm dần
@apiParam (Body:) {ArrayInteger}	[status]        Trạng thái của tin tức : Giá trị:
<li><code>1</code> Hiển thị   </li>
<li><code>2</code> Ẩn  </li>

@apiParam (Body:) {string}	[type]   Kiểu tin tức : Giá trị:
<li><code>SAKUKOTV</code>  </li>
<li><code>NEWS</code> </li>
<li><code>PRODUCT</code> </li>

@apiParam (Body:) {Integer}	[status]                Kiểu trạng thị: Giá trị: 0-HIDEN 1-DISPLAY
@apiParam (Body:) {arrayObject}     [mapping_merchant]      Danh sách thông tin của merchant hỗ trợ việc lọc
<li><b>id</b> UUID của merchant</li>
<li><b>name</b> Tên của merchant</li>


@apiParamExample {json} Body example
{
    "title": "Tieu de bai dang",
    "start_time": "2019-08-11T12:00:00Z",
    "end_time" : "2019-08-11T12:00:00Z",
    "order_by":1,
    "order_type":1,
    "status":[1],
    "mapping_merchant":[
        {
            "id":"uudi1",
            "name":"Ten nha cung cap so 1"
        },
         {
            "id":"uudi1",
            "name":"Ten nha cung cap so 1"
        },
        ...
    ]

}


@apiSuccess {Array} data Danh sách tin tức
@apiSuccess (data)     {string}        id                  UUID tin tức
@apiSuccess (data)     {string}        titles              Tiêu đề tin tức
@apiSuccess (data)     {number= 0-HIDEN 1-DISPLAY}         status Trạng thái của sản phẩm.<code>HIDEN: Ẩn</code>, <code>DISPLAY: Hiển thị</code>
@apiSuccess (data)     {String}        avatar              Ảnh đại diện
@apiSuccess (data)     {String}        created_time        Thời gian tạo tin tức
@apiSuccess (data)     {String}        update_time         Thời gian sửa tin tức
@apiSuccess (data)     {String}        start_time          Thời gian hiển thị tin tức
@apiSuccess (data)     {String}        end_time            Thời gian kết thúc hiển thị tin tức
@apiSuccess (data)     {String}        link                 Link đến kênh youtube,... của tin tức,
 với type = "SAKUKOTV" hoặc "PRODUCT"

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "c7229324-ffe3-4090-8acb-27093003f703",
            "merchant_id": "c7229324-ffe3-4090-8acb-27093003f703",
            "titles": "Tưng bừng khai trương siêu thị free tất cả mặt hàng",
            "status": 0,
            "link": "",
            "avatar": "https://lh3.googleusercontent.com/-gl7FITDjU0M/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3rfErC03uIbV2sVO1zmjUxxOgjWxdA/s32-c-mo/photo.jpg",
            "start_time": "2019-08-11T12:00:00Z",
            "end_time": "2019-08-11T12:00:00Z",
            "created_time": "2019-08-11T12:00:00Z",
            "update_time": "2019-08-11T12:00:00Z",
        },
        ....
    ],
    "paging":{
        "page":1,
        "per_page":15,
        "total_page":1,
        "total_item":10
    }
    "message": "request thành công."
}

"""


# ********************************************************************
# ********************** Lấy chi tiết tin tức  **********************
# ********************************************************************


"""
@api {get} /news/<new_id> Lấy chi tiết tin tức
@apiVersion 1.0.0
@apiName DetailNews
@apiGroup News

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Object}   data   Nội dung tin tức
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:) {string} id             UUID tin tức
@apiSuccess (data:) {string} content        Nội dung tin tức string html
@apiSuccess (data:) {string} push_time      Thời gian đăng tin tức
@apiSuccess (data:) {string} avatar         Link ảnh đại diện cho tin tức
@apiSuccess (data:) {string} title          Tiêu đề cho tin tức
@apiSuccess (data:) {string} end_time       Thời gian kết thúc tin tức
@apiSuccess (data:) {string} start_time     Thời gian bắt đầu tin tức
@apiSuccess (data:) {string} language       Ngôn ngữ

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
            "attributes": [],
            "avatar": null,
            "content": "Sekiro: Shadows Die Twice là một trò chơi điện tử phiêu lưu hành động được phát triển bởi FromSoftware và được Activision phát hành. Trò chơi ra mắt trên toàn thế giới cho Microsoft Windows, PlayStation 4 và Xbox One vào ngày 22 tháng 3 năm 2019.",
            "end_time": "2019-05-11T12:00:00Z",
            "id": "a8aed142-95ef-49ec-a249-926f89057322",
            "language": "vi",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "push_time": null,
            "start_time": "2019-03-11T12:00:00Z",
            "title": "Sekiro: Shadow die twice"
        }
}
"""

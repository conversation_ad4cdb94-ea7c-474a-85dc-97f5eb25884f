#*******************************************************************************
#************************ Đăng ký phần mềm máy *********************************
#* version: 1.0.0                                                              *
#*******************************************************************************
"""
@api {post} /register/devices  Đăng ký phần mềm máy
@apiDescription Dịch vụ đăng ký phần mềm máy
@apiGroup Account
@apiVersion 1.0.0
@apiName RegisterDevice

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Body:)     {String}    app_code                    Mã phần mềm: client fix: SAKUKO
@apiParam      (Body:)     {String}    app_version                 Phiên bản phần mềm
@apiParam      (Body:)     {String}    platform                    Nền tảng: client fix (ANDROID | IOS | WP)
@apiParam      (Body:)     {String}    width                       Cỡ màn hình ngang
@apiParam      (Body:)     {String}    height                      Cỡ màn hình dọc
@apiParam      (Body:)     {String}    language                    Ngôn ngữ máy
@apiParam      (Body:)     {String}    longitude                   Kinh độ
@apiParam      (Body:)     {String}    latitude                     Vĩ độ
@apiParam      (Body:)     {String}    manufacturer                Hãng sản xuất
@apiParam      (Body:)     {String}    model                       Model máy
@apiParam      (Body:)     {String}    profile_id                  Khách hàng ID
@apiParam      (Body:)     {String}    push_id                     PushID
@apiParam      (Body:)     {String}    device_id                   Thiết bị ID (tùy client quyết định: tự sinh ID | serial | android ID | iOS ID | ...)
@apiParam      (Body:)     {String}    imei                        Số imei
@apiParam      (Body:)     {String}    imei2                       Số imei 2
@apiParam      (Body:)     {String}    serial                      Số serial
@apiParam      (Body:)     {String}    os                          OS name
@apiParam      (Body:)     {String}    os_version                       OS version

@apiParamExample {json} Body example:
{
  "app_code": "SAKUKO",  // Mã phần mềm: client fix: SAKUKO
  "app_version": "1.0",  // Phiên bản phần mềm
  "platform": "ANDROID",  // Nền tảng: client fix (ANDROID | IOS | WP)
  "width": "720",  // Cỡ màn hình ngang
  "height": "1280", // Cỡ màn hình dọc
  "language": "vi",             // Ngôn ngữ máy
  "longitude": "105.19201",      // Kinh độ
  "latitude": "37.102229",      // Vĩ độ
  "manufacturer": "SAMSUNG",       // Hãng sản xuất
  "model": "ST-319",       // Model máy
  "profile_id": "73d01e82-ff57-4ca0-b3ac-de620b51a4c1",              // Khách hàng ID
  "push_id": "LDSAKFJPQISADFKMLASDldjaSOIQOJASDkdhaisluLKHDIUHLSAKJDHhdu", //PushID
  "device_id": "192l1k29sk1", // Thiết bị ID (tùy client quyết định: tự sinh ID | serial | android ID | iOS ID | ...)
  "imei": "29101930193", // Số imei
  "imei2": "2103910192", // Số imei2
  "serial": "92011210912", // Số serial
  "os": "ANDROID", // os name
  "os_version": "4.2"  // os version
}

@apiSuccess  {String} P Phần mềm máy ID

@apiSuccessExample {json} Response example
{
  "P": "4c4f3a56-881d-496e-b860-a02487b79deb",
  "code": 200,
  "message": "request thành công"
}

"""

#*******************************************************************************
#********************* Dịch vụ đăng nhập qua số điện thoại/email ***************
#* version: 1.0.0                                                              *
#*******************************************************************************
"""
@api {post} /login Dịch vụ login qua số điện thoại hoặc email
@apiDescription Dịch vụ login qua số điện thoại hoặc email
@apiGroup Account
@apiVersion 1.0.0
@apiName Login

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Query:)     {String}   merchant_id                       ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>

@apiParam      (Query:)     {String}   [app_version]                     Phiên bản phần mềm
@apiParam      (Query:)     {String}   pd_id                             Phần mềm máy ID
@apiParam      (Query:)     {String}   app_code                          Mã phần mềm: client fix: SAKUKO


@apiParam      (Body:)     {String}    username                          Tên đăng nhập
@apiParam      (Body:)     {String}    password                          Mật khẩu đăng nhập


@apiParamExample {json} Body example:
{
  "username": "+************",
  "password": "abcxyz"
}

@apiSuccess  {Array} address             Địa chỉ khách hàng
@apiSuccess  {String} avatar              Ảnh avatar
@apiSuccess  {String} birthday            Ngày sinh. Format: <code>YYYY-MM-DD</code>
@apiSuccess  {String} created_time        Thời điểm tạo (theo giờ UTC).
@apiSuccess  {String} phone_number        Số điện thoại
@apiSuccess  {String} email               Email
@apiSuccess  {Number} gender              Giới tính khách hàng. <br/>
Allowed values:<br/>
<li><code>1: Unknown</code></li>
<li><code>2: Male</code></li>
<li><code>3: Female</code></li>
@apiSuccess  {String} profile_id          ID Khách hàng
@apiSuccess  {String} updated_time        Thời điểm cập nhật
@apiSuccess  {String} token               Json web token
@apiSuccess  {Number} voucher             Số voucher chưa dùng 
@apiSuccess  {Number} point               Điểm thưởng

@apiSuccess  {Object} card                Thẻ

@apiSuccess  (card:)    {String}          card_name                Tên thẻ
@apiSuccess  (card:)    {String}          code                     Mã thẻ

@apiSuccessExample {json} Đăng nhập thành công
{
    "address": null,
    "birthday": null,
    "card": {
        "card_name": null,
        "code": null
    },
    "code": 200,
    "created_time": "2019-03-30T03:46:20.898Z",
    "email": null,
    "gender": 2,
    "iat": 1558434881.9332848,
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "message": "request thành công.",
    "name": "Trần Huy Tiệp",
    "phone_number": "+84363335020",
    "point": null,
    "profile_id": "c264ac2b-e4cd-467f-b44e-e0d101e7c5e8",
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ijWSSwXq0jabbQR524KR2XihXR6Nw9mU-FR4bd6zZPU",
    "updated_time": "2019-05-10T03:17:58.298Z",
    "voucher": 4
}

@apiSuccessExample {json} Sai username/mật khẩu
{
  "code": 412,
  "message": "Số điện thoại/email hoặc mật khẩu không chính xác. Vui lòng kiểm tra lại."
}

"""

#*******************************************************************************
#********************* Dịch vụ đăng nhập qua mạng xã hội ***********************
#* version: 1.0.0                                                              *
#*******************************************************************************
"""
@api {post} /login/social Dịch vụ đăng nhập qua mạng xã hội
@apiDescription Dịch vụ đăng nhập qua mạng xã hội
@apiGroup Account
@apiVersion 1.0.0
@apiName LoginSocial

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Query:)     {String}    merchant_id                       ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>

@apiParam      (Query:)     {String}    pd_id                             Phần mềm máy ID
@apiParam      (Query:)     {String}    app_code                          Mã phần mềm: client fix: XLOYALTY | XLTVENDOR



@apiParam      (Body:)     {Number}     social                            Kiểu mạng xã hội. Allowed values:<br/>
<li><code>1: Facebook</code></li>
<li><code>93: Apple</code></li>

@apiParam      (Body:)     {String}     token                             Access token tùy thuộc mạng xã hội


@apiParamExample {json} Body example:
{
  "social": 1,
  "token": "access token"
}


@apiSuccess  {Array} address             Địa chỉ khách hàng
@apiSuccess  {String} avatar              Ảnh avatar
@apiSuccess  {String} birthday            Ngày sinh. Format: <code>YYYY-MM-DD</code>
@apiSuccess  {String} created_time        Thời điểm tạo (theo giờ UTC).
@apiSuccess  {String} phone_number        Số điện thoại
@apiSuccess  {String} email               Email
@apiSuccess  {Number} gender              Giới tính khách hàng. <br/>
Allowed values:<br/>
<li><code>1: Unknown</code></li>
<li><code>2: Male</code></li>
<li><code>3: Female</code></li>
@apiSuccess  {String} profile_id          ID Khách hàng
@apiSuccess  {String} updated_time        Thời điểm cập nhật
@apiSuccess  {String} token               Json web token
@apiSuccess  {Number} voucher             Số voucher chưa dùng 
@apiSuccess  {Number} point               Điểm thưởng

@apiSuccess  {Object} card                Thẻ

@apiSuccess  (card:)    {String}          card_name                Tên thẻ
@apiSuccess  (card:)    {String}          code                     Mã thẻ


@apiSuccessExample {json} Đăng nhập thành công
{
    "address": null,
    "birthday": null,
    "card": {
        "card_name": null,
        "code": null
    },
    "code": 200,
    "created_time": "2019-03-30T03:46:20.898Z",
    "email": null,
    "gender": 2,
    "iat": 1558434881.9332848,
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "message": "request thành công.",
    "name": "Trần Huy Tiệp",
    "phone_number": "+84363335020",
    "point": null,
    "profile_id": "c264ac2b-e4cd-467f-b44e-e0d101e7c5e8",
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ijWSSwXq0jabbQR524KR2XihXR6Nw9mU-FR4bd6zZPU",
    "updated_time": "2019-05-10T03:17:58.298Z",
    "voucher": 4
}

@apiSuccessExample {json} Lỗi verify token
{
  "code": 412,
  "message": "Không thể xác thực mạng xã hội. Vui lòng thử lại."
}

@apiSuccessExample {json} Lỗi verify số điện thoại
{
    "message": "Vui lòng xác thực số điện thoại để đăng nhập.",
    "code": 405
}
"""


# *******************************************************************************
# ********************* DỊCH VỤ GỬI MÃ CODE XÁC THỰC SỐ ĐIỆN THOẠI **************
# * version: 1.0.0                                                              *
# *******************************************************************************

# ****** Version 1.0.1 *********
"""
@api {post} /login/social/verify/phone_number/code Dịch vụ gửi mã code xác thực số điện thoại
@apiDescription Dịch vụ gửi mã code xác thực số điện thoại
@apiGroup Account
@apiVersion 1.0.1
@apiName SendCodeVerifyPhoneNumber

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Query:)     {String}    merchant_id                               ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>

@apiParam      (Query:)     {String}    pd_id                             Phần mềm máy ID
@apiParam      (Query:)     {String}    app_code                          Mã phần mềm: client fix: XLOYALTY | XLTVENDOR



@apiParam      (Body:)     {Number}     social                            Kiểu mạng xã hội. Allowed values:<br/>
<li><code>1: Facebook</code></li>

@apiParam      (Body:)     {String}     token                             Access token tùy thuộc mạng xã hội
@apiParam      (Body:)     {String}     phone_number                      Số điện thoại


@apiParamExample {json} Body example:
{
  "social": 1,
  "token": "access token"
  "phone_number":"012365...."
}

@apiSuccessExample {json} Response success
{
  "code": 200,
  "message": "request thành công",
}

@apiSuccessExample {json} Lỗi verify token
{
  "code": 412,
  "message": "Không thể xác thực mạng xã hội. Vui lòng thử lại."
}
"""


# ****** Version 1.0.0 *********
"""
@api {post} /login/social/verify/phone_number/code Dịch vụ gửi mã code xác thực số điện thoại
@apiDescription Dịch vụ gửi mã code xác thực số điện thoại
@apiGroup Account
@apiVersion 1.0.0
@apiName SendCodeVerifyPhoneNumber

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Query:)     {String}    merchant_id                               ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>

@apiParam      (Query:)     {String}    pd_id                             Phần mềm máy ID
@apiParam      (Query:)     {String}    app_code                          Mã phần mềm: client fix: XLOYALTY | XLTVENDOR



@apiParam      (Body:)     {Number}     social                            Kiểu mạng xã hội. Allowed values:<br/>
<li><code>1: Facebook</code></li>

@apiParam      (Body:)     {String}     token                             Access token tùy thuộc mạng xã hội
@apiParam      (Body:)     {String}     phone_number                      Số điện thoại


@apiParamExample {json} Body example:
{
  "social": 1,
  "token": "access token"
  "phone_number":"012365...."
}

@apiSuccessExample {json} Response success
{
  "code": 200,
  "message": "request thành công",
}

@apiSuccessExample {json} Lỗi verify token
{
  "code": 412,
  "message": "Không thể xác thực mạng xã hội. Vui lòng thử lại."
}
"""


# *******************************************************************************
# ********************* Xác thực số điện thoại ***********************
# * version: 1.0.0                                                              *
# *******************************************************************************

# ********* Version 1.0.1 *****************
"""
@api {post} /login/social/verify/phone_number Dịch vụ xác thực số điện thoại
@apiDescription Dịch vụ xác thực số điện thoại
@apiGroup Account
@apiVersion 1.0.0
@apiName VerifyPhoneNumber

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.


@apiParam      (Query:)     {String}    merchant_id                               ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>

@apiParam      (Query:)     {String}    pd_id                             Phần mềm máy ID
@apiParam      (Query:)     {String}    app_code                          Mã phần mềm: client fix: XLOYALTY | XLTVENDOR



@apiParam      (Body:)     {Number}     social                            Kiểu mạng xã hội. Allowed values:<br/>
<li><code>1: Facebook</code></li>

@apiParam      (Body:)     {String}     token                             Access token tùy thuộc mạng xã hội
@apiParam      (Body:)     {String}     phone                             Số điện thoại
@apiParam      (Body:)     {String}     code                              Mẫ xác thực
@apiParam      (Body:)     {String}    [birthday]                         Ngày sinh <code>Định dạng: YYYY-mm-dd </code>.
@apiParam      (Body:)     {String}    [identify_type]                    Loại giấy tờ. (GPLX, Passport, CMND, ...)
                                                                            <ul>
                                                                                <li>GPLX: Giấy phép lái xe</li>
                                                                                <li>PASSPORT: Hộ chiếu</li>
                                                                                <li>CMND: Chứng minh nhân dân</li>
                                                                                <li>CCCD: Căn cước công dân</li>
                                                                                <li>CMNDQD: Chứng minh thư quân đội</li>
                                                                            </ul>
@apiParam      (Body:)     {String}    [identify_code]                    Số chứng minh nhân dân / căn cước công dân.
@apiParam      (Body:)     {String}    [province_code]                         Mã tỉnh thành
@apiParam      (Body:)     {String}    [fullname]                         Họ tên đầy đủ
@apiParam      (Body:)     {Number}    [gender]                           Giới tính khách hàng. <br/>


@apiParamExample {json} Body example:
{
    "social": 1,
    "token": "access token"
    "phone":"0123456....",
    "code":"FSaDfd"
}


@apiSuccess  {String} address             Địa chỉ khách hàng
@apiSuccess  {String} avatar              Ảnh avatar
@apiSuccess  {String} birthday            Ngày sinh. Format: <code>YYYY-MM-DD</code>
@apiSuccess  {String} created_time        Thời điểm tạo (theo giờ UTC).
@apiSuccess  {String} phone_number        Số điện thoại
@apiSuccess  {String} email               Email
@apiSuccess  {Number} gender              Giới tính khách hàng. <br/>
Allowed values:<br/>
<li><code>1: Unknown</code></li>
<li><code>2: Male</code></li>
<li><code>3: Female</code></li>
@apiSuccess  {String} profile_id          ID Khách hàng
@apiSuccess  {String} updated_time        Thời điểm cập nhật
@apiSuccess  {String} token               Json web token
@apiSuccess  {Number} voucher             Số voucher chưa dùng 
@apiSuccess  {Number} point               Điểm thưởng

@apiSuccess  {Object} card                Thẻ

@apiSuccess  (card:)    {String}          card_name                Tên thẻ
@apiSuccess  (card:)    {String}          code                     Mã thẻ


@apiSuccessExample {json} Đăng nhập thành công
{
    "address": null,
    "birthday": null,
    "card": {
        "card_name": null,
        "code": null
    },
    "code": 200,
    "created_time": "2019-03-30T03:46:20.898Z",
    "email": null,
    "gender": 2,
    "iat": 1558434881.9332848,
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "message": "request thành công.",
    "name": "Trần Huy Tiệp",
    "phone_number": "+84363335020",
    "point": null,
    "profile_id": "c264ac2b-e4cd-467f-b44e-e0d101e7c5e8",
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ijWSSwXq0jabbQR524KR2XihXR6Nw9mU-FR4bd6zZPU",
    "updated_time": "2019-05-10T03:17:58.298Z",
    "voucher": 4
}

@apiSuccessExample {json} Lỗi verify token
{
  "code": 412,
  "message": "Không thể xác thực mạng xã hội. Vui lòng thử lại."
}
"""



# ********* Version 1.0.0 *****************
"""
@api {post} /login/social/verify/phone_number Dịch vụ xác thực số điện thoại
@apiDescription Dịch vụ xác thực số điện thoại
@apiGroup Account
@apiVersion 1.0.0
@apiName VerifyPhoneNumber

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.


@apiParam      (Query:)     {String}    merchant_id                               ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>

@apiParam      (Query:)     {String}    pd_id                             Phần mềm máy ID
@apiParam      (Query:)     {String}    app_code                          Mã phần mềm: client fix: XLOYALTY | XLTVENDOR



@apiParam      (Body:)     {Number}     social                            Kiểu mạng xã hội. Allowed values:<br/>
<li><code>1: Facebook</code></li>

@apiParam      (Body:)     {String}     token                             Access token tùy thuộc mạng xã hội
@apiParam      (Body:)     {String}     phone                             Số điện thoại
@apiParam      (Body:)     {String}     code                              Mẫ xác thực


@apiParamExample {json} Body example:
{
    "social": 1,
    "token": "access token"
    "phone":"0123456....",
    "code":"FSaDfd"
}


@apiSuccess  {String} address             Địa chỉ khách hàng
@apiSuccess  {String} avatar              Ảnh avatar
@apiSuccess  {String} birthday            Ngày sinh. Format: <code>YYYY-MM-DD</code>
@apiSuccess  {String} created_time        Thời điểm tạo (theo giờ UTC).
@apiSuccess  {String} phone_number        Số điện thoại
@apiSuccess  {String} email               Email
@apiSuccess  {Number} gender              Giới tính khách hàng. <br/>
Allowed values:<br/>
<li><code>1: Unknown</code></li>
<li><code>2: Male</code></li>
<li><code>3: Female</code></li>
@apiSuccess  {String} profile_id          ID Khách hàng
@apiSuccess  {String} updated_time        Thời điểm cập nhật
@apiSuccess  {String} token               Json web token
@apiSuccess  {Number} voucher             Số voucher chưa dùng 
@apiSuccess  {Number} point               Điểm thưởng

@apiSuccess  {Object} card                Thẻ

@apiSuccess  (card:)    {String}          card_name                Tên thẻ
@apiSuccess  (card:)    {String}          code                     Mã thẻ


@apiSuccessExample {json} Đăng nhập thành công
{
    "address": null,
    "birthday": null,
    "card": {
        "card_name": null,
        "code": null
    },
    "code": 200,
    "created_time": "2019-03-30T03:46:20.898Z",
    "email": null,
    "gender": 2,
    "iat": 1558434881.9332848,
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "message": "request thành công.",
    "name": "Trần Huy Tiệp",
    "phone_number": "+84363335020",
    "point": null,
    "profile_id": "c264ac2b-e4cd-467f-b44e-e0d101e7c5e8",
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ijWSSwXq0jabbQR524KR2XihXR6Nw9mU-FR4bd6zZPU",
    "updated_time": "2019-05-10T03:17:58.298Z",
    "voucher": 4
}

@apiSuccessExample {json} Lỗi verify token
{
  "code": 412,
  "message": "Không thể xác thực mạng xã hội. Vui lòng thử lại."
}
"""


#*******************************************************************************
#********************** Dịch vụ yêu cầu reset mật khẩu *************************
#* version: 1.0.0                                                              *
#*******************************************************************************
"""
@api {post} /profile/reset-password Dịch vụ yêu cầu reset mật khẩu
@apiDescription Dịch vụ yêu cầu reset mật khẩu
@apiGroup Account
@apiVersion 1.0.0
@apiName ResetUserPassword

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Query:)     {String}    merchant_id                               ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>

@apiParam      (Body:)     {String}    email                            Email yêu cầu reset mật khẩu.
@apiParam      (Body:)     {String}    phone                            Số điện thoại yêu cầu reset mật khẩu.

@apiParam      (Query:)    {String}    app_code                        Phiên bản phần mềm
@apiParam      (Query:)    {String}    pd_id                             Phần mềm máy ID

@apiParamExample {json} Body example:
{
  "phone": "**********",
}

@apiParamExample {json} Body example:
{
  "email": "<EMAIL>",
}

@apiSuccessExample {json} Response success
{
  "code": 200,
  "message": "request thành công",
}

@apiSuccessExample {json} Email không tồn tại trên hệ thống.
{
  "code": 412,
  "message": "Email không tồn tại trên hệ thống. Vui lòng kiểm tra lại",
}
"""

#*******************************************************************************
#*************************** Dịch vụ đổi mật khẩu ******************************
#* version: 1.0.0                                                              *
#*******************************************************************************
"""
@api {post} /profile/change-password Dịch vụ user đổi mật khẩu
@apiDescription Dịch vụ user đổi mật khẩu
@apiGroup Account
@apiVersion 1.0.0
@apiName ChangePassword

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Query:)     {String}    merchant_id                     ID của merchant.

@apiParam      (Body:)     {String}    old_password                      Mật khẩu cũ
@apiParam      (Body:)     {String}    new_password                      Mật khẩu mới

@apiParam      (Query:)    {String}    app_code                          Phiên bản phần mềm
@apiParam      (Query:)    {String}    pd_id                             Phần mềm máy ID

@apiParamExample {json} Body example:
{
  "old_password": "abcxyz",
  "new_password": "abc123"
}


@apiSuccessExample {json} Response success
{
  "code": 200,
  "message": "Yêu cầu đổi mật khẩu thành công",
}

@apiSuccessExample {json} Mật khẩu cũ không đúng
{
  "code": 412,
  "message": "Mật khẩu cũ không chính xác. Vui lòng kiểm tra lại.",
}
"""

#*******************************************************************************
#****************************** Dịch vụ đăng xuất ******************************
#* version: 1.0.0                                                              *
#*******************************************************************************
"""
@api {post} /profile/logout Dịch vụ đăng xuất
@apiDescription Dịch vụ đăng xuất
@apiGroup Account
@apiVersion 1.0.0
@apiName UserLogout

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Query:)     {String}    profile_id                               User id

@apiParam      (Query:)    {String}    app_code                        Phiên bản phần mềm
@apiParam      (Query:)    {String}    pd_id                             Phần mềm máy ID


@apiSuccessExample {json} Response success
{
  "code": 200,
  "message": "Đăng xuất thành công",
}

"""

#*******************************************************************************
#********************* Dịch vụ update thông tin tài khoản **********************
#* version: 1.0.0                                                              *
#*******************************************************************************
"""
@api {patch} /profile/action/update Update thông tin tài khoản
@apiDescription Dịch vụ update thông tin tài khoản. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiGroup Account
@apiVersion 1.0.0
@apiName UpdateUserInfo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)        {File}       avatar                              File ảnh đại diện 
@apiParam       (Body:)        {Object}     infor                             Thông tin profile


@apiParam       (Infor:)        {String}     [address]             Địa chỉ khách hàng
@apiParam       (Infor:)        {String}     [name]                Họ và Tên
@apiParam       (Infor:)        {String}     [birthday]            Ngày sinh. Format: <code>YYYY-MM-DD</code>
@apiParam       (Infor:)        {Number}     [gender]              Giới tính khách hàng. <br/>
@apiParam       (Infor:)         {String}    [birthday]                         Ngày sinh <code>Định dạng: YYYY-mm-dd </code>.
@apiParam      (Infor:)     {String}    [identify_type]                    Loại giấy tờ. (GPLX, Passport, CMND, ...)
                                                                            <ul>
                                                                                <li>GPLX: Giấy phép lái xe</li>
                                                                                <li>PASSPORT: Hộ chiếu</li>
                                                                                <li>CMND: Chứng minh nhân dân</li>
                                                                                <li>CCCD: Căn cước công dân</li>
                                                                                <li>CMNDQD: Chứng minh thư quân đội</li>
                                                                            </ul>
@apiParam      (Infor:)     {String}    [identify_code]                    Số chứng minh nhân dân / căn cước công dân.
@apiParam      (Infor:)     {String}    [province_code]                         Mã tỉnh thành
@apiParam      (Infor:)     {Number}    [gender]                           Giới tính khách hàng. <br/>

Allowed values:<br/>
<li><code>1: Unknown</code></li>
<li><code>2: Male</code></li>
<li><code>3: Female</code></li>

@apiSuccess         {String}        address             Địa chỉ khách hàng
@apiSuccess         {String}        avatar              Ảnh avatar
@apiSuccess         {String}        birthday            Ngày sinh. Format: <code>YYYY-MM-DD</code>
@apiSuccess         {String}        created_time        Thời điểm tạo (theo giờ UTC).
@apiSuccess         {Number}        gender              Giới tính khách hàng. <br/>
Allowed values:<br/>
<li><code>1: Unknown</code></li>
<li><code>2: Male</code></li>
<li><code>3: Female</code></li>
@apiSuccess         {String}        profile_id          ID Khách hàng


@apiSuccessExample {json} Response success
{
    "code": 200,
    "data": {
        "address": "ha nam",
        "avatar": "https://mbodevstorage.blob.core.windows.net/images/5fe2e579-dc21-40d0-8f74-428e8a88fe5b",
        "birthday": null,
        "gender": 2,
        "name": "Trần Huy Tiệp",
        "profile_id": "5fe2e579-dc21-40d0-8f74-428e8a88fe5b",
    },
    "message": "Cập nhật khách hàng thành công.",
    "process_type": "update"
}
"""

#***********************************************************************
#************************ Cập nhật cấu hình app ************************
#* Version: 1.0.0                                                      *
#***********************************************************************
"""
@api {PUT} /profile/configs Cập nhật cấu hình app
@apiGroup Account
@apiVersion 1.0.0
@apiName PutAccountConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam   {string}        app_code                Mã phần mềm: client fix: SAKUKO
@apiParam   {string}        is_receive_notify       Nhận thông báo về app:<br/>
Allowed values:<br/>
<li><code>1: Nhận thông báo</code></li>
<li><code>0: Không nhận thông báo</code></li>

@apiParam   {ArrayObject}   configs                 List config của user
@apiParam   (configs)       {string}                key Key config của user
@apiParam   (configs)       {string}                value giá trị config của user theo <code>key</code>

@apiParamExample {json} Body
{
	"configs": [
        {
            "key": "",
            "value": ""
        }
    ],
    "app_code": "SAKUKO",
    "is_receive_notify": 1
}

@apiSuccessExample {json} Response success
{
  "code": 200,
  "message": "request thành công"
}
"""


#***********************************************************************
#************************ Lấy cấu hình app ************************
#* Version: 1.0.0                                                      *
#***********************************************************************
"""
@api {GET} /profile/configs Lấy cấu hình phần mềm máy
@apiGroup Account
@apiVersion 1.0.0
@apiName GetAccountConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Query:)     {String}    app_code     Mã phần mềm: client fix: SAKUKO

@apiSuccessExample {json} Response success
{
  "code": 200,
  "message": "request thành công",
  "data":{
        "configs": [
            {
                "key": "",
                "value": ""
            }
        ],
        "is_receive_notify": 1
    }
}
"""

#*****************************************************************************
#********************* Dịch vụ đăng ký qua số điện thoại/email ***************
#* version: 1.0.1                                                            *
#* version: 1.0.0                                                            *
#*****************************************************************************
"""
@api {post} /register Dịch vụ đăng ký qua số điện thoại/email
@apiDescription Dịch vụ đăng ký qua số điện thoại/email
@apiGroup Account
@apiVersion 1.0.1
@apiName Register

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Body:)     {String}    [email]                            Email yêu cầu đăng ký tài khoản.
@apiParam      (Body:)     {String}    [phone]                            Số điện thoại yêu cầu đăng ký tài khoản.
@apiParam      (Body:)     {String}    [birthday]                         Ngày sinh <code>Định dạng: YYYY-mm-dd </code>.
@apiParam      (Body:)     {String}    [identify_type]                    Loại giấy tờ. (GPLX, Passport, CMND, ...)
                                                                            <ul>
                                                                                <li>GPLX: Giấy phép lái xe</li>
                                                                                <li>PASSPORT: Hộ chiếu</li>
                                                                                <li>CMND: Chứng minh nhân dân</li>
                                                                                <li>CCCD: Căn cước công dân</li>
                                                                                <li>CMNDQD: Chứng minh thư quân đội</li>
                                                                            </ul>
@apiParam      (Body:)     {String}    [identify_code]                    Số chứng minh nhân dân / căn cước công dân.
@apiParam      (Body:)     {String}    [province_code]                         Mã tỉnh thành
@apiParam      (Body:)     {String}    [fullname]                         Họ tên đầy đủ
@apiParam      (Body:)     {Number}    [gender]                           Giới tính khách hàng. <br/>
Allowed values:<br/>
<li><code>1: Unknown</code></li>
<li><code>2: Male</code></li>
<li><code>3: Female</code></li>


@apiParam      (Query:)    {String}   merchant_id                       ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>
@apiParam      (Query:)    {String}   app_code                          Phiên bản phần mềm
@apiParam      (Query:)    {String}   pd_id                             Phần mềm máy ID

@apiParamExample {json} Body example:
{
  "phone": "**********",
}

@apiParamExample {json} Body example:
{
  "email": "<EMAIL>",
}

@apiSuccessExample {json} Response success
{
  "code": 200,
  "message": "request thành công",
}

@apiSuccessExample {json} Email không tồn tại trên hệ thống.
{
  "code": 412,
  "message": "Email không tồn tại trên hệ thống. Vui lòng kiểm tra lại",
}
"""

# -------------------

"""
@api {post} /register Dịch vụ đăng ký qua số điện thoại/email
@apiDescription Dịch vụ đăng ký qua số điện thoại/email
@apiGroup Account
@apiVersion 1.0.0
@apiName Register

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Body:)     {String}    [email]                            Email yêu cầu đăng ký tài khoản.
@apiParam      (Body:)     {String}    [phone]                            Số điện thoại yêu cầu đăng ký tài khoản.

@apiParam      (Query:)    {String}   merchant_id                               ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>
@apiParam      (Query:)    {String}   app_code                          Phiên bản phần mềm
@apiParam      (Query:)    {String}   pd_id                             Phần mềm máy ID

@apiParamExample {json} Body example:
{
  "phone": "**********",
}

@apiParamExample {json} Body example:
{
  "email": "<EMAIL>",
}

@apiSuccessExample {json} Response success
{
  "code": 200,
  "message": "request thành công",
}

@apiSuccessExample {json} Email không tồn tại trên hệ thống.
{
  "code": 412,
  "message": "Email không tồn tại trên hệ thống. Vui lòng kiểm tra lại",
}
"""


# *******************************************************************************
# ********************* Dịch vụ update thông tin liên lạc  **********************
# * version: 1.0.0                                                              *
# *******************************************************************************
"""
@api {post} /profile/change/contact_info Update thông tin liên lạc 
@apiDescription Dịch vụ update thông tin liên lạc 
@apiGroup Account
@apiVersion 1.0.0
@apiName UpdateUserInfoContact

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Body:)     {String}    [email]                            Email yêu cầu đăng ký tài khoản.
@apiParam      (Body:)     {String}    [phone]                            Số điện thoại yêu cầu đăng ký tài khoản.

@apiParam      (Query:)    {String}   merchant_id                       ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>
@apiParam      (Query:)    {String}   app_code                          Phiên bản phần mềm
@apiParam      (Query:)    {String}   pd_id                             Phần mềm máy ID

@apiParamExample {json} Body example:
{
  "phone": "**********",
}

@apiParamExample {json} Body example:
{
  "email": "<EMAIL>",
}

@apiSuccessExample {json} Response success
{
    "code": 200,
    "message": "request thành công..",
}
"""


# *******************************************************************************
# ********************* Dịch vụ xác thực update thông tin liên lạc  **********************
# * version: 1.0.0                                                              *
# *******************************************************************************
"""
@api {post} /profile/change/contact_info/verify Xác thực update thông tin liên lạc 
@apiDescription Xác thực update thông tin liên lạc
@apiGroup Account
@apiVersion 1.0.0
@apiName UpdateUserInfoContactVerify

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Body:)     {String}    [email]                            Email yêu cầu đăng ký tài khoản.
@apiParam      (Body:)     {String}    [phone]                            Số điện thoại yêu cầu đăng ký tài khoản.
@apiParam      (Body:)     {String}     code                                Mã xác thực.


@apiParam      (Query:)    {String}   merchant_id                               ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>
@apiParam      (Query:)    {String}   app_code                          Phiên bản phần mềm
@apiParam      (Query:)    {String}   pd_id                             Phần mềm máy ID

@apiParamExample {json} Body example:
{
	"phone":"**********",
	"code":"caOUbc"
}

@apiParamExample {json} Body example:
{
  "email": "<EMAIL>",
  "code":"caOUbc"
}

@apiSuccessExample {json} Response success
{
    "code": 200,
    "message": "request thành công..",
}
"""

# ***********************************************************************
# ************************ Lấy thông tin của profile ************************
# * Version: 1.0.0                                                      *
# ***********************************************************************
"""
@api {Get} /profile/detail Lấy thông tin của profile
@apiGroup Account
@apiVersion 1.0.0
@apiName GetDetailProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam      (Query:)     {String}    app_code     Mã phần mềm: client fix: SAKUKO


@apiSuccess  {String} address             Địa chỉ khách hàng
@apiSuccess  {String} avatar              Ảnh avatar
@apiSuccess  {String} birthday            Ngày sinh. Format: <code>YYYY-MM-DD</code>
@apiSuccess  {String} created_time        Thời điểm tạo (theo giờ UTC).
@apiSuccess  {String} phone_number        Số điện thoại
@apiSuccess  {String} email               Email
@apiSuccess  {Number} gender              Giới tính khách hàng. <br/>
Allowed values:<br/>
<li><code>1: Unknown</code></li>
<li><code>2: Male</code></li>
<li><code>3: Female</code></li>
@apiSuccess  {String} profile_id          ID Khách hàng
@apiSuccess  {String} updated_time        Thời điểm cập nhật
@apiSuccess  {Number} voucher             Số voucher chưa dùng 
@apiSuccess  {Number} point               Điểm thưởng

@apiSuccess  {Object} card                Thẻ

@apiSuccess  (card:)    {String}          card_name                Tên thẻ
@apiSuccess  (card:)    {String}          code                     Mã thẻ
@apiSuccess  (card:)    {String}          card_id                  Id hạng thẻ
@apiSuccess  {string}   identify_type     Loại giấy tờ định danh
@apiSuccess  {string}   identify_code     Mã giấy tờ định danh
@apiSuccess  {string}   province_code     Mã tỉnh thành


@apiSuccessExample {json} Response success
{
    "code": 200,
    "data": {
        "address": "",
        "birthday": null,
         "card": {
            "card_id": "4fac1a40-ba74-4cb3-9fba-a0fe4ea0d79f",
            "card_name": "Thẻ TIêu chuẩn ",
            "code": "6949705554859"
         },
        "created_time": "2019-06-04T08:02:53Z",
        "email": null,
        "gender": 2,
        "name": "Hoang Nam Vu",
        "phone_number": "+84367024969",
        "point": null,
        "profile_id": "5ae059e8-348e-4a0a-80fe-2734fedfcfd4",
        "updated_time": "2019-06-04T08:02:53Z",
        "voucher": 0,
        "identify_type": "CCCD",
        "identify_code": "*********",
        "province_code": null,
    },
    "message": "request thành công."
}
"""

#************************ Log in by apple   *********************************
#* version: 1.0.0                                                              *
#*******************************************************************************

"""
@api {post} /login/apple Dịch vụ đăng nhập qua tài khoản apple
@apiDescription Dịch vụ đăng nhập bằng tài khoản apple
@apiGroup Account
@apiVersion 1.0.0
@apiName LoginApple

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Header:)   {String}        X-Merchant-ID   Định danh của tenant.

@apiParam      (Query:)     {String}       merchant_id     ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>

@apiParam      (Query:)     {String}       pd_id           Phần mềm máy ID
@apiParam      (Query:)     {String}       app_code        Mã phần mềm: client fix: XLOYALTY | XLTVENDOR

@apiParam      (Body:)     {String}     token              Access token


@apiParamExample {json} Body example:
{
  "token": "access token"
}


@apiSuccess  {Array}    address              Địa chỉ khách hàng
@apiSuccess  {String}   email               Email



@apiSuccessExample {json} Đăng nhập thành công
{
    "email": "<EMAIL>",
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ijWSSwXq0jabbQR524KR2XihXR6Nw9mU-FR4bd6zZPU",
    "updated_time": "2019-05-10T03:17:58.298Z",
}

"""

#************************ Đăng nhập không dùng tài khoản   *********************
#* version: 1.0.0                                                              *
#*******************************************************************************

"""
@api {post} /login/not/account  Đăng nhập không dùng tài khoản
@apiDescription Dịch vụ đăng nhập không dùng tài khoản
@apiGroup Account
@apiVersion 1.0.0
@apiName LoginNotAcount

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam      (Query:)     {String}       merchant_id     ID của merchant. Client fix <code>********-f185-49df-a514-52feb459c3c3</code>

@apiParam      (Query:)     {String}       device_id       ID thiết bị
@apiParam      (Query:)     {String}       app_code        Mã phần mềm: client fix: MOBIO_APP | SAKUKO_APP


@apiSuccess  {Object}    Data              Thông tin đăng nhập
@apiSuccess  (Data)     {String}    app_code               Mã phần mềm: client fix: MOBIO_APP | SAKUKO_APP
@apiSuccess  (Data)     {String}    device_id              ID thiết bị
@apiSuccess  (Data)     {String}    device_id              ID thiết bị
@apiSuccess  (Data)     {String}    merchant_id            ID Merchant
@apiSuccess  (Data)     {String}    token                  Token dùng để đăng nhập


@apiSuccessExample {json} Đăng nhập thành công
{
  "code": 200,
  "data": {
    "app_code": "MOBIO_APP",
    "device_id": "2046E405-6103-4B96-892B-9201405E8CA9",
    "iat": 1596621521.303744,
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkZXZpY2VfaWQiOiIyMDQ2RTQwNS02MTAzLTRCOTYtODkyQi05MjAxNDA1RThDQTkiLCJtZXJjaGFudF9pZCI6IjFiOTliZGNmLWQ1ODItNGY0OS05NzE1LTFiNjFkZmZmMzkyNCIsImFwcF9jb2RlIjoiTU9CSU9fQVBQIiwiaWF0IjoxNTk2NjIxNTIxLjMwMzc0NH0.3E4S_ilr6TkAF2L9R_62SCI5XSlHJlV3PhRhyiA-DDk"
  },
  "message": "request thành công."
}

"""


#*******************************************************************************
#********************* Dịch vụ đăng nhập sau khi đăng ký ***************
#* version: 1.0.0                                                              *
#*******************************************************************************
"""
@api {post} /verification/code/register Dịch vụ xác thực sau khi đăng ký
@apiDescription Dịch vụ xác thực sau khi đăng ký
@apiGroup Account
@apiVersion 1.0.0
@apiName VerificationCodeRegister

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam      (Query:)     {String}   merchant_id                       ID của merchant. 

@apiParam      (Query:)     {String}   [app_version]                     Phiên bản phần mềm
@apiParam      (Query:)     {String}   pd_id                             Phần mềm máy ID
@apiParam      (Query:)     {String}   app_code                          Mã phần mềm: client fix: SAKUKO


@apiParam      (Body:)     {String}    username                          Tên đăng nhập
@apiParam      (Body:)     {String}    password                          Mật khẩu đăng nhập


@apiParamExample {json} Body example:
{
  "username": "+************",
  "password": "abcxyz"
}

@apiSuccess  {Array} address              Địa chỉ khách hàng
@apiSuccess  {String} avatar              Ảnh avatar
@apiSuccess  {String} birthday            Ngày sinh. Format: <code>YYYY-MM-DD</code>
@apiSuccess  {String} created_time        Thời điểm tạo (theo giờ UTC).
@apiSuccess  {String} phone_number        Số điện thoại
@apiSuccess  {String} email               Email
@apiSuccess  {Number} gender              Giới tính khách hàng. <br/>
Allowed values:<br/>
<li><code>1: Unknown</code></li>
<li><code>2: Male</code></li>
<li><code>3: Female</code></li>
@apiSuccess  {String} profile_id          ID Khách hàng
@apiSuccess  {String} updated_time        Thời điểm cập nhật
@apiSuccess  {String} token               Json web token
@apiSuccess  {Number} voucher             Số voucher chưa dùng 
@apiSuccess  {Number} point               Điểm thưởng

@apiSuccess  {Object} card                Thẻ

@apiSuccess  (card:)    {String}          card_name                Tên thẻ
@apiSuccess  (card:)    {String}          code                     Mã thẻ

@apiSuccessExample {json} Đăng nhập thành công
{
    "address": null,
    "birthday": null,
    "card": {
        "card_name": null,
        "code": null
    },
    "code": 200,
    "created_time": "2019-03-30T03:46:20.898Z",
    "email": null,
    "gender": 2,
    "iat": 1558434881.9332848,
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "message": "request thành công.",
    "name": "Trần Huy Tiệp",
    "phone_number": "+84363335020",
    "point": null,
    "profile_id": "c264ac2b-e4cd-467f-b44e-e0d101e7c5e8",
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ijWSSwXq0jabbQR524KR2XihXR6Nw9mU-FR4bd6zZPU",
    "updated_time": "2019-05-10T03:17:58.298Z",
    "voucher": 4
}

@apiSuccessExample {json} Sai username/mật khẩu
{
  "code": 412,
  "message": "Số điện thoại/email hoặc mật khẩu không chính xác. Vui lòng kiểm tra lại."
}

"""

#*******************************************************************************
#********************* Dịch vụ xác thực sau khi reset mật khẩu ***************
#* version: 1.0.0                                                              *
#*******************************************************************************
"""
@api {post} /verification/code/reset/password Dịch vụ xác thực sau khi reset mật khẩu
@apiDescription Dịch vụ xác thực sau khi reset mật khẩu
@apiGroup Account
@apiVersion 1.0.0
@apiName VerificationCodeResetPassword

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam      (Query:)     {String}   merchant_id                       ID của merchant. 

@apiParam      (Query:)     {String}   [app_version]                     Phiên bản phần mềm
@apiParam      (Query:)     {String}   pd_id                             Phần mềm máy ID
@apiParam      (Query:)     {String}   app_code                          Mã phần mềm: client fix: SAKUKO


@apiParam      (Body:)     {String}    username                          Tên đăng nhập
@apiParam      (Body:)     {String}    password                          Mật khẩu đăng nhập


@apiParamExample {json} Body example:
{
  "username": "+************",
  "password": "abcxyz"
}

@apiSuccess  {Array} address              Địa chỉ khách hàng
@apiSuccess  {String} avatar              Ảnh avatar
@apiSuccess  {String} birthday            Ngày sinh. Format: <code>YYYY-MM-DD</code>
@apiSuccess  {String} created_time        Thời điểm tạo (theo giờ UTC).
@apiSuccess  {String} phone_number        Số điện thoại
@apiSuccess  {String} email               Email
@apiSuccess  {Number} gender              Giới tính khách hàng. <br/>
Allowed values:<br/>
<li><code>1: Unknown</code></li>
<li><code>2: Male</code></li>
<li><code>3: Female</code></li>
@apiSuccess  {String} profile_id          ID Khách hàng
@apiSuccess  {String} updated_time        Thời điểm cập nhật
@apiSuccess  {String} token               Json web token
@apiSuccess  {Number} voucher             Số voucher chưa dùng 
@apiSuccess  {Number} point               Điểm thưởng

@apiSuccess  {Object} card                Thẻ

@apiSuccess  (card:)    {String}          card_name                Tên thẻ
@apiSuccess  (card:)    {String}          code                     Mã thẻ

@apiSuccessExample {json} Đăng nhập thành công
{
    "address": null,
    "birthday": null,
    "card": {
        "card_name": null,
        "code": null
    },
    "code": 200,
    "created_time": "2019-03-30T03:46:20.898Z",
    "email": null,
    "gender": 2,
    "iat": 1558434881.9332848,
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "message": "request thành công.",
    "name": "Trần Huy Tiệp",
    "phone_number": "+84363335020",
    "point": null,
    "profile_id": "c264ac2b-e4cd-467f-b44e-e0d101e7c5e8",
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ijWSSwXq0jabbQR524KR2XihXR6Nw9mU-FR4bd6zZPU",
    "updated_time": "2019-05-10T03:17:58.298Z",
    "voucher": 4
}

@apiSuccessExample {json} Sai username/mật khẩu
{
  "code": 412,
  "message": "Số điện thoại/email hoặc mật khẩu không chính xác. Vui lòng kiểm tra lại."
}

"""
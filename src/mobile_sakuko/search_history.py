#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: THONGNV
    Company: M O B I O
    Date Created: 7/31/20
"""

# ********************************************************************
# ************ L<PERSON>y lịch sử tìm kiếm của profile **********************
# ********************************************************************
"""
@api {GET} /search_history/action/get/list  Danh sách lịch sử tìm kiếm
@apiDescription Lấy danh sách lịch sử tìm kiếm của profile
@apiName ListSearchHistory
@apiGroup SearchHistory
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging

@apiUse merchant_id_header

@apiParam (Query:) {string}     merchant_id     UUID tenant lấy danh sách sản phẩm
@apiParam (Query:) {string}     app_code        Mã app
@apiParam (Query:) {string}     [search_text]   Nội dung tìm kiếm

@apiSuccess {Array}            data             Danh sách lịch sử tìm kiếm
@apiSuccess (data) {string}    content          Tên từ khoá tìm kiếm
@apiSuccess (data) {string}    id               ID của từ khoá tìm kiếm

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "content": "quần đùi thái",
      "id":"5f293da8bfaf21bc58b312ad"
    },
    {
      "content": "quần đùi Việt Nam",
      "id":"5f293da8bfaf21bc58b312ca"
    }
  ],
  "message": "request thành công."
}

"""

# ********************************************************************
# ************ Tạo lịch sử tìm kiếm của profile **********************
# ********************************************************************
"""
@api {POST} /search_history/action/add  Tạo lịch sử tìm kiếm
@apiDescription Tạo lịch sử tìm kiếm của profile
@apiName AddSearchHistory
@apiGroup SearchHistory
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging

@apiUse merchant_id_header

@apiParam (Query:) {string}     merchant_id     UUID tenant lấy danh sách sản phẩm
@apiParam (Query:) {string}     app_code        Mã app

@apiParam (Body:)  {string}     content         Từ khoá tìm kiếm


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}

"""

# ********************************************************************
# ************ Xoá lịch sử tìm kiếm của profile **********************
# ********************************************************************
"""
@api {DELETE} /search_history/action/delete  Xoá lịch sử tìm kiếm
@apiDescription Xoá lịch sử tìm kiếm của profile
@apiName DeleteSearchHistory
@apiGroup SearchHistory
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging

@apiUse merchant_id_header

@apiParam (Query:) {string}     merchant_id     UUID tenant lấy danh sách sản phẩm
@apiParam (Query:) {string}     app_code        Mã app

@apiParam (Body:)  {Array}     ids              Danh sách id các từ khoá cần tìm kiếm
@apiParam (Body:)  {String}    type_delete      Kiểu xoá. type_delete = 'all' xoá tất cả lịch sử tìm kiếm của profile

@apiParamExample  {json}  Example:
{
    "ids": ["5f23c2ea296977b31fb638c7"],
    "type_delete": "all"
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}

"""

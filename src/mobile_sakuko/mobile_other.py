#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: THONGNV
    Company: M O B I O
    Date Created: 9/1/20
"""

# *****************************************************************************
# ********************** lấy Danh sách các ca hẹn *********************
# *****************************************************************************
"""
@api {POST} /introduce_friends   Giới thiệu bạn bè
@apiDescription Lấy danh sách các ca hẹn
@apiName IntroduceFriends
@apiGroup MobileOther
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam      (Body:)     {Array}     friends              Danh sách bạn bè được giải thích
@apiParam      (friends:)  {String}    phone_number         Số điện thoại bạn bè
@apiParam      (friends:)  {String}    name                 Tên bạn bè
@apiParam      (friends:)  {String}    province_name        Tên tỉnh thành
@apiParam      (friends:)  {Number}    province_id          Mã tỉnh thành

@apiParamExample    {Body}      Body example:
{
    "friends": [
        {
            "phone_number": "0372528961",
            "name": "Nguyễn Ronaldo",
            "province_name": "Nghệ An",
            "province_id": 10
        }
    ]
}

@apiSuccess      {Object} data     Danh sách các ca hẹn
@apiSuccess      (data) {String}      id                   ID đặt lịch
@apiSuccess      (data:)  {String}    phone_number         Số điện thoại bạn bè
@apiSuccess      (data:)  {String}    name                 Tên bạn bè
@apiSuccess      (data:)  {String}    province_name        Tên tỉnh thành
@apiSuccess      (data:)  {Number}    province_id          Mã tỉnh thành
@apiSuccess      (data:)  {Number}    status               Trạng thái giới thiệu
                                                           <ul>
                                                                <li>1: Giới thiệu thành công</li> 
                                                                <li>2: Giới thiệu không thành công</li> 
                                                           </ul>
@apiSuccess      (data:)  {Number}    reason_code          Mã lỗi giới thiệu không thành công 
                                                           <ul>
                                                                <li>1: Số điện thoại đã là khách hàng</li> 
                                                                <li>2: Số điện thoại đã được giới thiệu</li> 
                                                           </ul>

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "5f4df991cfa16650e29635f9",
            "name": "Nguyễn Ronaldo",
            "phone_number": "0372528961",
            "province_id": 10,
            "province_name": "Nghệ An"
            "status": 2
            "reason_code": 1
        }
    ],
    "message": "request thành công."
}
"""
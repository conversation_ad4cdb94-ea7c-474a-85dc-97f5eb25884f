#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: THONGNV
    Company: M O B I O
    Date Created: 8/27/20
"""

# L<PERSON>y danh sách hạng thẻ

"""
@api {get} /card/list  Danh sách hạng thẻ
@apiDescription Dịch vụ lấy danh sách hạng thẻ, kèm tìm kiếm, sắ<PERSON> xếp
@apiGroup Card
@apiVersion 1.0.2
@apiName ListCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam    (Query:)     {String} [merchant_id]   tìm kiếm theo merchant_id
@apiParam	   (Query:)			{String}	[search_text]				    Chuỗi tìm kiếm
@apiParam	   (Query:)			{Integer}	[order_by]						Sắp xếp theo thuộc tính nào. Giá trị: 1=card_name, 2=condition, 3=expiry_in, 4=hold_condition
@apiParam	   (Query:)			{Integer}	[order_type]					Kiểu sắp xếp: Giá trị: 1=Tăng dần, 2=Giảm dần
@apiParam    (Query:)     {Integer=1-Visible 0-Hide -1-All} [status=-1]		Trạng thái hiển thị

@apiSuccess {Array}   datas    Danh sách hạng thẻ
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (datas) {String} 	id						    Mã định danh hạng thẻ
@apiSuccess (datas) {String} 	card_name				  Tên hạng thẻ
@apiSuccess (datas) {String} 	avatar					  Ảnh đại diện hạng thẻ
@apiSuccess (datas) {Integer}   condition    			Điều kiện thẻ
@apiSuccess (datas) {Integer}   hold_condition    	    Điều kiện giữ hạng thẻ
@apiSuccess (datas) {Integer}   expiry_in    			Thời hạn sử dụng thẻ. Đơn vị: Tháng
@apiSuccess (datas) {Integer}   generate_type			Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess (datas) {Integer}	approve_type			Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess (datas) {Integer}	status					  Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response list card example
{
  "datas": [{
  	"id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
  	"card_name": "Thẻ bạc",
  	"avatar": "https://avatar.url",
  	"condition": 1000,
  	"expiry_in": 12,
  	"generate_type": 1,
  	"approve_type": 2,
  	"status": 1
  }],
  "code": 200,
  "message": "request thành công"
}

"""

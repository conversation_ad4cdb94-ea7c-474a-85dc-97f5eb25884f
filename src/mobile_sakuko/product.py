# ********************************************************************
# ********************** L<PERSON>y danh sách sản phẩm **********************
# ********************************************************************
"""
@api {GET} /products Lấy danh sách sản phẩm
@apiDescription L<PERSON>y danh sách sản phẩm theo tenant
@apiName GetProductList
@apiGroup Product
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging

@apiParam (Query:) {string}     merchant_id UUID tenant lấy danh sách sản phẩm
@apiParam (Query:) {string}     [category]      M<PERSON> chức năng (danh mục) sản phẩm muốn lọc
@apiParam (Query:) {string}     [supplier_id]   UUID thương hiệu (hãng sản xuất sản phẩm)
@apiParam (Query:) {string}     [search]        Tìm kiếm theo tên/mã sản phẩm
@apiParam (Query:) {Integer}    [order_by]	    Sắp xếp theo thuộc tính nào. Giá trị: 1=name, 2=price
@apiParam (Query:) {Integer}	[order_type]    Kiểu sắp xếp: Giá trị: 1=Tăng dần, 2=Giảm dần
@apiParam (Query:) {Integer}	[interactive]   Kiểu tương tác: Giá trị: 1=View, 2=Like, 3=Favourite, 4=Rate, -1 là lấy tất cả sản phẩm có tương tác
. Không gửi interactive thì sẽ lấy tất cả sản phẩm có tương tác và không tương tác


@apiSuccess {Array} datas   Danh sách sản phẩm
@apiSuccess (datas) {string}         id      UUID sản phẩm
@apiSuccess (datas) {string}         name    Tên sản phẩm
@apiSuccess (datas) {string}         code    Mã sản phẩm
@apiSuccess (datas) {float}          price              Giá của sản phẩm
@apiSuccess (datas) {String}         currency_code      Đơn vị tiền mặt
@apiSuccess (datas) {String}         avatar             Ảnh đại diện
@apiSuccess (datas) {String}         created_time       Thời gian tạo sản phẩm
@apiSuccess (data)  {String}         desription         Mô tả sản phẩm

@apiSuccess {Array} data[images]                    Danh sách ảnh mô tả
@apiSuccess (data[images]) {string} id              UUID ảnh mô tả
@apiSuccess (data[images]) {string} link            link ảnh mô tả

@apiSuccess  {Object} datas[interactive]    Thông tin tương tác sản phẩm
@apiSuccess (datas[interactive]) {number}   number_favourite          số lượng yêu thích
@apiSuccess (datas[interactive]) {number}   number_like               số lượng thích
@apiSuccess (datas[interactive]) {number}   number_view               số lượng view
@apiSuccess (datas[interactive]) {number}   rate                      tỷ lệ đánh giá

@apiSuccess  {Object} datas[profile_interactive]    Thông tin profile tương tác sản phẩm
@apiSuccess (datas[profile_interactive]) {number}   is_favourite          Yêu thích
@apiSuccess (datas[profile_interactive]) {number}   is_like               Thích
@apiSuccess (datas[profile_interactive]) {number}   is_view               View
@apiSuccess (datas[profile_interactive]) {number}   number_rate           Đánh giá

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "datas": [
        {
            "id": "c7229324-ffe3-4090-8acb-27093003f703",
            "name": "Bình sữa cho Lộc cà ná",
            "code": "BSXYZ123",
            "avatar": "https://lh3.googleusercontent.com/-gl7FITDjU0M/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3rfErC03uIbV2sVO1zmjUxxOgjWxdA/s32-c-mo/photo.jpg",
            "price": 300000.0,
            "currency_code": "VND",
            "created_time": "2019-08-11T12:00:00Z",
            "interactive":{
                "number_favourite": 0,
                "number_like": 0,
                "number_comment": 0,
                "number_view": 0,
                "rate": 3.5,
            },
            "images":[
                {
                    "id": "af25367-ffe3-4090-8acb-27093003f703",
                    "link": "https://lh3.googleusercontent.com/-gl7FITDjU0M/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3rfErC03uIbV2sVO1zmjUxxOgjWxdA/s32-c-mo/photo.jpg",
                },
                ...
            ]
        },
        ....
    ],
    "paging":{
        "page":1,
        "per_page":15,
        "total_page":1,
        "total_item":10
    }
    "message": "request thành công."
}

"""

# ********************************************************************
# ********************** Lấy chi tiết sản phẩm **********************
# ********************************************************************
"""
@api {GET} /products/detail/<product_id> Lấy chi tiết sản phẩm
@apiDescription Lấy chi tiết sản phẩm
@apiName GetDetailProduct
@apiGroup Product
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {string}     id      UUID sản phẩm
@apiSuccess (data) {string}     name    Tên sản phẩm
@apiSuccess (data) {string}     code    Mã sản phẩm
@apiSuccess (data) {float}       price              Giá của sản phẩm
@apiSuccess (data) {String}      currency_code      Đơn vị tiền mặt
@apiSuccess (data) {String}      avatar             Ảnh đại diện
@apiSuccess (data) {String}      created_time       Thời gian tạo sản phẩm
@apiSuccess (data) {String}      supplier_id        UUID thương hiệu sản phẩm
@apiSuccess (data) {String}      catetory_id        UUID danh mục sản phẩm
@apiSuccess (data) {String}      desription         Mô tả sản phẩm

@apiSuccess {Array} data[images]                    Danh sách ảnh mô tả
@apiSuccess (data[images]) {string} id              UUID ảnh mô tả
@apiSuccess (data[images]) {string} link            link ảnh mô tả

@apiSuccess {Object} data[interactive]      Thông tin tương tác sản phẩm
@apiSuccess (datas[interactive]) {number}   number_favourite          số lượng yêu thích
@apiSuccess (datas[interactive]) {number}   number_like               số lượng thích
@apiSuccess (datas[interactive]) {number}   number_comment            số lượng bình luận
@apiSuccess (datas[interactive]) {number}   number_view               số lượng view
@apiSuccess (datas[interactive]) {number}   rate                      tỷ lệ đánh giá

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": 
        {
            "id": "c7229324-ffe3-4090-8acb-27093003f703",
            "name": "Bình sữa cho Lộc cà ná",
            "code": "BSXYZ123",
            "avatar": "https://lh3.googleusercontent.com/-gl7FITDjU0M/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3rfErC03uIbV2sVO1zmjUxxOgjWxdA/s32-c-mo/photo.jpg",
            "price": 300000.0,
            "currency_code": "VND",
            "supplier_id": "c7229324-ffe3-4090-8acb-225573a69",
            "catetory_id": "25366a2-ffe3-4090-8acb-225573a69",
            "desription": "",
            "interactive":{
                "number_favourite": 0,
                "number_like": 0,
                "number_comment": 0,
                "number_view": 0,
                "rate": 3.5,
            }
            "images":[
                {
                    "id": "af25367-ffe3-4090-8acb-27093003f703",
                    "link": "https://lh3.googleusercontent.com/-gl7FITDjU0M/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3rfErC03uIbV2sVO1zmjUxxOgjWxdA/s32-c-mo/photo.jpg",
                },
                ...
            ]
            "created_time": "2019-08-11T12:00:00Z",
        },
    ,
    "message": "request thành công."
}
"""

# *****************************************************************************
# ********************** Tạo tương tác sản phẩm **********************
# *****************************************************************************
"""
@api {POST} /products/interactive/<product_id> Tạo tương tác sản phẩm
@apiDescription Tạo tương tác sản phẩm
@apiName InteractiveProduct
@apiGroup Product
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam      (Body:)     {Number}    value                Giá trị. 0(false) hoăc 1(true) với interactive_type là 1, 2, 3. 0 <= value <= 5  với interactive_type là 4 
@apiParam      (Body:)     {Number}    interactive_type     Loại tương tác. 1-like, 2-view, 3-favourite, 4-rate

@apiParamExample {json} Body example
{   
	"value": 0,
	"interactive_type": 1,
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": 
        {
            'product_id': "aaaaaaaa",
            'number_favourite': 1,
            'number_like': 1,
            'number_view': 1,
            'rate': 2.5
        }
    ,
    "message": "request thành công."
}
"""

# *****************************************************************************
# ********************** lấy thông tin tương tác sản phẩm *********************
# Lý do: Tách product ra làm module riêng nên phần tương tác không join từ bảng product được nữa
# *****************************************************************************
"""
@api {GET} /products/action/get/interactive/<product_id> Lấy thông tin tương tác sản phẩm
@apiDescription Lấy thông tin tương tác sản phẩm
@apiName GetInteractiveProduct
@apiGroup Product
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Object} data        Thông tin tương tác sản phẩm
@apiSuccess (data) {number}   number_favourite          số lượng yêu thích
@apiSuccess (data) {number}   number_like               số lượng thích
@apiSuccess (data) {number}   number_comment            số lượng bình luận
@apiSuccess (data) {number}   number_view               số lượng view
@apiSuccess (data) {number}   rate                      tỷ lệ đánh giá
@apiSuccess (data) {Object}   profile_interactive       Đánh giá sản phẩm của profile
@apiSuccess (profile_interactive) {number}   is_favourite       Đã yêu thích?
@apiSuccess (profile_interactive) {number}   is_like            Đã thích?
@apiSuccess (profile_interactive) {number}   is_view            Đã view?
@apiSuccess (profile_interactive) {number}   number_rate        Điểm đánh giá



@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "number_favourite": 0,
    "number_like": 0,
    "number_view": 35,
    "profile_interactive": {
      "is_favourite": 0,
      "is_like": 0,
      "is_view": 0,
      "number_rate": 0
    },
    "rate": 0
  },
  "message": "request thành công."
}
"""

# *****************************************************************************
# ********************** lấy danh sách sản phẩm yêu thích *********************
# *****************************************************************************
"""
@api {GET} /products/favourite Danh sách sản phẩm yêu thích
@apiDescription Danh sách sản phẩm yêu thích
@apiName GetProductFavourite
@apiGroup Product
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam   (Query:)     {String}    [search]   Tên sản phẩm ;
@apiParam   (Query:)     {String}    [categories_id]   Danh sách danh mục cách nhau bởi dấu ;

@apiSuccess {Object} data        Danh sách sản phẩm yêu thích
@apiSuccess (data) {String}   avatar          Url ảnh đại diện sản phẩm
@apiSuccess (data) {String}   id              ID sản phẩm
@apiSuccess (data) {String}   name            Tên sản phẩm


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "avatar": "https:....",
      "id": "5f278b0e939255b8fbc2d9d1",
      "name": "test lần 2 30/08"
    },
    {
      "avatar": null,
      "id": "5f27d738e25a10c4a90ea61b",
      "name": "Test SP Vòng đời 02"
    }
  ],
  "message": "request thành công.",
  "paging": {
    "page": 1,
    "per_page": 10,
    "total_count": 2,
    "total_page": 1
  }
}
"""




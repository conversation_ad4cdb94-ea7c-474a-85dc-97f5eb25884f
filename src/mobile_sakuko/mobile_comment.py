# ********************************************************************
# ********************** Tạo Comment  **********************
# ********************************************************************
"""
@api {post} /comments/add Tạo Comment
@apiVersion 1.0.1
@apiName AddCommnet
@apiGroup Mobile Comment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Form:)  {Files}  [images] Danh sách file của ảnh mô tả cho comment
@apiParam   (Form:)  {string}  [object_id] là Id cụ thể của Nhà cung cấp hoặc Sản phẩm hoặc Khuyến mãi hoặc cửa hàng.
@apiParam   (Form:)  {string}  [object_type] Mỗi đối tượng có nhiều loại cụ thể khác nhau,
 nên bản có thêm trường này để xác đinh thuộc 4 loại sau :
  <code>CUSTOMER</code>, <code>STAFF</code>,<code>MERCHANT</code>,<code>PRODUCT</code>
@apiParam   (Form:)  {string}  [commenter_id] ID của tài khoản commment
@apiParam   (Form:)  {int}  [commenter_type] xác định ngừoi trả lời là staff hay khách hàng.
 <code>1</code> : khách hàng, <code>2</code> : staff
@apiParam   (Form:)  {string}  [content] Nội dung comment
@apiParam   (Form:)  {int}  [repl_status] trạng thái được trả lời;
<code>1</code>: chưa được trả lời; <code>2</code>: đã được trả lời
@apiParam   (Form:)  {int}  [status] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiParam   (Form:)  {int}  [merchant_id] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiParam   (Form:)  {decimal} avg_rated Điểm đánh giá trung bình (có giá trị thấp nhất là 0.0 và cao nhất là 5.0)
@apiParam   (Form:)  {string}  [repl_comment_id] ID của comment được trả lời trong trường hợp comment đẩy lên là comment trả lời
@apiParam   (Form:)  {string}  [page_social_id] Mã định danh app
@apiParam   (Form:)  {number}  [social_type]    Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:)  {string} comment_id ID của comment

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "comment_id": "uuid"
  }
}
"""
==================
"""
@api {post} /comments/add Tạo Comment
@apiVersion 1.0.0
@apiName AddCommnet
@apiGroup Mobile Comment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Form:)  {Files}  [images] Danh sách file của ảnh mô tả cho comment
@apiParam   (Form:)  {string}  [object_id] là Id cụ thể của Nhà cung cấp hoặc Sản phẩm hoặc Khuyến mãi hoặc cửa hàng.
@apiParam   (Form:)  {string}  [object_type] Mỗi đối tượng có nhiều loại cụ thể khác nhau,
 nên bản có thêm trường này để xác đinh thuộc 4 loại sau :
  <code>CUSTOMER</code>, <code>STAFF</code>,<code>MERCHANT</code>,<code>PRODUCT</code>
@apiParam   (Form:)  {string}  [commenter_id] ID của tài khoản commment
@apiParam   (Form:)  {int}  [commenter_type] xác định ngừoi trả lời là staff hay khách hàng.
 <code>1</code> : khách hàng, <code>2</code> : staff
@apiParam   (Form:)  {string}  [content] Nội dung comment
@apiParam   (Form:)  {int}  [repl_status] trạng thái được trả lời;
<code>1</code>: chưa được trả lời; <code>2</code>: đã được trả lời
@apiParam   (Form:)  {int}  [status] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiParam   (Form:)  {int}  [merchant_id] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiParam   (Form:)  {decimal} avg_rated Điểm đánh giá trung bình (có giá trị thấp nhất là 0.0 và cao nhất là 5.0)
@apiParam   (Form:)  {string}  [repl_comment_id] ID của comment được trả lời trong trường hợp comment đẩy lên là comment trả lời

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:)  {string} comment_id ID của comment

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "comment_id": "uuid"
  }
}
"""

# ********************************************************************
# ********************** Lấy danh sách comment  **********************
# ********************************************************************
"""
@api {get} /comments/list Lấy danh sách comment
@apiVersion 1.0.1
@apiName ListComment 
@apiGroup Mobile Comment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging


@apiParam   (Query:)  {string}  [object_id] id của đối tượng muốn lấy comment 
@apiParam   (Query:)  {string}  [object_type] <ul>kiểu đối tượng muốn lấy comment Trong đó:</ul>
<li><code>MERCHANT</code> - Kiểu nhà cung cấp </li>
<li><code>STORE</code> - Kiểu đối tượng là cửa hang  </li>
<li><code>PRODUCT</code> - Kiểu đối tượng là sản phẩm </li>
<li><code>VOUCHER</code> - Kiểu đối tượng là voucher </li>
@apiParam   (Query:)  {string}  [repl_comment_id] id của đối tượng muốn lấy comment trả lời 
@apiParam   (Query:)  {string}  [page_social_id] Mã định danh app
@apiParam   (Query:)  {number}  [social_type]    Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>


@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:)  {string} id ID của comment
@apiSuccess   (data:)  {array}  [images] Danh sách link của ảnh mô tả cho comment
@apiSuccess   (data:)  {string}  [object_id] là Id cụ thể của Nhà cung cấp hoặc Sản phẩm hoặc Khuyến mãi hoặc cửa hàng.
@apiSuccess   (data:)  {string}  [object_type] Mỗi đối tượng có nhiều loại cụ thể khác nhau,
 nên bản có thêm trường này để xác đinh thuộc 4 loại sau :
  <code>CUSTOMER</code>, <code>STAFF</code>,<code>MERCHANT</code>,<code>PRODUCT</code> 
@apiSuccess   (data:)  {string}  [commenter_id] ID của tài khoản commment
@apiSuccess   (data:)  {int}  [commenter_type] xác định ngừoi trả lời là staff hay khách hàng.
 <code>1</code> : khách hàng, <code>2</code> : staff
@apiSuccess   (data:)  {string}  [content] Nội dung comment
@apiSuccess   (data:)  {int}  [repl_status] trạng thái được trả lời;
<code>1</code>: chưa được trả lời; <code>2</code>: đã được trả lời
@apiSuccess   (data:)  {int}  [merchant_id] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiSuccess   (data:)  {decimal} avg_rated Điểm đánh giá trung bình (có giá trị thấp nhất là 0.0 và cao nhất là 5.0)
@apiSuccess   (data:)  {int} count_repl Số comment trả lời 
@apiSuccess   (data:)  {object} profile Thông tin của user comment 
@apiSuccess   (data:)  {string} profile...name  Tên của user 
@apiSuccess   (data:)  {string} profile...avatar Link avatar của user 
@apiSuccess   (data:)  {string} replies Danh sách các câu trả lời của 
@apiSuccess   (data:)  {string} replies...name  Tên của user 
@apiSuccess   (data:)  {string} replies...avatar Link avatar của user 
@apiSuccess   (data:)  {string} replies...images Danh sách link ảnh mô tả của user 
@apiSuccess   (data:)  {string} replies...content Nội dung trả lời của user 
@apiSuccess   (data:)  {string} replies...created_time Thời gian trả lời 
@apiSuccess   (data:)   {string}  page_social_id Mã định danh app
@apiSuccess   (data:)   {number}  social_type    Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[{

            "avg_rate": 3.5,
            "commenter_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "commenter_type": "STAFF",
            "content": null,
            "count_repl": 0,
            "created_time": "Fri, 22 Mar 2019 02:18:03 GMT",
            "id": "c8bd7fa8-34f2-4541-8878-505f0ffa0bc2",
            "images": [
                "https://mbodevstorage.file.core.windows.net/loyalty/images/0a148fd7-6c3d-4895-b75a-7f5b0108523c?se=2027-06-08T02%3A18%3A02Z&sp=r&sv=2018-03-28&sr=s&sig=u3rq210vOwVBSzktIGUIQpEcgzQBdQ9CDP4B%2B/RtHUM%3D",
                "https://mbodevstorage.file.core.windows.net/loyalty/images/4a02d8fe-13f7-488e-9f12-1c490674ecdf?se=2027-06-08T02%3A18%3A03Z&sp=r&sv=2018-03-28&sr=s&sig=adfu9g9h2h2lPTrLU%2BFVwtidpxz7BdPKHP4DSFdg8zE%3D"
            ],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "update_time": "Fri, 22 Mar 2019 02:18:03 GMT",
            "profile":{
                "name":"Ten nguoi dung",
                "avatar":"https://mbodevstorage.file.core.windows.net/loyalty/images/0a148fd7-6c3d-4895-b75a-7f5b0108523c?se=2027-06-08T02%3A18%3A02Z&sp=r&sv=2018-03-28&sr=s&sig=u3rq210vOwVBSzktIGUIQpEcgzQBdQ9CDP4B%2B/RtHUM%3D"
            },
            "replies":[
                "name":"Ten nguoi dung",
                "avatar":"https://mbodevstorage.file.core.windows.net/loyalty/images/0a148fd7-6c3d-4895-b75a-7f5b0108523c?se=2027-06-08T02%3A18%3A02Z&sp=r&sv=2018-03-28&sr=s&sig=u3rq210vOwVBSzktIGUIQpEcgzQBdQ9CDP4B%2B/RtHUM%3D",
                "content":"Noi dung phan hoi",
                "created_time":"Fri, 22 Mar 2019 02:18:03 GMT",
                "images":[]
              }
            ],
            "page_social_id": "SAKUKO",
            "social_type": 5
        }
  ...],
  "paging":{
    "page":1,
    "per_page":10,
    "total_page":1,
    "total_item":10
  }
}
"""
======================
"""
@api {get} /comments/list Lấy danh sách comment
@apiVersion 1.0.0
@apiName ListComment 
@apiGroup Mobile Comment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging


@apiParam   (Query:)  {string}  [object_id] id của đối tượng muốn lấy comment 
@apiParam   (Query:)  {string}  [object_type] <ul>kiểu đối tượng muốn lấy comment Trong đó:</ul>
<li><code>MERCHANT</code> - Kiểu nhà cung cấp </li>
<li><code>STORE</code> - Kiểu đối tượng là cửa hang  </li>
<li><code>PRODUCT</code> - Kiểu đối tượng là sản phẩm </li>
<li><code>VOUCHER</code> - Kiểu đối tượng là voucher </li>
@apiParam   (Query:)  {string}  [repl_comment_id] id của đối tượng muốn lấy comment trả lời 


@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:)  {string} id ID của comment
@apiSuccess   (data:)  {array}  [images] Danh sách link của ảnh mô tả cho comment
@apiSuccess   (data:)  {string}  [object_id] là Id cụ thể của Nhà cung cấp hoặc Sản phẩm hoặc Khuyến mãi hoặc cửa hàng.
@apiSuccess   (data:)  {string}  [object_type] Mỗi đối tượng có nhiều loại cụ thể khác nhau,
 nên bản có thêm trường này để xác đinh thuộc 4 loại sau :
  <code>CUSTOMER</code>, <code>STAFF</code>,<code>MERCHANT</code>,<code>PRODUCT</code> 
@apiSuccess   (data:)  {string}  [commenter_id] ID của tài khoản commment
@apiSuccess   (data:)  {int}  [commenter_type] xác định ngừoi trả lời là staff hay khách hàng.
 <code>1</code> : khách hàng, <code>2</code> : staff
@apiSuccess   (data:)  {string}  [content] Nội dung comment
@apiSuccess   (data:)  {int}  [repl_status] trạng thái được trả lời;
<code>1</code>: chưa được trả lời; <code>2</code>: đã được trả lời
@apiSuccess   (data:)  {int}  [merchant_id] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiSuccess   (data:)  {decimal} avg_rated Điểm đánh giá trung bình (có giá trị thấp nhất là 0.0 và cao nhất là 5.0)
@apiSuccess   (data:)  {int} count_repl Số comment trả lời 
@apiSuccess   (data:)  {object} profile Thông tin của user comment 
@apiSuccess   (data:)  {string} profile...name  Tên của user 
@apiSuccess   (data:)  {string} profile...avatar Link avatar của user 
@apiSuccess   (data:)  {string} replies Danh sách các câu trả lời của 
@apiSuccess   (data:)  {string} replies...name  Tên của user 
@apiSuccess   (data:)  {string} replies...avatar Link avatar của user 
@apiSuccess   (data:)  {string} replies...images Danh sách link ảnh mô tả của user 
@apiSuccess   (data:)  {string} replies...content Nội dung trả lời của user 
@apiSuccess   (data:)  {string} replies...created_time Thời gian trả lời 


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[{

            "avg_rate": 3.5,
            "commenter_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "commenter_type": "STAFF",
            "content": null,
            "count_repl": 0,
            "created_time": "Fri, 22 Mar 2019 02:18:03 GMT",
            "id": "c8bd7fa8-34f2-4541-8878-505f0ffa0bc2",
            "images": [
                "https://mbodevstorage.file.core.windows.net/loyalty/images/0a148fd7-6c3d-4895-b75a-7f5b0108523c?se=2027-06-08T02%3A18%3A02Z&sp=r&sv=2018-03-28&sr=s&sig=u3rq210vOwVBSzktIGUIQpEcgzQBdQ9CDP4B%2B/RtHUM%3D",
                "https://mbodevstorage.file.core.windows.net/loyalty/images/4a02d8fe-13f7-488e-9f12-1c490674ecdf?se=2027-06-08T02%3A18%3A03Z&sp=r&sv=2018-03-28&sr=s&sig=adfu9g9h2h2lPTrLU%2BFVwtidpxz7BdPKHP4DSFdg8zE%3D"
            ],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "update_time": "Fri, 22 Mar 2019 02:18:03 GMT",
            "profile":{
                "name":"Ten nguoi dung",
                "avatar":"https://mbodevstorage.file.core.windows.net/loyalty/images/0a148fd7-6c3d-4895-b75a-7f5b0108523c?se=2027-06-08T02%3A18%3A02Z&sp=r&sv=2018-03-28&sr=s&sig=u3rq210vOwVBSzktIGUIQpEcgzQBdQ9CDP4B%2B/RtHUM%3D"
            },
            "replies":[
                "name":"Ten nguoi dung",
                "avatar":"https://mbodevstorage.file.core.windows.net/loyalty/images/0a148fd7-6c3d-4895-b75a-7f5b0108523c?se=2027-06-08T02%3A18%3A02Z&sp=r&sv=2018-03-28&sr=s&sig=u3rq210vOwVBSzktIGUIQpEcgzQBdQ9CDP4B%2B/RtHUM%3D",
                "content":"Noi dung phan hoi",
                "created_time":"Fri, 22 Mar 2019 02:18:03 GMT",
                "images":[]
              }
            ]
        }
  ...],
  "paging":{
    "page":1,
    "per_page":10,
    "total_page":1,
    "total_item":10
  }
}
"""
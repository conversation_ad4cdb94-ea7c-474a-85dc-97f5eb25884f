# ********************************************************************
# ********************** L<PERSON>y danh sách thông báo **********************
# ********************************************************************
"""
@api {GET} /notifies Lấy danh sách thông báo
@apiDescription Lấy danh sách thông báo
@apiName GetNotifiesList
@apiGroup Notifies
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging

@apiParam (Query:) {string}     [status]        Trạng thái của thông báo: Giá trị: 0-UNOPENDED 1-OPENDED 2-DELETED. Không gửi sẽ lấy các thông báo chưa đọc.
@apiParam (Query:) {string}     [start_time]    Thời gian thông báo post  .Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam (Query:) {string}     [end_time]      Thời gian kết thúc thông báo post .Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {array} notifies  Danh sách thông báo


@apiSuccess (notifies) {string} id              UUID của thông bào
@apiSuccess (notifies) {string} status          Trạng thái của thông báo: Giá trị: 0-UNOPENDED 1-OPENDED 2-DELETED.
@apiSuccess (notifies) {string} type            Loại thông báo.
<li><b>TRANSACTION</b> </li>
<li><b>VOUCHER</b> Thông báo về voucher</li>
<li><b>PRODUCT</b> Thông báo về sản phẩm</li>
<li><b>CARD</b> Thông báo về thẻ thành viên </li>
<li><b>ORDER</b> Thông báo liên quan đến đơn hàng</li>
<li><b>NEWS</b> Thông báo liên quan đến tin tức</li>
<li><b>UPDATE</b> Thông báo cập nhật</li>
<li><b>OTHER</b>   Thông báo kh</li>
@apiSuccess (notifies) {array}  fields          Mảng key, value thương ứng với một fields trong format_content.
@apiSuccess (notifies) {string} pushed_time     Thời gian nhận thông bào.
@apiSuccess (notifies) {string} body            Body thông bào.
@apiSuccess (notifies) {string} created_time    Thời gian tạo thông bào.
@apiSuccess (notifies) {int} order_status       Trạng thái đơn hàng với type=ORDER
@apiSuccess (notifies) {int} point              Điểm


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "notifies": [
            {
                "content": "",
                "created_time": "2019-06-15T01:23:17Z",
                "fields": [
                    {
                        "key": "code",
                        "value": "abdcn123"
                    }
                ],
                "id": "6dbc17cb-3b14-4565-8214-1e47c8bb73fa",
                "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
                "pushed_time": "2019-06-15T12:00:00Z",
                "status": 0,
                "title": "Trạng thài đơn hàng abdcn123",
                "type": "ORDER",
                "order_status": 3,
                "point": null
            },
            ...

        ],
        "paging": {
            "page": 1,
            "per_page": 15,
            "total_count": 10,
            "total_page": 1
        }
    },
    "message": "request thành công."
}

"""

# ********************************************************************
# ********************** Xóa thông báo **********************
# ********************************************************************
"""
@api {DELETE} /notifies Xóa thông báo
@apiDescription Xóa thông báo
@apiName DeleteNotifies
@apiGroup Notifies
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam      (Body:)     {String}    ids              Tập hợp các UUID của thông báo.
@apiParam      (Body:)     {String}    all              Xóa tất cả thông báo. true hoặc false.

@apiParamExample {json} Body example
{
	"ids": [
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703"
	],
	"all": "true"
}

"""

# ********************************************************************
# ********************** Cập nhập trạng thái thông báo **********************
# ********************************************************************
"""
@api {PUT} /notifies/status Cập nhập trạng thái thông báo
@apiDescription Cập nhập trạng thái thông báo
@apiName UpdateNotifies
@apiGroup Notifies
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam      (Body:)     {ArrayString}    ids              Tập hợp các UUID của thông báo.
@apiParam      (Body:)     {Boolean}    all             Xóa tất cả thông báo. <code>true</code> hoặc <code>false</code>.
@apiParam      (Body:)     {Number}    status           Trạng thái của thông báo: Giá trị: 0-UNOPENDED 1-OPENDED 2-DELETED.

@apiParamExample {json} Body example
{
    "status": 1,
	"ids": [
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703"
	],
	"all": true 
}

"""

# ********************************************************************
# ********************** Lấy danh sách số lượng thông báo **********************
# ********************************************************************
"""
@api {GET} /notifies/count Lấy danh sách số lượng thông báo
@apiDescription Lấy danh sách số lượng thông báo
@apiName GetNotifiesCount
@apiGroup Notifies
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {string}     [status]        Trạng thái của thông báo: Giá trị: 0-UNOPENDED 1-OPENDED. Không gửi sẽ lấy các sô lượng thông báo
có trạng thái UNOPENDED, OPENDED

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {array}  Danh sách thông báo


@apiSuccess (data) {int} status          Trạng thái của thông báo: Giá trị: 0-UNOPENDED 1-OPENDED 
@apiSuccess (data) {int} count           Số lượng


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "count": 12,
            "status": 0
        }
    ],
    "message": "request thành công."
}

"""

****************************************************************************************
**********************************Notify RATING OBJECT**********************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {POST} /rating Gửi yêu cầu đánh giá theo đối tượng
@apiName PostNotifyRating
@apiGroup Notifies
@apiVersion 1.0.0

@apiParam  {string} object_id UUID đối tượng cần đánh giá
@apiParam  {string=NORMAL UNDERESTIMATE} type Kiểu đánh giá
@apiParam  {number} [rate] Điểm đánh giá. Gửi lên với <code>type=NORMAL</code>
@apiParam  {string} [ref_id] UUID item được chọn. Đi kèm với <code>type=UNDERESTIMATE</code>
@apiParam  {string} [note] Text note đi cùng với <code>ref_id</code>
@apiParamExample {json} Body normal
{
    "object_id": "8f9c24b9-76f9-43ed-8413-76825269c3fd",
    "type": "NORMAL",
    "rate": 2
}

@apiParamExample {json} Body underestimate
{
    "object_id": "8f9c24b9-76f9-43ed-8413-76825269c3fd",
    "type": "UNDERESTIMATE",
    "ref_id": "16952ad9-c667-4bee-b958-b5dcda43cbc4",
    "note": "text note"
}
"""

****************************************************************************************
**********************************Notify RATING OBJECT**********************************
* version: 1.0.1                                                                    *
****************************************************************************************
"""
@api {POST} /rating Gửi yêu cầu đánh giá theo đối tượng
@apiName PostNotifyRating
@apiGroup Notifies
@apiVersion 1.0.1

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam  {string} object_id UUID đối tượng cần đánh giá
@apiParam  {number} [rate] Điểm đánh giá. 
@apiParamExample {json} Body Example
{
    "object_id": "8f9c24b9-76f9-43ed-8413-76825269c3fd",
    "rate": 2
}

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data)   {array}   reason   Danh sách lý do
@apiSuccess (data) {string}  reason.id       Mã định danh lý do
@apiSuccess (data) {string}  reason.name     Nội dung lý do 
@apiSuccess (data)   {string}  status   Trạng thái đánh giá <code>NORMAL: Hoàn thành, UNDERESTIMATE: Gửi danh sách lý do </code>
@apiSuccess (data)   {string}  ref_id   UUID đánh giá
@apiSuccess (data)   {string}  [title]   Nội dung thông báo yêu cầu khách hàng cho biết lý do đánh giá thấp

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "reason": [
            {
                "id": "002eb601-1a90-469f-be1f-1e2f81c79194",
                "name": "Nhân viên không làm việc, chỉ ngồi một chỗ"
            },
            {
                "id": "00f15341-0121-4600-b2ed-1a95c89afd96",
                "name": "Cửa hàng không có chỗ gửi xe"
            },
            ...
        ],
        "status": "UNDERESTIMATE",
        "ref_id": "6449bbdc-0de3-40ba-a67a-adc405238052"
        "title": "Vui lòng cho biết lý do đánh giá thấp"
    }
    "message": "request thành công."
}


"""

****************************************************************************************
**********************************Send Reason underestimate**********************************
* version: 1.0.0                                                                 *
****************************************************************************************
"""
@api {POST} /rating/reason Gửi lý do đánh giá thâp
@apiName PostRatingReason
@apiGroup Notifies
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam  {string} ref_id UUID  đánh giá
@apiParam  {string} [reason_id] UUID lý do 
@apiParam  {string} [note] ghi chú lý do 
@apiParamExample {json} Body Example
{
    "ref_id": "6449bbdc-0de3-40ba-a67a-adc405238052",
    "reason_id": "0077524b-3438-4b04-9db9-f6633ccea142"
    "note": "Tôi không hài lòng ..."
}

@apiSuccess {String}  message  Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}

"""
****************************************************************************************
**********************************Check rating**********************************
* version: 1.0.0                                                                 *
****************************************************************************************
"""
@api {POST} /rating/check Kiểm tra đánh giá giao dịch
@apiName GetCheckRating
@apiGroup Notifies
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam  {string} object_id UUID  đối tượng cần đánh giá
@apiParamExample {json} Body Example
{
    "object_id": "6449bbdc-0de3-40ba-a67a-adc405238052",
}

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi
 
@apiSuccess (data)   {string}  status   Trạng thái đánh giá <code>RATED: ĐÃ đánh giá, YET_RATED: Chưa đánh giá </code>
@apiSuccess (data)   {string}  object_id   UUID đối tượng cần đánh giá
@apiSuccess (data)   {string}  [title]   Thông báo yêu cầu khách hàng đánh giá chất lượng dịch vụ

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{   
    "data":{
        "status": "RATED",
        "object_id": "6449bbdc-0de3-40ba-a67a-adc405238052", 
        "title": "Vui lòng cho biết mức độ hài lòng của bạn về chất lượng dịch vụ" 
    },
    "code": 200,
    "message": "request thành công."
}

"""



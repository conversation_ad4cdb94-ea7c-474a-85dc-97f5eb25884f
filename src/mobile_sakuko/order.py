# ********************************************************************
# ********************** L<PERSON>y danh sách đơn hàng **********************
# ********************************************************************
"""
@api {GET} /orders Lấy danh sách đơn hàng
@apiDescription Lấy danh sách đơn hàng
@apiName GetOrderList
@apiGroup Order
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging

@apiParam (Query:) {string}     [merchant_id]   UUID tenant
@apiParam (Query:) {string}     [start_time]    Thời gian đơn hàng post filter .Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam (Query:) {string}     [end_time]      Thời gian kết thúc đơn hàng filter .Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam (Query:) {string}     [search]        Tìm kiếm theo mã đơn hàng
@apiParam (Query:) {Integer}	[order_by]	    Sắp xếp theo thuộc tính nào. Giá trị: 2=created_time type text
@apiParam (Query:) {Integer}	[order_type]    Kiểu sắp xếp: Giá trị: 1=Tăng dần, 2=Giảm dần
@apiParam (Query:) {Integer}	[status]        Trạng thái của đơn dàng: Giá trị:0-WAIT, 1-RECEIVED, 2-DELIVER, 3-ACCOMPLISHED, 4-CANCEL. Không gửi sẽ lấy All.

@apiSuccess {Array}   datas  Danh sách đơn hàng
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data)   {string}   day     Ngày tạo đơn hàng
@apiSuccess (data)   {Array}    orders  Danh sách đơn hàng theo ngày

@apiSuccess (orders) {string} id UUID đơn hàng
@apiSuccess (orders) {string} code Mã đơn hàng
@apiSuccess (orders) {string} created_time Thời gian tạo đơn hàng
@apiSuccess (orders) {float} amount Tổng giá trị của đơn hàng
@apiSuccess (orders) {string} currency_code Đơn vị thanh toán
@apiSuccess (orders) {number= 0-WAIT, 1-RECEIVED, 2-DELIVER, 3-ACCOMPLISHED, 4-CANCEL} status Trạng thái của đơn hàng. <code>WAIT: Chờ xử lý</code>, <code>RECEIVED: Đã tiếp nhận</code>, <code>DELIVER: Đang giao hàng</code>,<code>ACCOMPLISHED: Hoàn tất</code>, <code>CANCEL: Hủy bỏ</code>

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "datas": [
        {
            "day":"Fri, 22 Mar 2019",
            "orders":[
                {
                    "id": "c7229324-ffe3-4090-8acb-27093003f703",
                    "code": "BSXYZ123"
                    "amount": 300000.0,
                    "currency_code": "VND",
                    "status": 1,
                    "created_time": "2019-08-11T12:00:00Z",
                },
                ....
            ]
        }
        ....
    ],
    "paging":{
        "page":1,
        "per_page":15,
        "total_page":1,
        "total_item":10
      }
    "message": "request thành công."
}

"""


# ********************************************************************
# ********************** Lấy chi tiết đơn hàng **********************
# ********************************************************************
"""
@api {GET} /orders/detail/<order_id> Lấy chi tiết đơn hàng
@apiDescription Lấy chi tiết đơn hàng
@apiName GetDetailOrder
@apiGroup Order
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {string} id                  UUID đơn hàng
@apiSuccess (data) {string} code                Mã đơn hàng
@apiSuccess (data) {string} created_time        Thời gian tạo đơn hàng
@apiSuccess (data) {string} update_time         Thời gian cập nhập đơn hàng
@apiSuccess (data) {float}  amount              Tổng giá trị của đơn hàng
@apiSuccess (data) {string} currency_code       Đơn vị thanh toán
@apiSuccess (data) {number= 0-WAIT, 1-RECEIVED, 2-DELIVER, 3-ACCOMPLISHED, 4-CANCEL} status Trạng thái của đơn hàng. <code>WAIT: Chờ xử lý</code>, <code>RECEIVED: Đã tiếp nhận</code>, <code>DELIVER: Đang giao hàng</code>,<code>ACCOMPLISHED: Hoàn tất</code>, <code>CANCEL: Hủy bỏ</code>

@apiSuccess  {Array} data[products]                 Danh sách sản phẩm của đơn hàng
@apiSuccess (data[products]) {string} id            UUID sản phẩm
@apiSuccess (data[products]) {string} name          Tên sản phẩm
@apiSuccess (data[products]) {float}  price         Giá của sản phẩm
@apiSuccess (data[products]) {number} quantity      Số lượng của sản phẩm
@apiSuccess (data[products]) {string} currency_code Đơn vị thanh toán
@apiSuccess (data[products]) {string} avatar        Ảnh đại diện cho sản phẩm

@apiSuccess  {Object} data[contact_information]             Thông tin liên hệ
@apiSuccess (data[contact_information]) {string} name       Tên người nhận
@apiSuccess (data[contact_information]) {string} email      Email người nhận
@apiSuccess (data[contact_information]) {string} phone      Số điện thoại người nhận
@apiSuccess (data[contact_information]) {string} address    Địa chỉ người nhận

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": 
        {
            "id": "c7229324-ffe3-4090-8acb-27093003f703",
            "code": "BSXYZ123"
            "update_time": "2019-08-11T12:00:00Z",
            "created_time": "2019-08-11T12:00:00Z",
            "amount": 300000.0,
            "currency_code": "VND",
            "status": 1,
            "note": "Giao hàng trong giờ hành chính",
            "products":[
                {
                    "id": "c7229324-ffe3-4090-8acb-27093003f703",
                    "name": "Áo sơ mi tay ngáy",
                    "price":300000.0,
                    "quantity":5,
                    "currency_code":"VND",
                    "avatar":"https://axample.com/abc",
                },
                ....
            ],
            "contact_information":{
                "name":"Trang Nguyen",
                "phone":"0123456789",
                "email":"trannguyenthi@gmail",
                "address":"Cầu Giấy, Hà Nội"
            }
        },
    ,
    "message": "request thành công."
}

"""


# ********************************************************************
# ********************** Cập nhập giỏ hàng **********************
# ********************************************************************
"""
@api {PUT} /orders/shop_card/update Cập nhập giỏ hàng
@apiDescription Cập nhập giỏ hàng
@apiName UpdateOrder
@apiGroup Order
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam      (Body:)     {Array}     [vouchers]                       Danh sách UUID voucher kèm theo.
@apiParam      (Body:)     {String}    [note]                           Ghi chú của đơn hàng.
@apiParam      (Body:)     {Number}    [status]                           Trạng thái đơn hàng.{number= -1-DRAFT, 0-WAIT,} status Trạng thái của đơn hàng. <code>WAIT: Chờ xử lý</code> <code>DRAFT: Nháp chưa đặt đơn hàng</code>
@apiParam      (Body:)     {Array}     [products_delete]                Danh sách UUID sản phẩm xóa.

@apiParam {Array} Body[products] Thông tin sản phẩm cập nhập
@apiParam (Body[products]) {string} product_id      ID UUID của sản phẩm.
@apiParam (Body[products]) {string} quantity        Số lượng của sản phẩm.

@apiParam {Object} Body[contact_information]        Thông tin liên hệ.
@apiParam (Body[contact_information]) {string} phone           Số điện thoại.
@apiParam (Body[contact_information]) {string} email           Email.
@apiParam (Body[contact_information]) {string} address         Địa chỉ.
@apiParam (Body[contact_information]) {string} name            Tên.
@apiParam (Body[contact_information]) {string} [description]   Mô tả kèm theo.


@apiParamExample {json} Body example
{
	"vouchers": ["5a2415fa-ffe3-4090-8acb-ab124d3","5a2415fa-ffe3-4090-8acb-ab124d4"],
    "note": "Giao hàng trước 8h",
    "status": -1,
	"products":[
        {
            "product_id":"c7229324-ffe3-4090-8acb-2709303f7a",
            "quantity":5,
        },
        ...
	],
	"products_delete": ["c7229324-ffe3-4090-8acb-2709303f7a","b7229324-ffe3-4090-8acb-2709303f7a"],
	"contact_information": {
	    "phone": "0123456789",
	    "email": "<EMAIL>",
	    "address": "Cầu Giấy, Hà Nội",
	    "name": "Vô Danh",
	    "description": ""
	}
}
"""


# ********************************************************************
# ********************** Thêm sản phẩm vào giỏ hàng **********************
# ********************************************************************
"""
@api {POST} /orders/shop_card/add_product Thêm sản phẩm vào giỏ hàng
@apiDescription Thêm sản phẩm vào giỏ hàng
@apiName AddProductOrder
@apiGroup Order
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500


@apiParam {Array} Body[products] Thông tin sản phẩm cập nhập
@apiParam (Body[products]) {string} product_id      ID UUID của sản phẩm.
@apiParam (Body[products]) {string} quantity        Số lượng của sản phẩm.

@apiSuccess {Array} data    Thông tin sản phẩm trong giỏ hàng 
@apiSuccess (data) {string}     id           ID UUID của sản phẩm.
@apiSuccess (data) {number}     quantity    Số lượng sản phẩm giỏ hàng  


@apiParamExample {json} Body example
{
	"products":[
	    {
            "product_id":"c7229324-ffe3-4090-8acb-2709303f7a",
            "quantity":5,
	    },
	    ...
	]
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "38b996e6-587e-44dd-b1dd-833ba5a2fc40",
            "quantity": 4
        }
    ],
    "message": "request thành công."
}

"""


# ********************************************************************
# ********************** Lấy thông tin giỏ hàng **********************
# ********************************************************************
"""
@api {GET} /orders/shop_card Lấy thông tin giỏ hàng
@apiDescription Lấy thông tin giỏ hàng
@apiName GetDetailShopCard
@apiGroup Order
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {string} id                  UUID đơn hàng
@apiSuccess (data) {string} code                Mã đơn hàng
@apiSuccess (data) {string} created_time        Thời gian tạo đơn hàng
@apiSuccess (data) {string} update_time         Thời gian cập nhập đơn hàng
@apiSuccess (data) {float}  amount              Tổng giá trị của đơn hàng
@apiSuccess (data) {string} currency_code       Đơn vị thanh toán

@apiSuccess  {Array} data[products]                 Danh sách sản phẩm của đơn hàng
@apiSuccess (data[products]) {string} id            UUID sản phẩm
@apiSuccess (data[products]) {string} name          Tên sản phẩm
@apiSuccess (data[products]) {float}  price         Giá của sản phẩm
@apiSuccess (data[products]) {number} quantity      Số lượng của sản phẩm
@apiSuccess (data[products]) {string} currency_code Đơn vị thanh toán
@apiSuccess (data[products]) {string} avatar        Ảnh đại diện cho sản phẩm

@apiSuccess  {Object} data[contact_information]             Thông tin liên hệ
@apiSuccess (data[contact_information]) {string} name       Tên người nhận
@apiSuccess (data[contact_information]) {string} email      Email người nhận
@apiSuccess (data[contact_information]) {string} phone      Số điện thoại người nhận
@apiSuccess (data[contact_information]) {string} address    Địa chỉ người nhận

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": 
        {
            "id": "c7229324-ffe3-4090-8acb-27093003f703",
            "code": "BSXYZ123"
            "update_time": "2019-08-11T12:00:00Z",
            "created_time": "2019-08-11T12:00:00Z",
            "amount": 300000.0,
            "currency_code": "VND",
            "note": "Giao hàng trong giờ hành chính",
            "products":[
                {
                    "id": "c7229324-ffe3-4090-8acb-27093003f703",
                    "name": "Áo sơ mi tay ngáy",
                    "price":300000.0,
                    "quantity":5,
                    "currency_code":"VND"
                    "avatar": "https://axample.com/abc"
                },
                ....
            ],
            "contact_information":{
                "name":"Trang Nguyen",
                "phone":"0123456789",
                "email":"trannguyenthi@gmail",
                "address":"Cầu Giấy, Hà Nội"
            }
        },
    "message": "request thành công."
}

"""

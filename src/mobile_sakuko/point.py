# ******************************************************************************************
# **************************** Tặng điểm khi ghé thăm cửa hàng *****************************
# v1.0
# ******************************************************************************************
"""
@api {POST} /point/visit_store Tặng điểm khi ghé thăm cửa hàng
@apiDescription Tặng điểm khi ghé thăm cửa hàng
@apiVersion 1.0.0
@apiName GiftPointVisitStore
@apiGroup Point

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}  store_code  Code của cửa hàng.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
}

"""

# **************** Lấy thông tin điểm của Profile **

"""
@api {GET} /point/profile/info Lấy thông tin điểm của profile
@apiDescription Lấy thông tin điểm của profile
@apiVersion 1.0.0
@apiName GetPointProfile
@apiGroup Point

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiSuccess {Object}     data   Kết quả xử lý   
@apiSuccess (data)  {Number} point              Số điểm tiêu dùng hiện có
@apiSuccess (data)  {Number} rank_point         Số điểm xét hạng hiện có
@apiSuccess (data)  {Array}  list_point         Danh sách điểm tiêu dùng
@apiSuccess (data)  {String}  list_point.expire_date         Thời hạn điểm
@apiSuccess (data)  {String}  list_point.point               Số điểm tương ứng với thời hạn
@apiSuccess (data)  {Array}  list_rank_point         Danh sách điểm xét hạng
@apiSuccess (data)  {String}  list_rank_point.expire_date         Thời hạn điểm
@apiSuccess (data)  {String}  list_rank_point.point               Số điểm tương ứng với thời hạn
@apiSuccess (data)  {Object}  warning         Cảnh báo trạng thái thẻ
@apiSuccess (data)  {String}  warning.status         Trạng thái cảnh báo
                                                    <ul>
                                                        <li><code>no_warning </code>: Khách hàng đã đạt đủ điều kiện lên hạng và chỉ còn chờ ngày xét </li>
                                                        <li><code>max </code>: Khách hàng đã đạt hạng thẻ tối đa </li>
                                                        <li><code>down </code>: Cảnh báo xuống hạng thẻ </li>
                                                        <li><code>up </code>: Cảnh báo cần số điểm để lên hạng </li>
                                                        <li><code>errror </code>: Không tính được điều kiện thay đổi hạng thẻ</li>
                                                    </ul>
@apiSuccess (data)  {Number}  warning.point_warning         Số điểm tương ứng cần thông báo
@apiSuccess (data)  {Number}  warning.date_reviews          Thời gian xét hạng thẻ


@apiSuccess {String}    message Mô tả phản hồi
@apiSuccess {Integer}   code    Mã phản hồi

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "list_point": [
            {
                "expire_date": null,
                "point": 15005
            }
        ],
        "list_rank_point": [
            {
                "expire_date": null,
                "point": 14855
            }
        ],
        "point": 14855,
        "rank_point": 15005,
        "warning": {
            "date_reviews": "Fri, 18 Sep 2020 00:00:00 GMT",
            "point_warning": 0,
            "status": "no_warning"
        }
        
    },
    "message": "request thành công."
}
"""

# **************** Tặng điểm cho bạn bè **

"""
@api {POST} /transfer_point Tặng điểm cho bạn bè
@apiDescription Tặng điểm cho bạn bè
@apiVersion 1.0.0
@apiName GivePointFriend
@apiGroup Point

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Header:)   {String}    X-Merchant-ID   Định danh của tenant.

@apiParam   (Body:)   {String}  receiver_phone  Số điện thoại tài khoản nhận
@apiParam   (Body:)   {Number}  transfer_point  Số điểm chuyển nhượng

@apiParamExample  {json}  Example
{
    "receiver_phone":"0372528961", 
    "transfer_point": 4
}


@apiSuccess {Object}     data   Kết quả xử lý   
@apiSuccess {String}    message Mô tả phản hồi
@apiSuccess {Integer}   code    Mã phản hồi

@apiSuccess (data)  {String} receiver_phone     Số điện thoại người nhận
@apiSuccess (data)  {Number} transfer_point     Số điểm tặng


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "receiver_phone": "0372528961",
        "transfer_point": 4
    },
    "message": "request thành công."
}
"""
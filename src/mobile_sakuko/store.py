************************ List Province ************************
* version: 1.0.0                                              *
***************************************************************
"""
@api {get} /stores/provinces Lấy danh sách tỉnh thành
@apiDescription Dịch vụ lấy danh sách tỉnh thành có cửa hàng.
@apiGroup Store
@apiVersion 1.0.0
@apiName StoreListProvince

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "provinces": [
    "HANOI",
    "HOCHIMINH"
  ]
}
"""

# ********************************************************************
# ********************** Chi tiết cửa hàng      **********************
# ********************************************************************

"""
@api {get} /stores/<store_id> Chi tiết cửa hàng
@apiDescription Chi tiết cửa hàng
@apiVersion 1.1.0
@apiGroup Store
@apiName DetailStore

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500


@apiSuccess  {object}  store Đối tượng thông tin chi tiết cửa hàng
@apiSuccess (store:) {string}  id ID của cửa hàng
@apiSuccess (store:) {string}  name Tên cửa hàng
@apiSuccess (store:) {string}  code Mã của cửa hàng
@apiSuccess (store:) {string}  latitude Vĩ độ của cửa hàng
@apiSuccess (store:) {string}  longitude Kinh độ của cửa hàng
@apiSuccess (store:) {string}  email Địa chỉ email của cửa hàng
@apiSuccess (store:) {string}  phone_number Số điện thoại của cửa hàng
@apiSuccess (store:) {string}  province_code Mã tỉnh thành của cửa hàng

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "store": {
    "id":"120bfsd-b3hb4hm-343g24fg-dhasgdhasdjn4",
    "name": "Cửa hàng Vinmart số 1 Duy Tân",
    "code":"VINMART01DUYTAN",
    "latitude":102.23127837875,
    "longitude": 21.2312387875675438,
    "email": "<EMAIL>",
    "phone_number":"0123456789",
    "province_code": "HANOI"
  },
  "message": "request thành công."
}
"""

# ********************************************************************
# ********************** Checkin tại cửa hàng   **********************
# ********************************************************************

"""
@api {get} /stores/<store_id>/checkin Checkin tại cửa hàng
@apiDescription Checkin tại cửa hàng
@apiVersion 1.1.0
@apiGroup Store
@apiName CheckinStore

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500


@apiSuccess  {object}  store Đối tượng thông tin chi tiết cửa hàng
@apiSuccess (store:) {string}  id ID của cửa hàng
@apiSuccess (store:) {string}  name Tên cửa hàng
@apiSuccess (store:) {string}  code Mã của cửa hàng
@apiSuccess (store:) {string}  latitude Vĩ độ của cửa hàng
@apiSuccess (store:) {string}  longitude Kinh độ của cửa hàng
@apiSuccess (store:) {string}  email Địa chỉ email của cửa hàng
@apiSuccess (store:) {string}  phone_number Số điện thoại của cửa hàng
@apiSuccess (store:) {string}  province_code Mã tỉnh thành của cửa hàng

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "store": {
    "id":"120bfsd-b3hb4hm-343g24fg-dhasgdhasdjn4",
    "name": "Cửa hàng Vinmart số 1 Duy Tân",
    "code":"VINMART01DUYTAN",
    "latitude":102.23127837875,
    "longitude": 21.2312387875675438,
    "email": "<EMAIL>",
    "phone_number":"0123456789",
    "province_code": "HANOI"
  },
  "message": "request thành công."
}
"""

# ********************** Lấy danh sách cửa hàng  ********************** version: 1.0.0                                                   *
# ********************************************************************

"""
@api {post} /stores Lấy danh sách cửa hàng theo merchant
@apiDescription Lấy danh sách cửa hàng theo merchant :<br>
Lấy danh sách tỉnh thành theo API <a href="https://dev.mobio.vn/crm/api/v1.0/static/provinces.json"> https://dev.mobio.vn/crm/api/v1.0/static/provinces.json</a>
@apiVersion 1.0.0
@apiGroup Store
@apiName GetStore

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging

@apiUse order_sort

@apiParam  {array} [province_codes] Danh sách mã tỉnh thành
@apiParam  {string} [address]  Địa chỉ cửa hàng cần tìm kiếm
@apiParam  {array} [status]  Trạng thái cửa hàng cần tìm kiếm

@apiParamExample    {Body}      Body example:
{
    "province_codes":["HANOI","HOCHIMINH",...],
    "address":"Vinmart",
    "status":[1,2]
}


@apiSuccess  {array}  data Danh sách các cửa hàng được tìm thấy
@apiSuccess (Data:) {string}  id Uuid của cửa hàng
@apiSuccess (Data:) {string}  name Tên cửa hàng
@apiSuccess (Data:) {string}  address Địa chỉ cửa hàng
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
     "id": "0237ca1d-f751-4095-9469-ac0f21624a50",
     "name":"Cua hang so mot",
     "address": "2/82 Duy Tan Cau Giay Ha Noi"
    }
  ],
  "message": "request thành công."
}
"""

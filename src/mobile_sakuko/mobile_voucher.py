# ********************************************************************
# ********************** L<PERSON>y danh sách voucher  **********************
# ********************************************************************
"""
@api {post} /vouchers Lấy danh sách voucher
@apiVersion 1.0.0
@apiDescription Lấy danh sách voucher
@apiName GetVouches
@apiGroup Mobile Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam (Body:) {array} [categories] Danh sách danh mục muốn lọc.
@apiParam (Body:) {array} [stories] Danh sách các cửa hàng  muốn lọc theo cửa hàng
@apiParam (Body:) {array} [province_codes] Danh sách các mã tỉnh thành muốn lọc theo VD province_codes = ["HANOI"]
@apiParam (Body:) {string} [start_time] Thời gian bắt đầu filter .Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam (Body:) {string} [end_time] Thời gian kết thúc filter .Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam (Body:) {array} status Filter theo trạng thái của voucher. VD: <code>status=[1]</code><br/>Allow values: <code>-1-DEACTIVE 0-DRAFT 1-ACTIVE</code>
@apiParam (Body:) {int} [order_by] <ul>Sắp xếp theo thuộc tính nào. Giá trị:</ul>
<li><b>1</b>  Sắp xếp theo thời điểm bắt đầu</li>
<li><b>2</b>  Sắp xếp theo thời điểm kết thúc </li>
<li><b>3</b>  Sắp xếp theo điểm </li>
<li><b>4</b>  Sắp xếp theo thời gian tạo </li>
<li><b>5</b>  Sắp xếp theo mức phổ biến là số lượt xem của voucher </li>
<li><b>6</b>  Sắp xếp theo mức phổ biến là số lượt yêu thích của voucher </li>
<li><b>7</b>  Sắp xếp theo mức phổ biến là số lượt đánh giá của voucher </li>
<li><b>8</b>  Sắp xếp theo mức phổ biến là số lượt mua của voucher </li>



@apiParam (Body:) {string} [order_type] <ul>Kiểu sắp xếp: Giá trị:</ul>
 <li><b>1</b> Tăng dần</li>
 <li> <b>2</b> Giảm dần</li>
@apiParam (Body:) {int} [min_price] Điểm thấp nhất để đổi của voucher
@apiParam (Body:) {int} [max_price] Điểm cao nhất để đổi của voucher

@apiParamExample    {json}      Body example:
{
    "status" : [1],
    "province_codes": ["HANOI"]
}

@apiSuccess {Array}   data    Danh sách voucher
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:) {string} id UUID voucher
@apiSuccess (data:) {string} name Tên voucher
@apiSuccess (data:) {string} avatar Link ảnh đại diện cho voucher
@apiSuccess (data:) {number} discount Số lượng giảm
@apiSuccess (data:) {string=PERCENT MONEY} discount_type Kiểu giảm giá
@apiSuccess (data:) {string=VND USD} [discount_unit] Đơn vị giảm giá. Trả về khi <code>discount_type=MONEY</code>
@apiSuccess (data:) {string} start_time Thời gian bắt đầu voucher. Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess (data:) {string} end_time Thời gian kết thúc voucher. Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess (data:) {number=-1-DEACTIVE 0-DRAFT 1-ACTIVE} status Trạng thái của voucher. <code>DEACTIVE: Ẩn</code>, <code>ACTIVE: Hiển thị</code>, <code>DRAFT: nháp</code>
@apiSuccess (data:) {number} interactive <ul>Đối tượng mô tả trạng thái tương tác với voucher:</ul>
<li><b>favourite</b> <u>int</u> Số lượt yêu thích của voucher </li>
<li><b>likes</b> <u>int</u> Số lượng thích của voucher </li>
<li><b>is_like</b> <u>boolean</u> User có thích voucher hay chưa
@apiSuccess (data:) {int} price Số điểm cần để đổi lấy voucher
@apiSuccess (data:) {float} avg_rated Số điểm đánh giá của voucher
@apiSuccess (data:) {string} stores Danh sách các cửa hàng được áp dụng voucher
@apiSuccess (data:) {string} stores...name Tên cửa hàng được áp dụng voucher
@apiSuccess (data:) {string} stores...address Địa chỉ cửa hàng áp dụng voucher
@apiSuccess (data:) {float} stores...latitude Kinh độ của cửa hàng được áp dụng voucher
@apiSuccess (data:) {float} stores...longitude Vĩ độ của cửa hàng được áp dụng voucher

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "avatar": "https://mbodevstorage.blob.core.windows.net/images/6d08ee33-6230-46b5-98ce-58c3db013d86",
            "avg_rated": 0,
            "content": "<p>&Aacute;p dụng to&agrave;n hệ thống.</p>\n",
            "discount": 10,
            "discount_type": "PERCENT",
            "discount_unit": "%",
            "end_time": null,
            "id": "6d08ee33-6230-46b5-98ce-58c3db013d86",
            "interactive": {
                "favourite": 113,
                "likes": 0
            },
            "name": "[KHUYẾN MÃI LỚN] Giảm giá 10% toàn bộ cửa hàng",
            "price": 0,
            "price_unit": null,
            "start_time": "Tue, 05 Feb 2019 10:20:00 GMT",
            "status": 1,
            "stores": [
                {
                    "address": "128 Láng Hạ, Q.Đống Đa, Hà Nội",
                    "latitude": 21.01141,
                    "longitude": 105.81093,
                    "name": "An Phước - Cửa hàng Hà Nội 6 - Láng Hạ"
                }
            ]
        }
    ],
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 10,
        "total_count": 1,
        "total_page": 1
    }
}
"""

# ********************************************************************
# ********************** Lấy danh sách danh mục categories  **********************
# ********************************************************************

"""
@api {get} /categories Lấy danh sách categories
@apiVersion 1.0.0
@apiDescription Lấy danh sách categories
@apiName GetCategories
@apiGroup Mobile Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Array}   datas    Danh sách category
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (datas:) {string} id UUID category
@apiSuccess (datas:) {Object} name Tên category
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "datas": [
    {
      "id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
      "name": {
      "default":"Mẹ và bé",
      "vi":"Mẹ và bé ",
      "en":"Mother and baby "
      }
    },
    ...
  ]
}
"""

# ********************************************************************
# **********************Chi tiết  Voucher   **********************
# ********************************************************************

"""   
@api {get} /vouchers/<voucher_id>/detail  Lấy chi tiết Voucher                                                                                           
@apiVersion 1.0.0              
@apiDescription Lấy chi tiết Voucher        
@apiName DetailVoucher  
@apiGroup Mobile Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Object}   data  Nội dung trả về  
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess   (data:)  {string}   [name]       Danh sách các id của voucher cần delete  
@apiSuccess   (data:)  {array}   [category_ids]       Uuid của Category tương ứng  
 VD: "category_ids" : ["uuid1","uuid2",...]
@apiSuccess   (data:)  {string}   [discount_type]      <ul> Kiểu giảm giá:</ul> 
<li><b>PERCENT</b>-Phần trăm</li> 
<li><b>MONEY</b>-Giảm tiền</li>  

@apiSuccess   (data:)  {string}   [discount_unit]       <ul>Đơn vị của giá trị giảm giá:</ul>
<li><b> % </b>-Phần trăm </li>
<li><b>VND </b> Việt Nam đồng </li>
<li><b>USD </b></li>
@apiSuccess   (data:)  {string}   [avatar] Link avatar của voucher   
@apiSuccess   (data:)  {array}   [merchant_ids] Danh sách các merchant_id được áp dụng voucher 
VD: "merchant_ids" : ["uuid1","uuid2",...]
@apiSuccess   (data:)   {array} [store_ids] danh sách các cửa hàng được áp dụng voucher  
VD: "store_ids" : ["uuid1","uuid2",...]

@apiSuccess   (data:)  {int}   price       <ul>Số điểm để đổi </ul> 
<li>Nếu price =0 thì phát hành miễn phí </li> 
<li>Nếu price >0 thì phát hành kiểu đổi điểm </li>  
@apiSuccess   (data:)  {int}   bill_invoice_status      <ul> Được tặng theo giá trị hóa đơn </ul>
<li>Nếu <b>0</b> thì voucher phát hành kiểu tặng không kèm hóa đơn </li> 
<li>Nếu <b>1</b> thì voucher phát hành kiểu tặng kèm hóa đơn và 
phải có ít nhất 1 trong các giá trị <b>bill_invoice_start</b>,<b>bill_invoice_end</b></li>
@apiSuccess   (data:)  {int}    [bill_invoice_start] Giá trị thấp nhất của  hóa đơn được tính  
@apiSuccess   (data:)  {int}    [bill_invoice_end] Giá trị cao nhất của  hóa đơn được tính  
@apiSuccess   (data:)  {string} [bill_invoice_description] Mô tả hóa đơn được áp dụng   
@apiSuccess   (data:)  {string} [content] Nội dung của voucher   
@apiSuccess   (data:)  {string} [tags] Gợi ý tìm kiếm voucher. VD: tags = 100k;tết    
@apiSuccess   (data:)  {array}  [brand_url] Danh sách link của ảnh mô tả cho voucher 
 
@apiSuccess   (data:)  {int}  [max_code]  Số lượng mã code toàn chương trình 


@apiSuccess   (data:)   {string} [start_time]  Thời gian bắt đầu phát hành voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess   (data:)   {string} [end_time]  Thời gian kết thúc  phát hành voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess   (data:)   {string} [display_start_time ] Thời gian bắt đầu hiển thị  voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess   (data:)   {string} [display_end_time] Thời gian bắt đầu hiển thị  voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>

@apiSuccess   (data:)   {float}  [avg_rated] Điểm trung bình đánh giá 

@apiSuccess   (data:)   {string}  [created_time] Thời gian tạo voucher 
@apiSuccess   (data:)   {string}  [created_user] Uuid của account tạo voucher
@apiSuccess   (data:)   {float}  [discount] Giá trị của voucher 

@apiSuccess   (data:)   {int}  [status] Trạng thái của voucher   
@apiSuccess   (data:)   {int}  [total_codes] Số mã voucher còn lại
@apiSuccess   (data:)   {int}  [time_get_per_profile] Thời gian khoảng cách giữa hai lấy mã voucher 
@apiSuccess   (data:)   {string}  [time_get_per_profile_type] Kiểu thời gian khoảng cách giữa lấy hai lần lấy mã voucher 
vd: second ,minute ,hour, day

@apiSuccess   (data:)   {int}  [max_code_per_profile] Số mã voucher được cấp tối đa cho một user  
@apiSuccess   (data:)   {int}  [max_code_per_day] Số mã voucher được cấp tối đa trong ngày 

@apiSuccess (data:) {number} interactive <ul>Đối tượng mô tả trạng thái tương tác với voucher:</ul>
<li><b>likes</b> <u>int</u> Số lượng thích của voucher </li>
<li><b>is_like</b> <u>boolean</u> User có thích voucher hay chưa  

@apiSuccess (data:) {ArrayObject} stores Danh sách các cửa hàng được áp dụng voucher
@apiSuccess (data:) {string} stores...name Tên cửa hàng được áp dụng voucher
@apiSuccess (data:) {string} stores...address Địa chỉ cửa hàng áp dụng voucher
@apiSuccess (data:) {float} stores...latitude Kinh độ của cửa hàng được áp dụng voucher
@apiSuccess (data:) {float} stores...longitude Vĩ độ của cửa hàng được áp dụng voucher
@apiSuccess (data:) {string} stores...phone_number Số điện thoại của cửa hàng 

@apiSuccess (data:) {ArrayObject} codes Danh sách mã code của voucher 
@apiSuccess (data:) {string} code...code Mã code của voucher 
@apiSuccess (data:) {string} code...expire_time Thời gian hết hạn của code 
@apiSuccess (data:) {int} code...status Trạng thái sử dụng của code 
@apiSuccess (data:) {string} code...created_time Thời gian cấp mã 
<code>1</code> Đã sử dụng </br>
<code>0</code> Chưa sử dụng </br>

@apiSuccessExample {json} Response: HTTP/1.1 200 OK                                                                                             
{     
  "code": 200,
  "message": "request thành công."    
  "data":{
      "name":"Voucher giảm giá hóa đơn từ 100K",
      "avatar": "https://mobio.vn/logo.png",
      "merchant_ids": ["6afff536-1fe1-4b89-86d9-401a6b899a14",...],
      "category_ids":["6afff536-1fe1-4b89-86d9-401a6b899a14",...]
      "discount_type":"PERCENT",
      "discount_unit:" "%",
      "price": "20",
      "bill_invoice_status":true,
      "bill_invoice_start":100000,
      "content":"Nội dung của voucher ",
      "brand_url":["https://mobio.bn/static/logo.png",...],
      "tags":"tết;100k",
      "stores": [
                {
                    "address": "128 Láng Hạ, Q.Đống Đa, Hà Nội",
                    "latitude": 21.01141,
                    "longitude": 105.81093,
                    "name": "An Phước - Cửa hàng Hà Nội 6 - Láng Hạ",
                    "phone_number":"0123456789"
                }
      ],
      "total_codes":100,
      "start_time": "2018-07-26T12:00:00Z",
      "end_time": "2019-08-11T12:00:00Z",
      "display_start_time": "2018-07-26T12:00:00Z",
      "display_end_time": "2019-08-11T12:00:00Z",
      "store_ids":["e5cfc5a9-e72e-4cea-942b-f27906677a0f","d018c7a4-9cd3-4afe-b204-ea08242d7bca",..],
      "audience_id":"e969f293-b126-42a8-90e4-0fcbd0c9f89e",
      "interactive":{
        "likes":123,
        "is_like":true 
      },
      "codes":[{
        "code":"1234567890123"
        "expiry_time": "2018-07-26T12:00:00Z",
        "status":1
      }]
  }                                                                                                          
}     
"""

# ********************************************************************
# **********************Chi tiết  Voucher   **********************
# ********************************************************************

"""   
@api {get} /vouchers/<voucher_id>/detail  Lấy chi tiết Voucher                                                                                           
@apiVersion 1.0.1              
@apiDescription Lấy chi tiết Voucher        
@apiName DetailVoucher  
@apiGroup Mobile Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Object}   data  Nội dung trả về  
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess   (data:)  {string}   [name]       Danh sách các id của voucher cần delete  
@apiSuccess   (data:)  {array}   [category_ids]       Uuid của Category tương ứng  
 VD: "category_ids" : ["uuid1","uuid2",...]
@apiSuccess   (data:)  {string}   [discount_type]      <ul> Kiểu giảm giá:</ul> 
<li><b>PERCENT</b>-Phần trăm</li> 
<li><b>MONEY</b>-Giảm tiền</li>  

@apiSuccess   (data:)  {string}   [discount_unit]       <ul>Đơn vị của giá trị giảm giá:</ul>
<li><b> % </b>-Phần trăm </li>
<li><b>VND </b> Việt Nam đồng </li>
<li><b>USD </b></li>
@apiSuccess   (data:)  {string}   [avatar] Link avatar của voucher   
@apiSuccess   (data:)  {array}   [merchant_ids] Danh sách các merchant_id được áp dụng voucher 
VD: "merchant_ids" : ["uuid1","uuid2",...]
@apiSuccess   (data:)   {array} [store_ids] danh sách các cửa hàng được áp dụng voucher  
VD: "store_ids" : ["uuid1","uuid2",...]

@apiSuccess   (data:)  {int}   price       <ul>Số điểm để đổi </ul> 
<li>Nếu price =0 thì phát hành miễn phí </li> 
<li>Nếu price >0 thì phát hành kiểu đổi điểm </li>  
@apiSuccess   (data:)  {int}   bill_invoice_status      <ul> Được tặng theo giá trị hóa đơn </ul>
<li>Nếu <b>0</b> thì voucher phát hành kiểu tặng không kèm hóa đơn </li> 
<li>Nếu <b>1</b> thì voucher phát hành kiểu tặng kèm hóa đơn và 
phải có ít nhất 1 trong các giá trị <b>bill_invoice_start</b>,<b>bill_invoice_end</b></li>
@apiSuccess   (data:)  {int}    [bill_invoice_start] Giá trị thấp nhất của  hóa đơn được tính  
@apiSuccess   (data:)  {int}    [bill_invoice_end] Giá trị cao nhất của  hóa đơn được tính  
@apiSuccess   (data:)  {string} [bill_invoice_description] Mô tả hóa đơn được áp dụng   
@apiSuccess   (data:)  {string} [content] Nội dung của voucher   
@apiSuccess   (data:)  {string} [tags] Gợi ý tìm kiếm voucher. VD: tags = 100k;tết    
@apiSuccess   (data:)  {array}  [brand_url] Danh sách link của ảnh mô tả cho voucher 

@apiSuccess   (data:)  {int}  [max_code]  Số lượng mã code toàn chương trình 


@apiSuccess   (data:)   {string} [start_time]  Thời gian bắt đầu phát hành voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess   (data:)   {string} [end_time]  Thời gian kết thúc  phát hành voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess   (data:)   {string} [display_start_time ] Thời gian bắt đầu hiển thị  voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess   (data:)   {string} [display_end_time] Thời gian bắt đầu hiển thị  voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>

@apiSuccess   (data:)   {float}  [avg_rated] Điểm trung bình đánh giá 

@apiSuccess   (data:)   {string}  [created_time] Thời gian tạo voucher 
@apiSuccess   (data:)   {string}  [created_user] Uuid của account tạo voucher
@apiSuccess   (data:)   {float}  [discount] Giá trị của voucher 

@apiSuccess   (data:)   {int}  [status] Trạng thái của voucher   
@apiSuccess   (data:)   {int}  [total_codes] Số mã voucher còn lại
@apiSuccess   (data:)   {int}  [time_get_per_profile] Thời gian khoảng cách giữa hai lấy mã voucher 
@apiSuccess   (data:)   {string}  [time_get_per_profile_type] Kiểu thời gian khoảng cách giữa lấy hai lần lấy mã voucher 
vd: second ,minute ,hour, day

@apiSuccess   (data:)   {int}  [max_code_per_profile] Số mã voucher được cấp tối đa cho một user  
@apiSuccess   (data:)   {int}  [max_code_per_day] Số mã voucher được cấp tối đa trong ngày 

@apiSuccess (data:) {number} interactive <ul>Đối tượng mô tả trạng thái tương tác với voucher:</ul>
<li><b>likes</b> <u>int</u> Số lượng thích của voucher </li>
<li><b>is_like</b> <u>boolean</u> User có thích voucher hay chưa  

@apiSuccess (data:) {ArrayObject} stores Danh sách các cửa hàng được áp dụng voucher
@apiSuccess (data:) {string} stores...name Tên cửa hàng được áp dụng voucher
@apiSuccess (data:) {string} stores...address Địa chỉ cửa hàng áp dụng voucher
@apiSuccess (data:) {float} stores...latitude Kinh độ của cửa hàng được áp dụng voucher
@apiSuccess (data:) {float} stores...longitude Vĩ độ của cửa hàng được áp dụng voucher
@apiSuccess (data:) {string} stores...phone_number Số điện thoại của cửa hàng 

@apiSuccess (data:) {ArrayObject} codes Danh sách mã code của voucher 
@apiSuccess (data:) {string} code...code Mã code của voucher 
@apiSuccess (data:) {string} code...expire_time Thời gian hết hạn của code 
@apiSuccess (data:) {int} code...status Trạng thái sử dụng của code 
@apiSuccess (data:) {string} code...created_time Thời gian cấp mã 
<code>1</code> Đã sử dụng </br>
<code>0</code> Chưa sử dụng </br>

@apiSuccess   (data:)   {int}  [get_code_per_day] Số mã voucher được đã lấy trong ngày 
@apiSuccess   (data:)   {int}  [get_code_per_profile_day] Số mã voucher đã được người dùng lấy trong ngày 



@apiSuccessExample {json} Response: HTTP/1.1 200 OK                                                                                             
{     
  "code": 200,
  "message": "request thành công."    
  "data":{
      "name":"Voucher giảm giá hóa đơn từ 100K",
      "avatar": "https://mobio.vn/logo.png",
      "merchant_ids": ["6afff536-1fe1-4b89-86d9-401a6b899a14",...],
      "category_ids":["6afff536-1fe1-4b89-86d9-401a6b899a14",...]
      "discount_type":"PERCENT",
      "discount_unit:" "%",
      "price": "20",
      "bill_invoice_status":true,
      "bill_invoice_start":100000,
      "content":"Nội dung của voucher ",
      "brand_url":["https://mobio.bn/static/logo.png",...],
      "tags":"tết;100k",
      "stores": [
                {
                    "address": "128 Láng Hạ, Q.Đống Đa, Hà Nội",
                    "latitude": 21.01141,
                    "longitude": 105.81093,
                    "name": "An Phước - Cửa hàng Hà Nội 6 - Láng Hạ",
                    "phone_number":"0123456789"
                }
      ],
      "total_codes":100,
      "start_time": "2018-07-26T12:00:00Z",
      "end_time": "2019-08-11T12:00:00Z",
      "display_start_time": "2018-07-26T12:00:00Z",
      "display_end_time": "2019-08-11T12:00:00Z",
      "store_ids":["e5cfc5a9-e72e-4cea-942b-f27906677a0f","d018c7a4-9cd3-4afe-b204-ea08242d7bca",..],
      "audience_id":"e969f293-b126-42a8-90e4-0fcbd0c9f89e",
      "interactive":{
        "likes":123,
        "is_like":true 
      },
      "codes":[{
        "code":"1234567890123"
        "expiry_time": "2018-07-26T12:00:00Z",
        "status":1
      }],
      "get_code_per_day":100,
      "get_code_per_profile_day":10,
  }                                                                                                          
}     
"""

# ********************************************************************
# ********************** Đánh giá voucher  **********************
# ********************************************************************

"""
@api {post} /vouchers/<voucher_id>/rate  Đánh giá voucher 
@apiVersion 1.0.0
@apiDescription Đánh giá số sao của voucher ,hay yêu thích voucher 
@apiName RateVoucher
@apiGroup Mobile Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam {decimal}  rate Số sao đánh giá với mức giá trị 0 <= value <= 5
@apiParam {boolean}  like   Trạng thái thích voucher 
@apiParam {boolean}  favourite   Trạng thái yêu thích voucher 

@apiParamExample    {json}      Body example:
{
  "rate" : 1.5,
  "like": false ,
  "favourite": true
}


@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "favourite": true,
        "like": false,
        "rate": 5,
        "voucher_id":"uuid"
    },
    "message": "request thành công."
}
"""

# ********************************************************************
# ********************** Đổi voucher            **********************
# ********************************************************************

"""
@api {post} /vouchers/<voucher_id>/get   Đổi voucher 
@apiVersion 1.0.0
@apiDescription Đổi voucher 
@apiName GetVoucher 
@apiGroup Mobile Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParamExample    {json}      Body example:
{}


@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:) {Object} voucher Danh sách mã code của voucher 
@apiSuccess (data:) {string} voucher...code Mã code của voucher 
@apiSuccess (data:) {string} voucher...expiry_time Thời gian hết hạn của code 
@apiSuccess (data:) {int} voucher...total_codes Sỗ mã voucher còn lại 
@apiSuccess (data:) {int} voucher...user_points Số điểm còn lại của user 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "voucher": {
    "code": "ABCBASNDAS",
    "expire_time" :"2017-08-11T12:00:00Z",
    "total_codes":100,
    "user_points":1000
  }
}
"""

# ********************************************************************
# ********************** Đổi voucher            **********************
# ********************************************************************

"""
@api {post} /vouchers/<voucher_id>/get   Đổi voucher 
@apiVersion 1.0.1
@apiDescription Đổi voucher 
@apiName GetVoucher 
@apiGroup Mobile Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParamExample    {json}      Body example:
{}


@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:) {Object} voucher Danh sách mã code của voucher 
@apiSuccess (data:) {string} voucher...code Mã code của voucher 
@apiSuccess (data:) {string} voucher...expiry_time Thời gian hết hạn của code 
@apiSuccess (data:) {int} voucher...total_codes Sỗ mã voucher còn lại 
@apiSuccess (data:) {int} voucher...user_points Số điểm còn lại của user 
@apiSuccess (data:) {int} voucher...get_code_per_day Số voucher đã được lấy trong ngày 
@apiSuccess (data:) {int} voucher...get_code_per_profile_day Số voucher đã được người dùng lấy trong ngày 
@apiSuccess (data:) {int} voucher...max_code Số voucher tối đa
@apiSuccess (data:) {int} voucher...max_code_per_day Số voucher tối đa
@apiSuccess (data:) {int} voucher...max_code_per_profile Số voucher tối đa người dùng có thể lấy trong ngày


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "voucher": {
    "code": "ABCBASNDAS",
    "expire_time" :"2017-08-11T12:00:00Z",
    "total_codes":100,
    "max_code":100,
    "max_code_per_day":100,
    "max_code_per_profile":10,
    "get_code_per_day":90,
    "get_code_per_profile_day":9
  }
}
"""

# *********************************************************************************
# ********************** Lấy danh sách voucher  theo profile **********************
# *********************************************************************************

"""
@api {POST} /vouchers/self   Lấy danh sách voucher của tôi  
@apiVersion 1.0.0
@apiDescription Lấy danh sách voucher của tôi 
@apiName SelfVoucher  
@apiGroup Mobile Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam (Body:) {string}  [name] Tìm kiếm theo tên của voucher 

@apiParam (Body:) {ArrayInt}  [options] Hỗ trợ tìm kiếm theo các thông tin bổ sung sau 
<li><code>1</code> Đang hoạt động </li>
<li><code>2</code> Đã hết hạn </li>
<li><code>3</code> Đã sử dụng </li>

@apiParamExample    {json}      Body example:
{
  "options":[1,3],
  "name":"ten voucher muon tim"
}

@apiSuccess {Array}  vouchers Danh sách voucher của tôi 
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (vouchers:) {string}  id ID của voucher 
@apiSuccess (vouchers:) {string}  name Tên của voucher 
@apiSuccess (vouchers:) {string}  avatar    Link ảnh avatar của voucher nếu có 
@apiSuccess (vouchers:) {string}  merchant_ids   Danh sách các mã của Merchant được gán cho voucher 
@apiSuccess (vouchers:) {string}  category_ids   Danh sách các danh mục của voucher 
@apiSuccess   (vouchers:)  {string}   discount_type      <ul> Kiểu giảm giá:</ul> 
<li><b>PERCENT</b>-Phần trăm</li> 
<li><b>MONEY</b>-Giảm tiền</li>  

@apiSuccess   (vouchers:)  {string}   discount_unit       <ul>Đơn vị của giá trị giảm giá:</ul>
<li><b> % </b>-Phần trăm </li>
<li><b>VND </b> Việt Nam đồng </li>
<li><b>USD </b></li>
@apiSuccess (vouchers:) {int}  discount   Mức giảm giá của voucher 
@apiSuccess (vouchers:) {int}  price Số diểm cần để đổi voucher 
@apiSuccess (vouchers:) {number} interactive <ul>Đối tượng mô tả trạng thái tương tác với voucher:</ul>
<li><b>favourite</b> <u>int</u> Số luợt yêu thích của voucher </li>
<li><b>likes</b> <u>int</u> Số lượng thích của voucher </li>
<li><b>is_like</b> <u>boolean</u> User có thích voucher hay chưa  
@apiSuccess (vouchers:) {number=-1-DEACTIVE 0-DRAFT 1-ACTIVE} status Trạng thái của voucher. <code>DEACTIVE: Ẩn</code>,
 <code>ACTIVE: Hiển thị</code>, <code>DRAFT: nháp</code>
@apiSuccess (vouchers:) {int} number_codes Số mã còn lại của voucher  
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "vouchers": [
  { 
    "id" : "uuid",
    "name":"Voucher giảm giá hóa đơn từ 100K",
    "avatar": "https://mobio.vn/logo.png",
    "merchant_ids": ["6afff536-1fe1-4b89-86d9-401a6b899a14",...],
    "category_ids":["6afff536-1fe1-4b89-86d9-401a6b899a14",...]
    "discount_type":"PERCENT",
    "discount": 12,
    "discount_unit": "%",
    "interactive":{
        "favourite":113,
        "likes":123,
        "is_like":true 
    },
    "price": 0,
    "number_codes": 1
  }
}
"""

# *********************************************************************************
# ********************** Lấy danh sách voucher yêu thích  **********************
# *********************************************************************************

"""
@api {POST} /vouchers/favourite  Lấy danh sách voucher yêu thích 
@apiVersion 1.0.0
@apiDescription Lấy danh sách voucher yêu thích 
@apiName VoucherFavourite  
@apiGroup Mobile Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam (Body:) {string}  [name] Tìm kiếm theo tên của voucher 


@apiParamExample    {json}      Body example:
{
  "name":"ten voucher muon tim"
}

@apiSuccess {Array}  vouchers Danh sách voucher của tôi 
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (vouchers:) {string}  id ID của voucher 
@apiSuccess (vouchers:) {string}  name Tên của voucher 
@apiSuccess (vouchers:) {string}  avatar    Link ảnh avatar của voucher nếu có 
@apiSuccess (vouchers:) {string}  merchant_ids   Danh sách các mã của Merchant được gán cho voucher 
@apiSuccess (vouchers:) {string}  category_ids   Danh sách các danh mục của voucher 
@apiSuccess   (vouchers:)  {string}   discount_type      <ul> Kiểu giảm giá:</ul> 
<li><b>PERCENT</b>-Phần trăm</li> 
<li><b>MONEY</b>-Giảm tiền</li>  

@apiSuccess   (vouchers:)  {string}   discount_unit       <ul>Đơn vị của giá trị giảm giá:</ul>
<li><b> % </b>-Phần trăm </li>
<li><b>VND </b> Việt Nam đồng </li>
<li><b>USD </b></li>
@apiSuccess (vouchers:) {int}  discount   Mức giảm giá của voucher 
@apiSuccess (vouchers:) {int}  price Số diểm cần để đổi voucher 
@apiSuccess (vouchers:) {number} interactive <ul>Đối tượng mô tả trạng thái tương tác với voucher:</ul>
<li><b>favourite</b> <u>int</u> Số luợt yêu thích của voucher </li>
<li><b>likes</b> <u>int</u> Số lượng thích của voucher </li>
<li><b>is_like</b> <u>boolean</u> User có thích voucher hay chưa  

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "vouchers": [
  { 
    "id" : "uuid",
    "name":"Voucher giảm giá hóa đơn từ 100K",
    "avatar": "https://mobio.vn/logo.png",
    "merchant_ids": ["6afff536-1fe1-4b89-86d9-401a6b899a14",...],
    "category_ids":["6afff536-1fe1-4b89-86d9-401a6b899a14",...]
    "discount_type":"PERCENT",
    "discount": 12,
    "discount_unit": "%",
    "interactive":{
        "favourite":113,
        "likes":123,
        "is_like":true 
    },
    "price": 0
  }
}
"""

# *********************************************************************************
# ********************** Lấy số lượng mã voucher của profile  *********************
# *********************************************************************************

"""
@api {GET} /vouchers/profile/statistic   Thống kê số lượng mã voucher của profile
@apiVersion 1.0.0
@apiDescription Thống kê số lượng mã voucher của profile (Mã chưa sử dụng và đã sử dụng)
@apiName VoucherProfileStatistic
@apiGroup Mobile Voucher

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiSuccess {Array}     Data            Kết quả thống kê mã voucher
@apiSuccess {String}    message         Mô tả phản hồi
@apiSuccess {Integer}   code            Mã phản hồi

@apiSuccess (Data:) {Number}  status    Trạng thái mã voucher
                                        <ul>
                                            <li><code>0 </code>: Chưa sử dụng</li>
                                            <li><code>1 </code>: Đã sử dụng</li>
                                        </ul> 
@apiSuccess (Data:) {Number}  count     Số lượng mã voucher tương ứng với trạng thái 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "count": 30,
      "status": 0
    },
    {
      "count": 14,
      "status": 1
    }
  ],
  "message": "request thành công."
}
"""

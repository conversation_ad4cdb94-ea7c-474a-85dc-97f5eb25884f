*******************************************************************************
************************ Dịch vụ check-in tại cửa hàng ************************
* Version: 1.0.0
*******************************************************************************
"""
@api {POST} /foot-traffic/check-in Dịch vụ check-in tại cửa hàng
@apiVersion 1.0.0
@apiGroup Foot-traffic
@apiName PostCheckIn

@apiUse 404
@apiUse 401
@apiUse 405
@apiUse lang

@apiParam  {string} merchant_id UUID tenant. Client Fix
@apiParam  {string} code Mã cần check-in
@apiParamExample {json} Body
{
    "merchant_id": "b1f2c5fe-38b9-45dd-aafc-8dcef8fe535c",
    "code": "KDUQIK21ODI219"
}

@apiSuccess  {Object} data Dữ liệu được tặng
@apiSuccess (data) {Number} [current_point] Số điểm được tặng
@apiSuccess (data) {string} [voucher_name] Tên voucher được tặng
@apiSuccess (data) {string} [voucher_id] UUID voucher được tặng
@apiSuccessExample {json} Response HTTP1.1
{
    "code": 200,
    "data": {
        "current_point": 102,
        "voucher_name": "Giảm 50% ...",
        "voucher_id": "3773612c-c0a0-4b0c-a519-be5fbd8b3234"
    }
    "message": "request thành công."
}

"""
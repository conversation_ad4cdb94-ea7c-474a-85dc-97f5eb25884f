# *******************************************************************************************
# ***************************** L<PERSON>y danh sách lịch sử giao dịch *****************************
# v1.0
# *******************************************************************************************
"""
@api {GET} /transaction/history Lấy danh sách lịch sử giao dịch
@apiDescription Lấy danh sách lịch sử giao dịch
@apiVersion 1.0.0
@apiName GetTransactionHistory
@apiGroup Transaction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam   (Query:)  {String}  profile_id  Định danh của profile cần xem lịch sử.
@apiParam   (Query:)  {StringArray}  [type]  Loại giao dịch.
<li><code>checkin</code> Ghé thăm</li>
<li><code>buying</code> Mua hàng</li>
<li><code>rating</code>Đánh giá dịch vụ</li>
<li><code>redeem</code>Đổi điểm</li>
<li><code>tick_voucher</code>Tích voucher</li>
<li><code>use_voucher</code>Sử dụng voucher</li><br/>
Example: <code>type=checkin,buying</code>
@apiParam   (Query:)  {Date}  [since]     Mốc thời điểm đầu. Example: <code>&since=1509433200</code>.
@apiParam   (Query:)  {Date}  [until]     Mốc thời điểm cuối. Example: <code>&until=1517126400</code>. Nếu client không gửi thì lấy thời điểm hiện tại.
@apiParam   (Query:)  {String}  [query]   Từ khoá tìm kiếm. Tìm kiếm trong nội dung giao dịch: tên cửa hàng, tên voucher, mã voucher
@apiUse order_sort

@apiSuccess   (Body:)   {Array}   data  Danh sách lịch sử giao dịch

@apiSuccess   (transaction)  {Date}   created_time  Thời điểm phát sinh giao dịch.
@apiSuccess   (transaction)  {String} type  Loại giao dịch.
@apiSuccess   (transaction)   {Number}   [point_changed]  Số điểm bị thay đổi. Nếu tenant chưa sử dụng hoặc không cấu hình hệ thống điểm trong module Loyalty thì các giao dịch sẽ không có giá trị điểm.
@apiSuccess   (transaction)   {Object}  [reference]   Thông tin liên quan của giao dịch. Ví dụ: thông tin cửa hàng, thông tin voucher.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "data":[{
        "merchant_id": "",
        "profile_id": "",
        "created_user": "",
        "created_time": "",//timestamp
        "before_profile_point": 123,
        "after_profile_point": 0,
        "point_changed": -123,
        "point_type": "point", //rank_point
        "source": "",// nguon phat sinh gd
        "affected_block": [
            {
                "id": "3d2a7dc8-e861-4663-9aa1-34b252a28792",
                "point_changed": 12
                "detail": [
                    {
                        "id": "fd6c9838-362b-465a-a447-b22b25f5f2ed",
                        "point": 10
                    },
                    {
                        "id": "915621ec-70ff-45ef-9ee9-7f87b9970c0f",
                        "point": 2
                    }
                ]
            }
        ],
        "type": "buying", //like;comment;gift;redeem
        "reason": "", //https://dev.mobio.vn/docs/loyalty/#api-Point-GetPointCondition
        "reference": {
            "voucher_id": "cf8ad864-030e-43b4-be0e-06c0f4681fcf",
            "voucher_code": "982GHSJ",
            "voucher_name": "Giảm giá 50%",
            "voucher_tick_number": 4, //Số lượt tích voucher hiện tại của profile
            "voucher_tick_max": 5, //Số lượt tích đủ để nhận voucher
            "store_id": "8dc01717-2781-42d3-bb3d-c7009a228988",
            "store_name": "89 Cầu Giấy, Hà Nội"
        }
    }],
    "paging" : {
    ...
    }
}

"""

# *******************************************************************************************
#v 1.0.1
"""
@api {GET} /transaction/history Lấy danh sách lịch sử giao dịch
@apiDescription Lấy danh sách lịch sử giao dịch
@apiVersion 1.0.1
@apiName GetTransactionHistory
@apiGroup Transaction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam   (Query:)  {String}  profile_id  Định danh của profile cần xem lịch sử.
@apiParam   (Query:)  {StringArray}  [type]  Loại giao dịch.
<li><code>checkin</code> Ghé thăm</li>a
<li><code>buying</code> Mua hàng</li>
<li><code>rating</code>Đánh giá dịch vụ</li>
<li><code>redeem</code>Đổi điểm</li>
<li><code>tick_voucher</code>Tích voucher</li>
<li><code>use_voucher</code>Sử dụng voucher</li><br/>
Example: <code>type=checkin,buying</code>
@apiParam   (Query:)  {Date}  [since]     Mốc thời điểm đầu. Example: <code>&since=1509433200</code>.
@apiParam   (Query:)  {Date}  [until]     Mốc thời điểm cuối. Example: <code>&until=1517126400</code>. Nếu client không gửi thì lấy thời điểm hiện tại.
@apiParam   (Query:)  {String}  [query]   Từ khoá tìm kiếm. Tìm kiếm trong nội dung giao dịch: tên cửa hàng, tên voucher, mã voucher
@apiUse order_sort

@apiSuccess   (Body:)   {Array}   data  Danh sách lịch sử giao dịch

@apiSuccess   (transaction)  {Date}   created_time  Thời điểm phát sinh giao dịch.
@apiSuccess   (transaction)  {String} type  Loại giao dịch.
@apiSuccess   (transaction)   {Number}   [point_changed]  Số điểm bị thay đổi. Nếu tenant chưa sử dụng hoặc không cấu hình hệ thống điểm trong module Loyalty thì các giao dịch sẽ không có giá trị điểm.
@apiSuccess   (transaction)   {Object}  [reference]   Thông tin liên quan của giao dịch. Ví dụ: thông tin cửa hàng, thông tin voucher.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "data":[{
        "merchant_id": "",
        "profile_id": "",
        "created_user": "",
        "created_time": "",//timestamp
        "before_profile_point": 123,
        "after_profile_point": 0,
        "point_changed": -123,
        "point_type": "point", //rank_point
        "source": "",// nguon phat sinh gd
        "affected_block": [
            {
                "id": "3d2a7dc8-e861-4663-9aa1-34b252a28792",
                "point_changed": 12
                "detail": [
                    {
                        "id": "fd6c9838-362b-465a-a447-b22b25f5f2ed",
                        "point": 10
                    },
                    {
                        "id": "915621ec-70ff-45ef-9ee9-7f87b9970c0f",
                        "point": 2
                    }
                ]
            }
        ],
        "type": "buying", //like;comment;gift;redeem
        "title": {
            "vi": "Tieu de",
            "en": "test title"
        },
        "content": {
            "vi": "noi dung",
            "en": "test title"
        },        
        "reason": "", //https://dev.mobio.vn/docs/loyalty/#api-Point-GetPointCondition
        "reference": {
            "voucher_id": "cf8ad864-030e-43b4-be0e-06c0f4681fcf",
            "voucher_code": "982GHSJ",
            "voucher_name": "Giảm giá 50%",
            "voucher_tick_number": 4, //Số lượt tích voucher hiện tại của profile
            "voucher_tick_max": 5, //Số lượt tích đủ để nhận voucher
            "store_id": "8dc01717-2781-42d3-bb3d-c7009a228988",
            "store_name": "89 Cầu Giấy, Hà Nội"
        }
    }],
    "paging" : {
    ...
    }
}

"""

# *******************************************************************************************
# v1.0.2

"""
@api {GET} /transaction/history Lấy danh sách lịch sử giao dịch
@apiDescription Lấy danh sách lịch sử giao dịch
@apiVersion 1.0.2
@apiName GetTransactionHistory
@apiGroup Transaction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam   (Query:)  {String}  profile_id  Định danh của profile cần xem lịch sử.
@apiParam   (Query:)  {StringArray}  [type]  Loại giao dịch.
<li><code>checkin</code> Ghé thăm cửa hàng</li>
<li><code>buying</code> Mua hàng</li>
<li><code>rating</code>Đánh giá dịch vụ</li>
<li><code>redeem</code>Đổi điểm</li>
<li><code>tick_voucher</code>Tích voucher</li>
<li><code>use_voucher</code>Sử dụng voucher</li><br/>
Example: <code>type=checkin,buying</code>
@apiParam   (Query:)  {Date}  [since]     Mốc thời điểm đầu. Example: <code>&since=1509433200</code>.
@apiParam   (Query:)  {Date}  [until]     Mốc thời điểm cuối. Example: <code>&until=1517126400</code>. Nếu client không gửi thì lấy thời điểm hiện tại.
@apiParam   (Query:)  {String}  [query]   Từ khoá tìm kiếm. Tìm kiếm trong nội dung giao dịch: tên cửa hàng, tên voucher, mã voucher
@apiUse order_sort

@apiSuccess   (Body:)   {Array}   data  Danh sách lịch sử giao dịch

@apiSuccess   (transaction)  {Date}   created_time  Thời điểm phát sinh giao dịch.
@apiSuccess   (transaction)  {String} type  Loại giao dịch.
@apiSuccess   (transaction)   {Number}   [point_changed]  Số điểm bị thay đổi. Nếu tenant chưa sử dụng hoặc không cấu hình hệ thống điểm trong module Loyalty thì các giao dịch sẽ không có giá trị điểm.
@apiSuccess   (transaction)   {Object}  [reference]   Thông tin liên quan của giao dịch. Ví dụ: thông tin cửa hàng, thông tin voucher.
@apiSuccess   (transaction)   {String}  [title]   Tiêu đề
@apiSuccess   (transaction)   {String}  [content] Nội dung giao dịch 
@apiSuccess   (transaction)   {String}  [point_type] Loại điểm 

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "data":[{
        "merchant_id": "",
        "profile_id": "",
        "created_user": "",
        "created_time": "",//timestamp
        "before_profile_point": 123,
        "after_profile_point": 0,
        "point_changed": -123,
        "point_type": "point", //rank_point
        "source": "",// nguon phat sinh gd
        "affected_block": [
            {
                "id": "3d2a7dc8-e861-4663-9aa1-34b252a28792",
                "point_changed": 12
                "detail": [
                    {
                        "id": "fd6c9838-362b-465a-a447-b22b25f5f2ed",
                        "point": 10
                    },
                    {
                        "id": "915621ec-70ff-45ef-9ee9-7f87b9970c0f",
                        "point": 2
                    }
                ]
            }
        ],
        "type": "buying", //like;comment;gift;redeem
        "title": "Bạn được tặng điểm",
        "content": "Bạn được tặng điểm nhân dịp sinh nhật",        
        "reference": {
            "voucher_id": "cf8ad864-030e-43b4-be0e-06c0f4681fcf",
            "voucher_code": "982GHSJ",
            "voucher_name": "Giảm giá 50%",
            "voucher_tick_number": 4, //Số lượt tích voucher hiện tại của profile
            "voucher_tick_max": 5, //Số lượt tích đủ để nhận voucher
            "store_id": "8dc01717-2781-42d3-bb3d-c7009a228988",
            "store_name": "89 Cầu Giấy, Hà Nội"
        }
    }],
    "paging" : {
    ...
    }
}

"""
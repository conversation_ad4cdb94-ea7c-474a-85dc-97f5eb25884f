#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: THONGNV
    Company: M O B I O
    Date Created: 8/5/20
"""

# *****************************************************************************
# ********************** L<PERSON>y danh sách dịch vụ yêu thích  *********************
# *****************************************************************************
"""
@api {GET} /profile_service/favourite   Danh sách dịch vụ yêu thích
@apiDescription Danh sách dịch vụ yêu thích
@apiName GetServiceFavourite
@apiGroup ProfileServices
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header
@apiParam   (Query:)     {String}    [search]   Tên dịch vụ ;
@apiParam   (Query:)     {String}    [categories_id]   Danh sách danh mục cách nhau bởi dấu ;


@apiSuccess {Object} data        Danh sách dịch vụ yêu thích
@apiSuccess (data) {String}   avatar          Url ảnh đại diện dịch vụ
@apiSuccess (data) {String}   id              ID dịch vụ
@apiSuccess (data) {String}   name            Tên dịch vụm


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "avatar": "https:....",
      "id": "5f278b0e939255b8fbc2d9d1",
      "name": "test lần 2 30/08"
    },
    {
      "avatar": null,
      "id": "5f27d738e25a10c4a90ea61b",
      "name": "Test SP Vòng đời 02"
    }
  ],
  "message": "request thành công.",
  "paging": {
    "page": 1,
    "per_page": 10,
    "total_count": 2,
    "total_page": 1
  }
}
"""

# *****************************************************************************
# ********************** Lấy danh sách dịch vụ của tôi    *********************
# *****************************************************************************
"""
@api {GET} /profile_service/my/self   Danh sách dịch vụ của tôi
@apiDescription Danh sách dịch vụ của tôi
@apiName GetServiceMySelf
@apiGroup ProfileServices
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam   (Query:)     {String}    [search]   Tên dịch vụ ;
@apiParam   (Query:)     {String}    [use_status]  Trạng thái gói dịch vụ cách nhau bởi dấu;
                                                <ul>
                                                    <li><code>1: Đang trị liệu </code></li>
                                                    <li><code>2: Đã hoàn thành </code></li>
                                                    <li><code>3: Cần thanh toán tiền mặt </code></li>
                                                    <li><code>4: Quá hạn </code></li>
                                                    <li><code>5: Chuyển nhượng </code></li>
                                                    <li><code>6: Đã trả lại </code></li>
                                                    <li><code>7: Bảo lưu </code></li>
                                               </ul>


@apiSuccess {Object} data                     Danh sách dịch vụ của tôi
@apiSuccess (data) {String}   avatar          Url ảnh đại diện dịch vụ
@apiSuccess (data) {String}   id              ID dịch vụ
@apiSuccess (data) {String}   name            Tên dịch vụ
@apiSuccess (data) {String}   profile_service_id            ID gói dịch vụ của profile
@apiSuccess (data) {String}   use_status          Trạng thái gói dịch vụ
                                              <ul>
                                                    <li><code>1: Đang trị liệu </code></li>
                                                    <li><code>2: Đã hoàn thành </code></li>
                                                    <li><code>3: Cần thanh toán tiền mặt </code></li>
                                                    <li><code>4: Quá hạn </code></li>
                                                    <li><code>5: Chuyển nhượng </code></li>
                                                    <li><code>6: Đã trả lại </code></li>
                                                    <li><code>7: Bảo lưu </code></li>
                                               </ul>

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "avatar": "https:....",
      "id": "5f278b0e939255b8fbc2d9d1",
      "name": "test lần 2 30/08",
      "status": 1,
      "profile_service_id": "47fc14e3-b8a5-483f-bea8-afb6f826c8f6"
    },
    {
      "avatar": null,
      "id": "5f27d738e25a10c4a90ea61b",
      "name": "Test SP Vòng đời 02",
      "status": 2,
      "profile_service_id": "47fc14e3-b8a5-483f-bea8-afb6f826c8f6"
    }
  ],
  "message": "request thành công.",
  "paging": {
    "page": 1,
    "per_page": 10,
    "total_count": 2,
    "total_page": 1
  }
}
"""

# *****************************************************************************
# ********************** Tạo tương tác dịch vụ           **********************
# *****************************************************************************
"""
@api {POST} /profile_service/interactive/<service_id> Tạo tương tác dịch vụ
@apiDescription Tạo tương tác dịch vụ
@apiName InteractiveProfileService
@apiGroup ProfileServices
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam      (Body:)     {Number}    value                Giá trị. 0(false) hoăc 1(true) với interactive_type là 1, 2, 3. 0 <= value <= 5  với interactive_type là 4 
@apiParam      (Body:)     {Number}    interactive_type     Loại tương tác. 1-like, 2-view, 3-favourite, 4-rate

@apiParamExample {json} Body example
{   
	"value": 0,
	"interactive_type": 1,
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": 
        {
            'service_id': "dd4d777f-a1db-4edd-9fb3-e9a6812cd6c0",
            'number_favourite': 1,
            'number_like': 1,
            'number_view': 1,
            'rate': 2.5
        }
    ,
    "message": "request thành công."
}
"""

# *****************************************************************************
# ********************** lấy thông tin tương tác dịch vụ *********************
# *****************************************************************************
"""
@api {GET} /profile_service/action/get/interactive/<service_id> thông tin tương tác dịch vụ
@apiDescription Lấy thông tin tương tác dịch vụ
@apiName GetInteractiveService
@apiGroup ProfileServices
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Object} data        Thông tin tương tác dịch vụ
@apiSuccess (data) {number}   number_favourite          số lượng yêu thích
@apiSuccess (data) {number}   number_like               số lượng thích
@apiSuccess (data) {number}   number_comment            số lượng bình luận
@apiSuccess (data) {number}   number_view               số lượng view
@apiSuccess (data) {number}   rate                      tỷ lệ đánh giá
@apiSuccess (data) {Object}   profile_interactive       Đánh giá dịch vụ của profile
@apiSuccess (profile_interactive) {number}   is_favourite       Đã yêu thích?
@apiSuccess (profile_interactive) {number}   is_like            Đã thích?
@apiSuccess (profile_interactive) {number}   is_view            Đã view?
@apiSuccess (profile_interactive) {number}   number_rate        Điểm đánh giá

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "number_favourite": 1,
    "number_like": 1,
    "number_view": 0,
    "profile_interactive": {
      "is_favourite": 0,
      "is_like": 0,
      "is_view": 0,
      "number_rate": 0
    },
    "rate": 2
  },
  "message": "request thành công."
}
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: THONGNV
    Company: M O B I O
    Date Created: 8/11/20
"""

# *****************************************************************************
# ********************** L<PERSON>y danh sách lịch hẹn
# *****************************************************************************
"""
@api {GET} /profile_appointment   Danh sách lịch hẹn
@apiDescription Danh sách lịch hẹn
@apiName GetProfileAppointment
@apiGroup ProfileAppointment
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging

@apiUse merchant_id_header

@apiParam   (Query:)     {Number}    status     Trạng thái lịch hẹn
                                                <ul>
                                                    <li><code> 1 </code>: Chưa đến </li>
                                                    <li><code> 2 </code>: Đã đến </li>
                                                </ul>
@apiParam   (Query:)     {String}    [search]   Tên liệu trình ;
@apiParam   (Query:)     {String}    [store_id]   Danh sách ID cơ sở cách nhau bởi dấu ;
@apiParam   (Query:)     {String}    [categories_id]   Danh sách danh mục cách nhau bởi dấu ;
@apiParam   (Query:)     {String}    [profile_service_id]   ID gói dịch vụ của profile

@apiSuccess {ArrayObject}     data                                Danh sách lịch hẹn
@apiSuccess (data)      {String}   id                       ID lịch hẹn
@apiSuccess (data)      {String}   appointment_time         Thời gian hẹn
@apiSuccess (data)      {String}   code                     Mã lịch hẹn
@apiSuccess (data)      {String}   description              Ghi chú
@apiSuccess (data)      {String}   content                  Nội dung
@apiSuccess (data)      {String}   profile_service_id       ID dịch vụ khách hàng
@apiSuccess (data)      {Number}   session_number           Buổi thứ
@apiSuccess (data)      {Number}   session_total            Tổng số buổi
@apiSuccess (data)      {string}   store_address            Địa chỉ trung tâm
@apiSuccess (data)      {string}   store_name               Tên trung tâm
@apiSuccess (data)      {string}   store_id                 ID trung tâm
@apiSuccess (data)      {string}   service_name             Tên liệu trình
@apiSuccess (data)      {string}   start_time_allow         Thời gian bắt đầu được phép đặt lịch
@apiSuccess (data)      {string}   end_time_allow           Thời gian kết thúc được phép đặt lịch
@apiSuccess (data)      {string}   staff                    Tên nhân viên CS


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "appointment_time": "Wed, 26 Aug 2020 00:00:00 GMT",
      "code": "BK-2108-02",
      "description": "Đến đúng giờ nhe x2",
      "id": "09dd73c2-bd00-4d77-a6a3-a8e6ab5fc631",
      "product_id": "5f27d6e48b6b8e214f841366",
      "profile_service_id": "fe677da4-7906-4d5f-a2e4-3de234d96dcf",
      "service_name": "G11-Scrubber+Deaura prof anti-acne - 8 Lần",
      "session_number": 2,
      "session_total": 12,
      "status": 1,
      "store_address": "số 6 ngõ 82 duy tân ",
      "store_id": "041dfc5f-abf5-4999-83ef-cd877da3a510",
      "store_name": "bán đồ thê thao",
      "start_time_allow": "Wed, 16 Aug 2020 00:00:00 GMT",
      "end_time_allow": "Wed, 26 Aug 2020 00:00:00 GMT",
      "staff": "Nguyễn Hoa"
    },
  ],
  "message": "request thành công.",
  "paging": {
    "page": 1,
    "per_page": 10,
    "total_count": 2,
    "total_page": 1
  }
}
"""


# *****************************************************************************
# ********************** lấy Danh sách các ca hẹn *********************
# Version: 1.0.1
# Version: 1.0.0
# *****************************************************************************
"""
@api {POST} /contract_code/workshift  Lấy danh sách các ca hẹn
@apiDescription Lấy danh sách các ca hẹn
@apiName GetWorkshift
@apiGroup ProfileAppointment
@apiVersion 1.0.1

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam      (Body:)     {string}    store_id                 ID cơ sở cần lấy lịch hẹn
@apiParam      (Body:)     {string}    [date_appointment]        Ngày cần lấy lịch hẹn, nếu không gửi lên lấy theo ngày 
                                                                hiện tại. Format: %Y-%m-%d

@apiSuccess {Object} data     Danh sách các ca hẹn
@apiSuccess (data) {number}   work_shift_id        ID ca hẹn
@apiSuccess (data) {string}   start_time           Thời gian bắt đầu ca hẹn
@apiSuccess (data) {string}   end_time             Thời gian kết thúc ca hẹn

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
        {
            "work_shift_id": 1,
            "start_time": "09:00:00",
            "end_time":"10:00:00"
        }
    ],
  "message": "request thành công."
}
"""

# Version 1.0.0
"""
@api {POST} /contract_code/workshift  Lấy danh sách các ca hẹn
@apiDescription Lấy danh sách các ca hẹn
@apiName GetWorkshift
@apiGroup ProfileAppointment
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam      (Body:)     {string}    [date_appointment]        Ngày cần lấy lịch hẹn, nếu không gửi lên lấy theo ngày 
                                                                hiện tại. Format: %Y-%m-%d

@apiSuccess {Object} data     Danh sách các ca hẹn
@apiSuccess (data) {number}   work_shift_id        ID ca hẹn
@apiSuccess (data) {string}   start_time           Thời gian bắt đầu ca hẹn
@apiSuccess (data) {string}   end_time             Thời gian kết thúc ca hẹn

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
        {
            "work_shift_id": 1,
            "start_time": "09:00:00",
            "end_time":"10:00:00"
        }
    ],
  "message": "request thành công."
}
"""

# *****************************************************************************
# ********************** Cập nhật lịch hẹn                *********************
# *****************************************************************************
"""
@api {PUT} /profile_appointment  Cập nhật lịch hẹn
@apiDescription  Cập nhật lịch hẹn
@apiName UpdateAppointment
@apiGroup ProfileAppointment
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam      (Body:)     {string}    id                   ID lịch hẹn
@apiParam      (Body:)     {string}    code                 Mã lịch hẹn
@apiParam      (Body:)     {number}    work_shift_id        ID ca hẹn
@apiParam      (Body:)     {Date}      appointment_time     Ngày hẹn. <code>Định dạng: %Y-%m-%d </code>
@apiParam      (Body:)     {string}    [description]        Ghi chú

                                                            
@apiSuccess {Object}       data                    Thông tin lịch hẹn    
@apiSuccess (data) {number}   id                   ID lịch hẹn
@apiSuccess (data) {string}   appointment_time     Ngày hẹn
@apiSuccess (data) {string}   code                 Mã lịch hẹn
@apiSuccess (data) {string}   description          Ghi chú 

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "appointment_time": "2020-08-22",
    "code": "TA019",
    "description": "Ưu tiên người nhà",
    "id": "6b4fecf5-6360-46e3-b3df-00726811fed7",
    "work_shift_id": 1
  },
  "message": "request thành công."
}
"""


# *****************************************************************************
# ********************** Lấy danh sách lịch sử trị liệu
# *****************************************************************************
"""
@api {GET} /therapy   Danh sách lịch sử trị liệu
@apiDescription Danh sách lịch sử trị liệu
@apiName GetProfileTherapy
@apiGroup ProfileTherapy
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging

@apiUse merchant_id_header

@apiParam   (Query:)     {String}    [search]   Tên liệu trình ;
@apiParam   (Query:)     {String}    [store_id]   Danh sách ID cơ sở cách nhau bởi dấu ;
@apiParam   (Query:)     {String}    [categories_id]   Danh sách danh mục cách nhau bởi dấu ;
@apiParam   (Query:)     {String}    [profile_service_id]   ID gói dịch vụ của profile


@apiSuccess {ArrayObject}     data                          Danh sách lịch sử trị liệu
@apiSuccess (data)      {String}   id                       ID buổi hẹn
@apiSuccess (data)      {String}   used_time                Thời gian sử dụng buổi trị liệu
@apiSuccess (data)      {String}   content                  Nội dung buổi trị liệu
@apiSuccess (data)      {String}   description              Ghi chú
@apiSuccess (data)      {String}   profile_service_id       ID dịch vụ khách hàng
@apiSuccess (data)      {Number}   session_number           Buổi thứ
@apiSuccess (data)      {Number}   session_total            Tổng số buổi
@apiSuccess (data)      {string}   store_address            Địa chỉ trung tâm
@apiSuccess (data)      {string}   store_name               Tên trung tâm
@apiSuccess (data)      {string}   store_id                 ID trung tâm
@apiSuccess (data)      {string}   service_name             Tên liệu trình


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "description": "Buổi trị liệu thứ 1 đến đúng giờ sửa tiếp\n",
      "content": "Nâng cơ",
      "id": "17ee029a-e07e-4f68-93e2-14a9a8400d21",
      "product_id": "5f27d6e48b6b8e214f841366",
      "profile_service_id": "4314605a-3a92-4b20-9c31-c178561293d3",
      "service_name": "G11-Scrubber+Deaura prof anti-acne - 8 Lần",
      "session_number": 1,
      "session_total": 12,
      "store_address": "số 1 lô 1 nguyễn khánh toàn",
      "store_id": "74c09755-1f48-4bb4-95fb-671b7393a8ea",
      "store_name": "Kobe bryant",
      "used_time": "Mon, 24 Aug 2020 00:00:00 GMT"
    },
    {
      "description": "Buổi trị liệu thứ 2 đến đúng giờ",
      "content": "Nâng cơ",
      "id": "19992a54-13b6-4068-beba-3c945e388602",
      "product_id": "5f27d6e48b6b8e214f841366",
      "profile_service_id": "47fc14e3-b8a5-483f-bea8-afb6f826c8f6",
      "service_name": "G11-Scrubber+Deaura prof anti-acne - 8 Lần",
      "session_number": 2,
      "session_total": 12,
      "store_address": "số 1 lô 1 nguyễn khánh toàn",
      "store_id": "74c09755-1f48-4bb4-95fb-671b7393a8ea",
      "store_name": "Kobe bryant",
      "used_time": "Sun, 23 Aug 2020 00:00:00 GMT"
    }
  ],
  "message": "request thành công.",
  "paging": {
    "page": 1,
    "per_page": 10,
    "total_count": 2,
    "total_page": 1
  }
}
"""

# *****************************************************************************
# ********************** Lấy số buổi trị liệu
# *****************************************************************************
"""
@api {GET} /therapy/last   Số buổi trị liệu của liệu trình
@apiDescription Thông tin số buổi trị liệu của liệu trình
@apiName GetProfileTherapyLast
@apiGroup ProfileTherapy
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging

@apiUse merchant_id_header

@apiParam   (Query:)     {String}    product_id             ID liệu trình
@apiParam   (Query:)     {String}    [profile_service_id]   ID gói dịch vụ của profile

@apiSuccess {ArrayObject}     data                          Thông  tin liệu trình
@apiSuccess (data)      {Number}   session_number           Buổi thứ
@apiSuccess (data)      {Number}   session_total            Tổng số buổi
@apiSuccess (data)      {Number}   use_status               Trạng thái của gói liệu trình


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "session_number": 4,
    "session_total": 12,
    "use_status": 1
  },
  "message": "request thành công."
}
"""

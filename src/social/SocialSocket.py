************************* Socket assign comment **************************
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau phân công bình luận
@apiDescription Socket nhận sau phân công bình luận
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketAssignComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'ASSIGN_COMMENT_SOCKET',
  'merchant_id': "",
  'page_id': "",
  'page_social_id': "",
  'social_type': 1,
  'data': {
    "comment": {
      "assignees": [
        {
          "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "comment_id": "c3f2a577-8a3a-49d7-a2df-8c9e6f53d6a3",
          "created_time": "2019-09-23T08:38:24Z",
          "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "id": "70c0c4fa-fe18-4ab9-90c3-4bce00515f14",
          "note": "",
          "status": 1
        },
        {
          "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "comment_id": "c3f2a577-8a3a-49d7-a2df-8c9e6f53d6a3",
          "created_time": "2019-09-23T08:32:49Z",
          "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "id": "6aeda81f-16ce-4568-ac59-7351b5ad233b",
          "note": "",
          "status": 0
        },
        {
          "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "comment_id": "c3f2a577-8a3a-49d7-a2df-8c9e6f53d6a3",
          "created_time": "2019-09-23T08:29:50Z",
          "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "id": "0b775fad-b195-43ac-904e-b5ea8f5f26f4",
          "note": "",
          "status": 0
        }
      ],
      "classify": 0,
      "comment_social_id": "459529091315817_463628024239257",
      "created_user": "148518185914499",
      "id": "c3f2a577-8a3a-49d7-a2df-8c9e6f53d6a3",
      "is_reply": 1,
      "last_message": {
        "created_time": "2019-09-20T09:03:27Z",
        "from": {
          "id": "148518185914499",
          "name": ""
        },
        "message": " đã gửi 1 video"
      },
      "resolved_time": "2019-09-22T07:58:42Z",
      "social": {
        "id": "164769917458404_459529091315817",
        "title": "đăng bài từ socialstyle"
      },
      "state": 1,
      "status": 1,
      "tags": [],
      "topic_id": "a6b5a173-eca8-4a57-813c-b3183b797cb3",
      "type": 3,
      "unread_number": 0,
      "updated_time": "2019-09-23T08:38:24Z"
    }
  }
}
"""
========================
"""
@api NONE Socket nhận sau phân công bình luận
@apiDescription Socket nhận sau phân công bình luận
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketAssignComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'ASSIGN_COMMENT_SOCKET',
  'merchant_id': "",
  'page_id': "",
  'page_social_id': "",
  'social_type': 1,
  'data': {
    'assign': [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "comment_id": "cfdf935a-99bc-469d-816a-f21ac9d140ee", 
        "created_time": "2019-09-10T10:46:24Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "02470342-70d4-4910-bcbb-318e3f9fab31", 
        "note": null, 
        "status": 1
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "comment_id": "cfdf935a-99bc-469d-816a-f21ac9d140ee", 
        "created_time": "2019-09-10T00:39:31Z", 
        "created_user": null, 
        "id": "856646a1-0d63-406e-a4a4-48e8f4c72729", 
        "note": null, 
        "status": 0
      }, 
      {
        "assignee_id": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f", 
        "comment_id": "cfdf935a-99bc-469d-816a-f21ac9d140ee", 
        "created_time": "2019-09-10T00:09:32Z", 
        "created_user": null, 
        "id": "27e20ff6-d17e-4c52-a136-6bb5c8440119", 
        "note": null, 
        "status": 0
      }
    ],
    'tags': []
  }
}
"""
************************* Socket assign conversation *********************
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau phân công hội thoại
@apiDescription Socket nhận sau phân công hội thoại
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketAssignConversation

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'ASSIGN_CONVERSATION_SOCKET',
  'merchant_id': "",
  'page_id': "",
  'page_social_id': "",
  'social_type': 1,
  'data': {
    "conversation": {
      "assignees": [
        {
          "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "conversation_id": "43a7a177-075d-4eba-bafd-dece8afd6c12",
          "created_time": "2019-09-23T08:41:53Z",
          "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "id": "be5dd1aa-a1bf-43ba-8daa-96dec0c90e62",
          "note": "",
          "status": 1
        },
        {
          "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "conversation_id": "43a7a177-075d-4eba-bafd-dece8afd6c12",
          "created_time": "2019-09-20T04:47:17Z",
          "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "id": "6688e589-aa4c-47eb-b4ed-223fdbe665a9",
          "note": "",
          "status": 0
        },
        {
          "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "conversation_id": "43a7a177-075d-4eba-bafd-dece8afd6c12",
          "created_time": "2019-09-20T04:46:09Z",
          "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "id": "83ac5ba6-93ca-4386-b2e8-30f87c717954",
          "note": "",
          "status": 0
        }
      ],
      "classify": 1,
      "id": "43a7a177-075d-4eba-bafd-dece8afd6c12",
      "is_reply": 1,
      "last_message": {
        "created_time": "2019-09-16T07:48:35Z",
        "from": {
          "id": "2228316327456739",
          "name": ""
        },
        "message": "hello"
      },
      "resolved_time": "2019-09-17T04:47:46Z",
      "social": {
        "id": "t_2364818863806484",
        "title": ""
      },
      "status": 1,
      "tags": [],
      "type": 2,
      "unread_number": 0,
      "updated_time": "2019-09-17T04:47:46Z"
    }
  }
}
"""
====================
"""
@api NONE Socket nhận sau phân công hội thoại
@apiDescription Socket nhận sau phân công hội thoại
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketAssignConversation

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'ASSIGN_CONVERSATION_SOCKET',
  'merchant_id': "",
  'page_id': "",
  'page_social_id': "",
  'social_type': 1,
  'data': {
    'assign': [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "conversation_id": "486623b2-9d78-49eb-9897-41926cdf0244", 
        "created_time": "2019-09-10T10:43:46Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "d59d4779-b707-4558-a594-4eb65f106ccc", 
        "note": null, 
        "status": 1
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "conversation_id": "486623b2-9d78-49eb-9897-41926cdf0244", 
        "created_time": "2019-09-10T10:41:00Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "ca9c2a54-c95d-467e-9ecd-1a4cc01d7d65", 
        "note": null, 
        "status": 0
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "conversation_id": "486623b2-9d78-49eb-9897-41926cdf0244", 
        "created_time": "2019-08-23T05:25:23Z", 
        "created_user": null, 
        "id": "eada04e0-eae0-4244-ba16-af4fe5def8af", 
        "note": null, 
        "status": 0
      }
    ],
    'tags': []
  }
}
"""
************************* Socket assign rating ***************************
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau phân công đánh giá
@apiDescription Socket nhận sau phân công đánh giá
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketAssignRating

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'ASSIGN_RATING_SOCKET',
  'merchant_id': "",
  'page_id': "",
  'page_social_id': "",
  'social_type': 1,
  'data': {
    "rating": {
      "assignees": [
        {
          "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "created_time": "2019-09-23T08:43:43Z",
          "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "id": "34fba69f-084a-46fd-a5e2-c3e68c5c90b6",
          "note": "",
          "rating_id": "e3ef7afa-3673-4fd1-b2dc-bc6557eb0d1e",
          "status": 1
        },
        {
          "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "created_time": "2019-09-20T02:59:15Z",
          "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "id": "5a7f4f24-93f2-4dc2-9414-22b210ede31e",
          "note": "",
          "rating_id": "e3ef7afa-3673-4fd1-b2dc-bc6557eb0d1e",
          "status": 0
        },
        {
          "assignee_id": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f",
          "created_time": "2019-09-20T02:57:47Z",
          "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "id": "56ebfae4-4848-44a4-95ea-28e09926bc46",
          "note": "",
          "rating_id": "e3ef7afa-3673-4fd1-b2dc-bc6557eb0d1e",
          "status": 0
        }
      ],
      "classify": 0,
      "id": "e3ef7afa-3673-4fd1-b2dc-bc6557eb0d1e",
      "is_reply": 1,
      "last_message": {
        "created_time": "2019-09-18T10:41:02Z",
        "from": {
          "id": "147733942657541",
          "name": ""
        },
        "message": ""
      },
      "resolved_time": "2019-09-18T10:41:25Z",
      "social": {
        "id": "514300679334197",
        "title": "khá hay ho. nhiều thông tin hữu ích"
      },
      "status": 1,
      "tags": [],
      "type": 4,
      "unread_number": 0,
      "updated_time": "2019-09-23T08:43:43Z"
    }
  }
}
"""
======================
"""
@api NONE Socket nhận sau phân công đánh giá
@apiDescription Socket nhận sau phân công đánh giá
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketAssignRating

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'ASSIGN_RATING_SOCKET',
  'merchant_id': "",
  'page_id': "",
  'page_social_id': "",
  'social_type': 1,
  'data': {
    'assign': [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "created_time": "2019-09-10T10:48:19Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "935a2b5a-cb4b-44e8-9755-4e38972a1f0d", 
        "note": null, 
        "rating_id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c", 
        "status": 1
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "created_time": "2019-08-05T09:57:50Z", 
        "created_user": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f", 
        "id": "b8584c4f-6c16-482f-88d8-e2b81a59a70f", 
        "note": null, 
        "rating_id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c", 
        "status": 0
      }, 
      {
        "assignee_id": "8ba5e082-393f-47e0-a4a4-b91643056583", 
        "created_time": "2019-08-05T09:57:43Z", 
        "created_user": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f", 
        "id": "6f11f242-df85-476f-8daf-3c597e9e6414", 
        "note": null, 
        "rating_id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c", 
        "status": 0
      }
    ],
    'tags': []
  }
}
"""
************************* Socket resolve comment  ************************
* version: 1.0.2                                                         *
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau hoàn tất bình luận
@apiDescription Socket nhận sau hoàn tất bình luận
@apiGroup SocialSocket
@apiVersion 1.0.2
@apiName SocketResolveComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'RESOLVED_COMMENT_SOCKET',
  'merchant_id': '',
  'social_type': 1,
  'page_id': '',
  'page_social_id': '',
  'data': {
    'resolve_object_id': '',
    'assign': [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "comment_id": "cfdf935a-99bc-469d-816a-f21ac9d140ee", 
        "created_time": "2019-09-10T10:46:24Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "02470342-70d4-4910-bcbb-318e3f9fab31", 
        "note": null, 
        "status": 1
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "comment_id": "cfdf935a-99bc-469d-816a-f21ac9d140ee", 
        "created_time": "2019-09-10T00:39:31Z", 
        "created_user": null, 
        "id": "856646a1-0d63-406e-a4a4-48e8f4c72729", 
        "note": null, 
        "status": 0
      }, 
      {
        "assignee_id": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f", 
        "comment_id": "cfdf935a-99bc-469d-816a-f21ac9d140ee", 
        "created_time": "2019-09-10T00:09:32Z", 
        "created_user": null, 
        "id": "27e20ff6-d17e-4c52-a136-6bb5c8440119", 
        "note": null, 
        "status": 0
      }
    ],
    'external_data': {}
  }
}
"""
=====================
"""
@api NONE Socket nhận sau hoàn tất bình luận
@apiDescription Socket nhận sau hoàn tất bình luận
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketResolveComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'RESOLVED_COMMENT_SOCKET',
  'merchant_id': '',
  'social_type': 1,
  'page_id': '',
  'page_social_id': '',
  'data': {
    'resolve_object_id': '',
    'assign': [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "comment_id": "cfdf935a-99bc-469d-816a-f21ac9d140ee", 
        "created_time": "2019-09-10T10:46:24Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "02470342-70d4-4910-bcbb-318e3f9fab31", 
        "note": null, 
        "status": 1
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "comment_id": "cfdf935a-99bc-469d-816a-f21ac9d140ee", 
        "created_time": "2019-09-10T00:39:31Z", 
        "created_user": null, 
        "id": "856646a1-0d63-406e-a4a4-48e8f4c72729", 
        "note": null, 
        "status": 0
      }, 
      {
        "assignee_id": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f", 
        "comment_id": "cfdf935a-99bc-469d-816a-f21ac9d140ee", 
        "created_time": "2019-09-10T00:09:32Z", 
        "created_user": null, 
        "id": "27e20ff6-d17e-4c52-a136-6bb5c8440119", 
        "note": null, 
        "status": 0
      }
    ]
  }
}
"""
=====================
"""
@api NONE Socket nhận sau hoàn tất bình luận
@apiDescription Socket nhận sau hoàn tất bình luận
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketResolveComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'RESOLVED_COMMENT_SOCKET',
  'merchant_id': '',
  'social_type': 1,
  'page_id': '',
  'page_social_id': '',
  'data': {
    'resolve_object_id': ''
  }
}
"""
************************* Socket resolve conversation ********************
* version: 1.0.2                                                         *
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau hoàn tất hội thoại
@apiDescription Socket nhận sau hoàn tất hội thoại
@apiGroup SocialSocket
@apiVersion 1.0.2
@apiName SocketResolveConversation

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'RESOLVED_CONVERSATION_SOCKET',
  'merchant_id': '',
  'social_type': 1,
  'page_id': '',
  'page_social_id': '',
  'data': {
    'resolve_object_id': '',
    'assign': [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "conversation_id": "486623b2-9d78-49eb-9897-41926cdf0244", 
        "created_time": "2019-09-10T10:43:46Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "d59d4779-b707-4558-a594-4eb65f106ccc", 
        "note": null, 
        "status": 1
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "conversation_id": "486623b2-9d78-49eb-9897-41926cdf0244", 
        "created_time": "2019-09-10T10:41:00Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "ca9c2a54-c95d-467e-9ecd-1a4cc01d7d65", 
        "note": null, 
        "status": 0
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "conversation_id": "486623b2-9d78-49eb-9897-41926cdf0244", 
        "created_time": "2019-08-23T05:25:23Z", 
        "created_user": null, 
        "id": "eada04e0-eae0-4244-ba16-af4fe5def8af", 
        "note": null, 
        "status": 0
      }
    ],
    'external_data': {}
  }
}
"""
====================
"""
@api NONE Socket nhận sau hoàn tất hội thoại
@apiDescription Socket nhận sau hoàn tất hội thoại
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketResolveConversation

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'RESOLVED_CONVERSATION_SOCKET',
  'merchant_id': '',
  'social_type': 1,
  'page_id': '',
  'page_social_id': '',
  'data': {
    'resolve_object_id': '',
    'assign': [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "conversation_id": "486623b2-9d78-49eb-9897-41926cdf0244", 
        "created_time": "2019-09-10T10:43:46Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "d59d4779-b707-4558-a594-4eb65f106ccc", 
        "note": null, 
        "status": 1
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "conversation_id": "486623b2-9d78-49eb-9897-41926cdf0244", 
        "created_time": "2019-09-10T10:41:00Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "ca9c2a54-c95d-467e-9ecd-1a4cc01d7d65", 
        "note": null, 
        "status": 0
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "conversation_id": "486623b2-9d78-49eb-9897-41926cdf0244", 
        "created_time": "2019-08-23T05:25:23Z", 
        "created_user": null, 
        "id": "eada04e0-eae0-4244-ba16-af4fe5def8af", 
        "note": null, 
        "status": 0
      }
    ]
  }
}
"""
====================
"""
@api NONE Socket nhận sau hoàn tất hội thoại
@apiDescription Socket nhận sau hoàn tất hội thoại
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketResolveConversation

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'RESOLVED_CONVERSATION_SOCKET',
  'merchant_id': '',
  'social_type': 1,
  'page_id': '',
  'page_social_id': '',
  'data': {
    'resolve_object_id': ''
  }
}
"""
************************* Socket resolve rating **************************
* version: 1.0.2                                                         *
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau hoàn tất đánh giá
@apiDescription Socket nhận sau hoàn tất đánh giá
@apiGroup SocialSocket
@apiVersion 1.0.2
@apiName SocketResolveRating

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'RESOLVED_RATING_SOCKET',
  'merchant_id': '',
  'social_type': 1,
  'page_id': '',
  'page_social_id': '',
  'data': {
    'resolve_object_id': '',
    'assign': [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "created_time": "2019-09-10T10:48:19Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "935a2b5a-cb4b-44e8-9755-4e38972a1f0d", 
        "note": null, 
        "rating_id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c", 
        "status": 1
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "created_time": "2019-08-05T09:57:50Z", 
        "created_user": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f", 
        "id": "b8584c4f-6c16-482f-88d8-e2b81a59a70f", 
        "note": null, 
        "rating_id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c", 
        "status": 0
      }, 
      {
        "assignee_id": "8ba5e082-393f-47e0-a4a4-b91643056583", 
        "created_time": "2019-08-05T09:57:43Z", 
        "created_user": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f", 
        "id": "6f11f242-df85-476f-8daf-3c597e9e6414", 
        "note": null, 
        "rating_id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c", 
        "status": 0
      }
    ],
    'external_data': {}
  }
}
"""
=================
"""
@api NONE Socket nhận sau hoàn tất đánh giá
@apiDescription Socket nhận sau hoàn tất đánh giá
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketResolveRating

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'RESOLVED_RATING_SOCKET',
  'merchant_id': '',
  'social_type': 1,
  'page_id': '',
  'page_social_id': '',
  'data': {
    'resolve_object_id': '',
    'assign': [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "created_time": "2019-09-10T10:48:19Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "935a2b5a-cb4b-44e8-9755-4e38972a1f0d", 
        "note": null, 
        "rating_id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c", 
        "status": 1
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "created_time": "2019-08-05T09:57:50Z", 
        "created_user": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f", 
        "id": "b8584c4f-6c16-482f-88d8-e2b81a59a70f", 
        "note": null, 
        "rating_id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c", 
        "status": 0
      }, 
      {
        "assignee_id": "8ba5e082-393f-47e0-a4a4-b91643056583", 
        "created_time": "2019-08-05T09:57:43Z", 
        "created_user": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f", 
        "id": "6f11f242-df85-476f-8daf-3c597e9e6414", 
        "note": null, 
        "rating_id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c", 
        "status": 0
      }
    ]
  }
}
"""
=================
"""
@api NONE Socket nhận sau hoàn tất đánh giá
@apiDescription Socket nhận sau hoàn tất đánh giá
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketResolveRating

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'RESOLVED_RATING_SOCKET',
  'merchant_id': '',
  'social_type': 1,
  'page_id': '',
  'page_social_id': '',
  'data': {
    'resolve_object_id': ''
  }
}
"""
************************* Socket assign tag comment **********************
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau gán tag phân công bình luận
@apiDescription Socket nhận sau gán tag phân công bình luận
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketAssignTagComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'ASSIGN_TAG_COMMENT_SOCKET',
  'merchant_id': '',
  'page_id': '',
  'page_social_id': '',
  'social_type': 1,
  'data': {
    "tag": ["d3593aa7-c045-4e37-bc60-591d6c14eb61"],
    "target": {
      "id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c",
      "type": 3
    },
    'assign': [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "comment_id": "cfdf935a-99bc-469d-816a-f21ac9d140ee", 
        "created_time": "2019-09-10T10:46:24Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "02470342-70d4-4910-bcbb-318e3f9fab31", 
        "note": null, 
        "status": 1
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "comment_id": "cfdf935a-99bc-469d-816a-f21ac9d140ee", 
        "created_time": "2019-09-10T00:39:31Z", 
        "created_user": null, 
        "id": "856646a1-0d63-406e-a4a4-48e8f4c72729", 
        "note": null, 
        "status": 0
      }, 
      {
        "assignee_id": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f", 
        "comment_id": "cfdf935a-99bc-469d-816a-f21ac9d140ee", 
        "created_time": "2019-09-10T00:09:32Z", 
        "created_user": null, 
        "id": "27e20ff6-d17e-4c52-a136-6bb5c8440119", 
        "note": null, 
        "status": 0
      }
    ]
  }
}
"""
=========================
"""
@api NONE Socket nhận sau gán tag phân công bình luận
@apiDescription Socket nhận sau gán tag phân công bình luận
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketAssignTagComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'ASSIGN_TAG_COMMENT_SOCKET',
  'merchant_id': '',
  'page_id': '',
  'page_social_id': '',
  'social_type': 1,
  'data': {
    "tag": ["d3593aa7-c045-4e37-bc60-591d6c14eb61"],
    "target": {
      "id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c",
      "type": 3
    }
  }
}
"""
************************* Socket assign tag conversation *****************
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau gán tag phân công hội thoại
@apiDescription Socket nhận sau gán tag phân công hội thoại
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketAssignTagConversation

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'ASSIGN_TAG_CONVERSATION_SOCKET',
  'merchant_id': '',
  'page_id': '',
  'page_social_id': '',
  'social_type': 1,
  'data': {
    "tag": ["d3593aa7-c045-4e37-bc60-591d6c14eb61"],
    "target": {
      "id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c",
      "type": 2
    },
    'assign': [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "conversation_id": "486623b2-9d78-49eb-9897-41926cdf0244", 
        "created_time": "2019-09-10T10:43:46Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "d59d4779-b707-4558-a594-4eb65f106ccc", 
        "note": null, 
        "status": 1
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "conversation_id": "486623b2-9d78-49eb-9897-41926cdf0244", 
        "created_time": "2019-09-10T10:41:00Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "ca9c2a54-c95d-467e-9ecd-1a4cc01d7d65", 
        "note": null, 
        "status": 0
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "conversation_id": "486623b2-9d78-49eb-9897-41926cdf0244", 
        "created_time": "2019-08-23T05:25:23Z", 
        "created_user": null, 
        "id": "eada04e0-eae0-4244-ba16-af4fe5def8af", 
        "note": null, 
        "status": 0
      }
    ]
  }
}
"""
====================
"""
@api NONE Socket nhận sau gán tag phân công hội thoại
@apiDescription Socket nhận sau gán tag phân công hội thoại
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketAssignTagConversation

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'ASSIGN_TAG_CONVERSATION_SOCKET',
  'merchant_id': '',
  'page_id': '',
  'page_social_id': '',
  'social_type': 1,
  'data': {
    "tag": ["d3593aa7-c045-4e37-bc60-591d6c14eb61"],
    "target": {
      "id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c",
      "type": 2
    }
  }
}
"""
************************* Socket assign tag rating ***********************
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau gán tag phân công đánh giá
@apiDescription Socket nhận sau gán tag phân công đánh giá
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketAssignTagRating

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'ASSIGN_TAG_RATING_SOCKET',
  'merchant_id': '',
  'page_id': '',
  'page_social_id': '',
  'social_type': 1,
  'data': {
    "tag": ["d3593aa7-c045-4e37-bc60-591d6c14eb61"],
    "target": {
      "id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c",
      "type": 4
    },
    'assign': [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "created_time": "2019-09-10T10:48:19Z", 
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "id": "935a2b5a-cb4b-44e8-9755-4e38972a1f0d", 
        "note": null, 
        "rating_id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c", 
        "status": 1
      }, 
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003", 
        "created_time": "2019-08-05T09:57:50Z", 
        "created_user": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f", 
        "id": "b8584c4f-6c16-482f-88d8-e2b81a59a70f", 
        "note": null, 
        "rating_id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c", 
        "status": 0
      }, 
      {
        "assignee_id": "8ba5e082-393f-47e0-a4a4-b91643056583", 
        "created_time": "2019-08-05T09:57:43Z", 
        "created_user": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f", 
        "id": "6f11f242-df85-476f-8daf-3c597e9e6414", 
        "note": null, 
        "rating_id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c", 
        "status": 0
      }
    ]
  }
}
"""
=====================
"""
@api NONE Socket nhận sau gán tag phân công đánh giá
@apiDescription Socket nhận sau gán tag phân công đánh giá
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketAssignTagRating

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'socket_type': 'ASSIGN_TAG_RATING_SOCKET',
  'merchant_id': '',
  'page_id': '',
  'page_social_id': '',
  'social_type': 1,
  'data': {
    "tag": ["d3593aa7-c045-4e37-bc60-591d6c14eb61"],
    "target": {
      "id": "a3e61017-7ba1-48c4-95f4-d6c69e71a28c",
      "type": 4
    }
  }
}
"""
************************* Socket reply comment topic *********************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau trả lời bình luận bài viết
@apiDescription Socket nhận sau trả lời bình luận bài viết
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketReplyCommentTopic

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "comment": {
      "classify": 1,
      "comment_social_id": "441874109747982_457778611490865",
      "content": "test 10",
      "created_time": "2019-09-10T10:59:44Z",
      "customer_id": null,
      "id": "73c53c8d-394b-40b1-93d0-f4626b5e4ea7",
      "is_read": 1,
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "rating_social_id": null,
      "reader": null,
      "social_type": "1",
      "state": 0,
      "status": 1,
      "topic_social_id": "164769917458404_441874109747982",
      "updated_time": "2019-09-10T10:59:44Z",
      "updated_user": null,
      "user_social_id": "164769917458404"
    }
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": "1",
  "socket_type": "REPLY_COMMENT_TOPIC_SOCKET"
}
"""
************************* Socket reply comment rating **************************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau trả lời bình luận đánh giá
@apiDescription Socket nhận sau trả lời bình luận đánh giá
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketReplyCommentRating

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "comment": {
      "classify": 1,
      "comment_social_id": "407727936659670_524909818274814",
      "content": "tesst 11",
      "created_time": "2019-09-10T11:01:59Z",
      "customer_id": null,
      "id": "a780be86-c170-414f-a9fa-cd313429e7eb",
      "is_read": 1,
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "rating_social_id": "407727936659670",
      "reader": null,
      "social_type": "1",
      "state": 0,
      "status": 1,
      "topic_social_id": null,
      "updated_time": "2019-09-10T11:01:59Z",
      "updated_user": null,
      "user_social_id": "164769917458404"
    }
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": "1",
  "socket_type": "REPLY_COMMENT_RATING_SOCKET"
}
"""
************************* Socket reply message ***************************
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau trả lời tin nhắn
@apiDescription Socket nhận sau trả lời tin nhắn
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketReplyMessage

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "conversation": {
      "conversation_social_id": "t_2364818863806484",
      "created_date": "2019-08-23T04:04:55Z",
      "id": "486623b2-9d78-49eb-9897-41926cdf0244",
      "message": "test 12",
      "page_social_id": "164769917458404",
      "social_type": 1,
      "updated_time": null,
      "user_social_id": "2228316327456739"
    },
    "message_social_id": ""
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": "1",
  "socket_type": "REPLY_MESSAGE_SOCKET"
}
"""
=======================
"""
@api NONE Socket nhận sau trả lời tin nhắn
@apiDescription Socket nhận sau trả lời tin nhắn
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketReplyMessage

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "conversation": {
      "conversation_social_id": "t_2364818863806484",
      "created_date": "2019-08-23T04:04:55Z",
      "id": "486623b2-9d78-49eb-9897-41926cdf0244",
      "message": "test 12",
      "page_social_id": "164769917458404",
      "social_type": 1,
      "updated_time": null,
      "user_social_id": "2228316327456739"
    }
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": "1",
  "socket_type": "REPLY_MESSAGE_SOCKET"
}
"""
************************* Socket post sub comment ************************
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau bình luận cấp 2 từ người dùng
@apiDescription Socket nhận sau bình luận cấp 2 từ người dùng
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketPostSubComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "classify": 1,
    "comment_id": "aa00ca52-7b14-491b-9ae7-232a42838600",
    "comment_social_id": "457129894889070_457798224822237",
    "content": "test 1111",
    "parent_social_id": "457129894889070_457199774882082",
    "topic_id": "43fa37f0-1338-401a-8427-fe566040c765",
    "topic_social_id": "164769917458404_457129894889070",
    "parent_id": "9e9ef4e4-fb68-4240-b61d-125fda673504",
    "parent_assign": [
      {
        "note": null,
        "status": 1,
        "comment_id": "9e9ef4e4-fb68-4240-b61d-125fda673504",
        "id": "8b930fa2-ca25-402a-b305-204229c38bd9",
        "created_user": null,
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2019-09-20T08:01:30Z"
      },
      {
        "note": null,
        "status": 0,
        "comment_id": "9e9ef4e4-fb68-4240-b61d-125fda673504",
        "id": "e476b052-346e-4e3e-925c-ae429659182d",
        "created_user": null,
        "assignee_id": "8ba5e082-393f-47e0-a4a4-b91643056583",
        "created_time": "2019-09-20T07:03:26Z"
      },
      {
        "note": null,
        "status": 0,
        "comment_id": "9e9ef4e4-fb68-4240-b61d-125fda673504",
        "id": "992cf06d-4223-4f7a-abdc-e85e52d8832e",
        "created_user": null,
        "assignee_id": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f",
        "created_time": "2019-09-20T06:47:25Z"
      }
    ]
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": 1,
  "socket_type": "POST_SUB_COMMENT_SOCKET"
}
"""
========================
"""
@api NONE Socket nhận sau bình luận cấp 2 từ người dùng
@apiDescription Socket nhận sau bình luận cấp 2 từ người dùng
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketPostSubComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "classify": 1,
    "comment_id": "aa00ca52-7b14-491b-9ae7-232a42838600",
    "comment_social_id": "457129894889070_457798224822237",
    "content": "test 1111",
    "parent_social_id": "457129894889070_457199774882082",
    "topic_id": "43fa37f0-1338-401a-8427-fe566040c765",
    "topic_social_id": "164769917458404_457129894889070"
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": 1,
  "socket_type": "POST_SUB_COMMENT_SOCKET"
}
"""
************************* Socket like topic **************************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau thích bài viết
@apiDescription Socket nhận sau thích bài viết
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketLikeTopic

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "reaction_type": "like",
    "topic_social_id": "164769917458404_457129894889070"
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": 1,
  "socket_type": "LIKE_TOPIC_SOCKET"
}
"""
************************* Socket dislike topic **************************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau bỏ thích bài viết
@apiDescription Socket nhận sau bỏ thích bài viết
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketDislikeTopic

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "topic_social_id": "164769917458404_457129894889070"
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": 1,
  "socket_type": "DISLIKE_TOPIC_SOCKET"
}
"""
************************* Socket like comment **************************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau thích bình luận
@apiDescription Socket nhận sau thích bình luận
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketLikeComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "comment_social_id": "457129894889070_457755051493221",
    "reaction_type": "like",
    "topic_social_id": "164769917458404_457129894889070"
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": 1,
  "socket_type": "LIKE_COMMENT_SOCKET"
}
"""
************************* Socket dislike comment **************************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau bỏ thích bình luận
@apiDescription Socket nhận sau bỏ thích bình luận
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketDislikeComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "comment_social_id": "457129894889070_457755051493221",
    "topic_social_id": "164769917458404_457129894889070"
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": 1,
  "socket_type": "DISLIKE_COMMENT_SOCKET"
}
"""
************************* Socket hide comment topic **********************
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau ẩn bình luận
@apiDescription Socket nhận sau ẩn bình luận
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketHideComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "f71b7416-d418-4b6e-a746-a599e0f1b50a",
  "data": {
    "comment_social_id": "381388835890976_381873739175819",
    "topic_social_id": "121358061894056_381388835890976",
    "assign": [
      {
        "note": null,
        "status": 1,
        "comment_id": "cb591bf1-3a48-4c5d-9c21-68554aadcdb0",
        "id": "2883d8f8-c626-4a8e-8732-2ba284bc17f3",
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2019-09-24T08:56:03Z"
      },
      {
        "note": null,
        "status": 0,
        "comment_id": "cb591bf1-3a48-4c5d-9c21-68554aadcdb0",
        "id": "5cdae171-dbd8-462e-9bb2-41dc1b186083",
        "created_user": null,
        "assignee_id": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f",
        "created_time": "2019-09-24T08:37:09Z"
      }
    ],
    "comment_id": "cb591bf1-3a48-4c5d-9c21-68554aadcdb0"
  },
  "socket_type": "HIDE_COMMENT_TOPIC_SOCKET",
  "page_social_id": "121358061894056",
  "social_type": 1
}
"""
==================
"""
@api NONE Socket nhận sau ẩn bình luận
@apiDescription Socket nhận sau ẩn bình luận
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketHideComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "comment_social_id": "457129894889070_457755051493221",
    "topic_social_id": "164769917458404_457129894889070"
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": 1,
  "socket_type": "HIDE_COMMENT_TOPIC_SOCKET"
}
"""
************************* Socket unhide comment topic ********************
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau hiển thị bình luận
@apiDescription Socket nhận sau hiển thị bình luận
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketUnhideComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "f71b7416-d418-4b6e-a746-a599e0f1b50a",
  "data": {
    "comment_social_id": "381388835890976_381873739175819",
    "topic_social_id": "121358061894056_381388835890976",
    "assign": [
      {
        "note": null,
        "status": 1,
        "comment_id": "cb591bf1-3a48-4c5d-9c21-68554aadcdb0",
        "id": "2883d8f8-c626-4a8e-8732-2ba284bc17f3",
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2019-09-24T08:56:03Z"
      },
      {
        "note": null,
        "status": 0,
        "comment_id": "cb591bf1-3a48-4c5d-9c21-68554aadcdb0",
        "id": "5cdae171-dbd8-462e-9bb2-41dc1b186083",
        "created_user": null,
        "assignee_id": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f",
        "created_time": "2019-09-24T08:37:09Z"
      }
    ],
    "comment_id": "cb591bf1-3a48-4c5d-9c21-68554aadcdb0"
  },
  "socket_type": "UNHIDE_COMMENT_TOPIC_SOCKET",
  "page_social_id": "121358061894056",
  "social_type": 1
}
"""
================================
"""
@api NONE Socket nhận sau hiển thị bình luận
@apiDescription Socket nhận sau hiển thị bình luận
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketUnhideComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "comment_social_id": "457129894889070_457755051493221",
    "topic_social_id": "164769917458404_457129894889070"
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": 1,
  "socket_type": "UNHIDE_COMMENT_TOPIC_SOCKET"
}
"""
************************* Socket edit comment topic **********************
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau sửa bình luận
@apiDescription Socket nhận sau sửa bình luận
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketEditComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "assign": [
      {
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "comment_id": "3ae80396-63fb-4aa2-8a79-3b2781dcecfd",
        "created_time": "2019-09-20T08:31:32Z",
        "created_user": null,
        "id": "51645c51-b036-4f4b-b223-ffac5c1ea62b",
        "note": null,
        "status": 1
      },
      {
        "assignee_id": "8ba5e082-393f-47e0-a4a4-b91643056583",
        "comment_id": "3ae80396-63fb-4aa2-8a79-3b2781dcecfd",
        "created_time": "2019-09-20T07:31:30Z",
        "created_user": null,
        "id": "c5886406-e8ef-4f23-8972-c1bcd6d7e16f",
        "note": null,
        "status": 0
      }
    ],
    "classify": 1,
    "comment_id": "3ae80396-63fb-4aa2-8a79-3b2781dcecfd",
    "comment_social_id": "461282557807137_463549414247118",
    "content": "test 1011",
    "photo": "https://scontent.fhan3-1.fna.fbcdn.net/v/t1.0-9/70662698_2515765475169260_3789695003857518592_n.jpg?_nc_cat=111&_nc_oc=AQk6wVVWgJwsJ26Ro2RCLng8llXVHiGpLs0XkK7VPR2m5cyMIqHIx7Vdk6nGW497vdI&_nc_ht=scontent.fhan3-1.fna&oh=811dcf63e5b7c9e2aa4d0bf2a7e77d87&oe=5DFBE681",
    "topic_social_id": "164769917458404_461282557807137",
    "video": null
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": 1,
  "socket_type": "EDIT_COMMENT_TOPIC_SOCKET"
}
"""
===================
"""
@api NONE Socket nhận sau sửa bình luận
@apiDescription Socket nhận sau sửa bình luận
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketEditComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "classify": 1,
    "comment_id": null,
    "comment_social_id": "457129894889070_457755051493221",
    "content": "test 22",
    "photo": null,
    "topic_social_id": "164769917458404_457129894889070",
    "video": null
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": 1,
  "socket_type": "EDIT_COMMENT_TOPIC_SOCKET"
}
"""
************************* Socket delete comment topic ********************
* version: 1.0.1                                                         *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau xóa bình luận
@apiDescription Socket nhận sau xóa bình luận
@apiGroup SocialSocket
@apiVersion 1.0.1
@apiName SocketDeleteComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "data": {
    "comment_social_id": "461282557807137_466038187331574",
    "topic_social_id": "164769917458404_461282557807137",
    "assign": [
      {
        "note": null,
        "status": 1,
        "comment_id": "ebe35d98-8830-470c-baaa-ddc69d9e29f9",
        "id": "01b9c1d8-201c-4b68-bfc4-cd5f9cdae1aa",
        "created_user": null,
        "assignee_id": "512cd02a-07fd-4ad1-9e3d-a251dbe53c4f",
        "created_time": "2019-09-24T09:38:43Z"
      }
    ],
    "comment_id": "ebe35d98-8830-470c-baaa-ddc69d9e29f9"
  },
  "socket_type": "DELETE_COMMENT_TOPIC_SOCKET",
  "page_social_id": "164769917458404",
  "social_type": 1
}
"""
=======================
"""
@api NONE Socket nhận sau xóa bình luận
@apiDescription Socket nhận sau xóa bình luận
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketDeleteComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "comment_social_id": "457129894889070_457755051493221",
    "topic_social_id": "164769917458404_457129894889070"
  },
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "page_social_id": "164769917458404",
  "social_type": 1,
  "socket_type": "DELETE_COMMENT_TOPIC_SOCKET"
}
"""
*********************** Socket classify comment topic ********************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau phân loại bình luận
@apiDescription Socket nhận sau phân loại bình luận
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketClassifyComment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "data": {
    "comment_social_id": "459529091315817_463628024239257",
    "assign": [
      {
        "note": "",
        "status": 1,
        "comment_id": "c3f2a577-8a3a-49d7-a2df-8c9e6f53d6a3",
        "id": "cdcb090c-3573-4dc9-8445-8018f57ce2ca",
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2019-09-24T03:22:50Z"
      },
      {
        "note": "",
        "status": 0,
        "comment_id": "c3f2a577-8a3a-49d7-a2df-8c9e6f53d6a3",
        "id": "70c0c4fa-fe18-4ab9-90c3-4bce00515f14",
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2019-09-23T08:38:24Z"
      }
    ],
    "comment_id": "c3f2a577-8a3a-49d7-a2df-8c9e6f53d6a3",
    "classify": 2
  },
  "socket_type": "CLASSIFY_COMMENT_SOCKET",
  "page_social_id": "164769917458404",
  "social_type": 1
}
"""
*********************** Socket revoke comment assign *********************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau thu hồi phân công bình luận
@apiDescription Socket nhận sau thu hồi phân công bình luận
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketRevokeCommentAssign

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'page_id': '',
  'page_social_id': '',
  'social_type': 1,
  'socket_type': 'REVOKE_COMMENT_ASSIGN_SOCKET',
  'merchant_id': '',
  'data': {
      'comment_id': '',
      'comment_social_id': '',
      'topic_social_id': '',
      'topic_id': ''
  }
}
"""
*********************** Socket revoke conversation assign ****************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau thu hồi phân công hội thoại
@apiDescription Socket nhận sau thu hồi phân công hội thoại
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketRevokeConversationAssign

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'page_id': '',
  'page_social_id': '',
  'social_type': 1,
  'socket_type': 'REVOKE_CONVERSATION_ASSIGN_SOCKET',
  'merchant_id': '',
  'data': {
    'conversation_id': '',
    'conversation_social_id': ''
  }
}
"""
*********************** Socket revoke rating assign **********************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau thu hồi phân công đánh giá
@apiDescription Socket nhận sau thu hồi phân công đánh giá
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketRevokeRatingAssign

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  'page_id': '',
  'page_social_id': '',
  'social_type': 1,
  'socket_type': 'REVOKE_RATING_ASSIGN_SOCKET',
  'merchant_id': '',
  'data': {
    'rating_id': '',
    'rating_social_id': ''
  }
}
"""
*************** Socket update assign tag activity status *****************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api NONE Socket nhận sau khi cập nhật trạng thái gán tag hành vi
@apiDescription Socket nhận sau khi cập nhật trạng thái gán tag hành vi
@apiGroup SocialSocket
@apiVersion 1.0.0
@apiName SocketUpdateAssignTagActivityStatus

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "object_type": 2,
  "social_type": 1,
  "object_id": "a100bd84-82b7-4848-9290-654dce511b92",
  "assign_tag_activity_status": 1,
  "socket_type": "UPDATE_ASSIGN_TAG_ACTIVITY_STATUS_SOCKET",
  "page_id": "12bff8f3-ffd4-40df-9095-0ab4edc9fbf7",
  "object_assignee_id": "ed404763-4dc5-4171-ae25-c2f3a99c81bf",
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_social_id": "858617877680201"
}
"""
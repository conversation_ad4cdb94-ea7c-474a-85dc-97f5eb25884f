#!/usr/bin/python
# -*- coding: utf8 -*-

***************************************  List conversation   ***************************************
* version: 1.0.1                                                                                   *
****************************************************************************************************
"""
@api {get} /merchants/<merchant_id>/socials/<social_type>/pages/<page_id>/conversations Lấy danh sách conversation
@apiDescription Lấy danh sách conversation của một page.
@apiGroup Conversation
@apiVersion 1.0.1
@apiName ListConversation

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse social_type_path
@apiParam   (Resource:)     {String}    merchant_id     Định danh của nhãn hàng
@apiParam   (Resource:)     {String}    page_id     Định danh của page

@apiSuccess   {Conversation[]}    conversations   Danh sách các hội thoại của nhãn hàng với khách hàng. Xem chi tiết đối tượng <code>conversation</code>.

@apiSuccess   (Conversation)    {String}    id    Định danh của đoạn hội thoại.
@apiSuccess   (Conversation)    {String}    conversation_social_id    Định danh của đoạn hội thoại trên mạng xã hội.
@apiUse social_type_success
@apiSuccess   (Conversation)    {String}    title    Tiêu đề của đoạn hội thoại, thường là message cuối cùng của đoạn hội thoại.
@apiSuccess   (Conversation)    {Datetime}    updated_time    Thời điểm cập nhật
@apiSuccess   (Conversation)    {Number}    message_count    Số lượng tin nhắn trong đoạn hội thoại.
@apiSuccess   (Conversation)    {Number}    unread_count    Số lượng tin nhắn chưa đọc.
@apiSuccess     (Conversation)     {Number=1-NORMAL;2-RESOLVED;}   status Trạng thái của conversation.
@apiSuccess   (Conversation)   {Number=0-NEGATIVE; 1-NEUTRAL; 2-POSITIVE}  classify    Giá trị phân loại content.
@apiSuccess   (Conversation)    {Number}    status    Số lượng tin nhắn chưa đọc.
@apiSuccess   (Conversation)    {Assignee[]}    [assignees]    Danh sách thông tin phân công. Xem chi tiết đối tượng <code>Assignee</code>.
@apiSuccess   (Conversation)    {Participant[]}    participants    Danh sách thông tin người tham gia hội thoại. Thông thường là khách hàng và nhãn hàng. Xem chi tiết đối tượng <code>Participant</code>.
@apiSuccess   (Conversation)    {Tag[]}     [tags]    Danh sách nhãn được gán với hội thoại. <a href="#api-Tags-ListTags">Xem mô tả <code>Tag</code></a>

@apiSuccess   (Assignee)    {String}    id    Định danh của lần phân công.
@apiSuccess   (Assignee)    {String}    [note]  Nội dung ghi chú khi phân công.
@apiSuccess   (Assignee)    {Datetime}  created_time  Thời điểm phân công
@apiSuccess   (Assignee)    {String}    assignee_id  Định danh của người được phân công.
@apiSuccess   (Assignee)    {String}    created_user  Định danh của người phân công.

@apiSuccess   (Participant)    {String}    user_social_id   Định danh trên mạng xã hội của người tham gia hội thoại.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code":"001",
  "conversations":[
      {
        "id": "0c28c20b-2cd3-4050-9a43-1a288256edd4",
        "conversation_social_id": "2345678",
        "social_type": 2,
        "title": "",
        "updated_time": "2017-08-07T04:02:28.002Z",
        "message_count": 12,
        "unread_count": 10,
        "status": 1,
        "classify": 2,
        "assignees":[
          {
            "id":"9d6b9bae-1af8-497f-961a-26b7fb44474b",
            "note":"Khách hàng mới",
            "created_time":"2017-08-07T04:02:28.002Z",
            "assignee_id":"84d4a00d-37b4-4549-bc69-e13d1f3a258f",
            "created_user":"2a9bd2e2-df8a-4979-8e4d-ce7ded983f8c"
          }
        ],
        "participants":[
          {
            "user_social_id": "50078da6-2dea-4291-8155-3662746e612a"
          },
          {
            "user_social_id": "164422bc-54c4-4353-9805-204733c57da4"
          }
        ],
        "tags":[
          {
              "id": "93772013-4510-41f4-83aa-8a9362f2b6bb",
              "value": "phục vụ",
              "properties": {
                  "background_color": "#ff0000",
                  "foreground_color": "#ffffff"
              }
          },
          {
              "id": "fc5fdf88-89fd-489e-adcd-5658123f1f79",
              "value": "thẻ thành viên",
              "properties": {
                  "background_color": "#ff0000",
                  "foreground_color": "#ffffff"
              }
          }
        ]
      }
  ]
}
"""

"""
@api {post} /merchants/<merchant_id>/socials/<social_type>/pages/<page_id>/conversations/<conversation_id>/read Đánh dấu conversation đã đọc
@apiDescription Đánh dấu conversation đã đọc. Cập nhật số lượng tin chưa đọc = 0.
@apiGroup Conversation
@apiVersion 1.0.1
@apiName MarkRead

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code":"001",
    "conversation":{
        "id":"daa595ab-de88-4fac-b8d6-e431c62eab1d",
        "conversation_social_id":"",
        "unread_number":0
    }
}
"""

"""
@api {get} /merchants/<merchant_id>/socials/<social_type>/pages/<page_id>/conversations/actions/search Tìm kiếm conversation
@apiDescription Tìm kiếm conversation theo id trên mạng xã hội của khách hàng.
@apiGroup Conversation
@apiVersion 1.0.0
@apiName SearchConversation

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    user_social_id  Id trên mạng xã hội của user. Example: <code>&user_social_id=23452345</code>

@apiError     (ErrorCode)     {String}  013 Không tìm thấy hội thoại. (Do khách hàng chưa từng message với page).
@apiErrorExample    {json}  013 example:
{
    "code":"013",
    "message":"Không tìm thấy"
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code":"001",
    "conversation":{
        "id":"fb97a2f9-ea5d-40ba-a722-19d0fa7b22c0",
        "conversation_social_id":"",
        "status":1,
        "classify":2,
        "is_apply_bot":1
    }
}
"""

"""
@api {post} /merchants/<merchant_id>/socials/<social_type>/pages/<page_id>/conversations/<conversation_id>/actions/resolve Resolve a conversation
@apiGroup Conversation
@apiVersion 1.0.2
@apiName ResolveConversation

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)   {Object}    [external_data]  Thông tin thêm.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code":"001"
}
"""

"""
@api {post} /merchants/<merchant_id>/socials/<social_type>/pages/<page_id>/conversations/<conversation_id>/actions/resolve Resolve a conversation
@apiGroup Conversation
@apiVersion 1.0.1
@apiName ResolveConversation

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code":"001"
}
"""

"""
@api {patch} /merchants/<merchant_id>/socials/<social_type>/pages/<page_id>/conversations/<conversation_id>/actions/classify Phân loại conversation
@apiGroup Conversation
@apiVersion 1.0.1
@apiName ClassifyConversation
@apiDescription Dịch vụ để nhân viên phân loại comment là Tích cực, Trung lập hay Tiêu cực. Sử dụng <b>Content-type</b>: <code>form-urlencoded</code>.

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse classify_body_param
@apiParamExample    {form}  Body example:
- classify: {Number} 2

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code":"001",
    "conversation":{
        "id":"fb97a2f9-ea5d-40ba-a722-19d0fa7b22c0",
        "conversation_social_id":"",
        "classify":2
    }
}
"""

"""
@api {patch} /merchants/<merchant_id>/socials/<social_type>/pages/<page_id>/conversations/<conversation_id>/actions/classify Phân loại conversation
@apiGroup Conversation
@apiVersion 1.0.0
@apiName ClassifyConversation
@apiDescription Dịch vụ để nhân viên phân loại comment là Tích cực, Trung lập hay Tiêu cực. Sử dụng <b>Content-type</b>: <code>form-data</code>.

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse classify_body_param
@apiParamExample    {form}  Body example:
- classify: {Number} 2

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code":"001",
    "conversation":{
        "id":"fb97a2f9-ea5d-40ba-a722-19d0fa7b22c0",
        "conversation_social_id":"",
        "classify":2
    }
}
"""

***************************************  Sync conversation   ***************************************
* version: 1.0.2                                                                                   *
* version: 1.0.1                                                                                   *
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /merchants/<merchant_id>/socials/<social_type>/pages/<page_id>/conversations Đồng bộ danh sách conversation
@apiDescription Dịch vụ lưu và truy xuất thông tin assign của conversation.
- Nếu conversation cần lấy thông tin chưa được lưu trong hệ thống --> hệ thống tự động lưu conversation này lại;
- Nếu conversation đã từng được lưu --> return thông tin assign;
@apiGroup Conversation
@apiVersion 1.0.2
@apiName SyncConversation

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {Conversation[]}   conversations   Danh sách các conversation cần lấy thông tin. Mô tả chi tiết conversation's object:
<li><code>conversation_social_id</code>: id của conversation trên mạng xã hội</li>
<li><code>message</code>(optional): Nội dung của message cuối cùng trong hội thoại. Ví dụ: với Facebook sử dụng key: snippet</li>
<li><code>user_social_id</code>: Id của khách hàng(lấy từ social network) </li>
<li><code>last_post_time</code>(optional): Thời điểm gửi tin nhắn cuối cùng</li>
@apiParamExample    {json}  Body Example:
{
    "conversations":[
        {
            "conversation_social_id":"3KJG13G1G23K12K3",
            "message":"",
            "user_social_id":"",
            "last_post_time":"2017-08-07T04:02:28.002Z"
        },
        {
            "conversation_social_id":"2132312398123",
            "message":"",
            "user_social_id":"",
            "last_post_time":"2017-08-07T04:02:28.002Z"
        }
    ]
}

@apiSuccess     {String}   id  Id của conversation.
@apiSuccess     {Assignee[]} assignees   Danh sách nhân viên đã từng được assign với conversation này. <code>Empty</code> nếu conversation chưa được assign.<br/>
Chi tiết <code>assignee object</code>:
<li><code>id</code>: Định danh;</li>
<li><code>note</code>: Ghi chú;</li>
<li><code>created_time</code>: Thời điểm phân công;</li>
<li><code>assignee_id</code>: Id của nhân viên được phân công;</li>
<li><code>created_user</code>: Id của nhân viên phân công;</li>
@apiSuccess     {Number=0-OFF;1-ON} is_apply_bot    Trạng thái cho biết conversation này đang được support bởi Bot hay nhân viên(Staff).
@apiSuccess     {Number}    unread_comments Số lượng message chưa được xem.
@apiUse status_conversation_success
@apiUse classify_success
@apiSuccess    {Tag[]}     [tags]    Danh sách nhãn được gán với hội thoại. <a href="#api-Tags-ListTags">Xem mô tả <code>Tag</code></a>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code":"001",
    "conversations":[
        {
            "id":"6d46bfc9-2243-492d-a25f-57239c1fe227",
            "social_type":1
            "conversation_social_id":"3KJG13G1G23K12K3",
            "title":"",
            "status":1,
            "assignees":[
                {
                    "id":"9d6b9bae-1af8-497f-961a-26b7fb44474b",
                    "note":"Khách hàng mới",
                    "created_time":"2017-08-07T04:02:28.002Z",
                    "assignee_id":"84d4a00d-37b4-4549-bc69-e13d1f3a258f",
                    "created_user":"2a9bd2e2-df8a-4979-8e4d-ce7ded983f8c"
                }
            ],
            "is_apply_bot":1
            "unread_number":123
            "classify":2,
            "tags":[
              {
                "id": "93772013-4510-41f4-83aa-8a9362f2b6bb",
                "value": "phục vụ",
                "properties": {
                    "background_color": "#ff0000",
                    "foreground_color": "#ffffff"
              }
              },
              {
                  "id": "fc5fdf88-89fd-489e-adcd-5658123f1f79",
                  "value": "thẻ thành viên",
                  "properties": {
                      "background_color": "#ff0000",
                      "foreground_color": "#ffffff"
                  }
              }
            ]
        },
        {
            "id":"6c4674bd-2e60-4018-84bf-03546ff133ca",
            "social_type":1
            "conversation_social_id":"2132312398123",
            "title":"",
            "status":2,
            "assignees":[
                {
                    "id":"9d6b9bae-1af8-497f-961a-26b7fb44474b",
                    "note":"Khách hàng mới",
                    "created_time":"2017-08-07T04:02:28.002Z",
                    "assignee_id":"84d4a00d-37b4-4549-bc69-e13d1f3a258f",
                    "created_user":"2a9bd2e2-df8a-4979-8e4d-ce7ded983f8c"
                }
            ],
            "is_apply_bot":0
            "unread_number":456
            "classify":2,
            "tags":[
              {
                "id": "93772013-4510-41f4-83aa-8a9362f2b6bb",
                "value": "phục vụ",
                "properties": {
                    "background_color": "#ff0000",
                    "foreground_color": "#ffffff"
              }
              },
              {
                  "id": "fc5fdf88-89fd-489e-adcd-5658123f1f79",
                  "value": "thẻ thành viên",
                  "properties": {
                      "background_color": "#ff0000",
                      "foreground_color": "#ffffff"
                  }
              }
            ]
        }
    ]
}
"""
******************
"""
@api {post} /merchants/<merchant_id>/socials/<social_type>/pages/<page_id>/conversations Đồng bộ danh sách conversation
@apiDescription Dịch vụ lưu và truy xuất thông tin assign của conversation.
- Nếu conversation cần lấy thông tin chưa được lưu trong hệ thống --> hệ thống tự động lưu conversation này lại;
- Nếu conversation đã từng được lưu --> return thông tin assign;
@apiGroup Conversation
@apiVersion 1.0.1
@apiName SyncConversation

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {Conversation[]}   conversations   Danh sách các conversation cần lấy thông tin. Mô tả chi tiết conversation's object:
<li><code>conversation_social_id</code>: id của conversation trên mạng xã hội</li>
<li><code>message</code>(optional): Nội dung của message cuối cùng trong hội thoại. Ví dụ: với Facebook sử dụng key: snippet</li>
<li><code>user_social_id</code>: Id của khách hàng(lấy từ social network) </li>
<li><code>last_post_time</code>(optional): Thời điểm gửi tin nhắn cuối cùng</li>
@apiParamExample    {json}  Body Example:
{
    "conversations":[
        {
            "conversation_social_id":"3KJG13G1G23K12K3",
            "message":"",
            "user_social_id":"",
            "last_post_time":"2017-08-07T04:02:28.002Z"
        },
        {
            "conversation_social_id":"2132312398123",
            "message":"",
            "user_social_id":"",
            "last_post_time":"2017-08-07T04:02:28.002Z"
        }
    ]
}

@apiSuccess     {String}   id  Id của conversation.
@apiSuccess     {Assignee[]} assignees   Danh sách nhân viên đã từng được assign với conversation này. <code>Empty</code> nếu conversation chưa được assign.<br/>
Chi tiết <code>assignee object</code>:
<li><code>id</code>: Định danh;</li>
<li><code>note</code>: Ghi chú;</li>
<li><code>created_time</code>: Thời điểm phân công;</li>
<li><code>assignee_id</code>: Id của nhân viên được phân công;</li>
<li><code>created_user</code>: Id của nhân viên phân công;</li>
@apiSuccess     {Number=0-OFF;1-ON} is_apply_bot    Trạng thái cho biết conversation này đang được support bởi Bot hay nhân viên(Staff).
@apiSuccess     {Number}    unread_comments Số lượng message chưa được xem.
@apiUse status_conversation_success
@apiUse classify_success

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code":"001",
    "conversations":[
        {
            "id":"6d46bfc9-2243-492d-a25f-57239c1fe227",
            "social_type":1
            "conversation_social_id":"3KJG13G1G23K12K3",
            "title":"",
            "status":1,
            "assignees":[
                {
                    "id":"9d6b9bae-1af8-497f-961a-26b7fb44474b",
                    "note":"Khách hàng mới",
                    "created_time":"2017-08-07T04:02:28.002Z",
                    "assignee_id":"84d4a00d-37b4-4549-bc69-e13d1f3a258f",
                    "created_user":"2a9bd2e2-df8a-4979-8e4d-ce7ded983f8c"
                }
            ],
            "is_apply_bot":1
            "unread_number":123
            "classify":2
        },
        {
            "id":"6c4674bd-2e60-4018-84bf-03546ff133ca",
            "social_type":1
            "conversation_social_id":"2132312398123",
            "title":"",
            "status":2,
            "assignees":[
                {
                    "id":"9d6b9bae-1af8-497f-961a-26b7fb44474b",
                    "note":"Khách hàng mới",
                    "created_time":"2017-08-07T04:02:28.002Z",
                    "assignee_id":"84d4a00d-37b4-4549-bc69-e13d1f3a258f",
                    "created_user":"2a9bd2e2-df8a-4979-8e4d-ce7ded983f8c"
                }
            ],
            "is_apply_bot":0
            "unread_number":456
            "classify":2
        }
    ]
}
"""
******************
"""
@api {post} /merchants/<merchant_id>/socials/<social_type>/pages/<page_id>/conversations Đồng bộ danh sách conversation
@apiDescription Dịch vụ lưu và truy xuất thông tin assign của conversation.
- Nếu conversation cần lấy thông tin chưa được lưu trong hệ thống --> hệ thống tự động lưu conversation này lại;
- Nếu conversation đã từng được lưu --> return thông tin assign;
@apiGroup Conversation
@apiVersion 1.0.0
@apiName SyncConversation

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {Conversation[]}   conversations   Danh sách các conversation cần lấy thông tin. Mô tả chi tiết conversation's object:
<li><code>conversation_social_id</code>: id của conversation trên mạng xã hội</li>
<li><code>message</code>(optional): Nội dung của message cuối cùng trong hội thoại. Ví dụ: với Facebook sử dụng key: snippet</li>
@apiParamExample    {json}  Conversation object Example:
{
    "conversations":[
        {
            "conversation_social_id":"3KJG13G1G23K12K3",
            "message":""
        },
        {
            "conversation_social_id":"2132312398123",
            "message":""
        }
    ]
}

@apiSuccess     {String}   id  Id của conversation.
@apiSuccess     {Assignee[]} assignees   Danh sách nhân viên đã từng được assign với conversation này. <code>Empty</code> nếu conversation chưa được assign.<br/>
Chi tiết <code>assignee object</code>:
<li><code>id</code>: Định danh;</li>
<li><code>note</code>: Ghi chú;</li>
<li><code>created_time</code>: Thời điểm phân công;</li>
<li><code>assignee_id</code>: Id của nhân viên được phân công;</li>
<li><code>created_user</code>: Id của nhân viên phân công;</li>
@apiSuccess     {Number=0-OFF;1-ON} is_apply_bot    Trạng thái cho biết conversation này đang được support bởi Bot hay nhân viên(Staff).
@apiSuccess     {Number}    unread_comments Số lượng message chưa được xem.
@apiUse status_conversation_success
@apiUse classify_success

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code":"001",
    "conversations":[
        {
            "id":"6d46bfc9-2243-492d-a25f-57239c1fe227",
            "social_type":1
            "conversation_social_id":"3KJG13G1G23K12K3",
            "title":"",
            "status":1,
            "assignees":[
                {
                    "id":"9d6b9bae-1af8-497f-961a-26b7fb44474b",
                    "note":"Khách hàng mới",
                    "created_time":"2017-08-07T04:02:28.002Z",
                    "assignee_id":"84d4a00d-37b4-4549-bc69-e13d1f3a258f",
                    "created_user":"2a9bd2e2-df8a-4979-8e4d-ce7ded983f8c"
                }
            ],
            "is_apply_bot":1
            "unread_number":123
            "classify":2
        },
        {
            "id":"6c4674bd-2e60-4018-84bf-03546ff133ca",
            "social_type":1
            "conversation_social_id":"2132312398123",
            "title":"",
            "status":2,
            "assignees":[
                {
                    "id":"9d6b9bae-1af8-497f-961a-26b7fb44474b",
                    "note":"Khách hàng mới",
                    "created_time":"2017-08-07T04:02:28.002Z",
                    "assignee_id":"84d4a00d-37b4-4549-bc69-e13d1f3a258f",
                    "created_user":"2a9bd2e2-df8a-4979-8e4d-ce7ded983f8c"
                }
            ],
            "is_apply_bot":0
            "unread_number":456
            "classify":2
        }
    ]
}
"""
*************************************  Resolve conversation 'for' chat tool  ***********************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /socials/7/pages/<page_social_id>/user/<user_social_id> Resolve conversation for chat tool
@apiDescription API resolve conversation for chat tool
@apiGroup Conversation
@apiVersion 1.0.0
@apiName ResolveConversationForChatTool

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code":"001",
    "message": "Request thành công"
}
"""
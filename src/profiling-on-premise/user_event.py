######################### Import B2C  ###########################
# version: 1.0.0
############################################################################################
"""
@api {post} https://{domain}/events/api/v1.0/systems/e33/b2c  Import B2C
@apiDescription API import B2C user_event 
@apiVersion 1.0.0
@apiGroup Transaction
@apiName ImportB2C

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)   {string}        merchant_id                 UUID merchant_id quản lý khách hàng
@apiParam (Body:)   {string}       booking_date                Ngày booking (YYYY-MM-DD)
@apiParam (Body:)   {string}       business_code               Business code mà khách hàng tương tác:
<li><code>SMARTWF_PXP_1010:  Fansipan</code></li>
<li><code>SMARTWF_HALONG_1010:  HaLong</code></li>
@apiParam (Body:)   {Array[]}       datas                       Tập thông tin khách hàng. Tối đa 1000 phần tử
@apiParam (datas:)   {string}       phone_number                SDT của Khách hàng
@apiParam (datas:)   {string}       email                       Email của Khách hàng
@apiParam (datas:)   {string}       name                        Tên của Khách hàng
@apiParam (datas:)   {string}       people_id                   Số CMND/CCCD của Khách hàng
@apiParam (datas:)   {int}          quantity_of_ticket          Số lượng vé Khách hàng mua
@apiParam (datas:)   {string}       booking_time                Thời gian user tương tác (timestamp)
@apiParam (datas:)   {string}       check_in_day                Ngày sử dụng ticket (timestamp)


@apiParamExample    {json}      Body example:
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "business_code": "SMARTWF_PXP_1010",
  "booking_date": "2018-11-11",
  "datas": [
    {
      "booking_time": "1538703463.440775",
      "check_in_day": "1544576400.000000"
      "phone_number": "0986223344",
      "email": "<EMAIL>",
      "name": "Nguyen Van A",     
      "people_id": "012xxxxxx",   
      "quantity_of_ticket": 2    
    },
    {
      "booking_time": "1538803663.440775",
      "check_in_day": "1544586400.000000"
      "phone_number": "0912789987",
      "email": "<EMAIL>",
      "name": "Nguyen Thi A",     
      "people_id": "012xxxxxx",   
      "quantity_of_ticket": 5
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "code": 200,
   "lang": "vi",
   "message": "request thành công."
}
"""

######################### Import B2B  ###########################
# version: 1.0.0
############################################################################################
"""
@api {post} https://{domain}/events/api/v1.0/systems/e32/b2b  Import B2B
@apiDescription API import B2B user_event 
@apiVersion 1.0.0
@apiGroup Transaction
@apiName ImportB2B

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)   {string}        merchant_id                 UUID merchant_id quản lý khách hàng
@apiParam (Body:)   {string}        booking_date                Ngày booking (YYYY-MM-DD)
@apiParam (Body:)   {string}       business_code               Business code mà khách hàng tương tác:
<li><code>SMARTWF_PXP_1010:  Fansipan</code></li>
<li><code>SMARTWF_HALONG_1010:  HaLong</code></li>
@apiParam (Body:)   {Array[]}       datas                       Tập thông tin khách hàng. Tối đa 1000 phần tử
@apiParam (datas:)   {string}       phone_number                SDT của Khách hàng
@apiParam (datas:)   {string}       email                       Email của Khách hàng
@apiParam (datas:)   {string}       name                        Tên của Khách hàng
@apiParam (datas:)   {string}       people_id                   Số CMND/CCCD của Khách hàng
@apiParam (datas:)   {int}          quantity_of_ticket          Số lượng vé Khách hàng mua
@apiParam (datas:)   {string}       booking_time                Thời gian user tương tác (timestamp)
@apiParam (datas:)   {string}       check_in_day                Ngày sử dụng ticket (timestamp)
@apiParam (datas:)   {string}       company_id                  Uuid Travel Agency
@apiParam (datas:)   {string}       company_name                Tên của công ty Travel Agency


@apiParamExample    {json}      Body example:
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "business_code": "SMARTWF_HALONG_1010",
  "booking_date": "2018-11-11",
  "datas": [
    {
      "booking_time": "1538703463.440775",
      "check_in_day": "1544576400.000000"
      "phone_number": "0986223344",
      "email": "<EMAIL>",
      "name": "Nguyen Van A",     
      "people_id": "012xxxxxx",   
      "quantity_of_ticket": 2,
      "company_id": "uuid",
      "company_name": "Cong Ty Du Lich A"
    },
    {
      "booking_time": "1538703463.440775",
      "check_in_day": "1544576400.000000"
      "phone_number": "0912789987",
      "email": "<EMAIL>",
      "name": "Nguyen Thi A",     
      "people_id": "012xxxxxx",   
      "quantity_of_ticket": 5,
      "company_id": "uuid",
      "company_name": "Cong Ty Du Lich B"
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "code": 200,
   "lang": "vi",
   "message": "request thành công."
}
"""
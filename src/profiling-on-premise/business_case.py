#!/usr/bin/python
# -*- coding: utf8 -*-

******************************* Get business-cases ***********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {get} [HOST]/profiling/v2.0/business-cases Get list business case.
@apiDescription Get all business case
@apiGroup BusinessCase
@apiVersion 1.0.0
@apiName GetListBusinessCase

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String[]}    [merchant_ids] If present, only get business case belong to merchant.<br/>
If not, get all business of system.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "data":[
        {
            "id": "27aeaa12-2d72-4441-b888-b7c26d6903a0",
            "name": "Big Campaign",
            "start": "2018-05-14T00:00:00",
            "end": "2018-08-14T00:00:00",
            "code": "",
            "keyword": ""
        }
    ]
}
"""
****************************** Criterial Key **********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@apiDefine critetia_key_body
@apiVersion 1.0.0
@apiParam   (profile_filter:) {String}    criteria_key    Các criteria key để filter khách hàng. Allowed values:<br/>
<li><code>cri_address</code>: Address
<li><code>cri_age</code>: Age
<li><code>cri_birthday</code>: Birthday
<li><code>cri_birthday_period</code>: Sinh nhật theo chu kỳ.
<li><code>cri_business_case</code>: Business case
<li><code>cri_card_level</code>: Loại thẻ
<li><code>cri_card_status</code>: Customer card status
<li><code>cri_city</code>: City
<li><code>cri_created_account_type</code>: Nguồn ghi nhận khách hàng
<li><code>cri_gender</code>: gender
<li><code>cri_hobby</code>: Hobby
<li><code>cri_job</code>: Job
<li><code>cri_marital_status</code>: Tình trạng hôn nhân
<li><code>cri_mkt_action_value</code>: Marketing action value
<li><code>cri_mkt_business_case_id</code>: MKT Business ID
<li><code>cri_mkt_campaign_id</code>: MKT Campaign ID
<li><code>cri_mkt_process_type</code>: Process type
<li><code>cri_mkt_root_process</code>: MKT root_process_id
<li><code>cri_mkt_step</code>: Loại khách hàng marketing
<li><code>cri_operation</code>: Lĩnh vực hoạt động
<li><code>cri_region</code>: Region
<li><code>cri_tags</code>: Lọc theo tags
"""

****************************** Operator Key ***********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@apiDefine operator_key_body
@apiVersion 1.0.0
@apiParam   (profile_filter:) {String="op_is_between","op_is_greater_equal","op_is_in","op_is_equal","op_is_greater","op_is_has","op_is_has_not","op_is_less_equal","op_is_less","op_is_regex"}    operator_key    Các toán tử để filter khách hàng.
"""


********************** GET USER INFO BY FIELD AND FILTER ************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/systems/mkt_strategy/users Lấy danh sách khách hàng.
@apiDescription API lấy danh sách khách hàng theo field và bộ lọc
@apiGroup Customers
@apiVersion 1.0.0
@apiName MKTGetUsers

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse paging_tokens

@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} [business_case_id]                          ID business case

@apiParam      (Body:)     {String}    search                           Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]                 Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Body:)     {Array}     fields                           Danh sách các field cần lấy. Gồm:<br/>
Allowed values: <br/>
<li><code>phone_number</code>
<li><code>email</code>
<li><code>name</code>
<li><code>social_user</code>
<li><code>gender</code>
<li><code>address</code>
<li><code>source_type</code>
<li><code>source_id</code>
<li><code>birthday</code>



@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                  Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "fields": ["name", "phone_number", "email", "social_user", "gender", "address", "source_type", "source_id", "birthday"],
  "search": "search_str",
  "profile_filter": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách thông tin user
@apiSuccess       {String}                                 signature         Chữ ký theo dữ liệu trả về

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "birthday": "1981-07-28",
      "created_time": "2018-07-27T09:53:21Z",
      "name": "Vũ Thị thọ 123",
      "email": "['<EMAIL>', '<EMAIL>']",
      "gender": 3,
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "phone_number": ["+841215150001"],
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "updated_time": "2018-07-28T04:57:35Z",
      "source_type": 1,
      "source_id": "435ecfeb-e229-4076-806f-982580475e88"
    },
    ...
  ],
  "signature": "data signature",
}

"""


********************** GET USER INFO BY FIELD AND FILTER ************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/systems/mkt_strategy/users Lấy danh sách khách hàng.
@apiDescription API lấy danh sách khách hàng theo field và bộ lọc
@apiGroup Customers
@apiVersion 1.0.0
@apiName MKTGetUsers

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse paging_tokens

@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} [business_case_id]                          ID business case

@apiParam      (Body:)     {String}    search                           Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]                 Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Body:)     {Array}     fields                           Danh sách các field cần lấy. Gồm:<br/>
Allowed values: <br/>
<li><code>phone_number</code>
<li><code>email</code>
<li><code>name</code>
<li><code>social_user</code>
<li><code>gender</code>
<li><code>address</code>
<li><code>source_type</code>
<li><code>source_id</code>
<li><code>birthday</code>


<span id="mkt-get-users"></span> 
@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                  Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "fields": ["name", "phone_number", "email", "social_user", "gender", "address", "source_type", "source_id", "birthday"],
  "search": "search_str",
  "profile_filter": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách thông tin user
@apiSuccess       {String}                                 signature         Chữ ký theo dữ liệu trả về

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "birthday": "1981-07-28",
      "created_time": "2018-07-27T09:53:21Z",
      "name": "Vũ Thị thọ 123",
      "email": "['<EMAIL>', '<EMAIL>']",
      "gender": 3,
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "phone_number": ["+841215150001"],
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "updated_time": "2018-07-28T04:57:35Z",
      "source_type": 1,
      "source_id": "435ecfeb-e229-4076-806f-982580475e88"
    },
    ...
  ],
  "signature": "data signature",
}

"""


********************** GET USER INFO BY ID ************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/systems/mkt_strategy/users/<user_id> Lấy khách hàng theo id.
@apiDescription API lấy user theo id
@apiGroup Customers
@apiVersion 1.0.0
@apiName MKTGetUserInfo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} business_case_id                            ID business case

@apiParam      (Body:)     {Array}     fields                           Danh sách các field cần lấy. Gồm:<br/>
Allowed values: <br/>
<li><code>phone_number</code>
<li><code>email</code>
<li><code>name</code>
<li><code>social_user</code>
<li><code>gender</code>
<li><code>address</code>
<li><code>source_type</code>
<li><code>source_id</code>
<li><code>birthday</code>

@apiParamExample    {json}    Body example:
{
  "fields": ["name", "phone_number", "email", "social_user", "gender", "address", "source_type", "source_id", "birthday"],
}

@apiSuccess       {Object}                                  data              Dữ liệu thông tin user
@apiSuccess       {String}                                 signature         Chữ ký theo dữ liệu trả về

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": {
    "address": "Tỉnh Thanh Hóa",
    "birthday": "1981-07-28",
    "created_time": "2018-07-27T09:53:21Z",
    "name": "Vũ Thị thọ 123",
    "email": "['<EMAIL>', '<EMAIL>']",
    "gender": 3,
    "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
    "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
    "phone_number": [
      "+841215150001"
    ],
    "social_user": [
      {
        "id_social": "2139374173003427",
        "social_type": 1
      }
    ],
    "updated_time": "2018-07-28T04:57:35Z",
    "source_type": 1,
    "source_id": "435ecfeb-e229-4076-806f-982580475e88"
  },
  "signature": "data signature"
}
"""

*************************** Estimate Customer *********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/merchants/<merchant_id>/customers/actions/estimate Estimate số lượng khách hàng
@apiDescription Dịch vụ estimate số lượng khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.0
@apiName CustomerEstimate

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Number}                                  estimate              Số lượng customer estimate.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
  "estimate": 5000
}
"""

********************* GET USER FOR MKT - MULTI PROFILE FILTER *******************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/internal/v3.0/systems/mkt_strategy/users_audiences Lấy danh sách khách hàng theo nhiều bộ lọc audiences
@apiDescription API lấy danh sách khách hàng theo nhiều bộ lọc audiences. Các biểu thức hỗ trợ: <code>Intersection (Int), Subtraction (Sub)</code>.
@apiGroup Customers
@apiVersion 1.0.0
@apiName MKTGetUsersAudiences

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse paging_tokens

@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} [business_case_id]                          ID business case

@apiParam      (Body:)     {String}    search                           Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     fields                           Danh sách các field cần lấy. Gồm:<br/>
@apiParam      (Body:)     {String}     op                              Phép toán để tính toán trên tập hợp. Allowed values:<br/>
<li><code>int</code>: Phép giao, <b>không quan trọng thứ tự</b> của các tập hợp trong <code>profile_filters</code>.
<li><code>sub</code>: Phép trừ, api sẽ lấy phần từ đầu tiên trong <code>profile_filters</code> và trừ đi phần tử còn lại.
@apiParam      (Body:)     {Array}     profile_filters                  Danh sách các tập hợp điều kiện lọc. Các phần tử là mảng <code>profile_filter</code> như api <a href="#api-Customers-MKTGetUsers">Lấy danh sách khách hàng</a>



@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                  Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "fields": [
    "name",
    "phone_number",
    "email",
    "social_user",
    "gender",
    "address",
    "source_type",
    "source_id",
    "birthday"
  ],
  "search": "search_str",
  "profile_filters": [
    [
      {
        "criteria_key": "cri_age",
        "operator_key": "op_is_between",
        "values": [
          18,
          50
        ]
      }
    ],
    [
      {
        "criteria_key": "cri_age",
        "operator_key": "op_is_between",
        "values": [
          25,
          30
        ]
      }
    ]
  ],
  "op": "int"
}

@apiSuccess       {Array}                                  data              Mảng danh sách thông tin user

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "birthday": "1981-07-28",
      "created_time": "2018-07-27T09:53:21Z",
      "name": "Vũ Thị thọ 123",
      "email": "['<EMAIL>', '<EMAIL>']",
      "gender": 3,
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "phone_number": ["+841215150001"],
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "updated_time": "2018-07-28T04:57:35Z",
      "source_type": 1,
      "source_id": "435ecfeb-e229-4076-806f-982580475e88"
    },
    ...
  ]
}

"""
********************* ESTIMATE USER FOR MKT - MULTI AUDIENCE ********************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/systems/mkt_strategy/users_audiences/estimate Lấy số lượng khách hàng theo nhiều bộ lọc audiences
@apiDescription API lấy số lượng khách hàng theo nhiều bộ lọc audiences.
@apiGroup Customers
@apiVersion 1.0.0
@apiName MKTEstimateUsersAudiences

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} [business_case_id]                          ID business case

@apiParam      (Body:)     {String}    [search]                           Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     audience_ids                     Mảng các audience id cần lấy số lượng khách hàng

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "audience_ids": []
}

@apiSuccess       {Number}                                  estimate              Số lượng customer estimate.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
  "estimate": 5000
}

"""
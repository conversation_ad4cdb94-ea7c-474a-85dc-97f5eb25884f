"""
@api {get} /loyalty/api/v2.0/statisticscode/vouchers Thống kê danh sách Voucher
@apiName StatisticsCodeVouchers
@apiGroup VoucherStatistic
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam {String} [search_text]  Chuỗi tìm kiếm theo tên voucher. Nếu tên của voucher có chứa param được truyền vào search_text thì trả về voucher đó.
@apiParam {String} [brands]  Tìm kiếm theo brand. Nếu giá trị được truyền vào:
<li>Ví dụ: <code>brands=brand_id1, brand_id2...</code></li> <br>
Mặc định nếu không truyền vào thì lấy tất cả
@apiParam {Int} [status]  Lọc theo điều kiện. Nếu giá trị status được truyền vào:
<li><code>status=1</code> hoặc <code>status=-1</code>: Hiển thị voucher theo trạng thái đang diễn ra hay đã hết hạn</li> <br>
Mặc định nếu không truyền status vào thì hiển thị tất cả các loại status (đang diễn ra và đã hết hạn)
@apiParam {String} [categories]  Lọc theo điều kiện. Nếu giá trị categories được truyền vào:
<li><code>categories=uuid1,uuid2,...</code> Các giá trị uuid tương ứng với các category: Mẹ và bé,...</li>
@apiParam {String} [page]  Page number. Request <code>page=-1</code> if need get all items.
<code>MIN_VALUE=1</code>
Example: <code>&page=2</code>
Default value: <code>1</code>
@apiParam {String} [per_page]  Number of item on a page.
Example: <code>&per_page=5</code>
@apiParam {String} [order_by]  Sắp xếp theo tiêu chí cụ thể
Example:
 <li><code>order_by=1</code> Sắp xếp theo thứ tự tên theo Alphabet (name)</li>
 <li><code>order_by=2</code> Sắp xếp theo số voucher phát hành (total)</li>
 <li><code>order_by=3</code> Sắp xếp theo số voucher sử dụng (used)</li>
 <li><code>order_by=4</code> Sắp xếp theo tỉ lệ voucher sử dụng (ratio)</li> <br>
 Default value: <code>1</code>
@apiParam {String} [order_type] order results <br>
Default value: <code>asc</code> <br>
Allowed values: <code>asc</code>, <code>desc</code>
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Object} [paging] Thông tin phân trang
<li><code>page:</code> Vị trí page request<li>
<li><code>per_page:</code> Số lượng phần tử trên một page<li>
<li><code>total_page:</code> Tổng số page.<li>
<li><code>total_count:</code> Tổng số phần tử<li>
@apiSuccess {List} data Data trả về là danh sách các voucher cần lấy
<li><code>voucher_id</code>: uuid của voucher</li>
<li><code>name</code>: tên của voucher</li>
<li><code>start_time</code>: thời gian phát hành của voucher</li>
<li><code>end_time</code>: thời gian kết thúc của voucher</li>
<li><code>image</code>: đường dẫn ảnh của voucher</li>
<li><code>total_add</code>: số mã phát hành của voucher</li>
<li><code>total_get</code>: số mã đã sử dụng của voucher</li>
<li><code>ratio</code>: tỉ lệ sử dụng mã của voucher</li>
<li><code>status</code>: trạng thái voucher, <code>1</code>: active, <code>-1</code>: deactive</li>
<li><code>code_profile</code>: số mã voucher mà profile đã nhận</li>
<li><code>used_code_profile</code>: số mã voucher mà profile đã sử dụng</li>

@apiSuccessExample Response success:
{
  "code": 200,
  "data": {
      "count": 15
  }
}
"""

"""
@api {get} /loyalty/api/v2.0/statistic/voucher/active Lấy số lượng voucher active
@apiDescription Lấy số lượng voucher active.
@apiGroup Statistic
@apiVersion 1.0.1
@apiName GetActiveVoucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse since_until_query

@apiSuccess (Data:) {number}  count Số lượng voucher active đến thời điểm sau (until)
@apiSuccess (Data:) {number}  ratio tỉ lệ tăng/giảm của until so với since (%)
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
      "count": 15,
      "ratio": 13
  }
}
"""

"""
@api {get} /loyalty/api/v2.0/statistic/voucher/active Lấy số lượng voucher active
@apiDescription Lấy số lượng voucher active.
@apiGroup Statistic
@apiVersion 1.0.2
@apiName GetActiveVoucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess (Data:) {number}  count Số lượng voucher active đến thời điểm hiện tại
@apiSuccess (Data:) {number}  ratio tỉ lệ tăng/giảm của until so với since (%)
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
      "count": 15,
      "ratio": 13
  }
}
"""

** ** ** ** ** ** ** ** ** ** ** Lấy
số
lượng
thẻ
thành
viên ** ** ** ** ** ** ** ** ** ** *
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {patch} /loyalty/api/v2.0/statistic/card Lấy số lượng thẻ thành viên
@apiDescription Lấy số lượng thẻ thành viên.
@apiGroup Statistic
@apiVersion 1.0.0
@apiName GetCardStatistic


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse since_until_query

@apiSuccess (Data:) {number}  count Số lượng thẻ thành viên đến thời điểm sau (until)
@apiSuccess (Data:) {number}  ratio tỉ lệ tăng/giảm của until so với since (%)
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
      "count": 15,
      "ratio": 13
  }
}
"""
"""
@api {get} /loyalty/api/v2.0/statistic/card Lấy số lượng thẻ thành viên
@apiDescription Lấy số lượng thẻ thành viên.
@apiGroup Statistic
@apiVersion 1.0.1
@apiName GetCardStatistic
@apiParam (Query:) {string} [start_time]      Mốc thời điểm đầu xem báo cáo. VD:  "2017-07-01T12:00:00Z"
@apiParam (Query:) {string} [end_time]        Mốc thời điểm cuối xem báo cáo. VD: "2017-07-30T12:00:00Z"

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess (Data:) {number}  count Số lượng thẻ thành viên đến thời điểm hiện tại
@apiSuccess (Data:) {number}  ratio tỉ lệ tăng/giảm của until so với since (%)
@apiSuccess (Data:) {Object}  date_data số  lượng thẻ thành viên phát sinh trong mỗi ngày
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
      "count": 15,
      "ratio": 13,
      "date_data": [
        {
            "voucher_id": "0349f688-3d0b-498f-a869-c1952ee8be99",
            "name": "Tặng voucher giảm giá 2 triệu cho khách hàng copy 1",
            "start_time": "2019-07-08 01:55:10",
            "end_time": "2019-07-08 04:05:35",
            "image": "https://mbodevstorage.blob.core.windows.net/images/18cf1927-a023-4573-b1ce-4dfba5ccc6ac",
            "total_add": 1022,
            "total_get": 22,
            "ratio": 2
            "status": 1,
            "code_profile": 100,
            "used_code_profile": 10
        {,
        {
            "voucher_id": "1349f688-3d0b-498f-a869-c1952ee8be99",
            "name": "Tặng voucher giảm giá 5 triệu cho khách hàng VIP",
            "start_time": "2019-07-08 01:55:10",
            "end_time": "2019-07-08 04:05:35",
            "image": "https://mbodevstorage.blob.core.windows.net/images/18cf1927-a023-4573-b1ce-4dfba5ccc6ac",
            "total_add": 1000,
            "total_get": 100,
            "ratio": 10,
            "status": -1,
            "code_profile": 200,
            "used_code_profile": 20
        },
        ...
    ]
}
"""

"""
@api {get} /loyalty/api/v2.0/statisticscode/vouchers/export/excel Xuất thống kê danh sách Voucher ra Excel
@apiName StatisticCodeVoucherExportExcel
@apiGroup VoucherStatistic
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam {String} [search_text]  Chuỗi tìm kiếm theo tên voucher. Nếu tên của voucher có chứa param được truyền vào search_text thì trả về voucher đó.
@apiParam {String} [brands]  Tìm kiếm theo brand. Nếu giá trị được truyền vào:
<li>Ví dụ: <code>brands=brand_id1, brand_id2...</code></li> <br>
Mặc định nếu không truyền vào thì lấy tất cả
@apiParam {Int} [status]  Lọc theo điều kiện. Nếu giá trị status được truyền vào:
<li><code>status=1</code> hoặc <code>status=-1</code>: Hiển thị voucher theo trạng thái đang diễn ra hay đã hết hạn</li> <br>
Mặc định nếu không truyền status vào thì hiển thị tất cả các loại status (đang diễn ra và đã hết hạn)
@apiParam {String} [categories]  Lọc theo điều kiện. Nếu giá trị categories được truyền vào:
<li><code>categories=uuid1,uuid2,...</code> Các giá trị uuid tương ứng với các category: Mẹ và bé,...</li>
@apiParam {String} [page]  Page number. Request <code>page=-1</code> if need get all items.
<code>MIN_VALUE=1</code>
Example: <code>&page=2</code>
Default value: <code>1</code>
@apiParam {String} [per_page]  Number of item on a page.
Example: <code>&per_page=5</code>
@apiParam {String} [order_by]  Sắp xếp theo tiêu chí cụ thể
Example:
 <li><code>order_by=1</code> Sắp xếp theo thứ tự tên theo Alphabet (name)</li>
 <li><code>order_by=2</code> Sắp xếp theo số voucher phát hành (total)</li>
 <li><code>order_by=3</code> Sắp xếp theo số voucher sử dụng (used)</li>
 <li><code>order_by=4</code> Sắp xếp theo tỉ lệ voucher sử dụng (ratio)</li> <br>
 Default value: <code>1</code>
@apiParam {String} [order_type] order results <br>
Default value: <code>asc</code> <br>
Allowed values: <code>asc</code>, <code>desc</code>
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {String} excel Link download excel file

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "excel": "link_excel"
}
"""

"""
@api {get} /loyalty/api/v2.0/statisticscode/vouchers/<voucher_id> Thống kê số lượng mã voucher theo thời gian
@apiName StatisticCodeVoucherVoucherId
@apiGroup VoucherStatistic
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500


@apiParam {datetime} start_time Thời gian bắt đầu
@apiParam {datetime} end_time Thời gian kết thúc
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Object} data Danh sách thông tin mã voucher theo ngày
<li><code>voucher_id</code>: uuid của voucher</li>
<li><code>name</code>: tên của voucher</li>
<li><code>total_add</code>: số mã voucher phát hành</li>
<li><code>total_get</code>: số mã voucher đã sử dụng</li>
<li><code>date</code>: ngày hiện tại. Định đạng '%Y-%m-%d</li>

@apiSuccessExample Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request successfully",
    "data": [
    {
        "voucher_id": "0349f688-3d0b-498f-a869-c1952ee8be99",
        "name": "Tặng voucher giảm giá 2 triệu cho khách hàng copy 1",
        "total_add": 50,
        "total_get": 22,
        "date": "2019-09-02"
    },
    {
        "voucher_id": "0349f688-3d0b-498f-a869-c1952ee8be99",
        "name": "Tặng voucher giảm giá 2 triệu cho khách hàng copy 1",
        "total_add": 100,
        "total_get": 2,
        "date": "2019-09-03"
    },
    {
        "voucher_id": "0349f688-3d0b-498f-a869-c1952ee8be99",
        "name": "Tặng voucher giảm giá 2 triệu cho khách hàng copy 1",
        "total_add": 30,
        "total_get": 10,
        "date": "2019-09-04"
    }
    ]
}
"""

"""
@api {get} /loyalty/api/v2.0/statisticscode/vouchers/profiles/<voucher_id> Thống kê số lượng profile đã nhận và sử dụng mã voucher theo thời gian
@apiName StatisticsCodeVouchersProfileVoucherID
@apiGroup VoucherStatistic
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam {datetime} start_time Thời gian bắt đầu
@apiParam {datetime} end_time Thời gian kết thúc
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Object} data Danh sách thông tin mã voucher theo ngày
<li><code>voucher_id</code>: uuid của voucher</li>
<li><code>name</code>: tên của voucher</li>
<li><code>total_profiles_get</code>: số lượng profile đã nhận mã voucher</li>
<li><code>total_profiles_use</code>: số mã voucher profile nhận đã sử dụng</li>
<li><code>date</code>: ngày hiện tại. Định đạng '%Y-%m-%d</li>

@apiSuccessExample Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request successfully",
    "data": [
    {
        "voucher_id": "0349f688-3d0b-498f-a869-c1952ee8be99",
        "name": "Tặng voucher giảm giá 2 triệu cho khách hàng copy 1",
        "total_profiles_get": 50,
        "total_profiles_use": 22,
        "date": "2019-09-02"
    },
    {
        "voucher_id": "0349f688-3d0b-498f-a869-c1952ee8be99",
        "name": "Tặng voucher giảm giá 2 triệu cho khách hàng copy 1",
        "total_profiles_get": 100,
        "total_profiles_use": 2,
        "date": "2019-09-03"
    },
    {
        "voucher_id": "0349f688-3d0b-498f-a869-c1952ee8be99",
        "name": "Tặng voucher giảm giá 2 triệu cho khách hàng copy 1",
        "total_profiles_get": 30,
        "total_profiles_use": 10,
        "date": "2019-09-04"
    }
    ]
}
"""

"""
@api {get} /loyalty/api/v2.0/statisticscode/detail/vouchers/<voucher_id> Lấy chi tiết báo cáo voucher
@apiName StatisticCodeDetailVouchersVoucherId
@apiGroup VoucherStatistic
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam {String} [excel]  Xuất nội dung ra excel. <code>&excel=1</code>: xuất excel, <code>&excel=0</code>: không xuất excel. <code>Default</code>: 0
@apiParam {datetime} [start_time] Thời gian bắt đầu
@apiParam {datetime} [end_time] Thời gian kết thúc
@apiParam {String} [search_text]  Chuỗi tìm kiếm theo voucher name, tên người dùng, số điện thoại hoặc email. Nếu voucher name, tên người dùng, số điện thoại hoặc email có chứa param được truyền vào search_text thì trả về voucher đó.
@apiParam {String} [page]  Page number. Request <code>page=-1</code> if need get all items.
<code>MIN_VALUE=1</code>
Example: <code>&page=2</code>
Default value: <code>1</code>
@apiParam {String} [per_page]  Number of item on a page.
Example: <code>&per_page=5</code>
@apiParam {String} [order_by]  Sắp xếp theo tiêu chí cụ thể
Example:
 <li><code>order_by=1</code> Sắp xếp theo thứ tự tên theo tên voucher (voucher name)</li>
 <li><code>order_by=2</code> Sắp xếp theo thời gian voucher phát hành (release_time</li>
 <li><code>order_by=3</code> Sắp xếp theo thời gian voucher được sử dụng (used_time)</li>
 Default value: <code>1</code>
@apiParam {String} [order_type] order results <br>
Default value: <code>asc</code> <br>
Allowed values: <code>asc</code>, <code>desc</code>
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Object} [paging] Thông tin phân trang
<li><code>page:</code> Vị trí page request<li>
<li><code>per_page:</code> Số lượng phần tử trên một page<li>
<li><code>total_page:</code> Tổng số page.<li>
<li><code>total_count:</code> Tổng số phần tử<li>
@apiSuccess {List} data Data trả về là danh sách các voucher cần lấy. Trong đó:
<li><code>voucher_id</code>: uuid của voucher</li>
<li><code>name</code>: tên của voucher</li>
<li><code>voucher_id</code>: uuid của voucher</li>
<li><code>voucher_code</code>: mã code của voucher</li>
<li><code>release_time</code>: thời gian phát hành mã code</li>
<li><code>release_type</code>: uuid của loại phát hành</li>
<li><code>username</code>: tên người dùng</li>
<li><code>phone</code>: số điện thoại</li>
<li><code>email</code>: email người dùng</li>
<li><code>status</code>: tình trạng mã voucher</li>
<li><code>point</code>: điểm</li>
<li><code>used_time</code>: thời gian mã voucher được sử dụng</li>

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        [
            "voucher_id": "0349f688-3d0b-498f-a869-c1952ee8be99",
            "name": "Tặng voucher giảm giá 2 triệu cho khách hàng copy 1",
            "voucher_code": "HABJSHH7A",
            "release_time": "2019-07-08 01:55:10",
            "release_type": 1,
            "username": "Nguyen Van Hoi",
            "phone": "0994994995",
            "email": "<EMAIL>",
            "status": 1,
            "point": 30,
            "used_time": "2019-07-09 01:55:10"
        ],
        [
            "voucher_id": "1349f688-3d0b-498f-a869-c1952ee8be99",
            "name": "Tặng voucher giảm giá 100 triệu cho VIP",
            "voucher_code": "SBJSHH7A",
            "release_time": "2019-07-08 01:55:10",
            "release_type": 1,
            "username": "Nguyen Phu Trong",
            "phone": "0994994995",
            "email": "<EMAIL>",
            "status": 1,
            "point": 100,
            "used_time": "2019-07-09 01:55:10"
        ],
        ...
    }
}
"""

"""
@api {get} /loyalty/api/v2.0/statisticscode/detail/vouchers/export/<voucher_id> Xuất chi tiết báo cáo voucher ra Excel
@apiName ExportDetailVoucher
@apiGroup VoucherStatistic
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam {datetime} [start_time] Thời gian bắt đầu
@apiParam {datetime} [end_time] Thời gian kết thúc
@apiParam {String} [search_text]  Chuỗi tìm kiếm theo voucher name, tên người dùng, số điện thoại hoặc email. Nếu voucher name, tên người dùng, số điện thoại hoặc email có chứa param được truyền vào search_text thì trả về voucher đó.
@apiParam {String} [page]  Page number. Request <code>page=-1</code> if need get all items.
<code>MIN_VALUE=1</code>
Example: <code>&page=2</code>
Default value: <code>1</code>
@apiParam {String} [per_page]  Number of item on a page.
Example: <code>&per_page=5</code>
@apiParam {String} [order_by]  Sắp xếp theo tiêu chí cụ thể
Example:
 <li><code>order_by=1</code> Sắp xếp theo thứ tự tên theo tên voucher (voucher name)</li>
 <li><code>order_by=2</code> Sắp xếp theo thời gian voucher phát hành (release_time</li>
 <li><code>order_by=3</code> Sắp xếp theo thời gian voucher được sử dụng (used_time)</li>
 Default value: <code>1</code>
@apiParam {String} [order_type] order results <br>
Default value: <code>asc</code> <br>
Allowed values: <code>asc</code>, <code>desc</code>
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>

@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {String} link Link download excel file
@apiSuccess {number} type  Kiểu xuất file <code>1: download . 2: sent email</code>

@apiSuccessExample Response success:
{
  "code": 200,
  "link": "https://api-p.test1.mobio.vn/loyalty/static/export_excel/Bao_Cao_Chi_Tiet_Voucher_20200416_0832AM.xls",
  "message": "request thành công.",
  "type": 1
}

@apiSuccessExample Response Sent Email:
{
    "code": 200,
    "message": "request successfully",
    "description: "File Export will be sent to email"
    "type": "2",
    "limit": 1000
}

"""

"""
@api {get} /loyalty/api/v2.0/statisticscode/evaluation/vouchers Lấy danh sách đánh giá sau giao dịch
@apiName StatisticsCodeEvaluationVouchers
@apiGroup VoucherStatistic
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam {datetime} [start_time] Thời gian bắt đầu
@apiParam {datetime} [end_time] Thời gian kết thúc
@apiParam {String} [search_text]  Chuỗi tìm kiếm theo tên người dùng, số điện thoại hoặc email. Nếu tên người dùng, số điện thoại hoặc email có chứa param được truyền vào search_text thì trả về kết quả đó.
@apiParam {String} [brand] tìm kiếm theo brand. Giá trị truyền vào là uuid của brand. Mặc định lấy tất cả brand.
<li>Ví dụ: <code>&brand=0349f688-3d0b-498f-a869-c1952ee8be99</code></li>
@apiParam {String} [points] Lọc theo điều kiện điểm đánh giá. Nếu giá trị point được truyền vào:
<li><code>&point=1,2,3,4,5</code>Lọc theo điểm đánh giá</li> <br>
Mặc định nếu không truyền point vào thì hiển thị tất cả các loại point.
@apiParam {String} [stores]  Lọc theo điều kiện. Nếu giá trị store được truyền vào:
<li><code>&stores=uuid1,uuid2...</code> trong đó uuid tương ứng với các địa điểm store</li>
@apiParam {String} [page]  Page number. Request <code>page=-1</code> if need get all items.
<code>MIN_VALUE=1</code>
Example: <code>&page=2</code>
Default value: <code>1</code>
@apiParam {String} [per_page]  Number of item on a page.
Example: <code>&per_page=5</code>
@apiParam {String} [order_by]  Sắp xếp theo tiêu chí cụ thể
Example:
 <li><code>order_by=time</code> Sắp xếp theo thời gian đánh giá</li>
 <li><code>order_by=point</code> Sắp xếp theo điểm đánh giá</li> <br>
 Default value: <code>time</code>
@apiParam {String} [order_type] order results <br>
Default value: <code>asc</code> <br>
Allowed values: <code>asc</code>, <code>desc</code>
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Object} [paging] Thông tin phân trang
<li><code>page:</code> Vị trí page request<li>
<li><code>per_page:</code> Số lượng phần tử trên một page<li>
<li><code>total_page:</code> Tổng số page.<li>
<li><code>total_count:</code> Tổng số phần tử<li>
@apiSuccess {List} data Data trả về là danh sách các nội dung đánh giá
<li><code>evaluate_time</code>: Thời gian đánh giá. Định dạng '%Y-%m-%d %H:%M:%S'</li>
<li><code>evaluate_point</code>: Điểm đánh giá</li>
<li><code>username</code>: tên người dùng đánh giá</li>
<li><code>phone</code>: số điện thoại</li>
<li><code>email</code>: email</li>
<li><code>store</code>: địa điểm đánh giá</li>
<li><code>evaluate_content</code>: nội dung đánh giá</li>

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        [
            "evaluate_time": "2019-07-08 01:55:10",
            "evaluate_point": 1,
            "username": "Nguyen Van Hoi",
            "phone": "0994994995",
            "email": "<EMAIL>",
            "store": "82 Duy Tan",
            "evaluate_content": "Chat Luong binh thuong"
        ],
        [
            "evaluate_time": "2019-07-08 01:55:10",
            "evaluate_point": 1,
            "username": "Nguyen Van Toan",
            "phone": "0994994995",
            "email": "<EMAIL>",
            "store": "82 Duy Tan",
            "evaluate_content": "Chat Luong binh thuong"
        ],
        ...
    }
}
"""

"""
@api {get} /loyalty/api/v2.0/statisticscode/evaluation/vouchers/export/excel Xuất danh sách đánh giá sau giao dịch ra Excel
@apiName StatisticsCodeEvaluationVouchersExportExcel
@apiGroup VoucherStatistic
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam {datetime} [start_time] Thời gian bắt đầu
@apiParam {datetime} [end_time] Thời gian kết thúc
@apiParam {String} [search_text]  Chuỗi tìm kiếm theo tên người dùng, số điện thoại hoặc email. Nếu tên người dùng, số điện thoại hoặc email có chứa param được truyền vào search_text thì trả về kết quả đó.
@apiParam {String} [brand] tìm kiếm theo brand. Giá trị truyền vào là uuid của brand. Mặc định lấy tất cả brand.
<li>Ví dụ: <code>&brand=0349f688-3d0b-498f-a869-c1952ee8be99</code></li>
@apiParam {String} [points] Lọc theo điều kiện điểm đánh giá. Nếu giá trị point được truyền vào:
<li><code>&point=1,2,3,4,5</code>Lọc theo điểm đánh giá</li> <br>
Mặc định nếu không truyền point vào thì hiển thị tất cả các loại point.
@apiParam {String} [stores]  Lọc theo điều kiện. Nếu giá trị store được truyền vào:
<li><code>&stores=uuid1,uuid2...</code> trong đó uuid tương ứng với các địa điểm store</li>
@apiParam {String} [page]  Page number. Request <code>page=-1</code> if need get all items.
<code>MIN_VALUE=1</code>
Example: <code>&page=2</code>
Default value: <code>1</code>
@apiParam {String} [per_page]  Number of item on a page.
Example: <code>&per_page=5</code>
@apiParam {String} [order_by]  Sắp xếp theo tiêu chí cụ thể
Example:
 <li><code>order_by=time</code> Sắp xếp theo thời gian đánh giá</li>
 <li><code>order_by=point</code> Sắp xếp theo điểm đánh giá</li> <br>
 Default value: <code>time</code>
@apiParam {String} [order_type] order results <br>
Default value: <code>asc</code> <br>
Allowed values: <code>asc</code>, <code>desc</code>
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiParam {String} [lang] Mã ngôn ngữ. Nếu client không request lên mặc định là tiếng Việt.<br>
Default value: <code>vi</code><br>
Allowed values: <cdoe>vi, en</code>
@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {String} excel Link download excel file

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "excel": "link_excel"
}
"""

"""
@api {get} /loyalty/api/v2.0/statisticscode/profile/vouchers/number/<voucher_id> Số lượng Mã voucher của profile theo thời gian
@apiName GetCodeVoucherProfileByTime
@apiGroup VoucherStatistic
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam {datetime} [start_time] Thời gian bắt đầu
@apiParam {datetime} [end_time] Thời gian kết thúc

@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {String} data Số lượng mã voucher theo trạng thái
@apiSuccess (data) {String} status trạng thái mã voucher <code> 1: Đã sử dụng, 0: Chưa sử dụng </code>
@apiSuccess (data) {String} number  số lượng mã voucher theo trạng thái



@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data":[
        {
            "status": 0,
            "number": 4
        },
        {
            "status": 1,
            "number": 2
        }
    ]
}
"""

# -------------------------------------
# Cập nhật trạng thái sử dụng voucher
# -------------------------------------

"""
@api {post} /loyalty/api/v2.0/vouchers_code/action/update_status  Cập nhật trạng thái mã voucher
@apiName UpdateStatusVoucherCode
@apiGroup VoucherStatistic
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)  {Array}    vouchers           Danh sách mã voucher cần kiểm tra trạng thái 
@apiParam   (vouchers:)  {String}   voucher_code       Mã code Voucher 
@apiParam   (vouchers:)  {String}   profiling_profile_id       ID Profile 
@apiParam   (vouchers:)  {String}   voucher_id       ID voucher

@apiParamExample    {FormData}      Body example:
{   "vouchers": [
        {
            "voucher_id": "0154d0aa-871e-4f39-bd65-455347445c8a",
            "voucher_code": "BGK20Y2",
            "profiling_profile_id": "af98c69e-63b5-4455-9d8d-e08019466f3e"
        },
        {
            "voucher_id": "0154d0aa-871e-4f39-bd65-455347445c8a",
            "voucher_code": "F188U",
            "profiling_profile_id": "vf98c69e-63b5-4455-9d8d-e08019466f3c"
        }
    ]
}

@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Array} data  Thông tin trạng thái mã voucher
@apiSuccess (data) {String} voucher_code    Mã voucher
@apiSuccess (data) {String} used_time       Thời gian sử dụng mã voucher
@apiSuccess (data) {String} status Trạng thái của mã voucher 
                            <ul>
                                <li> <code>0 </code>: Chưa sử dụng</li>
                                <li> <code>1 </code>: Đã sử dụng</li>
                            </ul>

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data":[
        {
            "voucher_code": "BGK20Y2",
            "status": 0,
            "used_time": ""
        },
        {
            "voucher_code": "F188U",
            "status": 1,
            "used_time": "Thu, 04 Jun 2020 10:20:40 GMT"
        }
    ]
}
"""

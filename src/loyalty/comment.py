# ********************************************************************
# ********************** Tạo Comment  **********************
# ********************************************************************
"""
@api {post} /loyalty/api/v2.0/comments/add Tạo Comment
@apiVersion 1.0.2
@apiDescription Tạo comment
@apiName AddCommnet
@apiGroup Comment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Form:)  {Files}  [images] Danh sách file của ảnh mô tả cho comment
@apiParam   (Form:)  {string}  object_id là Id cụ thể của Nhà cung cấp hoặc Sản phẩm hoặc Khuyến mãi hoặc cửa hàng.
@apiParam   (Form:)  {string}  object_type Mỗi đối tượng có nhiều loại cụ thể khác nhau,
 nên bản có thêm trường này để xác đinh thuộc 4 loại sau :
  <code>CUSTOMER</code>, <code>STAFF</code>,<code>MERCHANT</code>,<code>PRODUCT</code>
@apiParam   (Form:)  {string}  [commenter_id] ID của tài khoản commment
@apiParam   (Form:)  {int}  [commenter_type] xác định ngừoi trả lời là staff hay khách hàng.
 <code>1</code> : khách hàng, <code>2</code> : staff
@apiParam   (Form:)  {string}  [content] Nội dung comment
@apiParam   (Form:)  {int}  [repl_status] trạng thái được trả lời;
<code>1</code>: chưa được trả lời; <code>2</code>: đã được trả lời
@apiParam   (Form:)  {int}  [status] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiParam   (Form:)  {int}  [merchant_id] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiParam   (Form:)  {decimal} avg_rated Điểm đánh giá trung bình (có giá trị thấp nhất là 0.0 và cao nhất là 5.0)
@apiParam   (Form:)  {string}  [repl_comment_id] ID của comment được trả lời trong trường hợp comment đẩy lên là comment trả lời
@apiParam   (Form:)  {string}  [page_social_id] Mã định danh app
@apiParam   (Form:)  {number}  [social_type]    Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:)  {string} comment_id ID của comment
@apiSuccess (data:)  {array}  images Ảnh đính kèm

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "comment_id": "uuid",
    "images": ["https://mbodevstorage.blob.core.windows.net/images/a4d4da19-**************-71dee599667c"]
  }
}
"""
==========================
"""
@api {post} /loyalty/api/v2.0/comments/add Tạo Comment
@apiVersion 1.0.1
@apiDescription Tạo comment
@apiName AddCommnet
@apiGroup Comment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Form:)  {Files}  [images] Danh sách file của ảnh mô tả cho comment
@apiParam   (Form:)  {string}  object_id là Id cụ thể của Nhà cung cấp hoặc Sản phẩm hoặc Khuyến mãi hoặc cửa hàng.
@apiParam   (Form:)  {string}  object_type Mỗi đối tượng có nhiều loại cụ thể khác nhau,
 nên bản có thêm trường này để xác đinh thuộc 4 loại sau :
  <code>CUSTOMER</code>, <code>STAFF</code>,<code>MERCHANT</code>,<code>PRODUCT</code>
@apiParam   (Form:)  {string}  [commenter_id] ID của tài khoản commment
@apiParam   (Form:)  {int}  [commenter_type] xác định ngừoi trả lời là staff hay khách hàng.
 <code>1</code> : khách hàng, <code>2</code> : staff
@apiParam   (Form:)  {string}  [content] Nội dung comment
@apiParam   (Form:)  {int}  [repl_status] trạng thái được trả lời;
<code>1</code>: chưa được trả lời; <code>2</code>: đã được trả lời
@apiParam   (Form:)  {int}  [status] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiParam   (Form:)  {int}  [merchant_id] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiParam   (Form:)  {decimal} avg_rated Điểm đánh giá trung bình (có giá trị thấp nhất là 0.0 và cao nhất là 5.0)
@apiParam   (Form:)  {string}  [repl_comment_id] ID của comment được trả lời trong trường hợp comment đẩy lên là comment trả lời
@apiParam   (Form:)  {string}  [page_social_id] Mã định danh app
@apiParam   (Form:)  {number}  [social_type]    Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:)  {string} comment_id ID của comment

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "comment_id": "uuid"
  }
}
"""
==========================
"""
@api {post} /loyalty/api/v2.0/comments/add Tạo Comment
@apiVersion 1.0.0
@apiDescription Tạo comment
@apiName AddCommnet
@apiGroup Comment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Form:)  {Files}  [images] Danh sách file của ảnh mô tả cho comment
@apiParam   (Form:)  {string}  [object_id] là Id cụ thể của Nhà cung cấp hoặc Sản phẩm hoặc Khuyến mãi hoặc cửa hàng.
@apiParam   (Form:)  {string}  [object_type] Mỗi đối tượng có nhiều loại cụ thể khác nhau,
 nên bản có thêm trường này để xác đinh thuộc 4 loại sau :
  <code>CUSTOMER</code>, <code>STAFF</code>,<code>MERCHANT</code>,<code>PRODUCT</code>
@apiParam   (Form:)  {string}  [commenter_id] ID của tài khoản commment
@apiParam   (Form:)  {int}  [commenter_type] xác định ngừoi trả lời là staff hay khách hàng.
 <code>1</code> : khách hàng, <code>2</code> : staff
@apiParam   (Form:)  {string}  [content] Nội dung comment
@apiParam   (Form:)  {int}  [repl_status] trạng thái được trả lời;
<code>1</code>: chưa được trả lời; <code>2</code>: đã được trả lời
@apiParam   (Form:)  {int}  [status] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiParam   (Form:)  {int}  [merchant_id] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiParam   (Form:)  {decimal} avg_rated Điểm đánh giá trung bình (có giá trị thấp nhất là 0.0 và cao nhất là 5.0)
@apiParam   (Form:)  {string}  [repl_comment_id] ID của comment được trả lời trong trường hợp comment đẩy lên là comment trả lời

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:)  {string} comment_id ID của comment

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "comment_id": "uuid"
  }
}
"""

# ********************************************************************
# ********************** Lấy danh sách comment  **********************
# ********************************************************************

"""
@api {get} /loyalty/api/v2.0/comments/list Lấy danh sách comment
@apiVersion 1.0.1
@apiName ListComment 
@apiGroup Comment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging


@apiParam   (Query:)  {string}  [object_id] id của đối tượng muốn lấy comment 
@apiParam   (Query:)  {string}  [object_type] <ul>kiểu đối tượng muốn lấy comment Trong đó:</ul>
<li><code>MERCHANT</code> - Kiểu nhà cung cấp </li>
<li><code>STORE</code> - Kiểu đối tượng là cửa hang  </li>
<li><code>PRODUCT</code> - Kiểu đối tượng là sản phẩm </li>
<li><code>VOUCHER</code> - Kiểu đối tượng là voucher </li>
@apiParam   (Query:)  {string}  [repl_comment_id] id của đối tượng muốn lấy comment trả lời 
@apiParam   (Query:)  {string}  [page_social_id] Mã định danh app
@apiParam   (Query:)  {number}  [social_type]    Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>



@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:)  {string} id ID của comment
@apiSuccess   (data:)  {array}  [images] Danh sách link của ảnh mô tả cho comment
@apiSuccess   (data:)  {string}  [object_id] là Id cụ thể của Nhà cung cấp hoặc Sản phẩm hoặc Khuyến mãi hoặc cửa hàng.
@apiSuccess   (data:)  {string}  [object_type] Mỗi đối tượng có nhiều loại cụ thể khác nhau,
 nên bản có thêm trường này để xác đinh thuộc 4 loại sau :
  <code>CUSTOMER</code>, <code>STAFF</code>,<code>MERCHANT</code>,<code>PRODUCT</code> 
@apiSuccess   (data:)  {string}  [commenter_id] ID của tài khoản commment
@apiSuccess   (data:)  {int}  [commenter_type] xác định ngừoi trả lời là staff hay khách hàng.
 <code>1</code> : khách hàng, <code>2</code> : staff
@apiSuccess   (data:)  {string}  [content] Nội dung comment
@apiSuccess   (data:)  {int}  [repl_status] trạng thái được trả lời;
<code>1</code>: chưa được trả lời; <code>2</code>: đã được trả lời
@apiSuccess   (data:)  {int}  [merchant_id] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiSuccess   (data:)  {decimal} avg_rated Điểm đánh giá trung bình (có giá trị thấp nhất là 0.0 và cao nhất là 5.0)
@apiSuccess   (data:)  {int} count_repl Số comment trả lời 
@apiSuccess   (data:)   {string}  page_social_id Mã định danh app
@apiSuccess   (data:)   {number}  social_type    Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[{
            "avg_rate": 3.5,
            "commenter_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "commenter_type": "STAFF",
            "content": null,
            "count_repl": 0,
            "created_time": "Fri, 22 Mar 2019 02:18:03 GMT",
            "id": "c8bd7fa8-34f2-4541-8878-505f0ffa0bc2",
            "images": [
                "https://mbodevstorage.file.core.windows.net/loyalty/images/0a148fd7-6c3d-4895-b75a-7f5b0108523c?se=2027-06-08T02%3A18%3A02Z&sp=r&sv=2018-03-28&sr=s&sig=u3rq210vOwVBSzktIGUIQpEcgzQBdQ9CDP4B%2B/RtHUM%3D",
                "https://mbodevstorage.file.core.windows.net/loyalty/images/4a02d8fe-13f7-488e-9f12-1c490674ecdf?se=2027-06-08T02%3A18%3A03Z&sp=r&sv=2018-03-28&sr=s&sig=adfu9g9h2h2lPTrLU%2BFVwtidpxz7BdPKHP4DSFdg8zE%3D"
            ],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "update_time": "Fri, 22 Mar 2019 02:18:03 GMT",
            "page_social_id": "SAKUKO",
            "social_type": 5
        }
  ...],
  "paging":{
    "page":1,
    "per_page":10,
    "total_page":1,
    "total_item":10
  }
}
"""
=========================
"""
@api {get} /loyalty/api/v2.0/comments/list Lấy danh sách comment
@apiVersion 1.0.0
@apiName ListComment 
@apiGroup Comment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging


@apiParam   (Query:)  {string}  [object_id] id của đối tượng muốn lấy comment 
@apiParam   (Query:)  {string}  [object_type] <ul>kiểu đối tượng muốn lấy comment Trong đó:</ul>
<li><code>MERCHANT</code> - Kiểu nhà cung cấp </li>
<li><code>STORE</code> - Kiểu đối tượng là cửa hang  </li>
<li><code>PRODUCT</code> - Kiểu đối tượng là sản phẩm </li>
<li><code>VOUCHER</code> - Kiểu đối tượng là voucher </li>
@apiParam   (Query:)  {string}  [repl_comment_id] id của đối tượng muốn lấy comment trả lời 


@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:)  {string} id ID của comment
@apiSuccess   (data:)  {array}  [images] Danh sách link của ảnh mô tả cho comment
@apiSuccess   (data:)  {string}  [object_id] là Id cụ thể của Nhà cung cấp hoặc Sản phẩm hoặc Khuyến mãi hoặc cửa hàng.
@apiSuccess   (data:)  {string}  [object_type] Mỗi đối tượng có nhiều loại cụ thể khác nhau,
 nên bản có thêm trường này để xác đinh thuộc 4 loại sau :
  <code>CUSTOMER</code>, <code>STAFF</code>,<code>MERCHANT</code>,<code>PRODUCT</code> 
@apiSuccess   (data:)  {string}  [commenter_id] ID của tài khoản commment
@apiSuccess   (data:)  {int}  [commenter_type] xác định ngừoi trả lời là staff hay khách hàng.
 <code>1</code> : khách hàng, <code>2</code> : staff
@apiSuccess   (data:)  {string}  [content] Nội dung comment
@apiSuccess   (data:)  {int}  [repl_status] trạng thái được trả lời;
<code>1</code>: chưa được trả lời; <code>2</code>: đã được trả lời
@apiSuccess   (data:)  {int}  [merchant_id] trạng thái của comment; <code>1</code>: hiện; <code>2</code>: ẩn
@apiSuccess   (data:)  {decimal} avg_rated Điểm đánh giá trung bình (có giá trị thấp nhất là 0.0 và cao nhất là 5.0)
@apiSuccess   (data:)  {int} count_repl Số comment trả lời 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[{
            "avg_rate": 3.5,
            "commenter_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "commenter_type": "STAFF",
            "content": null,
            "count_repl": 0,
            "created_time": "Fri, 22 Mar 2019 02:18:03 GMT",
            "id": "c8bd7fa8-34f2-4541-8878-505f0ffa0bc2",
            "images": [
                "https://mbodevstorage.file.core.windows.net/loyalty/images/0a148fd7-6c3d-4895-b75a-7f5b0108523c?se=2027-06-08T02%3A18%3A02Z&sp=r&sv=2018-03-28&sr=s&sig=u3rq210vOwVBSzktIGUIQpEcgzQBdQ9CDP4B%2B/RtHUM%3D",
                "https://mbodevstorage.file.core.windows.net/loyalty/images/4a02d8fe-13f7-488e-9f12-1c490674ecdf?se=2027-06-08T02%3A18%3A03Z&sp=r&sv=2018-03-28&sr=s&sig=adfu9g9h2h2lPTrLU%2BFVwtidpxz7BdPKHP4DSFdg8zE%3D"
            ],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "update_time": "Fri, 22 Mar 2019 02:18:03 GMT"
        }
  ...],
  "paging":{
    "page":1,
    "per_page":10,
    "total_page":1,
    "total_item":10
  }
}
"""
******************************************  Assign Comment   ***************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {put} /loyalty/api/v2.0/comments/assign Assign comment
@apiGroup Comment
@apiVersion 1.0.0
@apiName AssignComment
@apiDescription Dịch vụ chuyển tiếp bình luận

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {string} assignee         Mã định danh nhân viên cần phân công
@apiParam (Body:)  {string} id               Mã định danh comment cần phân công
@apiParam (Body:)  {number} [note]           Ghi chú phân công
@apiParam (Body:)  {string} tags             Mảng các mã định danh tag cần gán. Example: <code>["ec40489d-0a67-4466-90be-f8f3123b2cec","3b8ae590-226e-48e0-808f-fa1e0c972778"]</code>
@apiParamExample    {json}  Body example:
{
  "assignee": "435782b2-7d87-4441-87e3-2fd05c763128",
  "id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
  "note": "abcdefgh",
  "tags": ["ec40489d-0a67-4466-90be-f8f3123b2cec","3b8ae590-226e-48e0-808f-fa1e0c972778"]
}

@apiSuccess   {string}  assignee_id   Mã định danh nhân viên được phân công
@apiSuccess   {string}  comment_id   Mã định danh bình luận
@apiSuccess   {string}  created_time   Thời điểm phân công
@apiSuccess   {string}  created_user   Người phân công
@apiSuccess   {string}  id   Mã định danh
@apiSuccess   {string}  note   Ghi chú phân công
@apiSuccess   {string}  status   Trạng thái phân công. Giá trị: <code>1=Available,0=Disable</code>
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "datas": [
        {
            "assignee_id": "435782b2-7d87-4441-87e3-2fd05c763128",
            "comment_id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
            "created_time": "2019-07-08T03:10:07Z",
            "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "id": "ad416589-4fca-4500-b7b7-f4d649f7d519",
            "note": "abcdefgh",
            "status": 1
        },
        {
            "assignee_id": "435782b2-7d87-4441-87e3-2fd05c763128",
            "comment_id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
            "created_time": "2019-07-06T09:22:18Z",
            "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "id": "01f75135-2644-41c8-8132-bbe7f20e3e24",
            "note": "abcdefgh",
            "status": 0
        },
        {
            "assignee_id": "435782b2-7d87-4441-87e3-2fd05c763128",
            "comment_id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
            "created_time": "2019-07-06T03:17:39Z",
            "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "id": "719039e0-252b-456f-9d22-683440a87d24",
            "note": "abcde",
            "status": 0
        }
    ],
    "message": "request thành công."
}
"""
****************************************  List Comment Assignee  ***********************************
* version: 1.0.0                                                                                   *
* version: 1.0.1                                                                                   *
****************************************************************************************************
"""
@api {get} /loyalty/api/v2.0/comments/list-assign List comment assignee
@apiGroup Comment
@apiVersion 1.0.1
@apiName ListCommentAssignee
@apiDescription Dịch vụ lấy danh sách phân công bình luận

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam   (Query:)  {string}  page_social_id   Mã định danh app
@apiParam   (Query:)  {number}  social_type      Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>
@apiParam   (Query:)  {string}  assignees        Các mã định danh nhân viên cần tìm kiếm. Example: <code>435782b2-7d87-4441-87e3-2fd05c763128,435782b2-7d87-4441-87e3-2fd05c763128</code>
@apiParam   (Query:)  {string}  tags             Các mã định danh tag cần tìm kiếm. Example: <code>ec40489d-0a67-4466-90be-f8f3123b2cec,3b8ae590-226e-48e0-808f-fa1e0c972778</code>
@apiParam   (Query:)  {number}  assign_status    Trạng thái phân công của comment cần tìm kiếm. Giá trị: <code>1=Normal, 2=Resolved</code>
@apiParam   (Query:)  {string}  classify         Trạng thái cảm xúc cần tìm kiếm. Giá trị: <code>0=Negative,1=Neutral,2=Positive</code>
@apiParam   (Query:)  {string}  content          Nội dung bình luận cầm tìm kiếm.
@apiParam   (Query:)  {string}  profile_id       Mã profile cần tìm kiếm
@apiParam   (Query:)  {string}  comment_id       Mã bình luận cần lấy
@apiParam   (Query:)  {string}  object_type      Kiểu đối tượng cần lấy comment. Giá trị: <code>VOUCHER, PRODUCT, STORE, MERCHANT</code>

@apiSuccess   {array}  assignees   Thông tin phân công của comment
@apiSuccess   {object}  comment   Thông tin comment
@apiSuccess   {string}  merchant_id   Mã định danh merchant
@apiSuccess   {object}  page   Thông tin page
@apiSuccess   {array}  tags   Thông tin nhãn được gán
@apiSuccess   {object}  topic   Thông tin topic

@apiSuccess   {string}  assignees.assignee_id   Mã định danh nhân viên được phân công
@apiSuccess   {string}  assignees.comment_id   Mã định danh bình luận
@apiSuccess   {string}  assignees.created_time   Thời điểm phân công
@apiSuccess   {string}  assignees.created_user   Người phân công
@apiSuccess   {string}  assignees.id   Mã định danh
@apiSuccess   {string}  assignees.note   Ghi chú phân công
@apiSuccess   {string}  assignees.status   Trạng thái phân công. Giá trị: <code>1=Available,0=Disable</code>

@apiSuccess   {number}  comment.assign_status     Trạng thái phân công comment. Giá trị: <code>1=Normal,2=Resolved</code>
@apiSuccess   {number}  comment.classify          Trạng thái cảm xúc của comment. Giá trị: <code>0=Negative,1=Neutral,2=Positive</code>
@apiSuccess   {string}  comment.commenter_avatar  Avatar của người thực hiện comment
@apiSuccess   {string}  comment.commenter_id      Mã định danh người comment trên hệ thống loyalty
@apiSuccess   {string}  comment.commenter_name    Tên người comment
@apiSuccess   {string}  comment.content           Nội dung comment
@apiSuccess   {string}  comment.created_time      Thời gian thực hiện comment
@apiSuccess   {string}  comment.id                Mã định danh comment
@apiSuccess   {string}  comment.profile_id        Mã định danh người comment trên hệ thống profiling
@apiSuccess   {number}  comment.read_status       Trạng thái đã đọc comment. Giá trị: <code>0=Chưa đọc,1=Đã đọc</code>
@apiSuccess   {number}  comment.repl_staff_status Trạng thái nhân viên đã trả lời comment. Giá trị: <code>0=Chưa trả lời, 1=Đã trả lời</code>
@apiSuccess   {arraystring}  comment.attachments  Mảng link ảnh đính kèm.

@apiSuccess   {string}  page.name   Tên page
@apiSuccess   {string}  page.page_social_id   Mã định danh app
@apiSuccess   {number}  page.social_type      Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>

@apiSuccess   {string}  tags.id               Mã định danh nhãn cần cập nhật
@apiSuccess   {string}  tags.page_social_id   Mã ứng dụng
@apiSuccess   {number}  tags.social_type      Loại mạng xã hội. <code>Giá trị: 5=MobileApp</code>
@apiSuccess   {string}  tags.value            Tên nhãn
@apiSuccess   {object}  tags.properties       Thuộc tính của nhãn
@apiSuccess   {string}  tags.merchant_id      Mã định danh merchant

@apiSuccess   {string}  topic.id    Mã định danh topic
@apiSuccess   {string}  topic.name  Nội dung topic

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "datas": [
        {
            "assignees": [
                {
                    "assignee_id": "435782b2-7d87-4441-87e3-2fd05c763128",
                    "comment_id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
                    "created_time": "2019-07-08T03:10:07Z",
                    "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "id": "ad416589-4fca-4500-b7b7-f4d649f7d519",
                    "note": "abcdefgh",
                    "status": 1
                },
                {
                    "assignee_id": "435782b2-7d87-4441-87e3-2fd05c763128",
                    "comment_id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
                    "created_time": "2019-07-06T09:22:18Z",
                    "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "id": "01f75135-2644-41c8-8132-bbe7f20e3e24",
                    "note": "abcdefgh",
                    "status": 0
                },
                {
                    "assignee_id": "435782b2-7d87-4441-87e3-2fd05c763128",
                    "comment_id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
                    "created_time": "2019-07-06T03:17:39Z",
                    "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "id": "719039e0-252b-456f-9d22-683440a87d24",
                    "note": "abcde",
                    "status": 0
                }
            ],
            "comment": {
                "assign_status": 1,
                "classify": 1,
                "commenter_avatar": "https://mbodevstorage.blob.core.windows.net/images/a6ca19c5-d6dc-483b-a34d-9fd35ab3ddd5",
                "commenter_id": "ffa00fc3-5db9-43d0-a6c4-d7a49a3123ab",
                "commenter_name": "Lê Quang Nguyên",
                "content": "abc 1",
                "created_time": "2019-06-27T03:14:29Z",
                "id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
                "profile_id": "257970f6-60b9-468a-8564-e1614441a2f2",
                "read_status": 0,
                "repl_staff_status": 0,
                "attachments": ["https://mbodevstorage.blob.core.windows.net/images/a6ca19c5-d6dc-483b-a34d-9fd35ab3ddd5"]
            },
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "page": {
                "name": "Sakuko",
                "page_social_id": "SAKUKO",
                "social_type": "5"
            },
            "tags": [
                {
                    "id": "3b8ae590-226e-48e0-808f-fa1e0c972778",
                    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
                    "page_social_id": "SAKUKO",
                    "properties": {
                        "background_color": "#000000",
                        "foreground_color": "#FFFFFF"
                    },
                    "social_type": 5,
                    "value": "pingcom1"
                },
                {
                    "id": "ec40489d-0a67-4466-90be-f8f3123b2cec",
                    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
                    "page_social_id": "SAKUKO",
                    "properties": {
                        "background_color": "#000000",
                        "foreground_color": "#FFFFFF"
                    },
                    "social_type": 5,
                    "value": "test1"
                }
            ],
            "topic": {
                "id": "46278e2f-044c-4325-9782-2e9daae4aa63",
                "name": "Tặng voucher giảm giá 1 nghìn cho khách"
            }
        }
    ],
    "message": "request thành công.",
    ...
}
"""
==================
"""
@api {get} /loyalty/api/v2.0/comments/list-assign List comment assignee
@apiGroup Comment
@apiVersion 1.0.0
@apiName ListCommentAssignee
@apiDescription Dịch vụ lấy danh sách phân công bình luận

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam   (Query:)  {string}  page_social_id   Mã định danh app
@apiParam   (Query:)  {number}  social_type      Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>
@apiParam   (Query:)  {string}  assignees        Các mã định danh nhân viên cần tìm kiếm. Example: <code>435782b2-7d87-4441-87e3-2fd05c763128,435782b2-7d87-4441-87e3-2fd05c763128</code>
@apiParam   (Query:)  {string}  tags             Các mã định danh tag cần tìm kiếm. Example: <code>ec40489d-0a67-4466-90be-f8f3123b2cec,3b8ae590-226e-48e0-808f-fa1e0c972778</code>
@apiParam   (Query:)  {number}  assign_status    Trạng thái phân công của comment cần tìm kiếm. Giá trị: <code>1=Normal, 2=Resolved</code>
@apiParam   (Query:)  {string}  classify         Trạng thái cảm xúc cần tìm kiếm. Giá trị: <code>0=Negative,1=Neutral,2=Positive</code>
@apiParam   (Query:)  {string}  content          Nội dung bình luận cầm tìm kiếm.
@apiParam   (Query:)  {string}  profile_id       Mã profile cần tìm kiếm
@apiParam   (Query:)  {string}  comment_id       Mã bình luận cần lấy
@apiParam   (Query:)  {string}  object_type      Kiểu đối tượng cần lấy comment. Giá trị: <code>VOUCHER, PRODUCT, STORE, MERCHANT</code>

@apiSuccess   {array}  assignees   Thông tin phân công của comment
@apiSuccess   {object}  comment   Thông tin comment
@apiSuccess   {string}  merchant_id   Mã định danh merchant
@apiSuccess   {object}  page   Thông tin page
@apiSuccess   {array}  tags   Thông tin nhãn được gán
@apiSuccess   {object}  topic   Thông tin topic

@apiSuccess   {string}  assignees.assignee_id   Mã định danh nhân viên được phân công
@apiSuccess   {string}  assignees.comment_id   Mã định danh bình luận
@apiSuccess   {string}  assignees.created_time   Thời điểm phân công
@apiSuccess   {string}  assignees.created_user   Người phân công
@apiSuccess   {string}  assignees.id   Mã định danh
@apiSuccess   {string}  assignees.note   Ghi chú phân công
@apiSuccess   {string}  assignees.status   Trạng thái phân công. Giá trị: <code>1=Available,0=Disable</code>

@apiSuccess   {number}  comment.assign_status     Trạng thái phân công comment. Giá trị: <code>1=Normal,2=Resolved</code>
@apiSuccess   {number}  comment.classify          Trạng thái cảm xúc của comment. Giá trị: <code>0=Negative,1=Neutral,2=Positive</code>
@apiSuccess   {string}  comment.commenter_avatar  Avatar của người thực hiện comment
@apiSuccess   {string}  comment.commenter_id      Mã định danh người comment trên hệ thống loyalty
@apiSuccess   {string}  comment.commenter_name    Tên người comment
@apiSuccess   {string}  comment.content           Nội dung comment
@apiSuccess   {string}  comment.created_time      Thời gian thực hiện comment
@apiSuccess   {string}  comment.id                Mã định danh comment
@apiSuccess   {string}  comment.profile_id        Mã định danh người comment trên hệ thống profiling
@apiSuccess   {number}  comment.read_status       Trạng thái đã đọc comment. Giá trị: <code>0=Chưa đọc,1=Đã đọc</code>
@apiSuccess   {number}  comment.repl_staff_status Trạng thái nhân viên đã trả lời comment. Giá trị: <code>0=Chưa trả lời, 1=Đã trả lời</code>

@apiSuccess   {string}  page.name   Tên page
@apiSuccess   {string}  page.page_social_id   Mã định danh app
@apiSuccess   {number}  page.social_type      Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>

@apiSuccess   {string}  tags.id               Mã định danh nhãn cần cập nhật
@apiSuccess   {string}  tags.page_social_id   Mã ứng dụng
@apiSuccess   {number}  tags.social_type      Loại mạng xã hội. <code>Giá trị: 5=MobileApp</code>
@apiSuccess   {string}  tags.value            Tên nhãn
@apiSuccess   {object}  tags.properties       Thuộc tính của nhãn
@apiSuccess   {string}  tags.merchant_id      Mã định danh merchant

@apiSuccess   {string}  topic.id    Mã định danh topic
@apiSuccess   {string}  topic.name  Nội dung topic

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "datas": [
        {
            "assignees": [
                {
                    "assignee_id": "435782b2-7d87-4441-87e3-2fd05c763128",
                    "comment_id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
                    "created_time": "2019-07-08T03:10:07Z",
                    "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "id": "ad416589-4fca-4500-b7b7-f4d649f7d519",
                    "note": "abcdefgh",
                    "status": 1
                },
                {
                    "assignee_id": "435782b2-7d87-4441-87e3-2fd05c763128",
                    "comment_id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
                    "created_time": "2019-07-06T09:22:18Z",
                    "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "id": "01f75135-2644-41c8-8132-bbe7f20e3e24",
                    "note": "abcdefgh",
                    "status": 0
                },
                {
                    "assignee_id": "435782b2-7d87-4441-87e3-2fd05c763128",
                    "comment_id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
                    "created_time": "2019-07-06T03:17:39Z",
                    "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "id": "719039e0-252b-456f-9d22-683440a87d24",
                    "note": "abcde",
                    "status": 0
                }
            ],
            "comment": {
                "assign_status": 1,
                "classify": 1,
                "commenter_avatar": "https://mbodevstorage.blob.core.windows.net/images/a6ca19c5-d6dc-483b-a34d-9fd35ab3ddd5",
                "commenter_id": "ffa00fc3-5db9-43d0-a6c4-d7a49a3123ab",
                "commenter_name": "Lê Quang Nguyên",
                "content": "abc 1",
                "created_time": "2019-06-27T03:14:29Z",
                "id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
                "profile_id": "257970f6-60b9-468a-8564-e1614441a2f2",
                "read_status": 0,
                "repl_staff_status": 0
            },
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "page": {
                "name": "Sakuko",
                "page_social_id": "SAKUKO",
                "social_type": "5"
            },
            "tags": [
                {
                    "id": "3b8ae590-226e-48e0-808f-fa1e0c972778",
                    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
                    "page_social_id": "SAKUKO",
                    "properties": {
                        "background_color": "#000000",
                        "foreground_color": "#FFFFFF"
                    },
                    "social_type": 5,
                    "value": "pingcom1"
                },
                {
                    "id": "ec40489d-0a67-4466-90be-f8f3123b2cec",
                    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
                    "page_social_id": "SAKUKO",
                    "properties": {
                        "background_color": "#000000",
                        "foreground_color": "#FFFFFF"
                    },
                    "social_type": 5,
                    "value": "test1"
                }
            ],
            "topic": {
                "id": "46278e2f-044c-4325-9782-2e9daae4aa63",
                "name": "Tặng voucher giảm giá 1 nghìn cho khách"
            }
        }
    ],
    "message": "request thành công.",
    ...
}
"""
******************************************  Resolve Comment   **************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {put} /loyalty/api/v2.0/comments/resolve Resolve comment
@apiGroup Comment
@apiVersion 1.0.0
@apiName ResolveComment
@apiDescription Dịch vụ hoàn tất bình luận

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)  {string}  comment_id       Mã định danh comment cần hoàn tất
@apiParam   (Body:)  {string}  page_social_id   Mã định danh app
@apiParam   (Body:)  {number}  social_type      Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>
@apiParamExample    {json}  Body example:
{
  "page_social_id": "SAKUKO",
  "comment_id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
  "social_type": 5
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}
"""
*******************************************  Read Comment   ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {put} /loyalty/api/v2.0/comments/read Read comment
@apiGroup Comment
@apiVersion 1.0.0
@apiName ReadComment
@apiDescription Dịch vụ đánh dâu bình luận đã đọc

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)  {string}  comment_id       Mã định danh comment cần đọc
@apiParam   (Body:)  {string}  page_social_id   Mã định danh app
@apiParam   (Body:)  {number}  social_type      Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>
@apiParamExample    {json}  Body example:
{
  "page_social_id": "SAKUKO",
  "comment_id": "01c061ab-bbe2-45ca-a126-e32dcf843f7e",
  "social_type": 5
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}
"""
****************************************  List Comment Assignee  ***********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /loyalty/api/v2.0/comments/summary Summary comment data
@apiGroup Comment
@apiVersion 1.0.0
@apiName SummaryComment
@apiDescription Dịch vụ thống kê dữ liệu bình luận

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  page_social_id   Mã định danh app
@apiParam   (Query:)  {number}  social_type      Loại mạng xã hội. Giá trị: <code>5=MobileApp</code>
@apiParam   (Query:)  {string}  assignees        Các mã định danh nhân viên cần tìm kiếm. Example: <code>435782b2-7d87-4441-87e3-2fd05c763128,435782b2-7d87-4441-87e3-2fd05c763128</code>
@apiParam   (Query:)  {string}  tags             Các mã định danh tag cần tìm kiếm. Example: <code>ec40489d-0a67-4466-90be-f8f3123b2cec,3b8ae590-226e-48e0-808f-fa1e0c972778</code>
@apiParam   (Query:)  {number}  assign_status    Trạng thái phân công của comment cần tìm kiếm. Giá trị: <code>1=Normal, 2=Resolved</code>
@apiParam   (Query:)  {string}  classify         Trạng thái cảm xúc cần tìm kiếm. Giá trị: <code>0=Negative,1=Neutral,2=Positive</code>
@apiParam   (Query:)  {string}  content          Nội dung bình luận cầm tìm kiếm.
@apiParam   (Query:)  {string}  profile_id       Mã profile cần tìm kiếm
@apiParam   (Query:)  {string}  comment_id       Mã bình luận cần lấy

@apiSuccess   {array}  summary   Thông tin thống kê
@apiSuccess   {object}  summary.data   Dữ liệu thống kê
@apiSuccess   {string}  summary.name   Tên dữ liệu thống kê
@apiSuccess   {number}  summary.data.total_unread_comment_voucher   Số lượng bình luận voucher chưa đọc

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "summary": [
    {
      "data": {
        "total_unread_comment_voucher": 105
      },
      "name": "total_unread"
    }
  ]
}
"""
******************************************  Classify Comment   *************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {put} /loyalty/api/v2.0/comments/classify Classify comment
@apiGroup Comment
@apiVersion 1.0.0
@apiName ClassifyComment
@apiDescription Dịch vụ phân loại cảm xúc bình luận

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)  {string}  comment_id       Mã định danh comment cần phân loại
@apiParam   (Body:)  {number}  classify         Trạng thái cảm xúc. Giá trị: <code>0=Negative,1=Neutral,2=Positive</code>
@apiParamExample    {json}  Body example:
{
  "comment_id": "002bc2a6-3a7d-421e-9b07-1921e681b417",
  "classify": 2
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}
"""
******************************************  Hide Comment   *****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {put} /loyalty/api/v2.0/comments/hide Hide comment
@apiGroup Comment
@apiVersion 1.0.0
@apiName HideComment
@apiDescription Dịch vụ ẩn hiện bình luận

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)  {string}  comment_id       Mã định danh comment cần phân loại
@apiParam   (Body:)  {number}  status           Trạng thái bình luận. Giá trị: <code>1=Display,2=Disable</code>
@apiParamExample    {json}  Body example:
{
  "comment_id": "002bc2a6-3a7d-421e-9b07-1921e681b417",
  "status": 2
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}
"""
*****************************************  Update Comment   ****************************************
* version: 1.0.1                                                                                   *
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {put} /loyalty/api/v2.0/comments/update Update comment
@apiGroup Comment
@apiVersion 1.0.1
@apiName UpdateComment
@apiDescription Dịch vụ cập nhật bình luận

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Form:)  {Files}  [images] Danh sách file của ảnh mô tả cho comment
@apiParam   (Form:)  {string}  comment_id Id comment cần update
@apiParam   (Form:)  {string}  content Nội dung comment
@apiParam   (Form:)  {array}  image_delete Danh sách các link ảnh cần xóa


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data":{
      "comment_id": "uuid"
    }
}
"""
====================
"""
@api {put} /loyalty/api/v2.0/comments/update Update comment
@apiGroup Comment
@apiVersion 1.0.0
@apiName UpdateComment
@apiDescription Dịch vụ cập nhật bình luận

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Form:)  {Files}  [images] Danh sách file của ảnh mô tả cho comment
@apiParam   (Form:)  {string}  comment_id Id comment cần update
@apiParam   (Form:)  {string}  content Nội dung comment

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data":{
      "comment_id": "uuid"
    }
}
"""
*****************************************  Delete Comment   ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {delete} /loyalty/api/v2.0/comments/delete Delete comment
@apiGroup Comment
@apiVersion 1.0.0
@apiName DeleteComment
@apiDescription Dịch vụ xóa bình luận

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)  {array}  comment_ids  Mảng Id comment cần update.
@apiParamExample  {json}  Body example:
{
  "comment_ids": ["002bc2a6-3a7d-421e-9b07-1921e681b417"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}
"""
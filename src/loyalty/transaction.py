# ************************************* Import Transaction *************************************
# version: 1.0.3
# version: 1.0.2
# version: 1.0.1
# version: 1.0.0
# **********************************************************************************************

# Version: 1.0.3
"""
@api {post} /loyalty/transaction/api/v2.1/transactions/upsert  Cập nhật thông tin giao dịch
@apiDescription Dịch vụ để cập nhật thông tin giao dịch của một profile.
<li>Nếu profile chưa có trên hệ thống --> t<PERSON><PERSON> mới profile</li>
<li><PERSON><PERSON><PERSON> giao d<PERSON>(mã giao dịch) đã tồn tại trên hệ thống --> cập nhật thông tin mới.</li>
@apiVersion 1.0.3
@apiGroup Transaction
@apiName UpsertTransaction

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiHeader (Headers:) {String} X-Merchant-ID <code>xxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {Object}  profile   Thông tin profile thực hiện giao dịch. Hệ thống sẽ tự động tạo profile nếu chưa có.
@apiParam   (Body:)   {Object}  transaction   Thông tin giao dịch
@apiParam   (Body:)   {String}  source  Nguồn phát sinh giao dịch. Example: offline, online, affilite
@apiParam   (Body:)   {Number}  [own_business]   Nghiệp vụ xử lý riêng cho từng giao dịch
                                        <ul>
                                            <li><code>1</code> : Thông tin profile giao dịch được lấy theo mã voucher redeem</li>
                                        </ul>

@apiParam   (Profile)   {String}  [id]  Định danh profile trên hệ thống Mobio.
@apiParam   (Profile)   {String}  [phone_number]  Số điện thoại.
@apiParam   (Profile)   {String}  [email]  Địa chỉ thư điện tử.
@apiParam   (Profile)   {String}  [tax_code]  Mã số thuế. Dùng trong trường hợp đại diện công ty mua hàng.
@apiParam   (Profile)   {Object}  [third_party_info]    Thông tin thêm của profile trên hệ thống của tenant.

@apiParam   (Transaction)   {String}  code  Mã giao dịch.
@apiParam   (Transaction)   {String}  action_type   Loại hành động giao dịch: mua hàng (buy),...
@apiParam   (Transaction)   {float}   action_time   Thời gian giao dịch (Timestamp, lấy đến milisecond).
@apiParam   (Transaction)   {Number}  amount  Tổng số tiền giao dịch.
@apiParam   (Transaction)   {Number}  [payment_point]  Số điểm được dùng để thanh toán
@apiParam   (Transaction)   {String=VND, USD}  currency_code   Đơn vị tiền tệ.
@apiParam   (Transaction)   {float}   status  Trạng thái giao dịch.
@apiParam   (Transaction)   {Item[]}  [items]  Danh sách item(sản phẩm) trong giao dịch.

<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id<span class="label label-optional">optional</span></td>
      <td>String</td>
      <td>Định danh item trên hệ thống Mobio</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên của item</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>StringArray</td>
      <td>Danh sách tag được gán cho item</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>Number</td>
      <td>Số lượng item</td>
    </tr>
    <tr>
      <td>price</td>
      <td>Number</td>
      <td>Đơn giá của item (sản phẩm)</td>
    </tr>
    <tr>
      <td>total_amount</td>
      <td>Number</td>
      <td>tổng giá trị của item (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã item (mã sản phẩm)</td>
    </tr>
    <tr>
        <td>supplier</td>
        <td>object </td>
        <td>Thông tin thương hiệu của sản phẩm </td>
    </tr>
    <tr>
        <td>supplier.code</td>
        <td>string </td>
        <td>Mã thương hiệu </td>
    </tr>
    <tr>
        <td>supplier.name</td>
        <td>string </td>
        <td>Tên thương hiệu</td>
    </tr>
  </tbody>
</table>
@apiParam   (Transaction)   {Voucher[]}   [vouchers]  Danh sách voucher sử dụng trong lần giao dịch.
@apiParam   (Transaction)   {Object}  [store]   Thông tin cửa hàng thực hiện giao dịch.
@apiParam   (Transaction)   {Object}  [payment]   Thông tin thanh toán.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>type</td>
      <td>String</td>
      <td>Hình thức thanh toán: atm, cash</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card</td>
    </tr>
  </tbody>
</table>
@apiParam   (Transaction)   {Object}  [staff_info]   Thông tin nhân viên thực hiện giao dịch
@apiParam   (Transaction)   {String}  [staff_info.staff_id]   ID nhân viên trên hệ thống Mobio


@apiParamExample    {json}      Body example:
{
  "profile": {
    "id": "d071c51e-4cec-43a0-85e1-c0768982787d",
    "phone_number": "84399285833",
    "email": "<EMAIL>",
    "people_id": "*********",
    "tax_code": "xxxxxx",
    "third_party_info": {
      "id": "86a6d10d-7b19-46f0-bdf2-7daef3010680"
    }
  },
  "transaction": {
    "code": "07e8c3db-a3bf-4508-8f79-297a31e2c02e",
    "action_type": "buy",
    "action_time": 1538703463.440775,
    "amount": 100000,
    "payment_point": 10,
    "currency_code": "vnd",
    "status": 0.0,
    "items": [
      {
        "id": "ea4d37b1-5522-4494-ac21-3a3012b2efb2",
        "name": "",
        "tags": ["", ""],
        "quantity": 2,
        "code": "SP001",
        "price": 100000,
        "total_amount": 200000,
        "supplier":{
            "code": "BG618T7X",
            "name": "TOHATO"
        }
      }
    ],
    "vouchers": [
      {
        "code": "AAIOWNDAFJ"
      }
    ],
    "store": {
      "code": "CH001"
    },
    "payment": {
      "type": "mc",
      "code": "*********",
      "name": ""
    },
    "staff_info": {
        "staff_id": "b3350cbc-974e-4130-983d-ac1289eb53f6"
    }
  },
  "source": "offline",
  "own_business": 1
}

@apiSuccess (Response Headers:)   {string}  request_id   ID request (Dùng để đối soát dữ liệu).
@apiSuccess {string}    message     Nội dung phản hồi
@apiSuccess {Integer}   code        Mã phản hồi

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}

"""


# Version: 1.0.2
"""
@api {post} /loyalty/transaction/api/v2.0/transactions/upsert  Cập nhật thông tin giao dịch
@apiDescription Dịch vụ để cập nhật thông tin giao dịch của một profile.
<li>Nếu profile chưa có trên hệ thống --> tạo mới profile</li>
<li>Nếu giao dịch(mã giao dịch) đã tồn tại trên hệ thống --> cập nhật thông tin mới.</li>
@apiVersion 1.0.2
@apiGroup Transaction
@apiName UpsertTransaction

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiHeader (Headers:) {String} X-Merchant-ID <code>xxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {Object}  profile   Thông tin profile thực hiện giao dịch. Hệ thống sẽ tự động tạo profile nếu chưa có.
@apiParam   (Body:)   {Object}  transaction   Thông tin giao dịch
@apiParam   (Body:)   {String}  source  Nguồn phát sinh giao dịch. Example: offline, online, affilite
@apiParam   (Body:)   {Number}  [own_business]   Nghiệp vụ xử lý riêng cho từng giao dịch
                                        <ul>
                                            <li><code>1</code> : Thông tin profile giao dịch được lấy theo mã voucher redeem</li>
                                        </ul>

@apiParam   (Profile)   {String}  [id]  Định danh profile trên hệ thống Mobio.
@apiParam   (Profile)   {String}  [phone_number]  Số điện thoại.
@apiParam   (Profile)   {String}  [email]  Địa chỉ thư điện tử.
@apiParam   (Profile)   {String}  [tax_code]  Mã số thuế. Dùng trong trường hợp đại diện công ty mua hàng.
@apiParam   (Profile)   {Object}  [third_party_info]    Thông tin thêm của profile trên hệ thống của tenant.

@apiParam   (Transaction)   {String}  code  Mã giao dịch.
@apiParam   (Transaction)   {String}  action_type   Loại hành động giao dịch: mua hàng (buy),...
@apiParam   (Transaction)   {float}   action_time   Thời gian giao dịch (Timestamp, lấy đến milisecond).
@apiParam   (Transaction)   {Number}  amount  Tổng số tiền giao dịch.
@apiParam   (Transaction)   {Number}  [payment_point]  Số điểm được dùng để thanh toán
@apiParam   (Transaction)   {String=VND, USD}  currency_code   Đơn vị tiền tệ.
@apiParam   (Transaction)   {float}   status  Trạng thái giao dịch.
@apiParam   (Transaction)   {Item[]}  [items]  Danh sách item(sản phẩm) trong giao dịch.

<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id<span class="label label-optional">optional</span></td>
      <td>String</td>
      <td>Định danh item trên hệ thống Mobio</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên của item</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>StringArray</td>
      <td>Danh sách tag được gán cho item</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>Number</td>
      <td>Số lượng item</td>
    </tr>
    <tr>
      <td>price</td>
      <td>Number</td>
      <td>Đơn giá của item (sản phẩm)</td>
    </tr>
    <tr>
      <td>total_amount</td>
      <td>Number</td>
      <td>tổng giá trị của item (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã item (mã sản phẩm)</td>
    </tr>
    <tr>
        <td>supplier</td>
        <td>object </td>
        <td>Thông tin thương hiệu của sản phẩm </td>
    </tr>
    <tr>
        <td>supplier.code</td>
        <td>string </td>
        <td>Mã thương hiệu </td>
    </tr>
    <tr>
        <td>supplier.name</td>
        <td>string </td>
        <td>Tên thương hiệu</td>
    </tr>
  </tbody>
</table>
@apiParam   (Transaction)   {Voucher[]}   [vouchers]  Danh sách voucher sử dụng trong lần giao dịch.
@apiParam   (Transaction)   {Object}  [store]   Thông tin cửa hàng thực hiện giao dịch.
@apiParam   (Transaction)   {Object}  [payment]   Thông tin thanh toán.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>type</td>
      <td>String</td>
      <td>Hình thức thanh toán: atm, cash</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card</td>
    </tr>
  </tbody>
</table>

@apiParamExample    {json}      Body example:
{
  "profile": {
    "id": "d071c51e-4cec-43a0-85e1-c0768982787d",
    "phone_number": "84399285833",
    "email": "<EMAIL>",
    "people_id": "*********",
    "tax_code": "xxxxxx",
    "third_party_info": {
      "id": "86a6d10d-7b19-46f0-bdf2-7daef3010680"
    }
  },
  "transaction": {
    "code": "07e8c3db-a3bf-4508-8f79-297a31e2c02e",
    "action_type": "buy",
    "action_time": 1538703463.440775,
    "amount": 100000,
    "payment_point": 10,
    "currency_code": "vnd",
    "status": 0.0,
    "items": [
      {
        "id": "ea4d37b1-5522-4494-ac21-3a3012b2efb2",
        "name": "",
        "tags": ["", ""],
        "quantity": 2,
        "code": "SP001",
        "price": 100000,
        "total_amount": 200000,
        "supplier":{
            "code": "BG618T7X",
            "name": "TOHATO"
        }
      }
    ],
    "vouchers": [
      {
        "code": "AAIOWNDAFJ"
      }
    ],
    "store": {
      "code": "CH001"
    },
    "payment": {
      "type": "mc",
      "code": "*********",
      "name": ""
    }
  },
  "source": "offline",
  "own_business": 1
}

@apiSuccess (Response Headers:)   {string}  request_id   ID request (Dùng để đối soát dữ liệu).
@apiSuccess {string}    message     Nội dung phản hồi
@apiSuccess {Integer}   code        Mã phản hồi

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}

"""

# Version: 1.0.1
"""
@api {post} /loyalty/transaction/api/v2.0/transactions/upsert  Cập nhật thông tin giao dịch
@apiDescription Dịch vụ để cập nhật thông tin giao dịch của một profile.
<li>Nếu profile chưa có trên hệ thống --> tạo mới profile</li>
<li>Nếu giao dịch(mã giao dịch) đã tồn tại trên hệ thống --> cập nhật thông tin mới.</li>
@apiVersion 1.0.1
@apiGroup Transaction
@apiName UpsertTransaction

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiHeader (Headers:) {String} X-Merchant-ID <code>xxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {Object}  profile   Thông tin profile thực hiện giao dịch. Hệ thống sẽ tự động tạo profile nếu chưa có.
@apiParam   (Body:)   {Object}  transaction   Thông tin giao dịch
@apiParam   (Body:)   {String}  source  Nguồn phát sinh giao dịch. Example: offline, online, affilite

@apiParam   (Profile)   {String}  [id]  Định danh profile trên hệ thống Mobio.
@apiParam   (Profile)   {String}  [phone_number]  Số điện thoại.
@apiParam   (Profile)   {String}  [email]  Địa chỉ thư điện tử.
@apiParam   (Profile)   {String}  [tax_code]  Mã số thuế. Dùng trong trường hợp đại diện công ty mua hàng.
@apiParam   (Profile)   {Object}  [third_party_info]    Thông tin thêm của profile trên hệ thống của tenant.

@apiParam   (Transaction)   {String}  code  Mã giao dịch.
@apiParam   (Transaction)   {String}  action_type   Loại hành động giao dịch: mua hàng (buy),...
@apiParam   (Transaction)   {float}   action_time   Thời gian giao dịch.
@apiParam   (Transaction)   {Number}  amount  Tổng số tiền giao dịch.
@apiParam   (Transaction)   {String=VND, USD}  currency_code   Đơn vị tiền tệ.
@apiParam   (Transaction)   {float}   status  Trạng thái giao dịch.
@apiParam   (Transaction)   {Item[]}  [items]  Danh sách item(sản phẩm) trong giao dịch.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id<span class="label label-optional">optional</span></td>
      <td>String</td>
      <td>Định danh item trên hệ thống Mobio</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên của item</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>StringArray</td>
      <td>Danh sách tag được gán cho item</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>Number</td>
      <td>Số lượng item</td>
    </tr>
    <tr>
      <td>price</td>
      <td>Number</td>
      <td>Đơn giá của item (sản phẩm)</td>
    </tr>
    <tr>
      <td>total_amount</td>
      <td>Number</td>
      <td>tổng giá trị của item (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã item (mã sản phẩm)</td>
    </tr>
    <tr>
        <td>supplier</td>
        <td>object </td>
        <td>Thông tin thương hiệu của sản phẩm </td>
    </tr>
    <tr>
        <td>supplier.code</td>
        <td>string </td>
        <td>Mã thương hiệu </td>
    </tr>
    <tr>
        <td>supplier.name</td>
        <td>string </td>
        <td>Tên thương hiệu</td>
    </tr>
  </tbody>
</table>
@apiParam   (Transaction)   {Voucher[]}   [vouchers]  Danh sách voucher sử dụng trong lần giao dịch.
@apiParam   (Transaction)   {Object}  [store]   Thông tin cửa hàng thực hiện giao dịch.
@apiParam   (Transaction)   {Object}  [payment]   Thông tin thanh toán.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>type</td>
      <td>String</td>
      <td>Hình thức thanh toán: atm, cash</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card</td>
    </tr>
  </tbody>
</table>

@apiParamExample    {json}      Body example:
{
  "profile": {
    "id": "d071c51e-4cec-43a0-85e1-c0768982787d",
    "phone_number": "84399285833",
    "email": "<EMAIL>",
    "people_id": "*********",
    "tax_code": "xxxxxx",
    "third_party_info": {
      "id": "86a6d10d-7b19-46f0-bdf2-7daef3010680"
    }
  },
  "transaction": {
    "code": "07e8c3db-a3bf-4508-8f79-297a31e2c02e",
    "action_type": "buy",
    "action_time": 1538703463.440775,
    "amount": 100000,
    "currency_code": "vnd",
    "status": 0.0,
    "items": [
      {
        "id": "ea4d37b1-5522-4494-ac21-3a3012b2efb2",
        "name": "",
        "tags": ["", ""],
        "quantity": 2,
        "code": "SP001",
        "price": 100000,
        "total_amount": 200000,
        "supplier":{
            "code": "BG618T7X",
            "name": "TOHATO"
        }
      }
    ],
    "vouchers": [
      {
        "code": "AAIOWNDAFJ"
      }
    ],
    "store": {
      "code": "CH001"
    },
    "payment": {
      "type": "mc",
      "code": "*********",
      "name": ""
    }
  },
  "source": "offline"
}
"""

# Version: 1.0.0
"""
@api {post} /loyalty/transaction/api/v2.0/transactions/upsert  Cập nhật thông tin giao dịch
@apiDescription Dịch vụ để cập nhật thông tin giao dịch của một profile.
<li>Nếu profile chưa có trên hệ thống --> tạo mới profile</li>
<li>Nếu giao dịch(mã giao dịch) đã tồn tại trên hệ thống --> cập nhật thông tin mới.</li>
@apiVersion 1.0.0
@apiGroup Transaction
@apiName UpsertTransaction

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiHeader (Headers:) {String} X-Merchant-ID <code>xxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {Object}  profile   Thông tin profile thực hiện giao dịch. Hệ thống sẽ tự động tạo profile nếu chưa có.
@apiParam   (Body:)   {Object}  transaction   Thông tin giao dịch
@apiParam   (Body:)   {String}  source  Nguồn phát sinh giao dịch. Example: offline, online, affilite

@apiParam   (Profile)   {String}  [id]  Định danh profile trên hệ thống Mobio.
@apiParam   (Profile)   {String}  [phone_number]  Số điện thoại.
@apiParam   (Profile)   {String}  [email]  Địa chỉ thư điện tử.
@apiParam   (Profile)   {String}  [tax_code]  Mã số thuế. Dùng trong trường hợp đại diện công ty mua hàng.
@apiParam   (Profile)   {Object}  [third_party_info]    Thông tin thêm của profile trên hệ thống của tenant.

@apiParam   (Transaction)   {String}  code  Mã giao dịch.
@apiParam   (Transaction)   {String}  action_type   Loại hành động giao dịch: mua hàng (buy),...
@apiParam   (Transaction)   {float}   action_time   Thời gian giao dịch.
@apiParam   (Transaction)   {Number}  amount  Tổng số tiền giao dịch.
@apiParam   (Transaction)   {String=VND, USD}  currency_code   Đơn vị tiền tệ.
@apiParam   (Transaction)   {float}   status  Trạng thái giao dịch.
@apiParam   (Transaction)   {Item[]}  [items]  Danh sách item(sản phẩm) trong giao dịch.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id<span class="label label-optional">optional</span></td>
      <td>String</td>
      <td>Định danh item trên hệ thống Mobio</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên của item</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>StringArray</td>
      <td>Danh sách tag được gán cho item</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>Number</td>
      <td>Số lượng item</td>
    </tr>
    <tr>
      <td>price</td>
      <td>Number</td>
      <td>Đơn giá của item (sản phẩm)</td>
    </tr>
    <tr>
      <td>total_amount</td>
      <td>Number</td>
      <td>tổng giá trị của item (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã item (mã sản phẩm)</td>
    </tr>
  </tbody>
</table>
@apiParam   (Transaction)   {Voucher[]}   [vouchers]  Danh sách voucher sử dụng trong lần giao dịch.
@apiParam   (Transaction)   {Object}  [store]   Thông tin cửa hàng thực hiện giao dịch.
@apiParam   (Transaction)   {Object}  [payment]   Thông tin thanh toán.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>type</td>
      <td>String</td>
      <td>Hình thức thanh toán: atm, cash</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card</td>
    </tr>
  </tbody>
</table>

@apiParamExample    {json}      Body example:
{
  "profile": {
    "id": "d071c51e-4cec-43a0-85e1-c0768982787d",
    "phone_number": "84399285833",
    "email": "<EMAIL>",
    "people_id": "*********",
    "tax_code": "xxxxxx",
    "third_party_info": {
      "id": "86a6d10d-7b19-46f0-bdf2-7daef3010680"
    }
  },
  "transaction": {
    "code": "07e8c3db-a3bf-4508-8f79-297a31e2c02e",
    "action_type": "buy",
    "action_time": 1538703463.440775,
    "amount": 100000,
    "currency_code": "vnd",
    "status": 0.0,
    "items": [
      {
        "id": "ea4d37b1-5522-4494-ac21-3a3012b2efb2",
        "name": "",
        "tags": ["", ""],
        "quantity": 2,
        "code": "SP001",
        "price": 100000,
        "total_amount": 200000,
      }
    ],
    "vouchers": [
      {
        "code": "AAIOWNDAFJ"
      }
    ],
    "store": {
      "code": "CH001"
    },
    "payment": {
      "type": "mc",
      "code": "*********",
      "name": ""
    }
  },
  "source": "offline"
}
"""

#########################################################
## Import danh sách giao dịch
## Version 1.0.3
## Version 1.0.1
#########################################################

# Version: 1.0.3
"""
@api {post} /loyalty/transaction/api/v2.0/transactions/list/upsert  Cập nhật danh sách thông tin giao dịch
@apiDescription Dịch vụ để cập nhật danh sách giao dịch.
<li>Nếu profile chưa có trên hệ thống --> tạo mới profile</li>

@apiVersion 1.0.3
@apiGroup Transaction
@apiName ListUpsertTransaction

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiHeader (Headers:) {String} X-Merchant-ID <code>xxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {Array}   transaction_data   Danh sách giao dịch

@apiParam   (transaction_data:)   {Object}  profile   Thông tin profile thực hiện giao dịch. Hệ thống sẽ tự động tạo profile nếu chưa có.
@apiParam   (transaction_data:)   {Object}  transaction   Thông tin giao dịch
@apiParam   (transaction_data:)   {String}  source  Nguồn phát sinh giao dịch. Example: offline, online, affilite
@apiParam   (transaction_data:)   {Number}  [own_business]   Nghiệp vụ xử lý riêng cho từng giao dịch
                                                            <ul>
                                                                <li><code>1</code> : Thông tin profile giao dịch được lấy theo mã voucher redeem</li>
                                                            </ul>


@apiParam   (Profile)   {String}  [id]  Định danh profile trên hệ thống Mobio.
@apiParam   (Profile)   {String}  [phone_number]  Số điện thoại.
@apiParam   (Profile)   {String}  [email]  Địa chỉ thư điện tử.
@apiParam   (Profile)   {String}  [tax_code]  Mã số thuế. Dùng trong trường hợp đại diện công ty mua hàng.
@apiParam   (Profile)   {Object}  [third_party_info]    Thông tin thêm của profile trên hệ thống của tenant.

@apiParam   (Transaction)   {String}  code  Mã giao dịch (mã giao dịch chưa tồn tại trong hệ thống).
@apiParam   (Transaction)   {String= buy, returns}  action_type   Loại hành động giao dịch: mua hàng (buy), Trả hàng (returns)

@apiParam   (Transaction)   {float}   action_time   Thời gian giao dịch.(lấy đến milisecond, VD: 1582104899910)
@apiParam   (Transaction)   {Number}  amount  Tổng số tiền giao dịch.
@apiParam   (Transaction)   {Number}  [discount]  Tổng số tiền được chiết khấu.
@apiParam   (Transaction)   {Number}  [payment_point]  Số điểm được dùng để thanh toán
@apiParam   (Transaction)   {String=VND, USD}  currency_code   Đơn vị tiền tệ.
@apiParam   (Transaction)   {float}   status  Trạng thái giao dịch. VD: <code>1.0 </code>
@apiParam   (Transaction)   {Item[]}  [items]  Danh sách item(sản phẩm) trong giao dịch.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id<span class="label label-optional">optional</span></td>
      <td>String</td>
      <td>Định danh item trên hệ thống Mobio</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên của item</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>StringArray</td>
      <td>Danh sách tag được gán cho item</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>Number</td>
      <td>Số lượng item</td>
    </tr>
    <tr>
      <td>price</td>
      <td>Number</td>
      <td>Đơn giá của item (sản phẩm)</td>
    </tr>
    <tr>
      <td>total_amount</td>
      <td>Number</td>
      <td>tổng giá trị của item (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã item (mã sản phẩm)</td>
    </tr>
    <tr>
        <td>supplier</td>
        <td>object </td>
        <td>Thông tin thương hiệu của sản phẩm </td>
    </tr>
    <tr>
        <td>supplier.code</td>
        <td>string </td>
        <td>Mã thương hiệu </td>
    </tr>
    <tr>
        <td>supplier.name</td>
        <td>string </td>
        <td>Tên thương hiệu</td>
    </tr>
  </tbody>
</table>
@apiParam   (Transaction)   {Object}   [vouchers]  Danh sách voucher sử dụng trong lần giao dịch.
@apiParam   (Transaction)   {String}   vouchers.code  Mã voucher
@apiParam   (Transaction)   {Object}  [store]   Thông tin cửa hàng thực hiện giao dịch.
@apiParam   (Transaction)   {String}  [store.name]   Tên cửa hàng
@apiParam   (Transaction)   {String}  store.code   Mã cửa hàng
@apiParam   (Transaction)   {Object}  [payment]   Thông tin thanh toán.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>type</td>
      <td>String</td>
      <td>Hình thức thanh toán: atm, cash</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card</td>
    </tr>
  </tbody>
</table>

@apiParamExample    {json}      Body example:
{
    "transaction_data": [
        {
          "profile": {
            "id": "d071c51e-4cec-43a0-85e1-c0768982787d",
            "phone_number": "84399285833",
            "email": "<EMAIL>",
            "people_id": "*********",
            "tax_code": "xxxxxx",
            "third_party_info": {
              "id": "86a6d10d-7b19-46f0-bdf2-7daef3010680"
            }
          },
          "transaction": {
            "code": "07e8c3db-a3bf-4508-8f79-297a31e2c02e",
            "action_type": "buy",
            "action_time": 1538703463.440775,
            "amount": 100000,
            "discount": 100000,
            "payment_point": 12,
            "currency_code": "vnd",
            "status": 0.0,
            "items": [
              {
                "id": "ea4d37b1-5522-4494-ac21-3a3012b2efb2",
                "name": "",
                "tags": ["TAG1", "TAG2"],
                "quantity": 2,
                "code": "SP001",
                "price": 100000,
                "total_amount": 200000,
                "supplier":{
                    "code": "BG618T7X",
                    "name": "TOHATO"
                }
              }
            ],
            "vouchers": [
              {
                "code": "AAIOWNDAFJ"
              }
            ],
            "store": {
              "name": "Cửa hàng 01",
              "code": "CH001"
            },
            "payment": {
              "type": "mc",
              "code": "*********",
              "name": ""
            }
          },
          "source": "offline",
          "own_business": 1
        },
        ....
    ]
}    

@apiSuccess (Response Headers:)   {string}  request_id   ID request (Dùng để đối soát dữ liệu).
@apiSuccess {string}    message     Nội dung phản hồi
@apiSuccess {Integer}   code        Mã phản hồi

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}

"""


# Version: 1.0.2
"""
@api {post} /loyalty/transaction/api/v2.0/transactions/list/upsert  Cập nhật danh sách thông tin giao dịch
@apiDescription Dịch vụ để cập nhật danh sách giao dịch.
<li>Nếu profile chưa có trên hệ thống --> tạo mới profile</li>

@apiVersion 1.0.2
@apiGroup Transaction
@apiName ListUpsertTransaction

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiHeader (Headers:) {String} X-Merchant-ID <code>xxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {Array}   transaction_data   Danh sách giao dịch

@apiParam   (transaction_data:)   {Object}  profile   Thông tin profile thực hiện giao dịch. Hệ thống sẽ tự động tạo profile nếu chưa có.
@apiParam   (transaction_data:)   {Object}  transaction   Thông tin giao dịch
@apiParam   (transaction_data:)   {String}  source  Nguồn phát sinh giao dịch. Example: offline, online, affilite
@apiParam   (transaction_data:)   {Number}  [own_business]   Nghiệp vụ xử lý riêng cho từng giao dịch
                                                            <ul>
                                                                <li><code>1</code> : Thông tin profile giao dịch được lấy theo mã voucher redeem</li>
                                                            </ul>


@apiParam   (Profile)   {String}  [id]  Định danh profile trên hệ thống Mobio.
@apiParam   (Profile)   {String}  [phone_number]  Số điện thoại.
@apiParam   (Profile)   {String}  [email]  Địa chỉ thư điện tử.
@apiParam   (Profile)   {String}  [tax_code]  Mã số thuế. Dùng trong trường hợp đại diện công ty mua hàng.
@apiParam   (Profile)   {Object}  [third_party_info]    Thông tin thêm của profile trên hệ thống của tenant.

@apiParam   (Transaction)   {String}  code  Mã giao dịch (mã giao dịch chưa tồn tại trong hệ thống).
@apiParam   (Transaction)   {String= buy, returns}  action_type   Loại hành động giao dịch: mua hàng (buy), Trả hàng (returns)

@apiParam   (Transaction)   {float}   action_time   Thời gian giao dịch.(lấy đến milisecond, VD: 1582104899910)
@apiParam   (Transaction)   {Number}  amount  Tổng số tiền giao dịch.
@apiParam   (Transaction)   {Number}  [payment_point]  Số điểm được dùng để thanh toán
@apiParam   (Transaction)   {String=VND, USD}  currency_code   Đơn vị tiền tệ.
@apiParam   (Transaction)   {float}   status  Trạng thái giao dịch. VD: <code>1.0 </code>
@apiParam   (Transaction)   {Item[]}  [items]  Danh sách item(sản phẩm) trong giao dịch.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id<span class="label label-optional">optional</span></td>
      <td>String</td>
      <td>Định danh item trên hệ thống Mobio</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên của item</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>StringArray</td>
      <td>Danh sách tag được gán cho item</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>Number</td>
      <td>Số lượng item</td>
    </tr>
    <tr>
      <td>price</td>
      <td>Number</td>
      <td>Đơn giá của item (sản phẩm)</td>
    </tr>
    <tr>
      <td>total_amount</td>
      <td>Number</td>
      <td>tổng giá trị của item (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã item (mã sản phẩm)</td>
    </tr>
    <tr>
        <td>supplier</td>
        <td>object </td>
        <td>Thông tin thương hiệu của sản phẩm </td>
    </tr>
    <tr>
        <td>supplier.code</td>
        <td>string </td>
        <td>Mã thương hiệu </td>
    </tr>
    <tr>
        <td>supplier.name</td>
        <td>string </td>
        <td>Tên thương hiệu</td>
    </tr>
  </tbody>
</table>
@apiParam   (Transaction)   {Voucher[]}   [vouchers]  Danh sách voucher sử dụng trong lần giao dịch.
@apiParam   (Transaction)   {Object}  [store]   Thông tin cửa hàng thực hiện giao dịch.
@apiParam   (Transaction)   {String}  [store.name]   Tên cửa hàng
@apiParam   (Transaction)   {String}  store.code   Mã cửa hàng
@apiParam   (Transaction)   {Object}  [payment]   Thông tin thanh toán.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>type</td>
      <td>String</td>
      <td>Hình thức thanh toán: atm, cash</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card</td>
    </tr>
  </tbody>
</table>

@apiParamExample    {json}      Body example:
{
    "transaction_data": [
        {
          "profile": {
            "id": "d071c51e-4cec-43a0-85e1-c0768982787d",
            "phone_number": "84399285833",
            "email": "<EMAIL>",
            "people_id": "*********",
            "tax_code": "xxxxxx",
            "third_party_info": {
              "id": "86a6d10d-7b19-46f0-bdf2-7daef3010680"
            }
          },
          "transaction": {
            "code": "07e8c3db-a3bf-4508-8f79-297a31e2c02e",
            "action_type": "buy",
            "action_time": 1538703463.440775,
            "amount": 100000,
            "payment_point": 12,
            "currency_code": "vnd",
            "status": 0.0,
            "items": [
              {
                "id": "ea4d37b1-5522-4494-ac21-3a3012b2efb2",
                "name": "",
                "tags": ["TAG1", "TAG2"],
                "quantity": 2,
                "code": "SP001",
                "price": 100000,
                "total_amount": 200000,
                "supplier":{
                    "code": "BG618T7X",
                    "name": "TOHATO"
                }
              }
            ],
            "vouchers": [
              {
                "code": "AAIOWNDAFJ"
              }
            ],
            "store": {
              "name": "Cửa hàng 01",
              "code": "CH001"
            },
            "payment": {
              "type": "mc",
              "code": "*********",
              "name": ""
            }
          },
          "source": "offline",
          "own_business": 1
        },
        ....
    ]
}    

@apiSuccess (Response Headers:)   {string}  request_id   ID request (Dùng để đối soát dữ liệu).
@apiSuccess {string}    message     Nội dung phản hồi
@apiSuccess {Integer}   code        Mã phản hồi

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}

"""

#Version: 1.0.1
"""
@api {post} /loyalty/transaction/api/v2.0/transactions/list/upsert  Cập nhật danh sách thông tin giao dịch
@apiDescription Dịch vụ để cập nhật danh sách giao dịch.
<li>Nếu profile chưa có trên hệ thống --> tạo mới profile</li>

@apiVersion 1.0.1
@apiGroup Transaction
@apiName ListUpsertTransaction

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiHeader (Headers:) {String} X-Merchant-ID <code>xxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {Array}   transaction_data   Danh sách giao dịch

@apiParam   (transaction_data:)   {Object}  profile   Thông tin profile thực hiện giao dịch. Hệ thống sẽ tự động tạo profile nếu chưa có.
@apiParam   (transaction_data:)   {Object}  transaction   Thông tin giao dịch
@apiParam   (transaction_data:)   {String}  source  Nguồn phát sinh giao dịch. Example: offline, online, affilite

@apiParam   (Profile)   {String}  [id]  Định danh profile trên hệ thống Mobio.
@apiParam   (Profile)   {String}  [phone_number]  Số điện thoại.
@apiParam   (Profile)   {String}  [email]  Địa chỉ thư điện tử.
@apiParam   (Profile)   {String}  [tax_code]  Mã số thuế. Dùng trong trường hợp đại diện công ty mua hàng.
@apiParam   (Profile)   {Object}  [third_party_info]    Thông tin thêm của profile trên hệ thống của tenant.

@apiParam   (Transaction)   {String}  code  Mã giao dịch (mã giao dịch chưa tồn tại trong hệ thống).
@apiParam   (Transaction)   {String= buy}  action_type   Loại hành động giao dịch: mua hàng (buy),...
                                                        
@apiParam   (Transaction)   {float}   action_time   Thời gian giao dịch.(lấy đến milisecond, VD: 1582104899910)
@apiParam   (Transaction)   {Number}  amount  Tổng số tiền giao dịch.
@apiParam   (Transaction)   {String=VND, USD}  currency_code   Đơn vị tiền tệ.
@apiParam   (Transaction)   {float}   status  Trạng thái giao dịch. VD: <code>1.0 </code>
@apiParam   (Transaction)   {Item[]}  [items]  Danh sách item(sản phẩm) trong giao dịch.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id<span class="label label-optional">optional</span></td>
      <td>String</td>
      <td>Định danh item trên hệ thống Mobio</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên của item</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>StringArray</td>
      <td>Danh sách tag được gán cho item</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>Number</td>
      <td>Số lượng item</td>
    </tr>
    <tr>
      <td>price</td>
      <td>Number</td>
      <td>Đơn giá của item (sản phẩm)</td>
    </tr>
    <tr>
      <td>total_amount</td>
      <td>Number</td>
      <td>tổng giá trị của item (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã item (mã sản phẩm)</td>
    </tr>
    <tr>
        <td>supplier</td>
        <td>object </td>
        <td>Thông tin thương hiệu của sản phẩm </td>
    </tr>
    <tr>
        <td>supplier.code</td>
        <td>string </td>
        <td>Mã thương hiệu </td>
    </tr>
    <tr>
        <td>supplier.name</td>
        <td>string </td>
        <td>Tên thương hiệu</td>
    </tr>
  </tbody>
</table>
@apiParam   (Transaction)   {Voucher[]}   [vouchers]  Danh sách voucher sử dụng trong lần giao dịch.
@apiParam   (Transaction)   {Object}  [store]   Thông tin cửa hàng thực hiện giao dịch.
@apiParam   (Transaction)   {Object}  [payment]   Thông tin thanh toán.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>type</td>
      <td>String</td>
      <td>Hình thức thanh toán: atm, cash</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card</td>
    </tr>
  </tbody>
</table>

@apiParamExample    {json}      Body example:
{
    "transaction_data": [
        {
          "profile": {
            "id": "d071c51e-4cec-43a0-85e1-c0768982787d",
            "phone_number": "84399285833",
            "email": "<EMAIL>",
            "people_id": "*********",
            "tax_code": "xxxxxx",
            "third_party_info": {
              "id": "86a6d10d-7b19-46f0-bdf2-7daef3010680",
            }
          },
          "transaction" {
            "code": "07e8c3db-a3bf-4508-8f79-297a31e2c02e",
            "action_type": "buy",
            "action_time": 1538703463.440775,
            "amount": 100000,
            "currency_code": "vnd",
            "status": 0.0,
            "items": [
              {
                "id": "ea4d37b1-5522-4494-ac21-3a3012b2efb2",
                "name": "",
                "tags": ["TAG1", "TAG2"],
                "quantity": 2,
                "code": "SP001",
                "price": 100000,
                "total_amount": 200000,
                "supplier":{
                    "code": "BG618T7X",
                    "name": "TOHATO"
                }
              }
            ],
            "vouchers": [
              {
                "code": "AAIOWNDAFJ"
              }
            ],
            "store": {
              "code": "CH001"
            },
            "payment": {
              "type": "mc",
              "code": "*********",
              "name": ""
            }
          },
          "source": "offline"
        },
        ....
    ]
}    

"""

# ======== Đồng bộ thông tin giao dịch ========
# Version: 1.0.1
"""
@api {post} /loyalty/transaction/api/v2.1/transactions/synchronized  Đồng bộ danh sách giao dịch
@apiDescription Dịch vụ đồng bộ danh sách giao dịch
<li>Nếu profile chưa có trên hệ thống --> tạo mới profile</li>
<li>Chỉ áp dụng các chính sách loyalty đối với giao dịch có trạng thái hoàn thành</li>
<li>Giao dịch hoàn thành sẽ không thay đổi được trạng thái</li>

@apiVersion 1.0.1
@apiGroup Transaction
@apiName SynchronizedTransactions

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiHeader (Headers:) {String} X-Merchant-ID <code>xxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {Array}   transaction_data   Danh sách giao dịch <code>Tối đa 50 giao dịch </code>

@apiParam   (transaction_data:)   {Object}  profile   Thông tin profile thực hiện giao dịch. Hệ thống sẽ tự động 
                                                        tạo profile nếu chưa có.
                                            <br>
                                             Bắt buộc có 1 trong các thông tin theo mô tả ở dưới: id ,phone_number,
                                              email </code>
                                            
@apiParam   (transaction_data:)   {Object}  transaction   Thông tin giao dịch
@apiParam   (transaction_data:)   {String}  source  Nguồn phát sinh giao dịch. Example: offline, online, affilite

@apiParam   (Profile)   {String}  [id]  Định danh profile trên hệ thống Mobio.
@apiParam   (Profile)   {String}  [phone_number]  Số điện thoại.
@apiParam   (Profile)   {String}  [email]  Địa chỉ thư điện tử.
@apiParam   (Profile)   {String}  [address]  Thông tin địa chỉ (Số nhà, tên đường, khối/xóm, xã/phường, Quận/Huyện, Tỉnh/Thành phố) .
@apiParam   (Profile)   {Object}  [third_party_info] Thông tin thêm của profile trên hệ thống của tenant.
@apiParam   (Profile)   {String}  third_party_info.id  Thông tin định danh của profile trên hệ thống tenant (VD: customer_id)

@apiParam   (Transaction)   {String}  code  Mã giao dịch.
@apiParam   (Transaction)   {String= buy, returns}  action_type  Loại hành động giao dịch
                                                        <ul>
                                                            <li>buy : mua hàng</li>
                                                            <li>returns: trả hàng </li>
                                                        </ul>

@apiParam   (Transaction)   {float}   action_time   Thời gian giao dịch. (Giờ UTC) (Timestamp, ấy đến milisecond, VD: 1582104899910)
@apiParam   (Transaction)   {Number}  amount  Tổng số tiền giao dịch.
@apiParam   (Transaction)   {Number}  [discount]  Tổng số tiền được chiết khấu.
@apiParam   (Transaction)   {Number}  [payment_point]  Số điểm được dùng để thanh toán
@apiParam   (Transaction)   {String=VND, USD}  currency_code   Đơn vị tiền tệ.
@apiParam   (Transaction)   {String}   status  Mã trạng thái giao dịch. (Đối với từng tenant mà danh sách các trạng thái
                                                giao dịch được config khác nhau)
                                                <ul>
                                                Example:
                                                    <li><code>pending </code></li>
                                                    <li><code>Completed </code></li>
                                                    <li><code>Redial </code></li>
                                                    <li><code>Confirm</code></li>
                                                    <li><code>In Progress </code></li>
                                                    <li><code>Cancel </code></li>
                                                </ul>
@apiParam   (Transaction)   {bool}  [is_complete]   Giao dịch hoàn thành sẽ được áp dụng các chính sách loyalty
                                                    <ul>
                                                        <li> true :Giao dịch hoàn thành</li>
                                                    </ul>
                                                    
@apiParam   (Transaction)   {Item[]}  [items]  Danh sách item(sản phẩm) trong giao dịch.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id<span class="label label-optional">optional</span></td>
      <td>String</td>
      <td>Định danh item trên hệ thống Mobio</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên của item</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>StringArray</td>
      <td>Danh sách tag được gán cho item</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>Number</td>
      <td>Số lượng item</td>
    </tr>
    <tr>
      <td>price</td>
      <td>Number</td>
      <td>Đơn giá của item (sản phẩm)</td>
    </tr>
    <tr>
      <td>total_amount</td>
      <td>Number</td>
      <td>tổng giá trị của item (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã item (mã sản phẩm)</td>
    </tr>
    <tr>
        <td>supplier <span class="label label-optional">optional</span></td>
        <td>object </td>
        <td>Thông tin thương hiệu của sản phẩm </td>
    </tr>
    <tr>
        <td>supplier.code</td>
        <td>string </td>
        <td>Mã thương hiệu </td>
    </tr>
    <tr>
        <td>supplier.name</td>
        <td>string </td>
        <td>Tên thương hiệu</td>
    </tr>
  </tbody>
</table>
@apiParam   (Transaction)   {Array}   [vouchers]  Danh sách voucher sử dụng trong lần giao dịch.
@apiParam   (Transaction)   {Object}  [store]   Thông tin cửa hàng thực hiện giao dịch (DS cửa hàng cần được tạo trước trên hệ thống MOBIO).
@apiParam   (Transaction)   {String}  [store.id]   ID cửa hàng trên hệ thống Mobio 
@apiParam   (Transaction)   {String}  store.code   Mã cửa hàng 
@apiParam   (Transaction)   {String}  [store.name]   Tên cửa hàng 
@apiParam   (Transaction)   {Object}  [payment]   Thông tin thanh toán.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>type</td>
      <td>String</td>
      <td>Hình thức thanh toán: atm, cash</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM</td>
    </tr>
    <tr>
      <td>code<span class="label label-optional">optional</span></td>
      <td>String</td>
      <td>Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card</td>
    </tr>
  </tbody>
</table>

@apiParamExample    {json}      Body example:
{
    "transaction_data": [
        {
          "profile": {
            "id": "d071c51e-4cec-43a0-85e1-c0768982787d",
            "phone_number": "84399285833",
            "email": "<EMAIL>",
            "address": "Duy Tân, Cầu Giấy, Hà Nội",
            "third_party_info": {
              "id": "86a6d10d-7b19-46f0-bdf2-7daef3010680"
            }
          },
          "transaction": {
            "code": "07e8c3db-a3bf-4508-8f79-297a31e2c02e",
            "action_type": "buy",
            "action_time": 1538703463.440775,
            "amount": 100000,
            "discount": 10000,
            "currency_code": "VND",
            "status": "pending",
            "items": [
              {
                "id": "ea4d37b1-5522-4494-ac21-3a3012b2efb2",
                "name": "",
                "tags": ["TAG1", "TAG2"],
                "quantity": 2,
                "code": "SP001",
                "price": 100000,
                "total_amount": 200000,
                "supplier":{
                    "code": "BG618T7X",
                    "name": "TOHATO"
                }
              }
            ],
            "vouchers": [
              {
                "code": "AAIOWNDAFJ"
              }
            ],
            "store": {
              "code": "CH001"
            },
            "payment": {
              "type": "mc",
              "code": "*********",
              "name": ""
            }
          },
          "source": "offline"
        },
        ....
    ]
}    

@apiSuccess {string}    message     Nội dung phản hồi
@apiSuccess {Integer}   code        Mã phản hồi

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}

"""
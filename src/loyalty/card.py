#!/usr/bin/python
# -*- coding: utf8 -*-
# **************************************  Kiểm tra hạng thẻ tồn tại  ********************************
# version: 1.0.0                                                                                   *
# ***************************************************************************************************
"""
@api {get} /loyalty/api/v2.0/cards/check-exist  Kiểm tra hạng thẻ tồn tại
@apiDescription Dịch vụ kiểm tra danh sách hạng thẻ có tồn tại không
@apiGroup Card
@apiVersion 1.0.0
@apiName CheckExistCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {Integer} is_exist 				Thuộc tính nhận biết có hạng thẻ nào hay không. Giá trị: 1:True, 0:False

@apiSuccessExample {json} Response example
{
  "data": {
  	"is_exist": 1,
  },
  "code": 200,
  "message": "request thành công"
}

"""
#**************************************  Danh sách hạng thẻ  ***************************************
# version: 1.0.2
# version: 1.0.1                                                                                   *
#***************************************************************************************************

# **************** Version 1.0.2 ***********************
"""
@api {get} /loyalty/api/v2.0/cards/list  Danh sách hạng thẻ
@apiDescription Dịch vụ lấy danh sách hạng thẻ, kèm tìm kiếm, sắp xếp
@apiGroup Card
@apiVersion 1.0.2
@apiName ListCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam    (Query:)     {String} [merchant_id]   tìm kiếm theo merchant_id
@apiParam	   (Query:)			{String}	[search_text]				    Chuỗi tìm kiếm
@apiParam	   (Query:)			{Integer}	[order_by]						Sắp xếp theo thuộc tính nào. Giá trị: 1=card_name, 2=condition, 3=expiry_in, 4=hold_condition
@apiParam	   (Query:)			{Integer}	[order_type]					Kiểu sắp xếp: Giá trị: 1=Tăng dần, 2=Giảm dần
@apiParam    (Query:)     {Integer=1-Visible 0-Hide -1-All} [status=-1]		Trạng thái hiển thị

@apiSuccess {Array}   datas    Danh sách hạng thẻ
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (datas) {String} 	id						    Mã định danh hạng thẻ
@apiSuccess (datas) {String} 	card_name				  Tên hạng thẻ
@apiSuccess (datas) {String} 	avatar					  Ảnh đại diện hạng thẻ
@apiSuccess (datas) {Integer}   condition    			Điều kiện thẻ
@apiSuccess (datas) {Integer}   hold_condition    	    Điều kiện giữ hạng thẻ
@apiSuccess (datas) {Integer}   expiry_in    			Thời hạn sử dụng thẻ. Đơn vị: Tháng
@apiSuccess (datas) {Integer}   generate_type			Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess (datas) {Integer}	approve_type			Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess (datas) {Integer}	status					  Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response list card example
{
  "datas": [{
  	"id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
  	"card_name": "Thẻ bạc",
  	"avatar": "https://avatar.url",
  	"condition": 1000,
  	"expiry_in": 12,
  	"generate_type": 1,
  	"approve_type": 2,
  	"status": 1
  }],
  "code": 200,
  "message": "request thành công"
}

"""

#********************** VERSION 1.0.1 **********************
"""
@api {get} /loyalty/api/v2.0/cards/list  Danh sách hạng thẻ
@apiDescription Dịch vụ lấy danh sách hạng thẻ, kèm tìm kiếm, sắp xếp
@apiGroup Card
@apiVersion 1.0.1
@apiName ListCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam	   (Query:)			{String}	[search_text]								  Chuỗi tìm kiếm
@apiParam	   (Query:)			{Integer}	[order_by]								  Sắp xếp theo thuộc tính nào. Giá trị: 1=card_name, 2=condition, 3=expiry_in
@apiParam	   (Query:)			{Integer}	[order_type]							  Kiểu sắp xếp: Giá trị: 1=Tăng dần, 2=Giảm dần
@apiParam    (Query:)     {Integer=1-Visible 0-Hide -1-All} [status=-1]										Trạng thái hiển thị

@apiSuccess {Array}   datas    Danh sách hạng thẻ
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (datas) {String} 	id						    Mã định danh hạng thẻ
@apiSuccess (datas) {String} 	card_name				  Tên hạng thẻ
@apiSuccess (datas) {String} 	avatar					  Ảnh đại diện hạng thẻ
@apiSuccess (datas) {Integer} condition    			Điều kiện thẻ
@apiSuccess (datas) {Integer} expiry_in    			Thời hạn sử dụng thẻ. Đơn vị: Tháng
@apiSuccess (datas) {Integer} generate_type			Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess (datas) {Integer}	approve_type			Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess (datas) {Integer}	status					  Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response list card example
{
  "datas": [{
  	"id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
  	"card_name": "Thẻ bạc",
  	"avatar": "https://avatar.url",
  	"condition": 1000,
  	"expiry_in": 12,
  	"generate_type": 1,
  	"approve_type": 2,
  	"status": 1
  }],
  "code": 200,
  "message": "request thành công"
}

"""

# ************************** version: 1.0.0 ********************
"""
@api {get} /loyalty/api/v2.0/cards/list  Danh sách hạng thẻ
@apiDescription Dịch vụ lấy danh sách hạng thẻ, kèm tìm kiếm, sắp xếp
@apiGroup Card
@apiVersion 1.0.0
@apiName ListCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam    (Query:)     {String}  [search_text]                 Chuỗi tìm kiếm
@apiParam    (Query:)     {Integer} [order_by]                  Sắp xếp theo thuộc tính nào. Giá trị: 1=card_name, 2=condition, 3=expiry_in
@apiParam    (Query:)     {Integer} [order_type]                Kiểu sắp xếp: Giá trị: 1=Tăng dần, 2=Giảm dần

@apiSuccess {Array}   datas    Danh sách hạng thẻ
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (datas) {String}  id                Mã định danh hạng thẻ
@apiSuccess (datas) {String}  card_name         Tên hạng thẻ
@apiSuccess (datas) {String}  avatar            Ảnh đại diện hạng thẻ
@apiSuccess (datas) {Integer} condition         Điều kiện thẻ
@apiSuccess (datas) {Integer} expiry_in         Thời hạn sử dụng thẻ. Đơn vị: Tháng
@apiSuccess (datas) {Integer} generate_type     Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess (datas) {Integer} approve_type      Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess (datas) {Integer} status            Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response list card example
{
  "datas": [{
    "id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
    "card_name": "Thẻ bạc",
    "avatar": "https://avatar.url",
    "condition": 1000,
    "expiry_in": 12,
    "generate_type": 1,
    "approve_type": 2,
    "status": 1
  }],
  "code": 200,
  "message": "request thành công"
}

"""
# **************************************  Cập nhật trạng thái thẻ  **********************************
# version: 1.0.0                                                                                   *
# ***************************************************************************************************
"""
@api {put} /loyalty/api/v2.0/cards/update-status Cập nhật trạng thái thẻ 
@apiDescription Cập nhật trạng thái thẻ
@apiGroup Card
@apiVersion 1.0.0
@apiName UpdateStatusCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)  {array}   card_ids       	Danh sách các id của card cần cập nhật trạng thái
@apiParam   (Body:)  {Integer} status 			Trạng thái thẻ cần cập nhật. Gía trị: 1=Visible, 0=Hide

@apiParamExample    {json}      Body example:
{
  "card_ids" : ["uuid1","uuid2"],
  "status": 1
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""
#**************************************  Thêm mới hạng thẻ  ****************************************
# version: 1.0.1                                                                                   *
# version: 1.0.0                                                                                   *
#***************************************************************************************************

# ********************* Version 1.0.1 **********************
"""
@api {post} /loyalty/api/v2.0/cards/create Thêm mới hạng thẻ 
@apiDescription Thêm mới hạng thẻ. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiGroup Card
@apiVersion 1.0.1
@apiName CreateCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Form:)     {File}   avatar              File ảnh đại diện hạng thẻ
@apiParam 	   (Form:)		{String}	card_name						Tên hạng thẻ
@apiParam 	   (Form:)		{Integer}	condition						Điều kiện thẻ
@apiParam 	   (Form:)		{Integer}	hold_condition					Điều kiện giữ hạng thẻ
@apiParam 	   (Form:) 		{Integer}	expiry_in						Thời hạn sử dụng thẻ. Đơn vị: Tháng
@apiParam 	   (Form:)		{Integer}	generate_type				Kiểu tạo thẻ
@apiParam 	   (Form:)		{Integer}	approve_type				Kiểu duyệt thẻ
@apiParam 	   (Form:)		{String}	description 				Mô tả
@apiParam 	   (Form:)		{Integer}	status 							Trạng thái hiển thị
@apiParam      (Form:)    {String} [merchant_ids]       Danh sách các merchant được gán với hạng thẻ

@apiParamExample    {form}  Body example:
- avatar: {File}
- card_name: {String} "Thẻ bạc"
- condition: {Integer} 1000
- hold_condition: {Integer} 1000
- expiry_in: {Integer} 12
- generate_type: {Integer} 1
- approve_type: {Integer} 2
- description: {String} "This is description of card"
- status: {Integer} 1
- merchant_ids: {String} "1b7d4e3c-a105-48f1-978b-74527cb5271d,1b7d4e3c-a105-48f1-978b-74527cb5271d"

@apiSuccess  {Object}   data    Thông tin hạng thẻ vừa tạo
@apiSuccess  {String}   message     Nội dung phản hồi
@apiSuccess  {Integer}  code        Mã phản hồi

@apiSuccess  (data)  {String} 	id						Mã định danh hạng thẻ
@apiSuccess  (data)  {String} 	card_name				Tên hạng thẻ
@apiSuccess  (data)  {String} 	avatar					Ảnh đại diện hạng thẻ
@apiSuccess  (data)  {Integer} 	condition    			Điều kiện thẻ
@apiSuccess  (data)  {Integer} 	hold_condition    		Điều kiện giữ hạng thẻ
@apiSuccess  (data)  {Integer} 	expiry_in    			Thời hạn thẻ
@apiSuccess  (data)  {Integer} 	generate_type			Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess  (data)  {Integer}	approve_type			Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess  (data)  {Integer}	status					Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
  	"id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
  	"card_name": "Thẻ bạc",
  	"avatar": "https://avatar.url",
  	"condition": 1000,
  	"hold_condition": 1000,
  	"expiry_in": 12,
  	"generate_type": 1,
  	"approve_type": 2,
  	"status": 1
  }
}

"""

# ********************* Version 1.0.0 **********************
"""
@api {post} /loyalty/api/v2.0/cards/create Thêm mới hạng thẻ 
@apiDescription Thêm mới hạng thẻ. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiGroup Card
@apiVersion 1.0.0
@apiName CreateCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Form:)     {File}   avatar              File ảnh đại diện hạng thẻ
@apiParam 	   (Form:)		{String}	card_name						Tên hạng thẻ
@apiParam 	   (Form:)		{Integer}	condition						Điều kiện thẻ
@apiParam 	   (Form:) 		{Integer}	expiry_in						Thời hạn sử dụng thẻ. Đơn vị: Tháng
@apiParam 	   (Form:)		{Integer}	generate_type				Kiểu tạo thẻ
@apiParam 	   (Form:)		{Integer}	approve_type				Kiểu duyệt thẻ
@apiParam 	   (Form:)		{String}	description 				Mô tả
@apiParam 	   (Form:)		{Integer}	status 							Trạng thái hiển thị
@apiParam      (Form:)    {String} [merchant_ids]       Danh sách các merchant được gán với hạng thẻ

@apiParamExample    {form}  Body example:
- avatar: {File}
- card_name: {String} "Thẻ bạc"
- condition: {Integer} 1000
- expiry_in: {Integer} 12
- generate_type: {Integer} 1
- approve_type: {Integer} 2
- description: {String} "This is description of card"
- status: {Integer} 1
- merchant_ids: {String} "1b7d4e3c-a105-48f1-978b-74527cb5271d,1b7d4e3c-a105-48f1-978b-74527cb5271d"

@apiSuccess  {Object}   data    Thông tin hạng thẻ vừa tạo
@apiSuccess  {String}   message     Nội dung phản hồi
@apiSuccess  {Integer}  code        Mã phản hồi

@apiSuccess  (data)  {String} 	id						Mã định danh hạng thẻ
@apiSuccess  (data)  {String} 	card_name				Tên hạng thẻ
@apiSuccess  (data)  {String} 	avatar					Ảnh đại diện hạng thẻ
@apiSuccess  (data)  {Integer} 	condition    			Điều kiện thẻ
@apiSuccess  (data)  {Integer} 	expiry_in    			Thời hạn thẻ
@apiSuccess  (data)  {Integer} 	generate_type			Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess  (data)  {Integer}	approve_type			Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess  (data)  {Integer}	status					Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
  	"id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
  	"card_name": "Thẻ bạc",
  	"avatar": "https://avatar.url",
  	"condition": 1000,
  	"expiry_in": 12,
  	"generate_type": 1,
  	"approve_type": 2,
  	"status": 1
  }
}

"""
# **************************************  Cập nhật hạng thẻ  ****************************************
# version: 1.0.2                                                                                   *
# version: 1.0.1                                                                                   *
# version: 1.0.0                                                                                   *
# ***************************************************************************************************

# *********************** Version 1.0.2 ***********************
"""
@api {put} /loyalty/api/v2.0/cards/update Cập nhật hạng thẻ 
@apiDescription Cập nhật hạng thẻ. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiGroup Card
@apiVersion 1.0.2
@apiName UpdateCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Form:)     {File}      [avatar]       File ảnh đại diện hạng thẻ
@apiParam      (Form:)		{String} 	id								Mã định danh hạng thẻ
@apiParam 	   (Form:)		{String}	card_name					Tên hạng thẻ
@apiParam 	   (Form:)		{Integer}	condition					Điều kiện thẻ
@apiParam 	   (Form:)		{Integer}	hold_condition				Điều kiện giữ hạng thẻ
@apiParam 	   (Form:) 		{Integer}	expiry_in					Thời hạn sử dụng thẻ. Đơn vị: Tháng
@apiParam 	   (Form:)		{Integer}	generate_type			Kiểu tạo thẻ
@apiParam 	   (Form:)		{Integer}	approve_type			Kiểu duyệt thẻ
@apiParam 	   (Form:)		{String}	description 			Mô tả
@apiParam 	   (Form:)		{Integer}	status 						Trạng thái hiển thị
@apiParam 	   (Form:)		{String}	[delete_avatar] 			Link ảnh avatar cần xóa

@apiParamExample    {form}  Body example:
- avatar: {File}
- id: {String} "1b7d4e3c-a105-48f1-978b-74527cb5271d"
- card_name: {String} "Thẻ bạc"
- condition: {Integer} 1000
- hold_condition: {Integer} 1000
- expiry_in: {Integer} 12
- generate_type: {Integer} 1
- approve_type: {Integer} 2
- description: {String} "This is description of card"
- status: {Integer} 1
- delete_avatar: {String} "https://avatar.url"

@apiSuccess  {Object}   data        Thông tin hạng thẻ vừa cập nhật
@apiSuccess  {String}   message     Nội dung phản hồi
@apiSuccess  {Integer}  code        Mã phản hồi

@apiSuccess  (data)  {String} 	id						Mã định danh hạng thẻ
@apiSuccess  (data)  {String} 	card_name				Tên hạng thẻ
@apiSuccess  (data)  {String} 	avatar					Ảnh đại diện hạng thẻ
@apiSuccess  (data)  {Integer} 	condition    			Điều kiện thẻ
@apiSuccess  (data)  {Integer} 	hold_condition    		Điều kiện giữ hạng thẻ
@apiSuccess  (data)  {Integer} 	expiry_in    			Thời hạn thẻ
@apiSuccess  (data)  {Integer} 	generate_type			Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess  (data)  {Integer}	approve_type			Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess  (data)  {Integer}	status					Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
  	"id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
  	"card_name": "Thẻ bạc",
  	"avatar": "https://avatar.url",
  	"condition": 1000,
  	"hold_condition": 1000,
  	"expiry_in": 12,
  	"generate_type": 1,
  	"approve_type": 2,
  	"status": 1
  }
}

"""

# *********************** Version 1.0.1 ***********************
"""
@api {put} /loyalty/api/v2.0/cards/update Cập nhật hạng thẻ 
@apiDescription Cập nhật hạng thẻ. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiGroup Card
@apiVersion 1.0.1
@apiName UpdateCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Form:)     {File}      [avatar]       File ảnh đại diện hạng thẻ
@apiParam      (Form:)		{String} 	id								Mã định danh hạng thẻ
@apiParam 	   (Form:)		{String}	card_name					Tên hạng thẻ
@apiParam 	   (Form:)		{Integer}	condition					Điều kiện thẻ
@apiParam 	   (Form:)		{Integer}	hold_condition				Điều kiện giữ hạng thẻ
@apiParam 	   (Form:) 		{Integer}	expiry_in					Thời hạn sử dụng thẻ. Đơn vị: Tháng
@apiParam 	   (Form:)		{Integer}	generate_type			Kiểu tạo thẻ
@apiParam 	   (Form:)		{Integer}	approve_type			Kiểu duyệt thẻ
@apiParam 	   (Form:)		{String}	description 			Mô tả
@apiParam 	   (Form:)		{Integer}	status 						Trạng thái hiển thị

@apiParamExample    {form}  Body example:
- avatar: {File}
- id: {String} "1b7d4e3c-a105-48f1-978b-74527cb5271d"
- card_name: {String} "Thẻ bạc"
- condition: {Integer} 1000
- hold_condition: {Integer} 1000
- expiry_in: {Integer} 12
- generate_type: {Integer} 1
- approve_type: {Integer} 2
- description: {String} "This is description of card"
- status: {Integer} 1

@apiSuccess  {Object}   data        Thông tin hạng thẻ vừa cập nhật
@apiSuccess  {String}   message     Nội dung phản hồi
@apiSuccess  {Integer}  code        Mã phản hồi

@apiSuccess  (data)  {String} 	id						Mã định danh hạng thẻ
@apiSuccess  (data)  {String} 	card_name				Tên hạng thẻ
@apiSuccess  (data)  {String} 	avatar					Ảnh đại diện hạng thẻ
@apiSuccess  (data)  {Integer} 	condition    			Điều kiện thẻ
@apiSuccess  (data)  {Integer} 	hold_condition    		Điều kiện giữ hạng thẻ
@apiSuccess  (data)  {Integer} 	expiry_in    			Thời hạn thẻ
@apiSuccess  (data)  {Integer} 	generate_type			Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess  (data)  {Integer}	approve_type			Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess  (data)  {Integer}	status					Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
  	"id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
  	"card_name": "Thẻ bạc",
  	"avatar": "https://avatar.url",
  	"condition": 1000,
  	"hold_condition": 1000,
  	"expiry_in": 12,
  	"generate_type": 1,
  	"approve_type": 2,
  	"status": 1
  }
}

"""

# *********************** Version 1.0.0 ***********************
"""
@api {put} /loyalty/api/v2.0/cards/update Cập nhật hạng thẻ 
@apiDescription Cập nhật hạng thẻ. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiGroup Card
@apiVersion 1.0.0
@apiName UpdateCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Form:)     {File}      [avatar]       File ảnh đại diện hạng thẻ
@apiParam      (Form:)		{String} 	id								Mã định danh hạng thẻ
@apiParam 	   (Form:)		{String}	card_name					Tên hạng thẻ
@apiParam 	   (Form:)		{Integer}	condition					Điều kiện thẻ
@apiParam 	   (Form:) 		{Integer}	expiry_in					Thời hạn sử dụng thẻ. Đơn vị: Tháng
@apiParam 	   (Form:)		{Integer}	generate_type			Kiểu tạo thẻ
@apiParam 	   (Form:)		{Integer}	approve_type			Kiểu duyệt thẻ
@apiParam 	   (Form:)		{String}	description 			Mô tả
@apiParam 	   (Form:)		{Integer}	status 						Trạng thái hiển thị

@apiParamExample    {form}  Body example:
- avatar: {File}
- id: {String} "1b7d4e3c-a105-48f1-978b-74527cb5271d"
- card_name: {String} "Thẻ bạc"
- condition: {Integer} 1000
- expiry_in: {Integer} 12
- generate_type: {Integer} 1
- approve_type: {Integer} 2
- description: {String} "This is description of card"
- status: {Integer} 1

@apiSuccess  {Object}   data        Thông tin hạng thẻ vừa cập nhật
@apiSuccess  {String}   message     Nội dung phản hồi
@apiSuccess  {Integer}  code        Mã phản hồi

@apiSuccess  (data)  {String} 	id						Mã định danh hạng thẻ
@apiSuccess  (data)  {String} 	card_name				Tên hạng thẻ
@apiSuccess  (data)  {String} 	avatar					Ảnh đại diện hạng thẻ
@apiSuccess  (data)  {Integer} 	condition    			Điều kiện thẻ
@apiSuccess  (data)  {Integer} 	expiry_in    			Thời hạn thẻ
@apiSuccess  (data)  {Integer} 	generate_type			Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess  (data)  {Integer}	approve_type			Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess  (data)  {Integer}	status					Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
  	"id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
  	"card_name": "Thẻ bạc",
  	"avatar": "https://avatar.url",
  	"condition": 1000,
  	"expiry_in": 12,
  	"generate_type": 1,
  	"approve_type": 2,
  	"status": 1
  }
}

"""
# **************************************  Lấy thông tin chi tiết hạng thẻ  **************************
# version: 1.0.1                                                                                  *
# version: 1.0.0                                                                                   *
# ***************************************************************************************************

# *************************** Version 1.0.1 ********************************
"""
@api {get} /loyalty/api/v2.0/cards/<card_id>/detail Thông tin chi tiết hạng thẻ
@apiDescription Dịch vụ lấy thông tin chi tiết hạng thẻ
@apiGroup Card
@apiVersion 1.0.1
@apiName DetailCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    card_id                               Mã định danh của hạng thẻ.

@apiSuccess  {Object}   data    Thông tin chi tiết hạng thẻ
@apiSuccess  {String}   message Nội dung phản hồi
@apiSuccess  {Integer}  code    Mã phản hồi

@apiSuccess  (data)  {String} 	id						Mã định danh hạng thẻ
@apiSuccess  (data)  {String} 	card_name				Tên hạng thẻ
@apiSuccess  (data)  {String} 	avatar					Ảnh đại diện hạng thẻ
@apiSuccess  (data)  {Integer} 	condition    			Điều kiện thẻ
@apiSuccess  (data)  {Integer} 	hold_condition    		Điều kiện giữ hạng thẻ
@apiSuccess  (data)  {Integer} 	expiry_in    			Thời hạn thẻ
@apiSuccess  (data)  {Integer} 	generate_type			Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess  (data)  {Integer}	approve_type			Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess  (data)  {String}	description 			Mô tả
@apiSuccess  (data)  {Integer}	status					Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
  	"id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
  	"card_name": "Thẻ bạc",
  	"avatar": "https://avatar.url",
  	"condition": 1000,
  	"expiry_in": 12,
  	"generate_type": 1,
  	"approve_type": 2,
  	"description": "This is description of card",
  	"status": 1
  }
}

"""

# *************************** Version 1.0.0 ********************************
"""
@api {get} /loyalty/api/v2.0/cards/<card_id>/detail Thông tin chi tiết hạng thẻ
@apiDescription Dịch vụ lấy thông tin chi tiết hạng thẻ
@apiGroup Card
@apiVersion 1.0.0
@apiName DetailCard

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    card_id                               Mã định danh của hạng thẻ.

@apiSuccess  {Object}   data    Thông tin chi tiết hạng thẻ
@apiSuccess  {String}   message Nội dung phản hồi
@apiSuccess  {Integer}  code    Mã phản hồi

@apiSuccess  (data)  {String} 	id						Mã định danh hạng thẻ
@apiSuccess  (data)  {String} 	card_name				Tên hạng thẻ
@apiSuccess  (data)  {String} 	avatar					Ảnh đại diện hạng thẻ
@apiSuccess  (data)  {Integer} 	condition    			Điều kiện thẻ
@apiSuccess  (data)  {Integer} 	expiry_in    			Thời hạn thẻ
@apiSuccess  (data)  {Integer} 	generate_type			Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess  (data)  {Integer}	approve_type			Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess  (data)  {String}	description 			Mô tả
@apiSuccess  (data)  {Integer}	status					Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
  	"id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
  	"card_name": "Thẻ bạc",
  	"avatar": "https://avatar.url",
  	"condition": 1000,
  	"expiry_in": 12,
  	"generate_type": 1,
  	"approve_type": 2,
  	"description": "This is description of card",
  	"status": 1
  }
}

"""
# ****************************  Lấy thông tin điều kiện tăng giảm hạng thẻ  *************************
# version: 1.0.0                                                                                   *
# ***************************************************************************************************
"""
@api {get} /loyalty/api/v2.0/card-setting Thông tin điều kiện tăng giảm hạng thẻ
@apiDescription Dịch vụ lấy thông tin điều kiện tăng giảm hạng thẻ
@apiGroup Card
@apiVersion 1.0.0
@apiName GetCardSetting

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {Object}   data    Thông tin chi tiết điều kiện tăng giảm hạng thẻ
@apiSuccess  {String}   message Nội dung phản hồi
@apiSuccess  {Integer}  code    Mã phản hồi

@apiSuccess  (data)  {String} 	id									Mã định danh cấu hình hạng thẻ
@apiSuccess  (data)  {Integer} 	condition_upgrade					Điều kiện tăng hạng thẻ
@apiSuccess  (data)  {Integer} 	card_after_upgrade					Hạng thẻ sau khi tăng
@apiSuccess  (data)  {Integer} 	condition_downgrade    				Điều kiện giảm hạng thẻ
@apiSuccess  (data)  {Integer} 	card_after_downgrade    			Hạng thẻ sau khi giảm
@apiSuccess  (data)  {Integer} 	period								Tần suất sử dụng thẻ của người dùng

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
  	"id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
  	"condition_upgrade": 2,
  	"card_after_upgrade": 2,
  	"condition_downgrade": 1,
  	"card_after_downgrade": 1,
  	"period": 1,
  }
}

"""

# **************************************  Lấy danh sách hạng thẻ theo ids  **************************
# version: 1.0.2                                                                                    *
# version: 1.0.1                                                                                    *
# ***************************************************************************************************

"""
@api {get} /loyalty/api/v2.0/cards/card-id  Lấy danh sách hạng thẻ theo ids
@apiDescription Dịch vụ lấy danh sách hạng thẻ theo ids
@apiGroup Card
@apiVersion 1.0.2
@apiName ListCardByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam	   (Query:)			{String}	 ids			Danh sách card-id cách nhau bởi dấu phẩn. Ví dụ: ,"uuid","uuid",


@apiSuccess {Array}   data    Danh sách thẻ
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (datas) {String} 	id						    Mã định danh hạng thẻ
@apiSuccess (datas) {String} 	card_name				    Tên hạng thẻ
@apiSuccess (datas) {String} 	avatar					    Ảnh đại diện hạng thẻ
@apiSuccess (datas) {Integer}   condition    			    Điều kiện thẻ
@apiSuccess (datas) {Integer}   hold_condition    			Điều kiện giữ hạng thẻ
@apiSuccess (datas) {Integer}   expiry_in    			    Thời hạn sử dụng thẻ. Đơn vị: Tháng
@apiSuccess (datas) {Integer}   generate_type			    Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess (datas) {Integer}	approve_type			    Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess (datas) {Integer}	status					    Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response list card example
{
  "data": [
      {
        "id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
        "card_name": "Thẻ bạc",
        "avatar": "https://avatar.url",
        "condition": 1000,
        "hold_condition": 1000,
        "expiry_in": 12,
        "generate_type": 1,
        "approve_type": 2,
        "status": 1
      }
  ],
  "code": 200,
  "message": "request thành công"
}

"""

# ***************************** Version 1.0.1 *********************

"""
@api {get} /loyalty/api/v2.0/cards/card-id  Lấy danh sách hạng thẻ theo ids
@apiDescription Dịch vụ lấy danh sách hạng thẻ theo ids
@apiGroup Card
@apiVersion 1.0.1
@apiName ListCardByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam	   (Query:)			{String}	 ids			Danh sách card-id cách nhau bởi dấu phẩn. Ví dụ: ,"uuid","uuid",


@apiSuccess {Array}   data    Danh sách thẻ
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (datas) {String} 	id						    Mã định danh hạng thẻ
@apiSuccess (datas) {String} 	card_name				    Tên hạng thẻ
@apiSuccess (datas) {String} 	avatar					    Ảnh đại diện hạng thẻ
@apiSuccess (datas) {Integer}   condition    			    Điều kiện thẻ
@apiSuccess (datas) {Integer}   expiry_in    			    Thời hạn sử dụng thẻ. Đơn vị: Tháng
@apiSuccess (datas) {Integer}   generate_type			    Kiểu mã thẻ. Gía trị: 1=Auto, 2=Store 
@apiSuccess (datas) {Integer}	approve_type			    Kiểu duyệt thẻ. Gía trị: 1=Auto, 2=Manual
@apiSuccess (datas) {Integer}	status					    Trạng thái hiển thị. Gía trị: 1=Visible, 0=Hide

@apiSuccessExample {json} Response list card example
{
  "data": [
      {
        "id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
        "card_name": "Thẻ bạc",
        "avatar": "https://avatar.url",
        "condition": 1000,
        "hold_condition": 1000,
        "expiry_in": 12,
        "generate_type": 1,
        "approve_type": 2,
        "status": 1
      }
  ],
  "code": 200,
  "message": "request thành công"
}

"""

# ****************************** Lấy  Thông tin chính sách tăng giảm hạng thẻ    ********************
# version: 1.0.1                                                                                    *
# ***************************************************************************************************

# Ver 1.0.1

"""
@api {get} /loyalty/api/v2.0/cards/card_policy  Lấy thông tin chính sách tăng giảm hạng thẻ
@apiDescription Lấy thông tin chính sách tăng giảm hạng thẻ theo merchant
@apiGroup Card
@apiVersion 1.0.1
@apiName CardPolicyByMerchant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID      ID của merchant

@apiParam	   (Query:)			{String}	merchant_id    Id merchant, trường hợp không có merchant_id thì sẽ lấy theo 
                                                            X-Merchant-ID ở header   

@apiSuccess {Objects}   data      Thông tin trạng thái thẻ theo merchant
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	merchant_id					ID merchant
@apiSuccess (data) {Number} 	change_type				    Kiểu xét duyệt, thay đổi hạng thẻ
                                                            <ul>
                                                                <li><code>1</code>: Xét và điều chỉnh hàng tháng </li>
                                                                <li><code>2</code>: Xét và điều chỉnh hàng năm </li>
                                                                <li><code>3</code>: Xét và điều chỉnh ngay tại thời điểm thỏa mãn </li>
                                                            </ul>
@apiSuccess (data) {String} 	change_value				Thời gian điều chỉnh
                                                            Example: 
                                                            <ul>
                                                                <li> kiểu xét và điều chỉnh hàng tháng: 1 : (ngày 1 hàng tháng) </li>
                                                                </li> kiểu xét và điều chỉnh hàng năm: 1/1 : (ngày 1 tháng 1 hàng năm) </li>
                                                            </ul>
@apiSuccess (data) {Number} 	after_point					Điểm tiêu dùng sau khi điều chỉnh hạng thẻ
                                                            <ul>
                                                                <li><code>1</code>: Điểm về 0 </li>
                                                                <li><code>2</code>: Giữ nguyên điểm </li>
                                                            </ul>
@apiSuccess (data) {Number} 	after_rank_point			Điểm xét hạng sau khi điều chỉnh hạng thẻ
                                                            <ul>
                                                                <li><code>1</code>: Điểm về 0 </li>
                                                                <li><code>2</code>: Giữ nguyên điểm </li>
                                                            </ul>

@apiSuccessExample {json} Response Card Policy example
{
    "data":{
        "merchant_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
        "change_type": 2,
        "change_value": "30/12",
        "after_point": 0,
        "after_rank_point": 1,
    },
    "code": 200,
    "message": "request thành công"
}
"""

# ****************************** Cập nhật chính sách tăng giảm hạng thẻ   ********************
# version: 1.0.1                                                                                    *
# ***************************************************************************************************

"""
@api {post} /loyalty/api/v2.0/cards/card_policy/upsert  Cập nhật chính sách tăng giảm hạng thẻ
@apiDescription Cập nhật hoặc thêm mới nếu merchant chưa có chính sách tăng giảm hạng thẻ
@apiGroup Card
@apiVersion 1.0.1
@apiName UpsertCardPolicy

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID      ID của merchant

@apiParam	   (Body:)			{String}	merchant_id    Id merchant. Trường hợp không có merchant_id thì sẽ lấy theo 
                                                            X-Merchant-ID ở header   
@apiParam	   (Body:)			{Number}	change_type    Kiểu xét duyệt, thay đổi hạng thẻ
                                                            <ul>
                                                                <li><code>1</code>: Xét và điều chỉnh hàng tháng </li>
                                                                <li><code>2</code>: Xét và điều chỉnh hàng năm </li>
                                                                <li><code>3</code>: Xét và điều chỉnh ngay tại thời điểm thỏa mãn </li>
                                                            </ul>
@apiParam	   (Body:)			{Number}	change_value    Thời gian điều chỉnh
                                                            Example: 
                                                            <ul>
                                                                <li> kiểu xét và điều chỉnh hàng tháng: 1 : (ngày 1 hàng tháng) </li>
                                                                </li> kiểu xét và điều chỉnh hàng năm: 1/1 : (ngày 1 tháng 1 hàng năm) </li>
                                                            </ul>
@apiParam	   (Body:)			{Number}	after_point     Điểm tiêu dùng sau khi điều chỉnh hạng thẻ
                                                            <ul>
                                                                <li><code>1</code>: Điểm về 0 </li>
                                                                <li><code>2</code>: Giữ nguyên điểm </li>
                                                            </ul>      
@apiParam	   (Body:)			{Number}	after_point     Điểm tiêu dùng sau khi điều chỉnh hạng thẻ                                                     
                                

@apiSuccess {Objects}   data      Thông tin trạng thái thẻ theo merchant
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	merchant_id					ID merchant
@apiSuccess (data) {Number} 	change_type				    Kiểu xét duyệt, thay đổi hạng thẻ
                                                            <ul>
                                                                <li><code>1</code>: Xét và điều chỉnh hàng tháng </li>
                                                                <li><code>2</code>: Xét và điều chỉnh hàng năm </li>
                                                                <li><code>3</code>: Xét và điều chỉnh ngay tại thời điểm thỏa mãn </li>
                                                            </ul>
@apiSuccess (data) {String} 	change_value				Thời gian điều chỉnh
                                                            Example: 
                                                            <ul>
                                                                <li> kiểu xét và điều chỉnh hàng tháng: 1 : (ngày 1 hàng tháng) </li>
                                                                </li> kiểu xét và điều chỉnh hàng năm: 1/1 : (ngày 1 tháng 1 hàng năm) </li>
                                                            </ul>
@apiSuccess (data) {Number} 	after_point					Điểm tiêu dùng sau khi điều chỉnh hạng thẻ
                                                            <ul>
                                                                <li><code>1</code>: Điểm về 0 </li>
                                                                <li><code>2</code>: Giữ nguyên điểm </li>
                                                            </ul>
@apiSuccess (data) {Number} 	after_rank_point			Điểm xét hạng sau khi điều chỉnh hạng thẻ
                                                            <ul>
                                                                <li><code>1</code>: Điểm về 0 </li>
                                                                <li><code>2</code>: Giữ nguyên điểm </li>
                                                            </ul>

@apiSuccessExample {json} Response Card Policy example
{
    "data":{
        "merchant_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
        "change_type": 2,
        "change_value": "30/12",
        "after_point": 0,
        "after_rank_point": 1,
    },
    "code": 200,
    "message": "request thành công"
}
"""

# ==================== Chính sách tăng giảm hạng thẻ V2 =============================

# Lấy thông tin config

"""
@api {get} /loyalty/api/v2.1/card-policy  Lấy thông tin chính sách tăng giảm hạng thẻ
@apiDescription Thông tin cấu hình chính sách tăng, giảm, duy trì hạng thẻ
@apiGroup CardPolicy
@apiVersion 1.0.2
@apiName CardPolicyMerchantV2

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String}  X-Merchant-ID      ID của merchant
@apiParam	   (Query:)			{String}	merchant_id     Id merchant, trường hợp không có merchant_id thì sẽ lấy theo 
                                                            X-Merchant-ID ở header   

@apiSuccess {Objects}   data        Thông tin chính sách tăng, giảm, duy trì hạng thẻ
@apiSuccess {String}    message     Mô tả phản hồi
@apiSuccess {Integer}   code        Mã phản hồi

@apiSuccess (data) {String} 	    merchant_id					ID merchant
@apiSuccess (data) {ArrayObject} 	config_card				Danh sách config    
@apiSuccess (config_card) {String} 	policy_type				loại chính sách  
                                                            <ul>
                                                                <li><code>UPGRADE</code>: Tăng hạng </li>
                                                                <li><code>DOWNGRADE</code>: Giảm hạng </li>
                                                            </ul>
@apiSuccess (config_card) {ArrayObject} 	config_time		Danh sách thời gian xét và điều chỉnh
@apiSuccess (config_card) {String} 	config_time.type		Loại thời gian
                                                            <ul>
                                                                <li><code>YEAR</code>:  Năm</li>
                                                                <li><code>MONTH</code>:  Tháng</li>
                                                                <li><code>QUARTER</code>:  Quý</li>
                                                                <li><code>AT_THE_TIME_SATISFY</code>:  Tại thời điểm thỏa mãn</li>
                                                            </ul>
@apiSuccess (config_card) {Object} 	config_time.detail		Chi tiết cấu hình
                                                            <ul>
                                                                <li><code>YEAR:</code>: {
                                                                        "day": 1,
                                                                        "month": 2
                                                                    }</li>
                                                                <li><code>MONTH</code>: {
                                                                    "day": 1
                                                                } </li>
                                                                <li><code>QUARTER</code>: {
                                                                    "day": 1,
                                                                    "the_month": 1 // Tháng thứ mấy của quý
                                                                }</li>
                                                            </ul>
@apiSuccess (config_card) {ArrayObject} 	config_point			Cấu hình điểm sau khi điều chỉnh hạng thẻ
@apiSuccess (config_card) {String} 	config_point.point_type			Hình thức điểm
                                                            <ul>
                                                                <li><code>POINT</code>: Điểm tiêu dùng </li>
                                                                <li><code>RANK_POINT</code>: Điểm xét hạng</li>
                                                            </ul>
@apiSuccess (config_card) {String} 	config_point.after_point       Số điểm sau khi điều chỉnh
                                                            <ul>
                                                                <li><code>ZERO</code>: Điểm chuyển về 0 </li>
                                                                <li><code>KEEP</code>: Điểm tích lũy hợp lệ tại thời điểm điều chỉnh hạng thẻ</li>
                                                            </ul>

@apiSuccessExample {json} Response Card Policy example
{
    "data":{
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "config_card": [
            {
                "policy_type": "UPGRADE", //Tăng hạng
                "config_time": [
                    {
                        "type": "YEAR", // Điều chỉnh hàng năm
                        "detail": {
                            "day": 1,
                            "month": 2
                        }
                    },
                    {
                        "type": "MONTH", // Điều chỉnh hàng tháng
                        "detail": {
                            "day": 1
                        }
                    },
                    {
                        "type": "QUARTER", // Điều chỉnh hàng quý
                        "detail": {
                            "day": 1,
                            "the_month": 1 // Tháng thứ mấy của quý
                        }
                    },
                    {
                        "type": "AT_THE_TIME_SATISFY" // Tại thời điểm thỏa mãn
                    }

                ],
                "config_point": [
                    {
                        "point_type": "POINT",
                        "after_point": "ZERO" // ZERO: Điểm chuyển về 0. KEEP: giữ nguyên điểm
                    },
                    {
                        "point_type": "RANK_POINT",
                        "after_point": "KEEP" // ZERO: Điểm chuyển về 0. KEEP: giữ nguyên điểm
                    }
                ]
            },
            {
                "policy_type": "DOWNGRADE", //Duy trì, Giảm hạng
                "config_time": [
                    {
                        "type": "YEAR", // Điều chỉnh hàng năm
                        "detail": {
                            "day": 1,
                            "month": 2
                        }
                    },
                    {
                        "type": "MONTH", // Điều chỉnh hàng tháng
                        "detail": {
                            "day": 1
                        }
                    },
                    {
                        "type": "QUARTER", // Điều chỉnh hàng quý
                        "detail": {
                            "day": 1,
                            "the_month": 1 // Tháng thứ mấy của quý
                        }
                    },
                    {
                        "type": "AT_THE_TIME_SATISFY" // Tại thời điểm thỏa mãn
                    }

                ],
                "config_point": [
                    {
                        "point_type": "POINT",
                        "after_point": "ZERO" // ZERO: Điểm chuyển về 0. KEEP: giữ nguyên điểm
                    },
                    {
                        "point_type": "RANK_POINT",
                        "after_point": "KEEP" // ZERO: Điểm chuyển về 0. KEEP: giữ nguyên điểm
                    }
                ]
            }
        ]
    },
    "code": 200,
    "message": "request thành công"
}
"""

# Cập nhật chính sách tăng giảm hạng thẻ

"""
@api {post} /loyalty/api/v2.1/card-policy/upsert  Cập nhật chính sách tăng giảm hạng thẻ
@apiDescription Cập nhật hoặc thêm mới nếu merchant chưa có chính sách tăng giảm hạng thẻ
@apiGroup CardPolicy
@apiVersion 1.0.1
@apiName UpsertCardPolicyV2

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID      ID của merchant

@apiParam	   (Body:)			{String}	merchant_id    Id merchant.
                                                 
@apiParam (Body:) {ArrayObject} 	config_card				Danh sách config    
@apiParam (config_card) {String} 	policy_type				loại chính sách  
                                                            <ul>
                                                                <li><code>UPGRADE</code>: Tăng hạng </li>
                                                                <li><code>DOWNGRADE</code>: Giảm hạng </li>
                                                            </ul>
@apiParam (config_card) {ArrayObject} 	config_time		Danh sách thời gian xét và điều chỉnh
@apiParam (config_card) {String} 	config_time.type		Loại thời gian
                                                            <ul>
                                                                <li><code>YEAR</code>:  Năm</li>
                                                                <li><code>MONTH</code>:  Tháng</li>
                                                                <li><code>QUARTER</code>:  Quý</li>
                                                                <li><code>AT_THE_TIME_SATISFY</code>:  Tại thời điểm thỏa mãn</li>
                                                            </ul>
@apiParam (config_card) {Object} 	config_time.detail		Chi tiết cấu hình
                                                            <ul>
                                                                <li><code>YEAR:</code>: {
                                                                        "day": 1,
                                                                        "month": 2
                                                                    }</li>
                                                                <li><code>MONTH</code>: {
                                                                    "day": 1
                                                                } </li>
                                                                <li><code>QUARTER</code>: {
                                                                    "day": 1,
                                                                    "the_month": 1 // Tháng thứ mấy của quý
                                                                }</li>
                                                            </ul>
@apiParam (config_card) {ArrayObject} 	config_point			Cấu hình điểm sau khi điều chỉnh hạng thẻ
@apiParam (config_card) {String} 	config_point.point_type			Hình thức điểm
                                                            <ul>
                                                                <li><code>POINT</code>: Điểm tiêu dùng </li>
                                                                <li><code>RANK_POINT</code>: Điểm xét hạng</li>
                                                            </ul>
@apiParam (config_card) {String} 	config_point.after_point       Số điểm sau khi điều chỉnh
                                                            <ul>
                                                                <li><code>ZERO</code>: Điểm chuyển về 0 </li>
                                                                <li><code>KEEP</code>: Điểm tích lũy hợp lệ tại thời điểm điều chỉnh hạng thẻ</li>
                                                            </ul>

@apiParamExample {json} Body example
    {
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "config_card": [
            {
                "policy_type": "UPGRADE", //Tăng hạng
                "config_time": [
                    {
                        "type": "YEAR", // Điều chỉnh hàng năm
                        "detail": {
                            "day": 1,
                            "month": 2
                        }
                    }
                ],
                "config_point": [
                    {
                        "point_type": "POINT",
                        "after_point": "ZERO" // ZERO: Điểm chuyển về 0. KEEP: giữ nguyên điểm
                    },
                    {
                        "point_type": "RANK_POINT",
                        "after_point": "KEEP" // ZERO: Điểm chuyển về 0. KEEP: giữ nguyên điểm
                    }
                ]
            },
            {
                "policy_type": "DOWNGRADE", //Duy trì, Giảm hạng
                "config_time": [
                    {
                        "type": "QUARTER", // Điều chỉnh hàng quý
                        "detail": {
                            "day": 1,
                            "the_month": 1 // Tháng thứ mấy của quý
                        }
                    }
                ],
                "config_point": [
                    {
                        "point_type": "POINT",
                        "after_point": "ZERO" // ZERO: Điểm chuyển về 0. KEEP: giữ nguyên điểm
                    },
                    {
                        "point_type": "RANK_POINT",
                        "after_point": "KEEP" // ZERO: Điểm chuyển về 0. KEEP: giữ nguyên điểm
                    }
                ]
            }
        ]
    }

@apiSuccess {Objects}   data      Thông tin trạng thái thẻ theo merchant
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	    merchant_id					ID merchant
@apiSuccess (data) {ArrayObject} 	config_card				Danh sách config    
@apiSuccess (config_card) {String} 	policy_type				loại chính sách  
                                                            <ul>
                                                                <li><code>UPGRADE</code>: Tăng hạng </li>
                                                                <li><code>DOWNGRADE</code>: Giảm hạng </li>
                                                            </ul>
@apiSuccess (config_card) {ArrayObject} 	config_time		Danh sách thời gian xét và điều chỉnh
@apiSuccess (config_card) {ArrayObject} 	config_time.type		Loại thời gian
                                                            <ul>
                                                                <li><code>YEAR</code>:  Năm</li>
                                                                <li><code>MONTH</code>:  Tháng</li>
                                                                <li><code>QUARTER</code>:  Quý</li>
                                                                <li><code>AT_THE_TIME_SATISFY</code>:  Tại thời điểm thỏa mãn</li>
                                                            </ul>
@apiSuccess (config_card) {ArrayObject} 	config_time.detail		Chi tiết cấu hình
                                                            <ul>
                                                                <li><code>YEAR:</code>: {
                                                                        "day": 1,
                                                                        "month": 2
                                                                    }</li>
                                                                <li><code>MONTH</code>: {
                                                                    "day": 1
                                                                } </li>
                                                                <li><code>QUARTER</code>: {
                                                                    "day": 1,
                                                                    "the_month": 1 // Tháng thứ mấy của quý
                                                                }</li>
                                                            </ul>
@apiSuccess (config_card) {ArrayObject} 	config_point			Cấu hình điểm sau khi điều chỉnh hạng thẻ
@apiSuccess (config_card) {String} 	config_point.point_type			Hình thức điểm
                                                            <ul>
                                                                <li><code>POINT</code>: Điểm tiêu dùng </li>
                                                                <li><code>RANK_POINT</code>: Điểm xét hạng</li>
                                                            </ul>
@apiSuccess (config_card) {String} 	config_point.after_point       Số điểm sau khi điều chỉnh
                                                            <ul>
                                                                <li><code>ZERO</code>: Điểm chuyển về 0 </li>
                                                                <li><code>KEEP</code>: Điểm tích lũy hợp lệ tại thời điểm điều chỉnh hạng thẻ</li>
                                                            </ul>

@apiSuccessExample {json} Response Card Policy example
{
    "data":{
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "config_card": [
            {
                "policy_type": "UPGRADE", //Tăng hạng
                "config_time": [
                    {
                        "type": "YEAR", // Điều chỉnh hàng năm
                        "detail": {
                            "day": 1,
                            "month": 2
                        }
                    }
                ],
                "config_point": [
                    {
                        "point_type": "POINT",
                        "after_point": "ZERO" // ZERO: Điểm chuyển về 0. KEEP: giữ nguyên điểm
                    },
                    {
                        "point_type": "RANK_POINT",
                        "after_point": "KEEP" // ZERO: Điểm chuyển về 0. KEEP: giữ nguyên điểm
                    }
                ]
            },
            {
                "policy_type": "DOWNGRADE", //Duy trì, Giảm hạng
                "config_time": [
                    {
                        "type": "QUARTER", // Điều chỉnh hàng quý
                        "detail": {
                            "day": 1,
                            "the_month": 1 // Tháng thứ mấy của quý
                        }
                    }
                ],
                "config_point": [
                    {
                        "point_type": "POINT",
                        "after_point": "ZERO" // ZERO: Điểm chuyển về 0. KEEP: giữ nguyên điểm
                    },
                    {
                        "point_type": "RANK_POINT",
                        "after_point": "KEEP" // ZERO: Điểm chuyển về 0. KEEP: giữ nguyên điểm
                    }
                ]
            }
        ]
    },
    "code": 200,
    "message": "request thành công"
}
"""
# Tạo category

"""
@api {POST} /api/v1.0/categories         Tạo category
@apiDescription  API dùng để tạo danh mục mới.
@apiGroup Category
@apiVersion 1.0.0
@apiName CreateCategory

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của danh mục <br>Tên của category là duy nhất.</br>
@apiParamExample {json} Body example
{
  "name": "Th<PERSON> mục"
}

@apiSuccess   {String}  id  Định danh của category.
@apiSuccess   {String}  name  Tên của category.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "id": "64954051efb3a48203e2908f",
    "name": "<PERSON><PERSON><PERSON> mụ<PERSON>",
  }
}
"""


# Sửa category

"""
@api {PATCH} /api/v1.0/categories/<category_id>         Update category
@apiDescription  Cập nhật category.
@apiGroup Category
@apiVersion 1.0.0
@apiName UpdateCategory

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của danh mục <br>Tên của category là duy nhất.
@apiParamExample {json} Body example
{
  "name": "Thư mục 1",
}

@apiSuccess   {String}  id  Định danh của category.
@apiSuccess   {String}  name  Tên của category.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "id": "64954051efb3a48203e2908f",
    "name": "Thư mục 1",
  }
}
"""


# Xoá category

"""
@api {DELETE} /api/v1.0/categories/<category_id> Xoá category
@apiDescription  Xóa thư mục.<br>Chỉ xóa được thư mục trống (không chứa bất kỳ site đang hoạt động nào).
@apiVersion 1.0.0
@apiGroup Category
@apiName DeleteCategory

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}

"""


# Danh sách category

"""
@api {GET} /api/v1.0/categories Danh sách category
@apiDescription  API lấy danh sách category trên hệ thống.
@apiGroup Category
@apiVersion 1.0.0
@apiName ListCategory

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse paging
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)  {String}  [name]  Tìm kiếm theo tên danh mục. Trả về danh sách danh mục có tên chứa từ cần tìm.

@apiSuccess   {String}  id  Định danh của category.
@apiSuccess   {String}  name  Tên của category.
@apiSuccess   {String}  sites_amount  Số lượng site trong danh mục.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "64954051efb3a48203e2908f",
      "name": "Thư mục 1",
    },
    {
      "id": "649165d345a5e0074b074d25",
      "name": "Thư mục 2",
    }
  ],
  ...
}
"""
"""
@apiDefine FormConfigProcess
@apiParam (Body: ) {Object}         content                         Dữ liệu thiết kế của form
@apiParam (Body: ) {String}         edit_version                    Phiên bản chỉnh sửa hiện tại của form, đ<PERSON><PERSON><PERSON> sử dụng để tránh conflict khi 2 users đang cùng tương tác chỉnh sửa.
@apiParam (Body: ) {String}         thumb_img                       Link hình ảnh xem trước của form

@apiParamExample {json} Form Config

{
  "content": {...},
  "edit_version": "xxxxx",
}

@apiSuccess {String} code                   Mã lỗi xảy ra khi xuất bản form
                                            <li><code>200</code>: thành công</li>
                                            <li><code>1001</code>: Form đang cấu hình version mới hơn version hiện tại</li>
                                            <li><code>1002</code>: Form đã được xuất bản trư<PERSON><PERSON> đ<PERSON></li>
                                            <li><code>1003</code>: Form bị xoá</li>

@apiSuccess  {Object} 	    data		                Thông tin cấu hình form
@apiSuccess  {String} 	    data.form_id		        ID form
@apiSuccess  {Object}       data.content            Dữ liệu thiết kế của form
@apiSuccess  {String}       data.edit_version       Phiên bản chỉnh sửa hiện tại của form
@apiSuccess  {Timestamp}    data.create_on          Thời gian cấu hình form
"""

"""
@apiDefine FormConfigResponse

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "content":  {...},
    "edit_version": "1",
    "form_id": "xxx",
    "create_on": 1714713620,
    "thumb_img": "https://...."
  },
  "lang": "vi",
  "message": "request thành công."
}
"""

"""
@apiDefine ConectorConfig

@apiSuccess     {Object} 	          data.connector_config	                              Cấu hình connector khi form đã <code>xuất bản</code>
@apiSuccess     {Int} 	            data.connector_config.connector_id	                  Id định danh connector
@apiSuccess     {String} 	          data.connector_config.submit_url	                    Link submit form
@apiSuccess     {Object}            data.connector_config.param_headers                  Tham số trên header. Khi request url submit thì cần truyền đầy đủ những tham số này lên.
@apiSuccess     {String}            data.connector_config.param_headers.key              Key của tham số
@apiSuccess     {String}            data.connector_config.param_headers.value            Giá trị của tham số
@apiSuccess     {String}            data.connector_config.formula_signature_request      Cách tạo chữ ký khi request submit
"""


"""
@apiDefine FormPublished
@apiSuccess (data) {String} 	      id					                                ID form
@apiSuccess (data) {String} 	      category_id			                            id category
@apiSuccess (data) {String} 	      name				                                Tên form
@apiSuccess (data) {String} 	      merchant_id			                            Mã định danh tenant
@apiSuccess (data) {String} 	      account_create	                            account id người tạo
@apiSuccess (data) {String} 	      account_update	                            account id người cập nhật
@apiSuccess (data) {Timestamp} 	    create_on	                                  Thời gian tạo
@apiSuccess (data) {Timestamp} 	    update_on	                                  Thời gian cập nhật
@apiSuccess (data) {String} 	      status	                                    Trạng thái form, các trạng thái:
                                                                                <li>Nháp (Chưa xuất bản): <code>pending</code></li>
                                                                                <li>Đã xuất bản: <code>published</code></li>
                                                                                <li>Ngừng xất bản: <code>stopped</code></li>
                                                                                <li>Đã xoá: <code>deleted</code></li>
@apiSuccess (data)    {Object} 	          connector_config	                    Cấu hình connector khi form đã <code>xuất bản</code>
@apiSuccess (data)    {Int} 	            connector_config.connector_id	                  Id định danh connector
@apiSuccess (data)    {String} 	          connector_config.submit_url	                    Link submit form
@apiSuccess (data)    {Object}            connector_config.param_headers                  Tham số trên header. Khi request url submit thì cần truyền đầy đủ những tham số này lên.
@apiSuccess (data)    {String}            connector_config.param_headers.key              Key của tham số
@apiSuccess (data)    {String}            connector_config.param_headers.value            Giá trị của tham số
@apiSuccess (data)    {String}            connector_config.formula_signature_request      Cách tạo chữ ký khi request submit
@apiSuccess (data)    {String}            thumb_img      Hình ảnh xem trước form

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [{
        "id": "660a4061883286f2c24a85a3"
        "category_id": "64954051efb3a48203e2908f",
        "name": "form 1",
        "name_search": "form 1",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "account_create": "0f5be325-bd84-420b-9e03-8a7161404233",
        "account_update": "0f5be325-bd84-420b-9e03-8a7161404233",
        "status": "published",
        "create_on": **********,
        "update_on": **********,
        "connector_config": {
          "connector_id": 1,
          "submit_url": "https://ingest-test.mobio.vn/market-place/external/api/v1.0/bulk-data",
          "param_headers": [
                  {
                      "key": "X-Merchant-ID",
                      "value": "3895b19b-f877-11ee-8bfc-9b54d42576d5"
                  },
                  {
                      "key": "Mobio-Connector-Identifier",
                      "value": "47fd810ab392e9435e7d88ea22471e33"
                  },
                  {
                      "key": "Mobio-Access-Token",
                      "value": "544162b05025c2915c330d8b407a7273"
                  },
                  {
                      "key": "Mobio-Connector-AppKey",
                      "value": "QxYysyqo4PyulMq3f2uI"
                  }
          ],
          "formula_signature_request": "SHA256("544162b05025c2915c330d8b407a7273", string(requestBody))"
        },
        "thumb_img": "https://t1.mobio.vn/static/1cb2a489-b34a-41b3-aa7d-f1efc580d687/6617862492bdab939d4e3690.png"
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


# Tạo form

"""
@api {POST} /api/v1.0/forms    Tạo form
@apiDescription Tạo mới form
@apiVersion 1.0.0
@apiGroup Form
@apiName CreateForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Body: ) {String} [category_id]         Id thư mục, mặc định <code>default</code>
@apiParam (Body: ) {String} name               Tên form


@apiParamExample {json} Body example
{
  "category_id": "64954051efb3a48203e2908f",
  "name": "Form 1"
}

@apiSuccess (data) {String} 	id					ID form
@apiSuccess (data) {String} 	merchant_id			Mã định danh tenant
@apiSuccess (data) {String} 	category_id			 Id thư mục 
@apiSuccess (data) {String} 	name			Tên form
@apiSuccess (data) {String} 	status			Trạng thái form <code>default: pending</code>


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":
      {
        "id": "660a4061883286f2c24a85a3",
        "name": "Form"
        "status": "pending"
        "category_id": "64954051efb3a48203e2908f",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298"
      },
    "lang": "vi",
    "message": "request thành công."
}

"""


# Sửa form

"""
@api {PATCH} /api/v1.0/forms/<form_id>  Sửa form
@apiDescription API chỉ hỗ trợ sửa đổi tên form và thư mục lưu trữ form
@apiVersion 1.0.0
@apiGroup Form
@apiName UpdateForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Body: ) {String} [category_id]               Id thư mục 
@apiParam (Body: ) {String} [name]               Tên form

@apiParamExample {json} Body example
{
  "category_id": "64954051efb3a48203e2908f",
  "name": "Form 1"
}

@apiSuccess (data) {String} 	id					Mã định danh form
@apiSuccess (data) {String} 	merchant_id			Mã định danh tenant
@apiSuccess (data) {String} 	category_id			 Id thư mục 
@apiSuccess (data) {String} 	name			Tên form
@apiSuccess (data) {String} 	status			Trạng thái form <code>default: pending</code>


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":
      {
        "id": "660a4061883286f2c24a85a3",
        "name": "Form 1"
        "status": "pending"
        "category_id": "64954051efb3a48203e2908f"
      },
    "lang": "vi",
    "message": "request thành công."
}

"""


# Xoá form

"""
@api {DELETE} /api/v1.0/forms/<form_id>  Xoá form
@apiDescription API xoá form, khi truyền giá trị permanent=1 và form trong thùng rác thì sẽ xoá vình viễn
@apiVersion 1.0.0
@apiGroup Form
@apiName DeleteForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Query: ) {Int}        [permanent]         Xoá vĩnh viễn hay không
                                                     <li><code>1</code>: Xoá vĩnh viễn</li>
                                                     <li><code>0</code>: Di chuyển vào thùng rác</li>
                                                     <li>Mặc định: <code>0</code></li>

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công."
}
"""


# Form detail

"""
@api {GET} /api/v1.0/forms/<form_id>/detail     Thông tin form
@apiDescription Thông tin chi tiết form
@apiVersion 1.0.0
@apiGroup Form
@apiName BasicFormDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccess (data) {String} 	    id					              ID form
@apiSuccess (data) {String} 	    category_id			          id category
@apiSuccess (data) {String} 	    name				              Tên form
@apiSuccess (data) {String} 	    merchant_id			          Mã định danh tenant
@apiSuccess (data) {String} 	    account_create	          account id người tạo
@apiSuccess (data) {String} 	    account_update	          account id người cập nhật
@apiSuccess (data) {Timestamp} 	  create_on	                Thời gian tạo
@apiSuccess (data) {Timestamp} 	  update_on	                Thời gian cập nhật
@apiSuccess (data) {Timestamp} 	  published_time	          Thời gian publish form
@apiSuccess (data) {String} 	    status	                  Trạng thái form
                                                            <li>Nháp: <code>pending</code></li>
                                                            <li>Đã xuất bản: <code>published</code></li>
                                                            <li>Ngừng xất bản<code>stopped</code></li>
@apiSuccess (data) {Bool}         notify_submit             Nhận email khi có lượt điền form mới. <li>Giá trị <code>true/false</code>, default <code>false</code></li>
@apiSuccess (data) {String}       email_address             Địa chỉ email nhận thông báo
@apiSuccess (data) {Bool}         auto_fill                 Tự động điền thông tin  <li>Giá trị <code>true/false</code>, default <code>false</code></li>

@apiUse ConectorConfig

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "id": "660a4061883286f2c24a85a3"
        "category_id": "64954051efb3a48203e2908f",
        "name": "Form 1",
        "name_search": "form 1",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "account_create": "0f5be325-bd84-420b-9e03-8a7161404233",
        "account_update": "0f5be325-bd84-420b-9e03-8a7161404233",
        "published_time": **********,
        "status": "pending",
        "create_on": **********,
        "update_on": **********,
        "notify_submit": true,
        "email_address": "<EMAIL>",
        "auto_fill": true,
        "thumb_img": "https://t1.mobio.vn/static/1cb2a489-b34a-41b3-aa7d-f1efc580d687/6617862492bdab939d4e3690.png",
        "connector_config": {
            "connector_id": 1,
            "submit_url": "https://ingest-test.vn/market-place/external/api/v1.0/bulk-data",
            "param_headers": [
                    {
                        "key": "X-Merchant-ID",
                        "value": "3895b19b-f877-11ee-8bfc-9b54d42576d5"
                    },
                    {
                        "key": "Mobio-Connector-Identifier",
                        "value": "47fd810ab392e9435e7d88ea22471e33"
                    },
                    {
                        "key": "Mobio-Access-Token",
                        "value": "544162b05025c2915c330d8b407a7273"
                    },
                    {
                        "key": "Mobio-Connector-AppKey",
                        "value": "QxYysyqo4PyulMq3f2uI"
                    }
            ],
            "formula_signature_request": "SHA256("544162b05025c2915c330d8b407a7273", string(requestBody))"
          }
        },
    },
    "lang": "vi",
    "message": "request thành công."
}

"""


# Danh sách form

"""
@api {GET} /api/v1.0/forms     Danh sách form
@apiDescription Danh sách form có hỗ trợ tìm kiếm theo trạng thái
@apiVersion 1.0.0
@apiGroup Form
@apiName ListForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse FormPublished
@apiUse paging


@apiParam (Query:) {String}  [name_search]      Tên form cần tìm
@apiParam (Query:) {String}  [category_id]      Mã định danh folder
@apiParam (Query:) {String}  [status]           Trạng thái form, nhận các giá trị:
                                                <li>Nháp(Chưa xuất bản): <code>pending</code></li>
                                                <li>Đã xuất bản: <code>published</code></li>
                                                <li>Ngừng xất bản: <code>stopped</code></li>
                                                <li>Tất cả: <code>all</code></li>
                                                <li>Đã xoá: <code>deleted</code> (Danh sách form trong thùng rác thì truyền status này để lây dữ liệu)</li>
"""


# Vị trí hiển thị form

"""
@api {GET} /api/v1.0/forms/<form_id>/position     Vị trí hiển thị của form
@apiDescription Vị trí hiển thị của form (form được gán ở landing_page/popup...)
@apiVersion 1.0.0
@apiGroup Form
@apiName FormAddress

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccess (data) {String} 	form_id		        ID form
@apiSuccess (data) {String} 	object_type			 Loại đối tượng được gán
@apiSuccess (data) {Array} 	    position	        Danh sách id vị trí hiển thị form


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
      {
        "form_id": "6601390dn58fm5615a211113"
        "object_type": "landing_page",
        "position": ["661f2f2c928fae23c568e55e", "661f706d9ed8255208e08b97"]
      }, ...
    ],
    "lang": "vi",
    "message": "request thành công."
}

"""


# Form config

"""
@api {POST} /api/v1.0/forms/<form_id>/config    Lưu cấu hình form
@apiDescription Lưu cấu hình form sau khi config
@apiVersion 1.0.0
@apiGroup Form
@apiName FormConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiUse FormConfigProcess
@apiUse FormConfigResponse
@apiSuccessExample {json} Error: HTTP/1.1 200 Version invalid
{
  "code": 1001,
  "data": {},
  "lang": "vi",
  "message": "request thành công."
}

"""


# Detail Form config

"""
@api {GET} /api/v1.0/forms/<form_id>/config    Chi tiết cấu hình form
@apiDescription  Chi tiết cấu hình form (Lấy cấu hình mới nhất)
@apiVersion 1.0.0
@apiGroup Form
@apiName FormConfigDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiUse FormConfigProcess
@apiUse FormConfigResponse
"""

# Auto save config

"""
@api {POST} /api/v1.0/forms/<form_id>/config/draft   Lưu nháp form config
@apiDescription Lưu nháp form config
@apiVersion 1.0.0
@apiGroup Form
@apiName AutoSaveFormConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiUse FormConfigProcess
@apiUse FormConfigResponse

"""


# Xuất bản form

"""
@api {POST} /api/v1.0/forms/<form_id>/config/publish  Xuất bản form
@apiDescription Xuất bản form
@apiVersion 1.0.0
@apiGroup Form
@apiName FormPublish

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiUse FormConfigProcess
@apiUse ConectorConfig

@apiParam (Body: ) {Array}         field_config                      Dữ liệu mapping field
@apiParam (Body: ) {String}        field_config.field_key            Key định danh của field
@apiParam (Body: ) {String}        field_config.field_name           Tên field
@apiParam (Body: ) {String}        field_config.field_type           Loại field config
                                                                     <li><code>config</code>: được cấu hình trên form</li>
                                                                     <li><code>base</code>: Field cố định, vd: UTM...</li>
@apiParam (Body: ) {String}        field_config.object               Field thuộc đối tượng nào, vd: profiles, sale
@apiParam (Body: ) {String}        field_config.display_type         Type hiển thị của field
@apiParam (Body: ) {Int}           field_config.field_property       Kiểu dữ liệu của field
@apiParam (Body: ) {String}        field_config.is_personalize       Được phép cá nhân hoá hay không
                                                                     <li><code>1</code> Cá nhân hoá</li>
                                                                     <li><code>2</code> Không cá nhân hoá</li>
                                                                     <li>default: <code>2</code></li>
@apiParam (Body: ) {String}        [field_config.parent_field]       field cha của field_key đang được config (áp dụng cho các field thuộc kiểu dữ liệu array object)
@apiParam (Body: ) {String}        [field_config.original_property]  Kiểu dữ liệu. Áp dụng với những field đặc biệt vd field dạng Array object.
@apiParam (Body: ) {String}        [field_config.format]             Data format khi kiểu dữ liệu là datetime

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "content":  {...},
    "edit_version": "1",
    "form_id": "xxx",
    "create_on": 1714713620
    "connector_config": {
      "connector_id": 1,
      "submit_url": "https://ingest-test.vn/market-place/external/api/v1.0/bulk-data",
      "param_headers": [
              {
                  "key": "X-Merchant-ID",
                  "value": "3895b19b-f877-11ee-8bfc-9b54d42576d5"
              },
              {
                  "key": "Mobio-Connector-Identifier",
                  "value": "47fd810ab392e9435e7d88ea22471e33"
              },
              {
                  "key": "Mobio-Access-Token",
                  "value": "544162b05025c2915c330d8b407a7273"
              },
              {
                  "key": "Mobio-Connector-AppKey",
                  "value": "QxYysyqo4PyulMq3f2uI"
              }
      ],
      "formula_signature_request": "SHA256("544162b05025c2915c330d8b407a7273", string(requestBody))"
    }
  },
  "lang": "vi",
  "message": "request thành công."
}

@apiSuccessExample {json} Error: HTTP/1.1 200 Version invalid
{
  "code": 1001,
  "data": {},
  "lang": "vi",
  "message": "request thành công."
}
"""


# Ngừng xuất bản form

"""
@api {POST} /api/v1.0/forms/<form_id>/config/un-publish  Ngừng Xuất bản form
@apiDescription Ngừng xuất bản form (Chỉ ngừng xuất bản khi đã xoá hết các vị trí mà form được gán)
@apiVersion 1.0.0
@apiGroup Form
@apiName FormUnPublish

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công."
}


"""


# Callback lưu vị trí sử dụng form

"""
@api {POST} /api/v1.0/forms/<form_id>/assignment  Lưu vị trí sử dụng form
@apiDescription Lưu vị trí sử dụng form (Bao gồm toàn bộ các vị trí form được gán trên landing_page/popup...)
@apiVersion 1.0.0
@apiGroup Form
@apiName FormAssign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Body: ) {String} object_id Mã định danh đối tượng sử dụng form <code>landing_page id, popup id ...</code>
@apiParam (Body: ) {String} object_type Loại đối tượng sử dụng form <code>landing_page, popup, workflow...</code>
@apiParam (Body: ) {Array} [position_name] Danh sách vị trí sử dụng form, nếu không truyền position_name hoặc position_name=[] thì xoá tất cả vị trí sử dụng form

@apiParamExample {json} Body example
{
  "object_id": "xxx",
  "object_type": "xxxx",
  "position_name": ["vị trí 1"]
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công."
}
"""


# Danh sách vị trí form được sử dụng

"""
@api {GET} /api/v1.0/forms/<form_id>/assignment  Danh sách vị trí form được sử dụng
@apiDescription Danh sách những đối tượng đang sử dụng form vd: landing_page, popup, ...
@apiVersion 1.0.0
@apiGroup Form
@apiName ListFormAssign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse paging
@apiUse merchant_id_header

@apiSuccess (data) {String} 	  form_id		         Mã định danh form
@apiSuccess (data) {Object}     object_id          Mã định danh đối tượng sử dụng form <code>landing_page id, popup id ...</code>
@apiSuccess (data) {String}     object_type        Loại đối tượng sử dụng form <code>landing_page, popup, workflow...</code>
@apiSuccess (data) {Array}      position_name      Danh sách vị trí gán form

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "data": [
    {
      "form_id": "form_id",
      "object_id": "landingpage_id",
      "object_type": "landing_page",
      "position_name": ["vị trí 1"]
    },
    ...
  ],
  "paging": {
    "page": 1,
    "per_page": 1,
    "total_count": 1,
    "total_page": 1
  }
}
"""


# Danh sách form đã public
"""
@api {POST} /api/v1.0/forms/published/list  Danh sách form đã xuất bản
@apiDescription Danh sách form đã được xuất bản
@apiVersion 1.0.0
@apiGroup Form
@apiName ListFormPublish

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Body: ) {Array} [category_ids] Danh sách category_id cần lấy thông tin form đã publish, nếu không truyền category_id thì lấy tất cả form đã publish

@apiParamExample {json} Body example
{
  "category_ids": ["xxxx"],
}


@apiUse FormPublished

"""


# Đếm tổng số form

"""
@api {POST} /api/v1.0/forms/categories/count Đếm tổng số form trong thư mục theo danh sách category_id
@apiDescription  API Đếm tổng số form trong thư mục theo danh sách category_id
@apiGroup Form
@apiVersion 1.0.0
@apiName CountFormInCategory

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)  {Array}  category_ids  Danh sách id định danh category

@apiSuccess   {String}  id  id định danh của category.
@apiSuccess   {Int}  sites_amount  Số lượng form trong danh mục.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "64954051efb3a48203e2908f",
      "sites_amount": 5,
    }
  ]
}
"""


# Đếm tổng số vị trí được gán form (1 landing_page = 1 lượt gán, không tính có bao nhiêu vị trí sử dụng bên trong)

"""
@api {POST} /api/v1.0/forms/assignment/count Đếm số vị trí đã gán form
@apiDescription  API Đếm số vị trí đã gán form (1 site được gán form chỉ tính 1 lượt, không tính form được sử dụng bao nhiêu lần trong site)
@apiGroup Form
@apiVersion 1.0.0
@apiName CountFormAssign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)  {Array}  form_ids  Danh sách id định danh form

@apiSuccess   {String}  id  id định danh của form.
@apiSuccess   {Int}  total_assign  Tổng số vị trí được gán form.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "64954051efb3a48203e2908e",
      "total_assign": 5,
    }
  ]
}
"""


# Form detail

"""
@api {POST} /api/v1.0/forms/<form_id>/restore     Khôi phục form trong thùng rác
@apiDescription Khôi phục form đã xoá
@apiVersion 1.0.0
@apiGroup Form
@apiName RestoreForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccess (data) {String} 	id					ID form
@apiSuccess (data) {String} 	category_id			 Id thư mục 
@apiSuccess (data) {String} 	name			Tên form
@apiSuccess (data) {String} 	status			Trạng thái form <code>pending</code>


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":
      {
        "id": "660a4061883286f2c24a85a3",
        "name": "Form 2"
        "status": "pending"
        "category_id": "64954051efb3a48203e2908f",
      },
    "lang": "vi",
    "message": "request thành công."
}

"""


# Form detail

"""
@api {POST} /api/v1.0/forms/<form_id>/other-setting     Cài đặt khác trong form
@apiDescription Cài đặt khác trong form (gửi email khi có lượt điền form, tự động điền thông tin...)
@apiVersion 1.0.0
@apiGroup Form
@apiName OtherSettingForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Body: ) {Bool}        [notify_submit]      Nhận email khi có lượt điền form mới. <li>Giá trị <code>true/false</code></li>
@apiParam (Body: ) {String}        [email_address]     Địa chỉ email nhận thông báo <li><code>required khi notify_submit = true</code></li>
@apiParam (Body: ) {Bool}        [auto_fill]      Tự động điền thông tin  <li>Giá trị <code>true/false</code></li>

@apiParamExample {json} Body example
{
  "notify_submit": true,
  "email_address": "<EMAIL>",
  "auto_fill": true
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":
      {
        "form_id": "660a4061883286f2c24a85a3",
        "notify_submit": true,
        "email_address": "<EMAIL>",
        "auto_fill": true,
      },
    "lang": "vi",
    "message": "request thành công."
}

"""


# Danh sách  field config trong form

"""
@api {GET} /api/v1.0/forms/<form_id>/config/mapping-field    Danh sách mapping-field được cấu hình trong form
@apiVersion 1.0.0
@apiGroup Form
@apiName MappingField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Query: ) {Int}        [is_personalize]         Lấy thông tin field cá nhân hoá hay không
                                                          <li><code>1</code>: Lấy thông tin field cá nhân hoá</li>
                                                          <li><code>2</code>: không lấy thông tin cá nhân hoá</li>
                                                          <li>default: <code>2</code></li>


@apiSuccess (data: ) {String}        field_key            Key định danh của field
@apiSuccess (data: ) {String}        field_name           Tên field
@apiSuccess (data: ) {String}        form_id              ID định danh form
@apiSuccess (data: ) {String}        field_type           Loại field config
                                                          <li><code>config</code>: được cấu hình trên form</li>
                                                          <li><code>base</code>: Field cơ sở, vd: utm_source, utm_campaign...</li>
@apiSuccess (data: ) {String}        object               Field thuộc đối tượng nào, vd: profiles, sale
@apiSuccess (data: ) {String}        display_type         Type hiển thị của field
@apiSuccess (data: ) {Int}           field_property       Kiểu dữ liệu của field
@apiSuccess (data: ) {String}        format               Data format khi kiểu dữ liệu là datetime
@apiSuccess (data: ) {Int}            is_personalize      Là field cá nhân hoá hay không <code>1/2</code>
@apiSuccess (data: ) {String}        replace              Field thay thế


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
      {
        "field_name": "E-mail",
        "field_key": "primary_email",
        "field_type": "config",
        "display_type": "single_line_text",
        "form_id": "66b9b9e960dd43122ef421f1",
        "merchant_id": "profiles",
        "field_property": 2,
        "format": null,
        "object": "profiles"
        "replace": "*|primary_email||PROFILES|*",
        "is_personalize": true
      },
    ],
    "message": "request thành công."
}

"""

# Tạo form từ landing_page/copy form

"""
@api {POST} /api/v1.0/forms/auto-generate    Tạo form từ mẫu trên landing_page/sao chép form có sẵn
@apiVersion 1.0.0
@apiGroup Form
@apiName AutoGenerateForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Body: ) {String}         [form_id]                         ID định danh form cần sao chép
@apiParam (Body: ) {String}         category_id                       Id địnf danh thư mục
@apiParam (Body: ) {String}         name                              Tên form
@apiParam (Body: ) {Object}         [content]                         Dữ liệu thiết kế của form
                                                                      <li>Bắt buộc nếu tạo form từ landing_page</li>
                                                                      <li>Nếu là copy và có giá trị <code>form_id</code>, content sẽ lấy từ cấu hình form được copy ở version mới nhất</li>
@apiParam (Body: ) {String}         [thumb_img]                        Ảnh xem trước


@apiSuccess (data: ) {Object}        content                    Dữ liệu thiết kế của form
@apiSuccess (data: ) {String}        name                       Tên form được tạo
@apiSuccess (data: ) {String}        form_id                    ID định danh form
@apiSuccess (data: ) {String}        thumb_img                   Ảnh xem trước

@apiSuccess (data: ) {String}        edit_version               Phiên bản chỉnh sửa
@apiSuccess (data: ) {String}        status                     Trạng thái sau khi được tạo

@apiParamExample {json} Form Config

{
  "form_id": "671f40fbdbccd465d981bc4b",
  "content": {...},
  "name": "form 1",
  "category_id": "66f0f24272ada344add4bb87"
}


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
      "content":  {...},
      "edit_version": "xxxxx",
      "form_id": "new form_id",
      "thumb_img": "https://...."
      "name": "form 1",
      "status": "pending"
    },
    "message": "request thành công."
}

"""
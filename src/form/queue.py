"""
@api {CSM} - [CSM] <PERSON>hi nhận dữ liệu submit form
@apiDescription Validate filter
@apiGroup SubmitForm
@apiVersion 1.0.0
@apiName ReceiveSubmitFormData

@apiParam      (Consumer:)     {topic_name}   topic_name  form-receive-submit-data
@apiParam      (Consumer:)     {topic_group}   topic_group  form-receive-submit-data-group

@apiParam      (Input:)     {String}        request_id                  ID định danh lượt submit
@apiParam      (Input:)     {String}        object_type                 Loại đối tượng
@apiParam      (Input:)     {String}        object_id                   Id định danh đối tượng
@apiParam      (Input:)     {Object}        request_data                D<PERSON> liệu submit của form
@apiParam      (Input:)     {String}        submitted_time              Thời gian submit form, giờ UTC format <code>Định dạng: yyyy-MM-dd HH:mm:ss | ex: 2024-09-19 00:00:00 </code> 
@apiParam      (Input:)     {Int}           connector_id                ID định danh connector
@apiParam      (Input:)     {String}        merchant_id                 Id tenant
@apiParam      (Input:)     {Object}        [device_info]               Thông tin thiết bị truy cập, có thể sử dụng MobioAdminSDK().detect_device_info() để lấy thông tin
@apiParam      (Input:)     {Object}        [device_info.ip_address]    Địa chỉ ip submit form
@apiParam      (Input:)     {Object}        [device_info.device_os]     Thiết bị truy cập


@apiParamExample [json] Input example:
{
    "request_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
    "object_type": "form",
    "object_id": "form_id"
    "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
    "request_data": { // Thông tin dữ liệu submit
        "primary_email": "<EMAIL>",
        "primary_phone": "0987654321",
        "address": {
            "province_code": "Hà Nội",
            "district_code": "Cầu Giấy",
            "ward_code": "Dịch Vọng Hậu",
            "address_personal": "Địa chỉ cụ thể"
        },
    },
    "connector_id": 999,
    "request_time": "2024-08-29 07:03:16.098"
}
"""
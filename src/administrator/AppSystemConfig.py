#!/usr/bin/python
# -*- coding: utf8 -*-

******************************* API Lấy danh sách app system config ******************************
*                                                                                                *
* version: 1.0.0                                                                                 *
**************************************************************************************************
"""
@api {GET} /api/v2.1/app-setting Lấy danh sách app system config.
@apiDescription Lấy danh sách app system config
@apiVersion 1.0.0
@apiGroup AppSystemConfig
@apiName ListAppSystemConfig

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiSuccess {String} vm_type   Môi trường của config <code> 

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "app": "admin",
            "data": [
                "nm_socket_ms_"
            ],
            "descript": "key allow socket event listen",
            "status": 1.0,
            "type": "log_event_socket",
            "type_config": "listen_socket",
            "vm_type": "all",
            "id": "uuid"
        },
        ...
      ],
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* API thêm  app system config **************************************
*                                                                                                *
* version: 1.0.0                                                                                 *
**************************************************************************************************
"""
@api {POST} /api/v2.1/app-setting Thêm app system config.
@apiDescription Thêm app system config
@apiVersion 1.0.0
@apiGroup AppSystemConfig
@apiName AddAppSystemConfig

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiSuccess {String} vm_type   Môi trường của config

@apiParamExample {json} Body example
{
  
  "app": "admin",
  "data": [
      "nm_socket_ms_"
  ],
  "descript": "key allow socket event listen",
  "status": 1.0,
  "type": "log_event_socket",
  "type_config": "listen_socket",
  "vm_type": "all",

}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":{
            "app": "admin",
            "data": [
                "nm_socket_ms_"
            ],
            "descript": "key allow socket event listen",
            "status": 1.0,
            "type": "log_event_socket",
            "type_config": "listen_socket",
            "vm_type": "all",
            "id": "uuid"
        },
    "lang": "vi",
    "message": "request thành công."
}

"""

******************************* API sửa  app system config **************************************
*                                                                                                *
* version: 1.0.0                                                                                 *
**************************************************************************************************
"""
@api {PUT} /api/v2.1/app-setting/<app_setting_id> api sửa app system config.
@apiDescription api sửa app system config
@apiVersion 1.0.0
@apiGroup AppSystemConfig
@apiName UpdateAppSystemConfig

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiSuccess {String} vm_type   Môi trường của config

@apiParamExample {json} Body example
{
  
  "app": "admin",
  "data": [
      "nm_socket_ms_"
  ],
  "descript": "key allow socket event listen",
  "status": 1.0,
  "type": "log_event_socket",
  "type_config": "listen_socket",
  "vm_type": "all",

}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":{
            "app": "admin",
            "data": [
                "nm_socket_ms_"
            ],
            "descript": "key allow socket event listen",
            "status": 1.0,
            "type": "log_event_socket",
            "type_config": "listen_socket",
            "vm_type": "all",
            "id": "uuid"
        },
    "lang": "vi",
    "message": "request thành công."
}

"""
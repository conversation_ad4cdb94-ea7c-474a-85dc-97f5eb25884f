#!/usr/bin/python
# -*- coding: utf8 -*-



# ============================================
# Thêm provider config merchant 
# ============================================

"""
@api {post} /api/v2.1/provider-config thêm provider
@apiDescription Thêm provider config cho merchant 
@apiGroup ProviderConfig
@apiVersion 1.0.0
@apiName ProviderCreate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   merchant_id    Merchant ID
@apiParam   (Body)   {String}   auth_name     Tên truy cập 
@apiParam   (Body)   {String}   auth_pass     Mật khẩu  
@apiParam   (Body)   {String}   [provider_name]     Tên đối tác  
@apiParam   (Body)   {String}   provider_type     Mã đối tác  
@apiParam   (Body)   {String}   auth_attachment     Tên domain/brand name 
@apiParam   (Body)   {String}   [provider_api]     Link api nếu có  
@apiParam   (Body)   {Object}   [others]     Thông tin khác nếu có 
@apiParam   (Body)    {Number}   target     Mục đích của cấu hình, 1: gửi số lượng lớn cho nhiều người(quảng cáo), 2: gửi cho từng cá nhân(Chăm sóc khách hàng), 3: chưa rõ mục đích gửi   
@apiParam   (Body)    {Number}   config_type   Loại cấu hình, 1: sms, 2: email, 3: app mobile
@apiParam   (Body)    {Number}   status   Trạng thái, 1: enable, 2: disable


@apiParamExample {json} Body
{
    "merchant_id": "1d995-9c64-4794-9e44-2b7e243e1111",
    "auth_name": "mobio",
    "auth_pass": "12345",
    "provider_name": "SMTP",
    "provider_type": 206,
    "auth_attachment": "mobio.vn",
    "provider_api": "",
    "others": {
        'host': "smtp.gmail.com",
	    'port': 587,
	    'cert_file':'cert_file',
	    'key_file':'key_file',
	    'not_verify_smtp':true
    },
    "status": 1,
    "target": 1,
    "config_type": 1,
}

@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


# ============================================
# Sửa provider config merchant 
# ============================================

"""
@api {put} /api/v2.1/provider-config Sửa provider
@apiDescription Sửa provider config cho merchant 
@apiGroup ProviderConfig
@apiVersion 1.0.0
@apiName ProviderUpdate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   config_id    Config ID
@apiParam   (Body)   {String}   auth_name     Tên truy cập 
@apiParam   (Body)   {String}   auth_pass     Mật khẩu  
@apiParam   (Body)   {String}   [provider_name]     Tên đối tác  
@apiParam   (Body)   {String}   provider_type     Mã đối tác  
@apiParam   (Body)   {String}   auth_attachment     Tên domain/brand name 
@apiParam   (Body)   {String}   [provider_api]     Link api nếu có  
@apiParam   (Body)   {Object}   [others]     Thông tin khác nếu có 
@apiParam   (Body)    {Number}   target     Mục đích của cấu hình, 1: gửi số lượng lớn cho nhiều người(quảng cáo), 2: gửi cho từng cá nhân(Chăm sóc khách hàng), 3: chưa rõ mục đích gửi   
@apiParam   (Body)    {Number}   config_type   Loại cấu hình, 1: sms, 2: email, 3: app mobile
@apiParam   (Body)    {Number}   status   Trạng thái, 1: enable, 2: disable


@apiParamExample {json} Body
{
    "config_id": "1d995-9c64-4794-9e44-2b7e243e1111",
    "auth_name": "mobio",
    "auth_pass": "12345",
    "provider_name": "SMTP",
    "provider_type": 206,
    "auth_attachment": "mobio.vn",
    "provider_api": "",
    "others": {
        'host': "smtp.gmail.com",
	    'port': 587,
	    'cert_file':'cert_file',
	    'key_file':'key_file',
	    'not_verify_smtp':true
    },
    "status": 1,
    "target": 1,
    "config_type": 1,
}

@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


# ============================================
# Tìm provider theo id
# ============================================

"""
@api {get} /api/v2.1/provider-config Tìm provider theo id
@apiDescription Tìm provider theo id
@apiGroup ProviderConfig
@apiVersion 1.0.0
@apiName ProviderFindOne

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Query:)   {String}   config_id   Config ID


@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "merchant_id": "gkngh1d995-9c64-4794-9e44-2b7e243e1111",
        "config_id": "1d995-9c64-4794-9e44-2b7e243e1111",
        "auth_name": "mobio",
        "auth_pass": "12345",
        "provider_name": "SMTP",
        "provider_type": 206,
        "auth_attachment": "mobio.vn",
        "provider_api": "",
        "others": {
            'host': "smtp.gmail.com",
            'port': 587,
            'cert_file':'cert_file',
            'key_file':'key_file',
            'not_verify_smtp':true
        },
        "status": 1,
        "target": 1,
        "config_type": 1,
    },
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


# ============================================
# danh sách provider theo bộ lọc 
# ============================================

"""
@api {get} /api/v2.1/provider-config/filter Danh sách provider
@apiDescription Danh sách provider theo bộ lọc, không phân trang  
@apiGroup ProviderConfig
@apiVersion 1.0.0
@apiName ProviderFilter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Query:)   {String}   [auth_attachment]   Tên domain/brand name 
@apiParam   (Query:)   {String}   [target]   Mục đích của cấu hình, 1: gửi số lượng lớn cho nhiều người(quảng cáo), 2: gửi cho từng cá nhân(Chăm sóc khách hàng), 3: chưa rõ mục đích gửi   
@apiParam   (Query:)   {String}   [config_type]   Loại cấu hình, 1: sms, 2: email, 3: app mobile
@apiParam   (Query:)   {String}   [provider_type]   Mã số provider VD: "206,207"


@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "merchant_id": "gkngh1d995-9c64-4794-9e44-2b7e243e1111",
            "config_id": "1d995-9c64-4794-9e44-2b7e243e1111",
            "auth_name": "mobio",
            "auth_pass": "12345",
            "provider_name": "SMTP",
            "provider_type": 206,
            "auth_attachment": "mobio.vn",
            "provider_api": "",
            "others": {
                'host': "smtp.gmail.com",
                'port': 587,
                'cert_file':'cert_file',
                'key_file':'key_file',
                'not_verify_smtp':true
            },
            "status": 1,
            "target": 1,
            "config_type": 1,
        }, ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


# ============================================
# tìm kiếm provider cho bên NM
# ============================================

"""
@api {get} /api/v2.1/provider-config/find NM get provider 
@apiDescription Tìm kiếm provider cho module NM
@apiGroup ProviderConfig
@apiVersion 1.0.0
@apiName ProviderFind

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Query:)   {String}   config_id   Config ID
@apiParam   (Query:)   {String}   config_type   Loại lấy cấu hình, 1: lấy cấu hình theo merchant, 2: lấy cấu hình theo tài khoản 


@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "merchant_id": "gkngh1d995-9c64-4794-9e44-2b7e243e1111",
        "config_id": "1d995-9c64-4794-9e44-2b7e243e1111",
        "auth_name": "mobio",
        "auth_pass": "12345",
        "provider_name": "SMTP",
        "provider_type": 206,
        "auth_attachment": "mobio.vn",
        "provider_api": "",
        "others": {
            'host': "smtp.gmail.com",
            'port': 587,
            'cert_file':'cert_file',
            'key_file':'key_file',
            'not_verify_smtp':true
        },
        "status": 1,
        "target": 1,
        "config_type": 1,
    },
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""

# ============================================
# create domain cho merchant
# ============================================

"""
@api {POST} /api/v2.1/merchant-config-url create domain cho merchant
@apiDescription create domain cho merchant
@apiGroup ProviderConfig
@apiVersion 1.0.0
@apiName CreateDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)    {List}   list_domain   List domain merchant dùng

@apiParamExample {json} Body
{
    "list_domain": ["http"]
}
@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

# ============================================
# update domain cho merchant
# ============================================

"""
@api {PUT} /api/v2.1/merchant-config-url/<merchant_id> update domain cho merchant
@apiDescription update domain cho merchant
@apiGroup ProviderConfig
@apiVersion 1.0.0
@apiName UpdateDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)    {List}   list_domain   List domain merchant dùng

@apiParamExample {json} Body
{
    "list_domain": ["http"]
}
@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

# ============================================
# list domain merchant
# ============================================

"""
@api {GET} /api/v2.1/merchant-config-url list domain merchant
@apiDescription list domain merchant
@apiGroup ProviderConfig
@apiVersion 1.0.0
@apiName ListDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "list_domain": [
            "http"
        ],
        "merchant_id": "c9e1fafe-ac06-4fea-ad6e-4bf845acf151"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


# ============================================
# delete domain cho merchant
# ============================================

"""
@api {DELETE} /api/v2.1/merchant-config-url/<merchant_id> delete domain cho merchant
@apiDescription delete domain cho merchant
@apiGroup ProviderConfig
@apiVersion 1.0.0
@apiName DeleteDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""


# ============================================
# danh sách provider select 
# ============================================

"""
@api {get} /api/v2.1/provider-config/select Danh sách provider theo loại  
@apiDescription Danh sách provider theo loại, dùng để chọn cấu hình   
@apiGroup ProviderConfig
@apiVersion 1.0.0
@apiName ProviderSelect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Query:)   {String}   [config_type]   Loại cấu hình, 1: sms, 2: email, 3: app mobile


@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "name": "mobio.vn",
            "type": 2
        },
        {
            "name": "unsub.mobio.vn",
            "type": 2
        }, ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""

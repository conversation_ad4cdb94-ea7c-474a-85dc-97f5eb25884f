#!/usr/bin/python
# -*- coding: utf8 -*-


"""
@api {get} /api/v2.1/config-notify <PERSON><PERSON> s<PERSON>ch cấu hình thông báo  
@apiDescription  c<PERSON> phân trang 
@apiVersion 1.0.0
@apiGroup SettingNotify
@apiName GetListConfigNotify
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header
@apiUse paging
@apiUse order_sort
@apiUse search


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
	{
		"notify_id": "647879d7491cda83ac648412"
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name": "Team Ha Noi",
        "name_search": "team ha noi",
        "config_notify": [
			{
	            "group": "cdp",
	            "config": [
	                {
	                	"config": {
                            "app": "hidden",
                            "browser": "hidden",
                            "email": "disable",
                            "web": "hidden"
                        },
                        "order": 1,
	                    "key": "cdp_result_upload_profile",
	                    "label": "Kết quả xử lý file Profiles được tải lên hệ thống bởi tôi",
	                    "setting": {
	                        "app": "off",
	                        "web": "off",
	                        "email": "on",
	                        "browser": "off",
	                    },
	                    "tooltip": "",
	                }
	            ]
	        },
	        ...
        ],
        "account_created": "uuid",
        "account_updated": "uuid",
        "created_time": "2024-03-28T10:28:49Z",
        "updated_time": "2024-03-28T10:28:49Z",
        "team_ids": ["4bad1402-21f4-11ee-a17f-45311bd761af"],
        "account": [
			{
				"account_id": "c2052960-eb1c-11ee-b488-e9c7a8fbfd6c",
				"team_ids": ["4bad1402-21f4-11ee-a17f-45311bd761af"]
			}
        ],
	}
  ],
  "paging": {
    "page": 1,
    "per_page": 5,
    "total_count": 220,
    "total_page": 44
  }
  
}
"""


"""
@api {post} /api/v2.1/config-notify Tạo cấu hình thông báo  
@apiDescription  thêm mới cấu hình thông báo 
@apiVersion 1.0.0
@apiGroup SettingNotify
@apiName CreateConfigNotify
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header



@apiParam      (Body:)     {String}      name                  Tên cấu hình 
@apiParam      (Body:)     {Array}      config_notify         Thông tin các key thông báo 
@apiParam      (Body:)     {Array} 		[team_ids] 				Danh sách team được chọn     
@apiParam      (Body:)     {Array} 		[account_ids] 				Danh sách account được chọn 
@apiParam      (Body:)     {Bool}      not_alert         		không thông báo cho user: true - không thông báo, false - có thông báo 

@apiParamExample {json} Info example
{
    "name": "Team Ha Noi",
    "config_notify": [
		{
            "group": "cdp",
            "config": {
                "cdp_result_upload_profile": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                },
                "cdp_result_upload_company": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                }
            }
        },
        ...
    ],
    "team_ids": ["4bad1402-21f4-11ee-a17f-45311bd761af"],
    "account_ids": ["c2052960-eb1c-11ee-b488-e9c7a8fbfd6c"],
    "not_alert": false 
 }

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
		"notify_id": "647879d7491cda83ac648412"
	    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	    "name": "Team Ha Noi",
	    "name_search": "team ha noi",
        "config_notify": [
			{
	            "group": "cdp",
	            "config": [
	                {
	                	"config": {
                            "app": "hidden",
                            "browser": "hidden",
                            "email": "disable",
                            "web": "hidden"
                        },
                        "order": 1,
	                    "key": "cdp_result_upload_profile",
	                    "label": "Kết quả xử lý file Profiles được tải lên hệ thống bởi tôi",
	                    "setting": {
	                        "app": "off",
	                        "web": "off",
	                        "email": "on",
	                        "browser": "off",
	                    },
	                    "tooltip": "",
	                }
	            ]
	        },
	        ...
        ],
	    "created_by": "uuid",
        "updated_by": "uuid",
	    "created_time": "2024-03-28T10:28:49Z",
	    "updated_time": "2024-03-28T10:28:49Z",
	    "team_ids": ["4bad1402-21f4-11ee-a17f-45311bd761af"],
	    "account": [
			{
				"account_id": "c2052960-eb1c-11ee-b488-e9c7a8fbfd6c",
				"team_ids": ["4bad1402-21f4-11ee-a17f-45311bd761af"]
			}
        ],
  	}
  
}
"""


"""
@api {put} /api/v2.1/config-notify/<notify_id> Sửa cấu hình thông báo  
@apiDescription  Sửa cấu hình thông báo 
@apiVersion 1.0.0
@apiGroup SettingNotify
@apiName UpdateConfigNotify
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header



@apiParam      (Body:)     {String}      name                  Tên cấu hình 
@apiParam      (Body:)     {Array}      config_notify         Thông tin các key thông báo 
@apiParam      (Body:)     {Array} 		[team_ids] 				Danh sách team được chọn     
@apiParam      (Body:)     {Array} 		[account_ids] 				Danh sách account được chọn 
@apiParam      (Body:)     {Bool}      not_alert         		không thông báo cho user: true - không thông báo, false - có thông báo 

@apiParamExample {json} Info example
{
    "name": "Team Ha Noi",
    "config_notify": [
		{
            "group": "cdp",
            "config": {
                "cdp_result_upload_profile": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                },
                "cdp_result_upload_company": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                }
            }
        },
        ...
    ],
    "team_ids": ["4bad1402-21f4-11ee-a17f-45311bd761af"],
    "account_ids": ["c2052960-eb1c-11ee-b488-e9c7a8fbfd6c"],
    "not_alert": false 
 }

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
		"notify_id": "647879d7491cda83ac648412"
	    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	    "name": "Team Ha Noi",
	    "name_search": "team ha noi",
        "config_notify": [
			{
	            "group": "cdp",
	            "config": [
	                {
	                	"config": {
                            "app": "hidden",
                            "browser": "hidden",
                            "email": "disable",
                            "web": "hidden"
                        },
                        "order": 1,
	                    "key": "cdp_result_upload_profile",
	                    "label": "Kết quả xử lý file Profiles được tải lên hệ thống bởi tôi",
	                    "setting": {
	                        "app": "off",
	                        "web": "off",
	                        "email": "on",
	                        "browser": "off",
	                    },
	                    "tooltip": "",
	                }
	            ]
	        },
	        ...
        ],
	    "created_by": "uuid",
        "updated_by": "uuid",
	    "created_time": "2024-03-28T10:28:49Z",
	    "updated_time": "2024-03-28T10:28:49Z",
	    "team_ids": ["4bad1402-21f4-11ee-a17f-45311bd761af"],
	    "account": [
			{
				"account_id": "c2052960-eb1c-11ee-b488-e9c7a8fbfd6c",
				"team_ids": ["4bad1402-21f4-11ee-a17f-45311bd761af"]
			}
        ],
  	}
  
}
"""


"""
@api {get} /api/v2.1/config-notify/<notify_id> Chi tiết cấu hình thông báo  
@apiDescription  Chi tiết cấu hình thông báo 
@apiVersion 1.0.0
@apiGroup SettingNotify
@apiName DetailConfigNotify
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
		"notify_id": "647879d7491cda83ac648412"
	    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	    "name": "Team Ha Noi",
	    "name_search": "team ha noi",
        "config_notify": [
			{
	            "group": "cdp",
	            "config": [
	                {
	                	"config": {
                            "app": "hidden",
                            "browser": "hidden",
                            "email": "disable",
                            "web": "hidden"
                        },
                        "order": 1,
	                    "key": "cdp_result_upload_profile",
	                    "label": "Kết quả xử lý file Profiles được tải lên hệ thống bởi tôi",
	                    "setting": {
	                        "app": "off",
	                        "web": "off",
	                        "email": "on",
	                        "browser": "off",
	                    },
	                    "tooltip": "",
	                }
	            ]
	        },
	        ...
        ],
	    "created_by": "uuid",
        "updated_by": "uuid",
	    "created_time": "2024-03-28T10:28:49Z",
	    "updated_time": "2024-03-28T10:28:49Z",
	    "team_ids": ["4bad1402-21f4-11ee-a17f-45311bd761af"],
	    "account": [
			{
				"account_id": "c2052960-eb1c-11ee-b488-e9c7a8fbfd6c",
				"team_ids": ["4bad1402-21f4-11ee-a17f-45311bd761af"]
			}
        ],
  	}
  
}
"""


"""
@api {delete} /api/v2.1/config-notify/<notify_id> Xóa cấu hình thông báo  
@apiDescription  Xóa cấu hình thông báo 
@apiVersion 1.0.0
@apiGroup SettingNotify
@apiName DeleteConfigNotify
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  
}
"""


"""
@api {get} /api/v2.1/config-notify/team-account lấy team và account đã cấu hình   
@apiDescription  lấy tất cả dữ liệu đã cấu hình 
@apiVersion 1.0.0
@apiGroup SettingNotify
@apiName TeamAccountHasConfig 
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
	"code": 200,
	"message": "request thành công.",
	"data": {
		"team_ids": ["647879d7491cda83ac648412"],
	    "account_ids": ["647879d7491cda83ac648412"],
	}
  
}
"""



"""
@api {get} /api/v2.1/config-notify/base mẫu cấu hình thông báo 
@apiDescription  sử dụng khi tạo mới cấu hình thông báo toàn cục 
@apiVersion 1.0.0
@apiGroup SettingNotify
@apiName BaseConfigNotify
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
        "config_notify": [
			{
	            "group": "cdp",
	            "label": "cdp",
	            "config": [
	                {
	                	"config": {
                            "app": "hidden",
                            "browser": "hidden",
                            "email": "disable",
                            "web": "hidden"
                        },
                        "order": 1,
	                    "key": "cdp_result_upload_profile",
	                    "label": "Kết quả xử lý file Profiles được tải lên hệ thống bởi tôi",
	                    "setting": {
	                        "app": "off",
	                        "web": "off",
	                        "email": "on",
	                        "browser": "off",
	                    },
	                    "tooltip": "",
	                }
	            ]
	        },
	        ...
        ],
  	}
  
}
"""


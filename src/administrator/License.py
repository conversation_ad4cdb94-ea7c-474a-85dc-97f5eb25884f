#!/usr/bin/python
# -*- coding: utf8 -*-

"""
@api {get} /license Get license
@apiDescription Lấy thông tin license.
@apiGroup License
@apiVersion 1.0.0
@apiName GetLicense

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccess   {String}  merchant_id   Định danh tenant.
@apiSuccess   {String}  package_code  Mã gói, tenant đang sử dụng.
@apiSuccess   {String}  expire_time   Thời điểm hết hạn sử dụng của tenant.
@apiSuccess   {Number}  max_profile_number  Số lượng profile tối đa.
@apiSuccess   {Number}  current_profile_number  Số lượng profile hiện tại.
@apiSuccess   {Number}  max_email_sent_number   Số lượng email gửi tối đa hàng tháng.
@apiSuccess   {Number}  current_email_sent_number S<PERSON> lượng email đã gửi trong tháng hiện tại.
@apiSuccess   {Number}  max_user_number   Số lượng user.tối đa đư<PERSON> sử dụng.
@apiSuccess   {Number}  current_user_number   Số lượng user hiện tại.
@apiSuccess   {Number}  max_page_social_number  Số lượng tối đa page mạng xã hội được tích hợp vào hệ thống.
@apiSuccess   {Number}  current_page_social_number  Số lượng page mạng xã hội đang tích hợp.

@apiSuccessExample 	{json}	Response: HTTP/1.1 200 OK
{
  "merchant_id": "2b4ecc9d-a89f-4d20-b6a8-dbb143193074",
  "package_code": "sme_enterprise_package",
  "expire_time": "2019-12-31T12:00:00Z",
  "max_profile_number": 1050000,
  "current_profile_number": 525000,
  "max_email_sent_number": 3180000,
  "current_email_sent_number": 90000,
  "max_user_number": 16,
  "current_user_number": 5,
  "max_page_social_number": 10,
  "current_page_social_number": 5
}
"""

#------------------------ Danh sách trạng thái gói --------------------------------------
"""
@api {get} /{host}/adm/api/v2.1/license/status-merchant Danh sách trạng thái doanh nghiệp
@apiDescription Danh sách trạng thái doanh nghiệp.
@apiGroup License
@apiVersion 1.0.0
@apiName GetListStatusMerchant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccessExample 	{json}	Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "key": "active",
            "status": "Đang hoạt động"
        },
        {
            "key": "about_expired",
            "status": "Sắp hết hạn"
        },
        {
            "key": "out_of_service",
            "status": "Ngưng hoạt động"
        },
        {
            "key": "not_registered",
            "status": "Chưa đăng ký dịch vụ"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
# ----------------------------------- Danh sách chức năng ----------------------------------
"""
@api {get} /{host}/adm/api/v2.1/license/functions Danh sách chức năng
@apiDescription Danh sách chức năng.
@apiGroup License
@apiVersion 1.0.0
@apiName GetListFunction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccessExample 	{json}	Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "key": "cdp",
            "name": "Activation CDP"
        },
        {
            "key": "sales",
            "name": "Operation CDP - Sales Management"
        },
        {
            "key": "services",
            "name": "Operation CDP - Service Management"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

#---------------------------  Danh sách loại gói -----------------------------
"""
@api {get} /{host}/adm/api/v2.1/license/category-packages Danh sách loại gói 
@apiDescription Danh sách loại gói.
@apiGroup License
@apiVersion 1.0.0
@apiName GetListCategoryPackages

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccessExample 	{json}	Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "key": "free",
            "name": "Free"
        },
        {
            "key": "growth",
            "name": "Growth"
        },
        {
            "key": "professional",
            "name": "Professional"
        },
        {
            "key": "enterprise",
            "name": "Enterprise"
        },
        {
            "key": "enterprise_plus",
            "name": "Enterprise Plus"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

#------------------------------- Danh sách lịch sử giao dịch ----------------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/license/invoices Danh sách lịch sử giao dịch
@apiDescription Danh sachs lịch sử giao dịch
@apiGroup License
@apiVersion 1.0.0
@apiName GetListInvoice
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam   (Query:)   {String}   action_type       Kiểu giao dịch nhiều giá trị cách nhau bởi dầu "," [new_module, upgrade_package, renew_license, monthly_check, add_attribute]
@apiParam   (Query:)   {String}   since             20231212010101 Ngày bắt đầu
@apiParam   (Query:)   {String}   until              20241212010101 Ngày kết thúc

@apiSuccess {String}             action_type                        Kiểu giao dịch
@apiSuccess {String}             active_time                        Thời gian gói bắt đầu hoạt động: Định dạng "%Y-%m-%d %H:%M:%S" theo múi giờ UTC
@apiSuccess {String}             create_on                          Thời gian tạo giao dịch Định dạng "%Y-%m-%d %H:%M:%S" theo múi giờ UTC
@apiSuccess {String}             expire_time                        Thời gian hết hạn Định dạng "%Y-%m-%d %H:%M:%S" theo múi giờ UTC
@apiSuccess {Object}             packages                    Danh sách các gói
@apiSuccess {String}             packages.module             Tên module
@apiSuccess {String}             packages.number_buy         Chỉ số đã mua của gói
@apiSuccess {String}             packages.package_code       Loại gói
@apiSuccess {Object}             packages.total_price       Giá tiền tại thời điểm mua
@apiSuccess {String}             payment_amount                     Giá trị cần thanh toán
@apiSuccess {String}             remaining_amount                   Số dư còn lại sau khi thanh toán
@apiSuccess {String}             start_time                         Thời điểm bắt đầu áo dụng Định dạng "%Y-%m-%d %H:%M:%S" theo múi giờ UTC
@apiSuccess {String}             total_amount                       Phí sử dụng gói
@apiSuccess {String}             start_time_calculate                        Thời gian bắt đầu tính phí Định dạng "%Y-%m-%d %H:%M:%S" theo múi giờ UTC
@apiSuccess {String}             end_time_calculate                        Thời gian kết thúc tính phí Định dạng "%Y-%m-%d %H:%M:%S" theo múi giờ UTC



@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "action_type": "merchant_register",
            "active_time": "2023-06-08 17:00:00",
            "create_on": "2023-10-12 09:27:40",
            "currency_unit": "vnd",
            "expire_time": "2024-06-08 17:00:00",
            "start_time": "2023-06-08 17:00:00",
            "packages": [
                {
                    "module": "cdp",
                    "number_buy": 300000,
                    "package_code": "professional",
                    "start_time_calculate": "2023-06-08 17:00:00",
                    "end_time_calculate": "2024-06-08 17:00:00",
                    "total_price": {
                        "usd": 1200,
                        "vnd": 30000000
                    }
                },
                {
                    "module": "sales",
                    "number_buy": 40,
                    "package_code": "professional",
                    "start_time_calculate": "2023-06-08 17:00:00",
                    "end_time_calculate": "2024-06-08 17:00:00",
                    "total_price": {
                        "usd": 9600.0,
                        "vnd": 240000000.0
                    }
                },
                {
                    "module": "services",
                    "number_buy": 30,
                    "package_code": "professional",
                    "start_time_calculate": "2023-06-08 17:00:00",
                    "end_time_calculate": "2024-06-08 17:00:00",
                    "total_price": {
                        "usd": 7200.0,
                        "vnd": 180000000.0
                    }
                }
            ],
            "payment_amount": 0,
            "remaining_amount": 0,
            "total_amount": {
                "usd": 18000.0,
                "vnd": 450000000.0
            }
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 1,
        "total_count": 4,
        "total_page": 4
    }
}

"""

#--------------------------- Danh sách kiểu giao dịch --------------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/invoices/transaction-type Danh sách kiểu giao dịch
@apiDescription Danh sách kiểu giao dịch
@apiGroup License
@apiVersion 1.0.0
@apiName GetListInvoicesTransactionType
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang



@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "key": "new_module",
            "name": "Mua mới"
        },
        {
            "key": "upgrade_package",
            "name": "Nâng cấp gói"
        },
        {
            "key": "renew_license",
            "name": "Gia hạn"
        },
        {
            "key": "monthly_check",
            "name": "Phí hàng tháng"
        },
        {
            "key": "add_attribute",
            "name": "Mua thêm chỉ số"
        },
        {
            "key" : "trial",
            "name": "Dùng thử"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}

"""

# ---------------------------------- GET UNIT BY LANG -----------------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/license/unit-function Danh sách đơn vị tính theo từng chức năng
@apiDescription Danh sách đơn vị tính theo từng chức năng
@apiGroup License
@apiVersion 1.0.0
@apiName GetLisUnitByLang
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "module": "cdp",
            "unit": "MTPs"
        },
        {
            "module": "sales",
            "unit": "User"
        },
        {
            "module": "services",
            "unit": "User"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
# ---------------------------------- GET DETAIL MERCHANT ------------------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/license/merchants/detail Thông tin doanh nghiệp
@apiDescription Thông tin doanh nghiệp
@apiGroup License
@apiVersion 1.0.0
@apiName GetMerchantDetail
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "currency_unit": "vnd",
        "expire_time": "2024-11-12 17:00:00",
        "merchant_code": "HDB",
        "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
        "merchant_info": {
            "customer_name": "mobio",
            "email": "<EMAIL>",
            "merchant_code": "HDB",
            "name": "hdbank",
            "phone": "**********"
        },
        "merchant_type": "BANK",
        "remaining_amount": **********.7311828,
        "start_time": "2023-11-12 17:00:00",
        "status": "active",
        "status_delete": "not_allow",
        "update_on": "2023-11-16 08:39:42"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------- Báo cáo tổng số message send trong ngày ------------------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/license/report/count-message-send-by-day Báo cáo tổng số message trong ngày 
@apiDescription Báo cáo tổng số message gửi trong ngày 
@apiGroup License
@apiVersion 1.0.0
@apiName ReportCountMessageSendByDay
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}   start_time              	Format "%Y-%m-%dT%H:%M:%S" Ngày bắt đầu 
@apiParam   (Body:)   {String}   end_time                  Format "%Y-%m-%dT%H:%M:%S" Ngày kết thúc 


@apiSuccess {ArrayObject}        data                           Data
@apiSuccess {String}             data.total                     Tổng số lượng message
@apiSuccess {String}             data.day                       Ngày đếm message
@apiSuccess {Integer}            total                          Tống các ngày

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{

  "code": 200, 
  "data": [
    {
      "day": "2023-02-26""
      "total": 20,
      "total_exchange": 12
    }
  ],
  "start_time": "2024-01-01T00:00:00",
   "end_time": "2024-02-01T00:00:00"
  "total": 20
  "lang": "vi", 
  "message": "request thành công."
}

"""

# ---------------------------------- Tổng số messaage đã send trong chu kỳ ------------------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/license/report/count-message-send    Tổng số message đã send trong chu kỳ
@apiDescription Report count message send
@apiGroup License
@apiVersion 1.0.0
@apiName ReportCountMessageSend
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}   start_time              	Format "%Y-%m-%dT%H:%M:%S" Ngày bắt đầu 
@apiParam   (Body:)   {String}   end_time                  Format "%Y-%m-%dT%H:%M:%S" Ngày kết thúc 


@apiSuccess {Object}        data                           Data
@apiSuccess {String}             data.total                     Tổng số lượng message


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "total": 20,
        "start_time": "2024-01-01T00:00:00",
        "end_time": "2024-02-01T00:00:00",
        "total_exchange": 12
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------------------- Báo cáo tổng số MTP send trong ngày ------------------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/license/report/count-mtp-send-by-day Báo cáo tổng số MTP trong ngày 
@apiDescription Báo cáo tổng số MTP gửi trong ngày 
@apiGroup License
@apiVersion 1.0.0
@apiName ReportCountMTPSendByDay
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}   start_time              	Format "%Y-%m-%dT%H:%M:%S" Ngày bắt đầu 
@apiParam   (Body:)   {String}   end_time                  Format "%Y-%m-%dT%H:%M:%S" Ngày kết thúc 


@apiSuccess {ArrayObject}        data                           Data
@apiSuccess {String}             data.total                     Tổng số lượng MTP
@apiSuccess {String}             data.day                       Ngày đếm MTP
@apiSuccess {Integer}            total                          Tống các ngày

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{

  "code": 200, 
  "data": [
    {
      "day": "2023-02-26""
      "total": 20
    }
  ],
  "start_time": "2024-01-01T00:00:00",
    "end_time": "2024-02-01T00:00:00"
  "total": 20
  "lang": "vi", 
  "message": "request thành công."
}

"""

# ---------------------------------- Tổng số MTP đã send trong chu kỳ ------------------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/license/report/count-mtp    Tổng số MTP đã send trong chu kỳ
@apiDescription Report count MTP send
@apiGroup License
@apiVersion 1.0.0
@apiName ReportCountMTP
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}   start_time              	Format "%Y-%m-%dT%H:%M:%S" Ngày bắt đầu 
@apiParam   (Body:)   {String}   end_time                  Format "%Y-%m-%dT%H:%M:%S" Ngày kết thúc 


@apiSuccess {Object}        data                           Data
@apiSuccess {String}             data.total                     Tổng số lượng MTP


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "total": 20,
        "start_time": "2024-01-01T00:00:00",
        "end_time": "2024-02-01T00:00:00"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


# ---------------------------------- Danh sách các chu kỳ dđã đựợc sử dụng cho đền thời gian hiệntại  ------------------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/license/period-dashboard    Danh sách các chu kỳ dđã đựợc sử dụng cho đền thời gian hiện tại
@apiDescription List Period Dashboard
@apiGroup License
@apiVersion 1.0.0
@apiName ListPeriodDashboard
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang



@apiSuccess {ArrayObject}        data                           Data
@apiSuccess {String}             data.start_time                Ngày bắt đầu 
@apiSuccess {String}             data.end_time                  Ngày kết thúc 


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "start_time": 192379192,
            "end_time": **********,
        }
    ]
    "lang": "vi",
    "message": "request thành công."
}
"""

## -----------------------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/license/info-modules  Thông tin các module theo merchant
@apiDescription info_modules
@apiGroup License
@apiVersion 1.0.0
@apiName info_modules
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
                "module": CDP & MARKETING,
                "package_code": growth,
                "start_time": **********,
                "active_time": ************,
                "expire_time": ***********,
                "key": "cdp"
        }
    ]
    "lang": "vi",
    "message": "request thành công."
}
"""

## ----------------------------------------- Đếm số lượng user theo merchant ------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/license/report/account  Đếm số lượng account theo merchant
@apiDescription count_number_account_by_merchant
@apiGroup License
@apiVersion 1.0.0
@apiName CountNumberAccountByMerchant
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)   {String}   module                 Tên chức năng ["sales", "services"]

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "total": 12,
        "total_allow": 25,
        "unlimited": True   // True là có giới hạn số lượng False không giới hạn 
    }
    "lang": "vi",
    "message": "request thành công."
}
"""

#------------------------------ Đếm số lượng team theo merchant ---------------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/license/report/team  Đếm số lượng team theo merchant
@apiDescription count_number_team_by_merchant
@apiGroup License
@apiVersion 1.0.0
@apiName CountNumberTeamByMerchant
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)   {String}   module                 Tên chức năng ["sales", "services"]

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "total": 12,
        "total_allow": 25,
        "unlimited": True           // True là có giới hạn số lượng False không giới hạn 
    }
    "lang": "vi",
    "message": "request thành công."
}
"""

#------------------------------ Info thông tin báo cáo sale ---------------------------------
"""
@api {GET} {HOST}/adm/api/v2.1/license/dashboard/sales-process  Thông tin báo cáo sale 
@apiDescription dashboard_sales_process
@apiGroup License
@apiVersion 1.0.0
@apiName DashboardSalesProcess
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)   {String}   module                 Bắt buộc phải là sales 

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "current_number": 0,
        "max_number": 0,
        "unlimited": true,
        "used_percentage": null
    },
    "lang": "en",
    "message": "request successful."
}
"""

"""
@api {GET} /adm/api/v2.1/product-line/config/mapping  Thông tin mapping nhánh sản phẩm 
@apiDescription menu, card này thuộc nhánh sản phẩm nào 
@apiGroup License
@apiVersion 1.0.0
@apiName GetConfigProductLine 
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)   {String}   mapping_type           loại mapping: menu, card_dashboard
@apiParam   (Query:)   {String}   [key_search]             menu id hoặc key card, nhiều giá trị cách nhau dấu ,

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "mapping_type": "card_dashboard",
            "source_value": "mo-dashboard-admin-account_performance",
            "destination_value": [
                "activation",
                "operation_service",
                "operation_sale"
            ]
        },
        ...
    ],
    "lang": "en",
    "message": "request successful."
}
"""

"""
@api {POST} /adm/api/v2.1/product-line/config/mapping  Cập nhật mapping nhánh sản phẩm 
@apiDescription menu, card này thuộc nhánh sản phẩm nào 
@apiGroup License
@apiVersion 1.0.0
@apiName UpdateConfigProductLine 
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {String}      mapping_type               loại mapping: menu, card_dashboard
@apiParam      (Body:)     {Array}      data                     danh sách mapping 
@apiParam      (data:)     {String}      source_value             menu id hoặc key card 
@apiParam      (data:)     {Array}      destination_value         danh sách nhánh sản phẩm:  "activation", "operation_service", "operation_sale"


@apiParamExample {json} Info example
{
    "mapping_type": "card_dashboard",
    "data": [
        {
            "source_value": "mo-dashboard-admin-account_performance",
            "destination_value": [
                "activation",
                "operation_service",
                "operation_sale"
            ]
        }
    ],
 }

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200
    
}
"""


"""
@api {GET} /adm/api/v2.1/license/packages/biggest  Lấy thông tin gói license lớn nhất 
@apiDescription hiện đang sử dụng cho module retention 
@apiGroup License
@apiVersion 1.0.0
@apiName GetPackageBiggest  
@apiUse json_header
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "package_name": "Enterprise",
        "package_code": "enterprise",
    },
    "lang": "en",
    "message": "request successful."
}
"""



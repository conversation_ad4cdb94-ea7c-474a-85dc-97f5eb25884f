
"""
@api {get} /api/v2.1/working-hours/holidays l<PERSON>y danh sách ngày lễ
@apiDescription API lấy danh sách ngày lễ
@apiGroup WorkingHours
@apiVersion 1.0.0
@apiName ListHolidays

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "code": "day_new_year",
            "order": 1,
            "name": "Tết <PERSON>ư<PERSON> lị<PERSON> (01 Tháng 01)"
        },
        {
            "code": "29th_lunar_new_year",
            "order": 2,
            "name": "Tết <PERSON><PERSON> lị<PERSON> (29 tết)"
        },
        {
            "code": "30th_lunar_new_year",
            "order": 3,
            "name": "Tết Âm lịch (30 tết)"
        },
        {
            "code": "1st_lunar_new_year",
            "order": 4,
            "name": "Tết <PERSON> l<PERSON> (Mùng 1 Tết <PERSON>)"
        },
        {
            "code": "2nd_lunar_new_year",
            "order": 5,
            "name": "Tết <PERSON>m lịch (Mùng 2 Tết <PERSON>uyên Đán)"
        },
        {
            "code": "3rd_lunar_new_year",
            "order": 6,
            "name": "Tết Âm lịch (Mùng 3 Tết Nguyên Đán)"
        },
        {
            "code": "liberation_day",
            "order": 7,
            "name": "Ngày Thống nhất Đất nước (30 tháng 04)"
        },
        {
            "code": "international_labor_day",
            "order": 8,
            "name": "Ngày Quốc tế Lao Động (01 Tháng 05)"
        },
        {
            "code": "independence_day",
            "order": 9,
            "name": "Ngày Quốc Khánh (02 Tháng 09)"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    
}
"""

"""
@apiDefine status_query_params
@apiVersion 1.0.0
@apiParam     (Query:)   {String}             [status]    Trạng thái tài kho<PERSON>n
                                                        <li><code>active</code>: <PERSON><PERSON> hoạt động</li>
                                                        <li><code>close</code>: <PERSON><PERSON>g</li>
                                                        <li><code>all</code>: Tất cả trạng thái</li>
                                                        <li>Default: <code>active</code></li>

"""
"""
@apiDefine account_status_query_params
@apiVersion 1.0.0
@apiParam     (Query:)   {String}     [account_status]    Trạng thái tài khoản
                                                        <li><code>active</code>: <PERSON>ang hoạt động</li>
                                                        <li><code>close</code>: Đóng</li>
                                                        <li><code>all</code>: Tất cả trạng thái</li>
                                                        <li>Default: <code>active</code></li>

"""
"""
@apiDefine account_status_body
@apiVersion 1.0.0
@apiParam       (Body:)         {String}        [account_status]                Trạng thái tài kho<PERSON>n
                                                                                <li><code>active</code>: Đang hoạt động</li>
                                                                                <li><code>close</code>: Đóng</li>
                                                                                <li><code>all</code>: Tất cả trạng thái</li>
                                                                                <li>Default: <code>active</code></li>

"""


#!/usr/bin/python
# -*- coding: utf8 -*-
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *API
BULK
SYNC
THÔNG
TIN
NHÂN
VIÊN ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 2.1
.1 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {post} /api/v2.1/accounts/actions/sync/bulk Bulk sync thông tin nhân viên
@apiDescription API sync thông tin nhân viên từ hệ thống ngoài.
@apiGroup Account
@apiVersion 2.1.1
@apiName SyncBulkAccountInfo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  [source] mã đối tác do mobio cung cấp
@apiParam   (Body:)   {String}  [requestId] định danh cho lần đồng bộ
@apiParam   (Body:)   {Array}  data  Danh sách <code>Account</code> cần đồng bộ thông tin. Hệ thống sẽ dựa vào thông tin định danh <code>userName</code> để kiểm tra account đã tồn tại trên hệ thống hay chưa.<br/> Mảng lưu trữ tối đa 200 accounts.
@apiParam   (data)   {String}  fullName  Họ và tên 
@apiParam   (data)   {String}  email  Địa chỉ email 
@apiParam   (data)   {String}  phone  Số điện thoại 
@apiParam   (data)   {String}  userName  tên tài khoản truy cập trên hệ thống mobio 
@apiParam   (data)   {String}  [staffCode]  mã nhân viên 
@apiParam   (data)   {String}  [scope_code]  Mã cấp đơn vị, được sắp xếp theo cấu trúc:level1#level2#level3#level4
@apiParam   (data)   {String}  [titleCode]  mã chức danh  
@apiParam   (data)   {String}  [titleName]  tên chức danh 
@apiParam   (data)   {Array}  [role]  danh sách quyền của tài khoản bắt buộc với "syncStatus" là "new", Với giá trị field "roleGroup" là “admin” hoặc "manager" thì giá trị sẽ là tên các modules mặc định của Mobio. 
Với giá trị "roleGroup" là "user" thì giá trị là tên các nhóm quyền tự tạo.
@apiParam   (data)   {String}  [roleGroup]  nhóm quyền tài khoản  bắt buộc với "syncStatus" là "new", có các giá trị "admin", "manager"; "user". 
@apiParam   (data)   {String}  syncStatus  Trạng thái đồng bộ bắt buộc để xử lý theo từng trường hợp: new, update, delete 
@apiParam   (data)   {String}  [area]  tên khu vực 
@apiParam   (data)   {String}  [area_code]  mã khu vực 
@apiParam   (data)   {Array}  [time_onleave]  danh sách khoảng thời gian nghỉ phép, dữ liệu được ghi nhận nếu ngày kết thúc lớn hơn thời điểm hiện tại, theo múi giờ Việt Nam.  
@apiParam   (Body:)   {Object}  callback Thông tin đầu nhận kết quả sau khi xử lý xong.

@apiParamExample {json} Body example
{
  "data":[
    {
      "fullName": "Nguyễn Văn Anh",
      "email": "<EMAIL>",
      "phone": "84904123456"
      "userName": "anhnv",
      "syncStatus": "new",
      "roleGroup": "user",
      "role": ["quyền sales"],
      "staffCode": "",
      "scope_code": "",
      "titleCode": "",
      "titleName": "",
      "area": "",
      "area_code": "",
      "time_onleave": [
        {
          "start_time": "2025-01-12 08:00:00", // %Y-%m-%d %H:%M:%S timezone ICT 
          "end_time": "2025-01-15 18:00:00", // %Y-%m-%d %H:%M:%S timezone ICT 
          "action": "overwrite"                        // hành động: overwrite - ghi đè, delete - xóa, insert - thêm  
          
        }
      ]
    }
  ],
  "callback": {
    "type": "api",
    "url": "https://callback...",
    "method": "post",
    "headers": {
        "Authorization": "Basic ...",
        "Content-Type": "application/json"
      },
  }
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "resultCode": "00",
  "resultMessage": "SUCCESS", 
}
"""

** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {get} /api/v2.1/merchants/<merchant_id>/all-accounts Lấy danh sách tất cả nhân viên có quyền vào module 
@apiDescription Thông tin ngắn gọn của tất cả nhân viên còn hiệu lực
@apiVersion 1.0.2
@apiGroup Account
@apiName GetAll

@apiParam (Query:) {String=MXH CSKH MKT TD} modules Danh sách module cần filter. Ví dụ: &modules=MXH,CSKH
@apiParam (Query:) {String} [status_custom]      Trạng thái tuỳ chỉnh của tài khoản
                                                  <li><code>on_leave</code>: Đang vắng mặt</li>
@apiUse status_query_params

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {String} merchant_id ID nhãn hàng lấy danh sách nhân viên
@apiSuccess (data) {String} id ID tài khoản nhân viên
@apiSuccess (data) {String} username Tên truy cập nhân viên
@apiSuccess (data) {String} fullname Tên dầy đủ nhân viên
@apiSuccess (data) {String} email email nhân viên
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
  "data": [
    {
      "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
      "username": "Locnh",
      "fullname": "Nguyễn Hữu Lộc",
      "email": "",
    },
    ...
  ]
}
"""
** ** ** ** ** ** ** ** ** ** ** ** *

"""
@api {get} /api/v2.1/merchants/<merchant_id>/accounts/<account_id> Lấy chi tiết nhân viên (ko sử dụng)
@apiDescription Lấy thông tin chi tiết nhân viên thuộc nhãn hàng
@apiVersion 2.0.0
@apiGroup Account
@apiName GetDetailAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess  {String} merchant_id ID nhãn hàng lấy danh sách nhân viên
@apiSuccess  {String} id ID tài khoản nhân viên
@apiSuccess  {String} username Tên truy cập nhân viên
@apiSuccess  {String} fullname Tên dầy đủ nhân viên
@apiSuccess  {String} avatar Ảnh đại điện nhân viên
@apiSuccess  {String} phone_number Số điện thoại nhân viên
@apiSuccess  {String} email Thư điện tử nhân viên
@apiUse created_time
@apiUse updated_time

@apiSuccess                   {String}              merchant_id                           ID nhãn hàng lấy danh sách nhân viên
@apiSuccess                   {String}              id                                    ID tài khoản nhân viên
@apiSuccess                   {String}              username                              Tên truy cập nhân viên
@apiSuccess                   {String}              fullname                              Tên dầy đủ nhân viên
@apiSuccess                   {String}              avatar                                Ảnh đại điện nhân viên
@apiSuccess                   {String}              phone_number                          Số điện thoại nhân viên
@apiSuccess                   {String}              email                                 Thư điện tử nhân viên
@apiSuccess                   {String}              created_account                       Tên truy cập tài khoản tạo account nhân viên
@apiSuccess                   {String}              full_name_created_account             Tên dầy đủ nhân viên tạo account nhân viên
@apiSuccess                   {String}              full_name_updated_account             Tên dầy đủ nhân viên sửa account nhân viên
@apiSuccess                   {number}              is_admin                              Phân biệt tài khoản quản trị của nhãn hàng với tài khoản thường
                                                                                          <li><code>1</code>:admin</li>
                                                                                          <li><code>2</code>:normal</li>
@apiSuccess                   {number}              is_mobio                              Phân biệt tài khoản của Mobio với các nhãn hàng khác
                                                                                          <li><code>1</code>:Mobio</li>
                                                                                          <li><code>2</code>:others</li>
@apiSuccess                   {Int}                 status                                Trạng thái của nhân viên
                                                                                          <li><code>1</code>:Đang hoạt động</li>
                                                                                          <li><code>4</code>:Đóng</li>  
@apiSuccess                   {String}              [push_operating_system]               Hệ điều hành của app mobile
@apiSuccess                   {Array}               master_campaign                       Master campaign của nhân viên
@apiSuccess       (data)      {String}              [push_ids]                            Danh sách push id
@apiSuccess       (data)      {String}              [push_ids.push_id]                    Mã app mobile dùng để push notify đến app
@apiSuccess       (data)      {String}              [push_ids.device_operating_system]    Hệ điều hành của app mobile
@apiSuccess       (data)      {String}              [push_ids.device_id]                  id thiết bị
@apiSuccess       (data)      {Bool}                [push_ids.sandbox]                    true/false , <code>true: dev, false: product, None: không xác định</code>

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
  "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "device_operating_system": "ios"
  "avatar": "",
  "phone_number": "",
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "is_admin": 1,
  "is_mobio": 1,
  "push_ids":[
        {
          "push_id": "KLAJSIOWN!O()@@LKLASJSOI!)@KSLA",
          "device_operating_system": "IOS",
          "device_id": "uuid",
          "sandbox": True
        },
        ....
      ],
  "master_campaign":[
    {
      "id": "0015ba02-2cf9-4ee9-8ec0-6a1829bfeb0e",
      "name": "Nhà hàng Lã Vọng"
      }
      ],
  "full_name_created_account": "Nam Anh (admin@mobioshop)",
  "full_name_updated_account": "Nam Anh (admin@mobioshop)"
  "policies":["f702b59b-312c-4a04-b62f-ad34b7c8d904"],
  "field_extra": {
        "MaChucDanhMoiNhat": "CD024",
        "TenChucDanhMoiNhat": "Phó Giám đốc phụ trách Khối",
        "account_id": "fba7215d-afd2-4bea-8a35-0623fcd19d68",
        "block": [
            "KHCN"
        ],
        "block_mapping": [
            {
                "name": "Khách hàng cá nhân",
                "value": "KHCN"
            }
        ],
        "department_ids": [
            "CBP_1001",
        ],
        "department_mapping": [
            {
                "name": "CBP_1001 - Phòng Khách hàng doanh nghiệp",
                "value": "CBP_1001"
            }
        ],
        "group_department_ids": [
            "dvkh",
            "ho_tro_tin_dung",
            "ban_giam_doc"
        ],
        "group_department_mapping": [
            {
                "name": "Phòng Dịch vụ khách hàng",
                "value": "dvkh"
            },
            {
                "name": "Bộ phận Hỗ trợ tín dụng",
                "value": "ho_tro_tin_dung"
            },
            {
                "name": "Ban Giám đốc",
                "value": "ban_giam_doc"
            }
        ],
        "email": "<EMAIL>",
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "scope_code": "1000#CNTT",
        "scope_name": "Khối CNTT",
        "sol_id": "1007",
        "sol_name": "Chi nhánh Hòa Bình"
    },
}
"""
** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {get} /api/v2.1/merchants/<merchant_id>/accounts/<account_id> Lấy chi tiết nhân viên
@apiDescription Lấy thông tin chi tiết nhân viên thuộc nhãn hàng
@apiVersion 1.0.0
@apiGroup Account
@apiName GetDetailAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {String} merchant_id ID nhãn hàng lấy danh sách nhân viên
@apiSuccess  {String} id ID tài khoản nhân viên
@apiSuccess  {String} username Tên truy cập nhân viên
@apiSuccess  {String} fullname Tên dầy đủ nhân viên
@apiSuccess  {String} avatar Ảnh đại điện nhân viên
@apiSuccess  {String} phone_number Số điện thoại nhân viên
@apiSuccess  {String} email Thư điện tử nhân viên
@apiUse created_time
@apiUse updated_time
@apiSuccess  {String} created_account Tên truy cập tài khoản tạo account nhân viên
@apiSuccess  {number=1:admin 2:normal} is_admin Phân biệt tài khoản quản trị của nhãn hàng với tài khoản thường
@apiSuccess  {number=1:Mobio 2:others} is_mobio Phân biệt tài khoản của Mobio với các nhãn hàng khác
@apiSuccess  {String} [push_id] Mã app mobile dùng để push notify đến app
@apiSuccess  {String} [push_operating_system] Hệ điều hành của app mobile
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
  "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "avatar": "",
  "phone_number": "",
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "is_admin": 1,
  "is_mobio": 1,
  "push_id": "KLAJSIOWN!O()@@LKLASJSOI!)@KSLA",
  "push_operating_system": "IOS"
  "email_verify": "is_verified"
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *API
THÔNG
TIN
MODULE
ASSIGNMENT
CỦA
MỘT
ACCOUNT ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {get} /adb/api/v2.1/merchants/<merchant_id>/accounts/<account_id>/module_assignment Thông tin module assignment
@apiDescription Lấy thông tin module assignment của một account.
@apiVersion 1.0.0
@apiGroup Account
@apiName GetAccountAssigment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {String} merchant_id ID nhãn hàng lấy danh sách nhân viên
@apiSuccess  {String} id ID tài khoản nhân viên
@apiSuccess  {String} username Tên truy cập nhân viên
@apiSuccess  {Array} assignments Danh sách các module được gán cho nhân viên
@apiSuccess (assignment) {String} module_id ID module
@apiSuccess (assignment) {String} name Tên module
@apiSuccess (assignment) {String} description Mô tả module
@apiSuccess (assignment) {boolean} is_checked Trạng thái được gán. Server sẽ trả về danh sách tất cả module cả không gán và có gán.
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
  "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
  "username": "Locnh",
  "assignments": [
    {
      "module_id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
      "name": "Quản lý nội dung",
      "description": "Quản lý nội dung chương trình",
      "is_checked": true
    }
  ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *API
THÔNG
TIN
ASSIGNMENT
CỦA
MỘT
ACCOUNT ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {get} /api/v2.1/merchants/<merchant_id>/accounts/<account_id>/assignment/<module_id> Thông tin assignment
@apiDescription Thông tin assignment của một account
@apiVersion 1.0.0
@apiGroup Account
@apiName GetAccountAssigmentModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {String} merchant_id ID nhãn hàng lấy danh sách nhân viên
@apiSuccess  {String} account_id ID tài khoản nhân viên
@apiSuccess  {String} module_id ID module
@apiSuccess  {array} assignments Danh sách chức năng được gán
@apiSuccess (assignment) {String} function_id ID chức năng
@apiSuccess (assignment) {String} name Tên chức năng
@apiSuccess (assignment) {String} description Mô tả chức năng
@apiSuccess (assignment) {array} actions Danh Sách action được gán vào chức năng
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>Number</td>
      <td>Mã action.</td>
    </tr>
    <tr>
      <td>name_vi</td>
      <td>String</td>
      <td>Tên action bằng tiếng Việt.</td>
    </tr>
    <tr>
      <td>name_en</td>
      <td>String</td>
      <td>Tên action bằng tiếng Anh.</td>
    </tr>
    <tr>
      <td>is_checked</td>
      <td>boolean</td>
      <td>Trạng thái được gán cho chức năng. Server sẽ trả về danh sách cả không gán và có gán</td>
    </tr>
  </tbody>
</table>

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
  "account_id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
  "module_id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
  "assignments": [
    {
      "function_id": "7a810df9-7be5-486f-bd4e-1d0d825d593d",
      "name": "Quản lý cửa hàng",
      "description": "Chức năng quản lý danh sách cửa hàng.",
      "actions": [
        {
          "id": 1,
          "name_vi": "Thêm",
          "name_en": "Add",
          "is_checked": true
        }
      ]
    }
  ]
}

"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *API
CẬP
NHẬT
THÔNG
TIN
NHÂN
VIÊN ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
*version: 2.1
.0 *
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **

"""
@api {put} /api/v2.1/merchants/sub-brands/<sub_brands_id>/accounts/<profile_id> Update nhân viên
@apiDescription Update nhân viên. Dịch vụ gửi lên request dạng <code>form-data</code>  
@apiVersion 2.1.0
@apiGroup Account
@apiName Patch
@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {File}      avatar                          Ảnh đại diện
@apiParam      (Body:)     {String}    info                            Thông tin  sản phẩm, String object json


@apiParam             (info)                    {string}               password                    Mật khẩu
@apiParam             (info)                    {string}               fullname                    Tên đầu đủ
@apiParam             (info)                    {string}               phone_number                Số điện thoại
@apiParam             (info)                    {string}               email                       Email của tài khoản
@apiParam             (info)                    {String}               role_group                  Nhóm quyền <code> owner, admin, manager, user</code>
@apiParam             (info)                    {string}               [module_ids]                Mảng các module_id:
@apiParam             (info)                    {array}                [policies]                  Mảng các ID policies:
@apiParam             (info)                    {array}                [block]                     Mảng các khối
@apiParam             (info)                    {array}                [department_ids]            Mảng các phòng ban
@apiParam             (info)                    {string}               [sol_id]                    Đơn vị kinh doanh
@apiParam             (info)                    {string}               [scope_code]                Mã cấp quản lý
@apiParam             (info)                    {string}               [position_id]               Chức danh
@apiParam             (info)                    {string}               [gender]                    Giới tính <code>male/female</code>
@apiParam             (info)                    {array}                [product_line]              Danh sách dòng sản phẩm account có quyền truy cập
@apiParam             (info)                    {array}                [tags]                      Danh sách tags
@apiParam             (info)                    {array}                [staff_code]                Mã nhân viên


@apiParamExample {json} Info example
{
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "phone_number": "",
  "email": "<EMAIL>",
  "password":"1234",
  "module_ids":["18a07df3-5fdc-41c4-9bc9-00cee694c68a"],
  "policies": ["18a07df3-5fdc-41c4-9bc9-00cee694c68a"],
  "block":["KHCN"],
  "department_ids": ["CBP_1001"],
  "sol_id":"1001",
  "scope_code":"Directsale",
  "position_id":"CD056",
  "group_department_ids": ["dvkh","ho_tro_tin_dung","ban_giam_doc"],
  "product_line": ["base_service","activation"],
  "tags": ["18a07df3-5fdc-41c4-9bc9-00cee694c68a"],
  "staff_code":"CD056"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "86b15b04-d4d1-41aa-95f8-b826e4bca0be",
  "id": "cbce917d-f9f8-4088-bcad-da01975d3163",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "avatar": "",
  "phone_number": "",
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "role_group": "manager",
  "modules":[
          {
                "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                "name": "Chăm sóc khách hàng",
                "description": ""
          },
          ...
  ],
  "policies": ["18a07df3-5fdc-41c4-9bc9-00cee694c68a"],
  "block":["KHCN"],
  "department_ids": ["CBP_1001"],
  "sol_id":"1001",
  "scope_code":"Directsale",
  "position_id":"CD056",
  "group_department_ids": [
            "dvkh",
            "ho_tro_tin_dung",
            "ban_giam_doc"
        ],
    "group_department_mapping": [
        {
            "name": "Phòng Dịch vụ khách hàng",
            "value": "dvkh"
        },
        {
            "name": "Bộ phận Hỗ trợ tín dụng",
            "value": "ho_tro_tin_dung"
        },
        {
            "name": "Ban Giám đốc",
            "value": "ban_giam_doc"
        }
    ],
    "tags": ["18a07df3-5fdc-41c4-9bc9-00cee694c68a"],
}

"""
** ** ** ** ** ** ** **
"""
@api {patch} /api/v2.1/merchants/<merchant_id>/accounts/<account_id> Cập nhật thông tin nhân viên
@apiDescription Cập nhật thông tin nhân viên của nhãn hàng
@apiVersion 1.0.0
@apiGroup Account
@apiName Patch

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body: form-data) {String} [fullname] Tên đầu đủ
@apiParam (Body: form-data) {file} [avatar] Ảnh đại diện
@apiParam (Body: form-data) {String} [phone_number] Số điện thoại
@apiParam (Body: form-data) {String} [email] Email của tài khoản
@apiParam (Body: form-data) {String} [modules] mảng các module có kiến trúc json như sau:

<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>module_id</td>
      <td>string</td>
      <td>ID module được gán cho nhân viên</td>
    </tr>
    <tr>
      <td>function_id</td>
      <td>string</td>
      <td>ID chức năng được gán cho nhân viên</td>
    </tr>
    <tr>
      <td>actions</td>
      <td>array</td>
      <td>Danh sách chức năng tương ứng với function được gán cho nhân viên</td>
    </tr>
  </tbody>
</table>

<pre class="prettyprint language-json prettyprinted"><span class="str">
[</br>
  {</br>
    "module_id": "9dac9fb4-53c2-401b-bbc9-2464ce771068",</br>
    "assignments":</br>
    [</br>
      {</br>
        "function_id": "278d33c7-a6db-45e0-8cc3-9434eabe3bde",</br>
        "actions": [</br>
          1029,</br>
          128</br>
        ]</br>
      },</br>
      ...</br>
    ]</br>
  },</br>
  ...</br>
]</span></pre>

@apiSuccess  {String} merchant_id ID nhãn hàng
@apiSuccess  {String} id ID nhân viên
@apiSuccess  {String} username Tên truy cập của nhân viên
@apiSuccess  {String} fullname Họ tên nhân viên
@apiSuccess  {String} avatar Linh ảnh đại diện nhân viên
@apiSuccess  {String} phone_number Số điện thoại nhân viên
@apiSuccess  {String} email Thư điện tử nhân viên
@apiUse created_time
@apiUse updated_time
@apiSuccess  {String} created_account Tên truy cập tài khoản tạo account nhân viên
@apiSuccess  {number=1:admin 2:normal} is_admin Phân biệt tài khoản quản trị của nhãn hàng với tài khoản thường
@apiSuccess  {number=1:Mobio 2:others} is_mobio Phân biệt tài khoản của Mobio với các nhãn hàng khác
@apiSuccess  {String} [push_id] Mã app mobile dùng để push notify đến app
@apiSuccess  {String} [push_operating_system] Hệ điều hành của app mobile
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "86b15b04-d4d1-41aa-95f8-b826e4bca0be",
  "id": "cbce917d-f9f8-4088-bcad-da01975d3163",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "avatar": "",
  "phone_number": "",
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "is_admin": 1,
  "is_mobio": 1,
  "push_id": "KLAJSIOWN!O()@@LKLASJSOI!)@KSLA",
  "push_operating_system": "IOS",
  "modules": [
    {
      "module_id": "9dac9fb4-53c2-401b-bbc9-2464ce771068",
      "assignments": [
        {
          "function_id": "278d33c7-a6db-45e0-8cc3-9434eabe3bde",
          "actions": [
            1029,
            128
          ]
        }
      ]
    }
  ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *API
TẠO
NHÂN
VIÊN ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
*version: 1.0
.0
*version: 2.1
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {post} /api/v2.1/merchants/sub-brands/<sub_brands_id>/accounts Tạo nhân viên
@apiDescription Tạo một account nhân viên theo thương hiệu. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiVersion 2.1.0
@apiGroup Account
@apiName Post
@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {File}      avatar                           Ảnh đại diện
@apiParam      (Body:)     {String}    info                            Thông tin  sản phẩm, String object json


@apiParam (info) {string} username         Tên truy cập
@apiParam (info) {string} password         Mật khẩu
@apiParam (info) {string} fullname         Tên đầu đủ
@apiParam (info) {string} phone_number     Số điện thoại
@apiParam (info) {string} email            Email của tài khoản
@apiParam (info) {String} role_group         Nhóm quyền <code> owner, admin, manager, user</code>
@apiParam (info) {String} type_password      Nguời nhập mật khẩu <code> 1- người dùng cập nhập mật khẩu, 0- hệ thống, người khác cập nhập mật khẩu, 2- người dùng mới bắt buộc phải thay đổi mật khẩu khi đăng</code>
@apiParam (info) {string} [module_ids]     Mảng các module_id:
@apiParam (info) {array} [policies]        Mảng các policy id
@apiParam (info) {array} [block]        Mảng các khối
@apiParam (info) {array} [department_ids]        Mảng các phòng ban
@apiParam (info) {string} [sol_id]        Đơn vị kinh doanh
@apiParam (info) {string} [scope_code]        Mã cấp quản lý
@apiParam (info) {string} [position_id]        Chức danh
@apiParam (info) {string} [gender]        Giới tính <code>male/female</code>
@apiParam (info) {array} [product_line]     danh sách dòng sản phẩm account có quyền truy cập: "activation","operation_sale","operation_service"
@apiParam (info) {array} [tags]             Danh sách id tags



@apiParamExample {json} Info example
{
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "phone_number": "",
  "email": "<EMAIL>",
  "password":"1234",
  "role_group": "manager",
  "type_password": 1,
  "module_ids":["f702b59b-312c-4a04-b62f-ad34b7c8d904"]
  "policies":["f702b59b-312c-4a04-b62f-ad34b7c8d904"],
  "block":["KHCN"],
  "department_ids": ["CBP_1001"],
  "sol_id":"1001",
  "scope_code":"Directsale",
  "position_id":"CD056",
  "group_department_ids": ["dvkh", "ho_tro_tin_dung", "ban_giam_doc"],
  "product_line": ["activation","operation_sale","operation_service"],
  "tags": []
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "86b15b04-d4d1-41aa-95f8-b826e4bca0be",
  "id": "cbce917d-f9f8-4088-bcad-da01975d3163",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "avatar": "",
  "phone_number": "",
  "role_group": 2,
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "is_online": 0,
  "role_group": "manager",
  "module_ids":["f702b59b-312c-4a04-b62f-ad34b7c8d904"]
  "policies":["f702b59b-312c-4a04-b62f-ad34b7c8d904"],
  "block":["KHCN"],
  "department_ids": ["CBP_1001"],
  "sol_id":"1001",
  "scope_code":"Directsale",
  "position_id":"CD056",
  "group_department_ids": ["dvkh", "ho_tro_tin_dung", "ban_giam_doc"],
  "tags": []
}
"""
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {post} /api/v2.1/merchants/<merchant_id>/accounts Tạo nhân viên
@apiDescription Tạo một account nhân viên cho hệ thống theo nhãn hàng
@apiVersion 1.0.0
@apiGroup Account
@apiName Post

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body: form-data) {String} username Tên truy cập
@apiParam (Body: form-data) {String} password Mật khẩu
@apiParam (Body: form-data) {String} [fullname] Tên đầu đủ
@apiParam (Body: form-data) {file} [avatar] Ảnh đại diện
@apiParam (Body: form-data) {String} [phone_number] Số điện thoại
@apiParam (Body: form-data) {String} [email] Email của tài khoản
@apiParam (Body: form-data) {number=1:admin 2:normal} is_admin=2 Phân biệt tài khoản quản trị của nhãn hàng với tài khoản thường
@apiParam (Body: form-data) {String} [modules] mảng các module có kiến trúc json như sau:

<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>module_id</td>
      <td>string</td>
      <td>ID module được gán cho nhân viên</td>
    </tr>
    <tr>
      <td>function_id</td>
      <td>string</td>
      <td>ID chức năng được gán cho nhân viên</td>
    </tr>
    <tr>
      <td>actions</td>
      <td>array</td>
      <td>Danh sách chức năng tương ứng với function được gán cho nhân viên</td>
    </tr>
  </tbody>
</table>

<pre class="prettyprint language-json prettyprinted"><span class="str">
[</br>
  {</br>
    "module_id": "9dac9fb4-53c2-401b-bbc9-2464ce771068",</br>
    "assignments":</br>
    [</br>
      {</br>
        "function_id": "278d33c7-a6db-45e0-8cc3-9434eabe3bde",</br>
        "actions": [</br>
          1029,</br>
          128</br>
        ]</br>
      },</br>
      ...</br>
    ]</br>
  },</br>
  ...</br>
]</span></pre>

@apiSuccess  {String} merchant_id ID nhãn hàng
@apiSuccess  {String} id ID nhân viên
@apiSuccess  {String} username Tên truy cập của nhân viên
@apiSuccess  {String} fullname Họ tên nhân viên
@apiSuccess  {String} avatar Linh ảnh đại diện nhân viên
@apiSuccess  {String} phone_number Số điện thoại nhân viên
@apiSuccess  {String} email Thư điện tử nhân viên
@apiUse created_time
@apiUse updated_time
@apiSuccess  {String} created_account Tên truy cập tài khoản tạo account nhân viên
@apiSuccess  {number=1:admin 2:normal} is_admin Phân biệt tài khoản quản trị của nhãn hàng với tài khoản thường
@apiSuccess  {number=1:Mobio 2:others} is_mobio Phân biệt tài khoản của Mobio với các nhãn hàng khác
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "86b15b04-d4d1-41aa-95f8-b826e4bca0be",
  "id": "cbce917d-f9f8-4088-bcad-da01975d3163",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "avatar": "",
  "phone_number": "",
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "is_admin": 1,
  "is_mobio": 1,
  "modules": [
    {
      "module_id": "9dac9fb4-53c2-401b-bbc9-2464ce771068",
      "assignments": [
        {
          "function_id": "278d33c7-a6db-45e0-8cc3-9434eabe3bde",
          "actions": [
            1029,
            128
          ]
        }
      ]
    }
  ]
}

"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *API
XOÁ
DANH
SÁCH
NHÂN
VIÊN ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
*version: 2.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {delete} /api/v2.1/accounts/sub-brands/actions/delete Xoá danh sách nhân viên
@apiDescription Xoá danh sách nhân viên, chỉ cho phép dùng loại authen là Bearer vì cần check quyền xóa của tài khoản đang đăng nhập 
@apiVersion 2.1.0
@apiGroup Account
@apiName Delete
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)  {String}  staff_ids   danh sách các nhân viên cần xóa cách nhau dấu , <code>staff_ids=id1,id2</code>

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "staff_ids": ["id1","id2"]
  }
   "message": "delete successful!!!"
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *API
XOÁ
NHÂN
VIÊN ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {delete} /api/v2.1/merchants/<merchant_id>/accounts/<account_id> Xoá nhân viên
@apiDescription Xoá một nhân viên khỏi hệ thống
@apiVersion 1.0.0
@apiGroup Account
@apiName Delete

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {String} merchant_id ID nhãn hàng lấy danh sách nhân viên
@apiSuccess  {String} id ID tài khoản nhân viên
@apiSuccess  {String} username Tên truy cập nhân viên
@apiSuccess  {String} fullname Tên dầy đủ nhân viên
@apiSuccess  {String} avatar Ảnh đại điện nhân viên
@apiSuccess  {String} phone_number Số điện thoại nhân viên
@apiSuccess  {String} email Thư điện tử nhân viên
@apiUse created_time
@apiUse updated_time
@apiSuccess  {String} created_account Tên truy cập tài khoản tạo account nhân viên
@apiSuccess  {number=1:admin 2:normal} is_admin Phân biệt tài khoản quản trị của nhãn hàng với tài khoản thường
@apiSuccess  {number=1:Mobio 2:others} is_mobio Phân biệt tài khoản của Mobio với các nhãn hàng khác
@apiSuccess  {String} [push_id] Mã app mobile dùng để push notify đến app
@apiSuccess  {String} [push_operating_system] Hệ điều hành của app mobile
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
  "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "avatar": "",
  "phone_number": "",
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "is_admin": 1,
  "is_mobio": 1,
  "push_id": "KLAJSIOWN!O()@@LKLASJSOI!)@KSLA",
  "push_operating_system": "IOS"
}

"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *API
ĐẶT
MẬT
KHẨU
CHO
NHÂN
VIÊN ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {put} /api/v2.1/merchants/<merchant_id>/accounts/<account_id>/password Đặt mật khẩu cho nhân viên
@apiDescription Cập nhật mật khẩu cho nhân viên
@apiVersion 1.0.0
@apiGroup Account
@apiName PutPasswordAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {String} old_password Mật khẩu cũ của nhân viên
@apiParam  {String} new_password Mật khẩu mới của nhân viên
@apiParam  {String} confirm_password Xác nhận mật khẩu mới của nhân viên
@apiParamExample {json} Body
{
  "old_password": "abcxyz",
  "new_password": "xyzabc",
  "confirm_password": "xyzabc"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Thành công"
}
"""

# ***********************************************************************************************************************
# ************************************* API Lấy danh sách acount theo ids  ***********************************************
# ***********************************************************************************************************************
"""
@api {post} /api/v2.1/merchants/account-id Lấy danh sách acount theo danh sách id
@apiDescription Lấy danh sách account theo danh sách id
@apiVersion 1.0.0
@apiGroup Account
@apiName GetAccountIds 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {Array}        ids                 Danh sách id của account
@apiParam (Body:)  {String}       [status_custom]     Trạng thái tuỳ chỉnh của tài khoản
                                                      <li><code>on_leave</code>: Đang vắng mặt</li>
@apiParam (Body:)  {Array}        [status]            Trạng thái tài khoản
                                                      <li><code>1</code>: Đang hoạt động</li>
                                                      <li><code>4</code>: Đóng</li>
                                                      <li>Default:<code>1</code>: Đang hoạt động</li>



@apiSuccess {String}       avatar                     avatar
@apiSuccess {String}       fullname                   Tên đầy đủ
@apiSuccess {String}       id                         id nhân viên
@apiSuccess {String}       username                   username
@apiSuccess {String}       status                     Trạng thái của tài khoản
@apiSuccess {String}       [status_custom]            Trạng thái hoạt động của nhân viên
                                                      <li><code>on_leave</code>: Đang vắng mặt/nghỉ phép...</li>


@apiParamExample {json} Body
{
  "ids":["00822505-0a63-4bff-bf96-e2431a3e4b91","015edfa2-d94d-42d4-8489-c7b7226c9dcb"]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "avatar": null,
            "fullname": "subitachi1",
            "id": "00822505-0a63-4bff-bf96-e2431a3e4b91",
            "is_admin": 1,
            "username": "subitachi1@itachi",
            "status": 1
            "status_custom": "on_leave"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *API
Lấy
log ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
*version: 2.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {get} /api/v2.1/accounts/logs Lấy danh sách log của nhân viên
@apiDescription Lấy danh sách log của nhân viên
@apiVersion 2.1.0
@apiGroup Account
@apiName Get Log
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse search


@apiParam      (Query:)     {String}    [id_account]                   user_id
@apiParam      (Query:)     {String}    [start_time]                   tìm log từ thời gian này, UTC (VD: 2023-05-03T17:00:00)
@apiParam      (Query:)     {String}    [end_time]                     tìm log cho đến thời gian này, UTC (VD: 2023-06-02T16:59:59)
@apiParam      (Query:)     {Bool}    [socket_event]                 <code>True/False</code> Lọc ra các socket_event của account. Không truyền lên để lấy tất cả. 
                                                                      Gửi False để lấy danh sách k có socket. 

@apiParam (info) {string} created_time     Thời gian tạo log UTC
@apiParam (info) {string} action           Hoạt động của hoạt động
@apiParam (info) {string} id_account             ID tài khoản 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": [
      {
        "id_account": "0006d21a-2eaf-40a6-88d5-9c77188c7c23",
        "created_time": "2017-08-07T04:02:28.002Z",
        "action": {
                    "id": 1,
                    "name_vi": "Thêm",
                    "name_en": "Add",
                    "description": "Thêm item"
                },
      },
      {
        "id_account":"0006d21a-2eaf-40a6-88d5-9c77188c7c23",
        "created_time": "2017-08-07T04:02:28.002Z",
        "action": {
                    "id": 2,
                    "name_vi": "Sửa",
                    "name_en": "Edit",
                    "description": "Thêm item"
                },
      },
      ....
    ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** API
lấy
danh
sách
nhân
viên
online ** ** ** ** ** ** ** ** ** ** ** *
*version: 2.1
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {get} /api/v2.1/account/state/online Lấy danh sách nhân viên online 
@apiDescription Lấy danh sách nhân viên online của merchant
@apiVersion 2.1.0
@apiGroup Account
@apiName Get List staff online
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Query:)     {String}    merchant_id                   Danh sách tenant cần lấy danh sách staff online

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công.",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "staff_ids": [
        "bc695ead-517f-4e96-9d6b-1a53c378e4f1",
        "********-ddfb-4113-bb40-5da47a4843ea",
        "c1b427f3-122c-4ae7-b1b3-a3901fc34de5",
        "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "823ed8f0-83b2-491d-936d-32c2accfa15b"
    ]
}
"""

"""
@api {put} /api/v2.1/mobile/merchants/accounts mobile Update thông tin nhân viên
@apiDescription mobile Update thông tin nhân viên 
@apiVersion 2.1.0
@apiGroup Account
@apiName MobileUpdateAccount 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam      (Query:)   {String} account_id                          	id nhân viên cần update 
@apiParam      (Query:)   {String} merchant_id                          id của merchant
@apiParam      (Body:)     {Object}      avatar                          	Ảnh đại diện, object json
@apiParam      (Body:)     {Object}    info                            Thông tin cập nhật, object json


@apiParam (avatar) {string} data          base64 của ảnh avatar 
@apiParam (avatar) {string} ext     đuôi file 

@apiParam (info) {string} fullname         Tên đầu đủ
@apiParam (info) {string} phone_number     Số điện thoại
@apiParam (info) {string} email            Email của tài khoản


@apiParamExample {json} Info example
{
  "fullname": "Nguyễn Hữu Lộc",
  "phone_number": "**********",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200 
  
}

"""

"""
@api {get} /api/v2.1/account/find/staff-code Lấy thông tin nhân viên theo mã 
@apiDescription Lấy chi tiết thông tin nhân viên theo tên truy cập hoặc mã nhân viên 
@apiVersion 2.1.0
@apiGroup Account
@apiName GetDetailByStaffCode
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Query:)     {String}    text_search        tên truy cập hoặc mã nhân viên 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "email": "<EMAIL>",
    "fullname": "Nguyễn Văn A",
    "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "phone_number": "+***********",
    "role_group": "owner",
    "staff_code": "admin",
    "username": "admin@pingcomshop"
  },
  "lang": "vi",
  "message": "request thành công."
}
"""

"""
@api {get} /api/v2.1/account/find/role-group Lấy danh sách nhân viên theo nhóm quyền 
@apiDescription Lấy danh sách nhân viên theo nhóm quyền 
@apiVersion 2.1.0
@apiGroup Account
@apiName AccountByRoleGroup
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Query:)     {String}    role_group        nhóm quyền: admin, owner, manager, user 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": 
  [
      {
        "email": "<EMAIL>",
        "fullname": "Nguyễn Văn A",
        "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "phone_number": "+***********",
        "role_group": "owner",
        "staff_code": "admin",
        "username": "admin@pingcomshop"
      }
  ],
  "lang": "vi",
  "message": "request thành công."
}
"""

"""
@api {get} /api/v2.1/accounts/list Lấy danh sách nhân viên cơ bản 
@apiDescription Dịch vụ lấy danh sách nhân viên đang hoạt động.
@apiVersion 2.1.0
@apiGroup Account
@apiName ListAccountBase

@apiParam         (Query:)        {String}          [search]                Chuỗi tìm kiếm. UrlEncode dữ liệu trước khi request.
@apiParam         (Query:)        {String}          [staff_ids]             Danh sách id của nhân viên cần lấy thông tin. Ex: <code>&staff_ids=78074fc8-4617-48a3-9e79-bccf96fc34f3,dc509905-94...</code>
@apiParam         (Query:)        {string}          [role_group]            Nhóm quyền <code> owner, admin, manager, user</code>. Ex:<code>&role_group:owner,admin,user</code>
@apiParam         (Query:)        {String}          [field_search]          Trường cần tìm kiếm, nhiều giá trị cách nhau dấu ",".("username", "email", "phone_number", "fullname")
@apiParam         (Query:)        {String}          [account_priority]      Định danh của tài khoản sẽ được ưu tiên hiển thị lên đầu.
@apiParam         (Query:)        {String}          [select_field]          Field lựa chọn để trả về, nhiều giá trị cách nhau dấu "," (VD: id,username,fullname)
@apiParam         (Query:)        {String}          [status]                Trạng thái tài khoản
                                                                            <li><code>active</code>: Đang hoạt động</li>
                                                                            <li><code>close</code>: Đóng</li>
                                                                            <li><code>all</code>: Tất cả trạng thái</li>
                                                                            <li>Default: <code>active</code></li>
@apiParam         (Query:)        {String}          [status_custom]         Trạng thái tuỳ chỉnh của tài khoản
                                                                            <li><code>on_leave</code>: Đang vắng mặt</li>

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse order_sort
@apiUse paging
@apiUse merchant_id_header

@apiSuccess         (data)        {String}                merchant_id                   ID nhãn hàng lấy danh sách nhân viên
@apiSuccess         (data)        {String}                id                            ID tài khoản nhân viên
@apiSuccess         (data)        {String}                username                      Tên truy cập nhân viên
@apiSuccess         (data)        {String}                fullname                      Tên dầy đủ nhân viên
@apiSuccess         (data)        {String}                avatar                        Ảnh đại điện nhân viên
@apiSuccess         (data)        {String}                phone_number                  Số điên thoại 
@apiSuccess         (data)        {String}                email                         Email  
@apiSuccess         (data)        {number}                is_admin                      Trạng thái nhận biết admin của account
                                                                                        <li><code>1</code>:Admin</li>
                                                                                        <li><code>2</code>:Normal</li>
@apiSuccess         (data)        {number}                status                        Trạng thái nhân viên
                                                                                        <li><code>1</code>:Enable</li>
                                                                                        <li><code>2</code>:Disable</li>
@apiSuccess         (data)        {String}                last_login_date               Thời điểm login lần cuối. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess         (data)        {String}                role_group                    Nhận biết quyền của account
@apiSuccess         (data)        {String}                [status_custom]               Trạng thái hoạt động của nhân viên
                                                                                        <li><code>on_leave</code>: Đang vắng mặt/nghỉ phép...</li>


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
      "username": "Locnh",
      "fullname": "Nguyễn Hữu Lộc",
      "avatar": "",
      "status": 1,
      "merchant_id": "3e669f7b-ac5a-42f8-b565-e140277362a8",
      "last_login_date": "2017-08-07T04:02:28.002Z",
      "role_group": "owner",
      "status_custom": "on_leave"
    },
    {
      "id": "32f09021-f573-472e-8674-1e0812e1713e",
      "username": "Locnh",
      "fullname": "Nguyễn Hữu Lộc",
      "avatar": "",
      "status": 1,
      "merchant_id": "b4ebcbab-d945-4073-8e26-fe36fe283eb9",
      "last_login_date": "2017-08-07T04:02:28.002Z",
      "role_group": "admin",
      "status_custom": ""
    }
  ],
  "paging": {
    "page": 1,
    "per_page": 5,
    "total_count": 220,
    "total_page": 44
  }
}
"""



# ------------------------------------- Get list account with ignore_ids -------------------------------------
"""
@api {POST} /api/v2.1/accounts/list/with-ignore Lấy danh sách nhân viên cơ bản, bỏ qua những id không cần thiết
@apiDescription Dịch vụ lấy danh sách nhân viên đang hoạt động, bỏ qua những ids cần ignore
@apiVersion 2.1.0
@apiGroup Account
@apiName ListAccountWithIgnore

@apiParam   (Query:)  {string}  [role_group]   Nhóm quyền <code> owner, admin, manager, user</code>. Ex:<code>&role_group:owner,admin,user</code>
@apiParam   (Query:)  {String}  [select_field]  Thông tin các field muốn lấy ra, nhiều giá trị cách nhau dấu ",". Nếu không gửi lên mặc định lấy tất cả thông tin
@apiUse status_query_params

@apiParam   (BODY:)     {List}            ignore_ids                    Dánh sách những id muốn bỏ qua

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse order_sort
@apiUse paging
@apiUse merchant_id_header

@apiSuccess (data) {String} merchant_id ID nhãn hàng lấy danh sách nhân viên
@apiSuccess (data) {String} id ID tài khoản nhân viên
@apiSuccess (data) {String} username Tên truy cập nhân viên
@apiSuccess (data) {String} fullname Tên dầy đủ nhân viên
@apiSuccess (data) {String} avatar Ảnh đại điện nhân viên
@apiSuccess (data) {String} phone_number Số điên thoại 
@apiSuccess (data) {String} email   Email  
@apiSuccess (data) {number=1-Admin 2-Normal} is_admin Trạng thái nhận biết admin của account
@apiSuccess (data) {number=1-Enable 2-Disable} status Trạng thái nhân viên
@apiSuccess (data) {String} update_time Thời gian update 
@apiSuccess (data) {DateTime}   last_login_date   Thời điểm login lần cuối. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess (data) {String}   role_group   Nhận biết quyền của account



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
      "username": "Locnh",
      "fullname": "Nguyễn Hữu Lộc",
      "avatar": "",
      "is_admin": 1,
      "status": 1,
      "merchant_id": "3e669f7b-ac5a-42f8-b565-e140277362a8",
      "is_online": 0,
      "last_login_date": "2017-08-07T04:02:28.002Z",
      "role_group": "owner",
    },
    {
      "id": "32f09021-f573-472e-8674-1e0812e1713e",
      "username": "Locnh",
      "fullname": "Nguyễn Hữu Lộc",
      "avatar": "",
      "is_admin": 1,
      "status": 1,
      "merchant_id": "b4ebcbab-d945-4073-8e26-fe36fe283eb9",
      "is_online": 1,
      "last_login_date": "2017-08-07T04:02:28.002Z",
      "role_group": "admin",
    }
  ],
  "paging": {
    "page": 1,
    "per_page": 5,
    "total_count": 220,
    "total_page": 44
  }
}
"""
# -------------------------------------------------------

"""
@api {GET} /api/v2.1/accounts/list/extra Lấy danh sách nhân viên và thông tin từ partner 
@apiDescription Lấy danh sách nhân viên và thông tin từ partner
@apiVersion 2.1.0
@apiGroup Account
@apiName GetListAccountExtra

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header


@apiParam   (Query:)  {string}  [search]    Từ khóa cần tìm kiếm (Tìm kiếm theo fullname và username)



@apiSuccess (data) {Array} block                    Mã các khối khách hàng
@apiSuccess (data) {String} scope_code              Mã cấp nhân viên
@apiSuccess (data) {String} username                Tên truy cập nhân viên
@apiSuccess (data) {String} fullname                Tên dầy đủ nhân viên
@apiSuccess (data) {String} phone_number            Số điên thoại 
@apiSuccess (data) {String} email                   Email của nhân viên 
@apiSuccess (data) {String} id                      Id tài khoản 
@apiSuccess (data) {DateTime} last_login_date       Thời điểm login lần cuối. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess (data) {String}   role_group            Nhận biết quyền của account


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "block": [
                "KHCN"
            ],
            "email": "<EMAIL>",
            "fullname": "Nguyễn Văn A",
            "id": "00631f64-ebdc-45c1-aa53-27c262cce0d0",
            "last_login_date": "2022-12-02T05:35:12Z",
            "phone_number": "**********",
            "role_group": "user",
            "staff_code": "015575",
            "username": "abc@mobio",
            "scope_code": "1#2#3"
        },
        {
            "block": [
                "KHDN",
                "NHBH"
            ],
            "email": "<EMAIL>",
            "fullname": "ĐINH THỊ C",
            "id": "009347ec-c413-479b-9163-af633ddc4391",
            "last_login_date": "2022-12-02T05:31:33Z",
            "phone_number": "**********",
            "role_group": "user",
            "staff_code": "014380",
            "username": "cde@mobio",
            "scope_code": "1#2"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 2,
        "per_page": 2,
        "total_count": 813,
        "total_page": 407
    }
}
"""


"""
@api {GET} /api/v2.1/accounts/role-name-create Lấy danh sách nhân viên theo nhóm quyền tự tạo hoặc theo nhóm quyền nhân viên 
@apiDescription path cho mobile: adm/mobile/api/v2.1/accounts/role-name-create
@apiVersion 2.1.0
@apiGroup Account

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse order_sort
@apiUse paging
@apiUse merchant_id_header

@apiName GetListAccountByRole 
@apiParam   (Query:)  {string}  [search]    Từ khóa cần tìm kiếm (Tìm kiếm theo fullname và username)
@apiParam   (Query:)  {string}  role_name   Tên nhóm quyền tự tạo (nhiều giá trị cách nhau ;)
@apiParam   (Query:)  {string}  [role_group]    Nhóm quyền user (owner, admin, manager, user). nhiều giá trị cách nhau ;
@apiParam   (Query:)  {string}  [module_name]   Quyền cho module (SALES, ..) nhiều giá trị cách nhau ;



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "email": "<EMAIL>",
      "fullname": "hongngoc",
      "id": "a1b99ef0-06bd-466b-a243-6816b979d409",
      "username": "smt01@hdb"
    }, 
    {
      "block": ["KHDN"],
      "email": "<EMAIL>",
      "fullname": "thanhnt02",
      "id": "e80f7af8-e3ca-432f-96bd-b7f063bf7f81",
      "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
      "scope_code": "4#8",
      "username": "thanhnt02@hdb"
    }
  ],
  "paging": {
    "page": 1,
    "per_page": 5,
    "total_count": 220,
    "total_page": 44
  }
}
"""


"""
@api {GET} /api/v2.1/accounts/scope-parent Lấy danh sách nhân viên theo nhóm quyền tự tạo có mã cấp cao hơn  
@apiDescription path cho mobile: adm/mobile/api/v2.1/accounts/scope-parent
@apiVersion 2.1.0
@apiGroup Account

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiName GetListAccountScopeParent  
@apiParam   (Query:)  {string}  [search]    Từ khóa cần tìm kiếm (Tìm kiếm theo fullname và username)
@apiParam   (Query:)  {string}  role_name   Tên nhóm quyền tự tạo (nhiều giá trị cách nhau ;)



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "email": "<EMAIL>",
      "fullname": "hongngoc",
      "id": "a1b99ef0-06bd-466b-a243-6816b979d409",
      "username": "smt01@hdb"
    }, 
    {
      "block": ["KHDN"],
      "email": "<EMAIL>",
      "fullname": "thanhnt02",
      "id": "e80f7af8-e3ca-432f-96bd-b7f063bf7f81",
      "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
      "scope_code": "4#8",
      "username": "thanhnt02@hdb"
    }
  ]
}
"""

"""
@api {get} /api/v2.1/accounts/module/paging Lấy danh sách nhân viên có quyền vào module 
@apiDescription Thông tin ngắn gọn của nhân viên có phân trang
@apiVersion 1.0.2
@apiGroup Account
@apiName GetAccountHasModule

@apiParam   (Query:)  {string}  [search]    Từ khóa cần tìm kiếm (Tìm kiếm theo fullname và username)
@apiParam   (Query:)  {string}  module_names   Tên module: SALES, ... (nhiều giá trị cách nhau ,)
@apiParam   (Query:)  {String}  [select_field]  Field lựa chọn để trả về, nhiều giá trị cách nhau dấu "," (VD: id,username,fullname)
@apiUse status_query_params

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header

@apiSuccess  {String} merchant_id ID nhãn hàng lấy danh sách nhân viên
@apiSuccess (data) {String} id ID tài khoản nhân viên
@apiSuccess (data) {String} username Tên truy cập nhân viên
@apiSuccess (data) {String} fullname Tên dầy đủ nhân viên
@apiSuccess (data) {String} email email nhân viên
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
      "username": "Locnh",
      "fullname": "Nguyễn Hữu Lộc",
      "email": "",
    },
    ...
  ]
}
"""

"""
@api {GET} /api/v2.1/accounts/call-report/dvkd  Danh sach account cho hdb_dvkd
@apiDescription path cho mobile: adm/mobile/api/v2.1/accounts/call-report/dvkd
@apiVersion 2.1.0
@apiGroup Account
@apiName GetListAccountForHDB_DVDK

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam   (Query:)  {string}  team_id     ID của team muốn tìm kiếm
@apiParam   (Query:)  {string}  role_name   Tên nhóm quyền tự tạo (nhiều giá trị cách nhau ";")
@apiParam   (Query:)  {string}  [search]      Từ khóa muốn tìm kiếm 



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "MaChucDanhMoiNhat": 2,
            "TenChucDanhMoiNhat": null,
            "block": [
                "KHDN",
                "KHCN",
                "NHBH"
            ],
            "email": "<EMAIL>",
            "fullname": "a4nv",
            "id": "423e0be3-3efa-4097-9fa0-9c979752f2b8",
            "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
            "scope_code": "1#111",
            "username": "a4nv@hdb"
        },
        {
            "email": "<EMAIL>",
            "fullname": "Hiền SMT04",
            "id": "c158fe48-abe8-4ae0-addd-3489f88e9844",
            "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
            "scope_code": "1#111#235",
            "username": "hienlt4@hdb"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} /api/v2.1/accounts/call-report/hs Danh sách account cho hdb hội sở
@apiDescription path cho mobile: adm/mobile/api/v2.1/accounts/call-report/hs
@apiVersion 2.1.0
@apiGroup Account
@apiName GetListAccountForHDB_HS

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)  {string}  [search]    Từ khóa cần tìm kiếm (Tìm kiếm theo fullname và username)
@apiParam   (Query:)  {string}  role_name   Tên nhóm quyền tự tạo (nhiều giá trị cách nhau ";")
@apiParam   (Query:)  {string}  [role_group]    Nhóm quyền user (owner, admin, manager, user). nhiều giá trị cách nhau ";"
@apiParam   (Query:)  {string}  [module_name]   Quyền cho module (SALES, ..) nhiều giá trị cách nhau ";"



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "block": ["KHDN"],
      "email": "<EMAIL>",
      "fullname": "thanhnt02",
      "id": "e80f7af8-e3ca-432f-96bd-b7f063bf7f81",
      "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
      "scope_code": "4#8",
      "username": "thanhnt02@hdb"
    }
  ]
}
"""

"""
@api {get} /api/v2.1/account/detail Lấy thông tin cơ bản nhân viên
@apiDescription thông tin cơ bản của nhân viên 
@apiVersion 2.0.0
@apiGroup Account
@apiName GetDetailBaseAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)  {string}  account_id   id account 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": {
    "avatar": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/647879d7491cda83ac648412.jpg",
    "email": "<EMAIL>",
    "fullname": "Admin pingcomshop",
    "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "phone_number": "+***********",
    "role_group": "owner",
    "staff_code": "",
    "username": "admin@pingcomshop"
  },
  "code": 200,
  "message": "request thành công.",
  "lang": "vi", 
}
"""

"""
@api {post} /api/v2.1/accounts/by-scope-code Lấy danh sách account theo mã cấp 
@apiDescription trả về tất cả account có mã cấp bắt đầu hoặc bằng mã cấp yêu cầu 
@apiVersion 1.0.0
@apiGroup Account
@apiName GetAccountsByScopeCode  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {array} scope_codes Danh sách mã cấp 

@apiParamExample {json} Body
{
  "scope_codes":["AREA_1"]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
          "avatar": null,
          "email": "<EMAIL>",
          "fullname": "h",
          "id": "33d8401a-4096-48a3-9bf6-3a85fa52298e",
          "phone_number": "+***********",
          "role_group": "user",
          "scope_code": "AREA_1##3204",
          "staff_code": null,
          "username": "mienbac_user3@msb"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {get} /api/v2.1/account/detail-extra Lấy thông tin mở rộng của nhân viên 
@apiDescription thông tin mở rộng của nhân viên 
@apiVersion 1.0.0
@apiGroup Account
@apiName GetDetailExtraAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Query:)     {String}      account_id  id tài khoản 
@apiParam      (Query:)     {String}      [select_field]  các field cần lấy thêm thông tin cách nhau dấu , VD: group_business,leader_group_business


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": {
    "avatar": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/647879d7491cda83ac648412.jpg",
    "email": "<EMAIL>",
    "fullname": "Admin pingcomshop",
    "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "phone_number": "+***********",
    "role_group": "owner",
    "staff_code": "",
    "username": "admin@pingcomshop",
    "block": ["KHCN"]
    "scope_code": ""
    "MaChucDanhMoiNhat": ""
    "TenChucDanhMoiNhat": ""
    "area": ""
    "area_code": ""       // khu vực 
    "sol_id": ""            // đơn vị kinh doanh 
    "sol_name": ""
    "department_ids": [""]       // phòng 
    "block_mapping": [
        {
            "name": "Khách hàng cá nhân",
            "value": "KHCN"
        }
    ],
    "department_mapping": [
        {
            "name": "CBP_1001 - Phòng Khách hàng doanh nghiệp",
            "value": "CBP_1001"
        }
    ],
    "tier_id": ""
    "tier_name": ""
    "group_position_id": "RBO"
    "group_position_name": "",
    "group_business": [{
      "id": "",
      "name": "",
    }]
    "leader_group_business": [{
      "email": "<EMAIL>",
      "fullname": "Admin pingcomshop",
      "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "username": "admin@pingcomshop",
    }]
  },
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {post} /api/v2.1/accounts/filter-field-extra Lấy danh sách account lọc theo field mở rộng 
@apiDescription trả về phân trang account theo bộ lọc
@apiVersion 1.0.0
@apiGroup Account
@apiName GetAccountsFilterFieldExtra  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {array} [scope_codes] Danh sách mã cấp, lấy tất cả account bắt đầu bằng mã cấp này
@apiParam  {array} [tier_ids] Danh sách tier  
@apiParam  {array} [sol_ids] Danh sách sol id, dvkd  
@apiParam  {array} [group_position_ids] Danh sách nhóm chức danh  
@apiParam  {array} [position_ids] Danh sách chức danh  
@apiParam  {array} [area_ids] Danh sách khu vực  
@apiParam  {array} [department_ids] Danh sách phòng 
@apiParam  {number} [page] số trang, page=-1 lấy tất cả  
@apiParam  {number} [per_page] số lượng phần tử 1 trang   


@apiParamExample {json} Body
{
  "scope_codes":["AREA_1"],
  "tier_ids": ["Tier 01"],
  "sol_ids": ["1000"],
  "group_position_ids": [],
  "position_ids": [],
  "area_ids": [],
  "department_ids": [],
  "page": 1,
  "per_page": 10,
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
          "account_id": "33d8401a-4096-48a3-9bf6-3a85fa52298e",
          "scope_code": "AREA_1##3204",
          "block": ["KHCN"]
          "MaChucDanhMoiNhat": ""
          "TenChucDanhMoiNhat": ""
          "area": ""
          "area_code": ""       // khu vực 
          "sol_id": ""            // đơn vị kinh doanh 
          "sol_name": ""
          "department_ids": [""]       // phòng 
          "tier_id": ""
          "tier_name": ""
          "group_position_id": "RBO"
          "group_position_name": ""
        },
    ],
    "paging": {
          "page": 1,
          "per_page": 10,
          "page_count": 1,
          "total_count": 20,
      },
    "lang": "vi",
    "message": "request thành công."
}
"""



"""
@api {post} /api/v2.1/accounts/role-name Lấy thông tin quyền của account  
@apiDescription group user sẽ là quyền tự tạo, group khác là quyền mặc định 
@apiVersion 1.0.0
@apiGroup Account
@apiName GetRoleInfoAccount  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {array} account_ids Danh sách id tài khoản 

@apiParamExample {json} Body
{
  "account_ids":["33d8401a-4096-48a3-9bf6-3a85fa52298e"]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
          "account_id": "33d8401a-4096-48a3-9bf6-3a85fa52298e",
          "role_name": ["SMT05"],
          "role_group": "user"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""



"""
@api {get} /api/v2.1/account/be-managed EIB Lấy danh sách nhân viên cấp dưới   
@apiDescription gửi lên thông tin 1 tài khoản trả về danh sách tài khoản cấp dưới của tài khoản đó, dữ liệu theo lịch sử 
@apiVersion 1.0.0
@apiGroup Account
@apiName GetListAccountBeManaged

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Query:)     {String}      account_id  id tài khoản 
@apiParam      (Query:)     {String}      report_date  ngày xem dữ liệu định dạng (%Y-%m-%d) 
@apiParam      (Query:)     {String}      [status]  trạng thái tài khoản cần lấy, rỗng trả về cả tài khoản bị xóa, status=active trả về tài khoản đang hoạt động 
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "email": "<EMAIL>",
      "fullname": "Admin pingcomshop",
      "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "username": "admin@pingcomshop",
    }
  ],
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {get} /api/v2.1/account/history/detail-extra Lấy thông tin mở rộng của nhân viên theo thời gian 
@apiDescription nếu ngày cần lấy thông tin ko có thì lấy ngày gần nhất hướng quá khứ 
@apiVersion 1.0.0
@apiGroup Account
@apiName GetDetailExtraAccountHistory 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Query:)     {String}      account_id  id tài khoản 
@apiParam      (Query:)     {String}      report_date  ngày xem dữ liệu định dạng (%Y-%m-%d) 
@apiParam      (Query:)     {String}      [select_field]  các field cần lấy thêm thông tin cách nhau dấu , VD: group_business,leader_group_business


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": {
    "avatar": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/647879d7491cda83ac648412.jpg",
    "email": "<EMAIL>",
    "fullname": "Admin pingcomshop",
    "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "phone_number": "+***********",
    "role_group": "owner",
    "staff_code": "",
    "username": "admin@pingcomshop",
    "block": ["KHCN"]
    "scope_code": ""
    "MaChucDanhMoiNhat": ""
    "TenChucDanhMoiNhat": ""
    "area": ""
    "area_code": ""       // khu vực 
    "sol_id": ""            // đơn vị kinh doanh 
    "sol_name": ""
    "department_ids": [""]       // phòng 
    "block_mapping": [
        {
            "name": "Khách hàng cá nhân",
            "value": "KHCN"
        }
    ],
    "department_mapping": [
        {
            "name": "CBP_1001 - Phòng Khách hàng doanh nghiệp",
            "value": "CBP_1001"
        }
    ],
    "tier_id": ""
    "tier_name": ""
    "group_position_id": "RBO"
    "group_position_name": "",
    "group_business": [{
      "id": "",
      "name": "",
    }]
    "leader_group_business": [{
      "email": "<EMAIL>",
      "fullname": "Admin pingcomshop",
      "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "username": "admin@pingcomshop",
    }]
  },
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""

"""
@api {post} /api/v2.1/account/detail Lấy chi tiết nhân viên theo account_id
@apiDescription Lấy thông tin chi tiết nhân viên theo account_id
@apiVersion 1.0.0
@apiGroup Account
@apiName DetailAccountByID

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {String}      account_ids  Danh sách account id

@apiSuccess (data) {String} [push_ids] Danh sách push id
@apiSuccess (data) {String} [push_ids.push_id] Mã app mobile dùng để push notify đến app
@apiSuccess (data) {String} [push_ids.device_operating_system] Hệ điều hành của app mobile
@apiSuccess (data) {String} [push_ids.device_id] id thiết bị
@apiSuccess (data) {Bool} [push_ids.sandbox] true/false , <code>true: dev, false: product, None: không xác định</code>
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "account_id: "07873e41-4016-4cf6-86ed-047ebd462fc1"
            "push_ids": [
                {
                    "device_id": "*********-**********-34304830940384uf-09t04594",
                    "device_operating_system": "ios",
                    "push_id": "4343-3453fedf-dfdfde0f9034e-df0d9fd0fdf-3ere32422-wefiefieif3-2342432rowokow",
                    "sandbox": true
                }
            ]
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {post} /api/v2.1/accounts/eib/lv5-by-group-position EIB Lấy danh sách nhân viên lv5 
@apiDescription gửi lên thông tin 1 tài khoản trả về danh sách tài khoản cấp dưới thuộc lv5 cùng đơn vị của tài khoản đó, dữ liệu theo lịch sử 
@apiVersion 1.0.0
@apiGroup Account
@apiName GetListAccountLv5

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {String}      account_id  id tài khoản đang đăng nhập
@apiParam      (Body:)     {String}      report_date  ngày xem dữ liệu định dạng (%Y-%m-%d) 
@apiParam      (Body:)     {Array}      [group_position_ids]  danh sách nhóm chức danh(bộ lọc của level 3)

@apiParamExample {json} Body
{
  "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "group_position_ids": [],
  "report_date": "2023-12-25"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "email": "<EMAIL>",
      "fullname": "Admin pingcomshop",
      "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "username": "admin@pingcomshop",
    }
  ],
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {post} /api/v2.1/accounts/eib/lv6-by-group-position EIB Lấy danh sách nhân viên lv6 từ level cao hơn    
@apiDescription gửi lên thông tin tài khoản đang đăng nhập trả về danh sách tài khoản lv6, dữ liệu theo lịch sử 
@apiVersion 1.0.0
@apiGroup Account
@apiName GetListAccountLv6ByOtherLevel 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {String}      account_id  id tài khoản đang đăng nhập
@apiParam      (Body:)     {String}      report_date  ngày xem dữ liệu định dạng (%Y-%m-%d) 
@apiParam      (Body:)     {Array}      [group_position_ids]  danh sách nhóm chức danh
@apiParam      (Body:)     {String}      [find_type]  loại tìm kiếm: no_group (lấy nhân viên lv6 ko có quản lý)
@apiParam      (Body:)     {Array}      [area_ids]  danh sách mã khu vực 
@apiParam      (Body:)     {Array}      [sol_ids]  danh sách ĐVKD

@apiParamExample {json} Body
{
  "account_ids":["7fc0a33c-baf5-11e7-a7c2-0242ac180003"],
  "report_date": "2023-12-25",
  "group_position_ids": ["KHCN"],
  "area_ids": [],
  "sol_ids": [],
  "find_type": "",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "email": "<EMAIL>",
      "fullname": "Admin pingcomshop",
      "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "username": "admin@pingcomshop",
      "manager_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    }
  ],
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {post} /api/v2.1/accounts/eib/lv6-only EIB Lấy danh sách lv6 từ bộ lọc 
@apiDescription gửi lên thông tin tài khoản đang đăng nhập trả về danh sách tài khoản lv6, dữ liệu theo lịch sử, không có thông tin quản lý 
@apiVersion 1.0.0
@apiGroup Account
@apiName GetListAccountLv6Only  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {String}      account_id  id tài khoản đang đăng nhập
@apiParam      (Body:)     {String}      report_date  ngày xem dữ liệu định dạng (%Y-%m-%d) 
@apiParam      (Body:)     {Array}      [group_position_ids]  danh sách nhóm chức danh
@apiParam      (Body:)     {Array}      [area_ids]  danh sách mã khu vực 
@apiParam      (Body:)     {Array}      [sol_ids]  danh sách ĐVKD

@apiParamExample {json} Body
{
  "account_ids":["7fc0a33c-baf5-11e7-a7c2-0242ac180003"],
  "report_date": "2023-12-25",
  "group_position_ids": ["KHCN"],
  "area_ids": [],
  "sol_ids": [],
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "email": "<EMAIL>",
      "fullname": "Admin pingcomshop",
      "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "username": "admin@pingcomshop",
      "manager_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    }
  ],
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""



"""
@api {post} /api/v2.1/accounts/eib/count-position EIB Số lượng nhân viên đang quản lý theo nhóm chức danh  
@apiDescription gửi lên thông tin từ bảng quản lý  
@apiVersion 1.0.0
@apiGroup Account
@apiName GetCountUserByPosition  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {String}      account_view  id tài khoản đang xem báo cáo (tài khoản đang login) 
@apiParam      (Body:)     {Array}      [account_ids]  danh sách id tài khoản level 5
@apiParam      (Body:)     {String}      report_date  ngày xem dữ liệu định dạng (%Y-%m-%d) 
@apiParam      (Body:)     {Array}      [group_position_ids]  danh sách nhóm chức danh(bộ lọc của level 1,2,3)
@apiParam      (Body:)     {Array}      [area_ids]  danh sách mã khu vực (bộ lọc của level 1,2)
@apiParam      (Body:)     {Array}      [sol_ids]  danh sách ĐVKD(bộ lọc của level 1,2)

@apiParamExample {json} Body
{
  "account_view": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "group_position_ids": [],
  "account_ids": [],
  "report_date": "2023-12-25",
  "area_ids": [],
  "sol_ids": [],
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "group": "SRM",
      "count": 10,
    },
    {
      "group": "RM",
      "count": 100,
    },
  ],
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""

"""
@api {post} /api/v2.1/accounts/eib/lv56-by-group-position EIB Lấy danh sách nhân viên lv5-6 từ level cao hơn    
@apiDescription gửi lên thông tin tài khoản đang đăng nhập trả về danh sách tài khoản lv5-6, dữ liệu theo lịch sử 
@apiVersion 1.0.0
@apiGroup Account
@apiName GetListAccountLv56ByOtherLevel 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {String}      account_view  id tài khoản đang đăng nhập
@apiParam      (Body:)     {String}      report_date  ngày xem dữ liệu định dạng (%Y-%m-%d) 
@apiParam      (Body:)     {Array}      [account_ids]  danh sách id tài khoản level 5
@apiParam      (Body:)     {Array}      [group_position_ids]  danh sách nhóm chức danh(bộ lọc của level 3)
@apiParam      (Body:)     {Array}      [sol_ids]  danh sách đơn vị kinh doanh 
@apiParam      (Body:)     {Array}      [area_ids]  danh sách khu vực 
@apiParam      (Body:)     {Array}      [department_ids]  danh sách phòng ban
@apiParam      (Body:)     {Boolean}      [report_year]  lấy thông tin cả năm hay không 
@apiParam      (Body:)     {Array}      [group_position_exclude]  danh sách nhóm không trả về ["RBO"]
@apiParam      (Body:)     {String}      [full_group]  nếu group_position_ids có lv6 nào thì lấy thêm lv5 của group đó (truyền lên full_group=1) 


@apiParamExample {json} Body
{
  "account_ids":["7fc0a33c-baf5-11e7-a7c2-0242ac180003"],
  "report_date": "2023-12-25",
  "group_position_ids": ["KHCN"],
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "email": "<EMAIL>",
      "fullname": "Admin pingcomshop",
      "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "username": "admin@pingcomshop",
      "manager_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "level": "level_6"    // level_6, level_5
    }
  ],
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""

# ******************************** API nhập tài khoản quên mật khẩu *************************************
"""
@api {post} /api/v2.1/account/forget-password Nhập tài khoản quên mật khẩu 
@apiDescription account forget password
@apiVersion 2.1.0
@apiGroup Account v2
@apiName account_forget_password

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      username                         Tài khoản muốn lấy lại mật khẩu 


@apiParamExample {json} Info example
{
  "username": "Locnh",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    // case 1

    "data": {
        "result_code": "success"
         "username": "Locnd",
        "email": "qu************",
        "time_left": 120 Thời gian mã hết hạn 
    }
    
    // case 2
    "data": {
             "result_code": "email_invalid",   // ko có email 
    } 
     
     // case 3  
        "data": {
            "result_code": "no_ready_sending",   // chưa tới thời gian gửi lại mã  
            "time_left":    20              // thời gian còn lại mở khóa 
            "time_lock": 1800 ,                      // Thời gian khóa 
        }
    
    // case 4 
    
    "data": {
            "result_code": "block_sending",   // khóa gửi lại mã  
            "time_left":    20              // thời gian còn lại mở khóa 
            "time_lock": 1800 ,                      // Thời gian khóa 
    }
    
    // case 5 
    "data": {
          "username": "Locnd",
        "email": "qu************"
        "result_code": "not_verify_email" /// Tài khoản chưa được xác minh email chuyển sang màn hình xác minh email 
    },
    // case 6
     "data": {
        "result_code": "need_admin_change_email" //  email đã được verfy bới account khác thông báo lỗi và không được phép verify email,
        "email": "te**********"
    },
    
    // regex validate username : ^[^A-Z][\w@.-]{4,64}.*@.*$
}
"""

# ******************************** API Nhập mã xác minh quên mật khẩu *************************************
"""
@api {post} /api/v2.1/account/forget-password/verify  Nhập mã xác minh quên mật  khẩu
@apiDescription verify code forget password
@apiVersion 2.1.0
@apiGroup Account v2
@apiName verify_code_forget_password

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      username                         Tài khoản muốn lấy lại mật khẩu 
@apiParam      (Body:)     {String}      verify_code                      Mã xác minh vừa nhập 


@apiParamExample {json} Info example
{
  "username": "Locnh",
  "verify_code": "1235654",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  // case1 
    "data": {
        "username": "Locnd",
        "verify_code": "123233",
        "result_code": "success"
    }
    // case 2
     "data": {
            "result_code": "lock_account",
            "time_lock": 1800 ,                      // Khóa nhập mã xác thực vì nhập quá số lần cho phép
            "time_left": 1800, // Thời gian đếm ngược 
        }
    // case 3
     "data": {
            "result_code": "verify_code_wrong",
            "number_wrong": 3                       // User nhập sai mã xác thực nhưng chưa quá số lần cho phép nhập sai 
    }
"""

# ******************************** API Nhập mật khẩu mới phần quên mật khẩu *************************************
"""
@api {PUT} /api/v2.1/account/forget-password/change-password Thay đổi mật khẩu mới quên mật khẩu 
@apiDescription change password forget password
@apiVersion 2.1.0
@apiGroup Account v2
@apiName change_password_forget_password

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      username                         Tài khoản muốn lấy lại mật khẩu 
@apiParam      (Body:)     {String}      verify_code                      Mã xác minh vừa nhập 
@apiParam      (Body:)     {String}      password_new                     Mật khẩu mong muốn thay đổi 



@apiParamExample {json} Info example
{
  "username": "Locnh",
  "verify_code": "1235654",
  "password_new": "Mobio123"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200
}
"""

# ***************************** API Gửi mã xác minh để xác minh email ****************************
"""
@api {post} /api/v2.1/account/verify-email/send-mail Gửi mã xác thực để xác minh email
@apiDescription send  code to verify email
@apiVersion 2.1.0
@apiGroup Account v2
@apiName send_code_to_verify_email

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      username                        Tên tài khoản muốn xác thực email
@apiParam      (Body:)     {String}      password                        Mật khẩu tài khoản muốn xác thực email
@apiParam      (Body:)     {String}      category_email                 Loại email muốn gửi [no-change: email hiện tại của tài khoản đó nếu có , other: Chọn nhập email khác để gửi mã ]
@apiParam      (Body:)     {String}      email                          Email muốn gửi mã xác minh tồn tại khi category_email = other

@apiSuccess {string}            username                                Tên tài khoản
@apiSuccess {string}            password                                Mật khẩu
@apiSuccess {string}            email                                   Email muốn gửi mã xác minh tồn tại khi category_email = other
@apiSuccess {string}            category_email                          Loại email muốn gửi [no_change: email hiện tại của tài khoản đó nếu có , other: Chọn nhập email khác để gửi mã ]

@apiParamExample {json} Info example
{
  "username": "Locnh",
  "password": "Mobio123",
  "category_email": "other",
  "email": "<EMAIL>"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200
    // case 1
            "data": {
              "result_code": "success",
                "username": username,
                "password": password,
                "email": convert_email,              // Gửi mã thành công 
                 "time_left": 1800, // Thời gian đếm ngược mã có hiệu nghiệm 
            }
    
    // case 2
         "code": 200,
                "data": {
                    "result_code": "no_ready_sending",
                    "time_left": 120              // Thời gian đếm ngược        // Chưa đến thời gian gửi lại mã 
                    "time_lock": 120            // Thời gian bị khóa 
                }
                
    // case 3 
      "code": 200,
                "data": {
                    "result_code": "block_sending",
                    "time_left": 1800                   // Thời gian đếm ngược      // Bị khóa gửi mã vì gửi quá số lần cho phép trong 1 ggiờ 
                    "time_lock": 1900                   // Thời gian bị khóa 
                }
    // case 4:
            {
                    "code": 200,
                    "data": {
                    "result_code": "not_email"   // Không có email
            }
    // case 5:
    //   "code": 200,
                "data": {
                    "result_code": "email_invalid"      // email đã xác minh bởi tài khoản khác 
                }
                
}
"""



# ***************************** API Kiểm tra mã xác thực để xác minh email ****************************
"""
@api {post} /api/v2.1/account/verify-email/check-code  Kiểm tra mã xác thực để xác minh email
@apiDescription check code to verify email
@apiVersion 2.1.0
@apiGroup Account v2
@apiName check_code_to_verify_email

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      username                        Tên tài khoản muốn xác thực email
@apiParam      (Body:)     {String}      password                        Mật khẩu tài khoản muốn xác thực
@apiParam      (Body:)     {String}      verify_code                      Mã xác thực

@apiSuccess {String}            status                               Trạng thái sử lý  xác thực tài khoản [success: xác thực thành công, lock_account: Tài khoản bị khóa ]
@apiSuccess {int}            time_lock                               Thời điểm mở khóa tài khoản chỉ áp dụng cho status = lock_account

@apiParamExample {json} Info example
{
  "username": "Locnh",
  "password": "Mobio123",
  "verify_code": "222222",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200
    //case 1
    "data": {
            "result_code": "lock_account",
            "time_lock":           //Thời gian bị khóa                  // Khóa account khi nhập quá số lần cho phép 
            "time_left": 1800                       // Thời gian đếm ngược 
        }
    
    // case 2
    "data": {
             "result_code": "verify_code_wrong",
            "number_wrong": 1                   // Số lần nhập mã xác minh còn lại  
    }
    
    // case 3
    "data": {
                "result_code": "success"             // Nhập mã xác minh thành công 
            }
}
"""


# ***************************** Lấy cầu hình mật khẩu theo username   ****************************
"""
@api {post} /api/v2.1/account/pattern/username  Lấy cấu hình mật khẩu theo username  
@apiDescription get pattern by username
@apiVersion 2.1.0
@apiGroup Account v2
@apiName get_pattern_by_username

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      username                     Tên tài khoản 


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "password": {
      "contain": {
        "key": "least",
        "number": 2,
        "symbol": 3,
        "uppercase": 1,
        "value": "2"
      },
      "length_max": "15",
      "length_min": "4"
    },
    "pattern_password": "Xig/PSg/Oi4qW0EtWl0uKil7MSx9KSg/PSg/Oi4qWzAtOV0uKil7Mix9KSguezAsMTV9JCl8KD89KD86LipbQS1aXS4qKXsxLH0pKD89KD86LipbXmEtekEtWjAtOV0uKil7Myx9KSguezAsMTV9JCl8KD89KD86LipbMC05XS4qKXsyLH0pKD89KD86LipbXmEtekEtWjAtOV0uKil7Myx9KSguezAsMTV9JCkuKiQ=",
    "pattern_username": "Xig/PSg/Oi4qW0EtWl0uKil7MSx9KSg/PSg/Oi4qW2Etel0uKil7MSx9KSg/PSg/Oi4qWzAtOV0uKil7MSx9KSguezAsMjd9JCkuKiQ=",
    "username": {
      "contain": {
        "key": "least",
        "lowercase": 1,
        "number": 1,
        "uppercase": 1,
        "value": "3"
      },
      "length_max": "27",
      "length_min": "4"
    }
  },
  "lang": "vi",
  "message": "request thành công."
}

"""


# ***************************** API gửi mã xác minh cho phần xác minh email tài khoản quên mật khẩu  ****************************
"""
@api {post} /api/v2.1/forget-password/send-code/verify-email  Gửi mã xác minh email cho tài khoản khi sử dụng chức năng quên mật khẩu 
@apiDescription send verify code from forget password
@apiVersion 2.1.0
@apiGroup Account v2
@apiName send_verify_code_from_forget_password 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      username                  Tên tài khoản 



@apiParamExample {json} Info example
{
    "username": "<EMAIL>"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
      // case 1
    "data": {
          "username": "Locnd",          // Gửi mail thành công 
        "email": "qu************",
        "result_code": "success"
        "time_left": 120        Thời gian mã có hiệu lực 
    }
    
    // case 2
     "data": {
        "result_code": "email_invalid",   // ko có email hoặc email chưa verify , email đã được verfy bới account khác 
     } 
     
     // case 3  
       "data": {
                 "result_code": "no_ready_sending",   // chưa tới thời gian gửi lại mã  
                "time_left":    20              // thời gian còn lại mở khóa 
                "time_lock": 20         Thời gian bị khóa 
       }
    
    // case 4 
        "data": {
                "result_code": "block_sending",   // khóa gửi lại mã  
                "time_left":    20              // thời gian còn lại mở khóa 
                "time_lock": 20         Thời gian bị khóa 
        }
        
    // case 5 
    "data": {
          "username": "Locnd",
        "email": "qu************";
         "result_code": "not_verify_email" /// Tài khoản chưa được xác minh email chuyển sang màn hình xác minh email  
    },

    
}
"""

# ***************************** Kiểm tra mã khi xác minh email tài khoản ở giao diện quên mật khẩu   ****************************
"""
@api {post} /api/v2.1/forget-password/check-code/verify-email Kiểm tra mã khi xác minh email tài khoản quên mật khẩu 
@apiDescription check code verify email from forget password
@apiVersion 2.1.0
@apiGroup Account v2
@apiName check_code_verify_email_from_forget_password

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      username                  Tên tài khoản 



@apiParamExample {json} Info example
{
    "username": "<EMAIL>",
    "verify_code": "111111"

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
      // case 1
     "data": {
                    "result_code": "lock_account",
                    "time_lock": 21             // Tài khoản bị khóa do nhập quá số lần sai cho phép 
                    "time_left": 21 Thời gian khóa còn hiệu lực trong vòng bao lâu 
                }
    
    // case 2
      "data": {
                    "result_code": "email_invalid"      //  Tài khoản không có email hoặc email đã được một tài khoản khác xác minh trước đó 
                }
    // case 3
     "data": {
                    "result_code": "expire_code"          // Mã xác thực hết hạn hoặc chưa gửi mã xác minh về email 
                }
    
    //case 4
      "data": {
                    "result_code": "verify_code_wrong",
                    "number_wrong": 1                       // Người dùng nhập sai mã xác thực nhưng chưa quá số lần cho phép number_wrong số lần còn lại được nhập sai 
                }
    //case 5
     "data": {
                "result_code": "success"                // Nhập mã xác minh thành công
            }
}
"""

# ***************************** Kiểm tra email đã tồn tại trong hệ thống  ****************************
"""
@api {post} /api/v2.1/account/check-email Kiểm tra email đã tồn tại trong hệ thống 
@apiDescription   check email  address exits 
@apiVersion 2.1.0
@apiGroup Account v2
@apiName check_email_address_exits

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      email                 Email cần kiềm tra 



@apiParamExample {json} Info example
{
    "email": "<EMAIL>"
 }

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200
}
"""

# ------------------------ Danh sách Account V2------------------------------------------
"""
@api {POST} /api/v2.1/accounts/filter Danh sách account theo bộ lọc v2 
@apiDescription Danh sách Account theo bộ lọc v2
@apiVersion 2.1.0
@apiGroup Account_v2
@apiName ListAccountV2
@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      [merchant_id]                  ID doanh nghiệp cần tìm kiếm account
@apiParam      (Body:)     {Array}       [role_group]                   Nhóm quyền [admin,manager,owner,user]
@apiParam      (Body:)     {int}         [is_online]                    Tài khoản có đang online (Đăng nhập) trên hệ thống hay không
                                                                        <li><code>1</code>: Đang online</li>
                                                                        <li><code>0</code>: Đang offline</li>
                                                                        <li>Chọn cả 2 thì không gửi lên</li>
@apiParam      (Body:)     {String}      [search]                       Từ khóa tìm kiếm thoe [họ và tên, tên tài khoản, email, số điện thoại]
@apiParam      (Body:)     {String}      [sort]                         Field cần sắp xếp [username, fullname, last_login_date ] Nếu không gửi lên backend mặc định sắp xếp theo update_time
@apiParam      (Body:)     {String}      [order]                        Thứ tự cần sắp xếp [desc, asc] asc: tăng dần, desc: giảm dần
@apiParam      (Body:)     {String}      [since_login]                  2024-12-91T00:00:00  Định dạng %Y-%m-%dT%H:%M:%S thời gian bắt đầu lọc thời gian đăng nhập gần nhất
@apiParam      (Body:)     {String}      [until_login]                  2024-12-91T00:00:00  Định dạng %Y-%m-%dT%H:%M:%S thời gian kết thúc lọc thời gian đăng nhập gần nhất
@apiParam      (Body:)     {Object}      [teams]                        Thông tin query team
@apiParam      (Body:)     {String}      [teams.status_query  ]         [not_team, have_team] not_team: tìm kiếm account không nằm trong team nào, have_team: Tìm kiếm tài khoản có team
@apiParam      (Body:)     {List}        [teams.team_ids]               team_id chỉ áp dụng cho trường hợp status_query = have_team
@apiParam      (Body:)     {Array}       [status]                       Trạng thái của tài khoản
                                                                        <li><code>1</code>: Đang hoạt động</li>
                                                                        <li><code>4</code>: Đóng</li>
                                                                        <li><code>-1</code>: Đang vắng mặt</li>
@apiParam      (query:)    {int}         [page]                         Số trang
@apiParam      (query:)    {int}         [per_page]                     Số phần tử trong trang


@apiSuccess     {String}      username                             Tên tài khoản
@apiSuccess     {String}      fullname                             Họ tên người dùng
@apiSuccess     {String}      role_group                           Nhóm quyền    ["user", "manager", "admin", "owner"]
@apiSuccess     {Object}      field_extra                          Khối thông tin nhân sự
@apiSuccess     {List}        modules                              Quyền
@apiSuccess     {List}        policies                             Chính sách truy cập (Danh sách policies ID)
@apiSuccess     {String}      phone_number                         Số điện thoại
@apiSuccess     {String}      email                                Email
@apiSuccess     {String}      teams                                Team
@apiSuccess     {String}      staff_code                           Mã nhân viên
@apiSuccess     {String}      created_time                         Thời gian tạo
@apiSuccess     {String}      last_login_date                      Thời gian đăng nhập gần nhất
@apiSuccess     {String}      last_device_login                    Kênh đăng nhập gần nhất
@apiSuccess     {String}      email_verify                         [is_verified, not_verified] 1 trong 2 giá trị not_verified: chưa xác minh email, is_verified: Đã xác minh
@apiSuccess     {Int}         status                               Trạng thái tài khoản
@apiSuccess     {String}      status_custom                        Trạng thái làm việc của tài khoản
                                                                   <li><code>on_leave</code>: Đang vắng mặt</li>
    

@apiSuccess   (field_extra)     {list}          block_mapping         Danh sách khối
@apiSuccess   (field_extra)     {String}          block_mapping.name        Tên khối dùng để hiển thị
@apiSuccess   (field_extra)     {List}          group_department_mapping       Danh sách phòng ban
@apiSuccess   (field_extra)     {String}          group_department_mapping.name       Tên phòng ban dùng để hiển thị

@apiSuccess   (field_extra)     {String}       sol_id                           ID đơn vị kinh doanh
@apiSuccess   (field_extra)     {String}       sol_name                         Tên đơn vị kinh doanh
@apiSuccess   (field_extra)     {String}       MaChucDanhMoiNhat                Mã chức danh
@apiSuccess   (field_extra)     {String}       TenChucDanhMoiNhat               Tên chức danh

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    data: [
       {
            "avatar": null,
            "created_time": "2023-10-30T09:40:20Z",
            "email": "<EMAIL>",
            "field_extra": {
                "MaChucDanhMoiNhat": "CD110",
                "TenChucDanhMoiNhat": "RM cấp 02",
                "account_id": "********-e503-4a1e-94fb-118edd600620",
                "area": "KVMB",
                "area_code": "KVMB",
                "block": [
                    "KHCN"
                ],
                "block_mapping": [
                    {
                        "name": "Khách hàng cá nhân",            // Dùng field name trong danh sách object để hiển thị
                        "value": "KHCN"
                    }
                ],
                "date_of_joining": "10/11/2023",
                "date_off": "None",
                "date_on_month": "None",
                "department_mapping": [],
                "group_department_mapping": [], // Dùng field name trong danh sách object để hiển thị
                "group_position_id": "RM",
                "group_position_name": "RM",
                "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
                "scope_code": "KVMB#1001",
                "scope_name": "Chi nhánh Hà Nội",
                "sol_id": "1004",
                "sol_name": "Chi nhánh Chợ Lớn",
                "tier_id": "Tier 02",
                "tier_name": "Tier 02"
            },
            "fullname": "eosa2",
            "id": "********-e503-4a1e-94fb-118edd600620",
            "is_admin": 2,
            "is_online": 1,
            "last_device_login": null,
            "last_login_date": "2024-03-28T10:28:49Z",
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "modules": [
                {
                    "description": "Quản lý Media",
                    "id": "00083d7d-e10b-41df-9b19-9e7b1d909d0f",
                    "name": "MEDIA_STORE"  // Dùng field name trong danh sách object để hiển thị
                },
            ],
            "policies": {
                "merchant": [
                    "4bad1402-21f4-11ee-a17f-45311bd761af",
                    "4bad1405-21f4-11ee-a17f-45311bd761af"
                ],
                "role": [],
                "role_group": [],
                "team": [],
                "user": [
                    "40f45e07-ece6-11ee-84b4-e71278f8e76d",
                    "c2052960-eb1c-11ee-b488-e9c7a8fbfd6c",
                    "dd738dca-29d4-11ee-8cb9-0337691022ce"
                ]
            },
            "role_group": "admin",
            "staff_code": "100405228",
            "status": 1,
            "username": "eosa2@eib",
            "created_time": "2024-03-28T10:28:49Z",
            "last_device_login": "web",
            "teams": "Teamname,Teamtest",
            "email_verify": "is_verified",
            "status_custom": "on_leave",
        },
    ],
    "lang": "vi",
    "message": "request th\u00e0nh c\u00f4ng.",
    "paging": {
        "page": 1,
        "per_page": 30,
        "total_count": 6,
        "total_page": 1
    }
"""


"""
@api {post} /api/v2.1/account/export-account Export account theo bộ lọc 
@apiDescription   export account
@apiVersion 2.1.0
@apiGroup Account v2
@apiName export_account_by_filter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      [merchant_id]                   ID doanh nghiệp cần tìm kiếm account
@apiParam      (Body:)     {Array}      [role_group]                     Nhóm quyền [admin,manager,owner,user]
@apiParam      (Body:)     {int}      [is_online]                          1: là Đang hoạt động, 0: Không hoạt đông Chọn cả 2 thì không gửi lên
@apiParam      (Body:)     {String}      [search]                          Từ khóa tìm kiếm thoe [họ và tên, tên tài khoản, email, số điện thoại]
@apiParam      (Body:)     {String}      [sort]                            Field cần sắp xếp [username, fullname, last_login_date ] Nếu không gửi lên backend mặc định sắp xếp theo update_time
@apiParam      (Body:)     {String}      [order]                         Thứ tự cần sắp xếp [desc, asc] asc: tăng dần, desc: giảm dần
@apiParam      (Body:)     {String}      [since_login]                    12/01/2024  Định dạng %d/%m/%Y thời gian bắt đầu lọc thời gian đăng nhập gần nhất
@apiParam      (Body:)     {String}      [until_login]                      12/01/2024  Định dạng %d/%m/%Y thời gian kết thúc lọc thời gian đăng nhập gần nhất
@apiParam      (Body:)     {Object}      [teams]                            Thông tin query team
@apiParam      (Body:)     {String}      [teams.status_query  ]                    [not_team, have_team] not_team: tìm kiếm account không nằm trong team nào, have_team: Tìm kiếm tài khoản có team
@apiParam      (Body:)     {List}     [ teams.team_ids]                            team_id chỉ áp dụng cho trường hợp status_query = have_team



@apiParamExample {json} Info example
{
    "email": "<EMAIL>"
 }

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200
    
    // case 1 Dữ liệu nhỏ hơn 1000 bản ghi
    data = {
        "type_export": 1
        "link_download": "j1h24uh12edasdasdaassada",
        "file_name": "test-",
    }
    
    // case 2 Dữ liệu lớn hơn 1000 bản ghi
    data = {
        "type_export": 2,
        "link_download": "",
        "file_name": "",
    }
    
    // case 3 Không có dữ liệu
    data = {
        "type_export": 3,
        "link_download": "",
        "file_name": "",
    }
    
}
"""


"""
@api {post} /api/v2.1/account/role/assign gán gỡ quyền cho account 
@apiDescription  trong chi tiết account, chỉ cho gán gỡ quyền với nhóm quyền manager, user 
@apiVersion 2.1.0
@apiGroup Account
@apiName AccountAssignRole

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      account_id                   id tài khoản 
@apiParam      (Body:)     {Array}      role_ids                     danh sách id quyền 
@apiParam      (Body:)     {String}      action                       hành động gán hay gỡ quyền: add, remove 


@apiParamExample {json} Info example
{
    "account_id": "40f45e07-ece6-11ee-84b4-e71278f8e76d",
    "role_ids": [
        "40f45e07-ece6-11ee-84b4-e71278f8e76d",
        "c2052960-eb1c-11ee-b488-e9c7a8fbfd6c",
        "dd738dca-29d4-11ee-8cb9-0337691022ce"
    ],
    "action": "add"
 }

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200
    
}
"""


"""
@api {get} /api/v2.1/account/role/detail xem thông tin quyền của account 
@apiDescription  thêm thông tin chức năng và chính sách abac áp dụng 
@apiVersion 2.1.0
@apiGroup Account
@apiName AccountDetailRole

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Query:)     {String}      account_id  id tài khoản 


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200
    "data": [
      {
        "id": "40f45e07-ece6-11ee-84b4-e71278f8e76d",
        "description": "",
        "name": "role name",
        "functions": [
          "c2052960-eb1c-11ee-b488-e9c7a8fbfd6c",
          "dd738dca-29d4-11ee-8cb9-0337691022ce"  
        ],
        "policies": [
          "c2052960-eb1c-11ee-b488-e9c7a8fbfd6c",
          "dd738dca-29d4-11ee-8cb9-0337691022ce"  
        ],
     }
    ]
}
"""

"""
@api {get} /api/v2.1/accounts/role-lower danh sách account sắp xếp theo nhóm quyền  
@apiDescription  lấy tất cả account 
@apiVersion 2.1.0
@apiGroup Account
@apiName AccountSortRoleGroup 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam      (Query:)     {String}      account_id  id tài khoản 
@apiParam      (Query:)     {String}      type_filter  loại bộ lọc: role_group_lte - acc có nhóm quyền nhỏ hơn hoặc bằng. role_group_lt - trả về chính acc đó và các acc có nhóm quyền nhỏ hơn 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200
    "data": [
      {
        "avatar": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/647879d7491cda83ac648412.jpg",
        "email": "<EMAIL>",
        "fullname": "Admin pingcomshop",
        "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "phone_number": "+***********",
        "role_group": "owner",
        "staff_code": "",
        "username": "admin@pingcomshop"
     }
    ],
  "paging": {
    "page": 1,
    "per_page": 5,
    "total_count": 220,
    "total_page": 44
  }
}
"""



# ---------------------------------- Cài đặt cấu hình field cho danh sách account  -------------------------------
"""
@api {POST} /api/v2.1/account/import        Import account
@apiDescription import account 
@apiGroup Account
@apiVersion 1.0.0
@apiName ImportAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (files:)     {file}         file                    file muốn import  
@apiParam   (form:)     {string}        status_import           Hành động muốn thưc thi [import_add_account, import_update_account]



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
}

"""

# APi Mau file import account
"""
@api {GET} /api/v2.1/template/import-file        Template import file
@apiDescription template import file
@apiGroup Account
@apiVersion 1.0.0
@apiName TemplateImportFile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam      (Query:)     {String}      template_type  loại file mẫu: team, account 


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        // template_type = account 
        "add_account": "https://t1.mobio.vn/static/mobio/upload/file_name.xlsx",
        "update_account": "https://t1.mobio.vn/static/mobio/upload/file_name.xlsx",
        // template_type = team 
        "add_team": "https://t1.mobio.vn/static/mobio/upload/file_name.xlsx",
        "change_user_in_team": "https://t1.mobio.vn/static/mobio/upload/file_name.xlsx",
        "remove_user_in_team": "https://t1.mobio.vn/static/mobio/upload/file_name.xlsx",
        "update_team": "https://t1.mobio.vn/static/mobio/upload/file_name.xlsx",
    }
}

"""


"""
@api {get} /api/v2.1/accounts/eib/level-lower EIB Lấy danh sách nhân viên các cấp dưới   
@apiDescription gửi lên thông tin 1 tài khoản trả về tất cả các cấp dưới của tài khoản đó, dữ liệu quản lý thời điểm hiện tại 
@apiVersion 1.0.0
@apiGroup Account
@apiName GetAllAccountLevelLower 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Query:)     {String}      account_id  id tài khoản 
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "email": "<EMAIL>",
      "fullname": "Admin pingcomshop",
      "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "username": "admin@pingcomshop",
      "sol_id": ""
    }
  ],
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {get} /api/v2.1/account/extra/level Lấy thông tin level của nhân viên 
@apiDescription thông tin mở rộng của nhân viên 
@apiVersion 1.0.0
@apiGroup Account
@apiName GetLevelExtraAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Query:)     {String}      account_id  id tài khoản 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": {
    "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "area_code": ""       // khu vực 
    "sol_id": ""            // đơn vị kinh doanh 
    "group_position_id": "RBO"
    "level": "level_1"    // level_1, level_2, level_3, level_4, level_5, level_6 
  },
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""

"""
@api {GET} /api/v2.1/leave-config          Lấy danh sách thời gian vắng mặt của nhân viên
@apiDescription Lấy danh sách thời gian vắng mặt của nhân viên
@apiVersion 2.1.0
@apiGroup LeaveConfig
@apiName GetLeaveConfig
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam      (Query:)     {String}      account_id  id tài khoản nhân viên


@apiSuccess       {String}        data.id                                   ID bản ghi thời gian vắng mặt
@apiSuccess       {String}        data.start_time                           Thời gian bắt đầu vắng mặt
@apiSuccess       {String}        data.end_time                             Thời gian kết thúc vắng mặt



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": [
      {
        "id": "67ac646a81a96bdddf121c59",
        "start_time": "2025-02-12T01:00:00Z",
        "end_time": "2025-02-12T010:30Z",
      },
      {
        "id": "67ac65c844d3590ba1d99a43",
        "start_time": "2025-02-13T01:00:00Z",
        "end_time": "2025-02-13T010:30Z"
      },
      ....
    ]
}
"""

"""
@api {POST} /api/v2.1/leave-config Tạo thời gian vắng mặt của nhân viên
@apiDescription Tạo thời gian vắng mặt của nhân viên
@apiVersion 2.1.0
@apiGroup LeaveConfig
@apiName AddLeaveConfig
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam         (Body:)           {String}              start_time                  Thời gian bắt đầu vắng mặt
                                                                                      <li><code>Giờ UTC, Định dạng: YYYY-MM-ddTHH:mm:ssZ | ex: 2024-07-11T00:00:00Z </code></li>
@apiParam         (Body:)           {String}              end_time                    Thời gian kết thúc vắng mặt
                                                                                      <li><code>Giờ UTC, Định dạng: YYYY-MM-ddTHH:mm:ssZ | ex: 2024-07-11T00:00:00Z </code></li>
@apiParam         (Body:)           {String}              account_id                  ID nhân viên vắng mặt

@apiParamExample {json} Info example
{
  "start_time": "2025-02-12T01:00:00Z",
  "end_time": "2025-02-12T01:30:00Z",
  "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {PATCH} /api/v2.1/leave-config/<leave_id> Sửa thời gian vắng mặt của nhân viên
@apiDescription Sửa thời gian vắng mặt của nhân viên
@apiVersion 2.1.0
@apiGroup LeaveConfig
@apiName EditLeaveConfig
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam         (Body:)           {String}              start_time                  Thời gian bắt đầu vắng mặt
                                                                                      <li><code>Giờ UTC, Định dạng: YYYY-MM-ddTHH:mm:ssZ | ex: 2024-07-11T00:00:00Z </code></li>
@apiParam         (Body:)           {String}              end_time                    Thời gian kết thúc vắng mặt
                                                                                      <li><code>Giờ UTC, Định dạng: YYYY-MM-ddTHH:mm:ssZ | ex: 2024-07-11T00:00:00Z </code></li>

@apiParamExample {json} Info example
{
  "start_time": "2025-02-12T01:00:00Z",
  "end_time": "2025-02-12T01:30:00Z",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""



"""
@api {DELETE} /api/v2.1/leave-config/<leave_id> Xoá thời gian vắng mặt của nhân viên
@apiDescription Xoá thời gian vắng mặt của nhân viên
@apiVersion 2.1.0
@apiGroup LeaveConfig
@apiName DeleteLeaveConfig
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {GET} /api/v2.1/leave-config/max-record  Số lượng bản ghi tối đa của thời gian vắng mặt
@apiDescription Số lượng bản ghi tối đa của thời gian vắng mặt
@apiVersion 2.1.0
@apiGroup LeaveConfig
@apiName MaxRecordLeaveTime
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "data": 5
}
"""


"""
@api {POST} /api/v2.1/leave-config/by-account-ids Danh sách thời gian vắng mặt theo list account_id
@apiDescription Danh sách thời gian vắng mặt theo list account_id
@apiVersion 2.1.0
@apiGroup LeaveConfig
@apiName LeaveByAccountIds
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam         (Body:)           {Array}            account_ids      Danh sách account_id


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "leave_time": [
        {
          "start_time": "2025-02-24T2:00:00Z",
          "end_time": "2025-02-24T11:00:00Z",
        },
        {
          "start_time": "2025-02-25T2:00:00Z",
          "end_time": "2025-02-25T7:00:00Z",
        }
      ]
    }
  ]
}
"""


"""
@api {put} /api/v2.1/merchants/<merchant_id>/accounts/<account_id>/edit  Update thông tin nhân viên
@apiDescription Update thông tin nhân viên. Dịch vụ gửi lên request dạng <code>form-data</code>  
@apiVersion 2.1.0
@apiGroup Account
@apiName EditAccountInfoNew
@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {File}      avatar                          Ảnh đại diện
@apiParam      (Body:)     {String}    info                            Thông tin  sản phẩm, String object json


@apiParam             (info)                    {string}               password                    Mật khẩu
@apiParam             (info)                    {string}               fullname                    Tên đầu đủ
@apiParam             (info)                    {string}               phone_number                Số điện thoại
@apiParam             (info)                    {string}               email                       Email của tài khoản
@apiParam             (info)                    {String}               role_group                  Nhóm quyền <code> owner, admin, manager, user</code>
@apiParam             (info)                    {array}                [module_ids]                Mảng các module_id:
@apiParam             (info)                    {array}                [policies]                  Mảng các ID policies:
@apiParam             (info)                    {array}                [block]                     Mảng các khối
@apiParam             (info)                    {array}                [department_ids]            Mảng các phòng ban
@apiParam             (info)                    {string}               [sol_id]                    Đơn vị kinh doanh
@apiParam             (info)                    {string}               [scope_code]                Mã cấp quản lý
@apiParam             (info)                    {string}               [position_id]               Chức danh
@apiParam             (info)                    {string}               [gender]                    Giới tính <code>male/female</code>
@apiParam             (info)                    {array}                [product_line]              Danh sách dòng sản phẩm account có quyền truy cập
@apiParam             (info)                    {array}                [tags]                      Danh sách tags
@apiParam             (info)                    {String}               [staff_code]                Mã nhân viên
@apiParam             (info)                    {Int}                  [status]                    Trạng thái của nhân viên
                                                                                                   <li><code>1</code>: Đang hoạt động</li>
                                                                                                   <li><code>4</code>: Đóng</li>


@apiParamExample {json} Info example
{
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "phone_number": "",
  "email": "<EMAIL>",
  "password":"1234",
  "module_ids":["18a07df3-5fdc-41c4-9bc9-00cee694c68a"],
  "policies": ["18a07df3-5fdc-41c4-9bc9-00cee694c68a"],
  "block":["KHCN"],
  "department_ids": ["CBP_1001"],
  "sol_id":"1001",
  "scope_code":"Directsale",
  "position_id":"CD056",
  "group_department_ids": ["dvkh","ho_tro_tin_dung","ban_giam_doc"],
  "product_line": ["base_service","activation"],
  "tags": ["18a07df3-5fdc-41c4-9bc9-00cee694c68a"],
  "status": 4
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "86b15b04-d4d1-41aa-95f8-b826e4bca0be",
  "id": "cbce917d-f9f8-4088-bcad-da01975d3163",
  "username": "Locnh",
  "fullname": "Nguyễn Hữu Lộc",
  "avatar": "",
  "phone_number": "",
  "email": "<EMAIL>",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "created_account": "admin@mobio",
  "role_group": "manager",
  "modules":[
          {
                "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
                "name": "Chăm sóc khách hàng",
                "description": ""
          },
          ...
  ],
  "policies": ["18a07df3-5fdc-41c4-9bc9-00cee694c68a"],
  "block":["KHCN"],
  "department_ids": ["CBP_1001"],
  "sol_id":"1001",
  "scope_code":"Directsale",
  "position_id":"CD056",
  "group_department_ids": [
            "dvkh",
            "ho_tro_tin_dung",
            "ban_giam_doc"
        ],
    "group_department_mapping": [
        {
            "name": "Phòng Dịch vụ khách hàng",
            "value": "dvkh"
        },
        {
            "name": "Bộ phận Hỗ trợ tín dụng",
            "value": "ho_tro_tin_dung"
        },
        {
            "name": "Ban Giám đốc",
            "value": "ban_giam_doc"
        }
    ],
    "tags": ["18a07df3-5fdc-41c4-9bc9-00cee694c68a"],
    "status": 4
}

"""


"""
@api {GET} /api/v2.1/leave-config/account-leave  danh sách account vắng mặt
@apiDescription trả về tất cả id account vắng mặt, có cache dữ liệu 
@apiVersion 2.1.0
@apiGroup LeaveConfig
@apiName AllAccountLeave 
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "data": ["18a07df3-5fdc-41c4-9bc9-00cee694c68a"]
}
"""

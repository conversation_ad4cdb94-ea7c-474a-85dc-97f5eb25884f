#!/usr/bin/python
# -*- coding: utf8 -*-
******************************* <PERSON><PERSON><PERSON> chức năng *******************************
* version: 1.0.0                                                            *
*****************************************************************************
"""
@api {delete} /api/v2.1/functions/<function_id> <PERSON><PERSON><PERSON> chức năng
@apiDescription Xoá chức năng hệ thống.
@apiGroup Function
@apiVersion 1.0.0
@apiName DeleteFunc

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse function_type_success
@apiUse created_time
@apiUse updated_time

@apiParam   (Resource:) {String}    id          UUID của chức năng cần xoá.

@apiSuccess     {String}    id                  UUID của chức năng cần xoá.
@apiSuccess     {String}    [name]              Tên chức năng. Giới hạn <code>64 ký tự</code>.
@apiSuccess     {String}    [description]       Mô tả chức năng. Giới hạn <code>256 ký tự</code>.
@apiSuccess     {String}    [path]              Đường dẫn đến chức năng. Dành riêng cho client là kiểu WebCRM.
@apiSuccess     {String}    [parent]            Xâu theo format để quy định vị trí của chức năng trong menu.
@apiSuccess     {String}    [icon_name]         Tên icon đại diện cho chức năng. Tên icon sử dụng theo kiểu font. Ví dụ: <code>fas fa-edit</code> (Font Awesome Icons)

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "name":"Quản lý cửa hàng",
    "description":"Chức năng quản lý danh sách cửa hàng.",
    "path":"customer-care/content/shop",
    "parent":"CSKH::QLND",
    "icon_name":"fas fa-home",
    "type":1,
    "create_time":"2017-08-07T04:02:28.002Z",
    "updated_time":"2017-08-07T04:02:28.002Z"
}
"""
******************************* Lấy D/S chức năng *******************************
* version: 1.0.0                                                                *
* version: 2.1.0                                                                *
*********************************************************************************
"""
@api {get} /v2.0/functions Lấy danh sách chức năng
@apiDescription Lấy danh sách chức năng của hệ thống.
<li>Có hỗ trợ sắp xếp theo: <code>tên, đường dẫn</code>;</li>
<li>Có hỗ trợ tìm kiếm theo tên chức năng;</li>
<li>Có hỗ trợ phân trang;</li>
@apiGroup Function
@apiVersion 2.0.0
@apiName GetFuncs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse lang_success
@apiUse paging
@apiUse search
@apiUse order_sort
@apiParam    (Query:)  {int:}    [group_by_module]                   Sắp xếp nhóm chức năng theo module <code>1: sắp xếp , 2: mặc định không sắp xếp</code>
@apiSuccess     {Function[]}    data                        Danh sách chức năng của hệ thống.

@apiSuccess    (Function)     {String}        id                     Id của chức năng.
@apiSuccess    (Function)     {String}        name                   Tên của chức năng.
@apiSuccess    (Function)     {String}        description            Mô tả của chức năng.
@apiSuccess    (Function)     {String}        path                   Đường dẫn đến chức năng. Dành riêng cho client là kiểu WebCRM.
@apiSuccess    (Function)     {String}        parent                 Xâu theo format để quy định vị trí của chức năng trong menu.
@apiSuccess    (Function)     {String}        [icon_name]            Tên icon đại diện cho chức năng. Tên icon sử dụng theo kiểu font. Ví dụ: <code>fas fa-edit</code> (Font Awesome Icons)
@apiSuccess    (Function)     {Number}        [type]                 Xác định function sẽ có hiệu lực trên ứng dụng nào.
                                                                    <li><code>1-MerchantCRM</code>: function được phép chạy trên ứng dụng Merchant CRM;</li>
                                                                    <li><code>2-AdminCRM</code>: function được phép chạy trên ứng dụng Admin CRM;</li>
                                                                    <li><code>3-PartnerMobileApp</code>: function được phép chạy trên ứng dụng mobile Partner;</li>
@apiUse actions_success

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data":[
        {
            "id": "7a810df9-7be5-486f-bd4e-1d0d825d593d",
            "name": "Quản lý cửa hàng",
            "description": "Chức năng quản lý danh sách cửa hàng.",
            "path":"customer-care/content/shop",
            "parent":"CSKH::QLND",
            "icon_name":"fas fa-home",
            "type":1,
            "create_time": "2017-08-07T04:02:28.002Z",
            "update_time": "2017-08-07T04:02:28.002Z",
            "actions": [
                {
                    "id": 1,
                    "name_vi": "Thêm",
                    "name_en": "Add",
                    "description": "Thêm item"
                },
                {
                    "id": 2,
                    "name_vi": "Sửa",
                    "name_en": "Edit",
                    "description": "Sửa item"
                },
                {
                    "id": 3,
                    "name_vi": "Xoá",
                    "name_en": "Delete",
                    "description": "Xoá item"
                }
            ]
        },
        {
            "id": "d699b141-375e-4235-b91f-17850b806d03",
            "name": "Quản lý sản phẩm",
            "description": "Chức năng quản lý danh sách sản phẩm.",
            "path":"customer-care/content/product",
            "parent":"CSKH::QLND",
            "icon_name":"fas fa-home",
            "type":1,
            "create_time": "2017-08-07T04:02:28.002Z",
            "update_time": "2017-08-07T04:02:28.002Z",
            "actions": [
                {
                    "id": 1,
                    "name_vi": "Thêm",
                    "name_en": "Add",
                    "description": "Thêm item"
                },
                {
                    "id": 2,
                    "name_vi": "Sửa",
                    "name_en": "Edit",
                    "description": "Sửa item"
                },
                {
                    "id": 3,
                    "name_vi": "Xoá",
                    "name_en": "Delete",
                    "description": "Xoá item"
                }
            ]
        }
    ],
    "sort":"name_vi",
    "order":"asc",
    "paging": {
    ...
    }
}
@apiSuccessExample     {json}    group_by_module
{
    "data":
        [
            {
                "name_module": "CSKH::QLND",
                "parent":"CSKH::QLND",
                "data_group":[
                    {
                        "id": "7a810df9-7be5-486f-bd4e-1d0d825d593d",
                        "name": "Quản lý cửa hàng",
                        "description": "Chức năng quản lý danh sách cửa hàng.",
                        "path":"customer-care/content/shop",
                        "parent":"CSKH::QLND",
                        "icon_name":"fas fa-home",
                        "type":1,
                        "create_time": "2017-08-07T04:02:28.002Z",
                        "update_time": "2017-08-07T04:02:28.002Z",
                        "actions": [
                            {
                                "id": 1,
                                "name_vi": "Thêm",
                                "name_en": "Add",
                                "description": "Thêm item"
                            },
                            {
                                "id": 2,
                                "name_vi": "Sửa",
                                "name_en": "Edit",
                                "description": "Sửa item"
                            },
                            {
                                "id": 3,
                                "name_vi": "Xoá",
                                "name_en": "Delete",
                                "description": "Xoá item"
                            }
                        ]
                    },
                    {
                        "id": "d699b141-375e-4235-b91f-17850b806d03",
                        "name": "Quản lý sản phẩm",
                        "description": "Chức năng quản lý danh sách sản phẩm.",
                        "path":"customer-care/content/product",
                        "parent":"CSKH::QLND",
                        "icon_name":"fas fa-home",
                        "type":1,
                        "create_time": "2017-08-07T04:02:28.002Z",
                        "update_time": "2017-08-07T04:02:28.002Z",
                        "actions": [
                            {
                                "id": 1,
                                "name_vi": "Thêm",
                                "name_en": "Add",
                                "description": "Thêm item"
                            },
                            {
                                "id": 2,
                                "name_vi": "Sửa",
                                "name_en": "Edit",
                                "description": "Sửa item"
                            },
                            {
                                "id": 3,
                                "name_vi": "Xoá",
                                "name_en": "Delete",
                                "description": "Xoá item"
                            }
                        ]
                    }
                ]
            }
        ......
        ]
}
"""
**************************
"""
@api {get} /api/v2.1/functions Lấy danh sách chức năng
@apiDescription Lấy danh sách chức năng của hệ thống.
<li>Có hỗ trợ sắp xếp theo: <code>tên, đường dẫn</code>;</li>
<li>Có hỗ trợ tìm kiếm theo tên chức năng;</li>
<li>Có hỗ trợ phân trang;</li>
@apiGroup Function
@apiVersion 1.0.0
@apiName GetFuncs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse lang_success
@apiUse paging
@apiUse search
@apiUse order_sort

@apiSuccess     {Function[]}    data                        Danh sách chức năng của hệ thống.

@apiSuccess    (Function)     {String}        id                     Id của chức năng.
@apiSuccess    (Function)     {String}        name                   Tên của chức năng.
@apiSuccess    (Function)     {String}        description            Mô tả của chức năng.
@apiSuccess    (Function)     {String}        path                   Đường dẫn đến chức năng. Dành riêng cho client là kiểu WebCRM.
@apiSuccess    (Function)     {String}        parent                 Xâu theo format để quy định vị trí của chức năng trong menu.
@apiSuccess    (Function)     {String}        [icon_name]            Tên icon đại diện cho chức năng. Tên icon sử dụng theo kiểu font. Ví dụ: <code>fas fa-edit</code> (Font Awesome Icons)
@apiSuccess    (Function)     {Number}        [type]                 Xác định function sẽ có hiệu lực trên ứng dụng nào.
                                                                    <li><code>1-MerchantCRM</code>: function được phép chạy trên ứng dụng Merchant CRM;</li>
                                                                    <li><code>2-AdminCRM</code>: function được phép chạy trên ứng dụng Admin CRM;</li>
                                                                    <li><code>3-PartnerMobileApp</code>: function được phép chạy trên ứng dụng mobile Partner;</li>
@apiUse actions_success

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data":[
        {
            "id": "7a810df9-7be5-486f-bd4e-1d0d825d593d",
            "name": "Quản lý cửa hàng",
            "description": "Chức năng quản lý danh sách cửa hàng.",
            "path":"customer-care/content/shop",
            "parent":"CSKH::QLND",
            "icon_name":"fas fa-home",
            "type":1,
            "create_time": "2017-08-07T04:02:28.002Z",
            "update_time": "2017-08-07T04:02:28.002Z",
            "actions": [
                {
                    "id": 1,
                    "name_vi": "Thêm",
                    "name_en": "Add",
                    "description": "Thêm item"
                },
                {
                    "id": 2,
                    "name_vi": "Sửa",
                    "name_en": "Edit",
                    "description": "Sửa item"
                },
                {
                    "id": 3,
                    "name_vi": "Xoá",
                    "name_en": "Delete",
                    "description": "Xoá item"
                }
            ]
        },
        {
            "id": "d699b141-375e-4235-b91f-17850b806d03",
            "name": "Quản lý sản phẩm",
            "description": "Chức năng quản lý danh sách sản phẩm.",
            "path":"customer-care/content/product",
            "parent":"CSKH::QLND",
            "icon_name":"fas fa-home",
            "type":1,
            "create_time": "2017-08-07T04:02:28.002Z",
            "update_time": "2017-08-07T04:02:28.002Z",
            "actions": [
                {
                    "id": 1,
                    "name_vi": "Thêm",
                    "name_en": "Add",
                    "description": "Thêm item"
                },
                {
                    "id": 2,
                    "name_vi": "Sửa",
                    "name_en": "Edit",
                    "description": "Sửa item"
                },
                {
                    "id": 3,
                    "name_vi": "Xoá",
                    "name_en": "Delete",
                    "description": "Xoá item"
                }
            ]
        }
    ],
    "sort":"name_vi",
    "order":"asc",
    "paging": {
    ...
    }
}
"""
******************************* Sửa chức năng *******************************
* version: 1.0.0                                                            *
*****************************************************************************
"""
@api {patch} /api/v2.1/functions/<function_id> Sửa chức năng
@apiDescription Chỉnh sửa chức năng tương tác với hệ thống.
@apiGroup Function
@apiVersion 1.0.0
@apiName EditFunc

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse function_type_success
@apiUse function_type_body
@apiUse created_time
@apiUse updated_time

@apiParam   (Body:)     {String}    [name]              Tên chức năng. Giới hạn <code>64 ký tự</code>.
@apiParam   (Body:)     {String}    [description]       Mô tả chức năng. Giới hạn <code>256 ký tự</code>.
@apiParam   (Body:)     {String}    [path]              Đường dẫn đến chức năng. Dành riêng cho client là kiểu WebCRM.
@apiParam   (Body:)     {String}    [parent]            Xâu theo format để quy định vị trí của chức năng trong menu.
@apiParam   (Body:)     {String}    [icon_name]         Tên icon đại diện cho chức năng. Tên icon sử dụng theo kiểu font. Ví dụ: <code>fas fa-edit</code> (Font Awesome Icons)
@apiParam   (Body:)     {Number[]}  [actions]        Danh sách id của action được gán cho chức năng.
@apiParamExample    {json}  Body Examples:
{
    "name":"Quản lý cửa hàng",
    "description":"Chức năng quản lý danh sách cửa hàng.",
    "path":"customer-care/content/shop",
    "parent":"CSKH::QLND",
    "icon_name":"fas fa-home",
    "type":1,
    "actions":[
        1,2,3
    ]
}

@apiSuccess     {String}    id              Id của chức năng.
@apiSuccess     {String}    name            Tên của chức năng.
@apiSuccess     {String}    description     Mô tả chức năng.
@apiSuccess     {String}    path            Đường dẫn đến chức năng. Dành riêng cho client là kiểu WebCRM.
@apiSuccess     {String}    parent          Xâu theo format để quy định vị trí của chức năng trong menu.
@apiSuccess     {String}    [icon_name]     Tên icon đại diện cho chức năng. Tên icon sử dụng theo kiểu font. Ví dụ: <code>fas fa-edit</code> (Font Awesome Icons)
@apiUse actions_success

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "id": "7a810df9-7be5-486f-bd4e-1d0d825d593d",
    "name": "Quản lý cửa hàng",
    "description": "Chức năng quản lý danh sách cửa hàng.",
    "path":"customer-care/content/shop",
    "parent":"CSKH::QLND",
    "icon_name":"fas fa-home",
    "type":1,
    "create_time": "2017-08-07T04:02:28.002Z",
    "update_time": "2017-08-07T04:02:28.002Z",
    "actions": [
        {
            "id": 1,
            "name_vi": "Thêm",
            "name_en": "Add",
            "description": "Thêm item"
        },
        {
            "id": 2,
            "name_vi": "Sửa",
            "name_en": "Edit",
            "description": "Sửa item"
        },
        {
            "id": 3,
            "name_vi": "Xoá",
            "name_en": "Delete",
            "description": "Xoá item"
        }
    ]

}
"""
******************************* Tạo chức năng *******************************
* version: 1.0.0                                                            *
*****************************************************************************
"""
@api {post} /api/v2.1/functions Tạo chức năng
@apiDescription Tạo chức năng tương tác với hệ thống.
@apiGroup Function
@apiVersion 1.0.0
@apiName CreateFunc

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse function_type_success
@apiUse function_type_body
@apiUse created_time
@apiUse updated_time

@apiParam   (Body:)     {String}    name                Tên chức năng. Giới hạn <code>64 ký tự</code>.
@apiParam   (Body:)     {String}    description         Mô tả chức năng. Giới hạn <code>256 ký tự</code>.
@apiParam   (Body:)     {String}    path                Đường dẫn đến chức năng. Dành riêng cho client là kiểu WebCRM.
@apiParam   (Body:)     {String}    parent              Xâu theo format để quy định vị trí của chức năng trong menu.
@apiParam   (Body:)     {String}    [icon_name]         Tên icon đại diện cho chức năng. Tên icon sử dụng theo kiểu font. Ví dụ: <code>fas fa-edit</code> (Font Awesome Icons)
@apiParam   (Body:)     {Number[]}  actions          Danh sách id của action được gán cho chức năng.
@apiParamExample    {json}  Body Examples:
{
    "name":"Quản lý cửa hàng",
    "description":"Chức năng quản lý danh sách cửa hàng.",
    "path":"customer-care/content/shop",
    "parent":"CSKH::QLND",
    "icon_name":"fas fa-home",
    "type":1,
    "actions":[
        1,2,3
    ]
}

@apiSuccess     {String}    id              Id của chức năng.
@apiSuccess     {String}    name            Tên của chức năng.
@apiSuccess     {String}    description     Mô tả chức năng.
@apiSuccess     {String}    path            Đường dẫn đến chức năng. Dành riêng cho client là kiểu WebCRM.
@apiSuccess     {String}    parent          Xâu theo format để quy định vị trí của chức năng trong menu.
@apiSuccess     {String}    [icon_name]     Tên icon đại diện cho chức năng. Tên icon sử dụng theo kiểu font. Ví dụ: <code>fas fa-edit</code> (Font Awesome Icons)
@apiUse actions_success

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "id": "7a810df9-7be5-486f-bd4e-1d0d825d593d",
    "name": "Quản lý cửa hàng",
    "description": "Chức năng quản lý danh sách cửa hàng.",
    "path":"customer-care/content/shop",
    "parent":"CSKH::QLND",
    "icon_name":"fas fa-home",
    "type":1,
    "create_time": "2017-08-07T04:02:28.002Z",
    "update_time": "2017-08-07T04:02:28.002Z",
    "actions": [
        {
            "id": 1,
            "name_vi": "Thêm",
            "name_en": "Add",
            "description": "Thêm item"
        }
    ]

}
"""


******************************* CHECK ACCOUNT MODULE **********************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {GET} /api/v2.1/account/module/check  kiểm tra tài khoản theo module  
@apiDescription kiểm tra tài khoản có được phép truy cập vào module không 
@apiVersion 1.0.0
@apiGroup Function
@apiName ModuleCheck

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}  module_name       module cần kiểm tra, nhiều module cách nhau dấu ,
<li>Example: <code>SOCIAL</code>, <code>SALES</code></li>
@apiParam   (Query:)    {String}  account_id         id tài khoản


@apiSuccess (data) {String}   account_id        id tài khoản
@apiSuccess (data) {Integer}   module_access     được phép truy cập module hay không.
<li>1: có</li>
<li>0: không</li>
@apiSuccess (data) {String}   role_group    loại tài khoản
<li>owner</li>
<li>admin</li>
<li>manager</li>
<li>user</li>

     

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "account_id": "71eda34e-200c-4624-9e55-3ce05ac9e416",
        "module_access": 0,
        "role_group": "owner"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {POST} /api/v2.1/account/module/check  Thông tin tài khoản theo module  
@apiDescription Lấy ra thông tin tài khoản theo module 
@apiVersion 1.0.0
@apiGroup Function
@apiName AccountRoleByModuleName

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}  module_name       module cần kiểm tra, nhiều module cách nhau dấu ,
<li>Example: <code>SOCIAL</code>, <code>SALES</code></li>
@apiParam   (Query:)    {String}  account_id         id tài khoản


@apiSuccess (data) {String}   account_id        id tài khoản
@apiSuccess (data) {Integer}   module_access     được phép truy cập module hay không.
<li>1: có</li>
<li>0: không</li>
@apiSuccess (data) {String}   role_group    loại tài khoản
<li>owner</li>
<li>admin</li>
<li>manager</li>
<li>user</li>



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [{
        "account_id": "71eda34e-200c-4624-9e55-3ce05ac9e416",
        "module_access": 0,
        "role_group": "owner"
    },
    ...
    ]
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} /api/v2.1/account/function/filter  danh sách function của account   
@apiDescription lọc theo tên module và path menu 
@apiVersion 1.0.0
@apiGroup Function
@apiName AccountListFunction 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}  account_id         id tài khoản
@apiParam   (Query:)    {String}  [module_name]       module cần kiểm tra, nhiều module cách nhau dấu ,
<li>Example: <code>SOCIAL</code>, <code>SALES</code></li>
@apiParam   (Query:)    {String}  [path]         path menu cần lọc, vd: "waiting-assignment"

     

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
     "data": [
        {
        "action": [
            1,
            3,
            4,
            12
        ],
        "description": "Hàng chờ",
        "id": "e99ed34f-d00a-466a-b3b1-b405eedc081d",
        "module_id": "5eec13e6-bee9-492b-95d1-f6b50008f54a",
        "name": "Sales > sales order > Hàng chờ",
        "parent": "SALES",
        "path": "/sales/order/waiting-assignment",
        "status": 1,
        "type": 1
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/functions/account/by-path  danh sách account có function    
@apiDescription danh sách account có function 
@apiVersion 1.0.0
@apiGroup Function
@apiName FunctionListAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}  path         path menu cần tìm, vd: "/marketing/template-email/list-mail"
@apiParam   (Query:)    {String}  [action]     mã số action, nhiều action cách nhau dấu , (VD: 1,3,6)
     
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
     "data": [
        {
			"account_id": "d40145d8-099c-4da4-8ec2-f83b92947fef",
			"action": [
				1,
				3,
				4,
				6,
				8
			],
			"id": "3a05bec1-0902-4eeb-83a5-9ea49cde7abf",
			"name": "Mẫu email",
			"parent": "MARKETING",
			"path": "/marketing/template-email/list-mail",
			"status": 1
		},
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} /api/v2.1/functions/account/mobile  Lấy danh sách chức năng được phép thao tác của account cho mobile     
@apiDescription Lấy danh sách chức năng được phép thao tác của account cho mobile
@apiVersion 1.0.0
@apiGroup Function
@apiName FunctionRoleAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        "key_1", "key_2", "key_3"
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


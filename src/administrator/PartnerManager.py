#!/usr/bin/python
# -*- coding: utf8 -*-
************************************************** API LẤY CHI TIẾT PARTNER **************************************************
* version: 1.0.0                                                                                                             *
******************************************************************************************************************************
"""
@api {get} /api/v2.1/partners/<partner_id> Lấy chi tiết partner
@apiDescription Lấy chi tiết partner theo id
@apiGroup Partner
@apiVersion 1.0.0
@apiName GetPartner

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {string} id ID đối tác sử dụng API
@apiSuccess  {string} name Tên đối tác
@apiSuccess  {string} description Mô tả thông tin đối tác
@apiSuccess  {status=1:enable 2:disable} status Trạng thái sử dụng API
@apiSuccess  {string} email Thư điện tử liên hệ
@apiSuccess  {string} address Địa chỉ liên hệ
@apiSuccess  {string} phone_number Số điện thoại liên hệ
@apiUse created_time
@apiUse updated_time
@apiSuccess  {string} expired_time Thời điểm đối tác hết hiệu lực, sẽ không thể dùng được các module của hệ thống nữa
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "4bf4ac7d-6d83-41c0-ba70-563da03488a0",
  "name": "Jollibee",
  "description": "POS cho chuỗi Jollibee",
  "status": 1,
  "email": "<EMAIL>",
  "address": "Lầu 5, tòa nhà SCIC, 16 Trương Định, Q.3, Tp.Hồ Chí Minh, Việt Nam",
  "phone_number": "0949494091",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "expired_time": "2019-08-07T04:02:28.002Z"
}
"""

********************************************* API LẤY CHI TIẾT THÔNG TIN PARTNER *********************************************
* version: 1.0.0                                                                                                             *
******************************************************************************************************************************
"""
@api {get} /api/v2.1/partners/<partner_id>/info Lấy chi tiết thông tin partner
@apiDescription Phục vụ quản lý partner id đi kèm với merchant id. Thông tin chi tiết sẽ được mã hóa
@apiGroup Partner
@apiVersion 1.0.0
@apiName GetPartnerInfo

@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
  "data": "chuỗi mã hóa"
}

@apiSuccess  {string} id ID đối tác sử dụng API
@apiSuccess  {string} name Tên đối tác
@apiSuccess  {string} description Mô tả thông tin đối tác
@apiSuccess  {status=1:enable 2:disable} status Trạng thái sử dụng API
@apiSuccess  {string} email Thư điện tử liên hệ
@apiSuccess  {string} address Địa chỉ liên hệ
@apiSuccess  {string} phone_number Số điện thoại liên hệ
@apiUse created_time
@apiUse updated_time
@apiSuccess  {string} expired_time Thời điểm đối tác hết hiệu lực, sẽ không thể dùng được các module của hệ thống nữa
@apiSuccess  {string} merchant_id UUID nhà cung cấp sử dụng partner id cho hệ thống on-permise
@apiSuccessExample {json} Dữ liệu raw
{
  "id": "4bf4ac7d-6d83-41c0-ba70-563da03488a0",
  "name": "Jollibee",
  "description": "POS cho chuỗi Jollibee",
  "status": 1,
  "email": "<EMAIL>",
  "address": "Lầu 5, tòa nhà SCIC, 16 Trương Định, Q.3, Tp.Hồ Chí Minh, Việt Nam",
  "phone_number": "0949494091",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "expired_time": "2019-08-07T04:02:28.002Z",
  "merchant_id": "d067c164-2c94-42b0-a409-614fda6bda9e"
}
"""
************************************************** API TẠO ĐỐI TÁC **************************************************
* version: 1.0.0                                                                                                    *
*********************************************************************************************************************
"""
@api {post} /api/v2.1/partners Tạo đối tác
@apiDescription Tạo một đối tác sử dụng API của Mobio cung cấp
@apiGroup Partner
@apiVersion 1.0.0
@apiName Post

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {string} name Tên đối tác
@apiParam  {string} description Mô tả thông tin đối tác
@apiParam  {string} email Thư điện tử liên hệ
@apiParam  {string} address Địa chỉ liên hệ
@apiParam  {string} phone_number Số điện thoại liên hệ
@apiParamExample {json} Body
{
  "name": "Jollibee",
  "description": "POS cho chuỗi Jollibee",
  "email": "<EMAIL>",
  "address": "Lầu 5, tòa nhà SCIC, 16 Trương Định, Q.3, Tp.Hồ Chí Minh, Việt Nam",
  "phone_number": "0949494091",
  "expired_time": "2019-08-07T04:02:28.002Z"
}

@apiSuccess  {string} id ID đối tác sử dụng API
@apiSuccess  {string} name Tên đối tác
@apiSuccess  {string} description Mô tả thông tin đối tác
@apiSuccess  {status=1:enable 2:disable} status Trạng thái sử dụng API
@apiSuccess  {string} email Thư điện tử liên hệ
@apiSuccess  {string} address Địa chỉ liên hệ
@apiSuccess  {string} phone_number Số điện thoại liên hệ
@apiUse created_time
@apiUse updated_time
@apiSuccess  {string} expired_time Thời điểm đối tác hết hiệu lực, sẽ không thể dùng được các module của hệ thống nữa
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "4bf4ac7d-6d83-41c0-ba70-563da03488a0",
  "name": "Jollibee",
  "description": "POS cho chuỗi Jollibee",
  "status": 1,
  "email": "<EMAIL>",
  "address": "Lầu 5, tòa nhà SCIC, 16 Trương Định, Q.3, Tp.Hồ Chí Minh, Việt Nam",
  "phone_number": "0949494091",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "expired_time": "2019-08-07T04:02:28.002Z"
}
"""
************************************************** API SỬA ĐỐI TÁC **************************************************
* version: 1.0.0                                                                                                    *
*********************************************************************************************************************
"""
@api {patch} /api/v2.1/partners/<partner_id> Sửa đối tác
@apiDescription Sửa một đối tác sử dụng API của Mobio cung cấp
@apiGroup Partner
@apiVersion 1.0.0
@apiName Patch

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {string} [name] Tên đối tác
@apiParam  {string} [description] Mô tả thông tin đối tác
@apiParam  {string} [email] Thư điện tử liên hệ
@apiParam  {string} [address] Địa chỉ liên hệ
@apiParam  {string} [phone_number] Số điện thoại liên hệ
@apiParamExample {json} Body
{
  "name": "Jollibee",
  "description": "POS cho chuỗi Jollibee",
  "email": "<EMAIL>",
  "address": "Lầu 5, tòa nhà SCIC, 16 Trương Định, Q.3, Tp.Hồ Chí Minh, Việt Nam",
  "phone_number": "0949494091",
  "expired_time": "2019-08-07T04:02:28.002Z"
}

@apiSuccess  {string} id ID đối tác sử dụng API
@apiSuccess  {string} name Tên đối tác
@apiSuccess  {string} description Mô tả thông tin đối tác
@apiSuccess  {status=1:enable 2:disable} status Trạng thái sử dụng API
@apiSuccess  {string} email Thư điện tử liên hệ
@apiSuccess  {string} address Địa chỉ liên hệ
@apiSuccess  {string} phone_number Số điện thoại liên hệ
@apiUse created_time
@apiUse updated_time
@apiSuccess  {string} expired_time Thời điểm đối tác hết hiệu lực, sẽ không thể dùng được các module của hệ thống nữa
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "4bf4ac7d-6d83-41c0-ba70-563da03488a0",
  "name": "Jollibee",
  "description": "POS cho chuỗi Jollibee",
  "status": 1,
  "email": "<EMAIL>",
  "address": "Lầu 5, tòa nhà SCIC, 16 Trương Định, Q.3, Tp.Hồ Chí Minh, Việt Nam",
  "phone_number": "0949494091",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "expired_time": "2019-08-07T04:02:28.002Z"
}
"""
************************************************** API XOÁ ĐỐI TÁC **************************************************
* version: 1.0.0                                                                                                    *
*********************************************************************************************************************
"""
@api {delete} /api/v2.1/partners/<partner_id> Xoá đối tác
@apiDescription Xoá thông tin 1 đối tác sử dụng API của Mobio cung cấp
@apiGroup Partner
@apiVersion 1.0.0
@apiName Delete

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {string} id ID đối tác vừa xoá
@apiSuccess  {string} name Tên đối tác
@apiSuccess  {string} description Mô tả thông tin đối tác
@apiSuccess  {status=1:enable 2:disable} status Trạng thái sử dụng API
@apiSuccess  {string} email Thư điện tử liên hệ
@apiSuccess  {string} address Địa chỉ liên hệ
@apiSuccess  {string} phone_number Số điện thoại liên hệ
@apiUse created_time
@apiUse updated_time
@apiSuccess  {string} expired_time Thời điểm đối tác hết hiệu lực, sẽ không thể dùng được các module của hệ thống nữa
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "4bf4ac7d-6d83-41c0-ba70-563da03488a0",
  "name": "Jollibee",
  "description": "POS cho chuỗi Jollibee",
  "status": 1,
  "email": "<EMAIL>",
  "address": "Lầu 5, tòa nhà SCIC, 16 Trương Định, Q.3, Tp.Hồ Chí Minh, Việt Nam",
  "phone_number": "0949494091",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "expired_time": "2019-08-07T04:02:28.002Z"
}
"""
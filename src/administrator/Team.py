#!/usr/bin/python
# -*- coding: utf8 -*-

******************************* API ADD TEAM **************************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {POST} /api/v2.1/teams add team
@apiDescription add team
@apiVersion 1.0.0
@apiGroup Team
@apiName AddTeam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam (Body:) {String}  name  Tên của team.
@apiParam (Body:) {String}  describe  Mô tả cho team.
@apiParam (Body:) {list}  module_name  module dùng team.
@apiParam (Body:) {List}  list_account  mảng account được thêm vào team.

@apiParam (list_account:) {String}  account_id  id định danh của account.
@apiParam (list_account:) {String}  permission  quyền account trong team.
@apiParam (Body:) {Array}  [policies]        Danh sách ID policies

@apiParamExample  {json}  Body:
{
    "name": "bbb",
    "describe": "",
    "list_account": [
        {
            "account_id": "8cc0a33c-baf5-11e7-a7c2-0242ac180003",
            "permission": "LEADER"
        },
        {
            "account_id": "990aee81-3e9d-4f29-98b6-b52f6e61b9b9",
            "permission": "MEMBER"
        }
    ],
    "module_name": ["SOCIAL"],
    "policies": ["990aee81-3e9d-4f29-98b6-b52f6e61b9b9"]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "describe": "",
        "id_team": "fe8c3616-82ce-11ee-a183-e92eba9a3c60",
        "list_account": [
            {
                "account_id": "d5906592-29bc-4d9e-a24e-3bbf2dd3aa14",
                "permission": "LEADER"
            }
        ],
        "module_name": [
            "SALE"
        ],
        "name": "cccaa",
        "policies": [
            "1d2c59f1-21f4-11ee-a17f-45311bd761af",
            "88e27e2e-73cf-11ee-9512-9a1b69ea53c5"
        ],
        "team_code": "fe8c3616-82ce-11ee-a183-e92eba9a3c60",
        "total_account": 1,
        "total_module": 1
    },
    "lang": "vi",
    "message": "request thành công."
}

"""

******************************* API UPDATE TEAM *******************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {PUT} /api/v2.1/teams/<team_id> api update team
@apiDescription update team
@apiVersion 1.0.0
@apiGroup Team
@apiName UpdateTeam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Resources:)  {String}    team_id  Id team cần update.

@apiParam (Body:) {String}  name  tên của team.
@apiParam (Body:) {String}  describe  Mô tả cho team.
@apiParam (Body:) {list}  module_name  module dùng team.
@apiParam (Body:) {List}  list_account  mảng account được thêm vào team.

@apiParam (list_account:) {String}  account_id  id định danh của account.
@apiParam (list_account:) {String}  permission  quyền account trong team.
@apiParam (Body:) {Array}  [policies]          Danh sách ID policies Nếu gửi lên 1 list rỗng thì đi hủy bỏ tất cả chính sách

@apiParamExample  {json}  Body:
{
    "describe": "",
    "module_name": [
        "SALE"
    ],
    "list_account": [
        {
            "permission": "MEMBER",
            "account_id": "3ec95187-0768-4119-9363-6e751aebd906"
        }
    ],
    "name": "NHBH - CN QUẢNG BÌNHH",
    "policies": [
        "3ec95187-0768-4119-9363-6e751aebd906",
        "3ec95187-0768-4119-9363-6e751aebd906"
    ]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "describe": "",
        "id_team": "d2500fd4-76b1-11ed-8cad-aac18c65aa24",
        "list_account": [
            {
                "account_id": "32c23de9-14c7-4f83-ab68-e6d7ae38ce01",
                "permission": "MEMBER"
            }
        ],
        "module_name": [
            "SALE",
            "TICKET"
        ],
        "policies": [
            "d2500fd4-76b1-11ed-8cad-aac18c65aa24",
            "d2500fd4-76b1-11ed-8cad-aac18c65aa24"
        ]
        "name": "Sale_all",
        "team_code": "d2500fd4-76b1-11ed-8cad-aac18c65aa24",
        "total_account": 15,
        "total_module": 2
    },
    "lang": "vi",
    "message": "request th\u00e0nh c\u00f4ng."
}

"""

******************************* API LIST TEAM *************************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {GET} /api/v2.1/teams api list team
@apiDescription list team
@apiVersion 1.0.0
@apiGroup Team
@apiName ListTeam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Query:)   {String}   [team_ids]   team id, nhiều id cách nhau dấu ,
@apiParam   (Query:)   {String}   [module_name]   nhiều module cách nhau dấu ,

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "describe": null,
            "id_team": "1ab5bb28-b9af-4da4-bd98-85ac77c7fd2f",
            "name": "cuong",
            "list_account": [
                {
                    "account_id": "8cc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "permission": "LEADER"
                },
                {
                    "account_id": "990aee81-3e9d-4f29-98b6-b52f6e61b9b9",
                    "permission": "MEMBER"
                }
            ],
            "module_name": [
                "MARKETING",
                "SOCIAL"
            ],
            "total_account": 2,
            "total_module": 2
        },
        {
            "describe": null,
            "id_team": "c2ee5485-a185-49cf-85c7-3b02a35e7483",
            "name": "anb",
            "list_account": [
                {
                    "account_id": "9kc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "permission": "LEADER"
                },
                {
                    "account_id": "560aee81-3e9d-4f29-98b6-b52f6e61b9b9",
                    "permission": "MEMBER"
                }
            ],
            "module_name": [
                "MARKETING",
                "SOCIAL"
            ],
            "total_account": 2,
            "total_module": 2
        }
    ],
    "paging": {
        "page": 1,
        "per_page": 15,
        "total_count": 222,
        "total_page": 15
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* API DELETE TEAM ***********************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {DELETE} /api/v2.1/teams/<team_id>  api delete team
@apiDescription delete team
@apiVersion 1.0.0
@apiGroup Team
@apiName DeleteTeam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Resources:)  {String}    team_id  Id team cần delete.

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* API LIST NAME MODULE_TEAM *************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {GET} /api/v2.1/module-teams  api list name module_team
@apiDescription list name module_team
@apiVersion 1.0.0
@apiGroup Team
@apiName ListNameModuleTeam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "a30ddf84-64b3-4d25-9c00-d20f2f3eaa24",
            "label": "i18n_module_sale",
            "name": "SALE"
        },
        {
            "id": "aee3838a-074a-45dc-b6f0-1ae90bd277e4",
            "label": "i18n_online_social",
            "name": "SOCIAL"
        },
        {
            "id": "b5195131-69f7-499f-ab9a-cc5dccb12cec",
            "label": "i18n_module_maketing",
            "name": "MAKETING"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* API LIST ACCOUNT **********************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {GET} /api/v2.1/team/accounts  api list account of team
@apiDescription list account of team
@apiVersion 1.0.0
@apiGroup Team
@apiName ListAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam           (Query:)    {String}    module_name             module cần lấy danh sách team.
                                                                    <li>Example: <code>SOCIAL</code>, <code>SALE</code></li>
@apiParam           (Query:)    {String}    [team_id]               id của team cần lấy danh sách account. 
@apiParam           (Query:)    {String}    [account_id]            id của account cần lấy team.
@apiParam           (Query:)    {String}    [status_custom]         Trạng thái tuỳ chỉnh của tài khoản
                                                                    <li><code>on_leave</code>: Đang vắng mặt</li>
@apiUse account_status_query_params
@apiParam           (Query:)    {String}    [filter_custom]         bộ lọc tùy chỉnh:
                                                                    <li><code>active_not_on_leave</code>: Đang hoạt động và không vắng mặt </li>                                                                   

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id_team": "13a0a31a-c4ba-40bc-afda-c6858143f42e",
            "list_account": [
                {
                    "account_id": "0d8ee9eb-68a5-437f-b1d8-db3a5b5eb54b",
                    "permission": "MEMB ER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Lộc",
                    "avatar": "",
                    "phone_number": "",
                    "email": "<EMAIL>",
                    "is_admin": 1,
                    "status": 1,
                    "status_custom": "on_leave"
                },
                {
                    "account_id": "9c437c4a-4915-4343-b499-56932d72a8ae",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Bằng",
                    "avatar": "",
                    "phone_number": "",
                    "email": "",
                    "is_admin": 1,
                    "status": 1,
                    "status_custom": "on_leave"
                }
            ],
            "name": "new team 333"
        },
        {
            "id_team": "401d2145-48c2-41c3-84c1-3dc6185c1dbe",
            "list_account": [
                {
                    "account_id": "cad9cc6e-286b-4dd1-b734-afdd81b69a73",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Lộc",
                    "avatar": "",
                    "phone_number": "",
                    "email": "<EMAIL>",
                    "is_admin": 1,
                    "status": 1,
                    "status_custom": "on_leave"
                }
            ],
            "name": "team makettin"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/team/accounts/filter  api list account of team
@apiDescription Danh sách account theo team_ids
@apiVersion 1.0.0
@apiGroup Team
@apiName ListAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam           (Body:)     {String}    [type_filter]       Loại filter, nhận các giá trị <code>role_group_user: filter với quyền USER của account đang thực hiện hành động</code>, không có thì không truyền lên
@apiParam           (Body:)     {Array}     [team_filter]       nhận giá trị <code>have_team: account nằm trong team</code>, <code>not_have_team: account không nằm trong team</code>, không có thì không truyền lên
@apiParam           (Body:)     {Array}     [team_ids]          Danh sách team id
@apiParam           (Body:)     {String}    [search]            Username/họ tên user muốn tìm kiếm
@apiParam           (Body:)     {Int}       [page]              Số trang
@apiParam           (Body:)     {Int}       [per_page]          Số lượng bản ghi trên mỗi trang
@apiUse account_status_body
                                                              

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id_team": "13a0a31a-c4ba-40bc-afda-c6858143f42e",
            "list_account": [
                {
                    "account_id": "0d8ee9eb-68a5-437f-b1d8-db3a5b5eb54b",
                    "permission": "MEMB ER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Lộc",
                    "avatar": "",
                    "phone_number": "",
                    "email": "<EMAIL>",
                    "is_admin": 1,
                    "status": 1,
                    "status_custom": "on_leave"
                },
                {
                    "account_id": "9c437c4a-4915-4343-b499-56932d72a8ae",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Bằng",
                    "avatar": "",
                    "phone_number": "",
                    "email": "",
                    "is_admin": 1,
                    "status": 1,
                    "status_custom": "on_leave"
                }
            ],
            "name": "new team 333"
        },
        {
            "id_team": "401d2145-48c2-41c3-84c1-3dc6185c1dbe",
            "list_account": [
                {
                    "account_id": "cad9cc6e-286b-4dd1-b734-afdd81b69a73",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Lộc",
                    "avatar": "",
                    "phone_number": "",
                    "email": "<EMAIL>",
                    "is_admin": 1,
                    "status": 1,
                    "status_custom": "on_leave"
                }
            ],
            "name": "team makettin"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* API CHECK PERMISSION ACCOUNT **********************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {GET} /api/v2.1//team/account/permission api check permission account of team
@apiDescription check permission
@apiVersion 1.0.0
@apiGroup Team
@apiName CheckPermissionAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}  module_name       tên module.
<li>Example: <code>SOCIAL</code>, <code>SALE</code></li>
@apiParam   (Query:)    {String}  [team_id]          id của team.
@apiParam   (Query:)    {String}  account_id        id của account.

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "account_id": "1556bfa8-f0f2-461b-8bea-2645670e943e",
        "permission": "MEMBER",
        "team_id": "ab9fa537-9709-4d37-b21d-f8991bff49a7"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
******************************* API LIST ACCOUNT BY PERMISSION **********************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {GET} /api/v2.1/team/account-by-permission  api list account of team by permission when login
@apiDescription api list account of team by permission when login
@apiVersion 1.0.0
@apiGroup Team
@apiName ListAccountByPermission

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}  module_name       module cần kiểm tra (SALE, ...).
<li>Example: <code>SOCIAL</code>, <code>SALE</code></li>
@apiParam   (Query:)    {String}  account_id       id account cần kiểm tra .

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "list_account": [
            {
                "account_id": "9kc0a33c-baf5-11e7-a7c2-0242ac180003",
                "permission": "LEADER"
            },
            {
                "account_id": "560aee81-3e9d-4f29-98b6-b52f6e61b9b9",
                "permission": "MEMBER"
            }
        ]
        "team_id": "ab9fa537-9709-4d37-b21d-f8991bff49a7"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* INSERT MODULE **********************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {POST} /api/v2.1/module-teams  api insert MODULE
@apiDescription api insert MODULE
@apiVersion 1.0.0
@apiGroup Team
@apiName InsertModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiParamExample  {json}  Body:
{
  "name": "test1",
  "label": "test"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* UPDATE MODULE **********************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {PUT} /api/v2.1/module-teams/<module_team_id>  update MODULE
@apiDescription api update MODULE
@apiVersion 1.0.0
@apiGroup Team
@apiName UpdateModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiParamExample  {json}  Body:
{
  "name": "test1",
  "label": "test"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* API Find LIST TEAM *************************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {GET} /api/v2.1/teams/find danh sách team theo danh sách team id 
@apiDescription trả về thông tin tên team 
@apiVersion 1.0.0
@apiGroup Team
@apiName FindTeam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Query:)   {String}   team_ids   team id, nhiều id cách nhau dấu ,

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "describe": null,
            "id_team": "1ab5bb28-b9af-4da4-bd98-85ac77c7fd2f",
            "name": "cuong",
            
        },
        {
            "describe": null,
            "id_team": "c2ee5485-a185-49cf-85c7-3b02a35e7483",
            "name": "anb",
            
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


******************************* API Find Account by account_id *************************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {post} /api/v2.1/team/accounts Lấy danh sách thông tin acount
@apiDescription Lấy danh sách thông tin account và team theo danh sách id
@apiVersion 1.0.0
@apiGroup Team
@apiName FindAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.


@apiParam       (Body:)         {String}        [module_name]                   module dùng team. (VD: "SALE", "SOCIAL", ...)
@apiParam       (Body:)         {List}          [account_ids]                   mảng account id.
@apiParam       (Body:)         {String}        [status_custom]                 Trạng thái tuỳ chỉnh của tài khoản
                                                                                <li><code>on_leave</code>: Đang vắng mặt</li>
@apiUse account_status_body


@apiParamExample {json} Body
{
  "account_ids":["********-0a63-4bff-bf96-e2431a3e4b91","015edfa2-d94d-42d4-8489-c7b7226c9dcb"],
  "module_name": "SALE"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
            "username": "Locnh",
            "fullname": "Nguyễn Hữu Lộc",
            "avatar": "",
            "phone_number": "",
            "email": "<EMAIL>",
            "is_admin": 1,
            "status": 1,
            "status_custom": "on_leave"
            "team": [
                {
                    "id_team": "c2ee5485-a185-49cf-85c7-3b02a35e7483",
                    "name": "team 1",
                    "permission": "MEMBER",
                    "module": "SALE",
                }
            ]
        },...
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* API LIST ACCOUNT HISTORY **********************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {GET} /api/v2.1/team/history-accounts  danh sách tài khoản của team theo thời gian 
@apiDescription danh sách tài khoản của team theo thời gian
@apiVersion 1.0.0
@apiGroup Team
@apiName HistoryAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}  module_name       module cần lấy danh sách team.
<li>Example: <code>SOCIAL</code>, <code>SALE</code></li>
@apiParam   (Query:)    {String}  [team_id]          id của team cần lấy lịch sử, nhiều giá trị cách nhau dấu ;
@apiParam   (Query:)    {String}  [account_id]          id của account, lấy các team mà account từng được gán, nhiều giá trị cách nhau dấu ;
@apiParam   (Query:)    {String}  [start_date]       từ ngày định dạng YYYYMMDD
@apiParam   (Query:)    {String}  [end_date]       đến ngày định dạng YYYYMMDD

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id_team": "13a0a31a-c4ba-40bc-afda-c6858143f42e",
            "list_account": [
                {
                    "account_id": "0d8ee9eb-68a5-437f-b1d8-db3a5b5eb54b",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Lộc",
                    "avatar": "",
                    "phone_number": "",
                    "email": "<EMAIL>",
                    "is_admin": 1,
                },
                {
                    "account_id": "9c437c4a-4915-4343-b499-56932d72a8ae",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Bằng",
                    "avatar": "",
                    "phone_number": "",
                    "email": "",
                    "is_admin": 1,
                }
            ],
            "name": "new team 333"
        },
        {
            "id_team": "401d2145-48c2-41c3-84c1-3dc6185c1dbe",
            "list_account": [
                {
                    "account_id": "cad9cc6e-286b-4dd1-b734-afdd81b69a73",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Lộc",
                    "avatar": "",
                    "phone_number": "",
                    "email": "<EMAIL>",
                    "is_admin": 1,
                }
            ],
            "name": "team makettin"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""



"""
@api {get} /api/v2.1/module/accounts Lấy danh sách thông tin acount theo module
@apiDescription Lấy danh sách thông tin acount theo module
@apiVersion 1.0.0
@apiGroup Team
@apiName ModuleAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.


@apiParam   (Query:)    {String}  module_name  module dùng team. (VD: "SALE", "SOCIAL", ...)
@apiUse status_query_params

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
            "username": "Locnh",
            "fullname": "Nguyễn Hữu Lộc",
            "avatar": "",
            "phone_number": "",
            "email": "<EMAIL>",
            "is_admin": 1,
            "role_group": "user"
        },...
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""



"""
@api {GET} /api/v2.1/teams/find/module danh sách team theo module 
@apiDescription trả về thông tin team theo module
@apiVersion 1.0.0
@apiGroup Team
@apiName FindTeamModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Query:)   {String}   module_name   module dùng team. (VD: "SALE", "SOCIAL", ...)

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
         {
            "describe": "",
            "id_team": "922e912e-7ced-4633-83e1-e5387aa47209",
            "name": "team 2",
            "total_account": 4
        },
        {
            "describe": "",
            "id_team": "ce223d0b-5a90-4f77-8638-dba97ae9b9d4",
            "name": "team 1",
            "total_account": 3
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {post} /api/v2.1/team/accounts/detail lọc lấy danh sách team và account của team 
@apiDescription lọc lấy danh sách team và account của team 
@apiVersion 1.0.0
@apiGroup Team
@apiName FindDetailTeam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.


@apiParam (Body:) {String}  module_name  module dùng team. (VD: "SALE", "SOCIAL", ...)
@apiParam (Body:) {List}  [account_ids]  mảng account id.
@apiParam (Body:) {List}  [team_ids]  mảng team id.
@apiParam       (Body:)         {String}        [status_custom]                 trả thêm thông tin trạng thái đặc biệt của tài khoản, không phải điều kiện lọc dữ liệu 
                                                                                <li><code>on_leave</code>: Đang vắng mặt</li>
@apiParam       (Body:)         {String}        [account_status]                Trạng thái tài khoản
                                                                                <li><code>active</code>: Đang hoạt động</li>
                                                                                <li><code>close</code>: Đóng</li>
                                                                                <li><code>all</code>: Tất cả trạng thái</li>
                                                                                <li>Default: <code>active</code></li>

@apiParamExample {json} Body
{
  "account_ids":["********-0a63-4bff-bf96-e2431a3e4b91","015edfa2-d94d-42d4-8489-c7b7226c9dcb"],
  "module_name": "SALE",
  "team_ids": ["922e912e-7ced-4633-83e1-e5387aa47209"],
  "status_custom": "on_leave",
  "account_status": "all",
}


@apiSuccess {Int}            status          Trạng thái tài khoản: 1 - Đang hoạt động, 4 - Đóng
@apiSuccess {String}         status_custom   kết quả kiểm tra trạng thái:  on_leave - Đang vắng mặt


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "describe": "",
            "id_team": "922e912e-7ced-4633-83e1-e5387aa47209",
            "list_account": [
                {
                    "account_id": "121d0906-a116-4e3f-a509-b1a943ee14e8",
                    "avatar": null,
                    "email": "<EMAIL>",
                    "fullname": "Thịnh Phạm",
                    "is_admin": 2,
                    "permission": "LEADER",
                    "phone_number": "+***********",
                    "role_group": "manager",
                    "staff_code": null,
                    "username": "thinh123@pingcomshop",
                    "status": 1,
                    "status_custom": "on_leave"
                },
                ...
            ],
            "name": "team 2"
        },
        {
            "describe": "",
            "id_team": "0ef9664f-b052-4782-9e1a-39fce73899b7",
            "list_account": [
                {
                "account_id": "15bf46b6-4df3-43f7-bfe8-2f2ba1f89d55",
                "avatar": null,
                "email": "<EMAIL>",
                "fullname": "Thái ",
                "is_admin": 2,
                "permission": "MEMBER",
                "phone_number": "+***********",
                "role_group": "user",
                "staff_code": null,
                "username": "thai123@pingcomshop",
                "status": 4,
                },
                ...
            ],
            "name": "Team HCM"
        },
        ,...
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/team/find/account danh sách account theo team module 
@apiDescription danh sách account theo team module 
@apiVersion 1.0.0
@apiGroup Team
@apiName FindAccountTeamModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Query:)   {String}   module_name   module dùng team. (VD: "SALE", "SOCIAL", ...)
@apiParam   (Query:)   {String}   team_id   team id cần lấy danh sách account 


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
         {
            "account_id": "404d3c3a-441e-40f3-8970-10b1452bcceb",
            "avatar": "",
            "email": "<EMAIL>",
            "fullname": "Huyền Minh",
            "is_admin": 2,
            "permission": "MEMBER",
            "phone_number": "+***********",
            "role_group": "admin",
            "staff_code": null,
            "username": "huyenptm@pingcomshop"
        },
        {
            "account_id": "b939d995-9c64-4794-9e44-2b7e243e5145",
            "avatar": null,
            "email": "<EMAIL>",
            "fullname": "Lan",
            "is_admin": 2,
            "permission": "MEMBER",
            "phone_number": "+***********",
            "role_group": "admin",
            "staff_code": null,
            "username": "admin001@pingcomshop"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/team/find/account/by-role danh sách account theo quyền   
@apiDescription danh sách account được quyền xem dữ liệu báo cáo 
@apiVersion 1.0.0
@apiGroup Team
@apiName FindAccountByRole

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Query:)   {String}     module_name         module dùng team. (VD: "SALE", "SOCIAL", ...)
@apiParam   (Query:)   {String}     account_id          id account
@apiUse account_status_query_params

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
         {
            "account_id": "404d3c3a-441e-40f3-8970-10b1452bcceb",
            "avatar": "",
            "email": "<EMAIL>",
            "fullname": "Huyền Minh",
            "is_admin": 2,
            "permission": "MEMBER",
            "phone_number": "+***********",
            "role_group": "admin",
            "staff_code": null,
            "username": "huyenptm@pingcomshop"
        },
        {
            "account_id": "b939d995-9c64-4794-9e44-2b7e243e5145",
            "avatar": null,
            "email": "<EMAIL>",
            "fullname": "Lan",
            "is_admin": 2,
            "permission": "MEMBER",
            "phone_number": "+***********",
            "role_group": "admin",
            "staff_code": null,
            "username": "admin001@pingcomshop"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {post} /api/v2.1/team/find/by-code lấy thông tin team theo mã team  
@apiDescription cho phép tìm kiếm theo nhiều mã team 
@apiVersion 1.0.0
@apiGroup Team
@apiName FindTeamCode 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.


@apiParam (Body:) {String}  merchant_id  id merchant 
@apiParam (Body:) {List}  team_codes  danh sách mã team 
@apiParam (Body:) {String}  [module_name]  mã module: SALE, TICKET, ... 

@apiParamExample {json} Body
{
  "merchant_id": "********-0a63-4bff-bf96-e2431a3e4b91",
  "team_codes": ["CSKH"],
  "module_name": "SALE"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "922e912e-7ced-4633-83e1-e5387aa47209",
            "name": "team 2",
            "team_code": "CSKH",
            "merchant_id": "********-0a63-4bff-bf96-e2431a3e4b91"
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* API GET LEADER OF TEAM IDS **********************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {POST} /api/v2.1/teams/leader  API lấy thông tin leader của danh sách team id
@apiDescription get leader of team ids
@apiVersion 1.0.0
@apiGroup Team
@apiName GetLeaderOfTeamIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}  module_name       module cần lấy danh sách team.
<li>Example: <code>SALE</code>,...</li>
@apiParam   (Body:)    {Array}  list_team_id          Danh sách team id.

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "account_info": [
                {
                    "account_id": "327d892b-c261-4f69-86e3-196c84388d01",
                    "email": "<EMAIL>",
                    "fullname": "Thu Hà",
                    "phone_number": "+***********",
                    "username": "thuha01@pingcomshop"
                },
                {
                    "account_id": "df806712-e42c-4cc3-a6fe-adaaecd8d63e",
                    "email": "<EMAIL>",
                    "fullname": "thuha01",
                    "phone_number": "+***********",
                    "username": "haptt01@pingcomshop"
                }
            ],
            "id_team": "03c439ea-45f3-11ed-92e8-b635bf5ba62a",
            "team_name": "A"
        },
        {
            "account_info": [
                {
                    "account_id": "8eb05e7a-0015-4b91-9ba6-c7d0a08c1c3f",
                    "email": "<EMAIL>",
                    "fullname": "Mai DT",
                    "phone_number": "+***********",
                    "username": "maidt@testmoi"
                },
                {
                    "account_id": "96d8723f-8e67-44be-813d-7f641598a31f",
                    "email": "<EMAIL>",
                    "fullname": "User",
                    "phone_number": "+***********",
                    "username": "thuha011@testmoi"
                },
                {
                    "account_id": "d29cbfc7-7236-4d1c-9a96-5c2d70880531",
                    "email": "<EMAIL>",
                    "fullname": "Manager",
                    "phone_number": "+***********",
                    "username": "thuha02@testmoi"
                }
            ],
            "id_team": "6cf854cc-3a28-11ed-9cff-16f95825148d",
            "team_name": "Team Duy Tân"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/team/pagination Danh sách team có lọc và phân trang 
@apiDescription Phân trang, page -1 lấy tất cả 
@apiVersion 1.0.0
@apiGroup Team
@apiName ListTeamPagination

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header
  
@apiParam   (Query:) {String}   [search]   Chuỗi tìm kiếm. 
@apiParam   (Query:) {String}   [module_name]   module cần tìm(SALE, SOCIAL, ...), nhiều giá trị cách nhau dấu , 
@apiParam   (Query:) {String}   [type_filter]   loại tìm kiếm: role_group_user (tk đang đăng nhập role group là user thì chỉ nhìn thấy team mà user đang được gán)

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": 'team 1',
            "module_name": "SALE;SOCIAL",
        },
        {
            "id": "772e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": 'team 2',
            "module_name": "SALE",
        },
    ],
    "paging": {
        "page": 1,
        "per_page": 15,
        "total_count": 222,
        "total_page": 15
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {POST} /api/v2.1/team/by-ids Danh sách team theo id 
@apiDescription list id 
@apiVersion 1.0.0
@apiGroup Team
@apiName ListTeamByID

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:) {Array}   [team_ids]    danh sách team id    

@apiParamExample {json} Body
{
  "team_ids": ["772e6e1d-8891-4fdb-9d02-8a7855393298", "972e6e1d-8891-4fdb-9d02-8a7855393298"],
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": 'team 1',
            "module_name": "SALE;SOCIAL",
        },
        {
            "id": "772e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": 'team 2',
            "module_name": "SALE",
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} /api/v2.1/teams/search Danh sách team theo module name và account theo team
@apiDescription có phân trang 
@apiVersion 1.0.0
@apiGroup Team
@apiName ListTeamAndAccountByModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query:) {String}  [team_ids]  danh sách id team, nhiều giá trị cách nhau dấu , (MAX 50 phần tử) 
@apiParam   (Query:) {String}   [module_name]    Module name nhiều giá trị cách nhau dấu , (SALE, TICKET, SOCIAL hoặc để trống là tất các module)
@apiParam   (Query:)    {String}       [search]                tên team cần tìm kiếm   
@apiParam   (Query:) {int}      [page]        Vị trí page cần lấy dữ liệu. MIN_VALUE=1 Example: &page=2, Default value: 1
@apiParam   (Query:) {int}      [per_page]    Số phần tử trên một page. MAX=50 Example: &per_page=5

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
          "accounts": [
            {
              "id_account": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
              "username": "anhnv@mobio",
              "team_assign": [
                {
                  "module_name": "SALE",
                  "permission": "MEMBER"
                },
                {
                  "module_name": "SOCIAL",
                  "permission": "MEMBER"
                },
                {
                  "module_name": "TICKET",
                  "permission": "LEADER"
                }
              ]
            }
          ],
          "describe": "",
          "id_team": "43d4a8bc-2d9d-11ed-b03a-4642a87770bf",
          "team_name": "Team Hà Nội"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 1,
        "total_count": 981,
        "total_page": 981
    }
}
"""

"""
@api {GET} /api/v2.1/team/account-search Danh sách account theo team
@apiDescription get_account_by_team
@apiVersion 1.0.0
@apiGroup Team
@apiName ListAccountByTeam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header


@apiParam   (Query:) {String}   module_name    module name 
@apiParam   (Query:) {String}   team_id     id_team id team muốn tìm kiếm    
@apiParam   (Query:) {String}   [search]    Từ khóa muốn tìm kiếm (Tìm kiếm theo fullname và username) 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "email": "<EMAIL>",
            "fullname": "Thanh hà",
            "id": "a16a7da3-eb89-4836-9754-be020348d811",
            "permission": "MEMBER",
            "role_group": "user",
            "team_id": "0ef9664f-b052-4782-9e1a-39fce73899b7",
            "username": "thanhha123@pingcomshop"
        },
        {
            "email": "<EMAIL>",
            "fullname": "thanh nguyễn 1",
            "id": "277c9239-48b4-4f2c-8e3f-338e0762d17b",
            "permission": "MEMBER",
            "role_group": "user",
            "team_id": "0ef9664f-b052-4782-9e1a-39fce73899b7",
            "username": "thanhnt2@pingcomshop"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 15,
        "total_count": 2,
        "total_page": 1
    }
}
"""
#------------------------------------------------------
"""
@api {GET} /api/v2.1/team/count/by-module Số lượng team theo module
@apiDescription count_team_by_module
@apiVersion 1.0.0
@apiGroup Team
@apiName CountNumberByModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:) {String}   [module_name]    Tên module, nhiều giá trị cách nhau bởi dấu phẩy "," 
@apiParam   (Query:) {String}   [search]          Từ khóa cần tìm kiếm 
@apiParam   (Query:) {String}   [field_search]    Trường cần tìm kiếm (account, team_name)


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "module_name": "TICKET",
            "total": 5
        },
        {
            "module_name": "SALE",
            "total": 17
        },
        {
            "module_name": "SOCIAL",
            "total": 8
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "total": 28
}
"""
# -------------------------------------------

"""
@api {GET} /api/v2.1/team/id-by-module Danh sách team id theo module  
@apiDescription lấy tất cả 
@apiVersion 1.0.0
@apiGroup Team
@apiName ListTeamIDByModule 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
  
@apiParam   (Query:) {String}   module_name   module cần tìm(SALE, SOCIAL, ...) 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": ["972e6e1d-8891-4fdb-9d02-8a7855393298", "772e6e1d-8891-4fdb-9d02-8a7855393298"],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/teams/by-account  danh sách team và account trong team 
@apiDescription merchant hdb với user role user thì chỉ lấy danh sách team mà user đó được gán, các merchant khác lấy all team 
@apiVersion 1.0.0
@apiGroup Team
@apiName ListTeamByAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam       (Query:)    {String}            module_name                     module cần lấy danh sách team.
                                                                                <li>Example: <code>SOCIAL</code>, <code>SALE</code></li>
@apiParam       (Query:)    {String}            [status_custom]                 Trạng thái tuỳ chỉnh của tài khoản
                                                                                <li><code>on_leave</code>: Đang vắng mặt</li>
@apiUse account_status_query_params

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id_team": "13a0a31a-c4ba-40bc-afda-c6858143f42e",
            "list_account": [
                {
                    "account_id": "0d8ee9eb-68a5-437f-b1d8-db3a5b5eb54b",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Lộc",
                    "phone_number": "",
                    "email": "<EMAIL>",
                    "status": 1,
                    "status_custom": "on_leave"
                },
                {
                    "account_id": "9c437c4a-4915-4343-b499-56932d72a8ae",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Bằng",
                    "phone_number": "",
                    "email": "",
                    "status": 4
                }
            ],
            "name": "new team 333"
        },
        {
            "id_team": "401d2145-48c2-41c3-84c1-3dc6185c1dbe",
            "list_account": [
                {
                    "account_id": "cad9cc6e-286b-4dd1-b734-afdd81b69a73",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Lộc",
                    "phone_number": "",
                    "email": "<EMAIL>",
                    "status": 1,
                    "status_custom": "on_leave"
                }
            ],
            "name": "team makettin"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {POST} /api/v2.1/team/accounts/by-ids Danh sách account theo team id 
@apiDescription truyền list team id 
@apiVersion 1.0.0
@apiGroup Team
@apiName ListAccountByTeamIDs 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:) {Array}   [team_ids]    danh sách team id    

@apiParamExample {json} Body
{
  "team_ids": ["772e6e1d-8891-4fdb-9d02-8a7855393298", "972e6e1d-8891-4fdb-9d02-8a7855393298"],
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "772e6e1d-8891-4fdb-9d02-8a7855393298": ["cad9cc6e-286b-4dd1-b734-afdd81b69a73"],
        "972e6e1d-8891-4fdb-9d02-8a7855393298": ["cad9cc6e-286b-4dd1-b734-afdd81b69a73", "9c437c4a-4915-4343-b499-56932d72a8ae"],
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/teams/filter Danh sach team v4x 
@apiDescription Danh sách team v4x
@apiVersion 1.0.0
@apiGroup Team
@apiName ListTeamV4x

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)    {String}        [search]                Ký tự cần tìm kiếm   
@apiParam   (Query:)    {string}        [field_search]          Loại tìm kiếm [account, team]
@apiParam   (Query:)    {String}        [status_custom]         Trạng thái tuỳ chỉnh của tài khoản
                                                                <li><code>on_leave</code>: Đang vắng mặt</li>
@apiParam   (Query:)    {String}        [account_status]        Trạng thái tài khoản
                                                                <li><code>active</code>: Đang hoạt động</li>
                                                                <li><code>close</code>: Đóng</li>
                                                                <li><code>all</code>: Tất cả trạng thái</li>
                                                                <li>Default: <code>active</code></li>
@apiParam   (Query:)    {String}        [sort]                  Loại sort 
@apiParam   (Query:)    {string}        [order]                 Trường sort
@apiParam   (Query:)    {Array}         [ids]                   Danh sách team_id cần tìm kiếm, cách nhau bởi dấu <code>,</code> VD bac5b258-668a-11ef-916d-b91f3feca526,ada748d6-650f-11ef-afaf-9d49568e8cf5

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "accounts": [
                {
                     "account_id": "***************"
                    "avatar": null,
                    "email": "<EMAIL>",
                    "fullname": "linhlt",
                    "staff_code": "",
                    "status": 1,
                    "status_custom": "on_leave",
                    "team_assign": [
                        {
                            "module_name": "SALE",
                            "permission": "MEMBER"
                        },
                        {
                            "module_name": "TICKET",
                            "permission": "MEMBER"
                        }
                },
         
            ],
            "describe": "eased",
            "id_team": "06a7f84d-008f-11ef-9f3a-bdd4034334fa",
            "module_name": ["SOCIAL", "SALE", "TICKET"],
            "name": "esea",
            "policies": [
                "13c3a1b2-fc6c-11ee-bda3-31a46cb398f7",
                "1bd4b972-fc6e-11ee-b65c-69413c8b9c69"
            ],
            "team_code": "06a7f84d-008f-11ef-9f3a-bdd4034334fa"
        },
  
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 1,
        "total_count": 2,
        "total_page": 2
    }
}

"""

"""
@api {POST} /api/v2.1/teams/create   Tạo team v4x 
@apiDescription Tạo team v4x
@apiVersion 1.0.0
@apiGroup Team
@apiName CreateTeamV4x 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)    {String}         name              Tên team  
@apiParam   (Body:)    {string}       [describe]           Mô tả team 
@apiParam   (Body:)    {Object}       modules                      
@apiParam   (Body:)    {List}       modulse.SALE                  Module Sale 
@apiParam   (Body:)    {List}       modulse.SALE.permission                Chức vụ 
@apiParam   (Body:)    {List}       modulse.SALE.account_id                id account 

@apiParam   (Body:)    {List}       modulse.TICKET                  Module Sale 
@apiParam   (Body:)    {List}       modulse.TICKET.permission                Chức vụ 
@apiParam   (Body:)    {List}       modulse.TICKET.account_id                id account 

@apiParam   (Body:)    {List}       modulse.SOCIAL                  Module Sale 
@apiParam   (Body:)    {List}       modulse.SOCIAL.permission                Chức vụ 
@apiParam   (Body:)    {List}       modulse.SOCIAL.account_id                id account 
@apiParam   (Body:)    {List}       [policies]                                Danh sách ID policies 

@apiParamExample {json} Body
{
    // "name": "quang12345",
    "describe": "quang 1",
    "modules": {
        "SALE": [

            {
                "permission": "MEMBER",
                "account_id": "54e726e2-a35b-4b22-86c9-cfc00883ffe3"
            }
        ],
        "TICKET": [


        ],
        "SOCIAL": [

        ]
    },
    "policies": []

    
}
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    // case 1: Tạo team thành công 
   {
    "code": 200,
    "data": {
        "accounts": [
            {
                "avatar": null,
                "email": "<EMAIL>",
                "fullname": "qa",
                "staff_code": "",
                "team_assign": [
                    {
                        "module_name": "SALE",
                        "permission": "MEMBER"
                    },
                    {
                        "module_name": "TICKET",
                        "permission": "LEADER"
                    }
                ],
                "username": "quangtest123@ctyoho"
            },
            {
                "avatar": null,
                "email": "<EMAIL>",
                "fullname": "ánh 08",
                "staff_code": "uyuu77",
                "team_assign": [
                    {
                        "module_name": "SALE",
                        "permission": "MEMBER"
                    },
                    {
                        "module_name": "TICKET",
                        "permission": "MEMBER"
                    }
                ],
                "username": "user07@ctyoho"
            }
        ],
        "describe": "quang test",
        "id_team": "cea0e924-082f-11ef-ae24-f5b8d3f1fc7f",
        "name": "quang2",
        "policies": []
    },
    "lang": "vi",
    "message": "request thành công."
    }
    
    
    // case 2 Danh sách account không được để rỗng 
     "code": 413,
    "data": {
        "status": "data_account_not_null"
    }
    
    // case 3: Tên team đã tồn tại 
     "code": 413,
                "data": {"status": "Name_team_exists"}
    }
    
    // case 4: Account đã đã có module SOCIAL ở team khác trước đó 
    {
        "status": "user_have_module_social_an_other_team",
        "accounts": [
            "5fe6b5ef-ad0f-42db-9f43-e4178c809f51"
        ]
    }
    
    // case 5: Doanh nghiệp chưa đăng ký module 
    {
        "status": "not_register_module",
        "module": "sales"                   [sales, services] Danh sách các gói 
    }
        
    // case 6: Số lượng team quá giới hạn cho phép của gói đang sử dụng thời điểm hiện tài
    {
        "status": "team_exceeding_number",
        "module": sales,                    [sales, services] Danh sách các gói 
        "max": 10,                          Số lượng team tối đa 
    }
    
    // case 7: Số lượng  account quá giới hạn cho phép của gói đang sử dụng thời điểm hiện tại 
    {
           "status": "account_exceeding_number",
            "module": sales,        [sales, services] Danh sách các gói 
            "max": 10               Số lượng account tối đa 
    }
"""

#------------------------------------------------------------------------------------
"""
@api {PATCH} /api/v2.1/teams/<team_id>/update   Update team v4x 
@apiDescription Update team v4x
@apiVersion 1.0.0
@apiGroup Team
@apiName UpdateTeamV4x 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)    {String}         [name]             Tên team                  Nếu có sự thay đổi về tên thì gửi key còn không thì không gửi 
@apiParam   (Body:)    {string}       [describe]           Mô tả         Nếu có sự thay đổi về describe thì gửi key còn không thì không gửi 
@apiParam   (Body:)    {Object}       [modules]                          Nếu có sự thay đổi về account trong team  thì gửi key còn không thì không gửi                          
@apiParam   (Body:)    {List}       modulse.SALE                  Module Sale 
@apiParam   (Body:)    {List}       modulse.SALE.permission                Chức vụ 
@apiParam   (Body:)    {List}       modulse.SALE.account_id                id account 

@apiParam   (Body:)    {List}       modulse.TICKET                  Module Sale 
@apiParam   (Body:)    {List}       modulse.TICKET.permission                Chức vụ 
@apiParam   (Body:)    {List}       modulse.TICKET.account_id                id account 

@apiParam   (Body:)    {List}       modulse.SOCIAL                  Module Sale 
@apiParam   (Body:)    {List}       modulse.SOCIAL.permission                Chức vụ 
@apiParam   (Body:)    {List}       modulse.SOCIAL.account_id                id account 
@apiParam   (Body:)    {List}       [policies]                                Danh sách ID policies          Nếu không có sự thay đổi thì k gửi lên

@apiParamExample {json} Body
{
    // "name": "quang12345",
    "describe": "quang 1",
    "modules": {
        "SALE": [

            {
                "permission": "MEMBER",
                "account_id": "54e726e2-a35b-4b22-86c9-cfc00883ffe3"
            }
        ],
        "TICKET": [


        ],
        "SOCIAL": [

        ]
    },
    "policies": []

    
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    // case 1: Tạo team thành công 
  {
    "code": 200,
    "data": {
        "accounts": [
            {
                "avatar": null,
                "email": "<EMAIL>",
                "fullname": "qa",
                "staff_code": "",
                "team_assign": [
                    {
                        "module_name": "TICKET",
                        "permission": "MEMBER"
                    },
                    {
                        "module_name": "SALE",
                        "permission": "MEMBER"
                ],
                "username": "quangtest123@ctyoho"
            },
            {
                "avatar": null,
                "email": "<EMAIL>",
                "fullname": "ánh 08",
                "staff_code": "uyuu77",
                "team_assign": [
                    {
                        "module_name": "TICKET",
                        "permission": "MEMBER"
                    },
                    {
                        "module_name": "SALE",
                        "permission": "MEMBER"
                    }
                ],
                "username": "user07@ctyoho"
            }
        ],
        "describe": "quang 1",
        "name": "quang123456"
    },
    "lang": "vi",
    "message": "request thành công."
}


    // case 2 Danh sách account không được để rỗng 
     "code": 413,
    "data": {
        "status": "data_account_not_null"
    }

    // case 3: Tên team đã tồn tại 
     "code": 413,
                "data": {"status": "Name_team_exists"}
    }

    // case 4: Account đã đã có module SOCIAL ở team khác trước đó 
    {
        "status": "user_have_module_social_an_other_team",
        "accounts": [
            "5fe6b5ef-ad0f-42db-9f43-e4178c809f51"
        ]
    }

    // case 5: Doanh nghiệp chưa đăng ký module 
    {
        "status": "not_register_module",
        "module": "sales"                   [sales, services] Danh sách các gói 
    }

    // case 6: Số lượng team quá giới hạn cho phép của gói đang sử dụng thời điểm hiện tài
    {
        "status": "team_exceeding_number",
        "module": sales,                    [sales, services] Danh sách các gói 
        "max": 10,                          Số lượng team tối đa 
    }

    // case 7: Số lượng  account quá giới hạn cho phép của gói đang sử dụng thời điểm hiện tại 
    {
           "status": "account_exceeding_number",
            "module": sales,        [sales, services] Danh sách các gói 
            "max": 10               Số lượng account tối đa 
    }
}
"""

# ----------------------------------DELETE ACCOUNT FROM MODULE TEAM --------------------------------------------------
"""
@api {DELETE} /api/v2.1/teams/<team_id>/delete-accounts   Xoa account khoi module team 
@apiDescription Xoa account khoi module team 
@apiVersion 1.0.0
@apiGroup Team
@apiName DeleteAccountFromTeamByModule 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Querry:)    {string}        ids_account           Danh sách account                Danh sách account muốn xóa nhiều gì trị cách nhau bởi dấu ","
@apiParam   (Querry:)    {string}      module_name                 Module cuủa taài khoản muốn xóa   [SALE, TICKET, SOCIAL]



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    // case 1: Thành công 
    "data": {
        "team_id": "1321321312313123123",
        "ids_account": [],
        "module_name": SALE
    }
    
    // case 2: Không được phép xóa account khỏi module team khi còn duy nhất 1 thành viên trong team
    {
          "code": 413,
          "status": "not_del_acc_when_only_one_one_member"
    }
    
}
"""

# ----------------------------------GET INFO PACKAGE AND TOTAL TEAM NOW --------------------------------------------------
"""
@api {GET} /api/v2.1/license/packages/info-team  Thông tin gói license và tổng số team hiện tại 
@apiDescription Thông tin gói license và tổng số team hiện tại 
@apiVersion 1.0.0
@apiGroup Team
@apiName GetInfoPackageAndTotalTeamNow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        ## Nếu module không xuất hiện => doanh nghiệp không đăng ký module đó (không được tạo chức năng phụ trách đi theo module đó) 
        frontend căn cứ để ẩn chức năng phụ trách theo module không có trong danh sách    
        {
            "expire_time": **********,
            "max": 1,                        // Nêu max bằng 0 bỏ qua không cần đi check số lượng team, Max bằng 0 khi unlimited = True
            "module": ["TICKET", "SOCIAL"],
            "package_code": "free",
            "start_time": **********,
            "total_team_now": 1,
            "unlimited": false              // Nếu unlimited = True: không giới hạn số lượng team được tạo và ngược lại  và nếu unlimited = False  
                                                 đi kiểm tra field  total_team_now đã đạt đến giới hạn tối đa mà gói cho phép chưa ( field = max)
        }
    ]
    
    // case2 
    "data": []                              //Data rỗng không cần đi check số lượng team tối đa 
    
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {post} /api/v2.1/teams/accounts/permission lấy thông tin team quyền từ list account  
@apiDescription trả về cả leader team mà account đang là thành viên 
@apiVersion 1.0.0
@apiGroup Team
@apiName FindTeamPermission 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.


@apiParam (Body:) {List}  [module_name]  module dùng team. (VD: "SALE", "SOCIAL", "TICKET" ...)
@apiParam (Body:) {List}  account_ids  mảng account id.

@apiParamExample {json} Body
{
  "account_ids": ["********-0a63-4bff-bf96-e2431a3e4b91","015edfa2-d94d-42d4-8489-c7b7226c9dcb"],
  "module_name": ["SALE", "TICKET"]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id_team": "922e912e-7ced-4633-83e1-e5387aa47209",
            "list_account": [
                {
                    "account_id": "121d0906-a116-4e3f-a509-b1a943ee14e8",
                    "permission": "LEADER",
                    "module_name": "SALE"
                },
                {
                    "account_id": "121d0906-a116-4e3f-a509-b1a943ee14e8",
                    "permission": "MEMBER",
                    "module_name": "TICKET"
                },
                ...
            ],
            "name": "new team 333"
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {POST} /api/v2.1/teams/filter-team-module Danh sách account email theo team_ids
@apiDescription Danh sách account email theo team_ids
@apiVersion 1.0.0
@apiGroup Team
@apiName EmailInTeams 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)     {Array}       team_ids                      Danh sách team_id, max 10 team 1 lần
@apiParam   (Body:)     {Array}       module_name                   Module cần lấy danh sách. (VD: "SALE", "SOCIAL", "TICKET" ...)
@apiParam   (Body:)     {Array}       filters                       Lấy thông tin gì? Nhận các giá trị <code>account_id</code>, <code>email</code>, <code>push_id</code>. Mặc định <code>account_id</code>

@apiParamExample {json} Body
{   
    "team_ids": ["922e912e-7ced-4633-83e1-e5387aa47209"],
    "module_name": ["SALE"],
    "filters": ["account_id", "email", "push_id"]
}

@apiSuccess             {Array}       data                          
@apiSuccess             {String}      data.account_id               Id định danh account
@apiSuccess             {String}      data.email                    Email account
@apiSuccess             {Array}       data.push_ids                 Danh sách push_id của account


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "account_id": "121d0906-a116-4e3f-a509-b1a943ee14e8"
            "email": "<EMAIL>",
            "push_ids": [
                {
                    "device_id": "*********-**********-34304830940384uf-09t04594",
                    "device_operating_system": "ios",
                    "push_id": "4343-3453fedf-dfdfde0f9034e-df0d9fd0fdf-3ere32422-wefiefieif3-2342432rowokow",
                    "sandbox": true,
                    "app_code": "MOBIO_APP"
                }
            ]
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}

"""


"""
@api {post} /api/v2.1/teams/accounts/team-module Lấy danh sách tài khoản theo team module 
@apiDescription nếu tài khoản thuộc nhiều team thì trả về 1 
@apiVersion 1.0.0
@apiGroup Team
@apiName ListAccountUniqueTeamModule 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body:) {Array}     [team_ids]     danh sách id team 
@apiParam   (Body:) {String}   module_name    Module name  truyền duy nhất 1 gía trị(SALE, TICKET, SOCIAL)
@apiParam   (Body:) {String}   [search]    Từ khóa muốn tìm kiếm fullname, username
@apiParam   (Body:) {int}      page        Số trang
@apiParam   (Body:) {int}      per_page    Số phần tử trong 1 trang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "7a3f9273-1dd4-4835-a7bf-1be3ce7a445d",
            "username": "Locnh",
            "fullname": "Nguyễn Hữu Lộc",
            "avatar": "",
            "email": "<EMAIL>",
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 1,
        "total_count": 2,
        "total_page": 2
    }
}
"""


"""
@api {GET} /api/v2.1/teams/leader/by-account Danh sách account leader theo account
@apiDescription từ tài khoản và module lấy các tài khoản leader trong các team có chứa tài khoản cần tìm 
@apiVersion 1.0.0
@apiGroup Team
@apiName ListLeaderByAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header


@apiParam   (Query:) {String}   module_name    module name: SALE, TICKET, SOCIAL
@apiParam   (Query:) {String}   account_id      id tài khoản muốn tìm kiếm    

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "team_id": "13a0a31a-c4ba-40bc-afda-c6858143f42e",
            "account_leader": [
                {
                    "account_id": "0d8ee9eb-68a5-437f-b1d8-db3a5b5eb54b",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Lộc",
                    "phone_number": "",
                    "email": "<EMAIL>",
                    "avatar": "",
                },
                {
                    "account_id": "9c437c4a-4915-4343-b499-56932d72a8ae",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Bằng",
                    "phone_number": "",
                    "email": "",
                    "avatar": "",
                }
            ],
            "team_name": "new team 333"
        },
        {
            "team_id": "401d2145-48c2-41c3-84c1-3dc6185c1dbe",
            "account_leader": [
                {
                    "account_id": "cad9cc6e-286b-4dd1-b734-afdd81b69a73",
                    "permission": "MEMBER",
                    "username": "Locnh",
                    "fullname": "Nguyễn Hữu Lộc",
                    "phone_number": "",
                    "email": "<EMAIL>",
                    "avatar": "",
                }
            ],
            "team_name": "team makettin"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
}
"""

"""
@api {POST} /api/v2.1/teams/import        Import file team
@apiDescription import file team  theo loại 
@apiGroup Team
@apiVersion 1.0.0
@apiName ImportTeam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (files:)     {file}         file                    file muốn import  
@apiParam   (form:)     {string}        status_import           Hành động muốn thưc thi [add_team, change_user_in_team, remove_user_in_team, update_team]



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
}

"""


"""
@api {get} /api/v2.1/teams/export Export file team và account gắn trong team  
@apiDescription   export team
@apiVersion 2.1.0
@apiGroup Team
@apiName ExportTeam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}   [module_name]    Module name  truyền duy nhất 1 gía trị(SALE, TICKET, SOCIAL hoặc để trống là tất các module)
@apiParam   (Query:)    {String}       [search]                 Ký tự cần tìm kiếm   
@apiParam   (Query:)    {string}       [field_search]           Loại tìm kiếm [account, team]


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200
    
    // case 1 Dữ liệu nhỏ hơn 1000 bản ghi
    data = {
        "type_export": 1
        "link_download": "j1h24uh12edasdasdaassada",
        "file_name": "test-",
    }
    
    // case 2 Dữ liệu lớn hơn 1000 bản ghi
    data = {
        "type_export": 2,
        "link_download": "",
        "file_name": "",
    }
    
    // case 3 Không có dữ liệu
    data = {
        "type_export": 3,
        "link_download": "",
        "file_name": "",
    }
    
}
"""



"""
@api {post} /api/v2.1/teams/find/accounts lấy thông tin permission của tài khoản theo bộ lọc   
@apiDescription cho phép tìm theo team và account 
@apiVersion 1.0.0
@apiGroup Team
@apiName FindPermissionByFilter  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.


@apiParam (Body:) {List}  [account_ids]  danh sách id tài khoản 
@apiParam (Body:) {List}  [team_ids]  danh sách id team 
@apiParam (Body:) {String}  module_name  mã module: SALE, TICKET, ... 

@apiParamExample {json} Body
{
  "account_ids": ["********-0a63-4bff-bf96-e2431a3e4b91"],
  "team_ids": ["922e912e-7ced-4633-83e1-e5387aa47209"],
  "module_name": "SALE"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "team_id": "922e912e-7ced-4633-83e1-e5387aa47209",
            "account": [
                {
                    "account_id": "********-0a63-4bff-bf96-e2431a3e4b91",
                    "permission": "MEMBER",
                }
            ]
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {POST} /api/v2.1/teams/sync/bulk Đồng bộ thông tin team theo từng hành động  
@apiDescription cho các partner sử dụng để tạo/cập nhật team mobio 
@apiVersion 1.0.0
@apiGroup Team
@apiName TeamSyncBulk  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam   (Body:) {String}   requestId    id định danh cho lần gọi api 
@apiParam   (Body:) {String}   request_type   Loại yêu cầu thao tác: 
                                            <li><code>create_team</code>: Tạo team và gán user vào team</li>
                                            <li><code>assign_user</code>: Gán user vào team hoặc thay đổi vai trò của user trong team</li>
                                            <li><code>unassign_user</code>: Gỡ user khỏi team</li>
                                            <li><code>move_user</code>: Chuyển team cho user </li>

@apiParam   (Body:) {Array}   data    danh sách dữ liệu team và account tùy theo loại yêu cầu, maximum 100 
@apiParam   (Body:)   {Object}  callback Thông tin đầu nhận kết quả sau khi xử lý xong.

@apiParamExample {json} Body 
// create_team
{
  "requestId": "id request",       
  "request_type": "create_team",                            
  "data": [                                                    
    {
      "accounts": [                                          
        {
          "username": "anhnv@mobio",                             
          "team_assign": [                                     
            {
              "module_name": "SALE",                                  
              "permission": "MEMBER"                                  
            },
            {
              "module_name": "SOCIAL",
              "permission": "MEMBER"
            },
            {
              "module_name": "TICKET",
              "permission": "LEADER"
            }
          ]
        }
      ],
      "describe": "",                                          
      "team_name": "Team Hà Nội"                                 
    }
  ],
  "callback": {
    "type": "api",
    "url": "https://callback...",
    "method": "post",
    "headers": {
        "Authorization": "Basic ...",
        "Content-Type": "application/json"
      },
  }
}

// assign_user
{
  "requestId": "id request",
  "request_type": "assign_user",
  "data": [
    {
      "id_team": "43d4a8bc-2d9d-11ed-b03a-4642a87770bf",
      "accounts": [
        {
          "username": "anhnv@mobio",
          "team_assign": [
            {
              "module_name": "SALE",
              "permission": "MEMBER"
            },
            {
              "module_name": "SOCIAL",
              "permission": "MEMBER"
            },
            {
              "module_name": "TICKET",
              "permission": "LEADER"
            }
          ]
        }
      ]
    }
  ],
  "callback": {
    "type": "api",
    "url": "https://callback...",
    "method": "post",
    "headers": {
        "Authorization": "Basic ...",
        "Content-Type": "application/json"
      },
  }
}

// unassign_user
{
  "requestId": "id request",
  "request_type": "unassign_user",
  "data": [
    {
      "id_team": "43d4a8bc-2d9d-11ed-b03a-4642a87770bf",
      "accounts": [
        {
          "username": "anhnv@mobio",
          "module_name": ["SALE", "SOCIAL"]
        },
        {
          "username": "datlt@mobio",
          "module_name": ["ALL"]
        }
      ]
    }
  ],
  "callback": {
    "type": "api",
    "url": "https://callback...",
    "method": "post",
    "headers": {
        "Authorization": "Basic ...",
        "Content-Type": "application/json"
      },
  }
}
// move_user
{
  "requestId": "id request",
  "request_type": "move_user",
  "data": [
    {
      "id_team": "43d4a8bc-2d9d-11ed-b03a-4642a87770bf",
      "id_team_new": "8b2b56ba-e06c-47a4-b452-cb1dfb63d358",
      "accounts": [
        {
          "username": "anhnv@mobio",
          "module_name": ["SALE", "SOCIAL"]
        },
        {
          "username": "datlt@mobio",
          "module_name": ["ALL"]
        }
      ]
    }
  ],
  "callback": {
    "type": "api",
    "url": "https://callback...",
    "method": "post",
    "headers": {
        "Authorization": "Basic ...",
        "Content-Type": "application/json"
      },
  }
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "resultCode": "00",
  "resultMessage": "SUCCESS", 
}
"""


"""
@api {post} /api/v2.1/teams/team-of-account Lấy thông tin team của account 
@apiDescription trả về thông tin các team của tài khoản 
@apiVersion 1.0.0
@apiGroup Team
@apiName ListTeamOfAccount 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body:) {Array}     [team_ids]     danh sách id team, trả về danh sách account thuộc team lọc và các team của từng account  
@apiParam   (Body:) {Array}     [account_ids]     danh sách id account, trả về các team của account 


@apiParamExample {json} Info example
{
    "team_ids": ["4bad1402-21f4-11ee-a17f-45311bd761af"],   // ưu tiên tìm kiếm theo team nếu có giá trị 
    "account_ids": ["c2052960-eb1c-11ee-b488-e9c7a8fbfd6c"],    // muốn tìm theo account thì bỏ key team_ids
 }


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        // key là id account, value là mảng các team có account trong team 
        "c2052960-eb1c-11ee-b488-e9c7a8fbfd6c": ["4bad1402-21f4-11ee-a17f-45311bd761af"],
        ...
    }
}
"""


"""
@api {GET} /api/v2.1/accounts/team-info Danh sách tài khoản và thông tin team 
@apiDescription có phân trang, tài khoản có thể có hoặc không có thông tin team  
@apiVersion 1.0.0
@apiGroup Team
@apiName ListAccountAndTeamInfo 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam   (Query:) {int}      [page]        Vị trí page cần lấy dữ liệu. MIN_VALUE=1 Example: &page=2, Default value: 1
@apiParam   (Query:) {int}      [per_page]    Số phần tử trên một page. MAX=50 Example: &per_page=5

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "32f09021-f573-472e-8674-1e0812e1713e",
            "username": "locnh@mobio",
            "email": "<EMAIL>",
            "fullname": "Nguyễn Hữu Lộc",
            "role_group": "admin",
            "teams": [
                {
                  "id_team": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                  "team_name": "Team Hà Nội"
                  "team_assign": [
                    {
                      "module_name": "SALE",
                      "permission": "MEMBER"
                    },
                    {
                      "module_name": "SOCIAL",
                      "permission": "MEMBER"
                    },
                    {
                      "module_name": "TICKET",
                      "permission": "LEADER"
                    }
                  ]
                }
            ],
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 1,
        "total_count": 981,
        "total_page": 981
    }
}
"""

"""
@apiDefine QueryAction
@apiVersion 1.0.0

@apiParam   (Body: )        {String}           from_time                    Thời gian lọc từ ngày: <code>Định dạng: yyyy-MM-dd HH:mm:ss | ex: 2024-07-11 00:00:00 </code> (Giờ UTC), mặc định lấy ngày hiện tại
@apiParam   (Body: )        {String}           to_time                      Thời gian lọc đến ngày: <code>Định dạng: yyyy-MM-dd HH:mm:ss | ex: 2024-07-11 00:00:00 </code> (Giờ UTC), mặc định lấy ngày hiện tại
@apiParam   (Body: )        {Array}            [action]                     Danh sách hành động cần tìm kiếm, mỗi action là 1 object
                                                                            <li>Danh sách <a href="https://dev.mobio.vn/docs/administrator/#api-AccountActivity-ListAction">tại đâ<PERSON></code></li>
@apiParam   (Body: )        {Array}            action.key                   Hành động
@apiParam   (Body: )        {Array}            action.object_type           Đối tượng của hành động
@apiParam   (Body: )        {Array}            [source]                     Danh sách module cần tìm kiếm
                                                                            <li>Danh sách <a href="https://dev.mobio.vn/docs/administrator/#api-AccountActivity-ObjectHandled">tại đây</code></li>
@apiParam   (Body: )        {string}           [order_by]                   Sắp xếp theo field nào (Chỉ hỗ trợ sort theo 1 filed).
                                                                            <li>Nhận giá trị <code>action_time</code>: Thời gian phát sinh event</li>
                                                                            Nếu không truyền gì thì mặc định sắp xếp theo: <code>action_time</code>
@apiParam   (Body: )        {string}           [order_type]                 Thứ tự cần sắp xếp
                                                                            <li>Nhận giá trị <code>desc</code>, <code>asc</code></li>
                                                                            <li><code>asc</code>: tăng dần</li>
                                                                            <li><code>desc</code>: giảm dần</li>
                                                                            <li>Mặc định <code>desc</code></li>
@apiParam   (Body: )        {string}           [text_search]                Thông tin cần tìm kiếm dạng text (mô tả, ip address)
@apiParam   (Body: )        {Array}            [account_update]             Danh sách account_id tương tác (Account thực hiện hành động)
@apiParam   (Body: )        {Array}            [device_os]                  Danh sách thiết bị cần tìm kiếm 
                                                                            <li>Danh sách <a href="https://dev.mobio.vn/docs/administrator/#api-AccountActivity-ListDeviceOs">tại đây</code></li>

@apiParamExample {json} Form Config

{
  "action": [
    {
      "key": "import",
      "object_type": "profile"
    }
  ],
  "from_time": "2024-07-11 00:00:00",
  "to_time": "2024-07-11 00:00:00"
}

"""

"""
@apiDefine ResponseListActivity
@apiVersion 1.0.0

@apiSuccess     {Array}             data                                    Danh sách hành động
@apiSuccess     {String}            data.action                             Loại hành động
                                                                            <li>Danh sách hành động <a href="https://dev.mobio.vn/docs/administrator/#api-AccountActivity-ListAction">tại đây</a></li>
@apiSuccess     {Object}            data.detail                             Chi tiết hành động
                                                                            <li>Các field trong detail sẽ tuỳ thuộc theo từng hành động, thông tin chi tiết <a href="https://mobiojsc.sg.larksuite.com/wiki/GxeQwNll6i0lukk0LOrlzGjugfc#VHztdDyqFoBYz3xyiJLl0Xo9gTg">tại đây</a></li>
@apiSuccess     {String}            data.source                             Nguồn phát sinh event
@apiSuccess     {String}            data.action_time                        Thời gian phát sinh event, <code>Định dạng: yyyy-MM-dd HH:mm:ss | ex: 2024-07-11 00:00:00 </code>
@apiSuccess     {String}            data.account_update                     Id account thực hiện hành động
@apiSuccess     {String}            data.ip_address                         Địa chỉ ip phát sinh hành động
@apiSuccess     {String}            data.device_os                          Hệ điều hành
@apiSuccess     {String}            data.merchant_id                        Id tenant

"""


# Lấy danh sách action
"""
@api {GET} /adm/api/v2.1/accounts/actions/list                 API lấy danh sách action của account (tracking/event)
@apiGroup AccountActivity
@apiVersion 1.0.0
@apiName ListAction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess     {Array}             data                                    Danh sách action
@apiSuccess     {String}            data.key                                action key
@apiSuccess     {String}            data.name                               Tên của action
@apiSuccess     {String}            data.object_type                        Đối tượng của action
@apiSuccess     {String}            data.log_type                           Loại hành động <code>tracking</code>, <code>event</code>
@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "name": "Gỡ nhân viên khỏi team",
            "key": "remove_from_team",
            "object_type": "team"
            "log_type": "event"
        },
        {
            "name": "Thay đổi thông tin nhân viên",
            "key": "change_staff_info",
            "object_type": "user",
            "log_type": "tracking"
        }
        ...
    ],
    "message": "request thành công."
}
"""

# Lấy danh sách activity tracking của account
"""
@api {POST} /adm/api/v2.1/accounts/<account_id>/activity/events                 API danh sách event của account
@apiGroup AccountActivity
@apiVersion 1.0.0
@apiName ListActivityEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging

@apiUse QueryAction
@apiUse ResponseListActivity

@apiSuccess     {Array}             data                                    Danh sách event
@apiSuccess     {Array}             data.information                        Danh sách thông tin nhân viên bị thay đổi
                                                                            <li>Mô tả chi tiết <a href="https://mobiojsc.sg.larksuite.com/wiki/GxeQwNll6i0lukk0LOrlzGjugfc#JjGydoZDmoZ85Hx0BQ8l5WiOg5f">tại đây</a> </li>
@apiSuccess     {String}            data.information.field_key              Field thay đổi (<code>value_of_field_key</code>)
@apiSuccess     {String}            [data.information.value_of_field_key]   Là giá trị của field_key trong 1 vài trường hợp
                                                                            <li>VD: field_key <code>status_change</code> Trạng thái nhân viên thay đổi: </li>
                                                                            <code>{"field_key": "status_change", "status_change": "Đang hoạt động"}</code>

@apiSuccess     {String}            data.information.group_key              Nhóm thông tin
@apiSuccess     {String}            [data.information.change]               Sửa đổi
@apiSuccess     {String}            data.information.change.from            Value cũ
@apiSuccess     {String}            data.information.change.to              Value mới
@apiSuccess     {String}            [data.information.add]                  Thêm mới
@apiSuccess     {String}            [data.information.delete]               Xoá
@apiSuccess     {Array}             [data.group_config]                     Thông tin group_key
                                                                            <li><code>key</code>:  group_key</li>
                                                                            <li><code>name</code>: Tên group</li>
                                                                            <li><code>position</code>: Thứ tự hiển thị</li>

@apiSuccess     {String}            data.account_id                         Id định danh account

@apiSuccess     {String}            data.line_event                         Action thuộc cột event bên nào
                                                                            <li>Nhận các giá trị: <code>system</code>, <code>account</code></li>
                                                                            <li><code>system</code>:  hệ thống/doanh nghiệp</li>
                                                                            <li><code>account</code>: nhân viên</li>

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "id": "user_66c448632f80327e057ed376",
            "account_id": "0afbe866-9bdb-4db9-85a9-0ce189e001aa",
            "action_time": "2024-07-10 03:37:13",
            "group_config": [
                {
                    "key": "staff_detail",
                    "name": "Thông tin tài khoản",
                    "position": 1
                },
                {
                    "key": "staff_rm",
                    "name": "Thông tin nhân sự",
                    "position": 2
                },
                {
                    "key": "staff_role",
                    "name": "Quyền",
                    "position": 3
                }
            ],
            "information": [
                {
                    "change": [
                        {
                            "from": "Phuong wf",
                            "to": "Phuongwf"
                        }
                    ],
                    "field_key": "fullname",
                    "group_key": "staff_detail"
                },
                {
                    "change": [
                        {
                            "from": "+84962735860",
                            "to": "+84962735861"
                        }
                    ],
                    "field_key": "phone_number",
                    "group_key": "staff_detail"
                },
                {
                    "change": [
                        {
                            "from": "<EMAIL>",
                            "to": "<EMAIL>"
                        }
                    ],
                    "field_key": "email",
                    "group_key": "staff_detail"
                },
                {
                    "change": [
                        {
                            "from": "212312123p",
                            "to": "2123121231"
                        }
                    ],
                    "field_key": "staff_code",
                    "group_key": "staff_rm"
                },
                {
                    "change": [
                        {
                            "from": "admin",
                            "to": "user"
                        }
                    ],
                    "field_key": "role_group",
                    "group_key": "staff_role"
                },
                {
                    "add": [
                        {
                            "name": "Khách hàng cá nhân",
                            "id": "KHCN"
                        },
                        {
                            "name": "Khách hàng doanh nghiệp",
                            "id": "KHDN"
                        }
                    ],
                    "field_key": "block",
                    "group_key": "staff_rm"
                },
                {
                    "add": [],
                    "field_key": "group_department_ids",
                    "group_key": "staff_rm",
                    "remove": [
                        {
                            "name": "Ban Giám đốc",
                            "id": "ban_giam_doc"
                        }
                    ] 
                },
                {
                    "change": [
                        {
                            "from": {
                                "name": "Chi nhánh Test",
                                "id": "1002"
                            },
                            "to": {
                                "name": "Chi nhánh Hà Nội",
                                "id": "1001"
                            }
                        }
                    ],
                    "field_key": "sol_id",
                    "group_key": "staff_rm"
                },
                {
                    "change": [
                        {
                            "from": {
                                "name": "Chi nhánh Test",
                                "id": "KVBSH#1002_2024"
                            },
                            "to": {
                                "name": "Chi nhánh Hà Nội",
                                "id": "KVBSH#1001_2024"
                            }
                        }
                    ],
                    "field_key": "scope_code",
                    "group_key": "staff_rm"
                },
                {
                    "change": [
                        {
                            "from": {
                                "name": "Trợ lý GD",
                                "id": "CD011"
                            },
                            "to": {
                                "name": "Trợ lý",
                                "id": "CD010"
                            }
                        }
                    ],
                    "field_key": "position_id",
                    "group_key": "staff_rm"
                },
                {
                    "add": [
                        {
                            "description": "",
                            "name": "[P] wf",
                            "id": "a2c96d84-3dc6-11ef-a1ac-9747665036d1"
                        }
                    ],
                    "remove": [
                        {
                            "description": "",
                            "name": "[P] wf",
                            "id": "a2c96d84-3dc6-11ef-a1ac-9747665036d109"
                        }
                    ],
                    "field_key": "role",
                    "group_key": "staff_role",
                }
            ],
            "line_event": "account",
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "source": "user",
            "ip_address": "127.0.0.1",
            "device_os": "Windows"
        },
        {
            "event_type": "staff_click_link_web_push",
            "detail": {
              "subject": "Thông báo 1",
              "link": "https://mobio.io"
            }
            "account_id" : "b122802d-fe0d-4e9e-bdc1-4bcfc1ccf5eb",
            "merchant_id" : "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "line_event" : "account",
            "source" : "workflow",
            "action_time" : "2024-07-09 00:00:00",
            "account_update" : "65eebbe6-24c3-416a-bbe4-0aebe92f6695"
            "ip_address": "127.0.0.1",
            "device_os": "Windows"
        }
    ],
    "paging": {
        "page": 1,
        "per_page": 10,
        "total_count": 50,
        "total_page": 5
    },
    "message": "request thành công."
}
"""



"""
@api {GET} /adm/api/v2.1/object-handled           Danh sách đối tượng phát sinh activity
@apiGroup AccountActivity
@apiVersion 1.0.0
@apiName ObjectHandled

@apiSuccess     {Array}             data                                    Danh sách event
@apiSuccess     {String}            data.key                                Đối tượng
@apiSuccess     {String}            data.name                               Tên đối tượng

@apiSuccessExample {json} Response
{
  "code": 200,
  "data": [
    {
      "key": "user",
      "name": "User",
      "order": 1
    },
    {
      "key": "team",
      "name": "Team",
      "order": 2
    },
    {
      "key": "workflow",
      "name": "Workflow",
      "order": 3
    },
    {
      "key": "profile",
      "name": "Profile",
      "order": 4
    },
    {
      "key": "deal",
      "name": "Deal",
      "order": 5
    },
    {
      "key": "company",
      "name": "Company",
      "order": 6
    },
    {
      "key": "ticket",
      "name": "Ticket",
      "order": 7
    },
    {
      "key": "task",
      "name": "Task",
      "order": 8
    },
    {
      "key": "voucher",
      "name": "Voucher",
      "order": 9
    },
    {
      "key": "voucher_code_storage",
      "name": "Kho mã voucher",
      "order": 10
    },
    {
      "key": "loyalty",
      "name": "Loyalty",
      "order": 11
    }
  ],
  "lang": "en",
  "message": "request successful."
}
"""



"""
@api {GET} /adm/api/v2.1/accounts/events/fields           Danh sách field trong event
@apiGroup AccountActivity
@apiVersion 1.0.0
@apiName ListEventField

@apiSuccess     {Array}             data                                    Danh sách event
@apiSuccess     {String}            data.key                                Field key
@apiSuccess     {String}            data.name                               Tên field                   

@apiSuccessExample {json} Response
{
  "code": 200,
  "data": [
    {
      "key": "fullname",
      "name": "Họ và tên"
    },
    {
      "key": "email",
      "name": "E-mail"
    },
    {
      "key": "password",
      "name": "Mật khẩu"
    },
    {
      "key": "phone_number",
      "name": "Số điện thoại"
    },
    {
      "key": "block",
      "name": "Khối"
    },
    {
      "key": "group_department_ids",
      "name": "Phòng ban"
    },
    {
      "key": "sol_id",
      "name": "Đơn vị kinh doanh"
    },
    {
      "key": "scope_code",
      "name": "Mã cấp quản lý"
    },
    {
      "key": "position_id",
      "name": "Chức danh"
    },
    {
      "key": "staff_code",
      "name": "Mã nhân viên"
    },
    {
      "key": "role",
      "name": "Quyền"
    },
    {
      "key": "role_group",
      "name": "Nhóm quyền"
    },
    {
      "key": "status_change",
      "name": "Thay đổi trạng thái",
    },
    {
      "key": "create_team",
      "name": "Tạo team"
    },
    {
      "key": "delete_team",
      "name": "Xoá team"
    },
    {
      "key": "team_role_group",
      "name": "Thay đổi quyền trong team",
    },
    {
      "key": "add_to_team",
      "name": "Thêm vào team"
    },
    {
      "key": "remove_from_team",
      "name": "Xoá khỏi team"
    }
  ],
  "lang": "vi",
  "message": "request thành công."
}
"""


"""
@api {POST} /adm/api/v2.1/accounts/activity/tracking           API Danh sách user activity tracking
@apiGroup AccountActivity
@apiVersion 1.0.0
@apiName ActivityTrackingList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging

@apiUse QueryAction
@apiUse ResponseListActivity

@apiSuccess     {String}            data.account_id                         Id thực hiện hành động
@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "id": "user_66c448632f80327e057ed376",
            "action": "add",
            "account_id": "0afbe866-9bdb-4db9-85a9-0ce189e001aa",
            "action_time": "2024-07-10 03:37:13",
            "detail": {
              "id": "0afbe866-9bdb-4db9-85a9-0c1089e001aa",
              "name": "Nguyễn Văn A"
            },
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "source": "profle",
            "account_update": "0afbe866-9bdb-4db9-85a9-0ce189e001aa",
            "ip_address": "127.0.0.1",
            "device_os": "Windows"
        },
    ],
    "paging": {
        "page": 1,
        "per_page": 10,
        "total_count": 50,
        "total_page": 5
    },
    "message": "request thành công."
}
"""



"""
@api {POST} /adm/api/v2.1/accounts/activity/tracking/export           Export activity tracking
@apiGroup AccountActivity
@apiVersion 1.0.0
@apiName ExportActivityTracking

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse QueryAction
@apiParam   (Body: )        {String}           [from_time]                    Thời gian lọc từ ngày: <code>Định dạng: yyyy-MM-dd HH:mm:ss | ex: 2024-07-11 00:00:00 </code> (Giờ UTC), mặc định lấy ngày hiện tại
@apiParam   (Body: )        {String}           [to_time]                      Thời gian lọc đến ngày: <code>Định dạng: yyyy-MM-dd HH:mm:ss | ex: 2024-07-11 00:00:00 </code> (Giờ UTC), mặc định lấy ngày hiện tại, tối đa 90 ngày
@apiParam   (Body: )        {String}           [ids]                          Danh sách id của hành động muốn export

@apiSuccess     {String}            data.file_name                            Tên file export
@apiSuccess     {String}            [data.link_download]                      Link download nếu data được export trực tiếp (1000 bản ghi)
@apiSuccess     {String}            [data.number_row]                         Số lượng dòng được export
@apiSuccess     {Int}               data.type_export                          Loại export
                                                                              <li><code>1</code>: export trực tiếp</li>
                                                                              <li><code>2</code>: gửi qua email</li>

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "file_name": "ActivityTracking_20240826_142400.xlsx",
        "link_download": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/ActivityTracking_20240826_142400.xlsx",
        "type_export": 1,
        "number_row": 123
    },
    "message": "request thành công."
}
"""



"""
@api {GET} /adm/api/v2.1/accounts/activity/device-os           Danh sách thiết bị truy cập
@apiGroup AccountActivity
@apiVersion 1.0.0
@apiName ListDeviceOs

@apiSuccess     {Array}             data                                    Danh sách event       

@apiSuccessExample {json} Response
{
  "code": 200,
  "data": [
    "Windows", "Android"
  ],
  "lang": "vi",
  "message": "request thành công."
}
"""
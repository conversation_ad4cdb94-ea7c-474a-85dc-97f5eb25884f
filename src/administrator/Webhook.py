********************* Tracking SendEmail ***********************
* version: 2.0.0                                               *
****************************************************************
"""
@api {post} /webhook/api/v2.1/tracking/sent-email Tracking số lượng email đã gửi
@apiDescription Theo dõi số lượng email đã gửi của 1 tenant.
@apiGroup Webhook
@apiVersion 2.0.0
@apiName TrackingSentEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header

@apiParamExample  {json}  Example:
{
  "merchant_id": "6662c36e-64ef-4ac4-ad9d-8313038df064",
  "data": {
    "type": "sent_email_number",
    "number": 2
  },
  "checksum": "238a239d54b44abba9fb99d061a4cced"
}

@apiSuccessExample 	{json}	Response: HTTP/1.1 200 OK
{
  "message": "success",
  "code": 200
}
"""
********************* Tracking ValidateEmail *******************
* version: 2.0.0                                               *
****************************************************************
"""
@api {post} /webhook/api/v2.1/tracking/validated-email Tracking số lượng email đã validate
@apiDescription Theo dõi số lượng email đã validate của 1 tenant.
@apiGroup Webhook
@apiVersion 2.0.0
@apiName TrackingValidateEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header

@apiParamExample  {json}  Example:
{
  "merchant_id": "6662c36e-64ef-4ac4-ad9d-8313038df064",
  "data": {
    "type": "validate_email_number",
    "number": 2
  },
  "checksum": "1c4b163553b243c2b51205e51e7fd7ce"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "message": "success",
  "code": 200
}
"""
********************** Tracking AddProfile *********************
* version: 2.0.0                                               *
****************************************************************
"""
@api {post} /webhook/api/v2.1/tracking/add-profile Tracking số lượng profile
@apiDescription Theo dõi số lượng profile của 1 tenant.
@apiGroup Webhook
@apiVersion 2.0.0
@apiName TrackingAddProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header

@apiParamExample  {json}  Example:
{
  "merchant_id": "6662c36e-64ef-4ac4-ad9d-8313038df064",
  "data": {
    "type": "add_profile_number",
    "number": 2
  },
  "checksum": "807ab9f0e42f4cc1a9f2f43454c9dcaf"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "message": "success",
  "code": 200
}
"""
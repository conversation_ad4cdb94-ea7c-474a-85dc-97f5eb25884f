#!/usr/bin/python
# -*- coding: utf8 -*-

# ============================================
# L<PERSON>y danh sách event
# ============================================
"""
@api {get} /api/v2.1/google/calendar/api/list L<PERSON>y danh sách event
@apiDescription Danh sách event của 
@apiGroup GoogleCalendar
@apiVersion 1.0.0
@apiName GoogleCalendarList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam (Params:) {string} staff_id Mã người dùng
@apiParam (Params:) {string} calendar_id Mã người dùng

@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "account_id": "1111d995-9c64-4794-9e44-2b7e243e1111",
            "calendar_id": "primary",
            "created": "2021-08-10T11:14:41.000Z",
            "creator": {
                "email": "<EMAIL>",
                "self": true
            },
            "end": {
                "dateTime": "2021-08-09T18:45:34+07:00",
                "timeZone": "Asia/Ho_Chi_Minh"
            },
            "etag": "\"****************\"",
            "eventType": "default",
            "htmlLink": "https://www.google.com/calendar/event?eid=**********************************************************",
            "iCalUID": "<EMAIL>",
            "id": "n9ifan5csgsv1irsai692ko4os",
            "kind": "calendar#event",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "organizer": {
                "email": "<EMAIL>",
                "self": true
            },
            "reminders": {
                "useDefault": true
            },
            "sequence": 1,
            "start": {
                "dateTime": "2021-08-09T11:45:34+07:00",
                "timeZone": "Asia/Ho_Chi_Minh"
            },
            "status": "cancelled",
            "summary": "old",
            "updated": "2021-08-10T11:16:11.747Z"
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""

# ============================================
# Thêm event
# ============================================

"""
@api {post} /api/v2.1/google/calendar/api/create thêm event
@apiDescription Thêm event mới vào google calendar
@apiGroup GoogleCalendar
@apiVersion 1.0.0
@apiName GoogleCalendarCreate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   staff_id    Mã người dùng
@apiParam   (Body)   {String}   start       Thông tin giời gian bắt đầu event ITC
@apiParam   (Body)   {String}   end         Thông tin giời gian kết thúc event ITC
@apiParam   (Body)   {String}   summary     Tiêu đề của event
@apiParam   (Body)   {String}   [email_guest]     danh sách email khách mời 
@apiParam   (Body)   {String}   [reminder_time]   số phút thông báo trước khi đến giờ bắt đầu sự kiện 
@apiParam   (Body)   {String}   [description]     mô tả event

@apiParamExample {json} Body
{
    "staff_id":"1111d995-9c64-4794-9e44-2b7e243e1111",
    "start": "2021-08-11T11:45:34",
    "end": "2021-08-11T18:45:34",
    "summary": "now",
    "email_guest": ["<EMAIL>"],
    "reminder_time": 60,
    "description": "description"
}

@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "calendar_id": ""
    }
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""

# ============================================
# Sửa event
# ============================================

"""
@api {put} /api/v2.1/google/calendar/api/edit Sửa event
@apiDescription Sửa event trên google calendar
@apiGroup GoogleCalendar
@apiVersion 1.0.0
@apiName GoogleCalendarEdit

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   event_id    Mã event
@apiParam   (Body)   {String}   staff_id    Mã nhân viên 
@apiParam   (Body)   {String}   start       Thông tin giời gian bắt đầu event ITC
@apiParam   (Body)   {String}   end         Thông tin giời gian kết thúc event ITC
@apiParam   (Body)   {String}   summary     Nội dung của event
@apiParam   (Body)   {String}   [email_guest]     danh sách email khách mời 
@apiParam   (Body)   {String}   [reminder_time]   số phút thông báo trước khi đến giờ bắt đầu sự kiện 
@apiParam   (Body)   {String}   [description]     mô tả event


@apiParamExample {json} Body
{
    "event_id":"3t9abj2turs6rj14o4lm0d2lhk",
    "staff_id":"1111d995-9c64-4794-9e44-2b7e243e1111",
    "start": "2021-08-11T11:45:34",
    "end": "2021-08-11T18:45:34",
    "summary": "edit",
    "email_guest": ["<EMAIL>"],
    "reminder_time": 60,
    "description": "description"
}

@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""

# ============================================
# Xoá event
# ============================================

"""
@api {delete} /api/v2.1/google/calendar/api/delete Xoá event
@apiDescription Xoá event trên google calendar
@apiGroup GoogleCalendar
@apiVersion 1.0.0
@apiName GoogleCalendarDelete

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   event_id    Mã event
@apiParam   (Body)   {String}   staff_id    Mã người dùng

@apiParamExample {json} Body
{
    "event_id":"3t9abj2turs6rj14o4lm0d2lhk",
    "staff_id":"1111d995-9c64-4794-9e44-2b7e243e1111",
}

@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


"""
@api {post} /api/v2.1/google/calendar/api/sync Sync các event của người dùng
@apiDescription Sync các event của người dùng về db
@apiGroup GoogleCalendar
@apiVersion 1.0.0
@apiName GoogleCalendarSync

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   staff_id    Mã người dùng

@apiParamExample {json} Body
{
    "staff_id":"1111d995-9c64-4794-9e44-2b7e243e1111"
}

@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


"""
@api {post} /api/v2.1/google/connect kết nối app google
@apiDescription kết nối app google
@apiGroup GoogleCalendar
@apiVersion 1.0.0
@apiName GoogleConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body)   {String}   staff_id    Mã nhân viên 
@apiParam   (Body)   {String}   link_current_page     đường dẫn callback sau khi kết nối thành công 
@apiParam   (Body)   {String}   source    nguồn thực hiện kết nối: "mail", "calendar" 

@apiParamExample {json} Body
{
    "staff_id":"1111d995-9c64-4794-9e44-2b7e243e1111",
    "link_current_page": "https://test1.mobio.vn/account/setting/basic",
    "source": "mail"
}

@apiSuccess     {json} data response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": "https://accounts.google.com/o/oauth2/auth"     // chuyển hướng tới đường dẫn này 
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


"""
@api {get} /api/v2.1/google/connect lấy thông tin kết nối app google
@apiDescription lấy thông tin kết nối app google
@apiGroup GoogleCalendar
@apiVersion 1.0.0
@apiName GoogleGetConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body)   {String}   account_id    Mã nhân viên 

@apiSuccess     {json} data response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "merchant_id": "",
        "account_id": "",
        "config_id": "",
        "email": "",
        "status": 1,            // trạng thái sử dụng on/off, 1: on, 0: off 
        "status_connect": 1,            // trạng thái kết nối, 1: thành công, 0: thất bại  
    }
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


"""
@api {post} /api/v2.1/google/status thay đổi trạng thái sử dụng 
@apiDescription thay đổi trạng thái sử dụng
@apiGroup GoogleCalendar
@apiVersion 1.0.0
@apiName GoogleChangeStatus 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body)   {String}   config_id    id cấu hình  
@apiParam   (Body)   {Integer}   status     trạng thái sử dụng on/off, 1: on, 0: off  


@apiParamExample {json} Body
{
    "config_id":"1111d995-9c64-4794-9e44-2b7e243e1111",
    "status": 1
}

@apiSuccess     {json} data response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


"""
@api {get} /api/v2.1/google/check kiểm tra thông tin kết nối
@apiDescription kiểm tra thông tin kết nối
@apiGroup GoogleCalendar
@apiVersion 1.0.0
@apiName GoogleCheckConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body)   {String}   config_id    id cấu hình  

@apiSuccess     {json} data response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "status_connect": 1,            // trạng thái kết nối, 1: thành công, 0: thất bại   
    }
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


"""
@api {delete} /api/v2.1/google/revoke gỡ cấu hình kết nối 
@apiDescription gỡ cấu hình kết nối 
@apiGroup GoogleCalendar
@apiVersion 1.0.0
@apiName GoogleRevoke

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body)   {String}   config_id    id cấu hình  

@apiSuccess     {json} data response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""



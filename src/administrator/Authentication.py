*********************************** Generate Simple Token ***********************************
* version: 1.0.0                                                                            *
*********************************************************************************************
"""
@api {post} /api/v2.1/tokens/simple Generate token simple
@apiDescription API sinh token simple. API chặn theo IP.
@apiGroup Token
@apiVersion 1.0.0
@apiName GenerateSimpleToken

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse json_header
@apiUse order_sort

@apiParam   (Body:)   {String=Bearer}  type  Kiểu token cần generate
@apiParam   (Body:)   {Object}  [data]  Dữ liệu cần thêm vào body token. Chỉ áp dụng với <code>type=Bearer</code>
@apiParamExample  {json}  Body:
{
  "type": "Bearer",
  "data": {
    "key1": "value1",
    "key2": "value2"
  }
}

@apiSuccess   {String}  type  Kiểu token
@apiSuccess   {String}  token  new token
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "type": "Bearer",
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBbmhEYWlEaWVuIjoiaHR0cDovL3N0b3JhZ2UuZ29vZ2xlYXBpcy5jb20vdGVzdF94bG95YWx0eTEvaW1hZ2VzL25oYWN1bmdjYXAvMWI5OWJkY2YtZDU4Mi00ZjQ5LTk3MTUtMWI2MWRmZmYzOTI0PzE0NzY3ODEyODY3MDciLCJOaGFDdW5nQ2FwSUQiOiIxYjk5YmRjZi1kNTgyLTRmNDktOTcxNS0xYjYxZGZmZjM5MjQiLCJUcmFuZ1RoYWlUaGFtR2lheFBvaW50IjozLCJNYU5oYUN1bmdDYXAiOiJQSU5HQ09NU0hPUCIsIlF1eWVuIjpbIlJPTEVfQ0FVSElOSERBTkhNVUNUSUNIRElFTU1VQUhBTkdPRkZMSU5FIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPIiwiUk9MRV9DQVVISU5ITkhBTlRIT05HQkFPQ09OR05PIiwiUk9MRV9DQVVISU5IVEFOR0RJRU0iLCJST0xFX0NPTU1FTlQiLCJST0xFX0NVQUhBTkciLCJST0xFX0RBTkdOSEFQIiwiUk9MRV9EQVNIQk9BUkRfRElFTSIsIlJPTEVfREFTSEJPQVJEX0RPSVFVQSIsIlJPTEVfREFTSEJPQVJEX0ZPT1RUUkFGRklDIiwiUk9MRV9EQVNIQk9BUkRfUkFUSU5HQ1VBSEFORyIsIlJPTEVfREFTSEJPQVJEX1ZPVUNIRVIiLCJST0xFX0VYQ0VMVEFOR0RJRU0iLCJST0xFX0ZPT1RUUkFGRklDIiwiUk9MRV9LSEFDSEhBTkdEQU5HS1kiLCJST0xFX0tIVVlFTk1BSSIsIlJPTEVfTElDSFNVRE9JUVVBIiwiUk9MRV9MSUNIU1VLSEFDSEhBTkdEVU5HVk9VQ0hFUiIsIlJPTEVfTElDSFNVVEFOR0RJRU0iLCJST0xFX0xZRE9LSEFDSEhBTkdSQVRJTkdUSEFQIiwiUk9MRV9OSEFDVU5HQ0FQIiwiUk9MRV9RVUFOTFlDT05HTk9DSElUSUVUIiwiUk9MRV9RVUFOTFlUSEUiLCJST0xFX1FVQU5UUklCRUFDT04iLCJST0xFX1JBVElOR0NVQUhBTkciLCJST0xFX1JFUE9SVF9ET0lRVUEiLCJST0xFX1JFUE9SVF9QVVJDSEFTRUZPT1RUUkFGRklDIiwiUk9MRV9SRVBPUlRfVk9VQ0hFUiIsIlJPTEVfU0FOUEhBTSIsIlJPTEVfVEFPVEFJS0hPQU4iLCJST0xFX1RBT1RIRVRVRklMRSIsIlJPTEVfVk9VQ0hFUiJdLCJUZW5OaGFDdW5nQ2FwIjoiUElOR0NPTSBTSE9QIiwiaWF0IjoxNTAyMTU3NTAyLCJUZW5UcnV5Q2FwIjoicGluZ2NvbXNob3BAcGluZ2NvbXNob3AiLCJUYWlLaG9hblF1YW5UcmlJRCI6IjFhNzE0YTgzLWIxY2UtNDE4MS1hMDk4LTA0MjYyMDdlMWYzNCJ9.PmnQpD617Z9mkw5WubKemSJvYkZYiFz6E1NigL7IHYQ"
}
"""


************************************************** API ĐĂNG NHẬP TÀI KHOẢN **************************************************
* version: 1.1.0                                                                                                            *
*****************************************************************************************************************************
"""
@api {post} /api/v2.1/login Đăng nhập hệ thống
@apiDescription API Đăng nhập hệ thống
@apiVersion 1.1.0
@apiGroup Authentication
@apiName Login

@apiUse 404
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)  {String} [type_login] : Kiểu login mobile hoặc web. Nếu không truyền lên thì đăng nhập bằng web <code> 'web': đăng nhập bằng web, 'mobile': đăng nhập bằng điện thoại</code>
@apiParam   (Body:)    {String}    [source] nguồn đăng nhập khác mobio như: msb, hdb, ...

@apiParamExample {json} Body
{
  "username": "Locnh",
  "password": "123456",
  "source": "msb"   // nếu đăng nhập tài khoản khác mobio thì có thêm key này
}

@apiSuccessExample {json} Yêu cầu người dùng làm trước khi đăng nhập 
{
  "type_login": "not_verify_email"  // Chưa xác minh email, chuyển qua bước xác minh email 
  "type_login": "not_verify_other_email"  // Tài khoản chưa có email, nhập email để xác minh 
  "type_login": "lock_by_limit_verify_email"  // Bị khóa tài khoản vì xác minh sai quá số lần cho phép
  "type_login": "email_verify_already"  // Email đã được 1 tài khoản khác xác minh
  "type_login": "need_admin_change_email"  // Cần liên hệ với admin để thay đổi email
  "type_login": "account_verify_email_already"  // Tài khoản đã được xác minh email trước đó 
  "type_login": "should_change"  // cần đổi mật khẩu 
  "type_login": "change_in_first_sign_in"  // cần đổi mật khẩu lần đầu
  "type_login": "must_change"  // phải đổi mật khẩu 
  "type_login": "login_code_app_authenticator"  // nhập mã xác thực 2 bước từ app authenticator
  "type_login": "sso_require"  // bắt buộc đăng nhập sso 
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZOkweDQ8wdOJwUQrqDj5nAZLam1QrvPKmoghEmlPiRk"
}

@apiSuccess  {string} id ID nhân viên
@apiSuccess  {string} username Tên truy cập của nhân viên
@apiSuccess  {string} fullname Họ tên nhân viên
@apiSuccess  {string} avatar Linh ảnh đại diện nhân viên
@apiSuccess  {string} phone_number Số điện thoại nhân viên
@apiSuccess  {string} email Thư điện tử nhân viên
@apiSuccess  {number=1:Enable 2:Disable} status Trạng thái của nhân viên
@apiSuccess  {number=1:Owner 2:Normal} is_admin Phân biệt tài khoản quản trị owner với tài khoản thường
@apiSuccess  {number=1:Mobio 2:Others} is_mobio Phân biệt tài khoản của Mobio với các nhãn hàng khác
@apiSuccess  {number} iat Thời điểm tạo jwt (seconds)
@apiSuccess  {string} merchant_name Tên nhãn hàng quản lý nhân viên
@apiSuccess  {string} merchant_id ID nhãn hàng
@apiSuccess  {string} merchant_avatar Ảnh đại diện nhãn hàng
@apiSuccess  {string} merchant_avatar_collapse Ảnh đại diện nhãn hàng thu nhỏ 
@apiSuccess  {number=1:Retail 2:Supermarket 3:Group 4:Spa-Beauty 5:Karaoke 8:Restaurant 9:Coffee-shop} merchant_type Kiểu nhà cung cấp
@apiSuccess  {number=1:Have-not-joined-yet 2:Registered 3:Joined} xpoint_status Trạng thái tham gia sử dụng mPoint.
@apiSuccess  {string} merchant_code Mã merchant 
@apiSuccess  {array} type  Loại merchant(VD: ["BANK"]) 
@apiSuccess  {string} role_group Nhóm quyền của tài khoản <code>owner:Nhóm quyền owner, admin:Nhóm quyền admin, manager:Nhóm quyền manager, user:Nhóm quyền user</code>


@apiSuccessExample {json} JWT details
{
  "id": "07fe0ad4-2c6c-41e5-8ea4-1a066cfc9289",
  "username": "pingcomshop@pingcomshop",
  "fullname": "PINGCOM SHOP",
  "avatar": "",
  "phone_number": "+************",
  "email": "<EMAIL>",
  "status": 1,
  "is_admin": 1,
  "is_mobio": 2,
  "iat": **********,
  "merchant_name": "PINGCOMSHOP",
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "merchant_avatar": "",
  "merchant_avatar_collapse": "",
  "merchant_type": 1,
  "xpoint_status": 3,
  "role_group": "owner",
  "merchant_code": "PINGCOMSHOP",
  "type": ["BANK"],
  
}
"""

************************************************** API ĐĂNG XUẤT **************************************************
* version: 1.0.0                                                                                                  *
*******************************************************************************************************************
"""
@api {post} /api/v2.1/logout Đăng xuất tài khoản
@apiVersion 1.0.0
@apiGroup Authentication
@apiName LogOut
@apiUse 401
@apiParam   (Body:)  {String} [type_login] : Kiểu log out mobile hoặc web. Nếu không truyền lên thì đăng xuât bằng web <code> 'web': đăng xuất bằng web, 'mobile': đăng xuất bằng điện thoại</code>

@apiSuccessExample {json} Response
{
  "code": 200,
  "message": "request thành công."
}
"""

************************************************** API LẤY THỜI GIAN CÒN LẠI CỦA TOKEN **************************************************
* version: 1.0.0                                                                                                                        *
*****************************************************************************************************************************************
"""
@api {get} /api/v2.1/token/ttl Lấy thời gian còn lại của token
@apiVersion 1.0.0
@apiGroup Token
@apiName GetTTL

@apiSuccess  {number} ttl Thời gian còn lại của token tính theo <code>GIÂY</code>. Nếu <code>ttl = 0</code>, thì jwt hết hạn hoặc không tồn tại
@apiSuccessExample {json} Success
{
  "code": 200,
  "ttl": 100,
  "message": "request thành công."
}

@apiSuccessExample {json} Expired or Not found
{
  "code": 200,
  "ttl": 0,
  "message": "request thành công."
}
"""

************************************************** API Kiểm tra mật khẩu tài khoản **************************************************
* version: 1.0.0                                                                                                            *
*****************************************************************************************************************************
"""
@api {post} /api/v2.1/account/actions/check-password Kiểm tra mật khẩu tài khoản
@apiDescription API Kiểm tra mật khẩu tài khoản
@apiVersion 1.1.0
@apiGroup Authentication
@apiName CheckPasswordAccount

@apiUse 404
@apiUse 412
@apiUse 500
@apiUse lang


@apiParamExample {json} Body
{
  "username": "Locnh",
  "password": "123456"
}

@apiSuccessExample {json} Expired or Not found
{
  "code": 200,
  "ttl": 0,
  "message": "request thành công."
}

"""


"""
@api {post} /api/v2.1/sso/saml/login Lấy link đăng nhập SSO SAML  
@apiDescription /adm/mobile/v2.1/sso/saml/login path mobile 
@apiVersion 1.1.0
@apiGroup Authentication
@apiName SSOSAMLLogin

@apiUse 404
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String}    sp_entity_id mã cấu hình nhà cung cấp dịch vụ trên mobio 
@apiParam   (Body:)    {String}    redirect_url link truy cập web sau khi đăng nhập ở màn hình nhà cung cấp dịch vụ

@apiParamExample {json} BodyWeb
{
  "sp_entity_id": "<EMAIL>",
  "redirect_url": "{{domain}}/login?lang=vi"
}

@apiParamExample {json} BodyMobile 
{
  "sp_entity_id": "mobio.io",
  "redirect_url": "{{domain_deeplink}}"
}

@apiSuccess  {string} access_token backend tự thêm param này vào redirect_url, jwt của mobio FE sử dụng 
@apiSuccess  {string} message_error backend tự thêm param này vào redirect_url thông báo lỗi nếu có, FE hiển thị thông báo 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": "https://login.microsoftonline.com/...",
  "lang": "vi",
  "message": "request thành công."
}

"""


"""
@api {get} /api/v2.1/biometrics/detail Lấy thông tin cấu hình sinh trắc học 
@apiDescription lấy theo thông tin device id 
@apiVersion 1.0.0
@apiGroup Authentication
@apiName BiometricsDetail 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Query:)     {String}      device_id  id thiết bị 


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": {
      "device_os": "ios",
      "device_id": "d8b17601cbdc2477",
      "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
      "biometric_type": "face_id",
      "status": "on",
    },
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""



"""
@api {post} /api/v2.1/biometrics/register bật đăng ký xác thực sinh trắc học    
@apiDescription cấu hình theo thông tin device id 
@apiVersion 1.0.0
@apiGroup Authentication
@apiName BiometricsRegister 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {String}      device_id  id thiết bị 
@apiParam      (Body:)     {String}      public_key  khóa công khai 
@apiParam      (Body:)     {String}      [device_os]  hệ điều hành: os, android 
@apiParam      (Body:)     {String}      [biometric_type]  loại sinh trắc học đăng ký: face_id, touch_id


@apiParamExample {json} Body
{
  "device_id":"d8b17601cbdc2477",
  "public_key": "dheuNXjoQ9CkaQg6ncIgGP:APA91bH7JN2oqcH1BoVluEHJ8JL9nXl78hjjsfdzmIIht0028yKimy5cX37P5ylNAMm4kfr",
  "device_os": "ios",
  "biometric_type": "face_id",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {post} /api/v2.1/biometrics/verify xác thực thông tin từ chữ ký    
@apiDescription cấu trúc payload là stringify, các thông báo lỗi sẽ để key "message"
@apiVersion 1.0.0
@apiGroup Authentication
@apiName BiometricsVerify 


@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500


@apiParam      (Body:)     {String}      signature  chữ ký gen ra từ public_key và payload
@apiParam      (Body:)     {String}      payload  thông tin dữ liệu cần ký 


@apiParamExample {json} Body
{
  "signature":"CsceSCb8xBQDNQujaqnV0GRIKv7OmxXcBtcecJHk6zjgexqhFSBvzmBD4I0rak4iD",
  "payload": '{"account_id":"ae40bbfb-1432-46c3-a91d-76970e379747","device_id":"d8b17601cbdc2477","action_type":"login","action_time":**********}',
  
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": {
    "jwt": ""
  }
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {post} /api/v2.1/sso/basic/login Lấy link đăng nhập SSO basic   
@apiDescription /adm/mobile/v2.1/sso/basic/login path mobile 
@apiVersion 1.1.0
@apiGroup Authentication
@apiName SSOBasicLogin

@apiUse 404
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String}    provider mã nhà cung cấp
@apiParam   (Body:)    {String}    redirect_url link truy cập web sau khi đăng nhập ở màn hình nhà cung cấp dịch vụ
@apiParam   (Body:)    {String}    [lang] mã ngôn ngữ: vi, en 

@apiParamExample {json} BodyWeb
{
  "provider": "google",
  "redirect_url": "{{domain}}/login?lang=vi",
  "lang": "vi"
}

@apiParamExample {json} BodyMobile 
{
  "provider": "google",
  "redirect_url": "{{domain_deeplink}}",
  "lang": "vi"
}

@apiSuccess  {string} access_token backend tự thêm param này vào redirect_url, jwt của mobio FE sử dụng 
@apiSuccess  {string} message_error backend tự thêm param này vào redirect_url thông báo lỗi nếu có, FE hiển thị thông báo 
@apiSuccess  {string} action_code backend tự thêm param này vào redirect_url, trước khi đăng nhập phải thực hiện bước nào đó: success, fail, login_code_app_authenticator   
@apiSuccess  {string} state backend tự thêm param này vào redirect_url, thông tin kèm theo nếu có action_code

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": "https://login.microsoftonline.com/...",
  "lang": "vi",
  "message": "request thành công."
}

"""

"""
@api {post} /api/v2.1/sso/login/by-action đăng nhập theo action   
@apiDescription sau khi kết nối thành công provider cần thực hiện xác thực hai bước sẽ call api này 
@apiVersion 1.1.0
@apiGroup Authentication
@apiName SSOLoginAction

@apiUse 404
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String}    action_code hành động cần để login: login_code_app_authenticator (xác thực 2 bước)
@apiParam   (Body:)    {String}    state thông tin sau khi kết nối provider thành công 
@apiParam   (Body:)    {String}    value giá trị cần gửi lên theo hành động, login_code_app_authenticator là mã từ app authenticator


@apiParamExample {json} BodyWeb
{
  "action_code": "login_code_app_authenticator",
  "state": "eyJzdGFmZl9pZCI6ICI3ZmMwYTMzYy1iYWY1LTExZTctYTdjMi0w",
  "value": "123456"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "result_code": "success"
    "jwt": "eyJzdGFmZl9pZCI6ICI3ZmMwYTMzYy1iYWY1LTExZTctYTdjMi0w"

    "result_code": "wrong_code"   // sai mã 

    "result_code": "request_expire"   // yêu cầu login hết hiệu lực, quay lại luồng login ban đầu   
  },
  "lang": "vi",
  "message": "request thành công."
}

"""

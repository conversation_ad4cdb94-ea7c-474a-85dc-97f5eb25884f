#!/usr/bin/python
# -*- coding: utf8 -*-

"""
@api {POST} /api/v2.1/statements Filter statement
@apiDescription  API lấy danh sách statement thỏa mãn điều kiện lọc.
@apiGroup Statements
@apiVersion 1.0.0
@apiName FilterStatement

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {ArrayString}   user_ids   Danh sách định danh user cần lọc.
@apiParam   (Body:)   {ArrayString}   team_ids   Danh sách định danh team cần lọc.
@apiParam   (Body:)   {ArrayString}   merchant_ids   Danh sách định danh merchant cần lọc.
@apiParam   (Body:)   {ArrayString}   resources   Danh sách resource cần lọc.
@apiParam   (Body:)   {ArrayString}   actions   Danh sách action cần lọc.

@apiParamExample  {json}  Body:
{
  "user_ids": ["f16c84eb-4385-4c6b-8e98-5e00e9f567cd"],
  "team_ids": ["5d657c6b-45dc-40e7-928a-031fe3ae7152"],
  "merchant_ids": ["21699dd0-677c-40af-8098-b3930df5930d"],
  "resources": [],
  "actions": []
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "statements": [
    {
      "effect": "allow",
      "action": [
        "ProfileList"
      ],
      "resource": [
        "profiling:profile"
      ],
      "condition": [
        {
          "operator": "Null",
          "field": "profile:owner_id",
          "values": [],
          "qualifier": "ForAnyValue",
          "if_exists": true,
          "ignore_case": false
        }
      ]
    },
    {
      "effect": "allow",
      "action": [
        "ProfileList"
      ],
      "resource": [
        "profiling:profile"
      ],
      "condition": [
        {
          "operator": "Null",
          "field": "profile:owner_id",
          "values": [],
          "qualifier": "ForAnyValue",
          "if_exists": true,
          "ignore_case": false
        }
      ]
    }
  ]
}
"""
#!/usr/bin/python
# -*- coding: utf8 -*-

******************************* Sửa Action *******************************
* vesion: 1.0.0                                                          *
**************************************************************************
"""
@api {patch} /api/v2.1/actions/<id> Sửa action
@apiDescription Sửa thông tin action hệ thống.
@apiGroup Action
@apiVersion 1.0.0
@apiName UpdateAction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:)     {Number}    id   Mã của action cần cập nhật.
@apiParam   (Body:)         {String}    [name_vi] Tên của action bằng tiếng Việt.
@apiParam   (Body:)         {String}    [name_en] Tên của action bằng tiếng Anh.
@apiParam   (Body:)         {String}    [description] Mô tả của action.
@apiParamExample {json} Body
{
  "name_vi":"Thêm",
  "name_en":"Add",
  "description":"Chức năng thêm dữ liệu"
}

@apiSuccess     {Number}    id  Mã action.
@apiSuccess     {String}    name_vi  Tên action bằng tiếng Việt.
@apiSuccess     {String}    name_en  Tên action bằng tiếng Anh.
@apiSuccess     {String}    [description]   Mô tả action.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "id":1,
    "name_vi":"Thêm",
    "name_en":"Add",
    "description":"Chức năng thêm dữ liệu"
}
"""
******************************* Tạo Action *******************************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api {post} /api/v2.1/actions Tạo action
@apiDescription Tạo action cho hệ thống.
@apiGroup Action
@apiVersion 1.0.0
@apiName CreateAction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)     {String}    id Action id
@apiParam   (Body:)     {String}    name_vi Tên của action bằng tiếng Việt.
@apiParam   (Body:)     {String}    name_en Tên của action bằng tiếng Anh.
@apiParam   (Body:)     {String}    [description] Mô tả của action.
@apiParamExample {json} Body
{
  "id":1,
  "name_vi":"Thêm",
  "name_en":"Add",
  "description":"Chức năng thêm dữ liệu"
}

@apiSuccess     {Number}    id  Mã action.
@apiSuccess     {String}    name_vi  Tên action bằng tiếng Việt.
@apiSuccess     {String}    name_en  Tên action bằng tiếng Anh.
@apiSuccess     {String}    [description]   Mô tả action.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "id":1,
    "name_vi":"Thêm",
    "name_en":"Add",
    "description":"Chức năng thêm dữ liệu"
}
"""
******************************* Lấy D/S Action *******************************
* version: 1.0.0                                                             *
******************************************************************************
"""
@api {get} /api/v2.1/actions Lấy danh sách action
@apiDescription Lấy danh sách các action(thêm, sửa, xoá,...) đang hỗ trợ trong hệ thống.
<li>Có hỗ trợ tìm kiếm theo tên.</li>
<li>Có hỗ trợ sắp xếp theo tên.</li>
@apiGroup Action
@apiVersion 1.0.0
@apiName GetActions

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse lang_success
@apiUse paging
@apiUse search
@apiUse order_sort

@apiSuccess                                     {Action[]}  data                Danh sách các action được gắn với chức năng. Chi tiết action object:
<li><code>id:</code> Mã action.</li>
<li><code>name_vi:</code> Tên action bằng tiếng Việt.</li>
<li><code>name_en:</code> Tên action bằng tiếng Anh.</li>
<li><code>description:</code>Mô tả action</li>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data":[
        {
            "id":1,
            "name_vi":"Thêm",
            "name_en":"Add",
            "description":"Chức năng thêm dữ liệu"
        },
        {
            "id":2,
            "name_vi":"Sửa",
            "name_en":"Edit",
            "description":"Chức năng sửa dữ liệu"
        }
    ],
    "sort":"name_vi",
    "order":"asc",
    "paging": {
    ...
    }
}
"""
*******************************
"""
@apiDefine actions_success
@apiVersion 1.0.0
@apiSuccess    (Function)     {Action[]}      actions                Danh sách action của chức năng.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>Number</td>
      <td>Mã action.</td>
    </tr>
    <tr>
      <td>name_vi</td>
      <td>String</td>
      <td>Tên action bằng tiếng Việt.</td>
    </tr>
    <tr>
      <td>name_en</td>
      <td>String</td>
      <td>Tên action bằng tiếng Anh.</td>
    </tr>
    <tr>
      <td>description</td>
      <td>String</td>
      <td>Mô tả action.</td>
    </tr>
  </tbody>
</table>
"""









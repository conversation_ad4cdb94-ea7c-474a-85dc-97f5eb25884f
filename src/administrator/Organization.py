"""
@apiDefine respone_organization
@apiVersion 1.0.0

@apiSuccess     {String}        id                             Id định danh của đơn vị
@apiSuccess     {String}        name                           Tên đơn vị
@apiSuccess     {Int}           number_level                   Level của đơn vị hiện tại, gi<PERSON> trị từ 1 ... n
@apiSuccess     {String}        scope_code                     Mã cấp quản lý
@apiSuccess     {String}        delimiter                      Ký tự ngăn cách giữa các cấp trong mã cấp quản lý
@apiSuccess     {String}        parent_id                      Id của đơn vị cha
@apiSuccess     {String}        status                         Trạng thái hoạt động của đơn vị
                                                               <li><code>active</code>: Đang hoạt động</li>
                                                               <li><code>inactive</code>: Dừng hoạt động</li>
@apiSuccess     {Int}           children_count                 S<PERSON> lượng đơn vị con
@apiSuccess     {Int}           members_count                  <PERSON><PERSON> lượng thành viên trong đơn vị
@apiSuccess     {Int}           [manager_accounts]             Danh sách nhân viên có role là quản lý trong đơn vị
@apiSuccess     {Array}         path_ids                       Danh sách đường dẫn breadcrum của đơn vị
@apiSuccess     {String}        code                           Mã đơn vị
@apiSuccess     {String}        updated_by                     Người cập nhật
@apiSuccess     {String}        updated_time                   Thời gian cập nhật
"""


"""
@apiDefine delete_change_status_response
@apiVersion 1.0.0
@apiSuccess {String}            message                 Mô tả phản hồi
@apiSuccess {Integer}           code                    Mã phản hồi
@apiSuccess {Object}            data                    Thông tin phản hồi
@apiSuccess {Object}            data.ids_success        Danh sách id form xoá thành công
@apiSuccess {Object}            data.ids_success.id     ID định danh của đơn vị
@apiSuccess {Object}            data.ids_success.name   Tên của đơn vị
@apiSuccess {Object}            data.ids_error          Danh sách id form xoá thất bại
@apiSuccess {Object}            data.ids_error.id       ID định danh của đơn vị
@apiSuccess {Object}            data.ids_error.name     Tên của đơn v

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "data": {   
        "ids_success": [{"id": "661618f88e185d9b327bdab2", "name": "Chi nhánh Hà Nội"}],
        "ids_error": []
    }
    "message": "request thành công."
}
"""


# Danh sách đơn vị trong tổ chức
"""
@api {GET} {domain}/api/v2.1/organization   Danh sách đơn vị trong tổ chức
@apiVersion 1.0.0
@apiGroup Organizational
@apiName OrganizationData

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Query:)     {String}      [unit_id]                 ID của đơn vị cha 


@apiUse respone_organization


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": [
        {
            "id": "661618f88e185d9b327bdab2",
            "name": "Khu vực Bắc sông Hồng",
            "number_level": 1,
            "scope_code": "KVBSH",
            "code": "KVBSH",
            "delimiter": "#",
            "parent_id": null,
            "status": "active",
            "children_count": 1,
            "members_count": 10,
            "manager_accounts": ["65eebbe6-24c3-416a-bbe4-0aebe92f6695"]
            "path_ids": ["661618f88e185d9b327bdab2"],
            "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "updated_time": "2025-05-27T7:00:00Z"
        },
        {
            "id": "661618f88e185d9b327bdab3",
            "name": "Chi nhánh Hà Nội",
            "number_level": 2,
            "scope_code": "KVBSH#1001_2024",
            "code": "1001_2024",
            "delimiter": "#",
            "parent_id": "661618f88e185d9b327bdab2",
            "status": "active",
            "children_count": 0,
            "members_count": 200,
            "manager_accounts": ["65eebbe6-24c3-416a-bbe4-0aebe92f6695"]
            "path_ids": ["661618f88e185d9b327bdab2", "661618f88e185d9b327bdab3"],
            "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "updated_time": "2025-05-27T7:00:00Z"
        }
    ]
}
"""


# Tạo đơn vị
"""
@api {POST} {domain}/api/v2.1/organization Tạo mới đơn vị
@apiVersion 1.0.0
@apiGroup Organizational
@apiName CreateUnit

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}      [parent_id]                ID của đơn vị cha, nếu tạo đơn vị con thì phải có giá trị này
@apiParam      (Body:)     {String}      name                       Tên của đơn vị, không được để trống
@apiParam      (Body:)     {String}      status                     Trạng thái hoạt động của đơn vị
                                                                    <li><code>active</code>: Đang hoạt động</li>
                                                                    <li><code>inactive</code>: Dừng hoạt động</li>
@apiParam      (Body:)     {String}      code                       Mã đơn vị
@apiParam      (Body:)     {Array}       members                    Danh sách thành viên trong đơn vị
@apiParam      (Body:)     {Array}       members.account_id         Id tài khoản của thành viên
@apiParam      (Body:)     {Array}       members.role               Quyền của thành viên trong đơn vị
                                                                    <li><code>manager</code>: Quản lý</li>
                                                                    <li><code>member</code>: Thành viên</li>

@apiUse respone_organization

@apiParamExample {json} Body
{
    "name": "Chi nhánh Hà Nội",
    "parent_id": "661618f88e185d9b327bdab2",
    "status": "active",
    "code": "1001_2024",
    "members": [
        {
            "account_id": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "role": "manager"
        },
        {
            "account_id": "d3953bbb-e011-451b-8686-29a57322d791",
            "role": "member"
        }
    ]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": {
        "id": "661618f88e185d9b327bdab3",
        "name": "Chi nhánh Hà Nội",
        "number_level": 2,
        "code": "1001_2024",
        "scope_code": "KVBSH#1001_2024",
        "parent_id": "661618f88e185d9b327bdab2",
        "status": "active",
        "children_count": 0,
        "members_count": 2,
        "manager_accounts": ["65eebbe6-24c3-416a-bbe4-0aebe92f6695"],
        "path_ids": ["661618f88e185d9b327bdab2", "661618f88e185d9b327bdab3"],
        "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "updated_time": "2025-05-27T7:00:00Z", 
    }
}
"""

# Chi tiết đơn vị
"""
@api {GET} {domain}/api/v2.1/organization/<unit_id> Xem chi tiết đơn vị
@apiVersion 1.0.0
@apiGroup Organizational
@apiName UnitDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse respone_organization


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": {
        "id": "661618f88e185d9b327bdab3",
        "name": "Chi nhánh Hà Nội",
        "number_level": 2,
        "parent_id": "661618f88e185d9b327bdab2",
        "scope_code": "KVBSH#1001_2024",
        "delimiter": "#",
        "code": "1001_2024",
        "status": "inactive",
        "children_count": 0,
        "members_count": 200,
        "path_ids": ["661618f88e185d9b327bdab2", "661618f88e185d9b327bdab3"],
        "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "updated_time": "2025-05-27T7:00:00Z"
    }
}
"""



# Sửa đơn vị
"""
@api {PATCH} {domain}/api/v2.1/organization/<unit_id> Sửa đơn vị
@apiVersion 1.0.0
@apiGroup Organizational
@apiName EditUnit

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam       (Body:)     {String}        [parent_id]                 ID của đơn vị cha, nếu sửa đơn vị thì chỉ được chọn đơn vị đồng cấp hoặc thấp hơn đơn vị cha ban đầu
@apiParam       (Body:)     {String}        name                        Tên của đơn vị, không được để trống
@apiParam       (Body:)     {String}        status                      Trạng thái hoạt động của đơn vị
                                                                        <li><code>active</code>: Đang hoạt động</li>
                                                                        <li><code>inactive</code>: Dừng hoạt động</li>
@apiParam       (Body:)     {String}        [action]                    Hành động khi inactive đơn vị hiện tại (required nếu status là inactive và đơn vị hiện tại có đơn vị con)
                                                                        <li><code>inactive</code>: Dừng hoạt động đơn vị hiện tại</li>
                                                                        <li><code>move_and_inactive</code>: Dừng hoạt động đơn vị hiện tại và chuyển tất cả đơn vị con sang đơn vị cha mới</li>
@apiParam       (Body:)     {String}        [receiving_unit]            Đơn vị tiếp nhận các đơn vị con của đơn vị hiện tại, required khi action là <code>move_and_inactive</code>
@apiParam       (Body:)     {String}        code                        Mã đơn vị
@apiParam       (Body:)     {Array}         members                     Danh sách thành viên trong đơn vị
@apiParam       (Body:)     {Array}         members.account_id          Id tài khoản của thành viên
@apiParam       (Body:)     {Array}         members.role                Quyền của thành viên trong đơn vị
                                                                        <li><code>manager</code>: Quản lý</li>
                                                                        <li><code>member</code>: Thành viên</li>

@apiUse respone_organization

@apiParamExample {json} Body
{
    "name": "Chi nhánh Hà Nội",
    "parent_id": "661618f88e185d9b327bdab2",
    "status": "active",
    "code": "1001_2024",
    "members": [
        {
            "account_id": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "role": "manager"
        },
        {
            "account_id": "d3953bbb-e011-451b-8686-29a57322d791",
            "role": "member"
        }
    ]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": {
        "id": "661618f88e185d9b327bdab3",
        "name": "Chi nhánh Hà Nội",
        "number_level": 2,
        "parent_id": "661618f88e185d9b327bdab2",
        "scope_code": "KVBSH#1001_2024",
        "delimiter": "#",
        "code": "1001_2024",
        "status": "active",
        "children_count": 0,
        "members_count": 2,
        "path_ids": ["661618f88e185d9b327bdab2", "661618f88e185d9b327bdab3"],
        "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "updated_time": "2025-05-27T7:00:00Z"
    }
}
"""


# Xoá đơn vị
"""
@api {DELETE} {domain}/api/v2.1/organization/delete-units    Xoá đơn vị
@apiDescription Api sử dụng để xoá đơn vị, có thể xoá nhiều đơn vị cùng lúc. Với tuỳ chọn xoá đơn vị hiện tại và chuyển đơn vị con sang đơn vị khác <code>chỉ hỗ trợ khi tất cả các đơn vị bị xoá là đồng cấp hoặc thấp hơn đơn vị cha của đơn vị hiện tại</code>
@apiVersion 1.0.0
@apiGroup Organizational
@apiName DeleteUnit

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {Array}       unit_ids                   Danh sách đơn vị cần xoá
@apiParam      (Body:)     {String}      action                     Hành động khi xoá đơn vị hiện tại
                                                                    <li><code>delete</code>: Xoá đơn vị hiện tại</li>
                                                                    <li><code>move_and_delete</code>: Xoá đơn vị hiện tại và chuyển tất cả đơn vị con sang đơn vị cha mới</li>
@apiParam      (Body:)     {String}      [receiving_unit]           ID của đơn vị cha, bắt buộc phải có nếu action là <code>move_and_delete</code>


@apiParamExample {json} Body delete
{
    "unit_ids": ["661618f88e185d9b327bdab2"],
    "action": "delete",
}

@apiParamExample {json} Body move_and_delete
{
    "unit_ids": ["661618f88e185d9b327bdab2"],
    "action": "move_and_delete",
    "receiving_unit": "661618f88e185d9b327bdab2"
}

@apiUse delete_change_status_response
"""

# Kích hoạt đơn vị
"""
@api {POST} {domain}/api/v2.1/organization/active-units          Kích hoạt lại đơn vị đang dừng hoạt động
@apiVersion 1.0.0
@apiGroup Organizational
@apiName ActiveUnit

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {Array}      unit_ids                  Danh sách đơn vị cần kích hoạt lại, chỉ hỗ trợ kích hoạt lại đơn vị đang dừng hoạt động

@apiParamExample {json} Body
{
    "unit_ids": ["661618f88e185d9b327bdab2"]
}


@apiUse delete_change_status_response
"""


# Dừng hoạt động đơn vị
"""
@api {POST} {domain}/api/v2.1/organization/inactive-units          Dừng hoạt động đơn vị
@apiDescription Api sử dụng để dừng hoạt động đơn vị, chỉ hỗ trợ dừng hoạt động đơn vị đang hoạt động, cho phép dừng hoạt động nhiều đơn vị cùng lúc, cho phép chuyển đơn vị con của các đơn vị bị dừng hoạt động sang 1 đơn vị con khác đồng cấp hoặc thấp hơn đơn vị cha của đơn vị hiện tại, <code>chỉ hỗ trợ khi tất cả các đơn vị bị dừng hoạt động có cùng cấp độ hoăc thấp hơn đơn vị cha của đơn vị hiện tại</code>
@apiVersion 1.0.0
@apiGroup Organizational
@apiName InactiveUnit

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {Array}      unit_ids                Danh sách đơn vị cần dừng hoạt động
@apiParam      (Body:)     {String}     action                  Hành động khi dừng hoạt động đơn vị hiện tại
                                                                <li><code>inactive</code>: Dừng hoạt động đơn vị hiện tại</li>
                                                                <li><code>move_and_inactive</code>: Dừng hoạt động đơn vị hiện tại và chuyển tất cả đơn vị con sang đơn vị cha mới</li>
@apiParam      (Body:)     {String}     [receiving_unit]        ID đơn vị tiếp nhận các đơn vị con của các đơn vị bị dừng hoạt động

@apiParamExample {json} Body inactive
{
    "unit_ids": ["661618f88e185d9b327bdab2"],
    "action": "inactive"
}

@apiParamExample {json} Body move_and_inactive
{
    "unit_ids": ["661618f88e185d9b327bdab2"],
    "action": "move_and_inactive",
    "receiving_unit": "661618f88e185d9b327bdab2"
}


@apiUse delete_change_status_response
"""


# Chuyển đơn vị
"""
@api {POST} {domain}/api/v2.1/organization/move-units      Chuyển đơn vị
@apiDescription Api sử dụng để chuyển đơn vị qua đơn vị khác, chỉ có thể chuyển sang đơn vị đồng cấp hoặc thấp hơn, cho phép chuyển nhiều đơn vị cùng lúc, <code>chỉ hỗ trợ khi tất cả các đơn vị cần chuyển là đồng cấp hoặc thấp hơn</code>

@apiVersion 1.0.0
@apiGroup Organizational
@apiName MoveUnit

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {Array}      unit_ids                   Danh sách id của các đơn vị cần chuyển
@apiParam      (Body:)     {String}     receiving_unit             Thư mục tiếp nhận đơn vị con của các đơn vị cần chuyển, chỉ hỗ trợ khi tất cả các đơn vị cần chuyển là đồng cấp hoặc thấp hơn

@apiParamExample {json} Body
{
    "unit_ids": ["661618f88e185d9b327bdab2"],
    "receiving_unit": "661618b88e005d9b327bdab2"
}

@apiUse delete_change_status_response
"""



# Danh sách thành viên trong đơn vị
"""
@api {GET} {domain}/api/v2.1/organization/<unit_id>/members      Lấy danh sách thành viên trong đơn vị
@apiVersion 1.0.0
@apiGroup Organizational
@apiName MembersOfUnit

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess     {String}           account_id             Id tài khoản của thành viên
@apiSuccess     {String}           role                   Quyền của thành viên trong đơn vị
@apiSuccess     {String}           fullname               Họ tên
@apiSuccess     {String}           staff_code             Mã nhân viên
@apiSuccess     {String}           email                  Email của thành viên
@apiSuccess     {String}           status                 Trạng thái hoạt động của thành viên
                                                          <li><code>1</code>: Đang hoạt động</li>
                                                          <li><code>4</code>: Dừng hoạt động</li>
@apiSuccess     {String}           [avatar]               Ảnh đại diện của thành viên, nếu có


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": [
        {
            "account_id": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "role": "manager"
            "fullname": "Nguyễn Văn A",
            "staff_code": "NV001",
            "email": "<EMAIL>",
            "avatar": "https://example.com/avatar.jpg",
            "status": 1
        },
        {
            "account_id": "d3953bbb-e011-451b-8686-29a57322d791",
            "role": "member",
            "fullname": "Nguyễn Văn B",
            "staff_code": "NV002",
            "email": "<EMAIL>",
            "avatar": "https://example.com/avatar2.jpg",
            "status": 1
        }
    ]
}
"""



# Danh sách đơn vị đồng cấp
"""
@api {GET} {domain}/api/v2.1/organization/<unit_id>/same-level      Danh sách đơn đồng cấp với đơn vị hiện tại
@apiDescription Api sử dụng để lấy danh sách đơn vị đồng cấp/đơn vị con của đơn vị hiện tại
@apiVersion 1.0.0
@apiGroup Organizational
@apiName UnitSameLevel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Query:)     {String}      type                 Loại quan hệ cần lấy dữ liệu
                                                               <li><code>only_parent</code>: chỉ lấy danh sách đơn vị đồng cấp (không lấy đơn vị con)</li>
                                                               <li><code>all</code>: Lấy danh sách đơn vị (bao gồm đơn vị con) của đơn vị đồng cấp</li>
                                                               <li>Mặc định: <code>all</code></li>

@apiUse respone_organization


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": [
        {
            "id": "661618f88e185d9b327bdb97",
            "name": "Chi nhánh Hà Nội",
            "number_level": 2,
            "scope_code": "KVBSH#1603_2024",
            "delimiter": "#",
            "parent_id": 661618f88e185d9b327bdab2,
            "status": "active",
            "path_ids": ["661618f88e185d9b327bdab2", "661618f88e185d9b327bdb97"],
            "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "updated_time": "2025-05-27T7:00:00Z"
        }
    ]
}
"""


# Danh sách đơn vị trong tổ chức
"""
@api {GET} {domain}/api/v2.1/organization/<unit_id>/children  Danh sách đơn vị con của đơn vị hiện tại
@apiDescription Api sử dụng để lấy danh sách đơn vị con của đơn vị hiện tại
@apiVersion 1.0.0
@apiGroup Organizational
@apiName ChildrenOfUnit


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiUse respone_organization


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": [
        {
            "id": "661618f88e185d9b327bdab3",
            "name": "Chi nhánh Hà Nội",
            "number_level": 2,
            "scope_code": "KVBSH#1001_2024",
            "code": "1001_2024",
            "delimiter": "#",
            "parent_id": "661618f88e185d9b327bdab2",
            "status": "active",
            "children_count": 0,
            "members_count": 200,
            "manager_accounts": ["65eebbe6-24c3-416a-bbb4-a0ebe92f6695"],
            "path_ids": ["661618f88e185d9b327bdab2", "661618f88e185d9b327bdab3"],
            "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "updated_time": "2025-05-27T7:00:00Z"
        },
        {
            "id" : "66136c758e185d9b321a2f20",
            "scope_code" : "KVBSH#1603_2024",
            "number_level" : 2,
            "name" : "Chi nhánh Hải Phòng",
            "delimiter": "#",
            "children_count" : 4,
            "code" : "1603_2024",
            "members_count" : 0,
            "manager_accounts": ["65eebbe6-24c3-416a-bbe4-0aeb0e2f6695"],
            "parent_id" : "66136c758e185d9b321a2e99",
            "path_ids" : ["66136c758e185d9b321a2e99", "66136c758e185d9b321a2f20"],
            "status" : "active",
            "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "updated_time" : "2025-05-28T08:43:14.428+0000",
        }
    ]
}
"""

# Danh sách đơn vị theo ids
"""
@api {POST} {domain}/api/v2.1/organization/by-ids  Danh sách đơn vị theo id định danh
@apiVersion 1.0.0
@apiGroup Organizational
@apiName UnitsByIds


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {Array}       unit_ids                 Danh sách id định danh của đơn vị cần lấy thông tin
@apiParam      (Body:)     {Array}       field_selected           Danh sách các field cần lấy thông tin, nếu không có sẽ lấy mặc định <code>id, name, number_level</code>
@apiParamExample {json} Body
{
    "unit_ids": ["661618f88e185d9b327bdab2", "661618f88e185d9b327bdab3"]
}


@apiUse respone_organization


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data": [
        {
            "id": "661618f88e185d9b327bdab3",
            "name": "Chi nhánh Hà Nội",
            "number_level": 2
        },
        {
            "id" : "66136c758e185d9b321a2f20",
            "scope_code" : "KVBSH#1603_2024",
            "number_level" : 2
        }
    ]
}
"""


# Import đơn vị
"""
@api {POST} {domain}/api/v2.1/organization/import Tạo mới đơn vị - Import
@apiVersion 1.0.0
@apiGroup Organizational
@apiName ImportUnits

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (files:)     {file}         file                    file muốn import  

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200
}
"""
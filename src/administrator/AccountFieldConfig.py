# ================================================ FOR SALE ========================================================
"""
@apiDefine ResponseForSale

@apiSuccess {String}            data.merchant_id                       Định danh của tennant
@apiSuccess {String}            data.id                                <code>ID</code> định danh của resource
@apiSuccess {String}            data.field_name                        Tên field hiển thị
@apiSuccess {String}            data.field_key                         Tên key lưu trữ trong database
@apiSuccess {String}            data.field_property                    Kiểu dữ liệu của filed_key
@apiSuccess {String}            data.description                       Mô tả thông tin cấu hình
@apiSuccess {String}            data.field_location                    Vị trí lưu trữ, nhận giá trị <code>mysql, mongo</code>
@apiSuccess {String}            data.status                            Trạng thái sử dụng của config

"""

# ----------------------- Get list field by merchant_id  -----------------------
"""
@api {GET} /api/v2.1/accounts/field-config                Lấy danh sách field theo merchant_id
@apiGroup AccountFieldConfig
@apiDescription Lấy danh sách field theo merchant_id
@apiVersion 1.0.0
@apiName GetListFieldByMerchantID
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách field theo merchant_id


@apiSuccess {String}            data.merchant_id                       Định danh của tennant
@apiSuccess {String}            data.id                                <code>ID</code> định danh của resource
@apiSuccess {String}            data.field_name                        Tên field hiển thị
@apiSuccess {Integer}           data.field_key                         Tên key lưu trữ trong database
@apiSuccess {Integer}           data.field_property                    Kiểu dữ liệu của filed key
@apiSuccess {String}            data.description                       Mô tả thông tin cấu hình
@apiSuccess {String}            data.field_location                    Vị trí lưu trữ, nhận giá trị <code>mysql, mongo</code>
@apiSuccess {String}            data.status                            Trạng thái sử dụng của config

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "request thành công.",
    "data":  [
        {
            "description": "",
            "field_key": "email",
            "field_location": "mysql",
            "field_name": "E-mail",
            "field_property": "string",
            "merchant_id": "",
            "status": "enable"
        },
        {
            "description": "",
            "field_key": "phone_number",
            "field_location": "mysql",
            "field_name": "Số điện thoại",
            "field_property": "string",
            "merchant_id": "",
            "status": "enable"
        }
    ]
}

"""
# ----------------------- Search Assign -----------------------
"""
@api {POST} /api/v2.1/accounts/search-assign                Tìm account thỏa mãn bộ lọc theo field
@apiGroup AccountFieldConfig
@apiDescription Tìm account thỏa mãn bộ lọc theo field
@apiVersion 1.0.0
@apiName SearchAssign
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}              module_name                 Tên module
@apiParam   (BODY:)     {List}                team_ids                    Danh sách id của team
@apiParam   (BODY:)     {String}              field_key                   Tên key lưu trữ trong database
@apiParam   (BODY:)     {String}              field_value                 Giá trị của field_key


@apiParamExample    {json}      BODY:
{
    "module_name": "SALE",
    "team_ids": ["0ef9664f-b052-4782-9e1a-39fce73899b7", "2b8e2474-13de-4c81-b231-1aac73605a87", "a3de319d-6064-49ff-8654-5380aec921cc"],
    "field_key": "username",
    "field_value": "luongnd@pingcomshop"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Tìm account thỏa mãn bộ lọc theo field

@apiSuccess {List}            data.account_ids                Danh sách id của account


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
       "data": {
        "account_ids": []
    },
}
"""

# ================================== MANAGER COLLECTION ACCOUNT FIELD CONFIG ==========================================
"""
@apiDefine ResponseMangerAccountFieldConfig

@apiSuccess {String}            data.merchant_id                       Định danh của tennant
@apiSuccess {String}            data.id                                <code>ID</code> định danh của resource
@apiSuccess {String}            data.field_name                        Tên field hiển thị
@apiSuccess {String}            data.field_key                         Tên key lưu trữ trong database
@apiSuccess {String}            data.field_property                    Kiểu dữ liệu của filed_key
@apiSuccess {String}            data.description                       Mô tả thông tin cấu hình
@apiSuccess {String}            data.field_location                    Vị trí lưu trữ, nhận giá trị <code>mysql, mongo</code>
@apiSuccess {String}            data.status                            Trạng thái sử dụng của config

"""

# ----------------------- Create config -----------------------
"""
@api {POST} /api/v2.1/accounts/account-field-config                Tạo config
@apiGroup AccountFieldConfig
@apiDescription Tạo config
@apiVersion 1.0.0
@apiName CreateConfig
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}          merchant_id                       Định danh của tennant
@apiParam   (BODY:)     {String}          field_name                        Tên field hiển thị
@apiParam   (BODY:)     {String}          field_key                         Tên key lưu trữ trong database
@apiParam   (BODY:)     {String}          field_property                    Kiểu dữ liệu của filed_key
@apiParam   (BODY:)     {String}          description                       Mô tả thông tin cấu hình
@apiParam   (BODY:)     {String}          field_location                    Vị trí lưu trữ, nhận giá trị <code>mysql, mongo</code>
@apiParam   (BODY:)     {String}          status                            Trạng thái sử dụng của config


@apiParamExample    {json}      BODY:
{
    "merchant_id" : "truongcl",
    "field_name" : "Truongcl",
    "field_key" : "username",
    "field_property" : "string",
    "description" : "",
    "field_location" : "mysql",
    "status" : "enable"
}


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm Config

@apiUse ResponseMangerAccountFieldConfig

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
            "description": "",
            "field_key": "username",
            "field_location": "mysql",
            "field_name": "Username (Tên đăng nhập)",
            "field_property": "string",
            "merchant_id": "",
            "status": "enable"
    }
}

"""
# ----------------------- Update config  -----------------------
"""
@api {PATCH} /api/v2.1/accounts/account-field-config                    Câp nhật config
@apiGroup AccountFieldConfig
@apiDescription Sửa config theo merchant_id và field_key
@apiVersion 1.0.0
@apiName UpdateConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}          merchant_id                       Định danh của tennant
@apiParam   (BODY:)     {String}          field_name                        Tên field hiển thị
@apiParam   (BODY:)     {String}          field_key                         Tên key lưu trữ trong database
@apiParam   (BODY:)     {String}          field_property                    Kiểu dữ liệu của filed_key
@apiParam   (BODY:)     {String}          description                       Mô tả thông tin cấu hình
@apiParam   (BODY:)     {String}          field_location                    Vị trí lưu trữ, nhận giá trị <code>mysql, mongo</code>
@apiParam   (BODY:)     {String}          status                            Trạng thái sử dụng của config


@apiParamExample    {json}      BODY:
{
    "merchant_id" : "truongcl",
    "field_name" : "Truongcl",
    "field_key" : "username",
    "field_property" : "string",
    "description" : "",
    "field_location" : "mysql",
    "status" : "enable"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa config theo merchant_id và field_key

@apiUse ResponseMangerAccountFieldConfig

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
            "description": "",
            "field_key": "username",
            "field_location": "mysql",
            "field_name": "Username (Tên đăng nhập)",
            "field_property": "string",
            "merchant_id": "",
            "status": "enable"
    }
}

"""
# ----------------------- Upsert config  -----------------------
"""
@api {PUT} /api/v2.1/accounts/account-field-config                    Upsert config
@apiGroup AccountFieldConfig
@apiDescription Sửa config theo merchant_id và field_key, tạo mới nếu không tồn tại
@apiVersion 1.0.0
@apiName UpsertConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}          merchant_id                       Định danh của tennant
@apiParam   (BODY:)     {String}          field_name                        Tên field hiển thị
@apiParam   (BODY:)     {String}          field_key                         Tên key lưu trữ trong database
@apiParam   (BODY:)     {String}          field_property                    Kiểu dữ liệu của filed_key
@apiParam   (BODY:)     {String}          description                       Mô tả thông tin cấu hình
@apiParam   (BODY:)     {String}          field_location                    Vị trí lưu trữ, nhận giá trị <code>mysql, mongo</code>
@apiParam   (BODY:)     {String}          status                            Trạng thái sử dụng của config


@apiParamExample    {json}      BODY:
{
    "merchant_id" : "truongcl",
    "field_name" : "Truongcl",
    "field_key" : "username",
    "field_property" : "string",
    "description" : "",
    "field_location" : "mysql",
    "status" : "enable"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa hoặc tạo mới config theo merchant_id và field_key

@apiUse ResponseMangerAccountFieldConfig

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
            "description": "",
            "field_key": "username",
            "field_location": "mysql",
            "field_name": "Username (Tên đăng nhập)",
            "field_property": "string",
            "merchant_id": "",
            "status": "enable"
    }
}

"""
# ----------------------- Delete config  -----------------------
"""
@api {DELETE} /api/v2.1/accounts/account-field-config                   Xóa config
@apiGroup AccountFieldConfig
@apiDescription Xóa Config theo merchant_id và field_key
@apiVersion 1.0.0
@apiName DeleteConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}          merchant_id                       Định danh của tennant
@apiParam   (BODY:)     {String}          field_key                         Tên key lưu trữ trong database

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa config theo merchant_id và field_key


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Get list config -----------------------
"""
@api {POST} /api/v2.1/accounts/account-field-config              Lấy danh sách config
@apiGroup AccountFieldConfig
@apiDescription Lấy danh sách config
@apiVersion 1.0.0
@apiName GetListConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)     {String}         search              Tìm kiếm tương đối theo merchant_id và field_key

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách config

@apiUse ResponseMangerAccountFieldConfig

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "description": "",
            "field_key": "username",
            "field_location": "mysql",
            "field_name": "Username (Tên đăng nhập)",
            "field_property": "string",
            "merchant_id": "",
            "status": "enable"
        },
        {
            "description": "",
            "field_key": "phone_number",
            "field_location": "mysql",
            "field_name": "Số điện thoại",
            "field_property": "string",
            "merchant_id": "",
            "status": "enable"
        }
    ],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}

"""

# ---------------------------------- Cài đặt cấu hình field cho danh sách account  -------------------------------
"""
@api {POST} /api/v2.1/account/config/display-field       cấu hình field hiển thị theo account 
@apiDescription Cài đặt cấu hình field cho danh sách account 
@apiGroup AccountFieldConfig
@apiVersion 1.0.0
@apiName ConfigDisplayFieldForAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)     {Array}         config              Cấu hình hiển thị mới muốn cài đặt các, thứ tự  field hiển thị sẽ sắp xếp tương ứng theo vị trí của nó 

@apiParamExample {json} Body
{
  "config": ["fullname", "department", "block"]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "config": ["fullname", "department", "block"]
    }
}

"""

# ---------------------------------- Danh sách caác cấu hình hiển thị field  -------------------------------
"""
@api {GET} /api/v2.1/account/config/display-field     Danh sách tất cả field hiển thị  
@apiDescription  Danh sách tất cả field hiển thị  
@apiGroup AccountFieldConfig
@apiVersion 1.0.0
@apiName ListDisplayField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
                "field_key": "username",
                "field_name": "Tên tài khoản",
                "display_in_list_field": True,
                "display_in_list_field_selected": True,
                "disable_remove_list": True,
                "support_sort": True,
                "sort_order": 1,  Thứ tự hiển thị của field từ trái sang phải 

            },
            {
                "field_key": "fullname",
                "field_name": "Họ và tên",
                "display_in_list_field": True,
                "display_in_list_field_selected": True,
                "disable_remove_list": False,
                "support_sort": True,
                "sort_order": 2
            },
            {
                "field_key": "role_group",
                "field_name": "Nhóm quyền",
                "display_in_list_field": True,
                "display_in_list_field_selected": True,
                "disable_remove_list": False,
                "support_sort": True,
                "sort_order": None
            },
            {
                "field_key": "block",
                "field_name": "Khối",
                "display_in_list_field": True,
                "display_in_list_field_selected": False,
                "disable_remove_list": False,
                "support_sort": False,
                "sort_order": None
            },
            {
                "field_key": "department",
                "field_name": "Phòng ban",
                "display_in_list_field": True,
                "display_in_list_field_selected": False,
                "disable_remove_list": False,
                "support_sort": False,
                "sort_order": None
            },
            {
                "field_key": "sol_name",
                "field_name": "Đơn vị kinh doanh",
                "display_in_list_field": True,
                "display_in_list_field_selected": False,
                "disable_remove_list": False,
                "support_sort": False,
                "sort_order": None
            },
            {
                "field_key": "TenChucDanhMoiNhat",
                "field_name": "Chức danh",
                "display_in_list_field": True,
                "display_in_list_field_selected": True,
                "disable_remove_list": False,
                "support_sort": False,
                "sort_order": None
            },
            {
                "field_key": "modules",
                "field_name": "Quyền - Role",
                "display_in_list_field": True,
                "display_in_list_field_selected": True,
                "disable_remove_list": False,
                "support_sort": False,
                "sort_order": None
            },
            {
                "field_key": "policies",
                "field_name": "Chính sách truy cập",
                "display_in_list_field": True,
                "display_in_list_field_selected": False,
                "disable_remove_list": False,
                "support_sort": False,
                "sort_order": None
            },
            {
                "field_key": "phone_number",
                "field_name": "Số điện thoại",
                "display_in_list_field": True,
                "display_in_list_field_selected": False,
                "disable_remove_list": False,
                "support_sort": False,
                "sort_order": None
            },
            {
                "field_key": "email",
                "field_name": "E-mail",
                "display_in_list_field": True,
                "display_in_list_field_selected": False,
                "disable_remove_list": False,
                "support_sort": False,
                "sort_order": None
            },
            {
                "field_key": "teams",
                "field_name": "Team",
                "display_in_list_field": True,
                "display_in_list_field_selected": False,
                "disable_remove_list": False,
                "support_sort": False,
                "sort_order": None
            },
            {
                "field_key": "staff_code",
                "field_name": "Mã nhân viên",
                "display_in_list_field": True,
                "display_in_list_field_selected": False,
                "disable_remove_list": False,
                "support_sort": True,
                "sort_order": None
            },
            {
                "field_key": "created_time",
                "field_name": "Thời gian tạo",
                "display_in_list_field": True,
                "display_in_list_field_selected": False,
                "disable_remove_list": False,
                "support_sort": True,
                "sort_order": None
            },
            {
                "field_key": "last_login_date",
                "field_name": "Thời gian đăng nhập gần nhất",
                "display_in_list_field": True,
                "display_in_list_field_selected": True,
                "disable_remove_list": False,
                "support_sort": True,
                "sort_order": None
            },
            {
                "field_key": AccountSetting.KeyActionLogin.last_device_login,
                "field_name": "Kênh đăng nhập gần nhất",
                "display_in_list_field": True,
                "display_in_list_field_selected": False,
                "disable_remove_list": False,
                "support_sort": False,
                "sort_order": None
            }
    ],
    "lang": "vi",
    "message": "request thành công."
}

"""


# ----------------------- Get list personalize  -----------------------
"""
@api {GET} /api/v2.1/accounts/fields/personalize/list              Lấy danh sách field cá nhân hoá
@apiGroup AccountFieldConfig
@apiDescription Lấy danh sách field cá nhân hoá
@apiVersion 1.0.0
@apiName GetListFieldPersonalize
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccess {Array} data    Response Data
@apiSuccess {String} data.field_name    Tên field
@apiSuccess {String} data.fields    Danh sách field
@apiSuccess {String} data.key    Key field

@apiSuccessExample {json} Response
{
  "code": 200,
  "data": [
    {
      "field_name": "THÔNG TIN NHÂN VIÊN",
      "fields": [
        {
          "field_name": "Danh xưng theo giới tính",
          "field_key": "gender_vocative",
          "replace": "*|GENDER_VOCATIVE|*",
          "merchant_type": "BASE",
          "merchant_id: "",
          "source": "USER"
        },
        {
          "field_name": "Họ và tên",
          "field_key": "fullname",
          "replace": "*|FULLNAME|*",
          "merchant_type": "BASE",
          "merchant_id: "",
          "source": "USER"
        },
        {
          "field_name": "Email",
          "field_key": "email",
          "replace": "*|USER_EMAIL|*",
          "merchant_type": "BASE",
          "merchant_id: "",
          "source": "USER"
        },
        {
          "field_name": "Số điện thoại",
          "field_key": "phone_number",
          "replace": "*|PHONE_NUMBER|*",
          "merchant_type": "BASE",
          "merchant_id: "",
          "source": "USER"
        },
        {
          "field_name": "Chức danh",
          "field_key": "TenChucDanhMoiNhat",
          "replace": "*|STAFF_POSITION|*",
          "merchant_type": "BASE",
          "merchant_id: "",
          "source": "USER"
        },
        {
          "field_name": "Mã nhân viên",
          "field_key": "staff_code",
          "replace": "*|STAFF_CODE|*",
          "merchant_type": "BASE",
          "merchant_id: "",
          "source": "USER"
        },
        {
          "field_name": "Tên đơn vị kinh doanh",
          "field_key": "sol_name",
          "replace": "*|SOL_NAME|*",
          "merchant_type": "BASE",
          "merchant_id: "",
          "source": "USER"
        }
      ],
      "key": "USER"
    }
  ]
  "lang": "en",
  "message": "Request successful."
}

"""
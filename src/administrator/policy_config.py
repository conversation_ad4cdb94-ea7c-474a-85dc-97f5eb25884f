# ============================================ CONDITION KEYS ============================================

"""
@apiDefine ResponseDetailConditionKey

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của domain
@apiSuccess {String}            data.merchant_id                        Định danh của tennant, Default mặc định là ""
@apiSuccess {String}            data.key                                Trường thông tin của resource
@apiSuccess {String}            data.name                               Tên trường thông tin của resource kiểu i18n
@apiSuccess {List}              data.type                               Danh sách kiểu dữ liệu, nhận giá trị <code>string, numeric, date, list, boolean, ip_address, default</code>

@apiSuccess {String}            data.create_on                          Thời điểm khởi tạo
@apiSuccess {String}            data.update_on                          Thời điểm cập nhật

"""

# ----------------------- Create condition key -----------------------
"""
@api {POST} /api/v2.1/policies/condition-keys                Tạo condition key
@apiGroup Condition key
@apiDescription Tạo mới condition key
@apiVersion 2.1.0
@apiName CreateConditionKey
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            [merchant_id]               Định danh của tennant, Default mặc định là ""
@apiParam   (BODY:)     {String}            key                         Trường thông tin của resource
@apiParam   (BODY:)     {String}            name                        Tên trường thông tin của resource kiểu i18n
@apiParam   (BODY:)     {List}              type                        Danh sách kiểu dữ liệu


@apiParamExample    {json}      BODY:
{
    "domain": "ck.mobio.vn"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Tạo mới condition key

@apiUse ResponseDetailConditionKey

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
            "key": "1",
            "merchant_id": "",
            "name": "https://truongcl18.vn",
            "type": [
                "string"
            ],
            "create_on": "Tue, 07 Mar 2023 04:56:53 GMT",
            "update_on": "Tue, 07 Mar 2023 04:56:53 GMT"
        }
}

"""
# ----------------------- Update condition key  -----------------------
"""
@api {PATCH} /api/v2.1/policies/condition-keys                   Cập nhật condition key
@apiGroup Condition key
@apiDescription Cập nhật condition key
@apiVersion 2.1.0
@apiName UpdateConditionKey

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            [merchant_id]               Định danh của tennant, Default mặc định là ""
@apiParam   (BODY:)     {String}            key                         Trường thông tin của resource
@apiParam   (BODY:)     {String}            name                        Tên trường thông tin của resource kiểu i18n
@apiParam   (BODY:)     {List}              type                        Danh sách kiểu dữ liệu


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Cập nhật condition key


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Delete condition key  -----------------------
"""
@api {DELETE} /api/v2.1/policies/condition-keys                   Xóa condition keys
@apiGroup Condition key
@apiDescription Xóa condition keys
@apiVersion 2.1.0
@apiName DeleteConditionKeys

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              merchant_id                  Định danh của tennant, Default mặc định là ""
@apiParam   (Query:)     {String}              key                          Trường thông tin của resource


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa condition keys


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Get list condition key -----------------------
"""
@api {GET} /api/v2.1/policies/condition-keys              Lấy danh sách condition-keys
@apiGroup Condition key
@apiDescription Lấy danh sách condition keys
@apiVersion 2.1.0
@apiName GetListConditionKeys

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách condition keys

@apiUse ResponseDetailConditionKey

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
            "key": "1",
            "merchant_id": "",
            "name": "https://truongcl18.vn",
            "type": [
                "string"
            ],
            "create_on": "Tue, 07 Mar 2023 04:56:53 GMT",
            "update_on": "Tue, 07 Mar 2023 04:56:53 GMT"
        },
        ...
        ],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}

"""

# ============================================ POLICY ACTION ============================================

"""
@apiDefine ResponseDetailPolicyAction

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của action
@apiSuccess {String}            data.merchant_id                        Định danh của tennant, Default mặc định là ""
@apiSuccess {String}            data.key                                Trường thông tin của resource
@apiSuccess {String}            data.name                               Tên trường thông tin của action kiểu i18n
@apiSuccess {String}            data.des                                Mô tả
@apiSuccess {String}            data.service                            Đinh danh service
@apiSuccess {String}            data.access_level                       Level quyền truy cập
@apiSuccess {String}            data.resource                           Định danh resource
"""

# ----------------------- Get list policy action -----------------------
"""
@api {GET} /api/v2.1/policies/actions              Lấy danh sách action
@apiGroup Policy action
@apiDescription Lấy danh sách action
@apiVersion 2.1.0
@apiName GetListPolicyAction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging


@apiParam   (Query:)     {String}              service                  Service cần lọc
@apiParam   (Query:)     {String}              resource                 Resource cần lọc, các resource cách nhau bằng dấu phẩy (,)


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách action

@apiUse ResponseDetailPolicyAction

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
            "access_level": "Add",
            "des": "",
            "key": "AddFromSale",
            "merchant_id": "",
            "name": "Thêm mới từ Sale",
            "resource": "deal",
            "service": "sale"
        },
        ...
        ],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}

"""

# ============================================ POLICY RESOURCE ============================================

"""
@apiDefine ResponseDetailPolicyResource

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của resouce
@apiSuccess {String}            data.merchant_id                        Định danh của tennant, Default mặc định là ""
@apiSuccess {String}            data.key                                Trường thông tin của resource
@apiSuccess {String}            data.name                               Tên trường thông tin của action kiểu i18n
@apiSuccess {String}            data.des                                Mô tả
@apiSuccess {String}            data.service                            Đinh danh service
"""

# ----------------------- Get list policy resource -----------------------
"""
@api {GET} /api/v2.1/policies/resources              Lấy danh sách resource
@apiGroup Policy resource
@apiDescription Lấy danh sách resource
@apiVersion 2.1.0
@apiName GetListPolicyResource

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)     {String}              service                  Service cần lọc

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách resource

@apiUse ResponseDetailPolicyResource

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
            "des": "",
            "key": "deal",
            "merchant_id": "",
            "name": "Cơ hội bán",
            "service": "sale"
        },
        ...
        ],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}
"""

# ============================================ POLICY SERVICE ============================================

"""
@apiDefine ResponseDetailPolicyService

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của resouce
@apiSuccess {String}            data.merchant_id                        Định danh của tennant, Default mặc định là ""
@apiSuccess {String}            data.key                                Trường thông tin của service
@apiSuccess {String}            data.name                               Tên trường thông tin của action kiểu i18n
@apiSuccess {String}            data.des                                Mô tả
"""

# ----------------------- Get list policy service -----------------------
"""
@api {GET} /api/v2.1/policies/services              Lấy danh sách service
@apiGroup Policy service
@apiDescription Lấy danh sách service
@apiVersion 2.1.0
@apiName GetListPolicyService

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách service

@apiUse ResponseDetailPolicyService

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
            "des": "",
            "key": "sale",
            "merchant_id": "",
            "name": "Sales"
        },
        ...
        ],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}
"""

# ============================================ POLICIES  ============================================

"""
@apiDefine ResponseDetailPolicies

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của domain
@apiSuccess {String}            data.merchant_id                        Định danh của tennant, Default mặc định là ""
@apiSuccess {String}            data.code                               Định danh của resource
@apiSuccess {String}            data.name                               Tên của resource
@apiSuccess {String}            data.description                        Mô tả của resource
@apiSuccess {String}            data.type                               Kiểu dữ liệu, nhận giá trị <code>system, private, public</code>
@apiSuccess {String}            data.version                            Phiên bản của resource
@apiSuccess {List}              data.statement                          Danh sách id của statement

@apiSuccess {String}            data.create_on                          Thời điểm khởi tạo
@apiSuccess {String}            data.update_on                          Thời điểm cập nhật

"""

# ----------------------- Create polices -----------------------
"""
@api {POST} /api/v2.1/policies                Tạo polices
@apiGroup Policies
@apiDescription Tạo mới polices
@apiVersion 2.1.0
@apiName CreatePolices
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            name                        Tên của resource
@apiParam   (BODY:)     {String}            description                 Mô tả của resource
@apiParam   (BODY:)     {Dict}              [object_apply]              Chứa thông tin đối tượng áp dụng chính sách, chỉ chứa các key <code>merchant, team, user, role_group, merchant</code>
@apiParam   (BODY:)     {List}              object_apply.user           Danh sách object_id, không thể tồn tại đồng thời với merchant = True
@apiParam   (BODY:)     {List}              object_apply.team           Danh sách object_id
@apiParam   (BODY:)     {List}              object_apply.role           Danh sách object_id
@apiParam   (BODY:)     {List}              object_apply.role_group     Danh sách object_id
@apiParam   (BODY:)     {Bool}              object_apply.merchant       Nếu áp dụng cho toàn bộ user, gửi lên giá trị True, nếu không thì bỏ qua hoặc gửi lên False. Không thể tồn tại đồng thời với user có giá trị

@apiParam   (BODY:)     {List}              statement_fe            Dữ liệu base từ FE


@apiParamExample    {json}      BODY:
{
    "name": "policy 10",
    "description": "policy",
    "object_apply": {
        "user": ["object_id"],
        "team": ["object_id"],
        "role": ["object_id"],
        "role_group": ["object_id"]
    },
    "statement_fe": [
        {
            "effect": "allow",
            "service": "sale",
            "statement_id": "truongcl",
            "resource_all": false,
            "resource": [
                "deal",
                "sale_process"
            ],
            "resource_field_all": {
                "deal": false
            },
            "resource_field": {
                "deal": [
                    "field_1",
                    "field_2"
                ]
            },
            "action_all": false,
            "action": {
                "Add": {
                    "check_all": true,
                    "values": [
                        {
                            "key": "ListFromSale",
                            "service": "sale",
                            "resource": "deal"
                        }
                    ]
                }
            },
            "condition": [
                {
                    "criteria_key": "cri_dyn_multiple_line_text_chu_1652067951246",
                    "values": [
                        "aa"
                    ],
                    "operator_key": "op_is_has_not",
                    "fe_values": {
                        "resource": "deal",
                        "field_key": "number_transactions",
                        "display_type": "single_line",
                        "field_property": 2,
                        "format": "dd/mm/yyyy"
                    }
                }
            ]
        }
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Tạo mới policies

@apiUse ResponseDetailConditionKey

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "646c807d136e292a594f24c4",
        "code": "2736f5d1-f948-11ed-859d-358616ea4de0",
        "create_on": "20230523083646",
        "description": "policy",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name": "policy 10",
        "name_code": "policy 10",
        "number_of_objects_applied": 4,
        "statement": [],
        "statement_abac": [
            {
                "action": [
                    "deal:Add*",
                    "sale_process:Add*"
                ],
                "condition": [
                    {
                        "field": "deal:number_transactions",
                        "if_exists": false,
                        "ignore_case": false,
                        "operator": "StringNotContains",
                        "qualifier": "ForAnyValue",
                        "values": [
                            "aa"
                        ]
                    }
                ],
                "effect": "allow",
                "resource": [
                    "sale:deal",
                    "sale:sale_process"
                ],
                "resource_field": {
                    "deal": [
                        "field_1",
                        "field_2"
                    ]
                },
                "statement_id": "truongcl"
            }
        ],
        "statement_fe": [
            {
                "action": {
                    "Add": {
                        "check_all": true,
                        "values": [
                            {
                                "key": "ListFromSale",
                                "resource": "deal",
                                "service": "sale"
                            }
                        ]
                    }
                },
                "action_all": false,
                "condition": [
                    {
                        "criteria_key": "cri_dyn_multiple_line_text_chu_1652067951246",
                        "fe_values": {
                            "display_type": "single_line",
                            "field_key": "number_transactions",
                            "field_property": 2,
                            "format": "dd/mm/yyyy",
                            "resource": "deal"
                        },
                        "operator_key": "op_is_has_not",
                        "values": [
                            "aa"
                        ]
                    }
                ],
                "effect": "allow",
                "resource": [
                    "deal",
                    "sale_process"
                ],
                "resource_all": false,
                "resource_field": {
                    "deal": [
                        "field_1",
                        "field_2"
                    ]
                },
                "resource_field_all": {
                    "deal": false
                },
                "service": "sale",
                "statement_id": "truongcl"
            }
        ],
        "type": "public",
        "update_on": "20230523083646",
        "version": null
    },
}

"""
# ----------------------- Update policies  -----------------------
"""
@api {PATCH} /api/v2.1/policies/<policy_id>                   Cập nhật policies
@apiGroup Policies
@apiDescription Cập nhật policies
@apiVersion 2.1.0
@apiName UpdatePolicies

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            name                    Tên của resource
@apiParam   (BODY:)     {String}            type                    Kiểu dữ liệu, nhận giá trị <code>system, private, public</code>
@apiParam   (BODY:)     {String}            description             Mô tả của resource
@apiParam   (BODY:)     {String}            version                 Phiên bản của resource
@apiParam   (BODY:)     {Dict}              [object_apply]            Chứa thông tin đối tượng áp dụng chính sách, chỉ chứa các key <code>merchant, team, user, role_group, merchant</code>
@apiParam   (BODY:)     {List}              object_apply.user       Danh sách object_id
@apiParam   (BODY:)     {List}              object_apply.team       Danh sách object_id
@apiParam   (BODY:)     {List}              object_apply.role       Danh sách object_id
@apiParam   (BODY:)     {List}              object_apply.role_group       Danh sách object_id

@apiParam   (BODY:)     {Dict}              [object_un_apply]            Chứa thông tin đối tượng áp dụng chính sách, chỉ chứa các key <code>merchant, team, user, role_group</code>
@apiParam   (BODY:)     {List}              object_un_apply.user       Danh sách object_id
@apiParam   (BODY:)     {List}              object_un_apply.team       Danh sách object_id
@apiParam   (BODY:)     {List}              object_un_apply.role       Danh sách object_id
@apiParam   (BODY:)     {List}              object_un_apply.role_group       Danh sách object_id


@apiParam   (BODY:)     {List}              statement_fe            Dữ liệu base từ FE


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Cập nhật policies


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Delete policies  -----------------------
"""
@api {POST} /api/v2.1/policies/delete                   Xóa policies
@apiGroup Policies
@apiDescription Xóa policies
@apiVersion 2.1.0
@apiName DeletePolicies

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {List}              ids                         Danh sách id cần xóa


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa policies

@apiSuccess   (data)     {List}              data.ids_deleted       Danh sách id đã bị xóa


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "ids_deleted": []
    }
}

"""
# ----------------------- Get list policies -----------------------
"""
@api {GET} /api/v2.1/policies              Lấy danh sách policies
@apiGroup Policies
@apiDescription Lấy danh sách policies
@apiVersion 2.1.0
@apiName GetListPolicies

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)  {String}  [search]                    Chuỗi tìm kiếm
@apiParam   (Query:)  {String}  [sort]                      Field cần sort, một trong các field <code>name, type, number_of_objects_applied</code>
@apiParam   (Query:)  {String}  [order]                     Một trong các giá trị <code>asc, desc</code>


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách policies

@apiUse ResponseDetailPolicies

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "_id": "646c807d136e292a594f24c4",
        "code": "2736f5d1-f948-11ed-859d-358616ea4de0",
        "create_on": "20230523083646",
        "description": "policy",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name": "policy 10",
        "name_code": "policy 10",
        "number_of_objects_applied": 4,
        "statement": [],
        "statement_abac": [
            {
                "action": [
                    "deal:Add*",
                    "sale_process:Add*"
                ],
                "condition": [
                    {
                        "field": "deal:number_transactions",
                        "if_exists": false,
                        "ignore_case": false,
                        "operator": "StringNotContains",
                        "qualifier": "ForAnyValue",
                        "values": [
                            "aa"
                        ]
                    }
                ],
                "effect": "allow",
                "resource": [
                    "sale:deal",
                    "sale:sale_process"
                ],
                "resource_field": {
                    "deal": [
                        "field_1",
                        "field_2"
                    ]
                },
                "statement_id": "truongcl"
            }
        ],
        "statement_fe": [
            {
                "action": {
                    "Add": {
                        "check_all": true,
                        "values": [
                            {
                                "key": "ListFromSale",
                                "resource": "deal",
                                "service": "sale"
                            }
                        ]
                    }
                },
                "action_all": false,
                "condition": [
                    {
                        "criteria_key": "cri_dyn_multiple_line_text_chu_1652067951246",
                        "fe_values": {
                            "display_type": "single_line",
                            "field_key": "number_transactions",
                            "field_property": 2,
                            "format": "dd/mm/yyyy",
                            "resource": "deal"
                        },
                        "operator_key": "op_is_has_not",
                        "values": [
                            "aa"
                        ]
                    }
                ],
                "effect": "allow",
                "resource": [
                    "deal",
                    "sale_process"
                ],
                "resource_all": false,
                "resource_field": {
                    "deal": [
                        "field_1",
                        "field_2"
                    ]
                },
                "resource_field_all": {
                    "deal": false
                },
                "service": "sale",
                "statement_id": "truongcl"
            }
        ],
        "type": "public",
        "update_on": "20230523083646",
        "version": null
    },,
        ...
        ],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}

"""
# ----------------------- Get detail policy  -----------------------
"""
@api {GET} /api/v2.1/policies/<policy_id>                   Lấy chi tiết policy
@apiGroup Policies
@apiDescription Lấy chi tiết policy
@apiVersion 2.1.0
@apiName GetDetailPolicies

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết policy


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "646c807d136e292a594f24c4",
        "code": "2736f5d1-f948-11ed-859d-358616ea4de0",
        "create_on": "20230523083646",
        "description": "policy",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name": "policy 10",
        "name_code": "policy 10",
        "number_of_objects_applied": 4,
        "statement": [],
        "statement_abac": [
            {
                "action": [
                    "deal:Add*",
                    "sale_process:Add*"
                ],
                "condition": [
                    {
                        "field": "deal:number_transactions",
                        "if_exists": false,
                        "ignore_case": false,
                        "operator": "StringNotContains",
                        "qualifier": "ForAnyValue",
                        "values": [
                            "aa"
                        ]
                    }
                ],
                "effect": "allow",
                "resource": [
                    "sale:deal",
                    "sale:sale_process"
                ],
                "resource_field": {
                    "deal": [
                        "field_1",
                        "field_2"
                    ]
                },
                "statement_id": "truongcl"
            }
        ],
        "statement_fe": [
            {
                "action": {
                    "Add": {
                        "check_all": true,
                        "values": [
                            {
                                "key": "ListFromSale",
                                "resource": "deal",
                                "service": "sale"
                            }
                        ]
                    }
                },
                "action_all": false,
                "condition": [
                    {
                        "criteria_key": "cri_dyn_multiple_line_text_chu_1652067951246",
                        "fe_values": {
                            "display_type": "single_line",
                            "field_key": "number_transactions",
                            "field_property": 2,
                            "format": "dd/mm/yyyy",
                            "resource": "deal"
                        },
                        "operator_key": "op_is_has_not",
                        "values": [
                            "aa"
                        ]
                    }
                ],
                "effect": "allow",
                "resource": [
                    "deal",
                    "sale_process"
                ],
                "resource_all": false,
                "resource_field": {
                    "deal": [
                        "field_1",
                        "field_2"
                    ]
                },
                "resource_field_all": {
                    "deal": false
                },
                "service": "sale",
                "statement_id": "truongcl"
            }
        ],
        "type": "public",
        "update_on": "20230523083646",
        "version": null
    },
}

"""
# ----------------------- Quick update policies  -----------------------
"""
@api {PATCH} /api/v2.1/policies/<policy_id>/quick-update                   Cập nhật nhanh policies
@apiGroup Policies
@apiDescription Cập nhật nhanh policies
@apiVersion 2.1.0
@apiName QuickUpdatePolicy

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            name                    Tên của resource
@apiParam   (BODY:)     {String}            [description]             Mô tả của resource


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Cập nhật nhanh policies


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Apply policy  -----------------------
"""
@api {PATCH} /api/v2.1/policies/<policy_id>/apply-policy                   Áp dụng policies
@apiGroup Policies
@apiDescription Áp dụng policy cho đối tượng
@apiVersion 2.1.0
@apiName ApplyPolicy

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {Dict}              [object_apply]            Chứa thông tin đối tượng áp dụng chính sách, chỉ chứa các key <code>merchant, team, user, role_group, merchant</code>
@apiParam   (BODY:)     {List}              object_apply.user       Danh sách object_id, không thể tồn tại đồng thời với merchant = True
@apiParam   (BODY:)     {List}              object_apply.team       Danh sách object_id
@apiParam   (BODY:)     {List}              object_apply.role       Danh sách object_id
@apiParam   (BODY:)     {List}              object_apply.role_group       Danh sách object_id
@apiParam   (BODY:)     {Bool}              object_apply.merchant       Nếu áp dụng cho toàn bộ user, gửi lên giá trị True, nếu không thì bỏ qua hoặc gửi lên False. Không thể tồn tại đồng thời với user có giá trị

@apiParam   (BODY:)     {Dict}              [object_un_apply]            Chứa thông tin đối tượng áp dụng chính sách, chỉ chứa các key <code>merchant, team, user, role_group</code>
@apiParam   (BODY:)     {List}              object_un_apply.user       Danh sách object_id
@apiParam   (BODY:)     {List}              object_un_apply.team       Danh sách object_id
@apiParam   (BODY:)     {List}              object_un_apply.role       Danh sách object_id
@apiParam   (BODY:)     {List}              object_un_apply.role_group       Danh sách object_id


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Áp dụng policy cho đối tượng


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Quick update statement  -----------------------
"""
@api {PATCH} /api/v2.1/policies/<policy_id>/statements/<statement_id>                   Cập nhật nhanh statement
@apiGroup Policies
@apiDescription Cập nhật nhanh statement theo statement_id
@apiVersion 2.1.0
@apiName QuickUpdateStatement

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            effect                    Loại áp dụng
@apiParam   (BODY:)     {String}            service                   Tên service
@apiParam   (BODY:)     {String}            statement_id              Định danh statement
@apiParam   (BODY:)     {Bool}              resource_all              Tất cả resource hoặc không
@apiParam   (BODY:)     {String}            resource                  Danh sách resource khi resource_all = false
@apiParam   (BODY:)     {Dict}              resource_field_all        Tất cả field theo resource
@apiParam   (BODY:)     {Dict}              resource_field            Chi tiết từng field trong resource khi resource_field_all = false
@apiParam   (BODY:)     {Bool}              action_all                Tất cả action
@apiParam   (BODY:)     {Dict}              action                    Chi tiết action theo nhóm
@apiParam   (BODY:)     {List}             condition                  Danh sách điều kiện


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Cập nhật nhanh statement theo statement_id


@apiParamExample    {json}      BODY:
{
    "effect": "allow",
    "service": "sale",
    "statement_id": "truongcl",
    "resource_all": false,
    "resource": [
        "deal",
        "sale_process"
    ],
    "resource_field_all": {
        "deal": false
    },
    "resource_field": {
        "deal": [
            "field_1",
            "field_2"
        ]
    },
    "action_all": false,
    "action": {
        "Add": {
            "check_all": true,
            "values": [
                {
                    "key": "ListFromSale",
                    "service": "sale",
                    "resource": "deal"
                }
            ]
        }
    },
    "condition": [
        {
            "criteria_key": "cri_dyn_multiple_line_text_chu_1652067951246",
            "values": [
                "aa"
            ],
            "operator_key": "op_is_has_not",
            "fe_values": {
                "resource": "deal",
                "field_key": "number_transactions",
                "display_type": "single_line",
                "field_property": 2,
                "format": "dd/mm/yyyy"
            }
        }
    ]
}
@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Quick delete statement  -----------------------
"""
@api {DELETE} /api/v2.1/policies/<policy_id>/statements/<statement_id>                   Xóa nhanh statement trong chi tiết policy
@apiGroup Policies
@apiDescription Xóa nhanh statement trong chi tiết policy theo statement_id
@apiVersion 2.1.0
@apiName QuickDeleteStatement

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa nhanh statement theo statement_id


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Get list account -------------------------------------
"""
@api {GET} /api/v2.1/policies/accounts  Lấy danh sách nhân viên
@apiDescription Lấy danh sách nhân viên
@apiVersion 2.1.0
@apiGroup Policies
@apiName ListAccounts

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header

@apiParam   (Query:)  {String}  [search]  Chuỗi tìm kiếm
@apiParam   (Query:)  {String}  [select_field]  Field lựa chọn để trả về, giá trị được chọn gồm <code>id, merchant_id, fullname, email, username</code>, nhiều giá trị cách nhau bằng dấu phẩy (,). Nếu không gửi lên mặc định trả về tât cả field

@apiSuccess (data) {String} merchant_id ID nhãn hàng lấy danh sách nhân viên
@apiSuccess (data) {String} id ID tài khoản nhân viên
@apiSuccess (data) {String} username Tên truy cập nhân viên
@apiSuccess (data) {String} fullname Tên dầy đủ nhân viên
@apiSuccess (data) {String} email   Email 


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
        "email": "<EMAIL>",
        "fullname": "hienlt",
        "id": "039df779-622a-4a32-96d2-7a3402981cf7",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "username": "hienlt1@pingcomshop"
    },
    {
        "email": "<EMAIL>",
        "fullname": "Admin22",
        "id": "0c617361-003a-4095-a74e-be6e4a045693",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "username": "admin22@pingcomshop"
    },
  ],
  "paging": {
    "page": 1,
    "per_page": 5,
    "total_count": 220,
    "total_page": 44
  }
}
"""
# ----------------------- Get list role -------------------------------------
"""
@api {GET} /api/v2.1/policies/roles  Lấy danh sách quyền
@apiDescription Lấy danh sách quyền
@apiVersion 2.1.0
@apiGroup Policies
@apiName ListRoles

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header

@apiParam   (Query:)  {String}  [search]  Chuỗi tìm kiếm


@apiSuccess (data)    {string}       id               ID quyền
@apiSuccess (data)    {string}       name             Tên quyền


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
        "id": "03116fb9-eacd-4b56-b7d3-c6a9882a0fba",
        "name": "Ngoan test ads"
    },
    {
        "id": "05596998-3ee7-4a36-b27e-34e2789ed93a",
        "name": "Nhóm quyền Hướng FE (Vui lòng không thao tác)"
    },
  ],
  "paging": {
    "page": 1,
    "per_page": 5,
    "total_count": 220,
    "total_page": 44
  }
}
"""
# ----------------------- Get list role by ids -------------------------------------
"""
@api {POST} /api/v2.1/policies/roles/by-ids  Lấy danh sách quyền theo danh sách id
@apiDescription Lấy danh sách quyền theo danh sách id
@apiVersion 2.1.0
@apiGroup Policies
@apiName ListRolesByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header

@apiParam   (Query:)  {String}  [search]  Chuỗi tìm kiếm


@apiSuccess (data)    {string}       id               ID nhóm quyền
@apiSuccess (data)    {string}       name             Tên nhóm quyền

@apiParam   (BODY:)     {List}              ids                        Danh sách ids cần lấy
@apiParamExample    {json}      BODY:
{
    "ids": ["03116fb9-eacd-4b56-b7d3-c6a9882a0fba"]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
        "id": "03116fb9-eacd-4b56-b7d3-c6a9882a0fba",
        "name": "Ngoan test ads"
    }
  ],
  "paging": {
    "page": 1,
    "per_page": 5,
    "total_count": 220,
    "total_page": 44
  }
}
"""
# ----------------------- Get list role group -------------------------------------
"""
@api {GET} /api/v2.1/policies/role-groups  Lấy danh sách nhóm quyền
@apiDescription Lấy danh sách nhóm quyền
@apiVersion 2.1.0
@apiGroup Policies
@apiName ListRoleGroups

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header


@apiSuccess (data)    {string}       key              Key định danh cho nhóm quyền
@apiSuccess (data)    {string}       name             Tên nhóm quyền


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "key": "owner",
            "name": "Owner"
        },
        {
            "key": "admin",
            "name": "Admin"
        },
        {
            "key": "manager",
            "name": "Manager"
        },
        {
            "key": "user",
            "name": "User"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": -1,
        "total_count": 4,
        "total_page": 1
    }
}
"""
# ----------------------- Get list block -------------------------------------
"""
@api {GET} /api/v2.1/policies/blocks  Lấy danh sách khối
@apiDescription Lấy danh sách khối
@apiVersion 2.1.0
@apiGroup Policies
@apiName ListBlocks

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header


@apiSuccess (data)    {string}       key              Key định danh cho khối
@apiSuccess (data)    {string}       name             Tên khối


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "key": "KHCN",
            "name": "Khách hàng cá nhân"
        },
        {
            "key": "KHDN",
            "name": "Khách hàng doanh nghiệp"
        },
        {
            "key": "NHBH",
            "name": "Khách hàng bảo hiểm"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": -1,
        "total_count": 3,
        "total_page": 1
    }
}
"""
# ----------------------- Get list objects applied  -----------------------
"""
@api {GET} /api/v2.1/policies/<policy_id>/objects-applied                   Lấy danh sách đối tượng áp dụng
@apiGroup Policies
@apiDescription Lấy danh sách đối tượng áp dụng
@apiVersion 2.1.0
@apiName GetListObjectsApplied

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách đối tượng áp dụng

@apiSuccess   (data)     {Dict}              data            Chứa thông tin đối tượng áp dụng chính sách, chỉ chứa các key <code>merchant, team, user, role_group, merchant</code>
@apiSuccess   (data)     {List}              data.user       Danh sách object_id
@apiSuccess   (data)     {List}              data.team       Danh sách object_id
@apiSuccess   (data)     {List}              data.role       Danh sách object_id
@apiSuccess   (data)     {List}              data.role_group       Danh sách object_id

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "role": [
            "object_id"
        ],
        "role_group": [
            "object_id"
        ],
        "team": [
            "object_id"
        ],
        "user": [
            "object_id",
            "object_id",
            "object_id"
        ]
    },
}

"""

# ============================================ POLICY ACCOUNT FIELD ============================================

"""
@apiDefine ResponseDetailPolicyAccountField

@apiSuccess {Object}            data.fields                                    Danh sách field
@apiSuccess {String}            data.fields.merchant_id                        Định danh của tennant, Default mặc định là ""
@apiSuccess {String}            data.fields.field_key                          Tên key lưu trữ trong database
@apiSuccess {String}            data.fields.display_type                       Kiểu hiển thị
@apiSuccess {String}            data.fields.field_property                     Kiểu dữ liệu của key
@apiSuccess {String}            data.fields.display_in_abac_config             Có sử dụng cho ABAC không
@apiSuccess {String}            data.fields.field_name                         Tên field

@apiSuccess {String}            data.group_key                          Key của group
@apiSuccess {String}            data.group_name                         Tên của group
@apiSuccess {String}            data.is_base                            Có phải là cơ bản
@apiSuccess {String}            data.group_index                        Vị trí của group
"""

# ----------------------- Get list policy account field -----------------------
"""
@api {GET} /api/v2.1/policies/account-fields              Lấy danh sách account field
@apiGroup Policy account field
@apiDescription Lấy danh sách account field
@apiVersion 2.1.0
@apiName GetListPolicyAccountField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách account field

@apiUse ResponseDetailPolicyAccountField

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "fields": [
                {
                    "display_in_abac_config": true,
                    "display_type": "multi_line",
                    "field_key": "role_id",
                    "field_name": "Quyền - Role",
                    "field_property": 2,
                    "merchant_id": ""
                },
                {
                    "display_in_abac_config": true,
                    "display_type": "multi_line",
                    "field_key": "team_id",
                    "field_name": "Team",
                    "field_property": 2,
                    "merchant_id": ""
                }
            ],
            "group_key": "information",
            "group_name": "Nhóm mặc định",
            "is_base": true
            "group_index": 0
        }
    ],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}
"""

#----------------------------------- GET INFO POLICIES BY POLICY_ID -----------------------------------
"""
@api {POST} /api/v2.1/policies/by-ids  Thông tin chi tiết theo id chính sách
@apiDescription Danh sách thông tin chi tiết theo id chính sách
@apiVersion 1.0.0
@apiGroup Policies_v2
@apiName ListPoliciesByIDPolicy

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Body:)    {string}       merchant_id        id merchant con
@apiParam   (Body:)    {Array}    policies   Danh sách chính sách id


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
            {
                "id": "23sdaui-1323nsnda-123123",
                "name": "test",
                "description": "test"
            },
            {
                "id": "23sdaui-1323nsnda-123123",
                "name": "test",
                "description": "test"
            },
    ],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
    "lang": "vi",
    "message": "request th\u00e0nh c\u00f4ng."
}
"""

#------------------------------ LIST POLICIES -----------------------------------------------
"""
@api {GET} /api/v2.1/policies/all Danh sách chính sách
@apiDescription Danh sách chính sách
@apiVersion 1.0.0
@apiGroup Policies_v2
@apiName ListPolicies

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query:)    {string}       merchant_id        id merchant     

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
            {
                "code": "23sdaui-1323nsnda-123123",
                "name": "test",
                "description": "test"
            },
            {
                "code": "23sdaui-1323nsnda-123123",
                "name": "test",
                "description": "test"
            },
    ],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
    "lang": "vi",
    "message": "request th\u00e0nh c\u00f4ng."
}
"""

# --------------------------- DANH SÁCH CHÍNH SÁCH CHƯA ĐƯỢC ÁP DỤNG THEO ROLE ------------------------------

"""
@api {get} /api/v2.1/policies/object/<object_id>/not-apply Lấy danh sách chính sách chưa được áp dụng
@apiDescription Lấy danh sách tất cả chính sách chưa được áp dụng
@apiVersion 2.1.0
@apiGroup Policies_v2
@apiName ListPolicyNotApplyModule 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam (Query:)    {string}       merchant_id        id merchant
@apiParam (Query:)    {string}       type               Loại đối tượng cần tác động [ role, user, team ]

@apiSuccess (data) {string} id Module id
@apiSuccess (data) {string} name Tên module
@apiSuccess (data) {string} description Mô tả module


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
      "name": "Quản lý nội dung",
      "description": "Quản lý nội dung chương trình",
    },
    ...
  ],
  "lang": "vi"
}
"""

# ----------------------- POLICIES APPLY ----------------------------------
"""
@api {PATCH} /api/v2.1/policies/policy-apply/<object_id>/by-type  Thêm hoặc gỡ policy theo type
@apiDescription  Thêm hoặc gỡ policy theo type
@apiVersion 2.1.0
@apiGroup Policies_v2
@apiName AddOrDeleteByType


@apiParam   (Body:)    {Array}        [apply]                Danh sách chính sách cần thêm mới
@apiParam   (Body:)    {Array}        [un_apply]             Danh sách chính sách cần gỡ bỏ
@apiParam   (Body:)    {String}       type                   Loại đối tượng cần tác động [ role, user, team ]
@apiParam   (Body:)    {String}       merchant_id            ID doanh nghiệp

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":{
        "merchant_id": "",
        "apply": [],
        "un_apply": [],
        "type": "",
        "object_id": ""
    }
    "lang": "vi",
    "message": "request th\u00e0nh c\u00f4ng."
}
"""
# ---------------------- GET LIST POLICY BY ACCOUNT_ID ----------------------
"""
@api {GET} /api/v2.1/policies/assign-account  Danh sách chính sách theo tài khoản người dùng
@apiDescription  Danh sách chính sách theo tài khoản người dùng
@apiVersion 2.1.0
@apiGroup Policies_v2
@apiName ListPoliciesByAccount

@apiParam   (Query:)    {string}       account_id            account id 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "code": "1d2c59f1-21f4-11ee-a17f-45311bd761af",
            "name": "Chính sách cấp toàn bộ quyền thao tác ở các module"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------------------- GET LIST OBJECT APPLY BY POLICY ID----------------------
"""
@api {GET} /api/v2.1/policies/objects/<object_id>/apply Danh sách đổi tượng truy cập theo policy id
@apiDescription  Danh sách đổi tượng truy cập theo policy id
@apiVersion 2.1.0
@apiGroup Policies_v2
@apiName ListObjectByPolicyID

@apiParam   (Query:)    {string}      [merchant_id]   ID doanh nghiệp

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "role": [],
        "team": [
            "36efb590-82d0-11ee-a183-e92eba9a3c60",
            "3f7f6aae-82bf-11ee-9801-1e491148a59c",
            "61ddd32c-82cb-11ee-9801-1e491148a59c",
            "8d53ba29-82ce-11ee-a183-e92eba9a3c60",
            "c2e21d56-82ba-11ee-93fd-c1c4eb028675",
            "fe8c3616-82ce-11ee-a183-e92eba9a3c60"
        ],
        "user": [
            "0002973d-81fd-4c0d-9aa5-7ebddd4a92e4",
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ----------------------- Get list policies except by ids -----------------------
"""
@api {POST} /api/v2.1/policies/except/by-ids              Lấy danh sách policies ngoại trừ ids gừi xuống 
@apiGroup Policies
@apiDescription Lấy danh sách policies ngoại trừ ids gừi xuống 
@apiVersion 2.1.0
@apiName GetListPoliciesExceptByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Body:)  {String}  [search]                    Chuỗi tìm kiếm
@apiParam   (Body:)  {String}  [sort]                      Field cần sort, một trong các field <code>name, type, number_of_objects_applied</code>
@apiParam   (Body:)  {String}  [order]                     Một trong các giá trị <code>asc, desc</code>
@apiParam   (Body:)  {List}  [ids_policy]                     Một trong các giá trị <code>asc, desc</code>


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách policies

@apiUse ResponseDetailPolicies

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "_id": "646c807d136e292a594f24c4",
        "code": "2736f5d1-f948-11ed-859d-358616ea4de0",
        "create_on": "20230523083646",
        "description": "policy",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name": "policy 10",
        "name_code": "policy 10",
        "number_of_objects_applied": 4,
        "statement": [],
        "statement_abac": [
            {
                "action": [
                    "deal:Add*",
                    "sale_process:Add*"
                ],
                "condition": [
                    {
                        "field": "deal:number_transactions",
                        "if_exists": false,
                        "ignore_case": false,
                        "operator": "StringNotContains",
                        "qualifier": "ForAnyValue",
                        "values": [
                            "aa"
                        ]
                    }
                ],
                "effect": "allow",
                "resource": [
                    "sale:deal",
                    "sale:sale_process"
                ],
                "resource_field": {
                    "deal": [
                        "field_1",
                        "field_2"
                    ]
                },
                "statement_id": "truongcl"
            }
        ],
        "statement_fe": [
            {
                "action": {
                    "Add": {
                        "check_all": true,
                        "values": [
                            {
                                "key": "ListFromSale",
                                "resource": "deal",
                                "service": "sale"
                            }
                        ]
                    }
                },
                "action_all": false,
                "condition": [
                    {
                        "criteria_key": "cri_dyn_multiple_line_text_chu_1652067951246",
                        "fe_values": {
                            "display_type": "single_line",
                            "field_key": "number_transactions",
                            "field_property": 2,
                            "format": "dd/mm/yyyy",
                            "resource": "deal"
                        },
                        "operator_key": "op_is_has_not",
                        "values": [
                            "aa"
                        ]
                    }
                ],
                "effect": "allow",
                "resource": [
                    "deal",
                    "sale_process"
                ],
                "resource_all": false,
                "resource_field": {
                    "deal": [
                        "field_1",
                        "field_2"
                    ]
                },
                "resource_field_all": {
                    "deal": false
                },
                "service": "sale",
                "statement_id": "truongcl"
            }
        ],
        "type": "public",
        "update_on": "20230523083646",
        "version": null
    },,
        ...
        ],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}

"""
#!/usr/bin/python
# -*- coding: utf8 -*-

******************************* API THÊM CARD DASHBOARD MỚI VÀO KHO ******************************
*                                                                                                *
* version: 1.0.0                                                                                 *
**************************************************************************************************
"""
@api {POST} /api/v2.1/dashboard-cards Thêm card dashboard vào kho.
@apiDescription Thêm mới card dashboard vào kho 
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName AddCardDashboard

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)  {String} key Key của card 
@apiParam   (Body:)  {String} [title] Tiêu đề của card
@apiParam   (Body:) {String} group_code Tên nhóm của card 
@apiParam   (Body:) {String} [tag] Tag của card 
@apiParam   (Body:) {Json} [data] Thông tin của card. ex:thông tin vị trí của card trên dashboard 
@apiParam   (Body:) {int} [is_base] Có phải mặc định của toàn hệ thống hay không,
mặc định nếu không truyền lên thì giá trị là 0 <code>1-mặc định,0-bình thường</code>
@apiParam   (Body:) {int} [status]    Trạng thái của card <code>1- hoạt động, 0- tắt</code>
@apiParamExample {json} Body example
{
  "tag": "chuong trinh voucher con hieu luc",
  "key": "mo-main_dashboard-valid-voucher",\
  "rows": 1,
  "order": 3,
  "cols": 1,
  "x": 0,
  "y": 0
  "group_code": "MARKETING",
  "title": "Chương trình Voucher còn hiệu lực",
  "is_base": 1,
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "card_id":"53610ff3-e850-4787-a862-c8c195974609",
    "tag": "chuong trinh voucher con hieu luc",
    "key": "mo-main_dashboard-valid-voucher",
    "rows": 1,
    "order": 3,
    "cols": 1,
    "x": 0,
    "y": 0
    "group_code": "MARKETING",
    "title": "Chương trình Voucher còn hiệu lực",
    "is_base": 1,
    "status": 1
  }
}
"""
******************************* API SỬA CARD DASHBOARD TRONG KHO ******************************
*                                                                                                *
* version: 1.0.0                                                                                 *
**************************************************************************************************
"""
@api {PUT} /api/v2.1/dashboard-cards/<card_id> Sửa card dashboard trong kho.
@apiDescription Sửa card dashboard trong kho.
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName UpdateCardDashboard

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)  {String} [title] Tiêu đề của card
@apiParam   (Body:) {String} [group_code] Tên nhóm của card 
@apiParam   (Body:) {String} [tag] Tag của card 
@apiParam   (Body:) {Json} [data] Thông tin của card. ex:thông tin vị trí của card trên dashboard 
@apiParam   (Body:) {int} [is_base] Có phải mặc định của toàn hệ thống hay không<code>1-mặc định,0-bình thường</code>
@apiParamExample {json} Body example
{
  "tag": "chuong trinh voucher con hieu luc",
  "rows": 1,
  "order": 3,
  "cols": 1,
  "x": 0,
  "y": 0
  "group_code": "MARKETING",
  "title": "Chương trình Voucher còn hiệu lực",
  "is_base": 1
}
@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "id": "53610ff3-e850-4787-a862-c8c195974609",
    "tag": "chuong trinh voucher con hieu luc",
    "key": "mo-main_dashboard-valid-voucher",
    "rows": 1,
    "order": 3,
    "cols": 1,
    "x": 0,
    "y": 0
    "group_code": "MARKETING",
    "title": "Chương trình Voucher còn hiệu lực",
    "is_base": 1,
    "status": 1
  }
}
"""
******************************* API XÓA CARD DASHBOARD TRONG KHO *********************************
*                                                                                                *
* version: 1.0.0                                                                                 *
**************************************************************************************************
"""
@api {DELETE} /api/v2.1/dashboard-cards Xóa card dashboard trong kho.
@apiDescription Xóa cards dashboard trong kho.
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName DeleteCardDashboard

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header

@apiParam   (Query:) {String} ids  Danh sách card dashboard id cần xóa ex: "uuid1,uuid2,uuid3"
@apiSuccess {Array} card_ids    Danh sách mảng dashboard card ids đã được xóa thành công.
@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "card_ids": ["uuid1", "uuid2", "uuid3"]

}
"""

******************************* API LẤY DANH SÁCH CARD TRONG KHO DASHBOARD CARD ******************
*                                                                                                *
* version: 1.0.0                                                                                 *
**************************************************************************************************
"""
@api {GET} /api/v2.1/dashboard-cards  Lấy danh sách card dashboard 
@apiDescription Lấy danh sách card dashboard. Phân trang chỉ có ở assign = all
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName GetCardDashboard

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)    {String}  [assign]   Danh sách card được phân cho merchant hoặc user
<ul>
    <li>merchant: Danh sách card được phân cho merchant  </li>
    <li>user: Danh sách card được phân cho user  </li>
    <li>all: Lấy card trong kho </li>
  </ul>
@apiParam   (Query:)    {String}  [search]     Tìm kiếm fields theo key.
@apiParam   (Query:)    {String}  [ids]  ids của Merchant hoặc user id của user được phân công card ex: "uuid1,uuid2,uuid3"
<code>Nếu assign là merchant và user thì ids là bắt buộc</code>
@apiSuccessExample {Body} Response assign = merchant/user: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
    {
        "merchant_id": "",
        "account_id": "",
        "cards":[
          {
            "card_id": "53610ff3-e850-4787-a862-c8c195974609",
            "tag": "chuong trinh voucher con hieu luc",
            "key": "mo-main_dashboard-valid-voucher",
            "rows": 1,
            "order": 3,
            "cols": 1,
            "x": 0,
            "y": 0,
            "id": "MARKETING",
            "group_code": "MARKETING",
            "title": "Chương trình Voucher còn hiệu lực",
            "is_base": 1,
            "status": 1
          },
            ...
        ]
    },
    ...
  ]
}

@apiSuccessExample {Body} Response assign = all: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
          {
            "card_id": "53610ff3-e850-4787-a862-c8c195974609",
            "tag": "chuong trinh voucher con hieu luc",
            "key": "mo-main_dashboard-valid-voucher",
            "rows": 1,
            "order": 3,
            "cols": 1,
            "x": 0,
            "y": 0
            "group_code": "MARKETING",
            "id": "MARKETING",
            "title": "Chương trình Voucher còn hiệu lực",
            "is_base": 1,
            "status": 1
          },
            ...
        ]
}
"""
******************************* API ASSIGNMENT CARD DASHBOARD *****************
*                                                                              *
* version: 1.0.0                                                               *
********************************************************************************
"""
@api {POST} /api/v2.1/dashboard-cards/actions/assign Assignment cards dashboard cho merchant
@apiDescription Phân công các cards dashboard cho merchant nằm trong mảng ids, thu hồi quyền sử dụng các card không trong danh sách gửi lên
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName AssignCardDashboard

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)    {String}  assign   Loại phân công card dashboard
<ul>
    <li>merchant: Phân công card cho merchant  </li>
</ul>
@apiParam   (Body:)    {Array}  ids  ids của Merchant được phân công card 
@apiParam   (Body:)    {Array}  card_ids  Danh sách các card dashboard assignment

@apiParamExample {json} Body example
{
  "assign": "merchant",
  "ids": ["88deffa9-ba36-43fc-ace3-18aaa41454fb", "59e2eaf3-43e4-4154-a7d2-6c2d4c74d667"],
  "card_ids": ["8edd15fd-a432-49f8-83b8-f78be603c9c8", "cce6f1eb-b9fc-426f-8076-4a497f07b4cb"]
  
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
      "assign": "merchant",
      "ids": ["88deffa9-ba36-43fc-ace3-18aaa41454fb", "59e2eaf3-43e4-4154-a7d2-6c2d4c74d667"],
      "card_ids": ["8edd15fd-a432-49f8-83b8-f78be603c9c8", "cce6f1eb-b9fc-426f-8076-4a497f07b4cb"]
  }
}
"""
******************************* API UNASSIGNMENT CARD DASHBOARD *****************
*                                                                              *
* version: 1.0.0                                                               *
********************************************************************************
"""
@api {POST} /api/v2.1/dashboard-cards/actions/unassign Unassignment cards dashboard cho merchant
@apiDescription Thu hồi quyền sử dụng các card dashboard của merchant nằm trong mảng ids
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName UnassignCardDashboard

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)    {String}  assign   Loại gỡ phân công card dashboard
<ul>
    <li>merchant: Gỡ phân công card cho merchant  </li>
</ul>
@apiParam   (Body:)    {Array}  ids  ids của Merchant bị thu hồi quyền sử dụng card
@apiParam   (Body:)    {Array}  card_ids  Danh sách các card dashboard assignment

@apiParamExample {json} Body example
{
  "assign": "merchant",
  "ids": ["88deffa9-ba36-43fc-ace3-18aaa41454fb","59e2eaf3-43e4-4154-a7d2-6c2d4c74d667"],
  "card_ids": ["8edd15fd-a432-49f8-83b8-f78be603c9c8", "cce6f1eb-b9fc-426f-8076-4a497f07b4cb"]
  
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
      "assign": "merchant",
      "ids": ["88deffa9-ba36-43fc-ace3-18aaa41454fb", "59e2eaf3-43e4-4154-a7d2-6c2d4c74d667"],
      "card_ids": ["8edd15fd-a432-49f8-83b8-f78be603c9c8", "cce6f1eb-b9fc-426f-8076-4a497f07b4cb"]
  }
}
"""
******************************* API TẠO DASHBOARD *****************
*                                                                 *
* version: 1.0.0                                                  *
*******************************************************************
"""
@api {POST} /api/v2.1/dashboards Tạo dashboard
@apiDescription Tạo dashboard
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName AddDashboard

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} merchant_id  id của merchant tạo dashboard 
@apiParam   (Body:) {String} account_id  id của user tạo dashboard 
@apiParam   (Body:) {int} is_default  Dashboard mặc định hay không <code>1-mặc định, 0-bình thường</code>
<code>Nếu là mặc định thì tất cả các user của merchant đều có dashboard này</code>
@apiParam   (Body:) {String} name  Tên cảu dashboard trong hệ thống

@apiParamExample {json} Body example
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "account_id": "88deffa9-ba36-43fc-ace3-18aaa41454fb",
  "is_default": 1,
  "name": "dashboard mặc định"
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "account_id": "88deffa9-ba36-43fc-ace3-18aaa41454fb",
      "is_default": 1,
      "name": "dashboard mặc định",
      "id": "53610ff3-e850-4787-a862-c8c195974609"
    }
}
"""
******************************* API LẤY DASHBOARD *****************
*                                                                 *
* version: 1.0.0                                                  *
*******************************************************************
"""
@api {GET} /api/v2.1/dashboards Lấy dashboard
@apiDescription Lấy dashboard
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName GetDashboard

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:) {String} account_id  id của user tạo dashboard 

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "data":[
  {
      "is_default": 1,
      "name": "dashboard mặc định",
      "id": "53610ff3-e850-4787-a862-c8c195974609",
      "selected": true,
      "categoryCardFilterHistory": {
                                "dateTimeType": "_30days_ago"
      },
      "cards":[
        {
            "card_id": "53610ff3-e850-4787-a862-c8c195974609",
            "tag": "chuong trinh voucher con hieu luc",
            "key": "mo-main_dashboard-valid-voucher",
            "rows": 1,
            "order": 3,
            "cols": 1,
            "x": 0,
            "y": 0
            "group_code": "MARKETING",
            "id": "MARKETING",
            "title": "Chương trình Voucher còn hiệu lực",
            "is_base": 1,
            "status": 1,
            "display": true
          },
        ....
      ]
  },
  {
    "is_default": 0,
    "name": "dashboard cho nhân viên",
    "selected": true,
    "id": "53610ff3-e850-4787-a862-c8c195974669",
    "cards":
    [   {
          "card_id": "1d59d351-2f7d-41ab-b01d-eea62fafc23e",
          "categoryCardFilterHistory": {
              "dateTimeType": "_30days_ago",
              "pages": [
                  "105713404894650",
                  "858617877680201",
                  "105263841245993",
                  "100750295549695",
                  "106501704395221",
                  "110892141182972"
              ],
              "staff_ids": "f213475e-9b8c-4f15-8496-1294563ac091,7fc0a33c-baf5-11e7-a7c2-0242ac180003,b939d995-9c64-4794-9e44-2b7e243e5145,1cc41434-f053-414b-baa8-bb90b39c4355,906ae4ff-3721-4fab-a2ff-90e3d3eff03b,ff210289-ff75-48a4-a318-58a4e5baeb92,9f16a212-bc65-411c-95cf-a7ee6e54c481,22306193-7e15-4b81-a9b6-d3d17454e2ba,dae7d928-7c20-4d6f-801b-3426747066ac,d6dcf8a6-8dfb-4fbc-bd62-6e4264036b12,595c311a-c453-4b4e-b03f-b1e62be672e8,754a7036-f169-4fd8-826f-9c5943e38d25,a2fcb147-0dd1-46ae-b0fe-849b9c82d51d,c1b427f3-122c-4ae7-b1b3-a3901fc34de5,cb2b68a7-49d8-466e-9ccb-dac3e0d86eb4,d72f7f98-e21d-4e8a-90d2-e730b3e4b49a,d8904795-d0a5-4fec-b6c9-66d901e49d5e,e35ad580-2647-412c-a265-cb002bd624ea,857b220f-06e8-4491-9d21-c2509506a2f2,2a87f9ea-1ba1-40f0-bc15-9b2b9c9de7a8,d7eb21d4-5f26-4f1b-9f2c-ef3d1f87b859,37592687-58b0-4612-9fec-8002c280ec99,4cee8bdf-5570-4256-a906-5c3f48b022dc,7d26a083-03df-4432-8035-629bca17f449,0c617361-003a-4095-a74e-be6e4a045693,f2b7b1d4-b352-4b51-9e23-89f0bc9f2fb4,823ed8f0-83b2-491d-936d-32c2accfa15b,43459fb0-865b-45d3-9b7e-00948420caf4,cdfdf3fe-4b77-4277-80d2-2f979f77254c,e7ac073f-6ec0-4930-9a9d-454c9e2179cf,435782b2-7d87-4441-87e3-2fd05c763128,d6b819a6-c14b-430c-8ef4-a1f0dae0f13f,d1e4cac7-5d4d-4c13-855c-b41061dcae7b,f18a23aa-2aff-45f9-a67c-9474afbd1b5c,f271a35f-5f29-4c43-94db-c68c72575c77,b4c74885-6810-4abc-909c-14efad547ff1,7a5cecf1-48a3-46c4-aead-5430ea0aff98,3b77c9fb-308f-45f9-afae-515b593be5d2,dc4bfe36-7638-4401-94cb-ab20c4510c9c,3506728c-3d46-4a1b-8ce2-db6cf56bd71d,c3028260-ce3e-41d8-8841-ad69ff008fbc,c921c5af-cb95-464e-b1a6-5052788c39d6,5e5be4dc-f7d7-4ef7-af65-b279f17d9907,95cd4352-bd90-45df-990a-6605c876dde3,e18dfc3a-2fd8-4380-a454-ab019e46dedc,13f156da-d8e5-40c9-bcc1-5db5a3451939,38c39edf-f081-44de-94d4-78b07aebcc37,a9098be6-90c8-496b-b313-d5a8a7680bf4,c666de95-3a97-41ec-a492-57781a99fe36,9234421a-8276-45fa-88c2-b2d8e58d4381,ec3d7766-f10d-4b16-a6dc-0319afe0a767,1a707425-64ac-4c8c-a4c8-254c41053109,2d869099-9555-4178-9a6c-39eff000bee0,5225b037-e9d6-4980-ae86-e77553ac3e60,72c9932b-a54a-4290-a0c4-4ff530137288,d7403f0f-fce5-4e6c-a3c1-0811618a61a7,5a5a890d-936c-4bdd-99b0-fa9133f3752c,25dab7d2-4ef0-4c67-9f27-c2ee06e8605a,404d3c3a-441e-40f3-8970-10b1452bcceb,1786ea6b-ed38-4cb1-960e-e6db8ab6af37,a5e6932f-e8dc-4314-aff1-81bf50f501da,bada58e1-9615-4c04-84eb-ac98ed52b889,cad9cc6e-286b-4dd1-b734-afdd81b69a73,3c02f57a-8c63-49c9-bbb2-682ba311482e,b2d7f1e3-7cdb-424b-bac7-49cac78925d7,e05ef4dd-2e87-4b19-8ba7-7dacf15499d4,bd7d12f6-df36-4f0b-aa33-9f21b6ba7fa8,262f31cd-5069-4e76-b004-04da626a6ee1,0d8ee9eb-68a5-437f-b1d8-db3a5b5eb54b,5ad73e6f-2900-4db3-b4c3-a13fde36e13b,72f2adce-e38b-4904-a952-ba30605416f7,b3d5e624-0074-4825-8f2e-186227882512,b39c2974-3b53-4b03-a01e-49570528d6db,512cd02a-07fd-4ad1-9e3d-a251dbe53c4f,24a8b356-3c34-47a4-9a30-2c9a8c10d6ad,8aa61c03-164e-47e8-945f-396688edb95a,f34cb0f8-5bbd-4382-a052-16dfb70babd0,c21c48e4-96fc-4dee-adfb-14ad9f1a8c25,63a513a9-e8cc-44ac-8cae-8e3a6f86f22c,eea5770a-19b0-4aef-affd-cfc1a072fb15,2fae098c-0140-4038-8cef-e84644fb7555,71ae0558-ed00-481e-963d-c0d305c5bdb9,277c9239-48b4-4f2c-8e3f-338e0762d17b,121d0906-a116-4e3f-a509-b1a943ee14e8,fece7a2c-afab-4f2f-b99a-cb7e3f8dcf9d,eeff76e3-9c9f-41cc-b402-04a544d37ede,024faf35-70fa-45ad-835d-1087ad326e90,725952e7-ae5a-475e-b6f1-0113036d7109,7aa30851-e7a5-4ddb-9979-bcb96cf774cb,37648fd5-e98e-41f7-8b12-a8d7b70f837b,4dbcd6d5-45a8-49df-9880-4dd73df3b550,4ad5c863-d367-436f-9434-8f70b355580d,5774e198-c473-493e-876b-f8bb306d2ef6,161c4f3d-e738-4efa-823f-2e2ba7205367,3bcbe4ce-ad7f-4c40-ad32-d54fa2f86878,bc695ead-517f-4e96-9d6b-1a53c378e4f1,e4868ab0-4e1e-4024-bcfc-6a24a344ee6a,78de76e3-0b57-41f4-9efe-b52a4e34bf4f,91331124-bff5-4f13-83cd-f9b4aa8a333a,6140ee71-7808-4e82-b564-70a66caa0a29,71a0a28d-908b-436f-913b-ff467fc1e7ee,c32cb3e1-600a-4b1f-b7a2-cf38eb1c832a,af8ac9b2-2afb-420d-b2e8-76b893c2f3eb"
          },
          "cols": 2,
          "display": true,
          "group_code": "SOCIAL",
          "id": "SOCIAL",
          "is_base": 1,
          "key": "mo-dashboard-report-response-performance-comments",
          "rows": 1,
          "status": 0,
          "tag": "Báo cáo hiệu suất phản hồi Bình Luận",
          "title": "Báo cáo hiệu suất phản hồi Bình Luận",
          "x": 0,
          "y": 3
        }
        ....
    ]
  },
  ....
}
"""
******************************* API SỬA DASHBOARD *****************
*                                                                 *
* version: 1.0.0                                                  *
*******************************************************************
"""
@api {PUT} /api/v2.1/dashboards/<dashboard_id> Sửa dashboard
@apiDescription Sửa dashboard
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName UpdateDashboard

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse paging
@apiUse json_header

@apiParam   (Body:) {int} [is_default]  Dashboard mặc định hay không <code>1-mặc định, 0-bình thường</code>
@apiParam   (Body:) {String} [name]  Tên của dashboard
@apiParam   (Body:) {Array} [cards]  Danh sách và thông tin của các card trong danh sách.
@apiParam   (Body:) {Array} [selected]  Dashboard view đang được lựa chọn trên dashboard


@apiParamExample {json} Body example
{
  "is_default": 1,
  "name": "dashboard mặc định",
  "cards":[
    {
      "id": "53610ff3-e850-4787-a862-c8c195974609"
      "rows": 2,
      "order": 5,
      "cols": 1,
      "x": 0,
      "y": 0
    },
  "selected": true,
    ....
  ]
}
@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "is_default": 1,
    "name": "dashboard mặc định",
    "id": "53610ff3-e850-4787-a862-c8c195974609",
    "cards":[
      {
        "id": "53610ff3-e850-4787-a862-c8c195974609"
        "rows": 2,
        "order": 5,
        "cols": 1,
        "x": 0,
        "y": 0
      },
      ....
    ],
    "selected": true
  }
}
"""

******************************* API SỬA NHIỀU DASHBOARD ***********
*                                                                 *
* version: 1.0.0                                                  *
*******************************************************************
"""
@api {POST} /api/v2.1/dashboards/actions/update_many Sửa nhiều dashboard
@apiDescription Sửa nhiều dashboard
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName UpdateDashboards

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {int} id  id của dashboard update
@apiParam   (Body:) {int} [is_default]  Dashboard mặc định hay không <code>1-mặc định, 0-bình thường</code>
@apiParam   (Body:) {String} [name]  Tên của dashboard
@apiParam   (Body:) {Array} [cards]  Danh sách và thông tin của các card trong danh sách.
@apiParam   (Body:) {Array} [selected]  Dashboard view đang được lựa chọn trên dashboard


@apiParamExample {json} Body example
[
  {
    "id": "53610ff3-e850-4787-a862-c8c195974607",
    "is_default": 1,
    "name": "dashboard mặc định",
    "cards":[
      {
        "id": "53610ff3-e850-4787-a862-c8c195974609"
        "rows": 2,
        "order": 5,
        "cols": 1,
        "x": 0,
        "y": 0
      },
    "selected": true,
      ....
    ]
  },
  {
    "id": "53610ff3-e850-4787-a862-c8c195974610",
    "is_default": 0,
    "name": "dashboard binh thuong",
    "cards":[
      {
        "id": "53610ff3-e850-4787-a862-c8c195974609"
        "rows": 2,
        "order": 5,
        "cols": 1,
        "x": 0,
        "y": 0
      },
    "selected": true,
      ....
    ]
  }
]
@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
  {
    "id": "53610ff3-e850-4787-a862-c8c195974607",
    "is_default": 1,
    "name": "dashboard mặc định",
    "cards":[
      {
        "id": "53610ff3-e850-4787-a862-c8c195974609"
        "rows": 2,
        "order": 5,
        "cols": 1,
        "x": 0,
        "y": 0
      },
    "selected": true,
      ....
    ]
  },
  {
    "id": "53610ff3-e850-4787-a862-c8c195974610",
    "is_default": 0,
    "name": "dashboard binh thuong",
    "cards":[
      {
        "id": "53610ff3-e850-4787-a862-c8c195974609"
        "rows": 2,
        "order": 5,
        "cols": 1,
        "x": 0,
        "y": 0
      },
    "selected": true,
      ....
    ]
  }
]
}
"""

******************************* API LẤY DANH SÁCH PHÂN QUYỀN CHO CARD DASHBOARD  ************
*                                                                 							*
* version: 1.0.0                                                  							*
*********************************************************************************************
"""
@api {GET} /api/v2.1/dashboard-cards/user Lấy danh sách phân quyền dashboard_card của user
@apiDescription Lấy danh sách phân quyền dashboard_card của user
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName ListDashboardsUserPermission

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiSuccess {String} card_id    id của card dashboard
@apiSuccess   {Array} account_ids  danh sách các user có quyền sử dụng dashboardcard
@apiSuccess  {int} accounts_permission  0: các account được chỉ định 1: tất cả các account của tenant 


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
    {
      "card_id": "uuid1",
      "account_ids": ["account_id1", "account_id2", ...],
      "accounts_permission": 0
    },
    {
      "card_id": "uuid2",
      "accounts_permission": 1
    },
    {
      "card_id": "uuid3",
      "account_ids": ["account_id1", "account_id2", ...],
      "accounts_permission": 0
    }
  ]
}
"""

******************************* API PHÂN QUYỀN CHO CARD DASHBOARD  ************
*                                                                 			  *
* version: 1.0.0                                                  			  *
*******************************************************************************
"""
@api {POST} /api/v2.1/dashboard-cards/user Cập nhập quyền dashboard_card của user
@apiDescription Lấy danh sách phân quyền dashboard_card của user
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName AssignDashboardsUserPermission

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} card_id  id của dashboard card
@apiParam   (Body:) {Array} [account_ids]  danh sách các user có quyền sử dụng dashboardcard <code>Phải gửi lên nếu accounts_permission = 0 </code>
@apiParam   (Body:) {int} accounts_permission  0: các account được chỉ định 1: tất cả các account của tenant 

@apiParamExample {json} Body example
[
  {
  	"card_id": "uuid1",
    "accounts_permission": 1
  },
  {
    "card_id": "uuid2",
    "account_ids": ["account_id1", "account_id2", ...],
    "accounts_permission": 0
  },
  {
    "card_id": "uuid3",
    "account_ids": ["account_id1", "account_id2", ...],
    "accounts_permission": 0
  }
]

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""

******************************* API PHÂN QUYỀN CHO CARD DASHBOARD  ************
*                                                                         *
* version: 1.0.0                                                          *
*******************************************************************************
"""
@api {DELETE} /api/v2.1/dashboards/<dashboard_id> Xóa dashboard của user
@apiDescription Xóa dashboard của user
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName DeleteDashboardsUser

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""

******************************* API UPDATE DASHBOARD LIST USER ************
*                                                                         *
* version: 1.0.0                                                          *
***************************************************************************
"""
@api {POST} /api/v2.1/dashboards/user/actions/update_display_config Cập nhập danh sách dashboard của user 
@apiDescription Cập nhập danh sách dashboard của user 
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName DashboardsUserUpdate

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} [account_id]  ID của account <code> nếu không truyền lên thì update của tài khoản đang gọi api </code>
@apiParam   (Body:) {Array} list_dashboard  Danh sách các dashboard id của account
@apiParam   (Body:) {String} dashboard_selected  danh sách các user có quyền sử dụng dashboardcard

@apiParamExample {json} Body example
{
  "account_id": "uuid"
  "list_dashboard": ["uuid1", ...],
  "dashboard_selected": "uuid1"
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""
"""
@api {GET} /api/v2.1/dashboard-cards/all  Lấy danh sách tất cả card dashboard 
@apiDescription Lấy danh sách tất cả không phân trang  
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName GetAllCardDashboard

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
      {
        "id": "53610ff3-e850-4787-a862-c8c195974609",
        "key": "mo-main_dashboard-valid-voucher",
        "status": 1,
        "is_base": 1,
      },
        ...
    ]
}

"""

"""
@api {GET} /api/v2.1/dashboard-cards/assign/merchant  Lấy danh sách card đã assign merchant  
@apiDescription Lấy danh sách tất cả không phân trang  
@apiVersion 1.0.0
@apiGroup DashboardCard
@apiName GetCardAssignMerchant

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header

@apiParam   (Query:)    {String}  merchant_id    id merchant
@apiSuccessExample {Body} Response : HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
    {
        "merchant_id": "59e2eaf3-43e4-4154-a7d2-6c2d4c74d667",
        "assign_id": "88deffa9-ba36-43fc-ace3-18aaa41454fb",,
        "card_id": "53610ff3-e850-4787-a862-c8c195974609",
        "key": "mo-main_dashboard-valid-voucher",
    },
    ...
  ]
}

"""

#_________________________CREATE DASHBOARD CATEGORY_______________________________
"""
@api {POST} /api/v2.1/dashboard-category  Thêm loại bảng thống kê  
@apiDescription Thêm loại bảng thống kê 
@apiVersion 1.0.0
@apiGroup Dashboard-Category
@apiName CreateDashboardCategory

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header

@apiParam   (Body:)    {String}  name           Tên loại bảng thống kê

@apiSuccessExample {Body} Response : HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "account_id": "uqwnaseaseaesatest123132",
        "id": "017c4794-76fe-11ee-aa3c-2793eaab7e36",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name": "test_test",
        "selected": 1,
        "type": "kpi"
    },
    "lang": "vi",
    "message": "request thành công."
}

"""

#_________________________UPDATE DASHBOARD CATEGORY_______________________________
"""
@api {PATCH} /api/v2.1/dashboard-category/<dashboard_id>  Cập nhập bảng thống kê  
@apiDescription Cập nhập bảng thống kê 
@apiVersion 1.0.0
@apiGroup Dashboard-Category
@apiName UpdateDashboardCategory

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header

@apiParam   (Body:)    {String}  [name]           Tên loại bảng thống kê
@apiParam   (Body:)    {Array}   [data]           Danh sách các dữ liệu   TRuyền lên tất cả để ghi đè hoặc thay đổi, nếu để trống sẽ xóa toàn bộ card
@apiParam   (Body:)    {Object}   [filter]         Trường tìm kiếm

@apiSuccessExample {Body} Response : HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "account_id": "a573fd5a-76ff-11ee-aa3c-2793eaab7e36",
        "data": [
            {
                "data": {
                    "test": "updatetest"
                },
                "key_card": "updatetest"
            },
            {
                "data": {
                    "test": "updatetest2"
                },
                "key_card": "updatetest2"
            }
        ],
        "id": "a573fd5a-76ff-11ee-aa3c-2793eaab7e36",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name": "test1",
        "selected": 1,
        "filter": {}
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ------------------------------- UPDATE DASHBOARD CATEGORY DEFAULT ---------------------------
"""
@api {PATCH} /api/v2.1/dashboard-category/default Thay đổi dashboard category mặc định
@apiDescription Thay đổi dashboard category mặc định
@apiVersion 1.0.0
@apiGroup Dashboard-Category
@apiName UpdateDashboardCategoryDefault

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header

@apiParam   (Body:)  {String}                   [id_dashboard] ID dashboard cần cập nhập mặc định 
@apiParam   (Body:)  {Array}                    [id_delete]  Danh sách ID cần xóa bỏ 

@apiSuccessExample {Body} Response : HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

#-------------------------------- GET INFO DASHBOARD CATEGORY DEFAULT ----------------------------
"""
@api {GET} /api/v2.1/dashboard-category/default Thông tin của dashboard category mặc định
@apiDescription Thông tin của dashboard category mặc định
@apiVersion 1.0.0
@apiGroup Dashboard-Category
@apiName GetInfoDashBoardCategoryDefault

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header


@apiSuccessExample {Body} Response : HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "data": [
            {
                "data": {
                    "test": "updatetest"
                },
                "key_card": "updatetest"
            },
            {
                "data": {
                    "test": "updatetest2"
                },
                "key_card": "updatetest2"
            }
        ],
        "id": "a9d83a32-76ff-11ee-aa3c-2793eaab7e36",
        "name": "ccc",
        "filter": {},
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

#_________________________DELETE DASHBOARD CATEGORY_______________________________
"""
@api {DELETE} /api/v2.1/dashboard-category/<dashboard_id>  Xóa bảng thống kê 
@apiDescription Xóa bảng thống kê
@apiVersion 1.0.0
@apiGroup Dashboard-Category
@apiName DeleteDashboardCategory

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header


@apiSuccessExample {Body} Response : HTTP/1.1 200 OK
{
{
  "code": 200,
  "message": "request thành công.",
  }
}

"""

#______________________________  LIST DASHBOARD CATEGORY ___________________________________
"""
@api {GET} /api/v2.1/dashboard-category  Danh sách bảng thống kê 
@apiDescription Danh sách bảng thống kê
@apiVersion 1.0.0
@apiGroup Dashboard-Category
@apiName GetDashboardCategory

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header


@apiSuccessExample {Body} Response : HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "account_id": "uqwnaseaseaesatest123132",
            "id": "a573fd5a-76ff-11ee-aa3c-2793eaab7e36",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "adm_test1",
            "selected": 0,
            "type": "kpi"
        },
        {
            "account_id": "uqwnaseaseaesatest123132",
            "id": "a9d83a32-76ff-11ee-aa3c-2793eaab7e36",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "test2",
            "selected": 1,
            "type": "kpi"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}

"""

#------------------------------- GET LIST CARD BY CATEGORY ID ---------------------------
"""
@api {GET} /api/v2.1/dashboard-category/<dashboard_id>/card-assign  Danh sách Card Assign theo category_id
@apiDescription Danh sách Card Assign theo category_id
@apiVersion 1.0.0
@apiGroup Dashboard-Category
@apiName ListCardAssignCategory

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header


@apiSuccessExample {Body} Response : HTTP/1.1 200 OK

{
    "code": 200,
    "data": [
        {
            "account_id": "uqwnaseaseaesatest123132",
            "category_id": "a9d83a32-76ff-11ee-aa3c-2793eaab7e36",
            "data": {
                "test": "updatetest"
            },
            "key_card": "updatetest",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924"
        },
        {
            "account_id": "uqwnaseaseaesatest123132",
            "category_id": "a9d83a32-76ff-11ee-aa3c-2793eaab7e36",
            "data": {
                "test": "updatetest2"
            },
            "key_card": "updatetest2",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
# -----------------------------------------------------------------------------------------
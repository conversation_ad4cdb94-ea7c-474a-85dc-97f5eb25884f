********************************* API LẤY DANH SÁCH CẤU HÌNH PUBLIC THEO MERCHANT *********************************
* version: 1.0.0                                                                                                  *
*******************************************************************************************************************
"""
@api {get} /api/v2.1/merchants/<merchant_id>/public-configs Lấy danh sách cấu hình public theo merchant
@apiDescription API Lấy danh sách cấu hình theo merchant
@apiVersion 1.1.0
@apiGroup Config
@apiName ListPublicConfig

@apiUse 500
@apiUse lang

@apiSuccess (data) {string} jwt_algorithm Thuật toán giải mã JWT
@apiSuccess (data) {string} jwt_secret_key secret key giải mã
@apiSuccess (data) {string} p_t Partner key
@apiSuccess (data) {string} ads_host Host Ads automation
@apiSuccess (data) {string} au_host Host audience
@apiSuccess (data) {string} loyalty_host Host loyalty
@apiSuccess (data) {string} mkt_host Host marketing
@apiSuccess (data) {string} profiling_host Host profiling
@apiSuccess (data) {string} social_host Host social

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "ads_host": "https://dev.mobio.vn/",
    "au_host": "https://dev.mobio.vn/",
    "jwt_algorithm": "HS256",
    "jwt_secret_key": "mobioadmin",
    "loyalty_host": "https://api-p.dev.mobio.vn/",
    "mkt_host": "https://loind.mobio.vn/",
    "p_t": "f38b67fa-22f3-4680-9d01-c36b23bd0cad",
    "profiling_host": "https://api-p.dev.mobio.vn/",
    "social_host": "https://dev.mobio.vn/"
  },
  "message": "request thành công."
}
"""



********************************* API LẤY DANH SÁCH CẤU HÌNH THEO MERCHANT *********************************
* version: 1.1.0                                                                                           *
************************************************************************************************************
"""
@api {get} /api/v2.1/merchants/<merchant_id>/configs Lấy danh sách cấu hình theo merchant
@apiDescription API Lấy danh sách cấu hình theo merchant
@apiVersion 1.1.0
@apiGroup Config
@apiName ListConfig

@apiUse 500
@apiUse lang

@apiSuccess (data) {string} jwt_algorithm Thuật toán giải mã JWT
@apiSuccess (data) {string} jwt_secret_key secret key giải mã
@apiSuccess (data) {string} p_t Partner key
@apiSuccess (data) {string} ads_host Host Ads automation
@apiSuccess (data) {string} au_host Host audience
@apiSuccess (data) {string} loyalty_host Host loyalty
@apiSuccess (data) {string} mkt_host Host marketing
@apiSuccess (data) {string} profiling_host Host profiling
@apiSuccess (data) {string} social_host Host social
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "ads_host": "https://dev.mobio.vn/",
    "au_host": "https://dev.mobio.vn/",
    "jwt_algorithm": "HS256",
    "jwt_secret_key": "mobioadmin",
    "loyalty_host": "https://api-p.dev.mobio.vn/",
    "mkt_host": "https://loind.mobio.vn/",
    "p_t": "f38b67fa-22f3-4680-9d01-c36b23bd0cad",
    "profiling_host": "https://api-p.dev.mobio.vn/",
    "social_host": "https://dev.mobio.vn/"
  },
  "message": "request thành công."
}
"""


********************************* API LẤY DANH SÁCH CẤU HÌNH FIELD MÃ HÓA **********************************
* version: 2.1.1                                                                                           *
* version: 2.1.0                                                                                           *
************************************************************************************************************
"""
@api {get} /api/v2.1/merchants/config  Lấy danh sách cấu hình field mã hóa
@apiDescription API Lấy danh sách cấu hình field mã hóa
@apiVersion 2.1.1
@apiGroup Config
@apiName ListConfigField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)  {String}  [merchant_id]   merchant_id của merchant lấy config 
@apiParam   (Query:)  {String}  [account_id]   id của account lấy config 

@apiSuccess (data) {String} field tên field cần ẩn
@apiSuccess (data) {String} type kiểu ẩn của field 
@apiSuccess (data) {Boolean} [profile_owner_can_view] Cấu hình cho phép profile owner có thể view thông tin mã hoá.
@apiSuccess (data) {Object} [format_logic] Cấu hình thuật toán mã hoá thông tin bao gồm hướng (start, end) và số lượng ký tự cần giữ. Ví dụ:<br />
<li><code>{"end": 4}</code>: Giữ lại 4 ký tự ở cuối</li>
<li><code>{"start": 3, "end": 3}</code>: Giữ lại 2 ký tự ở đầu và 2 ký tự ở cuối</li><br />
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "field": "email",
      "type": "hide_by_merchant",
      "module": null,
      "format_logic": {"end": 4}
    },
    {
      "field": "phone_number",
      "type": "hide_by_merchant",
      "profile_owner_can_view": true,
      "module": null,
      "format_logic": {"start": 3, "end": 3}
    },
  ],
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "account_id": "75be1743-940e-41e6-846f-1453bcce7a86",
  "message": "request thành công."
}
"""
*****************
"""
@api {get} /api/v2.1/merchants/config  Lấy danh sách cấu hình field mã hóa
@apiDescription API Lấy danh sách cấu hình field mã hóa
@apiVersion 2.1.0
@apiGroup Config
@apiName ListConfigField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)  {String}  [merchant_id]   merchant_id của merchant lấy config 
@apiParam   (Query:)  {String}  [account_id]   id của account lấy config 



@apiSuccess (data) {String} field tên field cần ẩn
@apiSuccess (data) {String} type kiểu ẩn của field 
@apiSuccess (data) {String} fields điều kiện của field bị ẩn 
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "field": "email",
      "type": "hide_by_merchant"
    },
    {
      "field": "phone_number",
      "type": "hide_by_fields",
      "fields": [
        {
          "key": "created_account_type",
          "values": [
            1,
            2,
            3
          ]
        },
        {
          "key": "source",
          "values": [
            "DC"
          ]
        }
      ]
    },
    {
      "field": "voucher_code",
      "type": "hide_by_merchant"
    }
  ],
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "account_id": "account_id",
  "message": "request thành công."
}
"""

********************************* API LƯU CẤU HÌNH MÃ HÓA CHO CÁC FIELD ************************************
* version: 2.1.1                                                                                           *
* version: 2.1.0                                                                                           *
************************************************************************************************************
"""
@api {post} /api/v2.1/merchants/actions/config  Lưu cấu hình mã hóa các field 
@apiDescription API Lưu cấu hình mã hóa các field 
@apiVersion 2.1.1
@apiGroup Config
@apiName UpdateConfigField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Body:)  {String}  [merchant_id]   Cấu hình mã hoá theo merchant (<code>merchant_id</code>). Nếu không truyền cả merchant_id và account_id thì sẽ cấu hình cho x-merchant_id.
@apiParam  (Body:)  {Array}  data  Thông tin cấu hình các trường mã hoá.
@apiParam  (Body:)  {String}  data..field  Tên field cần mã hoá.
@apiParam  (Body:)  {String=hide_by_merchant}  data..type  Kiểu mã hoá.
@apiParam  (Body:)  {String}  data..module  Tên module.
@apiParam  (Body:)  {Object}  data..others  Cấu hình bổ sung.

@apiParamExample {json} Body
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "data": [
    {
      "field": "email",
      "type": "hide_by_merchant",
      "module": null,
      "others": {
        "format_logic": {"end": 4}
      }
    },
    {
      "field": "phone_number",
      "type": "hide_by_merchant",
      "module": null,
      "others": {
        "profile_owner_can_view": true,
        "format_logic": {"start": 3, "end": 3}
      }
    }
  ]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "field": "email",
      "type": "hide_by_merchant",
      "module": null,
      "format_logic": {"end": 4}
    },
    {
      "field": "phone_number",
      "type": "hide_by_merchant",
      "profile_owner_can_view": true,
      "module": null,
      "format_logic": {"start": 3, "end": 3}
    },
  ],
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "account_id": "account_id",
  "message": "request thành công."
}
"""
*****************
"""
@api {post} /api/v2.1/merchants/actions/config  Lưu cấu hình mã hóa các field 
@apiDescription API Lưu cấu hình mã hóa các field 
@apiVersion 2.1.0
@apiGroup Config
@apiName UpdateConfigField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  {String}  [merchant_id]   merchant_id của merchant lấy config <code> nếu không truyền cả merchant_id và account_id thì sẽ cấu hình cho x-merchant_id</code>
@apiParam  {String}  [account_id]   id của account lấy config <code> nếu không tryền lên thì sẽ cấu hình cho merchant_id</code>
@apiParam  {String}   ids Danh sách id của account 

@apiParamExample {json} Body
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "account_id": "aed31923-c365-4266-aeb3-8fafab20ef5c'",
  "data": [
    {
      "field": "email",
      "type": "hide_by_merchant"
    },
    {
      "field": "phone_number",
      "type": "hide_by_fields",
      "fields": [
        {
          "key": "created_account_type",
          "values": [
            1,
            2,
            3
          ]
        },
        {
          "key": "source",
          "values": [
            "DC"
          ]
        }
      ]
    },
    {
      "field": "voucher_code",
      "type": "hide_by_merchant"
    }
  ]
}


@apiSuccess (data) {String} field tên field cần ẩn
@apiSuccess (data) {String} type kiểu ẩn của field 
@apiSuccess (data) {String} fields điều kiện của field bị ẩn 
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "field": "email",
      "type": "hide_by_merchant"
    },
    {
      "field": "phone_number",
      "type": "hide_by_fields",
      "fields": [
        {
          "key": "created_account_type",
          "values": [
            1,
            2,
            3
          ]
        },
        {
          "key": "source",
          "values": [
            "DC"
          ]
        }
      ]
    },
    {
      "field": "voucher_code",
      "type": "hide_by_merchant"
    }
  ],
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "account_id": "account_id",
  "message": "request thành công."
}
"""
********************************* API GIẢI MÃ   **********************************
* version: 2.1.0                                                                      *
***************************************************************************************
"""
@api {post} /api/v2.1/merchants/actions/decrypt  API giải mã thông tin mã hóa 
@apiDescription API giải mã thông tin mã hóa 
@apiVersion 2.1.0
@apiGroup Config
@apiName decrypt

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam   (Body:)  {array}  encrypt_data   danh sách các data mã hóa 

@apiSuccess (data) {array} data danh sách  data mã hóa 
@apiSuccess (data) {string} data.encrypt_data danh sách  data mã hóa 
@apiSuccess (data) {string} data.decrypt_data danh sách  data sau khi được giải mã 

@apiParamExample {json} Body
{
  "encrypt_data":["awdkaowdkosaockaiwdamwid","iaokwawdkmwokcxac","ixjciawjdnawdawdoascas"]
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "encrypt_data":"awdkaowdkosaockaiwdamwid",
      "decrypt_data":"<EMAIL>",
    },
    {
      "encrypt_data":"iaokwawdkmwokcxac",
      "decrypt_data": "0981765718",
    },
    {
      "encrypt_data":"ixjciawjdnawdawdoascas",
      "decrypt_data":"0987615728"
    }
  ]
  "message": "request thành công."
}
"""

********************************* API LẤY CẤU HÌNH QUYỀN TÙY CHỈNH CỦA MERCHANT ****************************
* version: 2.1.0                                                                                           *
************************************************************************************************************
"""
@api {get} /api/v2.1/merchants/dynamic_config  Lấy danh sách cấu hình tùy biến của merchant
@apiDescription API Lấy danh sách cấu hình tùy biến của merchant
@apiVersion 2.1.0
@apiGroup Config
@apiName ListDynamicConfigMerchant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)  {String}  title_config   tên của config kiểm tra xem merchant có quyền sử dụng hay không <code>Truyền lên nhiều config thì cách nhau bởi dấu , ex: 'key1,key2'</code>



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data":[
    {
      'permission': True,
      'title_config': 'social_chat_bot'
    }
  ]
  "message": "request thành công."
}
"""

"""
@api {get} /api/v2.1/merchants/<merchant_id>/config/detail Lấy thông tin cấu hình theo merchant mới
@apiDescription API Lấy thông tin cấu hình theo merchant mới
@apiVersion 1.1.0
@apiGroup Config
@apiName GetMerchantConfig

@apiUse 500
@apiUse lang

@apiSuccess (data) {string} jwt_algorithm Thuật toán giải mã JWT
@apiSuccess (data) {string} jwt_secret_key secret key giải mã
@apiSuccess (data) {string} p_t Partner key
@apiSuccess (data) {string} ads_host Host Ads automation
@apiSuccess (data) {string} au_host Host audience
@apiSuccess (data) {string} loyalty_host Host loyalty
@apiSuccess (data) {string} mkt_host Host marketing
@apiSuccess (data) {string} profiling_host Host profiling
@apiSuccess (data) {string} social_host Host social
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
	"code": 200,
	"data": {
		"admin-app-api-deployment": "https://api-test1.mobio.vn/",
		"admin-app-api-service-host": "http://admin-app-api-service/",
		"admin-app-webhook-service-host": "http://admin-app-webhook-service/",
		"admin_host": "https://api-test1.mobio.vn/",
		"ads-app-api-service-host": "http://ads-app-api-service/",
		"ads_host": "https://api-test1.mobio.vn/",
		"api-management-app-api-service-host": "http://api-management-app-api-service/",
		"au_host": "https://api-test1.mobio.vn/",
		"not_active_in_days": -1,
	},
	"lang": "vi",
	"message": "request th\u00e0nh c\u00f4ng."
}
"""

"""
@api {get} /api/v2.1/merchants/config/session-account Lấy thông tin cấu hình phiên của user 
@apiDescription API Lấy thông tin cấu hình phiên của user 
@apiVersion 1.1.0
@apiGroup Config
@apiName GetConfigSession

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "session_login": {
      "value_web": 1, "unit_web": "day",  // minute, hour, day 
      "value_mobile": 8, "unit_mobile": "day"
    },
    "session_inactive": {
      "status_web": "off", "value_web": 30, "unit_web": "minute",
      "status_mobile": "off", "value_mobile": 30, "unit_mobile": "minute",
    },      
  },
  "lang": "vi",
  "message": "request th\u00e0nh c\u00f4ng."
}
"""

"""
@api {post} /api/v2.1/merchants/config/session-account cập nhật cấu hình phiên của user 
@apiDescription cập nhật thông tin cấu hình phiên của user 
@apiVersion 1.1.0
@apiGroup Config
@apiName UpdateConfigSession

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParamExample {json} Body
{
  "action_account": "logout", // sau khi cập nhật có hành động nào cho account: logout - đăng xuất tất cả user, not_logout - giữ phiên hiện tại  
  "session_login": {
    "value_web": 1, "unit_web": "day",  // minute, hour, day 
    "value_mobile": 8, "unit_mobile": "day"
  },
  "session_inactive": {
    "status_web": "on", "value_web": 30, "unit_web": "minute",
    "status_mobile": "on", "value_mobile": 30, "unit_mobile": "minute",
  },  
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request th\u00e0nh c\u00f4ng."
}
"""



"""
@api {get} /api/v2.1/merchants/config/session-select-time Lấy danh sách khoảng thời gian cấu hình phiên  
@apiDescription dùng để hiển thị danh sách thời gian lựa chọn 
@apiVersion 1.1.0
@apiGroup Config
@apiName GetConfigSessionSelectTime 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "session_login": [
      {
        "value": 10,
        "unit": "minute",
      },
      {
        "value": 2,
        "unit": "hour",
      },
      {
        "value": 1,
        "unit": "day",
      },
    ],
    "session_inactive": [
      {
        "value": 10,
        "unit": "minute",
      },
      {
        "value": 20,
        "unit": "minute",
      },
      
    ]      
  },
  "lang": "vi",
  "message": "request th\u00e0nh c\u00f4ng."
}
"""
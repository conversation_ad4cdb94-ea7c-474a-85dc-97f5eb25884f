#!/usr/bin/python
# -*- coding: utf8 -*-

################### Đăng nhập vào hệ thống <PERSON>bio #########################
# version: 1.0.1                                                         #
##########################################################################

"""
@api {POST} /api/v2.1/login Đăng nhập vào hệ thống Mobio
@apiDescription Đăng nhập vào hệ thống Mobio
@apiGroup AdditionAction
@apiVersion 1.0.0
@apiName LoginAction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Body:)    {String}    token Token từ hệ thống ngo<PERSON><PERSON> g<PERSON><PERSON> sang
@apiParam   (Body:)    {String}    source <PERSON><PERSON><PERSON><PERSON> g<PERSON> (ví dụ "icheck")


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "jwt": "Mobio jwt token"
}
"""

################### Tạo tenant và user ###################################
# version: 1.0.1                                                         #
##########################################################################

"""
@api {POST} /api/v2.1/merchants/actions/register Tạo tenant và user
@apiDescription Tạo tenant và user
@apiGroup AdditionAction
@apiVersion 1.0.0
@apiName CreateTenantUserAction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse json_header

@apiParam   (Body:)   {Object}      tenant              Thông tin tenant cần tạo
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>string</td>
      <td>
        Định danh của tenant
      </td>
    </tr>
    <tr>
      <td>name</td>
      <td>string</td>
      <td>
        Tên của tenant
      </td>
    </tr>
    <tr>
      <td>code<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>
        Code của tenant
      </td>
    </tr>
    <tr>
      <td>email<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>
        Email owner của tenant
      </td>
    </tr>
    <tr>
      <td>introducer<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>
        Tên người giới thiệu
      </td>
    </tr>
  </tbody>
</table>

@apiParam   (Body:)   {Object}      [user]              Thông tin user cần tạo
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>string</td>
      <td>
        Định danh của user
      </td>
    </tr>
    <tr>
      <td>fullname</td>
      <td>string</td>
      <td>
        Tên của user
      </td>
    </tr>
    <tr>
      <td>username</td>
      <td>string</td>
      <td>
        Username của user (không gồm suffix @CODE)
      </td>
    </tr>
    <tr>
      <td>email<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>
        Email của user
      </td>
    </tr>
    <tr>
      <td>phone<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>
        Số điện thoại của user
      </td>
    </tr>
  </tbody>
</table>


@apiParam   (Body:)   {object}      hosts       [optional]      Danh sách hosts các module theo dạng key-value // Nếu không truyền lên thì gán theo giá trị mặc định mặc định
@apiParam   (Body:)   {string}      source            Nguồn tạo: nếu nguồn là "mobio" thì tenant_id và user_id được sử dụng luôn để làm định danh trong hệ thống Mobio
@apiParam   (Body:)   {string}      callback          Callback trả thông tin sau khi tạo tenant và User


@apiParamExample {json} Body example
{
    "tenant": {
        "id": "2ddab483-d276-4f80-94d9-89fb07e90203", // ID doanh nghiệp
        "name": "Vin Group",
        "code": "VINGROUP" // Optional, nếu không có thông tin thì tự sinh code theo name
        "email": "<EMAIL>" // Optional, email của owner
        "introducer": "Pham Van Tien" // optional, tên người giới thiệu
    }
    "user": { // Nếu không có thông tin Object "user" thì mặc định vẫn tạo tài khoản Owner
        "id": "dd2ab483-d276-4f80-94d9-89fb07e90302",
        "fullname": "Nguyen Van Trong",
        "username": "trongnv",
        "email": "<EMAIL>" // optional
        "phone": "0912343478", // optional
    },
    "hosts": {
        "ck_tenant_domain": "https://t1.mobio.vn/"
        "ads_host": "https://api-test1.mobio.vn/"
        "au_host": "https://api-test1.mobio.vn/"
        "callcenter_host": "https://api-test1.mobio.vn/"
        "chattool_host": "https://api-test1.mobio.vn/"
        "crm_host": "https://api-test1.mobio.vn/"
        "emk_host": "https://api-test1.mobio.vn/"
        "loyalty_host": "https://api-p.test1.mobio.vn/"
        "mkt_host": "https://api-test1.mobio.vn/"
        "nm_host": "https://api-test1.mobio.vn/"
        "profiling_host": "https://api-p.test1.mobio.vn/"
        "sale_host": "https://api-test1.mobio.vn/"
        "social_host": "https://api-test1.mobio.vn/"
        "ticket_host": "https://api-test1.mobio.vn/"
    }
    "source": "mobio", // Nguồn tạo, nếu nguồn tạo là "mobio" thì tenant_id và user_id dùng bằng id phía request gửi lên
    "callback": "https://api.mobio.vn/callback..." // URL API callback để trả thông tin tenant và user sau khi tạo thành công
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
	"code": 200,
	"message": "Yêu cầu tạo tenant và user đã được tiếp nhận. Sẽ có thông báo ngay sau khi thực hiện xong!"
}
"""

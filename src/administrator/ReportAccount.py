
"""
@api {get} /api/v2.1/report/account/total tổng số account hiện tại  
@apiDescription tổng số tài khoản của merchant đang active 
@apiVersion 1.0.0
@apiGroup ReportAccount 
@apiName DashboardTotalAccount  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": 2300,
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""



"""
@api {post} /api/v2.1/report/account/create số lượng tài khoản tạo theo ngày     
@apiDescription số lượng tài khoản tạo 
@apiVersion 1.0.0
@apiGroup ReportAccount
@apiName DashboardAccountCreate  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {String}      from_time  Thời gian lọc từ ngày (ICT: 2024-03-01) 
@apiParam      (Body:)     {String}      to_time  Thời gian lọc đến ngày  (ICT: 2024-03-25)


@apiParamExample {json} Body
{
  "from_time":"2024-03-01",
  "to_time": "2024-03-25",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "report": [
      {
        "report_date": "2024-03-01",
        "account_create": 20 
      },
      {
        "report_date": "2024-03-11",
        "account_create": 2 
      },
    ],
    "total_before": 15,
    "total_after": 22,
  }
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {post} /api/v2.1/report/account/login-rate tỉ lệ đăng nhập      
@apiDescription biểu đồ tròn 
@apiVersion 1.0.0
@apiGroup ReportAccount
@apiName DashboardAccountLoginRate 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {String}      from_time  Thời gian lọc từ ngày (ICT: 2024-03-01) 
@apiParam      (Body:)     {String}      to_time  Thời gian lọc đến ngày  (ICT: 2024-03-25)


@apiParamExample {json} Body
{
  "from_time":"2024-03-01",
  "to_time": "2024-03-25",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "not_login_before": 15,
    "not_login_after": 22,
    "login_before": 15,
    "login_after": 22,
  }
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {post} /api/v2.1/report/account/login-by-day số lượng login theo ngày     
@apiDescription biểu đồ theo ngày
@apiVersion 1.0.0
@apiGroup ReportAccount
@apiName DashboardAccountLoginByDay   

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {String}      from_time  Thời gian lọc từ ngày (ICT: 2024-03-01) 
@apiParam      (Body:)     {String}      to_time  Thời gian lọc đến ngày  (ICT: 2024-03-25)


@apiParamExample {json} Body
{
  "from_time":"2024-03-01",
  "to_time": "2024-03-25",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
      {
        "report_date": "2024-03-01",
        "total_login": 20,
        "total_not_login": 2,
      },
      {
        "report_date": "2024-03-11",
        "total_login": 20,
        "total_not_login": 10,
      },
    ],
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {post} /api/v2.1/report/account/times-login-logout hiệu suất đăng nhập từng tài khoản 
@apiDescription danh sách tài khoản theo bộ lọc, trả về thông tin tổng số lượt đăng nhập đăng xuất 
@apiVersion 1.0.0
@apiGroup ReportAccount
@apiName DashboardAccountTimesLogin   

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {String}      from_time  Thời gian lọc từ ngày (ICT: 2024-03-01) 
@apiParam      (Body:)     {String}      to_time  Thời gian lọc đến ngày  (ICT: 2024-03-25)
@apiParam      (Body:)     {Array}      [filter_team]  danh sách kiểu lọc theo: have_team - có thông tin team, not_have_team - ko có thông tin team
@apiParam      (Body:)     {Array}      [team_ids]  danh sách team id theo bộ lọc có thông tin team 
@apiParam      (Body:)     {Array}      [account_ids]  danh sách account id theo bộ lọc 


@apiParamExample {json} Body
{
  "from_time":"2024-03-01",
  "to_time": "2024-03-25",
  "filter_team": ["have_team", "not_have_team"],
  "account_ids": ["7fc0a33c-baf5-11e7-a7c2-0242ac180003"]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
      {
        "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "username": "admin@pingcomshop",
        "fullname": "Nguyễn Văn A",
        "staff_code": "",
        "total_login": 0,   // tổng số lượt đăng nhập 
        "total_logout": 0,   // tổng số lượt đăng xuất 
        "rate_login": 0,    // tổng số login / số ngày 
        "rate_logout": 0,  // tổng số logout / số ngày 
      },
      
    ],
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {post} /api/v2.1/report/account/export/times-login-logout xuất file hiệu suất đăng nhập từng tài khoản 
@apiDescription xuất theo bộ lọc
@apiVersion 1.0.0
@apiGroup ReportAccount
@apiName DashboardAccountExportTimesLogin   

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {String}      from_time  Thời gian lọc từ ngày (ICT: 2024-03-01) 
@apiParam      (Body:)     {String}      to_time  Thời gian lọc đến ngày  (ICT: 2024-03-25)
@apiParam      (Body:)     {Array}      [filter_team]  danh sách kiểu lọc theo: have_team - có thông tin team, not_have_team - ko có thông tin team
@apiParam      (Body:)     {Array}      [team_ids]  danh sách team id theo bộ lọc có thông tin team  
@apiParam      (Body:)     {Array}      [account_ids]  danh sách account id theo bộ lọc 

@apiParamExample {json} Body
{
  "from_time":"2024-03-01",
  "to_time": "2024-03-25",
  "filter_team": ["have_team", "not_have_team"],
  "account_ids": ["7fc0a33c-baf5-11e7-a7c2-0242ac180003"]
}

@apiSuccess     {json} data   json agent detail.

@apiSuccess {String}            data.link_download              Link download
@apiSuccess {String}            data.type_export                Kiểu export, <code>0: ko có dữ liệu, 1: gửi link_download qua api, 2: gửi link_download qua email</code>


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    'link_download': "https://...",
    'type_export': 1
  },
  "lang": "vi",
  "message": "request thành công.",
}
"""

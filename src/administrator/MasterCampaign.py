*********************************** Update MasterCampaign ***********************************
* version: 1.0.1                                                                            *
* version: 1.0.0                                                                            *
*********************************************************************************************
"""
@api {patch} /api/v2.1/master-campaigns/<master_campaign_id> Update master campaign
@apiDescription API cập nhật master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.1
@apiName UpdateMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Resources:)  {String}    master_campaign_id  Định danh master campaign cần xem chi tiết.

@apiParam   (Body:)   {String}  name  Tên master campaign
@apiParam   (Body:)   {String}  [describe]  Mô tả 
@apiParam   (Body:)   {StringArray}  [keywords]  Danh sách keyword được gắn với master campaign
@apiParam   (Body:)   {DateTime}  [start] Thời gian bắt đầu của master campaign. Ví dụ: 2017-08-07T04:02:28.002Z
@apiParam   (Body:)   {DateTime}  [end] Thời gian kết thúc của master campaign. Ví dụ: 2018-08-07T04:02:28.002Z
@apiParam   (Body:)   {StringArray}   [staffs]  Danh sách id của nhân viên được phân quyền quản lý master campaign này.<br />
<code>Note:</code> Danh sách dữ liệu mới sẽ được thay thế toàn bộ danh sách đã phân quyền trước đó.
@apiParam   (Body:)   {String}  [use_for]  master campaign được dùng cho moduls nào
        <br/> Ví dụ: <code>marketing,journey-builder</code>

@apiParamExample  {json}  Body:
{
  "code": "ABC",
  "name": "",
  "describe": "",
  "sub_brand_id": "bdae6f6a-7f40-4669-b2d5-5a1011e3f188",
  "keywords": ["abc"],
  "start": "2017-08-07T04:02:28.002Z",
  "end": "2018-08-07T04:02:28.002Z",
  "staffs": ["0efe86d3-7eeb-40b4-81e9-9bdb33e8733c", "6bb848d6-3429-4a48-95c2-a86308d9929b"],
  "use_for": "marketing"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
  "code": 200,
  "message": "Request thành công"
}
"""
************************
"""
@api {patch} /api/v2.1/master-campaigns/<master_campaign_id> Update master campaign
@apiDescription API cập nhật master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.0
@apiName UpdateMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Resources:)  {String}    master_campaign_id  Định danh master campaign cần xem chi tiết.

@apiParam   (Body:)   {String}  name  Tên master campaign
@apiParam   (Body:)   {StringArray}  [keywords]  Danh sách keyword được gắn với master campaign
@apiParam   (Body:)   {DateTime}  [start] Thời gian bắt đầu của master campaign. Ví dụ: 2017-08-07T04:02:28.002Z
@apiParam   (Body:)   {DateTime}  [end] Thời gian kết thúc của master campaign. Ví dụ: 2018-08-07T04:02:28.002Z
@apiParam   (Body:)   {StringArray}   [staffs]  Danh sách id của nhân viên được phân quyền quản lý master campaign này.<br />
<code>Note:</code> Danh sách dữ liệu mới sẽ được thay thế toàn bộ danh sách đã phân quyền trước đó.

@apiParamExample  {json}  Body:
{
  "name": "",
  "keywords": ["abc"],
  "start": "2017-08-07T04:02:28.002Z",
  "end": "2018-08-07T04:02:28.002Z",
  "staffs": ["0efe86d3-7eeb-40b4-81e9-9bdb33e8733c", "6bb848d6-3429-4a48-95c2-a86308d9929b"]
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
  "code": 200,
  "message": "Request thành công"
}
"""

*********************************** Get Detai MasterCampaign *****************************
* version: 1.0.1                                                                         *
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /api/v2.1/master-campaigns/<master_campaign_id> Lấy chi tiết master campaign
@apiDescription API lấy thông tin chi tiết master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.1
@apiName DetailMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Resources:)  {String}    master_campaign_id  Định danh master campaign cần xem chi tiết.

@apiSuccess   {MasterCampaign[]}  data  Danh sách master campaign phù hợp tiêu chí
@apiSuccess   (MasterCampaign)  {String}  id  Id của master campaign
@apiSuccess   (MasterCampaign)  {String}  code  Mã định danh của master campaign
@apiSuccess   (MasterCampaign)  {String}  name  Tên của master campaign
@apiSuccess   (MasterCampaign)  {String}  [describe]  Mô tả 
@apiSuccess   (MasterCampaign)  {StringArray}  keywords  Danh sách từ khoá nhận diện
@apiSuccess   (MasterCampaign)  {String}  keyword  <code>[DEPRECATED]</code>Danh sách từ khoá nhận diện. (Sử dụng <code>keywords</code> thay thế)
@apiSuccess   (MasterCampaign)  {DateTime}  start  Id của master campaign
@apiSuccess   (MasterCampaign)  {DateTime}  end  Id của master campaign
@apiSuccess   (MasterCampaign)  {Staff[]}  [staffs]  Danh sách staff được phân công quản trị master campaign
@apiSuccess   (MasterCampaign)  {String}  [use_for]  Master campaign được dùng cho moduls nào.

@apiSuccess   (Staff)  {String}  id   Id của nhân viên
@apiSuccess   (Staff)  {String}  username   Username của nhân viên
@apiSuccess   (Staff)  {String}  fullname   Tên của nhân viên
@apiSuccess   (Staff)  {String}  avatar   Link ảnh đại diện
@apiSuccess   (Staff)  {Number=1-admin,2-normal}  is_admin   Đánh dấu tài khoản quản trị
@apiSuccess   (Staff)   {StringArray=detail, insight}   permissions   Danh sách quyền tương ứng của nhân viên với master campaign này
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": "SS",
  "created_time": "2021-02-01T03:30:27Z",
  "crm_type": 0,
  "id": "f67e025d-18a5-4f76-914f-097a51fe1083",
  "is_default": 0,
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "name": "{}ss",
  "describe": "",
  "staffs": [
      {
          "avatar": "/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/accounts/avatar_7fc0a33c-baf5-11e7-a7c2-0242ac18000320201015022719.jpg",
          "email": "<EMAIL>",
          "fullname": "ngoanvt test 1",
          "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
          "is_admin": 1,
          "permission": "detail",
          "username": "admin@pingcomshop"
      },
      {
          "avatar": null,
          "email": "<EMAIL>",
          "fullname": "Admin 21",
          "id": "2a87f9ea-1ba1-40f0-bc15-9b2b9c9de7a8",
          "is_admin": 2,
          "permission": "detail",
          "username": "admin21@pingcomshop"
      }
  ],
  "use_for": "marketing",
  "version_code": 2
}
"""
************************
"""
@api {get} /api/v2.1/master-campaigns/<master_campaign_id> Lấy chi tiết master campaign
@apiDescription API lấy thông tin chi tiết master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.0
@apiName DetailMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Resources:)  {String}    master_campaign_id  Định danh master campaign cần xem chi tiết.

@apiSuccess   {MasterCampaign[]}  data  Danh sách master campaign phù hợp tiêu chí
@apiSuccess   (MasterCampaign)  {String}  id  Id của master campaign
@apiSuccess   (MasterCampaign)  {String}  code  Mã định danh của master campaign
@apiSuccess   (MasterCampaign)  {String}  name  Tên của master campaign
@apiSuccess   (MasterCampaign)  {StringArray}  keywords  Danh sách từ khoá nhận diện
@apiSuccess   (MasterCampaign)  {String}  keyword  <code>[DEPRECATED]</code>Danh sách từ khoá nhận diện. (Sử dụng <code>keywords</code> thay thế)
@apiSuccess   (MasterCampaign)  {DateTime}  start  Id của master campaign
@apiSuccess   (MasterCampaign)  {DateTime}  end  Id của master campaign
@apiSuccess   (MasterCampaign)  {Staff[]}  [staffs]  Danh sách staff được phân công quản trị master campaign

@apiSuccess   (Staff)  {String}  id   Id của nhân viên
@apiSuccess   (Staff)  {String}  username   Username của nhân viên
@apiSuccess   (Staff)  {String}  fullname   Tên của nhân viên
@apiSuccess   (Staff)  {String}  avatar   Link ảnh đại diện
@apiSuccess   (Staff)  {Number=1-admin,2-normal}  is_admin   Đánh dấu tài khoản quản trị
@apiSuccess   (Staff)   {StringArray=detail, insight}   permissions   Danh sách quyền tương ứng của nhân viên với master campaign này
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
  "code": "ABC",
  "name": "",
  "keywords": ["abc", "abc1"],
  "keyword": "abc,abc1",
  "start": "2017-08-07T04:02:28.002Z",
  "end": "2018-08-07T04:02:28.002Z",
  "staffs": [
    {
      "id": "5b50afe0-26fc-44eb-86dc-6002d320baca",
      "username": "staff1",
      "fullname": "Nguyễn Văn A",
      "avatar": "",
      "is_admin": 1,
      "permissions": ["detail"]
    },
    {
      "id": "0406a7b4-4bdd-4e34-96a9-3db64e75bb00",
      "username": "staff2",
      "fullname": "Nguyễn Văn B",
      "avatar": "",
      "is_admin": 0,
      "permissions": ["insight"]
    }
  ]
}
"""

*********************************** Get MasterCampaign ***********************************
* version: 1.0.4                                                                         *
* version: 1.0.3                                                                         *
* version: 1.0.2                                                                         *
* version: 1.0.1                                                                         *
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /api/v2.1/master-campaigns Lấy danh sách master campaign
@apiDescription API lấy danh sách master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.4
@apiName ListMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)  {String}  [search]  Tìm kiếm master campaign theo mã, từ khoá
@apiParam   (Query:)  {String}  [merchant_ids]  Lọc master campaign theo danh sách merchant.<br />
Nếu không có tham số này, danh sách master campaign được lọc theo merchant_id trong header.<br />
Ví dụ: <code>merchant_ids=7b432feb-6831-4827-9f20-5eac34037a84,96ba630b-01</code>
@apiParam   (Query:)  {String}  [master_campaign_ids]  Lấy master campaign theo danh sách ID. Nếu có tham số này, sẽ bỏ qua các tham số filter khác.
Ví dụ: <code>merchant_ids=d36760b2-507a-4c80-bd1a-82d44af7f11b,7b4f5fa8-7f...</code>
@apiParam   (Query:)  {Number=0-FALSE,1-TRUE}  [is_default]  Lấy master campaign mặc định của tenant. Mỗi tenant sẽ chỉ có 1 Master campaign mặc định.
@apiParam   (Query:)  {String}  [use_for]  Tìm kiếm master campaign được dùng cho moduls nào.
          <br/> Ví dụ: <code>marketing,journey-builder</code>
@apiUse account_status_query_params

@apiSuccess   {MasterCampaign[]}  data  Danh sách master campaign phù hợp tiêu chí
@apiSuccess   (MasterCampaign)  {String}  id  Id của master campaign
@apiSuccess   (MasterCampaign)  {String}  code  Mã định danh của master campaign
@apiSuccess   (MasterCampaign)  {String}  name  Tên của master campaign
@apiSuccess   (MasterCampaign)  {String}  [describe]  Mô tả 
@apiSuccess   (MasterCampaign)  {StringArray}  keywords  Danh sách từ khoá nhận diện
@apiSuccess   (MasterCampaign)  {String}  keyword  <code>[DEPRECATED]</code>Danh sách từ khoá nhận diện. (Sử dụng <code>keywords</code> thay thế)
@apiSuccess   (MasterCampaign)  {DateTime}  start  Id của master campaign
@apiSuccess   (MasterCampaign)  {DateTime}  end  Id của master campaign
@apiSuccess   (MasterCampaign)  {Staff[]}  [staffs]  Danh sách staff được phân công quản trị master campaign
@apiSuccess   (MasterCampaign)  {String}  [use_for]  master campaign được dùng cho moduls nào.

@apiSuccess   (Staff)  {String}  id   Id của nhân viên.
@apiSuccess   (Staff)  {String}  username   Username của nhân viên.
@apiSuccess   (Staff)  {String}  fullname   Tên của nhân viên.
@apiSuccess   (Staff)  {String}  avatar   Link ảnh đại diện.
@apiSuccess   (Staff)  {Number=1-admin,2-normal}  is_admin   Đánh dấu tài khoản quản trị.
@apiSuccess   (Staff)   {StringArray=detail, insight}   permissions   Danh sách quyền tương ứng của nhân viên với master campaign này.
@apiSuccess   (Staff)   {Number}  [crm_type]  Kiểu support hệ thống CRM.
@apiSuccess   (Staff)   {Number}  [is_default]  Đánh dấu Master Campaign mặc định của 1 tenant.
@apiSuccess   (Staff)   {Number}  [version_code]  Phiên bản code phân tích/xử lý MasterCampaign.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "code": "SS",
            "created_time": "2021-02-01T03:30:27Z",
            "crm_type": 0,
            "id": "f67e025d-18a5-4f76-914f-097a51fe1083",
            "is_default": 0,
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "{}ss",
            "describe": "",
            "staffs": [
                {
                    "avatar": "/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/accounts/avatar_7fc0a33c-baf5-11e7-a7c2-0242ac18000320201015022719.jpg",
                    "email": "<EMAIL>",
                    "fullname": "ngoanvt test 1",
                    "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "is_admin": 1,
                    "permission": "detail",
                    "username": "admin@pingcomshop"
                },
                {
                    "avatar": null,
                    "email": "<EMAIL>",
                    "fullname": "Admin 21",
                    "id": "2a87f9ea-1ba1-40f0-bc15-9b2b9c9de7a8",
                    "is_admin": 2,
                    "permission": "detail",
                    "username": "admin21@pingcomshop"
                }
            ],
            "use_for": "marketing,journey-builder",
            "version_code": 2
        },
        {
            "code": "YY",
            "created_time": "2021-02-18T14:24:41Z",
            "crm_type": 0,
            "id": "8700e5fe-151c-4c74-a10b-257d518f20f6",
            "is_default": 0,
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "yy",
            "staffs": [
                {
                    "avatar": "/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/accounts/avatar_7fc0a33c-baf5-11e7-a7c2-0242ac18000320201015022719.jpg",
                    "email": "<EMAIL>",
                    "fullname": "ngoanvt test 1",
                    "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "is_admin": 1,
                    "permission": "detail",
                    "username": "admin@pingcomshop"
                }
            ],
            "use_for": "marketing",
            "version_code": 2
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 5,
        "total_items": 50,
        "total_pages": 10
    }
}
"""
************************
"""
@api {get} /api/v2.1/master-campaigns Lấy danh sách master campaign
@apiDescription API lấy danh sách master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.3
@apiName ListMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)  {String}  [search]  Tìm kiếm master campaign theo mã, từ khoá
@apiParam   (Query:)  {String}  [merchant_ids]  Lọc master campaign theo danh sách merchant.<br />
Nếu không có tham số này, danh sách master campaign được lọc theo merchant_id trong header.<br />
Ví dụ: <code>merchant_ids=7b432feb-6831-4827-9f20-5eac34037a84,96ba630b-01</code>
@apiParam   (Query:)  {String}  [master_campaign_ids]  Lấy master campaign theo danh sách ID. Nếu có tham số này, sẽ bỏ qua các tham số filter khác.
Ví dụ: <code>merchant_ids=d36760b2-507a-4c80-bd1a-82d44af7f11b,7b4f5fa8-7f...</code>
@apiParam   (Query:)  {Number=0-FALSE,1-TRUE}  [is_default]  Lấy master campaign mặc định của tenant. Mỗi tenant sẽ chỉ có 1 Master campaign mặc định.

@apiSuccess   {MasterCampaign[]}  data  Danh sách master campaign phù hợp tiêu chí
@apiSuccess   (MasterCampaign)  {String}  id  Id của master campaign
@apiSuccess   (MasterCampaign)  {String}  code  Mã định danh của master campaign
@apiSuccess   (MasterCampaign)  {String}  name  Tên của master campaign
@apiSuccess   (MasterCampaign)  {StringArray}  keywords  Danh sách từ khoá nhận diện
@apiSuccess   (MasterCampaign)  {String}  keyword  <code>[DEPRECATED]</code>Danh sách từ khoá nhận diện. (Sử dụng <code>keywords</code> thay thế)
@apiSuccess   (MasterCampaign)  {DateTime}  start  Id của master campaign
@apiSuccess   (MasterCampaign)  {DateTime}  end  Id của master campaign
@apiSuccess   (MasterCampaign)  {Staff[]}  [staffs]  Danh sách staff được phân công quản trị master campaign

@apiSuccess   (Staff)  {String}  id   Id của nhân viên.
@apiSuccess   (Staff)  {String}  username   Username của nhân viên.
@apiSuccess   (Staff)  {String}  fullname   Tên của nhân viên.
@apiSuccess   (Staff)  {String}  avatar   Link ảnh đại diện.
@apiSuccess   (Staff)  {Number=1-admin,2-normal}  is_admin   Đánh dấu tài khoản quản trị.
@apiSuccess   (Staff)   {StringArray=detail, insight}   permissions   Danh sách quyền tương ứng của nhân viên với master campaign này.
@apiSuccess   (Staff)   {Number}  [crm_type]  Kiểu support hệ thống CRM.
@apiSuccess   (Staff)   {Number}  [is_default]  Đánh dấu Master Campaign mặc định của 1 tenant.
@apiSuccess   (Staff)   {Number}  [version_code]  Phiên bản code phân tích/xử lý MasterCampaign.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
      "code": "ABC",
      "name": "",
      "keywords": ["abc"],
      "keyword": "abc",
      "start": "2017-08-07T04:02:28.002Z",
      "end": "2018-08-07T04:02:28.002Z",
      "staffs": [
        {
          "id": "5b50afe0-26fc-44eb-86dc-6002d320baca",
          "username": "staff1",
          "fullname": "Nguyễn Văn A",
          "avatar": "",
          "is_admin": 1,
          "permissions": ["detail"]
        },
        {
          "id": "0406a7b4-4bdd-4e34-96a9-3db64e75bb00",
          "username": "staff2",
          "fullname": "Nguyễn Văn B",
          "avatar": "",
          "is_admin": 0,
          "permissions": ["insight"]
        }
      ]
    },
    {
      "id": "784e3f6b-e34a-4ea5-a585-8c2788d323b0",
      "code": "ABC1",
      "name": "",
      "keywords": ["abc1"],
      "keyword": "abc1",
      "start": "2017-08-07T04:02:28.002Z",
      "end": "2018-08-07T04:02:28.002Z",
      "staffs": [
        {
          "id": "44799cb4-9936-480c-b332-8cc28b2cf7a4",
          "username": "staff1",
          "fullname": "Nguyễn Văn A",
          "avatar": "",
          "is_admin": 1,
          "permissions": ["detail"]
        },
        {
          "id": "ffc59f31-6c51-48c4-a271-6439d63ce1b5",
          "username": "staff3",
          "fullname": "Nguyễn Văn C",
          "avatar": "",
          "is_admin": 0,
          "permissions": ["detail"]
        }
      ]
    }
  ]
}
"""
************************
"""
@api {get} /api/v2.1/master-campaigns Lấy danh sách master campaign
@apiDescription API lấy danh sách master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.2
@apiName ListMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)  {String}  [search]  Tìm kiếm master campaign theo mã, từ khoá
@apiParam   (Query:)  {String}  [merchant_ids]  Lọc master campaign theo danh sách merchant.<br />
Nếu không có tham số này, danh sách master campaign được lọc theo merchant_id trong header.<br />
Ví dụ: <code>merchant_ids=7b432feb-6831-4827-9f20-5eac34037a84,96ba630b-01</code>

@apiSuccess   {MasterCampaign[]}  data  Danh sách master campaign phù hợp tiêu chí
@apiSuccess   (MasterCampaign)  {String}  id  Id của master campaign
@apiSuccess   (MasterCampaign)  {String}  code  Mã định danh của master campaign
@apiSuccess   (MasterCampaign)  {String}  name  Tên của master campaign
@apiSuccess   (MasterCampaign)  {StringArray}  keywords  Danh sách từ khoá nhận diện
@apiSuccess   (MasterCampaign)  {String}  keyword  <code>[DEPRECATED]</code>Danh sách từ khoá nhận diện. (Sử dụng <code>keywords</code> thay thế)
@apiSuccess   (MasterCampaign)  {DateTime}  start  Id của master campaign
@apiSuccess   (MasterCampaign)  {DateTime}  end  Id của master campaign
@apiSuccess   (MasterCampaign)  {Staff[]}  [staffs]  Danh sách staff được phân công quản trị master campaign

@apiSuccess   (Staff)  {String}  id   Id của nhân viên
@apiSuccess   (Staff)  {String}  username   Username của nhân viên
@apiSuccess   (Staff)  {String}  fullname   Tên của nhân viên
@apiSuccess   (Staff)  {String}  avatar   Link ảnh đại diện
@apiSuccess   (Staff)  {Number=1-admin,2-normal}  is_admin   Đánh dấu tài khoản quản trị
@apiSuccess   (Staff)   {StringArray=detail, insight}   permissions   Danh sách quyền tương ứng của nhân viên với master campaign này
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
      "code": "ABC",
      "name": "",
      "keywords": ["abc"],
      "keyword": "abc",
      "start": "2017-08-07T04:02:28.002Z",
      "end": "2018-08-07T04:02:28.002Z",
      "staffs": [
        {
          "id": "5b50afe0-26fc-44eb-86dc-6002d320baca",
          "username": "staff1",
          "fullname": "Nguyễn Văn A",
          "avatar": "",
          "is_admin": 1,
          "permissions": ["detail"]
        },
        {
          "id": "0406a7b4-4bdd-4e34-96a9-3db64e75bb00",
          "username": "staff2",
          "fullname": "Nguyễn Văn B",
          "avatar": "",
          "is_admin": 0,
          "permissions": ["insight"]
        }
      ]
    },
    {
      "id": "784e3f6b-e34a-4ea5-a585-8c2788d323b0",
      "code": "ABC1",
      "name": "",
      "keywords": ["abc1"],
      "keyword": "abc1",
      "start": "2017-08-07T04:02:28.002Z",
      "end": "2018-08-07T04:02:28.002Z",
      "staffs": [
        {
          "id": "44799cb4-9936-480c-b332-8cc28b2cf7a4",
          "username": "staff1",
          "fullname": "Nguyễn Văn A",
          "avatar": "",
          "is_admin": 1,
          "permissions": ["detail"]
        },
        {
          "id": "ffc59f31-6c51-48c4-a271-6439d63ce1b5",
          "username": "staff3",
          "fullname": "Nguyễn Văn C",
          "avatar": "",
          "is_admin": 0,
          "permissions": ["detail"]
        }
      ]
    }
  ]
}
"""
********************
"""
@api {get} /api/v2.1/master-campaigns Lấy danh sách master campaign
@apiDescription API lấy danh sách master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.1
@apiName ListMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)  {String}  [search]  Tìm kiếm master campaign theo mã, từ khoá
@apiParam   (Query:)  {String}  [merchant_ids]  Lọc master campaign theo danh sách merchant.<br />
Nếu không có tham số này, danh sách master campaign được lọc theo merchant_id trong header.<br />
Ví dụ: <code>merchant_ids=7b432feb-6831-4827-9f20-5eac34037a84,96ba630b-01</code>

@apiSuccess   {MasterCampaign[]}  data  Danh sách master campaign phù hợp tiêu chí
@apiSuccess   (MasterCampaign)  {String}  id  Id của master campaign
@apiSuccess   (MasterCampaign)  {String}  code  Mã định danh của master campaign
@apiSuccess   (MasterCampaign)  {String}  name  Tên của master campaign
@apiSuccess   (MasterCampaign)  {StringArray}  keywords  Danh sách từ khoá nhận diện
@apiSuccess   (MasterCampaign)  {String}  keyword  <code>[DEPRECATED]</code>Danh sách từ khoá nhận diện. (Sử dụng <code>keywords</code> thay thế)
@apiSuccess   (MasterCampaign)  {DateTime}  start  Id của master campaign
@apiSuccess   (MasterCampaign)  {DateTime}  end  Id của master campaign
@apiSuccess   (MasterCampaign)  {Staff[]}  [staffs]  Danh sách staff được phân công quản trị master campaign

@apiSuccess   (Staff)  {String}  id   Id của nhân viên
@apiSuccess   (Staff)  {String}  username   Username của nhân viên
@apiSuccess   (Staff)  {String}  fullname   Tên của nhân viên
@apiSuccess   (Staff)  {String}  avatar   Link ảnh đại diện
@apiSuccess   (Staff)  {Number=1-admin,2-normal}  is_admin   Đánh dấu tài khoản quản trị
@apiSuccess   (Staff)   {StringArray=detail, insight}   permissions   Danh sách quyền tương ứng của nhân viên với master campaign này
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
      "code": "ABC",
      "name": "",
      "keywords": ["abc"],
      "keyword": "abc",
      "start": "2017-08-07T04:02:28.002Z",
      "end": "2018-08-07T04:02:28.002Z",
      "staffs": [
        {
          "id": "5b50afe0-26fc-44eb-86dc-6002d320baca",
          "username": "staff1",
          "fullname": "Nguyễn Văn A",
          "avatar": "",
          "is_admin": 1,
          "permissions": ["detail"]
        },
        {
          "id": "0406a7b4-4bdd-4e34-96a9-3db64e75bb00",
          "username": "staff2",
          "fullname": "Nguyễn Văn B",
          "avatar": "",
          "is_admin": 0,
          "permissions": ["insight"]
        }
      ]
    },
    {
      "id": "784e3f6b-e34a-4ea5-a585-8c2788d323b0",
      "code": "ABC1",
      "name": "",
      "keywords": ["abc1"],
      "keyword": "abc1",
      "start": "2017-08-07T04:02:28.002Z",
      "end": "2018-08-07T04:02:28.002Z",
      "staffs": [
        {
          "id": "44799cb4-9936-480c-b332-8cc28b2cf7a4",
          "username": "staff1",
          "fullname": "Nguyễn Văn A",
          "avatar": "",
          "is_admin": 1,
          "permissions": ["detail"]
        },
        {
          "id": "ffc59f31-6c51-48c4-a271-6439d63ce1b5",
          "username": "staff3",
          "fullname": "Nguyễn Văn C",
          "avatar": "",
          "is_admin": 0,
          "permissions": ["detail"]
        }
      ]
    }
  ]
}
"""
********************
"""
@api {get} /api/v2.1/master-campaigns Lấy danh sách master campaign
@apiDescription API lấy danh sách master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.0
@apiName ListMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)  {String}  [search]  Tìm kiếm master campaign theo mã, từ khoá
@apiParam   (Query:)  {String}  [merchant_ids]  Lọc master campaign theo danh sách merchant.<br />
Nếu không có tham số này, danh sách master campaign được lọc theo merchant_id trong header.<br />
Ví dụ: <code>merchant_ids=7b432feb-6831-4827-9f20-5eac34037a84,96ba630b-01</code>

@apiSuccess   {MasterCampaign[]}  data  Danh sách master campaign phù hợp tiêu chí
@apiSuccess   (MasterCampaign)  {String}  id  Id của master campaign
@apiSuccess   (MasterCampaign)  {String}  code  Mã định danh của master campaign
@apiSuccess   (MasterCampaign)  {String}  name  Tên của master campaign
@apiSuccess   (MasterCampaign)  {StringArray}  keywords  Danh sách từ khoá nhận diện
@apiSuccess   (MasterCampaign)  {String}  keyword  <code>[DEPRECATED]</code>Danh sách từ khoá nhận diện. (Sử dụng <code>keywords</code> thay thế)
@apiSuccess   (MasterCampaign)  {DateTime}  start  Id của master campaign
@apiSuccess   (MasterCampaign)  {DateTime}  end  Id của master campaign
@apiSuccess   (MasterCampaign)  {Staff[]}  [staffs]  Danh sách staff được phân công quản trị master campaign

@apiSuccess   (Staff)  {String}  id   Id của nhân viên
@apiSuccess   (Staff)  {String}  username   Username của nhân viên
@apiSuccess   (Staff)  {String}  fullname   Tên của nhân viên
@apiSuccess   (Staff)  {String}  avatar   Link ảnh đại diện
@apiSuccess   (Staff)  {Number=1-admin,2-normal}  is_admin   Đánh dấu tài khoản quản trị
@apiSuccess   (Staff)   {StringArray=detail, insight}   permissions   Danh sách quyền tương ứng của nhân viên với master campaign này
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
      "code": "ABC",
      "name": "",
      "keywords": ["abc"],
      "keyword": "abc",
      "start": "2017-08-07T04:02:28.002Z",
      "end": "2018-08-07T04:02:28.002Z",
      "staffs": [
        {
          "id": "5b50afe0-26fc-44eb-86dc-6002d320baca",
          "username": "staff1",
          "fullname": "Nguyễn Văn A",
          "avatar": "",
          "is_admin": 1,
          "permissions": ["detail"]
        },
        {
          "id": "0406a7b4-4bdd-4e34-96a9-3db64e75bb00",
          "username": "staff2",
          "fullname": "Nguyễn Văn B",
          "avatar": "",
          "is_admin": 0,
          "permissions": ["insight"]
        }
      ]
    },
    {
      "id": "784e3f6b-e34a-4ea5-a585-8c2788d323b0",
      "code": "ABC1",
      "name": "",
      "keywords": ["abc1"],
      "keyword": "abc1",
      "start": "2017-08-07T04:02:28.002Z",
      "end": "2018-08-07T04:02:28.002Z",
      "staffs": [
        {
          "id": "44799cb4-9936-480c-b332-8cc28b2cf7a4",
          "username": "staff1",
          "fullname": "Nguyễn Văn A",
          "avatar": "",
          "is_admin": 1,
          "permissions": ["detail"]
        },
        {
          "id": "ffc59f31-6c51-48c4-a271-6439d63ce1b5",
          "username": "staff3",
          "fullname": "Nguyễn Văn C",
          "avatar": "",
          "is_admin": 0,
          "permissions": ["detail"]
        }
      ]
    }
  ]
}
"""

*********************************** Delete MasterCampaign ***********************************
* version: 1.0.0                                                                            *
*********************************************************************************************
"""
@api {delete} /api/v2.1/master-campaigns   Xoá master campaign
@apiDescription API xoá master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.0
@apiName DeleteMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)  {String}  ids   Danh sách id các master campaign cần xoá.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "data_list_not_delete": [
            "f67e025d-18a5-4f76-914f-097a51fe1083"
        ],
        "data_list_delete": [
            "7756148f-19b1-4562-b244-9b038e9c06de"
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

*********************************** Create MasterCampaign ***********************************
* version: 1.0.2                                                                            *
* version: 1.0.1                                                                            *
* version: 1.0.0                                                                            *
*********************************************************************************************
"""
@api {post} /api/v2.1/master-campaigns Create master campaign
@apiDescription API tạo master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.2
@apiName CreateMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)   {String}  code  Mã master campaign
@apiParam   (Body:)   {String}  name  Tên master campaign
@apiParam   (Body:)   {String}  [describe]  Mô tả 
@apiParam   (Body:)   {String}  [sub_brand_id]  Định danh của sub brand. Trong trường hợp tạo master campaign cho sub-brand. <br />
Nếu không có field này, master campaign mặc định được tạo cho tenant của nhân viên đang login.
@apiParam   (Body:)   {StringArray}  [keywords]  Danh sách keyword được gắn với master campaign
@apiParam   (Body:)   {DateTime}  [start] Thời gian bắt đầu của master campaign. Ví dụ: 2017-08-07T04:02:28.002Z
@apiParam   (Body:)   {DateTime}  [end] Thời gian kết thúc của master campaign. Ví dụ: 2018-08-07T04:02:28.002Z
@apiParam   (Body:)   {StringArray}   [staffs]  Danh sách id của nhân viên được phân quyền quản lý master campaign này.
@apiParam   (Body:)   {String}  [use_for]  master campaign được dùng cho moduls nào
        <br/> Ví dụ: <code>marketing,journey-builder</code>

@apiParamExample  {json}  Body:
{
  "code": "ABC",
  "name": "",
  "describe": "",
  "sub_brand_id": "bdae6f6a-7f40-4669-b2d5-5a1011e3f188",
  "keywords": ["abc"],
  "start": "2017-08-07T04:02:28.002Z",
  "end": "2018-08-07T04:02:28.002Z",
  "staffs": ["0efe86d3-7eeb-40b4-81e9-9bdb33e8733c", "6bb848d6-3429-4a48-95c2-a86308d9929b"],
  "use_for": "marketing,journey-builder"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
  "code": 200,
  "message": "Request thành công"
}
"""
*********************
"""
@api {post} /api/v2.1/master-campaigns Create master campaign
@apiDescription API tạo master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.1
@apiName CreateMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)   {String}  code  Mã master campaign
@apiParam   (Body:)   {String}  name  Tên master campaign
@apiParam   (Body:)   {String}  [sub_brand_id]  Định danh của sub brand. Trong trường hợp tạo master campaign cho sub-brand. <br />
Nếu không có field này, master campaign mặc định được tạo cho tenant của nhân viên đang login.
@apiParam   (Body:)   {StringArray}  [keywords]  Danh sách keyword được gắn với master campaign
@apiParam   (Body:)   {DateTime}  [start] Thời gian bắt đầu của master campaign. Ví dụ: 2017-08-07T04:02:28.002Z
@apiParam   (Body:)   {DateTime}  [end] Thời gian kết thúc của master campaign. Ví dụ: 2018-08-07T04:02:28.002Z
@apiParam   (Body:)   {StringArray}   [staffs]  Danh sách id của nhân viên được phân quyền quản lý master campaign này.
@apiParamExample  {json}  Body:
{
  "code": "ABC",
  "name": "",
  "sub_brand_id": "bdae6f6a-7f40-4669-b2d5-5a1011e3f188",
  "keywords": ["abc"],
  "start": "2017-08-07T04:02:28.002Z",
  "end": "2018-08-07T04:02:28.002Z",
  "staffs": ["0efe86d3-7eeb-40b4-81e9-9bdb33e8733c", "6bb848d6-3429-4a48-95c2-a86308d9929b"]
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
  "code": 200,
  "message": "Request thành công"
}
"""
*********************
"""
@api {post} /api/v2.1/master-campaigns Create master campaign
@apiDescription API tạo master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.0
@apiName CreateMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)   {String}  code  Mã master campaign
@apiParam   (Body:)   {String}  name  Tên master campaign
@apiParam   (Body:)   {StringArray}  [keywords]  Danh sách keyword được gắn với master campaign
@apiParam   (Body:)   {DateTime}  [start] Thời gian bắt đầu của master campaign. Ví dụ: 2017-08-07T04:02:28.002Z
@apiParam   (Body:)   {DateTime}  [end] Thời gian kết thúc của master campaign. Ví dụ: 2018-08-07T04:02:28.002Z
@apiParam   (Body:)   {StringArray}   [staffs]  Danh sách id của nhân viên được phân quyền quản lý master campaign này.
@apiParamExample  {json}  Body:
{
  "code": "ABC",
  "name": "",
  "keywords": ["abc"],
  "start": "2017-08-07T04:02:28.002Z",
  "end": "2018-08-07T04:02:28.002Z",
  "staffs": ["0efe86d3-7eeb-40b4-81e9-9bdb33e8733c", "6bb848d6-3429-4a48-95c2-a86308d9929b"]
}

@apiSuccessExample 	{json}	Response: HTTP/1.1 200 OK
{
  "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
  "code": 200,
  "message": "Request thành công"
}
"""


*********************************** Get List Id MasterCampaign ***********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /api/v2.1/master-campaign-ids Lấy danh sách id master campaign
@apiDescription API lấy danh sách id master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.0
@apiName ListIdMC

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)  {String}  [search]  Tìm kiếm master campaign theo mã, từ khoá
@apiParam   (Query:)  {String}  [merchant_ids]  Lọc master campaign theo danh sách merchant.<br />
Nếu không có tham số này, danh sách master campaign được lọc theo merchant_id trong header.<br />
Ví dụ: <code>merchant_ids=7b432feb-6831-4827-9f20-5eac34037a84,96ba630b-01</code>
@apiParam   (Query:)  {String}  [master_campaign_ids]  Lấy master campaign theo danh sách ID. Nếu có tham số này, sẽ bỏ qua các tham số filter khác.
Ví dụ: <code>merchant_ids=d36760b2-507a-4c80-bd1a-82d44af7f11b,7b4f5fa8-7f...</code>
@apiParam   (Query:)  {Number=0-FALSE,1-TRUE}  [is_default]  Lấy master campaign mặc định của tenant. Mỗi tenant sẽ chỉ có 1 Master campaign mặc định.
@apiParam   (Query:)  {String}  [use_for]  Tìm kiếm master campaign được dùng cho moduls nào.
        <br/> Ví dụ: <code>marketing,journey-builder</code>

@apiSuccess   {MasterCampaign[]}  data  Danh sách master campaign phù hợp tiêu chí

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        "8aa3fd8b-99b0-44e6-9374-0201eab1fa23",
        "298571e1-504d-4388-af93-2fc7325a8b95"
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 5,
        "total_items": 50,
        "total_pages": 10
    }
}
"""

"""
@api {get} /api/v2.1/master-campaigns/list Lấy danh sách master campaign thông tin cơ bản 
@apiDescription API lấy danh sách master campaign
@apiGroup MasterCampaign
@apiVersion 1.0.0
@apiName ListMCBaseInfo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)  {String}  [search]  Tìm kiếm master campaign theo mã, từ khoá
@apiParam   (Query:)  {String}  [merchant_ids]  Lọc master campaign theo danh sách merchant.<br />
Nếu không có tham số này, danh sách master campaign được lọc theo merchant_id trong header.<br />
Ví dụ: <code>merchant_ids=7b432feb-6831-4827-9f20-5eac34037a84,96ba630b-01</code>
@apiParam   (Query:)  {String}  [master_campaign_ids]  Lấy master campaign theo danh sách ID. 
Ví dụ: <code>merchant_ids=d36760b2-507a-4c80-bd1a-82d44af7f11b,7b4f5fa8-7f...</code>
@apiParam   (Query:)  {Number=0-FALSE,1-TRUE}  [is_default]  Lấy master campaign mặc định của tenant. Mỗi tenant sẽ chỉ có 1 Master campaign mặc định.
@apiParam   (Query:)  {String}  [use_for]  Tìm kiếm master campaign được dùng cho moduls nào. mặc định là tất cả, "null" lấy campaign chưa dùng cho module nào.
          <br/> Ví dụ: <code>marketing,journey-builder</code>

@apiSuccess   {MasterCampaign[]}  data  Danh sách master campaign phù hợp tiêu chí
@apiSuccess   (MasterCampaign)  {String}  id  Id của master campaign
@apiSuccess   (MasterCampaign)  {String}  code  Mã định danh của master campaign
@apiSuccess   (MasterCampaign)  {String}  name  Tên của master campaign
@apiSuccess   (MasterCampaign)   {String}   [describe]  Mô tả 
@apiSuccess   (MasterCampaign)  {StringArray}  keywords  Danh sách từ khoá nhận diện
@apiSuccess   (MasterCampaign)  {String}  keyword  <code>[DEPRECATED]</code>Danh sách từ khoá nhận diện. (Sử dụng <code>keywords</code> thay thế)
@apiSuccess   (MasterCampaign)  {DateTime}  start  Id của master campaign
@apiSuccess   (MasterCampaign)  {DateTime}  end  Id của master campaign
@apiSuccess   (MasterCampaign)  {Staff[]}  [staffs]  Danh sách staff được phân công quản trị master campaign
@apiSuccess   (MasterCampaign)  {String}  [use_for]  master campaign được dùng cho moduls nào.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "code": "SS",
            "created_time": "2021-02-01T03:30:27Z",
            "crm_type": 0,
            "id": "f67e025d-18a5-4f76-914f-097a51fe1083",
            "is_default": 0,
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "{}ss",
            "describe": "",
            "use_for": "marketing,journey-builder",
            "version_code": 2
        },
        {
            "code": "YY",
            "created_time": "2021-02-18T14:24:41Z",
            "crm_type": 0,
            "id": "8700e5fe-151c-4c74-a10b-257d518f20f6",
            "is_default": 0,
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "yy",
            "describe": "",
            "use_for": "marketing",
            "version_code": 2
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 5,
        "total_items": 50,
        "total_pages": 10
    }
}
"""

#!/usr/bin/python
# -*- coding: utf8 -*-

"""
@apiDefine function_type_query
@apiVersion 1.0.0
@apiParam   (Query:)    {Number=1-MerchantCRM;2-AdminCRM;3-PartnerMobileApp}    type=1-Merchant    <PERSON>á<PERSON> đ<PERSON>nh function sẽ có hiệu lực trên ứng dụng nào.
<li><code>1-MerchantCRM</code>: function được phép chạy trên ứng dụng Merchant CRM;</li>
<li><code>2-AdminCRM</code>: function được phép chạy trên ứng dụng Admin CRM;</li>
<li><code>3-PartnerMobileApp</code>: function được phép chạy trên ứng dụng mobile Partner;</li>
"""

"""
@apiDefine function_type_body
@apiVersion 1.0.0
@apiParam   (Body:)    {Number=1-MerchantCRM;2-AdminCRM;3-PartnerMobileApp}    type=1-MerchantCRM    Xác định function sẽ có hiệu lực trên ứng dụng nào.
<li><code>1-MerchantCRM</code>: function được phép chạy trên ứng dụng Merchant CRM;</li>
<li><code>2-AdminCRM</code>: function được phép chạy trên ứng dụng Admin CRM;</li>
<li><code>3-PartnerMobileApp</code>: function được phép chạy trên ứng dụng mobile Partner;</li>
"""

"""
@apiDefine function_type_success
@apiVersion 1.0.0
@apiSuccess    {Number=1-MerchantCRM;2-AdminCRM;3-PartnerMobileApp}    type=1-MerchantCRM    Xác định function sẽ có hiệu lực trên ứng dụng nào.
<li><code>1-MerchantCRM</code>: function được phép chạy trên ứng dụng Merchant CRM;</li>
<li><code>2-AdminCRM</code>: function được phép chạy trên ứng dụng Admin CRM;</li>
<li><code>3-PartnerMobileApp</code>: function được phép chạy trên ứng dụng mobile Partner;</li>
"""

"""
@apiDefine function_status_query
@apiVersion 1.0.0
@apiParam   (Query:)    {Number=1-ENABLE;2-DISABLE}     [status=1-ENABLE]    Trạng thái của chức năng.<li><code>1-ENABLE</code>: Chức năng hoạt động bình thường;</li><li><code>2-DISABLE</code>: Chức năng đã bị xoá;</li>
"""

"""
@apiDefine function_status_body
@apiVersion 1.0.0
@apiParam   (Body:)    {Number=1-ENABLE;2-DISABLE}     [status=1-ENABLE]    Trạng thái của chức năng.<li><code>1-ENABLE</code>: Chức năng hoạt động bình thường;</li><li><code>2-DISABLE</code>: Chức năng đã bị xoá;</li>
"""

"""
@apiDefine function_status_success
@apiVersion 1.0.0
@apiSuccess   {Number=1-ENABLE;2-DISABLE}     [status=1-ENABLE]    Trạng thái của chức năng.<li><code>1-ENABLE</code>: Chức năng hoạt động bình thường;</li><li><code>2-DISABLE</code>: Chức năng đã bị xoá;</li>
"""
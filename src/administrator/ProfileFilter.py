#!/usr/bin/python
# -*- coding: utf8 -*-

*********************************** Add profilefilter ***************************************
* version: 2.1.0                                                                             *
*********************************************************************************************

"""
@api {POST} /api/v2.1/profile-filters Add filter
@apiDescription API thêm filter mới vào kho.
@apiGroup ProfileFilter
@apiVersion 2.1.0
@apiName AddProfileFilter

@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)   {String}  key  M<PERSON> bộ lọc
@apiParam   (Body:)   {String}  title  Tên bộ lọc
@apiParam   (Body:)   {String}  group_code  group của bộ lọc
@apiParam   (Body:)   {Integer}  is_base  bộ lọc mặc định
                      <li><code>0:</code>Không phải là bộ lọc mặc định</li>
                      <li><code>1:</code>Là bộ lọc mặc định</li>
@apiParam   (Body:)   {Integer}  status  trạng thái khóa/mở của bộ lọc
                      <li><code>0:</code>trạng thái khóa</li>
                      <li><code>1:</code>trạng thái mở</li>

@apiParamExample  {json}  Body:
{
    "key": "trigger_abandoned_periodaaa",
    "is_base": 1,
    "status": 1,
	"title": "Journey Builder trigger",
	"group_code": "Phát Sinh GIỎ HÀNG BỊ BỎ QUÊN"
}

@apiUse lang

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "id": "34e529ad-0d2d-4a92-935d-bc36ec10cdc8"
    },
    "lang": "vi",
    "message": "request thành công."
}
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""



*********************************** update profilefilter ************************************
* version: 2.1.0                                                                            *
*********************************************************************************************


"""
@api {PUT} /api/v2.1/profile-filters/<profilefilter_id> update filter
@apiDescription API update filter trong kho.
@apiGroup ProfileFilter
@apiVersion 2.1.0
@apiName UpdateProfileFilter

@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Resources:)  {String}    profilefilter_id  Id profile-filter cần update.

@apiParam   (Body:)   {String}  key  Mã bộ lọc
@apiParam   (Body:)   {String}  title  Tên bộ lọc
@apiParam   (Body:)   {String}  group_code  group của bộ lọc
@apiParam   (Body:)   {Integer}  is_base  bộ lọc mặc định
                      <li><code>0:</code>Không phải là bộ lọc mặc định</li>
                      <li><code>1:</code>Là bộ lọc mặc định</li>
@apiParam   (Body:)   {Integer}  status  trạng thái khóa/mở của bộ lọc
                      <li><code>0:</code>trạng thái khóa</li>
                      <li><code>1:</code>trạng thái mở</li>

@apiParamExample  {json}  Body:
{
    "key": "trigger_abandoned_perioda",
    "is_base": 1,
    "status": 1,
	"title": "Journey Builder trigger",
	"group_code": "Phát Sinh GIỎ HÀNG BỊ BỎ QUÊN"
}

@apiUse lang

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "group_code": "Phát Sinh GIỎ HÀNG BỊ BỎ QUÊN",
        "id": "34e529ad-0d2d-4a92-935d-bc36ec10cdc8",
        "is_base": 1,
        "key": "trigger_abandoned_periodaaa",
        "status": 1,
        "title": "Journey Builder trigger"
    },
    "lang": "vi",
    "message": "request thành công."
}
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""



*********************************** delete profilefilter ************************************
* version: 2.1.0                                                                               *
*********************************************************************************************

"""
@api {DELETE} /api/v2.1/profile-filters delete filter
@apiDescription API xóa filter trong kho.
@apiGroup ProfileFilter
@apiVersion 2.1.0
@apiName DeleteProfileFilter

@apiUse merchant_id_header

@apiParam   (Query:)  {String}  ids   Danh sách id các profile-filter cần xoá.

@apiUse lang

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "deleted": ["17", "40"]
  "code": 200,
  "message": "Request thành công"
}
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""



*********************************** assign profile_filter cho merchant ************************************
* version: 2.1.0                                                                              *
*********************************************************************************************


"""
@api {POST} /api/v2.1/profile-filters/actions/assign assign profile filter cho merchant
@apiDescription API assign profile filter cho merchant.
@apiGroup ProfileFilter
@apiVersion 2.1.0
@apiName AssignProfileFilter

@apiUse merchant_id_header
@apiUse json_header
@apiUse lang


@apiParam (Body:) {list}  [profilefilters]  Danh sách ID của profile filter.
@apiParam (Body:) {String}  merchant_id  id của merchant.

@apiParamExample  {json}  Body:
{
  "profilefilters": ["57097228-670d-448f-bc83-15b10523d781"],
  "merchant_id": '1b99bdcf-d582-4f49-9715-1b61dfff3924'
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Request thành công"
}
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""

*********************************** gỡ profile_filter cho merchant ************************************
* version: 2.1.0                                                                            *
*********************************************************************************************


"""
@api {POST} /api/v2.1/profile-filters/actions/unassign Gỡ profile filter cho merchant
@apiDescription API Gỡ profile filter cho merchant.
@apiGroup ProfileFilter
@apiVersion 2.1.0
@apiName UnassignProfileFilter

@apiUse merchant_id_header
@apiUse json_header
@apiUse lang


@apiParam (Body:) {list}  [profilefilters]  Danh sách ID của profile filter.
@apiParam (Body:) {String}  merchant_id  id của merchant.

@apiParamExample  {json}  Body:
{
  "profilefilters": ["57097228-670d-448f-bc83-15b10523d781"],
  "merchant_id": '1b99bdcf-d582-4f49-9715-1b61dfff3924'
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Request thành công"
}
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""


*********************************** Lấy danh sách bộ lọc assign cho merchant ************************************
* version: 2.1.0                                                                         *
*********************************************************************************************


"""
@api {GET} /api/v2.1/profile-filters Lấy danh sách bộ lọc assign cho merchant
@apiDescription API Lấy danh sách bộ lọc assign cho merchant.
@apiGroup ProfileFilter
@apiVersion 2.1.0
@apiName GetAssignProfileFilter

@apiUse merchant_id_header
@apiUse lang

@apiParam   (Query:)  {String}  assign   Danh sách bộ lọc được phân cho merchant
    <ul>
        <li>merchant: Danh sách bộ lọc được phân cho merchant  </li>
        <li>all: Lấy bộ lọc trong kho </li>
    </ul>
@apiParam   (Query:)    {String}  [search]     Tìm kiếm fields theo key.
@apiParam   (Query:)    {String}  [id]  ids của Merchant được phân công
<code>Nếu assign là merchant thì id là bắt buộc</code>

@apiUse paging
@apiUse order_sort

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "merchant_id": "",
        "profile_filter": [
            {
                "id": "0b72b1c8-10f6-4a76-a8ca-dc5d47329a1e",
                "group_code": null,
                "is_base": 1,
                "key": "cri_card_status",
                "title": null
            },
            {
                "id": "0b72b1c8-10f6-4a76-a8ca-dc5d47329a1e",
                "group_code": null,
                "is_base": 1,
                "key": "cri_transaction",
                "title": null
            },
            {
                "id": "0b72b1c8-10f6-4a76-a8ca-dc5d47329a1e",
                "group_code": null,
                "is_base": 1,
                "key": "trigger_dynamic_logic",
                "title": null
            },
            {
                "id": "0b72b1c8-10f6-4a76-a8ca-dc5d47329a1e",
                "group_code": null,
                "is_base": 1,
                "key": "cri_transaction_time",
                "title": null
            },
            {
                "id": "0b72b1c8-10f6-4a76-a8ca-dc5d47329a1e",
                "group_code": null,
                "is_base": 1,
                "key": "cri_job",
                "title": null
            }
        ]
    },
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "page_count": 12,
        "per_page": 5,
        "total_count": 57
    }
}
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""

*********************************** get profilefilter ************************************
* version: 2.1.0                                                                               *
*********************************************************************************************

"""
@api {GET} /api/v2.1/profile-filter?key=trigger_abandoned_periodaaa get filter
@apiDescription API get filter trong kho.
@apiGroup ProfileFilter
@apiVersion 2.1.0
@apiName GetProfileFilter

@apiUse merchant_id_header

@apiParam   (Query:)  {String}  key   Mã bộ lọc.
@apiParam   (Query:)  {String}  [id]   id định danh bộ lọc.

@apiUse lang

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "group_code": "Phát Sinh GIỎ HÀNG BỊ BỎ QUÊN",
        "id": "34e529ad-0d2d-4a92-935d-bc36ec10cdc8",
        "is_base": 1,
        "key": "trigger_abandoned_periodaaa",
        "status": 1,
        "title": "Journey Builder trigger"
    },
    "lang": "vi",
    "message": "request thành công."
}
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""

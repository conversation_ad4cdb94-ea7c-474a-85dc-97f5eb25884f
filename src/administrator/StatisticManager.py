********************************* API LẤY THÔNG TIN QUOTA HIỆN TẠI THEO MERCHANT *********************************
* version: 1.1.0                                                                                                 *
******************************************************************************************************************
"""
@api {GET} /api/v2.1/merchants/<merchant_id>/statistic/quota Lấy thông tin quota của merchant
@apiDescription Lấy thông tin quota của merchant
@apiVersion 1.1.0
@apiGroup Statistic
@apiName GetStatisticMerchant

@apiSuccess  {string=MONTHLY NO_REPEAT} type_email=MONTHLY Chu kỳ đếm lại số lượng gửi email

@apiSuccess  {object} max_amount Thông tin số lượng tối đa theo merchant
@apiSuccess (max_amount) {number} users Số lượng users tối đa được phép quản lý
@apiSuccess (max_amount) {number} emails Số lượng email được phép gửi tối đa.
@apiSuccess (max_amount) {number} check_email_valid Số lượng email được check valid tối đa

@apiSuccess  {object} current_amount Thông số hiện tại của merchant
@apiSuccess (current_amount) {number} users Số lượng users hiện tại
@apiSuccess (current_amount) {number} emails Số lượng email đã gửi.
@apiSuccess (current_amount) {number} check_email_valid Số lượng email đã check valid
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "max_amount": {
    "users": 100000,
    "emails": 300000,
    "check_email_valid": 100000
  },
  "current_amount": {
    "users": 10000,
    "emails": 5000,
    "check_email_valid": 1000
  },
  "type_email": 'MONTHLY'
}
"""
********************************* API TĂNG SỐ LƯỢNG SỬ DỤNG *********************************
* version: 1.1.0                                                                            *
*********************************************************************************************
"""
@api {POST} /api/v2.1/merchants/<merchant_id>/statistic/increase API tăng số lượng sử dụng
@apiDescription API tăng số lượng sử dụng
@apiVersion 1.1.0
@apiGroup Statistic
@apiName PostStatisticIncrease
@apiDeprecated use now (<a href="#api-Webhook">Webhook</a>)

@apiParam  {number=1-user 2-send_email 3-check_email} type Kiểu tăng số lượng.
@apiParam  {number} number Số lượng tăng
@apiParam  {number=1-Tăng_thêm 2-Tăng_bằng_number} state Kiểu tăng số lượng
@apiParamExample {json} Body
{
    "type": 1,
    "state": 1,
    "number": 1
}
"""
#!/usr/bin/python
# -*- coding: utf8 -*-


******************************* API LIST ACCOUNT BY PRODUCT *************************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {GET} /api/v2.1/sale/rm Danh sách nhân viên có trường thông tin thêm 
@apiDescription có phân trang và lọc  
@apiVersion 1.0.0
@apiGroup Sale
@apiName ListAccountFieldNew

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header

@apiParam          (Query:) {String}        [account_ids]    ID nhân viên, nhiều nhân viên cách nhau bằng dấu , 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "account_id": "1ab5bb28-b9af-4da4-bd98-85ac77c7fd2f",
            "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
            "target_customer": "KHCN",
            "scope_code": "VUNG1#HANOI",
            ...
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


******************************* API LIST ACCOUNT BY FILTER *************************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {GET} /api/v2.1/accounts/finds Danh sách nhân viên theo bộ lọc
@apiDescription lấy danh sách thông tin nhân viên theo bộ lọc: tên truy cập, email   
@apiVersion 1.0.0
@apiGroup Sale
@apiName FindsAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.
@apiParam          (Query:) {String}        [username]    Tên truy cập, nhiều giá trị cách nhau bằng dấu , 
@apiParam          (Query:) {String}        [email]    Email nhân viên, nhiều giá trị cách nhau bằng dấu , 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "avatar": "",
            "email": "<EMAIL>",
            "fullname": "Ngoan VT",
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "is_admin": 1,
            "last_login_date": "2021-08-20T04:24:01Z",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "role_group": "owner",
            "status": 1,
            "username": "admin@pingcomshop"
        },
        
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


******************************* API DETAIL RM *************************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {GET} /api/v2.1/sale/rm/detail Thông tin chi tiết RM
@apiDescription Thông tin chi tiết RM 
@apiVersion 1.0.0
@apiGroup Sale
@apiName RMDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam          (Query:) {String}        account_id    id nhân viên   


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "account_id": "8b30295f-f7a2-4fc3-8325-0712dcceb31f",
        "merchant_id": "bdee9565-3cad-40ae-b2dc-421121d70279",
        "avatar": null,
        "staff_code": null,
        "email": "<EMAIL>",
        "name": "Ngoan vt",
        "phone": "**********",
        "target_customer": "KHCN",
        "scope_code": "VUNG1#HANOI",
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {POST} /api/v2.1/sale/rm  cập nhật thông tin RM  
@apiDescription  cập nhật thông tin RM  
@apiVersion 1.0.0
@apiGroup Sale
@apiName UpdateRM

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header


@apiParam   (Body:) {String}   account_id    id nhân viên  
@apiParam   (Body:) {String}   [target_customer]  loại khách hàng nhân viên quản lý: khách hàng cá nhân hoặc doanh nghiệp  
@apiParam   (Body:) {String}   [scope_code]   mã phạm vi phụ trách của nhân viên 


@apiParamExample {json} Body example
{
    "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "target_customer": "KHCN",
    "scope_code": "VUNG1#HANOI",
    
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
        "account_id": "8b30295f-f7a2-4fc3-8325-0712dcceb31f",
        "merchant_id": "bdee9565-3cad-40ae-b2dc-421121d70279",
        "target_customer": "KHCN",
        "scope_code": "VUNG1#HANOI",
  }
  "message": "request thành công.",
}

@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
}

"""

"""
@api {GET} /api/v2.1/manager/scope/list Danh sách phạm vi của merchant  
@apiDescription lấy tất cả 
@apiVersion 1.0.0
@apiGroup Sale
@apiName ListScope

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "scope_name": "HaNoi",
            "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
            "delimiter": "#",
            "scope_code": "VUNG1#HANOI",
            "number_level": 1
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {POST} /api/v2.1/manager/scope/detail chi tiết phạm vi 
@apiDescription lấy chi tiết nhiều phạm vi 
@apiVersion 1.0.0
@apiGroup Sale
@apiName ScopeDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:) {String}   scope_code    danh sách mã phạm vi lấy chi tiết 

@apiParamExample {json} Body example
{
    "scope_code": ["HANOI", "VUNG1#HANOI"]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "scope_name": "HaNoi",
            "merchant_id": "e52277ca-1cf1-4402-8da3-417092af3523",
            "delimiter": "#",
            "scope_code": "VUNG1#HANOI",
            "number_level": 1
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/account/check/role-scope Kiểm tra thông tin quyền và phạm vi của tài khoản   
@apiDescription Kiểm tra thông tin quyền và phạm vi của tài khoản 
@apiVersion 1.0.0
@apiGroup Sale
@apiName AccountCheckRoleScope

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam          (Query:) {String}        account_id    id nhân viên  
@apiParam          (Query:) {String}        module_name    module cần kiểm tra: COMPANY, PROFILING  

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "type_account": "srm",      // loại tài khoản: normal, rm, srm 
        "scope_code": "VUNG1#HANOI", // mã phạm vi của tài khoản 
        "role_group": "manager", // nhóm user  
        "staff_code": "", // mã nhân viên  
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {POST} /api/v2.1/manager/scope/upsert thêm mới nhiều mã phạm vi 
@apiDescription nếu có mã rồi thì cập nhật 
@apiVersion 1.0.0
@apiGroup Sale
@apiName ScopeUpsert

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:) {String}   delimiter    ký tự phân cách giữa các mã 
@apiParam   (Body:) {Array}   data    danh sách các mã 
@apiParam   (data:) {String}   scope_code   mã phạm vi  
@apiParam   (data:) {String}   scope_name   tên mã  


@apiParamExample {json} Body example
{
	"delimiter": "##",
	"data": [
		{
			"scope_code": "HOI_SO",
			"scope_name": "HOI_SO"
		},
		{
			"scope_code": "HOI_SO##MIEN_BAC",
			"scope_name": "MIEN_BAC"
		},
        ...
    ]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {POST} /api/v2.1/manager/scope/delete Xóa nhiều mã phạm vi 
@apiDescription  Xóa nhiều mã phạm vi 
@apiVersion 1.0.0
@apiGroup Sale
@apiName ScopeDelete

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (data:) {Array}   scope_code   danh sách mã phạm vi muốn xóa


@apiParamExample {json} Body example
{
    "scope_code": ["HOI_SO##MIEN_BAC", "HOI_SO"]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {POST} /api/v2.1/account/update/multi cập nhật mã phạm vi cho nhân viên 
@apiDescription cập nhật nhiều tài khoản, tối đa 100 
@apiVersion 1.0.0
@apiGroup Sale
@apiName AccountUpdateScope

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:) {Array}   data    danh sách tài khoản  
@apiParam   (data:) {String}   email   email tài khoản   
@apiParam   (data:) {String}   scope_code   mã phạm vi    
@apiParam   (data:) {String}   staff_code   mã nhân viên   

@apiParamExample {json} Body example
{
	"data": [
		{
			"email": "<EMAIL>",
			"scope_code": "HOI_SO",
			"staff_code": "luong01"
		},
        ...
	]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} /api/v2.1/account/scope-code Thông tin mã cấp của tài khoản 
@apiDescription Thông tin mã cấp của tài khoản  
@apiVersion 1.0.0
@apiGroup Sale
@apiName AcountScopeCodeDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam          (Query:) {String}        account_id    id nhân viên   


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "delimiter": "##",
        "number_level": 4,
        "scope_code": "HO##01RB000048##Vung04##PGD08",
        "scope_name": "PGD08"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/manager/scope/pagination Danh sách phạm vi  
@apiDescription Phân trang 
@apiVersion 1.0.0
@apiGroup Sale
@apiName ListScopePagination

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse merchant_id_header

@apiParam   (Query:) {String}   [search]   Chuỗi tìm kiếm. 


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "delimiter": "##",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "number_level": 1,
            "scope_code": "AREA_1",
            "scope_name": "AREA 1"
        },
        {
            "delimiter": "##",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "number_level": 2,
            "scope_code": "AREA_1##3201",
            "scope_name": "MSB Thanh Xuân Nam (AREA 1)"
        },
    ],
    "paging": {
        "page": 1,
        "per_page": 15,
        "total_count": 222,
        "total_page": 15
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/manager/scope/delimiter lấy ký tự phân cách mã cấp của merchant  
@apiDescription merchant không sử dụng mã cấp thì trả về rỗng 
@apiVersion 1.0.0
@apiGroup Sale
@apiName MerchantGetScopeDelimiter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "delimiter": "#"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {POST} /api/v2.1/accounts/field-extra lấy các trường thông tin phụ của user 
@apiDescription danh sách thông tin theo id tài khoản 
@apiVersion 1.0.0
@apiGroup Sale
@apiName ListAccountFieldExtra 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:) {Array}   ids     danh sách id tài khoản  
@apiParam   (Body:) {Array}   [select_field]     danh sách field cần lấy thêm thông tin VD: ["username", "fullname"]

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "fullname": "Admin pingcomshop",
            "username": "admin@pingcomshop",
            "account_id": "8b30295f-f7a2-4fc3-8325-0712dcceb31f",
            "block": ["KHCN"],
            "scope_code": "VUNG1#HANOI",
            ...
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


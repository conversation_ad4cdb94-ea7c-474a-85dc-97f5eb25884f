#!/usr/bin/python
# -*- coding: utf8 -*-

******************************* API LẤY TẤT CẢ BỘ SETTING DNC *************************************
* version: 1.0.0                                                                                 *
**************************************************************************************************
"""
@api {get} /api/v2.1/settings-dnc Lấy tất cả bộ settings dnc
@apiDescription Lấy tất cả bộ settings dnc
@apiVersion 1.0.0
@apiGroup Setting
@apiName GetAll

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccess  (data) {String} group Tên nhóm các bộ điều kiện kiểm tra setting account dnc.
@apiSuccess  (data) {String} label Tên il8n nhóm các bộ điều kiện kiểm tra setting account dnc.
@apiSuccess (data) {String} type_check  Mảng các bộ điều kiện kiểm tra setting account dnc.

@apiSuccess (type_check) {String} label Tên il8n bộ điều kiện kiểm tra setting account dnc.
@apiSuccess (type_check) {String} options Mảng bộ điều kiện kiểm tra setting account dnc thuộc group.

@apiSuccess (options) {String} key  Là key điều kiện kiểm tra setting account dnc.
@apiSuccess (options) {String} label  Là Tên điều kiện kiểm tra setting account dnc.

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "group": "calling",
            "label": "i18n_check_call",
            "type_check": [
                {
                    "label": "",
                    "options": [
                        {
                            "key": "NOT_CHECK_AND_CALLED_NORMALLY",
                            "label": "i18n_not_check_and_call"
                        },
                        {
                            "key": "CHECK_BEFORE_CALL_PROFILE_AND_WITH_WARNING",
                            "label": "i18n_check_before_call_and_warning"
                        },
                        {
                            "key": "CHECK_BEFORE_CALLED_PROFILE_AND_AUTO_STOP_CALL",
                            "label": "i18n_check_before_call_and_auto_stop"
                        }
                    ],
                    "type": "calling"
                }
            ]
        },
        {
            "group": "send_email",
            "label": "i18n_check_send_email",
            "type_check": [
                {
                    "label": "i18n_send_email_marketing",
                    "options": [
                        {
                            "key": "NOT_CHECK_AND_SEND_NORMALLY",
                            "label": "i18n_not_check_and_send"
                        },
                        {
                            "key": "CHECK_BEFORE_SEND_AND_DO_NOT_CONTINUE_SEND",
                            "label": "i18n_check_before_send_and_stop_send"
                        }
                    ],
                    "type": "send_email_marketing"
                },
                {
                    "label": "i18n_send_email_one_profile",
                    "options": [
                        {
                            "key": "NOT_CHECK_AND_SEND_NORMALLY",
                            "label": "i18n_not_check_and_send"
                        },
                        {
                            "key": "CHECK_BEFORE_SEND_PROFILE_AND_WITH_WARNING",
                            "label": "i18n_check_before_send_and_warning"
                        },
                        {
                            "key": "CHECK_BEFORE_SEND_AND_DO_NOT_CONTINUE_SEND",
                            "label": "i18n_check_before_send_and_stop_send"
                        }
                    ],
                    "type": "send_email_profile"
                }
            ]
        },
        {
            "group": "send_sms",
            "label": "i18n_check_send_sms_marketing",
            "type_check": [
                {
                    "label": "",
                    "options": [
                        {
                            "key": "NOT_CHECK_AND_SEND_NORMALLY",
                            "label": "i18n_not_check_and_send"
                        },
                        {
                            "key": "CHECK_EMAIL_BEFORE_SEND_AND_DO_NOT_CONTINUE_SEND",
                            "label": "i18n_check_before_send_and_stop_send"
                        }
                    ],
                    "type": "send_sms_marketing"
                }
            ]
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* API LẤY SETTING ACCOUNT DNC *************************************
* version: 1.0.0                                                                                 *
**************************************************************************************************
"""
@api {get} /api/v2.1/merchants/accounts/settings-dnc Lấy setting account dnc
@apiDescription Lấy setting account dnc
@apiVersion 1.0.0
@apiGroup Setting
@apiName GetSettingDnc

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccess  (data) {String} merchant_id id của merchant.
@apiSuccess (data) {String} account_id id account đăng nhập.
@apiSuccess (data) {List} template Bộ setting account dnc mẫu.
            <code>Note:</code> Template là bộ setting account dnc được lấy từ api /api/v2.1/settings-dnc.
@apiSuccess (data) {List} data_selected Bộ setting account dnc được chọn.

@apiSuccess (data_seleted) {String} type Tên điều kiện kiểm tra setting account dnc.
@apiSuccess (data_seleted) {String} selected Key điều kiện kiểm tra setting account dnc được chọn.
@apiSuccess (data_seleted) {String} group Tên nhóm điều kiện kiểm tra setting account dnc.

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "data_selected": [
            {
                "type": "calling",
                "selected": "NOT_CHECK_AND_CALLED_NORMALLY",
                "group": "calling"
            }
        ],
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "template": [
            {
                "group": "calling",
                "label": "i18n_check_call",
                "type_check": [
                    {
                        "label": "",
                        "options": [
                            {
                                "key": "NOT_CHECK_AND_CALLED_NORMALLY",
                                "label": "i18n_not_check_and_call"
                            },
                            {
                                "key": "CHECK_BEFORE_CALL_PROFILE_AND_WITH_WARNING",
                                "label": "i18n_check_before_call_and_warning"
                            },
                            {
                                "key": "CHECK_BEFORE_CALLED_PROFILE_AND_AUTO_STOP_CALL",
                                "label": "i18n_check_before_call_and_auto_stop"
                            }
                        ],
                        "type": "calling"
                    }
                ]
            },
            {
                "group": "send_email",
                "label": "i18n_check_send_email",
                "type_check": [
                    {
                        "label": "i18n_send_email_marketing",
                        "options": [
                            {
                                "key": "NOT_CHECK_AND_SEND_NORMALLY",
                                "label": "i18n_not_check_and_send"
                            },
                            {
                                "key": "CHECK_BEFORE_SEND_AND_DO_NOT_CONTINUE_SEND",
                                "label": "i18n_check_before_send_and_stop_send"
                            }
                        ],
                        "type": "send_email_marketing"
                    },
                    {
                        "label": "i18n_send_email_one_profile",
                        "options": [
                            {
                                "key": "NOT_CHECK_AND_SEND_NORMALLY",
                                "label": "i18n_not_check_and_send"
                            },
                            {
                                "key": "CHECK_BEFORE_SEND_PROFILE_AND_WITH_WARNING",
                                "label": "i18n_check_before_send_and_warning"
                            },
                            {
                                "key": "CHECK_BEFORE_SEND_AND_DO_NOT_CONTINUE_SEND",
                                "label": "i18n_check_before_send_and_stop_send"
                            }
                        ],
                        "type": "send_email_profile"
                    }
                ]
            },
            {
                "group": "send_sms",
                "label": "i18n_check_send_sms_marketing",
                "type_check": [
                    {
                        "label": "",
                        "options": [
                            {
                                "key": "NOT_CHECK_AND_SEND_NORMALLY",
                                "label": "i18n_not_check_and_send"
                            },
                            {
                                "key": "CHECK_EMAIL_BEFORE_SEND_AND_DO_NOT_CONTINUE_SEND",
                                "label": "i18n_check_before_send_and_stop_send"
                            }
                        ],
                        "type": "send_sms_marketing"
                    }
                ]
            }
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* API ADD SETTING ACCOUNT DNC *************************************
* version: 1.0.0                                                                                 *
**************************************************************************************************
"""
@api {post} /api/v2.1/merchants/accounts/settings-dnc Lưu setting account dnc
@apiDescription Lưu setting account dnc
@apiVersion 1.0.0
@apiGroup Setting
@apiName AddSettingDnc

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse json_header

@apiParam (Body:) {dict}  data_selected  bộ setting account dnc được chọn.

@apiParamExample  {json}  Body:
{
  "data_selected": [
    {
        "type": "calling",
        "selected": "NOT_CHECK_AND_CALLED_NORMALLY",
        "group": "calling"
    }
  ]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""



"""
@api {GET} /api/v2.1/account/config/tab_view Lấy thông tin cấu tab hiển thị 
@apiDescription  tab hiển thị trong chi tiết profile, company, sale, ticket 
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountGetTabView
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParam   (Query:)    {String}  account_id         id tài khoản

@apiSuccess {Array} profile  thông tin cấu hình profile
@apiSuccess {Array} company  thông tin cấu hình company
@apiSuccess {Array} sale  thông tin cấu hình sale
@apiSuccess {Array} ticket  thông tin cấu hình ticket
@apiSuccess {Object} social  thông tin cấu hình social
<code>page_show_info: </code> số lượng cuộc hội thoại, bình luận, đánh giá hiển thị trên trang công việc.
<li><code>number_unanswered:</code> số lượng chưa trả lời .</li>
<li><code>number_unread:</code> số lượng chưa đọc.</li>

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "profile": ["work", "callcenter", "email", "event", "note", "history_point", "history_transaction", "social", "sms"],
    "company": ["work", "callcenter", "email", "event", "note", "social", "sms"],
    "sale": ["work", "callcenter", "email", "event", "note", "social", "sms"],
    "ticket": ["work", "callcenter", "email", "event", "note", "social", "sms"],
    "social": {
        "page_show_info": "number_unanswered" 
    }
  },
  
}
"""


"""
@api {POST} /api/v2.1/account/config/tab_view  Cập nhật cấu hình tab view
@apiDescription  Cập nhật cấu hình tab view
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountConfigTabView

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} account_id id tài khoản
@apiParam (Body:) {Array} profile  thông tin cấu hình profile
@apiParam (Body:)  {Array} company  thông tin cấu hình company
@apiParam (Body:)  {Array} sale  thông tin cấu hình sale
@apiParam (Body:)  {Array} ticket  thông tin cấu hình ticket
@apiParam (Body:)  {Object} social  thông tin cấu hình social
<code>page_show_info: </code> số lượng cuộc hội thoại, bình luận, đánh giá hiển thị trên trang công việc.
<li><code>number_unanswered:</code> số lượng chưa trả lời .</li>
<li><code>number_unread:</code> số lượng chưa đọc.</li>

@apiParamExample {json} Body example
{
    "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "profile": ["work", "callcenter", "email", "event", "note", "history_point", "history_transaction", "social", "sms"],
    "company": ["work", "callcenter", "email", "event", "note", "social", "sms"],
    "sale": ["work", "callcenter", "email", "event", "note", "social", "sms"],
    "ticket": ["work", "callcenter", "email", "event", "note", "social", "sms"],
    "social": {
        "page_show_info": "number_unanswered" 
    }
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""


"""
@api {GET} /api/v2.1/account/config/menu-default Lấy thông tin cấu hình menu mặc định 
@apiDescription  menu mặc định 
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountGetMenuDefault
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParam   (Query:)    {String}  account_id         id tài khoản

@apiSuccess {string} menu_default  thông tin menu

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "menu_default": "/page/default"
  },
  
}
"""


"""
@api {POST} /api/v2.1/account/config/menu-default  Cập nhật cấu hình menu mặc định 
@apiDescription  Cập nhật cấu hình menu mặc định 
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountConfigMenuDefault

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} account_id id tài khoản
@apiParam (Body:) {String} menu_default  thông tin menu


@apiParamExample {json} Body example
{
    "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "menu_default": "/page/default"
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""



"""
@api {POST} /api/v2.1/account/setting/notify Cập nhật thông báo theo nhóm chức năng  
@apiDescription merchant cấu hình chức năng nào sẽ có nhóm thông báo theo chức năng đó 
@apiVersion 1.0.0
@apiGroup Setting
@apiName SettingAccountConfigNotify

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} account_id ID của tài khoản

@apiParamExample {json} Body example
{
    "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "data":[
        {
            "group": "cdp",
            "config": {
                "cdp_result_upload_profile": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                },
                "cdp_result_upload_company": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                }
            }
        },
        {
            "group": "jb",
            "config": {
                "jb_result": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "jb_estimate_target_size": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "jb_create_by_me_begin":{
                    "value": 1,
                    "unit": "day",
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "jb_create_by_me_end":{
                    "value": 1,
                    "unit": "day",
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "jb_pending_approval":{
                    "value": 1,
                    "unit": "day",
                    "times": 1,
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "jb_result_approval": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
            }
        },
        {
            "group": "sale",
            "config": {
                "sale_result_upload_deal": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                },
                "sale_alert_max_deal_not_assign": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "sale_deal_assign_me":{
                    "permission": "owner_supporter",
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "sale_deal_revoke":{
                    "permission": "owner_supporter",
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "sale_deal_remove_me":{
                    "permission": "owner_supporter",
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
            }
        },
        {
            "group": "social",
            "config": {
                "social_alert_max_chat_not_assign": {
                    "app": "on",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                },
                "social_chat_assign_me": {
                    "ringtone_web": "on",
                    "all_page": "on",
                    "page_ids": [],
                    "app": "on",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                },
                "social_comment_assign_me": {
                    "ringtone_web": "on",
                    "all_page": "on",
                    "page_ids": [],
                    "app": "on",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                },
                "social_chat_assign_me_revoke": {
                    "app": "on",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                },
                "social_comment_assign_me_revoke": {
                    "app": "on",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                }
            }
        },
        {
            "group": "ticket",
            "config": {
                "ticket_result_upload": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                },
                "ticket_alert_max_ticket_not_assign": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "ticket_assign_me":{
                    "permission": "owner_supporter",
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "ticket_remove_me":{
                    "permission": "owner_supporter",
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
            }
        },
        {
            "group": "note_work",
            "config": {
                "work_assign_me": {
                    "app": "off",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                },
                "note_mention_me": {
                    "app": "off",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                },
                "work_mention_me": {
                    "app": "off",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                }
            }
        },
        {
            "group": "loyalty",
            "config": {
                "loyalty_result_upload_card": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                }
            }
        },
        {
            "group": "voucher",
            "config": {
                "voucher_code_empty":{
                    "value": 20,
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "voucher_create_by_me_begin":{
                    "value": 1,
                    "unit": "day",
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "voucher_create_by_me_end":{
                    "value": 1,
                    "unit": "day",
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "voucher_result_upload_code": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                }
            }
        },
        {
            "group": "survey",
            "config": {
                "survey_create_by_me_end": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "survey_create_by_other_end": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "survey_result":{
                    "value": 30,
                    "unit": "day",
                    "times": 1,
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                }
            }
        },
        {
            "group": "admin",
            "config": {
                "admin_integrate_email": {
                    "app": "off",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                }
            }
        }
    ]

}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  
}
"""


"""
@api {GET} /api/v2.1/account/setting/notify Lấy thông tin cấu hình thông báo nhóm chức năng 
@apiDescription  Lấy thông tin một account 
@apiVersion 1.0.0
@apiGroup Setting
@apiName SettingGetConfigNotify
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Query:)  {String} account_id account_id cần lấy thông tin 

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
        {
            "group": "cdp",
            "config": {
                "cdp_result_upload_profile": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                },
                "cdp_result_upload_company": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                }
            }
        },
        {
            "group": "jb",
            "config": {
                "jb_result": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "jb_estimate_target_size": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "jb_create_by_me_begin":{
                    "value": 1,
                    "unit": "day" // minute, hour, day, month,
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                },
                "jb_create_by_me_end":{
                    "value": 1,
                    "unit": "day" // minute, hour, day, month,
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
                "jb_pending_approval":{
                    "value": 1,
                    "unit": "day" // minute, hour, day, month,
                    "times": 1
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
                "jb_result_approval": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
            }
        },
        {
            "group": "sale",
            "config": {
                "sale_result_upload_deal": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                }
                "sale_alert_max_deal_not_assign": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
                "sale_deal_assign_me":{
                    "permission": "owner_supporter" // "owner_supporter", "owner", "supporter"
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
                "sale_deal_revoke":{
                    "permission": "owner_supporter" // "owner_supporter", "owner", "supporter"
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
                "sale_deal_remove_me":{
                    "permission": "owner_supporter" // "owner_supporter", "owner", "supporter"
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
            }
        },
        {
            "group": "social",
            "config": {
                "social_alert_max_chat_not_assign": {
                    "app": "on",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                }
                "social_chat_assign_me": {
                    "ringtone_web": "on",
                    "all_page": "on",
                    "page_ids": [],
                    "app": "on",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                }
                "social_comment_assign_me": {
                    "ringtone_web": "on",
                    "all_page": "on",
                    "page_ids": [],
                    "app": "on",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                }
                "social_chat_assign_me_revoke": {
                    "app": "on",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                }
                "social_comment_assign_me_revoke": {
                    "app": "on",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                }
            }
        },
        {
            "group": "ticket",
            "config": {
                "ticket_result_upload": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                }
                "ticket_alert_max_ticket_not_assign": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
                "ticket_assign_me":{
                    "permission": "owner_supporter" // "owner_supporter", "owner", "supporter"
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
                "ticket_remove_me":{
                    "permission": "owner_supporter" // "owner_supporter", "owner", "supporter"
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
            }
        },
        {
            "group": "note_work",
            "config": {
                "work_assign_me": {
                    "app": "off",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                }
                "note_mention_me": {
                    "app": "off",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                }
                "work_mention_me": {
                    "app": "off",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                }
            }
        },
        {
            "group": "loyalty",
            "config": {
                "loyalty_result_upload_card": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                }
            }
        },
        {
            "group": "voucher",
            "config": {
                "voucher_code_empty":{
                    "value": 20
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
                "voucher_create_by_me_begin":{
                    "value": 1,
                    "unit": "day" // minute, hour, day, month,
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
                "voucher_create_by_me_end":{
                    "value": 1,
                    "unit": "day" // minute, hour, day, month,
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
                "voucher_result_upload_code": {
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                }
            }
        },
        {
            "group": "survey",
            "config": {
                "survey_create_by_me_end": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
                "survey_create_by_other_end": {
                    "app": "off",
                    "web": "on",
                    "email": "on",
                    "browser": "on",
                }
                "survey_result":{
                    "value": 30,
                    "unit": "day" // minute, hour, day, month,
                    "times": 1
                    "app": "off",
                    "web": "off",
                    "email": "on",
                    "browser": "off",
                }
            }
        },
        {
            "group": "admin",
            "config": {
                "admin_integrate_email": {
                    "app": "off",
                    "web": "on",
                    "email": "off",
                    "browser": "on",
                }
            }
        }
    ]
  
}
"""


"""
@api {GET} /api/v2.1/account/setting/social-filter Lấy thông tin bộ lọc mạng xã hội 
@apiDescription  Lấy thông tin bộ lọc mạng xã hội 
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountGetSocialFilter
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParam   (Query:)    {String}  account_id         id tài khoản


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
		{
			"setting": [
				{
					"assignee": null,
					"feature_code": 2,
					"page_social_ids": null,
					"reply_status": null,
					"sort_type": null,
					"status": null,
					"tags": null
				},
				{
					"assignee": null,
					"feature_code": 3,
					"page_social_ids": null,
					"reply_status": null,
					"sort_type": null,
					"status": null,
					"tags": null
				},
				{
					"assignee": null,
					"feature_code": 4,
					"page_social_ids": null,
					"reply_status": null,
					"sort_type": null,
					"status": null,
					"tags": null
				}
			],
			"social_type": 1
		},
		{
			"setting": [
				{
					"assignee": null,
					"feature_code": 2,
					"page_social_ids": null,
					"reply_status": null,
					"sort_type": null,
					"status": null,
					"tags": null
				}
			],
			"social_type": 2
		},
		{
			"setting": [
				{
					"assignee": null,
					"feature_code": 3,
					"page_social_ids": null,
					"reply_status": null,
					"sort_type": null,
					"status": null,
					"tags": null
				}
			],
			"social_type": 3
		},
		{
			"setting": [
				{
					"assignee": null,
					"feature_code": 4,
					"page_social_ids": null,
					"reply_status": null,
					"sort_type": null,
					"status": null,
					"tags": null
				}
			],
			"social_type": 4
		},
		{
			"setting": [
				{
					"assignee": null,
					"feature_code": 2,
					"page_social_ids": null,
					"reply_status": null,
					"sort_type": null,
					"status": null,
					"tags": null
				}
			],
			"social_type": 6
		},
		{
			"setting": [
				{
					"assignee": null,
					"feature_code": 2,
					"page_social_ids": null,
					"reply_status": null,
					"sort_type": null,
					"status": null,
					"tags": null
				}
			],
			"social_type": 7
		}
	],
}
"""


"""
@api {POST} /api/v2.1/account/setting/social-filter  Cập nhật bộ lọc mạng xã hội  
@apiDescription  Cập nhật bộ lọc mạng xã hội 
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountUpdateSocialFilter 

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} account_id id tài khoản
@apiParam (Body:) {array} social_filter  thông tin bộ lọc 


@apiParamExample {json} Body example
{
    "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "social_filter": [
        {
			"setting": [
				{
					"assignee": null,
					"feature_code": 2,
					"page_social_ids": null,
					"reply_status": null,
					"sort_type": null,
					"status": null,
					"tags": null
				}
			],
			"social_type": 7
		}
    ]
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""


"""
@api {GET} /api/v2.1/account/setting/collapse-expand  Lấy thông tin cấu hình thu gọn mở rộng giao diện 
@apiDescription  Lấy thông tin cấu hình thu gọn mở rộng giao diện 
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountGetCollapseExpand  
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "company_event": "collapse",
    "profile_event": "collapse",
    "sale_event": "expand",
    "ticket_event": "expand",
    "web_form": "collapse",
  }
}
"""


"""
@api {POST} /api/v2.1/account/setting/collapse-expand  Cập nhật cấu hình thu gọn mở rộng giao diện 
@apiDescription  Cập nhật cấu hình thu gọn mở rộng giao diện 
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountUpdateCollapseExpand  

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} [profile_event] cột 2 event profile 
@apiParam   (Body:) {String} [company_event] cột 2 event company 
@apiParam   (Body:) {String} [sale_event] cột 2 event sale  
@apiParam   (Body:) {String} [ticket_event] cột 2 event ticket  
@apiParam   (Body:) {String} [web_form] danh sách form 

@apiParamExample {json} Body example
{
    "company_event": "collapse",
    "profile_event": "collapse",
    "sale_event": "expand",
    "ticket_event": "expand",
    "web_form": "collapse",
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""


"""
@api {GET} /api/v2.1/account/config/field-filter Lấy thông tin field tìm kiếm của các module 
@apiDescription  nếu không có mặc định lấy của merchant 
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountGetFieldFilter 
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParam   (Query:)    {String}  account_id         id tài khoản

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "profile": ["name", "primary_email", "phone_number", "tags", "devices"],
  },
  
}
"""


"""
@api {POST} /api/v2.1/account/config/field-filter  Cập nhật cấu hình field tìm kiếm của các module  
@apiDescription  Cập nhật cấu hình theo từng tài khoản  
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountConfigFieldFilter 

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} account_id id tài khoản
@apiParam (Body:) {object} config_field_filter  thông tin cấu hình các module 


@apiParamExample {json} Body example
{
    "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "config_field_filter":{
        "profile": ["name", "primary_email", "phone_number", "tags", "devices"],
        
      }
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""


"""
@api {GET} /api/v2.1/account/config/order-column3  Lấy cấu hình thứ tự hiển thị các module ở cột 3  
@apiDescription  Lấy thông tin theo account nếu chưa có thì trả về cấu hình mặc định  
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountGetOrderModule   
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "company": {
            "config": "collapse",   // expand
            "order": [
                "media","profile","sale","ticket","contact","partner"
            ],
            "data": ["media","profile","sale","ticket","contact","partner"]
        },
        ...
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {POST} /api/v2.1/account/config/order-column3  Cập nhật cấu hình thứ tự hiển thị các module ở cột 3  
@apiDescription  cập nhật cho account đang đăng nhập  
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountUpdateOrderModule  

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParamExample {json} Body example
{
    "company": {
        "config": "collapse",
        "order": ["media","profile","sale","ticket","contact","partner"]
    }
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""



"""
@api {GET} /api/v2.1/account/config/order-column2  Lấy cấu hình thứ tự hiển thị các module ở cột 2  
@apiDescription  Lấy thông tin theo account nếu chưa có thì trả về cấu hình mặc định  
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountGetOrderModuleColumn2   
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header



@apiSuccess (data) {String} event event 
@apiSuccess (data) {String} detail thông tin chi tiết(cơ hội bán, profile, ... )
@apiSuccess (data) {String} note ghi chú 
@apiSuccess (data) {String} task công việc 
@apiSuccess (data) {String} staff nhân viên phụ trách 
@apiSuccess (data) {String} profile khách hàng 
@apiSuccess (data) {String} product sản phẩm 
@apiSuccess (data) {String} media media 

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "sale": {
            "data": ["event", "detail", "note", "task", "staff", "profile", "product", "media"],
            "default": ["event", "detail", "note", "task", "staff"],
            "order": ["event", "detail", "note", "task", "staff"]
        },
        ...
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {POST} /api/v2.1/account/config/order-column2  Cập nhật cấu hình thứ tự hiển thị các module ở cột 2  
@apiDescription  cập nhật cho account đang đăng nhập  
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountUpdateOrderModuleColumn2   

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParamExample {json} Body example
{
    "sale": {
        "order": ["event", "detail", "note", "task", "staff"]
    },
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

"""
@api {POST} /api/v2.1/account/product-line/last-apply  Cập nhật dòng sản phẩm tài khoản chọn  
@apiDescription  cập nhật cho account đang đăng nhập  
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountChooseProduct   

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParamExample {json} Body example
{
    "product_apply": "activation"     // activation, operation, analytics
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

"""
@api {POST} /api/v2.1/account/product-line/by-ids  lấy thông tin sản phẩm của tài khoản  
@apiDescription  truyền danh sách tài khoản, tối đa 100 id 
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountGetProduct   

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParamExample {json} Body example
{
    "ids":["9c707704-a9dd-4fde-afe3-1ff1715b815d"]
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
        {
            "account_id": "9c707704-a9dd-4fde-afe3-1ff1715b815d",
            "last_apply": "operation",
            "product_apply": [
                "operation",
                "activation"
            ],
            "product_line": [
                "operation_sale",
                "activation",
                "operation_service"
            ]
        }
    ],
}
"""



"""
@api {GET} /api/v2.1/account/setting/notify/detail  danh sách cấu hình thông báo của tài khoản  
@apiDescription  Lấy thông tin một account 
@apiVersion 1.0.0
@apiGroup Setting
@apiName SettingGetConfigNotifyAccount 
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Query:)  {String} account_id account_id cần lấy thông tin 

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
        {
            "group": "cdp",
            "config": [
                {
                    "key": "cdp_result_upload_profile",
                    "label": "Kết quả xử lý file Profiles được tải lên hệ thống bởi tôi",
                    "setting": {
                        "app": "off",
                        "web": "off",
                        "email": "on",
                        "browser": "off",
                    },
                    "tooltip": "",
                }
            ]
        },
    ]
  
}
"""



"""
@api {GET} /api/v2.1/account/config/mobile  Lấy cấu hình hiển thị chức năng trên mobile  
@apiDescription  Lấy thông tin theo account
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountGetMobileConfig    
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "home_screen_function": {
            "value": ["sale", "profile", "sale_report", "product", "task", "check_los", "check_cic"],
            "all": ["sale", "profile", "sale_report", "product", "task", "check_los", "check_cic", "company", "kpi", "sale_tip"]
        },
        
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {POST} /api/v2.1/account/config/mobile  Cập nhật cấu hình hiển thị chức năng trên mobile  
@apiDescription  cập nhật cho account đang đăng nhập  
@apiVersion 2.1.0
@apiGroup Setting
@apiName AccountUpdateMobileConfig  

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParamExample {json} Body example
{
    "home_screen_function": ["sale", "profile", "sale_report", "product", "task", "check_los", "check_cic"]
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""
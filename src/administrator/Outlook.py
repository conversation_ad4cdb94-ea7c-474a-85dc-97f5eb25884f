#!/usr/bin/python
# -*- coding: utf8 -*-

# ============================================
# OLDEmailServiceAddEmail
# ============================================

"""
@api {post} /api/v2.1/outlook/connect kết nối đến outlook
@apiDescription kết nối đến outlook
@apiGroup Outlook
@apiVersion  2.1.0
@apiName OutlookConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body)   {String}   staff_id    Mã nhân viên 
@apiParam   (Body)   {String}   link_current_page     đường dẫn callback sau khi kết nối thành công 
@apiParam   (Body)   {String}   source    nguồn thực hiện kết nối: "mail", "calendar" 

@apiParamExample {json} Body
{
    "staff_id":"1111d995-9c64-4794-9e44-2b7e243e1111",
    "link_current_page": "https://test1.mobio.vn/account/setting/basic",
    "source": "mail"
}

@apiSuccess     {json} data response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": "https://login.microsoftonline.com"     // chuyển hướng tới đường dẫn này 
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}
"""
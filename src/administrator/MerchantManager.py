#!/usr/bin/python
# -*- coding: utf8 -*-

************************************************** API LẤY THÔNG TIN TENANT **************************************************
* version: 1.0.0                                                                                                             *
******************************************************************************************************************************
"""
@api {get} /api/v2.1/merchants/<merchant_id> Lấy thông tin tenant
@apiDescription Dịch vụ lấy thông tin tenant
@apiGroup MerchantModule
@apiVersion 1.0.0
@apiName DetailMerchant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiParam   (Query:)  {String}  merchant_id   Định danh của tenant cần lấy thông tin.

@apiSuccess   (merchant)  {String}  id    Định danh của tenant
@apiSuccess   (merchant)  {String}  code  Mã của tenant
@apiSuccess   (merchant)  {String}  name  Tên.
@apiSuccess   (merchant)  {String}  avatar  Link ảnh đại diện tenant
@apiSuccess   (merchant)  {String}  avatar_collapse  Link ảnh đại diện rút gọn của tenant

@apiSuccess   (register_info)  {Object}  register_info  Thông tin đăng ký tenant

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": {
        "merchant": {
            "address": "Số 23, ngõ 37/2, phố Dịch Vọng , quận Cầu Giấy, Hà Nội.",
            "avatar": "https://storage.googleapis.com/mobio-test/images/nhacungcap/1b99bdcf-d582-4f49-9715-1b61dfff3924?**************",
            "avatar_collapse": "https://api-test1.mobio.vn/adm/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/merchant/avatar_1b99bdcf-d582-4f49-9715-1b61dfff392420210629041959.jpg"
            "code": "PINGCOMSHOP",
            "created_time": "2016-10-18T08:56:52Z",
            "description": null,
            "email": "",
            "id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "PingcomShop",
            "phone_number": "",
            "updated_time": "2019-08-28T11:40:31Z"
        },
        "register_info": {
            "address": "Số 23, ngõ 37/2, phố Dịch Vọng , quận Cầu Giấy, Hà Nội.",
            "email": "",
            "phone_number": ""
        }
    }
}
"""

************************************************** API LẤY DANH SÁCH TENANT **************************************************
* version: 1.0.0                                                                                                             *
******************************************************************************************************************************
"""
@api {get} /api/v2.1/merchants/<merchant_id>/related Lấy danh sách tenant
@apiDescription Dịch vụ lấy danh sách teant có liên quan tới 1 tenant.
@apiGroup MerchantModule
@apiVersion 1.0.0
@apiName ListMerchantRelated

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse paging
@apiUse order_sort

@apiSuccess   (data)  {String}  id  Định danh của tenant
@apiSuccess   (data)  {String}  name  Tên của tenant
@apiSuccess   (data)  {String}  avatar  Link ảnh đại diện của tenant
@apiSuccess   (data)  {Boolean=true,false}  is_brand  <code>true</code> nếu tenant này là Brand, <code>false</code> nếu là sub-brand.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "10e94af4-c80f-45cf-b338-c2ef85403545",
      "name": "ABC",
      "avatar": "https://avatar.link",
      "is_brand": true
    },
    {
      "id": "d3166176-69b4-497f-97e2-880ead31d1ea",
      "name": "Sub 1",
      "avatar": "https://avatar.link",
      "is_brand": false
    },
    {
      "id": "92a92647-9425-4fd9-9529-f652898af4f7",
      "name": "Sub 2",
      "avatar": "https://avatar.link",
      "is_brand": false
    },
    {
      "id": "c9fa72cb-d771-4ebb-9b1b-88bc765acb35",
      "name": "Sub 3",
      "avatar": "https://avatar.link",
      "is_brand": false
    }
  ]
}
"""

************************************************** API LẤY DANH SÁCH MODULE THEO MERCHANT **************************************************
* version: 1.0.0                                                                                                                           *
********************************************************************************************************************************************
"""
@api {get} /api/v2.1/merchants/<merchant_id>/modules Lấy danh sách module theo merchant
@apiDescription Lấy danh sách module theo merchant
@apiVersion 1.0.0
@apiGroup MerchantModule
@apiName Get

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort

@apiSuccess  {string} merchant_id ID nhãn hàng
@apiSuccess (data) {string} module_id ID module được gán cho nhãn hàng
@apiSuccess (data) {string} name Tên module được gán cho nhãn hàng
@apiSuccess (data) {string} description Mô tả module
@apiSuccess (data) {boolean} is_checked Trạng thái được module được gán cho merchant
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "a83269d2-9a9f-403c-8afe-a9d3cb4fb1f4",
  "data": [
    {
      "module_id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
      "name": "Quản lý nội dung",
      "description": "Quản lý nội dung chương trình",
      "is_checked": true
    },
    ...
  ],
  "lang": "vi",
  "sort": "name",
  "order": "asc",
  "paging": {
    ...
  }
}
"""

************************************************** API GÁN MODULE CHO MERCHANT **************************************************
* version: 1.0.0                                                                                                                *
*********************************************************************************************************************************
"""
@api {put} /api/v2.1/merchants/<merchant_id>/modules Gán module cho merchant
@apiDescription Gán module cho merchant
@apiVersion 1.0.0
@apiGroup MerchantModule
@apiName Put

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {Array} module_ids Danh sách module id muốn gán.
@apiParamExample {json} Body
{
    "module_ids": [
        "26ebae9c-ec2e-47c4-ba09-4921f3b512e9",
        "e5de4b87-05e4-4111-8ccc-134f6e57c692",
        "8086bbd2-0ee8-4f90-b4b8-7f3f067d5527"
    ]
}

@apiSuccess  {Array} module_ids Danh sách module id được gán thành công.
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "module_ids": [
        "26ebae9c-ec2e-47c4-ba09-4921f3b512e9",
        "e5de4b87-05e4-4111-8ccc-134f6e57c692",
        "8086bbd2-0ee8-4f90-b4b8-7f3f067d5527"
    ]
}
"""


************************************************** API LẤY DANH SÁCH SUB BRAND **************************************************
* version: 1.0.1                                                                                                                *
*********************************************************************************************************************************
"""
@api {get} /api/v2.1/sub-brands Lấy danh sách subbrand của merchant
@apiDescription Lấy danh sách subbrand của merchant
@apiVersion 1.0.1
@apiGroup MerchantModule
@apiName GetMerchantSubBrand

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Params:) {string} merchant_id UUID merchant muốn lấy danh sách brands

@apiSuccess  {ArrayObject} data Danh sách sub-brand
@apiSuccess (data) {string} id UUID sub-brand
@apiSuccess (data) {string} name Tên sub-brand
@apiSuccess (data) {string} avatar Ảnh đại diện sub-brand
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "id": "14539533-3b4f-4dfc-a863-a2087280be9b",
      "name": "Brand 1",
      'avatar': 'https://.....'
    },
    {
      "id": "14539533-3b4f-4dfc-a863-a2087280be9b",
      "name": "Brand 2",
      'avatar': 'https://.....'
    },
    ...
  ],
  "mesage": "Requests sucessfull"
}
"""
*****************************
"""
@api {get} /api/v2.1/sub-brands Lấy danh sách subbrand của merchant
@apiDescription Lấy danh sách subbrand của merchant
@apiVersion 1.0.0
@apiGroup MerchantModule
@apiName GetMerchantSubBrand

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Params:) {string} merchant_id UUID merchant muốn lấy danh sách brands

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "id": "14539533-3b4f-4dfc-a863-a2087280be9b",
      "name": "Brand 1"
    },
    {
      "id": "14539533-3b4f-4dfc-a863-a2087280be9b",
      "name": "Brand 1"
    },
    ...
  ],
  "mesage": "Requests sucessfull"
}
"""


************************************************** API LẤY DANH SÁCH SUB BRAND **************************************************
* version: 1.0.1                                                                                                                *
*********************************************************************************************************************************
"""
@api {get} /api/v2.1/merchants/<merchant_id>/parent Lấy danh sách parent của merchant
@apiDescription Lấy danh sách parent của merchant
@apiVersion 1.0.1
@apiGroup MerchantModule
@apiName GetParentMerchant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Params:) {string} merchant_id UUID merchant muốn lấy danh sách parent merchant

@apiSuccess  {ArrayObject} data Danh sách sub-brand
@apiSuccess (data) {string} id UUID sub-brand
@apiSuccess (data) {string} name Tên sub-brand
@apiSuccess (data) {string} avatar Ảnh đại diện sub-brand
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "id": "14539533-3b4f-4dfc-a863-a2087280be9b",
      "name": "Brand 1",
      'avatar': 'https://.....'
    },
    {
      "id": "14539533-3b4f-4dfc-a863-a2087280be9b",
      "name": "Brand 2",
      'avatar': 'https://.....'
    },
    ...
  ],
  "mesage": "Requests sucessfull"
}
"""


************************************************** API CẬP NHẬP SUB BRAND **************************************************
* version: 1.0.1                                                                                                                *
*********************************************************************************************************************************
"""
@api {put} /api/v2.1/merchants/sub-brands/<sub_brand_id>/update Cập nhập thông tin subbrand
@apiDescription Cập nhập thông tin subbrand
@apiVersion 1.0.1
@apiGroup MerchantModule
@apiName UpdateSubBrand

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam  {FromData} info Thông tin của sub-brand cần cập nhập. 
@apiParam  {File} avatar Ảnh của merchant
@apiParam  {File} avatar_collapse Ảnh rút gọn của merchant

@apiSuccess  {ArrayObject} data Danh sách sub-brand
@apiSuccess (data) {string} id UUID sub-brand
@apiSuccess (data) {string} name Tên sub-brand
@apiSuccess (data) {string} avatar Ảnh đại diện sub-brand

@apiParamExample {json} FromData
{
  "id": "8325d157-6bb1-4bf8-8bb7-abfa9034f9bb",
  "name": "testmoi22",
  "description": "tạch tạch tạch",
  "email": "",
  "phone_number": "",
  "module_ids": [
    "f0887888-70c7-446b-b096-cdf67b1d3b12",
    "f96e9719-e1cb-415f-802c-e8cef07c0723"
  ],
  "status": 2,
  "account": {
    "username": "test@testmoi22",
    "fullname": "test22",
    "email": "<EMAIL>",
    "phone_number": "+***********"
  }
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "account": {
      "email": "<EMAIL>",
      "fullname": "test22",
      "id": "6aa7f68e-55f8-41f9-ae36-2b02609435d0",
      "phone_number": "+***********",
      "username": "test@testmoi22"
    },
    "avatar": "https://api-test1.mobio.vn/adm/static/8325d157-6bb1-4bf8-8bb7-abfa9034f9bb/merchant/avatar_8325d157-6bb1-4bf8-8bb7-abfa9034f9bb20210629024631.jpg",
    "avatar_collapse": "uri image"
    "description": "tạch tạch tạch",
    "email": "",
    "id": "8325d157-6bb1-4bf8-8bb7-abfa9034f9bb",
    "modules": []
      {
        "id": "f0887888-70c7-446b-b096-cdf67b1d3b12",
        "name": "PROFILING"
      },
      {
        "id": "f96e9719-e1cb-415f-802c-e8cef07c0723",
        "name": "TICKET"
      },
      .....
    ],
    "name": "testmoi22",
    "phone_number": "",
    "status": 2,
    "total_module": 26
  },
  "lang": "vi",
  "message": "request thành công."
}
"""
************************************************** API CẬP NHẬP THÔNG TIN TENANT *********************************************
* version: 1.0.0                                                                                                             *
******************************************************************************************************************************
"""
@api {PATCH} /api/v2.1/merchants/<merchant_id> Cập nhập thông tin tenant
@apiDescription Cập nhập thông tin tenant
@apiGroup MerchantModule
@apiVersion 1.0.0
@apiName UpdateTenant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiParam   (Query:)  {String}  merchant_id   Định danh của tenant cần lấy thông tin.

@apiSuccess    {String}  id    Định danh của tenant
@apiSuccess    {String}  code  Mã của tenant
@apiSuccess    {String}  name  Tên.
@apiSuccess    {String}  avatar  Link ảnh đại diện tenant
@apiSuccess    {String}  avatar_collapse  Link ảnh đại diện rút gọn của tenant

@apiParamExample {json} Body
{
  "description": "<p>ABCfwa</p>\n\n<p><strong>fwa</strong></p>",
  "phone_number": "0946578433",
  "email": "<EMAIL>",
  "avatar": :"url avatar",
  "avatar_collapse": "url avatar collapse",
  
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "address": "Số 23, ngõ 37/2, phố Dịch Vọng , quận Cầu Giấy, Hà Nội.",
  "avatar": "https://api-test1.mobio.vn/adm/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/merchant/avatar_1b99bdcf-d582-4f49-9715-1b61dfff392420210629041959.jpg",
  "avatar_collapse": null,
  "code": "PINGCOMSHOP",
  "created_time": "2016-10-18T08:56:52Z",
  "description": "<p>ABCfwa</p>\n\n<p><strong>fwa</strong></p>",
  "email": "<EMAIL>",
  "external_merchant_id": null,
  "id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "lang": "vi",
  "message": "request thành công.",
  "name": "PingcomShop",
  "phone_number": "0946578433",
  "source": null,
  "status": 2,
  "updated_time": "2019-12-26T06:44:57Z"
}
"""

************************************************** API LẤY DANH SÁCH CHI TIẾT SUB BRAND *****************************************
* version: 1.0.1                                                                                                                *
*********************************************************************************************************************************
"""
@api {get} /api/v2.1/merchants/sub-brands Lấy danh sách subbrand chi tiết của merchant
@apiDescription Lấy danh sách subbrand chi tiết của merchant
@apiVersion 1.0.1
@apiGroup MerchantModule
@apiName GetMerchantSubBrandList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort


@apiParam (Query:) {string} search Tìm kiếm theo tên merchant

@apiSuccess  {ArrayObject} data Danh sách sub-brand
@apiSuccess (data) {string} id UUID sub-brand
@apiSuccess (data) {string} name Tên sub-brand
@apiSuccess (data) {string} avatar Ảnh đại diện sub-brand
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "account": {
        "email": "<EMAIL>",
        "fullname": "van cuong",
        "id": "f5663709-1dac-4074-9673-ed4f26a2fd63",
        "phone_number": "+***********",
        "username": "cuongtesttest@testabccuong"
      },
      "avatar": "https://api-test1.mobio.vn/adm/static/7c6fe414-**************-1b581cdee2c4/merchant/avatar_7c6fe414-**************-1b581cdee2c420210306031714.png",
      "avatar_collapse": null,
      "description": null,
      "email": "<EMAIL>",
      "id": "7c6fe414-**************-1b581cdee2c4",
      "modules": [
        {
          "id": "1c3c87ce-61aa-40f1-a0d2-52c6d4816105",
          "name": "ACCOUNT"
        },
        {
          "id": "244a86fa-73da-4aa8-8606-5f7192f452c0",
          "name": "VOUCHER"
        },
        .....
      ],
      "name": "testabccuong",
      "phone_number": "",
      "status": 3,
      "total_module": 8
    }
  ],
  "lang": "vi",
  "merchant": {
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "total_module": 26
  },
  "message": "request thành công.",
  "paging": {
    "page": "1",
    "page_count": 1,
    "per_page": 15,
    "total_count": 1
  }
}
"""

************************************************** API LẤY DANH SÁCH MODULE BASE VÀ TRẠNG THÁI QUAN HỆ VỚI MODULE TỰ TẠO*********
* version: 2.1.0                                                                                                   *
*********************************************************************************************************************************
"""
@api {get} /api/v2.1/merchants/module/<module_id>/relationship/base-brand lấy danh sách module base và trạng thái quan hệ với module tự tạo
@apiDescription lấy danh sách module base và trạng thái quan hệ với module tự tạo
@apiVersion 2.1.0
@apiGroup MerchantModule
@apiName GetMerchantSubBrandRelationshipList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiParam (Query:) {string} [merchant_id] merchant id cuả module <code> Không truyền lên thì sẽ lấy theo X-Merchant-ID . 

@apiSuccess (data) {string} id UUID của module base <code>all_module là trường hợp đặc biệt thể hiện trạng thái tích tất cả các module mặc định</code>
@apiSuccess (data) {string} check_status  Nếu bằng True thì module tự tạo có quyền thuộc module mặc định
@apiSuccess (data) {string} name Module mặc định
@apiSuccess (data) {string} code Code parent của module mặc định


@apiParamExample {json} Body
{
  "description": "<p>ABCfwa</p>\n\n<p><strong>fwa</strong></p>",
  "phone_number": "0946578433",
  "email": "<EMAIL>",
  "avatar": :"url avatar",
  "avatar_collapse": "url avatar collapse"

}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "id": "all_module",
      "check_status": False,
      "name": "Chọn tất cả" 
      "code": "Chọn tất cả"
    },
    {
      "id": "id_module",
      "check_status": True,
      "name": "CALL_CENTER",
      "code": "CALL_CENTER"
    },
    .....
  ]
}
"""

************************************************** API SỬA TYPE CHO MERCHANT **********
* version: 2.1.0                                                                      *
***************************************************************************************
"""
@api {POST} /api/v2.1/merchants/action/update/type Sửa type cho merchant 
@apiDescription Sửa type cho merchant. Type sẽ quyết định xem merchant có được hiển thị một số thông tin trên web
@apiVersion 2.1.0
@apiGroup MerchantModule
@apiName AddTypeMerchant 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiParam  {body} type type của merchant <code> các giá trị cho phép hiện tại: "BANK", "SAMSUNG", "SAKUKO", "SAPPORO", "RETAIL"</code>
@apiParam  {body} merchant_ids danh sach thêm nhóm quyền của bank 
@apiParamExample {json} Body
{
  "type": ["BANK", "SAMSUNG"],
  "merchant_ids": ["merchant_id1", "merchant_ids2" ]

}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/merchants/config/menu Lấy thông tin cấu hình menu
@apiDescription  Lấy thông tin cấu hình menu, thông tin product line của account 
@apiVersion 2.1.0
@apiGroup MerchantModule
@apiName GetConfigMenu
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccess {object} config_menu  thông tin cấu hình menu

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "config_menu": [],
    "operation": [],
    "activation": [],
    "analytics": [],
    "product_apply": ["analytics", "activation", "operation"],
    "last_apply": "analytics",
  },
  
}
"""

"""
@api {POST} /api/v2.1/merchants/config/menu Cập nhật cấu hình menu
@apiDescription  Cập nhật cấu hình menu
@apiVersion 2.1.0
@apiGroup MerchantModule
@apiName AddConfigMenu

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} merchant_id ID merchant
@apiParam   (Body:) {object} config_menu thông tin cấu hình menu

@apiParamExample {json} Body example
{
  "merchant_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
	"config_menu": {
		...
	}
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""


"""
@api {GET} /api/v2.1/merchants/config/tab_view Lấy thông tin cấu tab hiển thị 
@apiDescription  tab hiển thị trong chi tiết profile, company, sale, ticket 
@apiVersion 2.1.0
@apiGroup MerchantModule
@apiName GetConfigTabView
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccess {Array} profile  thông tin cấu hình profile
@apiSuccess {Array} company  thông tin cấu hình company
@apiSuccess {Array} sale  thông tin cấu hình sale
@apiSuccess {Array} ticket  thông tin cấu hình ticket

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "profile": ["work", "callcenter", "email", "event", "note", "history_point", "history_transaction", "social", "sms"],
    "company": ["work", "callcenter", "email", "event", "note", "social", "sms"],
    "sale": ["work", "callcenter", "email", "event", "note", "social", "sms"],
    "ticket": ["work", "callcenter", "email", "event", "note", "social", "sms"],
  },
  
}
"""


"""
@api {POST} /api/v2.1/merchants/config/tab_view  Cập nhật cấu hình tab view
@apiDescription  Cập nhật cấu hình tab view
@apiVersion 2.1.0
@apiGroup MerchantModule
@apiName AddConfigTabView

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} merchant_id ID merchant
@apiParam (Body:) {object} tab_view  thông tin cấu hình tab view

@apiParamExample {json} Body example
{
  "merchant_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "tab_view":{
    "profile": ["work", "callcenter", "email", "event", "note", "history_point", "history_transaction", "social", "sms"],
    "company": ["work", "callcenter", "email", "event", "note", "social", "sms"],
    "sale": ["work", "callcenter", "email", "event", "note", "social", "sms"],
    "ticket": ["work", "callcenter", "email", "event", "note", "social", "sms"],
  }
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""


"""
@api {POST} /api/v2.1/merchants/config/menu/pick-purge Cập nhật menu cho nhiều merchant 
@apiDescription  cho phép thêm hoặc xóa menu của nhiều merchant 
@apiVersion 2.1.0
@apiGroup MerchantModule
@apiName AddRemoveConfigMenu

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {array} pick danh sách menu thêm vào merchant 
@apiParam   (Body:) {array} purge danh sách menu xóa khỏi merchant 

@apiParamExample {json} Body example
{
  "pick": [
    {
      "menu_id": "9da6de70-8a26-11ec-a8a3-0242ac120001",
      "merchant_ids": ["b758d486-4133-11ec-973a-0242ac130003"]
    }, ...
  ],
	"purge": [
    {
      "menu_id": "9da6de70-8a26-11ec-a8a3-0242ac120001",
      "merchant_ids": ["b758d486-4133-11ec-973a-0242ac130003"]
    }, ...
  ],
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""


"""
@api {GET} /api/v2.1/merchants/config/menu/related Lấy danh sách merchant cho cấu hình menu  
@apiDescription  bao gồm cả merchant cha và con 
@apiVersion 2.1.0
@apiGroup MerchantModule
@apiName GetALLMerchantTree
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "code": "DASHBOARD",
      "id": "03137674-117b-406f-a830-5fdfbbd69cb4",
      "name": "Dashboard",
      "parent_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "root_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924"
    }, {
      "code": "PHANNGUYEN",
      "id": "03d4e6e6-be7e-4442-b5ae-7fb25cce2cab",
      "name": "PHAN NGUY\u1ec4N",
      "parent_id": "",
      "root_id": ""
    }
  ]
}
"""

************************************************** API LẤY DANH SÁCH THÔNG TIN MERCHANT_IDS *****************************************
* version: 1.0.0                                                                                                    *
*********************************************************************************************************************
"""
@api {get} /api/v2.1/merchants Lấy danh sách thông tin merchant
@apiDescription  Lấy danh sách thông tin merchant
@apiGroup MerchantModule
@apiVersion 1.0.0
@apiName ListMerchant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)  {String}  search         Tìm kiếm theo mã code và tên.
@apiParam   (Query:)  {String}  merchant_ids   Lấy thông tin từ danh sách merchants gửi lên, các id cách nhau bằng dấu phẩy (,).

@apiSuccess   (merchant)  {String}  id    Định danh của tenant
@apiSuccess   (merchant)  {String}  code  Mã của tenant
@apiSuccess   (merchant)  {String}  name  Tên.
@apiSuccess   (merchant)  {String}  avatar  Link ảnh đại diện tenant
@apiSuccess   (merchant)  {String}  avatar_collapse  Link ảnh đại diện rút gọn của tenant

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "merchants": [
        {
            "address": null,
            "avatar": "http://storage.googleapis.com/test_xloyalty1/images/nhacungcap/a2db5304-fbd4-4a9a-9d32-c9e424395a10",
            "avatar_collapse": null,
            "code": "DXMAXVAGIAYDEPHAANH",
            "created_time": "2016-08-11T06:48:36Z",
            "description": "<div style=\"text-align: justify; \"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\">1. SĐT: (08)62677987</span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\"><br></span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\">2. Email: <EMAIL></span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\"><br></span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\">3. Website/ Fanpage:</span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\">https://www.facebook.com/GiayHaAnh.ThoiTrang</span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\">http://giayhaanh.com</span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\"><br></span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\">4. Giới thiệu chung:</span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\">Ra đời năm 2012, Giày Hà Anh bắt đầu từ việc thiết kế và phân phối các sản phẩm giầy dép thời trang cho Nam và Nữ khắp cả nước. Với tiêu chí đơn giản và tinh tế trong thiết kế, chất lượng sản phẩm cao và dịch vụ chăm sóc khách hàng hoàn hảo, Giày Hà Anh đã gắn bó với khách hàng từ những ngày đầu thành lập, và luôn nhận được sự ủng hộ từ phía quý khách hàng.</span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\"><br></span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\">Bên cạnh đó, ngoài những sản phẩm truyền thống, giày Hà Anh còn tiếp tục cho ra đời những sản phẩm luôn dẫn đầu xu hướng thời trang nhằm thoả mãn nhu cầu của quý khách hàng.</span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\"><br></span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\">5. Địa chỉ:</span></div><div style=\"text-align: justify;\"><span style=\"font-family: Calibri; line-height: normal; white-space: pre-wrap;\">2A Đường Tự Do 1, P.Tân Thành, &nbsp;Q.Tân Phú , TP.Hồ Chí Minh</span></div>",
            "email": null,
            "external_merchant_id": null,
            "id": "a2db5304-fbd4-4a9a-9d32-c9e424395a10",
            "name": "DXMAX VÀ GIÀY DÉP HÀ ANH",
            "phone_number": "(08)62677987",
            "source": null,
            "status": 2,
            "type": null,
            "updated_time": "2016-09-09T00:58:19Z"
        }
    ],
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 1,
        "total_count": 2,
        "total_page": 2
    }
}
"""


"""
@api {GET} /api/v2.1/merchants/config/field-filter Lấy thông tin trường tìm kiếm tại danh sách các module theo merchant 
@apiDescription  các module: profile, company, sale, ticket 
@apiVersion 2.1.0
@apiGroup MerchantModule
@apiName GetConfigFieldFilter 
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccess {Array} profile  thông tin cấu hình profile
@apiSuccess {Array} company  thông tin cấu hình company
@apiSuccess {Array} sale  thông tin cấu hình sale
@apiSuccess {Array} ticket  thông tin cấu hình ticket

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "profile": ["name", "primary_email", "phone_number", "tags", "devices"],
    
  },
  
}
"""


"""
@api {POST} /api/v2.1/merchants/config/field-filter  Cập nhật cấu hình trường tìm kiếm theo merchant 
@apiDescription  sử dụng cho toolkit 
@apiVersion 2.1.0
@apiGroup MerchantModule
@apiName AddConfigFieldFilter 

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} merchant_id ID merchant
@apiParam (Body:) {object} config_field_filter  thông tin cấu hình các module 

@apiParamExample {json} Body example
{
  "merchant_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "config_field_filter":{
    "profile": ["name", "primary_email", "phone_number", "tags", "devices"],
    
  }
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""


"""
@api {get} /api/v2.1/merchants/<merchant_id>/detail Lấy thông tin merchant
@apiDescription Dịch vụ lấy thông tin tenant
@apiGroup MerchantModule
@apiVersion 1.0.0
@apiName DetailMerchantByID

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiParam   (Query:)  {String}  merchant_id   Định danh của tenant cần lấy thông tin.

@apiSuccess   (data)  {String}  id    Định danh của tenant
@apiSuccess   (data)  {String}  code  Mã của tenant
@apiSuccess   (data)  {String}  name  Tên.
@apiSuccess   (data)  {String}  avatar  Link ảnh đại diện tenant
@apiSuccess   (data)  {String}  avatar_collapse  Link ảnh đại diện rút gọn của tenant

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": {
        "address": "Số 23, ngõ 37/2, phố Dịch Vọng , quận Cầu Giấy, Hà Nội.",
        "avatar": "https://storage.googleapis.com/mobio-test/images/nhacungcap/1b99bdcf-d582-4f49-9715-1b61dfff3924?**************",
        "avatar_collapse": "https://api-test1.mobio.vn/adm/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/merchant/avatar_1b99bdcf-d582-4f49-9715-1b61dfff392420210629041959.jpg"
        "code": "PINGCOMSHOP",
        "created_time": "2016-10-18T08:56:52Z",
        "description": null,
        "email": "",
        "id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name": "PingcomShop",
        "phone_number": "",
        "updated_time": "2019-08-28T11:40:31Z",
        "type": "BANK"
    }
}
"""


"""
@api {get} /api/v2.1/merchants/config/theme Lấy thông tin cấu hình theme 
@apiDescription Dịch vụ lấy thông tin tenant
@apiGroup MerchantModule
@apiVersion 1.0.0
@apiName DetailMerchantTheme 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": {
    "color": "#20ad60"
  }
}
"""


"""
@api {POST} /api/v2.1/merchants/config/theme  Cập nhật cấu hình theme 
@apiDescription sau có thể bổ sung các thông tin liên quan 
@apiVersion 2.1.0
@apiGroup MerchantModule
@apiName UpdateMerchantTheme 

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} color màu sắc 

@apiParamExample {json} Body example
{
  "color": "#20ad60"
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""



"""
@api {get} /api/v2.1/merchants/detail/by-code Lấy thông tin merchant theo code
@apiDescription có thể tìm nhiều code 
@apiGroup MerchantModule
@apiVersion 1.0.0
@apiName DetailMerchantCodes  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {String}  codes   danh sách code cách nhau dấu ;

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "code": "HDB",
            "email": "",
            "id": "10350f12-1bd7-4369-9750-46d35c416a46",
            "name": "hdbank",
            "phone_number": null,
            "status": 2,
            "type": "BANK"
        },
        {
            "code": "MSB",
            "email": "",
            "id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "msb",
            "phone_number": null,
            "status": 2,
            "type": "BANK"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {get} /api/v2.1/merchants/config/filter-module danh sách field base theo module 
@apiDescription thông tin được cấu hình ở toolkits 
@apiGroup MerchantModule
@apiVersion 1.0.0
@apiName MerchantFilterFieldBase 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {String}  module_filter   danh sách module cách nhau dấu ,

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":  {
        "sales": [
            "cri_assignment_status",
            "cri_name",
            "cri_type_create",
            "cri_assignee_id",
            "cri_supporter_ids",
            "cri_code"
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {get} /api/v2.1/merchants/config/dynamic-filter-module danh sách field dynamic theo module 
@apiDescription thông tin được cấu hình ở toolkits 
@apiGroup MerchantModule
@apiVersion 1.0.0
@apiName MerchantFilterFieldDynamic 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {String}  module_filter   danh sách module cách nhau dấu ,

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":  {
        "sales": [
            "cri_assignment_status",
            "cri_name",
            "cri_type_create",
            "cri_assignee_id"
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {get} /api/v2.1/merchants/config/feature Lấy thông tin tính năng merchant
@apiDescription các tính năng đặc biệt cần cấu hình như: call_report
@apiGroup MerchantModule
@apiVersion 1.0.0
@apiName GetMerchantFeature 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": {
    "call_report": {
        "permission_mobile": "full",  // "full", "edit", "view"
        "permission_web": "full",   // "full", "edit", "view"
        "status_mobile": "allow",   // "allow", "deny"
        "status_web": "allow"       // "allow", "deny"
    }
  }
}
"""


"""
@api {POST} /api/v2.1/merchants/config/feature  Cập nhật tính năng merchant 
@apiDescription cập nhật các tính năng đặc biệt 
@apiVersion 2.1.0
@apiGroup MerchantModule
@apiName UpdateMerchantFeature 

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParamExample {json} Body example
{
  "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
  "config_feature": {
    "call_report": {
      "status_web": "allow",
      "status_mobile": "allow",
      "permission_web": "full",
      "permission_mobile": "full"
    }
  }
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""



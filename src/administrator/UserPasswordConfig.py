#!/usr/bin/python
# -*- coding: utf8 -*-

# ***********************************************************************************************************************
# ************************************************** T<PERSON><PERSON>, sửa cấu hình username password  **************************************************
# ***********************************************************************************************************************
"""
@api {post} /api/v2.1/policy/account Tạo sửa, cấu hình username password của merchant
@apiDescription Tạo cấu hình username password của merchant
@apiVersion 2.1.0
@apiGroup ConfigUsernamePassword
@apiName Post

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Body:)     {Object}    data                          Cấu hình username và password
@apiParam      (Body:)     {String}    data.key                      Field_key
@apiParam      (Body:)     {int}       data.index                    Thử tự 
@apiParam      (Body:)     {Object}    data.policies                 Chính sách
@apiParam      (policies:)     {String}    key             Field_key
@apiParam      (policies:)     {int}       value           Giá trị của field theo interger
@apiParam      (policies:)     {String}    type            Lựa chọn cho key contain <code> "least": kí tự tối thiểu; "all": tất cả kí tự</code>
@apiParam      (policies:)     {Object}    properties      Cấu hình thêm của field

@apiSuccess      (apiSuccess:)     {int}    checked_properties_number      Số các cấu hình được lựa chọn ở properties

@apiParamExample {json} body
{
    "data":
        [
            {
                "key":"username",
                "policies":[
                  {
                      "key": "length_max",
                      "value": 10
                  },
                  {
                      "key": "length_min",
                      "value": 3
                  },
                  {
                      "key": "contain",
                      "type": "least",
                      "status": 1,
                      "value": 3,
                      "properties": [
                                      {
                                        "key": "uppercase",
                                        "status": 1,
                                        "value": 3,
                                      },
                                      {
                                        "key": "lowercase",
                                        "status": 1,
                                        "value": 3,
                                      },
                                      {
                                        "key": "number",
                                        "status": -1,
                                        "value": 3,
                                      },
                                      {
                                        "key": "symbol",
                                        "status": -1,
                                        "value": 5,
                                      }
                                  ]
                  }
                ]
            },
            {
                "key": "password",
                "policies":[
                  {
                      "key": "length_max",
                      "value": 10
                  },
                  {
                      "key": "length_min",
                      "value": 3
                  },
                  {
                      "key": "contain",
                      "type": "least",
                      "value": 3,
                      "properties": [
                                      {
                                        "key": "uppercase",
                                        "status": 1,
                                        "value": 3
                                      },
                                      {
                                        "key": "lowercase",
                                        "status": 1,
                                        "value": 3
                                      },
                                      {
                                        "key": "number",
                                        "status": -1,
                                        "value": 3
                                      },
                                      {
                                        "key": "symbol",
                                        "status": -1,
                                        "value": 5
                                      }
                                  ]
                  },
                  {
                    "key": "other",
                    "properties":
                      [
                          {
                              "key":"must_change",
                              "value": 3,
                              "currency_code": "month",
                              "status": 1
                          },
                          {
                              "key":"not_duplicate",
                              "value": 3,
                              "currency_code": "nearest_record",
                              "status": 1
                          },
                          {
                              "key":"change_in_first_sign_in",
                              "status": 1
                          },
                          {
                              "key":"not_duplicate_name_and_id",
                              "status": 1
                          },
                          {
                              "key":"lock_account",
                              "login_fail_number": 10,
                              "status": 1,
                              "lock_time": 23,
                              "lock_time_unit": "minute"
                          }
                        ]
            }
        ]
}



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "0006d21a-2eaf-40a6-88d5-9c77188c7c23",
    "data":
        [
            {
                "key":"username",
                "index":1,
                "policies":[
                  {
                      "key": "length_max",
                      "value": 10
                  },
                  {
                      "key": "length_min",
                      "value": 3
                  },
                  {
                      "key": "contain",
                      "type": "least",
                      "index": 1,
                      "status": 1,
                      "value": 3,
                      "checked_properties_number": 2,
                      "properties": [
                                      {
                                        "key": "uppercase",
                                        "status": 1,
                                        "value": 3,
                                        "index": 1
                                      },
                                      {
                                        "key": "lowercase",
                                        "status": 1,
                                        "value": 3,
                                        "index": 2
                                      },
                                      {
                                        "key": "number",
                                        "status": -1,
                                        "value": 3,
                                        "index":3
                                      },
                                      {
                                        "key": "symbol",
                                        "status": -1,
                                        "value": 5,
                                        "index":4
                                      }
                                  ]
                  }
                ]
            },
            {
                "key": "password",
                "index":1,
                "policies":[
                  {
                      "key": "length_max",
                      "value": 10
                  },
                  {
                      "key": "length_min",
                      "value": 3
                  },
                  {
                      "key": "contain",
                      "type": "least",
                      "index": 1,
                      "value": 3,
                      "checked_properties_number": 2,
                      "properties": [
                                      {
                                        "key": "uppercase",
                                        "status": 1,
                                        "value": 3,
                                        "index": 1
                                      },
                                      {
                                        "key": "lowercase",
                                        "status": 1,
                                        "value": 3,
                                        "index": 2
                                      },
                                      {
                                        "key": "number",
                                        "status": -1,
                                        "value": 3,
                                        "index":3
                                      },
                                      {
                                        "key": "symbol",
                                        "status": -1,
                                        "value": 5,
                                        "index":4
                                      }
                                  ]
                  },
                  {
                    "key": "other",
                    "properties":
                      [
                          {
                              "key":"must_change",
                              "index":1,
                              "value": 3,
                              "currency_code": "month",
                              "status": 1
                          },
                          {
                              "key":"not_duplicate",
                              "index":2,
                              "value": 3,
                              "currency_code": "nearest_record",
                              "status": 1
                          },
                          {
                              "key":"change_in_first_sign_in",
                              "index":3,
                              "status": 1
                          },
                          {
                              "key":"not_duplicate_name_and_id",
                              "index":4,
                              "status": 1
                          },
                          {
                              "key":"lock_account",
                              "index":5,
                              "login_fail_number": 10,
                              "status": 1,
                              "lock_time": 23,
                              "lock_time_unit": "minute"
                          }
                        ]
            }
        ]
  "created_time": "2019-05-21T02:36:30Z"
}
"""

# ***********************************************************************************************************************
# ************************************************** Lấy cấu hình username password template config**************************************************
# ***********************************************************************************************************************
"""
@api {get} /api/v2.1/policy/config/account Lấy cấu hình template config username password của merchant 
@apiDescription Lấy cấu hình template config username password của merchant
@apiVersion 2.1.0
@apiGroup ConfigUsernamePassword
@apiName GetUserPasswordTemplateConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccess      (data)      {String}    key                      Field_key
@apiSuccess      (data)      {int}       index                    Thử tự 
@apiSuccess      (data)      {Object}    policies                 Chính sách
@apiSuccess      (policies)  {String}    key                           Field_key
@apiSuccess      (policies)  {Object}    properties                    Cấu hình thêm của field (các bộ cấu hình để lựa chọn)
@apiSuccess      (policies:) {Bool}      required                      Yêu cầu bắt buộc hay không
@apiSuccess      (policies:) {String}    type                          Kiểu dữ liệu
@apiSuccess      (policies:) {String}    field_name                    Tên trường trả về
@apiSuccess      (properties:) {String}  field_name                    Tên trường trả về
@apiSuccess      (properties:) {String}  key                           Field_key
@apiSuccess      (properties:) {String}  type                          Kiểu dữ liệu
@apiSuccess      (properties:) {Bool}    required                      Yêu cầu bắt buộc hay không
@apiSuccess      (properties:) {int}     index                         Thứ tự
@apiSuccess      (properties:) {String}     lock_time_unit             Đơn vị thời gian khóa khi nhập sai mật khẩu nhiều lần 
lock_time_unit


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "data":
        [
            {
                "key":"username",
                "field_name": "i18n_name"
                "index":1,
                "policies":[
                  {
                      "key": "length_max",
                      "type": "int" ,
                      "index": 1,
                      "field_name": "i18n_length_max"
                      "required": false
                  },
                  {
                      "key": "length_min",
                      "type": "int",
                      "required": false,
                      "field_name": "i18n_length_min"
                      "index": 2
                  },
                  {
                      "key": "contain",
                      "type": "ratio",
                      "index": 3,
                      "field_name": "i18n...""
                      "required": false,
                      "properties":[
                                      {
                                        "field_name": "i18n...""
                                        "index": 1,
                                        "key": "all",
                                        "type": "checkbox",
                                        "required": false,
                                        "properties": [
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "uppercase",
                                                          "type": "int",
                                                          "required": false,
                                                          "index": 1
                                                        },
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "lowercase",
                                                          "type":"int",
                                                          "required": false,
                                                          "index": 2
                                                        },
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "number",
                                                          "type": "int",
                                                          "required": false,
                                                          "index":3
                                                        },
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "symbol",
                                                          "type": "int",
                                                          "required": false,
                                                          "index":4
                                                        }
                                                    ]
                                        },
                                        {
                                        "index": 2,
                                        "key": "least",
                                        "field_name": "i18n...""
                                        "type": "fill_int_checkbox",
                                        "required": false,
                                        "properties": [
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "uppercase",
                                                          "index": 1,
                                                          "required": false,
                                                          "type": "int",
                                                        },
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "lowercase",
                                                          "index": 2,
                                                          "required": false,
                                                          "type": "int"
                                                        },
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "number",
                                                          "index":3,
                                                          "required": false,
                                                          "type": "int"
                                                        },
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "symbol",
                                                          "required": false,
                                                          "index":4,
                                                          "type": "int"
                                                        }
                                                    ]
                                        }
                                        ]
                  }
                ]
            },
            {
                "key": "password",
                "index":1,
                "field_name": "i18n...""
                "policies":[
                  {
                      "field_name": "i18n...""
                      "key": "length_max",
                      "type": "int" ,
                      "required": false,
                      "index": 1
                  },
                  {
                      "field_name": "i18n...""
                      "key": "length_min",
                      "type": "int",
                      "required": false,
                      "index": 2
                  },
                  {
                      "field_name": "i18n...""
                      "key": "contain",
                      "type": "ratio",
                      "length_min": 3,
                      "required": false,
                      "index": 3,
                      "properties":[
                                      {
                                        "field_name": "i18n...""
                                        "index": 1,
                                        "key": "all",
                                        "type": "checkbox",
                                        "required": false,
                                        "properties": [
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "uppercase",
                                                          "type": "int",
                                                          "required": false,
                                                          "index": 1
                                                        },
                                                        { 
                                                          "field_name": "i18n...""
                                                          "key": "lowercase",
                                                          "required": false,
                                                          "type":"int",
                                                          "index": 2
                                                        },
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "number",
                                                          "required": false,
                                                          "type": "int",
                                                          "index":3
                                                        },
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "symbol",
                                                          "required": false,
                                                          "type": "int",
                                                          "index":4
                                                        }
                                                    ]
                                        },
                                        {
                                        "field_name": "i18n...""
                                        "index": 2,
                                        "key": "least",
                                        "type": "fill_int_checkbox",
                                        "required": false,
                                        "properties": [
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "uppercase",
                                                          "index": 1,
                                                          "required": false,
                                                          "type": "int"
                                                        },
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "lowercase",
                                                          "index": 2,
                                                          "required": false,
                                                          "type": "int"
                                                        },
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "number",
                                                          "index":3,
                                                          "required": false,
                                                          "type": "int"
                                                        },
                                                        {
                                                          "field_name": "i18n...""
                                                          "key": "symbol",
                                                          "index":4,
                                                          "required": false,
                                                          "type": "int"
                                                        }
                                                    ]
                                        }
                                        ]
                  },
                  {
                    "key": "other",
                    "type": "ratio",
                    "field_name": "i18n...""
                    "required": false,
                    "properties":
                      [
                          {
                              "field_name": "i18n...""
                              "key":"must_change",
                              "index":1,
                              "type": "int",
                              "required": false,
                              "currency_code": "month",
                          },
                          {
                              "field_name": "i18n...""
                              "key":"not_duplicate",
                              "index":2,
                              "required": false,
                              "type": "int",
                              "currency_code": "nearest_record",
                          },
                          {
                              "field_name": "i18n...""
                              "key":"change_in_first_sign_in",
                              "index":3
                          },
                          {
                              "field_name": "i18n...""
                              "key":"not_duplicate_name_and_id",
                              "index":4
                          },
                          {
                              "field_name": "i18n...""
                              "key":"lock_account",
                              "index":5,
                              "required": false,
                              "type": "lock_account_config"
                              "lock_time_unit": "minute"
                              "field_name_lock_time": "i18n..."
                          }
                        ]
            }
        ]
}


"""

# ***********************************************************************************************************************
# ************************************************** Lấy pattern merchant  **************************************************
# ***********************************************************************************************************************
"""
@api {get} /api/v2.1/merchants/actions/pattern/user Lấy pattern của merchant
@apiDescription Lấy pattern của merchant
@apiVersion 2.1.0
@apiGroup ConfigUsernamePassword
@apiName GetPattern

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam      (Query:)     {String}    merchant_id                     id merchant
@apiSuccess      (data)     {Object}    pattern_password                pattern của mật khẩu merchant (đã được encode base 64 'utf-8')
@apiSuccess      (data)     {Object}    pattern                         pattern của username merchant (đã được encode base 64 'utf-8')
@apiSuccess      (data)     {Object}    password                         policy của patter mật khẩu 
@apiSuccess      (data)     {Object}    username                         policy của patter username
  
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "password": {
      "contain": {
        "key": "least",
        "number": 2,
        "symbol": 3,
        "uppercase": 1,
        "value": "2"
      },
      "length_max": "15",
      "length_min": "4"
    },
    "pattern_password": "Xig/PSg/Oi4qW0EtWl0uKil7MSx9KSg/PSg/Oi4qWzAtOV0uKil7Mix9KSguezAsMTV9JCl8KD89KD86LipbQS1aXS4qKXsxLH0pKD89KD86LipbXmEtekEtWjAtOV0uKil7Myx9KSguezAsMTV9JCl8KD89KD86LipbMC05XS4qKXsyLH0pKD89KD86LipbXmEtekEtWjAtOV0uKil7Myx9KSguezAsMTV9JCkuKiQ=",
    "pattern_username": "Xig/PSg/Oi4qW0EtWl0uKil7MSx9KSg/PSg/Oi4qW2Etel0uKil7MSx9KSg/PSg/Oi4qWzAtOV0uKil7MSx9KSguezAsMjd9JCkuKiQ=",
    "username": {
      "contain": {
        "key": "least",
        "lowercase": 1,
        "number": 1,
        "uppercase": 1,
        "value": "3"
      },
      "length_max": "27",
      "length_min": "4"
    }
  },
  "lang": "vi",
  "message": "request thành công."
}


"""

# ***********************************************************************************************************************
# ************************************************** Lấy cấu hình username password  **************************************************
# ***********************************************************************************************************************
"""

@api {get} /api/v2.1/policy/account Lấy cấu hình username passwordd của merchant
@apiDescription Lấy cấu hình username password
@apiVersion 2.1.0
@apiGroup ConfigUsernamePassword
@apiName GetTemplateUserPassword
@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess      (data)     {String}    merchant_id                   id merchant
@apiSuccess      (data)     {Object}    data                          Cấu hình username và password
@apiSuccess      (data)     {String}    data.key                      Field_key
@apiSuccess      (data)     {int}       data.index                    Thử tự 
@apiSuccess      (data)     {Object}    data.policies                 Chính sách
@apiSuccess      (data)     {String}    data.policies.key             Field_key
@apiSuccess      (data)     {int}       data.policies.value          Giá trị của field theo interger
@apiSuccess      (data)     {String}    data.policies.type            Lựa chọn cho key contain <code> "least": kí tự tối thiểu; "all": tất cả kí tự</code>
@apiSuccess      (data)     {Object}    data.policies.properties               Cấu hình thêm của field
@apiSuccess      (data)     {Object}    data.policies.checked_properties_number         Số các cấu hình được lựa chọn ở properties

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "0006d21a-2eaf-40a6-88d5-9c77188c7c23",
    "data":
        [
            {
                "key":"username",
                "index":1,
                "policies":[
                  {
                      "key": "length_max",
                      "value": 10
                  },
                  {
                      "key": "length_min",
                      "value": 3
                  },
                  {
                      "key": "contain",
                      "type": "least",
                      "index": 1,
                      "status": 1,
                      "value": 3,
                      "checked_properties_number": 2,
                      "properties": [
                                      {
                                        "key": "uppercase",
                                        "status": 1,
                                        "value": 3,
                                        "index": 1
                                      },
                                      {
                                        "key": "lowercase",
                                        "status": 1,
                                        "value": 3,
                                        "index": 2
                                      },
                                      {
                                        "key": "number",
                                        "status": -1,
                                        "value": 3,
                                        "index":3
                                      },
                                      {
                                        "key": "symbol",
                                        "status": -1,
                                        "value": 5,
                                        "index":4
                                      }
                                  ]
                  }
                ]
            },
            {
                "key": "password",
                "index":1,
                "policies":[
                  {
                      "key": "length_max",
                      "value": 10
                  },
                  {
                      "key": "length_min",
                      "value": 3
                  },
                  {
                      "key": "contain",
                      "type": "least",
                      "index": 1,
                      "value": 3,
                      "checked_properties_number": 2,
                      "properties": [
                                      {
                                        "key": "uppercase",
                                        "status": 1,
                                        "value": 3,
                                        "index": 1
                                      },
                                      {
                                        "key": "lowercase",
                                        "status": 1,
                                        "value": 3,
                                        "index": 2
                                      },
                                      {
                                        "key": "number",
                                        "status": -1,
                                        "value": 3,
                                        "index":3
                                      },
                                      {
                                        "key": "symbol",
                                        "status": -1,
                                        "value": 5,
                                        "index":4
                                      }
                                  ]
                  },
                  {
                    "key": "other",
                    "properties":
                      [
                          {
                              "key":"must_change",
                              "index":1,
                              "value": 3,
                              "currency_code": "month",
                              "status": 1
                          },
                          {
                              "key":"not_duplicate",
                              "index":2,
                              "value": 3,
                              "currency_code": "nearest_record",
                              "status": 1
                          },
                          {
                              "key":"change_in_first_sign_in",
                              "index":3,
                              "status": 1
                          },
                          {
                              "key":"not_duplicate_name_and_id",
                              "index":4,
                              "status": 1
                          },
                          {
                              "key":"lock_account",
                              "index":5,
                              "login_fail_number": 10,
                              "status": 1,
                              "lock_time": 23,
                              "lock_time_unit": "minute"
                          }
                        ]
            }
        ]
  "created_time": "2019-05-21T02:36:30Z"
}
"""
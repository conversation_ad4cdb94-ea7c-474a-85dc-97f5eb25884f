# Danh sách trigger_key
"""
@api {GET} /api/v2.1/trigger-keys-config Danh sách key trigger
@apiDescription Danh sách key trigger
@apiVersion 1.0.0
@apiGroup Trigger
@apiName TriggerKeys

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam (Query:) {String} trigger_type      Loại trigger
                                              <li><code>change_staff_info</code>: Thay đổi thông tin nhân viên</li>
                                              <li><code>change_team_role</code>: Thay đổi vai trò trong team</li>
                                              <li><code>add_to_team</code>: Thêm nhân viên vào team</li>
                                              <li><code>remove_from_team</code>: Gỡ nhân viên khỏi team</li>
@apiParam (Query:) {String} [attribute]       Thuộc tính cần lấy gi<PERSON> trị tương ứng của từng trigger_type
                                              <ul> change_staff_info
                                                <li><code>status</code>: Tr<PERSON>ng thái của tài kho<PERSON>n</li>
                                                <li><code>gender</code>: Giới tính</li>
                                              </ul>
                                              <ul> change_team_role
                                                <li><code>team_module</code>: Chức năng phụ trách</li>
                                                <li><code>permission</code>: Quyền trong team (Vai trò)</li>
                                              </ul>
                                              <ul> add_to_team, remove_from_team
                                                <li><code>team_module</code>: Chức năng phụ trách</li>
                                              </ul>

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "group_key": "staff_detail",
            "group_name": "Thông tin tài khoản",
            "key": "fullname",
            "name": "Họ và tên"
        },
        {
            "group_key": "staff_detail",
            "group_name": "Thông tin tài khoản",
            "key": "email",
            "name": "E-mail"
        },
        {
            "group_key": "staff_detail",
            "group_name": "Thông tin tài khoản",
            "key": "password",
            "name": "Mật khẩu"
        },
        {
            "group_key": "staff_detail",
            "group_name": "Thông tin tài khoản",
            "key": "phone_number",
            "name": "Số điện thoại"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# Đăng ký trigger
"""

@api {POST} /api/v2.1/trigger Đăng ký trigger
@apiDescription Đăng ký trigger
@apiVersion 1.0.0
@apiGroup Trigger
@apiName TriggerRegister

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse json_header
@apiUse merchant_id_header

@apiParam      (Body:)       {String}         name                                  Tên trigger
@apiParam      (Body:)       {Array}          triggers                              Danh sách trigger

@apiParam      (Body:)       {String}         triggers.trigger_type                 Loại trigger <code>vd: change_staff_info, staff_status, change_team_role, add_to_team, remove_from_team</code>
@apiParam      (Body:)       {String}         triggers.operator_key                 Toán tử mối quan hệ giữa các điều kiện trigger 
                                                                                    <ul>
                                                                                        <li><code>op_is_multiple </code> : Điều kiện và</li>
                                                                                    </ul>
@apiParam      (Body:)       {Array}          triggers.properties_data              Chi tiết cấu hình điều kiện trigger

@apiParam      (Body:)       {String}        triggers.properties_data.key           Field kiểm tra thông tin
@apiParam      (Body:)       {String}        triggers.properties_data.operator_key  Toán tử để kiểm tra điều kiện
                                                                                    <ul>
                                                                                      <li><code>op_is_equal </code> : Bằng các giá trị values[0] hoặc values </li>
                                                                                      <li><code>op_is_between </code> : Thỏa mãn khoảng giá trị values[0] <= x <= values[1]</li>
                                                                                      <li><code>op_is_greater </code> : lớn hơn giá trị values[0] </li>
                                                                                      <li><code>op_is_greater_equal </code> : Lớn hơn hoặc bằng giá trị values[0] </li>
                                                                                      <li><code>op_is_less </code> : nhỏ hơn giá trị values[0] </li>
                                                                                      <li><code>op_is_less_equal </code> : nhỏ hơn hoặc bằng giá trị values[0] </li>
                                                                                      <li><code>op_is_in </code> :Có giá trị bằng 1 trong các giá trị được nhập </li>
                                                                                      <li><code>op_is_has </code> : So sánh contains chuỗi với giá trị values[0] </li>
                                                                                      <li><code>op_is_multiple </code> : Kiểm tra tất cả các điều kiện của giá trị trong values </li>
                                                                                      <li><code>op_is_any </code> : Kiểm tra field có bất kỳ giá trị nào <br/>
                                                                                                                    Trả về <code>False</code> nếu field không xuất hiện trong dữ liệu hoặc giá trị field là None </li>
                                                                                      <li><code>op_is_none </code> : Kiểm tra giá trị của field = None  </li>
                                                                                    </ul>
@apiParam      (Body:)       {Array}         triggers.properties_data.values        Các giá trị cần kiểm tra



@apiParamExample {json} Body example
{
   "name":"Trigger",
   "trigger":[
      {
         "trigger_type":"change_staff_info",  // Trigger Thay đổi thông tin nhân viên
         "properties_data":[
            {
               "key":"field_change",  // Thay đổi thông tin
               "operator_key":"op_is_equal",
               "values":[
                  "fullname" // Field cần kiểm tra thông tin bị thay đổi
               ]
            },
            {
               "key":"update_type", // Kiểu cập nhật
               "operator_key":"op_is_equal",
               "values":[
                  "'add_values'/'remove_values'/'change_values'"
               ]
            },
            {
               "key":"add_values",  // Thêm giá trị
               "operator_key":"op_is_any",
               "values":[
                  
               ]
            },
            {
               "key":"action_time", // Thời gian phát sinh
               "operator_key":"op_is_between",
               "values":[
                  "2025-03-10T00:00:00Z",
                  "2025-03-11T00:00:00Z"
               ]
            }
         ],
      },
      {
         "trigger_type":"change_team_role",
         "properties_data":[
            {
               "key":"module",  // Chức năng phụ trách
               "operator_key":"op_is_in",
               "value":[
                  "SALE"
               ]
            },
            {
               "key":"permission", // Quyền trong team (Vai trò)
               "operator_key":"op_is_in",
               "value":[
                  "MEMBER"
               ]
            }
         ],
      },
      {
         "trigger_type":"add_to_team",
         "properties_data":[
            {
               "key":"module",
               "operator_key":"op_is_in",
               "value":[
                  "SALE"
               ]
            },
            {
               "key":"action_time",
               "operator_key":"op_is_between",
               "value":[
                  
               ]
            }
         ],
      },
      {
         "trigger_type":"remove_from_team",
         "properties_data":[
            {
               "key":"module",
               "operator_key":"op_is_in",
               "value":[
                  "SALE"
               ]
            },
            {
               "key":"action_time",
               "operator_key":"op_is_between",
               "value":[
                  
               ]
            }
         ],
      }
   ],
   "source":"WORKFLOW"
}

@apiParamExample {json} Kiểu dữ liệu của field và operator key tương ứng

{
   "date_picker":{
      "field":{
         "action_time":"Thời gian phát sinh"
      },
      "operator_key":{
         "op_is_between":"Trong khoảng thời gian",
         "op_is_equal":"Vào thời gian",
         "op_is_greater":"Sau khoảng thời gian"
      }
   },
   "single_line_text":{
      "field":{
         "fullname":"Họ tên",
         "email":"Email",
         "password":"Mật khẩu",
         "phone_number":"Số điện thoại",
         "staff_code":"Mã nhân viên"
      },
      "operator_key":{
         "op_is_in":"Một trong những giá trị được nhập",
         "op_is_any":"Bất kì giá trị nào",
         "op_is_none":"Giá trị đang có"
      }
   },
   "dropdown_single_select":{
      "field":{
         "status":"Trạng thái tài khoản",
         "sol_id":"Đơn vị kinh doanh",
         "scope_code":"Mã cấp quản lý",
         "position_id":"Chức danh",
         "role_group":"Nhóm quyền",
         "gender":"Giới tính"
      },
      "operator_key":{
         "op_is_in":"Một trong những giá trị được chọn",
         "op_is_any":"Bất kì giá trị nào",
         "op_is_none":"Giá trị đang có"
      }
   },
   "dropdown_multi_select":{
      "field":{
         "block":"Khối",
         "group_department_ids":"Phòng ban",
         "role":"Quyền"
      },
      "operator_key":{
         "op_is_any":"Ít nhất một giá trị đang có",
         "op_is_none":"Tất cả giá trị đang có",
         "op_is_in":"Ít nhất một trong những giá trị được chọn",
         "op_is_equal":"Tất cả các giá trị được chọn"
      }
   },
   "tag":{
      "field":{
         "tag":"Tag phân loại công việc"
      },
      "operator_key":[
         {
            "op_is_any":"Ít nhất một giá trị đang có",
            "op_is_none":"Tất cả giá trị đang có",
            "op_is_in":"Ít nhất một trong những giá trị được chọn",
            "op_is_equal":"Tất cả các giá trị được chọn"
         }
      ]
   }
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {"trigger_id": ""}
    "lang": "vi",
    "message": "request thành công."
}

"""


"""

@api {POST} /api/v2.1/copy-trigger Copy trigger
@apiDescription Copy trigger
@apiVersion 1.0.0
@apiGroup Trigger
@apiName CopyTrigger

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  trigger_id trigger id cần copy
@apiParam   (Body:)   {String}  source_id id nguồn đăng ký <code>vd: workflow_id...</code>


@apiParamExample {json} Body example
{
  "trigger_id": "65fd2a343422f2ffff08ce75",
  "source_id": "72e519ac-e818-11ee-ba30-b5c8675f676a"
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "trigger_id": "65fd4193f3081c45d1be7a97"
    },
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccessExample {Body} Response error
{
  "code": 413,
  "message": "trigger_id does not exists"
}

"""

# Update trigger
"""

@api {PUT} /api/v2.1/trigger Cập nhật trigger
@apiDescription Cập nhật trigger
@apiVersion 1.0.0
@apiGroup Trigger
@apiName TriggerUpdate

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  trigger_id ID trigger
@apiParam   (Body:)   {String}  [name] Tên trigger
@apiParam   (Body:)   {Array}  trigger Danh sách trigger

@apiParam (Trigger:) {String} trigger_type Loại trigger
@apiParam (Trigger:) {Array} events Danh sách event cần ghi nhận


@apiParam (Events:) {String} field_change field thay đổi thông tin
@apiParam (Events:) {Array} values Giá trị thay đổi <đổi với time_change [timestamp_from, timestamp_to]>
@apiParam (Events:) {String} property Thuộc tính của trigger <code>vd: field_staff_information, time_change, staff_status</code>
@apiParam (Events:) {String} group_key Nhóm giá trị thay đổi <code>vd: staff_detail, staff_rm</code>


@apiParamExample {json} Body example
{
  "trigger_id": "65cf29e6d8859237fb74dbc6"
  "name":"Trigger",
  "trigger":[
    {
        "trigger_type":"change_staff_info",
        "properties_data":[
          {
              "key":"field_change",
              "operator_key":"op_is_equal",
              "values":[
                "fullname"
              ]
          },
          {
              "key":"update_type",
              "operator_key":"op_is_equal",
              "values":[
                "add_values"/"remove_values"/"change_values"
              ]
          },
          {
              "key":"add_values",
              "operator_key":"op_is_any",
              "values":[
                
              ]
          },
          {
              "key":"action_time",
              "operator_key":"op_is_between",
              "values":[
                "2025-03-10T00:00:00Z",
                "2025-03-11T00:00:00Z"
              ]
          }
        ],
    },
    {
        "trigger_type":"change_team_role",
        "properties_data":[
          {
              "key":"module",
              "operator_key":"op_is_in",
              "value":[
                "SALE"
              ]
          },
          {
              "key":"permission",
              "operator_key":"op_is_in",
              "value":[
                "MEMBER"
              ]
          }
        ],
    }
  ],
  "source":"WORKFLOW"
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {"trigger_id": ""}
    "lang": "vi",
    "message": "request thành công."
}

"""

# Lưu data callback

"""
@api {PUT} /api/v2.1/trigger-callback Lưu dữ liệu data callback
@apiDescription Lưu dữ liệu data callback
@apiVersion 1.0.0
@apiGroup Trigger
@apiName TriggerCallback

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse json_header
@apiUse merchant_id_header

@apiParam      (Body:)     {String}    trigger_id       Id trigger cần lưu callback
@apiParam      (Body:)     {String}    time_arise_event      Thời gian phát sinh event theo giây <code>format int</code>
@apiParam      (Body:)     {Array}   [account_ids]  Danh sách account id
@apiParam      (Body:)     {Object}   callback  cấu hình callback
@apiParam      (Body:)     {Object}  callback.queue_config                 Cấu hình queue
@apiParam      (Body:)     {String}  callback.queue_config.key             Key của queue
@apiParam      (Body:)     {String}  callback.queue_config.target          Tên topic cần bắn message vào
@apiParam      (Body:)     {Object}   callback.data  data trả về qua callback

@apiParamExample {json} Body example
{
    "trigger_id": "",
    "time_arise_event": **********,
    "callback": {
        "callback_type": "queue",
        'queue_config': {
            'key': '',
            'target': '' 
        },
        'data': {
            'merchant_id': '1b99bdcf-d582-4f49-9715-1b61dfff3924',
            'target_type': 'USER',
            "workflow_id": "ce1ba0ce-1fa1-11ee-b3d9-5e7267e51fab",
        }
    }
}


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccessExample  {json}  Callback Queue Trigger:
{
    "account_id": "xxxxxxxxxxx"
    "data_callback": {
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "target_type": "USER",
        "workflow_id": "ce1ba0ce-1fa1-11ee-b3d9-5e7267e51fab",
        ...
    }
}

"""

# Lấy cấu hình trigger
"""
@api {GET} /api/v2.1/trigger Lấy cấu hình trigger
@apiDescription Lấy cấu hình trigger
@apiVersion 1.0.0
@apiGroup Trigger
@apiName TriggerConfig

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam (Query:) {String} trigger_id id trigger

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "trigger_id": "65cf29e6d8859237fb74dbc6"
  "name":"Trigger",
  "trigger":[
    {
        "trigger_type":"change_staff_info",
        "properties_data":[
          {
              "key":"field_change",
              "operator_key":"op_is_equal",
              "values":[
                "fullname"
              ]
          },
          {
              "key":"update_type",
              "operator_key":"op_is_equal",
              "values":[
                "'add_values'/'remove_values'/'change_values'"
              ]
          },
          {
              "key":"add_values",
              "operator_key":"op_is_any",
              "values":[
                
              ]
          },
          {
              "key":"action_time",
              "operator_key":"op_is_between",
              "values":[
                "2025-03-10T00:00:00Z",
                "2025-03-11T00:00:00Z"
              ]
          }
        ],
    },
    {
        "trigger_type":"change_team_role",
        "properties_data":[
          {
              "key":"module",
              "operator_key":"op_is_in",
              "value":[
                "SALE"
              ]
          },
          {
              "key":"permission",
              "operator_key":"op_is_in",
              "value":[
                "MEMBER"
              ]
          }
        ],
    }
  ],
  "source":"WORKFLOW"
}

"""


# Unregister trigger
"""
@api {POST} /api/v2.1/trigger-status Cập nhật trạng thái trigger
@apiDescription Cập nhật trạng thái trigger
@apiVersion 1.0.0
@apiGroup Trigger
@apiName TriggerStatus

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse merchant_id_header

@apiParam (Body:) {String} source_id id nguồn đăng ký <code>vd: workflow_id...</code>
@apiParam (Body:) {String} status Trạng thái trigger <code>active/deactive</code>

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công."
}

"""

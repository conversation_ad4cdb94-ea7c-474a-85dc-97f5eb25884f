#!/usr/bin/python
# -*- coding: utf8 -*-

******************************* ĐĂNG KÝ TOKEN ******************************
*                                                                          *
* version: 1.0.0                                                           *
****************************************************************************
"""
@api {POST} /api/v2.1/push-id Đăng ký push_id cho thiết bị điện thoại 
@apiDescription Đăng ký push_id cho thiết bị điện thoại 
@apiVersion 1.0.0
@apiGroup PushId
@apiName AddPushId

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)  {String} device_operating_system  Hệ điều hành của thiết bị
@apiParam   (Body:) {String} merchant_id  Merchant của tài khoản sở hữu thiết bị
@apiParam   (Body:) {String} device_id  id của thiết bị
@apiParam   (Body:) {String} account_id ID của tài khoản
@apiParam   (Body:) {String} push_id push id của thiết bị
@apiParam   (Body:) {Bool} [sandbox]  true/false , <code>true: dev, false: product, None: không xác định</code>
@apiSuccess {String} push_id   push id của thiết bị

@apiParamExample {json} Body example
{
  "device_id": "id"
  "device_operating_system": "ios",
  "merchant_id": "uuid",
  "account_id": "uuid",
  "push_id": "uuid",
  "sandbox": True
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "device_id": "id",
    "push_id": "token_code",
    "device_operating_system": "ios",
    "merchant_id": "uuid",
    "account_id": "uuid",
    "sandbox": True
  }
}
"""

******************************* LẤY DANH SÁCH TOKEN ************************
*                                                                          *
* version: 1.0.0                                                           *
****************************************************************************
"""
@api {GET} /api/v2.1/push-id Lấy danh sách push_id thiết bị
@apiDescription  Lấy danh sách push_id thiết bị
@apiVersion 1.0.0
@apiGroup PushId
@apiName GetPushId
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Query:)  {String} merchant_ids : danh sách merchant_id cần lấy push_id ex: "uuid1,uuid2,..."
@apiParam   (Query:)  {String} [account_ids] : danh sách account_id cần lấy push_id. ex: "uuid1,uuid2,..."

@apiSuccess {String} device_operating_system  Hệ điều hành của thiết bị
@apiSuccess {String} merchant_id  Merchant của tài khoản sở hữu thiết bị
@apiSuccess {String} device_id  id của thiết bị
@apiSuccess {String} account_id ID của tài khoản
@apiSuccess {String} push_id push id của thiết bị
@apiSuccess  {Bool} [sandbox]  true/false , <code>true: dev, false: product, None: không xác định</code>
@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[{
      "push_id": "token_code",
      "device_operating_system": "ios",
      "merchant_id": "uuid",
      "account_id": "uuid",
      "device_id": "id",
      "sandbox": True
      },
      {
      "push_id": "token_code",
      "device_operating_system": "android",
      "merchant_id": "uuid",
      "account_id": "uuid2",
      "device_id": "id",
      "sandbox": None
      },
    ]
}
"""

******************************* XÓA TOKEN DEVICE ***************************
*                                                                          *
* version: 1.0.0                                                           *
****************************************************************************
"""
@api {DELETE} /api/v2.1/push-id Xóa push_id của thiết bị 
@apiDescription  Xóa push_id của thiết bị
@apiVersion 1.0.0
@apiGroup PushId
@apiName DeletePushId

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Query:)  {String} [push_ids] : danh sách push_id cần xóa. ex: "uuid1,uuid2,..."
@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "push_ids": ["uuid1","uuid2"]
  }
}
"""


******************* KIỂM TRA ACCOUNT CÓ ĐANG SỬ DỤNG APP *******************
*                                                                          *
* version: 1.0.0                                                           *
****************************************************************************
"""
@api {GET} /api/v2.1/account/state/<account_id> Kiểm tra account có đang đăng nhập
@apiDescription  Kiểm tra account có đang đăng nhập
@apiVersion 1.0.0
@apiGroup PushId
@apiName CheckAccountState

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header
@apiSuccess {Bool} mobile    Trạng thái đăng nhập của account trên mobile <code>1- đang đăng nhập, 0- không đăng nhập</code>
@apiSuccess {Bool} web    Trạng thái đăng nhập của account trên web <code>1- đang đăng nhập, 0- không đăng nhập</code>
@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "mobile": 1,
    "web": 1
  }
}
"""

"""
@api {GET} /api/v2.1/account/config/notify Lấy thông tin cấu hình thông báo theo tài khoản  
@apiDescription  Lấy thông tin một account 
@apiVersion 1.0.0
@apiGroup PushId
@apiName GetConfigNotify
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Query:)  {String} account_id account_id cần lấy thông tin 
@apiParam   (Query:)  {String} [key] option, danh sách key cần lấy thông tin cách nhau dấu ,: sale_notify_new_deal,...

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
			"account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
			"jb_pending_approval": {
				"app": "off",
				"browser": "on",
				"email": "on",
				"times": 1,
				"unit": "day",
				"value": 1,
				"web": "on"
			},
			"sale_deal_revoke": {
				"app": "off",
				"browser": "on",
				"email": "on",
				"permission": "owner_supporter",
				"web": "on"
			},
			"sale_result_upload_deal": {
				"app": "off",
				"browser": "off",
				"email": "on",
				"web": "off"
			},
			"social_alert_max_chat_not_assign": {
				"app": "on",
				"browser": "on",
				"email": "off",
				"web": "on"
			},
			"social_chat_assign_me": {
				"all_page": "on",
				"app": "on",
				"browser": "on",
				"email": "off",
				"page_ids": [],
				"ringtone_web": "on",
				"web": "on"
			},
			"social_chat_assign_me_revoke": {
				"app": "on",
				"browser": "on",
				"email": "off",
				"web": "on"
			},
			"social_comment_assign_me": {
				"all_page": "on",
				"app": "on",
				"browser": "on",
				"email": "off",
				"page_ids": [],
				"ringtone_web": "on",
				"web": "on"
			},
			"social_comment_assign_me_revoke": {
				"app": "on",
				"browser": "on",
				"email": "off",
				"web": "on"
			},
			"survey_result": {
				"app": "off",
				"browser": "off",
				"email": "on",
				"times": 1,
				"unit": "day",
				"value": 30,
				"web": "off"
			},
			"ticket_alert_max_ticket_not_assign": {
				"app": "off",
				"browser": "on",
				"email": "on",
				"web": "on"
			},
			...
		}
  },
  
}
"""

"""
@api {POST} /api/v2.1/account/config/notify Cập nhật cấu hình on/off thông báo 
@apiDescription cập nhật cho một tài khoản 
@apiVersion 1.0.0
@apiGroup PushId
@apiName UpsertConfigNotify

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {String} account_id ID của tài khoản
@apiParam   (Body:) {object} sale_notify_new_deal thông báo đơn hàng mới, trạng thái cho "app", "web" ("on", "off")

@apiParamExample {json} Body example
{
  "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
	"sale_notify_new_deal": {
		"app": "off",
		"web": "on",
		"email": "off",
		"browser": "off",
	}
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "sale_notify_new_deal": {
      "app": "off",
      "web": "on"
    },
  }
}
"""



"""
@api {POST} /api/v2.1/account/config/notify/multiple Danh sách cấu hình thông báo cho nhiều tài khoản  
@apiDescription có thể truyền lên danh sách key muốn lấy thông tin 
@apiVersion 1.0.0
@apiGroup PushId
@apiName GetMultiConfigNotify

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:) {array} account_ids danh sách id tài khoản 
@apiParam   (Body:) {array} [keys] danh sách loại thông báo cần lấy thông tin, ko có sẽ lấy tất cả  

@apiParamExample {json} Body example
{
  "account_ids": ["7fc0a33c-baf5-11e7-a7c2-0242ac180003"]
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
    {
			"account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
			"survey_create_by_other_end": {
				"app": "off",
				"browser": "on",
				"email": "on",
				"web": "on"
			},
			"survey_result": {
				"app": "off",
				"browser": "off",
				"email": "on",
				"times": 1,
				"unit": "day",  // minute, hour, day, month,
				"value": 30,
				"web": "off"
			},
			"ticket_alert_max_ticket_not_assign": {
				"app": "off",
				"browser": "on",
				"email": "on",
				"web": "on"
			},
			"ticket_assign_me": {
				"app": "off",
				"browser": "on",
				"email": "on",
				"permission": "owner_supporter", // "owner_supporter", "owner", "supporter"
				"web": "on"
			},
			"ticket_remove_me": {
				"app": "off",
				"browser": "on",
				"email": "on",
				"permission": "owner_supporter",
				"web": "on"
			},
			...
		},
		...
  ]
}
"""


"""
@api {get} /api/v2.1/account/config/notify/on-by-key Danh sách tài khoản bật thông báo theo từng kênh   
@apiDescription tìm kiếm theo từng loại thông báo 
@apiVersion 1.0.0
@apiGroup PushId
@apiName GetAccountOnConfigNotify

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParam   (Query:)  {String} key loại thông báo cần kiểm tra VD: "sale_deal_assign_me"   


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
	"code": 200,
	"data": {
		"app": [],
		"browser": [],
		"email": [
			"024faf35-70fa-45ad-835d-1087ad326e90",
			"0c617361-003a-4095-a74e-be6e4a045693",
			"113fb73f-9125-42f1-a87b-b93ccddf0857",
		],
		"web": [
			"024faf35-70fa-45ad-835d-1087ad326e90",
			"0c617361-003a-4095-a74e-be6e4a045693",
			"113fb73f-9125-42f1-a87b-b93ccddf0857",
		]
	},
	"lang": "vi",
	"message": "request thành công."
}
"""


******************************* ĐĂNG KÝ PUSH ID NO AUTHEN ******************
*                                                                          *
* version: 1.0.0                                                           *
****************************************************************************
"""
@api {POST} /mobile/api/v2.1/push-id/register Đăng ký push id theo app code
@apiDescription đăng ký theo app code để sử dụng cho tính năng gửi thông báo cho app từ workflow 
@apiVersion 1.0.0
@apiGroup PushId
@apiName RegisterPushIdByCode

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)  {String} device_operating_system  Hệ điều hành của thiết bị: android, ios
@apiParam   (Body:) {String} device_id  id của thiết bị
@apiParam   (Body:) {String} [account_id] ID của tài khoản
@apiParam   (Body:) {String} push_id push id của thiết bị
@apiParam   (Body:) {String} app_code id của app do Mobio cung cấp 
@apiParam   (Body:) {Bool} [sandbox]  true/false, default true, <code>true: dev, false: product, None: không xác định</code>

@apiParamExample {json} Body example
{
  "device_id": "id",
  "device_operating_system": "ios",
  "account_id": "account_id",
  "push_id": "push_id",
  "sandbox": True,
  "app_code": "app_code"
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":{
    "device_id": "id",
    "push_id": "token_code",
    "device_operating_system": "ios",
    "merchant_id": "uuid",
    "account_id": "uuid",
    "sandbox": True,
    "app_code": "app_code"
  }
}

@apiSuccessExample {json} Response error 
{
    "code": 413,
    "message": "Account does not exist"
}
"""

"""
@api {POST} /api/v2.1/push-id/account-filter lấy push id theo thông tin email 
@apiDescription sau có thể bổ sung lọc theo id account, mã nhân viên nếu cần 
@apiVersion 1.0.0
@apiGroup PushId
@apiName ListPushIDAccountFilter 

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)  {Array} emails  danh sách email của tài khoản cần lấy push, tối đa 20 phần tử 
@apiParam   (Body:) {String} app_code  mã app do module NM định nghĩa 

@apiParamExample {json} Body example
{
  "emails": ["<EMAIL>", "<EMAIL>"],
  "app_code": "app_code"
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
	  {
	    "email": "<EMAIL>",
	    "push_id": "token_code",
	    "account_id": "uuid",
	    "push_os": "android"		// ios, android
	  }
	]
}

"""

******************************* API Lấy danh sách cửa hàng theo merchant *******************************
* version: 1.0.0                                                                                       *
********************************************************************************************************
"""
@api {post} /api/v2.1/merchants/<merchant_id>/stores Lấy danh sách cửa hàng theo merchant  
@apiDescription Lấy danh sách cửa hàng theo merchant :<br>
Lấy danh sách tỉnh thành theo API <a href="https://dev.mobio.vn/crm/api/api/v2.1/static/provinces.json"> https://dev.mobio.vn/crm/api/api/v2.1/static/provinces.json</a>
@apiVersion 1.1.0
@apiGroup Store
@apiName GetStore

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam  {array} [province_codes] Danh sách mã tỉnh thành 
@apiParam  {string} [name]  Tên cửa hàng cần tìm kiếm 

@apiSuccess  {array}  data Danh sách các cửa hàng được tìm thấy 
@apiSuccess (Data:) {string}  id Uuid của cửa hàng  
@apiSuccess (Data:) {string}  name Tên cửa hàng  
@apiSuccess (Data:) {string}  address Địa chỉ cửa hàng  
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
     "id": "0237ca1d-f751-4095-9469-ac0f21624a50",
     "name":"Cua hang so mot",
     "address": "2/82 Duy Tan Cau Giay Ha Noi"   
    }
  ],
  "message": "request thành công."
}
"""

#!/usr/bin/python
# -*- coding: utf8 -*-


"""
@api {get} /api/v2.1/masking-fields <PERSON>h sách luật mã hóa  
@apiDescription  có phân trang 
@apiVersion 1.0.0
@apiGroup MaskingField
@apiName GetListMaskingField
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header
@apiUse paging
@apiUse search


@apiParam (Query) {string} status  trạng thái sử dụng, nhiều giá trị cách nhau dấu ,: active,inactive
@apiParam (Query) {string} group_type  mã hóa loại: group - nhóm,  single - đơn .nhiều giá trị cách nhau dấu ,


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
  	{
  		"config_id": "647879d7491cda83ac648412"
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name": "phone",
      "description": "ma hoa phone",
      "status": "active",       // active, inactive
      "group_type": "single",   // group, single
      "field": "phone", 
      "format_logic": {"start": 3, "end": 3}, 
      "use_format": "allow",   // not_allow, allow
      "enc_level": "enc_frontend", 
      "frontend_dec": "allow", // not_allow, allow
      "kms_id": "uuid", 
      "created_by": "uuid",
      "updated_by": "uuid",
      "created_time": "2024-03-28T10:28:49Z",
      "updated_time": "2024-03-28T10:28:49Z",
  	}
  ],
  "paging": {
    "page": 1,
    "per_page": 5,
    "total_count": 220,
    "total_page": 44
  }
  
}
"""


"""
@api {post} /api/v2.1/masking-fields Tạo cấu hình mã hóa  
@apiDescription  thêm mới 
@apiVersion 1.0.0
@apiGroup MaskingField
@apiName CreateMaskingField
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header



@apiParam      (Body:)     {String}      name                  Tên cấu hình 
@apiParam      (Body:)     {String}      [description]         mô tả 
@apiParam      (Body:)     {String}    status                trạng thái sử dụng: active, inactive
@apiParam      (Body:)     {String}    group_type           thuộc loại: group - nhóm,  single - đơn 
@apiParam      (Body:)     {String}    [module]             module áp dụng, có key này với loại single   
@apiParam      (Body:)     {String}    field                field key của module 
@apiParam      (Body:)     {String}    use_format           cho phép hiển thị 1 phần theo logic hay không:  not_allow, allow
@apiParam      (Body:)     {Object}    [format_logic]          logic hiển thị mã hóa, với use_format=allow
@apiParam      (Body:)     {String}      frontend_dec        cho phép hiển thị con mắt để giải mã hay không: not_allow, allow
@apiParam      (Body:)     {String}      kms_id             id thuật toán mã hóa 

@apiParamExample {json} Info example
{
    "name": "phone",
    "description": "ma hoa phone",
    "status": "active",       // active, inactive
    "group_type": "single",   // group, single
    "field": "phone", 
    "format_logic": {"start": 3, "end": 3}, 
    "use_format": "allow",   // not_allow, allow
    "frontend_dec": "allow", // not_allow, allow
    "module": "PROFILING",
    "kms_id": "uuid", 
 }

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
      "config_id": "647879d7491cda83ac648412"
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name": "phone",
      "description": "ma hoa phone",
      "status": "active",       // active, inactive
      "group_type": "single",   // group, single
      "field": "phone", 
      "format_logic": {"start": 3, "end": 3}, 
      "use_format": "allow",   // not_allow, allow
      "enc_level": "enc_frontend", 
      "frontend_dec": "allow", // not_allow, allow
      "module": "PROFILING",
      "kms_id": "uuid", 
      "created_by": "uuid",
      "updated_by": "uuid",
      "created_time": "2024-03-28T10:28:49Z",
      "updated_time": "2024-03-28T10:28:49Z",
    }
  
}
"""


"""
@api {put} /api/v2.1/masking-fields/<config_id>  Sửa cấu hình mã hóa  
@apiDescription  sửa 
@apiVersion 1.0.0
@apiGroup MaskingField
@apiName UpdateMaskingField
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParam      (Body:)     {String}      name                  Tên cấu hình 
@apiParam      (Body:)     {String}      [description]         mô tả 
@apiParam      (Body:)     {String}    status                trạng thái sử dụng: active, inactive
@apiParam      (Body:)     {String}    group_type           thuộc loại: group - nhóm,  single - đơn 
@apiParam      (Body:)     {String}    [module]             module áp dụng, có key này với loại single   
@apiParam      (Body:)     {String}    field                field key của module 
@apiParam      (Body:)     {String}    use_format           cho phép hiển thị 1 phần theo logic hay không:  not_allow, allow
@apiParam      (Body:)     {Object}    [format_logic]          logic hiển thị mã hóa, với use_format=allow
@apiParam      (Body:)     {String}      frontend_dec        cho phép hiển thị con mắt để giải mã hay không: not_allow, allow
@apiParam      (Body:)     {String}      kms_id             id thuật toán mã hóa 

@apiParamExample {json} Info example
{
    "name": "phone",
    "description": "ma hoa phone",
    "status": "active",       // active, inactive
    "group_type": "single",   // group, single
    "field": "phone", 
    "format_logic": {"start": 3, "end": 3}, 
    "use_format": "allow",   // not_allow, allow
    "frontend_dec": "allow", // not_allow, allow
    "module": "PROFILING",
    "kms_id": "uuid", 
 }

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
      "config_id": "647879d7491cda83ac648412"
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name": "phone",
      "description": "ma hoa phone",
      "status": "active",       // active, inactive
      "group_type": "single",   // group, single
      "field": "phone", 
      "format_logic": {"start": 3, "end": 3}, 
      "use_format": "allow",   // not_allow, allow
      "enc_level": "enc_frontend", 
      "frontend_dec": "allow", // not_allow, allow
      "module": "PROFILING",
      "kms_id": "uuid", 
      "created_by": "uuid",
      "updated_by": "uuid",
      "created_time": "2024-03-28T10:28:49Z",
      "updated_time": "2024-03-28T10:28:49Z",
    }
  
}
"""


"""
@api {get} /api/v2.1/masking-fields/<config_id>  chi tiết cấu hình mã hóa  
@apiDescription  xem chi tiết 
@apiVersion 1.0.0
@apiGroup MaskingField
@apiName DetailMaskingField
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
      "config_id": "647879d7491cda83ac648412"
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name": "phone",
      "description": "ma hoa phone",
      "status": "active",       // active, inactive
      "group_type": "single",   // group, single
      "field": "phone", 
      "format_logic": {"start": 3, "end": 3}, 
      "use_format": "allow",   // not_allow, allow
      "enc_level": "enc_frontend", 
      "frontend_dec": "allow", // not_allow, allow
      "module": "PROFILING",
      "kms_id": "uuid", 
      "created_by": "uuid",
      "updated_by": "uuid",
      "created_time": "2024-03-28T10:28:49Z",
      "updated_time": "2024-03-28T10:28:49Z",
    }
  
}
"""

"""
@api {post} /api/v2.1/masking-fields/status  Sửa trạng thái cấu hình mã hóa 
@apiVersion 1.0.0
@apiGroup MaskingField
@apiName UpdateStatusMaskingField
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParam      (Body:)     {String}      config_id                  id cấu hình 
@apiParam      (Body:)     {String}    status                trạng thái sử dụng: active, inactive

@apiParamExample {json} Info example
{
    "config_id": "647879d7491cda83ac648412"
    "status": "active",       // active, inactive
    
 }

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
 
}
"""



"""
@api {delete} /api/v2.1/masking-fields/<config_id> Xóa cấu hình mã hóa 
@apiVersion 1.0.0
@apiGroup MaskingField
@apiName DeleteMaskingField
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  
}
"""



"""
@api {get} /api/v2.1/masking-fields/group trường thông tin theo nhóm  
@apiVersion 1.0.0
@apiGroup MaskingField
@apiName FieldTypeGroup 
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "key": "phone_number",
      "name": "Phone"
    },
    {
      "key": "email",
      "name": "Email"
    }
  ]
"""



"""
@api {get} /api/v2.1/config-constant Danh sách module   
@apiVersion 1.0.0
@apiGroup MaskingField
@apiName GetListConfigConstant 
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParam (Query) {string} type loại hằng số: module_masking

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "key": "PROFILING",
      "name": "Profile"
    },
    {
      "key": "VOUCHER",
      "name": "Voucher"
    }
  ]
"""


"""
@api {post} /api/v2.1/masking-fields/field-group cập nhật cấu hình field mã hóa cho nhóm   
@apiDescription  thêm mới, xóa  
@apiVersion 1.0.0
@apiGroup MaskingField
@apiName UpdateMaskingFieldGroup 
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse json_header



@apiParam      (Body:)     {Array}      [data_upsert]            danh sách field cập nhật mới 
@apiParam      (Body:)     {Array}      [data_remove]            danh sách field xóa


@apiParamExample {json} Info example
{
    "data_upsert": [
      {
        "field": "primary_phone",
        "group_type": "phone_number",     // hiện có 2 nhóm là: phone_number và email
        "module": "PROFILING" 
      }
    ],
    "data_remove": [
      {
        "field": "phone",
        "group_type": "phone_number",     
        "module": "PROFILING" 
      }
    ],
 }

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  
}
"""



"""
@api {get} /api/v2.1/field-encrypt/kms/list Danh sách thuật toán mã hóa  
@apiDescription  trả về tất cả các thuật toán 
@apiVersion 1.0.0
@apiGroup MaskingField
@apiName GetListKMS
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
    {
      "kms_id": "647879d7491cda83ac648412"
      "kms_name": "Mobio",
    }
  ]
  
}
"""

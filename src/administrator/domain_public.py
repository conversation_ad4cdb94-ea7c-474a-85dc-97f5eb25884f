# ============================================ SETUP DOMAIN RUT GON LINK ============================================

"""
@apiDefine ResponseDetailSetupDomain

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của domain
@apiSuccess {String}            data.merchant_id                         Định danh của tennant
@apiSuccess {String}            data.domain_id                          Định danh của domain
@apiSuccess {String}            data.domain                             Tên domain
@apiSuccess {String}            data.protocol_domain                    Giao thức mạng + domain
@apiSuccess {String}            data.status_default                     Định danh domain có phải default không, nhận giá trị <code>default, not_default</code>
                                                                        <li><code>default</code>: Mặc định</li>
                                                                        <li><code>not_default</code>: Không phải domain mặc định</li>
@apiSuccess {String}            data.status_verify                      Định danh domain đã được xác thực chưa, nhận giá trị
                                                                        <li><code>verified</code>: Đã xác thực</li>
                                                                        <li><code>not_verified</code>: Chờ xác thực</li>
                                                                        <li><code>fail</code>: Xác thực thất bại</li>
@apiSuccess {String}            data.allow_delete                       Định danh domain được phép xóa hay không,nhận giá trị <code>allow, not_allow</code>
@apiSuccess {String}            data.create_by_id                       ID người tạo
@apiSuccess {String}            data.update_by_id                       ID người cập nhật
@apiSuccess {String}            data.cname                              Tên cname
@apiSuccess {String}            data.allow_delete                       Định danh domain được phép xóa hay không,nhận giá trị <code>allow, not_allow</code>

@apiSuccess {String}            data.create_on                          Thời điểm khởi tạo
@apiSuccess {String}            data.update_on                          Thời điểm cập nhật

"""

# ----------------------- Create domain -----------------------
"""
@api {POST} /api/v2.1/domain-public                Tạo domain
@apiGroup Setup domain
@apiDescription Tạo mới domain
@apiVersion 2.1.0
@apiName CreateDomain
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            domain                 Tên domain


@apiParamExample    {json}      BODY:
{
    "domain": "ck.mobio.vn"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm domain

@apiUse ResponseDetailSetupDomain

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'domain_id',
        "domain_id": 'domain_id',
        "merchant_id": "merchant_id",        
        "domain": "ck.mobio.vn/",        
        "protocol_domain": "https://ck.mobio.vn/",        
        "status_default": "default",        
        "status_verify": "verified",        
        "allow_delete": "allow",
        "cname": "ck.mobio.vn",
        "create_by_id": "",
        "update_by_id": "",
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Update domain  -----------------------
"""
@api {PATCH} /api/v2.1/domain-public                   Cập nhật domain
@apiGroup Setup domain
@apiDescription Cập nhật domain theo tên domain cũ
@apiVersion 2.1.0
@apiName UpdateDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              old_domain               Tên domain cũ cần cập nhật
@apiParam   (Query:)     {String}              new_domain               Tên domain thay thế


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Cập nhật domain


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Delete domain  -----------------------
"""
@api {DELETE} /api/v2.1/domain-public                   Xóa domain
@apiGroup Setup domain
@apiDescription Xóa domain theo tên domain
@apiVersion 2.1.0
@apiName DeleteDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              domain                 Tên domain cần xóa
@apiParam   (Query:)     {String}              domain_default         Tên domain thay thế, truyền lên nếu domain xóa đang là mặc định


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa domain


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Get list domain -----------------------
"""
@api {GET} /api/v2.1/domain-public/actions/list              Lấy danh sách domain
@apiGroup Setup domain
@apiDescription Lấy danh sách domain
@apiVersion 2.1.0
@apiName GetListDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)     {String}              search         Từ khóa cần tìm kiếm

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách domain

@apiUse ResponseDetailSetupDomain

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "id": "domain_id",
        "domain_id": "domain_id",
        "merchant_id": "merchant_id",
        "domain": "ck.mobio.vn/",        
        "protocol_domain": "https://ck.mobio.vn/",     
        "status_default": "default",        
        "status_verify": "verified",        
        "allow_delete": "allow",        
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z",
        "create_by_id": "",
        "update_by_id": "",
        "cname": "ck.mobio.vn",
        "object_used": [
            {
                "object_type": "LANDING_PAGE",  // LANDING_PAGE, JOURNEY_BUILDER, SHORTEN_URL, SURVEY
                "count": 20
            }
        ]
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}

"""
# ----------------------- Get detail domain  -----------------------
"""
@api {GET} /api/v2.1/domain-public                   Lấy chi tiết domain
@apiGroup Setup domain
@apiDescription Lấy chi tíết domain
@apiVersion 2.1.0
@apiName GetDetailDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              domain         Tên domain cần lấy thông tin chi tiết

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết thông tin domain

@apiUse ResponseDetailSetupDomain

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'domain_id',
        "domain_id": 'domain_id',
        "merchant_id": "merchant_id",        
        "domain": "ck.mobio.vn/",        
        "protocol_domain": "https://ck.mobio.vn/",       
        "status_default": "default",        
        "status_verify": "verified",        
        "allow_delete": "allow",        
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }
}
"""
# ----------------------- Set default domain  -----------------------
"""
@api {POST} /api/v2.1/domain-public/actions/set-default                   Chọn domain làm mặc định
@apiGroup Setup domain
@apiDescription Chọn domain làm domain mặc định
@apiVersion 2.1.0
@apiName SetDefaultForDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              domain         Tên domain chọn thành default


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa domain


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Check domain is active-----------------------
"""
@api {POST} /api/v2.1/domain-public/actions/check-domain-is-active              Kiểm tra domain có đang hoạt động
@apiGroup Setup domain
@apiDescription Kiểm tra domain có đang hoạt động
@apiVersion 2.1.0
@apiName CheckDomainIsActive
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            merchant_id                     Định danh của tennant
@apiParam   (BODY:)     {String}            protocol_domain                 Giao thức mạng + domain


@apiParamExample    {json}      BODY:
{
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "protocol_domain": "http://testdomain.vn/"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Kiểm tra domain có đang hoạt động

@apiSuccess {String}            data.is_active                <code>true:Đang-hoạt-động, false:Không-hoạt-động</code>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "is_active": false
    },
    "lang": "vi",
    "message": "request thành công."
}

"""
# ----------------------- Verify domain  -----------------------
"""

"""
# ----------------------- Get Cname default  -----------------------
"""
@api {GET} /api/v2.1/domain-public/actions/get-cname                   Get Cname default
@apiGroup Setup domain
@apiDescription Lấy thông tin Cname mặc định, nếu không có trả về rỗng
@apiVersion 2.1.0
@apiName GetCnameDefault

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa domain

@apiSuccess {String}            data.cname                    Tên cname default

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "cname": 'cname_default',
    }
}

"""
# ----------------------- Private update domain  -----------------------
"""
@api {PATCH} /api/v2.1/domain-public/actions/update-private                   Private update info domain
@apiGroup Setup domain
@apiDescription Chỉ có thể cập nhật các field trong body mẫu
@apiVersion 2.1.0
@apiName UpdateDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParamExample    {json}      BODY:
{
    "allow_delete": "allow",
    "create_by_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "date_start_verify": "20220910033256",
    "domain": "8h49am.10092022.update10h32am",
    "protocol": "http://",
    "domain_id": "f2d9d746-30aa-11ed-9fec-72587648ca46",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "protocol_domain": "https://8h49am.10092022.update10h32am",
    "status_check": "true",
    "status_default": "not_default",
    "status_verify": "truongcl",
    "update_on": "20220910050813"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Private update info domain


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""

# ============================================ PERSONAL DOMAIN PUBLIC ============================================
"""
@apiDefine ResponseDetailPersonalDomain

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của domain
@apiSuccess {String}            data.merchant_id                         Định danh của tennant
@apiSuccess {String}            data.domain_id                          Định danh của domain
@apiSuccess {String}            data.domain                             Tên domain
@apiSuccess {String}            data.protocol                           Giao thức mạng, nhận giá trị <code>http://, https://</code>
@apiSuccess {String}            data.status_default                     Định danh domain có phải default không, nhận giá trị <code>default, not_default</code>
@apiSuccess {String}            data.status_verify                      Định danh domain đã được xác thực chưa, nhận giá trị <code>verified, not_verified</code>
@apiSuccess {String}            data.allow_delete                       Định danh domain được phép xóa hay không,nhận giá trị <code>allow, not_allow</code>

@apiSuccess {String}            data.create_on                          Thời điểm khởi tạo
@apiSuccess {String}            data.update_on                          Thời điểm cập nhật

"""

# ----------------------- Get list Personal domain -----------------------
"""
@api {GET} /api/v2.1/domain-public/list              Lấy danh sách domain active
@apiGroup Setup domain
@apiDescription Lấy tất cả 
@apiVersion 2.1.0
@apiName GetListActiveDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách personal domain

@apiUse ResponseDetailPersonalDomain

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "id": "domain_id",
        "domain_id": "domain_id",
        "merchant_id": "merchant_id",        
        "domain": "ck.mobio.vn",
        "protocol": "https://"        
        "status_default": "default",        
        "status_verify": "verified",        
        "allow_delete": "allow",        
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }],
    
}

"""

"""
@api {POST} /api/v2.1/domain-public/check-active              Kiểm tra nhiều domain có đang hoạt động
@apiGroup Setup domain
@apiDescription Kiểm tra domain có đang hoạt động
@apiVersion 2.1.0
@apiName CheckMultiDomainActive
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {Array}            domains                    danh sách domain cần kiểm tra 


@apiParamExample    {json}      BODY:
{
    "domains": ["https://testdomain.vn/", "https://custom-domain.vn/"]
}


@apiSuccess {Array}            data                          danh sách domain hoạt động

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": ["https://testdomain.vn/"],
    "lang": "vi",
    "message": "request thành công."
}

"""

"""
@api {GET} /api/v2.1/domain-public/object-assign              Lấy danh sách đối tượng sử dụng domain
@apiGroup Setup domain
@apiDescription danh sách theo module, có phân trang 
@apiVersion 2.1.0
@apiName GetListObjectUseDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)     {String}              object_type         mã module: LANDING_PAGE, JOURNEY_BUILDER, SHORTEN_URL, SURVEY
@apiParam   (Query:)     {String}              protocol_domain     domain muốn xem nơi sử dụng 


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "object_type": "LANDING_PAGE",        
        "object_id": "024faf35-70fa-45ad-835d-1087ad326e90",        
        "domain": "https://ck.mobio.vn/",     
        "created_time": "2017-08-07T04:02:28Z",
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}

"""


# ----------------------- Verify doamin again  -----------------------
"""
@api {POST} /api/v2.1/domain-public/actions/retry-verify                   Xác thực lại domain
@apiGroup Setup domain
@apiVersion 2.1.0
@apiName RetryVerifyDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            domain                 Domain cần xác thực lại


@apiParamExample    {json}      BODY:
{
    "domain": "testdomain.vn"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Kiểm tra domain có đang hoạt động

@apiSuccess {String}            data.domain                   Tên domain
@apiSuccess {String}            data.status_verify            Trạng thái xác thực domain, nhận giá trị <code>not_verified</code>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "domain": "testdomain.vn",
        "status_verify": "not_verified"
    }
}

"""
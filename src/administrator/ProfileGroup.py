*********************************** Update ProfileGroup *************************************
* version: 1.0.0                                                                            *
*********************************************************************************************
"""
@api {patch} /api/v2.1/profile-groups/<profile_group_id> Update profile-group
@apiDescription API cập nhật profile-group
@apiGroup ProfileGroup
@apiVersion 1.0.0
@apiName UpdatePG

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Resources:)  {String}    profile_group_id  Định danh profile-group cần xem chi tiết.

@apiParam   (Body:)   {String}  name  Tên profile-group
@apiParam   (Body:)   {StringArray}  [keywords]  Danh sách keyword được gắn với profile-group
@apiParam   (Body:)   {StringArray}   [staffs]  Danh sách id của nhân viên được phân quyền quản lý profile-group này.<br />
<code>Note:</code> Danh sách dữ liệu mới sẽ được thay thế toàn bộ danh sách đã phân quyền trước đó.

@apiParamExample  {json}  Body:
{
  "name": "",
  "keywords": ["abc"],
  "staffs": ["0efe86d3-7eeb-40b4-81e9-9bdb33e8733c", "6bb848d6-3429-4a48-95c2-a86308d9929b"]
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
  "code": 200,
  "message": "Request thành công"
}
"""

*********************************** Get Detai ProfileGroup *******************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /api/v2.1/profile-groups/<profile_group_id> Lấy chi tiết profile-group
@apiDescription API lấy thông tin chi tiết profile-group
@apiGroup ProfileGroup
@apiVersion 1.0.0
@apiName DetailPG

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Resources:)  {String}    profile_group_id  Định danh profile-group cần xem chi tiết.

@apiSuccess   {ProfileGroup[]}  data  Danh sách profile-group phù hợp tiêu chí
@apiSuccess   (ProfileGroup)  {String}  id  Id của profile-group
@apiSuccess   (ProfileGroup)  {String}  code  Mã định danh của profile-group
@apiSuccess   (ProfileGroup)  {String}  name  Tên của profile-group
@apiSuccess   (ProfileGroup)  {StringArray}  keywords  Danh sách từ khoá nhận diện
@apiSuccess   (ProfileGroup)  {Staff[]}  [staffs]  Danh sách staff được phân công quản trị profile-group

@apiSuccess   (Staff)  {String}  id   Id của nhân viên
@apiSuccess   (Staff)  {String}  username   Username của nhân viên
@apiSuccess   (Staff)  {String}  fullname   Tên của nhân viên
@apiSuccess   (Staff)  {String}  avatar   Link ảnh đại diện
@apiSuccess   (Staff)  {Number=1-admin,2-normal}  is_admin   Đánh dấu tài khoản quản trị
@apiSuccess   (Staff)   {StringArray=detail, insight}   permissions   Danh sách quyền tương ứng của nhân viên với profile-group này
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
  "code": "ABC",
  "name": "",
  "keywords": ["abc", "abc1"],
  "staffs": [
    {
      "id": "5b50afe0-26fc-44eb-86dc-6002d320baca",
      "username": "staff1",
      "fullname": "Nguyễn Văn A",
      "avatar": "",
      "is_admin": 1,
      "permissions": ["detail"]
    },
    {
      "id": "0406a7b4-4bdd-4e34-96a9-3db64e75bb00",
      "username": "staff2",
      "fullname": "Nguyễn Văn B",
      "avatar": "",
      "is_admin": 0,
      "permissions": ["insight"]
    }
  ]
}
"""

********************************** List ProfileGroup *************************************
* version: 1.0.2                                                                         *
* version: 1.0.1                                                                         *
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /api/v2.1/profile-groups Lấy danh sách profile-group
@apiDescription API lấy danh sách profile-group
@apiGroup ProfileGroup
@apiVersion 1.0.2
@apiName ListPG

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)  {String}  [code]  Tìm kiếm chính xác profile-group theo mã. Nếu không tìm thấy theo mã sẽ tìm kiếm chính xác theo từ khoá.
@apiParam   (Query:)  {String}  [search]  Tìm kiếm profile-group theo mã, từ khoá, name
@apiParam   (Query:)  {String}  [merchant_ids]  Lọc profile-group theo danh sách merchant.<br />
Nếu không có tham số này, danh sách profile-group được lọc theo merchant_id trong header.<br />
Ví dụ: <code>merchant_ids=7b432feb-6831-4827-9f20-5eac34037a84,96ba630b-01</code>
@apiParam   (Query:)  {String}  [profile_group_ids]  Lấy profile-group theo danh sách ID. Nếu có tham số này, sẽ bỏ qua các tham số filter khác.
Ví dụ: <code>profile_group_ids=d36760b2-507a-4c80-bd1a-82d44af7f11b,7b4f5fa8-7f...</code>
@apiParam   (Query:)  {Number=0-FALSE,1-TRUE}  [is_default]  Lấy profile-group mặc định của tenant. Mỗi tenant sẽ chỉ có 1 profile-group mặc định.

@apiSuccess   {ProfileGroup[]}  data  Danh sách profile-group phù hợp tiêu chí
@apiSuccess   (ProfileGroup)  {String}  id  Id của profile-group
@apiSuccess   (ProfileGroup)  {String}  code  Mã định danh của profile-group
@apiSuccess   (ProfileGroup)  {String}  name  Tên của profile-group
@apiSuccess   (ProfileGroup)  {StringArray}  [keywords]  Danh sách từ khoá nhận diện
@apiSuccess   (ProfileGroup)  {String}  merchant_id  Định danh tenant sở hữu profile-group.
@apiSuccess   (ProfileGroup)  {Number}  [is_default]  Đánh dấu profile-group mặc định của 1 tenant.
@apiSuccess   (ProfileGroup)  {Object}  grant  Danh sách nhân viên được quyền truy cập vào profile-group được nhóm theo tenant.

@apiSuccess   (Staff)  {String}  id   Id của nhân viên.
@apiSuccess   (Staff)  {String}  username   Username của nhân viên.
@apiSuccess   (Staff)  {String}  fullname   Tên của nhân viên.
@apiSuccess   (Staff)  {Number=1-admin,2-normal}  is_admin   Đánh dấu tài khoản quản trị.
@apiSuccess   (Staff)   {StringArray=detail, insight}   permissions   Danh sách quyền tương ứng của nhân viên với profile-group này.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
      "code": "ABC",
      "name": "",
      "keywords": ["abc","abc1"],
      "merchant_id": "5fa3c120-fbc8-48f3-9bf9-e8b498cbb7af",
      "is_default": 1,
      "grant": {
        "1b99bdcf-d582-4f49-9715-1b61dfff3924": [
          {
            "id": "5b50afe0-26fc-44eb-86dc-6002d320baca",
            "username": "staff1",
            "fullname": "Nguyễn Văn A",
            "is_admin": 1,
            "permissions": ["detail"]
          },
          {
            "id": "0406a7b4-4bdd-4e34-96a9-3db64e75bb00",
            "username": "staff2",
            "fullname": "Nguyễn Văn B",
            "is_admin": 0,
            "permissions": ["insight"]
          }
        ],
        "f5423ea2-7e4b-4546-88d3-ec45138d9d11": [
          {
            "id": "8cbe3a56-9927-40b2-ac95-e9355b52be9e",
            "username": "staff3",
            "fullname": "Nguyễn Văn C",
            "is_admin": 1,
            "permissions": ["detail"]
          }
        ]
      }
    },
    {
      "id": "784e3f6b-e34a-4ea5-a585-8c2788d323b0",
      "code": "ABC1",
      "name": "",
      "keywords": ["abc1"],
      "merchant_id": "5fa3c120-fbc8-48f3-9bf9-e8b498cbb7af",
      "is_default": 0,
      "grant": {
        "1b99bdcf-d582-4f49-9715-1b61dfff3924": [
          {
            "id": "5b50afe0-26fc-44eb-86dc-6002d320baca",
            "username": "staff1",
            "fullname": "Nguyễn Văn A",
            "is_admin": 1,
            "permissions": ["detail"]
          },
          {
            "id": "0406a7b4-4bdd-4e34-96a9-3db64e75bb00",
            "username": "staff2",
            "fullname": "Nguyễn Văn B",
            "is_admin": 0,
            "permissions": ["insight"]
          }
        ]
      }
    }
  ]
}
"""
***********************************
"""
@api {get} /api/v2.1/profile-groups Lấy danh sách profile-group
@apiDescription API lấy danh sách profile-group
@apiGroup ProfileGroup
@apiVersion 1.0.1
@apiName ListPG

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)  {String}  [code]  Tìm kiếm chính xác profile-group theo mã. Nếu không tìm thấy theo mã sẽ tìm kiếm chính xác theo từ khoá.
@apiParam   (Query:)  {String}  [search]  Tìm kiếm profile-group theo mã, từ khoá, name
@apiParam   (Query:)  {String}  [merchant_ids]  Lọc profile-group theo danh sách merchant.<br />
Nếu không có tham số này, danh sách profile-group được lọc theo merchant_id trong header.<br />
Ví dụ: <code>merchant_ids=7b432feb-6831-4827-9f20-5eac34037a84,96ba630b-01</code>
@apiParam   (Query:)  {String}  [profile_group_ids]  Lấy profile-group theo danh sách ID. Nếu có tham số này, sẽ bỏ qua các tham số filter khác.
Ví dụ: <code>merchant_ids=d36760b2-507a-4c80-bd1a-82d44af7f11b,7b4f5fa8-7f...</code>
@apiParam   (Query:)  {Number=0-FALSE,1-TRUE}  [is_default]  Lấy profile-group mặc định của tenant. Mỗi tenant sẽ chỉ có 1 profile-group mặc định.

@apiSuccess   {ProfileGroup[]}  data  Danh sách profile-group phù hợp tiêu chí
@apiSuccess   (ProfileGroup)  {String}  id  Id của profile-group
@apiSuccess   (ProfileGroup)  {String}  code  Mã định danh của profile-group
@apiSuccess   (ProfileGroup)  {String}  name  Tên của profile-group
@apiSuccess   (ProfileGroup)  {StringArray}  keywords  Danh sách từ khoá nhận diện
@apiSuccess   (ProfileGroup)  {String}  keyword  <code>[DEPRECATED]</code>Danh sách từ khoá nhận diện. (Sử dụng <code>keywords</code> thay thế)
@apiSuccess   (ProfileGroup)  {Staff[]}  [staffs]  Danh sách staff được phân công quản trị profile-group

@apiSuccess   (Staff)  {String}  id   Id của nhân viên.
@apiSuccess   (Staff)  {String}  username   Username của nhân viên.
@apiSuccess   (Staff)  {String}  fullname   Tên của nhân viên.
@apiSuccess   (Staff)  {String}  avatar   Link ảnh đại diện.
@apiSuccess   (Staff)  {Number=1-admin,2-normal}  is_admin   Đánh dấu tài khoản quản trị.
@apiSuccess   (Staff)   {StringArray=detail, insight}   permissions   Danh sách quyền tương ứng của nhân viên với profile-group này.
@apiSuccess   (Staff)   {Number}  [crm_type]  Kiểu support hệ thống CRM.
@apiSuccess   (Staff)   {Number}  [is_default]  Đánh dấu profile-group mặc định của 1 tenant.
@apiSuccess   (Staff)   {Number}  [version_code]  Phiên bản code phân tích/xử lý profile-group.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
      "code": "ABC",
      "name": "",
      "keywords": ["abc"],
      "keyword": "abc",
      "staffs": [
        {
          "id": "5b50afe0-26fc-44eb-86dc-6002d320baca",
          "username": "staff1",
          "fullname": "Nguyễn Văn A",
          "avatar": "",
          "is_admin": 1,
          "permissions": ["detail"]
        },
        {
          "id": "0406a7b4-4bdd-4e34-96a9-3db64e75bb00",
          "username": "staff2",
          "fullname": "Nguyễn Văn B",
          "avatar": "",
          "is_admin": 0,
          "permissions": ["insight"]
        }
      ]
    },
    {
      "id": "784e3f6b-e34a-4ea5-a585-8c2788d323b0",
      "code": "ABC1",
      "name": "",
      "keywords": ["abc1"],
      "keyword": "abc1",
      "staffs": [
        {
          "id": "44799cb4-9936-480c-b332-8cc28b2cf7a4",
          "username": "staff1",
          "fullname": "Nguyễn Văn A",
          "avatar": "",
          "is_admin": 1,
          "permissions": ["detail"]
        },
        {
          "id": "ffc59f31-6c51-48c4-a271-6439d63ce1b5",
          "username": "staff3",
          "fullname": "Nguyễn Văn C",
          "avatar": "",
          "is_admin": 0,
          "permissions": ["detail"]
        }
      ]
    }
  ]
}
"""
***********************************
"""
@api {get} /api/v2.1/profile-groups Lấy danh sách profile-group
@apiDescription API lấy danh sách profile-group
@apiGroup ProfileGroup
@apiVersion 1.0.0
@apiName ListPG

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)  {String}  [search]  Tìm kiếm profile-group theo mã, từ khoá
@apiParam   (Query:)  {String}  [merchant_ids]  Lọc profile-group theo danh sách merchant.<br />
Nếu không có tham số này, danh sách profile-group được lọc theo merchant_id trong header.<br />
Ví dụ: <code>merchant_ids=7b432feb-6831-4827-9f20-5eac34037a84,96ba630b-01</code>
@apiParam   (Query:)  {String}  [profile_group_ids]  Lấy profile-group theo danh sách ID. Nếu có tham số này, sẽ bỏ qua các tham số filter khác.
Ví dụ: <code>merchant_ids=d36760b2-507a-4c80-bd1a-82d44af7f11b,7b4f5fa8-7f...</code>
@apiParam   (Query:)  {Number=0-FALSE,1-TRUE}  [is_default]  Lấy profile-group mặc định của tenant. Mỗi tenant sẽ chỉ có 1 profile-group mặc định.

@apiSuccess   {ProfileGroup[]}  data  Danh sách profile-group phù hợp tiêu chí
@apiSuccess   (ProfileGroup)  {String}  id  Id của profile-group
@apiSuccess   (ProfileGroup)  {String}  code  Mã định danh của profile-group
@apiSuccess   (ProfileGroup)  {String}  name  Tên của profile-group
@apiSuccess   (ProfileGroup)  {StringArray}  keywords  Danh sách từ khoá nhận diện
@apiSuccess   (ProfileGroup)  {String}  keyword  <code>[DEPRECATED]</code>Danh sách từ khoá nhận diện. (Sử dụng <code>keywords</code> thay thế)
@apiSuccess   (ProfileGroup)  {Staff[]}  [staffs]  Danh sách staff được phân công quản trị profile-group

@apiSuccess   (Staff)  {String}  id   Id của nhân viên.
@apiSuccess   (Staff)  {String}  username   Username của nhân viên.
@apiSuccess   (Staff)  {String}  fullname   Tên của nhân viên.
@apiSuccess   (Staff)  {String}  avatar   Link ảnh đại diện.
@apiSuccess   (Staff)  {Number=1-admin,2-normal}  is_admin   Đánh dấu tài khoản quản trị.
@apiSuccess   (Staff)   {StringArray=detail, insight}   permissions   Danh sách quyền tương ứng của nhân viên với profile-group này.
@apiSuccess   (Staff)   {Number}  [crm_type]  Kiểu support hệ thống CRM.
@apiSuccess   (Staff)   {Number}  [is_default]  Đánh dấu profile-group mặc định của 1 tenant.
@apiSuccess   (Staff)   {Number}  [version_code]  Phiên bản code phân tích/xử lý profile-group.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
      "code": "ABC",
      "name": "",
      "keywords": ["abc"],
      "keyword": "abc",
      "staffs": [
        {
          "id": "5b50afe0-26fc-44eb-86dc-6002d320baca",
          "username": "staff1",
          "fullname": "Nguyễn Văn A",
          "avatar": "",
          "is_admin": 1,
          "permissions": ["detail"]
        },
        {
          "id": "0406a7b4-4bdd-4e34-96a9-3db64e75bb00",
          "username": "staff2",
          "fullname": "Nguyễn Văn B",
          "avatar": "",
          "is_admin": 0,
          "permissions": ["insight"]
        }
      ]
    },
    {
      "id": "784e3f6b-e34a-4ea5-a585-8c2788d323b0",
      "code": "ABC1",
      "name": "",
      "keywords": ["abc1"],
      "keyword": "abc1",
      "staffs": [
        {
          "id": "44799cb4-9936-480c-b332-8cc28b2cf7a4",
          "username": "staff1",
          "fullname": "Nguyễn Văn A",
          "avatar": "",
          "is_admin": 1,
          "permissions": ["detail"]
        },
        {
          "id": "ffc59f31-6c51-48c4-a271-6439d63ce1b5",
          "username": "staff3",
          "fullname": "Nguyễn Văn C",
          "avatar": "",
          "is_admin": 0,
          "permissions": ["detail"]
        }
      ]
    }
  ]
}
"""

*********************************** Delete ProfileGroup *************************************
* version: 1.0.0                                                                            *
*********************************************************************************************
"""
@api {delete} /api/v2.1/profile-groups   Xoá profile-group
@apiDescription API xoá profile-group
@apiGroup ProfileGroup
@apiVersion 1.0.0
@apiName DeletePG

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)  {String}  ids   Danh sách id các profile-group cần xoá.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "deleted": ["17a3307d-c7ee-47d5-9bbc-2ed0bfc5e4c2", "40d67f0e-876e-483c-86b5-efca27735577"]
  "code": 200,
  "message": "Request thành công"
}
"""

*********************************** Create ProfileGroup *************************************
* version: 1.0.1                                                                            *
* version: 1.0.0                                                                            *
*********************************************************************************************
"""
@api {post} /api/v2.1/profile-groups Create profile-group
@apiDescription API tạo profile-group
@apiGroup ProfileGroup
@apiVersion 1.0.1
@apiName CreatePG

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)   {String}  code  Mã profile-group
@apiParam   (Body:)   {String}  name  Tên profile-group
@apiParam   (Body:)   {String}  [sub_brand_id]  Định danh của sub brand. Trong trường hợp tạo profile-group cho sub-brand. <br />
Nếu không có field này, profile-group mặc định được tạo cho tenant của nhân viên đang login.
@apiParam   (Body:)   {StringArray}  [keywords]  Danh sách keyword được gắn với profile-group
@apiParam   (Body:)   {StringArray}   [staffs]  Danh sách id của nhân viên được phân quyền quản lý profile-group này.
@apiParam   (Body:)   {String}  [description]   Mô tả
@apiParamExample  {json}  Body:
{
  "code": "ABC",
  "name": "",
  "sub_brand_id": "bdae6f6a-7f40-4669-b2d5-5a1011e3f188",
  "keywords": ["abc"],
  "staffs": ["0efe86d3-7eeb-40b4-81e9-9bdb33e8733c", "6bb848d6-3429-4a48-95c2-a86308d9929b"],
  "description": "mô tả"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
  "code": 200,
  "message": "Request thành công"
}
"""
**************************
"""
@api {post} /api/v2.1/profile-groups Create profile-group
@apiDescription API tạo profile-group
@apiGroup ProfileGroup
@apiVersion 1.0.0
@apiName CreatePG

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)   {String}  code  Mã profile-group
@apiParam   (Body:)   {String}  name  Tên profile-group
@apiParam   (Body:)   {String}  [sub_brand_id]  Định danh của sub brand. Trong trường hợp tạo profile-group cho sub-brand. <br />
Nếu không có field này, profile-group mặc định được tạo cho tenant của nhân viên đang login.
@apiParam   (Body:)   {StringArray}  [keywords]  Danh sách keyword được gắn với profile-group
@apiParam   (Body:)   {StringArray}   [staffs]  Danh sách id của nhân viên được phân quyền quản lý profile-group này.
@apiParamExample  {json}  Body:
{
  "code": "ABC",
  "name": "",
  "sub_brand_id": "bdae6f6a-7f40-4669-b2d5-5a1011e3f188",
  "keywords": ["abc"],
  "staffs": ["0efe86d3-7eeb-40b4-81e9-9bdb33e8733c", "6bb848d6-3429-4a48-95c2-a86308d9929b"]
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
  "code": 200,
  "message": "Request thành công"
}
"""

"""
@api {GET} /api/v2.1/profile-groups/for-account Lấy danh sách profile group của account 
@apiDescription  lấy thông tin 1 account 
@apiVersion 2.1.0
@apiGroup ProfileGroup
@apiName AccountGetProfileGroup
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiParam   (Query:)    {String}  account_id         id tài khoản

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
	"code": 200,
	"data": [{
		"code": "PINGCOMSHOP",
		"name": "PingcomShop",
		"permission": "detail",
		"profile_group_id": "541005c4-3dff-4063-8e77-66cc140015b8",
		"status": 1
	}],
	"lang": "vi",
	"message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/profile-groups/for-merchant Lấy danh sách profile group của merchant 
@apiDescription  lấy tất cả profile group của merchant 
@apiVersion 2.1.0
@apiGroup ProfileGroup
@apiName MerchantGetProfileGroup
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header



@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
      {
          "code": "PINGCOMSHOP",
          "is_default": 1,
          "name": "PingcomShop",
          "profile_group_id": "541005c4-3dff-4063-8e77-66cc140015b8"
      }
  ],
  "lang": "vi",
  "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/profile-groups/list Lấy danh sách profile group của tài khoản 
@apiDescription  lấy thông tin cơ bản 
@apiVersion 2.1.0
@apiGroup ProfileGroup
@apiName GetProfileGroupBase
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500


@apiUse merchant_id_header
@apiUse paging
@apiUse order_sort

@apiParam   (Query:)  {String}  [code]  Tìm kiếm chính xác profile-group theo mã. Nếu không tìm thấy theo mã sẽ tìm kiếm chính xác theo từ khoá.
@apiParam   (Query:)  {String}  [search]  Tìm kiếm profile-group theo mã, từ khoá, name
@apiParam   (Query:)  {String}  [profile_group_ids]  Lấy profile-group theo danh sách ID. Nếu có tham số này, sẽ bỏ qua các tham số filter khác.
Ví dụ: <code>profile_group_ids=d36760b2-507a-4c80-bd1a-82d44af7f11b,7b4f5fa8-7f...</code>
@apiParam   (Query:)  {Number=0-FALSE,1-TRUE}  [is_default]  Lấy profile-group mặc định của tenant. Mỗi tenant sẽ chỉ có 1 profile-group mặc định.

@apiSuccess   {ProfileGroup[]}  data  Danh sách profile-group phù hợp tiêu chí
@apiSuccess   (ProfileGroup)  {String}  id  Id của profile-group
@apiSuccess   (ProfileGroup)  {String}  code  Mã định danh của profile-group
@apiSuccess   (ProfileGroup)  {String}  name  Tên của profile-group
@apiSuccess   (ProfileGroup)  {StringArray}  [keywords]  Danh sách từ khoá nhận diện
@apiSuccess   (ProfileGroup)  {String}  merchant_id  Định danh tenant sở hữu profile-group.
@apiSuccess   (ProfileGroup)  {Number}  [is_default]  Đánh dấu profile-group mặc định của 1 tenant.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "18a39bb4-b24b-4b05-bbb3-16cd496b6a50",
      "code": "ABC",
      "name": "",
      "keywords": ["abc","abc1"],
      "merchant_id": "5fa3c120-fbc8-48f3-9bf9-e8b498cbb7af",
      "is_default": 1,
      
    },
    {
      "id": "784e3f6b-e34a-4ea5-a585-8c2788d323b0",
      "code": "ABC1",
      "name": "",
      "keywords": ["abc1"],
      "merchant_id": "5fa3c120-fbc8-48f3-9bf9-e8b498cbb7af",
      "is_default": 0,
      
    }
  ],
  "paging": {
    "page": 1,
    "per_page": 5,
    "total_count": 220,
    "total_page": 44
  }
}
"""



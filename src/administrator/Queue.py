# ******************************* Get list user by filter ****************************
"""
@api {QUEUE} - [QUEUE] User Filter Audience
@apiDescription Validate filter
@apiGroup WorkFlow
@apiVersion 1.0.0
@apiName UserFilterAudience

@apiParam      (Consumer:)     {topic_name}   topic_name  workflow-user-filter-audience
@apiParam      (Consumer:)     {topic_group}   topic_group  workflow-user-filter-group-audience

@apiParam      (Input:)     {String}   merchant_id                  ID định danh công ty
@apiParam      (Input:)     {Array}    audiences_filter       Danh sách nhóm audience (source USER)
@apiParam      (Input:)     {Array}    audiences_filter.profile_filter       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Input:)     {Int}      [per_page]    Số user trả về callback, <code>default: 10</code>
@apiParam      (Input:)     {Object}   callback  cấu hình callback
@apiParam      (Input:)     {Object}  callback.queue_config                 Cấu hình queue
@apiParam      (Input:)     {String}  callback.queue_config.key             Key của queue
@apiParam      (Input:)     {String}  callback.queue_config.target          Tên topic cần bắn message vào
@apiParam      (Input:)     {Object}   callback.data  data trả về qua callback
@apiParam      (Input:)     {String}   workflow_id  Workflow ID

@apiUse operator_key_body

@apiParamExample [json] Input example:
{
    "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
    "per_page": 10,
    "audiences_filter": [
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_account_fullname",
                    "operator_key": "op_is_not_empty",
                    "values": []
                },
                {
                    "criteria_key": "cri_account_block",
                    "operator_key": "op_is_in",
                    "values": [
                        "KHCN"
                    ]
                }
            ],
            "position":0,
            "operator": None
        },
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_account_fullname",
                    "operator_key": "op_is_not_in",
                    "values": [
                        "may 1"
                    ]
                }
            ],
            "position":1,
            "operator": "exclude"
        },
    ],
    "workflow_id": "ce1ba0ce-1fa1-11ee-b3d9-5e7267e51fab",
    "callback": {
        "callback_type": "queue",
        'queue_config': {
            'key': '',
            'target': 'workflow-user-filter-audience'
        },
        'data': {
            'merchant_id': '1b99bdcf-d582-4f49-9715-1b61dfff3924',
            'target_type': 'USER',
            "workflow_id": "ce1ba0ce-1fa1-11ee-b3d9-5e7267e51fab",
        }
    }
}

@apiSuccessExample  {json}  Callback Queue:
{
    "account_ids":[
        "02f61a06-f83f-4d6d-b870-ca2d45320faf"
    ],
    "data_callback": {
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "target_type": "USER",
        "workflow_id": "ce1ba0ce-1fa1-11ee-b3d9-5e7267e51fab",
        ...
    },
    "workflow_id": "ce1ba0ce-1fa1-11ee-b3d9-5e7267e51fab",
    "last_page": true,
    "total_count": 1
}
"""

# ******************************* Get account_detail ****************************
"""
@api {QUEUE} - [QUEUE] Get account detail
@apiDescription Validate filter
@apiGroup WorkFlow
@apiVersion 1.0.0
@apiName AccountDetail

@apiParam      (Consumer:)     {topic_name}   topic_name  workflow-account-detail
@apiParam      (Consumer:)     {topic_group}   topic_group  workflow-account-detail-group

@apiParam      (Input:)     {Object}    query_params
@apiParam      (Input:)     {Array}    query_params.account_ids Danh sách account_ids
@apiParam      (Input:)     {Array}    query_params.selected_type Danh sách type cần lấy
@apiParam      (Input:)     {Array}    query_params.field_personalize Danh sách field cá nhân hoá
@apiParam      (Input:)     {Array}    query_params.module_name Danh sách module của account (SALE, TICKET...)
@apiParam      (Input:)     {Array}    [query_params.team_ids] Danh sách mã định danh team_id cần lấy thông tin quản lý
@apiParam      (Input:)     {Object}   callback  cấu hình callback
@apiParam      (Input:)     {Object}  callback.queue_config                 Cấu hình queue
@apiParam      (Input:)     {String}  callback.queue_config.key             Key của queue
@apiParam      (Input:)     {String}  callback.queue_config.target          Tên topic cần bắn message vào
@apiParam      (Input:)     {Object}   callback.data  data trả về qua callback

@apiParam (SelectedType:) {String} push_ids Lấy thông tin push_id
@apiParam (SelectedType:) {String} manager_detail Lấy thông tin người quản lý

@apiParamExample [json] Input example:
{
    "query_params": {
        "account_ids": ["07873e41-4016-4cf6-86ed-047ebd462fc1"],
        "selected_type": ["account_detail", "rm_info", "push_ids", "manager_detail"],
        "field_personalize": ["fullname", "email"]
        "module_name": ["SALE"],
        "team_ids": ["ccf4be40-18da-11ef-aa8c-57b35d9e6000"]
    },
    "callback": {
        "callback_type": "queue",
        'queue_config': {
            'key': '',
            'target': 'workflow-user-filter-audience'
        },
        'data': {
            'merchant_id': '1b99bdcf-d582-4f49-9715-1b61dfff3924',
            'target_type': 'USER',
            "workflow_id": "ce1ba0ce-1fa1-11ee-b3d9-5e7267e51fab",
        }
    }
}

@apiSuccessExample  {json}  Callback Queue:
{
    "data": [
        {
            "account_id": "05e34489-8e82-45c6-93d1-99ed9dc893a7",
            "account_detail": {
                "email": "<EMAIL>",
                "merchant_id": "14d3dd4d-b4a2-4911-8d4b-1a8ca841d812",
                "fullname": "Dat Le",
                ...
            }
            "rm_info": {
                "account_id": "05e34489-8e82-45c6-93d1-99ed9dc893a7",
                "block": ["KHDN"]
                "block_mapping": [],
                "department_mapping": [],
                ...
            },
            "push_ids": [
                {
                    "device_id": "*********-**********-34304830940384uf-09t04594",
                    "device_operating_system": "ios",
                    "push_id": "4343-3453fedf-dfdfde0f9034e-df0d9fd0fdf-3ere32422-wefiefieif3-2342432rowokow",
                    "sandbox": true
                }
            ],
        },
        ...
    ]
    "data_callback": {
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "target_type": "USER",
        "workflow_id": "ce1ba0ce-1fa1-11ee-b3d9-5e7267e51fab",
        ...
    }
}
"""

# Consummer admin-account-event
"""
@api {QUEUE} - [QUEUE] Save account event and send data to Workflow
@apiDescription Save account event and send data to Workflow
@apiGroup WorkFlow
@apiVersion 1.0.0
@apiName AccountEvent

@apiParam      (Consumer:)     {topic_name}   topic_name  admin-account-event
@apiParam      (Consumer:)     {topic_group}   topic_group  group-admin-account-event

@apiParam      (Input:)     {String}        event_type Loại event
                                            <ul> Nhận các event
                                                <ul><strong>Staff information</strong>
                                                    <li><code>change_staff_info</code>: Thay đổi thông tin nhân viên</li>
                                                    <li><code>staff_status</code>: Trạng thái của nhân viên thay đổi</li>
                                                    <li><code>change_team_role</code>: Thay đổi vai trò trong team</li>
                                                    <li><code>add_to_team</code>: Thêm nhân viên vào team</li>
                                                    <li><code>remove_from_team</code>: Gỡ nhân viên khỏi team</li>
                                                </ul>
                                            </ul>
@apiParam      (Input:)     {String}        merchant_id                 Mã định danh tenant
@apiParam      (Input:)     {Array}         event_data                  Dữ liệu event
                                                                        <ul>
                                                                            <li>Dữ liệu của các event liên quan đến Staff information <a href="https://dev.mobio.vn/docs/administrator/#api-WorkFlow-StaffChange">Tại đây</a></li>
                                                                        </ul>
@apiParam      (Input:)     {String}        source                      Nguồn dữ liệu
                                                                        <li>Nhận các giá trị <code>user</code></li>
@apiParam      (Input:)     {String}        [line_event]                Action thuộc cột nào <code>system, account</code>
@apiParam      (Input:)     {Object}        [object_update]             Đối tượng thực hiện action, là tài khoản hoặc hệ thống: <code>system, account</code>
@apiParam      (Input:)     {Timestamp}     action_time                 Thời gian thực hiện event
@apiParam      (Input:)     {String}        [account_update]            Tài khoản thực hiện event

@apiParamExample [json] Input example staff_info:
{
    "event_type": "change_staff_info",
    "event_data": [
        {
            "account_id": "ce44a28e-1499-43af-9199-4d033f5cf4c0",
            "old_data": {
                "username": "hoai_khdn_longthanh@eib",
                "fullname": "Hoài - KHDN (CN Long Thành)",
                "phone_number": "+***********",
                "email": "<EMAIL>",
                "role_group": "admin",
                "staff_code": "hoai_khdn_longthanh",
                "role": [
                    "00083d7d-e10b-41df-9b19-9e7b1d909d0f",
                    "08fa0ed4-c751-40ac-9ac1-1d03799338f8"
                ],
                ...
            },
            "new_data": {
                "username": "hoai_khdn_longthanh@eib",
                "fullname": "Hoài",
                "phone_number": "+***********",
                "email": "<EMAIL>",
                "staff_code": "hoai_khdn_longthanh_1",
                "role_group": "admin",
                "role": [
                    "9d2bf862-ab0c-41dc-aab0-bb709126d2f3",
                    "63f92702-438e-44eb-88fb-203e9fe3da05",
                ],
                ...
            }
        }
    ],
    "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
    "source": "admin",
    "line_event": "account",
    "object_update": "account",
    "action_time": **********,
    "account_update": "65eebbe6-24c3-416a-bbe4-0aebe92f6695"
}

@apiSuccessExample  {json}  Response data khi user thoả mãn cấu hình trigger:
{
    "account_id": "xxxxxxxxxxx",
    "team_ids": ["ccf4be40-18da-11ef-aa8c-57b35d9e6000"],
    "data_callback": {
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "target_type": "USER",
        "workflow_id": "ce1ba0ce-1fa1-11ee-b3d9-5e7267e51fab",
        ...
    }
}
"""



# ******************************* Check user condition ****************************
"""
@api {QUEUE} - [QUEUE] Check user Condition
@apiDescription Kiểm tra user thoả mãn điều kiện filter
@apiGroup WorkFlow
@apiVersion 1.0.0
@apiName CheckUserCondition

@apiParam      (Consumer:)     {topic_name}   topic_name  workflow-check-user-condition
@apiParam      (Consumer:)     {topic_group}   topic_group  workflow-check-user-condition-group

@apiParam      (Input:)     {String}   merchant_id                  ID định danh công ty
@apiParam      (Input:)     {String}   account_ids                  Danh sách ID tài khoản
@apiParam      (Input:)     {Array}    audiences_filter       Danh sách nhóm audience (source USER)
@apiParam      (Input:)     {Array}    audiences_filter.profile_filter       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Input:)     {Object}   callback  cấu hình callback
@apiParam      (Input:)     {Object}  callback.queue_config                 Cấu hình queue
@apiParam      (Input:)     {String}  callback.queue_config.key             Key của queue
@apiParam      (Input:)     {String}  callback.queue_config.target          Tên topic cần bắn message vào
@apiParam      (Input:)     {Object}   callback.data  data trả về qua callback

@apiUse operator_key_body

@apiSuccess (data) {String} 	account_id					ID tài khoản
@apiSuccess (data) {String} 	satisfied				  Trạng thái acount_id thoả mãn bộ lọc hay không <code>true/false</code>
@apiSuccess (data) {String} 	data_callback			Dữ liệu callback

@apiParamExample [json] Input example:
{
    "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
    "account_ids": ["ce44a28e-1499-43af-9199-4d033f5cf4c0"]
    "audiences_filter": [
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_account_fullname",
                    "operator_key": "op_is_not_empty",
                    "values": []
                },
                {
                    "criteria_key": "cri_account_block",
                    "operator_key": "op_is_in",
                    "values": [
                        "KHCN"
                    ]
                }
            ],
            "position":0,
            "operator": None
        },
        {
            "profile_filter": [
                {
                    "criteria_key": "cri_account_fullname",
                    "operator_key": "op_is_not_in",
                    "values": [
                        "may 1"
                    ]
                }
            ],
            "position":1,
            "operator": "exclude"
        },
    ],
    "callback": {
        "callback_type": "queue",
        'queue_config': {
            'key': '',
            'target': 'topic name'
        },
        'data': {
            'merchant_id': '1b99bdcf-d582-4f49-9715-1b61dfff3924',
            'target_type': 'USER',
            "workflow_id": "ce1ba0ce-1fa1-11ee-b3d9-5e7267e51fab",
        }
    }
}

@apiSuccessExample  {json}  Callback Queue:
{
    "account_id": "",
    "satisfied": true,
    "data_callback": {
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "target_type": "USER",
        "workflow_id": "ce1ba0ce-1fa1-11ee-b3d9-5e7267e51fab",
        ...
    }
}
"""

"""
@api body_event_data_staff_change              Data event liên quan tới thông tin account
@apiGroup WorkFlow
@apiVersion 1.0.0
@apiName StaffChange

@apiParam (event_data:) {String} account_id Id tài khoản
@apiParam (event_data:) {String} old_data Data cũ
@apiParam (event_data:) {String} new_data Data mới

@apiParamExample [json] Input example change_staff_info:
{
    "event_data": [
        {
            "account_id": "ce44a28e-1499-43af-9199-4d033f5cf4c0",
            "old_data": {
                "username": "hoai_khdn_longthanh@eib",
                "fullname": "Hoài - KHDN (CN Long Thành)",
                "phone_number": "+***********",
                "email": "<EMAIL>",
                "role_group": "admin",
                "staff_code": "hoai_khdn_longthanh",
                "role": [
                    "00083d7d-e10b-41df-9b19-9e7b1d909d0f",
                    "08fa0ed4-c751-40ac-9ac1-1d03799338f8"
                ],
                ...
            },
            "new_data": {
                "username": "hoai_khdn_longthanh@eib",
                "fullname": "Hoài",
                "phone_number": "+***********",
                "email": "<EMAIL>",
                "staff_code": "hoai_khdn_longthanh_1",
                "role_group": "admin",
                "role": [
                    "9d2bf862-ab0c-41dc-aab0-bb709126d2f3",
                    "63f92702-438e-44eb-88fb-203e9fe3da05",
                ],
                ...
            }
        }
    ]
}

"""
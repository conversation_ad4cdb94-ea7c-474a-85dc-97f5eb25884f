#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" 
    Author: tungdd
    Date created: 07/11/2023
"""
"""
@api {QUEUE}     topic_name:admin-sync-data-etl     Topic sync data ETL
@apiDescription Sync data from etl, sync account hoặc sync KPI
@apiGroup SyncETL
@apiVersion 1.0.0
@apiName  AdminSyncDataETL

@apiParam	(BODY:)			{String}	merchant_id			        Định danh tenant phát sinh
@apiParam	(BODY:)			{String}	data_type			        Kiểu dữ liệu ETL
                                                                    <ul>
                                                                        <li><code>eib_staff_info</code>: Luồng ETL thông tin nhân viên từ EIB</li>
                                                                        <li><code>eib_staff_info_ihrp</code>: Sync data account IHRP</li>
                                                                    </ul>
@apiParam	(BODY:)			{Array}	    data	                    Dữ liệu ETL. <code>Tối đa 50 phần tử</code>

@apiParam   (BODY:)         {object}        [callback]                  <code>Dữ liệu callback</code>
@apiParam   (BODY:)         {object}        [callback.callback_type]    Dạng callback
                                                                        <ul>
                                                                            <li>queue : call back qua queue</li>
                                                                        </ul>
@apiParam   (BODY:)         {object}        [callback.queue_config]     Cấu hình callback qua queue
@apiParam   (BODY:)         {object}        [callback.queue_config.key] Key 
@apiParam   (BODY:)         {object}        [callback.queue_config.target] Tên topic send message 
@apiParam   (BODY:)         {object}        [callback.data]         Dữ liệu callback


@apiParamExample {json} Body example data type: eib_staff_info
{
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
    "data_type":"eib_staff_info",
    "data": [
        {
            "employee_number": "",
            "name": "",
            "last_name": "",
            "first_name": "",
            "cap_bac": "",
            "date_of_joining": "",
            "branch": "",
            "branch_name": "",
            "tier": "",
            "date_off": "",
            "date_on_month":"",
            "status": "",
            "date_add": ""
        }
    ],
    "callback": {
        "callback_type": "queue",
        "queue_config": {
            "key": 12356,
            "target": "callback_pvcb_sync_admin"
        },
        "data": { 
            "status": 1,
            "employee_code": "employee_code",
            "value_date": "value_date"
        }
    }
}

@apiParamExample {json} Body example data type: eib_staff_info_ihrp
{
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
    "data_type":"eib_staff_info_ihrp",
    "data": [
        {
            "fullname": "",
            "staff_code": ""
            "position_code": "",
            "position_name": "", 
            "department_code": "",
            "department_name": "",
            "phone_number": "",
            "email": "",
            "status": "",
            "sol_id": "",
            "branch_code_level_1": "",
            "branch_name_level_1": "",
            "branch_code_level_2": "",
            "branch_name_level_2": "",
            "branch_code_level_3": "",
            "branch_name_level_3": "",
            "branch_code_level_4": "",
            "branch_name_level_4": "",
            "branch_code_level_5": "",
            "branch_name_level_5": "",
            "data_date": "",
        }
    ],
    "callback": {
        "callback_type": "queue",
        "queue_config": {
            "key": 12356,
            "target": "callback_pvcb_sync_admin"
        },
        "data": { 
            "status": 1,
            "employee_code": "employee_code",
            "value_date": "value_date"
        }
    }
}



"""

"""
@api {GET} /api/v2.1/manage/info-extra Danh sách thông tin mở rộng của tài khoản 
@apiDescription lấy tất cả theo loại thông tin như: chức danh, phòng, khối, tier, sol(dvkd)
@apiVersion 1.0.0
@apiGroup SyncETL
@apiName GetListInfoExtra 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)  {string}  info_type   loại thông tin gồm: position, department, sol, tier, block, area
@apiParam   (Query:)  {string}  [code]    tìm theo mã. nhiều giá trị cách nhau ","

@apiParam (info_type value) {String} position Chức danh
@apiParam (info_type value) {String} department Phòng ban
@apiParam (info_type value) {String} sol Đơn vị kinh doanh
@apiParam (info_type value) {String} tier Tầng
@apiParam (info_type value) {String} block Khối
@apiParam (info_type value) {String} area Khu vực
@apiParam (info_type value) {String} group_department Khu vực



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "info_type": "position",
      "value_code": "CD045",
      "value_name": "Trưởng phòng_HO",
      "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    }
  ]
}
"""


"""
@api {GET} /api/v2.1/manage/role_group Danh sách thông tin nhóm quyền
@apiDescription Lấy danh sách nhóm quyền admin/manager/user
@apiVersion 1.0.0
@apiGroup SyncETL
@apiName GetRoleGroup 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)  {string}  [code]    tìm theo mã. nhiều giá trị cách nhau "," 


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
        "value_code": "admin",
        "value_name": "Admin"
    
  ]
}
"""


"""
@api {GET} api/v2.1/manage/mapping/scope Lấy mã cấp quản lý tương ứng với đơn vị kinh doanh 
@apiDescription Lấy mã cấp quản lý tương ứng với đơn vị kinh doanh 
@apiVersion 1.0.0
@apiGroup SyncETL
@apiName GetListScopeByMapValue 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)  {string}  code   Mã đơn vị kinh doanh, nhiều giá trị cách nhau dấu ;



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
            "delimiter": "#",
            "mapping_value": "1828",
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "normalize_name_code": "phong giao dich ngai giao",
            "number_level": 3,
            "scope_code": "KVDNB#1803#1828",
            "scope_name": "Phòng Giao Dịch Ngãi Giao"
        }
  ]
}
"""


"""
@api {post} /api/v2.1/manage/info-extra/upsert-bulk upsert thông tin mở rộng 
@apiDescription upsert nhiều theo loại 
@apiVersion 1.0.0
@apiGroup SyncETL
@apiName UpsertBulkInfoExtra  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {Array} data danh sách thông tin upsert 
@apiParam (data:)  {string} info_type loại thông tin gồm: position, department, sol, tier, block, area
@apiParam (data:)  {string} value_code mã định danh thông tin  
@apiParam (data:)  {string} value_name tên thông tin 

@apiParamExample {json} data
{
  "info_type": "position",
  "value_code": "CD045",
  "value_name": "Trưởng phòng_HO",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {post} /api/v2.1/manage/info-extra/upload-file upsert thông tin mở rộng bằng file  
@apiDescription xóa hết dữ liệu cũ và thêm lại từ file 
@apiVersion 1.0.0
@apiGroup SyncETL
@apiName UploadFileInfoExtra  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {File} file thông tin 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/manage/mapping Danh sách thông tin mapping 
@apiDescription lấy tất cả theo loại như: chức danh -> khối
@apiVersion 1.0.0
@apiGroup SyncETL
@apiName GetListMapping 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)  {string}  mapping_type   loại thông tin gồm: position_to_block, ...
@apiParam   (Query:)  {string}  [source_value]    tìm theo mã. nhiều giá trị cách nhau ";"



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "mapping_type": "position_to_block",
      "source_value": "CD045",
      "destination_value": "KHCN",
      "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    }
  ]
}
"""

"""
@api {post} /api/v2.1/manage/mapping/upsert-bulk upsert thông tin mapping 
@apiDescription upsert nhiều theo loại 
@apiVersion 1.0.0
@apiGroup SyncETL
@apiName UpsertBulkMapping  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {Array} data danh sách thông tin upsert 
@apiParam (data:)  {string} mapping_type loại thông tin gồm: position_to_block, ...
@apiParam (data:)  {string} source_value giá trị nguồn   
@apiParam (data:)  {string} destination_value giá trị đích 

@apiParamExample {json} data
{
  "mapping_type": "position_to_block",
  "source_value": "CD045",
  "destination_value": "KHCN",
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {post} /api/v2.1/manage/mapping/upload-file upsert thông tin mapping bằng file  
@apiDescription xóa hết dữ liệu cũ và thêm lại từ file 
@apiVersion 1.0.0
@apiGroup SyncETL
@apiName UploadFileMapping 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {File} file thông tin 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} /api/v2.1/manage/group-position/by-level Danh sách nhóm chức danh theo level 
@apiDescription ko truyền level thì lấy tất cả nhóm chức danh 
@apiVersion 1.0.0
@apiGroup SyncETL
@apiName GetListGroupPositionByLevel  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)  {string}  [level]    level. nhiều giá trị cách nhau "," (level_1, level_2, level_3, level_4, level_5, level_6)

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "value": "RBO",
      "name": "RBO",
    }
  ]
}
"""


"""
@api {GET} /api/v2.1/manage/sol/by-area Danh sách đơn vị kinh doanh theo khu vực  
@apiDescription ko truyền khu vực thì lấy tất cả
@apiVersion 1.0.0
@apiGroup SyncETL
@apiName GetListSolByArea   

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)  {string}  [area]    mã khu vực. nhiều giá trị cách nhau "," 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "value": "1007",
      "name": "Chi nhánh Ba Đình",
    }
  ]
}
"""

"""
@api {GET} /api/v2.1/manage/group-position/by-time thông tin nhóm chức danh theo thời gian 
@apiDescription từ 1/5/2024 SRBO chuyển thành SM  
@apiVersion 1.0.0
@apiGroup SyncETL
@apiName GetGroupPositionByTime  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)  {string}  report_date    thời gian muốn xem báo cáo (%Y-%m-%d) 
@apiParam   (Query:)  {string}  group_position_id   nhóm chức danh, VD: SRBO 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "group_position_id": "SM"
  }
}
"""



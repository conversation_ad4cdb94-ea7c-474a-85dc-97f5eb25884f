#!/usr/bin/python
# -*- coding: utf8 -*-

# ============================================
# Đăng nhập 2 bước bằng OTP
# ============================================



"""
@api {get} /api/v2.1/factor-authen/config/merchant lấy thông tin cho phép sử dụng xác thực 2 bước 
@apiDescription giao diện sẽ hiển thị nếu on cấu hình
@apiGroup FactorAuthen
@apiVersion 1.0.0
@apiName GetConfigMerchant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": {
      "app_authenticator": "on",    // on/off 
  },
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""


"""
@api {get} /api/v2.1/factor-authen/config/account lấy thông tin tài khoản đã bật xác thực 2 bước hay chưa  
@apiDescription giao diện sẽ hiển thị nếu on cấu hình
@apiGroup FactorAuthen
@apiVersion 1.0.0
@apiName GetConfigAccount 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": {
      "app_authenticator": "on",    // on/off 
  },
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
}
"""



"""
@api {post} /api/v2.1/factor-authen/send-code gửi mã vào email theo hành động 
@apiDescription có thể dùng cho các action khác 
@apiGroup FactorAuthen
@apiVersion 1.0.0
@apiName SendCode 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   action_type    hành động gửi mã: register_app_authenticator, change_email_your_self_authenticator


@apiParamExample {json} Body
{
    "action_type": "register_app_authenticator",
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        // case 1 
        "result_code": "success",   // gửi mã về email thành công 
        "email": "abc*********",
        "username": "abc@shop",
        "times_send": 2,            // số lần gửi mã 
        "max_times_send": 5,            // số lần tối đa cho phép gửi mã 
        "next_time_resend": 120     // thời gian đếm ngược gửi lại mã, giây 
        "time_code_exp": 300        // thời gian hiệu lực của mã 

        // case 2 
        "result_code": "email_invalid",   // ko có email hoặc email chưa verify 
        
        // case 3  
        "result_code": "resend_fail",   // chưa tới thời gian gửi lại mã  
        "time_left":    20              // thời gian còn lại mở khóa 
        
        // case 4 
        "result_code": "resend_block",   // khóa gửi lại mã  
        "time_left":    20              // thời gian còn lại mở khóa 
        
        "result_code": "block_code",   // khóa nhập mã  
        "times_wrong": 2,            // số lần gửi mã sai 
        "max_times_wrong": 5,            // số lần sai liên tiếp sẽ bị khóa 
        "time_left":    20              // thời gian còn lại mở khóa 
        
        
        "result_code": "email_already_verify" // Email đã được xác minh bởi tài khoản khác 
        
        "result_code": "email_duplicate" // Email giống với email hiện tai 
        
    },
    "lang": "vi",
    "message": "request thành công."
}

"""


"""
@api {post} /api/v2.1/factor-authen/config/verify-code  kiểm tra code theo action 
@apiDescription sau này có thể bổ sung nhiều loại khác 
@apiGroup FactorAuthen
@apiVersion 1.0.0
@apiName VerifyCodeByType  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   type_authen    phương thức xác thực: app_authenticator
@apiParam   (Body)   {String}   code_verify    mã xác nhận

@apiParamExample {json} Body
{
    "type_authen": "app_authenticator",
    "code_verify": "665899"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "result_code": "success",
        "uri": "otpauth://totp/Mobio:admin%40pingcomshop?secret=PZ4AWV2XUYHMI5KDQNJZWBZYPNODEDBQ&issuer=Mobio",
        "secret_key": "PZ4AWV2XUYHMI5KDQNJZWBZYPNODEDBQ"
        
        "result_code": "wrong_code",   // sai mã 
        "times_wrong": 2,            // số lần gửi mã sai 
        "max_times_wrong": 5,            // số lần sai liên tiếp sẽ bị khóa 
        
        "result_code": "block_code",   // khóa nhập mã  
        "times_wrong": 2,            // số lần gửi mã sai 
        "max_times_wrong": 5,            // số lần sai liên tiếp sẽ bị khóa 
        "time_left":    20              // thời gian còn lại mở khóa 
        
        
        "result_code": "email_already_verify" // Email đã được xác minh bơir tài khoản khác 
    },
    "lang": "vi",
    "message": "request thành công."
}

"""


"""
@api {post} /api/v2.1/factor-authen/config/on  bật xác thực 2 bước
@apiDescription có thể dùng cho các action khác 
@apiGroup FactorAuthen
@apiVersion 1.0.0
@apiName OnConfig 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   type_authen    phương thức xác thực: app_authenticator
@apiParam   (Body)   {String}   code_verify    mã xác nhận

@apiParamExample {json} Body
{
    "type_authen": "app_authenticator",
    "code_verify": "665899"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "result_code": "success", 
        
        "result_code": "wrong_code", 
    }
    "lang": "vi",
    "message": "request thành công."
}

"""

"""
@api {post} /api/v2.1/factor-authen/config/off  tắt xác thực 2 bước
@apiDescription có thể dùng cho các action khác 
@apiGroup FactorAuthen
@apiVersion 1.0.0
@apiName OffConfig 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   type_authen    phương thức xác thực: app_authenticator
@apiParam   (Body)   {String}   code_verify    mã xác nhận

@apiParamExample {json} Body
{
    "type_authen": "app_authenticator",
    "code_verify": "665899"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "result_code": "success",
        
        "result_code": "wrong_code",   // sai mã 
        "times_wrong": 2,            // số lần gửi mã sai 
        "max_times_wrong": 5,            // số lần sai liên tiếp sẽ bị khóa 
        
        "result_code": "block_code",   // khóa nhập mã  
        "times_wrong": 2,            // số lần gửi mã sai 
        "max_times_wrong": 5,            // số lần sai liên tiếp sẽ bị khóa 
        "time_left":    20              // thời gian còn lại mở khóa 

    },
    "lang": "vi",
    "message": "request thành công."
}

"""

"""
@api {post} /api/v2.1/account/check-email-verify  kiểm tra email đã xác thực hay chưa 
@apiDescription điều kiện kiểm tra có thể là id account hoặc email 
@apiGroup FactorAuthen
@apiVersion 1.0.0
@apiName CheckEmailVerify  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   [account_id]    id tài khoản cần check xác thực, ưu tiên check nếu key này có giá trị 
@apiParam   (Body)   {String}   [email]    email kiểm tra đã xác thực ở tài khoản nào 

@apiParamExample {json} Body
{
    "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "email": "<EMAIL>"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "email": "<EMAIL>",
        "status_verify": "not_verified",    // not_verified: chưa xác thực, is_verified: đã xác thực 
    },
    "lang": "vi",
    "message": "request thành công."
}

"""


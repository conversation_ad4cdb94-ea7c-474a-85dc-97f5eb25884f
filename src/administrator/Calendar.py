# ============================================ MANAGER ACCOUNT CONNECT =================================================

"""
@apiDefine ResponseDetailAccount

@apiSuccess {String}            data.config_id                           Định danh của kết nối
@apiSuccess {String}            data.merchant_id                         Định danh của tennant
@apiSuccess {String}            data.account_id                          Định danh tài khoản
@apiSuccess {String}            data.email                               Email kết nối
@apiSuccess {Int}               data.status                              Trạng thái bật tắt của kết nối, nhận giá trị <code>0:tắt, 1:bật</code>
@apiSuccess {Int}               data.type_connect                        Kết nối từ nguồn nào, nhận gi<PERSON> trị <code>1:Google, 2:Microsoft</code>
@apiSuccess {Int}               data.status_connect                      Trạng thái kết nối, nhận giá trị <code>0:kết nối thât bại, 1:kết nối thành công</code>
@apiSuccess {Int}               data.sync_priority                       Ưu tiên đồng bộ, nhận giá trị <code>0:không ưu tiên, 1:ưu tiên</code>

@apiSuccess {String}            data.create_on                          Thời điểm khởi tạo
@apiSuccess {String}            data.update_on                          Thời điểm cập nhật

"""

# ----------------------- Outlook connect -----------------------
"""
@api {post} /api/v2.1/outlook/connect               Kết nối đến outlook
@apiDescription kết nối đến outlook
@apiGroup Calendar
@apiVersion  2.1.0
@apiName OutlookConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body)   {String}   staff_id                Mã nhân viên 
@apiParam   (Body)   {String}   link_current_page       đường dẫn callback sau khi kết nối thành công 
@apiParam   (Body)   {String}   source                  nguồn thực hiện kết nối: "mail", "calendar" 

@apiParamExample {json} Body
{
    "staff_id":"1111d995-9c64-4794-9e44-2b7e243e1111",
    "link_current_page": "https://test1.mobio.vn/account/setting/basic",
    "source": "mail"
}

@apiSuccess     {json} data response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": "https://login.microsoftonline.com"     // chuyển hướng tới đường dẫn này 
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}
"""
# ----------------------- Disconnect  -----------------------
"""
@api {DELETE} /api/v2.1/calendars/accounts                   Gỡ kết nối tài khoản
@apiGroup Calendar
@apiDescription Gỡ kết nối tài khoản
@apiVersion 1.0.0
@apiName DisconnectAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            config_id                   Định danh account cần xóa
@apiParam   (BODY:)     {String}            [config_id_instead]         Định danh account thay thế ưu tiên đông bộ


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Gỡ kết nối tài khoản


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Set sync priority  -----------------------
"""
@api {POST} /api/v2.1/calendars/accounts/actions/set-sync-priority          Xét ưu tiên đông bộ
@apiGroup Calendar
@apiDescription Xét ưu tiên đông bộ
@apiVersion 1.0.0
@apiName SetSyncPriority

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}           config_id           Định danh account xét ưu tiên đồng bộ



@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xét ưu tiên đông bộ


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Check connect account  -----------------------
"""
@api {POST} /api/v2.1/calendars/accounts/actions/check-connect          Kiểm tra kết nối
@apiGroup Calendar
@apiDescription Kiểm tra kết nối
@apiVersion 1.0.0
@apiName CheckConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}           config_id           Định danh account cần kiểm tra kết nối


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Kiểm tra kết nối


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- On-off connect  -----------------------
"""
@api {POST} /api/v2.1/calendars/accounts/actions/change-status          Bật tắt kết nối
@apiGroup Calendar
@apiDescription Bật tắt kết nối
@apiVersion 1.0.0
@apiName OnOffConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            config_id                   Định danh account cần bật tắt kết nối
@apiParam   (BODY:)     {String}            status                      Trạng thái bật tắt của kết nối, nhận giá trị <code>0:tắt, 1:bật</code> 



@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Bật tắt kết nối


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- On connect  -----------------------
"""
@api {POST} /api/v2.1/calendars/accounts/actions/on-status-connect          Bật kết nối
@apiGroup Calendar
@apiDescription Bật kết nối
@apiVersion 1.0.0
@apiName OnConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            config_id                   Định danh account cần bật tắt kết nối


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Bật kết nối


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Off connect  -----------------------
"""
@api {POST} /api/v2.1/calendars/accounts/actions/off-status-connect          Tắt kết nối
@apiGroup Calendar
@apiDescription Tắt kết nối
@apiVersion 1.0.0
@apiName OffConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            config_id                   Định danh account cần bật tắt kết nối
@apiParam   (BODY:)     {String}            [config_id_instead]         Định danh account thay thế ưu tiên đông bộ 



@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Tắt kết nối


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Get list account  -----------------------
"""
@api {POST} /api/v2.1/calendars/accounts/actions/list          Lấy danh sách account kết nối
@apiGroup Calendar
@apiDescription Lấy danh sách account kết nối
@apiVersion 1.0.0
@apiName GetListAccountConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách account kết nối

@apiUse ResponseDetailAccount

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "account_id": "",
            "config_id": "8143da64-63f4-11ed-9767-6e523b60a908",
            "email": "<EMAIL>",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "status": 1,
            "status_connect": 1,
            "sync_priority": 1,
            "type_connect": 1
        }
    ]
}

"""
# ----------------------- Get detail account  -----------------------
"""
@api {POST} /api/v2.1/calendars/accounts          Lấy chi tiết thông tin account
@apiGroup Calendar
@apiDescription Lấy chi tiết thông tin account
@apiVersion 1.0.0
@apiName GetDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}           config_id           Định danh account 

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết thông tin account

@apiUse ResponseDetailAccount


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "account_id": "",
        "config_id": "8143da64-63f4-11ed-9767-6e523b60a908",
        "email": "<EMAIL>",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "status": 1,
        "status_connect": 1,
        "sync_priority": 1,
        "type_connect": 1
    }
}

"""

# ================================================== CALENDAR EVENT ==================================================

# -------------------------------------------------- Thêm event --------------------------------------------------
"""
@api {POST} /api/v2.1/calendars/events                Thêm calendar event 
@apiDescription Tạo calendar event mới
@apiGroup Calendar
@apiVersion 1.0.0
@apiName CalendarCreateEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (BODY:)     {String}        account_id                          Định danh tài khoản
@apiParam   (BODY:)     {String}        object_id                           Định danh thực thể cần tạo event
@apiParam   (BODY:)     {String}        source                              Nguồn tạo, nhận giá trị <code>NOTE, TASK</code>
@apiParam   (BODY:)     {String}        start                               Thông tin giời gian bắt đầu event ITC
@apiParam   (BODY:)     {String}        end                                 Thông tin giời gian kết thúc event ITC
@apiParam   (BODY:)     {String}        summary                             Tiêu đề của event
@apiParam   (BODY:)     {List}          [email_guest]                       Danh sách email khách mời
@apiParam   (BODY:)     {Number}        [reminder_time]                     Thời gian (phút) nhắc nhở trước khi event diễn ra
@apiParam   (BODY:)     {String}        [description]                       Nội dung event

@apiParamExample {json} Body
{
    "account_id": "",
    "object_id": "",
    "source": "TASK",
    "start": "2022-01-07T11:45:34",
    "end": "2022-01-07T18:45:34",
    "summary": "test sửa",
    "email_guest": [
        "<EMAIL>"
    ],
    "reminder_time": 60, 
    "description": "mô tả nội dung"
}

@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {},
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""
# -------------------------------------------------- Sửa event --------------------------------------------------
"""
@api {PATCH} /api/v2.1/calendars/events           Sửa calendar event
@apiDescription Sửa calendar event
@apiGroup Calendar
@apiVersion 1.0.0
@apiName CalendarEditEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (BODY:)     {String}        account_id                          Định danh tài khoản
@apiParam   (BODY:)     {String}        object_id                           Định danh thực thể cần tạo event
@apiParam   (BODY:)     {String}        source                              Nguồn tạo, nhận giá trị <code>NOTE, TASK</code>
@apiParam   (BODY:)     {String}        start                               Thông tin giời gian bắt đầu event ITC
@apiParam   (BODY:)     {String}        end                                 Thông tin giời gian kết thúc event ITC
@apiParam   (BODY:)     {String}        summary                             Tiêu đề của event
@apiParam   (BODY:)     {List}          [email_guest]                       Danh sách email khách mời
@apiParam   (BODY:)     {Number}        [reminder_time]                     Thời gian (phút) nhắc nhở trước khi event diễn ra
@apiParam   (BODY:)     {String}        [description]                       Nội dung event


@apiParamExample {json} Body
{
    "account_id": "",
    "object_id": "",
    "source": "TASK",
    "start": "2022-01-07T11:45:34",
    "end": "2022-01-07T18:45:34",
    "summary": "test sửa",
    "email_guest": [
        "<EMAIL>"
    ],
    "reminder_time": 60, 
    "description": "mô tả nội dung"
}

@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {},
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""
# -------------------------------------------------- Xóa event --------------------------------------------------
"""
@api {DELETE} /api/v2.1/calendars/events      Xóa calendar event
@apiDescription Xóa calendar event
@apiGroup Calendar
@apiVersion 1.0.0
@apiName CalendarDeleteEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header


@apiParam   (BODY:)     {String}        account_id                          Định danh tài khoản
@apiParam   (BODY:)     {String}        object_id                           Định danh thực thể cần tạo event
@apiParam   (BODY:)     {String}        source                              Nguồn tạo, nhận giá trị <code>NOTE, TASK</code>

@apiParamExample {json} Body
{
    "account_id": "",
    "object_id": "",
    "source": "TASK"
}

@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""

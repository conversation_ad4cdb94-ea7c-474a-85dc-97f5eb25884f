"""
@api {get} /api/v2.1/sso/domain L<PERSON><PERSON> danh sách domain cấu hình sso 
@apiDescription API L<PERSON>y danh sách cấu hình theo merchant
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName ListDomain 


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "id": "75be1743-940e-41e6-846f-1453bcce7a86",
      "domain": "mobio.io",
      "status": "verified",     // unverified
      "email_verify": "<EMAIL>",
      "time_verify": "2017-08-07T04:02:28.002Z",  // utc 
      "created_time": "2017-08-07T04:02:28.002Z", // utc 
      "created_by": "",
      "updated_by": "",
    },
  ],
  "message": "request thành công."
}
"""

"""
@api {post} /api/v2.1/sso/domain tạo domain sso 
@apiDescription tạo thành công thì gửi mã xác thực về email 
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName AddDomain 


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body)   {String}   email    email để xác minh domain 
@apiParam   (Body)   {String}   [code_verify]    mã xác nhận: chỉ có khi đến bước nhập mã 

@apiParamExample {json} Body example
{
  "email": "<EMAIL>",
  "code_verify": "665899"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200 
  "data": {
    // case 1 
    "result_code": "verify_code",   // nhập mã 
    "email": "abc*********",
    "times_send": 2,            // số lần gửi mã 
    "max_times_send": 5,            // số lần tối đa cho phép gửi mã 
    "next_time_resend": 120     // thời gian đếm ngược gửi lại mã, giây 
    "time_code_exp": 300        // thời gian hiệu lực của mã 

    // case 2 
    "result_code": "email_invalid",   // email ko hợp lệ, không phải email doanh nghiệp 
    
    // case 3  
    "result_code": "resend_fail",   // chưa tới thời gian gửi lại mã  
    "time_left":    20              // thời gian còn lại mở khóa 
    
    // case 4 
    "result_code": "resend_block",   // khóa gửi lại mã  
    "time_left":    20              // thời gian còn lại mở khóa 
    
    // case 5
    "result_code": "block_code",   // khóa nhập mã  
    "times_wrong": 2,            // số lần gửi mã sai 
    "max_times_wrong": 5,            // số lần sai liên tiếp sẽ bị khóa 
    "time_left":    20              // thời gian còn lại mở khóa 
    
    // case 6
    "result_code": "already_verify" // domain đã tồn tại  
    
    // case 7 
    "result_code": "success",
    "id":""
    "email": "",
    "domain": ""
    
    // case 8
    "result_code": "wrong_code",   // sai mã 
    "times_wrong": 2,            // số lần gửi mã sai 
    "max_times_wrong": 5,            // số lần sai liên tiếp sẽ bị khóa 
    

  }
}

"""

"""
@api {get} /api/v2.1/sso/domain-invalid Lấy danh sách domain không hợp lệ 
@apiDescription các domain phổ thông như gmail.com, outlook.com, ...
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName ListDomainInvalid 


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": ["gmail.com", "outlook.com"],
  "message": "request thành công."
}
"""

"""
@api {delete} /api/v2.1/sso/domain/<domain_id>/delete xóa domain  
@apiDescription gửi id domain muốn xóa 
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName DelDomain


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""

"""
@api {get} /api/v2.1/sso/config Lấy thông tin cấu hình sso 
@apiDescription Lấy trạng thái, và cấu hình khác chung 1 api 
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName GetConfigSSO  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "status": "on"  // on, off
    "status_saml": "on"  // on, off
    "create_account": {
        "status": "on"  // on, off
        "product_line": [],
        "role_group": "user",   // admin, manager
        "module_ids": [],
    },
    "sso_require": {
        "status": "on"  // on, off
        "account_exclude": [],
    },
  },
  "message": "request thành công."
}

"""


"""
@api {post} /api/v2.1/sso/config  cập nhật cấu hình sso 
@apiDescription truyền thông tin nào cập nhật thông tin đó 
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName UpdateConfigSSO  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParamExample    {json}      BODY:
{
    "status": "on"  // on, off
    "status_saml": "on"  // on, off
    "create_account": {
        "status": "on"  // on, off
        "product_line": [],
        "role_group": "user",   // admin, manager
        "module_ids": [],
    },
    "sso_require": {
        "status": "on"  // on, off
        "account_exclude": [],
    },
}

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "status": "on"  // on, off
        "status_saml": "on"  // on, off
        "create_account": {
            "status": "on"  // on, off
            "product_line": [],
            "role_group": "user",   // admin, manager
            "module_ids": [],
        },
        "sso_require": {
            "status": "on"  // on, off
            "account_exclude": [],
        },
    }
}

"""


"""
@api {get} /api/v2.1/sso/provider/basic Lấy danh sách nhà cung cấp sso 
@apiDescription nhà cung cấp sso basic 
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName GetProviderBasic

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
      {
        "name": "Google",
        "key": "google"
      }
  ],
  "message": "request thành công."
}

"""


"""
@api {get} /api/v2.1/sso/config/saml Lấy danh sách cấu hình saml  
@apiDescription danh sách saml theo merchant 
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName ListConfigSAML

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
      {
        "id": "75be1743-940e-41e6-846f-1453bcce7a86",
        "entity_id": "mobio.io",
        "provider_type": "microsoft",  //   google, microsoft, larksuite
        "sp_entity_url": "https://t1.mobio.vn/adm/partner/api/v2.1/sso/saml/metadata/mobio.io",
        "sp_acs_url": "https://t1.mobio.vn/adm/partner/api/v2.1/sso/saml/acs/mobio.io",
        "sp_logout_url": "https://t1.mobio.vn/adm/partner/api/v2.1/sso/saml/slo/mobio.io",
        "idp_entity_url": "https://sts.windows.net/b10fc262-6a52-4d11-84b6-a126e2fecab2/",
        "idp_login_url": "https://login.microsoftonline.com/b10fc262-6a52-4d11-84b6-a126e2fecab2/saml2",
        "idp_logout_url": "https://login.microsoftonline.com/b10fc262-6a52-4d11-84b6-a126e2fecab2/saml2",
        "certificate": "-----BEGIN CERTIFICATE----- MIIC8DC...brPh3G -----END CERTIFICATE-----",
        "file_name": "mobio_sso_saml.xml"
      }
  ],
  "message": "request thành công."
}

"""

"""
@api {post} /api/v2.1/sso/config/upload/xml upload file xml   
@apiDescription upload file để xem thông tin trước khi tạo sửa cấu hình saml 
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName UploadXML 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam (Body) {string} file_xml dữ liệu trong file xml


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "provider_type": "google",  // google, microsoft, larksuite
    "idp_entity_url": "https://sts.windows.net/b10fc262-6a52-4d11-84b6-a126e2fecab2/",
    "idp_login_url": "https://login.microsoftonline.com/b10fc262-6a52-4d11-84b6-a126e2fecab2/saml2",
    "idp_logout_url": "https://login.microsoftonline.com/b10fc262-6a52-4d11-84b6-a126e2fecab2/saml2",
    "certificate": "-----BEGIN CERTIFICATE----- MIIC8DC...brPh3G -----END CERTIFICATE-----",
  },
  "message": "request thành công."
}

"""


"""
@api {get} /api/v2.1/sso/config/param-sp lấy thông tin tham số mặc định   
@apiDescription các tham số url mặc định do mobio cung cấp 
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName ConfigSPDefault 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
      "sp_entity_url": "https://t1.mobio.vn/adm/partner/api/v2.1/sso/saml/metadata/{entity_id}",
      "sp_acs_url": "https://t1.mobio.vn/adm/partner/api/v2.1/sso/saml/acs/{entity_id}",
      "sp_logout_url": "https://t1.mobio.vn/adm/partner/api/v2.1/sso/saml/slo/{entity_id}",
  },
  "message": "request thành công."
}

"""

"""
@api {get} /api/v2.1/sso/saml/<entity_id>/check-exists kiểm tra domain có hợp lệ ko    
@apiDescription domain hợp lệ mới cho cấu hình các bước tiếp theo 
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName CheckDomain  

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  // hợp lệ 
  "code": 200,
  
  // không hợp lệ 
  "code": 400,

  "message": "request thành công."
}

"""


"""
@api {post} /api/v2.1/sso/config/saml tạo sửa cấu hình saml  
@apiDescription tạo sửa cấu hình saml
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName UpsertConfigSAML

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam (Body) {string} [id] id cấu hình cần sửa, tạo mới ko cần key này  
@apiParam (Body) {string} entity_id domain  
@apiParam (Body) {string} [file_xml] dữ liệu trong file xml
@apiParam (Body) {string} [file_name] tên file xml


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
      "id": "75be1743-940e-41e6-846f-1453bcce7a86",
      "entity_id": "mobio.io",
      "provider_type": "microsoft",  //   google, microsoft, larksuite
      "sp_entity_url": "https://t1.mobio.vn/adm/partner/api/v2.1/sso/saml/metadata/mobio.io",
      "sp_acs_url": "https://t1.mobio.vn/adm/partner/api/v2.1/sso/saml/acs/mobio.io",
      "sp_logout_url": "https://t1.mobio.vn/adm/partner/api/v2.1/sso/saml/slo/mobio.io",
      "idp_entity_url": "https://sts.windows.net/b10fc262-6a52-4d11-84b6-a126e2fecab2/",
      "idp_login_url": "https://login.microsoftonline.com/b10fc262-6a52-4d11-84b6-a126e2fecab2/saml2",
      "idp_logout_url": "https://login.microsoftonline.com/b10fc262-6a52-4d11-84b6-a126e2fecab2/saml2",
      "certificate": "-----BEGIN CERTIFICATE----- MIIC8DC...brPh3G -----END CERTIFICATE-----",
      "file_name": "mobio_sso_saml.xml"
    },
  "message": "request thành công."
}

"""


"""
@api {delete} /api/v2.1/sso/saml/<config_id>/delete xóa cấu hình saml   
@apiDescription gửi id muốn xóa 
@apiVersion 1.1.0
@apiGroup ConfigSSO 
@apiName DelConfigSaml


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""


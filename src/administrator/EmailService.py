#!/usr/bin/python
# -*- coding: utf8 -*-

# ============================================
# OLDEmailServiceAddEmail
# ============================================
"""
@api {post} /api/v2.1/email_service OLDEmailServiceAddEmail
@apiDescription OLDEmailServiceAddEmail
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceAddEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   account_id        account_id
@apiParam   (Body)   {String}   email             Work E-mail
@apiParam   (Body)   {String}   password          Mật khẩu Work E-maill
@apiParam   (Body)   {Object}   incoming_server   <PERSON><PERSON><PERSON> chủ nhận thư
@apiParam   (Body)   {Object}   outgoing_server   M<PERSON><PERSON> chủ gửi thư

@apiParam   (incoming_server)   {String}   protocol_type   Loại giao thức
<li>Default value:<code>imap</code></li>
<li>Allowed values: <code>imap, pop3</code></li>
@apiParam   (incoming_server)   {String}   host            Máy chủ
@apiParam   (incoming_server)   {Number}   port            Cổng
@apiParam   (incoming_server)   {Number}   ssl             Kết nối bảo mật
<li>Default value:<code>0</code></li>
<li>Allowed values: <code>0</code> (không sử dụng), <code>1</code (starttls)>, <code>2</code> (ssl/tls)</li>

@apiParam   (outgoing_server)   {String}   protocol_type   Loại giao thức
<li>Default value:<code>imap</code></li>
<li>Allowed values: <code>imap, pop3</code></li>
@apiParam   (outgoing_server)   {String}   host            Máy chủ
@apiParam   (outgoing_server)   {Number}   port            Cổng
@apiParam   (outgoing_server)   {Number}   ssl             Kết nối bảo mật
<li>Default value:<code>0</code></li>
<li>Allowed values: <code>0</code> (không sử dụng), <code>1</code> (starttls), <code>2</code> (ssl/tls)</li>


@apiParamExample {json} Body
{
    "account_id": "1111d995-9c64-4794-9e44-2b7e243e",
    "email":"<EMAIL>",
    "password": "mnrrlqeueknqzqmc",
    "incoming_server": {
    	"protocol_type": "imap",
    	"host": "imap.gmail.com",
    	"port": 993,
    	"ssl": 1
    },
    "outgoing_server": {
    	"protocol_type": "imap",
    	"host": "smtp.gmail.com",
    	"port": 587,
    	"ssl": 1
    }
}

@apiSuccess   {Number}    code      Mã trạng thái
<li><code>200</code>: Thành công</li>
<li><code>400</code>: Thất bại</li>
<li><code>410</code>: Kết nối máy chủ nhận thư không thành công</li>
<li><code>411</code>: Kết nối máy chủ gửi thư không thành công</li>
<li><code>412</code>: Xác thực email không thành công</li>
@apiSuccess   {String}   message   Tên trạng thái
@apiSuccess   {Object}   data      Dữ liệu trả về
@apiSuccess   {String}   lang      Mã ngôn ngữ trả về

@apiSuccess    (data) {String}    config_id config id

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {},
    "lang": "vi",
    "message": "Thêm cấu hình thành công"
}
@apiSuccessExample {json} Response error 
{
  "code": 410,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Không kết nối được với máy chủ nhận thư"
}

"""


# ============================================
# OLDEmailServiceEditEmail
# ============================================
"""
@api {put} /api/v2.1/email_service/<config_id> OLDEmailServiceEditEmail
@apiDescription OLDEmailServiceEditEmail
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceEditEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Resources:)   {String}   config_id   Mã định danh của cấu hình

@apiParam   (Body)   {String}   [email]             Work E-mail
@apiParam   (Body)   {String}   [password]          Mật khẩu Work E-maill
@apiParam   (Body)   {Object}   [incoming_server]   Máy chủ nhận thư
@apiParam   (Body)   {Object}   [outgoing_server]   Máy chủ gửi thư

@apiParam   (incoming_server)   {String}   protocol_type   Loại giao thức
<li>Default value:<code>imap</code></li>
<li>Allowed values: <code>imap, pop3</code></li>
@apiParam   (incoming_server)   {String}   host            Máy chủ
@apiParam   (incoming_server)   {Number}   port            Cổng
@apiParam   (incoming_server)   {Number}   ssl             Kết nối bảo mật
<li>Default value:<code>0</code></li>
<li>Allowed values: <code>0</code> (không sử dụng), <code>1</code (starttls)>, <code>2</code> (ssl/tls)</li>

@apiParam   (outgoing_server)   {String}   protocol_type   Loại giao thức
<li>Default value:<code>imap</code></li>
<li>Allowed values: <code>imap, pop3</code></li>
@apiParam   (outgoing_server)   {String}   host            Máy chủ
@apiParam   (outgoing_server)   {Number}   port            Cổng
@apiParam   (outgoing_server)   {Number}   ssl             Kết nối bảo mật
<li>Default value:<code>0</code></li>
<li>Allowed values: <code>0</code> (không sử dụng), <code>1</code (starttls)>, <code>2</code> (ssl/tls)</li>


@apiParamExample {json} Body
{
    "account_id": "1111d995-9c64-4794-9e44-2b7e243e",
    "email":"<EMAIL>",
    "password": "mnrrlqeueknqzqmc",
    "incoming_server": {
    	"protocol_type": "imap",
    	"host": "imap.gmail.com",
    	"port": 993,
    	"ssl": 1
    },
    "outgoing_server": {
    	"protocol_type": "imap",
    	"host": "smtp.gmail.com",
    	"port": 587,
    	"ssl": 1
    }
}

@apiSuccess   {Number}    code      Mã trạng thái
<li><code>200</code>: Thành công</li>
<li><code>400</code>: Thất bại</li>
<li><code>410</code>: Không kết nối được với máy chủ nhận thư</li>
<li><code>411</code>: Không kết nối được với máy chủ gửi thư</li>
@apiSuccess   {String}   message   Tên trạng thái
@apiSuccess   {Object}   data      Dữ liệu trả về
@apiSuccess   {String}   lang      Mã ngôn ngữ trả về

@apiSuccess    (data) {String}    config_id config id

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {},
    "lang": "vi",
    "message": "Cập nhật cấu hình thành công"
}
@apiSuccessExample {json} Response error 
{
  "code": 410,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Không kết nối được với máy chủ nhận thư"
}

"""


# ============================================
# Sửa thông tin email
# ============================================
"""
@api {put} /api/v2.1/email_service/<config_id>/work_mail Sửa thông tin email
@apiDescription Sửa thông tin email
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceEditEmailInfo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Resources:)   {String}   config_id   Mã định danh của cấu hình

@apiParam   (Body)   {String}   [account_name]             Tên người dùng
@apiParam   (Body)   {Number}   [use_connect]             Trạng thái sử dụng
<li><code>0</code>: Không sử dụng </li>
<li><code>1</code>: Sử dụng </li>
@apiParam   (Body)   {Number}   [config_default]             Cấu hình mặc định
<li><code>0</code>: Không mặc định </li>
<li><code>1</code>: Mặc định</li>

@apiParamExample {json} Body
{
    "account_name": "cuong phan van",
    "use_connect": 1,
    "config_default": 1
}

@apiSuccess   {Number}    code      Mã trạng thái
<li><code>200</code>: Thành công</li>
<li><code>400</code>: Thất bại</li>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "request không thành công"
}

"""



# ============================================
# Thêm cấu hình email
# ============================================
"""
@api {post} /api/v2.1/email_service/smtp-imap Thêm sửa cấu hình email smtp-imap 
@apiDescription Thêm sửa cấu hình email smtp-imap 
@apiGroup EmailService
@apiVersion 2.1.0
@apiName CreateEditConfigSMTP-IMAP

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Body)   {String}   config_id        id cấu hình, tạo mới thì ko có  
@apiParam   (Body)   {String}   account_id        id tài khoản nhân viên 
@apiParam   (Body)   {String}   email             Work E-mail
@apiParam   (Body)   {String}   password          Mật khẩu Work E-maill
@apiParam   (Body)   {String}   [account_name]      Họ tên tài khoản email 
@apiParam   (Body)   {Object}   incoming_server   Máy chủ nhận thư
@apiParam   (Body)   {Object}   outgoing_server   Máy chủ gửi thư

@apiParam   (incoming_server)   {String}   protocol_type   Loại giao thức
<li>Default value:<code>imap</code></li>
<li>Allowed values: <code>imap, pop3</code></li>
@apiParam   (incoming_server)   {String}   host            Máy chủ
@apiParam   (incoming_server)   {Number}   port            Cổng
@apiParam   (incoming_server)   {Number}   ssl             Kết nối bảo mật
<li>Default value:<code>0</code></li>
<li>Allowed values: <code>0</code> (không sử dụng), <code>1</code (starttls)>, <code>2</code> (ssl/tls)</li>

@apiParam   (outgoing_server)   {String}   protocol_type   Loại giao thức
<li>Default value:<code>imap</code></li>
<li>Allowed values: <code>imap, pop3</code></li>
@apiParam   (outgoing_server)   {String}   host            Máy chủ
@apiParam   (outgoing_server)   {Number}   port            Cổng
@apiParam   (outgoing_server)   {Number}   ssl             Kết nối bảo mật
<li>Default value:<code>0</code></li>
<li>Allowed values: <code>0</code> (không sử dụng), <code>1</code> (starttls), <code>2</code> (ssl/tls)</li>


@apiParamExample {json} Body
{
    "config_id": "1111d995-9c64-4794-9e44-2b7e243e",
    "account_id": "1111d995-9c64-4794-9e44-2b7e243e",
    "email":"<EMAIL>",
    "account_name":"Nguyen Anh",
    "password": "mnrrlqeueknqzqmc",
    "incoming_server": {
    	"protocol_type": "imap",
    	"host": "imap.gmail.com",
    	"port": 993,
    	"ssl": 1
    },
    "outgoing_server": {
    	"protocol_type": "imap",
    	"host": "smtp.gmail.com",
    	"port": 587,
    	"ssl": 1
    }
}

@apiSuccess   {Number}    code      Mã trạng thái
<li><code>200</code>: Thành công</li>
<li><code>400</code>: Thất bại</li>
<li><code>102</code>: config_id không tồn tại trong hệ thống</li>
<li><code>103</code>: email đã tồn tại trong hệ thống</li>
@apiSuccess   {String}   message   Tên trạng thái
@apiSuccess   {Object}   data      Dữ liệu trả về
@apiSuccess   {String}   lang      Mã ngôn ngữ trả về

@apiSuccess    (data) {String}    config_id config id

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "account_id": "1111d995-9c64-4794-9e44-2b7e243e1111",
        "config_id": "f219447c-5739-11ec-a97d-3f46c366d1a1",
        "create_on": "Tue, 07 Dec 2021 08:44:52 GMT",
        "email": "<EMAIL>",
        "incoming_server": {
            "host": "imap.gmail.com",
            "port": 993,
            "protocol_type": "imap",
            "ssl": 2,
            "status": 1
        },
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "outgoing_server": {
            "host": "smtp.gmail.com",
            "port": 587,
            "protocol_type": "smtp",
            "ssl": 1,
            "status": 1
        },
        "partition": 99,
        "password": "Dr4Yn7_3EflGR1qHYlNUHxpCOZmqVXISKPGwjIIEboOo_WlQ0Nu1pH4WtuGFcdnN",
        "status_connect": 1,
        "type_connect": 3,
        "update_on": "Tue, 07 Dec 2021 08:48:38 GMT",
        "use_connect": 1
    },
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccessExample {json} Response error 400
  "code": 400,
  "errors": "request_error",
  "lang": "vi",
  "message": "request không thành công"
}

@apiSuccessExample {json} Response error 102
{
    "code": 102,
    "lang": "vi",
    "message": "config_id [1111d995-9c64-4794-9e44-2b7e243e1111] không tồn tại trong hệ thống."
}

@apiSuccessExample {json} Response error 103
{
    "code": 103,
    "lang": "vi",
    "message": "email [<EMAIL>] đã tồn tại trong hệ thống."
}

"""


# ============================================
# Danh sách cấu hình email
# ============================================
"""
@api {get} /api/v2.1/email_service/list_email Danh sách cấu hình email
@apiDescription Danh sách cấu hình email
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceListEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Params:)   {String}   account_id   Mã định danh của tài khoản

@apiSuccess   {Number}    code      Mã trạng thái
<li><code>200</code>: Thành công</li>
<li><code>400</code>: Thất bại</li>
<li><code>410</code>: Không kết nối được với máy chủ nhận thư</li>
<li><code>411</code>: Không kết nối được với máy chủ gửi thư</li>
@apiSuccess   {String}   message   Tên trạng thái
@apiSuccess   {List}   data      Dữ liệu trả về
@apiSuccess   {String}   lang      Mã ngôn ngữ trả về

@apiSuccess   (data)   {String}   config_id         Mã định danh của cấu hình
@apiSuccess   (data)   {String}   merchant_id       ID của Merchant
@apiSuccess   (data)   {String}   account_id        Mã định danh của tài khoản
@apiSuccess   (data)   {String}   email             Work E-mail
@apiSuccess   (data)   {String}   account_name             Tên người dùng
@apiSuccess   (data)   {String}   type_connect              Loại service kết nối
<li><code>1</code>: google </li>
<li><code>2</code>: ms outlook </li>
<li><code>3</code>: smtp-imap </li>
@apiSuccess   (data)   {String}   status_connect             Trạng thái kết nối
<li><code>0</code>: Kết nối không thành công </li>
<li><code>1</code>: Kết nối thành công </li>
@apiSuccess   (data)   {Number}   use_connect             Trạng thái sử dụng
<li><code>0</code>: Không sử dụng </li>
<li><code>1</code>: Sử dụng </li>
@apiSuccess   (data)   {Number}   config_default             Cấu hình mặc định
<li><code>0</code>: Không mặc định </li>
<li><code>1</code>: Mặc định</li>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "config_id": "8a7079a2-50eb-11ec-9b8d-175efdecc8d4",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "account_id": "1111d995-9c64-4794-9e44-2b7e243e1111",
            "email": "<EMAIL>",
            "account_name": "Phan Van Cuong",
            "type_connect": 1,
            "status_connect": 1,
            "use_connect": 1
            "config_default": 0,
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 410,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Lấy cấu hình không thành công"
}

"""

# ============================================
# OLDEmailServiceListEmail
# ============================================
"""
@api {get} /api/v2.1/email_service OLDEmailServiceListEmail
@apiDescription OLDEmailServiceListEmail
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceListEmailOLD

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Params:)   {String}   account_id   Mã định danh của tài khoản

@apiSuccess   {Number}    code      Mã trạng thái
<li><code>200</code>: Thành công</li>
<li><code>400</code>: Thất bại</li>
<li><code>410</code>: Không kết nối được với máy chủ nhận thư</li>
<li><code>411</code>: Không kết nối được với máy chủ gửi thư</li>
@apiSuccess   {String}   message   Tên trạng thái
@apiSuccess   {List}   data      Dữ liệu trả về
@apiSuccess   {String}   lang      Mã ngôn ngữ trả về

@apiSuccess   (data)   {String}   config_id         Mã định danh của cấu hình
@apiSuccess   (data)   {String}   account_id        Mã định danh của tài khoản
@apiSuccess   (data)   {String}   email             Work E-mail
@apiSuccess   (data)   {Object}   incoming_server   Máy chủ nhận thư
@apiSuccess   (data)   {Object}   outgoing_server   Máy chủ gửi thư

@apiSuccess   (incoming_server)   {String}   status          Trạng thái kết nối
<li><code>0</code>: Kết nối không thành công </li>
<li><code>1</code>: Kết nối thành công </li>
@apiSuccess   (incoming_server)   {String}   protocol_type   Loại giao thức
@apiSuccess   (incoming_server)   {String}   host            Máy chủ
@apiSuccess   (incoming_server)   {Number}   port            Cổng
@apiSuccess   (incoming_server)   {Number}   ssl             Kết nối bảo mật
<li>Default value:<code>0</code></li>
<li>Allowed values: <code>0</code> (không sử dụng), <code>1</code (starttls)>, <code>2</code> (ssl/tls)</li>

@apiSuccess   (outgoing_server)   {String}   status          Trạng thái kết nối
<li><code>0</code>: Kết nối không thành công </li>
<li><code>1</code>: Kết nối thành công </li>
@apiSuccess   (outgoing_server)   {String}   protocol_type   Loại giao thức
@apiSuccess   (outgoing_server)   {String}   host            Máy chủ
@apiSuccess   (outgoing_server)   {Number}   port            Cổng
@apiSuccess   (outgoing_server)   {Number}   ssl             Kết nối bảo mật
<li>Default value:<code>0</code></li>
<li>Allowed values: <code>0</code> (không sử dụng), <code>1</code (starttls)>, <code>2</code> (ssl/tls)</li>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "config_id": "4a5bb119-ee7d-4685-9402-829045510c50",
            "account_id": "1111d995-9c64-4794-9e44-2b7e243e",
            "email":"<EMAIL>",
            "incoming_server": {
                "status": 1,
                "protocol_type": "imap",
                "host": "imap.gmail.com",
                "port": 993,
                "ssl": 1
            },
            "outgoing_server": {
                "status": 1,
                "protocol_type": "imap",
                "host": "smtp.gmail.com",
                "port": 587,
                "ssl": 1
            }
        },
        ...
    ],
    "lang": "vi",
    "message": "Lấy cấu hình thành công"
}
@apiSuccessExample {json} Response error 
{
  "code": 410,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Lấy cấu hình không thành công"
}

"""

# ============================================
# Chi tiết cấu hình email
# ============================================
"""
@api {get} /api/v2.1/email_service/<config_id>/work_mail Chi tiết cấu hình email
@apiDescription Chi tiết cấu hình email
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceDetailEmailInfo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Resources:)   {String}   config_id   Mã định danh của cấu hình


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "account_id": "1111d995-9c64-4794-9e44-2b7e243e1111",
        "account_name": "Phan Van Cuong",
        "config_id": "e294eee6-5187-11ec-b822-0208a27cd45c",
        "create_on": "Tue, 30 Nov 2021 02:47:40 GMT",
        "email": "<EMAIL>",
        "incoming_server": {
            "host": "imap.gmail.com",
            "port": 993,
            "protocol_type": "imap",
            "ssl": 2,
            "status": 1
        },
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "outgoing_server": {
            "host": "smtp.gmail.com",
            "port": 587,
            "protocol_type": "smtp",
            "ssl": 1,
            "status": 1
        },
        "partition": 34,
        "password": "ZRuCvYuTgJrY6iq1nwxuOWPEzcIOAUzXy09NlKiUkIwE0JcvApdqE_PxyK9SInG",
        "status_connect": 1,
        "type_connect": 3,
        "update_on": "Tue, 30 Nov 2021 02:47:40 GMT",
        "use_connect": 1
    },
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccessExample {json} Response error 
{
  "code": 410,
  "errors": "bad_request",
  "lang": "vi",
  "message": "request không thành công"
}

"""

# ============================================
# OLDEmailServiceDetailEmail
# ============================================
"""
@api {get} /api/v2.1/email_service/<config_id> OLDEmailServiceDetailEmail
@apiDescription OLDEmailServiceDetailEmail
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceDetailEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Resources:)   {String}   config_id   Mã định danh của cấu hình

@apiSuccess   {Number}    code      Mã trạng thái
<li><code>200</code>: Thành công</li>
<li><code>400</code>: Thất bại</li>
<li><code>410</code>: Không kết nối được với máy chủ nhận thư</li>
<li><code>411</code>: Không kết nối được với máy chủ gửi thư</li>
@apiSuccess   {String}   message   Tên trạng thái
@apiSuccess   {Object}   data      Dữ liệu trả về
@apiSuccess   {String}   lang      Mã ngôn ngữ trả về

@apiSuccess   (data)   {String}   config_id         Mã định danh của cấu hình
@apiSuccess   (data)   {String}   account_id        Mã định danh của tài khoản
@apiSuccess   (data)   {String}   email             Work E-mail
@apiSuccess   (data)   {Object}   incoming_server   Máy chủ nhận thư
@apiSuccess   (data)   {Object}   outgoing_server   Máy chủ gửi thư

@apiSuccess   (incoming_server)   {String}   status          Trạng thái kết nối
<li><code>0</code>: Kết nối không thành công </li>
<li><code>1</code>: Kết nối thành công </li>
@apiSuccess   (incoming_server)   {String}   protocol_type   Loại giao thức
@apiSuccess   (incoming_server)   {String}   host            Máy chủ
@apiSuccess   (incoming_server)   {Number}   port            Cổng
@apiSuccess   (incoming_server)   {Number}   ssl             Kết nối bảo mật
<li>Default value:<code>0</code></li>
<li>Allowed values: <code>0</code> (không sử dụng), <code>1</code (starttls)>, <code>2</code> (ssl/tls)</li>

@apiSuccess   (outgoing_server)   {String}   status          Trạng thái kết nối
<li><code>0</code>: Kết nối không thành công </li>
<li><code>1</code>: Kết nối thành công </li>
@apiSuccess   (outgoing_server)   {String}   protocol_type   Loại giao thức
@apiSuccess   (outgoing_server)   {String}   host            Máy chủ
@apiSuccess   (outgoing_server)   {Number}   port            Cổng
@apiSuccess   (outgoing_server)   {Number}   ssl             Kết nối bảo mật
<li>Default value:<code>0</code></li>
<li>Allowed values: <code>0</code> (không sử dụng), <code>1</code (starttls)>, <code>2</code> (ssl/tls)</li>


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "config_id": "4a5bb119-ee7d-4685-9402-829045510c50",
        "account_id": "1111d995-9c64-4794-9e44-2b7e243e",
        "email":"<EMAIL>",
        "incoming_server": {
            "status": 1,
            "protocol_type": "imap",
            "host": "imap.gmail.com",
            "port": 993,
            "ssl": 1
        },
        "outgoing_server": {
            "status": 1,
            "protocol_type": "imap",
            "host": "smtp.gmail.com",
            "port": 587,
            "ssl": 1
        }
    },
    "lang": "vi",
    "message": "Lấy cấu hình thành công"
}
@apiSuccessExample {json} Response error 
{
  "code": 410,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Lấy cấu hình không thành công"
}

"""

# ============================================
# Xoá cấu hình email
# ============================================
"""
@api {delete} /api/v2.1/email_service/<config_id>/work_mail Xoá cấu hình email
@apiDescription Xoá cấu hình email
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceDeleteWorkMail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Resources:)   {String}   config_id   Mã định danh của cấu hình

@apiSuccess   {Number}    code      Mã trạng thái
<li><code>200</code>: Thành công</li>
<li><code>400</code>: Thất bại</li>
@apiSuccess   {String}   message   Tên trạng thái
@apiSuccess   {Object}   data      Dữ liệu trả về
@apiSuccess   {String}   lang      Mã ngôn ngữ trả về

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 410,
  "errors": "bad_request",
  "lang": "vi",
  "message": "request không thành công"
}

"""


# ============================================
# OLDEmailServiceDeleteEmail
# ============================================
"""
@api {delete} /api/v2.1/email_service/<config_id> OLDEmailServiceDeleteEmail
@apiDescription OLDEmailServiceDeleteEmail
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceDeleteEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Resources:)   {String}   config_id   Mã định danh của cấu hình

@apiSuccess   {Number}    code      Mã trạng thái
<li><code>200</code>: Thành công</li>
<li><code>400</code>: Thất bại</li>
@apiSuccess   {String}   message   Tên trạng thái
@apiSuccess   {Object}   data      Dữ liệu trả về
@apiSuccess   {String}   lang      Mã ngôn ngữ trả về

@apiSuccess    (data) {String}    config_id config id

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
    },
    "lang": "vi",
    "message": "Xoá cấu hình thành công"
}
@apiSuccessExample {json} Response error 
{
  "code": 410,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Không kết nối được với máy chủ nhận thư"
}

"""


# ============================================
# Kiểm tra kết nối đến máy chủ
# ============================================
"""
@api {post} /api/v2.1/email_service/<config_id>/check_work_mail_connection Kiểm tra kết nối đến máy chủ
@apiDescription Kiểm tra kết nối đến máy chủ
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceCheckWorkMailConnection

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Resources:)   {String}   config_id   Mã định danh của cấu hình


@apiSuccess   {Number}    code      Mã trạng thái
<li><code>200</code>: Thành công</li>
<li><code>400</code>: Thất bại</li>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "request không thành công"
}

"""


# ============================================
# OLDEmailServiceCheckConnection
# ============================================
"""
@api {post} /api/v2.1/email_service/<config_id>/check_connection OLDEmailServiceCheckConnection
@apiDescription OLDEmailServiceCheckConnection
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceCheckConnection

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} x-merchant-id Merchant ID.

@apiParam   (Resources:)   {String}   config_id   Mã định danh của cấu hình

@apiParam   (Body)   {String}   check_server        Tên máy chủ cần kiểm tra
<li><code>incoming_server</code>: Máy chủ nhận thư</li>
<li><code>outgoing_server</code>: Máy chủ gửi thư</li>


@apiParamExample {json} Body
{
    "check_server": "outgoing_server"
}


@apiSuccess   {Number}    code      Mã trạng thái
<li><code>200</code>: Thành công</li>
<li><code>400</code>: Thất bại</li>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {},
    "lang": "vi",
    "message": "Kết nối thành công"
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Kết nối không thành công"
}

"""


"""
@api {get} /api/v2.1/email_service/domain Danh sách domain email
@apiDescription Danh sách domain email
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceDomainEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "domain_id": "8a7079a2-50eb-11ec-9b8d-175efdecc8d4",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "domain_name": "@mobio.vn",
            "incoming_server": {
                "protocol_type": "imap",
                "host": "imap.gmail.com",
                "port": 993,
                "ssl": 1
            },
            "outgoing_server": {
                "protocol_type": "smtp",
                "host": "smtp.gmail.com",
                "port": 587,
                "ssl": 1
            }
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error 
{
  "code": 410,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Lấy cấu hình không thành công"
}

"""


# ============================================
# Danh sách cấu hình email đang được sử dụng
# ============================================
"""
@api {get} /api/v2.1/email_service/list_email/used Danh sách workemail cá nhân đang được sử dụng
@apiDescription Danh sách work email cá nhân đang có trạng thái kết nối thành công và bật trạng thái sử dụng
@apiGroup EmailService
@apiVersion 2.1.0
@apiName EmailServiceListEmailUsed

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Params:)   {String}   account_id   Mã định danh của tài khoản


@apiSuccess   {Array}    data                   Dữ liệu trả về
@apiSuccess   {String}   data.config_id         Mã định danh của cấu hình
@apiSuccess   {String}   data.email             Work E-mail
@apiSuccess   {String}   data.account_name      Tên người dùng

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "account_name": "",
            "config_id": "dc555440-47e5-11ef-bcc4-993cd1088939",
            "email": "<EMAIL>"
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}

"""
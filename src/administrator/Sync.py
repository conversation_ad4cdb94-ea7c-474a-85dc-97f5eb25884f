********************* Sync SendEmailNumber *********************
* version: 2.0.0                                               *
****************************************************************
"""
@api {post} /webhook/api/v2.1/sync/sent-email-number Đồng bộ số lượng email đã gửi
@apiDescription Đồng bộ số lượng email đã gửi của 1 tenant.
@apiGroup Webhook
@apiVersion 2.0.0
@apiName SyncSentEmailNumber

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header

@apiParamExample  {json}  Example:
{
  "merchant_id": "6662c36e-64ef-4ac4-ad9d-8313038df064",
  "data": {
    "type": "sent_email_number",
    "number": 2
  },
  "checksum": "238a239d54b44abba9fb99d061a4cced"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "message": "success",
  "code": 200
}
"""
******************** Sync ValidateEmailNumber ******************
* version: 2.0.0                                               *
****************************************************************
"""
@api {post} /webhook/api/v2.1/sync/validated-email Đồng bộ số lượng email đã validate
@apiDescription Đồng bộ số lượng email đã validate của 1 tenant.
@apiGroup Webhook
@apiVersion 2.0.0
@apiName SyncValidateEmailNumber

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header

@apiParamExample  {json}  Example:
{
  "merchant_id": "6662c36e-64ef-4ac4-ad9d-8313038df064",
  "data": {
    "type": "validate_email_number",
    "number": 2
  },
  "checksum": "1c4b163553b243c2b51205e51e7fd7ce"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "message": "success",
  "code": 200
}
"""
********************* Sync AddProfileNumber ********************
* version: 2.0.0                                               *
****************************************************************
"""
@api {post} /webhook/api/v2.1/sync/add-profile Đồng bộ số lượng profile
@apiDescription Đồng bộ số lượng profile của 1 tenant.
@apiGroup Webhook
@apiVersion 2.0.0
@apiName SyncAddProfileNumber

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header

@apiParamExample  {json}  Example:
{
  "merchant_id": "6662c36e-64ef-4ac4-ad9d-8313038df064",
  "data": {
    "type": "add_profile_number",
    "number": 2
  },
  "checksum": "807ab9f0e42f4cc1a9f2f43454c9dcaf"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "message": "success",
  "code": 200
}
"""
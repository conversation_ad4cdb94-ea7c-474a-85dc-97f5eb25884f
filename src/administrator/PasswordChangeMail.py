#!/usr/bin/python
# -*- coding: utf8 -*-

******************************* API REQUEST CHANGE PASSWORD BY MAIL **********   
* version: 2.1.0                                                             *
******************************************************************************
"""
@api {post} /api/v2.1/account/action/request/mail_change_password Tạo request thay đổi mật khẩu qua mail
@apiDescription Yêu cầu gửi mail thay dổi mật khảu tài khoản
@apiVersion 2.1.0
@apiGroup AccountMailPasswordChange
@apiName RequestMailChangePassword
@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {String}    [account_id]                      id của tài khoản cần thay đổi mật khẩu <code>Nếu không truyền lên thì thay đổi mk tk hiện tại</code>
@apiParam      (Body:)     {String}    [email]                           email của nhận mail thay đổi mật khẩu <code> Nếu không truyền lên thì gửi vào mail của tk được thay đổi mk</code>


@apiParamExample {json} Info example
{
   "account_id": "uuid1",
   "email": "email.com"
 
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công."
}
"""

******************************* API LẤY INFO ACCOUNT THAY DOI MAT KHAU QUA MAIL **********                                                                     *
* version: 2.1.0                                                                         *
******************************************************************************************
"""
@api {get} /api/v2.1/account/cryption/infor_account Lấy info account thay đổi mật khẩu qua mail
@apiDescription Lấy info account thay đổi mật khẩu qua mail
@apiVersion 2.1.0
@apiGroup AccountMailPasswordChange
@apiName AccountInfoCryption
@apiUse 500

@apiParam      (query:) {String} cryption        Mã mã hóa để lấy thông tin của account.     



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "data": {
    "account_id": uuid,
    "account_name": "account_name"
    "email": "email.com",
    "cryption": "cryption gửi lên"
  }
  "message": "request thành công."
}
"""

******************************* API THAY ĐỔI MẬT KHẨU ACCOUNT BẰNG CRYPTION **************                                                              
* version: 2.1.0                                                                         *
******************************************************************************************
"""
@api {post} /api/v2.1/account/cryption/password_change Thay đổi mật khẩu account bằng cryption
@apiDescription Lấy info account Thay đổi mật khẩu account bằng cryption
@apiVersion 2.1.0
@apiGroup AccountMailPasswordChange
@apiName PasswordChangeCryption
@apiUse 500

@apiParam      (body:) {String} cryption        Mã mã hóa của account.     
@apiParam      (body:) {String} account_id      id của account
@apiParam      (body:) {String} password     Mật khẩu mới

@apiParamExample {json} Info example
{
   "cryption": "cryption code "
   "account_id": "uuid1",
   "password": "new password"
 
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công."
}
"""

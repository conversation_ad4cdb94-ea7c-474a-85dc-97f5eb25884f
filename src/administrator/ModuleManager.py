#!/usr/bin/python
# -*- coding: utf8 -*-

************************* API LẤY DANH SÁCH MODULE *************************
* version: 1.0.0                                                           *
* version: 2.1.0                                                           *
****************************************************************************
"""
@api {get} /api/v2.1/modules L<PERSON>y danh sách module
@apiDescription Lấy danh sách tất cả module
@apiVersion 2.1.0
@apiGroup Module
@apiName Get

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse search

@apiParam (Query:)    {string}       [type]         Lo<PERSON>i quyền <code>'created'- Tự tạo, 'default'- Mặc định </code>
@apiParam (Query:)    {string}       [account]      <code>1- trả về thông tin của người có quyền 2- mặc định không trả về </code>
@apiParam (Query:)    {string}       [sub_brand_id]      <code>merchant id của sub_brand</code>


@apiSuccess (data) {string} id Module id
@apiSuccess (data) {string} name Tên module
@apiSuccess (data) {string} description Mô tả module
@apiSuccess (data)    {Array}        accounts         Danh sách accounts
@apiSuccess (data)    {string}       policies           Danh sách ID policies

@apiSuccess (accounts)    {string}           id                 ID accounts
@apiSuccess (accounts)    {string}           username           username tên đăng nhập  của accounts
@apiSuccess (accounts)    {string}           avatar             Ảnh đại diện của accounts
@apiSuccess (accounts)    {string}           fullname           Tên đầy đủ của accounts
@apiSuccess (accounts)    {string}           phone_number       Số điện thoại của accounts
@apiSuccess (accounts)    {string}           email              Email của accounts

@apiUse created_time
@apiUse updated_time
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
           "account": [],
            "account_created": null,
            "account_updated": null,
            "created_time": "Thu, 04 Apr 2024 08:10:02 GMT",
            "description": "test",
            "function_names": [
                "Cấu hình nhận phân công công việc",
                "Đối soát email",
                "Cài đặt nâng cao",
                "Cài đặt nâng cao",
                "Cài đặt nâng cao",
                "Cài đặt nâng cao",
                "Đối soát email",
                "Đối soát email",
                "Cấu hình nhận phân công công việc",
                "Đối soát email"
            ],
            "id": "bca13509-f25a-11ee-a952-d9afa3c4e430",
            "name": "QUang test",
            "policies": [],
            "type": "created",
            "updated_time": "Thu, 04 Apr 2024 08:10:02 GMT"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "page_count": 97,
        "per_page": 1,
        "total_count": 97
    }
}
"""
*******************************
"""
@api {get} /api/v2.1/modules Lấy danh sách module
@apiDescription Lấy danh sách tất cả module
@apiVersion 1.0.0
@apiGroup Module
@apiName Get

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort
@apiUse search

@apiSuccess (data) {string} id Module id
@apiSuccess (data) {string} name Tên module
@apiSuccess (data) {string} description Mô tả module
@apiUse created_time
@apiUse updated_time
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
      "name": "Quản lý nội dung",
      "description": "Quản lý nội dung chương trình",
      "create_time": "2017-08-07T04:02:28.002Z",
      "update_time": "2017-08-07T04:02:d8.002Z"
    },
    ...
  ],
  "lang": "vi",
  "sort": "name",
  "order": "asc",
  "paging": {
    ...
  }
}
"""

************************* API LẤY CHI TIẾT MODULE *************************
* version: 1.0.0                                                          *
***************************************************************************
"""
@api {get} /api/v2.1/modules/<module_id> Lấy chi tiết module
@apiDescription Lấy thông tin chi tiết module
@apiVersion 1.0.0
@apiGroup Module
@apiName GetDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse created_time
@apiUse updated_time
@apiSuccess  {string} id module id
@apiSuccess  {string} id Module id
@apiSuccess  {string} name Tên module
@apiSuccess  {string} description Mô tả module
@apiSuccess (functions) {string} id function id
@apiSuccess (functions) {string} name Tên function
@apiSuccess (functions) {string} description Mô tả function
@apiSuccess (functions) {string} path Đường dẫn trên CRM để vào chức năng.
@apiSuccess (functions) {string} parent Phân biệt chức năng đang nằm trong nhóm menu.
@apiSuccess (actions) {string} id action id
@apiSuccess (actions) {string} name_vi Tên bằng tiếng Việt
@apiSuccess (actions) {string} name_en Tên bằng tiếng Anh
@apiSuccess (actions) {string} description Mô tả action
@apiSuccess (actions) {list} account   Danh sach id account
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
  "account": ["dasdasd121deasdasadasdasd"]
  "name": "Quản lý nội dung",
  "description": "Quản lý nội dung chương trình",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "functions": [
    {
      "id": "5fbe8dfe-a341-48b0-94b9-3fc9f6b412fa",
      "name": "Voucher",
      "description": "Quản lý voucher",
      "path": "/customer-care/content/voucher",
      "parent": "CSKH-Voucher"
      "iconname": "voucher.ico",
      "type": 1,
      "create_time": "2017-08-07T04:02:28.002Z",
      "update_time": "2017-08-07T04:02:28.002Z",
      "actions": [
        {
          "id": 2012,
          "name_vi": "Xem",
          "name_en": "View",
          "description": "Thao tác xem"
        },
        ...
      ]
    },
    ...
  ],
    "policies": [
        "88e27e2e-73cf-11ee-9512-9a1b69ea53c5
    ],
}
"""
************************* API TẠO MỘT MODULE cho sub_brands **********
* version: 2.1.0                                                     *
**********************************************************************
"""
@api{post} /api/v2.1/merchants/sub-brands/<sub_brand_id>/modules/create Tạo nhóm quyền cho sub brands
@apiDescription  Tạo nhóm quyền cho sub brands
@apiVersion 2.1.0
@apiGroup ModuleSubrand
@apiName Post

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {String}    name                 Tên nhóm quyền 
@apiParam      (Body:)     {String}    description          Mô tả nhóm quyên
@apiParam      (Body:)     {Array}     functions            Danh sách chức năng 
@apiParam      (Body:)     {number}    [is_all_function]    Chọn tất cả các quyền. <code>0: Không</code>, <code>1: Chọn tất cả</code>  
@apiSuccess    (Body:)     {String}       [type]           Loại quyền <code>'created'- Tự tạo, 'default'- Mặc định (không truyền lên lấy mặc định)</code>
@apiParam      (Body:)     {Array}     [policies]           Danh sách Id policy cần áp dụng cho module
@apiParam      (Functions:)     {String}    function_id     uuid chức năng 
@apiParam      (Functions:)     {Array}     action_ids      Danh sách uuid action


@apiParamExample {json} Body example
{   
  "merchant_id": "c7229324-ffe3-4090-8acb-27093003f703",
  "name": "Chăm sóc khách hàng",
  "description": "",
  "is_all_function": 1,
  "type": "created",
  "functions": [
      {
          "function_id": "c7229324-ffe3-4090-8acb-27093003f703",
          "function_name": "Cài đặt nâng cao",
          "action_ids": ["c7229324-ffe3-4090-8acb-27093003f703", "c7229324-ffe3-4090-8acb-27093003f703"]
      }
  ],
  "policies": []
}
"""

************************* API TẠO MỘT MODULE *************************
* version: 1.0.0                                                     *
**********************************************************************
"""
@api {post} /api/v2.1/modules Tạo một Module
@apiDescription Tạo một Module
@apiVersion 1.0.0
@apiGroup Module
@apiName Post

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {string} name Tên module
@apiParam  {string} description Mô tả module
@apiParam (assignments) {string} function_id ID function được gán cho module
@apiParam (assignments) {ArrayNumber} actions Danh sách id action được gán cho function
@apiParamExample {json} Body
{
  "name": "Voucher",
  "description": "Quản lý voucher",
  "assignments": [
    {
      "function_id": "5fbe8dfe-a341-48b0-94b9-3fc9f6b412fa",
      "actions": [
        1029,
        128
      ]
    },
    ...
  ]
}

@apiUse created_time
@apiUse updated_time
@apiSuccess  {string} id ID module vừa tạo thành công
@apiSuccess  {string} name Tên module vừa tạo
@apiSuccess  {string} description Mô tả module
@apiSuccess (assignments) {string} function_id ID chức năng thuộc module
@apiSuccess (assignments) {ArrayNumber} actions Danh sách id action thuộc function
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
  "name": "Voucher",
  "description": "Quản lý voucher",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "assignments": [
    {
      "function_id": "5fbe8dfe-a341-48b0-94b9-3fc9f6b412fa",
      "actions": [
        1029,
        128
      ]
    },
    ...
  ]
}
"""
************************* API SỬA MODULE cho sub_brand************
* version: 2.1.0                                                 *
******************************************************************

# **********************************************************************************************************************************
"""
@api{put} /api/v2.1/merchants/sub-brands/<sub_brand_id>/modules/<module_id>  Update nhóm quyền  cho subrand
@apiDescription Update nhóm quyền subrand
@apiVersion 2.1.0
@apiGroup ModuleSubrand
@apiName put

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {String}    name                 Tên nhóm quyền 
@apiParam      (Body:)     {String}    description          Mô tả nhóm quyên
@apiParam      (Body:)     {Array}     functions            Danh sách chức năng 
@apiParam      (Body:)     {number}    [is_all_function]    Chọn tất cả các quyền. <code>0: Không</code>, <code>1: Chọn tất cả</code>
@apiParam      (Body:)     {Array}    [policies]            Danh sách Id policies cần cập nhập
  

@apiParam      (Functions:)     {String}    function_id     uuid chức năng 
@apiParam      (Functions:)     {Array}     action_ids      Danh sách uuid action


@apiParamExample {json} Body example
{   
  "name": "Chăm sóc khách hàng",
  "description": "",
  "is_all_function": 1,
  "functions": [
      {
          "function_id": "c7229324-ffe3-4090-8acb-27093003f703",
          "action_ids": ["c7229324-ffe3-4090-8acb-27093003f703", "c7229324-ffe3-4090-8acb-27093003f703"]
      }
  ],
  "policies: []

}
"""

************************* API SỬA MODULE *************************
* version: 1.0.0                                                 *
******************************************************************

# **********************************************************************************************************************************
"""
@api {patch} /api/v2.1/modules/<module_id> Sửa module
@apiDescription Sửa một module
@apiVersion 1.0.0
@apiGroup Module
@apiName Patch

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {string} [name] Tên module
@apiParam  {string} [description] Mô tả module
@apiParam (assignments) {string} [function_id] ID function được gán cho module
@apiParam (assignments) {ArrayNumber} [actions] Danh sách id action được gán cho function
@apiParamExample {json} Body
{
  "name": "Voucher",
  "description": "Quản lý voucher",
  "assignments": [
    {
      "function_id": "5fbe8dfe-a341-48b0-94b9-3fc9f6b412fa",
      "actions": [
        1029,
        128
      ]
    },
    ...
  ]
}

@apiUse created_time
@apiUse updated_time
@apiSuccess  {string} id ID module vừa sửa
@apiSuccess  {string} name Tên module vừa sửa
@apiSuccess  {string} description Mô tả module
@apiSuccess (assignments) {string} function_id ID function thuộc module
@apiSuccess (assignments) {ArrayNumber} actions Danh sách id action thuộc function
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
  "name": "Voucher",
  "description": "Quản lý voucher",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z",
  "assignments": [
    {
      "function_id": "5fbe8dfe-a341-48b0-94b9-3fc9f6b412fa",
      "actions": [
        1029,
        128
      ]
    },
    ...
  ]
}
"""
************************* API XOÁ MODULE *************************
* version: 2.0.0                                                 *
******************************************************************
"""
@api {delete} /api/v2.1/modules Xóa nhóm quyền cho subrand
@apiDescription Xóa nhóm quyền cho subrand
@apiVersion 2.0.0
@apiGroup ModuleSubrand
@apiName Delete
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Query:)     {String}    sub_brand_id      Merchant_id của sub_brand merchant
@apiParam      (Query:)     {String}    ids      Tập hợp các UUID của nhóm quyền.

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công.",
  "ids_module": ["597281cb-03a5-437c-a4c1-95a6b5289af6",
          "2e673fa2-c292-4ea4-aca2-3a378234926e"]
}

"""

************************* API XOÁ MODULE *************************
* version: 1.0.0                                                 *
******************************************************************
"""
@api {delete} /api/v2.1/modules/<module_id> Xoá module
@apiDescription Xoá một module
@apiVersion 1.0.0
@apiGroup Module
@apiName Delete

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {string} id Module id vừa được yêu cầu xoá
@apiSuccess  {string} name Tên module
@apiSuccess  {string} description Mô tả module
@apiUse created_time
@apiUse updated_time
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
  "name": "Voucher",
  "description": "Quản lý voucher",
  "create_time": "2017-08-07T04:02:28.002Z",
  "update_time": "2017-08-07T04:02:28.002Z"
}

"""
"""
@api {get} /api/v2.1/merchants/modules/type Lấy danh sách module theo loại 
@apiDescription Lấy danh sách tất cả module theo loại 
@apiVersion 2.1.0
@apiGroup Module
@apiName GetAllByType 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:)    {string}       type        Loại quyền <code>'created'- Tự tạo, 'default'- Mặc định </code>
@apiParam (Query:)    {string}       [merchant_id]        id merchant
@apiParam (Query:)    {string}       [info_extra]        thông tin cần lấy thêm, nhiều giá trị cách nhau dấu , (VD: function,policy)


@apiSuccess (data) {string} id Module id
@apiSuccess (data) {string} name Tên module
@apiSuccess (data) {string} description Mô tả module
@apiSuccess (data) {Array} functions danh sách id chức năng 
@apiSuccess (data) {Array} policies danh sách id chính sách 
@apiSuccess (data) {Array} product_line danh sách line quyền này được truy cập  


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "493383ee-9c0a-45c4-b983-1c7fd6cc9428",
      "name": "Quản lý nội dung",
      "description": "Quản lý nội dung chương trình",
      "functions": [
          "c2052960-eb1c-11ee-b488-e9c7a8fbfd6c",
          "dd738dca-29d4-11ee-8cb9-0337691022ce"  
        ],
      "policies": [
        "c2052960-eb1c-11ee-b488-e9c7a8fbfd6c",
        "dd738dca-29d4-11ee-8cb9-0337691022ce"  
      ],
      "product_line": [
          "activation",
          "operation_sale",
          "operation_service"
      ]
    },
    ...
  ],
  "lang": "vi"
}
"""

"""
@api {get} /api/v2.1/accounts/role-assign/function-action Lấy function action gán cho tài khoản 
@apiDescription Lấy danh sách tất cả function và action gán cho tài khoản  
@apiVersion 2.1.0
@apiGroup Module
@apiName GetFunctionActionAssignAccount 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "actions": [1,7],
      "function_id": "********-511a-11ee-96bc-e6f755e1e4cc",
      "path": "/bank/export-report/sale-memo",
      "product_apply": ["analytics", "activation"],
    },
    ...
  ],
  "lang": "vi"
}
"""



"""
@api {GET} /adm/api/v2.1/modules/product-line  danh sách gói chức năng  
@apiDescription gói chức năng cho phần quyền account 
@apiGroup License
@apiVersion 1.0.0
@apiName LicenseProductLine 
@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "value": "analytics",
            "name": "Analytics",
        },
        
    ],
    "lang": "en",
    "message": "request successful."
}
"""

"""
@api {post} /api/v2.1/modules/policy gán gỡ policy abac cho nhóm quyền 
@apiDescription cập nhật policy abac cho nhóm quyền tự tạo 
@apiVersion 1.0.0
@apiGroup Module
@apiName AddPolicyToModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParamExample {json} Body
{
  "module_id": "********-511a-11ee-96bc-e6f755e1e4cc",
  "policies": ["5fbe8dfe-a341-48b0-94b9-3fc9f6b412fa"]  // gỡ hết policy thì truyền []
}


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "en",
    "message": "request successful."
}
"""

"""
@api {post} /api/v2.1/modules/<module_id>/check kiểm tra quyền được phép sửa hay không 
@apiDescription với nhóm quyền gán nhiều tài khoản thời gian xử lý gán quyền cho tài khoản dài, api này check xử lý xong hay chưa 
@apiVersion 1.0.0
@apiGroup Module
@apiName CheckUpdateModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang



@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    // đã hoàn thành 
    "code": 200,
    
    // chưa xử lý xong 
    "code": 400 

    "lang": "en",
    "message": "request successful."
}
"""


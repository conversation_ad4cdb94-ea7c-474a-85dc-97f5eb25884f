************************** Queue đăng kí trigger broadcast ****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} factory-jb-register-broadcast Luồng đăng kí trigger broadcast
@apiDescription Luồng đăng kí trigger broadcast. <code>Lưu ý luồng đăng kí trigger và luồng dừng trigger khi bắn sang cần truyền kafka_key giống nhau</code>
@apiGroup RegisterTrigger
@apiVersion 1.0.0
@apiName RegisterTriggerBroadcast

@apiParamExample [json] Input example:
{
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "object_id": "ads_id",
    "source": "ads", // workflow, journey
    "status": "start",
    "action_time": 1672887881.696831,
    "start_time": 1672887876.0,
    "end_time": 1672999200.0, // Nếu không có thì để null
    "audiences_info":
    {
        "audience_filter":
        [
            {
                "profile_filter": [
                    {
                        "criteria_key" : "cri_profile_address",
                        "operator_key" : "op_is_has",
                        "values" : [ 
                            "Hà Nội"
                        ]
                    }
                ],
                "profile_scan_filter": null,
                "position": 0,
                "operator": null
            }
        ],
        "event_setting_field":
        {
            "trigger_receive_noti_from_website":
            {
                "criteria_key": "trigger_receive_noti_from_website",
                "operator_key": "op_is_multiple",
                "values":
                [
                    {
                        "criteria_key": "cri_url",
                        "values":
                        [
                            "EQ",
                            "test37.mobio.vn"
                        ],
                        "operator_key": "op_is_equal"
                    }
                ]
            }
        },
        "version": 2
    },
    "call_back_info":
    {
        "kafka_topic": "kafka_topic_callback",
        "need_adding_kafka_data":
        {
            // ... Thông tin cần trả về
            "field_1": "123",
            "field_2": "321"
        }
    },
    "profile_data_field_name": ["name", "created_time", "updated_time"], # default ["merchant_id", "profile_id"]
    "condition_wait_all_event_seconds": 10, # default: null
}

@apiSuccessExample  {json}  Callback Queue Register Trigger Success:
{
    "code": 200,
    "message": "Register Success!",
    "data_callback": { // chính là field need_adding_kafka_data
        "field_1": "123",
        "field_2": "321"
    }
}

@apiSuccessExample  {json}  Callback Queue Register Trigger Error:
{
    "code": 500,
    "message": "Register Error!",
    "message_error": "trigger_transaction_id is required!",
    "data_callback": { // chính là field need_adding_kafka_data
        "field_1": "123",
        "field_2": "321"
    }
}

@apiSuccessExample  {json}  Callback Queue Profile:
{
    "code": 200,
    "data": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": [
            "618261e0-dee6-4440-a744-89f67235b347"
        ],
        "name": "Đoàn Bắc",
        "created_time": "2023-01-07T15:46:00.000Z",
        "updated_time": "2023-01-07T15:46:00.000Z",
        "is_non_profile": false
    },
    "data_callback": { // chính là field need_adding_kafka_data
        "field_1": "123",
        "field_2": "321",
        "data_triggers": [
            {
                "trigger_id": "648ab7c5967cf42e31209800",
                "event_id": "628b478cc9e3672bdb5855fa",
                "type": "dynamic_event"
            },
            {
                "trigger_id": "648ab7c5967cf42e31209800",
                "event_id": "_base_e27", // callcenter
                "type": "base_event",
                "call_id": ""
            },
            {
                "trigger_id": "648ab7c5967cf42e31209801",
                "event_id": "_base_special_day",
                "type": "base_event",
                "field": "birthday" // field profile: _dyn_field_example_xxx
            },
            {
                "trigger_id": "648ab7c5967cf42e31209802",
                "event_id": "_base_e163", // email support
                "type": "base_event",
                "message_id": ""
            },
            {
                "trigger_id": "648ab7c5967cf42e31209803",
                "event_id": "_base_e111", // Finish Survey
                "type": "base_event",
                "survey_id": ""
            },
            {
                "trigger_id": "648ab7c5967cf42e31209804",
                "event_id": "_base_e150", // Popup submit form
                "type": "base_event",
                "popup_id": ""
            }
        ]
    }
}

@apiSuccessExample  {json}  Callback Queue 404:
{
    "code": 404,
    "data_input": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": "618261e0-dee6-4440-a744-89f67235b347"
    },
    "reason": "profile is merged!",
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}
"""


***************************** Queue stop trigger broadcast ****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} factory-jb-register-broadcast Luồng stop trigger broadcast
@apiDescription Luồng stop trigger broadcast, <code>Lưu ý luồng đăng kí trigger và luồng dừng trigger khi bắn sang cần truyền kafka_key giống nhau</code>
@apiGroup RegisterTrigger
@apiVersion 1.0.0
@apiName StopTriggerBroadcast

@apiParamExample [json] Input example:
{
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "object_id": "ads_id",
    "source": "ads", // workflow, journey
    "status": "stop",
    "action_time": 1672887881.696831
}
"""


************************** Queue đăng kí trigger single *******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} factory-jb-register-single Luồng đăng kí trigger single
@apiDescription Luồng đăng kí trigger single. <code>Lưu ý: luồng của jb vì là luồng cũ nên chưa gửi instance_id ở cấp 1 sang</code>
@apiGroup RegisterTrigger
@apiVersion 1.0.0
@apiName RegisterTriggerSingle

@apiParamExample [json] Input example:
{
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "profile_id": "1b99bdcf-d582-4f49-9715-1b61dfff3920",
    "instance_id": "119f8f95-1a50-47a0-ac83-59397fe52019",
    "node_id": "1b99bdcf-d582-4f49-9715-1b61dfff3925",
    "object_id": "1b99bdcf-d582-4f49-9715-1b61dfff3926", // workflow_id, journey_id, ads_id
    "source": "workflow", // workflow, journey, ads
    "status": "start",
    "action_time": 1672887881.696831,
    "start_time": 1672887876.0,
    "end_time": 1672999200.0, // Nếu không có thì để null
    "audiences_info":
    {
        "audience_filter":
        [
            {
                "profile_filter": [
                    {
                        "criteria_key" : "cri_profile_address",
                        "operator_key" : "op_is_has",
                        "values" : [ 
                            "Hà Nội"
                        ]
                    }
                ],
                "profile_scan_filter": null,
                "position": 0,
                "operator": null
            }
        ],
        "event_setting_field":
        {
            "trigger_receive_noti_from_website":
            {
                "criteria_key": "trigger_receive_noti_from_website",
                "operator_key": "op_is_multiple",
                "values":
                [
                    {
                        "criteria_key": "cri_url",
                        "values":
                        [
                            "EQ",
                            "test37.mobio.vn"
                        ],
                        "operator_key": "op_is_equal"
                    }
                ]
            }
        },
        "version": 2
    },
    "call_back_info":
    {
        "kafka_topic": "kafka_topic_callback",
        "need_adding_kafka_data":
        {
            // ... Thông tin cần trả về
            "field_1": "123",
            "field_2": "321"
        }
    },
    "profile_data_field_name": ["name", "created_time", "updated_time"], # default ["merchant_id", "profile_id"]
    "condition_wait_all_event_seconds": 10, # default: null
}

@apiSuccessExample  {json}  Callback Queue Profile:
{
    "code": 200,
    "data": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": [
            "618261e0-dee6-4440-a744-89f67235b347"
        ],
        "name": "Đoàn Bắc",
        "created_time": "2023-01-07T15:46:00.000Z",
        "updated_time": "2023-01-07T15:46:00.000Z",
        "is_non_profile": false
    },
    "data_callback": { // chính là field need_adding_kafka_data
        "field_1": "123",
        "field_2": "321",
        "data_triggers": [
            {
                "trigger_id": "648ab7c5967cf42e31209800",
                "event_id": "628b478cc9e3672bdb5855fa",
                "type": "dynamic_event"
            },
            {
                "trigger_id": "648ab7c5967cf42e31209800",
                "event_id": "_base_e27", // callcenter
                "type": "base_event",
                "call_id": ""
            },
            {
                "trigger_id": "648ab7c5967cf42e31209801",
                "event_id": "_base_special_day",
                "type": "base_event",
                "field": "birthday" // field profile: _dyn_field_example_xxx
            },
            {
                "trigger_id": "648ab7c5967cf42e31209802",
                "event_id": "_base_e163", // email support
                "type": "base_event",
                "message_id": ""
            },
            {
                "trigger_id": "648ab7c5967cf42e31209803",
                "event_id": "_base_e111", // Finish Survey
                "type": "base_event",
                "survey_id": ""
            },
            {
                "trigger_id": "648ab7c5967cf42e31209804",
                "event_id": "_base_e150", // Popup submit form
                "type": "base_event",
                "popup_id": ""
            }
        ]
    }
}

@apiSuccessExample  {json}  Callback Queue 404:
{
    "code": 404,
    "data_input": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": "618261e0-dee6-4440-a744-89f67235b347"
    },
    "reason": "profile is merged!",
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}
"""
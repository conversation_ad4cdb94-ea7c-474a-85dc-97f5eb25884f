
********************************* Workflow Detail Report *********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /detail/workflow Báo cáo chi tiết workflow
@apiGroup WorkflowDetailReport
@apiVersion 1.0.0
@apiName DetailWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}    [start_time] Thời gian bắt đầu lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>    
@apiParam   (Query:)     {String}    [end_time]  Thời gian kết thúc lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Query:)    {String}    workflow_id      Mã định danh workflow


@apiSuccess     {Number}    total_objects    Đối tượng vào khối
@apiSuccess     {Number}    total_object_tracks   Lượt Đối tượng vào khối
@apiSuccess     {Number}    total_object_tracks_seven_days   Lượt Đối tượng vào khối trong 7 ngày gần nhất
@apiSuccess     {Number}    total_push    Đối tượng gửi
@apiSuccess     {Number}    total_push_tracks    Lượt đối tượng gửi
@apiSuccess     {Number}    total_sent    Đối tượng đã gửi thành công
@apiSuccess     {Number}    total_sent_tracks   Lượt gửi thành công
@apiSuccess     {Number}    total_sent_tracks_percent    Số phần trăm Lượt gửi thành công
@apiSuccess     {Number}    total_open_tracks    Lượt mở
@apiSuccess     {Number}    total_open_tracks_percent    Số phần trăm lượt mở
@apiSuccess     {Number}    total_click_link_tracks   lượt bấm link
@apiSuccess     {Number}    total_click_link_tracks_percent    Phần trăm lượt bấm link
@apiSuccess     {Number}    total_created_tickets    Tổng số ticket đã tạo
@apiSuccess     {Number}    total_created_tasks   Tổng số công việc đã tạo
@apiSuccess     {Number}    total_created_orders   Tổng số cơ hội bán đã tạo
@apiSuccess     {Number}    total_created_profiles   Tổng số profile đã tạo
@apiSuccess     {Number}    total_created_companies   Tổng số công ty đã tạo
@apiSuccess     {Number}    total_check_tickets    Tổng số ticket kiểm tra
@apiSuccess     {Number}    total_check_tasks   Tổng số công việc kiểm tra
@apiSuccess     {Number}    total_created_items   Tổng số hành động đã tạo
@apiSuccess     {Number}    total_percent    Số phần trăm
@apiSuccess     {Number}    total_satisfied_status_items    Số hành động thỏa mãn trạng thái
@apiSuccess     {Number}    total_sent_fail_tracks   Lượt gửi không thành công
@apiSuccess     {Number}    total_sent_fail_tracks_percent    Số phần trăm Lượt gửi không thành công
@apiSuccess     {Number}    total_arise_action   Số lượng đối tượng phát sinh hành động
@apiSuccess     {Number}    total_created_tickets_fail    Tổng số ticket tạo không thành công
@apiSuccess     {Number}    total_created_tasks_fail   Tổng số công việc tạo không thành công
@apiSuccess     {Number}    total_created_orders_fail   Tổng số chb tạo không thành công
@apiSuccess     {Number}    total_result_tracks     Số lượt vào nhánh
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "diagram": [],
        "nodes": [
            {
                "node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083",
                "node_type": "TRIGGER_EVENT",
                "element_type": "TRIGGER",
                "node_name": "Ten khoi"
                "node_config": {
                    "audience_id": "c830e338-e287-11ed-b545-8150b6abec2b",
                    "condition_wait_all_event": {
                        "unit": "minutes/hours/days",
                        "value": 5
                    }
                },
                "report": {
                    "total_objects": 10,
                    "total_object_tracks": 20,
                    "total_object_tracks_seven_days": 10
                },
                "node_connection": {
                    "next_node_id": "84497070-c6f5-11ed-8fe8-c7a3239de083",
                    "previous_node_id": "72d95c56-c6f5-11ed-8fe8-c7a3239de083"
                }
            },
            {
                "node_id": "4506c31a-c7a2-11ed-a58f-cd3807f6ef5e",
                "node_type": "CONDITION_FILTER_PROFILE",
                "element_type": "SPECIFIC_CONDITION",
                "node_name": "Ten khoi",
                "node_config": {
                    "audience_id": "c069393a-e28b-11ed-b545-8150b6abec2b"   
                },
                "report": {
                    "total_objects": 10,
                    "total_object_tracks": 20  
                },
                "node_connection": [
                    {
                        "result": "yes",
                        "next_node_id": "94d5413a-c6f5-11ed-8fe8-c7a3239de083",
                        "previous_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083",
                        "report": {
                            "total_percent": 45
                        }
                    },
                    {
                        "result": "no",
                        "next_node_id": "2d5db6d8-c7a2-11ed-a58f-cd3807f6ef5e",
                        "previous_node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083",
                        "report": {
                            "total_percent": 55
                        }
                    }
                ]
            },
            {
                "node_id": "2d5db6d8-c7a2-11ed-a58f-cd3807f6ef5e",
                "node_type": "ACTION_SEND_SMS",
                "element_type": "OUTER_ACTION",
                "node_name": "Ten khoi",
                "node_config": {
                    "content": "Noi dung sms",
                    "personalizes": [
                        {
                            "field_name": "Đối tượng",
                            "key": "WORKFLOW_TARGET",
                            "replace": "*|WORKFLOW_TARGET|*",
                            "source": "WORKFLOW"
                        },
                        {
                            "field_name": "Tên workflow",
                            "key": "WORKFLOW_NAME",
                            "replace": "*|WORKFLOW_NAME|*",
                            "source": "WORKFLOW"
                        }
                    ],
                    "links": [
                        {
                            "url": "http://abc.com",
                            "domain_shorting_link": "https://url.mobio.io/",
                            "key": "*|LINK|*"
                        }
                    ],
                    "sender_config":{
                      "brand_name":"MOBIO",
                      "send_to": "primary_phone/all"
                    }
                },
                "report": {
                    "total_objects": 10,
                    "total_object_tracks": 20,
                    "total_push": 10,
                    "total_push_tracks": 20,
                    "total_sent": 10,
                    "total_sent_tracks": 20,
                    "total_sent_tracks_percent": 50,
                    "total_open_tracks": 10,
                    "total_open_tracks_percent": 50
                    "total_click_link_tracks": 11,
                    "total_click_link_tracks_percent": 100
                },
                "node_connection": {
                    "next_node_id": "b28edc7c-c6f5-11ed-8fe8-c7a3239de083",
                    "previous_node_id": "4506c31a-c7a2-11ed-a58f-cd3807f6ef5e"
                }
            },
            {
                "node_id": "2d5db6d8-c7a2-11ed-a58f-cd3807f6ef5e",
                "node_type": "ACTION_CREATE_TICKET",
                "element_type": "MANAGEMENT_ACTION",
                "node_name": "Ten khoi",
                "node_config": {
                    "name": "Ten ticket",
                    "type_ticket_id": "Kieu ticket",
                    "priority_level": "Muc do uu tien",
                    "status_process_id": "Trang thai xu ly",
                    "description": "Mo ta",
                    "assign_information": {
                        "type_assign": "wait_assignment/staff",
                        "assign_id": "",
                        "type_staff_assign": "assign_staff/staff_charge_switchboard/profile_owner"
                    }
                },
                "report": {
                    "total_objects": 10,
                    "total_object_tracks": 20,
                    "total_created_tickets": 10
                },
                "node_connection": {
                    "next_node_id": "b28edc7c-c6f5-11ed-8fe8-c7a3239de083",
                    "previous_node_id": "4506c31a-c7a2-11ed-a58f-cd3807f6ef5e"
                }
            },
            {
                "node_id": "2d5db6d8-c7a2-11ed-a58f-cd3807f6ef5e",
                "node_type": "WAIT",
                "element_type": "MANAGEMENT_ACTION",
                "node_name": "Ten khoi",
                "node_config": {
                    
                },
                "report": {
                    "total_objects": 10,
                    "total_object_tracks": 20
                },
                "node_connection": {
                    "next_node_id": "b28edc7c-c6f5-11ed-8fe8-c7a3239de083",
                    "previous_node_id": "4506c31a-c7a2-11ed-a58f-cd3807f6ef5e"
                }
            },
            {
                "node_id": "dde957c2-3011-4a8f-b512-6529729938cf",
                "element_type": "SPECIFIC_CONDITION",
                "node_type": "CONDITION_MULTI_BRANCH",
                "node_name": "Tách nhánh theo điều kiện 1",
                "node_connection": [],
                "report": {
                    "total_objects": 10,
                    "total_object_tracks": 20
                },
                "node_config": {
                    "condition_branchs": [
                        {
                            "audiences": [
                                {
                                    "type": "PROFILE",
                                    "position": 0,
                                    "audience_id": "e6f9a35d-96e6-494f-83fc-01f45ef5879a"
                                }
                            ],
                            "position": 0,
                            "report": {
                                "total_percent": 55,
                                "total_result_tracks": 100
                            },
                            "node_connection": [
                                {
                                    "next_node_id": "6f46e35a-cbba-4cd8-a7f9-b9cd551bdfd0",
                                    "previous_node_id": "18cb8a2e-f7f2-49ee-bd7f-aaf39e8b9ed6"
                                }
                            ]
                        },
                        {
                            "audiences": [
                                {
                                    "type": "TICKET",
                                    "position": 0,
                                    "audience_id": "d5635ed9-05d2-42fe-b2c8-dc2e781ceb11"
                                }
                            ],
                            "position": 1,
                            "report": {
                                "total_percent": 55,
                                "total_result_tracks": 100
                            },
                            "node_connection": [
                                {
                                    "next_node_id": "89750cb4-3011-11ee-8095-4a10c3cbe706",
                                    "previous_node_id": "18cb8a2e-f7f2-49ee-bd7f-aaf39e8b9ed6"
                                }
                            ]
                        },
                        {
                            "position": 2,
                            "report": {
                                "total_percent": 55,
                                "total_result_tracks": 100
                            },
                            "node_connection": [
                                {
                                    "next_node_id": "76d832f4-621f-459e-bf4e-9bbe0a20af2b",
                                    "previous_node_id": "18cb8a2e-f7f2-49ee-bd7f-aaf39e8b9ed6"
                                }
                            ]
                        }
                    ]
                }
            },
            {
                "node_id": "2d5db6d8-c7a2-11ed-a58f-cd3807f6ef5e",
                "node_type": "CHECK_ACTION_INTERACTION",
                "element_type": "MANAGEMENT_ACTION",
                "node_name": "Ten khoi",
                "node_config": {
                    "actions": [
                        {
                            "action_id": "dd04f28f-b2a5-41a0-82df-3afbd39e428b",
                            "action_status": [""],
                            "total_created_items": 100,
                            "total_satisfied_status_items": 10
                        },
                        {
                            "action_id": "1c5ae5f0-ff52-47b7-94a2-2062c9b5f657",
                            "action_status": [""],
                            "total_created_items": 100,
                            "total_satisfied_status_items": 10
                        }
                    ]
                },
                "report": {
                    "total_objects": 10,
                    "total_object_tracks": 20,
                    "total_created_tickets": 100,
                    "total_created_tasks": 110
                },
                "node_connection": {
                    "next_node_id": "b28edc7c-c6f5-11ed-8fe8-c7a3239de083",
                    "previous_node_id": "4506c31a-c7a2-11ed-a58f-cd3807f6ef5e"
                }
            }
        ]
    }
}
"""
*********************** Action Communicate Internal Staff Detail *************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /detail/node/action-communicate-internal Báo cáo chi tiết nhân viên khối giao tiếp nội bộ
@apiGroup WorkflowDetailReport
@apiVersion 1.0.0
@apiName ActionCommunicateInternalStaffDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [start_time] Thời gian bắt đầu lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>     
@apiParam   (Body:)     {String}    [end_time]  Thời gian kết thúc lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Body:)    {String}    workflow_id      Mã định danh workflow
@apiParam   (Body:)    {String}    node_id      Mã định danh khối
@apiParam   (Body:)    {String}    status      Trạng thái lấy báo cáo. Giá trị: <code>SUCCESS</code>=Gửi thành công</br>
                                                                                <code>FAIL</code>=Gửi không thành công</br>
@apiParamExample    {json}  Body:
{
    "start_time": "Tên workflow",
    "end_time": "Mô tả workflow",
    "workflow_id": "163e0c28-b545-11ee-b485-38d57a786a3e",
    "node_id": "6a21de79-8884-48fd-bad6-0d78cad45386",
    "status": 'SUCCESS/FAIL'
}

@apiSuccess     {String}    id    ID định danh
@apiSuccess     {String}    input_object_name   Tên đối tượng đầu vào
@apiSuccess     {String}    input_object_code   Mã đối tượng đầu vào
@apiSuccess     {String}    staff_id    ID Nhân viên
@apiSuccess     {String}    push_time   Thời gian gửi
@apiSuccess     {String}    open_status Trạng thái mở. Giá trị: <code>OPENED</code>=Đã mở<br/>
                                                                <code>NOT_OPEN</code>=Chưa mở<br/>
                                                                <code>OPEN_ERROR</code>=Lỗi không ghi nhận được mở<br/>
@apiSuccess     {String}    click_link_status Trạng thái mở. Giá trị: <code>CLICKED</code>=Đã bấm link<br/>
                                                                <code>NOT_CLICK</code>=Chưa bấm link<br/>
                                                                <code>CLICK_ERROR</code>=Lỗi không ghi nhận được bấm link<br/>
@apiSuccess     {String}    message_error   Lý do gửi không thành công
@apiSuccess     {Object}    encrypt_data   Thông tin sử dụng để giải mã

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "id": "e0220691-5fae-4089-872c-4fdc1a19a348",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "staff_id": "d73db7ff-e109-4159-a6a0-8c3d0e1dc124",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Email bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "9caccc19-37ff-4695-9d68-86b2186ccccf",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "staff_id": "3026a75a-8512-498e-a5d7-54be16e64923",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Email bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "9fc378f1-92b9-4d47-af49-af0b831c1aea",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "staff_id": "3bdf8abf-6883-4995-870f-d7357f879791",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Email bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        }
    ]
}
"""
*********************** Action Send Email Internal Staff Detail **************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /detail/node/action-send-email-internal Báo cáo chi tiết nhân viên khối gửi email nội bộ
@apiGroup WorkflowDetailReport
@apiVersion 1.0.0
@apiName ActionSendEmailInternalStaffDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [start_time] Thời gian bắt đầu lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>     
@apiParam   (Body:)     {String}    [end_time]  Thời gian kết thúc lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Body:)    {String}    workflow_id      Mã định danh workflow
@apiParam   (Body:)    {String}    node_id      Mã định danh khối
@apiParam   (Body:)    {String}    status      Trạng thái lấy báo cáo. Giá trị: <code>SUCCESS</code>=Gửi thành công</br>
                                                                                <code>FAIL</code>=Gửi không thành công</br>

@apiParamExample    {json}  Body:
{
    "start_time": "Tên workflow",
    "end_time": "Mô tả workflow",
    "workflow_id": "163e0c28-b545-11ee-b485-38d57a786a3e",
    "node_id": "6a21de79-8884-48fd-bad6-0d78cad45386",
    "status": 'SUCCESS/FAIL'
}

@apiSuccess     {String}    id    ID định danh
@apiSuccess     {String}    input_object_name   Tên đối tượng đầu vào
@apiSuccess     {String}    input_object_code   Mã đối tượng đầu vào
@apiSuccess     {String}    email    Email gửi
@apiSuccess     {String}    staff_id    ID Nhân viên
@apiSuccess     {String}    push_time   Thời gian gửi
@apiSuccess     {String}    open_status Trạng thái mở. Giá trị: <code>OPENED</code>=Đã mở<br/>
                                                                <code>NOT_OPEN</code>=Chưa mở<br/>
                                                                <code>OPEN_ERROR</code>=Lỗi không ghi nhận được mở<br/>
@apiSuccess     {String}    click_link_status Trạng thái mở. Giá trị: <code>CLICKED</code>=Đã bấm link<br/>
                                                                <code>NOT_CLICK</code>=Chưa bấm link<br/>
                                                                <code>CLICK_ERROR</code>=Lỗi không ghi nhận được bấm link<br/>
@apiSuccess     {String}    message_error   Lý do gửi không thành công
@apiSuccess     {Object}    encrypt_data   Thông tin sử dụng để giải mã

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "id": "c1ecae68-c0dd-49bc-953f-0018cff75493",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "email": "<EMAIL>",
            "staff_id": "d73db7ff-e109-4159-a6a0-8c3d0e1dc124",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Email bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "52f52e4b-0a5d-446a-8dfc-2912094ce34f",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "email": "<EMAIL>",
            "staff_id": "3026a75a-8512-498e-a5d7-54be16e64923",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Email bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "ed8cd1c9-89fd-48da-bb47-ba7d5899e43e",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "email": "<EMAIL>",
            "staff_id": "3bdf8abf-6883-4995-870f-d7357f879791",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Email bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        }
    ]
}
"""
***************************** Check Notify Interaction Detail ****************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /detail/node/check-notify-interaction Báo cáo chi tiết khối kiểm tra tương tác thông báo
@apiGroup WorkflowDetailReport
@apiVersion 1.0.0
@apiName CheckNotifyInteractionDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [start_time] Thời gian bắt đầu lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>     
@apiParam   (Body:)     {String}    [end_time]  Thời gian kết thúc lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Body:)    {String}    workflow_id      Mã định danh workflow
@apiParam   (Body:)    {String}    node_id      Mã định danh khối

@apiParamExample    {json}  Body:
{
    "start_time": "Tên workflow",
    "end_time": "Mô tả workflow",
    "workflow_id": "163e0c28-b545-11ee-b485-38d57a786a3e",
    "node_id": "6a21de79-8884-48fd-bad6-0d78cad45386"
}

@apiSuccess     {String}    id    ID định danh
@apiSuccess     {String}    input_object_name   Tên đối tượng đầu vào
@apiSuccess     {String}    input_object_code   Mã đối tượng đầu vào
@apiSuccess     {String}    email    Email gửi
@apiSuccess     {String}    staff_id    ID Nhân viên
@apiSuccess     {String}    push_time   Thời gian gửi
@apiSuccess     {String}    sent_status Trạng thái nhận. Giá trị: <code>SENT</code>=Đã nhận<br/>
                                                                <code>NOT_SENT</code>=Chưa nhận<br/>
                                                                <code>SENT_ERROR</code>=Lỗi không ghi nhận được đã nhận<br/>
@apiSuccess     {String}    open_status Trạng thái mở. Giá trị: <code>OPENED</code>=Đã mở<br/>
                                                                <code>NOT_OPEN</code>=Chưa mở<br/>
                                                                <code>OPEN_ERROR</code>=Lỗi không ghi nhận được mở<br/>
@apiSuccess     {String}    click_link_status Trạng thái mở. Giá trị: <code>CLICKED</code>=Đã bấm link<br/>
                                                                <code>NOT_CLICK</code>=Chưa bấm link<br/>
                                                                <code>CLICK_ERROR</code>=Lỗi không ghi nhận được bấm link<br/>
@apiSuccess     {Object}    encrypt_data   Thông tin sử dụng để giải mã

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "id": "c1ecae68-c0dd-49bc-953f-0018cff75493",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "email": "<EMAIL>",
            "staff_id": "d73db7ff-e109-4159-a6a0-8c3d0e1dc124",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "52f52e4b-0a5d-446a-8dfc-2912094ce34f",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "email": "<EMAIL>",
            "staff_id": "3026a75a-8512-498e-a5d7-54be16e64923",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "ed8cd1c9-89fd-48da-bb47-ba7d5899e43e",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "email": "<EMAIL>",
            "staff_id": "3bdf8abf-6883-4995-870f-d7357f879791",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        }
    ]
}
"""
*********************** Action Forward Email Staff Detail ********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /detail/node/action-forward-email-staff Báo cáo chi tiết nhân viên khối chuyển tiếp email
@apiGroup WorkflowDetailReport
@apiVersion 1.0.0
@apiName ActionForwardEmailStaffDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [start_time] Thời gian bắt đầu lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>     
@apiParam   (Body:)     {String}    [end_time]  Thời gian kết thúc lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Body:)    {String}    workflow_id      Mã định danh workflow
@apiParam   (Body:)    {String}    node_id      Mã định danh khối
@apiParam   (Body:)    {String}    status      Trạng thái lấy báo cáo. Giá trị: <code>SUCCESS</code>=Gửi thành công</br>
                                                                                <code>FAIL</code>=Gửi không thành công</br>

@apiParamExample    {json}  Body:
{
    "start_time": "Tên workflow",
    "end_time": "Mô tả workflow",
    "workflow_id": "163e0c28-b545-11ee-b485-38d57a786a3e",
    "node_id": "6a21de79-8884-48fd-bad6-0d78cad45386",
    "status": 'SUCCESS/FAIL'
}

@apiSuccess     {String}    id    ID định danh
@apiSuccess     {String}    input_object_name   Tên đối tượng đầu vào
@apiSuccess     {String}    input_object_code   Mã đối tượng đầu vào
@apiSuccess     {String}    email    Email gửi
@apiSuccess     {String}    staff_id    ID Nhân viên
@apiSuccess     {String}    push_time   Thời gian forward
@apiSuccess     {String}    open_status Trạng thái mở. Giá trị: <code>OPENED</code>=Đã mở<br/>
                                                                <code>NOT_OPEN</code>=Chưa mở<br/>
                                                                <code>OPEN_ERROR</code>=Lỗi không ghi nhận được mở<br/>
@apiSuccess     {String}    click_link_status Trạng thái mở. Giá trị: <code>CLICKED</code>=Đã bấm link<br/>
                                                                <code>NOT_CLICK</code>=Chưa bấm link<br/>
                                                                <code>CLICK_ERROR</code>=Lỗi không ghi nhận được bấm link<br/>
@apiSuccess     {String}    message_error   Lý do gửi không thành công
@apiSuccess     {Object}    encrypt_data   Thông tin sử dụng để giải mã

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "id": "c1ecae68-c0dd-49bc-953f-0018cff75493",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "email": "<EMAIL>",
            "staff_id": "d73db7ff-e109-4159-a6a0-8c3d0e1dc124",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Email bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "52f52e4b-0a5d-446a-8dfc-2912094ce34f",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "email": "<EMAIL>",
            "staff_id": "3026a75a-8512-498e-a5d7-54be16e64923",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Email bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "ed8cd1c9-89fd-48da-bb47-ba7d5899e43e",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "email": "<EMAIL>",
            "staff_id": "3bdf8abf-6883-4995-870f-d7357f879791",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Email bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        }
    ]
}
"""
**************************** Action Send Email Customer Detail ***************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /detail/node/action-send-email Báo cáo chi tiết khối gửi email khách hàng
@apiGroup WorkflowDetailReport
@apiVersion 1.0.0
@apiName ActionSendEmailDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [start_time] Thời gian bắt đầu lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>     
@apiParam   (Body:)     {String}    [end_time]  Thời gian kết thúc lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Body:)    {String}    workflow_id      Mã định danh workflow
@apiParam   (Body:)    {String}    node_id      Mã định danh khối
@apiParam   (Body:)    {String}    status      Trạng thái lấy báo cáo. Giá trị: <code>SUCCESS</code>=Gửi thành công</br>
                                                                                <code>FAIL</code>=Gửi không thành công</br>

@apiParamExample    {json}  Body:
{
    "start_time": "Tên workflow",
    "end_time": "Mô tả workflow",
    "workflow_id": "163e0c28-b545-11ee-b485-38d57a786a3e",
    "node_id": "6a21de79-8884-48fd-bad6-0d78cad45386",
    "status": 'SUCCESS/FAIL'
}

@apiSuccess     {String}    id    ID định danh
@apiSuccess     {String}    input_object_name   Tên đối tượng đầu vào
@apiSuccess     {String}    input_object_code   Mã đối tượng đầu vào
@apiSuccess     {String}    email    Email gửi
@apiSuccess     {String}    profile_id    ID Khách hàng
@apiSuccess     {String}    profile_name    Họ tên Khách hàng
@apiSuccess     {String}    profile_avatar    Ảnh đại diện Khách hàng
@apiSuccess     {String}    push_time   Thời gian gửi
@apiSuccess     {String}    open_status Trạng thái mở. Giá trị: <code>OPENED</code>=Đã mở<br/>
                                                                <code>NOT_OPEN</code>=Chưa mở<br/>
                                                                <code>OPEN_ERROR</code>=Lỗi không ghi nhận được mở<br/>
@apiSuccess     {String}    click_link_status Trạng thái mở. Giá trị: <code>CLICKED</code>=Đã bấm link<br/>
                                                                <code>NOT_CLICK</code>=Chưa bấm link<br/>
                                                                <code>CLICK_ERROR</code>=Lỗi không ghi nhận được bấm link<br/>
@apiSuccess     {String}    message_error   Lý do gửi không thành công
@apiSuccess     {Object}    encrypt_data   Thông tin sử dụng để giải mã

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "id": "c1ecae68-c0dd-49bc-953f-0018cff75493",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "email": "<EMAIL>",
            "profile_id": "d73db7ff-e109-4159-a6a0-8c3d0e1dc124",
            "profile_name": "Nguyen Van A",
            "profile_avatar": "https://s120-ava-talk.zadn.vn/d/f/6/8/30/120/f7d7aedb1d8b0c0da47ff3000ab80b6c.jpg",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Email bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "profile_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "email": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "52f52e4b-0a5d-446a-8dfc-2912094ce34f",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "email": "<EMAIL>",
            "profile_id": "3026a75a-8512-498e-a5d7-54be16e64923",
            "profile_name": "Nguyen Van A",
            "profile_avatar": "https://s120-ava-talk.zadn.vn/d/f/6/8/30/120/f7d7aedb1d8b0c0da47ff3000ab80b6c.jpg",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Email bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "profile_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "email": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "ed8cd1c9-89fd-48da-bb47-ba7d5899e43e",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "email": "<EMAIL>",
            "profile_id": "3bdf8abf-6883-4995-870f-d7357f879791",
            "profile_name": "Nguyen Van A",
            "profile_avatar": "https://s120-ava-talk.zadn.vn/d/f/6/8/30/120/f7d7aedb1d8b0c0da47ff3000ab80b6c.jpg",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Email bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "profile_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "email": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        }
    ]
}
"""
************************************ Action Send SMS *************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /detail/node/action-send-sms Báo cáo chi tiết khối gửi sms khách hàng
@apiGroup WorkflowDetailReport
@apiVersion 1.0.0
@apiName ActionSendSMSDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [start_time] Thời gian bắt đầu lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>     
@apiParam   (Body:)     {String}    [end_time]  Thời gian kết thúc lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Body:)    {String}    workflow_id      Mã định danh workflow
@apiParam   (Body:)    {String}    node_id      Mã định danh khối
@apiParam   (Body:)    {String}    status      Trạng thái lấy báo cáo. Giá trị: <code>SUCCESS</code>=Gửi thành công</br>
                                                                                <code>FAIL</code>=Gửi không thành công</br>

@apiParamExample    {json}  Body:
{
    "start_time": "Tên workflow",
    "end_time": "Mô tả workflow",
    "workflow_id": "163e0c28-b545-11ee-b485-38d57a786a3e",
    "node_id": "6a21de79-8884-48fd-bad6-0d78cad45386",
    "status": 'SUCCESS/FAIL'
}

@apiSuccess     {String}    id    ID định danh
@apiSuccess     {String}    input_object_name   Tên đối tượng đầu vào
@apiSuccess     {String}    input_object_code   Mã đối tượng đầu vào
@apiSuccess     {String}    phone    Số điện thoại nhận tin
@apiSuccess     {String}    profile_id    ID Khách hàng
@apiSuccess     {String}    profile_name    Họ tên Khách hàng
@apiSuccess     {String}    profile_avatar    Ảnh đại diện Khách hàng
@apiSuccess     {String}    push_time   Thời gian gửi
@apiSuccess     {String}    open_status Trạng thái mở. Giá trị: <code>OPENED</code>=Đã mở<br/>
                                                                <code>NOT_OPEN</code>=Chưa mở<br/>
                                                                <code>OPEN_ERROR</code>=Lỗi không ghi nhận được mở<br/>
@apiSuccess     {String}    click_link_status Trạng thái mở. Giá trị: <code>CLICKED</code>=Đã bấm link<br/>
                                                                <code>NOT_CLICK</code>=Chưa bấm link<br/>
                                                                <code>CLICK_ERROR</code>=Lỗi không ghi nhận được bấm link<br/>
@apiSuccess     {String}    message_error   Lý do gửi không thành công
@apiSuccess     {Object}    encrypt_data   Thông tin sử dụng để giải mã

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "id": "c1ecae68-c0dd-49bc-953f-0018cff75493",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "phone": "0987654321",
            "profile_id": "d73db7ff-e109-4159-a6a0-8c3d0e1dc124",
            "profile_name": "Nguyen Van A",
            "profile_avatar": "https://s120-ava-talk.zadn.vn/d/f/6/8/30/120/f7d7aedb1d8b0c0da47ff3000ab80b6c.jpg",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Số điện thoại bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "profile_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "phone": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "52f52e4b-0a5d-446a-8dfc-2912094ce34f",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "phone": "0987654321",
            "profile_id": "3026a75a-8512-498e-a5d7-54be16e64923",
            "profile_name": "Nguyen Van A",
            "profile_avatar": "https://s120-ava-talk.zadn.vn/d/f/6/8/30/120/f7d7aedb1d8b0c0da47ff3000ab80b6c.jpg",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Số điện thoại bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "profile_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "phone": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "ed8cd1c9-89fd-48da-bb47-ba7d5899e43e",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "phone": "0987654321",
            "profile_id": "3bdf8abf-6883-4995-870f-d7357f879791",
            "profile_name": "Nguyen Van A",
            "profile_avatar": "https://s120-ava-talk.zadn.vn/d/f/6/8/30/120/f7d7aedb1d8b0c0da47ff3000ab80b6c.jpg",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "message_error": "Số điện thoại bị chặn",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "profile_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "phone": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        }
    ]
}
"""
******************** Check Customer Notify Interaction Detail ****************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /detail/node/check-customer-notify-interaction Báo cáo chi tiết khối kiểm tra tương tác thông báo khách hàng
@apiGroup WorkflowDetailReport
@apiVersion 1.0.0
@apiName CheckCustomerNotifyInteractionDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [start_time] Thời gian bắt đầu lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>     
@apiParam   (Body:)     {String}    [end_time]  Thời gian kết thúc lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Body:)    {String}    workflow_id      Mã định danh workflow
@apiParam   (Body:)    {String}    node_id      Mã định danh khối

@apiParamExample    {json}  Body:
{
    "start_time": "Tên workflow",
    "end_time": "Mô tả workflow",
    "workflow_id": "163e0c28-b545-11ee-b485-38d57a786a3e",
    "node_id": "6a21de79-8884-48fd-bad6-0d78cad45386"
}

@apiSuccess     {String}    id    ID định danh
@apiSuccess     {String}    input_object_name   Tên đối tượng đầu vào
@apiSuccess     {String}    input_object_code   Mã đối tượng đầu vào
@apiSuccess     {String}    email    Email gửi (Trả ra nếu kiểm tra khối gửi email)
@apiSuccess     {String}    phone    Số điện thoại nhận tin (Trả ra nếu kiểm tra khối gửi sms)
@apiSuccess     {String}    profile_id    ID Khách hàng
@apiSuccess     {String}    profile_name    Họ tên Khách hàng
@apiSuccess     {String}    profile_avatar    Ảnh đại diện Khách hàng
@apiSuccess     {String}    push_time   Thời gian gửi
@apiSuccess     {String}    sent_status Trạng thái nhận. Giá trị: <code>SENT</code>=Đã nhận<br/>
                                                                <code>NOT_SENT</code>=Chưa nhận<br/>
                                                                <code>SENT_ERROR</code>=Lỗi không ghi nhận được đã nhận<br/>
@apiSuccess     {String}    open_status Trạng thái mở. Giá trị: <code>OPENED</code>=Đã mở<br/>
                                                                <code>NOT_OPEN</code>=Chưa mở<br/>
                                                                <code>OPEN_ERROR</code>=Lỗi không ghi nhận được mở<br/>
@apiSuccess     {String}    click_link_status Trạng thái mở. Giá trị: <code>CLICKED</code>=Đã bấm link<br/>
                                                                <code>NOT_CLICK</code>=Chưa bấm link<br/>
                                                                <code>CLICK_ERROR</code>=Lỗi không ghi nhận được bấm link<br/>
@apiSuccess     {Object}    encrypt_data   Thông tin sử dụng để giải mã

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "id": "c1ecae68-c0dd-49bc-953f-0018cff75493",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "phone": "0987654321",
            "profile_id": "d73db7ff-e109-4159-a6a0-8c3d0e1dc124",
            "profile_name": "Nguyen Van A",
            "profile_avatar": "https://s120-ava-talk.zadn.vn/d/f/6/8/30/120/f7d7aedb1d8b0c0da47ff3000ab80b6c.jpg",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "profile_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "phone": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "52f52e4b-0a5d-446a-8dfc-2912094ce34f",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "phone": "0987654321",
            "profile_id": "3026a75a-8512-498e-a5d7-54be16e64923",
            "profile_name": "Nguyen Van A",
            "profile_avatar": "https://s120-ava-talk.zadn.vn/d/f/6/8/30/120/f7d7aedb1d8b0c0da47ff3000ab80b6c.jpg",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "profile_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "phone": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "ed8cd1c9-89fd-48da-bb47-ba7d5899e43e",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "phone": "0987654321",
            "profile_id": "3bdf8abf-6883-4995-870f-d7357f879791",
            "profile_name": "Nguyen Van A",
            "profile_avatar": "https://s120-ava-talk.zadn.vn/d/f/6/8/30/120/f7d7aedb1d8b0c0da47ff3000ab80b6c.jpg",
            "push_time": "2024-01-17T09:00:00Z",
            "open_status": "OPENED",
            "click_link_status": "NOT_CLICK",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "profile_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                },
                "phone": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        }
    ]
}
"""
***************************** Send message fail rate each node ***************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /detail/send-message-fail-rate-each-node Báo cáo tỷ lệ gửi tin lỗi
@apiGroup WorkflowDetailReport
@apiVersion 1.0.0
@apiName SendMessageRateEachNode

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [start_time] Thời gian bắt đầu lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>     
@apiParam   (Body:)     {String}    [end_time]  Thời gian kết thúc lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Body:)    {String}    workflow_id      Mã định danh workflow
@apiParam   (Body:)    {String}    node_id      Mã định danh khối

@apiSuccess     {String}    message_error   Tên lỗi
@apiSuccess     {Number}    total_fails   Số lượt thực hiện thất bại

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "message_error": "Không hợp lệ",
            "total_fails": 268
        },
        {
            "message_error": "Khác",
            "total_fails": 100
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
***************************** Execute mission Detail *************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /detail/node/execute-mission Báo cáo chi tiết khối thực hiện hành động
@apiGroup WorkflowDetailReport
@apiVersion 1.0.0
@apiName ExecuteMissionDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [start_time] Thời gian bắt đầu lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>     
@apiParam   (Body:)     {String}    [end_time]  Thời gian kết thúc lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Body:)    {String}    workflow_id      Mã định danh workflow
@apiParam   (Body:)    {String}    node_id      Mã định danh khối
@apiParam   (Body:)    {String}    status      Trạng thái lấy báo cáo. Giá trị: <code>SUCCESS</code>=Thành công</br>
                                                                                <code>FAIL</code>=Không thành công</br>

@apiParamExample    {json}  Body:
{
    "start_time": "Tên workflow",
    "end_time": "Mô tả workflow",
    "workflow_id": "163e0c28-b545-11ee-b485-38d57a786a3e",
    "node_id": "6a21de79-8884-48fd-bad6-0d78cad45386",
    "status": "SUCCESS"
}

@apiSuccess     {String}    id    ID định danh
@apiSuccess     {String}    input_object_name   Tên đối tượng đầu vào
@apiSuccess     {String}    input_object_code   Mã đối tượng đầu vào
@apiSuccess     {String}    target_type   Đối tượng phát sinh
@apiSuccess     {String}    item_name   Tên bản ghi phát sinh hành động
@apiSuccess     {String}    execute_time Thời gian phát sinh
@apiSuccess     {String}    message_error   Lý do không thành công
@apiSuccess     {String}    mission_name Tên hành động
@apiSuccess     {String}    mission_type Đối tượng phát sinh hành động. <br/>Giá trị: <br/><code>RELATED_TO_ORDER</code>=Cơ hội bán<br/>
                                                                                    <code>RELATED_TO_TASK</code>=Công việc<br/>
                                                                                    <code>RELATED_TO_TICKET</code>=Ticket<br/>
@apiSuccess     {Object}    encrypt_data   Thông tin sử dụng để giải mã

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "id": "c1ecae68-c0dd-49bc-953f-0018cff75493",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "target_type": "OPPTY",
            "mission_name": "Tên hành động",
            "mission_type": "RELATED_TO_TASK",
            "item_name": "Bản ghi 1",
            "execute_time": "2024-01-17T09:00:00Z",
            "message_error": "Lỗi",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "c1ecae68-c0dd-49bc-953f-0018cff75493",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "target_type": "OPPTY",
            "mission_name": "Tên hành động",
            "mission_type": "RELATED_TO_TASK",
            "item_name": "Bản ghi 1",
            "execute_time": "2024-01-17T09:00:00Z",
            "message_error": "Lỗi",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        }
    ]
}
"""
***************************** Action Multi In One Detail *********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /detail/node/action-multi-in-one Báo cáo chi tiết khối tạo nhiệm vụ
@apiGroup WorkflowDetailReport
@apiVersion 1.0.0
@apiName ActionMultiInOneDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [start_time] Thời gian bắt đầu lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code>     
@apiParam   (Body:)     {String}    [end_time]  Thời gian kết thúc lấy báo cáo. Định dạng: <code>%Y-%m-%dT%H:%M:%S+00:00</code> 
@apiParam   (Body:)    {String}    workflow_id      Mã định danh workflow
@apiParam   (Body:)    {String}    node_id      Mã định danh khối
@apiParam   (Body:)    {String}    action_id      Mã định danh hành động
@apiParam   (Body:)    {String}    status      Trạng thái lấy báo cáo. Giá trị: <code>SUCCESS</code>=Thành công</br>
                                                                                <code>FAIL</code>=Không thành công</br>

@apiParamExample    {json}  Body:
{
    "start_time": "Tên workflow",
    "end_time": "Mô tả workflow",
    "workflow_id": "163e0c28-b545-11ee-b485-38d57a786a3e",
    "node_id": "6a21de79-8884-48fd-bad6-0d78cad45386",
    "action_id": "b62837a4-b15c-11ef-97e7-38d57a786a3d",
    "status": "SUCCESS"
}

@apiSuccess     {String}    id    ID định danh
@apiSuccess     {String}    input_object_name   Tên đối tượng đầu vào
@apiSuccess     {String}    input_object_code   Mã đối tượng đầu vào
@apiSuccess     {String}    action_type   Loại hành động
@apiSuccess     {String}    action_name   Tên hành động
@apiSuccess     {String}    node_name   Tên hành động
@apiSuccess     {String}    item_name   Tên bản ghi được tạo
@apiSuccess     {String}    item_code   Mã bản ghi được tạo
@apiSuccess     {String}    execute_time Thời gian phát sinh
@apiSuccess     {String}    message_error   Lý do không thành công
@apiSuccess     {Object}    encrypt_data   Thông tin sử dụng để giải mã

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "id": "c1ecae68-c0dd-49bc-953f-0018cff75493",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "action_type": "CREATE_TASK",
            "action_name": "Tạo task",
            "node_name": "Tên khối",
            "item_name": "Bản ghi 1",
            "item_code": "ABCD",
            "execute_time": "2024-01-17T09:00:00Z",
            "message_error": "Lỗi",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        },
        {
            "id": "c1ecae68-c0dd-49bc-953f-0018cff75493",
            "input_object_name": "Đối tượng 1",
            "input_object_code": "ABCD",
            "action_type": "CREATE_TASK",
            "action_name": "Tạo task",
            "node_name": "Tên khối",
            "item_name": "Bản ghi 1",
            "item_code": "ABCD",
            "execute_time": "2024-01-17T09:00:00Z",
            "message_error": "Lỗi",
            "encrypt_data": {
                "input_object_name": {
                    "value": "abcdefgh",
                    "hidden_icon_decrypt": false
                }
            }
        }
    ]
}
"""
"""
@apiDefine ResponseDetailInformationLatest

@apiSuccess {Dict}            data.info_cpu                    Thông tin CPU.
@apiSuccess {Dict}            data.info_disk                   Thông tin bộ nhớ
@apiSuccess {Dict}            data.info_memory                  Thông tin RAM

"""

# __________________________ Create information latest ______________________________________
"""
@api {POST} {HOST}/monitor/api/v1.0/information-latest Thêm thông tin hệ thống thu thập 
@apiDescription Fake dữ liệu resource thu đ<PERSON><PERSON><PERSON>, sử dụng cho test 
@apiGroup InformationLatest
@apiVersion 1.0.0
@apiName CreateInformationLatest

@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Body:)   {Dict}      info_cpu                    Thông tin CPU
@apiParam   (Body:)   {Dict}      info_disk                   Thông tin bộ nhớ
@apiParam   (Body:)   {Dict}      info_memory                  Thông tin RAM


@apiParamExample  {json}  Example:
{
    "info_cpu" : {
        "mongo1" : 87.*************,
        "cassandra1" : 87.364945652139,
        "cassandra2" : 87.3774456521946,
        "cassandra3" : 87.3862460433336,
        "elasticsearch2" : 87.475271739121,
        "elasticsearch3" : 87.*************,
        "kafka1" : 87.3744565217408,
        "kafka2" : 87.*************,
        "cassandra" : 87.*************,
        "elasticsearch1" : 87.472282608669,
        "kafka3" : 87.*************,
        "mongo2" : 87.3737771739382,
        "mongo3" : 87.3486413043255
    },
    "info_disk" : {
        "cassandra1" : 81.311580657959,
        "cassandra2" : 81.6833953857422,
        "cassandra3" : 81.5687141418457,
        "cassandra" : 37.3239479064941,
        "elasticsearch1" : 156.760303497314,
        "elasticsearch2" : 152.291709899902,
        "elasticsearch3" : 155.018032073975,
        "kafka1" : 198.806121826172,
        "kafka2" : 201.167205810547,
        "kafka3" : 201.141216278076,
        "mongo1" : 26.4087715148926,
        "mongo2" : 178.737400054932,
        "mongo3" : 164.484275817871
    },
    "info_memory" : {
        "cassandra1" : 58.5513441859922,
        "cassandra2" : 58.0329743027796,
        "cassandra3" : 57.8459437175139,
        "cassandra" : 58.4202096602262,
        "elasticsearch1" : 51.9584644702491,
        "elasticsearch2" : 49.2574973797459,
        "elasticsearch3" : 49.9724373368629,
        "kafka1" : 72.489407176872,
        "kafka2" : 74.2446821617394,
        "kafka3" : 66.7828350733596,
        "mongo1" : 24.749548700203,
        "mongo2" : 41.2402468415374,
        "mongo3" : 46
    }
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {},
    "lang": "vi",
    "message": "request thành công."
}
"""
# __________________________ Detail information latest ______________________________________
"""
@api {GET} {HOST}/monitor/api/v1.0/information-latest Lấy thông tin tài nguyên hệ thống
@apiDescription Lấy thông tin tài nguyên hệ thống
@apiGroup InformationLatest
@apiVersion 1.0.0
@apiName DetailInformationLatest

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse ResponseDetailInformationLatest

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "info_cpu" : {
            "mongo1" : 87.*************,
            "cassandra1" : 87.364945652139,
            "cassandra2" : 87.3774456521946,
            "cassandra3" : 87.3862460433336,
            "elasticsearch2" : 87.475271739121,
            "elasticsearch3" : 87.*************,
            "kafka1" : 87.3744565217408,
            "kafka2" : 87.*************,
            "cassandra" : 87.*************,
            "elasticsearch1" : 87.472282608669,
            "kafka3" : 87.*************,
            "mongo2" : 87.3737771739382,
            "mongo3" : 87.3486413043255
        },
        "info_disk" : {
            "cassandra1" : 81.311580657959,
            "cassandra2" : 81.6833953857422,
            "cassandra3" : 81.5687141418457,
            "cassandra" : 37.3239479064941,
            "elasticsearch1" : 156.760303497314,
            "elasticsearch2" : 152.291709899902,
            "elasticsearch3" : 155.018032073975,
            "kafka1" : 198.806121826172,
            "kafka2" : 201.167205810547,
            "kafka3" : 201.141216278076,
            "mongo1" : 26.4087715148926,
            "mongo2" : 178.737400054932,
            "mongo3" : 164.484275817871
        },
        "info_memory" : {
            "cassandra1" : 58.5513441859922,
            "cassandra2" : 58.0329743027796,
            "cassandra3" : 57.8459437175139,
            "cassandra" : 58.4202096602262,
            "elasticsearch1" : 51.9584644702491,
            "elasticsearch2" : 49.2574973797459,
            "elasticsearch3" : 49.9724373368629,
            "kafka1" : 72.489407176872,
            "kafka2" : 74.2446821617394,
            "kafka3" : 66.7828350733596,
            "mongo1" : 24.749548700203,
            "mongo2" : 41.2402468415374,
            "mongo3" : 46
        }
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

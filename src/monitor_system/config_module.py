"""
@apiDefine ResponseDetailConfigModule

@apiSuccess {String}            data.module_name                       <PERSON><PERSON><PERSON>nh của doanh nghiệp.
@apiSuccess {String}            data.info_node                       Thông tin cấu hình của mỗi node
@apiSuccess {String}            data.info_node.type                  Kiểu của node
@apiSuccess {String}            data.info_node.cpu_limit             Ngưỡng giới hạn của CPU
@apiSuccess {String}            data.info_node.memory_limit          Ngưỡng giới hạn của RAM
@apiSuccess {String}            data.info_node.disk_limit             Ngưỡng giới hạn của bộ nhớ

"""

# __________________________ Create config module ______________________________________
"""
@api {POST} {HOST}/monitor/api/v1.0/modules T<PERSON><PERSON> cấ<PERSON> hình
@apiDescription Tạo cấu hình
@apiGroup ConfigModule
@apiVersion 1.0.0
@apiName CreateConfigModule

@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Body:)   {String}      module_name                    Tên module
@apiParam   (Body:)   {String}      info_node                       Thông tin cấu hình của mỗi node
@apiParam   (Body:)   {String}      info_node.type                  Kiểu của node
@apiParam   (Body:)   {String}      info_node.cpu_limit             Ngưỡng giới hạn của CPU
@apiParam   (Body:)   {String}      info_node.memory_limit          Ngưỡng giới hạn của RAM
@apiParam   (Body:)   {String}      info_node.disk_limit             Ngưỡng giới hạn của bộ nhớ


@apiParamExample  {json}  Example:
{
    "module_name": "journey_builder",
    "info_node": {
        "mongo1": {
            "type": "arbiter",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "mongo2": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "mongo3": {
            "type": "secondary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "kafka1": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "kafka2": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "kafka3": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "cassandra": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "mysql": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "elasticsearch1": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "elasticsearch2": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "elasticsearch3": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        }
    }
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {},
    "lang": "vi",
    "message": "request thành công."
}
"""
# __________________________ Update config module ______________________________________
"""
@api {PATCH} {HOST}/monitor/api/v1.0/modules/<config_module_id> Cập nhật thông tin  cấu hình
@apiDescription Cập nhật thông tin  cấu hình
@apiGroup ConfigModule
@apiVersion 1.0.0
@apiName UpdateConfigModule

@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Body:)   {String}      module_name                    Tên module
@apiParam   (Body:)   {String}      info_node                       Thông tin cấu hình của mỗi node
@apiParam   (Body:)   {String}      info_node.type                  Kiểu của node
@apiParam   (Body:)   {String}      info_node.cpu_limit             Ngưỡng giới hạn của CPU
@apiParam   (Body:)   {String}      info_node.memory_limit          Ngưỡng giới hạn của RAM
@apiParam   (Body:)   {String}      info_node.disk_limit             Ngưỡng giới hạn của bộ nhớ


@apiParamExample  {json}  Example:
{
    "module_name": "journey_builder",
    "info_node": {
        "mongo1": {
            "type": "arbiter",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "mongo2": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "mongo3": {
            "type": "secondary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "kafka1": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "kafka2": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "kafka3": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "cassandra": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "mysql": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "elasticsearch1": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "elasticsearch2": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "elasticsearch3": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        }
    }
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {},
    "lang": "vi",
    "message": "request thành công."
}
"""
# __________________________ Delete config module ______________________________________
"""
@api {DELETE} {HOST}/monitor/api/v1.0/modules Xóa cấu hình
@apiDescription Xóa cấu hình
@apiGroup ConfigModule
@apiVersion 1.0.0
@apiName DeleteConfigModule

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}      ids                    Danh sách id cấu hình cần xóa

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# __________________________ List config module ______________________________________
"""
@api {GET} {HOST}/monitor/api/v1.0/modules Lấy danh sách cấu hình
@apiDescription Lấy danh sách cấu hình
@apiGroup ConfigModule
@apiVersion 1.0.0
@apiName GetListConfigModule

@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiUse ResponseDetailConfigModule

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
    "code": 200,
    "data": [
        {
            "module_name": "journey_builder",
            "info_node": {
                "mongo1": {
                    "type": "arbiter",
                    "cpu_limit": 95,
                    "memory_limit": 95,
                    "disk_limit": 5
                },
                "mongo2": {
                    "type": "primary",
                    "cpu_limit": 95,
                    "memory_limit": 95,
                    "disk_limit": 5
                },
                "mongo3": {
                    "type": "secondary",
                    "cpu_limit": 95,
                    "memory_limit": 95,
                    "disk_limit": 5
                },
                "kafka1": {
                    "type": "primary",
                    "cpu_limit": 95,
                    "memory_limit": 95,
                    "disk_limit": 5
                },
                "kafka2": {
                    "type": "primary",
                    "cpu_limit": 95,
                    "memory_limit": 95,
                    "disk_limit": 5
                },
                "kafka3": {
                    "type": "primary",
                    "cpu_limit": 95,
                    "memory_limit": 95,
                    "disk_limit": 5
                },
                "cassandra": {
                    "type": "primary",
                    "cpu_limit": 95,
                    "memory_limit": 95,
                    "disk_limit": 5
                },
                "mysql": {
                    "type": "primary",
                    "cpu_limit": 95,
                    "memory_limit": 95,
                    "disk_limit": 5
                },
                "elasticsearch1": {
                    "type": "primary",
                    "cpu_limit": 95,
                    "memory_limit": 95,
                    "disk_limit": 5
                },
                "elasticsearch2": {
                    "type": "primary",
                    "cpu_limit": 95,
                    "memory_limit": 95,
                    "disk_limit": 5
                },
                "elasticsearch3": {
                    "type": "primary",
                    "cpu_limit": 95,
                    "memory_limit": 95,
                    "disk_limit": 5
                }
            }
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}
"""
# __________________________ Detail config module ______________________________________
"""
@api {GET} {HOST}/monitor/api/v1.0/modules/<config_module_id> Lấy chi tiết cấu hình
@apiDescription Lấy chi tiết cấu hình
@apiGroup ConfigModule
@apiVersion 1.0.0
@apiName DetailMerchant

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse ResponseDetailConfigModule

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
    "module_name": "journey_builder",
    "info_node": {
        "mongo1": {
            "type": "arbiter",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "mongo2": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "mongo3": {
            "type": "secondary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "kafka1": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "kafka2": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "kafka3": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "cassandra": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "mysql": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "elasticsearch1": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "elasticsearch2": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        },
        "elasticsearch3": {
            "type": "primary",
            "cpu_limit": 95,
            "memory_limit": 95,
            "disk_limit": 5
        }
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

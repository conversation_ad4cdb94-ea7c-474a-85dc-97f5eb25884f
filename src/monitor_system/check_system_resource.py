"""
@apiDefine ResponseDetailInformationLatest

@apiSuccess {Dict}            data.info_cpu                    Thông tin CPU.
@apiSuccess {Dict}            data.info_disk                   Thông tin bộ nhớ
@apiSuccess {Dict}            data.info_memory                  Thông tin RAM

"""

# __________________________ Create information latest ______________________________________
"""
@api {POST} {HOST}/monitor/api/v1.0/check-system-resources Thêm thông tin hệ thống thu thập 
@apiDescription Fake dữ liệu resource thu đ<PERSON><PERSON><PERSON>, sử dụng cho test 
@apiGroup CheckSystemResources
@apiVersion 1.0.0
@apiName CheckSystemResources

@apiUse json_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Body:)   {String}      module_name                    Tên module cần kiểm tra
@apiParam   (Body:)   {List}      type_need_check                  Ki<PERSON><PERSON> của node, nhận giá trị <code>primary, secondary, arbiter</code>


@apiParamExample  {json}  Example:
{
    "module_name": "journey_builder",
    "type_need_check": ["primary"]
}


@apiSuccess {Bool}            data.is_healthy                   Hệ thống có đang ổn định không, nhận giá trị <code>, true, false</code>.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "is_healthy": true
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

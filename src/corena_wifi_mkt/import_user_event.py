# -*- coding: utf-8 -*-
""" Author: TruongNV
    Company: MobioVN
    Created Date: 03/10/2018
    Editor:
    Updated Date:
"""
"""
@apiDefine 401
@apiVersion 1.0.0
@apiHeader (Headers:) {String} Authorization Token/api-key để sử dụng api. Nếu typically là:
<li><code>Basic</code> thì Authenticate là API-Key</li>
@apiHeaderExample Basic Token Example:
{
    "Authorization":"Basic 25630732-2976-444a-ba7a-3ca1b48fcc3a"
}
@apiError (Error 4xx) 401-Unauthorized token/api-key hết hạn hoặc sai định dạng. <PERSON><PERSON><PERSON> cầu login lại.
<li><code>code:</code> 401</li>
<li><code>message:</code> <PERSON><PERSON> tả lỗi.</li>
<br/>
@apiErrorExample    {json}  HTTP/1.1 401
HTTP/1.1 401 Unauthorized
{
    "code": 401,
    "message":"Token is invalid or is expired. Please login again."
}
"""


######################## Import From CORENA Captive Wifi_MKT Data ########################
# version: 1.0.0                                                                         #
##########################################################################################
"""
@api {post} https://dev.mobio.vn/profiling/v2.0/imports/wifi-data/non_smart Import From CORENA Captive Wifi_MKT
@apiDescription API import dữ liệu từ hệ thống CORENA-Captive-Wifi_MKT-System vào MOBIO-Profiling
@apiGroup Profiling
@apiVersion 1.0.0
@apiName ImportCorenaCaptiveWifiMKTData

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Body:)     {Array}    event       Loại data gửi . <br/>Allowed values: <br/>
<li><code>"Captive-Proximity-Event":</code> Dữ liệu được lấy khi khách hàng đến gần một AccessPoint nào đó</li>
<li><code>"Captive-Location-Event":</code> Dữ liệu tổng hợp các thiết bị trong một zone</li>
@apiParam   (Body:)     {Array}     data        Dữ liệu raw_data từ wifi marketing system. 

@apiParam   (data:)     {String}    mbo_business    Code nhận diện business - do Mobio định nghĩa. <br/>Allowed values: <br/>
<li><code>"SMARTWF_BANAHILL_0807: BaNa Hill </code></li>
@apiParam   (data:)     {String}    tentinh         Tên tỉnh của 
@apiParam   (data:)     {String}    matinh          Mã tỉnh của site
@apiParam   (data:)     {String}    diachi          Địa chỉ của site
@apiParam   (data:)     {Array}     calibrate       Chứa các thông tin setup của đại điểm nhận thông tin từ client 
@apiParam   (data:)     {Array}     observations    Chứa thông tin quan sát tổng hợp

@apiParam   (calibrate:)    {Int}       session_timeout      Số giây timeout, nếu khách hàng rời khỏi phạm vi AccessPoint vượt quá số giây ấy thì session của khách hàng ấy sẽ kết thúc
@apiParam   (calibrate:)    {Int}       timezone             Timezone của partner (in minutes), thời gian lệch giữa địa điểm thu thập thông tin của client với giờ UDT (theo phút)

@apiParam   (observations:)    {String}     ap_mac          MAC của Access Point
@apiParam   (observations:)    {String}     ap_name         Tên của Access Point
@apiParam   (observations:)    {Array}      ap_tag          Tập các tag của Access Point
@apiParam   (observations:)    {Array}      clients         Danh sách các thiết bị của khách  trong vùng lấy được
@apiParam   (observations:)    {String}     zone_id         UUID của Zone - định nghĩa bởi Corena
@apiParam   (observations:)    {String}     zone_name       Tên của zone wifi
@apiParam   (observations:)    {Array}      zone_tag        Tập các tag của Zone

@apiParam   (clients:)    {String}          mac             Địa chỉ MAC của thiết bị của khách hàng
@apiParam   (clients:)    {String}          phone           Số máy của khách hàng
@apiParam   (clients:)    {TimeStamp}       first_seen      Thời điểm đầu tiên lấy được kết nối của máy khách hàng - giờ UTC
@apiParam   (clients:)    {TimeStamp}       last_seen       Thời điểm cuối cùng lấy được kết nối của máy khách hàng - giờ UTC

@apiParamExample {json} Body example
{
  "data": [
    {
      "tentinh": "Hà Nội",
      "mbo_business": "SMARTWF_PXP_0807",
      "observations": [
        {
          "zone_id": "602fcc10-b724-11e8-b00b-0242ac120017",
          "zone_tag": {},
          "zone_name": "MOBIO",
          "clients": [
            {
              "phone": "0904123404",
              "mac": "1002b5bd8986",
              "first_seen": 1537758911,
              "last_seen": 1537759184
            },
            {
              "phone": "0904123402",
              "mac": "0024d7ddf4c8",
              "first_seen": 1537758919,
              "last_seen": 1537759187
            }
          ]
        },
        {
          "zone_id": "45435fac-b724-11e8-8e87-0242ac120017",
          "zone_tag": {},
          "zone_name": "Phòng họp",
          "clients": [
            {
              "phone": "0904123407",
              "mac": "1c3e842f1551",
              "first_seen": 1537758980,
              "last_seen": 1537759131
            }
          ]
        }
      ],
      "diachi": "Dịch Vọng, Cầu Giấy",
      "matinh": "ha_noi",
      "calibrate": {
        "timezone": 420,
        "session_timeout": 420
      }
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
}
"""

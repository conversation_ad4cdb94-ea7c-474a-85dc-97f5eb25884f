# ---- Search từ kho dữ liệu công ty   ----
"""
@api {POST} /companycrawler/api/v1.0/company/search Search từ kho dữ liệu công ty
@apiDescription Search từ kho dữ liệu công ty
@apiGroup CompanyWarehouse
@apiVersion 1.0.0
@apiName SearchCompanyWarehouse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)    {Number}    [per_page] Số phần tử trên một page.<br/> Example: <code>&per_page=5</code>
@apiParam   (Query:)    {String}    [after_token] Token để request lấy dữ liệu trang tiếp theo.

@apiParam (Body:) {String}     search                       Chuỗi cần search theo tên và website

@apiParamExample {json} Body example
{
    "search": "xuân mai",
}
@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "_id": "66c6f33264181415e3b512ff",
            "abbreviated_name": "",
            "active_time": "2018-04-11T00:00:00.000+00:00",
            "address": "ấp An Bình, Xã An Cư, Huyện Cái Bè, Tiền Giang",
            "business_sector": "Bán buôn kim loại và quặng kim loại. Hoạt động cấp tín dụng khác. Sản xuất đồ kim hoàn và chi tiết liên quan.",
            "business_type": "",
            "chartered_capital": "",
            "email": "",
            "foreign_name": "",
            "legal_representative": "Phạm Quốc Tiến",
            "license_time": "2018-04-11T00:00:00.000+00:00",
            "name": "CÔNG TY TNHH MTV TIỆM VÀNG KIM MAI",
            "phone_number": "",
            "status": "active",
            "tax_identification_number": "**********",
            "website": ""
        },
        {
            "_id": "66c6f33264181415e3b512ff",
            "abbreviated_name": "",
            "active_time": "2018-04-11T00:00:00.000+00:00",
            "address": "ấp An Bình, Xã An Cư, Huyện Cái Bè, Tiền Giang",
            "business_sector": "Bán buôn kim loại và quặng kim loại. Hoạt động cấp tín dụng khác. Sản xuất đồ kim hoàn và chi tiết liên quan.",
            "business_type": "",
            "chartered_capital": "",
            "email": "",
            "foreign_name": "",
            "legal_representative": "Phạm Quốc Tiến",
            "license_time": "2018-04-11T00:00:00.000+00:00",
            "name": "CÔNG TY TNHH MTV TIỆM VÀNG KIM MAI",
            "phone_number": "",
            "status": "active",
            "tax_identification_number": "**********",
            "website": ""
        }
    ],
    "lang": "vi",
    "message": "Request successfully",
    "paging": {
        "cursors": {
            "after": "WyJ2dSB2YSBkYW5nIHRhcCBkb2FuIiwgImh0dHBzOi8vd3d3LnZ1LmNvbS8iXQ==",
            "before": ""
        },
        "per_page": 11,
        "total_count": 17082
    }
}

"""


# ---- Search từ kho dữ liệu công ty   ----
"""
@api {POST} /companycrawler/api/v1.0/company/insert-proxy Insert proxy vào Database
@apiDescription Insert proxy vào Database
@apiGroup CompanyWarehouse
@apiVersion 1.0.0
@apiName InsertProxy

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)    {Array}    proxies Danh sách proxy
@apiParam   (Body:)    {String}    type Kiểu cập nhập dữ liệu có thể là <code>replace</code> hoặc <code>append</code>


@apiParamExample {json} Body example
{
    "proxies": [
        "VN753089:tJqH5l5k@**************:22221",
        "VN753019:tJqH5l5k@**************:22222",
    ],
    "type": "replace"
}
@apiSuccessExample {json} Response
{
    "status": "SUCCESS",
    "message": "Proxies import successfully"
}

"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 28/03/2024
"""
# ---------- Cập nhật địa chỉ ip được whitelist -----------
"""
@api {POST} {domain}/market-place/api/v1.0/object-handled/action/upsert              Upsert thông tin của các đối tượng được hỗ trợ xử lý
@apiGroup GeneralConfigurationParameters
@apiVersion 1.0.0
@apiName UpsertConfigurationObjectHanded

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiParam   (BODY:) {array}       objects                                           Danh sách dối tượng hỗ trợ
                                                                                    <ul>
                                                                                        <li>profile: <PERSON><PERSON><PERSON> tượng Profiles</li>
                                                                                        <li>company: <PERSON><PERSON><PERSON> tượng <PERSON>ng ty</li>
                                                                                        <li>ticket: Đối tượng Ticket</li>
                                                                                        <li>sale: Đối tượng Sale</li>
                                                                                    </ul>
@apiParam   (BODY:) {array}       ignore_objects                                   Danh sách dối tượng không hỗ trợ

@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

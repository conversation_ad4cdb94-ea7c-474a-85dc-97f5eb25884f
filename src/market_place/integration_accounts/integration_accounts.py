#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 11/09/2024
"""
"""
@api {GET} {domain}/market-place/api/v1.0/integration-accounts              L<PERSON>y danh sách tài khoản tích hợp
@apiDescription L<PERSON>y danh sách tài khoản tích hợp dựa theo kiểu
@apiGroup InteractionAccounts
@apiVersion 1.0.0
@apiName ListInteractionAccounts

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging_tokens

@apiParam   (Query:) {string}           [name_search]                       Từ khoá tìm kiếm app theo tên
                                                                            <li>không phân biệt hoa thường, có dấu hoặc không dấu, khoảng trắng giữa các ký tự</li>
@apiParam   (Query:) {string}           [selected_field]                    Field lựa chọn để trả về, nhiều giá trị cách nhau dấu "," (VD: id,name,created_by)
@apiParam   (Query:) {string}           [order_by]                          Sắp xếp theo field nào (Chỉ hỗ trợ sort theo 1 filed). Nếu không truyền gì thì mặc định sắp xếp theo: <code>created_time</code>
@apiParam   (Query:) {string}           [order_type]                        Thứ tự cần sắp xếp
                                                                            <li>Nhận giá trị <code>desc</code>, <code>asc</code></li>
                                                                            <li><code>asc</code>: tăng dần</li>
                                                                            <li><code>desc</code>: giảm dần</li>
                                                                            <li>Mặc định <code>desc</code></li>
@apiParam   (Query:) {string}           [type]                              Kiểu tài khoản tích hợp
                                                                            <li>google_sheet: Kiểu google sheet</li>
                                                                            <code>Trong trường hợp không truyền thì sẽ lấy tất cả.</code>

@apiSuccess     {Array}             data                                    Danh sách app được config
@apiSuccess     {String}            data.id                                 ID tài khoản tích hợp 
@apiSuccess     {String}            data.name                               Tên tài khoản tích hợp
@apiSuccess     {String}            data.type                               Loại kết nối của tài khoản tích hợp
                                                                            <ul>        
                                                                                <li>google_sheet: Google sheet</li>
                                                                            </ul>
@apiSuccess     {object}            data.config                             Cấu hình thông tài của tài khoản tích hợp
                                                                            <li>Type: <code>google_sheet</code>: {
                                                                                "email": "<EMAIL>",
                                                                                "id": "650aae6f3337edbd600b63df",
                                                                                "user_id": "116186621554215909442"
                                                                            } </li>
@apiSuccess     {string}            data.created_by                         Thông tin nhân viên tạo
@apiSuccess     {string}            data.updated_by                         Thông tin nhân viên cập nhật
@apiSuccess     {string}            data.created_time                       Thời gian tạo
@apiSuccess     {string}            data.updated_time                       Thời gian cập nhật

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "id": "60ffb75601c97db90579ae17",
            "name": "Tài khoản tích hợp 1",
            "type": "google_sheet",
            "config": {
                "email": "<EMAIL>",
                "id": "650aae6f3337edbd600b63df",
                "user_id": "116186621554215909442"
            },
            "created_by": "admin",
            "updated_by": "admin",
            "created_time": "2022-05-17T07:35:56Z",
            "updated_time": "2022-05-17T07:35:56Z",
        }
    ],
    "paging": ... Xem bên tab paging,
    "message": "request thành công."
}
"""
"""
@api {GET} {domain}/market-place/api/v1.0/integration-accounts/information/connect-app              Lấy thông tin kết nối đến APP Google
@apiDescription     Lấy thông tin kết nối đến APP Google
@apiGroup InteractionAccounts
@apiVersion 1.0.0
@apiName GetInformationConnectAppGoogle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:) {string}           page_current_url                    Link trang hiện tại đang đứng của user
@apiParam   (Query:) {string}           type                                Loại kết nối của tài khoản tích hợp
                                                                            <ul>
                                                                                <li>google_sheet: Google sheet</li>
                                                                            </ul>

@apiSuccess     {Object}            data                                    Dữ liệu trả về
@apiSuccess     {String}            data.link                               Link redirect


@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "link": "https://accounts.google.com/o/oauth2/auth?client_id=********************.apps.googleusercontent.com&response_type=code&redirect_uri=https://mobio.io/auth/google/callback&scope=https://www.googleapis.com/auth/userinfo.profile+https://www.googleapis.com/auth/userinfo.email&state=**********"
    },
}
"""
"""
@api {DELETE} {domain}/market-place/api/v1.0/integration-accounts/<integration_account_id>              Xoá tài khoản tích hợp
@apiDescription Xoá tài khoản tích hợp
@apiGroup InteractionAccounts
@apiVersion 1.0.0
@apiName DeleteInteractionAccounts

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "request thành công."
}
"""
"""
@api {GET} {domain}/market-place/api/v1.0/integration-accounts/<integration_account_id>              Chi tiết tài khoản kết nối
@apiDescription Chi tiết tài khoản kết nối
@apiGroup InteractionAccounts
@apiVersion 1.0.0
@apiName DetailInteractionAccounts

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess     {Object}             data                                    Danh sách app được config
@apiSuccess     {String}            data.id                                 ID tài khoản tích hợp 
@apiSuccess     {String}            data.name                               Tên tài khoản tích hợp
@apiSuccess     {String}            data.type                               Loại kết nối của tài khoản tích hợp
                                                                            <ul>        
                                                                                <li>google_sheet: Google sheet</li>
                                                                            </ul>
@apiSuccess     {object}            data.config                             Cấu hình thông tài của tài khoản tích hợp
                                                                            <li>Type: <code>google_sheet</code>: {
                                                                                "email": "<EMAIL>",
                                                                                "id": "650aae6f3337edbd600b63df",
                                                                                "user_id": "116186621554215909442"
                                                                            } </li>
@apiSuccess     {string}            data.created_by                         Thông tin nhân viên tạo
@apiSuccess     {string}            data.updated_by                         Thông tin nhân viên cập nhật
@apiSuccess     {string}            data.created_time                       Thời gian tạo
@apiSuccess     {string}            data.updated_time                       Thời gian cập nhật

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "60ffb75601c97db90579ae17",
        "name": "Tài khoản tích hợp 1",
        "type": "google_sheet",
        "config": {
            "email": "<EMAIL>",
            "id": "650aae6f3337edbd600b63df",
            "user_id": "116186621554215909442"
        },
        "created_by": "admin",
        "updated_by": "admin",
        "created_time": "2022-05-17T07:35:56Z",
        "updated_time": "2022-05-17T07:35:56Z",
    }
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 11/09/2024
"""

"""
@api {GET} {domain}/market-place/api/v1.0/integration-accounts/google-sheet/folder-default              L<PERSON>y thông tin folder mặc định khi liên kết tài khoản gg_sheet
@apiGroup InteractionAccountsGoogleSheet
@apiVersion 1.0.0
@apiName InteractionAccountsGoogleSheetFolderDefault

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:) {string}           account_id                        <code>ID</code> của tài khoản tích hợp

@apiSuccess     {Object}            data                                    Dữ liệu được trả về
@apiSuccess     {String}            data.id                                 ID của folder
@apiSuccess     {String}            data.name                               Tên folder

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "id": "**********",
        "name": "Folder mặc định"
    },
    "message": "request thành công."
}
"""

"""
@api {GET} {domain}/market-place/api/v1.0/integration-accounts/google-sheet/spreadsheets              Danh sách bảng tính
@apiGroup InteractionAccountsGoogleSheet
@apiVersion 1.0.0
@apiName InteractionAccountsGoogleSheetSpreadsheets

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging_tokens


@apiParam   (Query:) {string}           account_id                          <code>ID</code> của tài khoản tích hợp

@apiSuccess     {Array}             data                                    Dữ liệu được trả về
@apiSuccess     {String}            data.id                                 ID của của bảng tính
@apiSuccess     {String}            data.name                               Tên của bảng tính

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "id": "1PkZCr7hRPp8klY5HrhdF4Su4oktjCMqq-AO1KZqM2SY",
            "kind": "drive#file",
            "mimeType": "application/vnd.google-apps.spreadsheet",
            "name": "Tên bảng tính mới 11"
        }
    ],
    "message": "request thành công.",
    "paging": ... Xem bên tab paging
}
"""

"""
@api {GET} {domain}/market-place/api/v1.0/integration-accounts/google-sheet/spreadsheets/<spreadsheet_id>/sheets        Lấy danh sách trang tính của bảng tính
@apiGroup  InteractionAccountsGoogleSheet
@apiVersion 1.0.0
@apiName  InteractionAccountsGoogleSheetSpreadsheetsListSheet

@apiParam   (Query:)     {String}              account_id             <code></code>

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiSuccess     {Array}             data                                    Dữ liệu được trả về
@apiSuccess     {String}            data.id                                 ID trang tính
@apiSuccess     {String}            data.name                               Tên của trang tính

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
      {
          "id": "1PkZCr7hRPp8klY5HrhdF4Su4oktjCMqq-AO1KZqM2SY",
          "name": "Sheet 1",
      }
  ]
}
"""
"""
@api {POST} {domain}/market-place/api/v1.0/integration-accounts/google-sheet/spreadsheets/<spreadsheet_id>/sheets        Tạo trang tính trong bảng tính mới
@apiDescription Tạo trang tính trong bảng tính mới chỉ áp dụng trong trường hợp tạo bảng tính mới. <code>Không áp dụng trong trường hợp sử dụng bảng tính có sẵn</code>
@apiGroup  InteractionAccountsGoogleSheet
@apiVersion 1.0.0
@apiName  InteractionAccountsGoogleSheetSpreadsheetsAddSheet

@apiParam   (Query:)     {String}              account_id             <code>ID</code> tài khoản tích hợp

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam (BODY:)       {string}                        name                                                  Tên trang tính

@apiParamExample  {json}  Body example:
{
    "name": "Sheet 123",
}

@apiSuccess     {Object}             data                                    Dữ liệu được trả về
@apiSuccess     {String}            data.id                                 ID trang tính
@apiSuccess     {String}            data.name                               Tên của trang tính

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
        "id": "1PkZCr7hRPp8klY5HrhdF4Su4oktjCMqq-AO1KZqM2SY",
        "name": "Sheet 1",
    }
}
"""
"""
@api {POST} {domain}/market-place/api/v1.0/integration-accounts/google-sheet/spreadsheets/<spreadsheet_id>/sheets/<sheet_id>        Chi tiết trang tính
@apiDescription Chi tiết trang tính
@apiGroup  InteractionAccountsGoogleSheet
@apiVersion 1.0.0
@apiName  DetailInteractionAccountsGoogleSheetSpreadsheetsSheet

@apiParam   (Query:)     {String}              account_id             <code>ID</code> tài khoản tích hợp

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header


@apiSuccess     {Object}             data                                    Dữ liệu được trả về
@apiSuccess     {String}            data.id                                 ID trang tính
@apiSuccess     {String}            data.name                               Tên của trang tính

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
        "id": "1PkZCr7hRPp8klY5HrhdF4Su4oktjCMqq-AO1KZqM2SY",
        "name": "Sheet 1",
    }
}
"""

"""
@api {POST} {domain}/market-place/api/v1.0/integration-accounts/google-sheet/spreadsheets       Tạo bảng tính
@apiDescription             Api tạo bảng tính
@apiGroup  InteractionAccountsGoogleSheet
@apiVersion 1.0.0
@apiName InteractionAccountsGoogleAddSheetSpreadsheets

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              account_id             <code>ID</code> tài khoản tích hợp

@apiParam   (BODY:)       {string}                        name                                                  Tên bảng tính

@apiParamExample  {json}  Body example:
{
    "name": "Sheet 123",
}

@apiSuccess     {Object}             data                                   Dữ liệu được trả về
@apiSuccess     {String}            data.id                                 ID bảng tính
@apiSuccess     {String}            data.name                               Tên của bảng tính

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "1PkZCr7hRPp8klY5HrhdF4Su4oktjCMqq-AO1KZqM2SY",
        "name": "Sheet 123",
    }
}
"""
"""
@api {GET} {domain}/market-place/api/v1.0/integration-accounts/google-sheet/spreadsheets/<spreadsheet_id>       Chi tiết bảng tính
@apiDescription             Chi tiết bảng tính
@apiGroup  InteractionAccountsGoogleSheet
@apiVersion 1.0.0
@apiName DetailInteractionAccountsGoogleSpreadsheets

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              account_id             <code>ID</code> tài khoản tích hợp

@apiSuccess     {Object}             data                                   Dữ liệu được trả về
@apiSuccess     {String}            data.id                                 ID bảng tính
@apiSuccess     {String}            data.name                               Tên của bảng tính

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "1PkZCr7hRPp8klY5HrhdF4Su4oktjCMqq-AO1KZqM2SY",
        "name": "Sheet 123",
    }
}
"""

"""
@api {POST} {domain}/market-place/api/v1.0/integration-accounts/google-sheet/spreadsheets       Tạo bảng tính
@apiDescription             Api tạo bảng tính
@apiGroup  InteractionAccountsGoogleSheet
@apiVersion 1.0.0
@apiName InteractionAccountsGoogleAddSheetSpreadsheets

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              account_id             <code>ID</code> tài khoản tích hợp

@apiParam   (BODY:)       {string}                        name                                                  Tên bảng tính

@apiParamExample  {json}  Body example:
{
    "name": "Sheet 123",
}

@apiSuccess     {Object}             data                                   Dữ liệu được trả về
@apiSuccess     {String}            data.id                                 ID bảng tính
@apiSuccess     {String}            data.name                               Tên của bảng tính

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "1PkZCr7hRPp8klY5HrhdF4Su4oktjCMqq-AO1KZqM2SY",
        "name": "Sheet 123",
    }
}
"""
# ----------------------- Get first row of sheet -----------------------

"""
@api {GET} market-place/api/v1.0/integration-accounts/google-sheet/spreadsheets/<spreadsheet_id>/sheets/<sheet_id>/columns  Lấy danh sách các cột của Sheet
@apiDescription  Lấy dòng đầu tiên trong sheet
@apiGroup  InteractionAccountsGoogleSheet
@apiVersion 1.0.0
@apiName  InteractionAccountsGoogleSheetColumns

@apiParam   (Query:)     {String}              account_id             <code>ID</code> tài khoản tích hợp

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "range": "A1",
            "title": "Tên",
        },
        {
            "range": "B1",
            "title": "Địa chỉ",
        },
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
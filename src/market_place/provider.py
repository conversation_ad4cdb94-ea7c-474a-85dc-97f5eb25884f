************************ get link nhà cung cấp ********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {GET} {domain}/market-place/api/v1.0/providers         Danh sách nhà cung cấp
@apiDescription API danh sách link của nhà cung cấp.
@apiGroup Provider
@apiVersion 1.0.0
@apiName GetProviderLink

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Params)   {String}  service  Dịch vụ của nhà cung cấp, vd "email", "sms".

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Array}                  data                          Danh sách nhà cung cấp.
@apiSuccess {String}            data.id                   Id của link nhà cung cấp.
@apiSuccess {String}            data.provider                   Tên của nhà cung cấp.
@apiSuccess {Array}            data.type                  Loại cấu hình dịch vụ của nhà cung cấp.
@apiSuccess {String}            data.provider_link_image         Link image của nhà cung cấp.
@apiSuccess {Integer}            data.provider_type        Mã code nhà cung cấp (do bên mobio đinh nghĩa).
@apiSuccess {Array}            data.connect_config_info    Danh sách cấu hình kết nối của nhà cung cấp.
@apiSuccess {String}            data.connect_config_info.key   Key cấu hình kết nối.
@apiSuccess {String}            data.connect_config_info.key_name   Tên key cấu hình kết nối có vi-en.
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {Datetime}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật.
@apiSuccess {Datetime}            data.updated_time                       Thời điểm cập nhật.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
        "connect_config_info": [
            {
                "key": "username",
                "key_name": "Tên đăng nhập"
            },
            {
                "key": "password",
                "key_name": "Mật khẩu"
            },
            {
                "key": "api",
                "key_name": "Provider Api"
            }
        ],
        "created_by": "admin",
        "created_time": "2024-05-22 08:23",
        "id": "664d6c3686e9d93a0fbe6dff",
        "provider": "Vietguys",
        "provider_link_image": "https://www.vietguys.biz/images/web/logo.png",
        "provider_type": 105,
        "type": [
            "CSKH"
        ],
        "updated_by": "admin",
        "updated_time": "2024-05-22 08:23"
    },
    {
        "connect_config_info": [
            {
                "key": "username",
                "key_name": "Tên đăng nhập"
            },
            {
                "key": "password",
                "key_name": "Mật khẩu"
            },
            {
                "key": "api",
                "key_name": "Provider Api"
            }
        ],
        "created_by": "admin",
        "created_time": "2024-05-22 08:23",
        "id": "664d6c3686e9d93a0fbe6dff",
        "provider": "Vietguys",
        "provider_link_image": "https://www.vietguys.biz/images/web/logo.png",
        "provider_type": 105,
        "type": [
            "CSKH"
        ],
        "updated_by": "admin",
        "updated_time": "2024-05-22 08:23"
    }
  ]
}
"""


************************ Tạo nhà cung cấp *************************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {POST} {domain}/market-place/api/v1.0/providers          Thêm mới nhà cung cấp
@apiDescription API thêm mới nhà cung cấp.
@apiGroup Provider
@apiVersion 1.0.0
@apiName CreateProvider

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  provider  Tên nhà cung cấp.
@apiParam   (Body:)   {String}  image   Tên cấu hình sms.
@apiParam   (Body:)   {String}  description   Mô tả cấu hình sms.


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Array}                  data                          Danh sách nhà cung cấp.
@apiSuccess {String}            data.id                   Id của link nhà cung cấp.
@apiSuccess {String}            data.provider                   Tên của nhà cung cấp.
@apiSuccess {String}            data.provider_link         Link image của nhà cung cấp.
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {Datetime}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật.
@apiSuccess {Datetime}            data.updated_time                       Thời điểm cập nhật.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "1",
      "provider": "Google",
      "provider_link": "https://www.google.com.vn",
      "created_by": "admin",
      "updated_by": "admin",
      "created_time": "2018-01-01T00:00:00Z",
      "updated_time": "2018-01-01T00:00:00Z"
    },
    {
      "id": "1",
      "provider": "GoogleMap",
      "provider_link": "https://www.google.com.vn".
      "created_by": "admin",
      "updated_by": "admin",
      "created_time": "2018-01-01T00:00:00Z",
      "updated_time": "2018-01-01T00:00:00Z"
    }
  ]
}
"""

************************ get log action ***************************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {GET} {domain}/market-place/api/v1.0/providers/configs/<email_config_id>/actions/logs         Danh sách log của các hành động khi thao tác trên cấu hình của provider.
@apiDescription API danh sách log.
@apiGroup Provider
@apiVersion 1.0.0
@apiName GetLogAction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	   (Params)		{string}	type        Type của provider config, vd: sms, email.
@apiParam	   (Params)		{string}	[sort]      Sắp xếp theo field tuỳ chỉnh, mặc định là updated_time.
@apiParam	   (Params)		{integer}	[order]     Sắp xếp theo thuộc tính 1-asc, -1-desc.  
@apiParam       (Params) {Int} [page] Trang dữ liệu, ví dụ: <code>&page=2</code>. Mặc định page=1 
@apiParam       (Params) {Int} [per_page] Số phần tử trên một page. Example: <code>&per_page=10</code>. Mặc định lấy 20 bản ghi.

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Array}            data                          Danh sách hành động.
@apiSuccess {Datetime}            data.id                     Id của log.
@apiSuccess {Datetime}            data.provider_config_id      Id cấu hình nhà cung cấp.
@apiSuccess {String}            data.action                   Tên loại hành động: "create", "update", "send_email".
@apiSuccess {Integer}            data.status                  Trạng thái của hành động 1-thành công, 0-thất bại.
@apiSuccess {String}            data.status_detail            Chi tiết trạng thái của hành động.
@apiSuccess {String}            data.created_by               Id người tạo hành động.
@apiSuccess {String}            data.username                 Tên người tạo hành động.
@apiSuccess {String}            data.email                    Email người tạo hành động.
@apiSuccess {Datetime}            data.created_time             Thời gian thực hiện hành động.
@apiSuccess {Object}            paging                         Thông tin phân trang.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
        {
            "_id": "6650180f74a835e6733350fa",
            "action": "create",
            "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "created_time": "2024-05-24 04:31",
            "id": "6650180f74a835e6733350fa",
            "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
            "provider_config_id": "6650180f74a835e6733350f9",
            "status": 1,
            "status_detail": "",
        }
    ],
  "paging": {
    "page": 1,
    "per_page": 20,
    "total_items": 1,
    "total_pages": 1
  }
}
"""
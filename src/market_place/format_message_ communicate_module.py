#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 09/08/2024
"""

"""
@api {QUEUE} {topic_AppMarketPlace_tao}     Cấu trúc message khi MarketPlace gửi vào Topic của luồng dataflow
@apiDescription     Cấu trúc message khi MarketPlace gửi vào Topic của luồng dataflow
@apiGroup MessageQueues
@apiVersion 1.0.0
@apiName FormatMessageQueue

@apiParam      (Input:)     {Object}   event_value                  Dữ liệu của các module
@apiParam      (Input:)     {Object}   event_value.profile_data     Dữ liệu profile
@apiParam      (Input:)     {Object}   event_value.deal_data        Dữ liệu CHB
@apiParam      (Input:)     {Object}   event_value.company_data     Dữ liệu công ty
@apiParam      (Input:)     {Object}   event_value.ticket_data      Dữ liệu Ticket
@apiParam      (Input:)     {Object}   profile_connector_config         Thông tin cấu hình của connector Profile
@apiParam      (Input:)     {String}   profile_connector_config.name                Tên connector
@apiParam      (Input:)     {String}   profile_connector_config.action=upsert              Hành động thao tác với dữ liệu.
                                                                                            <ul>
                                                                                                <li>add :: Thêm mới</li>
                                                                                                <li>update :: Cập nhật</li>
                                                                                                <li>upsert :: Upsert data</li>
                                                                                            </ul>
@apiParam      (Input:)     {Array}    profile_connector_config.fields_verify       Danh sách field verify
@apiParam      (Input:)     {Boolean}  profile_connector_config.is_trust            Có phải nguồn định danh hay không?
@apiParam      (Input:)     {Array}    profile_connector_config.fields_replace      Danh sách field key thao tác replace
@apiParam      (Input:)     {Array}    profile_connector_config.fields_append       Danh sách field key thao tác append
@apiParam      (Input:)     {Array}    profile_connector_config.fields_replace_ignore_empty       Danh sách field key thao tác replace và bỏ qua giá trị empty
@apiParam      (Input:)     {Object}   profile_connector_config.unification_rules                 Rule unification
@apiParam      (Input:)     {Object}   profile_connector_config.data_recording_rules              Rule ghi nhận dữ liệu
@apiParam      (Input:)     {Object}   deal_connector_config            Thông tin cấu hình của connector CHB. Cấu trúc tương tự như profile_connector_config.
@apiParam      (Input:)     {Object}   company_connector_config         Thông tin cấu hình của connector Công ty. Cấu trúc tương tự như profile_connector_config.
@apiParam      (Input:)     {Object}   ticket_connector_config          Thông tin cấu hình của connector Ticket. Cấu trúc tương tự như profile_connector_config.
@apiParam      (Input:)     {Int}      connector_id                     <code>Định danh connector</code>
@apiParam      (Input:)     {String}   merchant_id                      Merchant ID
@apiParam      (Input:)     {String}   message_id                       Message ID
@apiParam      (Input:)     {Object}   callback                         Thông tin callback trả về Orchestration khi xử lý xong
@apiParam      (Input:)     {String}   source_key                       Source key của connector
@apiParam      (Input:)     {Int}      session_id                       Session ID do Orchestration quy định
@apiParam      (Input:)     {String}   created_time                     Thời gian phát sinh
@apiParam      (Input:)     {Object}   information_request              Thông tin request
@apiParam      (Input:)     {Object}   information_request.headers      Thông tin headers request
@apiParam      (Input:)     {Object}   information_request.query_parameters              Thông tin tham số query



@apiParamExample [json] Input example:
{
    "information_request": {
        "headers": "",
        "query_parameters": ""
    },
    "event_value": {
        "profile_data": {
            "name": "HienDT",
            "primary_email": "<EMAIL>",
            "source": "Core",
            "profile_identify": [
                {
                    "identify_type": "citizen_identity",
                    "identify_value": "cccd3466"
                }
            ],
            "cif": "cif_hien",
            "address_personal": [
                {
                    "type": "contact_address",
                    "detail": "test dia chi",
                    "district": "Xã Hiếu Nghĩa",
                    "city": "Huyện Vũng Liêm",
                    "county": "Tỉnh Vĩnh Long",
                    "country": "Việt Nam"
                }
            ]
        },
        "company_data": {

        }
    },
    "primary_object": "profile",
    "connector_id": 20,
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "source_key": "api"
    "created_time": "2024-08-29 07:03:16.098",
    "session_id": 20240701066837,
    "state": "consume",
    "message_id": 1719817184120345,
    "profile_connector_config": {
        "name": "profiles",
        "fields_verify": [],
        "action": "upsert",
        "is_trust": true,
        "fields_replace": [],
        "fields_append": [],
        "fields_replace_ignore_empty": [
            "source",
            "cif",
            "name",
            "primary_phone",
            "primary_email",
            "profile_identify",
            "profile_owner",
            "address_personal"
        ],
        "unification_rules": {
            "operators": [
                {
                    "priority": 1,
                    "fields": {
                        "cif": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        },
                        "source": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        }
                    }
                }
            ],
            "consent": {
                "analytics_consent": "Có",
                "mkt_consent": "Có",
                "tracking_consent": "Có"
            }
        },
        "data_recording_rules": {
            "operators": [
                {
                    "priority": 1,
                    "fields": {
                        "cif": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        }
                    }
                }
            ],
            "consent": {
                "analytics_consent": "Có",
                "mkt_consent": "Có",
                "tracking_consent": "Có"
            }
        },
    },
    "deal_connector_config": {
        "name": "deals",
        "fields_verify": [],
        "action": "add",
        "is_trust": true,
        "fields_replace": [],
        "fields_append": [],
        "fields_replace_ignore_empty": [
            "source",
            "cif",
            "name",
            "primary_phone",
            "primary_email",
            "profile_identify",
            "profile_owner",
            "address_personal"
        ],
        "unification_rules": {
            "operators": [
                {
                    "priority": 1,
                    "fields": {
                        "code": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        },
                    }
                }
            ],
        },
        "data_recording_rules": {}
    },
    "callback": {
        "callback_type": "queue",
        "queue_config": {
            "target": "data-flow.1b99bdcf-d582-4f49-9715-1b61dfff3924.profiles.20240701066837_callback",
            "key": 1719817184120345
        },
        "data": {
            "session_id": 20240701066837,
            "connector_id": 20,
            "created_time": "2024-08-29 07:03:16.098",
            "state": "consume",
            "message_id": 1719817184120345,
            "source_key": "api"
        }
    },
    "version": 1
}

@apiSuccessExample  {json}  {json}  Callback Queue (Trả về theo yêu cầu của Orchestration để lên báo cáo):
{
    "session_id": 20240701066837,
    "connector_id": 20,
    "created_time": "2024-08-29 07:03:16.098", // created_time: datetime.utcnow()
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "result": {
        "profile": {
            "profile_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d688",
            "action": "add",
            "state": "processed"
        },
        "company": {
            "company_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d688",
            "state": "processed",
            "action": "add",
        }
    },
    "source_key": "api",
    "state": "processed",
    "message_id": 1719817184120345,
}
@apiSuccessExample  {json}  {json}  Callback Queue (Trả về theo yêu cầu của Orchestration để lên báo cáo, trong trường hợp bị lỗi):
{
    "session_id": 20240701066837,
    "connector_id": 20,
    "created_time": "2024-08-29 07:03:16.098", // created_time: datetime.utcnow()
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "result": {
        "deal": {
            "deal_id": None,
            "action": None,
            "state": "error",
            "reason": "deal not found"
        },
        "company": {
            "company_id": None,
            "state": "error",
            "reason": "phone wrong"
        }
    },
    "source_key": "api",
    "state": "error",
    "message_id": 1719817184120345,
    "reason": "company required"
}
"""

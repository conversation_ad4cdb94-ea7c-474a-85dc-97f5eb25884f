"""
@apiDefine ResponseDetailEmailConfig
@apiSuccess {String}            data.id                                 <code>ID</code> Id của email config.
@apiSuccess {String}            data.provider                           Tên nhà cung cấp.
@apiSuccess {String}            data.provider_link_image                Ảnh của nhà cung cấp.
@apiSuccess {Integer}            data.provider_type                     Loại nhà cung cấp.
@apiSuccess {Array}            data.type                                Loại cấu hình email.
@apiSuccess {String}            data.name                               Domain name.
@apiSuccess {String}            data.description                        Mô tả của cấu hình email.
@apiSuccess {String}            data.merchant_id                        Id của merchant.
@apiSuccess {Array}            data.emails                             Danh sách email.
@apiSuccess {String}            data.emails.name                        Tên hiển thị.
@apiSuccess {String}            data.emails.email                       Email.
@apiSuccess {Array}            data.emails.modules                      Đ<PERSON><PERSON> tượng sử dụng email.
@apiSuccess {Array}            data.dns_records                        Danh sách bản ghi DNS.
@apiSuccess {String}            data.dns_records.name                    Tên bản ghi.
@apiSuccess {String}            data.dns_records.type                    Loại bản ghi.
@apiSuccess {String}            data.dns_records.value                    Giá trị bản ghi.
@apiSuccess {Integer}            data.status                            Trạng thái của cấu hình email [1-thành công, 0-thất bại, 2-chờ xác thực].
@apiSuccess {Array}            [data.connect_config_info]                 Thông tin cấu hình email cụ thể, khác nhau tùy từng nhà cung cấp.
@apiSuccess {String}            data.connect_config_info.key                Key của thông tin cấu hình.
@apiSuccess {String}            data.connect_config_info.key_name           Tên của key cấu hình.
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {Datetime}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật.
@apiSuccess {Datetime}            data.updated_time                       Thời điểm cập nhật.
    
"""

************************ Create email config **********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {POST} {domain}/market-place/api/v1.0/email-configs             Thêm mới cấu hình email
@apiDescription API tạo cấu hình email
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName CreateEmailConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name   Domain name.
@apiParam   (Body:)   {Array}  type   Loại cấu hình email.
@apiParam   (Body:)   {String}  description   Mô tả cấu hình email.
@apiParam   (Body:)   {String}  provider_type   Loại nhà cung cấp, lấy từ danh sách provider.
@apiParam   (Body:)   {Object}   [connect_config_info] Thông tin cấu hình email.
<li><code>key</code> Key cấu hình lấy từ thông tin của nhà cung cấp </li>
<li><code>value</code> Giá trị thô cấu hình</li>

@apiParamExample  {json}   Body example
{
  "name": "google.mail",
  "type": ["message", "notification"],
  "provider_type": 300,
  "description": "google mail description"
}

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Object}           data                          Dữ liệu cấu hình email.
@apiSuccess {String}           data.id                       <code>ID</code> Id của email config.
@apiSuccess {String}           data.type                        Type của email config.
@apiSuccess {String}           data.provider_link_image            Link ảnh nhà cung cấp.
@apiSuccess {String}           data.provider                 Tên nhà cung cấp.
@apiSuccess {String}           data.name                     Domain name.
@apiSuccess {Integer}  data.status   Trạng thái cấu hình email [1-thành công, 0-thất bại, 2-chờ xác thực].
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {Datetime}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật.
@apiSuccess {Datetime}            data.updated_time                       Thời điểm cập nhật.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
      "id": "620dc147dfd20bf34ac6954f",
      "provider_link_image": "https://www.sendcloud.com/email-api/doc/guide",
      "provider": "Mobio",
      "name": "google.mail",
      "type": ["message", "notification"],
      "status": 2,
      "created_by": "admin",
      "updated_by": "admin",
      "created_time": "2018-01-01T00:00:00Z",
      "updated_time": "2018-01-01T00:00:00Z"
    }
}
"""



************************ Get list email config ********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {GET} {domain}/market-place/api/v1.0/email-configs             Lấy danh sách cấu hình email.
@apiDescription API lấy danh sách cấu hình email.
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName GetEmailConfigs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse json_header
@apiUse merchant_id_header


@apiParam	   (Params)			{string}	[search]    Tìm kiếm theo email config.     
@apiParam	   (Params)		{string}	[sort]      Sắp xếp theo field tuỳ chỉnh, mặc định là updated_time.
@apiParam	   (Params)		{integer}	[order]     Sắp xếp theo thuộc tính 1-asc, -1-desc.  
@apiParam       (Params) {Int} [page] Trang dữ liệu, ví dụ: <code>&page=2</code>. Mặc định page=1 
@apiParam       (Params) {Int} [per_page] Số phần tử trên một page. Example: <code>&per_page=10</code>. Mặc định lấy 20 bản ghi. 


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Array}           data                          Danh sách email config.
@apiSuccess {Object}                paging                         Thông tin phân trang.

@apiSuccess {String}    data.id     Id email config.
@apiSuccess {String}    data.provider_link_image  Link image của nhà cung cấp.
@apiSuccess {String}    data.provider  Tên nhà cung cấp.
@apiSuccess {String}    data.name   Domain name.
@apiSuccess {Array}            data.type                                Loại cấu hình email.
@apiSuccess {String}            data.merchant_id                        Id của merchant.
@apiSuccess {Integer}            data.provider_type                     Loại nhà cung cấp.
@apiSuccess {Integer}  data.status   Trạng thái cấu hình email [1-thành công, 0-thất bại, 2-chờ xác thực].
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {Datetime}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật.
@apiSuccess {Datetime}            data.updated_time                       Thời điểm cập nhật.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "created_by": null,
      "created_time": "2024-05-21 08:25",
      "description": "",
      "id": "664c5a6308fce890b8ff295f",
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name": "domain1.edu.vn",
      "provider": "Mobio",
      "provider_link_image": "https://Mobio.vn/wp-content/uploads/2018/12/logo_Mobio.png",
      "provider_type": 115,
      "status": 1,
      "type": [],
      "updated_by": null,
      "updated_time": "2024-05-21 08:25"
    },
    {
      "created_by": null,
      "created_time": "2024-05-21 08:25",
      "description": "",
      "id": "664c5a6308fce890b8ff295f",
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name": "domain1.edu.vn",
      "provider": "Mobio",
      "provider_link_image": "https://Mobio.vn/wp-content/uploads/2018/12/logo_Mobio.png",
      "provider_type": 115,
      "status": 1,
      "type": [],
      "updated_by": null,
      "updated_time": "2024-05-21 08:25"
        }
    ],
    "paging": {
        "page": 1,
        "per_page": 20,
        "total_count": 813,
        "total_page": 407
    }
}
"""
************************ Delete email config **********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {DELETE} {domain}/market-place/api/v1.0/email-configs/<email_config_id>                     Xóa cấu hình email
@apiDescription API xoa cấu hình email
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName DeleteEmailConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

************************ send  test email config ******************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {POST} {domain}/market-place/api/v1.0/email-configs/<email_config_id>/actions/send-email           Gửi tin nhắn thử nghiệm cấu hình email.
@apiDescription API gửi tin nhắn thử nghiệm email.
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName SendEmailTest

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  from  Email người gửi.
@apiParam   (Body:)   {String}  to  Email người nhận.

@apiParamExample  {json}   Body example
{
  "from": "<EMAIL>",
  "to": "<EMAIL>",
}

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""
************************ detail email config **********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {GET} {domain}/market-place/api/v1.0/email-configs/<email_config_id>             Xem chi tiết cấu hình email
@apiDescription API xem chi tiết cấu hình email
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName DetailEmailConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Object}           data                           Cấu hình email config.
@apiUse ResponseDetailEmailConfig

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request này.",
  "data": {
  "_id": "664c5ccdb8a694223217c24d",
  "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "created_time": "2024-05-22 09:16",
  "description": "test",
  "id": "664c5ccdb8a694223217c24d",
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "name": "google.com",
  "dns_records": [
      {
          "name": "o76figdsnl7co5nfr6sdzedinpnl3f2j._domainkey.test.mobio.vn",
          "type": "CNAME",
          "value": "o76figdsnl7co5nfr6sdzedinpnl3f2j.dkim.mailer.mobio.vn"
      },
      {
          "name": "xle5yhjc2cjn6ey2drpaaigl7nfgbxai._domainkey.test.mobio.vn",
          "type": "CNAME",                 
          "value": "xle5yhjc2cjn6ey2drpaaigl7nfgbxai.dkim.mailer.mobio.vn"
      },
      {
          "name": "taoafa2ybqva6pt3urdvfctjxk3g4dx._domainkey.test.mobio.vn",
          "type": "CNAME",
          "value": "taoafa2ybqva6pt3urdvfctjxk3g4dx.dkim.mailer.mobio.vn"
      },
    ],
  "emails": [
      {
        "email": "<EMAIL>",
        "name": "Test",
        "modules": []
      }
    ],
  "provider": "mobio",
  "provider_link_image": "https://mobio.vn/wp-content/uploads/2018/12/logo_mobio.png",
  "provider_type": 115,
  "status": 0,
  "type": ["message"],
  "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "updated_time": "2024-05-22 09:28"
}
}
"""


************************ update email config **********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {PUT} {domain}/market-place/api/v1.0/email-configs/<email_config_id>             Update cấu hình email config
@apiDescription API update cấu hình email
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName UpdateEmailConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {Array}  type   Loại cấu hình email.

@apiParamExample  {json}   Body example
{
  "type": ["message"],
}


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Object}           data                           Cấu hình email config.
@apiUse ResponseDetailEmailConfig

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request này.",
  "data": {
  "_id": "664c5ccdb8a694223217c24d",
  "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "created_time": "2024-05-22 09:16",
  "description": "test",
  "id": "664c5ccdb8a694223217c24d",
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "name": "google.com",
  "provider": "mobio",
  "provider_link_image": "https://mobio.vn/wp-content/uploads/2018/12/logo_mobio.png",
  "provider_type": 115,
  "status": 0,
  "type": ["message"],
  "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "updated_time": "2024-05-22 09:28"
}
}
"""

************************ export record ****************************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {GET} {domain}/market-place/api/v1.0/email-configs/<email_config_id>/actions/export-records             Export danh sách bản ghi.
@apiDescription API xuất danh sách bản ghi.
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName ExportRecord

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.



@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""
************************ Re-validate email config *****************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {PATCH} {domain}/market-place/api/v1.0/email-configs/<email_config_id>/actions/change-status       Cập nhật trạng thái cho email config
@apiDescription API cập nhật trạng thái cho email config
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName UpdateStatusEmailConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {Integer}  status   Trạng thái cấu hình email [1-thành công, 0-thất bại, 2-chờ xác thực].

@apiParamExample  {json}   Body example
{
  "status": 2
}

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""
************************ sync email config ************************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {POST} {domain}/market-place/api/v1.0/email-configs/actions/sync-data           Api tạo mới cấu hình email bằng cách đồng bộ dữ liệu
@apiDescription API tạo mới cấu hình email bằng cách động bộ dữ liệu
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName SyncEmailConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name   Tên cấu hình email, không được trùng lặp trong một merchant.
@apiParam   (Body:)   {String}  type   Loại cấu hình email 
<ul>
                                                                                        <li>message: Gửi thông điệp</li>
                                                                                        <li>notification: Gửi thông báo</li>
                                                                                    </ul>.
@apiParam   (Body:)   {Integer}  status   Trạng thái cấu hình email [1-thành công, 0-thất bại].
@apiParam   (Body:)   {Integer}  provider_type   Mã code của nhà cung cấp.
@apiParam   (Body:)   {String}  description   Mô tả cấu hình email.
@apiParam   (Body:)   {Object}   connect_config_info Thông tin cấu hình email, với nhà cung cấp mobio mailer, cấu hình là {}.
@apiParam   (Body:)   {String}   connect_config_info.key Khoá giá trị của trường thông tin: Danh sách key với nhà cung cấp AWS SES:
                                                                                    <ul>
                                                                                        <li>AWS_ACCESS_KEY_ID (tài khoản)</li>
                                                                                        <li>AWS_SECRET_ACCESS_KEY (mật khẩu)</li>
                                                                                        <li>config_set (config set)</li>
                                                                                        <li>region (region)</li>
                                                                                    </ul>
                                                                                    

@apiParam   (Body:)   {String}   connect_config_info.value Giá trị thô của trường thông tin tương ứng.

@apiParamExample  {json}   Body example
{
  "name": "admin.vn",
  "description": "day la mo ta",
  "connect_config_info": {
      "AWS_ACCESS_KEY_ID": "value1",
      "AWS_SECRET_ACCESS_KEY": "hihi",
      "config_set": "hihi",
      "region": "abcd"
  },
  "type": ["message"],
  "status": 1,
  "provider_type": 203
}

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""

************************ get list email address *******************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {GET} {domain}/market-place/api/v1.0/email-configs/<email_config_id>/emails         Lấy danh sách email.
@apiDescription API lấy danh sách email.
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName GetEmails

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse json_header
@apiUse merchant_id_header

@apiParam	   (Params)		{string}	[sort]      Sắp xếp theo field tuỳ chỉnh, mặc định là updated_time.
@apiParam	   (Params)		{integer}	[order]     Sắp xếp theo thuộc tính 1-asc, -1-desc.  
@apiParam       (Params) {Int} [page] Trang dữ liệu, ví dụ: <code>&page=2</code>. Mặc định page=1 
@apiParam       (Params) {Int} [per_page] Số phần tử trên một page. Example: <code>&per_page=10</code>. Mặc định lấy 20 bản ghi.

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Array}           data                           Danh sách email.
@apiSuccess {Array}           data.name                      Tên hiển thị.
@apiSuccess {String}           data.email                     Email.
@apiSuccess {Array}           data.module                     Module đang sử dụng email 
@apiSuccess {Object}                paging                         Thông tin phân trang.



@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
  "data": [
      {
        "name": ["Maketing msb"],
        "email": "<EMAIL>",
        "module": ["Maketing"]
      },
      {
        "name": ["Maketing msb"],
        "email": "<EMAIL>",
        "module": ["Maketing"]
      }
   ],
   "paging": {
        "page": 1,
        "per_page": 20,
        "total_count": 813,
        "total_page": 407
    }
}
"""

************************ create email address *********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {POST} {domain}/market-place/api/v1.0/email-configs/<email_config_id>/emails         Thêm mới địa chỉ email
@apiDescription API  thêm mới địa chỉ email.
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName CreateEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse json_header
@apiUse merchant_id_header

@apiParam	   (Body:)		{String}	    name      Tên hiện thị.
@apiParam	   (Body:)		{String}	email      Email.

@apiParamExample  {json}   Body example
{
  "name": "Maketing msb",
  "email": "<EMAIL>"
}


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Array}           data                           Danh sách email.
@apiSuccess {String}           data.name                      Tên hiển thị.
@apiSuccess {String}           data.email                     Email.



@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
  "data": {
    "name": "Maketing msb",
    "email": "<EMAIL>",
},
}
"""
************************ delete email address *********************************
* version: 1.0.0                                                              *
*******************************************************************************     
"""
@api {DELETE} {domain}/market-place/api/v1.0/email-configs/<email_config_id>/emails/<email>         Xóa địa chỉ email
@apiDescription API xóa địa chỉ email.
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName DeleteEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.
}
"""

************************ decode field email config ******************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {GET} {domain}/market-place/api/v1.0/email-configs/<email_config_id>/actions/decode         Giải mã giá trị của field trong cấu hình email
@apiDescription API giải mã giá trị.
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName DecodeEmailConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Params)   {String}  key  Key cần giải mã giá trị, vd: "username".

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Object}           data                          Dữ liệu giải mã.
@apiSuccess {String}           data.value                    giá trị mã hoá.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "value": "test"
  }
}
"""

************************ get wf by email config ****************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {GET} {domain}/market-place/api/v1.0/email-configs/workflows-by-domain       Lấy danh sách workflow đang sử dụng domain
@apiDescription API giải mã giá trị.
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName GetWorkflowsByDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Params)   {String}  domain Domain cần lấy workflow.

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Array}           data                          Dữ liệu giải mã.
@apiSuccess {String}           data.workflow_id              Id của workflow.
@apiSuccess {String}           data.workflow_name            Tên của workflow.
@apiSuccess {String}           data.status                   Trạng thái hiện tại của workflow: [PROCESSING, PAUSED, FINISH].
@apiSuccess {Array}           data.email_inuse              Danh sách email có domain đang kiếm sử dụng trong workflow.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "workflow_id": "f223650b-c2b4-4fe6-978a-409cdeedf946",
            "workflow_name": "workflow 1",
            "status": "PROCESSING",
            "email_inuse": [
                "<EMAIL>",
                "<EMAIL>"
            ]
        },
        {
            "workflow_id": "4801f658-32d5-11ef-aea6-38d57a786a3e",
            "workflow_name": "workflow 2",
            "status": "PAUSED",
            "email_inuse": [
                "<EMAIL>",
                "<EMAIL>"
            ]
        }
    ]
}
"""


************************ get region provider AWS-SES ****************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {GET} {domain}/market-place/api/v1.0/email-configs/aws-regions      Lấy danh sách region dịch vụ AWS-SES
@apiDescription API lấy danh sách region với dịch vụ AWS-SES
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName GetListRegionAws

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Array}           data                          Dữ liệu trả về.
@apiSuccess {String}           data.region_code                    Mã code khu vực
@apiSuccess {String}           data.region_name                    Tên khu vực
@apiSuccess   {String}          lang                         Mã ngôn ngữ trả về


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data":[
        {
            "region_code": "af-south-1",
            "region_name": "Cape Town"
        },
        {
            "region_code": "ap-northeast-1",
            "region_name": "Tokyo"
        },
        {
            "region_code": "ap-northeast-2",
            "region_name": "Seoul"
        },
        {
            "region_code": "ap-northeast-3",
            "region_name": "Osaka"
        }
    ]
}
"""


************************ Create email config V2 FLOW AWS-SES**********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {POST} {domain}/market-place/api/v1.0/email-configs             Thêm mới cấu hình email V2
@apiDescription API tạo cấu hình email V2 (thay đổi flow AWS-SES)
@apiGroup EmailConfig
@apiVersion 2.0.0
@apiName CreateEmailConfigV2

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name   Domain name đối với provider Mobio_mailer, access_key_id với AWS-SES
@apiParam   (Body:)   {Array}  type   Loại cấu hình email.
@apiParam   (Body:)   {String}  description   Mô tả cấu hình email.
@apiParam   (Body:)   {String}  provider_type   Loại nhà cung cấp, lấy từ danh sách provider.
@apiParam   (Body:)   {Object}   [connect_config_info] Thông tin cấu hình email.
<li><code>key</code> Key cấu hình lấy từ thông tin của nhà cung cấp </li>
<li><code>value</code> Giá trị thô cấu hình</li>
@apiParam   (Body:)   {Object}   [domains] Thông tin danh sách domain (áp dụng provider AWS-SES)
<li><code>domain</code> Key cấu hình lấy từ thông tin của nhà cung cấp </li>
<li><code>status</code> Trạng thái domain (1- xác thực, 2- chờ xác thực)</li>
<li><code>type</code> Loại cấu hình gửi email</li>
<li><code>description</code> Mô tả</li>

@apiParamExample  {json}   Body example
{
    "name": "access_key_1",
    "description": "",
    "connect_config_info": {
        "AWS_ACCESS_KEY_ID": "access_key_1",
        "AWS_SECRET_ACCESS_KEY": "123456",
        "config_set": "abcdef",
        "region": "us-gov-east-1"
    },
    "type": [],
    "domains":[
        {
            "domain" : "so333.com",
            "status" : 1,
            "type" : [
                "message"
            ],
            "description" : ""
        },
        {
            "domain" : "so444.com",
            "status" : 2,
            "type" : [
                "message"
            ],
            "description" : ""
        }
    ]
    "provider_type": 203
}

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Object}           data                          Dữ liệu cấu hình email.
@apiSuccess {String}           data.id                       <code>ID</code> Id của email config.
@apiSuccess {String}           data.type                        Type của email config.
@apiSuccess {String}           data.provider_link_image            Link ảnh nhà cung cấp.
@apiSuccess {String}           data.provider                 Tên nhà cung cấp.
@apiSuccess {String}           data.name                     Domain name.
@apiSuccess {Integer}         data.status   Trạng thái cấu hình email [1-thành công, 0-thất bại, 2-chờ xác thực].
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {Datetime}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật.
@apiSuccess {Datetime}            data.updated_time                       Thời điểm cập nhật.
@apiSuccess {Array}            data.domains                       Danh sách domain.
@apiSuccess {String}            data.domains.domain                       Tên domain
@apiSuccess {String}            data.domains.status                    Trạng thái domain [1-xác thực, 2- chờ xác thực]
@apiSuccess {Array}            data.domains.type                    Loại thông báo domain [message: gửi mail, notification: push thông báo]
@apiSuccess {description}            data.domains.description           Mô tả

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
      "id": "620dc147dfd20bf34ac6954f",
      "provider_link_image": "https://www.sendcloud.com/email-api/doc/guide",
      "provider": "Mobio",
      "name": "google.mail",
      "type": ["message", "notification"],
      "status": 2,
      "created_by": "admin",
      "updated_by": "admin",
      "created_time": "2018-01-01T00:00:00Z",
      "updated_time": "2018-01-01T00:00:00Z",
      "domains": [
        {
            "domain" : "so333.com",
            "status" : 1,
            "type" : [
                "message"
            ],
            "description" : ""
        },
        {
            "domain" : "so444.com",
            "status" : 2,
            "type" : [
                "message"
            ],
            "description" : ""
        }
      ]
    }
}
"""


************************ Gỡ domain trong cấu hình email**********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {DELETE} {domain}/market-place/api/v1.0/email-configs/<email_config_id>/domain-config            Gỡ domain trong cấu hình email
@apiDescription API gỡ cấu hình domain trong cấu hình email
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName DeleteDomainConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Params)   {String}  domain   domain cần xoá


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess   {String}          lang                          Mã ngôn ngữ trả về

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""


************************ Chuyển trạng thái xác thực domain trong provider AWS-SES**********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {PUT} {domain}/market-place/api/v1.0/email-configs/<email_config_id>/domain-change-status          Chuyển trạng thái xác thực domain trong cấu hình provider AWS-SES
@apiDescription Chuyển trạng thái xác thực domain trong cấu hình email (flow AWS-SES)
@apiGroup EmailConfig
@apiVersion 1.0.0
@apiName UpdateStatusDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  domain            domain cần update
@apiParam   (Body:)   {Integer}  status          Trạng thái cần update [1-thành công, 2-chờ xác thực]

@apiParamExample  {json}   Body example
{
    "domain": "so333.com",
    "status" : 1
}

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess   {String}          lang                          Mã ngôn ngữ trả về

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

************************ update email config V2**********************************
* version: 2.0.0                                                              *
*******************************************************************************
"""
@api {PATCH} {domain}/market-place/api/v1.0/email-configs/<email_config_id>             Update cấu hình email config V2
@apiDescription API update cấu hình email V2
@apiGroup EmailConfig
@apiVersion 2.0.0
@apiName UpdateEmailConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {Array}  domains   Danh sách domain update
@apiParam   (Body:)   {String}  domains.domain      Tên domain
@apiParam   (Body:)    {Int}    domains.status      Trạng thái domain (1- đã xác thực, 2- chờ xác thực)
@apiParam   (Body:)     {Array}   domains.type      Loại cấu hình email
@apiParam   (Body:)     {String}   domains.description  Mô tả

@apiParamExample  {json}   Body example
{
    "domains": [
        {
            "domain": "so333.com",
            "status": 1,
            "type": [
                "message"
            ],
            "description": ""
        }
    ]
}


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {String}           lang                           Mã ngôn ngữ.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 25/06/2024
"""
# ---------------- RequestBodyInformationConnectDataInPostgres -------------------
"""
@api None                 RequestBodyInformationConnectDataInPostgres
@apiGroup Base
@apiVersion 1.0.0
@apiName RequestBodyInformationConnectDataInPostgres

@apiParam   (BODY:) {object}            config_connect                                      Thông tin kết nối

@apiParam   (BODY:) {string}            config_connect.hostname                                          Thông tin hostname
@apiParam   (BODY:) {int}               config_connect.port                                              Thông tin port kết nối database
@apiParam   (BODY:) {string}            config_connect.database_name                                     Tên database name
@apiParam   (BODY:) {string}            config_connect.database_username                                          Use<PERSON><PERSON> đăng nhập vào database
@apiParam   (BODY:) {string}            config_connect.database_password                                          Password đăng nhập vào database


@apiParamExample {json} Body
{
    "config_connect": {
        "database_name": "ladder",
        "database_password": "root",
        "database_username": "root",
        "host": "localhost",
        "port": 5432
    }
}
"""

# ---------------- RequestBodyInformationConnectDataOutWebhooks -------------------
"""
@api None                 RequestBodyInformationConnectDataOutWebhooks
@apiGroup Base
@apiVersion 1.0.0
@apiName RequestBodyInformationConnectDataOutWebhooks

@apiParam   (BODY:) {object}            data.config_connect                              Thông tin cấu hình kết nối
@apiParam   (BODY:) {string}            data.config_connect.app_id                       Định danh của APP Authentication. Api lấy danh APP <a href="https://dev.mobio.vn/docs/market_place/#api-DataFlowConfigApp-ListApps">tại đây</a>
@apiParam   (BODY:) {string}               data.config_connect.url                          URL connect
@apiParam   (BODY:) {Array}            data.config_connect.methods                      Danh sách Methods tương tác với URL.
@apiParam   (BODY:) {Array}            data.config_connect.param_headers                Tham số trên headers
@apiParam   (BODY:) {string}            data.config_connect.param_headers.key           Key của tham số
@apiParam   (BODY:) {string}            data.config_connect.param_headers.value         Giá trị của tham số
@apiParam   (BODY:) {string=string,formula}            data.config_connect.param_headers.type          Loại của tham số
@apiParam   (BODY:) {string=system,user}            data.config_connect.param_headers.created_by        Người tạo
@apiParam   (BODY:) {string=application/json}            data.config_connect.content_type        Type của Body


@apiParamExample {json} Body
{
    "config_connect": {
        "app_id": "",
        "url": "",
        "methods": ["GET", "POST"],
        "param_headers": [
            {
                "key": "mac",
                "value": "sha256(app_id + data_string + app_secret)",
                "type": "formula/string",
                "created_by": "system"
            },
            {
                "key": "",
                "value": "",
                "type": "string"
            }
        ],
        "content_type": "application/json",
    }
}
"""

# ---------------- RequestBodyInformationConnectDataInApi -------------------
"""
@api None                 RequestBodyInformationConnectDataInApi
@apiGroup Base
@apiVersion 1.0.0
@apiName RequestBodyInformationConnectDataInApi

@apiParam   (BODY:) {object}            config_connect                              Thông tin cấu hình kết nối
@apiParam   (BODY:) {string}            config_connect.app_id                       Định danh của APP Authentication. Api lấy danh APP <a href="https://dev.mobio.vn/docs/market_place/#api-DataFlowConfigApp-ListApps">tại đây</a>
@apiParam   (BODY:) {string}            config_connect.url                          URL connect
@apiParam   (BODY:) {Array}             config_connect.methods                      Danh sách Methods tương tác với URL.
@apiParam   (BODY:) {Array}             config_connect.param_headers                Tham số trên headers
@apiParam   (BODY:) {string}            config_connect.param_headers.key           Key của tham số
@apiParam   (BODY:) {string}            config_connect.param_headers.value         Giá trị của tham số
@apiParam   (BODY:) {string=string,formula}             config_connect.param_headers.type          Loại của tham số
@apiParam   (BODY:) {string=system,user}                config_connect.param_headers.created_by        Người tạo
@apiParam   (BODY:) {string}                            config_connect.param_query                     Tham số trên query
@apiParam   (BODY:) {string=application/json}           config_connect.content_type        Type của Body



@apiParamExample {json} Body
{
    "config_connect": {
        "app_id": "",
        "url": "",
        "methods": ["POST"],
        "param_headers": [
            {
                "key": "mac",
                "value": "sha256(app_id + data_string + app_secret)",
                "type": "formula/string",
                "created_by": "system"
            },
            {
                "key": "",
                "value": "",
                "type": "string"
            }
        ],
        "param_query": "limit=10&offset=0",
        "content_type": "application/json",
    }
}
"""

# ---------------- RequestBodyInformationConnectDataInContactForm -------------------
"""
@api None                 RequestBodyInformationConnectDataInContactForm
@apiGroup Base
@apiVersion 1.0.0
@apiName RequestBodyInformationConnectDataInContactForm

@apiParam   (BODY:) {object}            config_connect                              Thông tin cấu hình kết nối
@apiParam   (BODY:) {string}            config_connect.form_id                      Định danh của Form



@apiParamExample {json} Body
{
    "config_connect": {
        "form_id": ""
    }
}
"""
# ---------------- RequestBodyInformationConnectDataOutGoogleSheet -------------------
"""
@api None                 RequestBodyInformationConnectDataOutGoogleSheet
@apiGroup Base
@apiVersion 1.0.0
@apiName RequestBodyInformationConnectDataOutGoogleSheet

@apiParam   (BODY:) {object}            config_connect                              Thông tin cấu hình kết nối
@apiParam   (BODY:) {string}            config_connect.integration_account_id       Định danh tài khoản liên kết. Api lấy danh sách tài khoản liên kết <a href="https://dev.mobio.vn/docs/market_place/#api-InteractionAccounts-ListInteractionAccounts">tại đây</a>
@apiParam   (BODY:) {string}            config_connect.type_use_spreadsheet         Kiểu sử dụng bảng tính
                                                                                    <ul>
                                                                                        <li><code>new_spreadsheet</code>:: Tạo mới bảng tính </li>
                                                                                        <li><code>existing_spreadsheet</code>:: Sử dụng bảng tính có sẵn </li>
                                                                                    </ul>
@apiParam   (BODY:) {string}            [config_connect.folder_id]                  Folder mặc định do hệ thống Mobio tự tạo. Trong trường hợp sử dụng bảng tính mới. 
@apiParam   (BODY:) {Array}             config_connect.spreadsheet_id               <code>ID</code> của bảng tính.
@apiParam   (BODY:) {Array}             config_connect.sheet_id                     <code>ID</code> của trang tính



@apiParamExample {json} Body
{
    "config_connect": {
        "integration_account_id": "",
        "type_use_spreadsheet": "existing_spreadsheet",
        "spreadsheet_id": "**********",
        "sheet_id": 0
    }
}
"""

# ---------------- RequestBodyInformationConnectDataInContactForm -------------------
"""
@api None                 RequestBodyInformationConnectDataInContactForm
@apiGroup Base
@apiVersion 1.0.0
@apiName RequestBodyInformationConnectDataInContactForm

@apiParam   (BODY:) {object}            config_connect                              Thông tin cấu hình kết nối
@apiParam   (BODY:) {string}            config_connect.form_id                      Định danh của Form



@apiParamExample {json} Body
{
    "config_connect": {
        "form_id": ""
    }
}
"""

# ---------------- RequestBodyUpdateConnectDataInPostgres  ------------------------
"""
@api  None                  RequestBodyUpdateConnectDataInPostgres
@apiGroup Base
@apiVersion 1.0.0
@apiName RequestBodyUpdateConnectDataInPostgres
@apiParam   (BODY:) {string}                            object                                      Đối tượng xử lý dữ liệu
@apiParam   (BODY:) {string}                            object_attribute                            Thuộc tính của đối tượng. Trong trường hợp k có attribute cần truyền "".
@apiParam   (BODY:) {bool}                              is_trust_source                             Là nguồn ghi nhận đáng tin cậy.
@apiParam   (BODY:) {Array}                             list_field_verify                           Danh sách field verfiy
@apiParam   (BODY:) {Array}                              [config_data_default_of_primary_object]               Cấu hình dữ liệu mặc định của đối tượng chính
@apiParam   (BODY:) {String}                             [config_data_default_of_primary_object.field_key]     Field key
                                                                                                             <ul>
                                                                                                                <li>event_key :: <code>Định danh event của attribute dynamic event</code></li>
                                                                                                                <li>product_line_id :: <code>Định danh dòng sản phẩm của attribute product holding</code></li>
                                                                                                             </ul>
@apiParam   (BODY:) {String}                             [config_data_default_of_primary_object.value]         Dữ liệu của field
@apiParam   (BODY:) {Boolean}                            [config_data_default_of_primary_object.is_personalization]         Field này có cá nhân hoá không?
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization]         Config field cá nhân hoá
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization.field_key]         Field key cá nhân hoá
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization.value_replace]         Giá trị replace
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization.object]         Đối tượng

@apiParam   (BODY:) {object}                            config_mapping_data                                     Mapping trường thông tin.
@apiParam   (config_mapping_data:) {string}             table                                                   Tên table cấu hình 
@apiParam   (config_mapping_data:) {array}              fields                                                  Danh sách field mapping
@apiParam   (config_mapping_data:) {string}             fields.field_source                                     Field đến từ ngồn
@apiParam   (config_mapping_data:) {string}             fields.field_target                                     Field tương ứng vào hệ thống
@apiParam   (config_mapping_data:) {string}             fields.object                                           Đối tượng được mapping 
@apiParam   (config_mapping_data:) {string}             fields.value_type=record                              Kiểu ghi nhận giá trị
                                                                                                                <ul>
                                                                                                                    <li>fixed: Giá trị kiểu fix cứng</li>
                                                                                                                    <li>record: Ghi nhận giá trị từ việc đồng bộ dữ liệu</li>
                                                                                                                </ul>
@apiParam   (config_mapping_data:) {string}             [fields.value_by_type_fixed]                            Giá trị theo kiểu giá trị ghi nhận
@apiParam   (config_mapping_data:) {bool}               [fields.required]                                 Bắt buộc hay không?
@apiParam   (config_mapping_data:) {string}             fields.action                                   Action khi thao tác với dữ liệu
                                                                                                          <ul>
                                                                                                            <li>add: Thêm</li>
                                                                                                            <li>overwrite: Ghi đè</li>
                                                                                                            <li>overwrite_and_ignore_value_null: Ghi đè và bỏ quá giá trị null</li>
                                                                                                          </ul>
@apiParam   (BODY:) {object}                             config_rule_unification                       Quy tắc đồng bộ dữ liệu. <code>Trong trường hợp nếu không phải trust_source thì sẽ ghi nhận dữ liệu như rule ở quy tắc đồng bộ dữ liệu</code>
@apiParam   (config_rule_unification:)  {object}         config_rule_unification.data_recording_rules                       Cấu hình rule ghi nhận dữ liệu
@apiParam   (config_rule_unification:)  {array}          config_rule_unification.data_recording_rules.operators             Thông tin cấu hình rule ghi nhận dữ liệu: <a>https://dev.mobio.vn/docs/profiling/#api-Unification</a>
@apiParam   (config_rule_unification:)  {object}         config_rule_unification.data_update_rules                          Cấu hình rule cập nhật dữ liệu
@apiParam   (config_rule_unification:)  {array}          config_rule_unification.data_update_rules.operators                Thông tin cấu hình rule cập nhật dữ liệu: <a>https://dev.mobio.vn/docs/profiling/#api-Unification</a>     
@apiParam   (config_rule_unification:)  {Object}          [consent]                Thông tin consent
@apiParam   (config_rule_unification:) {String=Có,Không}       [consent.mkt_consent]             Marketing Consent.
@apiParam   (config_rule_unification:) {String=Có,Không}       [consent.analytics_consent]       Analytics Consent.
@apiParam   (config_rule_unification:) {String=Có,Không}       [consent.tracking_consent]        Tracking Consent.
@apiParam   (BODY:) {Array}                             [config_rule_unification_secondary_object]                       Quy tắc đồng bộ dữ liệu của đối tượng phụ.
@apiParam   (config_rule_unification_secondary_object:)  {object}         [config_rule_unification_secondary_object.data_recording_rules]                       Cấu hình rule ghi nhận dữ liệu
@apiParam   (config_rule_unification_secondary_object:)  {array}          [config_rule_unification_secondary_object.data_recording_rules.operators]             Thông tin cấu hình rule ghi nhận dữ liệu: <ul>
    <li>Profile: <a href=https://dev.mobio.vn/docs/profiling/#api-Unification>tại đây</a></li>
</ul>
@apiParam   (config_rule_unification_secondary_object:)  {Object}          [consent]                Thông tin consent
@apiParam   (config_rule_unification_secondary_object:) {String=Có,Không}       [consent.mkt_consent]             Marketing Consent.
@apiParam   (config_rule_unification_secondary_object:) {String=Có,Không}       [consent.analytics_consent]       Analytics Consent.
@apiParam   (config_rule_unification_secondary_object:) {String=Có,Không}       [consent.tracking_consent]        Tracking Consent.
@apiParam   (config_rule_unification_secondary_object:)  {string}               [secondary_object]                          Tên đối tượng phụ

@apiParamExample {json} Body
{
    "object": "profiles",
    "object_attribute": "dynamic_event",
    "is_trust_source": true,
    "config_data_default_of_primary_object": [
        {
            "field_key": "event_key",
            "value": "field_2123333",
            "is_personalization": false,
        }
    ],
    "list_field_verify": [
        "cif",
        "customer_id"
    ],
    "config_mapping_data": {
        "table": "profile",
        "fields": [
            {
                "field_source": "id",
                "field_target": "customer_id",
                "object": "profiles",
                "value_type": "record",
                "required": true,
                "action": "add"
            },
            {
                "field_source": "name",
                "field_target": "name",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "phone",
                "field_target": "primary_phone",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "email",
                "field_target": "primary_email",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "cif",
                "field_target": "cif",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            }
        ]
    },
    "config_rule_unification": {
            "data_recording_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "primary_phone": {
                                "match_type": "exact_normalized",
                                "normalized_type": "phone"
                            }
                        },
                        "priority": 1
                    }
                ]
            },
            "data_update_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "primary_phone": {
                                "match_type": "exact_normalized",
                                "normalized_type": "phone"
                            }
                        },
                        "priority": 1
                    }
                ]
            },
        "consent": {
            "analytics_consent": "Có",
            "tracking_consent": "Có",
            "mkt_consent": "Có"
        }
    },
    "config_sync_calendar": {
        "mode": "streaming",
        "auto_retry_w_error": {
            "status": 0,
            "number_max_retry": 10,
            "retry_interval_sec": 3600
        },
        "schedule": {
            "type": "interval",
            "config": {
                "hour": "16:49",
                "type": "day",
                "type_select_day_in_month": "flex_day",
                "values": [
                    "first_of_month"
                ]
            }
        }
    }
}

"""
# ---------------- RequestBodyUpdateConnectDataInApi  ------------------------
"""
@api  None                  RequestBodyUpdateConnectDataInApi
@apiGroup Base
@apiVersion 1.0.0
@apiName RequestBodyUpdateConnectDataInApi
@apiParam   (BODY:) {string}                            object                                      Đối tượng xử lý dữ liệu
@apiParam   (BODY:) {string}                            object_attribute                            Thuộc tính của đối tượng. Trong trường hợp k có attribute cần truyền "".
@apiParam   (BODY:) {bool}                              is_trust_source                             Là nguồn ghi nhận đáng tin cậy.
@apiParam   (BODY:) {Array}                             list_field_verify                           Danh sách field verfiy
@apiParam   (BODY:) {Object}                            schema_json_upload                          Dữ liệu file json khách hàng upload
@apiParam   (BODY:) {Array}                              [config_data_default_of_primary_object]               Cấu hình dữ liệu mặc định của đối tượng chính
@apiParam   (BODY:) {String}                             [config_data_default_of_primary_object.field_key]     Field key
                                                                                                             <ul>
                                                                                                                <li>event_key :: <code>Định danh event của attribute dynamic event</code></li>
                                                                                                                <li>product_line_id :: <code>Định danh dòng sản phẩm của attribute product holding</code></li>
                                                                                                             </ul>
@apiParam   (BODY:) {String}                             [config_data_default_of_primary_object.value]         Dữ liệu của field
@apiParam   (BODY:) {Boolean}                            [config_data_default_of_primary_object.is_personalization]         Field này có cá nhân hoá không?
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization]         Config field cá nhân hoá
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization.field_key]         Field key cá nhân hoá
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization.value_replace]         Giá trị replace
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization.object]         Đối tượng
@apiParam   (BODY:) {object}                            config_mapping_data                                     Mapping trường thông tin.
@apiParam   (config_mapping_data:) {object}             config_mapping_field_with_fe                            Thông tin mapping field của FE.
@apiParam   (config_mapping_data:) {array}              fields                                                  Danh sách field mapping
@apiParam   (config_mapping_data:) {string}             fields.field_source                                     Field đến từ ngồn
@apiParam   (config_mapping_data:) {string}             fields.field_source_type                                Kiểu dữ liệu của field đến từ nguồn
@apiParam   (config_mapping_data:) {string}             fields.field_target                                     Field tương ứng vào hệ thống
@apiParam   (config_mapping_data:) {string}             fields.object                                           Đối tượng được mapping 
@apiParam   (config_mapping_data:) {string}             fields.value_type=record                              Kiểu ghi nhận giá trị
                                                                                                                <ul>
                                                                                                                    <li>fixed: Giá trị kiểu fix cứng</li>
                                                                                                                    <li>record: Ghi nhận giá trị từ việc đồng bộ dữ liệu</li>
                                                                                                                </ul>
@apiParam   (config_mapping_data:) {string}             [fields.value_by_type_fixed]                            Giá trị theo kiểu giá trị ghi nhận
@apiParam   (config_mapping_data:) {bool}               [fields.required]                                 Bắt buộc hay không?
@apiParam   (config_mapping_data:) {int}                fields.field_property                           Kiểu dữ liệu
@apiParam   (config_mapping_data:) {int}                [fields.original_property]                      Kiểu dữ liệu. Áp dụng với những field đặc biệt của <code>Profile</code>.
@apiParam   (config_mapping_data:) {string}               [fields.format]                                   Data format với dạng datetime
@apiParam   (config_mapping_data:) {string}               [fields.format_value]                             Kiểu dữ liệu là số thì cần điền để xác định
@apiParam   (config_mapping_data:) {string}               fields.display_type                           Kiểu hiển thị dữ liệu
                                                                                                        <li>
                                                                                                            <ul><code>single_line: Single Line</code></ul>
                                                                                                            <ul><code>multi_line: Multi Line</code></ul>
                                                                                                            <ul><code>dropdown_single_line: Dropdown Single Line</code></ul>
                                                                                                            <ul><code>dropdown_multi_line: Dropdown Multi Line</code></ul>
                                                                                                            <ul><code>radio: Radio</code></ul>
                                                                                                            <ul><code>checkbox: Checkbox</code></ul>
                                                                                                            <ul><code>date_picker: Date Picker</code></ul>
                                                                                                        </li>
@apiParam   (config_mapping_data:) {string}             fields.action                                   Action khi thao tác với dữ liệu
                                                                                                          <ul>
                                                                                                            <li>add: Thêm</li>
                                                                                                            <li>overwrite: Ghi đè</li>
                                                                                                            <li>overwrite_and_ignore_value_null: Ghi đè và bỏ quá giá trị null</li>
                                                                                                          </ul>
@apiParam   (BODY:) {object}                             config_rule_unification                       Quy tắc đồng bộ dữ liệu. <code>Trong trường hợp nếu không phải trust_source thì sẽ ghi nhận dữ liệu như rule ở quy tắc đồng bộ dữ liệu</code>
@apiParam   (config_rule_unification:)  {object}         config_rule_unification.data_recording_rules                       Cấu hình rule ghi nhận dữ liệu
@apiParam   (config_rule_unification:)  {array}          config_rule_unification.data_recording_rules.operators             Thông tin cấu hình rule ghi nhận dữ liệu: <a>https://dev.mobio.vn/docs/profiling/#api-Unification</a>
@apiParam   (config_rule_unification:)  {object}         config_rule_unification.data_update_rules                          Cấu hình rule cập nhật dữ liệu
@apiParam   (config_rule_unification:)  {array}          config_rule_unification.data_update_rules.operators                Thông tin cấu hình rule cập nhật dữ liệu: <a>https://dev.mobio.vn/docs/profiling/#api-Unification</a>     
@apiParam   (config_rule_unification:)  {Object}          [consent]                Thông tin consent
@apiParam   (config_rule_unification:) {String=Có,Không}       [consent.mkt_consent]             Marketing Consent.
@apiParam   (config_rule_unification:) {String=Có,Không}       [consent.analytics_consent]       Analytics Consent.
@apiParam   (config_rule_unification:) {String=Có,Không}       [consent.tracking_consent]        Tracking Consent.


@apiParam   (BODY:) {Array}                             [config_rule_unification_secondary_object]                       Quy tắc đồng bộ dữ liệu của đối tượng phụ.



@apiParam   (config_rule_unification_secondary_object:)  {object}         [config_rule_unification_secondary_object.data_update_rules]                          Cấu hình rule cập nhật dữ liệu
@apiParam   (config_rule_unification_secondary_object:)  {array}          [config_rule_unification_secondary_object.data_update_rules.operators]                Thông tin cấu hình rule cập nhật dữ liệu: <ul>
    <li>Profile: <a href=https://dev.mobio.vn/docs/profiling/#api-Unification>tại đây</a></li>
</ul>
@apiParam   (config_rule_unification_secondary_object:)  {Object}          [consent]                Thông tin consent
@apiParam   (config_rule_unification_secondary_object:) {String=Có,Không}       [consent.mkt_consent]             Marketing Consent.
@apiParam   (config_rule_unification_secondary_object:) {String=Có,Không}       [consent.analytics_consent]       Analytics Consent.
@apiParam   (config_rule_unification_secondary_object:) {String=Có,Không}       [consent.tracking_consent]        Tracking Consent.
@apiParam   (config_rule_unification_secondary_object:)  {string}         [config_rule_unification_secondary_object.secondary_object]                          Tên đối tượng phụ

@apiParam   (BODY:) {Bool}                       is_notification                                         Có bật cấu hình thông báo hay không?
@apiParam   (BODY:) {Array}                      contact_info                                            Thông tin người liên lạc khi gặp sự cố
@apiParam   (BODY:) {Int}                        max_payload_size                                        Giới hạn paylod
@apiParam   (BODY:) {Int}                        max_record_request                                      Giới hạn số lượng bản ghi request
@apiParam   (contact_info:) {string=email,account_id}    contact_info.type                                       Kiểu liên lạc
                                                                                                                <ul>
                                                                                                                    <li>email :: là type email người dùng nhập</li>
                                                                                                                    <li>account_id :: là type account người dùng chọn từ hệ thống</li>
                                                                                                                </ul>
@apiParam   (contact_info:) {array}                      contact_info.values                                     Danh sách thông tin cần liên lạc     

@apiParamExample {json} Body
{
    "object": "deal",
    "object_attribute": "",
    "is_trust_source": true,
    "config_data_default_of_primary_object": [
        {
            "field_key": "event_key",
            "value": "field_2123333",
            "is_personalization": false,
        }
    ],
    "list_field_verify": [
        "cif",
        "customer_id"
    ],
    "schema_json_upload": {
        "schema": {
            "type": "object",
            "properties": {
                "id": {}
        }
    },
    "config_mapping_data": {
        "config_mapping_field_with_fe": {},
        "fields": [
            {
                "field_source": "id",
                "field_target": "customer_id",
                "object": "profiles",
                "value_type": "record",
                "required": true,
                "action": "add"
            },
            {
                "field_source": "name",
                "field_target": "name",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "phone",
                "field_target": "primary_phone",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "email",
                "field_target": "primary_email",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "cif",
                "field_target": "cif",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            }
        ]
    },
    "config_rule_unification": {
            "data_recording_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "primary_phone": {
                                "match_type": "exact_normalized",
                                "normalized_type": "phone"
                            }
                        },
                        "priority": 1
                    }
                ]
            },
            "data_update_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "primary_phone": {
                                "match_type": "exact_normalized",
                                "normalized_type": "phone"
                            }
                        },
                        "priority": 1
                    }
                ]
            },
        "consent": {
            "analytics_consent": "Có",
            "tracking_consent": "Có",
            "mkt_consent": "Có"
        }
    },
    "config_rule_unification_secondary_object": [
        {
            "data_recording_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "primary_phone": {
                                "match_type": "exact_normalized",
                                "normalized_type": "phone"
                            }
                        },
                        "priority": 1
                    }
                ]
            },
            "secondary_object": "profiles",
            "consent": {
                "analytics_consent": "Có",
                "tracking_consent": "Có",
                "mkt_consent": "Có"
            }
        }
    ],
    "config_sync_calendar": {
        "mode": "streaming",
        "auto_retry_w_error": {
            "status": 0,
            "number_max_retry": 10,
            "retry_interval_sec": 3600
        },
        "schedule": {
            "type": "interval",
            "config": {
                "hour": "16:49",
                "type": "day",
                "type_select_day_in_month": "flex_day",
                "values": [
                    "first_of_month"
                ]
            }
        }
    },
    "is_notification": false,
    "contact_info": [
        {
            "type": "email",
            "values": ["<EMAIL>"],
        },
        {
            "type": "account_id",
            "values": ["**********"]
        }
    ],
    "max_payload_size": 3 * 1024,
    "max_record_request": 200
}

"""
# ---------------- RequestBodyUpdateConnectDataInContactForm  ------------------------
"""
@api  None                  RequestBodyUpdateConnectDataInContactForm
@apiGroup Base
@apiVersion 1.0.0
@apiName RequestBodyUpdateConnectDataInContactForm
@apiParam   (BODY:) {string}                            object                                      Đối tượng xử lý dữ liệu
@apiParam   (BODY:) {string}                            object_attribute                            Thuộc tính của đối tượng. Trong trường hợp k có attribute cần truyền "".
@apiParam   (BODY:) {bool}                              is_trust_source                             Là nguồn ghi nhận đáng tin cậy.
@apiParam   (BODY:) {Array}                             list_field_verify                           Danh sách field verfiy
@apiParam   (BODY:) {Array}                              config_data_default_of_primary_object               Cấu hình dữ liệu mặc định của đối tượng chính
@apiParam   (BODY:) {String}                             config_data_default_of_primary_object.field_key     Field key
@apiParam   (BODY:) {String}                             config_data_default_of_primary_object.value         Dữ liệu của field
@apiParam   (BODY:) {Boolean}                            config_data_default_of_primary_object.is_personalization         Field này có cá nhân hoá không?
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization]         Config field cá nhân hoá
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization.field_key]         Field key cá nhân hoá
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization.value_replace]         Giá trị replace
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization.object]         Đối tượng
@apiParam   (BODY:) {object}                            config_mapping_data                                     Mapping trường thông tin.
@apiParam   (config_mapping_data:) {array}              fields                                                  Danh sách field mapping
@apiParam   (config_mapping_data:) {string}             fields.field_source                                     Field đến từ ngồn
@apiParam   (config_mapping_data:) {string}             fields.field_source_type                                Kiểu dữ liệu của field đến từ nguồn
@apiParam   (config_mapping_data:) {string}             fields.field_target                                     Field tương ứng vào hệ thống
@apiParam   (config_mapping_data:) {string}             fields.object                                           Đối tượng được mapping 
@apiParam   (config_mapping_data:) {string}             fields.value_type=record                              Kiểu ghi nhận giá trị
                                                                                                                <ul>
                                                                                                                    <li>fixed: Giá trị kiểu fix cứng</li>
                                                                                                                    <li>record: Ghi nhận giá trị từ việc đồng bộ dữ liệu</li>
                                                                                                                </ul>
@apiParam   (config_mapping_data:) {string}             [fields.value_by_type_fixed]                            Giá trị theo kiểu giá trị ghi nhận
@apiParam   (config_mapping_data:) {bool}               [fields.required]                                 Bắt buộc hay không?
@apiParam   (config_mapping_data:) {int}                fields.field_property                           Kiểu dữ liệu
@apiParam   (config_mapping_data:) {int}                [fields.original_property]                      Kiểu dữ liệu. Áp dụng với những field đặc biệt của <code>Profile</code>.
@apiParam   (config_mapping_data:) {string}               [fields.format]                                   Data format với dạng datetime
@apiParam   (config_mapping_data:) {string}               [fields.format_value]                             Kiểu dữ liệu là số thì cần điền để xác định
@apiParam   (config_mapping_data:) {string}               fields.display_type                           Kiểu hiển thị dữ liệu
                                                                                                        <li>
                                                                                                            <ul><code>single_line: Single Line</code></ul>
                                                                                                            <ul><code>multi_line: Multi Line</code></ul>
                                                                                                            <ul><code>dropdown_single_line: Dropdown Single Line</code></ul>
                                                                                                            <ul><code>dropdown_multi_line: Dropdown Multi Line</code></ul>
                                                                                                            <ul><code>radio: Radio</code></ul>
                                                                                                            <ul><code>checkbox: Checkbox</code></ul>
                                                                                                            <ul><code>date_picker: Date Picker</code></ul>
                                                                                                        </li>
@apiParam   (config_mapping_data:) {string}             fields.action                                   Action khi thao tác với dữ liệu
                                                                                                          <ul>
                                                                                                            <li>add: Thêm</li>
                                                                                                            <li>overwrite: Ghi đè</li>
                                                                                                            <li>overwrite_and_ignore_value_null: Ghi đè và bỏ quá giá trị null</li>
                                                                                                          </ul>
@apiParam   (BODY:) {object}                             config_rule_unification                       Quy tắc đồng bộ dữ liệu. <code>Trong trường hợp nếu không phải trust_source thì sẽ ghi nhận dữ liệu như rule ở quy tắc đồng bộ dữ liệu</code>
@apiParam   (config_rule_unification:)  {object}         config_rule_unification.data_recording_rules                       Cấu hình rule ghi nhận dữ liệu
@apiParam   (config_rule_unification:)  {array}          config_rule_unification.data_recording_rules.operators             Thông tin cấu hình rule ghi nhận dữ liệu: <a>https://dev.mobio.vn/docs/profiling/#api-Unification</a>
@apiParam   (config_rule_unification:)  {object}         config_rule_unification.data_update_rules                          Cấu hình rule cập nhật dữ liệu
@apiParam   (config_rule_unification:)  {array}          config_rule_unification.data_update_rules.operators                Thông tin cấu hình rule cập nhật dữ liệu: <a>https://dev.mobio.vn/docs/profiling/#api-Unification</a>     
@apiParam   (config_rule_unification:)  {Object}          [consent]                Thông tin consent
@apiParam   (config_rule_unification:) {String=Có,Không}       [consent.mkt_consent]             Marketing Consent.
@apiParam   (config_rule_unification:) {String=Có,Không}       [consent.analytics_consent]       Analytics Consent.
@apiParam   (config_rule_unification:) {String=Có,Không}       [consent.tracking_consent]        Tracking Consent.


@apiParam   (BODY:) {Array}                             [config_rule_unification_secondary_object]                       Quy tắc đồng bộ dữ liệu của đối tượng phụ.



@apiParam   (config_rule_unification_secondary_object:)  {object}         [config_rule_unification_secondary_object.data_update_rules]                          Cấu hình rule cập nhật dữ liệu
@apiParam   (config_rule_unification_secondary_object:)  {array}          [config_rule_unification_secondary_object.data_update_rules.operators]                Thông tin cấu hình rule cập nhật dữ liệu: <ul>
    <li>Profile: <a href=https://dev.mobio.vn/docs/profiling/#api-Unification>tại đây</a></li>
</ul>
@apiParam   (config_rule_unification_secondary_object:)  {Object}          [consent]                Thông tin consent
@apiParam   (config_rule_unification_secondary_object:) {String=Có,Không}       [consent.mkt_consent]             Marketing Consent.
@apiParam   (config_rule_unification_secondary_object:) {String=Có,Không}       [consent.analytics_consent]       Analytics Consent.
@apiParam   (config_rule_unification_secondary_object:) {String=Có,Không}       [consent.tracking_consent]        Tracking Consent.
@apiParam   (config_rule_unification_secondary_object:)  {string}         [config_rule_unification_secondary_object.secondary_object]                          Tên đối tượng phụ

@apiParam   (BODY:) {Bool}                       is_notification                                         Có bật cấu hình thông báo hay không?
@apiParam   (BODY:) {Array}                      contact_info                                            Thông tin người liên lạc khi gặp sự cố
@apiParam   (BODY:) {Int}                        max_payload_size                                        Giới hạn paylod
@apiParam   (BODY:) {Int}                        max_record_request                                      Giới hạn số lượng bản ghi request
@apiParam   (contact_info:) {string=email,account_id}    contact_info.type                                       Kiểu liên lạc
                                                                                                                <ul>
                                                                                                                    <li>email :: là type email người dùng nhập</li>
                                                                                                                    <li>account_id :: là type account người dùng chọn từ hệ thống</li>
                                                                                                                </ul>
@apiParam   (contact_info:) {array}                      contact_info.values                                     Danh sách thông tin cần liên lạc     

@apiParamExample {json} Body
{
    "object": "deal",
    "is_trust_source": true,
    "config_data_default_of_primary_object": [
        {
            "field_key": "name",
            "value": "1231231",
            "is_personalization": false,
            "config_field_personalization": [
                {
                    "field_key": "",
                    "value_replace": "",
                    "object": ""
                }
            ]
        }    
    ],
    "list_field_verify": [
        "cif",
        "customer_id"
    ],
    "schema_json_upload": {
        "schema": {
            "type": "object",
            "properties": {
                "id": {}
        }
    },
    "config_mapping_data": {
        "config_mapping_field_with_fe": {},
        "fields": [
            {
                "field_source": "id",
                "field_target": "customer_id",
                "object": "profiles",
                "value_type": "record",
                "required": true,
                "action": "add"
            },
            {
                "field_source": "name",
                "field_target": "name",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "phone",
                "field_target": "primary_phone",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "email",
                "field_target": "primary_email",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "cif",
                "field_target": "cif",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            }
        ]
    },
    "config_rule_unification": {
            "data_recording_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "primary_phone": {
                                "match_type": "exact_normalized",
                                "normalized_type": "phone"
                            }
                        },
                        "priority": 1
                    }
                ]
            },
            "data_update_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "primary_phone": {
                                "match_type": "exact_normalized",
                                "normalized_type": "phone"
                            }
                        },
                        "priority": 1
                    }
                ]
            },
        "consent": {
            "analytics_consent": "Có",
            "tracking_consent": "Có",
            "mkt_consent": "Có"
        }
    },
    "config_rule_unification_secondary_object": [
        {
            "data_recording_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "primary_phone": {
                                "match_type": "exact_normalized",
                                "normalized_type": "phone"
                            }
                        },
                        "priority": 1
                    }
                ]
            },
            "secondary_object": "profiles",
            "consent": {
                "analytics_consent": "Có",
                "tracking_consent": "Có",
                "mkt_consent": "Có"
            }
        }
    ],
    "config_sync_calendar": {
        "mode": "streaming",
        "auto_retry_w_error": {
            "status": 0,
            "number_max_retry": 10,
            "retry_interval_sec": 3600
        },
        "schedule": {
            "type": "interval",
            "config": {
                "hour": "16:49",
                "type": "day",
                "type_select_day_in_month": "flex_day",
                "values": [
                    "first_of_month"
                ]
            }
        }
    },
    "is_notification": false,
    "contact_info": [
        {
            "type": "email",
            "values": ["<EMAIL>"],
        },
        {
            "type": "account_id",
            "values": ["**********"]
        }
    ],
    "max_payload_size": 3 * 1024,
    "max_record_request": 200
}

"""

# ---------------- RequestBodyUpdateConnectDataOutWebhooks  ------------------------
"""
@api  None                  RequestBodyUpdateConnectDataOutWebhooks
@apiGroup Base
@apiVersion 1.0.0
@apiName RequestBodyUpdateConnectDataOutWebhooks
@apiParam   (BODY:) {Array}                      config_information_out                                  Cấu hình thông tin dữ liệu out
@apiParam   (BODY:) {Bool}                       is_notification                                         Có bật cấu hình thông báo hay không?
@apiParam   (BODY:) {Array}                      contact_info                                            Thông tin người liên lạc khi gặp sự cố
@apiParam   (BODY:) {int=10}                     auto_connection_check_interval                          Số phút/ lần
@apiParam   (BODY:) {int=6}                      max_notification_count                                  Số lần thông báo tối đa
@apiParam   (BODY:) {Object}                     sync_limit                                              Giới hạn đồng bộ
@apiParam   (BODY:) {Int}                        sync_limit.http_connection_timeout                      Thời gian chờ tối đa để kết nối giữa hệ thống và Webhooks. Đơn vị tính theo miliseconds


@apiParam   (config_information_out:) {string}          module                                                  Module được cấu hình
@apiParam   (config_information_out:) {array}           event_keys                                              Danh sách event được cấu hình <code>on</code>.

@apiParam   (contact_info:) {string=email,account_id}    contact_info.type                                       Kiểu liên lạc
                                                                                                                <ul>
                                                                                                                    <li>email :: là type email người dùng nhập</li>
                                                                                                                    <li>account_id :: là type account người dùng chọn từ hệ thống</li>
                                                                                                                </ul>
@apiParam   (contact_info:) {array}                      contact_info.values                                     Danh sách thông tin cần liên lạc      

            



@apiParamExample {json} Body
{
    "config_information_out": [
        {
            "module": "note",
            "event_keys": ["note_created"],
        }
    ],
    "contact_info": [
        {
            "type": "email",
            "values": ["<EMAIL>"],
        },
        {
            "type": "account_id",
            "values": ["**********"]
        }
    ],
    "auto_connection_check_interval": 10,
    "max_notification_count": 6,
    "sync_limit": {
        "http_connection_timeout": 30000
    }
}
"""

# ---------------- RequestBodyUpdateConnectDataOutGoogleSheet  ------------------------
"""
@api  None                  RequestBodyUpdateConnectDataOutGoogleSheet
@apiGroup Base
@apiVersion 1.0.0
@apiName RequestBodyUpdateConnectDataOutGoogleSheet
@apiParam   (BODY:) {Array}                      config_information_out                                  Cấu hình thông tin dữ liệu out
@apiParam   (BODY:) {Bool}                       is_notification                                         Có bật cấu hình thông báo hay không?
@apiParam   (BODY:) {Array}                      contact_info                                            Thông tin người liên lạc khi gặp sự cố
@apiParam   (BODY:) {int=10}                     auto_connection_check_interval                          Số phút/ lần
@apiParam   (BODY:) {int=6}                      max_notification_count                                  Số lần thông báo tối đa
@apiParam   (BODY:) {Object}                     sync_limit                                              Giới hạn đồng bộ
@apiParam   (BODY:) {Int}                        sync_limit.http_connection_timeout                      Thời gian chờ tối đa để kết nối giữa hệ thống và Webhooks. Đơn vị tính theo miliseconds


@apiParam   (config_information_out:) {string}          object_type                                      Đối tượng dùng để out dữ liệu
@apiParam   (config_information_out:) {string}          object_id                                        Định danh của đối tượng
@apiParam   (config_information_out:) {array}           mapping_fields                                   Cấu hình mapping dữ liệu được ủn đi
@apiParam   (config_information_out:) {string}          mapping_fields.field_source                      Field key phát sinh từ nguồn
@apiParam   (config_information_out:) {string}          mapping_fields.field_target                      Field key ghi nhận dữ liệu (Với gg sheet thì tương ứng <code>cột</code>)


@apiParam   (contact_info:) {string=email,account_id}    contact_info.type                                      Kiểu liên lạc
                                                                                                                <ul>
                                                                                                                    <li>email :: là type email người dùng nhập</li>
                                                                                                                    <li>account_id :: là type account người dùng chọn từ hệ thống</li>
                                                                                                                </ul>
@apiParam   (contact_info:) {array}                      contact_info.values                                     Danh sách thông tin cần liên lạc      

            



@apiParamExample {json} Body
{
    "config_information_out": [
        {
            "object_type": "contact_form",
            "object_id": "**********",
            "mapping_fields": [
                {
                    "field_source": "title",
                    "field_target": "A1"
                },
                {
                    "field_source": "content",
                    "field_target": "B1"
                }
            ]
        }
    ],
    "contact_info": [
        {
            "type": "email",
            "values": ["<EMAIL>"],
        },
        {
            "type": "account_id",
            "values": ["**********"]
        }
    ],
    "auto_connection_check_interval": 10,
    "max_notification_count": 6,
    "sync_limit": {
        "http_connection_timeout": 30000
    }
}
"""

# ---------------- ResponseSuccessConnectDataInPostgres  ------------------------
"""
@api  None                  ResponseSuccessConnectDataInPostgres
@apiGroup Base
@apiVersion 1.0.0
@apiName ResponseSuccessConnectDataInPostgres
@apiSuccess     {boolean}         id                                    ID của connectors
@apiSuccess     {boolean}         is_trust_source                       Nguồn đáng tin cậy hay không?
@apiSuccess     {string}          object                                Đối tượng xử lý
@apiSuccess     {string}          object_attribute                      Thuộc tính của đối tượng
@apiSuccess     {string}          name                                Tên connector
@apiSuccess     {string}          description                                Mô tả connector
@apiSuccess     {string}          source_key                                Source key cần kết nối
@apiSuccess     {string}          source_type                                Loại
@apiSuccess     {boolean}         is_type_sync_manunal                       Kiểu đồng bộ dữ liệu thủ công
@apiSuccess     {string}          status_sync                           Trạng thái đồng bộ dữ liệu
@apiSuccess     {string}          created_by                                 Thông tin nhân viên tạo
@apiSuccess     {string}          updated_by                                 Thông tin nhân viên cập nhật
@apiSuccess     {string}          created_time                               Thời gian tạo
@apiSuccess     {string}          updated_time                               Thời gian cập nhật          

@apiSuccessExample {json} Response 
{
    {
        "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "created_time": "2024-04-10T04:15Z",
        "id": 5,
        "is_type_sync_manunal": True,
        "description": "Test",
        "name": "Connector 1",
        "source_key": "postgres",
        "source_type": "databases",
        "is_trust_source": true,
        "object": "profiles",
        "status_sync": null,
        "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "updated_time": "2024-04-10T10:42Z"
    }
}
"""
# ---------------- ResponseSuccessConnectDataInApi  ------------------------
"""
@api  None                  ResponseSuccessConnectDataInApi
@apiGroup Base
@apiVersion 1.0.0
@apiName ResponseSuccessConnectDataInApi
@apiSuccess     {boolean}         id                                    ID của connectors
@apiSuccess     {boolean}         is_trust_source                       Nguồn đáng tin cậy hay không?
@apiSuccess     {string}          object                                Đối tượng xử lý
@apiSuccess     {string}          object_attribute                      Thuộc tính của đối tượng
@apiSuccess     {string}          name                                Tên connector
@apiSuccess     {string}          description                                Mô tả connector
@apiSuccess     {string}          source_key                                Source key cần kết nối
@apiSuccess     {string}          source_type                                Loại
@apiSuccess     {boolean}         is_type_sync_manunal                       Kiểu đồng bộ dữ liệu thủ công
@apiSuccess     {string}          status_sync                               Trạng thái đồng bộ dữ liệu
                                                                            <ul>
                                                                                <li><code>running</code> đang đồng bộ</code></li>
                                                                                <li><code>done</code> hoàn thành</code></li>
                                                                                <li><code>stopped</code> bị dừng đồng bộ dữ liệu</code></li>
                                                                                <li><code>sync_error</code> lỗi đồng bộ dữ liệu</code></li>
                                                                                <li><code>not_data_sync</code> không có dữ liệu đồng bộ</code></li>
                                                                            </ul>
@apiSuccess     {string}          created_by                                 Thông tin nhân viên tạo
@apiSuccess     {string}          updated_by                                 Thông tin nhân viên cập nhật
@apiSuccess     {string}          created_time                               Thời gian tạo
@apiSuccess     {string}          updated_time                               Thời gian cập nhật          

@apiSuccessExample {json} Response 
{
    {
        "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "created_time": "2024-04-10T04:15Z",
        "id": 5,
        "is_type_sync_manunal": false,
        "description": "Test",
        "name": "Connector 1",
        "source_key": "api",
        "source_type": "server",
        "is_trust_source": true,
        "object": "profiles",
        "object_atrribute": "attribute",
        "status_sync": null,
        "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "updated_time": "2024-04-10T10:42Z"
    }
}
"""

# ---------------- ResponseSuccessConnectDataOutWebhooks  ------------------------
"""
@api  None                  ResponseSuccessConnectDataOutWebhooks
@apiGroup Base
@apiVersion 1.0.0
@apiName ResponseSuccessConnectDataOutWebhooks
@apiSuccess     {boolean}         id                                    ID của connectors
@apiSuccess     {string}          name                                Tên connector
@apiSuccess     {string}          description                                Mô tả connector
@apiSuccess     {string}          source_key                                Source key cần kết nối
@apiSuccess     {Array}           object_configs                            Danh sách đối tượng cấu hình
@apiSuccess     {string}          source_type                                Loại
@apiSuccess     {string}          status_sync                           Trạng thái đồng bộ dữ liệu
@apiSuccess     {string}          created_by                                 Thông tin nhân viên tạo
@apiSuccess     {string}          updated_by                                 Thông tin nhân viên cập nhật
@apiSuccess     {string}          created_time                               Thời gian tạo
@apiSuccess     {string}          updated_time                               Thời gian cập nhật          

@apiSuccessExample {json} Response 
{
    {
        "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "created_time": "2024-04-10T04:15Z",
        "id": 5,
        "description": "Test",
        "name": "Connector 1",
        "source_key": "webhooks",
        "source_type": "server",
        "object_configs": ["profiles", "note"],
        "status_sync": null,
        "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "updated_time": "2024-04-10T10:42Z"
    }
}
"""

# ---------------- RequestBodyUpdateConnectDataInDb2  ------------------------
"""
@api  None                  RequestBodyUpdateConnectDataInDb2
@apiGroup Base
@apiVersion 1.0.0
@apiName RequestBodyUpdateConnectDataInDb2
@apiParam   (BODY:) {string}                            object                                      Đối tượng xử lý dữ liệu
@apiParam   (BODY:) {string}                            object_attribute                            Thuộc tính của đối tượng. Trong trường hợp k có attribute cần truyền "".
@apiParam   (BODY:) {bool}                              is_trust_source                             Là nguồn ghi nhận đáng tin cậy.
@apiParam   (BODY:) {Array}                             list_field_verify                           Danh sách field verfiy
@apiParam   (BODY:) {Array}                              [config_data_default_of_primary_object]               Cấu hình dữ liệu mặc định của đối tượng chính
@apiParam   (BODY:) {String}                             [config_data_default_of_primary_object.field_key]     Field key
                                                                                                             <ul>
                                                                                                                <li>event_key :: <code>Định danh event của attribute dynamic event</code></li>
                                                                                                                <li>product_line_id :: <code>Định danh dòng sản phẩm của attribute product holding</code></li>
                                                                                                             </ul>
@apiParam   (BODY:) {String}                             [config_data_default_of_primary_object.value]         Dữ liệu của field
@apiParam   (BODY:) {Boolean}                            [config_data_default_of_primary_object.is_personalization]         Field này có cá nhân hoá không?
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization]         Config field cá nhân hoá
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization.field_key]         Field key cá nhân hoá
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization.value_replace]         Giá trị replace
@apiParam   (BODY:) {String}                              [config_data_default_of_primary_object.config_field_personalization.object]         Đối tượng

@apiParam   (BODY:) {object}                            config_mapping_data                                     Mapping trường thông tin.
@apiParam   (config_mapping_data:) {string}             table                                                   Tên table cấu hình
@apiParam   (config_mapping_data:) {string}             schema                                                   Tên schema cấu hình  
@apiParam   (config_mapping_data:) {array}              fields                                                  Danh sách field mapping
@apiParam   (config_mapping_data:) {string}             fields.field_source                                     Field đến từ ngồn
@apiParam   (config_mapping_data:) {string}             fields.field_target                                     Field tương ứng vào hệ thống
@apiParam   (config_mapping_data:) {string}             fields.object                                           Đối tượng được mapping 
@apiParam   (config_mapping_data:) {string}             fields.value_type=record                              Kiểu ghi nhận giá trị
                                                                                                                <ul>
                                                                                                                    <li>fixed: Giá trị kiểu fix cứng</li>
                                                                                                                    <li>record: Ghi nhận giá trị từ việc đồng bộ dữ liệu</li>
                                                                                                                </ul>
@apiParam   (config_mapping_data:) {string}             [fields.value_by_type_fixed]                            Giá trị theo kiểu giá trị ghi nhận
@apiParam   (config_mapping_data:) {bool}               [fields.required]                                 Bắt buộc hay không?
@apiParam   (config_mapping_data:) {string}             fields.action                                   Action khi thao tác với dữ liệu
                                                                                                          <ul>
                                                                                                            <li>add: Thêm</li>
                                                                                                            <li>overwrite: Ghi đè</li>
                                                                                                            <li>overwrite_and_ignore_value_null: Ghi đè và bỏ quá giá trị null</li>
                                                                                                          </ul>
@apiParam   (BODY:) {object}                             config_rule_unification                       Quy tắc đồng bộ dữ liệu. <code>Trong trường hợp nếu không phải trust_source thì sẽ ghi nhận dữ liệu như rule ở quy tắc đồng bộ dữ liệu</code>
@apiParam   (config_rule_unification:)  {object}         config_rule_unification.data_recording_rules                       Cấu hình rule ghi nhận dữ liệu
@apiParam   (config_rule_unification:)  {array}          config_rule_unification.data_recording_rules.operators             Thông tin cấu hình rule ghi nhận dữ liệu: <a>https://dev.mobio.vn/docs/profiling/#api-Unification</a>
@apiParam   (config_rule_unification:)  {object}         config_rule_unification.data_update_rules                          Cấu hình rule cập nhật dữ liệu
@apiParam   (config_rule_unification:)  {array}          config_rule_unification.data_update_rules.operators                Thông tin cấu hình rule cập nhật dữ liệu: <a>https://dev.mobio.vn/docs/profiling/#api-Unification</a>     
@apiParam   (config_rule_unification:)  {Object}          [consent]                Thông tin consent
@apiParam   (config_rule_unification:) {String=Có,Không}       [consent.mkt_consent]             Marketing Consent.
@apiParam   (config_rule_unification:) {String=Có,Không}       [consent.analytics_consent]       Analytics Consent.
@apiParam   (config_rule_unification:) {String=Có,Không}       [consent.tracking_consent]        Tracking Consent.
@apiParam   (BODY:) {Array}                             [config_rule_unification_secondary_object]                       Quy tắc đồng bộ dữ liệu của đối tượng phụ.
@apiParam   (config_rule_unification_secondary_object:)  {object}         [config_rule_unification_secondary_object.data_recording_rules]                       Cấu hình rule ghi nhận dữ liệu
@apiParam   (config_rule_unification_secondary_object:)  {array}          [config_rule_unification_secondary_object.data_recording_rules.operators]             Thông tin cấu hình rule ghi nhận dữ liệu: <ul>
    <li>Profile: <a href=https://dev.mobio.vn/docs/profiling/#api-Unification>tại đây</a></li>
</ul>
@apiParam   (config_rule_unification_secondary_object:)  {Object}          [consent]                Thông tin consent
@apiParam   (config_rule_unification_secondary_object:) {String=Có,Không}       [consent.mkt_consent]             Marketing Consent.
@apiParam   (config_rule_unification_secondary_object:) {String=Có,Không}       [consent.analytics_consent]       Analytics Consent.
@apiParam   (config_rule_unification_secondary_object:) {String=Có,Không}       [consent.tracking_consent]        Tracking Consent.
@apiParam   (config_rule_unification_secondary_object:)  {string}               [secondary_object]                          Tên đối tượng phụ

@apiParamExample {json} Body
{
    "object": "profiles",
    "object_attribute": "dynamic_event",
    "is_trust_source": true,
    "config_data_default_of_primary_object": [
        {
            "field_key": "event_key",
            "value": "field_2123333",
            "is_personalization": false,
        }
    ],
    "list_field_verify": [
        "cif",
        "customer_id"
    ],
    "config_mapping_data": {
        "table": "profile",
        "schema": "TEST_SAMPLE",
        "fields": [
            {
                "field_source": "id",
                "field_target": "customer_id",
                "object": "profiles",
                "value_type": "record",
                "required": true,
                "action": "add"
            },
            {
                "field_source": "name",
                "field_target": "name",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "phone",
                "field_target": "primary_phone",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "email",
                "field_target": "primary_email",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "cif",
                "field_target": "cif",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            }
        ]
    },
    "config_rule_unification": {
            "data_recording_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "primary_phone": {
                                "match_type": "exact_normalized",
                                "normalized_type": "phone"
                            }
                        },
                        "priority": 1
                    }
                ]
            },
            "data_update_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "primary_phone": {
                                "match_type": "exact_normalized",
                                "normalized_type": "phone"
                            }
                        },
                        "priority": 1
                    }
                ]
            },
        "consent": {
            "analytics_consent": "Có",
            "tracking_consent": "Có",
            "mkt_consent": "Có"
        }
    },
    "config_sync_calendar": {
        "mode": "streaming",
        "auto_retry_w_error": {
            "status": 0,
            "number_max_retry": 10,
            "retry_interval_sec": 3600
        },
        "schedule": {
            "type": "interval",
            "config": {
                "hour": "16:49",
                "type": "day",
                "type_select_day_in_month": "flex_day",
                "values": [
                    "first_of_month"
                ]
            }
        }
    }
}

"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 27/06/2024
"""


# ---------- L<PERSON><PERSON> danh sách connector by Event key -----------
"""
@api {GET} {domain}/market-place/internal/api/v1.0/data-flow/connectors/get-by-event-key                 <PERSON><PERSON><PERSON> sách connector by Event key
@apiGroup InternalConnectors
@apiVersion 1.0.0
@apiName GetConnectorsByEventKey

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (QUERY:)      {string}       event_key                                  Event key cần l<PERSON>y danh sách connector

@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          M<PERSON> phản hồi
@apiSuccess     {Array}             data                          D<PERSON> liệu trả về
@apiSuccess     {string}            data.app_id                   Đ<PERSON><PERSON> danh của app
@apiSuccess     {int}               data.connector_id             <PERSON><PERSON><PERSON> danh của connector

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "app_id": "fb1af05a-ba67-11ee-abef-8d27b4c9df8b",
            "connector_id": 88
        }
    ]
}
"""

# ---------- Lấy chi tiết connector cho Data Out -----------
"""
@api {GET} {domain}/market-place/internal/api/v1.0/data-flow/connectors/<connector_id>/data-out                 Lấy chi tiết connector cho Data Out
@apiGroup InternalConnectors
@apiVersion 1.0.0
@apiName DetailConnectorToDataOut

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Object}            data                          Dữ liệu trả về
@apiSuccess     {string}            data.app_id                   Định danh của app
@apiSuccess     {int}               data.id                       <code>ID</code> của connector
@apiSuccess     {string}            data.name                     Tên của connector
@apiSuccess     {string}            data.url                      Url webhooks
@apiSuccess     {string}            data.merchant_id              Merchant ID
@apiSuccess     {Array}             data.headers                  Thông tin headers
@apiSuccess     {String}            data.app_secret               Secret key của APP
@apiSuccess     {String}            data.app_name                 Tên của APP


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "app_id": "fb1af05a-ba67-11ee-abef-8d27b4c9df8b",
        "id": 88,
        "name": "Connector 88",
        "url": "https://api.mobio.vn/market-place/internal/api/v1.0/data-flow/connectors/88/data-out",
        "merchant_id": "123456789",
        "headers": [
            {
                "key": "mac",
                "value": "sha256(app_id + data_string + app_secret)"
            }
        ],
        "app_secret": "123456789",
        "app_name": "Mobio"
    }
}
"""
# ---------- Lấy chi tiết connector cho Contact Form -----------
"""
@api {POST} {domain}/market-place/internal/api/v1.0/data-flow/destination/googlesheet/detail-by-object-ids                 Lấy chi tiết connector cho Destination GoogleSheet by object_id
@apiGroup InternalConnectors
@apiVersion 1.0.0
@apiName DetailConnectorDestinationGoogleSheet

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {array}        object_ids                                     Danh sách <code>ID</code> đối tượng
@apiParam   (BODY:) {string}       object_type                                   Kiểu đối tượng
                                                                                 <ul>
                                                                                    <li>contact_form: Đối tươnhj form</li>
                                                                                 </ul>

@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Array}             data                          Dữ liệu trả về
@apiSuccess     {String}            data.object_id                  Định danh của đối tượng
@apiSuccess     {Int}               data.connector_id             Định danh của connector
@apiSuccess     {Object}            data.integration_account      Thông tin tài khoản tích hợp
@apiSuccess     {String}            data.integration_account.token Token access
@apiSuccess     {String}            data.integration_account.refresh_token Refresh token
@apiSuccess     {String}            data.integration_account.token_uri     Token uri
@apiSuccess     {String}            data.integration_account.client_id     Client ID
@apiSuccess     {String}            data.integration_account.client_secret     Client Secret
@apiSuccess     {Array}             data.integration_account.scopes     Danh sách scopes
@apiSuccess     {String}            data.integration_account.expiry     Expiry
@apiSuccess     {String}            data.integration_account.user_id    Định danh User
@apiSuccess     {String}            data.integration_account.email      User email
@apiSuccess     {String}            data.integration_account.name       Tên User
@apiSuccess     {Object}            data.config_connect      Thông kết nối
@apiSuccess     {String}            data.config_connect.spreadsheet_id               <code>ID</code> của bảng tính.
@apiSuccess     {String}            data.config_connect.sheet_id                     <code>ID</code> của trang tính
@apiSuccess     {Object}            data.config_information_out                                  Cấu hình thông tin dữ liệu out
@apiSuccess     {string}            data.config_information_out.object_type                                      Đối tượng dùng để out dữ liệu
@apiSuccess     {string}            data.config_information_out.object_id                                        Định danh của đối tượng
@apiSuccess     {array}             data.config_information_out.mapping_fields                                   Cấu hình mapping dữ liệu được ủn đi
@apiSuccess     {string}            data.config_information_out.mapping_fields.field_source                      Field key phát sinh từ nguồn
@apiSuccess     {string}            data.config_information_out.mapping_fields.field_target                      Field key ghi nhận dữ liệu (Với gg sheet thì tương ứng <code>cột</code>)
@apiSuccess     {Array}             data.config_information_out.column_list_mapping                      Danh sách cột tại thời điểm mapping

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "object_id": "",
            "connector_id": 1,
            "integration_account" : {
                "token": "******************************************************************************************************************************************************************************************************************************",
                "refresh_token": "1//0e0i2DgZhE_JpCgYIARAAGA4SNwF-L9Irap33hsgEwXVXX8J9DVVJcR2in8UJgQfMz0biSmKjUWBntdw8uklToUKuwrXJzLQu10U",
                "token_uri": "https://oauth2.googleapis.com/token",
                "client_id": "************-2amn77n50abdna56k7tggeuu9am950vo.apps.googleusercontent.com",
                "client_secret": "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",
                "scopes": [
                    "https://www.googleapis.com/auth/drive",
                    "https://www.googleapis.com/auth/drive.file",
                    "https://www.googleapis.com/auth/spreadsheets",
                    "openid",
                    "https://www.googleapis.com/auth/userinfo.profile",
                    "https://www.googleapis.com/auth/userinfo.email"
                ],
                "expiry": ISODate("2024-09-25T12:21:44.315+07:00"),
                "user_id": "112374537114792136313",
                "email": "<EMAIL>",
                "name": "Phạm Thị Thu Hà"
            },
            "config_connect": {
                "spreadsheet_id": "1uK_O5Jc5Z2tRmKE_SScXbe4uniNam-Bq63MgoHIhQ1w",
                "sheet_id": 0
            },
            "config_information_out": {
                "object_type": "contact_form",
                "object_id": "1234567890",
                "mapping_fields": [
                    {
                        "field_source": "title",
                        "field_target": "A1"
                    },
                    {
                        "field_source": "content",
                        "field_target": "B1"
                    }
                ],
                "column_list_mapping":["A1", "B1"]
            }
        }
    ]
}
"""
# ---------- Gửi thông báo -----------
"""
@api {POST} {domain}/market-place/internal/api/v1.0/data-flow/connector-send-notify                 Gửi thông báo của Connector
@apiGroup InternalConnectors
@apiVersion 1.0.0
@apiName SendNotifyOfConnector

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:)     {Int}            connector_id                       <code>ID</code> của connector
@apiParam   (BODY:)     {String}         notify_type                        Loại email cần gửi
@apiParam   (BODY:)     {Object}         data_send                          Dữ liệu cần gửi do Notify SDK quy định.


@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
}
"""

# ---------- Lấy thông tin cấu hình kết nối của connector -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/connectors/config-connect-default
@apiGroup InternalConnectors
@apiVersion 1.0.0
@apiName GetConfigConnectorDefault

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string}       app_id                                     <code>ID</code> của app

@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Object}            data                          Thông tin tham số mặc định
@apiSuccess     {String}            data.url                      Thông tin URL
@apiSuccess     {String}            data.method                   Method tương ứng với url

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "url": "https://ingest-test.mobio.vn/market-place/external/api/v1.0/bulk-data",
        "method": "POST"
    }
}
"""

# ---------- Thêm connector form -----------
"""
@api {POST} {domain}/market-place/internal/api/v1.0/data-flow/connectors/forms/actions/add            Tạo connector form
@apiGroup InternalConnectors
@apiVersion 1.0.0
@apiName AddConnectorForm

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy                                 
@apiParam       (BODY:)     {string}        object_id                               <code>ID</code> của đối tượng cần tạo
@apiParam       (BODY:)     {string}        object_name                             Tên của đối tượng cần tạo

@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Object}            data                          Thông tin dữ liệu trả về.
@apiSuccess     {String}            data.connector_id             <code>Định danh</code> connector
@apiSuccess     {String}            data.submit_url               Link submit form
@apiSuccess     {Object}            data.param_headers            Tham số trên header. Khi request url submit thì cần truyền đầy đủ những tham số này lên.
@apiSuccess     {string}            data.param_headers.key           Key của tham số
@apiSuccess     {string}            data.param_headers.value         Giá trị của tham số
@apiSuccess     {string}            data.formula_signature_request   Cách tạo chữ ký khi request submit. Value của chữ ký sẽ được truyền vào header <code>Mobio-Signature</code>. Chữ ký này sẽ được truyền vào header Mobio-Signature của request. Chữ ký được tạo bằng cách sử dụng thuật toán SHA256 với công thức: <code>SHA256("Giá trị của key Mobio-Access-Token", string(requestBody))</code>
                                                                     Example: <pre><code class="language-javascript">
                                                                                const crypto = require('crypto');<br>
                                                                                const secretKey = 'YourSecretKey';<br>
                                                                                const requestBody = JSON.stringify({<br>
                                                                                    name: 'example',<br>
                                                                                    type: 'connector',<br>
                                                                                    config: {<br>
                                                                                        field1: 'value1',<br>
                                                                                        field2: 'value2',<br>
                                                                                    }<br>
                                                                                });<br>
                                                                                const signature = crypto<br>
                                                                                .createHmac('sha256', secretKey)<br>
                                                                                .update(requestBody)<br>
                                                                                .digest('hex');<br>
                                                                                </code></pre>
                                                                     



@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "connector_id": 1,
        "submit_url": "https://ingest-test.mobio.vn/market-place/external/api/v1.0/bulk-data",
        "param_headers": [
                {
                    "key": "X-Merchant-ID",
                    "value": "3895b19b-f877-11ee-8bfc-9b54d42576d5"
                },
                {
                    "key": "Mobio-Connector-Identifier",
                    "value": "47fd810ab392e9435e7d88ea22471e33"
                },
                {
                    "key": "Mobio-Access-Token",
                    "value": "544162b05025c2915c330d8b407a7273"
                },
                {
                    "key": "Mobio-Connector-AppKey",
                    "value": "QxYysyqo4PyulMq3f2uI"
                }
        ]
    }
}
"""

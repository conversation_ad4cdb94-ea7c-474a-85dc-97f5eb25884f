"""
@apiDefine ResponseDetailSmsConfig

@apiSuccess {String}            data.id                                 <code>ID</code> Id của sms config.
@apiSuccess {String}            data.provider                           Tên nhà cung cấp.
@apiSuccess {String}            data.provider_link_image                Ảnh của nhà cung cấp.
@apiSuccess {Integer}            data.provider_type                     Loại nhà cung cấp.
@apiSuccess {String}            data.type                               Loại cấu hình sms.
@apiSuccess {String}            data.name                               Tên của cấu hình sms.
@apiSuccess {String}            data.description                        Mô tả của cấu hình sms.
@apiSuccess {String}            data.merchant_id                        Id của merchant.
@apiSuccess {Integer}            data.status                            Trạng thái của cấu hình sms [1-activate, 0-inactive].
@apiSuccess {Array}            data.connect_config_info                 Thông tin cấu hình sms cụ thể, kh<PERSON><PERSON> nhau tùy từng nhà cung cấp.
@apiSuccess {String}            data.connect_config_info.key                Key của thông tin cấu hình.
@apiSuccess {String}            data.connect_config_info.key_name           Tên của key cấu hình.
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {Datetime}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật.
@apiSuccess {Datetime}            data.updated_time                       Thời điểm cập nhật.
    
"""

************************ Create sms config ************************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {POST} {domain}/market-place/api/v1.0/sms-configs             Thêm mới cấu hình sms
@apiDescription API tạo cấu hình sms
@apiGroup SmsConfig
@apiVersion 1.0.0
@apiName CreateSmsConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name   Tên cấu hình sms.
@apiParam   (Body:)   {String}  type   Loại cấu hình sms.
@apiParam   (Body:)   {String}  description   Mô tả cấu hình sms.
@apiParam   (Body:)   {String}  provider_type   Loại nhà cung cấp, lấy từ danh sách provider.
@apiParam   (Body:)   {Object}   connect_config_info Thông tin cấu hình sms.
<li><code>key</code> Key cấu hình lấy từ thông tin của nhà cung cấp </li>
<li><code>value</code> Giá trị thô cấu hình</li>

@apiParamExample  {json}   Body example
{
  "name": "brandname 1",
  "type": "CSKH",
  "provider_type": 300,
  "description": "brandname 1 description",
  "connect_config_info": {
      "username": "brandname1",
      "password": "brandname1password",
      "api": "https://api.example.com"
    }
}

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Object}           data                          Dữ liệu cấu hình sms.
@apiSuccess {String}           data.id                       <code>ID</code> Id của sms config.
@apiSuccess {String}           data.provider_link_image            Link ảnh nhà cung cấp.
@apiSuccess {String}           data.provider                 Tên nhà cung cấp.
@apiSuccess {String}           data.name                     Tên cấu hình sms.
@apiSuccess {Integer}  data.status   Trạng thái cấu hình sms [1-active, 0-inactive].
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {Datetime}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật.
@apiSuccess {Datetime}            data.updated_time                       Thời điểm cập nhật.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
      "id": "620dc147dfd20bf34ac6954f",
      "provider_link_image": "https://www.sendcloud.com/sms-api/doc/guide",
      "provider": "VNPAY",
      "name": "brandname 1",
      "status": 1,
      "created_by": "admin",
      "updated_by": "admin",
      "created_time": "2018-01-01T00:00:00Z",
      "updated_time": "2018-01-01T00:00:00Z"
    }
}
"""

************************ Delete sms config ************************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {DELETE} {domain}/market-place/api/v1.0/sms-configs/<sms_config_id>             Xoá cấu hình sms
@apiDescription API xoá cấu hình sms
@apiGroup SmsConfig
@apiVersion 1.0.0
@apiName DeleteSmsConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""


************************ Update status sms config *****************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {PATCH} {domain}/market-place/api/v1.0/sms-configs/<sms_config_id>/actions/change-status       Cập nhật trạng thái cho sms config
@apiDescription API cập nhật trạng thái cho sms config
@apiGroup SmsConfig
@apiVersion 1.0.0
@apiName UpdateStatusSmsConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {Integer}  status   Trạng thái cấu hình sms [1-active, 0-inactive].

@apiParamExample  {json}   Body example
{
  "status": 1
}

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""

************************ Get sms configs **************************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {GET} {domain}/market-place/api/v1.0/sms-configs             Lấy danh sách cấu hình sms.
@apiDescription API lấy danh sách cấu hình sms.
@apiGroup SmsConfig
@apiVersion 1.0.0
@apiName GetSmsConfigs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse json_header
@apiUse merchant_id_header


@apiParam	   (Params)			{string}	[search]    Tìm kiếm theo sms config.     
@apiParam	   (Params)		{string}	[sort]      Sắp xếp theo field tuỳ chỉnh, mặc định là updated_time.
@apiParam	   (Params)		{integer}	[order]     Sắp xếp theo thuộc tính 1-asc, -1-desc.  
@apiParam       (Params) {Int} [page] Trang dữ liệu, ví dụ: <code>&page=2</code>. Mặc định page=1 
@apiParam       (Params) {Int} [per_page] Số phần tử trên một page. Example: <code>&per_page=10</code>. Mặc định lấy 20 bản ghi. 


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Array}           data                          Danh sách sms config.
@apiSuccess {Object}                paging                         Thông tin phân trang.

@apiSuccess {String}    data.id     Id sms config.
@apiSuccess {String}    data.provider_link_image  Link image của nhà cung cấp.
@apiSuccess {String}    data.provider  Tên nhà cung cấp.
@apiSuccess {String}    data.name   Tên cấu hình sms.
@apiSuccess {String}            data.type                               Loại cấu hình sms.
@apiSuccess {String}            data.merchant_id                        Id của merchant.
@apiSuccess {Integer}            data.provider_type                     Loại nhà cung cấp.
@apiSuccess {Integer}  data.status   Trạng thái cấu hình sms [1-active, 0-inactive].
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {Datetime}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật.
@apiSuccess {Datetime}            data.updated_time                       Thời điểm cập nhật.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "created_by": null,
      "created_time": "2024-05-21 08:25",
      "description": "",
      "id": "664c5a6308fce890b8ff295f",
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name": "MOBIO 2",
      "provider": "Vivas",
      "provider_link_image": "https://vivas.vn/wp-content/uploads/2018/12/logo_vivas.png",
      "provider_type": 115,
      "status": 1,
      "type": "CSKH",
      "updated_by": null,
      "updated_time": "2024-05-21 08:25"
    },
    {
      "created_by": null,
      "created_time": "2024-05-21 08:25",
      "description": "",
      "id": "664c5a6308fce890b8ff295f",
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "name": "MOBIO 2",
      "provider": "Vivas",
      "provider_link_image": "https://vivas.vn/wp-content/uploads/2018/12/logo_vivas.png",
      "provider_type": 115,
      "status": 1,
      "type": "CSKH",
      "updated_by": null,
      "updated_time": "2024-05-21 08:25"
        }
    ],
    "paging": {
        "page": 1,
        "per_page": 20,
        "total_count": 813,
        "total_page": 407
    }
}
"""

************************ Detail sms config ************************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {GET} {domain}/market-place/api/v1.0/sms-configs/<sms_config_id>             Xem chi tiết cấu hình sms
@apiDescription API xem chi tiết cấu hình sms
@apiGroup SmsConfig
@apiVersion 1.0.0
@apiName DetailSmsConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Object}           data                           Cấu hình sms config.
@apiUse ResponseDetailSmsConfig

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "_id": "664c5ccdb8a694223217c24d",
  "connect_config_info": [
      {
          "key": "username",
          "key_name": "Tên đăng nhập"
      },
      {
          "key": "password",
          "key_name": "Mật khẩu"
      },
      {
          "key": "api",
          "key_name": "Provider Api"
      }
  ],
  "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "created_time": "2024-05-22 09:16",
  "description": "brandname 1 description",
  "id": "664c5ccdb8a694223217c24d",
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "name": "brandname 1",
  "provider": "Vivas",
  "provider_link_image": "https://vivas.vn/wp-content/uploads/2018/12/logo_vivas.png",
  "provider_type": 115,
  "status": 0,
  "type": "CSKH",
  "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "updated_time": "2024-05-22 09:28"
}
"""

************************ Send sms test ****************************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {POST} {domain}/market-place/api/v1.0/sms-configs/<sms_config_id>/actions/send-sms           Gửi tin nhắn thử nghiệm cấu hình sms.
@apiDescription API gửi tin nhắn thử nghiệm sms.
@apiGroup SmsConfig
@apiVersion 1.0.0
@apiName SendSmsTest

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {Array}  to  Danh sách người nhận.
@apiParam   (Body:)   {Array}  to.phone_number  Só điện thoại người nhận.
@apiParam   (Body:)   {Object}  to.body  Dữ liệu thông tin gửi đi.
@apiParam   (Body:)   {String}  to.body.content Nội dung tin nhắn.

@apiParamExample  {json}   Body example
{
  to: [{
  "phone_number": "0987654321",
  "body": {
    "content": "Hello"
  }
}]

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""


************************ decode field sms config ******************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {GET} {domain}/market-place/api/v1.0/sms-configs/<sms_config_id>/actions/decode         Giải mã giá trị của field trong cấu hình sms
@apiDescription API giải mã giá trị.
@apiGroup SmsConfig
@apiVersion 1.0.0
@apiName DecodeSmsConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Params)   {String}  key  Key cần giải mã giá trị, vd: "username".

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.
@apiSuccess {Object}           data                          Dữ liệu giải mã.
@apiSuccess {String}           data.value                    giá trị mã hoá.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "value": "test"
  }
}
"""

************************ Sync sms config ************************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {POST} {domain}/market-place/api/v1.0/sms-configs/actions/sync-data           Api tạo mới cấu hình sms bằng cách đồng bộ dữ liệu
@apiDescription API tạo mới cấu hình sms bằng cách động bộ dữ liệu
@apiGroup SmsConfig
@apiVersion 1.0.0
@apiName SyncSmsConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name   Tên cấu hình sms, không được trùng lặp trong một merchant.
@apiParam   (Body:)   {String}  type   Loại cấu hình sms.
@apiParam   (Body:)   {Integer}  status   Trạng thái cấu hình sms [1-active, 0-inactive].
@apiParam   (Body:)   {Integer}  provider_type   Mã code của nhà cung cấp.
@apiParam   (Body:)   {String}  provider_api   Api của nhà cung cấp.
@apiParam   (Body:)   {String}  description   Mô tả cấu hình sms.
@apiParam   (Body:)   {Array}     connect_config_info Thông tin cấu hình sms.
@apiParam   (Body:)   {string}   connect_config_info.key Khoá giá trị của trường thông tin ["username", "password"] hoặc other key với mỗi nhà cung cấp.
@apiParam   (Body:)   {string}   connect_config_info.key_name_vi Tên tiếng việt trường thông tin tương ứng với mỗi nhà cung cấp.
@apiParam   (Body:)   {string}   connect_config_info.key_name_en Tên tiếng anh trường thông tin tương ứng với mỗi nhà cung cấp.
@apiParam   (Body:)   {string}   connect_config_info.value Giá trị thô của trường thông tin.

@apiParamExample  {json}   Body example
{
  "name": "brandname 1",
  "type": "CSKH",
  "status": 1,
  "provider_type": 300,
  "provider_api": "https://sms.vivas.vn/SMSBNAPINEW/sendsms"
  "description": "",
  "connect_config_info": [
    {
      "key": "username",
      "key_name_vi": "Mã đối tác",
      "key_name_en": "",
      "value": "brandname1"
    },
    {
      "key": "password",
      "key_name_vi": "Secret key",
      "key_name_en": "",
      "value": "brandname1password"
    }
  ]
}

@apiSuccess {String}            message                       Mô tả phản hồi.
@apiSuccess {Integer}           code                          Mã phản hồi.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 27/09/2023
"""

"""
@apiDefine Paramconfigwebpush
@apiParam   (Body:) {string}  type                                                   loại config (nhận giá trị: webpush)
@apiParam   (Body:) {string}  website_name                                           Tên website
@apiParam   (Body:) {string}  website_url                                            URL website
@apiParam   (Body:) {string}  website_img_id                                         Id của ảnh website (lấy từ api upload file)
@apiParam   (Body:) {string}  gtm_id                                                 Google tag manager ID       
@apiParam   (Body:) {Object}  connect_information_config                             Thông tin cấu hình firebase (nếu truyền lên {"type":"DEFAULT"} sẽ là cấu hình mặc định của mobio)
@apiParam   (Body:) {string}  connect_information_config.type                        Nhận giá trị : DEFAULT/CUSTOM
@apiParam   (Body:) {string}  connect_information_config.server_key
@apiParam   (Body:) {string}  connect_information_config.api_key
@apiParam   (Body:) {string}  connect_information_config.auth_domain
@apiParam   (Body:) {string}  connect_information_config.database_url
@apiParam   (Body:) {string}  connect_information_config.project_id
@apiParam   (Body:) {string}  connect_information_config.storage_bucket
@apiParam   (Body:) {string}  connect_information_config.messagingsender_id
@apiParam   (Body:) {string}  connect_information_config.app_id
@apiParam   (Body:) {string}  [connect_information_config.measurement_id]
"""

"""
@apiDefine firebasedetail
@apiParam   (Body:) {Object}  connect_information_config                              Thông tin cấu hình firebase
@apiParam   (Body:) {string}  connect_information_config.server_key                   
@apiParam   (Body:) {string}  connect_information_config.api_key
@apiParam   (Body:) {string}  connect_information_config.auth_domain
@apiParam   (Body:) {string}  connect_information_config.database_url
@apiParam   (Body:) {string}  connect_information_config.project_id
@apiParam   (Body:) {string}  connect_information_config.storage_bucket
@apiParam   (Body:) {string}  connect_information_config.messagingsender_id
@apiParam   (Body:) {string}  connect_information_config.app_id
@apiParam   (Body:) {string}  [connect_information_config.measurement_id]                                             Google tag manager ID      
"""

# ---------- Create webpushconfig -----------
"""
@api {POST} {domain}/market-place/api/v1.0/market-place              Thêm mới cấu hình 
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName CreateMarketplaceCOnfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse Paramconfigwebpush

@apiParamExample {json} Body example
{
    "website_name": "",
    "website_url": "",
    "website_img_id": "62c7f7c2e6f54900100a040a",
    "gtm_id": "",
    "connect_information_config": {
        "type": "default",
    },
    "type": "webpush"
}

@apiSuccess   {Object}  data                                                      
@apiSuccess   {string}  data.website_name                                           Tên website
@apiSuccess   {string}  data.website_url                                            URL website
@apiSuccess   {string}  data.website_img_id                                         Id của ảnh website (lấy từ api upload file)
@apiSuccess   {Object}  data.website_img_info                                       Thông tin của ảnh website
@apiSuccess   {string}  data.gtm_id                                                 Google tag manager ID  
@apiSuccess   {string}  data.status                                                 Trạng thái cua cấu hình webpush
@apiSuccess   {Object}  data.connect_information_config                             Thông tin cấu hình firebase

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "id": "",
        "merchant_id": "",
        "website_name": "",
        "website_url": "",
        "website_img_id": "",
        "website_img_info": {
            "_id": "",
            "action_time": 1645043415.593171,
            "capacity": 1082923,
            "created_by": "",
            "filename": "",
            "format_file": "image/jpeg",
            "id": "",
            "local_path": "",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "objectId": None,
            "url": ""
        },
        "status": "ENABLE",
        "gtm_id": "",
        "connect_config_id: "",
        "created_by": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "updated_by": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "created_time": "2019-03-27T17:52:46Z",
        "updated_time": "2019-03-27T17:52:46Z"
    }
}
"""

# ---------- Update webpushconfig -----------
"""
@api {PUT} {domain}/market-place/api/v1.0/market-place/<config_id>              Sửa cấu hình 
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName UpdateMarketplaceCOnfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse Paramconfigwebpush

@apiParamExample {json} Body example
{
    "type": "webpush",
    "website_name": "",
    "website_url": "",
    "website_img_id": "62c7f7c2e6f54900100a040a",
    "gtm_id": "",
    "connect_information_config": {}
}

@apiSuccess {Array}             data                          Thông tin cấu hình
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccess   {Object}  data                                                      
@apiSuccess   {string}  data.website_name                                           Tên website
@apiSuccess   {string}  data.website_url                                            URL website
@apiSuccess   {string}  data.website_img_id                                         Id của ảnh website (lấy từ api upload file)
@apiSuccess   {Object}  data.website_img_info                                       Thông tin của ảnh website
@apiSuccess   {string}  data.gtm_id                                                 Google tag manager ID  
@apiSuccess   {string}  data.status                                                 Trạng thái cua cấu hình webpush
@apiSuccess   {Object}  data.connect_information_config                                    Thông tin cấu hình firebase
@apiSuccess   {string}  data.connect_information_config.server_key                         
@apiSuccess   {string}  data.connect_information_config.api_key                             
@apiSuccess   {string}  data.connect_information_config.auth_domain                           
@apiSuccess   {string}  data.connect_information_config.database_url                            
@apiSuccess   {string}  data.connect_information_config.project_id                             
@apiSuccess   {string}  data.connect_information_config.storage_bucket                          
@apiSuccess   {string}  data.connect_information_config.messagingsender_id                          
@apiSuccess   {string}  data.connect_information_config.app_id                                 
@apiSuccess   {string}  data.connect_information_config.measurement_id 

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "id": "",
        "merchant_id": "",
        "website_name": "",
        "website_url": "",
        "website_img_id": "",
        "website_img_info": {
            "_id": "",
            "action_time": 1645043415.593171,
            "capacity": 1082923,
            "created_by": "",
            "filename": "",
            "format_file": "image/jpeg",
            "id": "",
            "local_path": "",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "objectId": None,
            "url": ""
        },
        "status": "ENABLE",
        "gtm_id": "",
        "connect_config_id: "",
        "connect_information_config":{
            "server_key":"",
            "api_key":"",
            "auth_domain":"",
            "database_url":"",
            "project_id":"",
            "storage_bucket":"",
            "messagingsender_id":"",
            "app_id":"",
            "measurement_id":"",
        },
        "created_by": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "updated_by": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "created_time": "2019-03-27T17:52:46Z",
        "updated_time": "2019-03-27T17:52:46Z"
    }
}
"""

# ---------- Delete webpushconfig -----------
"""
@api {Delete} {domain}/market-place/api/v1.0/market-place              Xóa cấu hình 
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName DeleteMarketplaceCOnfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	   (Query:)			{string}	config_ids        Các config id muốn xóa, ngăn cách bới dấu ","

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

# ---------- Update status webpushconfig -----------
"""
@api {POST} {domain}/market-place/api/v1.0/market-place/update-status              Cập nhật trạng thái cấu hình 
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName UPdateStatusMarketplaceCOnfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {string}  config_id                                         Config id
@apiParam   (Body:) {string}  status                                            status (nhận giá trị: ENABLE/DISABLE)

@apiParamExample {json} Body example
{
    "config_id": "",
    "status": "DISABLE"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

# ---------- History action webpushconfig -----------
"""
@api {GET} {domain}/market-place/api/v1.0/market-place/action-history/<config_id>              Lịch sử tích hợp và chỉnh sửa 
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName ActionHistoryMarketplaceCOnfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	   (Query:)			{string}	[per_page]			  
@apiParam	   (Query:)			{string}	[after_token]

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data" :[
        {
            "merchant_id": "587bfd95-3d2f-490d-ba67-6fd65480a349",
            "account_id": "64f5bd34-8046-4d19-be43-5efdf662b674",
            "config_id":"",
            "action": "ADD",
            "time": "2019-03-27T17:52:46Z"
        }
    ],
    "paging": {
        "cursors": {
            "after_token": "********************************************************",
            "before_token": "********************************************************"
        }
    }
}
"""

# ---------- Get detail webpushconfig -----------
"""
@api {Get} {domain}/market-place/api/v1.0/market-place/<config_id>              Chi tiết cấu hình 
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName DetailMarketplaceCOnfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess   {Object}  data                                                      
@apiSuccess   {string}  data.website_name                                           Tên website
@apiSuccess   {string}  data.website_url                                            URL website
@apiSuccess   {string}  data.website_img_id                                         Id của ảnh website (lấy từ api upload file)
@apiSuccess   {Object}  data.website_img_info                                       Thông tin của ảnh website
@apiSuccess   {string}  data.gtm_id                                                 Google tag manager ID  
@apiSuccess   {string}  data.status                                                 Trạng thái cua cấu hình webpush
@apiSuccess   {Object}  data.connect_information_config                                    Thông tin cấu hình firebase
@apiSuccess   {string}  data.connect_information_config.server_key                         
@apiSuccess   {string}  data.connect_information_config.api_key                             
@apiSuccess   {string}  data.connect_information_config.auth_domain                           
@apiSuccess   {string}  data.connect_information_config.database_url                            
@apiSuccess   {string}  data.connect_information_config.project_id                             
@apiSuccess   {string}  data.connect_information_config.storage_bucket                          
@apiSuccess   {string}  data.connect_information_config.messagingsender_id                          
@apiSuccess   {string}  data.connect_information_config.app_id  
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "id": "",
        "merchant_id": "",
        "website_name": "",
        "website_url": "",
        "website_img_id": "",
        "website_img_info": {
            "_id": "",
            "action_time": 1645043415.593171,
            "capacity": 1082923,
            "created_by": "",
            "filename": "",
            "format_file": "image/jpeg",
            "id": "",
            "local_path": "",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "objectId": None,
            "url": ""
        },
        "status": "ENABLE",
        "gtm_id": "",
        "connect_config_id: "",
        "connect_information_config":{
            "server_key":"",
            "api_key":"",
            "auth_domain":"",
            "database_url":"",
            "project_id":"",
            "storage_bucket":"",
            "messagingsender_id":"",
            "app_id":"",
            "measurement_id":"",
        },
        "created_by": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "updated_by": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "created_time": "2019-03-27T17:52:46Z",
        "updated_time": "2019-03-27T17:52:46Z"
    }
}
"""


# ---------- Create file -----------
"""
@api {POST} {domain}/market-place/api/v1.0/file            Thêm file
@apiGroup Upload-file
@apiVersion 1.0.0
@apiName  UploadFile
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(FORM:)			{Binary}	    file		                file upload
@apiParam	(FORM:)			{string}	    type		                loại file (IMAGE/SDK)


@apiSuccess (data)      {String}         _id                      <code>ID</code> file upload lên hệ thống
@apiSuccess (data)      {Array}          type_media               Loại Media
@apiSuccess (data)      {String}         title                    Tên file upload
@apiSuccess (data)      {String}         format_file              Định dạng của file
@apiSuccess (data)      {String}         url                      Link file
@apiSuccess (data)      {String}         capacity                 Dung lượng file
@apiSuccess (data)      {String}         local_path               Đường dẫn vậy lý của file
@apiSuccess (data)      {String}         merchant_id              ID của merchant
@apiSuccess (data)      {String}         objectId                 ID của doi tuong (mặc định là None)


@apiSuccess {Array}             data                          Thông tin file upload
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "620dc147dfd20bf34ac6954f",
        "action_time": 1645043415.593171,
        "capacity": 1082923,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "filename": "1645068615_0E0A7559.jpg",
        "format_file": "image/jpeg",
        "id": "620dc147dfd20bf34ac6954f",
        "local_path": "/Users/<USER>/workspace/mobio/Module-Ticket/ticket/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "objectId": None,
        "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------- get sdk file -----------
"""
@api {GET} {domain}/market-place/api/v1.0/file            Lấy thông tin file sdk
@apiGroup Upload-file
@apiVersion 1.0.0
@apiName  GetSDKFile
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang



@apiSuccess (data)      {String}         _id                      <code>ID</code> file upload lên hệ thống
@apiSuccess (data)      {Array}          type_media               Loại Media
@apiSuccess (data)      {String}         title                    Tên file upload
@apiSuccess (data)      {String}         format_file              Định dạng của file
@apiSuccess (data)      {String}         url                      Link file
@apiSuccess (data)      {String}         capacity                 Dung lượng file
@apiSuccess (data)      {String}         local_path               Đường dẫn vậy lý của file
@apiSuccess (data)      {String}         merchant_id              ID của merchant
@apiSuccess (data)      {String}         objectId                 ID của doi tuong (mặc định là None)


@apiSuccess {Array}             data                          Thông tin file upload
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "620dc147dfd20bf34ac6954f",
        "action_time": 1645043415.593171,
        "capacity": 1082923,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "filename": "1645068615_0E0A7559.jpg",
        "format_file": "image/jpeg",
        "id": "620dc147dfd20bf34ac6954f",
        "local_path": "/Users/<USER>/workspace/mobio/Module-Ticket/ticket/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "objectId": None,
        "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------- Get list market place config -----------
"""
@api {POST} {domain}/market-place/api/v1.0/market-place/get-list-config            Danh sách cấu hình
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName GetListMarketplaceCOnfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiParam	   (Query:)			{string}	[per_page]			  
@apiParam	   (Query:)			{string}	[after_token]

@apiParam   (Body:) {string}  search                                            Tên website hoặc url config cần tìm
@apiParam   (Body:) {string}  [type]                                            Loại cấu hình (mặc định lấy tất cả. nhận giá trị : webpush)

@apiSuccess {Array}             data                          Danh sách cấu hình webpush                             
@apiSuccess   {string}  data.website_name                                           Tên website
@apiSuccess   {string}  data.code                                                   Tracking code của website
@apiSuccess   {string}  data.website_url                                            URL website
@apiSuccess   {string}  data.website_img_id                                         Id của ảnh website (lấy từ api upload file)
@apiSuccess   {Object}  data.website_img_info                                       Thông tin của ảnh website
@apiSuccess   {string}  data.gtm_id                                                 Google tag manager ID  
@apiSuccess   {string}  data.status                                                 Trạng thái cua cấu hình webpush                       
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
                                        <li><code>page:</code>Vị trí page request</li>
                                        <li><code>per_page:</code>Số lượng phần tử trên một page</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after_token]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before_token]    Token để lấy dữ liệu trang trước đó.

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [{
        "id": "",
        "code": "MVP-123123123",
        "merchant_id": "",
        "website_name": "",
        "website_url": "",
        "website_img_id": "",
        "website_img_info": {
            "_id": "",
            "action_time": 1645043415.593171,
            "capacity": 1082923,
            "created_by": "",
            "filename": "",
            "format_file": "image/jpeg",
            "id": "",
            "local_path": "",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "objectId": None,
            "url": ""
        },
        "status": "ENABLE",
        "gtm_id": "",
        "code": "f73e77db-6301-4fc1-bca2-1ddc33694a5d",
        "source": "mobio",
        "created_time": "2019-03-27T17:52:46Z",
        "updated_time": "2019-03-27T17:52:46Z"
    }],
    "paging": {
        "cursors": {
            "after_token": "********************************************************",
            "before_token": "********************************************************"
        }
    }
}
"""


# ---------- get firebase config by domain -----------
"""
@api {GET} {domain}/market-place/api/v1.0/market-place/connect-config            Lấy thông tin firebase
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName  getconnectconfig
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:) {Array}    website_url                       website_url của webpush

@apiSuccess (data)      {String}         server_key               
@apiSuccess (data)      {String}         api_key              
@apiSuccess (data)      {String}         database_url            
@apiSuccess (data)      {String}         project_id              
@apiSuccess (data)      {String}         messagingsender_id     
@apiSuccess (data)      {String}         app_id                 
@apiSuccess (data)      {String}         type               
@apiSuccess (data)      {String}         storage_bucket              
@apiSuccess (data)      {String}         auth_domain                
@apiSuccess (data)      {String}         connect_type              
@apiSuccess (data)      {String}         merchant_id              


@apiSuccess {Array}             data                          Thông tin file upload
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "server_key" : "",
        "api_key" : "",
        "database_url" : "",
        "project_id" : "",
        "messagingsender_id" : "",
        "app_id" : "",
        "measurement_id" : "",
        "type" : "CUSTOM",
        "storage_bucket" : "",
        "auth_domain" : "",
        "connect_type" : "firebase",
        "merchant_id" : "",
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------- Lấy thông tin cấu hình của webpush theo tracking code -----------
"""
@api {GET} {domain}/guest/market-place/api/v1.0/webpush/tracking-code/<tracking-code>/config            Lấy cấu hình webpush bởi tracking code           
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName  GetConfigWebPush
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang



        

@apiSuccess (data)      {String}         website_url                Url website tương ứng với tracking code
@apiSuccess (data)      {String}         connect_type               Loại kết nối:
                                                                    <ul>
                                                                        <li>firebase: Firebase</li>
                                                                    </ul>
@apiSuccess (data)      {string}        status                      Trạng thái
                                                                    <ul>
                                                                        <li>ENABLE :: Đang hoạt động</li>
                                                                        <li>DISABLE :: Không hoạt động</li>
                                                                        <li>DELETE :: Đã bị xoá</li>
                                                                    </ul>
@apiSuccess (data)      {Object}         information               Thông tin cấu hình
                                                                    
@apiSuccess (data)      {String}         information.serverKey               
@apiSuccess (data)      {String}         information.apiKey              
@apiSuccess (data)      {String}         information.databaseURL            
@apiSuccess (data)      {String}         information.projectId              
@apiSuccess (data)      {String}         information.messagingSenderId     
@apiSuccess (data)      {String}         information.measurementId     
@apiSuccess (data)      {String}         information.appId                 
@apiSuccess (data)      {String}         information.type               
@apiSuccess (data)      {String}         information.storageBucket              
@apiSuccess (data)      {String}         information.authDomain        
                                                            
@apiSuccess {Object}             data                         Dữ liệu trả về
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "connect_type": "firebase",
        "status":"ENABLE",
        "information": {
            "serverKey": "",
            "apiKey": "",
            "databaseURL": "",
            "measurementId":"",
            "projectId": "mobio",
            "storageBucket": "mobio",
            "messagingSenderId": "",
            "appId": "1:",
            "measurement_id": "",
            "connect_type": "firebase",
            "authDomain": "mobio-webpush-test"
        }
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------- get sdk js script -----------
"""
@api {GET} {domain}/market-place/api/v1.0/market-place/sdk-script           Gen script sdk
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName  getSDKScript
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:) {Array}    website_url                       website_url của webpush            


@apiSuccess {Array}             data                          Thông tin file upload
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": "<script>!function(b,c,e,f,g){var h=b.mo=b.mo||{},a=c.createElement('script');a.type='text/javascript',a.async=!0,a.defer=!0,a.onload=()=>{h.init||(h=b.mo=b.mo||{}),h.init({code:'f5cae459-7d16-4f9b-b966-4c7740481bdf',source:'mobio'})},a.src=e;var d=c.getElementsByTagName('script')[0];d.parentNode.insertBefore(a,d)}(window,document,'#link_url_js','#code','#source');</script>",
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------- upsert sdk js script -----------
"""
@api {POST} {domain}/market-place/api/v1.0/market-place/sdk-script           Upsert script sdk
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName  upsertSDKScript
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (BODY:) {string}    script                        script js sdk       


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------- Get list market place config -----------
"""
@api {POST} {domain}/market-place/api/v1.0/market-place/get-list-config-by-ids            Danh sách cấu hình theo ids
@apiDescription Chỉnh sửa lại để lấy theo danh sách code của cấu hình
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName GetListMarketplaceCOnfigBYIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiParam	   (Query:)			{string}	[per_page]			  
@apiParam	   (Query:)			{string}	[after_token]

@apiParam   (Body:) {array}  list_code                      Danh sách code cần lấy cấu hình

@apiSuccess {Array}             data                          Danh sách cấu hình webpush                             
@apiSuccess   {string}  data.website_name                                           Tên website
@apiSuccess   {string}  data.website_url                                            URL website
@apiSuccess   {string}  data.website_img_id                                         Id của ảnh website (lấy từ api upload file)
@apiSuccess   {Object}  data.website_img_info                                       Thông tin của ảnh website
@apiSuccess   {string}  data.gtm_id                                                 Google tag manager ID  
@apiSuccess   {string}  data.status                                                 Trạng thái cua cấu hình webpush     
@apiSuccess   {string}  data.code                                                 
@apiSuccess   {string}  data.source                                                              
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
                                        <li><code>page:</code>Vị trí page request</li>
                                        <li><code>per_page:</code>Số lượng phần tử trên một page</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after_token]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before_token]    Token để lấy dữ liệu trang trước đó.

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [{
        "id": "",
        "merchant_id": "",
        "website_name": "",
        "website_url": "",
        "website_img_id": "",
        "website_img_info": {
            "_id": "",
            "action_time": 1645043415.593171,
            "capacity": 1082923,
            "created_by": "",
            "filename": "",
            "format_file": "image/jpeg",
            "id": "",
            "local_path": "",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "objectId": None,
            "url": ""
        },
        "status": "ENABLE",
        "gtm_id": "",
        "created_time": "2019-03-27T17:52:46Z",
        "updated_time": "2019-03-27T17:52:46Z"
    }],
    "paging": {
        "cursors": {
            "after_token": "********************************************************",
            "before_token": "********************************************************"
        }
    }
}
"""

# ---------- Get list market place config -----------
"""
@api {GET} {domain}/market-place/api/v1.0/webpush/tracking-code/<tracking-code>/information            Thông tin cấu hình webpush bởi tracking code
@apiDescription Thông tin cấu hình webpush bởi tracking code
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName InformationTrackingCode

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apiSuccess   {Object}             data                         Thông tin của tracking code                     
@apiSuccess   {string}          data.merchant_id                Định danh merchant_id
@apiSuccess   {string}          data.status                     Trạng thái
                                                                <ul>
                                                                    <li>ENABLE :: Đang hoạt động</li>
                                                                    <li>DISABLE :: Không hoạt động</li>
                                                                    <li>DELETE :: Đã bị xoá</li>
                                                                    <li>NOT_EXIT :: Không tồn tại</li>
                                                                </ul>
@apiSuccess   {String}            message                       Mô tả phản hồi
@apiSuccess   {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "merchant_id": "",
        "status": "NOT_EXIT"
    }
}
"""

# ---------- Đăng ký landing page với cấu hình webpush -----------
"""
@api {POST} {domain}/market-place/api/v1.0/webpush/landing-pages/actions/upsert         Đăng ký thông tin landing page với Webpush
@apiDescription     Đăng ký thông tin landing page với Webpush
@apiGroup Marketplace
@apiVersion 1.0.0
@apiName UpsertLandingPageWebpush

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apiParam     (BODY:)          {string}     object_id                       <code>ID</code> của Landing Page
@apiParam     (BODY:)          {string}     name                            Tên của Landing Page
@apiParam     (BODY:)          {string}     public_url                      Link public của landing page
@apiParamExample {json} Response
{
    "object_id": "620dc147dfd20bf34ac6954f",
    "name": "Landing Page 1",
    "public_url": "https://landingpage.com"
}



@apiSuccess   {Object}             data                         Thông tin đăng ký webpush
@apiSuccess   {string}             data.id                      <code>ID</code> của webpush
@apiSuccess   {string}             data.website_name            Tên website
@apiSuccess   {string}             data.website_url             URL website
@apiSuccess   {string}             data.status                  Trạng thái cua cấu hình webpush
@apiSuccess   {string}             data.tracking_code           Mã tracking code
@apiSuccess   {string}             data.script_sdk_integrated   Scrip SDK đã được tích hợp



@apiSuccess   {String}            message                       Mô tả phản hồi
@apiSuccess   {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "id": "620dc147dfd20bf34ac6954f",
        "website_name": "Landing Page 1",
        "website_url": "https://landingpage.com",
        "status": "ENABLE",
        "tracking_code": "123456789",
        "script_sdk_integrated": "<script>...</script>"
    }
}
"""

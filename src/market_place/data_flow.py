#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 20/03/2024
"""

"""
@apiDefine paging_tokens
@apiVersion 1.0.0
@apiParam   (Query:)    {Number}    [per_page] Số phần tử trên một page.<br/> Example: <code>&per_page=5</code>
@apiParam   (Query:)    {String}    [after_token] Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)    {String}    [before_token] Token để request lấy dữ liệu trang trước đó. Nếu trong danh sách tham số gửi lên có <code>after_token</code> thì ưu tiên xử lý <code>after_token</code>

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code><PERSON><PERSON> lượng phần tử trên một page</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before]    Token để lấy dữ liệu trang trước đó.
@apiSuccess     {Object}    [paging..per_page]          Số lượng phần tử trên 1 page
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh",
            "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 15,
    }
}
"""
"""
@apiDefine DetailConnector
@apiSuccess   {Int}   data._id                                                     <code>ID</code> record
@apiSuccess   {bool}   data.is_trust_source                                          Có phải là trust source hay không.
                                                                                    <ul>
                                                                                        <li><code>true</code> Là trust source</li>
                                                                                        <li><code>false</code> Không là trust source</li>
                                                                                    </ul>
@apiSuccess     {String}            data.status_sync                                 Trạng thái đồng bộ
@apiSuccess     {String}            data.action                                      Thao tác với dữ liệu
@apiSuccess     (data-in)   {Object}            data.config_connect                              Thông tin cấu hình kết nối
@apiSuccess     (data-in)   {string}            data.config_connect.hostname                                          Thông tin hostname. T
@apiSuccess     (data-in)   {int}               data.config_connect.port                                              Thông tin port kết nối database
@apiSuccess     (data-in)   {string}            data.config_connect.database_name                                     Tên database name
@apiSuccess     (data-in)   {string}            data.config_connect.database_username                                 Username đăng nhập vào database
@apiSuccess     (data-in)   {string}            data.config_connect.database_password                                 Password đăng nhập vào database
@apiSuccess   (data-in)   {string}              data.object                                      Đối tượng xử lý dữ liệu
@apiSuccess   (data-in)   {string}              data.attribute_object                            Thuộc tính của đối tượng xử lý dữ liệu
@apiSuccess   (data-in)   {array}               data.list_field_verify                                      Danh sách field được verify
@apiSuccess   (data-in)   {bool}                data.is_trust_source                                 Là nguồn ghi nhận đáng tin cậy.
@apiSuccess   (data-in)   {object}              data.config_mapping_data                                     Mapping trường thông tin.
@apiSuccess   (data-in)   {string}             data.config_mapping_data.table                                                   Tên table cấu hình 
@apiSuccess   (data-in)   {array}              data.config_mapping_data.fields                                                  Danh sách field mapping
@apiSuccess   (data-in)   {string}             data.config_mapping_data.fields.field_source                                     Field đến từ ngồn
@apiSuccess   (data-in)   {string}             data.config_mapping_data.fields.field_target                                     Field tương ứng vào hệ thống
@apiSuccess   (data-in)   {string}             data.config_mapping_data.fields.object                                           Đối tượng được mapping 
@apiSuccess   (data-in)   {string}             data.config_mapping_data.fields.value_type=record                              Kiểu ghi nhận giá trị
                                                                                                                <ul>
                                                                                                                    <li>fixed: Giá trị kiểu fix cứng</li>
                                                                                                                    <li>record: Ghi nhận giá trị từ việc đồng bộ dữ liệu</li>
                                                                                                                </ul>
@apiSuccess   (data-in)   {string}             [data.config_mapping_data.fields.value_by_type_fixed]                            Giá trị theo kiểu giá trị ghi nhận
@apiSuccess   (data-in)   {bool}               data.config_mapping_data.fields.required                                 Bắt buộc hay không?
@apiSuccess   (data-in)   {string}             data.config_mapping_data.fields.action                                   Action khi thao tác với dữ liệu
                                                                                                                    <ul>
                                                                                                                        <li>add: Thêm</li>
                                                                                                                        <li>overwrite: Ghi đè</li>
                                                                                                                        <li>overwrite_and_ignore_value_null: Ghi đè và bỏ quá giá trị null</li>
                                                                                                                    </ul>
@apiSuccess   (data-in)   {object}                            [data.config_rule_unification]                       Quy tắc đồng bộ dữ liệu. <code>Trong trường hợp nếu không phải trust_source thì sẽ ghi nhận dữ liệu như rule ở quy tắc đồng bộ dữ liệu</code>
@apiSuccess   (data-in)   {object}         data.config_rule_unification.data_recording_rules                       Cấu hình rule ghi nhận dữ liệu
@apiSuccess   (data-in)   {array}          data.config_rule_unification.data_recording_rules.operators             Thông tin cấu hình rule ghi nhận dữ liệu: <a>https://dev.mobio.vn/docs/profiling/#api-Unification</a>
@apiSuccess   (data-in)   {object}         data.config_rule_unification.data_update_rules                          Cấu hình rule cập nhật dữ liệu
@apiSuccess   (data-in)   {array}          data.config_rule_unification.data_update_rules.operators                Thông tin cấu hình rule cập nhật dữ liệu: <a>https://dev.mobio.vn/docs/profiling/#api-Unification</a>                                
@apiSuccess   (data-in)   {obejct}          data.config_rule_unification.consent                                    Thông tin ghi nhân consent
@apiSuccess   (data-in)   {String=Có,Không}       data.config_rule_unification.consent.mkt_consent             Marketing Consent.
@apiSuccess   (data-in)   {String=Có,Không}       data.config_rule_unification.consent.analytics_consent       Analytics Consent.
@apiSuccess   (data-in)   {String=Có,Không}       data.config_rule_unification.consent.tracking_consent        Tracking Consent.

@apiSuccess     (data-out)   {Object}            data.config_connect                              Thông tin cấu hình kết nối
@apiSuccess     (data-out)   {string}            data.config_connect.app_id                       Định danh của APP Authentication. Api lấy danh APP <a href="https://dev.mobio.vn/docs/market_place/#api-DataFlowConfigApp-ListApps">tại đây</a>
@apiSuccess     (data-out)   {int}               data.config_connect.url                          URL connect
@apiSuccess     (data-out)   {Array}            data.config_connect.methods                      Danh sách Methods tương tác với URL.
@apiSuccess     (data-out)   {Array}            data.config_connect.param_headers                Tham số trên headers
@apiSuccess     (data-out)   {string}            data.config_connect.param_headers.key           Key của tham số
@apiSuccess     (data-out)   {string}            data.config_connect.param_headers.value         Giá trị của tham số
@apiSuccess     (data-out)   {string=string,formula}            data.config_connect.param_headers.type          Loại của tham số
@apiSuccess     (data-out)   {string=system,user}            data.config_connect.param_headers.create_by        Người tạo
@apiSuccess     (data-out)   {string=application/json}            data.config_connect.content_type        Type của Body
@apiSuccess   (data-out)   {bool}              data.is_notification                                     Có bật thông báo hay không?
@apiSuccess   (data-out)   {Object}              data.contact_info                                      Thông tin liên hệ khi gặp sự cố kết nối
@apiSuccess   (data-out)   {string=email}        data.contact_info.type                                 Kiểu thông tin liên hệ
@apiSuccess   (data-out)   {Array}              data.contact_info.values                                      Danh sách giá trị liên hệ
@apiSuccess   (data-out)   {Array}              data.config_information_out                                   Cấu hình thông tin Out
@apiSuccess   (data-out)   {String}             data.config_information_out.module                            Tên module
@apiSuccess   (data-out)   {Array}              data.config_information_out.event_keys                        Danh sách event key được bật cấu hình.
@apiSuccess   {object}                          data.config_sync_calendar                            Cấu hình thời gian lập lịch sync data
@apiSuccess   {string}            data.config_sync_calendar.mode                       Cơ chế đồng bộ
                                                                                                        <ul>
                                                                                                            <li>streaming</li>
                                                                                                            <li>snapshot</li>
                                                                                                        </ul>
@apiSuccess    {object}            data.config_sync_calendar.auto_retry_w_error          Cấu hình tự động đồng bộ khi xảy ra lỗi
@apiSuccess    {int}               data.config_sync_calendar.auto_retry_w_error.status   Trạng thái của cấu hình này.
                                                                                    <ul>
                                                                                        <li>0: là tắt</li>
                                                                                        <li>1: là bậtt</li>
                                                                                    </ul>
@apiSuccess   {int}               data.config_sync_calendar.auto_retry_w_error.number_max_retry      Số lần đồng bộ lại khi gặp lỗi                                                                                
@apiSuccess   {int}               data.config_sync_calendar.auto_retry_w_error.retry_interval_sec    Khoảng thời gian giữa các lần đồng bộ lại.
@apiSuccess   {object}            [data.config_sync_calendar.schedule]                 Cấu hình lập lịch chạy
@apiSuccess  {string}            [data.config_sync_calendar.schedule.type]            Kiểu lập lịch
                                                                                                        <ul>
                                                                                                            <li>manually: thủ công</li>
                                                                                                            <li>interval: định kỳ</li>
                                                                                                        </ul>
@apiSuccess    {object}            [data.config_sync_calendar.schedule.config]           Cấu hình thời gian lặp theo định kỳ
@apiSuccess    {object}            [data.config_sync_calendar.schedule.config.hour]      Thời gian đồng bộ. Định dạng (HH:MM). Example (16:49)
@apiSuccess    {object}            [data.config_sync_calendar.schedule.config.type]      Kiểu lịch trình
                                                                                                            <ul>
                                                                                                                <li><code>day</code>: Ngày</li>
                                                                                                                <li><code>week</code>: Tuần</li>
                                                                                                                <li><code>month</code>: Tháng</li>
                                                                                                                <li><code>year</code>: Năm</li>
                                                                                                            </ul>
@apiSuccess   {object}            [data.config_sync_calendar.schedule.config.type_select_day_in_month]      Kiểu chọn ngày trong tháng
                                                                                                        <ul>
                                                                                                            <li><code>exact_day</code>: Ngày cụ thể</li>
                                                                                                            <li><code>flex_day</code>: Ngày linh hoạt</li>
                                                                                                        </ul>                                                                                    
@apiSuccess   {Array}             [data.config_sync_calendar.schedule.config.values]            Cấu hình thông tin lặp vào (Ví dụ: thứ 2, thứ 3)
                                                                                            <ul>
                                                                                                <li> Case: type=<code>day</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                                                <li> Case: type=<code>week</code> thì <code>values: danh sách các thứ trong tuần - ví dụ ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]</code></li>
                                                                                                <li> Case: type=<code>month</code> thì <code>values: Danh sách các ngày trong tháng (Mặc định là 31 ngày) - ví dụ [1,2,3,4,5,6,7]</code> hoặc values có thể là : <code>first_of_month</code> (Ngày đầu tháng), <code>last_of_month </code> (Ngày cuối tháng)</li>
                                                                                                <li> Case: type=<code>year</code> thì <code>values: sẽ là mảng chứa giá trị ngày và tháng - ví dụ ["24/04"]</code></li>
                                                                                            </ul>
@apiSuccess {object}            [config_sync_calendar.snapshot_config]                     Cấu hình snapshot
@apiSuccess {string}            [config_sync_calendar.snapshot_config.snapshot_type]    Kiểu thực hiện đồng bộ snapshot
                                                                                                        <ul>
                                                                                                            <li><code>full</code>: full</li>
                                                                                                            <li><code>incremental</code>: incremental</li>
                                                                                                        </ul>   
@apiSuccess {object}            [config_sync_calendar.snapshot_config.checkpoint]       Thông tin checkpoint
@apiSuccess {string}            [config_sync_calendar.snapshot_config.checkpoint.field_name]     Tên field được chọn làm checkpoint
@apiSuccess {string}            [config_sync_calendar.snapshot_config.checkpoint.field_type]        Kiểu dữ liệu của field được chọn làm checkpoint
@apiSuccess {string}            [config_sync_calendar.snapshot_config.checkpoint.last_checkpoint]     Giá trị gần nhất của checkpoint
@apiSuccess {int}            [config_sync_calendar.snapshot_config.partition]               Số lượng mỗi partition
@apiSuccess {boolean}            [config_sync_calendar.snapshot_config.is_use_custom_checkpoint]          Sử dụng giá trị tùy chọn
                                                                                                        <ul>
                                                                                                            <li><code>true</code>:  Có sử dụng giá trị tùy chọn</li>
                                                                                                            <li><code>fasle</code>:  Không sử dụng giá trị tùy chọn</li>
                                                                                                        </ul>  
@apiSuccess {object}            [config_sync_calendar.snapshot_config.checkpoint.option]               Thông tin giá trị tùy chọn
@apiSuccess {string}            [config_sync_calendar.snapshot_config.checkpointoption.option_type]   Loại giá trị tùy chọn
                                                                                                        <ul>
                                                                                                            <li><code>beginning</code>:  Đồng bộ từ bản tin đầu tiên</li>
                                                                                                            <li><code>selected</code>:  Giá trị được chọn</li>
                                                                                                        </ul>   
@apiSuccess {string}            [config_sync_calendar.snapshot_config.checkpointoption.option_value]   Giá trị tùy chọn                                                                            
@apiSuccess {string}            [config_sync_calendar.snapshot_config.checkpointoption.session_id]      Session id
@apiSuccess {string}            [config_sync_calendar.snapshot_config.updated_time]          Thời gian cập nhật checkpoint

@apiSuccess     {string}            data.created_by                                 Thông tin nhân viên tạo
@apiSuccess     {string}            data.updated_by                                 Thông tin nhân viên cập nhật
@apiSuccess     {string}            data.created_time                               Thời gian tạo
@apiSuccess     {string}            data.updated_time                               Thời gian cập nhật

@apiSuccessExample {json} Response: Data In 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "_id": 1,
        "config_connect": {
            "database_name": "ladder",
            "database_password": "root",
            "database_username": "root",
            "host": "localhost",
            "port": 5432
        },
        "config_mapping_data": {
            "fields": [
                {
                    "action": "add",
                    "field_source": "employee_code",
                    "field_target": "name",
                    "object": "profiles",
                    "required": true,
                    "value_type": "record"
                },
                {
                    "action": "add",
                    "field_source": "phone_number",
                    "field_target": "phone_number",
                    "object": "profiles",
                    "required": true,
                    "value_type": "record"
                }
            ],
            "table": "contract"
        },
        "config_rule_unification": {
            "consent": {
                "analytics_consent": "Có",
                "mkt_consent": "Có",
                "tracking_consent": "Có"
            },
            "data_recording_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "primary_phone": {
                                "match_type": "exact_normalized",
                                "normalized_type": "phone"
                            }
                        },
                        "priority": 1
                    }
                ]
            },
            "data_update_rules": {
                "operators": [
                    {
                        "fields": {
                            "name": {
                                "match_type": "exact",
                                "normalized_type": "string"
                            },
                            "primary_phone": {
                                "match_type": "exact_normalized",
                                "normalized_type": "phone"
                            }
                        },
                        "priority": 1
                    }
                ]
            }
            
        },
        "config_sync_calendar": {
            "auto_retry_w_error": {
                "status": 0
            },
            "mode": "snapshot",
            "schedule": {
                "config": {
                    "hour": "16:49",
                    "type": "day",
                    "values": []
                },
                "type": "interval"
            },
            "config_snapshot": {
                "snapshot_type": "incremental",
                "checkpoint": {
                    "field_name": "created_time",
                    "field_type": "datetime",
                    "last_checkpoint": "2025-11-12 15:12:14",
                    "is_use_custom_checkpoint": true,
                    "option": {
                        "option_type": "selected",
                        "checkpoint_value": "2025-11-12 15:12:14",
                        "session_id": "123456789"
                    },
                    "updated_time": "2024-04-01T14:52Z"
                },
                "partition": 10000
            }   
        },
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2024-03-28T04:42Z",
        "description": "Đồng bọ",
        "is_trust_source": true,
        "list_field_verify": [
            "name"
        ],
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name": "Profile",
        "object": "profiles",
        "source_key": "postgres",
        "source_type": "databases",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_time": "2024-04-01T14:52Z"
    }
}

@apiSuccessExample {json} Response: Data Out 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "_id": 1,
        "config_connect": {
            "app_id": "",
            "url": "",
            "methods": ["GET", "POST"],
            "param-headers": [
                {
                    "key": "mac",
                    "value": "sha256(app_id + data_string + app_secret)",
                    "type": "formula/string",
                    "created_by": "system"
                },
                {
                    "key": "",
                    "value": "",
                    "type": "string"
                }
            ],
            "content-type": "application/json",
        },
        "contact_info": {
                "type": "email",
                "emails": ["<EMAIL>"]
        },
        "config_information_out": [
            {
                "module": "profile",
                "event_keys": [
                    ""
                ]
            }
        ],
        "config_sync_calendar": {
            "auto_retry_w_error": {
                "status": 0
            },
            "mode": "streaming",
            "schedule": {
            }
        },
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2024-03-28T04:42Z",
        "description": "Đồng bọ",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name": "Profile",
        "source_key": "webhooks",
        "source_type": "server",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_time": "2024-04-01T14:52Z"
    }
}
"""

# ---------- Define body update connector -----------
"""
@apiDefine  BodyRequestUpdateConnector
@apiParam   (BODY:) {string}            [name]                                                Tên nguồn dữ liệu
@apiParam   (BODY:) {string=upsert,add,update}            [action=upsert]                     Thao tác với dữ liệu
                                                                                              <ul>
                                                                                                    <li>upsert: Cập nhật hoặc thêm mới</li>
                                                                                                    <li>add: Thêm mới</li>
                                                                                                    <li>update: Cập nhật</li>
                                                                                              </ul>
                                                                                              <ul>
                                                                                                - Đối tượng là Profile thì mặc định sẽ là upsert
                                                                                                - Đối tượng là Sale thì add hoặc update
                                                                                              </ul>
                                                                                              
                               
@apiParam   (BODY:) {string}            [description]                                         Mô tả nguồn dữ liệu
@apiParam   (BODY:) {string}            [source_key]                                          Source key cần kết nối
@apiParam   (BODY:) {string}            [source_type]                                         Loại


@apiParam   (BODY:) {object}            [config_connect]                                      Thông tin kết nối. Tuỳ thuộc vào connector sẽ đưa cấu hình tương ứng.
                                                                                                <ul>
                                                                                                    <li>Thông tin cấu hình kết nối của Data In Postgres: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataInPostgres">tại đây</a></li>
                                                                                                    <li>Thông tin cấu hình kết nối của Data In Api: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataInApi">tại đây</a></li>
                                                                                                    <li>Thông tin cấu hình kết nối của Data Out Webhooks: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataOutWebhooks">tại đây</a></li>
                                                                                                    <li>Thông tin cấu hình kết nối của Data In ConnectForm: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataInContactForm">tại đây</a></li>
                                                                                                    <li>Thông tin cấu hình kết nối của Data Out GoogleSheet: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataOutGoogleSheet">tại đây</a></li>
                                                                                                </ul>
                                                                                            
@apiParam   (BODY:) {object}            config_sync_calendar                            Cấu hình thời gian lập lịch sync data
@apiParam   (BODY:) {string}            config_sync_calendar.mode                       Cơ chế đồng bộ
                                                                                        <ul>
                                                                                            <li>streaming</li>
                                                                                            <li>snapshot</li>
                                                                                        </ul>
@apiParam   (BODY:) {object}            config_sync_calendar.auto_retry_w_error          Cấu hình tự động đồng bộ khi xảy ra lỗi
@apiParam   (BODY:) {int}               config_sync_calendar.auto_retry_w_error.status   Trạng thái của cấu hình này.
                                                                                        <ul>
                                                                                            <li>0: là tắt</li>
                                                                                            <li>1: là bật</li>
                                                                                        </ul>
@apiParam   (BODY:) {int}               config_sync_calendar.auto_retry_w_error.number_max_retry      Số lần đồng bộ lại khi gặp lỗi                                                                                
@apiParam   (BODY:) {int}               config_sync_calendar.auto_retry_w_error.retry_interval_sec    Khoảng thời gian giữa các lần đồng bộ lại.
@apiParam   (BODY:) {object}            [config_sync_calendar.schedule]                 Cấu hình lập lịch chạy
@apiParam   (BODY:) {string}            [config_sync_calendar.schedule.type]            Kiểu lập lịch
                                                                                        <ul>
                                                                                            <li>manually: thủ công</li>
                                                                                            <li>interval: định kỳ</li>
                                                                                        </ul>
@apiParam   (BODY:) {object}            [config_sync_calendar.schedule.config]           Cấu hình thời gian lặp theo định kỳ
@apiParam   (BODY:) {object}            [config_sync_calendar.schedule.config.hour]      Thời gian đồng bộ. Định dạng (HH:MM). Example (16:49)
@apiParam   (BODY:) {object}            [config_sync_calendar.schedule.config.type]      Kiểu lịch trình
                                                                                        <ul>
                                                                                            <li><code>day</code>: Ngày</li>
                                                                                            <li><code>week</code>: Tuần</li>
                                                                                            <li><code>month</code>: Tháng</li>
                                                                                        </ul>
@apiParam   (BODY:) {object}            [config_sync_calendar.schedule.config.type_select_day_in_month]      Kiểu chọn ngày trong tháng
                                                                                                        <ul>
                                                                                                            <li><code>exact_day</code>: Ngày cụ thể</li>
                                                                                                            <li><code>flex_day</code>: Ngày linh hoạt</li>
                                                                                                        </ul>                                                                                    
@apiParam   (BODY:) {Array}             [config_sync_calendar.schedule.config.values]           Cấu hình thông tin lặp vào (Ví dụ: thứ 2, thứ 3)
                                                                                                <ul>
                                                                                                    <li> Case: type=<code>day</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                                                    <li> Case: type=<code>week</code> thì <code>values: danh sách các thứ trong tuần - ví dụ ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]</code></li>
                                                                                                    <li> Case: type=<code>month</code> thì <code>values: Danh sách các ngày trong tháng (Mặc định là 31 ngày) - ví dụ [1,2,3,4,5,6,7]</code> hoặc values có thể là : <code>first_of_month</code> (Ngày đầu tháng), <code>last_of_month </code> (Ngày cuối tháng)</li>
                                                                                                </ul>
@apiParam   (BODY:) {object}            [config_sync_calendar.snapshot_config]                     Cấu hình snapshot
@apiParam   (BODY:) {string}            [config_sync_calendar.snapshot_config.snapshot_type]    Kiểu thực hiện đồng bộ snapshot
                                                                                                        <ul>
                                                                                                            <li><code>full</code>: full</li>
                                                                                                            <li><code>incremental</code>: incremental</li>
                                                                                                        </ul>   
@apiParam   (BODY:) {object}            [config_sync_calendar.snapshot_config.checkpoint]       Thông tin checkpoint
@apiParam   (BODY:) {string}            [config_sync_calendar.snapshot_config.checkpoint.field_name]     Tên field được chọn làm checkpoint
@apiParam   (BODY:) {string}            [config_sync_calendar.snapshot_config.checkpoint.field_type]        Kiểu dữ liệu của field được chọn làm checkpoint
@apiParam   (BODY:) {boolean}            [config_sync_calendar.snapshot_config.checkpoint.is_use_custom_checkpoint]          Sử dụng giá trị tùy chọn
                                                                                                        <ul>
                                                                                                            <li><code>true</code>:  Có sử dụng giá trị tùy chọn</li>
                                                                                                            <li><code>fasle</code>:  Không sử dụng giá trị tùy chọn</li>
                                                                                                        </ul>  

@apiParam   (BODY:) {object}            [config_sync_calendar.snapshot_config.checkpoint.option]               Thông tin giá trị tùy chọn
@apiParam   (BODY:) {string}            [config_sync_calendar.snapshot_config.checkpoint.option.option_type]   Loại giá trị tùy chọn
                                                                                                        <ul>
                                                                                                            <li><code>beginning</code>:  Đồng bộ từ bản tin đầu tiên</li>
                                                                                                            <li><code>selected</code>:  Giá trị được chọn</li>
                                                                                                        </ul>   
@apiParam   (BODY:) {string}            [config_sync_calendar.snapshot_config.checkpoint.option.option_value]   Giá trị tùy chọn  
 
@apiParam   (BODY:) {int}            [config_sync_calendar.snapshot_config.partition]               Số lượng mỗi partition
@apiParam   (BODY:) {boolean}            [config_sync_calendar.snapshot_config.is_edit_checkpoint]          Có ấn lưu edit incremental
                                                                                                        <ul>
                                                                                                            <li><code>true</code>:  Có</li>
                                                                                                            <li><code>false</code>:  Không</li>
                                                                                                        </ul> 
@apiParam   (BODY:) {Any}            field_other                                                Danh sách các field tương ứng đối với từng loại. Cần lấy field tương ứng với từng loại và ghép vào Body.
                                                                                                <ul>
                                                                                                    <li>Data-In, Postgres :: Doc <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyUpdateConnectDataInPostgres">tại đây</a></li>
                                                                                                </ul>
                                                                                                <ul>
                                                                                                    <li>Data-In, Db2 :: Doc <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyUpdateConnectDataInDb2">tại đây</a></li>
                                                                                                </ul>
                                                                                                <ul>
                                                                                                    <li>Data-In, Api :: Doc <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyUpdateConnectDataInApi">tại đây</a></li>
                                                                                                </ul>
                                                                                                <ul>
                                                                                                    <li>Data-Out, Webhooks :: Doc <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyUpdateConnectDataOutWebhooks">tại đây</a></li>
                                                                                                </ul>
                                                                                                <ul>
                                                                                                    <li>Data-Out, GoogleSheet :: Doc <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyUpdateConnectDataOutGoogleSheet">tại đây</a></li>
                                                                                                </ul>
                                                                                                <ul>
                                                                                                    <li>Data-In, ContactForm :: Doc <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyUpdateConnectDataInContactForm">tại đây</a></li>
                                                                                                </ul>
@apiParamExample {json} Body: Data-In, Postgres
{
    "object": "profiles",
    "is_trust_source": true,
    "action": "upsert",
    "list_field_verify": [
        "cif",
        "customer_id"
    ],
    "config_mapping_data": {
        "table": "profile",
        "fields": [
            {
                "field_source": "id",
                "field_target": "customer_id",
                "object": "profiles",
                "value_type": "record",
                "required": true,
                "action": "add"
            },
            {
                "field_source": "name",
                "field_target": "name",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "phone",
                "field_target": "primary_phone",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "email",
                "field_target": "primary_email",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            },
            {
                "field_source": "cif",
                "field_target": "cif",
                "object": "profiles",
                "value_type": "record",
                "required": false,
                "action": "add"
            }
        ]
    },
    "config_rule_unification": {
        "data_recording_rules": {
            "operators": [
                {
                    "fields": {
                        "name": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        },
                        "primary_phone": {
                            "match_type": "exact_normalized",
                            "normalized_type": "phone"
                        }
                    },
                    "priority": 1
                }
            ]
        },
        "data_update_rules": {
            "operators": [
                {
                    "fields": {
                        "name": {
                            "match_type": "exact",
                            "normalized_type": "string"
                        },
                        "primary_phone": {
                            "match_type": "exact_normalized",
                            "normalized_type": "phone"
                        }
                    },
                    "priority": 1
                }
            ]
        },
        "consent": {
            "analytics_consent": "Có",
            "tracking_consent": "Có",
            "mkt_consent": "Có"
        }
    },
    "config_sync_calendar": {
        "mode": "snapshot",
        "auto_retry_w_error": {
            "status": 0,
            "number_max_retry": 10,
            "retry_interval_sec": 3600
        },
        "schedule": {
            "type": "interval",
            "config": {
                "hour": "16:49",
                "type": "day",
                "type_select_day_in_month": "flex_day",
                "values": [
                    "first_of_month"
                ]
            }
        },
        "config_snapshot": {
            "snapshot_type": "incremental",
            "checkpoint": {
                "field_name": "created_time",
                "field_type": "datetime",
                "last_checkpoint": "2025-11-12 15:12:14",
                "is_use_custom_checkpoint": true,
                "option": {
                    "option_type": "selected",
                    "checkpoint_value": "2025-11-12 15:12:14"
                }
            },
            "partition": 10000
        }
    }
}
"""

# ---------- Lấy danh sách nguồn dữ liệu -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/sources              Lấy danh sách nguồn dữ liệu
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName GetListSourceDataFlow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy
@apiParam   (QUERY:) {string}       [type]                                         Loại nguồn cần lấy dữ liệu.
@apiParam   (QUERY:) {string}       [key]                                          Source key cần lấy, Nếu trong trường hợp lấy nhiều thì cần truyền lên ngăn cách nhau bởi dấu ,
                                                                                    <code>Nếu không truyền thì mặc định lấy tất cả</code>
@apiParam   (QUERY:) {string}       [search]                                        Từ khoá cần tìm kiếm.

@apiSuccess   {Array}   data                                                      
@apiSuccess   {string}  data.name                                                   Tên nguồn dữ liệu
@apiSuccess   {string}  data.key                                                    Source key
@apiSuccess   {string}  data.type                                                   Loại nguồn dữ liệu 
@apiSuccess   {string}  data.detail_type                                            Chi tiết của nguồn dữ liệu
@apiSuccess   {string}  data.icon                                                   Ảnh icon của nguồn dữ liệu dạng <code>base64</code>
@apiSuccess   {string}  data.description                                            Mô tả của nguồn dữ liệu

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "_id": "660a2cc40faf00cefa002404",
            "description": "",
            "detail_type": "sql",
            "icon": "",
            "id": "660a2cc40faf00cefa002404",
            "key": "postgres",
            "name": "Postgres",
            "order": 0,
            "type": "databases"
        },
    ]
}
"""
# ---------- Xoá nguồn dữ liệu -----------
"""
@api {DELETE} {domain}/market-place/api/v1.0/data-flow/connectors              Xoá connectors
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName DeleteSourceDataFlow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy
@apiParam   (QUERY:) {string}       ids                                          Danh sách id nguồn cần xoá dữ liệu. Nếu cần xoá nhiều thì truyền lên danh sách id ngăn cách nhau bởi dấu ,

@apiSuccess   {Object}   data                                                      
@apiSuccess   {object}  data.success                                             Thông tin dữ liệu xoá thành công
@apiSuccess   {int}  data.success.number                                          Số lượng xoá thành công
@apiSuccess   {object}  data.fail                                             Thông tin dữ liệu xoá thất bại
@apiSuccess   {int}  data.fail.number                                          Số lượng xoá thất bại

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "success": {
            "number": 1
        },
        "fail": {
            "number": 0
        }
    }
}
"""

# ---------- Giải mã thông tin cấu hình kết nối -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/connectors/<connector_id>/decrypt-connection-config              Giải mã thông tin cấu hình kết nối.
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName DecodeInformationConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string}       key                                          Danh sách key cần giải mã. Nếu muốn lấy thông tin của nhiều field thì truyền ngăn cách nhau bởi dấu ,

@apiSuccess   {Object}   data                                                    Dữ liệu được giải mã

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "database_name": "help_me"
    }
}
"""
# ---------- Lấy danh sách loại nguồn dữ liệu -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/source-types              Lấy danh sách loại nguồn dữ liệu
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName GetListSourceTypeDataFlow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiSuccess   {Array}   data                                                      
@apiSuccess   {string}  data.key                                                    Key loại nguồn dữ liệu
@apiSuccess   {string}  data.name                                                   Tên loại nguồn dữ liệu
@apiSuccess   {string}  data.order                                                  Vị trí

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "key": "all",
            "name": "Tất cả",
            "order": 0
        },
        {
            "key": "databases",
            "name": "Databases",
            "order": 1
        },
        {
            "key": "server",
            "name": "Server",
            "order": 2
        },
        {
            "key": "message_queue",
            "name": "Message Queue",
            "order": 3
        },
        {
            "key": "website",
            "name": "Website",
            "order": 4
        },
        {
            "key": "mobile",
            "name": "Mobile",
            "order": 5
        },
        {
            "key": "other",
            "name": "Khác",
            "order": 6
        }
    ]
}
"""


# ---------- Lấy danh sách IP cần được Whitelist -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/whitelist/ips              Lấy danh sách IP cần Whitelist
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName WhiteListIPS

@apiSuccess   {Array}   data                                                      Danh sách IP cần Whitelist

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        "170.056.456"
    ]
}
"""


# ---------- Kiểm tra kết nối tới Database -----------
"""
@api {POST} {domain}/market-place/api/v1.0/data-flow/databases/check-connection                 Kiểm tra kết nối tới database
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName DatabaseCheckConnection

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiParam   (BODY:) {string}            source_key                                          Source key cần kết nối
@apiParam   (BODY:) {string}            source_type                                         Loại
@apiParam   (BODY:) {object}            config_connect                                      Thông tin kết nối
                                                                                            <ul>
                                                                                                <li>Thông tin cấu hình kết nối của Data In Postgres: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataInPostgres">tại đây</a></li>
                                                                                                <li>Thông tin cấu hình kết nối của Data Out Webhooks: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataOutWebhooks">tại đây</a></li>
                                                                                                <li>Thông tin cấu hình kết nối của Data In Contact Form: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataInApi">tại đây</a></li>
                                                                                                <li>Thông tin cấu hình kết nối của Data Out GoogleSheet: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataOutGoogleSheet">tại đây</a></li>
                                                                                            </ul>

@apiSuccess   {Object}   data                                                      Thông tin kiểm tra kết nối
@apiSuccess   {String}   data.status                                               Trạng thái kết nối
                                                                                   <ul>
                                                                                        <li><code>success</code>: Thành công</li>
                                                                                        <li><code>fail</code>: Thất bại</li>
                                                                                   </ul>
@apiSuccess   {Array}   data.information                                           Thông tin kiểm tra kết nối
@apiSuccess   {String}   data.information.name                                      Tên thông tin kiểm tra kết nối
@apiSuccess   {String}   data.information.status                                    Trạng thái kiểm tra thông tin kết nối
                                                                                    <ul>
                                                                                        <li><code>success</code>: Thành công</li>
                                                                                        <li><code>fail</code>: Thất bại</li>
                                                                                   </ul>
@apiSuccess   {Array}   data.information.status                                    Trạng thái kiểm tra thông tin kết nối
                                                                                    <ul>
                                                                                        <li><code>success</code>: Thành công</li>
                                                                                        <li><code>fail</code>: Thất bại</li>
                                                                                   </ul>
@apiSuccess   {Array}   data.information.errors                                    Danh sách lỗi. Trong tường hợp Trạng thái kết nối là thất bại                                                                                                                                   


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "information": [
            {
                "name": "Kết nối database",
                "status": "success"
            },
            {
                "name": "Authorization",
                "status": "success"
            },
            {
                "name": "Database",
                "status": "success"
            },
            {
                "name": "Permission",
                "status": "success"
            }
        ],
        "status": "success"
    }
}
"""

# ---------- Kiểm tra kết nối tới tới connector -----------
"""
@api {POST} {domain}/market-place/api/v1.0/data-flow/check-connection                 Kiểm tra kết nối tới connector
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName CheckConnection

@apiDescription Tuỳ thuộc vào loại connector gửi cấu hình connect tương ứng.

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiParam   (BODY:) {string}            source_key                                          Source key cần kết nối
@apiParam   (BODY:) {string}            source_type                                         Loại
@apiParam   (BODY:) {object}            config_connect                                      Thông tin kết nối
                                                                                            <ul>
                                                                                                <li>Thông tin cấu hình kết nối của Data In Postgres: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataInPostgres">tại đây</a></li>
                                                                                                <li>Thông tin cấu hình kết nối của Data Out Webhooks: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataOutWebhooks">tại đây</a></li>
                                                                                            </ul>

@apiSuccess   {Object}   data                                                      Thông tin kiểm tra kết nối
@apiSuccess   {String}   data.status                                               Trạng thái kết nối
                                                                                   <ul>
                                                                                        <li><code>success</code>: Thành công</li>
                                                                                        <li><code>fail</code>: Thất bại</li>
                                                                                   </ul>
@apiSuccess   {Array}   data.information                                           Thông tin kiểm tra kết nối
@apiSuccess   {String}   data.information.name                                      Tên thông tin kiểm tra kết nối
@apiSuccess   {String}   data.information.status                                    Trạng thái kiểm tra thông tin kết nối
                                                                                    <ul>
                                                                                        <li><code>success</code>: Thành công</li>
                                                                                        <li><code>fail</code>: Thất bại</li>
                                                                                   </ul>
@apiSuccess   {Array}   data.information.status                                    Trạng thái kiểm tra thông tin kết nối
                                                                                    <ul>
                                                                                        <li><code>success</code>: Thành công</li>
                                                                                        <li><code>fail</code>: Thất bại</li>
                                                                                   </ul>
@apiSuccess   {Array}   data.information.errors                                    Danh sách lỗi. Trong tường hợp Trạng thái kết nối là thất bại                                                                                                                                   


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "information": [
            {
                "name": "Kết nối database",
                "status": "success"
            },
            {
                "name": "Authorization",
                "status": "success"
            },
            {
                "name": "Database",
                "status": "success"
            },
            {
                "name": "Permission",
                "status": "success"
            }
        ],
        "status": "success"
    }
}
"""

# ---------- Tạo connectors -----------
"""
@api {POST} {domain}/market-place/api/v1.0/data-flow/connectors                 Khởi tạo connectors
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName CreateConnectors

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy


@apiParam   (BODY:) {string}            name                                                Tên nguồn dữ liệu
@apiParam   (BODY:) {string}            description                                         Mô tả nguồn dữ liệu
@apiParam   (BODY:) {string}            source_key                                          Source key cần kết nối
@apiParam   (BODY:) {string}            source_type                                         Loại
@apiParam   (BODY:) {object}            config_connect                                      Thông tin kết nối
                                                                                            <ul>
                                                                                                <li>Thông tin cấu hình kết nối của Data In Postgres: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataInPostgres">tại đây</a></li>
                                                                                                <li>Thông tin cấu hình kết nối của Data Out Webhooks: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataOutWebhooks">tại đây</a></li>
                                                                                                <li>Thông tin cấu hình kết nối của Data In Api: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataInApi">tại đây</a></li>
                                                                                                <li>Thông tin cấu hình kết nối của Data Out GoogleSheet: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-RequestBodyInformationConnectDataOutGoogleSheet">tại đây</a></li>
                                                                                            </ul>

@apiParamExample {json} Body: [Data-In][postgres]
{   

    "name": "Đồng bộ profile",
    "description": "Đồng bộ profile",
    "source_key": "postgres",
    "source_type": "databases",
    "config_connect": {
        "host": "127.0.0.1",
        "port": 3306,
        "database_name": "profile",
        "database_username": "user1",
        "database_password": "password"
    }
}

@apiSuccess   {Object}   data                                                      Dữ liệu trả về khi tạo connector thành công
@apiSuccess   {Int}   data._id                                                     <code>ID</code> record
@apiSuccess   {Bool}   data.is_trust_source                                          Có phải là trust source hay không.
                                                                                    <ul>
                                                                                        <li><code>true</code> Là trust source</li>
                                                                                        <li><code>false</code> Không là trust source</li>
                                                                                    </ul>
@apiSuccess     {String}            data.status_sync                                Trạng thái đồng bộ
@apiSuccess     {Object}            data.config_connect                             Thông tin cấu hình. Tuỳ thuộc vào loại connector sẽ trả về thông tin tương ứng.
@apiSuccess     {string}            data.created_by                                 Thông tin nhân viên tạo
@apiSuccess     {string}            data.updated_by                                 Thông tin nhân viên cập nhật
@apiSuccess     {string}            data.created_time                               Thời gian tạo
@apiSuccess     {string}            data.updated_time                               Thời gian cập nhật

@apiSuccessExample {json} Response: [Data-In][postgres]
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "_id": 3,
        "config_connect": {
            "database_name": "ladder",
            "database_password": "root",
            "database_username": "root",
            "host": "localhost",
            "port": 5432
        },
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2024-04-01T01:55Z",
        "description": "Đồng bọ",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name": "Profile",
        "source_key": "postgres",
        "source_type": "databases",
        "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_time": "2024-04-01T01:55Z"
    }
}
"""
# ---------- Lấy danh sách bảng của connectors với loại là database -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/connections/<connector-id>/databases/tables                 Danh sách bảng của connectors loại database
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName GetTablesOfDatabases

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess   {Array}   data                                                      Danh sách tên table

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        "Table1",
        "Table2"
    ]
}
"""

# ---------- Lấy dữ liệu mẫu table -----------
"""
@api {POST} {domain}/market-place/api/v1.0/data-flow/connections/<connector-id>/databases/tables/data/actions/get-sample                 Lấy danh sách dữ liệu mẫu của bảng
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName TablesDataSample

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:) {int}              [number_item=1000]                                    Số lượng phần tử muốn lấy. <code>Max: 1000 bản tin</code>
@apiParam   (BODY:) {string}            table_name                                          Tên table cần lấy dữ liệu
@apiParam   (BODY:) {string}            [schema]                                          Tên schema cần lấy dữ liệu
@apiParamExample {json} Body
{
    "table_name": "Table1",
}

@apiSuccess   {Object}   data                                                      Danh sách tên table
@apiSuccess   {Array}   data.colums                                                Danh sách các cột
@apiSuccess   {string}   data.colums.column_name                                    Tên cột
@apiSuccess   {int}   data.colums.order                                          Thứ tự của cột
@apiSuccess   {string}   data.colums.data_type                                      Kiểu dữ liệu  của cột 
@apiSuccess   {bool}   data.colums.valid_column_format                            Tên cột có đúng định dạng: <code>True/False</code>
@apiSuccess   {bool}   data.colums.valid_datatype                            Tên cột có được support: <code>True/False</code>
@apiSuccess   {bool}   data.colums.primary_key                            Tên cột có là primary key: <code>True/False</code>
@apiSuccess   {Array}   data.rows                                                  Dữ liệu của từng dòng

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "columns": [
            {
                "column_name": "id",
                "order": 1,
                "data_type: "int",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": True
            },
            {
                "column_name": "source",
                "order": 2,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "cif",
                "order": 3,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "name",
                "order": 4,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "phone",
                "order": 5,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "email",
                "order": 6,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "location",
                "order": 8,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "single_text_chu",
                "order": 11,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "single_text_so_thap_phan",
                "order": 13,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "multiple_chu",
                "order": 14,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "multiple_so",
                "order": 15,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "radio",
                "order": 16,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "checkbox",
                "order": 17,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "dropdown_single_select",
                "order": 18,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "dropdown_multiple_select",
                "order": 19,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "date_ddmm",
                "order": 20,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "date_ddmmyyyy",
                "order": 21,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "date_ddmmyyhhmm",
                "order": 22,
                "data_type: "varchar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "system_date",
                "order": 23,
                "data_type: "datetime",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "data_date",
                "order": 24,
                "data_type: "date",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "single_text_so_nguyen",
                "order": 25,
                "data_type: "vachar",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            },
            {
                "column_name": "birth_date",
                "order": 26,
                "data_type: "date",
                "valid_column_format": True,
                "valid_datatype": True,
                "primary_key": False
            }
        ],
        "rows": [
            {
                "contract_file_scan_link": "2",
                "contract_id": 1,
                "contract_number": "1",
                "created_at": null,
                "created_by": "1",
                "status": 1,
                "updated_at": null,
                "updated_by": "1"
            },
            {
                "contract_file_scan_link": null,
                "contract_id": 2,
                "contract_number": "2",
                "created_at": null,
                "created_by": "12312",
                "status": 3,
                "updated_at": null,
                "updated_by": "1"
            }
        ]
    
}
"""
# ---------- Lấy danh sách trường thông tin và kiểu dữ liệu của tables -----------
"""
@api {POST} {domain}/market-place/api/v1.0/data-flow/connections/<connector-id>/databases/tables/schemas             Lấy thông tin schemas của tables
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName TablesSchema

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {string}            table_name                                          Tên table cần lấy dữ liệu
@apiParam   (BODY:) {string}            [schema]                                          Tên schema cần lấy dữ liệu (Postgres/DB2 cần có)
@apiParamExample {json} Body
{
    "table_name": "Table1",
    "schema: "db2inst1"
}

@apiSuccess   {Array}   data                                                      Danh sách tên table
@apiSuccess   {int}   data.character_maximum_length                               Số lượng ký tự tối đa
@apiSuccess   {string}   data.column_name                               Tên cột
@apiSuccess   {string}   data.data_type                               Kiểu dữ liệu
@apiSuccess   {string}   data.is_nullable                               Cho phép giá trị null hay không?
@apiSuccess   {int}   data.ordinal_position                             Vị trí
@apiSuccess   {string}   data.type                                      Dạng dữ liệu được hiển thị trong database. <code>Giá trị field này sẽ được truyền vào field: field_source_type trong phần mapping</code>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "character_maximum_length": null,
            "column_name": "history_id",
            "data_type": "integer",
            "is_nullable": "NO",
            "ordinal_position": 1,
            "type": "NUMBER(38,0)"
        },
        {
            "character_maximum_length": 20,
            "column_name": "employee_code",
            "data_type": "character",
            "is_nullable": "YES",
            "ordinal_position": 2,
            "type": "VARCHAR2(20)"
        },
        {
            "character_maximum_length": null,
            "column_name": "before_data",
            "data_type": "json",
            "is_nullable": "YES",
            "ordinal_position": 3,
            "type": "JSON"
        },
        {
            "character_maximum_length": null,
            "column_name": "after_data",
            "data_type": "json",
            "is_nullable": "YES",
            "ordinal_position": 4,
            "type": "JSON"
        },
        {
            "character_maximum_length": null,
            "column_name": "status",
            "data_type": "integer",
            "is_nullable": "NO",
            "ordinal_position": 5,
            "type": "NUMBER(38,0)"
        },
        {
            "character_maximum_length": null,
            "column_name": "created_by",
            "data_type": "character",
            "is_nullable": "YES",
            "ordinal_position": 6,
            "type": "VARCHAR2(20)"
        },
        {
            "character_maximum_length": null,
            "column_name": "updated_by",
            "data_type": "character",
            "is_nullable": "YES",
            "ordinal_position": 7,
            "type": "VARCHAR2(20)"
        },
        {
            "character_maximum_length": null,
            "column_name": "created_at",
            "data_type": "timestamp",
            "is_nullable": "YES",
            "ordinal_position": 8,
            "type": "TIMESTAMP"
        },
        {
            "character_maximum_length": null,
            "column_name": "updated_at",
            "data_type": "timestamp",
            "is_nullable": "YES",
            "ordinal_position": 9,
            "type": "TIMESTAMP"
        }
    ]
}
"""

# ---------- Cập nhật cấu hình connectors -----------
"""
@api {PUT} {domain}/market-place/api/v1.0/data-flow/connections/<connector-id>             Cập nhật cấu hình connectors
@apiVersion 1.0.0
@apiName UpdateConnectors
@apiGroup DataFlow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy                                                                            
                                                                                            
@apiUse BodyRequestUpdateConnector

@apiSuccess   {Object}   data                                                      Dữ liệu trả về 
@apiUse DetailConnector
"""
# ---------- Bật tắt đồng bộ dữ liệu. -----------
"""
@api {POST} {domain}/market-place/api/v1.0/data-flow/connections/<connector-id>/actions/sync             Thao tác đồng bộ dữ liệu của connector
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName ConnectorActionSyncData

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiParam   (BODY:) {string=on,off}            action                                           Hành đồng thao tác đồng bộ dữ liệu
                                                                                                <ul>
                                                                                                    <li><code>on</code> bật đồng bộ dữ liệu, đối với những connector setup lịch đồng bộ là <code>thủ công</code></li>
                                                                                                    <li><code>off</code> tắt đồng bộ dữ liệu<code>Tạm thời chưa hỗ trợ</code></li>
                                                                                                </ul>
@apiParamExample {json} Body
{
    "action": "on",
}
@apiSuccess   {object}             data                                                     Danh sách nguồn kết nối dữ liệu

@apiSuccess     {object}          data.detail_connector                                 Chi tiết connector của nguồn dữ liệu


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "detail_connector": {
                "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
                "created_time": "2024-04-10T04:15Z",
                "id": 5,
                "is_type_sync_manunal": True,
                "description": "Test",
                "name": "Connector 1",
                "source_key": "postgres",
                "source_type": "databases",
                "is_trust_source": true,
                "object": "profiles",
                "status_sync": null,
                "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
                "updated_time": "2024-04-10T10:42Z"
            }
    }
}
"""

# ---------- Lấy detail connector. -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/connections/<connector-id>             Lấy detail connector
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName DetailConnector

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiSuccess   {Object}   data                                                      Dữ liệu trả về
@apiUse DetailConnector
"""
# ---------- Cập nhật trạng thái đồng bộ dữ liệu của connector -----------
"""
@api {PUT} {domain}/market-place/api/v1.0/data-flow/connections/<connector_id>/update/status-sync-data             Cập nhập trạng thái đồng bộ dữ liệu của Connector
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName UpdateStatusSyncData

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiParam   (BODY:) {string}            status_sync                                             Trạng thái đồng bộ
                                                                                                <ul>
                                                                                                    <li><code>running</code> đang đồng bộ</code></li>
                                                                                                    <li><code>done</code> hoàn thành</code></li>
                                                                                                    <li><code>stopped</code> bị dừng đồng bộ dữ liệu</code></li>
                                                                                                    <li><code>sync_error</code> lỗi đồng bộ dữ liệu</code></li>
                                                                                                    <li><code>not_data_sync</code> không có dữ liệu đồng bộ</code></li>
                                                                                                </ul>
@apiParam   (BODY:) {string}            orchestration_id                                        <code>ID</code> của orchestration
@apiParam   (BODY:) {string}            [reason]                                                Lý do dừng đồng bộ dữ liệu.
@apiParamExample {json} Body
{
    "status_sync": "running",
    "orchestration_id": "",
    "reason": "",
}


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""
# ---------- Lấy state của connectors -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/connections/<connector_id>/state             Lấy state của connector
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName GetStateConnector

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "action_time": "*********",
            "account_action": "",
            "state": ""
        }
    ]
    "message": "request thành công."
}
"""
# ---------- Lấy danh sách nguồn dữ liệu data flow -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/sources-connections             Lấy danh sách source cấu hình connect
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName GetSourceConnections

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy
@apiParam (QUERY:)      {string}       [search]           Từ khoá dùng để tìm kiếm

@apiSuccess   {Array}             data                                                     Danh sách nguồn kết nối dữ liệu
@apiSuccess     {string}          data.source_key                                          Source key cần kết nối
@apiSuccess     {string}          data.source_type                                         Loại nguồn dữ liệu
@apiSuccess     {int}             data.number_connector                                    Số lượng connector
@apiSuccess     {object}          [data.detail_connector]                                  Chi tiết connector của nguồn dữ liệu
@apiSuccess     {any}             [data.detail_connector.field]                            Các field sẽ được define dựa theo từng loại connector. Data In/Out
                                                                                           <ul>
                                                                                            <li>DataIn-Postgres: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-ResponseSuccessConnectDataInPostgres">Tại đây</a></li>
                                                                                            <li>DataIn-Api: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-ResponseSuccessConnectDataInApi">Tại đây</a></li>
                                                                                            <li>DataOut-Webhooks: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-ResponseSuccessConnectDataOutWebhooks">Tại đây</a></li>
                                                                                           </ul>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "detail_connector": {
                "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
                "created_time": "2024-04-10T04:15Z",
                "id": 5,
                "is_type_sync_manunal": True,
                "description": "Test",
                "name": "Connector 1",
                "source_key": "postgres",
                "source_type": "databases",
                "is_trust_source": true,
                "object": "profiles",
                "status_sync": null,
                "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
                "updated_time": "2024-04-10T10:42Z"
            },
            "number_connector": 1,
            "source_key": "postgres1",
            "source_type": "databases"
        },
        {
            "number_connector": 3,
            "source_key": "postgres",
            "source_type": "databases"
        }
    ],
    "message": "request thành công."
}
"""

# ---------- Lấy danh sách connector của nguồn dữ liệu data flow -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/sources-connections/<source_key>/connectors             Lấy danh sách connector của source cấu hình connect
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName GetListConnectorOfSourceConnections

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy
@apiParam (QUERY:)      {string}       [search]           Từ khoá dùng để tìm kiếm

@apiSuccess   {Array}             data                                                     Danh sách nguồn kết nối dữ liệu
@apiSuccess     {any}             [data.field]                                              Các field sẽ được define dựa theo từng loại connector. Data In/Out
                                                                                           <ul>
                                                                                            <li>DataIn-Postgres: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-ResponseSuccessConnectDataInPostgres">Tại đây</a></li>
                                                                                            <li>DataIn-Api: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-ResponseSuccessConnectDataInApi">Tại đây</a></li>
                                                                                            <li>DataOut-Webhooks: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-ResponseSuccessConnectDataOutWebhooks">Tại đây</a></li>
                                                                                           </ul>



@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "_id": 6,
            "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "created_time": "2024-04-11T02:19Z",
            "description": "Test",
            "is_type_sync_manunal": True,
            "name": "Connector 1",
            "source_key": "postgres",
            "source_type": "databases",
            "is_trust_source": true,
            "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
            "object": "profiles",
            "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "updated_time": "2024-04-11T03:12Z"
        },
        {
            "_id": 8,
            "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "created_time": "2024-04-12T08:32Z",
            "name": "Connector 1",
            "is_type_sync_manunal": True,
            "source_key": "postgres",
            "source_type": "databases",
            "is_trust_source": true,
            "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
            "object": "profiles",
            "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "updated_time": "2024-04-12T08:32Z"
        },
        {
            "_id": 9,
            "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "created_time": "2024-04-12T09:27Z",
            "name": "Connector 1",
            "is_type_sync_manunal": True,
            "source_key": "postgres",
            "source_type": "databases",
            "is_trust_source": true,
            "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
            "object": "profiles",
            "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "updated_time": "2024-04-12T09:51Z"
        }
    ],
    "message": "request thành công."
}
"""
# ---------- Lấy danh sách đối tượng -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/objects-handled             Lấy danh sách đối tượng xử lý 
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName GetListObjectsHandle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy
@apiParam (QUERY:)      {string}                        source_key                                            Source key cần lấy danh sách

@apiSuccess     {Array}           data                                          Danh sách đối tượng xử lý
@apiSuccess     {strting}         data.name                                     Tên đối tượng
@apiSuccess     {strting}         data.key                                      Key của đối tượng
@apiSuccess     {int}             data.order                                    Vị trí
@apiSuccess     {int}             data.status                                   Trạng thái hỗ trợ
                                                                                <ul>
                                                                                    <li>1: hỗ trợ</li>
                                                                                    <li>0: không hỗ trợ</li>
                                                                                </ul>
@apiSuccess     {array}           data.config_attributes                        Danh sách cấu hình thuộc tính của đối tượng
@apiSuccess     {strting}         data.config_attributes.name                   Tên cấu hình
@apiSuccess     {strting}         data.config_attributes.value                  Giá trị cấu hình
@apiSuccess     {int}             data.config_attributes.order                      Vị trí
@apiSuccess     {int}             data.config_attributes.status                 Trạng thái hỗ trợ
                                                                                <ul>
                                                                                    <li>1: hỗ trợ</li>
                                                                                    <li>0: không hỗ trợ</li>
                                                                                </ul>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "key": "profiles",
            "name": "Profile",
            "order": 1,
            "status": 1,
            "config_attributes": [
                {
                    "name": "Attribute",
                    "value": "attribute",
                    "order": 1,
                    "status": 1
                }
            ]
        },
        {
            "key": "company",
            "name": "Company",
            "order": 2,
            "status": 1
        },
        {
            "key": "sale",
            "name": "Sale",
            "order": 3,
            "status": 0
        },
        {
            "key": "ticket",
            "name": "Ticket",
            "order": 4,
            "status": 0
        }
    ],
    "message": "request thành công."
}
"""
# ---------- Cập nhật trạng thái connect của connector -----------
"""
@api {PUT} {domain}/market-place/api/v1.0/data-flow/connections/<connector-id>/status-connect                 Cập nhật trạng thái kết nối của connector
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName UpdateStatusConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy
@apiParam   (BODY:) {string=on,off}            action                                           Hành động thao kết nối
                                                                                                <ul>
                                                                                                    <li><code>on</code> bật kết nối</li>
                                                                                                    <li><code>off</code> tắt kết nối </li>
                                                                                                </ul>
@apiParamExample {json} Body
{
    "action": "on",
}


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""
# ---------- Lấy danh sách đối tượng dựa theo đốit tượng chính -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/<object_primary_key>/related-objects                 Lấy danh sách đối tượng dựa theo đốit tượng chính
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName GetListRelatedObjects

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam     (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy
@apiParam     (QUERY:)      {string}       object_attribute                                                       Thuộc tính đối tượng cần lấy

@apiSuccess   {Array}             data                                        Danh sách đối tượng xử lý
@apiSuccess     {strting}         data.name                                 Tên đối tượng
@apiSuccess     {strting}         data.key                                 Key của đối tượng
@apiSuccess     {int}             data.order                                 Vị trí
@apiSuccess     {strting}         data.status                                 Trạng thái hỗ trợ
                                                                              <ul>
                                                                                <li>1: hỗ trợ</li>
                                                                                <li>0: không hỗ trợ</li>
                                                                              </ul>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "key": "profiles",
            "name": "Profile",
            "order": 1,
            "status": 1
        },
        {
            "key": "company",
            "name": "Company",
            "order": 2,
            "status": 1
        }
    ],
    "message": "request thành công."
}
"""
# ---------- Lấy danh sách connectors -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/connectors                 Lấy danh sách connectors
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName ListConnectors

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging_tokens

@apiParam (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiParam   (QUERY:) {string}           search                                              Từ khoá tìm kiềm 
@apiParam   (QUERY:) {bool=True,Fasle}           is_trust_source                                       Lọc nguồn đáng tin cậy hay không? 
@apiParam   (QUERY:) {string}           status_sync                                        Trạng thái kết nối
                                                                                            <ul>
                                                                                                <li><code>running</code> :: đang kết nối</li>
                                                                                                <li><code>stop</code> :: ngắt kết nối</li>
                                                                                                <li><code>error</code> :: lỗi kết nối</li>
                                                                                                <li><code>not_sync_in_range_time</code> :: không có dữ liệu trong 1 khoảng thời gian</li>
                                                                                            </ul>
                                                                                            <code>Nhiều thì sẽ ngăn cách nhau bởi dấu ,</code>
@apiParam   (QUERY:) {string}           created_by                                          Lọc theo người tạo
@apiParam   (QUERY:) {string}           [source_types]                                        Lọc theo loại nguồn. Nếu muốn lấy nhiều thì truyền dạng string ngăn cách nhau bởi dấu ,. Vd: databases,server, ...
@apiParam   (QUERY:) {string}           [source_keys]                                         Lọc theo source key. Nếu muốn lấy nhiều thì truyền dạng string ngăn cách nhau bởi dấu ,.  Vd: db2,oracle,postgres,...
@apiParam   (QUERY:) {string}           [start_time]                                         Thời gian bắt đầu lọc thời gian đồng bộ dữ liệu gần nhất, truyền lên giờ utc
@apiParam   (QUERY:) {string}           [end_time]                                         Thời gian kết thúc lọc thời gian đồng bộ dữ liệu gần nhất, truyền lên giờ utc

@apiParam   (QUERY:) {string}           object_attribute                                        Đối tượng đồng bộ dữ liệu
                                                                                            <ul>
                                                                                                <li><code>profile</code> :: Profile</li>
                                                                                                <li><code>product_holding</code> :: Product Holding</li>
                                                                                                <li><code>dynamic_event</code> :: Dynamic Event</li>
                                                                                                <li><code>ticket</code> :: Ticket</li>
                                                                                                 <li><code>sale</code> :: Cơ hội bán</li>
                                                                                            </ul>
                                                                                            <code>Nhiều thì sẽ ngăn cách nhau bởi dấu ,</code>

@apiParam   (QUERY:) {string}           status_sync_conn                                        Trạng thái đồng bộ dữ liệu
                                                                                            <ul>
                                                                                                <li><code>prepare</code> :: Chờ xử lý</li>
                                                                                                <li><code>running</code> :: Đang đồng bộ</li>
                                                                                                <li><code>done</code> :: Hoàn thành đồng bộ</li>
                                                                                                <li><code>failed</code> :: Đồng bộ lỗi</li>
                                                                                                <li><code>stop</code> :: Dừng đồng bộ</li>
                                                                                            </ul>
                                                                                            <code>Nhiều thì sẽ ngăn cách nhau bởi dấu ,</code>
@apiSuccess     {Array}             data                                                     Danh sách nguồn kết nối dữ liệu
@apiSuccess     {any}               data.field                                              Các field sẽ được define dựa theo từng loại connector. Data In/Out
                                                                                           <ul>
                                                                                            <li>DataIn-Postgres: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-ResponseSuccessConnectDataInPostgres">Tại đây</a></li>
                                                                                            <li>DataIn-Api: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-ResponseSuccessConnectDataInApi">Tại đây</a></li>
                                                                                            <li>DataOut-Webhooks: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-ResponseSuccessConnectDataOutWebhooks">Tại đây</a></li>
                                                                                           </ul>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "_id": 6,
            "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "created_time": "2024-04-11T02:19Z",
            "description": "Test",
            "is_type_sync_manunal": True,
            "name": "Connector 1",
            "source_key": "postgres",
            "source_type": "databases",
            "is_trust_source": true,
            "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
            "object": "profiles",
            "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "updated_time": "2024-04-11T03:12Z"
        },
        {
            "_id": 8,
            "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "created_time": "2024-04-12T08:32Z",
            "name": "Connector 1",
            "is_type_sync_manunal": True,
            "source_key": "postgres",
            "source_type": "databases",
            "is_trust_source": true,
            "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
            "object": "profiles",
            "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "updated_time": "2024-04-12T08:32Z"
        },
        {
            "_id": 9,
            "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "created_time": "2024-04-12T09:27Z",
            "name": "Connector 1",
            "is_type_sync_manunal": True,
            "source_key": "postgres",
            "source_type": "databases",
            "is_trust_source": true,
            "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
            "object": "profiles",
            "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "updated_time": "2024-04-12T09:51Z"
        }
    ],
    "message": "request thành công."
}
"""
# ---------- Lấy thông tin của nhiều connectors theo list id -----------
"""
@api {POST} {domain}/market-place/api/v1.0/data-flow/actions/connector-detail-by-ids                 Lấy chi tiết connectors theo ids
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName ListDetailConnectorsByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam (BODY:)      {Array}       connector_ids                                          Danh sách id connector cần lấy dữ liệu


@apiSuccess     {Array}             data                                                     Danh sách nguồn kết nối dữ liệu
@apiSuccess     {any}               data.field                                              Các field sẽ được define dựa theo từng loại connector. Data In/Out
                                                                                           <ul>
                                                                                                <li>DataIn-Postgres: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-ResponseSuccessConnectDataInPostgres">Tại đây</a></li>
                                                                                                <li>DataIn-Api: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-ResponseSuccessConnectDataInApi">Tại đây</a></li>
                                                                                                <li>DataOut-Webhooks: <a href="https://dev.mobio.vn/docs/market_place/#api-Base-ResponseSuccessConnectDataOutWebhooks">Tại đây</a></li>
                                                                                           </ul>
@apiParamExample {json} Body
{
    "connector_ids": [1, 2, 3]
}


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "_id": 6,
            "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "created_time": "2024-04-11T02:19Z",
            "description": "Test",
            "is_type_sync_manunal": True,
            "name": "Connector 1",
            "source_key": "postgres",
            "source_type": "databases",
            "is_trust_source": true,
            "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
            "object": "profiles",
            "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "updated_time": "2024-04-11T03:12Z"
        },
        {
            "_id": 8,
            "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "created_time": "2024-04-12T08:32Z",
            "name": "Connector 1",
            "is_type_sync_manunal": True,
            "source_key": "postgres",
            "source_type": "databases",
            "is_trust_source": true,
            "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
            "object": "profiles",
            "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "updated_time": "2024-04-12T08:32Z"
        },
        {
            "_id": 9,
            "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "created_time": "2024-04-12T09:27Z",
            "name": "Connector 1",
            "is_type_sync_manunal": True,
            "source_key": "postgres",
            "source_type": "databases",
            "is_trust_source": true,
            "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
            "object": "profiles",
            "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "updated_time": "2024-04-12T09:51Z"
        }
    ],
    "message": "request thành công."
}
"""
# ---------- Chỉnh sửa thông tin cấu hình của connector sau khi đã hoàn thành -----------
"""
@api {PUT} {domain}/market-place/api/v1.0/data-flow/connectors/<connector_id>/modified/content                 Cập nhật thông tin của connector sau khi đã hoàn thiện cấu hình.
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName ModifiedContentConnector

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiUse BodyRequestUpdateConnector

@apiSuccess   {Object}   data                                                      Dữ liệu trả về
@apiUse DetailConnector
"""

# ---------- Gửi data test  -----------
"""
@api {POST} {domain}/market-place/api/v1.0/data-flow/connectors/<connector_id>/send-event/test                 Gửi thử nghiệm data out event
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName DataFlowSendEventTest

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiParam (BODY:)       {string}                        module                                                  Tên module cần gửi
@apiParam (BODY:)       {string}                        event_key                                               Event key cần gửi thử nghiệm

@apiSuccess   {Object}   data                                                      Dữ liệu trả về
@apiSuccess   {String}   data.status                                               Trạng thái
                                                                                   <ul>
                                                                                        <li><code>success</code>: Thành công</li>
                                                                                        <li><code>fail</code>: Thất bại</li>
                                                                                   </ul>
@apiSuccess   {Array}   data.information                                           Thông tin gửi thử nghiệm
@apiSuccess   {String}   data.information.name                                      Tên thông tin gửi thử nghiệm
@apiSuccess   {Array}   data.information.status                                    Trạng thái gửi thử nghiệm
                                                                                    <ul>
                                                                                        <li><code>success</code>: Thành công</li>
                                                                                        <li><code>fail</code>: Thất bại</li>
                                                                                   </ul>
@apiSuccess   {Array}   data.information.errors                                    Danh sách lỗi. Trong tường hợp gửi thử nghiệm thất bại                                                                                                                                   


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "information": [
            {
                "name": "Kết nối database",
                "status": "success"
            },
            {
                "name": "Authorization",
                "status": "success"
            },
            {
                "name": "Database",
                "status": "success"
            },
            {
                "name": "Permission",
                "status": "success"
            }
        ],
        "status": "success"
    }
}
"""

# ---------- Lấy url request của connector -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/url-request/connector                Lấy url request của connector
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName DataFlowGetUrlRequestConnector

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiParam (QUERY:)       {string}                        source_key                                            Key source cần lấy
@apiParam (QUERY:)       {string}                        source_type                                           Loại source cần lấy

@apiSuccess   {Object}   data                                                      Dữ liệu trả về                                                                                                                                   
@apiSuccess   {String}   data.url                                                  URL request                                                                                                                                  


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "url": "https://ingest-test.mobio.vn/market-place/external/api/v1.0/bluk-data"
    }
}
"""
# ---------- Lấy hướng dẫn tích hợp -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/<connector_id>/integration-guide         Hướng dẫn tích hợp data flow       
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName DataFlowIntegrationGuide

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiParam (QUERY:)       {string}                        source_key                                            Key source cần lấy
@apiParam (QUERY:)       {string}                        source_type                                           Loại source cần lấy
@apiParam (QUERY:)       {string}                        object                                                Đối tượng lấy hướng dẫn tích hợp

@apiSuccess   {Object}   data                                                      Dữ liệu trả về                                                                                                                                   
@apiSuccess   {String}   data.curl_example                                         Curl example                                                                                                                            
@apiSuccess   {Object}   data.body_example                                         Body example


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "curl_example": "curl -X POST "http://yourdomain.com/api/connectors" -H "accept: application/json" -H "Content-Type: application/json" -d '{}'",
        "body_example": {
            "data": {
                "name": "Profile 1"
            }
        }
    }
}
"""

# ---------- Lấy hướng dẫn tích hợp -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/<connector_id>/sample-response         Mẫu response
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName DataFlowSampleResponse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiParam (QUERY:)       {string}                        source_key                                            Key source cần lấy
@apiParam (QUERY:)       {string}                        source_type                                           Loại source cần lấy
@apiParam (QUERY:)       {string}                        object                                                Đối tượng lấy hướng dẫn tích hợp

@apiSuccess   {Object}   data                                                      Dữ liệu trả về  
@apiSuccess   {String}   data.tab                                                  Tên tab
@apiSuccess   {String}   data.description                                          Mô tả
@apiSuccess   {String}   data.code                                                 Mã response
                                                                                   <li>HTTP 200-OK</li>
                                                                                   <li>401-Unauthorized</li>
@apiSuccess   {Object}   data.response                                             Thông tin response
                                                                                   


                                                                                                                                  


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "success": [
            {
                "tab": "",
                "description": "Thông tin profile",
                "code": "HTTP 200-OK",
                "response": {
                    "data": {
                        "id": "123456",
                    }
                }
            }
        ],
        "error": [
            {
                "tab": "401-Unauthorized",
                "description": "token/api-key hết hạn hoặc sai định dạng. Yêu cầu login lại.",
                "code": "HTTP 1.1 401",
                "response": {
                    "code": 401,
                    "message": "Token is invalid or is expired. Please login again."
                }
            }
        ]
    }
}
"""

# ---------- Xử lý request data out -----------
"""
@api {KAFKA-TOPIC} topic-name:market-place-process-out-data     Xử lý request data out
@apiDescription     Xử lý request xuất dữ liệu ra ngoài hệ thống.<br/> Lưu ý: Các message được đẩy vào topic kafka theo key là request_id để đảm bảo thứ tự xử lý event cho từng yêu cầu xuất file
@apiGroup DataFlow 
@apiVersion 1.0.0
@apiName DataFlowProcessDataOut


@apiParam      (Message)        {String}              merchant_id                             ID merchant
@apiParam      (Message)        {String}              request_id                              ID request xuất file từ các module
@apiParam      (Message)        {String}              source_key                              Loại connector
                                                                                                <ul>
                                                                                                    <li><code>microsoft_excel</code> :: MS Excel</li>
                                                                                                </ul>
@apiParam      (Message)        {String}              module_name                             Tên module cần xuất file
@apiParam      (Message)        {Object}              extract_data_area                       Thông tin vùng dữ liệu cần trích xuất
@apiParam      (Message)        {String}              extract_data_area.object_id             ID của vùng dữ liệu
@apiParam      (Message)        {String}              extract_data_area.object_name           Tên của vùng dữ liệu
@apiParam      (Message)        {String}              area_code                               Khu vực xuất dữ liệu
@apiParam      (Message)        {String}              action_time                             Thời gian ghi nhận yêu cầu, định dạng: <code>"%Y-%m-%d %H:%M:%S.%f"</code>
@apiParam      (Message)        {Object}              condition_filters                       Điều kiện lọc dữ liệu, tùy thuộc vào từng module
@apiParam      (Message)        {Object}              search_filter                           Keyword search lọc dữ liệu
@apiParam      (Message)        {String}              status_process                          Trạng thái xử lý tương ứng
                                                                                                <ul>
                                                                                                    <li><code>wait_process</code> :: Chờ xử lý</li>
                                                                                                    <li><code>processing</code> :: Đang xử lý</li>
                                                                                                    <li><code>done</code> :: Hoàn thành</li>
                                                                                                    <li><code>stop_process</code> :: Dừng xử lý</li>
                                                                                                </ul>
@apiParam      (Message)        {Number}              estimate_number_row                     Số dòng ước tính trong file xuất
@apiParam      (Message)        {String}              estimate_start_time                     Thời gian ước tính bắt đầu xử lý, định dạng: <code>"%Y-%m-%d %H:%M:%S.%f"</code>
@apiParam      (Message)        {String}              estimate_completion_time                Thời gian ước tính hoàn thành xử lý, định dạng: <code>"%Y-%m-%d %H:%M:%S.%f"</code>
@apiParam      (Message)        {String}              actual_start_time                       Thời gian thực tế bắt đầu xử lý, định dạng: <code>"%Y-%m-%d %H:%M:%S.%f"</code>
@apiParam      (Message)        {String}              actual_completion_time                  Thời gian thực tế hoàn thành xử lý, định dạng: <code>"%Y-%m-%d %H:%M:%S.%f"</code>
@apiParam      (Message)        {Number}              actual_number_row                       Số dòng thực tế được xử lý trong file xuất
@apiParam      (Message)        {String}              account_id                              ID người dùng gửi yêu cầu
@apiParam      (Message)        {Number}              processing_speed                        Tốc độ xử lý (dòng/giây)
@apiParam      (Message)        {Object}              result_file                             Kết quả file xuất dữ liệu
@apiParam      (Message)        {String}              result_file.url                         URL file kết quả
@apiParam      (Message)        {String}              result_file.filename                    Tên file kết quả
@apiParam      (Message)        {String}              result_file.capacity                    Kích thước file kết quả
@apiParam      (Message)        {String}              result_file.format                      Kiểu file
@apiParam      (Message)        {String}              result_file.expire_time                 Thời gian hết hạn file kết quả (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiParam      (Message)        {Number}              current_process_row                     Số dòng hiện tại đang được xử lý



@apiParamExample Message example: Ghi nhận yêu cầu
{
    "merchant_id": "ID Merchant",
    "request_id": "ID xuất file từ các module",
    "source_key": "microsoft_excel",
    "module_name": "SALE",
    "extract_data_area": {
        "object_id": "obj001", 
        "object_name": "Customer Data"
    },
    "area_code": "DEAL_LIST",
    "action_time": "2025-01-20 14:32:32.105854",
    "condition_filters": {}, 
    "search_filter": "cv",
    "status_process": "wait_process",
    "estimate_number_row": 100,
    "estimate_start_time": "2025-01-20 14:32:32.105854",
    "estimate_completion_time": "2025-01-20 14:32:32.105854",
    "account_id": "ID User"
}

@apiParamExample Xử lý yêu cầu xuất file
{
    "merchant_id": "ID Merchant",
    "request_id": "ID xuất file từ các module",
    "source_key": "microsoft_excel",
    "module_name": "SALE",
    "area_code": "DEAL_LIST",
    "status_process": "processing",
    "actual_start_time": "2025-01-20 14:32:32.105854",
    "action_time": "2025-01-20 14:32:32.105854",
}

@apiParamExample Cập nhật số dòng trong quá trình xử lý
{
    "merchant_id": "ID Merchant",
    "request_id": "ID xuất file từ các module",
    "source_key": "microsoft_excel",
    "module_name": "SALE",
    "area_code": "DEAL_LIST",
    "status_process": "processing",
    "current_process_row": 2,
    "action_time": "2025-01-20 14:32:32.105854",
}

@apiParamExample Hoàn thành xử lý
{
    "merchant_id": "ID Merchant",
    "request_id": "ID xuất file từ các module",
    "source_key": "microsoft_excel",
    "module_name": "SALE",
    "area_code": "DEAL_LIST",
    "status_process": "done",
    "actual_number_row": 10000,
    "actual_completion_time": "2025-01-20 14:32:32.105854",
    "processing_speed": 100,
    "result_file": {
        "url": "https://example.com/files/result001.csv",
        "filename": "result001.csv",
        "capacity": "2MB",
        "format": "csv",
        "expire_time": "2023-10-08T08:00:00Z"
    },
    "action_time": "2025-01-20 14:32:32.105854"
}

@apiParamExample Xử lý thất bại
{
    "merchant_id": "ID Merchant",
    "request_id": "ID xuất file từ các module",
    "source_key": "microsoft_excel",
    "module_name": "SALE",
    "area_code": "DEAL_LIST",
    "status_process": "stop_process",
    "result_file": {
        "reason": "Lý do thất bại" 
    },
    "action_time": "2025-01-20 14:32:32.105854"
}


@apiParamExample Xử lý lại (retry)
{
    "merchant_id": "ID Merchant",
    "request_id": "ID xuất file từ các module",
    "source_key": "microsoft_excel",
    "module_name": "SALE",
    "area_code": "DEAL_LIST",
    "status_process": "processing",
    "retry_number": 0,
    "action_time": "2025-01-20 14:32:32.105854"
}
"""


#---------- Lấy danh sách bảng/ view của connectors với loại là database -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/connections/<connector-id>/databases/tables-views                 Danh sách bảng/view của connectors loại database
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName GetTablesViewsOfDatabases

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiParam   (Query:) {string}              [search]                Lọc theo ký tự

@apiSuccess   {Array[Object]}  data                                 Danh sách tên table/view
@apiSuccess   {string}  data.name                                   Tên bảng/view
@apiSuccess   {string}  data.type                                   Loại: giá trị là "table" hoặc "view"

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "name": "Table1",
            "type": "table"
        },
        {
            "name": "View1",
            "type": "view"
        }
    ]
}
"""


# ---------- Lấy tổng số lượng connector -----------
"""
@api {GET} {domain}/market-place/api/v1.0/data-flow/connectors/total-source                 Lấy tổng số lượng connector
@apiGroup DataFlow
@apiVersion 1.0.0
@apiName TotalConnectorsSource

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (QUERY:)      {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

@apiParam   (QUERY:) {string}           search                                              Từ khoá tìm kiềm 
@apiParam   (QUERY:) {bool=True,Fasle}           is_trust_source                                       Lọc nguồn đáng tin cậy hay không? 
@apiParam   (QUERY:) {string}           status_sync                                        Trạng thái kết nối
                                                                                            <ul>
                                                                                                <li><code>running</code> :: đang kết nối</li>
                                                                                                <li><code>stop</code> :: ngắt kết nối</li>
                                                                                                <li><code>error</code> :: lỗi kết nối</li>
                                                                                                <li><code>not_sync_in_range_time</code> :: không có dữ liệu trong 1 khoảng thời gian</li>
                                                                                            </ul>
                                                                                            <code>Nhiều thì sẽ ngăn cách nhau bởi dấu ,</code>
@apiParam   (QUERY:) {string}           created_by                                          Lọc theo người tạo
@apiParam   (QUERY:) {string}           [source_types]                                        Lọc theo loại nguồn. Nếu muốn lấy nhiều thì truyền dạng string ngăn cách nhau bởi dấu ,
@apiParam   (QUERY:) {string}           [source_keys]                                         Lọc theo source key. Nếu muốn lấy nhiều thì truyền dạng string ngăn cách nhau bởi dấu ,

@apiParam   (QUERY:) {string}           object_attribute                                        Đối tượng đồng bộ dữ liệu
                                                                                            <ul>
                                                                                                <li><code>profile</code> :: Profile</li>
                                                                                                <li><code>product_holding</code> :: Product Holding</li>
                                                                                                <li><code>dynamic_event</code> :: Dynamic Event</li>
                                                                                                <li><code>ticket</code> :: Ticket</li>
                                                                                                 <li><code>sale</code> :: Cơ hội bán</li>
                                                                                            </ul>
                                                                                            <code>Nhiều thì sẽ ngăn cách nhau bởi dấu ,</code>

@apiParam   (QUERY:) {string}           status_sync_conn                                        Trạng thái đồng bộ dữ liệu
                                                                                            <ul>
                                                                                                <li><code>prepare</code> :: Chờ xử lý</li>
                                                                                                <li><code>running</code> :: Đang đồng bộ</li>
                                                                                                <li><code>done</code> :: Hoàn thành đồng bộ</li>
                                                                                                <li><code>failed</code> :: Đồng bộ lỗi</li>
                                                                                                <li><code>stop</code> :: Dừng đồng bộ</li>
                                                                                            </ul>
                                                                                            <code>Nhiều thì sẽ ngăn cách nhau bởi dấu ,</code>
@apiSuccess     {Object}             data                                                     Danh sách nguồn kết nối dữ liệu
@apiParam   (QUERY:) {string}           [start_time]                                         Thời gian bắt đầu lọc thời gian đồng bộ dữ liệu gần nhất, truyền lên giờ utc
@apiParam   (QUERY:) {string}           [end_time]                                         Thời gian kết thúc lọc thời gian đồng bộ dữ liệu gần nhất, truyền lên giờ utc


@apiSuccess     {int}               data.total_connector                                       Tổng số lượng connector
                                                                                          

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "total_connector": 52
    },
    "lang": "vi",
    "message": "request thành công."
}

"""
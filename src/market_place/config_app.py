# L<PERSON>y danh sách app
"""
@api {GET} {domain}/market-place/api/v1.0/apps                 Lấy danh sách apps
@apiGroup ConfigApp
@apiVersion 1.0.0
@apiName ListApps

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging

@apiParam   (Query:) {string}           [name_search]                       Từ khoá tìm kiếm app theo tên
                                                                            <li>không phân biệt hoa thường, có dấu hoặc không dấu, khoảng trắng giữa các ký tự</li>
@apiParam   (Query:) {string}           [selected_field]                    Field lựa chọn để trả về, nhiều giá trị cách nhau dấu "," (VD: id,name,created_by)
@apiParam   (Query:) {string}           [order_by]                          Sắp xếp theo field nào (Chỉ hỗ trợ sort theo 1 filed). Nếu không truyền gì thì mặc định sắp xếp theo: <code>created_time</code>
@apiParam   (Query:) {string}           [order_type]                        Thứ tự cần sắp xếp
                                                                            <li>Nhận giá trị <code>desc</code>, <code>asc</code></li>
                                                                            <li><code>asc</code>: tăng dần</li>
                                                                            <li><code>desc</code>: giảm dần</li>
                                                                            <li>Mặc định <code>desc</code></li>

@apiSuccess     {Array}             data                                    Danh sách app được config
@apiSuccess     {String}            data.id                                 ID của app 
@apiSuccess     {String}            data.name                               Tên app
@apiSuccess     {String}            data.name_search                        Tên app dùng để tìm kiếm
@apiSuccess     {String}            data.secret_key                         Token app
@apiSuccess     {string}            data.created_by                         Thông tin nhân viên tạo
@apiSuccess     {string}            data.updated_by                         Thông tin nhân viên cập nhật
@apiSuccess     {bool}              data.is_new_app                         Là app mới hay không?
                                                                            <li>Giá trị <code>true</code>: Mới, <code>false</code>: Không</li>
                                                                            <li>Mặc định sau khi tạo app là <code>true</code>, sau khi có bất kì user nào click để xem chi tiết sẽ chuyển sang <code>false</code>, sử dụng <a href="https://dev.mobio.vn/docs/market_place/#api-ConfigApp-RemoveNewStatus">API</a> để cập nhật</li>
@apiSuccess     {Object}            data.total_used                         Tổng số connector đã khai báo app
@apiSuccess     {Int}               data.total_used.data_in                 Tổng số nguồn dữ liệu
@apiSuccess     {Int}               data.total_used.data_out                Tổng số đích dữ liệu
@apiSuccess     {string}            data.created_time                       Thời gian tạo
@apiSuccess     {string}            data.updated_time                       Thời gian cập nhật

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "id": "ae5699f0-634a-11ee-a689-5acfff56cad1",
            "name": "app 1",
            "name_search": "app1",
            "secret_key": "f6Igw7VfTXFNEx9aOeGP",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "created_time": "2024-04-11T02:19Z",
            "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
            "is_new_app": true,
            "total_used": {
                "data_in": 0,
                "data_out": 5
            }
        },
        ...
    ],
    "paging": {
        "page": 1,
        "per_page": 5,
        "total_count": 220,
        "total_page": 44
    },
    "message": "request thành công."
}
"""

# Tạo app
"""
@api {POST} {domain}/market-place/api/v1.0/apps                 Khởi tạo app
@apiGroup ConfigApp
@apiVersion 1.0.0
@apiName CreateApps

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:) {string}            name                               Tên app cần tạo


@apiParamExample {json} Body
{   
    "name": "app 1",
}
@apiSuccess     {Object}         data                                      Chi tiết app được tạo
@apiSuccess     {String}         data.id                                   ID của app 
@apiSuccess     {String}         data.name                                 Tên app được tạo
@apiSuccess     {String}         data.name_search                          Tên app dùng để tìm kiếm
@apiSuccess     {String}         data.secret_key                           Token app
@apiSuccess     {string}         data.created_by                           Thông tin nhân viên tạo
@apiSuccess     {string}         data.updated_by                           Thông tin nhân viên cập nhật
@apiSuccess     {bool}           data.is_new_app                           Là app mới hay không?
                                                                           <li>Giá trị <code>true</code>: Mới, <code>false</code>: Không</li>
                                                                           <li>Mặc định sau khi tạo app là <code>true</code>, sau khi có bất kì user nào click để xem chi tiết sẽ chuyển sang <code>false</code>, sử dụng <a href="https://dev.mobio.vn/docs/market_place/#api-ConfigApp-RemoveNewStatus">API</a> để cập nhật</li>
@apiSuccess     {Object}        data.total_used                            Tổng số connector đã khai báo app
@apiSuccess     {Int}           data.total_used.data_in                    Tổng số nguồn dữ liệu
@apiSuccess     {Int}           data.total_used.data_out                   Tổng số đích dữ liệu
@apiSuccess     {string}        data.created_time                          Thời gian tạo
@apiSuccess     {string}        data.updated_time                          Thời gian cập nhật


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "ae5699f0-634a-11ee-a689-5acfff56cad1",
        "name": "app 1",
        "name_search": "app1",
        "secret_key": "f6Igw7VfTXFNEx9aOeGP",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "created_time": "2024-04-11T02:19Z",
        "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "is_new_app": true,
        "total_used": {
            "data_in": 0,
            "data_out": 5
        }
    }
}
"""

# Lấy chi tiết app
"""
@api {GET} {domain}/market-place/api/v1.0/apps/<app_id>               Lấy thông tin chi tiết app
@apiGroup ConfigApp
@apiVersion 1.0.0
@apiName DetailApps

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "ae5699f0-634a-11ee-a689-5acfff56cad1",
        "name": "app 1",
        "name_search": "app1",
        "secret_key": "f6Igw7VfTXFNEx9aOeGP",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "created_time": "2024-04-11T02:19Z",
        "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "is_new_app": false,
        "updated_time": "2024-04-11T03:12Z"
    }
}
"""


# Sửa app
"""
@api {PATCH} {domain}/market-place/api/v1.0/apps/<app_id>                 Sửa thông tin app
@apiGroup ConfigApp
@apiVersion 1.0.0
@apiName UpdateApps

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:) {string}            [name]                               Tên app cần tạo


@apiParamExample {json} Body
{   
    "name": "app 1 new",
}

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "name": "app 1 new",
        "name_search": "app1new",
        "id": "ae5699f0-634a-11ee-a689-5acfff56cad1",
        "secret_key": "f6Igw7VfTXFNEx9aOeGP",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "created_time": "2024-04-11T02:19Z",
        "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "updated_time": "2024-04-11T03:12Z",
        "is_new_app": false,
    }
}
"""

# Xoá app
"""
@api {DELETE} {domain}/market-place/api/v1.0/apps/<app_id>                 Xoá app
@apiGroup ConfigApp
@apiVersion 1.0.0
@apiName DeleteApps

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
}
"""


# renew secret key
"""
@api {POST} {domain}/market-place/api/v1.0/apps/<app_id>/actions/renew-secret                 Tạo secret key mới
@apiGroup ConfigApp
@apiVersion 1.0.0
@apiName RenewSecretApp

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "ae5699f0-634a-11ee-a689-5acfff56cad1",
        "name": "app 1",
        "name_search": "app1",
        "secret_key": "f6Igw7VfTXFNEx9aOeGP",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "created_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "created_time": "2024-04-11T02:19Z",
        "updated_by": "704eac91-7416-497f-a17d-d81cfa2d3211",
        "updated_time": "2024-04-11T03:12Z",
        "is_new_app": false,
    }
}
"""


# Danh sách event
"""
@api {GET} {domain}/market-place/api/v1.0/apps/destination/events                 Danh sách event
@apiGroup ConfigApp
@apiVersion 1.0.0
@apiName ListEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:) {string}           group_code                        Đối tượng cần lấy danh sách event, cách nhau bởi dấu ,
                                                                          <li>Nhận các giá trị: <code>profile</code>, <code>journey builder</code>, <code>sale</code>, <code>task</code>, <code>company</code>, <code>ticket</code>, <code>note</code></li>
                                                                          <li>Truyền lên nhiều đối tượng thì cách nhau bởi dấu , ex: <code>profile,journey builder</code></li>

@apiParam   (Query:) {string}           [status]                          Trạng thái event
                                                                          <li>Nhận các giá trị: <code>active</code>, <code>inactive</code></li>
                                                                          <li><code>active</code>: đang hoạt động</li>
                                                                          <li><code>inactive</code>: ngừng hoạt động</li>
                                                                          <li>mặc định <code>active</code></li>
                                                                          <li>Truyền lên nhiều status thì cách nhau bởi dấu , ex: <code>active,deactive</code></li>

@apiSuccess     {Array}          data                                      Danh sách event
@apiSuccess     {String}         data.id                                   ID của event 
@apiSuccess     {String}         data.name                                 Tên event
@apiSuccess     {String}         data.status                               Trạng thái event (active/inactive)
@apiSuccess     {String}         data.data_test                            json mẫu dữ liệu test
@apiSuccess     {String}         data.event_key                            Key định danh cho event
@apiSuccess     {string}         data.group_code                           Đối tượng sở hữu event
@apiSuccess     {string}         data.link_doc                             Link tài liệu
@apiSuccess     {string}         data.created_time                         Thời gian tạo

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "created_time": "2022-05-06T10:57Z",
            "data_test": "{ \"action_time\": 1658919472, \"merchant_id\": \"1b99bdcf-d582-4f49-9715-1b61dfff3924\", \"event_type\": \"profile_open_email\", \"profile_id\": \"271b8657-cbf5-4d4b-84b9-1305671fe867\", \"body\": { \"from_email\": \"<EMAIL>\", \"profile_data\": { \"name\": \"Dương Ngân\", \"id\": \"271b8657-cbf5-4d4b-84b9-1305671fe867\" }, \"email_id\": \"e9a9fe18-0d9a-11ed-b9c8-3e012749f6e2\", \"subject\": \"Test.\" }, \"_id\": \"fc631e3488f6e5fd3b35d375fef41658\" }",
            "event_key": "profile_open_email",
            "group_code": "profile",
            "id": "41c57027-cd2b-11ec-b269-b7495dfa4a40",
            "link_doc": "https://help.mobio.io/articles/huong-dan-webhook-data-out/#profile-mo-email",
            "name": "Profile mở email",
            "status": "active"
        },
        {
            "created_time": "2022-05-06T10:57Z",
            "data_test" : "{ \"action_time\": 1658876078.505126, \"merchant_id\": \"1b99bdcf-d582-4f49-9715-1b61dfff3924\", \"created_time\": 1658876080.141136, \"fields\": [ { \"remove\": [], \"add\": [ { \"sale_process_id\": \"62cc023369c057ddec9e94fb\", \"sale_process_name\": \"Data test\", \"state_name\": \"Khởi tạo\", \"state_ratio\": 5, \"state_code\": \"ZPSYK3Y8\" } ], \"change\": [], \"field_key\": \"state_code\", \"field_name\": \"state_code\" } ], \"id\": \"62e070b08fa66451707b844b\", \"deal_id\": \"62e070aefc503549d550c99e\" }",
            "event_key" : "sale_update_deal",
            "group_code" : "sale",
            "id" : "41c57033-cd2b-11ec-b269-b7495dfa4a40",
            "link_doc" : "https://help.mobio.io/articles/huong-dan-webhook-data-out/#cap-nhat-thong-tin-don-hang",
            "name" : "Cập nhật thông tin đơn hàng",
            "status" : "active",
        }
    ]
}
"""

# Cập nhật trạng thái event
"""
@api {PUT} {domain}/market-place/api/v1.0/apps/destination/events/<event_id>/update/event-status               Thay đổi trạng thái của event
@apiGroup ConfigApp
@apiVersion 1.0.0
@apiName UpdateStatusEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {string}           status                           Trạng thái event
                                                                        <li>Nhận giá trị <code>active</code>, <code>inactive</code></li>
                                                                        <li><code>active</code>: đang hoạt động</li>
                                                                        <li><code>inactive</code>: ngừng hoạt động</li>

@apiParamExample {json} Body
{
    "status": "inactive"
}

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

# Lấy danh sách nguồn và đích dữ liệu đang sử dụng ứng dụng
"""
@api {GET} {domain}/market-place/api/v1.0/apps/<app_id>/actions/list-connector-in-used                 Danh sách nguồn và đích dữ liệu đang sử dụng app
@apiGroup ConfigApp
@apiVersion 1.0.0
@apiName ListConnectorUseApp

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging

@apiParam   (Query:) {string}           data_type                       Loại đang sử dụng ứng dụng
                                                                        <li>Nhận giá trị <code>data_in</code>, <code>data_out</code></li>
                                                                        <li><code>data_in</code>: Nguồn dữ liệu</li>
                                                                        <li><code>data_out</code>: đích dữ liệu</li>
                                                                        <li>Nếu muốn tìm toàn bộ connector đang sử dụng ứng dụng thì truyền cả 2 data_type cách nhau bởi dấu , <code>data_in,data_out</code></li>

@apiSuccess     {Array}          data                                      Danh sách connector sử dụng app
@apiSuccess     {Int}            data.id                                   Mã định danh connector
@apiSuccess     {String}         data.app_id                               Mã định danh app
@apiSuccess     {String}         data.name                                 Tên app
@apiSuccess     {bool}           data.is_trust_source                      Nguồn đáng tin cậy hay không <li>Giá trị <code>true</code>: tin cậy, <code>false</code>: Không tin cậy</li>
@apiSuccess     {Array}          data.module_used                          Danh sách module đang sử dụng
@apiSuccess     {String}         data.data_type                            Loại đang sử dụng ứng dụng
@apiSuccess     {String}         data.status_connect                       Trạng thái connector
@apiSuccess     {String}         data.status_sync                          Trạng thái đồng bộ
                                                                            <ul>
                                                                                <li><code>running</code> đang đồng bộ</code></li>
                                                                                <li><code>done</code> hoàn thành</code></li>
                                                                                <li><code>stopped</code> bị dừng đồng bộ dữ liệu</code></li>
                                                                                <li><code>not_data_sync</code> không có dữ liệu đồng bộ</code></li>
                                                                                <li><code>sync_error</code> lỗi đồng bộ dữ liệu</code></li>
                                                                            </ul>
@apiSuccess     {String}         data.source_type                          Loại nguồn dữ liệu
@apiSuccess     {String}         data.source_key                           Source key đã kết nối

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "id": 5,
            "app_id": "ae5699f0-634a-11ee-a689-5acfff56cad1",
            "name": "Connector 1",
            "is_trust_source": true,
            "module_used": ["profile"],
            "created_time": "2024-04-10T04:15Z",
            "data_type": "data_out",
            "status_connect": "on",
            "status_sync": "on"
        },
        ...
    ],
    "paging": {
        "page": 1,
        "per_page": 5,
        "total_count": 11,
        "total_page": 6
    },
}
"""

# Seen config app
"""
@api {POST} {domain}/market-place/api/v1.0/apps/<app_id>/actions/seen               Gỡ bỏ trạng thái New của app
@apiDescription   App hiển thị trạng thái New sau khi được tạo, khi có bất kỳ user nào click để xem chi tiết app thì sử dụng api này để bỏ trạng thái New
@apiGroup ConfigApp
@apiVersion 1.0.0
@apiName RemoveNewStatus

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

# ---------- Lấy danh sách header mặc định của phần kết nối server -----------
"""
@api {GET} {domain}/market-place/api/v1.0/apps/<app_id>/header-default              Lấy danh sách tham số mặc định của header
@apiGroup ConfigApp
@apiVersion 1.0.0
@apiName GetHeaderDefault

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy

                               
@apiParam   (QUERY:) {string}       source_key                    Source key cần lấy cấu hình.

@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Array}             data                          Thông tin tham số mặc định
@apiSuccess     {string}            data.key                      Field key
@apiSuccess     {string}            data.value                    Giá trị
@apiSuccess     {string=formula,string}            data.type=string                    Kiểu
@apiSuccess     {string=system,user}            data.created_by=system                 Tạo bởi ai

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "key": "X-Merchant-ID",
            "value": "12329322929",
            "type": "string",
            "created_by": "system"
        }
    ]
}
"""

# ---------- Lấy thông tin tham số cấu hình mặc định của app -----------
"""
@api {GET} {domain}/market-place/api/v1.0/apps/<app_id>/param-default/config-connect              Lấy thông tin tham số cấu hình mặc định của app
@apiGroup ConfigApp
@apiVersion 1.0.0
@apiName GetParamDefaultConfigConnect

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=data_in,data_out}       data_type=data_in                                     Xác định loại cần lấy
@apiParam   (QUERY:) {string}       source_key                    Source key cần lấy cấu hình.
@apiParam   (QUERY:) {string}       source_type                   Loại source

@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Object}            data                          Thông tin tham số mặc định
@apiSuccess     {String}            data.url                      Thông tin URL
@apiSuccess     {Array}             data.methods                  Danh sách method tương ứng với URL
@apiSuccess     {Array}             data.param_headers                  Danh sách header tương ứng với URL
@apiSuccess     {string}            data.param_headers.key                      Field key
@apiSuccess     {string}            data.param_headers.value                    Giá trị
@apiSuccess     {string=formula,string}            data.param_headers.type=string                    Kiểu
@apiSuccess     {string=system,user}            data.param_headers.created_by=system                 Tạo bởi ai
@apiSuccess     {String}            data.param_query              Các tham số trên query
@apiSuccess     {String}            data.content_type             Content type


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "url": "https://ingest-test.mobio.vn/market-place/external/api/v1.0/bulk-data",
        "methods": [
            "GET",
            "POST"
        ]
        "headers": [
            {
                "key": "X-Merchant-ID",
                "value": "12329322929",
                "type": "string",
                "created_by": "system"
            }
        ]
        "param_query": "param_query_example",
        "content_type: ""
    }
}
"""

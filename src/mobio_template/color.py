#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 22/11/2024t
"""
"""
@api {POST} {domain}/template/api/v1.0/colors-custom/actions/filter-by-resource                 L<PERSON><PERSON> danh sách màu tuỳ chỉnh của resource
@apiGroup Colors
@apiDescription Lấy danh sách màu tuỳ chỉnh của resource
@apiVersion 1.0.0
@apiName GetListColorCustomByResource
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Body:)     {Object}       resource_information     Thông tin nguồn cần lấy dữ liệu
@apiParam   (Body:)     {String}       resource_information.type     Loại nguồn cần lấy dữ liệu
                                                                    <ul>
                                                                        <li><code>POPUP</code>: popup</li>
                                                                        <li><code>LANDINGPAGE</code>: landingpage</li>
                                                                        <li><code>FORM_BUILDER</code>: form_builder</li>
                                                                    </ul>
@apiParam   (Body:)     {String}       resource_information.object_id     Định danh đối tượng cần ấy dữ liệu.
                                                                     
@apiParamExample    {json}      BODY :
{
    "resource_information": {
        "type": "FORM_BUILDER",
        "object_id": ""
    }
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Dữ liệu trả về
@apiSuccess {Array}             data.config_colors                      Cấu hình màu.
@apiSuccess {String}            data.config_colors.hex_color            Mã màu tuỳ chỉnh dạng hex
@apiSuccess {int}               data.config_colors.order                Vị trí
@apiSuccess {Object}            data.resource_information               Thông tin nguồn phát sinh.
@apiSuccess {string}            data.resource_information.type          Loại nguồn
                                                                        <ul>
                                                                            <li><code>POPUP</code>: popup</li>
                                                                            <li><code>LANDINGPAGE</code>: landingpage</li>
                                                                            <li><code>FORM_BUILDER</code>: form_builder</li>
                                                                        </ul>
@apiSuccess {string}            data.resource_information.object_id     Định danh đối tượng
@apiSuccess {Object}            data.attributes                         Thông tin thuộc tính của mã màu. Phần này do FE tự customize cần truyền thông gì lên cũng được.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "attributes": {},
        "resource_information": {
            "type": "FORM_BUILDER",
            "object_id": "1"
        },
        "config_colors": [
            {
                "hex_color": "#071631",
                "order": 1
            },
            {
                "hex_color": "#6A7383",
                "order": 2
            },
            {
                "hex_color": "#757380",
                "order": 3
            },
            {
                "hex_color": "#9CA2AD",
                "order": 4
            }
        ]
    }
}
"""
"""
@api {GET} {domain}/template/api/v1.0/colors-default                 Lấy danh sách màu mặc định
@apiGroup Colors
@apiDescription Lấy danh sách màu mặc định
@apiVersion 1.0.0
@apiName GetListColorDefault
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)     {Object}       [resource_information_type]     Thông tin nguồn cần lấy dữ liệu. Trong trường hợp không truyền lên thì sẽ lấy tất cả màu mặc định của hệ thống.
                                                                        <ul>
                                                                            <li><code>POPUP</code>: popup</li>
                                                                            <li><code>LANDINGPAGE</code>: landingpage</li>
                                                                            <li><code>FORM_BUILDER</code>: form_builder</li>
                                                                        </ul>

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Dữ liệu trả về
@apiSuccess {string}            data.hex_color                Mã màu tuỳ chỉnh dạng hex


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "hex_color": "#071631",
        },
        {
            "hex_color": "#6A7383",
        },
        {
            "hex_color": "#757380",
        },
        {
            "hex_color": "#9CA2AD",
        },
        {
            "hex_color": "#CDD0D6",
        },
        {
            "hex_color": "#E6E7EA",
        },
        {
            "hex_color": "#F8F9FA",
        },
        {
            "hex_color": "#FFFFFF",
        },
        {
            "hex_color": "#B81B1B",
        },
        {
            "hex_color": "#C75C12",
        },
        {
            "hex_color": "#CC9200",
        },
        {
            "hex_color": "#6AA312",
        },
        {
            "hex_color": "#0592AA",
        },
        {
            "hex_color": "#1B59C4",
        },
        {
            "hex_color": "#49038F",
        },
        {
            "hex_color": "#AE38BF",
        },
        {
            "hex_color": "#E62222",
        },
        {
            "hex_color": "#F97316",
        },
        {
            "hex_color": "#FFB700",
        },
        {
            "hex_color": "#84CC16",
        },
        {
            "hex_color": "#06B6D4",
        },
        {
            "hex_color": "#226FF5",
        },
        {
            "hex_color": "#5B04B3",
        },
        {
            "hex_color": "#D946EF",
        },
        {
            "hex_color": "#EB4E4E",
        },
        {
            "hex_color": "#FA8F45",
        },
        {
            "hex_color": "#FFC533",
        },
        {
            "hex_color": "#9DD645",
        },
        {
            "hex_color": "#38C5DD",
        },
        {
            "hex_color": "#4E8CF7",
        },
        {
            "hex_color": "#7C36C2",
        },
        {
            "hex_color": "#E16BF2",
        },
    ]
}
"""

"""
@api {POST} {domain}/template/api/v1.0/colors-custom                 Thêm màu custom
@apiGroup Colors
@apiDescription Thêm màu tuỳ chỉnh
@apiVersion 1.0.0
@apiName AddColorCustom
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Body:)     {Object}       resource_information                 Thông tin nguồn cần lấy dữ liệu
@apiParam   (Body:)     {String}       resource_information.type            Loại nguồn cần lấy dữ liệu
                                                                            <ul>
                                                                                <li><code>POPUP</code>: popup</li>
                                                                                <li><code>LANDINGPAGE</code>: landingpage</li>
                                                                                <li><code>FORM_BUILDER</code>: form_builder</li>
                                                                            </ul>
@apiParam   (Body:)     {String}       resource_information.object_id       Định danh đối tượng cần ấy dữ liệu.
@apiParam   (Body:)     {Object}       attributes                           Thuộc tính của màu. Tuỳ biến theo dữ liệu của FE.
@apiParam   (Body:)     {Array}        config_colors                        Danh sách màu cấu hình. Tối đa là 8.
@apiParam   (Body:)     {string}       config_colors.hex_color              Mã màu tuỳ chỉnh dạng hex
@apiParam   (Body:)     {int}          config_colors.order                  Vị trí

@apiParamExample    {json}      BODY :
{
    "resource_information": {
        "type": "FORM_BUILDER",
        "object_id": "123"
    },
    "attributes": {},
    "config_colors": [
        {
            "hex_color": "#071631",
            "order": 1
        },
        {
            "hex_color": "#6A7383",
            "order": 2
        },
        {
            "hex_color": "#757380",
            "order": 3
        },
        {
            "hex_color": "#9CA2AD",
            "order": 4
        }
    ]    
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Dữ liệu trả về
@apiSuccess {Object}            data.resource_information     Thông tin nguồn phát sinh.
@apiSuccess {string}            data.resource_information.type       Loại nguồn
                                                                    <ul>
                                                                        <li><code>POPUP</code>: popup</li>
                                                                        <li><code>LANDINGPAGE</code>: landingpage</li>
                                                                        <li><code>FORM_BUILDER</code>: form_builder</li>
                                                                    </ul>
@apiSuccess {string}            data.resource_information.object_id     Định danh đối tượng
@apiSuccess {Object}            data.attributes                         Thông tin thuộc tính của mã màu. Phần này do FE tự customize cần truyền thông gì lên cũng được.
@apiSuccess {Array}             data.config_colors                      Cấu hình màu.
@apiSuccess {String}            data.config_colors.hex_color            Mã màu tuỳ chỉnh dạng hex
@apiSuccess {int}               data.config_colors.order                Vị trí

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "attributes": {},
        "resource_information": {
            "type": "FORM_BUILDER",
            "object_id": "1"
        },
        "config_colors": [
            {
                "hex_color": "#071631",
                "order": 1
            },
            {
                "hex_color": "#6A7383",
                "order": 2
            },
            {
                "hex_color": "#757380",
                "order": 3
            },
            {
                "hex_color": "#9CA2AD",
                "order": 4
            }
        ]
    }
}
"""
#!/usr/bin/python
# -*- coding: utf8 -*-

********************************** Delete Assets *********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {post} {HOST}/template/api/v1.0/assets/actions/delete  Xóa nhiều asset.
@apiDescription API xóa các asset theo id
@apiGroup Assets
@apiVersion 1.0.0
@apiName DeleteAssets
@apiIgnore Not used

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {ArrayString}  assets   Danh sách định danh asset cần xóa.
@apiParamExample  {json}  Body example
{
  "assets": [
    "6406c41ab81ae2cdc5323fb2",
    "6406c41ab81ae2cdc5323fb3",
  ]
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Update Assets *********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {patch} {HOST}/template/api/v1.0/assets/<asset_id> Cập nhật asset.
@apiDescription API cập nhật thông tin của 1 asset. Hiện tại hệ thống chỉ hỗ trợ đổi tên của asset.
@apiGroup Assets
@apiVersion 1.0.0
@apiName UpdateAsset

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của asset. Các asset có thể trùng tên.
@apiParamExample {json} Body example
{
  "name": "Tên của asset"
}


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Delete Assets *********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {delete} {HOST}/template/api/v1.0/assets/<asset_id> Xóa 1 asset.
@apiDescription API xóa 1 asset cụ thể.
@apiGroup Assets
@apiVersion 1.0.0
@apiName DeleteOneAsset

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Create Assets *********************************
* version: 1.0.1                                                                 *
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {post} {HOST}/template/api/v1.0/assets  Tạo assets
@apiDescription   API tạo asset và lưu trữ vào <code>SavedAssets</code> hoặc thư viện mong muốn.
@apiGroup Assets
@apiVersion 1.0.1
@apiName CreateAssets

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của asset. Các asset có thể trùng tên.
@apiParam   (Body:)   {Object}  content  Dữ liệu thiết kế của asset.
@apiParam   (Body:)   {String}  [thumbnail]  Link ảnh thumbnail của asset.
@apiParam   (Body:)   {String}  [library_id]  Định danh của thư viện lưu asset.

@apiParamExample {json} Body example
{
  "name": "Tên của asset",
  "content": {},
  "library_id": "6406c41ab81ae2cdc5323fb2",
  "thumbnail": "https://..."
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "id": "6406c41ab81ae2cdc5323fb1",
    "name": "assets 1",
    "content":{},
    "library_id": "6406c41ab81ae2cdc5323fb2",
    "thumbnail": "https://...",
    "merchant_id": "f91737b2-e1ab-4447-a5c1-a3047bfc0f71",
    "created_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "created_time": 1324567892,
    "updated_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "updated_time": 5467891223,
  }
}
"""
******************
"""
@api {post} {HOST}/template/api/v1.0/assets Tạo assets
@apiDescription   API tạo asset và lưu trữ vào <code>SavedAssets</code> hoặc thư viện mong muốn.
@apiGroup Assets
@apiVersion 1.0.0
@apiName CreateAssets

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của asset. Các asset có thể trùng tên.
@apiParam   (Body:)   {String}  [library_id]  Định danh của thư viện lưu asset.

@apiParamExample {json} Body example
{
  "name": "Tên của asset",
  "content": {},
  "library_id": "6406c41ab81ae2cdc5323fb2"
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "6406c41ab81ae2cdc5323fb1",
      "name": "assets 1",
      "content":{}
    }
  ]
}
"""

********************************** List Assets *********************************
* version: 1.0.2                                                               *
* version: 1.0.1                                                               *
* version: 1.0.0                                                               *
********************************************************************************
"""
@api {get} {HOST}/template/api/v1.0/assets Lấy danh sách assets
@apiDescription API lấy danh sách assets theo nơi lưu trữ của Merchant.<br/>
Nếu không truyền <code>&library_id</code> sẽ trả về danh sách assets được lưu trữ trong <code>SavedAssets</code>.<br/>
Danh sách trả về sẽ được sắp xếp theo thời điểm tạo từ <code>mới nhất</code> đến <code>cũ nhất</code>.
@apiGroup Assets
@apiVersion 1.0.2
@apiName ListAssets

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse paging_tokens

@apiParam   (Query:)  {String}  [library_id]  Định danh của thư viện cần lấy assets.
@apiParam   (Query:)  {String}  [search]  Tìm kiếm theo tên assets. Trả về danh sách assets có tên chứa từ cần tìm.

@apiSuccess   {ArrayObject}   data  Danh sách các assets.
@apiSuccess   {String}    data..id  Định danh của assets.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "6406c41ab81ae2cdc5323fb1",
      "name": "assets 1",
      "content":{},
      "thumbnail": "https://...",
      "merchant_id": "f91737b2-e1ab-4447-a5c1-a3047bfc0f71",
      "created_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
      "created_time": 1324567892,
      "updated_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
      "updated_time": 5467891223,
    }
  ]
}
"""
******************
"""
@api {get} {HOST}/template/api/v1.0/assets Lấy danh sách assets
@apiDescription API lấy danh sách assets theo nơi lưu trữ của Merchant.<br/>
Nếu không truyền <code>&library_id</code> sẽ trả về danh sách assets được lưu trữ trong <code>SavedAssets</code>.<br/>
Danh sách trả về sẽ được sắp xếp theo thời điểm tạo từ <code>mới nhất</code> đến <code>cũ nhất</code>.
@apiGroup Assets
@apiVersion 1.0.1
@apiName ListAssets

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse paging_tokens

@apiParam   (Query:)  {String}  [library_id]  Định danh của thư viện cần lấy assets.

@apiSuccess   {ArrayObject}   data  Danh sách các assets.
@apiSuccess   {String}    data..id  Định danh của assets.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "6406c41ab81ae2cdc5323fb1",
      "name": "assets 1",
      "content":{},
      "thumbnail": "https://...",
      "merchant_id": "f91737b2-e1ab-4447-a5c1-a3047bfc0f71",
      "created_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
      "created_time": 1324567892,
      "updated_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
      "updated_time": 5467891223,
    }
  ]
}
"""
*******************
"""
@api {get} {HOST}/template/api/v1.0/assets Lấy danh sách assets
@apiDescription API lấy danh sách assets theo nơi lưu trữ của Merchant.<br/>
Nếu không truyền <code>&library_id</code> sẽ trả về danh sách assets được lưu trữ trong <code>SavedAssets</code>.<br/>
Danh sách trả về sẽ được sắp xếp theo thời điểm tạo từ <code>mới nhất</code> đến <code>cũ nhất</code>.
@apiGroup Assets
@apiVersion 1.0.0
@apiName ListAssets

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse paging_tokens

@apiParam   (Query:)  {String}  [library_id]  Định danh của thư viện cần lấy assets.

@apiSuccess   {ArrayObject}   data  Danh sách các assets.
@apiSuccess   {String}    data..id  Định danh của assets.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "6406c41ab81ae2cdc5323fb1",
      "name": "assets 1",
      "content":{},
      "created_time": 
    }
  ]
}
"""
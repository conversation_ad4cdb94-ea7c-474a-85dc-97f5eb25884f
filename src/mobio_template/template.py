#!/usr/bin/python
# -*- coding: utf8 -*-

********************************** List template *********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {get} {HOST}/template/api/v1.0/templates  Lấy danh sách template
@apiDescription  API lấy danh sách các template.<br/>
Hỗ trợ tìm kiếm theo tên template, lọc theo loại template.<br/>
Hỗ trợ sắp xếp danh sách theo thời điểm tạo, tên. Mặc định sắp xếp theo thời điểm tạo.
@apiGroup  Template
@apiVersion 1.0.0
@apiName ListTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse order_sort
@apiUse paging_tokens

@apiParam   (Query:)  {String}  [search]  Tìm kiếm theo tên template. Trả về danh sách template có tên chứa từ cần tìm.
@apiParam   (Query:)  {String}  [type]  Lọc theo loại template. Ex: <code>&type=site</code>.

@apiSuccess   {String}  info.name  Tên của template.
@apiSuccess   {String}  info.type  Loại template.
@apiSuccess   {String}  [info.thumbnail]  Link ảnh thumb.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "info": {
        "name": "Template 1",
        "type": "site",
        "thumbnail": "https://..."
      }
    },
    {...}
  ]
}
"""


********************************** Delete template *********************************
* version: 1.0.0                                                                   *
************************************************************************************
"""
@api {delete} {HOST}/template/api/v1.0/templates/<template_id>  Xóa 1 template
@apiDescription  API xóa template.
@apiGroup  Template
@apiVersion 1.0.0
@apiName Delete1Template

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Make template from item *********************************
* version: 1.0.0                                                                           *
********************************************************************************************
"""
@api {post} {HOST}/template/api/v1.0/templates/action/import  Tạo template từ 1 đối tượng đã có.
@apiDescription  API để tạo template từ 1 đối tượng đã có.
@apiGroup  Template
@apiVersion 1.0.0
@apiName MakeTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParamExample  {json}   Body example
{
  "info": {
    "name": "Template 1",
    "type": "site",
    "thumbnail": "https://..."
  },
  "import": {
    "object_id": "78asd6a8dsa87sdf",
    "object_type": "site"
  }
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Create template *********************************
* version: 1.0.0                                                                   *
************************************************************************************
"""
@api {post} {HOST}/template/api/v1.0/templates  Tạo template
@apiDescription  API tạo 1 template
@apiGroup Template
@apiVersion 1.0.0
@apiName CreateTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  info.name  Tên của template.
@apiParam   (Body:)   {String}  info.type  Loại template.
@apiParam   (Body:)   {String}  [info.thumbnail]  Link ảnh thumb.
@apiParam   (Body:)   {Object}  content  Dữ liệu thiết kế của template.
@apiParamExample  {json}   Body example
{
  "info": {
    "name": "Template 1",
    "type": "site",
    "thumbnail": "https://..."
  },
  "content": {...}
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""
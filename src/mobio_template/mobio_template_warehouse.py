# ----------------------- <PERSON><PERSON><PERSON> danh sách ngành của email template -----------------------
"""
@api {GET} {domain}/template/api/v1.1/emails-builder-warehouse/branch            L<PERSON><PERSON> danh sách ngành của email template
@apiGroup Email builder warehouse
@apiDescription L<PERSON>y danh sách ngành của email template
@apiVersion 1.1.0
@apiName GetListBranchEmailBuilderWarehouse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header



@apiSuccess {ArrayObject}            data                               Dữ liệu danh sách ngành
@apiSuccess {String}            data.branch_code                               Mã ngành
@apiSuccess {String}            data.branch_name                         Tên ngành

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "branch_code": "business_and_finance",
            "branch_name": "<PERSON>nh <PERSON> & <PERSON><PERSON>"
        },
        {
            "branch_code": "insurance",
            "branch_name": "Bảo hiểm"
        },
        {
            "branch_code": "real_estate",
            "branch_name": "Bất động sản"
        },
        {
            "branch_code": "e_commerce_and_retail",
            "branch_name": "E-commerce & Bán lẻ"
        },
        {
            "branch_code": "education",
            "branch_name": "Giáo dục"
        },
        {
            "branch_code": "healthcare_and_beauty",
            "branch_name": "Chăm sóc sức khoẻ & Làm đẹp"
        },
        {
            "branch_code": "fashion",
            "branch_name": "Thời trang"
        },
        {
            "branch_code": "entertainment_and_arts",
            "branch_name": "Giải trí & Nghệ thuật"
        },
        {
            "branch_code": "hotels_and_tourism",
            "branch_name": "Khách sạn & Du lịch"
        },
        {
            "branch_code": "restaurants",
            "branch_name": "Nhà hàng"
        },
        {
            "branch_code": "food",
            "branch_name": "Thực phẩm"
        },
        {
            "branch_code": "agriculture",
            "branch_name": "Nông nghiệp"
        },
        {
            "branch_code": "information_technology",
            "branch_name": "Công nghệ thông tin"
        },
        {
            "branch_code": "architecture_and_interior_design",
            "branch_name": "Kiến trúc, nội thất"
        }
    ],
    "lang": "vi",
    "message": "Request thành công."
}
"""


# ----------------------- Lấy danh sách mục đích của email template -----------------------
"""
@api {GET} {domain}/template/api/v1.1/emails-builder-warehouse/purpose           Lấy danh sách mục đích của email template
@apiGroup Email builder warehouse
@apiDescription Lấy danh sách mục đích của email template
@apiVersion 1.1.0
@apiName GetListPurposeEmailBuilderWarehouse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header



@apiSuccess {ArrayObject}            data                               Dữ liệu danh sách mục đích
@apiSuccess {String}            data.purpose_code                               Mã mục đích
@apiSuccess {String}            data.purpose_name                         Tên mục đích

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "purpose_code": "announcement",
            "purpose_name": "Thông báo"
        },
        {
            "purpose_code": "news",
            "purpose_name": "Tin tức"
        },
        {
            "purpose_code": "event_invitation",
            "purpose_name": "Thư mời tới sự kiện"
        },
        {
            "purpose_code": "advertisement",
            "purpose_name": "Quảng cáo"
        },
        {
            "purpose_code": "welcome",
            "purpose_name": "Chào mừng"
        },
        {
            "purpose_code": "thank_you",
            "purpose_name": "Cảm ơn"
        },
        {
            "purpose_code": "congratulation",
            "purpose_name": "Chúc mừng"
        }
    ],
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Lấy danh sách dịp lễ của email template -----------------------
"""
@api {GET} {domain}/template/api/v1.1/emails-builder-warehouse/holiday           Lấy danh sách dịp lễ của email template
@apiGroup Email builder warehouse
@apiDescription Lấy danh sách dịp lễ của email template
@apiVersion 1.1.0
@apiName GetListHolidayEmailBuilderWarehouse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header



@apiSuccess {ArrayObject}            data                               Dữ liệu danh sách dịp lễ
@apiSuccess {String}            data.holiday_code                               Mã dịp lễ
@apiSuccess {String}            data.holiday_name                         Tên dịp lễ

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "holiday_code": "birthday",
            "holiday_name": "Sinh nhật"
        },
        {
            "holiday_code": "black_friday",
            "holiday_name": "Black Friday"
        },
        {
            "holiday_code": "new_year",
            "holiday_name": "Tết dương lịch"
        },
        {
            "holiday_code": "lunar_new_year",
            "holiday_name": "Tết âm lịch"
        },
        {
            "holiday_code": "christmas",
            "holiday_name": "Giáng sinh"
        },
        {
            "holiday_code": "valentines_day",
            "holiday_name": "Lễ tình nhân 14/2"
        },
        {
            "holiday_code": "international_womens_day",
            "holiday_name": "Quốc tế phụ nữ 8/3"
        },
        {
            "holiday_code": "labour_day",
            "holiday_name": "Quốc tế lao động 1/5"
        },
        {
            "holiday_code": "national_day",
            "holiday_name": "Quốc khánh"
        },
        {
            "holiday_code": "back_to_school",
            "holiday_name": "Khai giảng"
        },
        {
            "holiday_code": "vietnamese_womens_day",
            "holiday_name": "Phụ nữ Việt Nam 20/10"
        },
        {
            "holiday_code": "halloween",
            "holiday_name": "Halloween"
        }
    ],
    "lang": "vi",
    "message": "Request thành công."
}
"""


# ----------------------- Lấy danh sách mẫu email template -----------------------
"""
@api {GET} {domain}/template/api/v1.1/emails-builder-warehouse/sample-library           Lấy danh sách mẫu email template
@apiGroup Email builder warehouse
@apiDescription Lấy danh sách mẫu email template
@apiVersion 1.1.0
@apiName GetListSampleLibraryEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	   (Query:)			{string}	[search]    Tìm kiếm theo tên email.     
@apiParam	   (Query:)			{string}	[is_favorite]    Lọc theo email yêu thích.   
@apiParam	   (Query:)			{string}	[branch_code]    Danh sách mã ngành nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam	   (Query:)			{string}	[purpose_code]   Danh sách mã mục đích nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam	   (Query:)			{string}	[holiday_code]    Danh sách mã dịp lễ nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam	   (Query:)			{string}	[sort_by]       Sắp xếp theo field tuỳ chỉnh.(Default: updated_time)
@apiParam	   (Query:)			{integer}	[order_by]      Sắp xếp theo thứ tự <code>(1:asc, -1:desc)</code>.
@apiParam	   (Query:)			{string}	[per_page]      Số lượng trên một trang.   
@apiParam	   (Query:)			{string}	[after_token]   Token để request lấy dữ liệu trang tiếp theo.

@apiSuccess {ArrayObject}            data                               Dữ liệu danh sách mẫu email template

@apiSuccess {String}            data.id                                Mã email template
@apiSuccess {String}            data.name                              Tên email template
@apiSuccess {String}            data.small_thumbnail                   Link ảnh thumbnail kích thước nhỏ
@apiSuccess {String}            data.description                        Mô tả
@apiSuccess {String}            data.thumbnail                         Link ảnh thumbnail
@apiSuccess {Boolean}            data.is_favorite                       Email yêu thích
@apiSuccess {Array}            data.branch_codes                       Danh sách mã ngành
@apiSuccess {Array}            data.holiday_codes                      Danh sách mã dịp lễ
@apiSuccess {Array}            data.purpose_codes                     Danh sách mã mục đích
@apiSuccess {String}            data.created_by                         Người tạo
@apiSuccess {String}            data.created_time                       Thời gian tạo
@apiSuccess {String}            data.updated_by                         Người cập nhật
@apiSuccess {String}            data.updated_time                       Thời gian cập nhật

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "id": "68245cafc70c09dac5368bd6",
            "name": "test2 ok",
            "small_thumbnail": "https://...",
            "description": "test 1",
            "thumbnail": "https://...",
            "is_favorite": false,
            "branch_codes": [
                "agriculture"
            ],
            "holiday_codes": [
                "new_year"
            ],
            "purpose_codes": [
                "thank_you"
            ],
            "created_by": "admin",
            "created_time": "2025-05-13T10:00Z",
            "updated_by": "admin",
            "updated_time": "2025-05-14T11:00Z"
        },
    ],
    "lang": "vi",
    "message": "Request thành công.",
    "paging": {
        "cursors": {
            "after_token": null,
            "before_token": null
        }
    }
}
"""


# ----------------------- Lấy chi tiết mẫu email template -----------------------
"""
@api {GET} {domain}/template/api/v1.1/emails-builder-warehouse/sample-library/<email_id>         Lấy chi tiết mẫu email template
@apiGroup Email builder warehouse
@apiDescription Lấy chi tiết mẫu email template
@apiVersion 1.1.0
@apiName GetDetailSampleLibraryEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apiUse merchant_id_header


@apiSuccess {Object}            data                               Dữ liệu danh sách mẫu email template

@apiSuccess {String}            data.id                                Mã email template
@apiSuccess {String}            data.name                              Tên email template
@apiSuccess {Object}            data.body                               Nội dung email
@apiSuccess {String}            data.description                        Mô tả
@apiSuccess {String}            data.thumbnail                         Link ảnh thumbnail
@apiSuccess {String}            data.small_thumbnail                   Link ảnh thumbnail kích thước nhỏ
@apiSuccess {Boolean}            data.is_favorite                       Email yêu thích
@apiSuccess {Array}            data.branch_codes                       Danh sách mã ngành
@apiSuccess {Array}            data.holiday_codes                      Danh sách mã dịp lễ
@apiSuccess {Array}            data.purpose_codes                     Danh sách mã mục đích
@apiSuccess {String}            data.created_by                         Người tạo
@apiSuccess {String}            data.created_time                       Thời gian tạo
@apiSuccess {String}            data.updated_by                         Người cập nhật
@apiSuccess {String}            data.updated_time                       Thời gian cập nhật

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "id": "68245cafc70c09dac5368bd6",
        "name": "test2 ok",
        "body": {
            "html": "<div>This is body email-builder</div>",
            "json": {
                "content": "This is body email-builder"
            }
        },
        "description": "test 1",
        "thumbnail": "https://...",
        "small_thumbnail": "https://...",
        "is_favorite": false,
        "branch_codes": [
            "agriculture"
        ],
        "holiday_codes": [
            "new_year"
        ],
        "purpose_codes": [
            "thank_you"
        ],
        "created_by": "admin",
        "created_time": "2025-05-13T10:00Z",
        "updated_by": "admin",
        "updated_time": "2025-05-14T11:00Z"   
    },
    "lang": "vi",
    "message": "Request thành công."
}
"""


# ----------------------- Đếm số lượng mẫu email template -----------------------
"""
@api {GET} {domain}/template/api/v1.1/emails-builder-warehouse/actions/count          Đếm số lượng mẫu email template
@apiGroup Email builder warehouse
@apiDescription Đếm số lượng mẫu email template
@apiVersion 1.1.0
@apiName CountSampleLibraryEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	   (Query:)			{string}	[search]    Tìm kiếm theo tên email.     
@apiParam	   (Query:)			{string}	[is_favorite]    Lọc theo email yêu thích.   
@apiParam	   (Query:)			{string}	[branch_code]    Danh sách mã ngành nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam	   (Query:)			{string}	[purpose_code]   Danh sách mã mục đích nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam	   (Query:)			{string}	[holiday_code]    Danh sách mã dịp lễ nếu có nhiều thì ngăn cách bởi dấu ' , ' .

@apiSuccess {Object}            data                               Dữ liệu số lượng mẫu email template

@apiSuccess {String}            data.total_sample_email_template      Số lượng mẫu email template
@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "total_sample_email_template": 1
    },
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Chuyển thành email mẫu ---------------------------

"""
@api {PUT} {domain}/template/api/v1.1/emails-builder-warehouse/actions/copy-to-sample-library         Chuyển thành email mẫu
@apiGroup Email builder warehouse
@apiDescription Copy to sample email 
@apiVersion 1.1.0
@apiName CopyToSampleLibraryEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}      email_id                         Id email builder   
@apiParam   (Body:)   {integer}      version_web                       Version web email 
@apiParam   (Body:)   {String}     [name]                           Tên email template
@apiParam   (Body:)   {String}     [branch_code]                    Danh sách mã ngành nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam   (Body:)   {String}     [holiday_code]                    Danh sách mã dịp lễ nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam   (Body:)   {String}     [purpose_code]                   Danh sách mã mục đích nếu có nhiều thì ngăn cách bởi dấu ' , ' .


@apiParamExample {json} Body example
{
    "email_id": "682af558eab84ec8bb5f66ab",
    "name": "test2 ok",
    "version_web": 1,
    "branch_code": "sports",
    "holiday_code": "new_year,birthday"
}

@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Cập nhật email mẫu ---------------------------

"""
@api {PUT} {domain}/template/api/v1.1/emails-builder-warehouse/sample-library      Cập nhật email mẫu
@apiGroup Email builder warehouse
@apiDescription Update email builder
@apiVersion 1.1.0
@apiName UpdateSampleLibraryEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}      email_id                         Id email builder   
@apiParam   (Body:)   {integer}      version_id                       Version email builder
@apiParam   (Body:)   {String}     [name]                           Tên email template
@apiParam   (Body:)   {String}     [description]                    Mô tả email template
@apiParam   (Body:)   {String}     [thumbnail]                      Link ảnh thumbnail
@apiParam   (Body:)   {String}     [small_thumbnail]                Link ảnh thumbnail kích thước nhỏ
@apiParam   (Body:)   {Boolean}     [body]                              Nội dung email 
@apiParam   (Body:)   {String}     [branch_code]                    Danh sách mã ngành nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam   (Body:)   {String}     [holiday_code]                    Danh sách mã dịp lễ nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam   (Body:)   {String}     [purpose_code]                   Danh sách mã mục đích nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam   (Body:)   {integer}      [version_web]                       Version web email

@apiParamExample {json} Body example
{
    "email_id": "682af558eab84ec8bb5f66ab",
    "name": "test2 ok",
    "description": "test 1",
    "thumbnail": "https://...",
    "small_thumbnail": "https://...",
    "body": {
        "html": "<div>This is body email-builder</div>",
        "json": {
            "content": "This is body email-builder"
        }
    },
    "version_web": 1,
    "branch_code": "sports",
    "holiday_code": "new_year,birthday"
}

@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "lang": "vi",
    "message": "Request thành công."
}
"""



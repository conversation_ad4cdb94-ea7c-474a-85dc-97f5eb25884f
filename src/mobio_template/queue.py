
"""
@api {QUEUE} Topic:mobio-template-support-workflow-get-info    Support WorkFlow get info popup
@apiDescription Support workflow lấy thông tin popup
@apiGroup Queue
@apiVersion 1.0.0
@apiName QueueSupportWorkflowGetInfo

@apiParam      (Input:)       {Dict}      data_filter                     Sử dụng cho tìm kiếm đối tượng popup
@apiParam      (Input:)       {String}    data_filter.merchant_id         Đị<PERSON> danh tenant
@apiParam      (Input:)       {String}    data_filter.popup_builder_id    Định danh popup
@apiParam      (Input:)       {String}    data_filter.profile_id          Đ<PERSON>nh danh profile
@apiParam      (Input:)       {List}      select_field                    Danh sách field cần lấy, mặc định lấy title

@apiParam      (Input:)       {Dict}      callback                        Thông tin callback
@apiParam      (Input:)       {String}    callback.queue_name             Tên Top<PERSON> gửi thông tin trả lại
@apiParam      (Input:)       {Dict}      callback.data                   Thông tin gửi trả lại (data_callback)

@apiParamExample [json] Input example:
{
  "data_filter": {
        'merchant_id': '1b99bdcf-d582-4f49-9715-1b61dfff3924',
        'popup_builder_id': '62301d8dbfce426fcf3ff4df',
        'profile_id': '2e7fb1dc-0fa8-40ff-8564-bdee13c53e38',
    }
  "select_field": []
  "callback": {}
}

@apiParam      (Response:)       {Dict}      data                     Thông tin ưu cầu tìm kiếm
@apiParam      (Response:)       {Dict}      data_callback            Thông tin callback lấy từ message

@apiSuccessExample  {json} Callback Queue:
{
  "data": {
    "id": "62301d8dbfce426fcf3ff4df",
    "title": "Popup mới 15/03",
  },
  "data_callback": {}
}
"""

#!/usr/bin/python
# -*- coding: utf8 -*-
********************************** Import site **********************************
* version: 1.0.1                                                                *
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} {HOST}/template/api/v1.0/sites/actions/import  Import site
@apiDescription  API tạo site từ file editor (.mopage).
@apiGroup Site
@apiVersion 1.0.1
@apiName ImportSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse form_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của site mới. Các site không được phép trùng tên.
@apiParam   (Body:)   {String}  category  Định danh category để lưu site. Nếu template thuộc nhiều category, các category cách nhau bởi dấu <code>,</code>.
@apiParam   (Body:)   {File}  file  File data.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Update site **********************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {patch} {HOST}/template/api/v1.0/sites/<site_id>   Cập nhật site
@apiDescription API dùng để cập nhật thông tin site
@apiGroup Site
@apiVersion 1.0.0
@apiName UpdateSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  [name]  Tên của site. Các site không được phép trùng tên.
@apiParam   (Body:)   {String}  [thumbnail]   Link ảnh thumbnail của site.
@apiParam   (Body:)   {ArrayString}  [category]  Danh sách định danh category để lưu site.
@apiParam   (Body:)   {Object}  [content]   Dữ liệu thiết kế của site.
@apiParamExample {json} Body example
{
  "name": "Site 1",
  "thumbnail": "https://...",
  "category": ["6406c41ab81ae2cdc53avxfb1"],
  "content": {...}
}

@apiSuccess   {String}  id  Định danh của site.
@apiSuccess   {String}  name  Tên của site.
@apiSuccess   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   {String}  view_url  Link preview site.
@apiSuccess   {ArrayString}  [category]  Danh sách định danh của category.
@apiSuccess   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""

********************************** Detail site **********************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {get} {HOST}/template/api/v1.0/sites/<site_id>  Lấy chi tiết site
@apiDescription  API dùng để lấy dữ liệu chi tiết của 1 site.
@apiGroup Site
@apiVersion 1.0.0
@apiName Read1Site

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccess   {String}  id  Định danh của site.
@apiSuccess   {String}  name  Tên của site.
@apiSuccess   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   {String}  view_url  Link preview site.
@apiSuccess   {ArrayString}  [category]  Danh sách định danh của category.
@apiSuccess   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).
@apiSuccess   {Object}  [content]  Dữ liệu thiết kế của site.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "site": {
    "id": "6406b81ae2cdc5378xfb1",
    "name": "Site 1",
    "thumbnail": "https://...",
    "view_url": "",
    "category": ["6406c41ab81e2cdc53avxfb1"],
    "created_time": 123456789678,
    "updated_time": 123456789678,
    "content": {...}
  }
}
"""
********************************** List sites *********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {get} {HOST}/template/api/v1.0/sites  Lấy danh sách site
@apiDescription  API dùng để lấy danh sách site.<br/>
Hỗ trợ tìm kiếm theo tên site.
@apiGroup Site
@apiVersion 1.0.0
@apiName ListSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse order_sort
@apiUse paging_tokens

@apiParam   (Query:)  {String}  [search]  Tìm kiếm theo tên site. Trả về danh sách site có tên chứa từ cần tìm.
@apiParam   (Query:)  {String}  [category]  Lọc danh sách site theo thư mục. Ex: <code>&category=6406c41ab81a23456ad</code>

@apiSuccess   {String}  id  Định danh của site.
@apiSuccess   {String}  name  Tên của site.
@apiSuccess   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   {ArrayString}  [category]  Danh sách định danh của category.
@apiSuccess   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":[
    {
      "id": "6406b81ae2cdc5378xfb1",
      "name": "Site 1",
      "thumbnail": "https://...",
      "category": ["6406c41ab81e2cdc53avxfb1"],
      "created_time": 123456789678,
      "updated_time": 123456789678
    }
  ]
}
"""
********************************** Delete 1 site *********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {delete} {HOST}/template/api/v1.0/sites/<site_id>  Xóa 1 site
@apiDescription  API dùng để xóa 1 site cụ thể.
@apiGroup Site
@apiVersion 1.0.0
@apiName Delete1Site

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Create site *********************************
* version: 1.0.0                                                               *
********************************************************************************
"""
@api {post} {HOST}/template/api/v1.0/sites   Tạo site mới
@apiDescription API dùng để tạo site mới
@apiGroup Site
@apiVersion 1.0.0
@apiName CreateSite

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của site. Các site không được phép trùng tên.
@apiParam   (Body:)   {String}  [thumbnail]  Link ảnh thumbnail của site.
@apiParam   (Body:)   {ArrayString}  [category]  Danh sách định danh category để lưu site.
@apiParam   (Body:)   {Object}  [content]  Dữ liệu thiết kế của site.
@apiParamExample {json} Body example
{
  "name": "Site 1",
  "thumbnail": "https://...",
  "category": ["6406c41ab81ae2cdc53avxfb1"],
  "content":{...}
}

@apiSuccess   (Site info)   {String}  id  Định danh của site.
@apiSuccess   (Site info)   {String}  name  Tên của site.
@apiSuccess   (Site info)   {String}  thumbnail   Link ảnh thumbnail của site.
@apiSuccess   (Site info)   {ArrayString}  [category]  Danh sách định danh của category.
@apiSuccess   (Site info)   {Timestamp}   created_time  Thời điểm tạo site (theo giờ UTC).
@apiSuccess   (Site info)   {Timestamp}   updated_time  Thời điểm cập nhật cuối cùng của site (theo giờ UTC).

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "site": {
    "id": "6406b81ae2cdc5378xfb1",
    "name": "Site 1",
    "thumbnail": "https://...",
    "category": ["6406c41ab81e2cdc53avxfb1"],
    "created_time": 123456789678,
    "updated_time": 123456789678,
  }
}
"""
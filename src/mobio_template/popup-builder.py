"""
    Author: LocDX
    Company: Mobio
    Date Created: 28/05/2025
"""

"""
@apiDefine paging_tokens
@apiParam   (Query:)    {Number}    [per_page] Số phần tử trên một page.<br/> Example: <code>&per_page=5</code>
@apiParam   (Query:)    {String}    [after_token] Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)    {String}    [before_token] Token để request lấy dữ liệu trang trước đó. Nếu trong danh sách tham số gửi lên có <code>after_token</code> thì ưu tiên xử lý <code>after_token</code>

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after_token]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before_token]    Token để lấy dữ liệu trang trước đó.
@apiSuccess     {Object}    [paging..per_page]          Số lượng phần tử trên 1 page
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after_token": "YXNkaGZha2RoZmFrZGZh",
            "before_token": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 15
    }
}
"""

#================================================================================


"""
@apiDefine ResponseDetailPopup

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của popup builder
@apiSuccess {String}            data.popup_template_id                   Định danh của mẫu popup sử dụng để tạo ra popup builder
@apiSuccess {String}            data.title                               Tên mẫu popup builder
@apiSuccess {Object}            data.content                             Nội dung của popup builder
@apiSuccess {String}            data.session                                Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không
@apiSuccess {String}            data.merchant_id                            Định danh tennant
@apiSuccess {String}            data.thumbnail                            Link ảnh thumbnail của popup builder
@apiSuccess {String}            data.small_thumbnail                       Link ảnh thumbnail kích thước nhỏ của popup builder
@apiSuccess {Integer}            data.second_page                            Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiSuccess {Integer}            data.active                                 Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Ngừng xuất bản, 1:Xuất bản, 2: Trạng thái nháp</code>
@apiSuccess {Array}             data.categories                             Danh sách <code>id</code> của danh mục popup builder
@apiSuccess {Array}             data.type_codes                             Danh sách mã loại popup

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật

"""


#----------------------- Create popup builder -----------------------


"""
@api {POST} {domain}/template/api/v1.0/popups-builder                  Tạo mới popup builder
@apiGroup PopupBuilderV4x
@apiDescription Tạo mới popup builder
@apiVersion 1.0.0
@apiName AddPopup
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}                            title                               Tên mẫu popup builder
@apiParam   (BODY:)     {String}                            [popup_template_id]                 Định danh của mẫu popup sử dụng để tạo ra popup builder
@apiParam   (BODY:)     {Array}                            [categories]                          Danh sách <code>id</code> của danh mục popup builder


@apiParamExample    {json}      BODY:
{
    "title": "popup builder create",
    "popup_template_id": "ID của mẫu popup",
    "categories": ["id_category"]
}  
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Tạo mới popup builder 

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của popup builder
@apiSuccess {String}            data.popup_template_id                   Định danh của mẫu popup sử dụng để tạo ra popup builder
@apiSuccess {String}            data.title                               Tên mẫu popup builder
@apiSuccess {Object}            data.content                             Nội dung của popup builder
@apiSuccess {String}            data.session                                Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không
@apiSuccess {String}            data.merchant_id                            Định danh tennant
@apiSuccess {String}            data.thumbnail                            Link ảnh thumbnail
@apiSuccess {String}            data.small_thumbnail                       Link ảnh thumbnail kích thước nhỏ
@apiSuccess {Integer}            data.second_page                            Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiSuccess {Integer}            data.active                                 Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Ngừng xuất bản, 1:Xuất bản, 2: Trạng thái nháp</code>
@apiSuccess {Array}             data.categories                             Danh sách <code>id</code> của danh mục popup builder

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "lang": "vi",
    "data": {
        "id": 'popup_builder_id',
        "popup_template_id": "62b2e82ae634635c05d02134",
        "title": "Tên popup",
        "content": {
            "html": "<div>This is body popup-builder</div>",
            "json": {
              "content": "This is body popup-builder"
            }
        },
        "categories": ["id_category"],
        "thumbnail": "https://...",
        "small_thumbnail": "https://...",
        "session": "123123",
        "merchant_id": "merchant_id",
        "second_page": 0,
        "active": 0,
        "type_codes": ["lightbox"],
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10:10Z"
        "updated_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "updated_time": "2021-10-03T08:10:10Z"
    }
}

"""

# ----------------------- Update popup builder -----------------------


"""
@api {PATCH} {domain}/template/api/v1.0/popups-builder/<popup_builder_id>                    Cập nhật popup builder
@apiGroup PopupBuilderV4x
@apiDescription Sửa popup theo popup_builder_id
@apiVersion 1.0.0
@apiName UpdatePopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {String}      popup_builder_id                Định danh của popup builder cần sửa


@apiParam   (Body:)     {String}                            [title]                             Tên mẫu popup builder
@apiParam   (Body:)     {String}                            [content]                             Nội dung của popup builder
@apiParam   (Body:)     {String}                            session                             Session được trả về từ backend để kiểm tra phiên bản có phải mới nhất không 
@apiParam   (Body:)     {String}                            [thumbnail]                           Link ảnh thumbnail của popup builder
@apiParam   (Body:)     {String}                            [small_thumbnail]                     Link ảnh thumbnail kích thước nhỏ của popup builder
@apiParam   (Body:)     {Integer}                           [second_page]                         Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiParam   (Body:)     {Integer}                           [active]                            Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Ngừng xuất bản, 1:Xuất bản, 2: Trạng thái nháp</code>
@apiParam   (Body:)     {Array}                             [categories]                        Danh sách <code>id</code> của danh mục popup builder
@apiParam   (Body:)     {Array}                            [type_codes]                          Danh sách mã loại popup


@apiParamExample    {json}      BODY:
{
    "title": "popup builder update",
    "content": {
            "html": "<div>This is body popup-builder</div>",
            "json": {
              "content": "This is body popup-builder"
        }
    },
    "categories": ["id_category"],
    "thumbnail": "https://...",
    "small_thumbnail": "https://...",
    "session": "123123",
    "second_page": 0,
    "type_codes": ["lightbox"],
    "active": 1,
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa popup builder 

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của popup builder
@apiSuccess {String}            data.popup_template_id                   Định danh của mẫu popup sử dụng để tạo ra popup builder
@apiSuccess {String}            data.title                               Tên mẫu popup builder
@apiSuccess {Object}            data.content                             Nội dung của popup builder
@apiSuccess {String}            data.session                                Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không
@apiSuccess {String}            data.merchant_id                            Định danh tennant
@apiSuccess {String}            data.thumbnail                            Link ảnh thumbnail của popup builder
@apiSuccess {String}            data.small_thumbnail                       Link ảnh thumbnail kích thước nhỏ của popup builder
@apiSuccess {Integer}            data.second_page                            Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiSuccess {Integer}            data.active                               Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Ngừng xuất bản, 1:Xuất bản, 2: Trạng thái nháp</code>
@apiSuccess {Array}             data.categories                             Danh sách <code>id<code> của danh mục popup builder
@apiSuccess {Array}             data.type_codes                             Danh sách mã loại popup

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'popup_builder_id',
        "title": "popup builder update",
        "content": {
            "html": "<div>This is body popup-builder</div>",
            "json": {
              "content": "This is body popup-builder"
            }
        },
        "categories": ["id_category"],
        "session": "123123",
        "merchant_id": "merchant_id",
        "thumbnail": "https://...",
        "small_thumbnail": "https://...",
        "second_page": 0,
        "active": 0,
        "type_codes": ["lightbox"],
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10:10Z",
        "updated_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "updated_time": "2021-10-03T08:10:10Z"
    }
}

"""


# ----------------------- Delete popup builder -----------------------


"""
@api {DELETE} {domain}/template/api/v1.0/popups-builder/<popup_builder_id>                    Xóa popup builder
@apiGroup PopupBuilderV4x
@apiDescription Xóa popup builder theo popup_builder_id
@apiVersion 1.0.0
@apiName DeletePopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id                Định danh của popup cần xóa

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa popup builder 


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""

# ----------------------- Get list popup builder-----------------------


"""
@api {GET} {domain}/template/api/v1.0/popups-builder              Lấy danh sách popup builder
@apiGroup PopupBuilderV4x
@apiDescription Lấy danh sách popup builder
@apiVersion 1.0.0
@apiName ListPopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging_tokens

@apiParam	   (Query:)			{String}	[title]         Tìm kiếm theo title popup builder.
@apiParam	   (Query:)			{String}	[active]        Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Ngừng xuất bản, 1:Xuất bản, 2: Trạng thái nháp</code>
@apiParam	   (Query:)			{String}	[type_codes]    Danh sách mã loại popup(cách nhau bởi dấu ,).
@apiParam	   (Query:)			{String}	[account_id]    <code>id</code> của người tạo popup builder(cách nhau bởi dấu ,).
@apiParam	   (Query:)			{Date}	    [start_date]    Filter theo thời gian bắt đầu tạo popup builder.
@apiParam	   (Query:)			{Date}	    [end_date]      Filter theo thời gian kết thúc tạo popup builder.
@apiParam	   (Query:)			{String}	[sort_by]       Sắp xếp theo field tuỳ chỉnh.(Default: updated_time)
@apiParam	   (Query:)			{Integer}	[order_by]      Sắp xếp theo thứ tự <code>(1:asc, -1:desc)</code>.
@apiParam	   (Query:)			{String}	[per_page]      Số lượng trên một trang.   
@apiParam	   (Query:)			{String}	[after_token]   Token để request lấy dữ liệu trang tiếp theo.


@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {ArrayObject}       data                                    Dữ liệu danh sách email builder.

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của popup builder
@apiSuccess {String}            data.popup_template_id                   Định danh của mẫu popup sử dụng để tạo ra popup builder
@apiSuccess {String}            data.title                               Tên mẫu popup builder
@apiSuccess {String}            data.session                                Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không
@apiSuccess {String}            data.merchant_id                            Định danh tennant
@apiSuccess {String}            data.thumbnail                            Link ảnh thumbnail của popup builder
@apiSuccess {String}            data.small_thumbnail                       Link ảnh thumbnail kích thước nhỏ của popup builder
@apiSuccess {Integer}            data.second_page                            Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiSuccess {Integer}            data.active                               Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Ngừng xuất bản, 1:Xuất bản, 2: Trạng thái nháp</code>
@apiSuccess {Array}             data.categories                             Danh sách <code>id</code> của danh mục popup builder

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "lang": "vi",
    "data": [{
        "id": 'popup_builder_id',
        "title": "Tên popup",
        "categories": ["id_category"],
        "session": "123123",
        "merchant_id": "merchant_id",
        "thumbnail": "https://...",
        "small_thumbnail": "https://...",
        "second_page": 0,
        "active": 0,
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10:10Z",
        "updated_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "updated_time": "2021-10-03T08:10:10Z"
    }],
    "paging": {
        "cursors": {
            "after_token": "YXNkaGZha2RoZmFrZGZh",
            "before_token": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 15
    }
}
"""


# ----------------------- Get detail popup builder-----------------------


"""
@api {GET} {domain}/template/api/v1.0/popups-builder/<popup_builder_id>                   Lấy chi tiết popup builder
@apiGroup PopupBuilderV4x
@apiDescription Lấy chi tiết popup theo popup_builder_id
@apiVersion 1.0.0
@apiName GetDetailPopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id                Định danh của popup builder cần xem chi tiết

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết popup builder  

@apiUse ResponseDetailPopup

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'popup_builder_id',
        "title": "Tên popup",
        "categories": ["id_category"],
        "content": {
            "html": "<div>This is body popup-builder</div>",
            "json": {
              "content": "This is body popup-builder"
            }
        },
        "session": "123123",
        "merchant_id": "merchant_id",
        "thumbnail": "https://...",
        "small_thumbnail": "https://...",
        "second_page": 0,
        "active": 0,
        "type_codes": ["lightbox"],
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10:10Z",
        "updated_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "updated_time": "2021-10-03T08:10:10Z"
    }
}

"""


#----------------------- Sao chép popup builder -----------------------


"""
@api {POST} {domain}/template/api/v1.0/popups-builder/actions/copy                  Sao chép popup builder
@apiGroup PopupBuilderV4x
@apiDescription Sao chép popup builder
@apiVersion 1.0.0
@apiName CopyPopup
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}                            title                               Tên mẫu popup builder
apiParam   (BODY:)     {String}                            popup_builder_id                    Định danh của popup builder muốn sao chép
@apiParam   (BODY:)     {Array}                            [categories]                          Danh sách <code>id</code> của danh mục popup builder


@apiParamExample    {json}      BODY:
{
    "title": "popup builder copy",
    "popup_builder_id": "id123456"
    "categories": []
}  
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          copy popup builder 

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của popup builder
@apiSuccess {String}            data.popup_template_id                   Định danh của mẫu popup sử dụng để tạo ra popup builder
@apiSuccess {String}            data.title                               Tên mẫu popup builder
@apiSuccess {Object}            data.content                             Nội dung của popup builder
@apiSuccess {String}            data.session                                Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không
@apiSuccess {String}            data.merchant_id                            Định danh tennant
@apiSuccess {String}            data.thumbnail                            Link ảnh thumbnail
@apiSuccess {String}            data.small_thumbnail                       Link ảnh thumbnail kích thước nhỏ
@apiSuccess {Integer}            data.second_page                            Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiSuccess {Integer}            data.active                                 Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Ngừng xuất bản, 1:Xuất bản, 2: Trạng thái nháp</code>
@apiSuccess {Array}             data.categories                             Danh sách <code>id</code> của danh mục popup builder
@apiSuccess {Array}             data.type_codes                             Danh sách mã loại popup

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "lang": "vi",
    "data": {
        "id": 'popup_builder_id',
        "popup_template_id": "62b2e82ae634635c05d02134",
        "title": "popup builder copy",
        "content": {
            "html": "<div>This is body popup-builder</div>",
            "json": {
              "content": "This is body popup-builder"
            }
        },
        "categories": [],
        "thumbnail": "https://...",
        "small_thumbnail": "https://...",
        "session": "123123",
        "merchant_id": "merchant_id",
        "second_page": 0,
        "active": 0,
        "type_codes": ["lightbox"],
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10:10Z"
        "updated_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "updated_time": "2021-10-03T08:10:10Z"
    }
}

"""


# ----------------------- Count popup builder by filter -----------------------

"""
@api {GET} {domain}/template/api/v1.0/popups-builder/actions/count  Count popup builder
@apiDescription  API lấy count số lượng popup builder theo filter.
@apiGroup PopupBuilderV4x
@apiVersion 1.0.0
@apiName CountPopupBuilderFilter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Param:)            {String}        category_ids                Danh sách <code>id</code> định danh category(cách nhau bởi dấu ,).
@apiParam   (Param:)            {String}        [title]                    Tên popup builder cần tìm kiếm.
@apiParam   (Param:)            {Integer}       [active]                    Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Ngừng xuất bản, 1:Xuất bản, 2: Trạng thái nháp</code>
@apiParam   (Param:)            {String}        [type_codes]                Danh sách mã loại popup(cách nhau bởi dấu ,).
@apiParam	   (Param:)			{String}	    [account_id]                <code>id</code> của người tạo popup builder(cách nhau bởi dấu ,).
@apiParam	   (Param:)			{Date}	        [start_date]                Filter theo thời gian bắt đầu tạo popup builder.
@apiParam	   (Param:)			{Date}	        [end_date]                  Filter theo thời gian kết thúc tạo popup builder.

@apiSuccess                 {String}        id              <code>ID</code> của category(all- tất cả email, default- email mặc định)
@apiSuccess                 {Integer}       count    Số lượng popup builder trong danh mục.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "6406c41ab81ae2cdc53avxfb1",
      "count": 10
    },
    {
      "id": "6406c41abi12jk3jk12h",
      "count": 40
    },
    {
      "id": "default",
      "count": 10
    },
    "{
      "id": "all",
      "count": 60
    }
  ]
}
"""

"""
    Author: LocDX
    Company: Mobio
    Date Created: 27/05/2025
"""


# ============================================= CATEGORY POPUP TEMPLATE =============================================
"""
@apiDefine ResponseDetailCategoryPopupBuilder

@apiSuccess {String}            data.id                          <code>ID</code> định danh của category popup template
@apiSuccess {String}            data.name                         Tên category popup template

@apiSuccess {String}            data.created_by                   Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                 Thời điểm khởi tạo 

@apiSuccess {String}            data.updated_by                   Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                 Thời điểm cập nhật

"""

"""
@apiDefine paging_tokens
@apiParam   (Query:)    {Number}    [per_page] Số phần tử trên một page.<br/> Example: <code>&per_page=20</code>
@apiParam   (Query:)    {String}    [after_token] Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)    {String}    [before_token] Token để request lấy dữ liệu trang trước đó. Nếu trong danh sách tham số gửi lên có <code>after_token</code> thì ưu tiên xử lý <code>after_token</code>

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after_token]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before_token]    Token để lấy dữ liệu trang trước đó.
@apiSuccess     {Object}    [paging..per_page]          Số lượng phần tử trên 1 page
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after_token": "YXNkaGZha2RoZmFrZGZh",
            "before_token": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 15
    }
}
"""

# ----------------------- Add category popup template -----------------------
"""
@api {POST} {domain}/template/api/v1.0/popup-builder/categories                Tạo mới danh mục popup builder
@apiGroup CategoryPopupBuilder
@apiDescription Tạo mới category của popup builder
@apiVersion 1.0.0
@apiName AddCategoryPopupBuilder
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}           name              Tên danh mục popup builder  


@apiParamExample    {json}      BODY:
{
    "name": "name create"
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm danh mục popup builder

@apiUse ResponseDetailCategoryPopupBuilder

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "category_id"
        "name": "category name",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10:20Z",
        "updated_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "updated_time": "2021-10-03T08:10:20Z"
    }
}

"""
# ----------------------- Update category popup template  -----------------------
"""
@api {PATCH} {domain}/template/api/v1.0/popup-builder/categories/<category_id>                 Cập nhật danh mục popup builder
@apiGroup CategoryPopupBuilder
@apiDescription Sửa tên danh mục của popup builder theo category_id 
@apiVersion 1.0.0
@apiName UpdateCategoryPopupBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      category_id                Định danh của category cần sửa 
@apiParam   (BODY:)     {String}           name                 Tên danh mục popup builder


@apiParamExample    {json}      BODY:
{
    "name": "name update"
}    

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa danh mục popup builder 

@apiUse ResponseDetailCategoryPopupBuilder

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "category_id"
        "name": "name update",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10:20Z",
        "updated_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "updated_time": "2021-10-03T08:10:20Z"
    }
}

"""
# ----------------------- Delete category popup template  -----------------------
"""
@api {DELETE} {domain}/template/api/v1.0/popup-builder/categories/<category_id>                    Xoá danh mục popup builder
@apiGroup CategoryPopupBuilder
@apiDescription Xóa danh mục của popup builder theo category_id
@apiVersion 1.0.0
@apiName DeleteCategoryPopupBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      category_id                Định danh danh mục cần xóa

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa danh mục popup template 

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}


"""
# ----------------------- Get list category popup template -----------------------
"""
@api {GET} {domain}/template/api/v1.0/popup-builder/categories        Get danh sách danh mục popup builder
@apiGroup CategoryPopupBuilder
@apiDescription Lấy danh sách danh mục của popup builder
@apiVersion 1.0.0
@apiName ListCategoryPopupBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging_tokens

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách danh mục popup builder

@apiUse ResponseDetailCategoryPopupBuilder

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "id": "category_id",
        "name": "category name",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }],
    "paging": {
        "cursors": {
            "after_token": "YXNkaGZha2RoZmFrZGZh",
            "before_token": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 15
    }
    "lang": "vi",
    "message": "Request thành công."
}

"""

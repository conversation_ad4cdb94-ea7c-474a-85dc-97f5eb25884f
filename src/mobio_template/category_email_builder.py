"""
    Author: LocDX
    Company: Mobio
    Date Created: 15/05/2025
"""


# ----------------------- Tạ<PERSON> danh mục email template -----------------------

"""
@apiDefine paging_tokens
@apiParam   (Query:)    {Number}    [per_page] Số phần tử trên một page.<br/> Example: <code>&per_page=5</code>
@apiParam   (Query:)    {String}    [after_token] Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)    {String}    [before_token] Token để request lấy dữ liệu trang trước đó. Nếu trong danh sách tham số gửi lên có <code>after_token</code> thì ưu tiên xử lý <code>after_token</code>

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after_token]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before_token]    Token để lấy dữ liệu trang trước đó.
@apiSuccess     {Object}    [paging..per_page]          Số lượng phần tử trên 1 page
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after_token": "YXNkaGZha2RoZmFrZGZh",
            "before_token": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 15
    }
}
"""

"""
@api {POST} {domain}/template/api/v1.1/emails-builder/categories           Tạo danh mục email template
@apiGroup CategoryEmailBuilder
@apiDescription Tạo danh mục email template
@apiVersion 1.1.0
@apiName CreateCategoryEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}  name   Tên danh mục

@apiParamExample {json} Body example
{
  "name": "Danh mục 1"
}


@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {Object}            data                                    Dữ liệu danh mục email template.
@apiSuccess {String}            data.id                                 <code>ID</code> Id của danh mục email template.
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {String}          data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.merchant_id                        Thông tin của merchant_id.
@apiSuccess {String}            data.name                               Tên danh mục email template.
@apiSuccess {String}            data.name_unsigned                      Tên danh mục email template không dấu.
@apiSuccess {String}            data.updated_by                         Thông tin người cập nhật.
@apiSuccess {Integer}           data.updated_time                       Thời điểm cập nhật.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "created_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "created_time": "2025-05-14T07:17:45Z",
        "id": "682443991ffa55f5f0621f1d",
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "name": "danh mục 10",
        "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "updated_time": "2025-05-14T07:17:45Z"
    },
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Cập nhật danh mục email template ---------------------------

"""
@api {PUT} {domain}/template/api/v1.1/emails-builder/categories/<category_id>           Update danh mục email template
@apiGroup CategoryEmailBuilder
@apiDescription Update danh mục email template
@apiVersion 1.1.0
@apiName UpdateCategoryEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}  name   Tên danh mục cần update

@apiParamExample {json} Body example
{
  "name": "Danh mục 1"
}

@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {Object}            lang                                    mã ngôn ngữ trả về.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "created_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "created_time": "2025-05-14T07:17:45Z",
        "id": "682443991ffa55f5f0621f1d",
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "name": "danh mục 1",
        "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "updated_time": "2025-05-14T07:17:45Z"
    },
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Danh sách danh mục email template ---------------------------

"""
@api {GET} {domain}/template/api/v1.1/emails-builder/categories          Danh sách danh mục email template
@apiGroup CategoryEmailBuilder
@apiDescription Danh sách danh mục email template
@apiVersion 1.1.0
@apiName GetListCategoryEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging_tokens


@apiParam   (Query:)   {String}  [search]     Tên danh mục cần tìm kiếm


@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {Object}            data                                    Dữ liệu danh mục email template.
@apiSuccess {String}            data.id                                 <code>ID</code> Id của danh mục email template.
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {String}          data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.merchant_id                        Thông tin của merchant_id.
@apiSuccess {String}            data.name                               Tên danh mục email template.
@apiSuccess {String}            data.name_unsigned                      Tên danh mục email template không dấu.
@apiSuccess {String}            data.updated_by                         Thông tin người cập nhật.
@apiSuccess {Integer}           data.updated_time                       Thời điểm cập nhật.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "created_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "created_time": "2025-05-14T07:16:25Z",
            "id": "682443491ffa55f5f0621f10",
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "name": "chưa phân loại 1a1",
            "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "updated_time": "2025-05-14T10:52:27Z"
        }
    ],
    "paging": {
        "cursors": {
            "after_token": "YXNkaGZha2RoZmFrZGZh",
            "before_token": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 15
    }
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Xoá danh mục email template ---------------------------

"""
@api {DELETE} {domain}/template/api/v1.1/emails-builder/categories         Xoá danh mục email template
@apiGroup CategoryEmailBuilder
@apiDescription Delete danh mục email template
@apiVersion 1.1.0
@apiName DeleteCategoryEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (PARAM:)     {String}            ids                        Danh sách ID danh mục email template cần xoá(cách nhau bởi dấu ,).

@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {Object}            lang                                    mã ngôn ngữ trả về.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "lang": "vi",
    "message": "Request thành công."
}
"""
#!/usr/bin/python
# -*- coding: utf8 -*-
********************************** Insight category *********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {get} {HOST}/template/api/v1.0/sites/category/<category_id>/insights/site-amount  Insight Site Amount
@apiDescription  API lấy thống kê số lượng site theo danh mục
@apiGroup SiteCategory
@apiVersion 1.0.0
@apiName InsightSiteAmount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccess   {String}  sites_amount  Số lượng site trong danh mục.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "sites_amount": 10
}
"""
********************************** Delete category *********************************
* version: 1.0.0                                                                   *
************************************************************************************
"""
@api {delete} {HOST}/template/api/v1.0/categories/<category_id>  Xóa 1 thư mục
@apiDescription  API để xóa 1 thư mục.<br>Chỉ xóa được thư mục trống (không chưa bất kỳ site đang hoạt động nào).
@apiGroup SiteCategory
@apiVersion 1.0.0
@apiName DeleteCategory
@apiIgnore Not finished

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Update category *********************************
* version: 1.0.0                                                                   *
************************************************************************************
"""
@api {patch} {HOST}/template/api/v1.0/categories/<category_id>  Cập nhật category
@apiDescription  API cập nhật category.
@apiGroup SiteCategory
@apiVersion 1.0.0
@apiName UpdateCategory
@apiIgnore Not finished

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của danh mục. Tên của danh mục không được đặt trùng trên hệ thống.
@apiParamExample {json} Body example
{
  "name": "Mobio 1"
}

@apiSuccess   {String}  id  Định danh của category.
@apiSuccess   {String}  name  Tên của category.
@apiSuccess   {String}  sites_amount  Số lượng site trong danh mục.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "id": "6406c41ab81ae2cdc53avxfb1",
    "name": "Mobio 1",
    "sites_amount": 10
  }
}
"""

********************************** List category *********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {get} {HOST}/template/api/v1.0/sites/categories  List category
@apiDescription  API lấy danh sách category trên hệ thống.
@apiGroup SiteCategory
@apiVersion 1.0.0
@apiName ListCategory

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiSuccess   {String}  id  Định danh nhóm danh mục.
@apiSuccess   {String}  name  Tên nhóm danh mục. Hỗ trợ đa ngôn ngữ.
@apiSuccess   {ArrayObject}  categories  Danh sách danh mục

@apiSuccess   (Category)  {String}  id  Định danh danh mục.
@apiSuccess   (Category)  {String}  name  Tên nhóm danh mục. Hỗ trợ đa ngôn ngữ.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "6406c41ab81ae2cdc53avxfb1",
      "name": {
        "en": "Group 1",
        "vi": "Nhóm 1"
      }
      "categories": [
        {
          "id": "6406c41ab81ae2cdc53avxfb2",
          "name": {
            "en": "Cat 1",
            "vi": "Danh mục 1"
          }
        }
      ]
    },
    {
      "id": "6406c41abi12jk3jk12h",
      "name": {
        "en": "Group 2",
        "vi": "Nhóm 2"
      },
      "categories": [
        {
          "id": "6406c41ab81ae2cdc53avxfb3",
          "name": {
            "en": "Cat 3",
            "vi": "Danh mục 3"
          }
        },
        {
          "id": "6406c41ab81ae2cdc53avxfb4",
          "name": {
            "en": "Cat 4",
            "vi": "Danh mục 3"
          }
        }
      ]
    }
  ],
  ...
}
"""

********************************** Create category *********************************
* version: 1.0.0                                                                   *
************************************************************************************
"""
@api {post} {HOST}/template/api/v1.0/sites/category  Tạo site category
@apiDescription  API dùng để tạo danh mục mới để nhóm các site.
@apiGroup SiteCategory
@apiVersion 1.0.0
@apiName CreateCategory
@apiIgnore Not finished

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của danh mục. Tên của danh mục không được đặt trùng trên hệ thống.
@apiParamExample {json} Body example
{
  "name": "Mobio"
}

@apiSuccess   {String}  id  Định danh của category.
@apiSuccess   {String}  name  Tên của category.
@apiSuccess   {String}  sites_amount  Số lượng site trong danh mục.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "id": "6406c41ab81ae2cdc53avxfb1",
    "name": "Mobio",
    "sites_amount": 10
  }
}
"""
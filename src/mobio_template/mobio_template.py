"""
    Author: TruongCL
    Company: Mobio
    Date Created: 03/11/2021
"""

# ================================================ EMAIL TEMPLATE ================================================
"""
@apiDefine ResponseDetailEmailTemplate

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của email template
@apiSuccess {String}            data.name                               Tên mẫu email template
@apiSuccess {String}            [data.description]                        Description của email template 
@apiSuccess {String}            data.value                              Body của email template
@apiSuccess {String}            data.link_image                         Link image thumbnail
@apiSuccess {Int}               data.applier_type                       Type của template, 2-Mobio-upload-for-all-merchants, 3-Upload-for-a-merchant
@apiSuccess {String}            data.email_folder_id                    Id của thư mục chứa mẫu email
@apiSuccess {Boolean}           data.is_favorite                        Mẫu email có phải ưa thích hay không
@apiSuccess {String}            data.created_by                         <PERSON>h<PERSON><PERSON> tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo 
    
"""

# ----------------------- Add email template -----------------------
"""
@api {POST} {domain}/template/api/v1.0/emails/templates                  Create email template
@apiGroup Email template
@apiDescription Tạo mới mẫu email template 
@apiVersion 1.0.0
@apiName AddEmailTemplate
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}                            name                               Tên mẫu popup template <code>giới hạn 256 kí tự</code>
@apiParam   (BODY:)     {String}                            [description]                        Description của email template<code>giới hạn 512 kí tự</code>
@apiParam   (BODY:)     {String}                            value                               Body của email template
@apiParam   (BODY:)     {String}                            email_folder_id                     Id của thư mục chứa mẫu email.


@apiParamExample    {json}      BODY:
{
    "name": "template 1",
    "value": "<div>This is body template</div>",
    "email_folder_id": "6406c41ab81ae2cdc5323fb2"
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm email template

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Update email template  -----------------------
"""
@api {PATCH} {domain}/template/api/v1.0/emails/templates/<template_id>                    Update email template
@apiGroup Email template
@apiDescription Sửa mẫu email template đã tồn tại theo template_id
@apiVersion 1.0.0
@apiName UpdateEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}                            [title]                             Tên mẫu popup template <code>giới hạn 256 kí tự</code>
@apiParam   (BODY:)     {String}                            [link_image]                          Link image template
@apiParam   (BODY:)     {String}                            [description]                       Description của email template <code>giới hạn 512 kí tự</code>
@apiParam   (BODY:)     {String}                            [value]                               Body của email template
@apiParam   (BODY:)     {String}                            [email_folder_id]                     Id của thư mục chứa mẫu email.
@apiParam   (BODY:)     {Boolean}                           [is_favorite]                         Mẫu email ưa thích.


@apiParamExample    {json}      BODY:
{
    "applier_type": 3,
    "name": "template 1",
    "description": "description template 1",
    "value": "<div>This is body template</div>",
    "email_folder_id": "6406c41ab81ae2cdc5323fb2",
    "link_image": "https://...",
    "is_favorite": True
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa email template 

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Delete email template  -----------------------
"""
@api {DELETE} {domain}/template/api/v1.0/emails/templates                  Delete email template
@apiGroup Email template
@apiDescription Xóa mẫu email template theo template_id
@apiVersion 1.0.0
@apiName DeleteEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:)     {Array}                           ids                       Danh sách mẫu email xoá.

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa email template 

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}


"""
# ----------------------- Get list email template -----------------------
"""
@api {GET} {domain}/template/api/v1.0/emails/templates             Get list email template
@apiGroup Email template
@apiDescription Lấy danh sách email template
@apiVersion 1.0.0
@apiName ListEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam	   (Query:)			{Integer}	[applier_type]       Lấy danh sách mẫu email hệ thống: 2 là mẫu email hệ thống tạo, 3 là người dùng tạo.           
@apiParam	   (Query:)			{Integer}	[is_favorite]        Lấy danh sách mẫu email yêu thích: 1 là mẫu email ưa thích, 0 là mẫu email không thích.     
@apiParam	   (Query:)			{string}	[email_folder_id]    Id của thư mục chứa email.     
@apiParam	   (Query:)			{string}	[search]    Tìm kiếm theo tên email.     
@apiParam	   (Query:)			{string}	[sort]       Sắp xếp theo field tuỳ chỉnh.
@apiParam	   (Query:)			{integer}	[order]        1-asc, -1-desc.     


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách email template

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của email template
@apiSuccess {String}            data.name                               Tên mẫu email template
@apiSuccess {String}            data.link_image                         Link image thumbnail
@apiSuccess {String}            data.email_folder_id                    Id của thư mục chứa mẫu email
@apiSuccess {Boolean}           data.is_favorite                        Mẫu email có phải ưa thích hay không
@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo 
@apiSuccess {String}            data.email_folder_name                  Tên thư mục chứa mẫu email

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "applier_type": 3,
        "name": "template 1",
        "description": "description template 1",
        "email_folder_id": "6406c41ab81ae2cdc5323fb2",
        "email_folder_name": "email folder 1",
        "is_favorite": True,
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z",
        "link_image": "https://test1.mobio.vn/emk/images/7df7f4ef-49a5-4ac0-a392-3e4fcac20abe.jpg?t=1637652332.0"
    }],
    "paging": {
        "page": 1,
        "per_page": 20,
        "total_count": 813,
        "total_page": 407
    }
}

"""
# ----------------------- Count email template -----------------------
"""
@api {GET} {domain}/template/api/v1.0/emails/templates/action/count             Count email template
@apiGroup Email template
@apiDescription Lấy số lượng email template
@apiVersion 1.0.0
@apiName CountEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}            data                          Lấy danh sách dữ liệu

@apiSuccess   {String}  data.type  Loại mẫu email.
@apiSuccess   {String}  data.number_of_email_templates  Số lượng mẫu email.

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "type": "all",
            "number_of_email_templates": 58
        },
        {
            "type": "base",
            "number_of_email_templates": 58
        },
        {
            "type": "not_base",
            "number_of_email_templates": 58
        },
        {
            "type": "favorite",
            "number_of_email_templates": 20
        }
    ]
}


"""
# ----------------------- Get detail email template  -----------------------
"""
@api {GET} {domain}/template/api/v1.0/emails/templates/<template_id>                 Get detail email template
@apiGroup Email template
@apiDescription Lấy chi tiết email template theo template_id
@apiVersion 1.0.0
@apiName GetDetailEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết email template  

@apiUse ResponseDetailEmailTemplate
@apiSuccess {String}            data.email_folder_name                  Tên thư mục chứa mẫu email

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "applier_type": 3,
        "name": "template 1",
        "description": "description template 1",
        "value": "<div>This is body template</div>",
        "email_folder_id": "6406c41ab81ae2cdc5323fb2",
        "email_folder_name": "email_folder",
        "is_favorite": True,
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z",
        "link_image": "https://test1.mobio.vn/emk/images/7df7f4ef-49a5-4ac0-a392-3e4fcac20abe.jpg?t=1637652332.0"
    }
}

"""

# ================================================ POPUP TEMPLATE ================================================

"""
@apiDefine ResponseDetailPopupTemplate

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của popup template
@apiSuccess {String}            data.apply_type                          Type upload template, <code>2:Mobio-upload-for-all-merchants, 3:Upload-for-a-merchant</code>
@apiSuccess {String}            data.title                               Tên mẫu popup template
@apiSuccess {String}            data.body_template                         Body của popup template
@apiSuccess {String}            data.category_id                         ID của category 
@apiSuccess {String}            data.merchant_id                            Định danh của tennant
@apiSuccess {String}            data.second_page                            Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiSuccess {Dictionary}        data.avatar_info                            Data infor of image, chứa link thumbnail

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo

"""

# ----------------------- Add popup template -----------------------
"""
@api {POST} {domain}/template/api/v1.0/popups/templates                  Tạo mới mẫu popup
@apiGroup Popup template
@apiDescription Tạo mới mẫu popup template
@apiVersion 1.0.0
@apiName AddPopupTemplate
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (FORM-DATA:)     {String = 2:Mobio-upload-for-all-merchants,
                                    3:Upload-for-a-merchant}    applier_type                        Type upload template.
@apiParam   (FORM-DATA:)     {String}                            title                               Tên mẫu popup template <code>giới hạn 36 kí tự</code>
@apiParam   (FORM-DATA:)     {String}                            body_template                       Body của popup template
@apiParam   (FORM-DATA:)     {String}                            category_id                        ID của category
@apiParam   (FORM-DATA:)     {File}                              avatar                              File binary of image
@apiParam   (FORM-DATA:)     {String}                            second_page                          Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>


@apiParamExample    {json}      BODY:
{
    "applier_type": 3,
    "title": "template create",
    "category_id": "61d7b2e2269516adfd6ca280",
    "body_template": "<div>This is body template</div>",
    "avatar": "file binary of image",
    "second_page": "0"
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm popup template

@apiUse ResponseDetailPopupTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'template_id',
        "applier_type": 3,
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "category_id": "category id",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "merchant_id": "merchant_id",
        "second_page": "0",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Update popup template  -----------------------
"""
@api {PATCH} {domain}/template/api/v1.0/popups/templates/<template_id>                    Sửa mẫu popup
@apiGroup Popup template
@apiDescription Sửa mẫu popup template theo template_id
@apiVersion 1.0.0
@apiName UpdatePopupTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam (Resource:)   {string}      template_id                Định danh của popup template cần sửa
@apiUse merchant_id_header

@apiParam   (FORM-DATA:)     {String = 2:Mobio-upload-for-all-merchants,
                                    3:Upload-for-a-merchant}    applier_type                        Type upload template.
@apiParam   (FORM-DATA:)     {String}                            title                               Tên mẫu popup template <code>giới hạn 36 kí tự</code>
@apiParam   (FORM-DATA:)     {String}                            body_template                       Body của popup template
@apiParam   (FORM-DATA:)     {String}                            category_id                        ID của category
@apiParam   (FORM-DATA:)     {File}                              avatar                              Avatar được gửi lên dưới dạng binary
@apiParam   (FORM-DATA:)     {String}                            second_page                          Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>


@apiParamExample    {json}      BODY:
{
    "title": "template update",
    "category_id": "category id update",
    "body_template": "<div>This is body template</div>",
    "avatar": "file binary of image",
    "second_page": "0"
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa popup template 

@apiUse ResponseDetailPopupTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Delete popup template  -----------------------
"""
@api {DELETE} {domain}/template/api/v1.0/popups/templates/<template_id>                    Xóa một mẫu popup
@apiGroup Popup template
@apiDescription Xóa mẫu popup template theo template_id
@apiVersion 1.0.0
@apiName DeletePopupTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam (Resource:)   {string}      template_id                Định danh của popup template cần xóa
@apiUse merchant_id_header

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa popup template 


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Delete list popup template  -----------------------
"""
@api {POST} {domain}/template/api/v1.0/popups/templates/actions/delete-list             Xóa nhiều mẫu popup
@apiGroup Popup template
@apiDescription Xóa mẫu popup template theo danh sách template_id
@apiVersion 1.0.0
@apiName DeleteListPopupTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)      {List}           ids                   Danh sách template_id của Mẫu template cần xóa  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa danh sách popup template 


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Get list popup template -----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/templates                Lấy danh sách mẫu popup
@apiGroup Popup template
@apiDescription Lấy danh sách mẫu popup template
@apiVersion 1.0.0
@apiName ListPopupTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam (Query:)   {String}               applier_type               Định danh dạng gửi <code>2:Mobio-upload-for-all-merchants, 3:Upload-for-a-merchant.</code> <br>
Default value: <code>3</code><br>
Allowed values: <code>2, 3</code>
@apiParam   (Query:)     {String}            title                      Tên mẫu popup cần tìm kiếm, không phân biệt hoa-thường, có dấu-không dấu

@apiParam (Query:)   {String}      category_id                Định danh của category muốn hiển thị, để trống nếu lấy tât cả category 

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách popup template

@apiUse ResponseDetailPopupTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "id": 'template_id',
        "applier_type": 3,
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "category_id": "category id",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "merchant_id": "merchant_id",
        "second_page": "0",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}
"""
# ----------------------- Get detail popup template  -----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/templates/<template_id>                   Lấy chi tiết mẫu popup
@apiGroup Popup template
@apiDescription Lấy chi tiết mẫu popup template theo template_id
@apiVersion 1.0.0
@apiName GetDetailPopupTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      template_id                Định danh của popup template cần xem chi tiết

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết popup template  

@apiUse ResponseDetailPopupTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'template_id',
        "applier_type": 3,
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "category_id": "category id",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "merchant_id": "merchant_id",
        "second_page": "0",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Check name popup template is exists -----------------------
"""
@api {POST} {domain}/template/api/v1.0/popups/templates/actions/check-title-is-exists           Kiểm tra xem tên popup template có tổn tại không
@apiGroup Popup template
@apiDescription Kiểm tra xem tên popup template có tổn tại không
@apiVersion 1.0.0
@apiName CheckNamePopupIsExists

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}                            title                               Tên popup template <code>giới hạn 256 kí tự</code>

@apiParamExample    {json}      BODY:
{
    "title": "template 1",
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Kiểm tra xem popup template có đang được sử dụng không

@apiSuccess {String}            data.is_exists                <code>true:Đã-sử-dụng, false:Chưa-sử-dụng</code>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "is_exists": false
    }
}

"""

# ================================================ POPUP BUILDER ================================================

"""
@apiDefine ResponseDetailPopup

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của popup template
@apiSuccess {String}            data.popup_draft_id                      Định danh bản nháp của popup, nếu không có bản nháp thì giá trị None
@apiSuccess {String}            data.popup_template_id                   Định danh của mẫu popup sử dụng để tạo ra popup builder
@apiSuccess {String}            data.title                               Tên mẫu popup template
@apiSuccess {String}            data.body_template                         Body của popup template
@apiSuccess {String}            data.build_template                         Body của popup template sau khi được build
@apiSuccess {String}            data.session                                Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không
@apiSuccess {String}            data.merchant_id                            Định danh tennant
@apiSuccess {Dictionary}        data.avatar_info                            Data infor of image, chứa link thumbnail
@apiSuccess {Dictionary}        data.popup_builder_info                     Data infor of popup builder, chứa link publish của popup builder, tồn tại sau khi active popup builder
@apiSuccess {String}            data.second_page                            Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiSuccess {Int}               data.view                                   Tổng số lượt view của popup 
@apiSuccess {Int}               data.profile                                Tổng số profile của popup
@apiSuccess {String}            data.active                                 Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Không-kich-hoat, 1:Kich-hoạt</code>
@apiSuccess {Array}             data.using_by                               Lưu định danh các bên đang sử dụng popup
@apiSuccess {Array}             data.number_link_tracking                   Số lượng link tracking

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật

"""

# ----------------------- Add popup builder -----------------------
"""
@api {POST} {domain}/template/api/v1.0/popups                  Tạo mới popup builder
@apiGroup Popup builder
@apiDescription Tạo mới popup builder
@apiVersion 1.0.0
@apiName AddPopup
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}                            title                               Tên mẫu popup <code>giới hạn 36 kí tự</code>
@apiParam   (BODY:)     {String}                            [popup_template_id]                 Định danh của mẫu popup sử dụng để tạo ra popup builder
@apiParam   (BODY:)     {String}                            applier_type                        Định danh dạng gửi <code>2:Mobio-upload-for-all-merchants, 3:Upload-for-a-merchant.</code> <br>


@apiParamExample    {json}      BODY:
{
    "title": "popup builder create"
    "popup_template_id": "ID của mẫu popup"
}  

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của popup template
@apiSuccess {String}            data.popup_draft_id                      Định danh bản nháp của popup, nếu không có bản nháp thì giá trị 
@apiSuccess {String}            data.popup_template_id                   Định danh của mẫu popup sử dụng để tạo ra popup builder
@apiSuccess {String}            data.title                               Tên mẫu popup template
@apiSuccess {String}            data.body_template                         Body của popup template
@apiSuccess {String}            data.build_template                         Body của popup template sau khi được build
@apiSuccess {String}            data.session                                Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không
@apiSuccess {String}            data.merchant_id                            Định danh tennant
@apiSuccess {Dictionary}        data.avatar_info                            Data infor of image, chứa link thumbnail
@apiSuccess {String}            data.second_page                            Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiSuccess {Int}               data.view                                   Tổng số lượt view của popup 
@apiSuccess {Int}               data.profile                                Tổng số profile của popup
@apiSuccess {String}            data.active                                 Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Không-kich-hoat, 1:Kich-hoạt</code>
@apiSuccess {Array}             data.using_by                               Lưu định danh các bên đang sử dụng popup

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'popup_builder_id',
        "popup_draft_id": "",
        "popup_template_id": "62b2e82ae634635c05d02134",
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "build_template": "<div>This is body template</div>",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "session": "123123",
        "merchant_id": "merchant_id",
        "second_page": "0",
        "profile": 0,
        "view": 0,
        "active": "0",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Update popup builder -----------------------
"""
@api {PATCH} {domain}/template/api/v1.0/popups/<popup_builder_id>                    Cập nhật popup builder
@apiGroup Popup builder
@apiDescription Sửa popup theo popup_builder_id
@apiVersion 1.0.0
@apiName UpdatePopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {String}      popup_builder_id                Định danh của popup builder cần sửa

@apiParam   (FORM-DATA:)     {String}                            popup_draft_id                      Định danh bản nháp của popup, nếu không có bản nháp thì giá trị None
@apiParam   (FORM-DATA:)     {String}                            popup_template_id                   Định danh popup template tạo lên popup builder
@apiParam   (FORM-DATA:)     {String}                            body_template                       Body của popup builder
@apiParam   (FORM-DATA:)     {String}                            build_template                       Body của popup builder sau khi được build 
@apiParam   (FORM-DATA:)     {String}                            dynamic                             Chứa thông tin dynamic truyền lên từ FE
@apiParam   (FORM-DATA:)     {String}                            session                             Session được trả về từ backend để kiểm tra phiên bản có phải mới nhất không 
@apiParam   (FORM-DATA:)     {File}                              avatar                              File binary of image
@apiParam   (FORM-DATA:)     {String}                            second_page                         Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiParam   (FORM-DATA:)     {String}                            current_field                       Chứa thông tin tất cả field trong popup
@apiParam   (FORM-DATA:)     {String}                            current_button                      Chứa thông tin tất cả button trong popup


@apiParamExample    {json}      BODY:
{
    "popup_draft_id": "popup_draft_id",
    "popup_template_id": "popup_template_id",
    "body_template": "<div>This is body popup builder</div>",
    "build_template": "<div>This is body popup builder</div>",
    "dynamic": "dynamic field",
    "session": "123123",
    "merchant_id": "merchant_id",
    "second_page": "0"
    "current_field": "current field"
    "current_button": "current button"
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa popup builder 

@apiUse ResponseDetailPopup

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'popup_builder_id',
        "popup_draft_id": "popup draft id",
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "build_template": "<div>This is body template</div>",
        "dynamic": "dynamic field",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "popup_builder_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "session": "123123",
        "merchant_id": "merchant_id",
        "second_page": "0",
        "profile": 0,
        "view": 0,
        "active": "0",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Update title popup builder -----------------------
"""
@api {PATCH} {domain}/template/api/v1.0/popups/<popup_builder_id>/actions/update-title                 Cập nhật tên popup builder
@apiGroup Popup builder
@apiDescription Sửa tên popup theo popup_builder_id
@apiVersion 1.0.0
@apiName UpdateTitlePopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {String}      popup_builder_id                Định danh của popup builder cần sửa

@apiParam   (FORM-DATA:)     {String}                            title                               Tên mẫu popup builder <code>giới hạn 36 kí tự</code>


@apiParamExample    {json}      BODY:
{
    "title": "popup builder update",
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa tên popup builder 

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Delete popup builder -----------------------
"""
@api {DELETE} {domain}/template/api/v1.0/popups/<popup_builder_id>                    Xóa popup builder
@apiGroup Popup builder
@apiDescription Xóa popup theo popup_builder_id
@apiVersion 1.0.0
@apiName DeletePopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id                Định danh của popup cần xóa

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa popup builder 


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}


"""
# ----------------------- Check license popup builder export -----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/actions/check-license-export                    Kiểm tra license xem merchant có được export popup builder không 
@apiGroup Popup builder
@apiDescription API kiểm tra license khi thực hiện thao tác export popup builder<br/>
Nếu <code>code == 200</code> FE cho phép client thực hiện export.<br/>
Nếu <code>code != 200</code> FE nhả lỗi và chặn việc export<br/>
@apiVersion 1.0.0
@apiName CheckLicenseExportPopupBuilder

@apiUse 401
@apiUse 402
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess {Integer}           code                          200
@apiSuccess {String}            message                       request thành công.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
}

"""
# ----------------------- Get list popup builder-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups                Lấy danh sách popup builder
@apiGroup Popup builder
@apiDescription Lấy danh sách popup builder
@apiVersion 1.0.0
@apiName ListPopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)     {String}              active                   Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Không-kich-hoat, 1:Kich-hoạt</code>, bỏ qua nếu lấy tất cả
@apiParam   (Query:)     {String}              title                    Tên popup cần tìm kiếm, không phân biệt hoa-thường, có dấu-không dấu

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách popup builder

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của popup template
@apiSuccess {String}            data.popup_draft_id                      Định danh bản nháp của popup, nếu không có bản nháp thì giá trị None
@apiSuccess {String}            data.title                               Tên mẫu popup template
@apiSuccess {String}            data.session                                Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không
@apiSuccess {String}            data.dynamic                                Chứa thông tin dynamic truyền lên từ FE
@apiSuccess {String}            data.merchant_id                            Định danh tennant
@apiSuccess {Dictionary}        data.avatar_info                            Data infor of image, chứa link thumbnail
@apiSuccess {Dictionary}        data.popup_builder_info                     Data infor of popup builder, chứa link publish của popup builder, tồn tại sau khi active popup builder
@apiSuccess {String}            data.second_page                            Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiSuccess {Int}               data.view                                   Tổng số lượt view của popup 
@apiSuccess {Int}               data.profile                                Tổng số  profile của popup
@apiSuccess {String}            data.active                                 Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Không-kich-hoat, 1:Kich-hoạt</code>
@apiSuccess {Array}            data.using_by                               Lưu định danh các bên đang sử dụng popup

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "id": 'popup_builder_id',
        "popup_draft_id": "popup_draft_id",
        "title": "Tên popup",
        "dynamic": "dynamic field",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "popup_builder_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "session": "123123",
        "merchant_id": "merchant_id",
        "second_page": "0",
        "profile": 0,
        "view": 0,
        "active": "0",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}
"""
# ----------------------- Get detail popup builder-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>                   Lấy chi tiết popup builder
@apiGroup Popup builder
@apiDescription Lấy chi tiết popup theo popup_builder_id
@apiVersion 1.0.0
@apiName GetDetailPopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id                Định danh của popup builder cần xem chi tiết

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết popup builder  

@apiUse ResponseDetailPopup

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'popup_builder_id',
        "popup_draft_id": "popup draft id",
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "build_template": "<div>This is body template</div>",
        "dynamic": "dynamic field",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "popup_builder_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "session": "123123",
        "merchant_id": "merchant_id",
        "second_page": "0",
        "profile": 0,
        "view": 0,
        "active": "0",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Active in list popup builder-----------------------
"""
@api {POST} {domain}/template/api/v1.0/popups/<popup_builder_id>/actions/active                   Kich hoạt popup builder
@apiGroup Popup builder
@apiDescription Kich hoạt popup builder
@apiVersion 1.0.0
@apiName ActivePopupInList
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Resource:)   {string}                            popup_builder_id                        Định danh của popup builder cần kíck hoạt

@apiParam   (FORM-DATA:)     {String}                            session                            Session được trả về từ backend để kiểm tra phiên bản có phải mới nhất không 

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Kích hoạt popup builder ngoài danh sách

@apiUse ResponseDetailPopup

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'popup_builder_id',
        "popup_draft_id": "popup draft id",
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "build_template": "<div>This is body template</div>",
        "dynamic": "dynamic field",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "popup_builder_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "session": "123123",
        "merchant_id": "merchant_id",
        "second_page": "0",
        "active": "0",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""
# ----------------------- Active in list popup builder-----------------------
"""
@api {POST} {domain}/template/api/v1.0/popups/<popup_builder_id>/actions/active/json                   Kích hoạt popup bên ngoài danh sách
@apiGroup Popup builder
@apiDescription Kich hoạt mẫu popup bên ngoài danh sách popup
@apiVersion 1.0.0
@apiName ActivePopupInListV2
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Resource:)   {string}                            popup_builder_id                        Định danh của popup builder cần kíck hoạt

@apiParam   (BODY:)     {String}                            session                            Session được trả về từ backend để kiểm tra phiên bản có phải mới nhất không 

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Kích hoạt popup builder ngoài danh sách

@apiUse ResponseDetailPopup

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'popup_builder_id',
        "popup_draft_id": "popup draft id",
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "build_template": "<div>This is body template</div>",
        "dynamic": "dynamic field",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "popup_builder_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "session": "123123",
        "merchant_id": "merchant_id",
        "second_page": "0",
        "active": "0",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""
# ----------------------- Update and Active popup builder -----------------------
"""
@api {PATCH} {domain}/template/api/v1.0/popups/<popup_builder_id>/actions/active-and-update                   Cập nhật và kích hoạt popup builder
@apiGroup Popup builder
@apiDescription Cập nhật và kích hoạt popup builder
@apiVersion 1.0.0
@apiName ActivePopup
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Resource:)   {String}      popup_builder_id                Định danh của popup builder cần sửa

@apiParam   (FORM-DATA:)     {String}                            popup_template_id                   Định danh popup template tạo lên popup builder
@apiParam   (FORM-DATA:)     {String}                            body_template                       Body của popup builder
@apiParam   (FORM-DATA:)     {String}                            build_template                      Body của popup builder sau khi được build 
@apiParam   (FORM-DATA:)     {String}                            dynamic                             Chứa thông tin dynamic truyền lên từ FE
@apiParam   (FORM-DATA:)     {String}                            session                             Session được trả về từ backend để kiểm tra phiên bản có phải mới nhất không 
@apiParam   (FORM-DATA:)     {File}                              avatar                              File binary of image
@apiParam   (FORM-DATA:)     {String}                            second_page                         Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiParam   (FORM-DATA:)     {Int}                               [number_link_tracking]                Số lượng link tracking 


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Kích hoạt popup trong trang chỉnh sửa

@apiUse ResponseDetailPopup

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'popup_builder_id',
        "popup_draft_id": "popup draft id",
        "body_template": "<div>This is body template</div>",
        "build_template": "<div>This is body template</div>",
        "dynamic": "dynamic field",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "popup_builder_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "session": "123123",
        "merchant_id": "merchant_id",
        "second_page": "0",
        "active": "1",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""
# ----------------------- Delete popup draft-----------------------
"""
@api {DELETE} {domain}/template/api/v1.0/popups/<popup_builder_id>/actions/delete-draft                  Xóa bản nháp của popup builder
@apiGroup Popup builder
@apiDescription Xóa bản nháp của popup builder
@apiVersion 1.0.0
@apiName DeletePopupDraft
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Resource:)   {string}                            popup_builder_id                      Định danh của popup builder cần xóa bản nháp


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa bản nháp của popup builder

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}
"""
# ----------------------- Edit with main popup builder -----------------------
"""
@api {PATCH} {domain}/template/api/v1.0/popups/<popup_builder_id>/actions/edit-with-main               Chỉnh sửa bản chính popup builder
@apiGroup Popup builder
@apiDescription Lựa chọn sửa trên bản chính của popup
@apiVersion 1.0.0
@apiName EditWithMainPopupBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id                Định danh của popup builder sửa trên bản chính

@apiParamExample    {json}      BODY:
{
    "popup_draft_id": "",
    "session": "123123"
} 

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Chỉnh sửa bản chính popup builder

@apiUse ResponseDetailPopup

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'popup_builder_id',
        "popup_draft_id": "popup draft id",
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "popup_builder_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "session": "123123",
        "merchant_id": "merchant_id",
        "second_page": "0",
        "profile": 0,
        "view": 0,
        "active": "0",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Add info entity connect with popup  -----------------------
"""
@api {PATCH} {domain}/template/api/v1.0/popups/<popup_builder_id>/actions/connect-with             Thêm thông tin định danh của thực thể sử dụng popup builder
@apiGroup Popup builder
@apiDescription Thêm thông tin định danh của thực thể sử dụng popup builder
@apiVersion 1.0.0
@apiName AddInfoEntityConnectWithPopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id          Định danh của popup builder được sử dụng


@apiParam   (BODY:)     {String}                            journey_builder_using                          Định danh của journey builder sử dụng popup
@apiParam   (BODY:)     {String}                            master_campaign_using                          Định danh của master campaign sử dụng popup


@apiParamExample    {json}      BODY:
{
    "journey_builder_using": "journey_builder_using",
    "master_campaign_using": "master_campaign_using"
}   

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm định danh thực thể sử dụng popup builder

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Add info entity connect with many popup  -----------------------
"""
@api {PATCH} {domain}/template/api/v1.0/popups/actions/connect-with             Thêm thông tin định danh của nhiều thực thể sử dụng popup builder 
@apiGroup Popup builder
@apiDescription Thêm thông tin định danh của nhiều thực thể sử dụng popup builder
@apiVersion 1.0.0
@apiName AddInfoEntityConnectWithManyPopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (BODY:)     {List}                              popup_builder_using                            Danh sách popup_builder_id
@apiParam   (BODY:)     {String}                            journey_builder_using                          Định danh của journey builder sử dụng popup
@apiParam   (BODY:)     {String}                            master_campaign_using                          Định danh của master campaign sử dụng popup


@apiParamExample    {json}      BODY:
{
    "popup_builder_using": [],
    "journey_builder_using": "journey_builder_using",
    "master_campaign_using": "master_campaign_using"
}   

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm thông tin định danh của nhiều thực thể sử dụng popup builder

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Add info entity disconnect with popup  -----------------------
"""
@api {PATCH} {domain}/template/api/v1.0/popups/<popup_builder_id>/actions/disconnect-with            Thêm thông tin định danh của thực thể bỏ sử dụng popup builder
@apiGroup Popup builder
@apiDescription Thêm thông tin định danh của thực thể bỏ sử dụng popup builder
@apiVersion 1.0.0
@apiName AddInfoEntityDisconnectWithPopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id          Định danh của popup builder được sử dụng


@apiParam   (BODY:)     {String}                            journey_builder_using                          Định danh của journey builder sử dụng popup
@apiParam   (BODY:)     {String}                            master_campaign_using                          Định danh của master campaign sử dụng popup


@apiParamExample    {json}      BODY:
{
    "journey_builder_using": "journey_builder_using",
    "master_campaign_using": "master_campaign_using"
}   

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm thông tin định danh của thực thể bỏ sử dụng popup builder

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Get list id popup builder-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/actions/get-list-id                Lấy danh sách id popup builder
@apiGroup Popup builder
@apiDescription Lấy danh sách id popup builder
@apiVersion 1.0.0
@apiName ListIdPopupBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)     {String}              active                   Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Không-kich-hoat, 1:Kich-hoạt</code>, bỏ qua nếu lấy tất cả
@apiParam   (Query:)     {String}              search                   Tên popup cần tìm kiếm, không phân biệt hoa-thường, có dấu-không dấu

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách id popup builder

@apiSuccess {List}            data.ids                        Danh sách danh id sách popup builder

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "ids": ["id_1, id_2"]
    }
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}

"""
# ----------------------- Get list name popup builder-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/actions/get-list-name                Lấy danh sách tên popup builder
@apiGroup Popup builder
@apiDescription Lấy danh sách tên popup builder
@apiVersion 1.0.0
@apiName ListNamePopupBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)     {String}              active                   Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Không-kich-hoat, 1:Kich-hoạt</code>, bỏ qua nếu lấy tất cả
@apiParam   (Query:)     {String}              search                   Tên popup cần tìm kiếm, không phân biệt hoa-thường, có dấu-không dấu

@apiParam   (BODY:)     {List}        ids                   Danh sách các ids cần lấy, nếu không truyền lên sẽ lấy all


@apiParamExample    {json}      BODY:
{
    "ids": []
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách tên popup builder

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của popup builder
@apiSuccess {String}            data.title                              Tên popup builder

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "id": 'popup_builder_id',
        "title": "Tên popup",
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}
"""

# ----------------------- Get list popup by ids-----------------------
"""
@api {POST} {domain}/template/api/v1.0/popups/actions/get-by-ids    Lấy danh sách popup builder theo ids
@apiGroup Popup builder
@apiDescription Lấy danh sách detail popup builder theo ids
@apiVersion 1.0.0
@apiName ListPopupByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)     {Array} ids           Danh sách các popup cần lấy url (Giới hạn max = 20)
@apiParam   (Body:)     {Array} [fields]      Danh sách các fields của detail popup cần trả về (Mặc định lấy <code>id, title</code>)

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {ArrayObject}            data                     Lấy danh sách popup builder

@apiUse ResponseDetailPopup


@apiParamExample    {json}  Body:
{
    "ids": ["62343a61sdce300e3c9c4499", "4499f2b1d9ff6f6f83a93e42"],
    "fields": ["id", "title", "popup_builer_info"]
}

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "id": "62343a61sdce300e3c9c4499",
            "title": "Khuyến mãi siêu khủng",
            "popup_builer_info": {
                "url": "https://fake.storage.vn/static/fake/upload/fake1.html",
                "local_path": "fake1.html",
                "filename": "text/html",
            }
        },
        {
            "id": "4499f2b1d9ff6f6f83a93e42",
            "title": "Form khảo sát khách hàng",
            "popup_builer_info": {
                "url": "https://fake.storage.vn/static/fake/upload/fake2.html",
                "local_path": "fake2.html",
                "filename": "text/html",
            }
        }
    ],
}
"""
# ----------------------- Check popup is used -----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/actions/check-is-used           Kiểm tra xem popup builder có đang được sử dụng không
@apiGroup Popup builder
@apiDescription Kiểm tra xem popup builder có đang được sử dụng không
@apiVersion 1.0.0
@apiName CheckPopupIsUsed

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id                Định danh của popup cần kiểm tra

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Kiểm tra xem popup builder có đang được sử dụng không

@apiSuccess {String}            data.is_used                  <code>true:Đã-sử-dụng, false:Chưa-sử-dụng</code>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "is_used": false
    }
}

"""
# ----------------------- Copy popup builder -----------------------
"""
@api {POST} {domain}/template/api/v1.0/popups/<popup_builder_id>/actions/copy           Sao chép popup builder
@apiGroup Popup builder
@apiDescription Tạo một bản sao chép của popup builder
@apiVersion 1.0.0
@apiName CopyPopup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id                Định danh của popup builder muốn sao chép

@apiParam   (BODY:)     {String}                            title                               Tên mẫu popup <code>giới hạn 36 kí tự</code>
@apiParam   (BODY:)     {String}                            session                           Session được trả về từ backend để kiểm tra phiên bản có phải mới nhất không

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Tạo một bản sao chép của popup builder

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của popup template
@apiSuccess {String}            data.popup_draft_id                      Định danh bản nháp của popup, nếu không có bản nháp thì giá trị 
@apiSuccess {String}            data.popup_template_id                   Định danh của mẫu popup sử dụng để tạo ra popup builder
@apiSuccess {String}            data.title                               Tên mẫu popup template
@apiSuccess {String}            data.body_template                         Body của popup template
@apiSuccess {String}            data.build_template                         Body của popup template sau khi được build
@apiSuccess {String}            data.session                                Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không
@apiSuccess {String}            data.merchant_id                            Định danh tennant
@apiSuccess {Dictionary}        data.avatar_info                            Data infor of image, chứa link thumbnail
@apiSuccess {String}            data.second_page                            Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiSuccess {Int}               data.view                                   Tổng số lượt view của popup 
@apiSuccess {Int}               data.profile                                Tổng số profile của popup
@apiSuccess {String}            data.active                                 Xác định popup ở trái thái kích hoạt hay không kích hoạt <code>0:Không-kich-hoat, 1:Kich-hoạt</code>
@apiSuccess {Array}             data.using_by                               Lưu định danh các bên đang sử dụng popup

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'popup_builder_id',
        "popup_draft_id": "",
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "build_template": "<div>This is body template</div>",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "session": "123123",
        "merchant_id": "merchant_id",
        "second_page": "0",
        "profile": 0,
        "view": 0,
        "active": "0",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Check name popup is exists -----------------------
"""
@api {POST} {domain}/template/api/v1.0/popups/actions/check-title-is-exists           Kiểm tra xem tên popup builder có tổn tại không
@apiGroup Popup builder
@apiDescription Kiểm tra xem tên popup builder có tổn tại không
@apiVersion 1.0.0
@apiName CheckNamePopupIsExists

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}                            title                               Tên popup builder <code>giới hạn 256 kí tự</code>

@apiParamExample    {json}      BODY:
{
    "title": "template 1",
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Kiểm tra xem popup builder có đang được sử dụng không

@apiSuccess {String}            data.is_exists                <code>true:Đã-sử-dụng, false:Chưa-sử-dụng</code>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "is_exists": false
    }
}

"""

# ================================================ POPUP DRAFT ================================================

"""
@apiDefine ResponseDetailPopupDraft

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của popup draft
@apiSuccess {String}            data.title                               Tên mẫu popup template
@apiSuccess {String}            data.body_template                         Body của popup template
@apiSuccess {String}            data.build_template                         Body của popup template sau khi được build
@apiSuccess {String}            data.session                                Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không
@apiSuccess {String}            data.merchant_id                            Định danh tennant
@apiSuccess {Dictionary}        data.avatar_info                            Data infor of image, chứa link thumbnail
@apiSuccess {String}            data.second_page                            Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật

"""

# ----------------------- Get detail popup draft-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/drafts/<popup_draft_id>                   Get detail popup draft
@apiGroup Popup draft
@apiDescription Lấy chi tiết bản nháp theo popup_draft_id
@apiVersion 1.0.0
@apiName GetDetailPopupDraft

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_draft_id                Định danh của bản nháp

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết bản nháp

@apiUse ResponseDetailPopupDraft

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'popup_draft_id',
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "build_template": "<div>This is body template</div>",
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "session": "123123",
        "merchant_id": "merchant_id",
        "second_page": "0",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}

"""

# ================================================ OBJECTS TEMPLATE ================================================

"""
@apiDefine ResponseDetailObjectsTemplate

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của objects template
@apiSuccess {String}            data.apply_type                          Type upload template, <code>2:Mobio-upload-for-all-merchants, 3:Upload-for-a-merchant</code>
@apiSuccess {String}            data.template_type                       Là một trong các dối tượng <code>buttons, forms, icons, counters</code>
@apiSuccess {String}            data.title                               Tên mẫu objects template
@apiSuccess {String}            data.body_template                         Body của objects template
@apiSuccess {Dictionary}        data.avatar_info                            Data infor of image, chứa link thumbnail
@apiSuccess {String}            data.merchant_id                            Định danh của tennant

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo

"""

# ----------------------- Add object template -----------------------
"""
@api {POST} {domain}/template/api/v1.0/<objects>/templates                 Create objects template
@apiGroup Objects template
@apiDescription Tạo mới mẫu objects template
@apiVersion 1.0.0
@apiName AddObjectsTemplate
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      objects              Là một trong các dối tượng <code>buttons, forms, icons, counters</code>

@apiParam   (FORM-DATA:)     {String = 2:Mobio-upload-for-all-merchants,
                                    3:Upload-for-a-merchant}    applier_type                        Type upload template.
@apiParam   (FORM-DATA:)     {String}                            title                               Tên mẫu objects template <code>giới hạn 36 kí tự</code>
@apiParam   (FORM-DATA:)     {String}                            body_template                       Body của objects template
@apiParam   (FORM-DATA:)     {String}                            element_ids                        Danh sách id của phần tử html, cách nhau bằng dấu ','
@apiParam   (FORM-DATA:)     {File}                              avatar                              File binary of image
@apiParam   (FORM-DATA:)     {String}                            merchant_id                         Định danh của tenant
@apiParam   (FORM-DATA:)     {String}                            [form_layout]                       Định nghĩa hướng của form, Là một trong các giá trị <code>1:Dọc, 2:Ngang</code>
@apiParam   (FORM-DATA:)     {String=popup,landingpage}         [resource=popup]                     Loại đối tượng sử dụng Icon. 


@apiParamExample    {json}      BODY:
{
    "applier_type": "3",
    "template_type": "buttons",
    "title": "template create",
    "body_template": "<div>This is body template</div>",
    "element_ids": "bac,acb,acb",
    "avatar": "file binary of image",
    "merchant_id": "merchant_id"
    "form_layout": "1"
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm objects template

@apiUse ResponseDetailObjectsTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'template_id',
        "applier_type": "3",
        "template_type": "buttons",
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "element_ids": [
            "bac",
            "acb",
            "acb"
        ],
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "merchant_id": "merchant_id",
        "form_layout": "1",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Update objects template  -----------------------
"""
@api {PATCH} {domain}/template/api/v1.0/<objects>/templates/<template_id>                    Update objects template
@apiGroup Objects template
@apiDescription Sửa mẫu objects template theo template_id
@apiVersion 1.0.0
@apiName UpdateObjectsTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      objects              Là một trong các dối tượng <code>buttons, forms, icons, counters</code>
@apiParam (Resource:)   {string}      template_id          Định danh của objects template cần sửa

@apiParam   (FORM-DATA:)     {String}                            title                               Tên mẫu objects template <code>giới hạn 36 kí tự</code>
@apiParam   (FORM-DATA:)     {String}                            body_template                       Body của objects template
@apiParam   (FORM-DATA:)     {File}                              avatar                              Avatar được gửi lên dưới dạng binary
@apiParam   (FORM-DATA:)     {String}                            merchant_id                         Định danh của tenant


@apiParamExample    {json}      BODY:
{
    "template_type": "buttons",
    "title": "template update",
    "body_template": "<div>This is body template</div>",
    "avatar": "file binary of image"
    "merchant_id": "merchant_id"
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa objects template 

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Delete objects template  -----------------------
"""
@api {DELETE} {domain}/template/api/v1.0/<objects>/templates/<template_id>                    Delete objects template
@apiGroup Objects template
@apiDescription Xóa mẫu objects template theo template_id
@apiVersion 1.0.0
@apiName DeleteObjectsTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      objects              Là một trong các dối tượng <code>buttons, forms, icons, counters</code>
@apiParam (Resource:)   {string}      template_id          Định danh của objects template cần xóa

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa objects template 


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Delete list objects template  -----------------------
"""
@api {DELETE} {domain}/template/api/v1.0/<objects>/templates                    Delete list objects template
@apiGroup Objects template
@apiDescription Xóa mẫu objects template theo danh sách template_id
@apiVersion 1.0.0
@apiName DeleteListObjectsTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      objects              Là một trong các dối tượng <code>buttons, forms, icons, counters</code>

@apiParam   (BODY:)     {List}        ids                   Danh sách các ids cần xóa  


@apiParamExample    {json}      BODY:
{
    "ids": []
}  
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa objects template theo danh sách ids 


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Get list objects template -----------------------
"""
@api {GET} {domain}/template/api/v1.0/<objects>/templates                Get list objects template
@apiGroup Objects template
@apiDescription Lấy danh sách mẫu objects template
@apiVersion 1.0.0
@apiName ListObjectsTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam (Resource:)   {string}      objects              Là một trong các dối tượng <code>buttons, forms, icons, counters</code>
@apiParam (Query:)      {String}      applier_type         Định danh dạng gửi <code>2:Mobio-upload-for-all-merchants, 3:Upload-for-a-merchant.</code> <br>
Default value: <code>3</code><br>
Allowed values: <code>2, 3</code>
@apiParam   (Query:)     {String}              title                    Tên objects cần tìm kiếm, không phân biệt hoa-thường, có dấu-không dấu
@apiParam   (Query:)     {String}              form_layout              Định nghĩa hướng của form, Là một trong các giá trị <code>1:Dọc, 2:Ngang</code>

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách objects template

@apiUse ResponseDetailObjectsTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "id": 'template_id',
        "applier_type": 3,
        "template_type": "buttons",
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "element_ids": [
            "bac",
            "acb",
            "acb"
        ],
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "merchant_id": "merchant_id",
        "form_layout": "1",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}


"""
# ----------------------- Get detail objects template  -----------------------
"""
@api {GET} {domain}/template/api/v1.0/<objects>/templates/<template_id>                   Get detail objects template
@apiGroup Objects template
@apiDescription Lấy chi tiết mẫu objects template theo template_id
@apiVersion 1.0.0
@apiName GetListObjectsTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      objects              Là một trong các dối tượng <code>buttons, forms, icons, counters</code>
@apiParam (Resource:)   {string}      template_id             Định danh của objects template cần xem chi tiết

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết objects template  

@apiUse ResponseDetailObjectsTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'template_id',
        "applier_type": 3,
        "template_type": "buttons",
        "title": "Tên popup",
        "body_template": "<div>This is body template</div>",
        "element_ids": [
            "bac",
            "acb",
            "acb"
        ],
        "avatar_info": {
            "url": "",
            "filename": "",
            "format": "",
        },
        "merchant_id": "merchant_id",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}

"""

# ================================================ FONT DEFAULT ================================================
"""
@apiDefine ResponseDetailFontDefault

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của objects template
@apiSuccess {String}            data.title                               Tên font default
@apiSuccess {String}            data.url                                Link font default

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo

"""
# ----------------------- Get list font default -----------------------
"""
@api {GET} {domain}/template/api/v1.0/font-default                Get list font default
@apiGroup Font default
@apiDescription Lấy danh sách font default
@apiVersion 1.0.0
@apiName ListFontDefault

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)     {String}              title                    Tên font default cần tìm kiếm, không phân biệt hoa-thường, có dấu-không dấu

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách font default

@apiUse ResponseDetailFontDefault

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "_id": "6246826fe93113d7a1daf139",
        "id": "6246826fe93113d7a1daf139",
        "title": "Poppins",
        "url": "https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,500;1,500&display=swap",
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_time": "2022-04-01 04:41:19.039543"
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}


"""

# ================================================ POPUP REPORT ================================================

# ----------------------- Get information of widget 1-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/widget-1               Profile điền form
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/profile-fill-in-the-form               Profile điền form
@apiGroup Popup report
@apiDescription Profile điền form
@apiVersion 1.0.0
@apiName InformationWidget1

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup 

@apiParam (Query:)      {String}      since                    Thời gian bắt đầu của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian
@apiParam (Query:)      {String}      until                    Thời gian kết thúc của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {List}              data                          Profile điền form
@apiSuccess {Dict}              comparison_between_2_time_periods   Thông tin tổng hợp giữa 2 khoảng thời gian

@apiSuccess {String}            data.count                    Số lượt profile điền form
@apiSuccess {String}            data.day                      Đinh danh cho ngày
@apiSuccess {String}            comparison_between_2_time_periods.summary_before          Tổng profile trong khoảng thời gian hiện tại
@apiSuccess {String}            comparison_between_2_time_periods.summary_present         Tổng profile trong khoảng thời gian trước đó
@apiSuccess {Integer}           number_of_profile                                         Số lượng profile điền form

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "count": 1,
            "day": "2022-03-20"
        },
        {
            "count": 1,
            "day": "2022-03-19"
        }
    ],
    "comparison_between_2_time_periods": {
        "summary_before": 0,
        "summary_present": 0
    }
    "number_of_profile": 2
}
"""
# ----------------------- Get information of widget 2-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/widget-2                   Tổng quan
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/overview                   Tổng quan
@apiGroup Popup report
@apiDescription Tổng quan
@apiVersion 1.0.0
@apiName InformationWidget2

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup 

@apiParam (Query:)      {String}      since                    Thời gian bắt đầu của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian
@apiParam (Query:)      {String}      until                    Thời gian kết thúc của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Tổng quan

@apiSuccess {String}            data.view                    Số lượt xem của popup 
@apiSuccess {String}            data.click                   Số lượt click trên popup

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "click": 50,
        "view": 50
    }
}
"""
# ----------------------- Get information of widget 3-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/widget-3                   Lượt đóng popup
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/popup-closes                   Lượt đóng popup
@apiGroup Popup report
@apiDescription Lượt đóng popup
@apiVersion 1.0.0
@apiName InformationWidget3

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup 

@apiParam (Query:)      {String}      since                    Thời gian bắt đầu của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian
@apiParam (Query:)      {String}      until                    Thời gian kết thúc của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {List}              data                          Lượt đóng popup
@apiSuccess {Dict}              comparison_between_2_time_periods   Thông tin tổng hợp giữa 2 khoảng thời gian

@apiSuccess {String}            data.day                     Định danh cho ngày 
@apiSuccess {String}            data.close                   Số lượt bấm button close trên popup
@apiSuccess {String}            comparison_between_2_time_periods.summary_before          Tổng lượt close trong khoảng thời gian hiện tại
@apiSuccess {String}            comparison_between_2_time_periods.summary_present         Tổng lượt close trong khoảng thời gian trước đó

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "close": 15,
            "day": "2022-03-20"
        },
        {
            "close": 5,
            "day": "2022-03-19"
        },
        {
            "close": 5,
            "day": "2022-03-18"
        },
        {
            "close": 5,
            "day": "2022-03-17"
        }
    ],
    "comparison_between_2_time_periods": {
        "summary_before": 0,
        "summary_present": 0
    }
}
"""
# ----------------------- Get information of widget 4-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/widget-4                   Hiệu quả tiếp cận
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/effective-approach                   Hiệu quả tiếp cận
@apiGroup Popup report
@apiDescription Hiệu quả tiếp cận
@apiVersion 1.0.0
@apiName InformationWidget4

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup 

@apiParam (Query:)      {String}      since                    Thời gian bắt đầu của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian
@apiParam (Query:)      {String}      until                    Thời gian kết thúc của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {List}              data                          Hiệu quả tiếp cận

@apiSuccess {String}            data.day                     Định danh cho ngày 
@apiSuccess {String}            data.click                   Số lượt bấm button trên popup
@apiSuccess {String}            data.view                    Số lượt view trên popup

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "click": 20,
            "day": "2022-03-20",
            "view": 20
        },
        {
            "click": 10,
            "day": "2022-03-19",
            "view": 10
        },
        {
            "click": 10,
            "day": "2022-03-18",
            "view": 10
        },
        {
            "click": 10,
            "day": "2022-03-17",
            "view": 10
        }
    ]
}
"""
# ----------------------- Get information of widget 5-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/widget-5                   Hiệu quả tương tác
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/effective-interaction                   Hiệu quả tương tác
@apiGroup Popup report
@apiDescription Hiệu quả tương tác
@apiVersion 1.0.0
@apiName InformationWidget5

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup 

@apiParam (Query:)   {string}         channel                  Định danh cho kênh, nhận giá trị popup <code>web_push, push_in_app</code>. Không truyền vào nếu muốn lấy tât cả
@apiParam (Query:)      {String}      since                    Thời gian bắt đầu của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian
@apiParam (Query:)      {String}      until                    Thời gian kết thúc của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {List}              data                          Hiệu quả tương tác
@apiSuccess {Object}            data_config_button            Thông tin các button của popup

@apiSuccess {String}            data.channel                   Định danh cho kênh, nhận giá trị popup <code>web_push, push_in_app</code>. Không truyền vào nếu muốn lấy tât cả
@apiSuccess {String}            data.click                     Số lượt bấm button trên popup
@apiSuccess {String}            data.name_button_click         Tên button click 

@apiSuccess {String}            data_config_button.available             Button còn tồn tại hay không, <code>0:Đã-xóa, 1:Đang-tồn-tại</code>
@apiSuccess {String}            data_config_button.button_key            Key của field trong form
@apiSuccess {String}            data_config_button.button_name           Tên của field trong form

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "channel": "web_push",
            "click": 10,
            "name_button_click": "kafka"
        },
        {
            "channel": "web_push",
            "click": 10,
            "name_button_click": "submit_form"
        },
        {
            "channel": "web_push",
            "click": 5,
            "name_button_click": "sub"
        }
    ],
   "data_config_button": [
        {
            "available": "0",
            "button_key": "id_button_1",
            "button_name": "Subscribe"
        },
        {
            "available": "1",
            "button_key": "submit",
            "button_name": "Submit form"
        }
    ],
}
"""
# ----------------------- Get information of widget 6-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/widget-6                   Hiệu quả áp dụng theo từng kênh
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/effectiveness-applied-to-each-channel                   Hiệu quả áp dụng theo từng kênh
@apiGroup Popup report
@apiDescription Hiệu quả áp dụng theo từng kênh
@apiVersion 1.0.0
@apiName InformationWidget6

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup 

@apiParam (Query:)      {String}      since                    Thời gian bắt đầu của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian
@apiParam (Query:)      {String}      until                    Thời gian kết thúc của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Hiệu quả áp dụng theo từng kênh

@apiSuccess {String}            data.push_in_app                   Định danh kênh push_in_app 
@apiSuccess {String}            data.push_in_app.click             Số lượt click trên kên
@apiSuccess {String}            data.push_in_app.view              Số lượt hiển thị trên kênh
@apiSuccess {String}            data.push_in_app.profile           Số profile điền form trên kênh

@apiSuccess {String}            data.web_push                   Định danh kênh web_push 
@apiSuccess {String}            data.web_push.click             Số lượt click trên kên
@apiSuccess {String}            data.web_push.view              Số lượt hiển thị trên kênh
@apiSuccess {String}            data.web_push.profile           Số profile điền form trên kênh

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "push_in_app": {
            "click": 5,
            "profile": 0,
            "view": 5
        },
        "web_push": {
            "click": 25,
            "profile": 0,
            "view": 25
        }
    },
}
"""
# ----------------------- Get information of widget 7-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/widget-7                   Lượt xem kênh Web push theo thiết bị
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/web-push-channel-views-by-device                   Lượt xem kênh Web push theo thiết bị
@apiGroup Popup report
@apiDescription Lượt xem kênh Web push theo thiết bị
@apiVersion 1.0.0
@apiName InformationWidget7

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup 

@apiParam (Query:)      {String}      since                    Thời gian bắt đầu của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian
@apiParam (Query:)      {String}      until                    Thời gian kết thúc của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lượt xem kênh Web push theo thiết bị

@apiSuccess {String}            data.device_desktop           Lượt xem web_push theo thiết bị desktop
@apiSuccess {String}            data.device_mobile            Lượt xem web_push theo thiêt bị mobile

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "device_desktop": 20,
        "device_mobile": 5
    },
}
"""
# ----------------------- Get information of widget 8-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/widget-8                   Lượt bấm button trên kênh Web push theo thiết bị
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/button-clicks-on-web-push-channel-by-device                   Lượt bấm button trên kênh Web push theo thiết bị
@apiGroup Popup report
@apiDescription Chuyển đổi kênh Web push theo thiết bị
@apiVersion 1.0.0
@apiName InformationWidget8

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup 

@apiParam (Query:)      {String}      since                    Thời gian bắt đầu của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian
@apiParam (Query:)      {String}      until                    Thời gian kết thúc của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Chuyển đổi kênh Web push theo thiết bị

@apiSuccess {String}            data.device_desktop           Lượt chuyển đổi web_push theo thiết bị desktop
@apiSuccess {String}            data.device_mobile            Lượt chuyển đổi web_push theo thiêt bị mobile

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "device_desktop": 20,
        "device_mobile": 5
    },
}
"""
# ----------------------- Get list profile submit form-----------------------
"""
@api {POST} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/profile             Get list profile submit form
@apiGroup Popup report
@apiDescription Lấy danh sách thông tin profile điền form
@apiVersion 1.0.0
@apiName GetListProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging


@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup

@apiParam (Query:)      {String}      since                    Thời gian bắt đầu của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian
@apiParam (Query:)      {String}      until                    Thời gian kết thúc của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian
@apiParam (Query:)      {String}      search                   Họ tên, email hoặc số điện thoại muốn search
@apiParam (Query:)      {String}      sort                     Key của cột cần sắp xếp
@apiParam (Query:)      {String}      order                    Tăng dần hoặc giảm dần, chỉ nhận 2 giá trị <code>asc:Tăng-dần, desc:Giảm-dần</code>

@apiParam   (BODY:)     {List}           channel                             Danh sách channel cần lấy
@apiParam   (BODY:)     {List}           master_campaign_id                  Danh sách master_campaign_id lấy
@apiParam   (BODY:)     {List}           journey_builder_id                  Danh sách journey_builder_id cần lấy

@apiParamExample    {json}      BODY:
{
    "channel": [],
    "master_campaign_id": [],
    "journey_builder_id": []
}    

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách thông tin profile điền form
@apiSuccess {Object}            data_config_field             Thông tin các field của popup

@apiSuccess {String}            data.journey_builder          Tên Journey Builder
@apiSuccess {String}            data.message                  Tên Thông điệp
@apiSuccess {String}            data.action_time              Thời gian ghi nhận profile
@apiSuccess {String}            data.more_info                Thông tin thêm của profile

@apiSuccess {String}            data_config_field.available            Field còn tồn tại hay không, <code>0:Đã-xóa, 1:Đang-tồn-tại</code>
@apiSuccess {String}            data_config_field.field_key            Key của field trong form
@apiSuccess {String}            data_config_field.field_name           Tên của field trong form

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data":  [{
            "_id": "62592ecae66e815a87567516",
            "action_time": "2022-04-15 15-37",
            "created_time": "2022-04-15 08-37",
            "id": "62592ecae66e815a87567516",
            "journey_builder": "journey_builder",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "message": "",
            "more_info": {
                "primary_email": "<EMAIL>",
                "name": "Cao Trường",
                "job": "Developer",
                "primary_phone": "09873837837"
            },
            "popup_builder_id": "6256b08759f46dabc9507cdc",
        }]
    "data_config_field": [
        {
            "available": "1",
            "field_key": "name",
            "field_name": "Họ và Tên"
        },
        {
            "available": "1",
            "field_key": "primary_email",
            "field_name": "Email"
        },
        {
            "available": "1",
            "field_key": "primary_phone",
            "field_name": "Số điện thoại"
        },
        {
            "available": "1",
            "field_key": "job",
            "field_name": "Nghề nghiệp"
        }
    ],
}
"""
# ----------------------- Get data config field-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/actions/get-data-config-field          Lấy dữ liệu của config field
@apiGroup Popup report
@apiDescription Lấy thông tin của config field
@apiVersion 1.0.0
@apiName GetDataConfigField

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {List}            data_config_field             Thông tin các field của popup

@apiSuccess {String}            data_config_field.available            Field còn tồn tại hay không, <code>0:Đã-xóa, 1:Đang-tồn-tại</code>
@apiSuccess {String}            data_config_field.field_key            Key của field trong form
@apiSuccess {String}            data_config_field.field_name           Tên của field trong form

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data_config_field": [
        {
            "available": "1",
            "field_key": "name",
            "field_name": "Họ và Tên"
        },
        {
            "available": "1",
            "field_key": "primary_email",
            "field_name": "Email"
        },
        {
            "available": "1",
            "field_key": "primary_phone",
            "field_name": "Số điện thoại"
        },
        {
            "available": "1",
            "field_key": "job",
            "field_name": "Nghề nghiệp"
        }
    ],
}
"""
# ----------------------- Download information profile submit form-----------------------
"""
@api {POST} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/actions/download-profile      Download information profile submit form
@apiGroup Popup report
@apiDescription Download thông tin profile điền form
@apiVersion 1.0.0
@apiName DownloadInformationProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup

@apiParam (Query:)      {String}      since                    Thời gian bắt đầu của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian
@apiParam (Query:)      {String}      until                    Thời gian kết thúc của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian 
@apiParam (Query:)      {String}      search                   Họ tên, email hoặc số điện thoại muốn search
@apiParam (Query:)      {String}      sort                     Key của cột cần sắp xếp
@apiParam (Query:)      {String}      order                    Tăng dần hoặc giảm dần, chỉ nhận 2 giá trị <code>asc:Tăng-dần, desc:Giảm-dần</code>


@apiParam   (BODY:)     {List}           channel                             Danh sách channel cần lấy
@apiParam   (BODY:)     {List}           master_campaign_id                  Danh sách master_campaign_id lấy
@apiParam   (BODY:)     {List}           journey_builder_id                  Danh sách journey_builder_id cần lấy

@apiParamExample    {json}      BODY:
{
    "channel": [],
    "master_campaign_id": [],
    "journey_builder_id": []
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Download thông tin profile điền form

@apiSuccess {String}            data.url                      Link download

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data":  {
            "url": "link download excel",
        },
}
"""
# ----------------------- Download information summary-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/actions/download-summary      Download information summary
@apiGroup Popup report
@apiDescription Download thông tin báo cáo chung
@apiVersion 1.0.0
@apiName DownloadSummary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup 

@apiParam (Query:)      {String}      since                    Thời gian bắt đầu của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian
@apiParam (Query:)      {String}      until                    Thời gian kết thúc của khoảng thời gian muốn chọn, nếu không gửi tính toàn thời gian

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Download thông tin báo cáo chung

@apiSuccess {String}            data.url                      Link download

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data":  {
            "url": "link download excel",
        },
}
"""
# ----------------------- Get data for filter-----------------------
"""
@api {GET} {domain}/template/api/v1.0/popups/<popup_builder_id>/reports/actions/get-data-for-filter     Get data for filter of get list profile
@apiGroup Popup report
@apiDescription Lấy thông tin cho bộ lọc của lấy danh sách profile
@apiVersion 1.0.0
@apiName GetDataForFilter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      popup_builder_id         Định danh của popup 

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy thông tin cho bộ lọc của lấy danh sách profile

@apiSuccess {List}            data.journey_builder_using                      Danh sách journey builder sử dụng popup builder
@apiSuccess {List}            data.master_campaign_using                      Danh sách master campaign chứa journey builder

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data":  {
            "journey_builder_using": ["67967900-0127-4678-8c08-98cb0268943a"]
            "master_campaign_using": ["321bb7a1-c125-43b0-b8fe-56b75b16ab18"]
        },
}
"""
#!/usr/bin/python
# -*- coding: utf8 -*-

# ================================================ FONT TEMPLATE ================================================

"""
@apiDefine ResponseDetailFontTemplate

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của objects template
@apiSuccess {String}            data.apply_type                          Type upload template, <code>2:Mobio-upload-for-all-merchants, 3:Upload-for-a-merchant</code>
@apiSuccess {String}            data.font_type                          Là một trong các dối tượng <code>google_font, custom_font</code>
@apiSuccess {String}            data.title                               Tên mẫu objects template
@apiSuccess {String}            data.url                                url của google-font
@apiSuccess {String}            data.merchant_id                         Định danh của tennant

@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo

"""
---------------------- Add font template ----------------------
+ version: 1.0.1                                              +
+ version: 1.0.0                                              +
---------------------------------------------------------------
"""
@api {POST} {domain}/template/api/v1.0/fonts/templates                 Create font template
@apiGroup Font template
@apiDescription Tạo mới mẫu font
@apiVersion 1.0.1
@apiName AddFontTemplate
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (FORM-DATA:)     {String}                           [font_default_id]                    Định danh của default font, gửi lên nếu font_type là google_font
@apiParam   (FORM-DATA:)     {String = 2:Mobio-upload-for-all-merchants, 3:Upload-for-a-merchant}    applier_type                        Type upload template.
@apiParam   (FORM-DATA:)     {String}                           font_type                            Là một trong các dối tượng <code>google_font, custom_font</code>
@apiParam   (FORM-DATA:)     {String}                            [title]                             Tên mẫu font <code>giới hạn 36 kí tự</code>, gửi lên nếu font_type là custom_font
@apiParam   (FORM-DATA:)     {File}                             [light]                              File font light 
@apiParam   (FORM-DATA:)     {File}                             [normal]                             File font medium 
@apiParam   (FORM-DATA:)     {File}                             [bold]                               File font bold 
@apiParam   (FORM-DATA:)     {String=popup,landingpage}         [resource=popup]                     Loại đối tượng sử dụng font.


@apiParamExample    {json}      BODY google_font:
{
    "font_default_id": "font_default_id",
    "font_type": "google_font"
}  

@apiParamExample    {json}      BODY custom_font:
{
    "applier_type": "3",
    "font_type": "custom_font",
    "title": "template create",
    "light": "file font light",
    "normal": "file font medium",
    "bold": "file font bold"
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm objects template

@apiUse ResponseDetailFontTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'template_id',
        "applier_type": "3",
        "font_type": "google_font",
        "font_weight": [],
        "title": "Tên popup",
        "resource": "popup"
        "url_info": {
            "url": "url",        
            "filename": ""        
        },
        "merchant_id": "merchant_id",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""
----------------
"""
@api {POST} {domain}/template/api/v1.0/fonts/templates                 Create font template
@apiGroup Font template
@apiDescription Tạo mới mẫu font
@apiVersion 1.0.0
@apiName AddFontTemplate
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (FORM-DATA:)     {String}                           [font_default_id]                    Định danh của default font, gửi lên nếu font_type là google_font
@apiParam   (FORM-DATA:)     {String = 2:Mobio-upload-for-all-merchants,
                                    3:Upload-for-a-merchant}    applier_type                        Type upload template.
@apiParam   (FORM-DATA:)     {String}                           font_type                            Là một trong các dối tượng <code>google_font, custom_font</code>
@apiParam   (FORM-DATA:)     {String}                            [title]                              Tên mẫu font <code>giới hạn 36 kí tự</code>, gửi lên nếu font_type là custom_font
@apiParam   (FORM-DATA:)     {File}                             [light]                              File font light 
@apiParam   (FORM-DATA:)     {File}                             [normal]                             File font medium 
@apiParam   (FORM-DATA:)     {File}                             [bold]                               File font bold 


@apiParamExample    {json}      BODY google_font:
{
    "font_default_id": "font_default_id",
    "font_type": "google_font"
}  

@apiParamExample    {json}      BODY custom_font:
{
    "applier_type": "3",
    "font_type": "custom_font",
    "title": "template create",
    "light": "file font light",
    "normal": "file font medium",
    "bold": "file font bold"
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm objects template

@apiUse ResponseDetailFontTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'template_id',
        "applier_type": "3",
        "font_type": "google_font",
        "font_weight": [],
        "title": "Tên popup",
        "url_info": {
            "url": "url",        
            "filename": ""        
        },
        "merchant_id": "merchant_id",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}
"""

----------------------- Delete font template  -----------------------
+ version: 1.0.0                                                    +
---------------------------------------------------------------------
"""
@api {DELETE} {domain}/template/api/v1.0/fonts/templates/<template_id>                    Delete font template
@apiGroup Font template
@apiDescription Xóa mẫu font theo template_id
@apiVersion 1.0.0
@apiName DeleteFontTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      template_id          Định danh của mẫu font cần xóa

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa mẫu font 


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Delete list font template  -----------------------
"""
@api {DELETE} {domain}/template/api/v1.0/fonts/templates                   Delete list font template
@apiGroup Font template
@apiDescription Xóa mẫu font theo list template_id
@apiVersion 1.0.0
@apiName DeleteListFontTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {List}        ids                   Danh sách các template_id cần xóa  


@apiParamExample    {json}      BODY:
{
    "ids": []
}  
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa font template theo danh sách template_id 


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""

----------------------- Get list font template -----------------------
+ version: 1.0.1                                                     +
+ version: 1.0.0                                                     +
----------------------------------------------------------------------
"""
@api {GET} {domain}/template/api/v1.0/fonts/templates              Get list font template
@apiGroup Font template
@apiDescription Lấy danh sách mẫu font
@apiVersion 1.0.1
@apiName ListFontTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam (Query:)      {String}      applier_type         Định danh dạng gửi <code>2:Mobio-upload-for-all-merchants, 3:Upload-for-a-merchant.</code> <br>
Default value: <code>3</code><br>
Allowed values: <code>2, 3</code>
@apiParam (Query:)      {String=popup,landingpage,form_builder}      [resource=popup]                     Loại đối tượng sử dụng font.

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách mẫu font

@apiUse ResponseDetailFontTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "id": 'template_id',
        "applier_type": "3",
        "font_type": "google_font",
        "title": "Tên popup",
        "url": "link url",
        "resource": "popup",
        "merchant_id": "merchant_id",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}
"""
------------------
"""
@api {GET} {domain}/template/api/v1.0/fonts/templates              Get list font template
@apiGroup Font template
@apiDescription Lấy danh sách mẫu font
@apiVersion 1.0.0
@apiName ListFontTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam (Query:)      {String}      applier_type         Định danh dạng gửi <code>2:Mobio-upload-for-all-merchants, 3:Upload-for-a-merchant.</code> <br>
Default value: <code>3</code><br>
Allowed values: <code>2, 3</code>

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách mẫu font

@apiUse ResponseDetailFontTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "id": 'template_id',
        "applier_type": "3",
        "font_type": "google_font",
        "title": "Tên popup",
        "url": "link url",
        "merchant_id": "merchant_id",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}
"""

# ----------------------- Get detail font template  -----------------------
"""
@api {GET} {domain}/template/api/v1.0/fonts/templates/<template_id>                   Get detail font template
@apiGroup Font template
@apiDescription Lấy chi tiết mẫu font theo template_id
@apiVersion 1.0.0
@apiName GetListFontTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resource:)   {string}      template_id             Định danh của mẫu font cần xem chi tiết

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết mẫu font

@apiUse ResponseDetailFontTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": 'template_id',
        "applier_type": "3",
        "font_type": "google_font",
        "title": "Tên popup",
        "url": "link url",
        "resource": "popup",
        "merchant_id": "merchant_id",
        "created_by": "5e57f10b-087a-4fbc-be27-926c04d53fab",
        "created_time": "2021-10-03T08:10Z"
    }
}

"""
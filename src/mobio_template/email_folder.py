# ================================================ EMAIL FOLDER ================================================
"""
@apiDefine ResponseDetailEmailFolder

@apiSuccess {String}            data.id                                 <code>ID</code> Id của email folder.
@apiSuccess {String}            data.name                               Tên email folder.
@apiSuccess {String}            data.is_default                         Kiểm tra có phải thư mục base hay không                            Body của email template
@apiSuccess {String}            data.merchant_id                        Id merchant.                                                Body của email template
@apiSuccess {String}            data.lower_case_name                    Tên thường của thư mục.                           Body của email template
@apiSuccess {Int}               data.level                              Level mẫu email
@apiSuccess {String}            data.created_by                         Thông tin của người đăng nhập
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo 
@apiSuccess {String}            data.updated_by                         Thông tin của người cập nhật
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật
    
"""


********************************** List email folders ****************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {get} {HOST}/template/api/v1.0/emails/folders  List email folder
@apiDescription  API lấy danh sách các thư mục email.<br/>
Hỗ trợ tìm kiếm theo tên thư mục, lọc theo level thư mục.<br/>
Hỗ trợ sắp xếp danh sách theo thời điểm tạo, tên. Mặc định sắp xếp theo thời điểm tạo.
@apiGroup Email folder
@apiVersion 1.0.0
@apiName ListEmailFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse order_sort
@apiUse paging_tokens

@apiParam   (Query:)  {String}  [search]  Tìm kiếm theo tên thư mục email. Trả về danh sách thư mục có tên chứa từ cần tìm.
@apiSuccess {String}            data.total_of_email_template                  Số lượng mẫu email trong thư mục.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
        "_id": "6406c41ab81ae2cdc5323fb2",
        "name": "Email folder 1",
        "level": 1,
        "total_of_email_template": 0,
        "merchant_id": "6406c41ab81ae2cdc5323fb2",
        "created_by": "6406c41ab81ae2cdc5323fb2",
        "updated_by": "6406c41ab81ae2cdc5323fb2",
        "lower_case_name": "email folder 1",
        "created_time": "2022-03-17T09:56:56.000Z",
        "updated_time": "2022-03-17T09:56:56.000Z",
    },
    {
        "_id": "6406c41ab81ae2cdc5323fb3",
        "name": "Email folder 2",
        "level": 1,
        "total_of_email_template": 3,
        "merchant_id": "6406c41ab81ae2cdc5323fb2",
        "created_by": "6406c41ab81ae2cdc5323fb2",
        "updated_by": "6406c41ab81ae2cdc5323fb2",
        "lower_case_name": "email folder 1",
        "created_time": "2022-03-17T09:56:56.000Z",
        "updated_time": "2022-03-17T09:56:56.000Z",
    }
  ]
}
"""

********************************** Create email folder *****************************
* version: 1.0.0                                                                   *
************************************************************************************
"""
@api {post} {HOST}/template/api/v1.0/emails/folders  Create one email folder
@apiDescription  API tạo 1 email folder
@apiGroup Email folder
@apiVersion 1.0.0
@apiName CreateOneEmailFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của thư mục email.
@apiParamExample  {json}   Body example
{
  "name": "Email folder 1",
}

@apiUse  ResponseDetailEmailFolder
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
        "_id": "6406c41ab81ae2cdc5323fb3",
        "name": "Email folder 2",
        "level": 1,
        "merchant_id": "6406c41ab81ae2cdc5323fb2",
        "created_by": "6406c41ab81ae2cdc5323fb2",
        "updated_by": "6406c41ab81ae2cdc5323fb2",
        "lower_case_name": "email folder 1",
        "created_time": "2022-03-17T09:56:56.000Z",
        "updated_time": "2022-03-17T09:56:56.000Z",
    }
  "message": "request thành công.",
}
"""

********************************** Delete email folder *****************************
* version: 1.0.0                                                                   *
************************************************************************************
"""
@api {delete} {HOST}/template/api/v1.0/emails/folders/<email_folder_id>  Delete email folder
@apiDescription  API xoá email folder
@apiGroup Email folder
@apiVersion 1.0.0
@apiName DeleteEmailFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String} dest_email_folder_id Id của thư mục email cần chuyển các mẫu email.
@apiParam   (Body:)   {Array} email_templates_id Danh sách các mẫu email cần chuyển.
@apiParamExample  {json} Body example
{
  "dest_email_folder_id": "6406c41ab81ae2cdc5323fb2"
  "email_templates_id": [
        "6406c41ab81ae2cdc5323fb2",
        "6406c41ab81ae2cdc5323fb3",
    ],
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""


********************************** Update email folder *****************************
* version: 1.0.0                                                                   *
************************************************************************************
"""
@api {put} {HOST}/template/api/v1.0/emails/folders/<email_folder_id>  Update one email folder
@apiDescription  API cập nhật tên thư mục email
@apiGroup Email folder
@apiVersion 1.0.0
@apiName UpdateOneEmailFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của thư mục email.
@apiParamExample  {json}   Body example
{
  "name": "Email folder update name",
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""


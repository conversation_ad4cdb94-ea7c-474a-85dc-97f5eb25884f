#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 29/05/2025
"""

# ----------------------- <PERSON><PERSON><PERSON> danh sách ngành của popup template warehouse-----------------------
"""
@api {GET} {domain}/template/api/v1/popups-template-warehouse/branch            L<PERSON><PERSON> danh sách ngành của popup template
@apiGroup PopupTemplateWarehouse
@apiDescription Lấy danh sách ngành của popup template
@apiVersion 1.1.0
@apiName GetListBranchPopupTemplateWarehouse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {String}            message                             <PERSON><PERSON> tả phản hồi
@apiSuccess {Integer}           code                                M<PERSON> phản hồi
@apiSuccess {ArrayObject}       data                               Dữ liệu danh sách ngành
@apiSuccess {String}            data.branch_code                     Mã ngành
@apiSuccess {String}            data.branch_name                     Tên ngành

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "branch_code": "business_and_finance",
            "branch_name": "Kinh doanh & Tài chính"
        },
        {
            "branch_code": "insurance",
            "branch_name": "Bảo hiểm"
        },
        {
            "branch_code": "real_estate",
            "branch_name": "Bất động sản"
        },
        {
            "branch_code": "e_commerce_and_retail",
            "branch_name": "E-commerce & Bán lẻ"
        },
        {
            "branch_code": "education",
            "branch_name": "Giáo dục"
        }
    ],
    "lang": "vi",
    "message": "Request thành công."
}
"""


# ----------------------- Lấy danh sách mục đích của popup template -----------------------
"""
@api {GET} {domain}/template/api/v1/popups-template-warehouse/purpose           Lấy danh sách mục đích của popup template
@apiGroup PopupTemplateWarehouse
@apiDescription Lấy danh sách mục đích của popup template
@apiVersion 1.1.0
@apiName GetListPurposePopupTemplateWarehouse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {String}                message                             Mô tả phản hồi
@apiSuccess {Integer}               code                                Mã phản hồi
@apiSuccess {ArrayObject}            data                               Dữ liệu danh sách mục đích
@apiSuccess {String}                data.purpose_code                   Mã mục đích
@apiSuccess {String}                data.purpose_name                    Tên mục đích

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "purpose_code": "announcement",
            "purpose_name": "Thông báo"
        },
        {
            "purpose_code": "news",
            "purpose_name": "Tin tức"
        },
        {
            "purpose_code": "event_invitation",
            "purpose_name": "Thư mời tới sự kiện"
        },
        {
            "purpose_code": "advertisement",
            "purpose_name": "Quảng cáo"
        },
        {
            "purpose_code": "welcome",
            "purpose_name": "Chào mừng"
        }
    ],
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Lấy danh sách dịp lễ của popup template -----------------------
"""
@api {GET} {domain}/template/api/v1/popups-template-warehouse/holiday           Lấy danh sách dịp lễ của popup template
@apiGroup PopupTemplateWarehouse
@apiDescription Lấy danh sách dịp lễ của popup template
@apiVersion 1.1.0
@apiName GetListHolidayPopupTemplateWarehouse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {String}                message                             Mô tả phản hồi
@apiSuccess {Integer}               code                                Mã phản hồi
@apiSuccess {ArrayObject}            data                               Dữ liệu danh sách dịp lễ
@apiSuccess {String}            data.holiday_code                       Mã dịp lễ
@apiSuccess {String}            data.holiday_name                         Tên dịp lễ

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "holiday_code": "birthday",
            "holiday_name": "Sinh nhật"
        },
        {
            "holiday_code": "black_friday",
            "holiday_name": "Black Friday"
        },
        {
            "holiday_code": "new_year",
            "holiday_name": "Tết dương lịch"
        },
        {
            "holiday_code": "lunar_new_year",
            "holiday_name": "Tết âm lịch"
        },
        {
            "holiday_code": "christmas",
            "holiday_name": "Giáng sinh"
        },
        {
            "holiday_code": "valentines_day",
            "holiday_name": "Lễ tình nhân 14/2"
        },
        {
            "holiday_code": "international_womens_day",
            "holiday_name": "Quốc tế phụ nữ 8/3"
        },
        {
            "holiday_code": "labour_day",
            "holiday_name": "Quốc tế lao động 1/5"
        },
        {
            "holiday_code": "national_day",
            "holiday_name": "Quốc khánh"
        }
    ],
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Lấy danh sách loại popup của popup template -----------------------


"""
@api {GET} {domain}/template/api/v1/popups-template-warehouse/types           Lấy danh sách loại popup của popup template
@apiGroup PopupTemplateWarehouse
@apiDescription Lấy danh sách loại popup của popup template
@apiVersion 1.1.0
@apiName GetListTypesPopupTemplateWarehouse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {String}                message                             Mô tả phản hồi
@apiSuccess {Integer}               code                                Mã phản hồi
@apiSuccess {ArrayObject}           data                               Dữ liệu danh sách mục đích
@apiSuccess {String}                data.type_code                      Mã loại popup
@apiSuccess {String}                data.type_name                      Tên loại popup

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "type_code": "lightbox",
            "type_name": "Lightbox"
        },
        {
            "type_code": "fullscreen",
            "type_name": "Toàn màn hình"
        },
        {
            "type_code": "floating_bar",
            "type_name": "Floating bar"
        }
    ],
    "lang": "vi",
    "message": "Request thành công."
}
"""


# ----------------------- Lấy danh sách mẫu popup template warehouse-----------------------


"""
@api {GET} {domain}/template/api/v1/popups-template-warehouse/sample-library           Lấy danh sách mẫu popup template
@apiGroup PopupTemplateWarehouse
@apiDescription Lấy danh sách mẫu popup template warehouse
@apiVersion 1.1.0
@apiName GetListSampleLibraryPopupWarehouse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	   (Query:)			{String}	[title]         Tìm kiếm theo title popup template.     
@apiParam	   (Query:)			{String}	[is_favorite]    Lọc theo popup template yêu thích.   
@apiParam	   (Query:)			{String}	[branch_codes]    Danh sách mã ngành nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam	   (Query:)			{String}	[purpose_codes]   Danh sách mã mục đích nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam	   (Query:)			{String}	[holiday_codes]    Danh sách mã dịp lễ nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam	   (Query:)			{String}	[type_codes]    Danh sách mã loại popup nếu có nhiều thì ngăn cách bởi dấu ' , ' .
@apiParam	   (Query:)			{String}	[sort_by]       Sắp xếp theo field tuỳ chỉnh.(Default: updated_time)
@apiParam	   (Query:)			{Integer}	[order_by]      Sắp xếp theo thứ tự <code>(1:asc, -1:desc)</code>.
@apiParam	   (Query:)			{String}	[per_page]      Số lượng trên một trang.   
@apiParam	   (Query:)			{String}	[after_token]   Token để request lấy dữ liệu trang tiếp theo.
@apiParam	   (Query:)			{Integer}	[applier_type]  Lọc theo popup template của Mobio. <code>(2:Mẫu thiết kế sẵn, 3:Mẫu bố cục)</code>.


@apiSuccess {String}                message                             Mô tả phản hồi
@apiSuccess {Integer}               code                                Mã phản hồi
@apiSuccess {ArrayObject}            data                               Dữ liệu danh sách mẫu popup template
@apiSuccess {String}            data.id                                <code>ID</code> popup template 
@apiSuccess {String}            data.title                              Tên popup template
@apiSuccess {Integer}            data.second_page                       Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>

@apiSuccess {String}            data.small_thumbnail                   Link ảnh thumbnail kích thước nhỏ
@apiSuccess {String}            data.thumbnail                         Link ảnh thumbnail
@apiSuccess {Boolean}           data.is_favorite                       Đánh dấu popup template yêu thích <code>True</code>: yêu thích, <code>False</code>: không yêu thích
@apiSuccess {String}            data.branch_codes                       Danh sách mã ngành
@apiSuccess {String}            data.holiday_codes                      Danh sách mã dịp lễ
@apiSuccess {String}            data.purpose_codes                     Danh sách mã mục đích
@apiSuccess {String}            data.type_codes                        Danh sách mã loại popup
@apiSuccess {String}            data.created_by                         Người tạo
@apiSuccess {String}            data.created_time                       Thời gian tạo
@apiSuccess {String}            data.updated_by                         Người cập nhật
@apiSuccess {String}            data.updated_time                       Thời gian cập nhật
@apiSuccess {String}            data.applier_type                      Loại popup template <code>2:Mẫu thiết kế sẵn, 3:Mẫu bố cục</code>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "id": "68245cafc70c09dac5368bd6",
            "title": "test2 ok",
            "small_thumbnail": "https://...",
            "thumbnail": "https://...",
            "is_favorite": false,
            "branch_codes": ["agriculture"],
            "holiday_codes": ["new_year"],
            "purpose_codes": ["thank_you"],
            "type_codes": ["lightbox"],
            "applier_type": 2,
            "second_page": 0,
            "created_by": "admin",
            "created_time": "2025-05-13T10:00:00Z",
            "updated_by": "admin",
            "updated_time": "2025-05-14T11:00:00Z"
        },
    ],
    "lang": "vi",
    "message": "Request thành công.",
    "paging": {
        "cursors": {
            "after_token": null,
            "before_token": null
        }
    }
}
"""


# ----------------------- Lấy chi tiết mẫu popup template -----------------------


"""
@api {GET} {domain}/template/api/v1/popups-template-warehouse/sample-library/<popup_template_id>         Lấy chi tiết mẫu popup template
@apiGroup PopupTemplateWarehouse
@apiDescription Lấy chi tiết mẫu popup template
@apiVersion 1.1.0
@apiName GetDetailSampleLibraryPopupTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apiUse merchant_id_header

@apiSuccess {String}                message                             Mô tả phản hồi
@apiSuccess {Integer}               code                                Mã phản hồi
@apiSuccess {Object}            data                               Dữ liệu danh sách mẫu popup template

@apiSuccess {String}            data.id                                <code>ID</code> popup template 
@apiSuccess {String}            data.name                              Tên popup template
@apiSuccess {Object}            data.content                            Nội dung popup template
@apiSuccess {Integer}            data.second_page                       Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiSuccess {String}            data.thumbnail                         Link ảnh thumbnail
@apiSuccess {String}            data.small_thumbnail                   Link ảnh thumbnail kích thước nhỏ
@apiSuccess {Boolean}            data.is_favorite                      Đánh dấu popup template yêu thích <code>True</code>: yêu thích, <code>False</code>: không yêu thích
@apiSuccess {Array}            data.branch_codes                       Danh sách mã ngành
@apiSuccess {Array}            data.holiday_codes                      Danh sách mã dịp lễ
@apiSuccess {Array}            data.purpose_codes                     Danh sách mã mục đích
@apiSuccess {String}            data.type_codes                        Danh sách mã loại popup
@apiSuccess {Integer}            data.applier_type                      Loại popup template <code>2:Mẫu thiết kế sẵn, 3:Mẫu bố cục</code>
@apiSuccess {String}            data.created_by                         Người tạo
@apiSuccess {String}            data.created_time                       Thời gian tạo
@apiSuccess {String}            data.updated_by                         Người cập nhật
@apiSuccess {String}            data.updated_time                       Thời gian cập nhật

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
            "id": "68245cafc70c09dac5368bd6",
            "title": "test2 ok",
            "small_thumbnail": "https://...",
            "thumbnail": "https://...",
            "is_favorite": false,
            "branch_codes": ["agriculture"],
            "holiday_codes": ["new_year"],
            "purpose_codes": ["thank_you"],
            "type_codes": ["lightbox"],
            "applier_type": 2,
            "content": {
                "html": "<div>This is body popup-builder</div>",
                "json": {
                    "content": "This is body popup-builder"
                }
            },
            "second_page": 0,
            "created_by": "admin",
            "created_time": "2025-05-13T10:00:00Z",
            "updated_by": "admin",
            "updated_time": "2025-05-14T11:00:00Z"
        },
    "lang": "vi",
    "message": "Request thành công."
}
"""


# ----------------------- Tạo mới popup template warehouse-----------------------


"""
@api {POST} {domain}/template/api/v1/popups-template-warehouse/sample-library         Tạo mới mẫu popup template warehouse
@apiGroup PopupTemplateWarehouse
@apiDescription Tạo mới mẫu popup template warehouse
@apiVersion 1.1.0
@apiName CreateSampleLibraryPopupTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apiUse merchant_id_header

@apiParam   (BODY:)     {String}                            title                               Tên mẫu popup template
@apiParam   (BODY:)     {Object}                            content                             Nội dung popup template
@apiParam   (BODY:)     {Integer}                           applier_type                      Loại popup template <code>2:Mẫu thiết kế sẵn, 3:Mẫu bố cục</code>
@apiParam   (BODY:)     {Integer}                           [second_page]                         Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiParam   (BODY:)     {String}                            [thumbnail]                         Link ảnh thumbnail
@apiParam   (BODY:)     {String}                            [small_thumbnail]                   Link ảnh thumbnail kích thước nhỏ
@apiParam   (BODY:)     {String}                             [branch_codes]                      Danh sách mã ngành (Cách nhau bởi dấu ',')
@apiParam   (BODY:)     {String}                             [holiday_codes]                     Danh sách mã dịp lễ (Cách nhau bởi dấu ',')
@apiParam   (BODY:)     {String}                             [purpose_codes]                     Danh sách mã mục đích (Cách nhau bởi dấu ',')
@apiParam   (BODY:)     {String}                             [type_codes]                        Danh sách mã loại popup (Cách nhau bởi dấu ',')


@apiSuccess {String}                message                             Mô tả phản hồi
@apiSuccess {Integer}               code                                Mã phản hồi
@apiSuccess {Object}                data                               Dữ liệu danh sách mẫu popup template

@apiSuccess {String}            data.id                                <code>ID</code> popup template 
@apiSuccess {String}            data.name                              Tên popup template
@apiSuccess {Object}            data.content                            Nội dung popup template
@apiSuccess {Integer}            data.second_page                       Xác định có page cảm ơn hay không <code>0:Không, 1:Có</code>
@apiSuccess {String}            data.thumbnail                         Link ảnh thumbnail
@apiSuccess {String}            data.small_thumbnail                   Link ảnh thumbnail kích thước nhỏ
@apiSuccess {Array}            data.branch_codes                       Danh sách mã ngành
@apiSuccess {Array}            data.holiday_codes                      Danh sách mã dịp lễ
@apiSuccess {Array}            data.purpose_codes                     Danh sách mã mục đích
@apiSuccess {String}            data.type_codes                        Danh sách mã loại popup
@apiSuccess {Integer}            data.applier_type                      Loại popup template <code>2:Mẫu thiết kế sẵn, 3:Mẫu bố cục</code>
@apiSuccess {String}            data.created_by                         Người tạo
@apiSuccess {String}            data.created_time                       Thời gian tạo
@apiSuccess {String}            data.updated_by                         Người cập nhật
@apiSuccess {String}            data.updated_time                       Thời gian cập nhật

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
            "id": "68245cafc70c09dac5368bd6",
            "title": "test2 ok",
            "small_thumbnail": "https://...",
            "thumbnail": "https://...",
            "branch_codes": ["agriculture"],
            "holiday_codes": ["new_year"],
            "purpose_codes": ["thank_you"],
            "type_codes": ["lightbox"],
            "applier_type": 2,
            "content": {
                "html": "<div>This is body popup-builder</div>",
                "json": {
                    "content": "This is body popup-builder"
                }
            },
            "second_page": 0,
            "created_by": "admin",
            "created_time": "2025-05-13T10:00:00Z",
            "updated_by": "admin",
            "updated_time": "2025-05-14T11:00:00Z"
        },
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Xoá popup template warehouse ---------------------------

"""
@api {DELETE} {domain}/template/api/v1/popups-template-warehouse/sample-library/<popup_template_id>         Xoá mẫu popup template warehouse
@apiGroup PopupTemplateWarehouse
@apiDescription Delete popup template warehouse
@apiVersion 1.1.0
@apiName DeletePopupTemplateWarehouse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (PARAM:)     {String}            ids                        <code>ID</code> popup template warehouse cần xoá(Cách nhau bởi dấu ',').

@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {String}            lang                                    mã ngôn ngữ trả về.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "lang": "vi",
    "message": "Request thành công."
}
"""


# ----------------------- Cập nhật email yêu thích ---------------------------


"""
@api {PUT} {domain}/template/api/v1/popups-template-warehouse/favorite/<popup_template_id>          Cập nhật popup template warehouse yêu thích
@apiGroup PopupTemplateWarehouse
@apiDescription Update favorite popup template warehouse
@apiVersion 1.1.0
@apiName UpdateFavoritePopupTemplateWarehouse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {Boolean}     is_favorite                      Trạng thái yêu thích (True-yêu thích, False-không yêu thích)


@apiParamExample {json} Body example
{
    "popup_template_id": "682af558eab84ec8bb5f66ab",
    "is_favorite": true
}

@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "lang": "vi",
    "message": "Request thành công."
}
"""
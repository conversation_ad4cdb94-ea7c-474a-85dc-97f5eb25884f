

#========================Tạo section mẫu email template=========================================================
"""@api {POST} {domain}/template/api/v1.1/emails-builder/section          Tạo section mẫu email template
@apiGroup SectionEmailBuilder
@apiDescription Tạo section mẫu email template
@apiVersion 1.1.0
@apiName CreateSectionEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}  name   Tên section mẫu
@apiParam   (Body:)   {String}  body   Body section mẫu
@apiParam   (Body:)   {String}  thumbnail   Link ảnh thumbnail

@apiSuccess {Object}            data                               Dữ liệu section mẫu email template

@apiSuccess {String}            data.id                                Mã section email template
@apiSuccess {String}            data.name                              Tên section email template
@apiSuccess {String}            data.body                               Nội  dung section email
@apiSuccess {String}            data.thumbnail                         Link ảnh thumbnail
@apiSuccess {String}            data.created_by                         Người tạo
@apiSuccess {String}            data.created_time                       Thời gian tạo
@apiSuccess {String}            data.updated_by                         Người cập nhật
@apiSuccess {String}            data.updated_time                       Thời gian cập nhật
@apiSuccess {String}            data.merchant_id                       Merchant id

@apiParamExample {json} Response
{
    "code": 200,
    "data": {
        "body": "<html>askfljdaste</html>",
        "created_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "created_time": "2025-05-21T09:03Z",
        "id": "682d96f4fbed293e4ea062e4",
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "name": "test 43434abc",
        "thumbnail": "https://jdasfhlk",
        "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "updated_time": "2025-05-21T09:10Z"
    },
    "lang": "vi",
    "message": "Request thành công."
}
"""

#========================Cập nhật section mẫu email template=========================================================
"""@api {PUT} {domain}/template/api/v1.1/emails-builder/section/<section_id>         Cập nhật section mẫu email template
@apiGroup SectionEmailBuilder
@apiDescription Cập nhật section mẫu email template
@apiVersion 1.1.0
@apiName UpdateSectionEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}  [name]   Tên section mẫu
@apiParam   (Body:)   {String}  [body]   Body section mẫu
@apiParam   (Body:)   {String}  [thumbnail]   Link ảnh thumbnail

@apiSuccess {Object}            data                               Dữ liệu section mẫu email template

@apiSuccess {String}            data.id                                Mã section email template
@apiSuccess {String}            data.name                              Tên section email template
@apiSuccess {String}            data.body                               Nội  dung section email
@apiSuccess {String}            data.thumbnail                         Link ảnh thumbnail
@apiSuccess {String}            data.created_by                         Người tạo
@apiSuccess {String}            data.created_time                       Thời gian tạo
@apiSuccess {String}            data.updated_by                         Người cập nhật
@apiSuccess {String}            data.updated_time                       Thời gian cập nhật
@apiSuccess {String}            data.merchant_id                       Merchant id

@apiParamExample {json} Response
{
    "code": 200,
    "data": {
        "body": "<html>askfljdaste</html>",
        "created_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "created_time": "2025-05-21T09:03Z",
        "id": "682d96f4fbed293e4ea062e4",
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "name": "test 43434abc",
        "thumbnail": "https://jdasfhlk",
        "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "updated_time": "2025-05-21T09:10Z"
    },
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Lấy danh sách section mẫu email template -----------------------
"""
@api {GET} {domain}/template/api/v1.1/emails-builder/section        Lấy danh sách section mẫu email template
@apiGroup SectionEmailBuilder
@apiDescription Lấy danh sách section mẫu email template
@apiVersion 1.1.0
@apiName GetListSectionEmail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	   (Query:)			{string}	[search]    Tìm kiếm theo tên section email.     
@apiParam	   (Query:)			{string}	[sort_by]       Sắp xếp theo field tuỳ chỉnh.(Default: updated_time)
@apiParam	   (Query:)			{integer}	[order_by]      Sắp xếp theo thứ tự <code>(1:asc, -1:desc)</code>.
@apiParam	   (Query:)			{string}	[per_page]      Số lượng trên một trang. Default: 20 
@apiParam	   (Query:)			{string}	[after_token]     Token để request lấy dữ liệu trang tiếp theo.        

@apiSuccess {ArrayObject}            data                               Dữ liệu danh sách mẫu email template

@apiSuccess {String}            data.id                                Mã email template
@apiSuccess {String}            data.name                              Tên email template
@apiSuccess {String}            data.body                               Nội dung email
@apiSuccess {String}            data.thumbnail                         Link ảnh thumbnail
@apiSuccess {String}            data.created_by                         Người tạo
@apiSuccess {String}            data.created_time                       Thời gian tạo
@apiSuccess {String}            data.updated_by                         Người cập nhật
@apiSuccess {String}            data.updated_time                       Thời gian cập nhật
@apiSuccess {String}            data.merchant_id                       Merchant id

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "body": "<html>askfljdaste</html>",
            "created_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "created_time": "2025-05-21T09:03Z",
            "id": "682d96f4fbed293e4ea062e4",
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "name": "test 43434abc",
            "thumbnail": "https://jdasfhlk",
            "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "updated_time": "2025-05-21T09:10Z"
        }
    ],
    "lang": "vi",
    "message": "Request thành công.",
    "paging": {
        "cursors": {
            "after_token": null,
            "before_token": null
        }
    }
}
"""

#========================Lấy chi tiết section mẫu email template=========================================================
"""@api {GET} {domain}/template/api/v1.1/emails-builder/section/<section_id>         Lấy chi tiết section mẫu email template
@apiGroup SectionEmailBuilder
@apiDescription Lấy chi tiết section mẫu email template
@apiVersion 1.1.0
@apiName GetDetailSectionEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess {Object}            data                               Dữ liệu section mẫu email template

@apiSuccess {String}            data.id                                Mã section email template
@apiSuccess {String}            data.name                              Tên section email template
@apiSuccess {String}            data.body                               Nội  dung section email
@apiSuccess {String}            data.thumbnail                         Link ảnh thumbnail
@apiSuccess {String}            data.created_by                         Người tạo
@apiSuccess {String}            data.created_time                       Thời gian tạo
@apiSuccess {String}            data.updated_by                         Người cập nhật
@apiSuccess {String}            data.updated_time                       Thời gian cập nhật
@apiSuccess {String}            data.merchant_id                       Merchant id


@apiParamExample {json} Response
{
    "code": 200,
    "data": {
        "body": "<html>askfljdaste</html>",
        "created_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "created_time": "2025-05-21T09:03Z",
        "id": "682d96f4fbed293e4ea062e4",
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "name": "test 43434abc",
        "thumbnail": "https://jdasfhlk",
        "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "updated_time": "2025-05-21T09:10Z"
    },
    "lang": "vi",
    "message": "Request thành công."
}
"""

#========================Xóa section mẫu email template=========================================================
"""@api {DELETE} {domain}/template/api/v1.1/emails-builder/section/<section_id>        Xóa section mẫu email template
@apiGroup SectionEmailBuilder
@apiDescription Xóa section mẫu email template
@apiVersion 1.1.0
@apiName DeleteSectionEmailTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParamExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "message": "Request thành công."
}
"""
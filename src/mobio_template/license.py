#!/usr/bin/python
# -*- coding: utf8 -*-
********************************** License *********************************
* version: 1.0.0                                                                 *
**********************************************************************************
"""
@api {POST} {HOST}/template/api/v1.0/license/check-feature   Check license feature của merchant
@apiGroup License
@apiDescription Check license feature của merchant với các tính năng FE không cần API BE và cột type là <code>feature</code> ở trong link define <br/>
Nếu <code>code == 200</code> <PERSON><PERSON><PERSON>c thao tác feature với key, module truy<PERSON>n lên<br/>
Nếu <code>code != 200</code> Không được phép thao tác<br/>
<a href="https://docs.google.com/spreadsheets/d/1CToYsKH31ELMG4QkZejeJKujOzigjOUxjanQRiabCUw/edit#gid=**********">Link define key, module</a>
@apiVersion 1.0.0
@apiName CheckLicenseFeature

@apiUse 401
@apiUse 402
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse lang

@apiParam   (BODY:)    {String}  key        Key cần check
@apiParam   (BODY:)    {String}  module     Module của key cần check
@apiParamExample {json} Body example
{
  "key": "cdp",
  "module": "landing_page_font"
}

@apiSuccess {Integer}           code                          200
@apiSuccess {String}            message                       request thành công.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
}

"""
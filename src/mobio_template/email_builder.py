"""
    Author: LocDX
    Company: Mobio
    Date Created: 15/05/2025
"""

"""
@apiDefine paging_tokens
@apiParam   (Query:)    {Number}    [per_page] Số phần tử trên một page.<br/> Example: <code>&per_page=5</code>
@apiParam   (Query:)    {String}    [after_token] Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)    {String}    [before_token] Token để request lấy dữ liệu trang trước đó. Nếu trong danh sách tham số gửi lên có <code>after_token</code> thì ưu tiên xử lý <code>after_token</code>

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after_token]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before_token]    Token để lấy dữ liệu trang trước đó.
@apiSuccess     {Object}    [paging..per_page]          Số lượng phần tử trên 1 page
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after_token": "YXNkaGZha2RoZmFrZGZh",
            "before_token": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 15
    }
}
"""
# ----------------------- Tạo mới email template -----------------------

"""
@api {POST} {domain}/template/api/v1.1/emails-builder           Tạo mới email builder
@apiGroup EmailBuilder
@apiDescription Tạo mới email builder
@apiVersion 1.1.0
@apiName CreateEmailBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {Array}       [categories]                   Danh sách id category
@apiParam   (Body:)   {Integer}     status                       Trạng thái phát hành email-builder<li><code>1</code>: Công khai</li><li><code>0</code>: Chỉ mình tôi</li>
@apiParam   (Body:)   {String}      name                         Tên email-builder
@apiParam   (Body:)   {Object}     body                         Body email-builder
@apiParam   (Body:)   {String}      [description]                Mô tả email-builder
@apiParam   (Body:)   {String}      thumbnail                    Link thumbnail của email-builder
@apiParam   (Body:)   {String}      small_thumbnail              Link ảnh thumbnail kích thước nhỏ


@apiParamExample {json} Body example
{
  "name": "email_builder 1",
  "body": {
    "html": "<div>This is body email-builder</div>",
    "json": {
      "content": "This is body email-builder"
    }
  },
  "description": "description email-builder",
  "thumbnail": "https://...",
  "small_thumbnail": "https://...",
  "status": 1,
  "categories": ["682443491ffa55f5f0621f10"]
}

@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {Object}            data                                    Dữ liệu email builder.
@apiSuccess {String}            data.id                                 <code>ID</code> của email builder.
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin người cập nhật.
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật.
@apiSuccess {String}            data.merchant_id                        Thông tin của merchant_id.
@apiSuccess {String}            data.name                               Tên email builder.
@apiSuccess {Object}            data.body                               Body email builder.
@apiSuccess {String}            data.description                        Mô tả email builder.
@apiSuccess {String}            data.thumbnail                          Link thumbnail của email builder.
@apiSuccess {String}            data.small_thumbnail                    Link ảnh thumbnail kích thước nhỏ
@apiSuccess {Integer}           data.status                             Trạng thái phát hành email-builder<li><code>1</code>: Công khai</li><li><code>0</code>: Chỉ mình tôi</li>
@apiSuccess {Array}             data.categories                         Danh sách id category.
@apiSuccess {String}            data.session                            Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không



@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "created_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "created_time": "2025-05-14T07:17:45Z",
        "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "updated_time": "2025-05-14T07:17:45Z"
        "id": "682443991ffa55f5f0621f1d",
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "name": "email_builder 1",
        "body": {
            "html": "<div>This is body email-builder</div>",
            "json": {
                "content": "This is body email-builder"
            }
        },
        "description": "description email-builder",
        "thumbnail": "https://...",
        "small_thumbnail": "https://...",
        "status": 1,
        "categories": ["682443491ffa55f5f0621f10"],
        "session": "123123"
    },
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Cập nhật email builder ---------------------------

"""
@api {PATCH} {domain}/template/api/v1.1/emails-builder/<email_builder_id>           Update email builder
@apiGroup EmailBuilder
@apiDescription Update email builder
@apiVersion 1.1.0
@apiName UpdateEmailBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {Array}       [categories]                   Danh sách id category
@apiParam   (Body:)   {Integer}     [status]                       Trạng thái phát hành email-builder<li><code>1</code>: Công khai</li><li><code>0</code>: Chỉ mình tôi</li>
@apiParam   (Body:)   {String}      [name]                         Tên email-builder
@apiParam   (Body:)   {String}     [body]                         Body email-builder
@apiParam   (Body:)   {String}      [description]                  Mô tả email-builder
@apiParam   (Body:)   {String}      [thumbnail]                    Link thumbnail của email-builder
@apiParam   (Body:)   {String}      [small_thumbnail]              Link ảnh thumbnail kích thước nhỏ
@apiParam   (Body:)   {String}      session                        Session được trả về từ backend để kiểm tra phiên bản có phải mới nhất không 

@apiParamExample {json} Body example
{
  "name": "email_builder 2",
  "body": {
    "html": "<div>This is body email-builder</div>",
    "json": {
      "content": "This is body email-builder"
    }
  },
  "description": "description email-builder",
  "thumbnail": "https://...",
  "small_thumbnail": "https://...",
  "status": 0,
  "categories": ["682443491ffa55f5f0621f10"],
  "session": "123123"
}

@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {Object}            data                                    Dữ liệu email builder.
@apiSuccess {String}            data.id                                 <code>ID</code> của email builder.
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin người cập nhật.
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật.
@apiSuccess {String}            data.merchant_id                        Thông tin của merchant_id.
@apiSuccess {String}            data.name                               Tên email builder.
@apiSuccess {String}            data.description                        Mô tả email builder.
@apiSuccess {String}            data.thumbnail                          Link thumbnail của email builder.
@apiSuccess {String}            data.small_thumbnail                    Link ảnh thumbnail kích thước nhỏ
@apiSuccess {Integer}           data.status                             Trạng thái phát hành email-builder<li><code>1</code>: Công khai</li><li><code>0</code>: Chỉ mình tôi</li>
@apiSuccess {Array}             data.categories                         Danh sách id category.
@apiSuccess {String}            data.session                            Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "created_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "created_time": "2025-05-14T07:17:45Z",
        "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
        "updated_time": "2025-05-14T07:17:45Z"
        "id": "682443991ffa55f5f0621f1d",
        "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
        "name": "email_builder 2",
        "description": "description email-builder",
        "thumbnail": "https://...",
        "small_thumbnail": "https://...",
        "status": 0,
        "categories": ["682443491ffa55f5f0621f10"],
        "session": "123123"
    },
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Danh sách email builder ---------------------------

"""
@api {GET} {domain}/template/api/v1.1/emails-builder          Danh sách email builder
@apiGroup EmailBuilder
@apiDescription Danh sách email builder
@apiVersion 1.1.0
@apiName GetListEmailBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging_tokens


@apiParam   (Query:)   {String}  [search]           Tên email builder cần tìm kiếm
@apiParam   (Query:)   {String}  [categories]       Id của category email builder cần lọc (cách nhau bởi dấu ,)
@apiParam   (Query:)   {String}  [status]           Trạng thái email builder<li><code>1</code>: Công khai</li><li><code>0</code>: Chỉ mình tôi</li>
@apiParam   (Query:)   {String}  [is_favorite]      Filter mẫu email builder yêu thích (True-yêu thích, False-không yêu thích) mặc định là False


@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {ArrayObject}       data                                    Dữ liệu danh sách email builder.
@apiSuccess {String}            data.id                                 <code>ID</code> của email builder.
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin người cập nhật.
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật.
@apiSuccess {String}            data.merchant_id                        Thông tin của merchant_id.
@apiSuccess {String}            data.name                               Tên email builder.
@apiSuccess {String}            data.description                        Mô tả email builder.
@apiSuccess {String}            data.thumbnail                          Link thumbnail của email builder.
@apiSuccess {String}            data.small_thumbnail                    Link ảnh thumbnail kích thước nhỏ
@apiSuccess {Boolean}           data.is_favorite                        Đánh dấu yêu thích của email builder(True-yêu thích, False-không yêu thích).
@apiSuccess {Integer}           data.status                             Trạng thái phát hành email-builder<li><code>1</code>: Công khai</li><li><code>0</code>: Chỉ mình tôi</li>
@apiSuccess {Array}             data.categories                         Danh sách id category.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "created_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "created_time": "2025-05-14T07:17:45Z",
            "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "updated_time": "2025-05-14T07:17:45Z"
            "id": "682443991ffa55f5f0621f1d",
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "name": "email_builder 2",
            "description": "description email-builder",
            "thumbnail": "https://...",
            "small_thumbnail": "https://...",
            "status": 0,
            "is_favorite": false,
            "categories": ["682443491ffa55f5f0621f10"]
        }
    ],
    "paging": {
        "cursors": {
            "after_token": "YXNkaGZha2RoZmFrZGZh",
            "before_token": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 15
    }
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Xoá email builder ---------------------------

"""
@api {DELETE} {domain}/template/api/v1.1/emails-builder         Xoá email builder
@apiGroup EmailBuilder
@apiDescription Delete email builder
@apiVersion 1.1.0
@apiName DeleteEmailBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (PARAM:)     {String}            ids                        Danh sách ID email builder cần xoá(cách nhau bởi dấu ,).

@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {String}            lang                                    mã ngôn ngữ trả về.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Get detail email builder ---------------------------

"""
@api {GET} {domain}/template/api/v1.1/emails-builder/<email_builder_id>          Chi tiết email builder
@apiGroup EmailBuilder
@apiDescription Chi tiết email builder
@apiVersion 1.1.0
@apiName GetDetailEmailBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header



@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {Array}            data                                     Dữ liệu danh sách email builder.
@apiSuccess {String}            data.id                                 <code>ID</code> của email builder.
@apiSuccess {String}            data.created_by                         Thông tin của người tạo.
@apiSuccess {String}            data.created_time                       Thời điểm khởi tạo.
@apiSuccess {String}            data.updated_by                         Thông tin người cập nhật.
@apiSuccess {String}            data.updated_time                       Thời điểm cập nhật.
@apiSuccess {String}            data.merchant_id                        Thông tin của merchant_id.
@apiSuccess {String}            data.name                               Tên email builder.
@apiSuccess {String}            data.body                               Body email builder.
@apiSuccess {String}            data.description                        Mô tả email builder.
@apiSuccess {String}            data.thumbnail                          Link thumbnail của email builder.
@apiSuccess {String}            data.small_thumbnail                    Link ảnh thumbnail kích thước nhỏ
@apiSuccess {Boolean}           data.is_favorite                        Đánh dấu yêu thích của email builder(True-yêu thích, False-không yêu thích).
@apiSuccess {Integer}           data.status                             Trạng thái phát hành email-builder<li><code>1</code>: Công khai</li><li><code>0</code>: Chỉ mình tôi</li>
@apiSuccess {Array}             data.categories                         Danh sách id category.
@apiSuccess {String}            data.session                            Session được sinh tự động, sử dụng để kiểm tra phiên bản có phải mới nhất không


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": 
        {
            "created_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "created_time": "2025-05-14T07:17:45Z",
            "updated_by": "65eebbe6-24c3-416a-bbe4-0aebe92f6695",
            "updated_time": "2025-05-14T07:17:45Z"
            "id": "682443991ffa55f5f0621f1d",
            "merchant_id": "57d559c1-39a1-4cee-b024-b953428b5ac8",
            "name": "email_builder 2",
            "body": {
                "html": "<div>This is body email-builder</div>",
                "json": {
                    "content": "This is body email-builder"
                }
            },
            "description": "description email-builder",
            "thumbnail": "https://...",
            "small_thumbnail": "https://...",
            "status": 0,
            "is_favorite": false,
            "categories": ["682443491ffa55f5f0621f10"],
            "session": "123123"
        }
    "lang": "vi",
    "message": "Request thành công."
}
"""


# ----------------------- Count email builder ---------------------------

"""
@api {GET} {domain}/template/api/v1.1/emails-builder/action/count         Count email builder
@apiGroup EmailBuilder
@apiDescription Count email builder
@apiVersion 1.1.0
@apiName CountEmailBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header



@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {Array}             data                                     Dữ liệu count email builder.
@apiSuccess {String}            data.type                               Loại count của email builder(
                                                                        public- count email builder có trạng thái công khai, 
                                                                        private - count email builder có trạng thái chỉ mình tôi
                                                                        email_custom - count email builder do người dùng tạo).
@apiSuccess {String}            data.count                              Tổng số lượng mẫu email

@apiParam   (Query:)   {String}  [search]           Tên email builder cần tìm kiếm
@apiParam   (Query:)   {String}  [category_ids]      Id của category email builder(cách nhau bởi dấu ,)
@apiParam   (Query:)   {String}  [is_favorite]      Filter mẫu email builder yêu thích (True-yêu thích, False-không yêu thích) mặc định là False


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "type": "public",
            "count": 1
        },
        {
            "type": "private",
            "count": 1
        },
        {
            "type": "email_custom",
            "count": 1
        }
    ],
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Upload thumbnail email builder ---------------------------

"""
@api {GET} {domain}/template/api/v1.1/emails-builder/actions/upload-image        Upload thumbnail email builder
@apiGroup EmailBuilder
@apiDescription Upload thumbnail email builder
@apiVersion 1.1.0
@apiName UploadThumbnailEmailBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Form:)   {File}      file                        File thumbnail email builder


@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {Array}             data                                    Dữ liệu count email builder.
@apiSuccess {String}            data.capacity                           Dung lượng file
@apiSuccess {String}            data.format                             Định dạng của file
@apiSuccess {String}            data.url                                Link file
@apiSuccess {String}            data.local_path                         Đường dẫn vật lý của file
@apiSuccess {String}            data.filename                           Tên file

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "capacity": "1MB",
        "format": "image/jpeg",
        "url": "https://...",
        "local_path": "/media/data/public_resources/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1222_1644459930.jpg",
        "filename": "1222_1644459930.jpg"
    },
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Gửi thử nghiệm email builder ---------------------------

"""
@api {POST} {domain}/template/api/v1.1/emails-builder/actions/send-test-email        Send test email builder
@apiGroup EmailBuilder
@apiDescription Send test email builder
@apiVersion 1.1.0
@apiName SendTestEmailBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}      from                         Email gửi 
@apiParam   (Body:)   {String}      from_name                    Tên người gửi                          
@apiParam   (Body:)   {String}      title                        Tiêu đề email
@apiParam   (Body:)   {String}      body                         Body của email
@apiParam   (Body:)   {Array}      send_to                       List email nhận email

@apiParamExample {json} Body example
{
  "from": "<EMAIL>",
  "body": "<div>This is body email-builder</div>",
  "from_name": "name1",
  "title": "send_email_test",
  "send_to": ["<EMAIL>", "<EMAIL>"]
}


@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {Array}             vi                                      Mã ngôn ngữ

@apiSuccessExample {json} Response 
{
    "code": 200,
    "lang": "vi",
    "message": "Request thành công."
}
"""


# ----------------------- Count email builder by category-----------------------

"""
@api {POST} {domain}/template/api/v1.1/emails-builder/actions/count/categories  Count category
@apiDescription  API lấy count số lượng email builder theo category trên hệ thống.
@apiGroup EmailBuilder
@apiVersion 1.0.0
@apiName CountCategoryEmailBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)  {String}        category_ids                    Danh sách <code>id</code> định danh category(cách nhau bởi dấu ,)


@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {Object}            data                                    data count email builder.
@apiSuccess                 {String}        data.id              <code>ID</code> của category.
@apiSuccess                 {Integer}        data.count         Số lượng site trong danh mục.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "6406c41ab81ae2cdc53avxfb1",
      "count": 10
    },
    {
      "id": "default",
      "count": 40
    }
  ]
}
"""

# ----------------------- Cập nhật email yêu thích ---------------------------

"""
@api {PUT} {domain}/template/api/v1.1/emails-builder/favorite          Cập nhật email yêu thích
@apiGroup EmailBuilder
@apiDescription Update favoriteemail builder
@apiVersion 1.1.0
@apiName UpdateFavoriteEmailBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}      email_id                         Id email builder   
@apiParam   (Body:)   {Boolean}     is_favorite                      Trạng thái yêu thích (True-yêu thích, False-không yêu thích)


@apiParamExample {json} Body example
{
    "email_id": "682af558eab84ec8bb5f66ab",
    "is_favorite": true
}

@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "lang": "vi",
    "message": "Request thành công."
}
"""

# ----------------------- Count all email builder-----------------------

"""
@api {GET} {domain}/template/api/v1.1/emails-builder/actions/count-all  Count all email builder
@apiDescription  API lấy count tất cả số lượng email builder theo merchant.
@apiGroup EmailBuilder
@apiVersion 1.0.0
@apiName CountAllCategoryEmailBuilder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header


@apiSuccess {String}            message                                 Mô tả phản hồi.
@apiSuccess {Integer}           code                                    Mã phản hồi.
@apiSuccess {Object}           data                                    Count email builder.
@apiSuccess {Integer}           data.count                               Tổng số lượng email builder.


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": 
    {
      "count": 10
    }
}
"""
#!/usr/bin/python
# -*- coding: utf8 -*-
********************************** Clean Color in Library *********************************
* version: 1.0.0                                                                          *
*******************************************************************************************
"""
@api {post} {HOST}/template/api/v1.0/libraries/<library_id>/colors/actions/clean  Clean color
@apiDescription  API xóa bộ màu trong thư viện.
@apiGroup Library
@apiVersion 1.0.0
@apiName LibraryCleanColor

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "library": {
    "id": "6406c41ab81ae2cdc5323321",
    "name": "Thư viện 1",
    "thumbnail": "",
    "merchant_id": "f91737b2-e1ab-4447-a5c1-a3047bfc0f71"
    "created_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "created_time": 1324567892,
    "updated_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "updated_time": 5467891223,
    "colors": {},
    "fonts": [],
    "assets": {
      "data": [],
      "paging": {
        "cursors": {
          "after": "",
          "before": ""
        }
      }
    }
  }
}

"""

********************************** Clean fonts in Library *********************************
* version: 1.0.0                                                                          *
*******************************************************************************************
"""
@api {post} {HOST}/template/api/v1.0/libraries/<library_id>/fonts/actions/clean  Clean font
@apiDescription API xóa font chữ trong thư viện.
@apiGroup Library
@apiVersion 1.0.0
@apiName LibraryCleanFont

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "library": {
    "id": "6406c41ab81ae2cdc5323321",
    "name": "Thư viện 1",
    "thumbnail": "",
    "merchant_id": "f91737b2-e1ab-4447-a5c1-a3047bfc0f71"
    "created_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "created_time": 1324567892,
    "updated_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "updated_time": 5467891223,
    "colors": {},
    "fonts": [],
    "assets": {
      "data": [],
      "paging": {
        "cursors": {
          "after": "",
          "before": ""
        }
      }
    }
  }
}

"""

********************************** Clean Asset in Library *********************************
* version: 1.0.0                                                                          *
*******************************************************************************************
"""
@api {post} {HOST}/template/api/v1.0/libraries/<library_id>/assets/actions/clean  Clean asset
@apiDescription API remove tất cả asset đã được add vào thư viện.
@apiGroup Library
@apiVersion 1.0.0
@apiName LibraryCleanAsset

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "library": {
    "id": "6406c41ab81ae2cdc5323321",
    "name": "Thư viện 1",
    "thumbnail": "",
    "merchant_id": "f91737b2-e1ab-4447-a5c1-a3047bfc0f71"
    "created_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "created_time": 1324567892,
    "updated_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "updated_time": 5467891223,
    "colors": {},
    "fonts": [],
    "assets": {
      "data": [],
      "paging": {
        "cursors": {
          "after": "",
          "before": ""
        }
      }
    }
  }
}

"""

********************************** Detail Library *********************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {get} {HOST}/template/api/v1.0/libraries/<library_id>  Lấy chi tiết thư viện
@apiDescription API lấy chi tiết dữ liệu của một thư viện (font-style, text-style, assets,...).
@apiGroup Library
@apiVersion 1.0.0
@apiName DetailLibrary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "library": {
    "id": "6406c41ab81ae2cdc5323321",
    "name": "Thư viện 1",
    "thumbnail": "",
    "merchant_id": "f91737b2-e1ab-4447-a5c1-a3047bfc0f71"
    "created_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "created_time": 1324567892,
    "updated_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "updated_time": 5467891223,
    "colors": {},
    "fonts": [],
    "assets": {
      "data": [],
      "paging": {
        "cursors": {
          "after": "YXNkaGZha2RoZmFrZGZh",
          "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        }
      }
    }
  }
}

"""

********************************** List Library *********************************
* version: 1.0.1                                                                *
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {get} {HOST}/template/api/v1.0/libraries  Lấy danh sách thư viện
@apiDescription API lấy danh sách các thư viện.
@apiGroup Library
@apiVersion 1.0.1
@apiName ListLibrary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse paging_tokens
@apiUse order_sort

@apiParam   (Query:)  {String}  [search]  Tìm kiếm theo tên thư viện. Trả về danh sách thư viện có tên chứa từ cần tìm.
@apiParam   (Query:)  {String}  [fields]  Truyền lên field cần lấy thêm, nhiều field các nhau bằng dấu phẩy (,). Các field hỗ trợ <code>fonts,colors</code>

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "libraries": [
    {
        "id": "6406c41ab81ae2cdc5323321",
        "name": "Thư viện 1",
        "thumbnail": "",
        "merchant_id": "f91737b2-e1ab-4447-a5c1-a3047bfc0f71"
        "created_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
        "created_time": 1324567892,
        "updated_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
        "updated_time": 5467891223,
        "colors": [ 
        {
            "colors" : [ 
                {
                    "hex" : "#ffffff",
                    "id" : "--c11",
                    "isDark" : false,
                    "rgb" : "255, 255, 255"
                }, 
                {
                    "hex" : "#cccccc",
                    "id" : "--c12",
                    "isDark" : false,
                    "rgb" : "204, 204, 204"
                }, 
                {
                    "hex" : "#999999",
                    "id" : "--c13",
                    "isDark" : false,
                    "rgb" : "153, 153, 153"
                }, 
                {
                    "hex" : "#666666",
                    "id" : "--c14",
                    "isDark" : true,
                    "rgb" : "102, 102, 102"
                }, 
                {
                    "hex" : "#333333",
                    "id" : "--c15",
                    "isDark" : true,
                    "rgb" : "51, 51, 51"
                }
            ],
            "hexColor" : "#ffffff",
            "id" : "c1",
            "title" : "Màu nền và văn bản"
        }, 
        ...
        ]
    ],
        "fonts": [
            {
                "id" : "--font-1",
                "name" : "Tiêu đề 1",
                "desc" : "Tiêu đề 1",
                "tag" : "h1",
                "order" : "1",
                "class" : "font-1",
                "custom" : "0",
                "style" : {
                    "font-family" : "Roboto, sans-serif",
                    "font-size" : "60px",
                    "font-style" : "normal",
                    "font-weight" : "700",
                    "line-height" : "150%",
                    "color" : "rgba(var(--c15), 1)"
                }
            }, 
            ...
        ]
    },
    ...
  ]
}
"""
*****************
"""
@api {get} {HOST}/template/api/v1.0/libraries  Lấy danh sách thư viện
@apiDescription API lấy danh sách các thư viện.
@apiGroup Library
@apiVersion 1.0.0
@apiName ListLibrary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse paging_tokens
@apiUse order_sort

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "libraries": [
    {
      "id": "6406c41ab81ae2cdc5323321",
      "name": "Thư viện 1",
      "thumbnail": "",
      "merchant_id": "f91737b2-e1ab-4447-a5c1-a3047bfc0f71"
      "created_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
      "created_time": 1324567892,
      "updated_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
      "updated_time": 5467891223,
    },
    {
      "id": "6406c41ab81ae2cdc532abs1",
      "name": "Thư viện 2",
      "thumbnail": "",
      "merchant_id": "f91737b2-e1ab-4447-a5c1-a3047bfc0f71"
      "created_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
      "created_time": 1324567892,
      "updated_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
      "updated_time": 5467891223,
    }
  ]
}
"""

********************************** Delete Library *********************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {delete} {HOST}/template/api/v1.0/libraries/<library_id> Xóa thư viện
@apiDescription API dùng để xóa thư viện không sử dụng. Thư viện bị xóa sẽ không ảnh hưởng đến các site đang dử dụng.
@apiGroup Library
@apiVersion 1.0.0
@apiName DeleteOneLibrary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

********************************** Update Library *********************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {patch} {HOST}/template/api/v1.0/libraries/<library_id>   Cập nhật thư viện
@apiDescription API dùng để cập nhật thông tin của thư viện, bao gồm: tên, dữ liệu font-style, text-style.
@apiGroup Library
@apiVersion 1.0.0
@apiName UpdateOneLibrary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  [name]  Tên mới của thư viện. Các thư viện có thể trùng tên.
@apiParam   (Body:)   {String}  [thumbnail]   Link ảnh thumbnail của thư viện.
@apiParam   (Body:)   {Object}  [colors]  Dữ liệu bộ color style.
@apiParam   (Body:)   {ArrayObject}   [fonts]   Dữ liệu bộ font style.
@apiParamExample {json} Body example
{
  "name": "Thư viện 2",
  "thumbnail": "",
  "colors": {},
  "fonts": [],
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "library": {
    "id": "6406c41ab81ae2cdc5323321",
    "name": "Thư viện 2",
    "thumbnail": "",
    "merchant_id": "f91737b2-e1ab-4447-a5c1-a3047bfc0f71"
    "created_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "created_time": 1324567892,
    "updated_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "updated_time": 5467891223,
    "colors": {},
    "fonts": [],
    "assets": {
      "data": [],
      "paging": {
        "page":1,
        "per_page":10,
        "page_count":10,
        "total_count":100
      }
    }
  }
}
"""
********************************** Create Library *********************************
* version: 1.0.0                                                                  *
***********************************************************************************
"""
@api {post} {HOST}/template/api/v1.0/libraries Tạo thư viện
@apiDescription API dùng để tạo các thư viện lưu trữ fonts-style, text-style, assets thuận tiện cho việc apply nhanh vào website.
@apiGroup Library
@apiVersion 1.0.0
@apiName CreateLibrary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {String}  name  Tên của thư viện cần tạo. Các thư viện có thể trùng tên.
@apiParam   (Body:)   {String}  [thumbnail]   Link ảnh thumbnail của thư viện.
@apiParam   (Body:)   {Object}  [colors]  Dữ liệu bộ color style.
@apiParam   (Body:)   {ArrayObject}   [fonts]   Dữ liệu bộ font style.
@apiParam   (Body:)   {ArrayObject}   [assets]  Dữ liệu các tài sản cần add vào thư viện.
@apiParamExample {json} Body example
{
  "name": "",
  "thumbnail": "",
  "colors": {},
  "fonts": [],
  "assets": []
}

@apiUse created_updated_timestamp_success
@apiUse created_updated_user_success
@apiSuccess   {String}  merchant_id   Định danh của tenant.
@apiSuccess   {String}  id   Định danh của thư viện vừa tạo.
@apiSuccess   {String}  name   Tên của thư viện vừa tạo.
@apiSuccess   {String}  thumbnail   Link ảnh thumbnail của thư viện.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "library": {
    "id": "6406c41ab81ae2cdc5323321",
    "name": "Thư viện 1",
    "thumbnail": "",
    "merchant_id": "f91737b2-e1ab-4447-a5c1-a3047bfc0f71"
    "created_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "created_time": 1324567892,
    "updated_user": "5bed1d9d-a289-4f3a-bf38-3d03dc2e4d28",
    "updated_time": 5467891223
  }
}
"""
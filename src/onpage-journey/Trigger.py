************************************* List Trigger ***************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /trigger/list Lấy danh sách trigger
@apiGroup Trigger
@apiVersion 1.0.0
@apiName ListTrigger

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [campaign_id]     ID chiee
@apiParam   (Body:)    {String}    [source_id]        ID source cần lọ<PERSON>
@apiParam   (Body:)    {ArrayString}    [trigger_type] <PERSON><PERSON>ng các thao tác cần lọc. Giá trị: <br/><code>ON_CLICK</code>
                                                                                            <br/><code>PAGE_VIEW</code>
                                                                                            <br/><code>ON_SUBMIT_FORM</code>

@apiParam   (Body:)    {String}    [status] Trạng thái tracking. Giá trị: <br/><code>ACTIVE</code>
                                                                          <br/><code>NOT_ACTIVE</code>
                                                                          
@apiParam   (Body:)    {ArrayString}    [create_event_status] Trạng thái tạo event. Giá trị: <br/><code>DONE</code>
                                                                          <br/><code>FAILED</code>
                                                                          <br/><code>PROCESSING</code>

@apiParam   (Body:)    {Object}    [updated_time] Thông tin cần tìm kiếm theo thời gian cập nhật. Giá trị gửi lên theo giờ UTC.
@apiParam   (Body:)    {String}    updated_time.operator_key Toán tử so sánh. Giá trị: <br><code>op_is_equal</code>=Vào thời gian
                                                                                <br><code>op_is_less</code>=Trước thời gian
                                                                                <br><code>op_is_greater</code>=Sau thời gian
                                                                                <br><code>op_is_between</code>=Trong khoảng thời gian
                                                                                <br><code>op_is_not_empty</code>=Có thông tin
                                                                                <br><code>op_is_empty</code>=Chưa có thông tin
@apiParam   (Body:)    {ArrayString}    [updated_time.values] Giá trị thời gian để tìm kiếm. Định dạng: <br/><code>%Y-%m-%dT%H:%MZ</code>

@apiParam   (Body:)    {String}    [after_token]     Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Body:)    {Number}    [per_page]    Số phần tử trên một page. Mặc định: 10
@apiParam   (Body:)     {String}    [sort_field]    Trường thông tin để sắp xếp. Giá trị: <br/><code>updated_time</code>=Thời gian cập nhật (mặc định)
                                                                                            <br/><code>trigger_name</code>=Tên trigger
@apiParam   (Body:)     {Number}    [sort_type]     Kiểu sắp xếp. Giá trị: <br/><code>1</code>=Tăng dần
                                                                            <br/><code>-1</code>= Giảm dần

@apiParamExample    {json}  Body:
{
    "trigger_name": "Tên trigger",
    "after_token": "YXNkaGZha2RoZmFrZGZh",
    "per_page": 10,
    "trigger_type": ["ON_CLICK", "PAGE_VIEW"],
    "updated_time": {
        "operator_key": "op_is_between",
        "values": ["2023-11-05T03:00Z", "2023-11-05T010:00Z"]
    },
    "sort_field": "updated_time",
    "sort_type": 1
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "trigger_id": "4c06480e-0e1e-4d2b-83e1-6660f6b3a2bd",
            "event_key": "ten_trigger_1711079661_1711920819",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "status" : "ACTIVE",
            "trigger_name": "Người dùng thử nghiệm trang web với tên Mobio",
            "trigger_type" : "ON_CLICK",
            "source_id" : "7007968a-61d9-11ee-ae9f-38d57a786a3e",
            "source_type": "WEBSITE",
            "target_name": "Tên mục tiêu",
            "target_text": "Tạo Dashboard mới",
            "target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
            "tracking_page_type": "SPECIFIC_PAGE",
            "tracking_page_value": ["/abc?123", "/cde?345"],
            "create_event_status": "PROCESSING",
            "create_event_time": "2024-03-20T07:02:28.002Z",
            "created_time": "2024-04-04T09:49:57Z",
            "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": "2024-04-04T09:49:57Z",
            "updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        },
        {
            "trigger_id": "4c06480e-0e1e-4d2b-83e1-6660f6b3a2bd",
            "event_key": "ten_trigger_1711079661_1711920819",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "status" : "ACTIVE",
            "trigger_name": "Người dùng click chuột tùm lum",
            "trigger_type" : "PAGE_VIEW",
            "source_id" : "7007968a-61d9-11ee-ae9f-38d57a786a3e",
            "source_type": "WEBSITE",
            "target_name": "Tên mục tiêu",
            "target_text": "Tạo Dashboard mới",
            "target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
            "tracking_page_type": "SPECIFIC_PAGE",
            "tracking_page_value": ["/abc?123", "/cde?345"],
            "create_event_status": "PROCESSING",
            "create_event_time": "2024-03-20T07:02:28.002Z",
            "created_time": "2024-04-04T09:49:57Z",
            "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": "2024-04-04T09:49:57Z",
            "updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
        }
    ],
    "paging": {
        "total_items": 10,
        "after_token": "YXNkaGZha2RoZmFrZGZh"
    }
}
"""
************************************* Create Trigger *************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /trigger/create Tạo trigger
@apiGroup Trigger
@apiVersion 1.0.0
@apiName CreateTrigger

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    trigger_name  Tên trigger cần tạo
@apiParam   (Body:)    {String}    trigger_type  Loại trigger. Giá trị: <br/><code>ON_CLICK</code>
                                                                        <br/><code>PAGE_VIEW</code>
                                                                        <br/><code>ON_SUBMIT_FORM</code>
                                                                        
@apiParam   (Body:)      {String}   status Trạng thái trigger. Giá trị:
                                                        <br/><code>ACTIVE</code>=Được hoạt động
                                                        <br/><code>NOT_ACTIVE</code>=Không được hoạt động
                                                        
@apiParam   (Body:)    {String}    source_id  Mã định danh nguồn.
@apiParam   (Body:)    {String}    source_type  Loại nguồn. Giá trị: <br/><code>WEBSITE</code>
                                                                     <br/><code>APP</code>
@apiParam   (Body:)     {String}    [target_name]  Tên mục tiêu
@apiParam   (Body:)    {String}    target_address  Giá trị vị trí mục tiêu (Xpath).
@apiParam   (Body:)    {String}    target_text  Tên hiển thị của vị trí mục tiêu.
@apiParam   (Body:)    {ArrayObject}    [trigger_fields]  Danh sách trường thông tin.
@apiParam   (Body:)    {String}    trigger_fields.field_method  Phương thức lấy dữ liệu. Ví dụ: TEXT
@apiParam   (Body:)    {String}    [trigger_fields.field_key]  Key để tạo bảng Behavior Event. Nếu không truyền thì tự động sinh field_key từ field_name
@apiParam   (Body:)    {String}    trigger_fields.field_name  Tên trường thông tin
@apiParam   (Body:)    {String}    trigger_fields.field_property  Kiểu dữ liệu của trường thông tin. Giá trị: <br/><code>string</code>
                                                                                                                <br/><code>int</code>
                                                                                                                <br/><code>decimal_19</code>
                                                                                                                <br/><code>boolean</code>
                                                                                                                <br/><code>date</code>
                                                                                                                <br/><code>datetime</code>
@apiParam   (Body:)    {String}    trigger_fields.target_text  Tên hiển thị nội dung giá trị của mục tiêu.
@apiParam   (Body:)    {String}    trigger_fields.target_address  Giá trị/mục tiêu.
@apiParam   (Body:)    {String}    trigger_fields.target_name  Tên mục tiêu
@apiParam   (Body:)    {Boolean}   trigger_fields.master_data     Lưu và hiển thị danh sách gợi ý khi lọc (Hành động để thu thập master data trên vùng OLAP)
@apiParam   (Body:)    {Object}    trigger_fields.target_other_info     Thông tin cấu hình khác của trigger

@apiParam   (Body:)    {ArrayObject}    [trigger_results]  Danh sách kết quả hành vi.
@apiParam   (Body:)    {String}    trigger_results.result_method  Phương thức lấy dữ liệu. Ví dụ: STATIC_TEXT
@apiParam   (Body:)    {String}    trigger_results.result_path  Path trang kết quả
@apiParam   (Body:)    {String}    trigger_results.target_text  Tên hiển thị nội dung giá trị mục tiêu.
@apiParam   (Body:)    {String}    trigger_results.target_address  Giá trị/mục tiêu .
@apiParam   (Body:)    {String}    trigger_results.target_name  Tên mục tiêu

@apiParam   (Body:)    {String}    tracking_page_type  Loại trang để tracking. Giá trị: <br/><code>CURRENT_PAGE</code>=Trang này
                                                                                        <br/><code>SAME_PAGE</code>=Tất cả các trang tương tự
                                                                                        <br/><code>SPECIFIC_PAGE</code>=Trang tùy chỉnh
@apiParam   (Body:)    {ArrayString}    tracking_page_value  URL các trang được chọn khi <code>tracking_page_type</code>=SPECIFIC_PAGE
@apiParam   (Body:)    {Object}    target_other_info     Thông tin cấu hình khác của trigger

@apiSuccess   (Description response)     {String}   status                  Trạng thái trigger. Giá trị:
                                                                                <br/><code>ACTIVE</code>=Được hoạt động
                                                                                <br/><code>NOT_ACTIVE</code>=Không được hoạt động

@apiSuccess   (Description response)     {String}   create_event_status     Trạng thái tạo event. Giá trị:
                                                                                <br/><code>DONE</code>=Thành công
                                                                                <br/><code>PROCESSING</code>=Đang xử lý
                                                                                <br/><code>FAILED</code>=Thất bại

@apiSuccess   (Description response)     {Date}     create_event_time       Thời điểm yêu cầu tạo event


@apiParamExample    {json}  Body:
{
    "trigger_name": "Ten trigger",
    "trigger_type" : "ON_CLICK",
    "source_id" : "7007968a-61d9-11ee-ae9f-38d57a786a3e",
    "source_type": "WEBSITE",
    "target_name": "Tên mục tiêu",
    "target_text": "Tạo Dashboard mới",
    "target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
    "trigger_fields": [
        {
            "field_method": "TEXT",
            "field_name": "tên field",
            "field_property": "string",
            "target_name": "Tên mục tiêu",
            "target_text": "Tạo Dashboard mới",
            "target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
            "target_other_info": {
                "target_event": ""
            },
            "master_data": true,
        }
    ],
    "trigger_results": [
        {
            "result_method": "STATIC_TEXT",
            "result_path": "/cart",
            "target_name": "Tên mục tiêu",
            "target_text": "Tạo Dashboard mới",
            "target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
        }
    ],
    "tracking_page_type": "SPECIFIC_PAGE",
    "tracking_page_value": ["/abc?123", "/cde?345"],
    "target_other_info": {
        "target_event": ""
    }
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "trigger_id": "4c06480e-0e1e-4d2b-83e1-6660f6b3a2bd",
        "status" : "ACTIVE",
        "trigger_name": "Ten trigger",
		"event_key": "ten_trigger_1712198997",
        "trigger_type" : "ON_CLICK",
        "source_id" : "7007968a-61d9-11ee-ae9f-38d57a786a3e",
        "source_type": "WEBSITE",
        "target_name": "Tên mục tiêu",
        "target_text": "Tạo Dashboard mới",
        "target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
        "trigger_fields": [
            {
                "field_method": "TEXT",
                "field_name": "tên field",
				"event_key": "ten_trigger_1712198997",
				"field_key": "ten_field_1712198997",
                "field_property": "string",
                "target_name": "Tên mục tiêu",
                "target_text": "Tạo Dashboard mới",
                "target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
                "master_data": true,
                "target_other_info": {
                    "target_event": ""
                },
				"created_time": "2024-04-04T09:49:57Z",
				"created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
				"updated_time": "2024-04-04T09:49:57Z",
				"updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            }
        ],
        "trigger_results": [
            {
                "result_method": "STATIC_TEXT",
                "result_path": "/cart",
                "target_name": "Tên mục tiêu",
                "target_text": "Tạo Dashboard mới",
                "target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
				"created_time": "2024-04-04T09:49:57Z",
				"created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
				"updated_time": "2024-04-04T09:49:57Z",
				"updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            }
        ],
        "tracking_page_type": "SPECIFIC_PAGE",
        "tracking_page_value": ["/abc?123", "/cde?345"],
        "target_other_info": {
            "target_event": ""
        },
        "create_event_status": "PROCESSING",
        "create_event_time": "2024-03-20T07:02:28.002Z",
        "created_time": "2024-04-04T09:49:57Z",
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "updated_time": "2024-04-04T09:49:57Z",
        "updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
    }
}

"""
*********************************** Retry create event ***********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /trigger/retry-create-event Retry tạo event
@apiGroup Trigger
@apiVersion 1.0.0
@apiName RetryCreateEventTrigger

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    trigger_id     ID trigger đang thao tác


@apiParamExample    {json}  Body:
{
    "trigger_id": "38d57a786a3e-7bfb-11ee-83bf-0c95bda4",
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
*********************************** Toggle Status Trigger ********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {put} /trigger/toggle-status Bật/tắt trigger
@apiDescription Bật/tắt trigger
@apiGroup Trigger
@apiVersion 1.0.0
@apiName ToggleStatusTrigger

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    trigger_id     ID trigger đang thao tác


@apiParamExample    {json}  Body:
{
    "trigger_id": "38d57a786a3e-7bfb-11ee-83bf-0c95bda4",
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
****************************** Count Campaign By Trigger Id ******************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /trigger/count-campaign-by-id Đếm số lượng campaign đang dùng trigger
@apiGroup Trigger
@apiVersion 1.0.0
@apiName CountCampaignByTriggerId

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}    trigger_id  ID của trigger
@apiParamExample    {json}  Query:
{
    "trigger_id": "b24b7654-f268-11ee-b80e-581cf8f75567"
}
@apiSuccess   (Description response)     {Integer}   count_campaign      Số lượng campaign đang dùng trigger này
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "count_campaign": 4499,
        "create_event_status": "FAILED",
        "create_event_time": "2024-04-04T09:49:58Z",
        "created_time": "2024-04-04T09:49:58Z",
        "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "event_key": "sonnn_trigger_1amvbkfaqk7q8ourcjjbmgrskzygny6f_1712198997",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "source_id": "367fe208-48b1-4ba1-b1c2-c768b05f0ed2",
        "source_type": "WEBSITE",
        "status": "ACTIVE",
        "target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
        "target_name": "Tên mục tiêu",
        "target_other_info": {
            "hihi": 123
        },
        "target_text": "Tạo Dashboard mới",
        "tracking_page_type": "SPECIFIC_PAGE",
        "tracking_page_value": [],
        "trigger_id": "b24b7654-f268-11ee-b80e-581cf8f75567",
        "trigger_name": "[sonnn] trigger 1amvBKFaQk7q8ourCjjbmGRSKzYgNy6F",
        "trigger_name_raw": "[sonnn] trigger 1amvbkfaqk7q8ourcjjbmgrskzygny6f",
        "trigger_type": "ON_CLICK",
        "updated_time": "2024-04-04T10:51:34Z",
        "updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
    }
}
"""
************************************* Delete Trigger *************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {put} /trigger/delete Xóa trigger
@apiGroup Trigger
@apiVersion 1.0.0
@apiName DeleteTrigger

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    trigger_id  ID của trigger cần xóa
@apiParamExample    {json}  Body:
{
    "trigger_id": "b6909098-61de-11ee-b707-38d57a786a3e"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công"
}
"""
************************************* Detail Trigger **************************************
* version: 1.0.0                                                                          *
*******************************************************************************************
"""
@api {get} /trigger/detail Lấy chi tiết trigger
@apiGroup Trigger
@apiVersion 1.0.0
@apiName DetailTrigger

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}    trigger_id     ID trigger

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
	"code": 200,
	"data": {
		"create_event_status": "FAILED",
		"create_event_time": "2024-04-09T04:00:01Z",
		"created_time": "2024-04-09T04:00:01Z",
		"created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"event_key": "sonnn_trigger_*******************************_1712610000",
		"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
		"source_id": "7007968a-61d9-11ee-ae9f-38d57a786a3e",
		"source_type": "WEBSITE",
		"status": "NOT_ACTIVE",
		"target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
		"target_name": "Tên mục tiêu",
		"target_other_info": {
			"hihi": 123
		},
		"target_text": "Tạo Dashboard mới",
		"tracking_page_type": "SPECIFIC_PAGE",
		"tracking_page_value": [],
		"trigger_fields": [
			{
				"created_time": "2024-04-09T04:00:00Z",
				"created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
				"event_key": "sonnn_trigger_*******************************_1712610000",
				"field_key": "ten_field_*********************************_1712610000",
				"field_method": "TEXT",
				"field_name": "tên field *********************************",
				"field_name_raw": "ten field *********************************",
				"field_property": "int",
				"master_data": true,
				"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
				"target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
				"target_name": "Tên mục tiêu",
				"target_other_info": {
					"hihi": 123
				},
				"target_text": "Tạo Dashboard mới",
				"trigger_id": "13603385880103376004",
				"updated_time": "2024-04-09T04:00:00Z",
				"updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
			},
			{
				"created_time": "2024-04-09T04:00:00Z",
				"created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
				"event_key": "sonnn_trigger_*******************************_1712610000",
				"field_key": "ten_field_******************************_1712610000",
				"field_method": "TEXT",
				"field_name": "tên field ******************************",
				"field_name_raw": "ten field ******************************",
				"field_property": "int",
				"master_data": true,
				"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
				"target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
				"target_name": "Tên mục tiêu",
				"target_other_info": {
					"hihi": 123
				},
				"target_text": "Tạo Dashboard mới",
				"trigger_id": "13603385880103376004",
				"updated_time": "2024-04-09T04:00:00Z",
				"updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
			}
		],
		"trigger_id": "13603385880103376004",
		"trigger_name": "[sonnn] trigger *******************************",
		"trigger_name_raw": "[sonnn] trigger *******************************",
		"trigger_results": [
			{
				"created_time": "2024-04-09T04:00:01Z",
				"created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
				"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
				"result_method": "STATIC_TEXT",
				"result_path": "/cart",
				"target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
				"target_name": "Tên mục tiêu",
				"target_text": "Tạo Dashboard mới",
				"trigger_id": "13603385880103376004",
				"updated_time": "2024-04-09T04:00:01Z",
				"updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
			},
			{
				"created_time": "2024-04-09T04:00:01Z",
				"created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
				"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
				"result_method": "STATIC_TEXT",
				"result_path": "/cart",
				"target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
				"target_name": "Tên mục tiêu",
				"target_text": "Tạo Dashboard mới",
				"trigger_id": "13603385880103376004",
				"updated_time": "2024-04-09T04:00:01Z",
				"updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
			}
		],
		"trigger_type": "ON_CLICK",
		"updated_time": "2024-04-09T05:00:24Z",
		"updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
	},
	"lang": "vi",
	"message": "request thành công."
}
"""

********************************* Get Trigger By IDs *************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /trigger/get-by-ids Lấy thông tin trigger theo ids
@apiGroup Trigger
@apiVersion 1.0.0
@apiName GetTriggerByIDs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {ArrayString}    trigger_ids  Danh sách ID của trigger cần lấy thông tin.
@apiParamExample    {json}  Body:
{
    "trigger_ids": ["b6909098-61de-11ee-b707-38d57a786a3e", "43ffd8b6-820b-11ee-9ce9-38d57a786a3e"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "trigger_id": "4c06480e-0e1e-4d2b-83e1-6660f6b3a2bd",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "status" : "ACTIVE",
            "trigger_name": "Người dùng thử nghiệm trang web với tên Mobio",
            "trigger_type" : "ON_CLICK",
            "source_id" : "7007968a-61d9-11ee-ae9f-38d57a786a3e",
            "source_type": "WEBSITE",
            "target_name": "Tên mục tiêu",
            "target_text": "Tạo Dashboard mới",
            "target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
            "tracking_page_type": "SPECIFIC_PAGE",
            "tracking_page_value": ["/abc?123", "/cde?345"],
            "updated_time": "2024-03-15T04:02:28.002Z",
            "updated_user": "653f6d66-c6ef-11ed-8fe8-c7a3239de083"
            "create_event_status": "PROCESSING",
            "create_event_time": "2024-03-20T07:02:28.002Z",
        },
        {
            "trigger_id": "4c06480e-0e1e-4d2b-83e1-6660f6b3a2bd",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "status" : "ACTIVE",
            "trigger_name": "Người dùng click chuột tùm lum",
            "trigger_type" : "PAGE_VIEW",
            "source_id" : "7007968a-61d9-11ee-ae9f-38d57a786a3e",
            "source_type": "WEBSITE",
            "target_name": "Tên mục tiêu",
            "target_text": "Tạo Dashboard mới",
            "target_address": "/html/body/mo-apps-micro-dashboard-root/mo-libs-shared-components-layout/div/div/mo-apps-micro-dashboard/mo-libs-dashboard/div/div[1]/div[2]/div/div[2]/mo-libs-shared-components-button[3]/mo-libs-shared-components-tooltip/button/mo-libs-shared-components-tooltip",
            "tracking_page_type": "SPECIFIC_PAGE",
            "tracking_page_value": ["/abc?123", "/cde?345"],
            "updated_time": "2024-03-15T04:02:28.002Z",
            "updated_user": "653f6d66-c6ef-11ed-8fe8-c7a3239de083"
            "create_event_status": "PROCESSING",
            "create_event_time": "2024-03-20T07:02:28.002Z",
        }
    ],
}
"""
************************************* Summary source *************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /trigger/summary-amount-by-source Thống kê số lượng trigger theo source
@apiGroup Trigger
@apiVersion 1.0.0
@apiName SummarySource

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam   (Body:)    {String}    [trigger_name]     Chuỗi tìm kiếm theo tên trigger
@apiParam   (Body:)    {String}    [source_id]        ID source cần lọc
@apiParam   (Body:)    {ArrayString}    [trigger_type] Mảng các thao tác cần lọc. Giá trị: <br/><code>ON_CLICK</code>
                                                                                            <br/><code>PAGE_VIEW</code>
                                                                                            <br/><code>ON_SUBMIT_FORM</code>

@apiParam   (Body:)    {String}    [status] Trạng thái tracking. Giá trị: <br/><code>ACTIVE</code>
                                                                          <br/><code>NOT_ACTIVE</code>

@apiParam   (Body:)    {Object}    [updated_time] Thông tin cần tìm kiếm theo thời gian cập nhật. Giá trị gửi lên theo giờ UTC.
@apiParam   (Body:)    {String}    updated_time.operator_key Toán tử so sánh. Giá trị: <br><code>op_is_equal</code>=Vào thời gian
                                                                                <br><code>op_is_less</code>=Trước thời gian
                                                                                <br><code>op_is_greater</code>=Sau thời gian
                                                                                <br><code>op_is_between</code>=Trong khoảng thời gian
                                                                                <br><code>op_is_not_empty</code>=Có thông tin
                                                                                <br><code>op_is_empty</code>=Chưa có thông tin
@apiParam   (Body:)    {ArrayString}    [updated_time.values] Giá trị thời gian để tìm kiếm. Định dạng: <br/><code>%Y-%m-%dT%H:%MZ</code>


@apiParamExample    {json}  Body:
{
    "trigger_name": "Tên trigger",
    "trigger_type": ["ON_CLICK", "PAGE_VIEW"],
    "status": "STATUS",
    "updated_time": {
        "operator_key": "op_is_between",
        "values": ["2023-11-05T03:00Z", "2023-11-05T010:00Z"]
    },
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "source_id": "fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "count": "10"
        },
        {
            "source_id": "b99bdcf-d582-4f49-9715-1b61dfff3924",
            "count": "4"
        },
        {
            "source_id": "f8e537e6-72d0-45ab-a2bc-071434a3aa19",
            "count": "1"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
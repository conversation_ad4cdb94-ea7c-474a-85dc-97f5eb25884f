************************************* List Campaign **************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /campaign/list Lấy danh sách chiến dịch
@apiGroup Campaign
@apiVersion 1.0.0
@apiName ListCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [campaign_name]     Chuỗi tìm kiếm theo tên chiến dịch
@apiParam   (Body:)    {ArrayString}    [source_id] Mảng các ID nguồn cần lọc.
@apiParam   (Body:)    {Object}    [start_time] Thông tin cần tìm kiếm theo thời gian bắt đầu. Giá trị gửi lên theo giờ UTC.
@apiParam   (Body:)    {String}    start_time.operator_key Toán tử so sánh. Giá trị: <br><code>op_is_equal</code>=Vào thời gian
                                                                                <br><code>op_is_less</code>=Trước thời gian
                                                                                <br><code>op_is_greater</code>=Sau thời gian
                                                                                <br><code>op_is_between</code>=Trong khoảng thời gian
                                                                                <br><code>op_is_not_empty</code>=Có thông tin
                                                                                <br><code>op_is_empty</code>=Chưa có thông tin
@apiParam   (Body:)    {ArrayString}    [start_time.values] Giá trị thời gian để tìm kiếm. Định dạng: <br/><code>%Y-%m-%dT%H:%MZ</code>
@apiParam   (Body:)    {Object}    [end_time] Thông tin cần tìm kiếm theo thời gian kết thúc. Giá trị gửi lên theo giờ UTC.
@apiParam   (Body:)    {String}    end_time.operator_key Toán tử so sánh. Giá trị: <br><code>op_is_equal</code>=Vào thời gian
                                                                                <br><code>op_is_less</code>=Trước thời gian
                                                                                <br><code>op_is_greater</code>=Sau thời gian
                                                                                <br><code>op_is_between</code>=Trong khoảng thời gian
                                                                                <br><code>op_is_not_empty</code>=Có thông tin
                                                                                <br><code>op_is_empty</code>=Chưa có thông tin
@apiParam   (Body:)    {ArrayString}    [end_time.values] Giá trị thời gian để tìm kiếm. Định dạng: <br/><code>%Y-%m-%dT%H:%MZ</code>
@apiParam   (Body:)    {Object}    [created_time] Thông tin cần tìm kiếm theo thời gian tạo chiến dịch. Giá trị gửi lên theo giờ UTC.
@apiParam   (Body:)    {String}    created_time.operator_key Toán tử so sánh. Giá trị: <br`><code>op_is_equal</code>=Vào thời gian
                                                                                <br><code>op_is_less</code>=Trước thời gian
                                                                                <br><code>op_is_greater</code>=Sau thời gian
                                                                                <br><code>op_is_between</code>=Trong khoảng thời gian
@apiParam   (Body:)    {ArrayString}    [created_time.values] Giá trị thời gian để tìm kiếm. Định dạng: <br/><code>%Y-%m-%dT%H:%MZ</code>
@apiParam   (Body:)    {ArrayString}    [created_user]     Danh sách ID người tạo chiến dịch cần tìm kiếm.
@apiParam   (Body:)    {ArrayString}    [master_campaign_id]     Danh sách ID master campaign cần tìm.
@apiParam   (Body:)    {String}     [status]    Trạng thái chiến dịch cần lấy danh sách. Giá trị: <br/><code>PROCESSING</code>=Đang diễn ra
                                                                                                    <br/><code>SCHEDULED</code>=Lên lịch
                                                                                                    <br/><code>FINISHED</code>=Hoàn thành
                                                                                                    <br/><code>PAUSED</code>=Tạm dừng
                                                                                                    <br/><code>DRAFT</code>=Nháp
@apiParam   (Body:)    {String}    [after_token]     Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Body:)    {Number}    [per_page]    Số phần tử trên một page. Mặc định: 10
@apiParam   (Body:)     {String}    [sort_field]    Trường thông tin để sắp xếp. Giá trị: <br/><code>updated_time</code>=Thời gian cập nhật
@apiParam   (Body:)     {Number}    [sort_type]     Kiểu sắp xếp. Giá trị: <br/><code>1</code>=Tăng dần
                                                                            <br/><code>-1</code>= Giảm dần
                                                                            
@apiSuccess   (Description response)     {String}   status                  Trạng thái campaign. Giá trị:
                                                                                <br/><code>PROCESSING</code>=Chiến dịch đang được diễn ra
                                                                                <br/><code>SCHEDULED</code>=Lên lịch
                                                                                <br/><code>FINISHED</code>=Kết thúc
                                                                                <br/><code>PAUSED</code>=Tạm dừng
                                                                                <br/><code>DRAFT</code>=Nháp
                                                                                
@apiSuccess   (Description response)     {String}   activate_status         Trạng thái activate campaign. Giá trị:
                                                                                <br/><code>WAIT</code>=Chờ kích hoạt (khi chưa bấm kích hoạt)
                                                                                <br/><code>FAILED</code>=Lỗi kích hoạt
                                                                                <br/><code>PROCESSING</code>=Đang xử lý
                                                                                <br/><code>SUCCESS</code>=Thành công

@apiParamExample    {json}  Body:
{
    "campaign_name": "Tên chiến dịch",
    "after_token": "YXNkaGZha2RoZmFrZGZh",
    "per_page": 10,
    "source_id": ["e43d4e22-68ce-11ee-a813-38d57a786a3e", "e5ee6e36-68ce-11ee-951c-38d57a786a3e"],
    "master_campaign_id": ["eda9de3a-68ce-11ee-9159-38d57a786a3e"],
    "created_user": ["eda9de3a-68ce-11ee-9159-38d57a786a3e", "ef6611f6-68ce-11ee-b48f-38d57a786a3e"],
    "start_time": {
        "operator_key": "op_is_between",
        "values": ["2023-11-05T03:00Z", "2023-11-05T010:00Z"]
    },
    "end_time": {
        "operator_key": "op_is_between",
        "values": ["2023-11-05T03:00Z", "2023-11-05T010:00Z"]
    },
    "created_time": {
        "operator_key": "op_is_between",
        "values": ["2023-11-05T03:00Z", "2023-11-05T010:00Z"]
    },
    "status": "DRAFT",
    "sort_field": "updated_time",
    "sort_type": 1
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "merchant_id": "37c90b9e-7bf8-11ee-ad32-38d57a786a3e",
            "campaign_id": "dbb50f6e-77a7-11ee-ac67-38d57a786a3e",
            "campaign_name": "Tên chiến dịch",
            "campaign_description": "Mô tả chiến dịch",
            "master_campaign_id": "41da52ca-7bf8-11ee-be4e-38d57a786a3e",
            "source_id": "d8cf9db4-77a7-11ee-8579-38d57a786a3e",
            "source_type": "WEBSITE/APP",
            "start_type": "IMMEDIATELY/AT_TIME",
            "start_time": "2023-10-31T07:00Z",
            "end_type": "INFINITY/AT_TIME",
            "end_time": "2023-10-31T07:00Z",
            "status": "PROCESSING/SCHEDULED/FINISHED/PAUSED/DRAFT",
            "activate_status": "WAIT/FAILED/PROCESSING/SUCCESS",
            "created_time": "2023-10-31T07:30:00Z",
            "created_user": "2da54f68-77c2-11ee-84f7-38d57a786a3e",
            "updated_time": "2023-10-31T07:30:00Z",
            "updated_user": "3a810414-77c2-11ee-9669-38d57a786a3e",
            "exit_page_sign_status": true,
            "exit_page_sign_config": {
                "page_type": "SPECIFIC_PAGE",
                "pages": [
                    {
                        "page_url": "https://abc.com/xyz1",
                        "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff",
                        "page_access_times_type": "FROM_TO",
                        "page_access_times_config": {
                            "from": 2,
                            "to": 5
                        }
                    },
                    {
                        "page_url": "https://abc.com/xyz2",
                        "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff",
                        "page_access_times_type": "SPECIFIC",
                        "page_access_times_config": {
                            "value": 10
                        }
                    }
                ]
            }
        },
        {
            "merchant_id": "37c90b9e-7bf8-11ee-ad32-38d57a786a3e",
            "campaign_id": "dbb50f6e-77a7-11ee-ac67-38d57a786a3e",
            "campaign_name": "Tên chiến dịch",
            "campaign_description": "Mô tả chiến dịch",
            "master_campaign_id": "41da52ca-7bf8-11ee-be4e-38d57a786a3e",
            "source_id": "d8cf9db4-77a7-11ee-8579-38d57a786a3e",
            "source_type": "WEBSITE/APP",
            "start_type": "IMMEDIATELY/AT_TIME",
            "start_time": "2023-10-31T07:00Z",
            "end_type": "INFINITY/AT_TIME",
            "end_time": "2023-10-31T07:00Z",
            "status": "PROCESSING/SCHEDULED/FINISHED/PAUSED/DRAFT",
            "activate_status": "WAIT/FAILED/PROCESSING/SUCCESS",
            "created_time": "2023-10-31T07:30:00Z",
            "created_user": "2da54f68-77c2-11ee-84f7-38d57a786a3e",
            "updated_time": "2023-10-31T07:30:00Z",
            "updated_user": "3a810414-77c2-11ee-9669-38d57a786a3e",
            "exit_page_sign_status": true,
            "exit_page_sign_config": {
                "page_type": "ANY_PAGE",
                "popup_send_times": 2,
                "popup_send_wait_value": 5,
                "popup_send_wait_unit": "MINUTES"
                "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff"
            }
        }
    ],
    "paging": {
        "after_token": "YXNkaGZha2RoZmFrZGZh"
    }
}
"""
************************************* Detail Campaign *************************************
* version: 1.0.0                                                                          *
*******************************************************************************************
"""
@api {get} /campaign/detail Lấy chi tiết chiến dịch
@apiGroup Campaign
@apiVersion 1.0.0
@apiName DetailCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}    campaign_id     ID chiến dịch

@apiSuccess   (Description response)     {String}   status                  Trạng thái campaign. Giá trị:
                                                                                <br/><code>PROCESSING</code>=Chiến dịch đang được diễn ra
                                                                                <br/><code>SCHEDULED</code>=Lên lịch
                                                                                <br/><code>FINISHED</code>=Kết thúc
                                                                                <br/><code>PAUSED</code>=Tạm dừng
                                                                                <br/><code>DRAFT</code>=Nháp
                                                                                
@apiSuccess   (Description response)     {String}   activate_status         Trạng thái activate campaign. Giá trị:
                                                                                <br/><code>WAIT</code>=Chờ kích hoạt (khi chưa bấm kích hoạt)
                                                                                <br/><code>PROCESSING</code>=Đang xử lý
                                                                                <br/><code>SUCCESS</code>=Thành công


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "campaign_id": "dbb50f6e-77a7-11ee-ac67-38d57a786a3e",
        "campaign_name": "Tên chiến dịch",
        "campaign_description": "Mô tả chiến dịch",
        "master_campaign_id": "41da52ca-7bf8-11ee-be4e-38d57a786a3e",
        "source_id": "d8cf9db4-77a7-11ee-8579-38d57a786a3e",
        "source_type": "WEBSITE/APP",
        "start_type": "IMMEDIATELY/AT_TIME",
        "start_time": "2023-10-31T07:00Z",
        "end_type": "INFINITY/AT_TIME",
        "end_time": "2023-10-31T07:00Z",
        "status": "PROCESSING/SCHEDULED/FINISHED/PAUSED/DRAFT",
        "activate_status": "WAIT/FAILED/SUCCESS/PROCESSING",
        "created_time": "2023-10-31T07:30:00Z",
        "created_user": "2da54f68-77c2-11ee-84f7-38d57a786a3e",
        "updated_time": "2023-10-31T07:30:00Z",
        "updated_user": "3a810414-77c2-11ee-9669-38d57a786a3e",
        
        "exit_page_sign_status": true,
        "exit_page_sign_config": {
            "page_type": "SPECIFIC_PAGE",
            "pages": [
                {
                    "page_url": "https://abc.com/xyz1",
                    "page_access_times_type": "FROM_TO",
                    "page_access_times_config": {
                        "from": 2,
                        "to": 5
                    },
                    "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff"
                },
                {
                    "page_url": "https://abc.com/xyz2",
                    "page_access_times_type": "SPECIFIC",
                    "page_access_times_config": {
                        "value": 10
                    },
                    "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff"
                }
            ]
        },
    }
}
"""
************************************* Detail Config Campaign ******************************
* version: 1.0.0                                                                          *
*******************************************************************************************
"""
@api {get} /campaign/config/detail Lấy cấu hình chiến dịch
@apiGroup Campaign
@apiVersion 1.0.0
@apiName DetailConfigCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)   {String}    campaign_id     ID chiến dịch

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
	"code": 200,
	"data": {
		"campaign_id": "0ebd09d0-db84-11ee-b62b-581cf8f75567",
		"diagram": {
			"nodes": [
				{
					"code": "TARGET",
					"config": {},
					"element_type": "TRIGGER",
					"id": "941dbebc-dba2-11ee-8e61-581cf8f75567",
					"name": "Đối tượng mục tiêu",
					"next_id": "941dc10a-dba2-11ee-8e61-581cf8f75567",
					"pre_id": null,
					"specific_counter_current_code": 0,
					"specific_height": 48,
					"specific_init": true,
					"specific_width": 165,
					"specific_x": 709,
					"specific_y": 97
				},
				{
					"code": "EXIT",
					"config": {},
					"element_type": "EXIT",
					"id": "941dc10a-dba2-11ee-8e61-581cf8f75567",
					"name": "Kết thúc hành trình",
					"next_id": null,
					"pre_id": "941dbebc-dba2-11ee-8e61-581cf8f75567",
					"specific_counter_current_code": 0,
					"specific_height": 48,
					"specific_init": true,
					"specific_width": 165,
					"specific_x": 709,
					"specific_y": 205
				}
			]
		},
		"nodes": [
			{
				"created_time": "2024-03-06T09:16:36Z",
				"created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
				"node_config": {},
				"node_connection": [
					{
						"next_node_id": "",
						"previous_node_id": "previous_node_id"
					}
				],
				"node_id": "8fc0a33c-baf5-baf5-a7c2-0242ac180022",
				"node_name": "Khối",
				"node_type": "EXIT",
				"updated_time": "2024-03-06T09:16:36Z",
				"updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
			}
		]
	},
	"lang": "vi",
	"message": "request thành công."
}
"""
************************************* Summary Campaign ***********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /campaign/summary Thống kê số lượng chiến dịch theo trạng thái
@apiGroup Campaign
@apiVersion 1.0.0
@apiName SummaryCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    [campaign_name]     Chuỗi tìm kiếm theo tên chiến dịch
@apiParam   (Body:)    {ArrayString}    [source_id] Mảng các ID nguồn cần lọc.
@apiParam   (Body:)    {Object}    [start_time] Thông tin cần tìm kiếm theo thời gian bắt đầu. Giá trị gửi lên theo giờ UTC.
@apiParam   (Body:)    {String}    start_time.operator_key Toán tử so sánh. Giá trị: <br><code>op_is_equal</code>=Vào thời gian
                                                                                <br><code>op_is_less</code>=Trước thời gian
                                                                                <br><code>op_is_greater</code>=Sau thời gian
                                                                                <br><code>op_is_between</code>=Trong khoảng thời gian
                                                                                <br><code>op_is_not_empty</code>=Có thông tin
                                                                                <br><code>op_is_empty</code>=Chưa có thông tin
@apiParam   (Body:)    {ArrayString}    [start_time.values] Giá trị thời gian để tìm kiếm. Định dạng: <br/><code>%Y-%m-%dT%H:%MZ</code>
@apiParam   (Body:)    {Object}    [end_time] Thông tin cần tìm kiếm theo thời gian kết thúc. Giá trị gửi lên theo giờ UTC.
@apiParam   (Body:)    {String}    end_time.operator_key Toán tử so sánh. Giá trị: <br><code>op_is_equal</code>=Vào thời gian
                                                                                <br><code>op_is_less</code>=Trước thời gian
                                                                                <br><code>op_is_greater</code>=Sau thời gian
                                                                                <br><code>op_is_between</code>=Trong khoảng thời gian
                                                                                <br><code>op_is_not_empty</code>=Có thông tin
                                                                                <br><code>op_is_empty</code>=Chưa có thông tin
@apiParam   (Body:)    {ArrayString}    [end_time.values] Giá trị thời gian để tìm kiếm. Định dạng: <br/><code>%Y-%m-%dT%H:%MZ</code>
@apiParam   (Body:)    {Object}    [created_time] Thông tin cần tìm kiếm theo thời gian tạo chiến dịch. Giá trị gửi lên theo giờ UTC.
@apiParam   (Body:)    {String}    created_time.operator_key Toán tử so sánh. Giá trị: <br><code>op_is_equal</code>=Vào thời gian
                                                                                <br><code>op_is_less</code>=Trước thời gian
                                                                                <br><code>op_is_greater</code>=Sau thời gian
                                                                                <br><code>op_is_between</code>=Trong khoảng thời gian
@apiParam   (Body:)    {ArrayString}    [created_time.values] Giá trị thời gian để tìm kiếm. Định dạng: <br/><code>%Y-%m-%dT%H:%MZ</code>
@apiParam   (Body:)    {ArrayString}    [created_user]     Danh sách ID người tạo chiến dịch cần tìm kiếm.
@apiParam   (Body:)    {ArrayString}    [master_campaign_id]     Master campaign cần tìm.

@apiParamExample    {json}  Body:
{
    "campaign_name": "Tên chiến dịch",
    "source_id": ["e43d4e22-68ce-11ee-a813-38d57a786a3e", "e5ee6e36-68ce-11ee-951c-38d57a786a3e"],
    "master_campaign_id": "eda9de3a-68ce-11ee-9159-38d57a786a3e",
    "created_user": ["eda9de3a-68ce-11ee-9159-38d57a786a3e", "ef6611f6-68ce-11ee-b48f-38d57a786a3e"],
    "start_time": {
        "operator_key": "op_is_between",
        "values": ["2023-11-05T03:00Z", "2023-11-05T010:00Z"]
    },
    "end_time": {
        "operator_key": "op_is_between",
        "values": ["2023-11-05T03:00Z", "2023-11-05T010:00Z"]
    },
    "created_time": {
        "operator_key": "op_is_between",
        "values": ["2023-11-05T03:00Z", "2023-11-05T010:00Z"]
    }
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "count": 1,
            "status": "DRAFT"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
************************************* Create Campaign ************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /campaign/create Tạo chiến dịch
@apiGroup Campaign
@apiVersion 1.0.0
@apiName CreateCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header


@apiParam   (Body:)    {String}    source_id     ID của website/app đã chọn
@apiParam   (Body:)    {String}    source_type     Loại nguồn. Giá trị: <br/><code>WEBSITE</code>
                                                                        <br/><code>APP</code>
@apiParam   (Body:)    {String}    campaign_name     Tên chiến dịch
@apiParam   (Body:)    {String}    [campaign_description]     Mô tả chiến dịch
@apiParam   (Body:)    {ArrayString}    [master_campaign_id]     ID của master campaign.

@apiParamExample    {json}  Body:
{
	"source_id": "09d7e03b-a14e-4e1d-b487-ab22934f8437",
	"campaign_name": "Tên chiến dịch",
	"campaign_description": "Mô tả chiến dịch",
	"source_type": "WEBSITE",
	"master_campaign_id": "09d7e03b-a14e-4e1d-b487-ab22934f8437"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
	"code": 200,
	"data": {
		"campaign_description": "13123123123",
		"campaign_id": "731752b2-f30a-11ee-b4bc-581cf8f75567",
		"campaign_name": "Tên chiến dịch 123 1712293670",
		"campaign_name_raw": "ten chien dich 123 1712293670",
		"exit_page_sign_config": {},
		"exit_page_sign_status": false,
		"master_campaign_id": "09d7e03b-a14e-4e1d-b487-ab22934f8437",
		"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
		"source_id": "656fe718847c2b89c714b31a",
		"source_type": "WEBSITE",
		"status": "DRAFT",
		"activate_status": "WAIT",
		"created_time": "2024-04-05T05:07:50Z",
		"created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"updated_time": "2024-04-05T05:07:50Z",
		"updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
	},
	"lang": "vi",
	"message": "request thành công."
}
"""
************************************* Update Campaign ************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {put} /campaign/update Sửa chiến dịch
@apiGroup Campaign
@apiVersion 1.0.0
@apiName UpdateCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    source_id     ID của website/app đã chọn
@apiParam   (Body:)    {String}    source_type     Loại nguồn. Giá trị: <br/><code>WEBSITE</code>
                                                                        <br/><code>APP</code>
@apiParam   (Body:)    {String}    campaign_id     ID chiến dịch cần sửa
@apiParam   (Body:)    {String}    campaign_name     Tên chiến dịch
@apiParam   (Body:)    {String}    [campaign_description]     Mô tả chiến dịch
@apiParam   (Body:)    {String}    [master_campaign_id]     ID master campaign.
@apiParam   (Body:)    {String}    start_type   Loại thời gian bắt đầu.Giá trị: <br/><code>IMMEDIATELY</code>=Ngay lập tức
                                                                        <br/><code>AT_TIME</code>=Vào thời gian
@apiParam   (Body:)    {String}    start_time   Giá trị thời gian bắt đầu (Nếu start_type=AT_TIME). <br/>Định dạng: <code>%Y-%m-%dT%H:%MZ</code> (gửi lên theo giờ <code>UTC</code>)
@apiParam   (Body:)    {String}    end_type   Loại thời gian kết thúc.Giá trị: <br/><code>INFINITY</code>=Không có thời gian kết thúc
                                                                        <br/><code>AT_TIME</code>=Vào thời gian
@apiParam   (Body:)    {String}    end_time   Giá trị thời gian kết thúc (Nếu end_type=AT_TIME). <br/>Định dạng: <code>%Y-%m-%dT%H:%MZ</code> (gửi lên theo giờ <code>UTC</code>)
@apiParam   (Body:)    {Boolean}    exit_page_sign_status  Trạng thái bật tắt cấu hình gửi thông điệp khi profile có dấu hiệu thoát trang. Giá trị: <br/><code>true</code>=Bật
                                                                                                                                                    <br/><code>false</code>=Tắt
@apiParam   (Body:)    {Object}    [exit_page_sign_config]  Cấu hình gửi thông điệp khi profile có dấu hiệu thoát trang
@apiParam   (Body:)    {String}    exit_page_sign_config.page_type  Kiểu trang ghi nhận cấu hình.Giá trị: <br/><code>ANY_PAGE</code>=Trang bất kỳ trên website đã chọn
                                                                                                            <br/><code>SPECIFIC_PAGE</code>=Trang cụ thể trên website đã chọn
@apiParam   (Body:)    {ArrayObject}    [exit_page_sign_config.pages]  Danh sách trang cụ thể áp dụng cấu hình <code>exit_page_sign_config</code>. <br/>Chỉ áp dụng khi <code>exit_page_sign_config.page_type</code>=SPECIFIC_PAGE
@apiParam   (Body:)    {String}    exit_page_sign_config.pages.page_url     URL trang áp dụng cấu hình.
@apiParam   (Body:)    {String}    exit_page_sign_config.pages.popup_id     Mã thông điệp
@apiParam   (Body:)    {String}    exit_page_sign_config.pages.page_access_times_type   Kiểu lượt truy cập trang để áp dụng cấu hình. Giá trị: <br/><code>FROM_TO</code>=Kể từ lần truy cập
                                                                                                                                                <br/><code>SPECIFIC</code>=Vào lần truy cập
@apiParam   (Body:)    {Object}    exit_page_sign_config.pages.page_access_times_config     Cấu hình lượt truy cập trang để áp dụng cấu hình.
@apiParam   (Body:)    {Number}    exit_page_sign_config.pages.page_access_times_config.from     Giá trị Từ lượt truy cập. Áp dụng khi <code>page_access_times_type</code>=FROM_TO
@apiParam   (Body:)    {Number}    exit_page_sign_config.pages.page_access_times_config.to     Giá trị Đến lượt truy cập. Áp dụng khi <code>page_access_times_type</code>=FROM_TO
@apiParam   (Body:)    {Number}    exit_page_sign_config.pages.page_access_times_config.value     Giá trị lượt truy cập. Áp dụng khi <code>page_access_times_type</code>=SPECIFIC
@apiParam   (Body:)    {Number}    [exit_page_sign_config.popup_send_times]  Giá trị số lần gửi thông điệp.<br/>Chỉ áp dụng khi <code>exit_page_sign_config.page_type</code>=ANY_PAGE
@apiParam   (Body:)    {Number}    [exit_page_sign_config.popup_send_wait_value]  Giá trị số thời gian giữa các lần gửi thông điệp.<br/>Chỉ áp dụng khi <code>exit_page_sign_config.page_type</code>=ANY_PAGE
@apiParam   (Body:)    {String}    [exit_page_sign_config.popup_send_wait_unit]  Giá trị đơn vị thời gian giữa các lần gửi thông điệp. Giá trị: <br/><code>SECONDS</code>=Giây
                                                                                                                                                <br/><code>MINUTES</code>=Phút
                                                                                    <br/>Chỉ áp dụng khi <code>exit_page_sign_config.page_type</code>=ANY_PAGE
@apiParam   (Body:)    {Number}    [exit_page_sign_config.popup_id]  Mã thông điệp. <br/>Chỉ áp dụng khi <code>exit_page_sign_config.page_type</code>=ANY_PAGE


@apiParamExample    {json}  Body:
{
    "campaign_id": "0c95bda4-7bfb-11ee-83bf-38d57a786a3e",
    "campaign_name": "Tên chiến dịch",
    "campaign_description": "Mô tả chiến dịch",
    "source_id": "dafe92ee-9802-432c-8d40-6cdbd9870eb1",
    "source_type": "WEBSITE",
    "master_campaign_id": "eda9de3a-68ce-11ee-9159-38d57a786a3e",
    "start_type": "AT_TIME",
    "start_time": "2023-11-05T05:00Z",
    "end_type": "AT_TIME",
    "end_time": "2023-11-05T05:00Z",
    "exit_page_sign_status": true,
    "exit_page_sign_config": {
        "page_type": "SPECIFIC_PAGE",
        "pages": [
            {
                "page_url": "https://abc.com/xyz1",
                "page_access_times_type": "FROM_TO",
                "page_access_times_config": {
                    "from": 2,
                    "to": 5
                },
                "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff"
            },
            {
                "page_url": "https://abc.com/xyz2",
                "page_access_times_type": "SPECIFIC",
                "page_access_times_config": {
                    "value": 10
                },
                "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff"
            }
        ]
    },
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "campaign_id": "0c95bda4-7bfb-11ee-83bf-38d57a786a3e",
        "campaign_name": "Tên chiến dịch",
        "campaign_description": "Mô tả chiến dịch",
        "source_id": "d8cf9db4-77a7-11ee-8579-38d57a786a3e",
        "source_type": "WEBSITE",
        "master_campaign_id": "eda9de3a-68ce-11ee-9159-38d57a786a3e",
        "created_time": "2023-10-31T07:30:00Z",
        "created_user": "2da54f68-77c2-11ee-84f7-38d57a786a3e",
        "status": "DRAFT",
        "start_type": "AT_TIME",
        "start_time": "2023-11-05T05:00Z",
        "end_type": "AT_TIME",
        "end_time": "2023-11-05T05:00Z",
        "exit_page_sign_status": true,
        "exit_page_sign_config": {
            "page_type": "SPECIFIC_PAGE",
            "pages": [
                {
                    "page_url": "https://abc.com/xyz1",
                    "page_access_times_type": "FROM_TO",
                    "page_access_times_config": {
                        "from": 2,
                        "to": 5
                    },
                    "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff"
                },
                {
                    "page_url": "https://abc.com/xyz2",
                    "page_access_times_type": "SPECIFIC",
                    "page_access_times_config": {
                        "value": 10
                    },
                    "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff"
                }
            ]
        },
        "updated_time": "2023-10-31T07:30:00Z",
        "updated_user": "3a810414-77c2-11ee-9669-38d57a786a3e"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
************************************* Config Campaign ************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {put} /campaign/config Cấu hình chi tiết chiến dịch
@apiGroup Campaign
@apiVersion 1.0.0
@apiName ConfigCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    campaign_id     ID chiến dịch cần cấu hình
@apiParam   (Body:)    {Array}    [diagram]  Thông tin cấu hình dành cho Frontend sử dụng.
@apiParam   (Body:)    {ArrayObject}    nodes   Danh sách các khối của chiến dịch.
@apiParam   (Body:)    {String}    nodes.node_id  Mã định danh node
@apiParam   (Body:)    {String}    nodes.node_name   Tên node.
@apiParam   (Body:)    {String}    nodes.element_type  Nhóm các khối (Frontend sử dụng). Giá trị: <br/><code>OPERATION</code>=Thao tác<br/>
                                                                                                        <code>SPECIFIC_CONDITION</code>=Kiểm tra điều kiện<br/>
                                                                                                        <code>MESSAGE</code>=Thông điệp<br/>
@apiParam   (Body:)    {String}    nodes.node_type  Kiểu node. Giá trị: <br/><code>TARGET</code>=node đối tượng mục tiêu<br/>
                                                                            <code>CONDITION_EVENT</code>=node kiểm tra event<br/>
                                                                            <code>CONDITION_FILTER_PROFILE</code>=node kiểm tra thông tin profile<br/>
                                                                            <code>ACTION_WEBPUSH</code>=node webpush<br/>
                                                                            <code>WAIT</code>=node chờ<br/>
                                                                            <code>EXIT</code>=node thoát<br/>
@apiParam   (Body:)    {ArrayObject}    nodes.node_connection   Thông tin liên kết node.
@apiParam   (Body:)    {String}    nodes.node_connection.next_node_id   Mã định danh node kế tiếp
@apiParam   (Body:)    {ArrayString}    nodes.node_connection.previous_node_id   Mã định danh node trước đó.
@apiParam   (Body:)    {Object}    [nodes.node_connection.result]  Kết quả tương ứng để rẽ nhánh khối điều kiện
@apiParam   (Body:)    {Object}    [nodes.node_config]  Thông tin cấu hình chi tiết của node.

@apiParamExample    {json}  Body:
{
	"nodes": [
		{
			"node_id": "319302d2-2e15-11ef-8b45-fdb37d6e520b",
			"element_type": "TRIGGER",
			"node_type": "TARGET",
			"node_name": "Đối tượng mục tiêu",
			"node_connection": [
				{
					"next_node_id": "ec482a0d-05d0-4598-b642-72306b3eefc0",
					"previous_node_id": []
				}
			],
			"node_config": {
				"trigger": {
					"audience_id": "8482e63c-3de1-4a59-ba34-9f10f3de9750",
					"triggers": [
						{
							"trigger_id": "13188404429416341",
							"event_key": "cri_olap_pc_bam_vao_user_more_1717746905",
							"trigger_event_key": "bam_vao_user_more_1717746905",
							"audience_operator": "and"
						},
						{
							"trigger_id": "13188404429416341",
							"event_key": "cri_olap_pc_bam_vao_user_more_1717746905",
							"trigger_event_key": "bam_vao_user_more_1717746905",
							"audience_operator": "and"
						},
						{
							"trigger_id": "64332179799942065",
							"event_key": "cri_olap_pc_bam_vao_icon_easy_layout_1714706432",
							"trigger_event_key": "bam_vao_icon_easy_layout_1714706432",
							"audience_operator": "exclude"
						},
						{
							"trigger_id": "50339674228822513",
							"event_key": "cri_olap_pc_bam_vao_sign_in1_1716344306",
							"trigger_event_key": "bam_vao_sign_in1_1716344306",
							"audience_operator": "exclude"
						}
					]
				},
				"profile_target_type": "ALL",
				"waiting_time_after_primary_value": 1,
				"waiting_time_after_primary_unit": "MINUTES",
				"profile_audience_id": "662e360f-d4de-4bbf-b289-843686cc6833"
			}
		},
		{
			"node_id": "ec482a0d-05d0-4598-b642-72306b3eefc0",
			"element_type": "SPECIFIC_CONDITION",
			"node_type": "CONDITION_EVENT",
			"node_name": "p",
			"node_connection": [
				{
					"result": "yes",
					"next_node_id": "b1fc8371-6c4f-43f2-b61f-061751e442b9",
					"previous_node_id": [
						"319302d2-2e15-11ef-8b45-fdb37d6e520b"
					]
				},
				{
					"result": "no",
					"next_node_id": "b8375cb8-4b50-4e81-a9d7-a1af393a8422",
					"previous_node_id": [
						"319302d2-2e15-11ef-8b45-fdb37d6e520b"
					]
				}
			],
			"node_config": {
				"capture_profile_exit": false,
				"waiting_time_after_primary_value": 1,
				"waiting_time_after_primary_unit": "MINUTES",
				"trigger": {
					"audience_id": "2695d599-08d7-4f48-86b2-27dde5528d46",
					"triggers": [
						{
							"trigger_id": "13188404429416341",
							"event_key": "cri_olap_pc_bam_vao_user_more_1717746905"
						},
						{
							"trigger_id": "50339674228822513",
							"event_key": "cri_olap_pc_bam_vao_sign_in1_1716344306"
						}
					]
				}
			}
		},
		{
			"node_id": "b1fc8371-6c4f-43f2-b61f-061751e442b9",
			"element_type": "MESSAGE",
			"node_type": "ACTION_WEBPUSH",
			"node_name": "p9",
			"node_connection": [
				{
					"next_node_id": "31930521-2e15-11ef-a98c-fdb37d6e520b",
					"previous_node_id": [
						"ec482a0d-05d0-4598-b642-72306b3eefc0"
					]
				}
			],
			"node_config": {
				"popup_id": "66694744e1cfe1fe72df505b",
				"popup_position": "tc"
			}
		},
		{
			"node_id": "31930521-2e15-11ef-a98c-fdb37d6e520b",
			"element_type": "EXIT",
			"node_type": "EXIT",
			"node_name": "Kết thúc hành trình 3",
			"node_connection": [
				{
					"next_node_id": "",
					"previous_node_id": [
						"b1fc8371-6c4f-43f2-b61f-061751e442b9"
					]
				}
			],
			"node_config": {}
		},
		{
			"node_id": "b8375cb8-4b50-4e81-a9d7-a1af393a8422",
			"element_type": "EXIT",
			"node_type": "EXIT",
			"node_name": "Kết thúc hành trình 4",
			"node_connection": [
				{
					"next_node_id": "",
					"previous_node_id": [
						"ec482a0d-05d0-4598-b642-72306b3eefc0"
					]
				}
			]
		}
	],
	"diagram": {
		"nodes": [
			{
				"code": "TARGET",
				"config": {
					"trigger": {
						"audience_id": "8482e63c-3de1-4a59-ba34-9f10f3de9750",
						"triggers": [
							{
								"trigger_id": "13188404429416341",
								"event_key": "cri_olap_pc_bam_vao_user_more_1717746905",
								"trigger_event_key": "bam_vao_user_more_1717746905",
								"audience_operator": "and"
							},
							{
								"trigger_id": "13188404429416341",
								"event_key": "cri_olap_pc_bam_vao_user_more_1717746905",
								"trigger_event_key": "bam_vao_user_more_1717746905",
								"audience_operator": "and"
							},
							{
								"trigger_id": "64332179799942065",
								"event_key": "cri_olap_pc_bam_vao_icon_easy_layout_1714706432",
								"trigger_event_key": "bam_vao_icon_easy_layout_1714706432",
								"audience_operator": "exclude"
							},
							{
								"trigger_id": "50339674228822513",
								"event_key": "cri_olap_pc_bam_vao_sign_in1_1716344306",
								"trigger_event_key": "bam_vao_sign_in1_1716344306",
								"audience_operator": "exclude"
							}
						]
					},
					"profile_target_type": "ALL",
					"waiting_time_after_primary_value": 1,
					"waiting_time_after_primary_unit": "MINUTES",
					"profile_audience_id": "662e360f-d4de-4bbf-b289-843686cc6833"
				},
				"element_type": "TRIGGER",
				"id": "319302d2-2e15-11ef-8b45-fdb37d6e520b",
				"name": "Đối tượng mục tiêu",
				"next_id": "ec482a0d-05d0-4598-b642-72306b3eefc0",
				"pre_id": null,
				"specific_counter_current_code": 0,
				"specific_height": 48,
				"specific_init": false,
				"specific_width": 165,
				"specific_x": 709,
				"specific_y": 97,
				"error": false,
				"elementSvg": {},
				"attributeSvgD": "M 791.5 97 l 0 108",
				"position_end": 205,
				"translateX": 0,
				"translateY": 0
			},
			{
				"element_type": "SPECIFIC_CONDITION",
				"code": "CONDITION_EVENT",
				"name": "p",
				"isModeDev": false,
				"specific_width": 165,
				"specific_height": 48,
				"specific_x": 709,
				"specific_y": 205,
				"specific_init": false,
				"specific_counter_current_code": 1,
				"branches": [
					{
						"elements": [
							{
								"element_type": "MESSAGE",
								"code": "ACTION_WEBPUSH",
								"name": "p9",
								"isModeDev": false,
								"specific_width": 165,
								"specific_height": 48,
								"specific_x": 610.5,
								"specific_y": 357,
								"specific_init": false,
								"specific_counter_current_code": 1,
								"pre_id": "ec482a0d-05d0-4598-b642-72306b3eefc0",
								"next_id": "31930521-2e15-11ef-a98c-fdb37d6e520b",
								"id": "b1fc8371-6c4f-43f2-b61f-061751e442b9",
								"error": false,
								"elementSvg": {},
								"attributeSvgD": "M 693 357 l 0 108",
								"position_end": 465,
								"config": {
									"popup_id": "66694744e1cfe1fe72df505b",
									"popup_position": "tc"
								}
							},
							{
								"code": "EXIT",
								"config": {},
								"element_type": "EXIT",
								"id": "31930521-2e15-11ef-a98c-fdb37d6e520b",
								"name": "Kết thúc hành trình",
								"next_id": "",
								"pre_id": "b1fc8371-6c4f-43f2-b61f-061751e442b9",
								"specific_counter_current_code": 0,
								"specific_height": 48,
								"specific_init": true,
								"specific_width": 165,
								"specific_x": 610.5,
								"specific_y": 465,
								"error": false,
								"elementSvg": {},
								"attributeSvgD": "M 693 465 l 0 24",
								"position_end": 573,
								"translateX": 0,
								"translateY": 0
							}
						],
						"info": {
							"specific_width": 184
						},
						"config": {
							"position": 0,
							"next_id": ""
						},
						"onTheSide": "above",
						"elementSvg": {},
						"widthBranch": {
							"width": 165,
							"above": 82.5,
							"under": 82.5
						},
						"positionMaxLeft": 693,
						"attributeSvgD": "M 791.5 205 L 791.5 259 M 791.5,259 Q 791.5,269 781.5,269 L 703 269 Q 693,269 693,279 L 693 357",
						"specific_start_branch_x": 693,
						"specific_start_branch_y": 279
					},
					{
						"elements": [
							{
								"element_type": "EXIT",
								"code": "EXIT",
								"name": "Kết thúc hành trình",
								"hasLine": false,
								"isModeDev": false,
								"specific_width": 165,
								"specific_height": 48,
								"specific_x": 807.5,
								"specific_y": 357,
								"specific_init": true,
								"specific_counter_current_code": 0,
								"pre_id": "ec482a0d-05d0-4598-b642-72306b3eefc0",
								"next_id": "",
								"id": "b8375cb8-4b50-4e81-a9d7-a1af393a8422",
								"error": false,
								"elementSvg": {},
								"attributeSvgD": "M 890 357 l 0 24",
								"position_end": 465,
								"translateX": 0,
								"translateY": 0
							}
						],
						"info": {
							"specific_width": 184
						},
						"config": {
							"position": 1,
							"next_id": ""
						},
						"onTheSide": "under",
						"elementSvg": {},
						"widthBranch": {
							"width": 165,
							"above": 82.5,
							"under": 82.5
						},
						"positionMaxLeft": 890,
						"attributeSvgD": "M 791.5 205 L 791.5 259 M 791.5,259 Q 791.5,269 801.5,269 L 880 269 Q 890,269 890,279 L 890 357",
						"specific_start_branch_x": 890,
						"specific_start_branch_y": 279
					}
				],
				"pre_id": "319302d2-2e15-11ef-8b45-fdb37d6e520b",
				"next_id": "",
				"id": "ec482a0d-05d0-4598-b642-72306b3eefc0",
				"error": false,
				"maxHeightBranch": 216,
				"position_end_branch": 661,
				"config": {
					"capture_profile_exit": false,
					"waiting_time_after_primary_value": 1,
					"waiting_time_after_primary_unit": "MINUTES",
					"trigger": {
						"audience_id": "2695d599-08d7-4f48-86b2-27dde5528d46",
						"triggers": [
							{
								"trigger_id": "13188404429416341",
								"event_key": "cri_olap_pc_bam_vao_user_more_1717746905"
							},
							{
								"trigger_id": "50339674228822513",
								"event_key": "cri_olap_pc_bam_vao_sign_in1_1716344306"
							}
						]
					}
				},
				"translateX": 0,
				"translateY": 0
			}
		]
	},
	"campaign_id": "29917156844712382"
}

@apiParam   (TARGET node_config:)    {Dict}    trigger  Trigger event
@apiParam   (TARGET node_config:)    {String}    trigger.audience_id  ID Audience
@apiParam   (TARGET node_config:)    {ArrayObject}    trigger.triggers  Danh sách trigger đang sử dụng
@apiParam   (TARGET node_config:)    {String}    trigger.triggers.trigger_id  ID trigger
@apiParam   (TARGET node_config:)    {String}    trigger.triggers.event_key  Trigger event key
@apiParam   (TARGET node_config:)    {Number}  waiting_time_after_primary_value  Giá trị thời gian chờ sau event đầu tiên
@apiParam   (TARGET node_config:)    {Number}  waiting_time_after_primary_unit  Đơn vị thời gian chờ sau event đầu tiên. Giá trị: <br/><code>HOURS</code>=Giờ
                                                                                                                                    <br/><code>MINUTES</code>=Phút
@apiParam   (TARGET node_config:)    {String}    profile_target_type     Đối tượng mục tiêu. Giá trị: <br/><code>ALL</code>=Mọi người dùng trên website
                                                                        <br/><code>SPECIFIC_CONDITION</code>=Profile thỏa mãn điều kiện được chọn
@apiParam   (TARGET node_config:)    {String}    [profile_audience_id]  ID audience event <br/>Sử dụng khi <code>profile_target_type</code> = <code>SPECIFIC_CONDITION</code>


@apiParamExample    {json}  TARGET node_config:
{
    "trigger": {
        "audience_id": "8de00cc2-d627-11ee-8c91-38d57a786a3e"
    },
    "waiting_time_after_primary_value": 60,
    "waiting_time_after_primary_unit": "MINUTES",
    "profile_target_type": "SPECIFIC_CONDITION",
    "profile_audience_id": "e24aeb9e-d627-11ee-bd41-38d57a786a3e"
}

@apiParam   (CONDITION_EVENT node_config:)    {Dict}    trigger  Trigger event
@apiParam   (CONDITION_EVENT node_config:)    {String}    trigger.audience_id  ID Audience
@apiParam   (CONDITION_EVENT node_config:)    {ArrayObject}    trigger.triggers  Danh sách trigger đang sử dụng
@apiParam   (CONDITION_EVENT node_config:)    {String}    trigger.triggers.trigger_id  ID trigger
@apiParam   (CONDITION_EVENT node_config:)    {String}    trigger.triggers.event_key  Trigger event key
@apiParam   (CONDITION_EVENT node_config:)    {Number}  waiting_time_after_primary_value  Giá trị thời gian chờ sau event đầu tiên
@apiParam   (CONDITION_EVENT node_config:)    {Number}  waiting_time_after_primary_unit  Đơn vị thời gian chờ sau event đầu tiên. Giá trị: <br/><code>HOURS</code>=Giờ
                                                                                                                                    <br/><code>MINUTES</code>=Phút
@apiParam   (CONDITION_EVENT node_config:)    {Number}  waiting_time_after_primary_value  Giá trị thời gian chờ sau event đầu tiên
@apiParam   (CONDITION_EVENT node_config:)    {Boolean}  capture_profile_exit  Ghi nhân sự kiện profile thoát trang. Giá trị: true/false

@apiParamExample    {json}  CONDITION_EVENT node_config:
{
    "trigger":{
        "audience_id": "8de00cc2-d627-11ee-8c91-38d57a786a3e"
    },
    "waiting_time_after_primary_value": 60,
    "waiting_time_after_primary_unit": "MINUTES",
    "capture_profile_exit": true
}

@apiParam   (CONDITION_FILTER_PROFILE node_config:)    {String}    profile_target_type     Đối tượng mục tiêu. Giá trị: <br/><code>ALL</code>=Mọi người dùng trên website
                                                                        <br/><code>SPECIFIC_CONDITION</code>=Profile thỏa mãn điều kiện được chọn
@apiParam   (CONDITION_FILTER_PROFILE node_config:)    {String}    [profile_audience_id]  ID audience event. <br/>Sử dụng khi <code>profile_target_type</code> = <code>SPECIFIC_CONDITION</code>
@apiParamExample    {json}  CONDITION_FILTER_PROFILE node_config:
{
    "profile_target_type": "SPECIFIC_CONDITION",
    "profile_audience_id": "0c95bda4-7bfb-11ee-83bf-38d57a786a3e"
}

@apiParam   (WAIT node_config:)    {String}    wait_type     Kiểu chờ. Giá trị: <br/><code>AFTER_TIME</code>=Sau khoảng thời gian
                                                                        <br/><code>SPECIFIC_DATETIME</code>=Đến thời điểm cụ thể
@apiParam   (WAIT node_config:)    {Number}    wait_time_value  Giá trị lượng thời gian cần chờ. <br/> Áp dụng khi <code>wait_type</code>=AFTER_TIME
@apiParam   (WAIT node_config:)    {String}    wait_time_unit  Đơn vị lượng thời gian cần chờ. Giá trị: <br/><code>MINUTES</code>=Phút
                                                                                                        <br/><code>HOURS</code>=Giờ
                                                                                                        <br/><code>DAYS</code>=Ngày
                                                                <br/> Áp dụng khi <code>wait_type</code>=AFTER_TIME
@apiParam   (WAIT node_config:)    {String}    wait_datetime_value  Giá trị ngày giờ. Định dạng: <code>%d/%m/%Y %H:%M</code> <br/> Áp dụng khi <code>wait_type</code>=SPECIFIC_DATETIME
@apiParamExample    {json}  WAIT node_config:
{
    "wait_type": "AFTER_TIME/SPECIFIC_DATETIME",
    "wait_time_value": 2,
    "wait_time_unit": "HOURS",
    "wait_datetime_value": "29/02/2024 07:00"
}

@apiParam   (ACTION_WEBPUSH node_config:)    {String}    popup_id     Mã popup
@apiParam   (ACTION_WEBPUSH node_config:)    {String}    popup_position  Vị trí popup hiển thị
@apiParamExample    {json}  ACTION_WEBPUSH node_config:
{
    "popup_id": "51c7359a-df97-4e1a-af6b-c91a2c95c711",
    "popup_position": "lt"
}

@apiErrorExample  Lỗi trigger không khả dụng [HTTP/1.1 400]
{
  "code": 400,
  "data": {
    "error_node_ids": ["50339674228822513"]
  }
  "message": "Trigger ... not ready to use."
}

"""
************************************* Delete Campaign ************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {put} /campaign/delete Xóa chiến dịch
@apiGroup Campaign
@apiVersion 1.0.0
@apiName DeleteCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    campaign_id     ID chiến dịch cần xóa

@apiParamExample    {json}  Body:
{
    "campaign_id": "0c95bda4-7bfb-11ee-83bf-38d57a786a3e",
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
************************************* Paused Campaign ************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {put} /campaign/paused Tạm dừng chiến dịch
@apiGroup Campaign
@apiVersion 1.0.0
@apiName PausedCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    campaign_id     ID chiến dịch cần tạm dừng

@apiParamExample    {json}  Body:
{
    "campaign_id": "0c95bda4-7bfb-11ee-83bf-38d57a786a3e",
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
*********************************** Activate Campaign ************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {put} /campaign/activate Kích hoạt chiến dịch
@apiGroup Campaign
@apiVersion 1.0.0
@apiName ActivateCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    campaign_id     ID chiến dịch cần kích hoạt


@apiParamExample    {json}  Body:
{
    "campaign_id": "0c95bda4-7bfb-11ee-83bf-38d57a786a3e",
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
*********************************** Draft Campaign ***************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {put} /campaign/draft Lưu nháp chiến dịch
@apiGroup Campaign
@apiVersion 1.0.0
@apiName DraftCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    campaign_id     ID chiến dịch cần cấu hình
@apiParam   (Body:)    {String}    [start_type]   Loại thời gian bắt đầu.Giá trị: <br/><code>IMMEDIATELY</code>=Ngay lập tức
                                                                        <br/><code>AT_TIME</code>=Vào thời gian
@apiParam   (Body:)    {String}    [start_time]   Giá trị thời gian bắt đầu (Nếu start_type=AT_TIME). <br/>Định dạng: <code>%Y-%m-%dT%H:%MZ</code> (gửi lên theo giờ <code>UTC</code>)
@apiParam   (Body:)    {String}    [end_type]   Loại thời gian kết thúc.Giá trị: <br/><code>INFINITY</code>=Không có thời gian kết thúc
                                                                        <br/><code>AT_TIME</code>=Vào thời gian
@apiParam   (Body:)    {String}    [end_time]   Giá trị thời gian kết thúc (Nếu end_type=AT_TIME). <br/>Định dạng: <code>%Y-%m-%dT%H:%MZ</code> (gửi lên theo giờ <code>UTC</code>)
@apiParam   (Body:)    {Boolean}    [exit_page_sign_status]  Trạng thái bật tắt cấu hình gửi thông điệp khi profile có dấu hiệu thoát trang. Giá trị: <br/><code>true</code>=Bật
                                                                                                                                                    <br/><code>false</code>=Tắt
@apiParam   (Body:)    {Object}    [exit_page_sign_config]  Cấu hình gửi thông điệp khi profile có dấu hiệu thoát trang
@apiParam   (Body:)    {String}    exit_page_sign_config.page_type  Kiểu trang ghi nhận cấu hình.Giá trị: <br/><code>ANY_PAGE</code>=Trang bất kỳ trên website đã chọn
                                                                                                            <br/><code>SPECIFIC_PAGE</code>=Trang cụ thể trên website đã chọn
@apiParam   (Body:)    {ArrayObject}    [exit_page_sign_config.pages]  Danh sách trang cụ thể áp dụng cấu hình <code>exit_page_sign_config</code>. <br/>Chỉ áp dụng khi <code>exit_page_sign_config.page_type</code>=SPECIFIC_PAGE
@apiParam   (Body:)    {String}    exit_page_sign_config.pages.page_url     URL trang áp dụng cấu hình.
@apiParam   (Body:)    {String}    exit_page_sign_config.pages.popup_id     Mã thông điệp
@apiParam   (Body:)    {String}    exit_page_sign_config.pages.page_access_times_type   Kiểu lượt truy cập trang để áp dụng cấu hình. Giá trị: <br/><code>FROM_TO</code>=Kể từ lần truy cập
                                                                                                                                                <br/><code>SPECIFIC</code>=Vào lần truy cập
@apiParam   (Body:)    {Object}    exit_page_sign_config.pages.page_access_times_config     Cấu hình lượt truy cập trang để áp dụng cấu hình.
@apiParam   (Body:)    {Number}    exit_page_sign_config.pages.page_access_times_config.from     Giá trị Từ lượt truy cập. Áp dụng khi <code>page_access_times_type</code>=FROM_TO
@apiParam   (Body:)    {Number}    exit_page_sign_config.pages.page_access_times_config.to     Giá trị Đến lượt truy cập. Áp dụng khi <code>page_access_times_type</code>=FROM_TO
@apiParam   (Body:)    {Number}    exit_page_sign_config.pages.page_access_times_config.value     Giá trị lượt truy cập. Áp dụng khi <code>page_access_times_type</code>=SPECIFIC
@apiParam   (Body:)    {Number}    [exit_page_sign_config.popup_send_times]  Giá trị số lần gửi thông điệp.<br/>Chỉ áp dụng khi <code>exit_page_sign_config.page_type</code>=ANY_PAGE
@apiParam   (Body:)    {Number}    [exit_page_sign_config.popup_send_wait_value]  Giá trị số thời gian giữa các lần gửi thông điệp.<br/>Chỉ áp dụng khi <code>exit_page_sign_config.page_type</code>=ANY_PAGE
@apiParam   (Body:)    {String}    [exit_page_sign_config.popup_send_wait_unit]  Giá trị đơn vị thời gian giữa các lần gửi thông điệp. Giá trị: <br/><code>SECONDS</code>=Giây
                                                                                                                                                <br/><code>MINUTES</code>=Phút
                                                                                    <br/>Chỉ áp dụng khi <code>exit_page_sign_config.page_type</code>=ANY_PAGE
@apiParam   (Body:)    {Number}    [exit_page_sign_config.popup_id]  Mã thông điệp. <br/>Chỉ áp dụng khi <code>exit_page_sign_config.page_type</code>=ANY_PAGE


@apiParamExample    {json}  Body:
{
    "campaign_id": "0c95bda4-7bfb-11ee-83bf-38d57a786a3e",
    "start_type": "AT_TIME",
    "start_time": "2023-11-05T05:00Z",
    "end_type": "AT_TIME",
    "end_time": "2023-11-05T05:00Z",
    "exit_page_sign_status": true,
    "exit_page_sign_config": {
        "page_type": "SPECIFIC_PAGE",
        "pages": [
            {
                "page_url": "https://abc.com/xyz1",
                "page_access_times_type": "FROM_TO",
                "page_access_times_config": {
                    "from": 2,
                    "to": 5
                },
                "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff"
            },
            {
                "page_url": "https://abc.com/xyz2",
                "page_access_times_type": "SPECIFIC",
                "page_access_times_config": {
                    "value": 10
                },
                "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff"
            }
        ]
    }
}
"""
*********************************** Draft Config Campaign ********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {put} /campaign/config/draft Lưu nháp cấu hình chiến dịch
@apiGroup Campaign
@apiVersion 1.0.0
@apiName DraftConfigCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    campaign_id     ID chiến dịch cần cấu hình
@apiParam   (Body:)    {Array}    [diagram]  Thông tin cấu hình dành cho Frontend sử dụng.
@apiParam   (Body:)    {ArrayObject}    [nodes]   Danh sách các khối của chiến dịch.
@apiParam   (Body:)    {String}    nodes.node_id  Mã định danh node
@apiParam   (Body:)    {String}    nodes.node_name   Tên node.
@apiParam   (Body:)    {String}    nodes.element_type  Nhóm các khối (Frontend sử dụng). Giá trị: <br/><code>OPERATION</code>=Thao tác<br/>
                                                                                                        <code>SPECIFIC_CONDITION</code>=Kiểm tra điều kiện<br/>
                                                                                                        <code>MESSAGE</code>=Thông điệp<br/>
@apiParam   (Body:)    {String}    nodes.node_type  Kiểu node. Giá trị: <br/><code>TARGET</code>=node đối tượng mục tiêu<br/>
                                                                            <code>CONDITION_EVENT</code>=node kiểm tra event<br/>
                                                                            <code>CONDITION_FILTER_PROFILE</code>=node kiểm tra thông tin profile<br/>
                                                                            <code>ACTION_WEBPUSH</code>=node webpush<br/>
                                                                            <code>WAIT</code>=node chờ<br/>
                                                                            <code>EXIT</code>=node thoát<br/>
@apiParam   (Body:)    {Object}    nodes.node_connection   Thông tin liên kết node.
@apiParam   (Body:)    {String}    nodes.node_connection.next_node_id   Mã định danh node kế tiếp
@apiParam   (Body:)    {String}    nodes.node_connection.previous_node_id   Mã định danh node trước đó.
@apiParam   (Body:)    {Object}    [nodes.node_connection.result]  Kết quả tương ứng để rẽ nhánh khối điều kiện
@apiParam   (Body:)    {Object}    [nodes.node_config]  Thông tin cấu hình chi tiết của node.

@apiParamExample    {json}  Body:
{
    "campaign_id": "0c95bda4-7bfb-11ee-83bf-38d57a786a3e",
    "start_type": "AT_TIME",
    "start_time": "2023-11-05T05:00Z",
    "end_type": "AT_TIME",
    "end_time": "2023-11-05T05:00Z",
    "exit_page_sign_status": true,
    "exit_page_sign_config": {
        "page_type": "SPECIFIC_PAGE",
        "pages": [
            {
                "page_url": "https://abc.com/xyz1",
                "page_access_times_type": "FROM_TO",
                "page_access_times_config": {
                    "from": 2,
                    "to": 5
                },
                "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff"
            },
            {
                "page_url": "https://abc.com/xyz2",
                "page_access_times_type": "SPECIFIC",
                "page_access_times_config": {
                    "value": 10
                },
                "popup_id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff"
            }
        ]
    },
    "diagram": [],
    "nodes":[
        {
            "node_id": "7584fb4a-c6f5-11ed-8fe8-c7a3239de083",
            "node_type": "TARGET",
            "element_type": "OPERATION",
            "node_name": "Ten khoi"
            "node_config": {
            },
            "node_connection": [{
                "next_node_id": "84497070-c6f5-11ed-8fe8-c7a3239de083",
                "previous_node_id": "72d95c56-c6f5-11ed-8fe8-c7a3239de083"
            }]
        }
    ]
}

@apiParam   (TARGET node_config:)    {Dict}    trigger  Trigger event
@apiParam   (TARGET node_config:)    {String}    trigger.audience_id  ID Audience
@apiParam   (TARGET node_config:)    {ArrayObject}    trigger.triggers  Danh sách trigger đang sử dụng
@apiParam   (TARGET node_config:)    {String}    trigger.triggers.trigger_id  ID trigger
@apiParam   (TARGET node_config:)    {String}    trigger.triggers.event_key  Trigger event key
@apiParam   (TARGET node_config:)    {Number}  waiting_time_after_primary_value  Giá trị thời gian chờ sau event đầu tiên
@apiParam   (TARGET node_config:)    {Number}  waiting_time_after_primary_unit  Đơn vị thời gian chờ sau event đầu tiên. Giá trị: <br/><code>HOURS</code>=Giờ
                                                                                                                                    <br/><code>MINUTES</code>=Phút
@apiParam   (TARGET node_config:)    {String}    profile_target_type     Đối tượng mục tiêu. Giá trị: <br/><code>ALL</code>=Mọi người dùng trên website
                                                                        <br/><code>SPECIFIC_CONDITION</code>=Profile thỏa mãn điều kiện được chọn
@apiParam   (TARGET node_config:)    {String}    [profile_audience_id]  ID audience event <br/>Sử dụng khi <code>profile_target_type</code> = <code>SPECIFIC_CONDITION</code>


@apiParamExample    {json}  TARGET node_config:
{
    "trigger": {
        "audience_id": "8de00cc2-d627-11ee-8c91-38d57a786a3e"
    },
    "waiting_time_after_primary_value": 60,
    "waiting_time_after_primary_unit": "MINUTES",
    "profile_target_type": "SPECIFIC_CONDITION",
    "profile_audience_id": "e24aeb9e-d627-11ee-bd41-38d57a786a3e"
}

@apiParam   (CONDITION_EVENT node_config:)    {Dict}    trigger  Trigger event
@apiParam   (CONDITION_EVENT node_config:)    {String}    trigger.audience_id  ID Audience
@apiParam   (CONDITION_EVENT node_config:)    {ArrayObject}    trigger.triggers  Danh sách trigger đang sử dụng
@apiParam   (CONDITION_EVENT node_config:)    {String}    trigger.triggers.trigger_id  ID trigger
@apiParam   (CONDITION_EVENT node_config:)    {String}    trigger.triggers.event_key  Trigger event key
@apiParam   (CONDITION_EVENT node_config:)    {Number}  waiting_time_after_primary_value  Giá trị thời gian chờ sau event đầu tiên
@apiParam   (CONDITION_EVENT node_config:)    {Number}  waiting_time_after_primary_unit  Đơn vị thời gian chờ sau event đầu tiên. Giá trị: <br/><code>HOURS</code>=Giờ
                                                                                                                                    <br/><code>MINUTES</code>=Phút
@apiParam   (CONDITION_EVENT node_config:)    {Number}  waiting_time_after_primary_value  Giá trị thời gian chờ sau event đầu tiên
@apiParam   (CONDITION_EVENT node_config:)    {Boolean}  capture_profile_exit  Ghi nhân sự kiện profile thoát trang. Giá trị: true/false

@apiParamExample    {json}  CONDITION_EVENT node_config:
{
    "trigger":{
        "audience_id": "8de00cc2-d627-11ee-8c91-38d57a786a3e"
    },
    "waiting_time_after_primary_value": 60,
    "waiting_time_after_primary_unit": "MINUTES",
    "capture_profile_exit": true
}

@apiParam   (CONDITION_FILTER_PROFILE node_config:)    {String}    profile_target_type     Đối tượng mục tiêu. Giá trị: <br/><code>ALL</code>=Mọi người dùng trên website
                                                                        <br/><code>SPECIFIC_CONDITION</code>=Profile thỏa mãn điều kiện được chọn
@apiParam   (CONDITION_FILTER_PROFILE node_config:)    {String}    [profile_audience_id]  ID audience event. <br/>Sử dụng khi <code>profile_target_type</code> = <code>SPECIFIC_CONDITION</code>
@apiParamExample    {json}  CONDITION_FILTER_PROFILE node_config:
{
    "profile_target_type": "SPECIFIC_CONDITION",
    "profile_audience_id": "0c95bda4-7bfb-11ee-83bf-38d57a786a3e"
}

@apiParam   (WAIT node_config:)    {String}    wait_type     Kiểu chờ. Giá trị: <br/><code>AFTER_TIME</code>=Sau khoảng thời gian
                                                                        <br/><code>SPECIFIC_DATETIME</code>=Đến thời điểm cụ thể
@apiParam   (WAIT node_config:)    {Number}    wait_time_value  Giá trị lượng thời gian cần chờ. <br/> Áp dụng khi <code>wait_type</code>=AFTER_TIME
@apiParam   (WAIT node_config:)    {String}    wait_time_unit  Đơn vị lượng thời gian cần chờ. Giá trị: <br/><code>MINUTES</code>=Phút
                                                                                                        <br/><code>HOURS</code>=Giờ
                                                                                                        <br/><code>DAYS</code>=Ngày
                                                                <br/> Áp dụng khi <code>wait_type</code>=AFTER_TIME
@apiParam   (WAIT node_config:)    {String}    wait_datetime_value  Giá trị ngày giờ. Định dạng: <code>%d/%m/%Y %H:%M</code> <br/> Áp dụng khi <code>wait_type</code>=SPECIFIC_DATETIME
@apiParamExample    {json}  WAIT node_config:
{
    "wait_type": "AFTER_TIME/SPECIFIC_DATETIME",
    "wait_time_value": 2,
    "wait_time_unit": "HOURS",
    "wait_datetime_value": "29/02/2024 07:00"
}

@apiParam   (ACTION_WEBPUSH node_config:)    {String}    popup_id     Mã popup
@apiParam   (ACTION_WEBPUSH node_config:)    {String}    popup_position  Vị trí popup hiển thị
@apiParamExample    {json}  ACTION_WEBPUSH node_config:
{
    "popup_id": "51c7359a-df97-4e1a-af6b-c91a2c95c711",
    "popup_position": "lt"
}
"""
************************************* Copy Campaign **************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /campaign/copy Sao chép chiến dịch
@apiGroup Campaign
@apiVersion 1.0.0
@apiName CopyCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}    copy_campaign_id     Mã chiến dịch được sao chép
@apiParam   (Body:)    {String}    campaign_name     Tên chiến dịch
@apiParam   (Body:)    {String}    [campaign_description]     Mô tả chiến dịch
@apiParam   (Body:)    {String}    [master_campaign_id]     ID master campaign

@apiParamExample    {json}  Body:
{
    "copy_campaign_id": "5f391c40-90c8-11ee-802f-38d57a786a3e",
    "campaign_name": "Tên chiến dịch",
    "campaign_description": "Mô tả chiến dịch",
    "master_campaign_id": "eda9de3a-68ce-11ee-9159-38d57a786a3e"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "merchant_id": "37c90b9e-7bf8-11ee-ad32-38d57a786a3e",
        "campaign_id": "dbb50f6e-77a7-11ee-ac67-38d57a786a3e",
        "campaign_name": "Tên chiến dịch",
        "campaign_description": "Mô tả chiến dịch",
        "master_campaign_id": "41da52ca-7bf8-11ee-be4e-38d57a786a3e",
        "source_id": "d8cf9db4-77a7-11ee-8579-38d57a786a3e",
        "source_type": "WEBSITE/APP",
        "trigger_type": "PROFILE_VISIT/PROFILE_TRIGGER",
        "trigger_id": ["d4c3504c-77a7-11ee-b1e7-38d57a786a3e", "d6857d30-77a7-11ee-b08f-38d57a786a3e"],
        "trigger_operator": "AND/OR",
        "display_time_type": "IMMEDIATELY/AFTER_PERIOD_TIME",
        "display_time_value": {
            "time": 5,
            "unit": "hours/minutes/seconds"
        },
        "profile_target_type": "ALL/SPECIFIC_CONDITION",
        "audience_id": "b5a4-77a7-11ee-b5a4-38d57a786a3e",
        "popup_id": "257d843a-77bc-11ee-b5a4-38d57a786a3e",
        "popup_position": "lt",
        "start_type": "IMMEDIATELY/AT_TIME",
        "start_time": "2023-10-31T07:00Z",
        "end_type": "INFINITY/AT_TIME",
        "end_time": "2023-10-31T07:00Z",
        "loop_type": "NOT_LOOP/EVERY_SESSION",
        "status": "PROCESSING/SCHEDULED/FINISHED/PAUSED/DRAFT",
        "created_time": "2023-10-31T07:30:00Z",
        "created_user": "2da54f68-77c2-11ee-84f7-38d57a786a3e",
        "updated_time": "2023-10-31T07:30:00Z",
        "updated_user": "3a810414-77c2-11ee-9669-38d57a786a3e"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
********************* Queue ghi nhận profile thoát chiến dịch ****************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {queue} /[queue] Ghi nhận profile thoát chiến dịch
@apiGroup OnpageJourneyQueue
@apiVersion 1.0.0
@apiName QueueListenProfileExitCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (QueueName:)    {String}    topic_name  onpage_listen_profile_exit_campaign

@apiParam   (QueueData:)    {String}    profile_id  Mã profile
@apiParam   (QueueData:)    {String}    campaign_id  Mã chiến dịch
@apiParam   (QueueData:)    {String}    source_id  Website code
@apiParamExample    {json}  QueueData:
{
    "profile_id": "146940fa-0476-11ee-824a-38d57a786a3d",
    "campaign_id": "688e5c3a-33d8-41e8-82e3-e37e0a5e177c",
    "source_id": "ed5a7a0a-0c4a-11ef-9f3f-38d57a786a3e"
}
"""
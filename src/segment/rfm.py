#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Toandd
    Company: M O B I O
    Date Created: 12/06/2023
"""

################### DEFINE ##########################

# RFM paging tokens
"""
@apiDefine rfm_paging_tokens
@apiParam   (Query:)    {Number}    [per_page] Số item trên mỗi trang. Default = 10
@apiParam   (Query:)    {String}    [after_token] Token để yêu cầu next page.
@apiParam   (Query:)    {String}    [before_token] Token để yêu cầu page trước. <PERSON><PERSON> là trước khi xử lý nếu có <code>after_token</code> trong danh sach param <code>after_token</code>

@apiParam   (paging:)     {Object}    paging                        Pagination.
@apiParam   (paging:)     {Object}    paging.cursors                Cursors.
@apiParam   (paging:)     {String}    paging.cursors.after          Token cho page sau.
@apiParam   (paging:)     {String}    paging.cursors.before         Token cho page trước.
@apiParam   (paging:)     {Number}    paging.per_page               Số bản ghi 1 trang.
@apiParam   (paging:)     {Number}    paging.total_count            Tổng bản ghi.

@apiSuccessExample {json} paging
{
  "paging": {
    "cursors": {
      "after": "WzE2MzQxMTk4NTA0NzgsICJjX3JhdyM2MTY2YjA2ZTE2NzhkYjE1MjEwMmMxNmMiXQ==",
      "before": "XXX2MzQxMTk4NTA0NzgsICJjX3JhdyM2MTY2YjA2ZTE2NzhkYjE1MjEwMmMxNmMiXQ==",
    }
    "per_page": 10,
    "total_count": 105
  }
}
"""


# value calculate RFM
"""
@apiDefine value_calculate_rfm
@apiParam   (value_calculate_rfm_object:)     {Object}    f_value                       Giá trị số lần mua hàng
@apiParam   (value_calculate_rfm_object:)     {Number}    f_value.min                   giá trị sô lần mua hàng nhỏ nhất
@apiParam   (value_calculate_rfm_object:)     {Number}    f_value.max                   Giá trị số lần mua hàng lớn nhất
@apiParam   (value_calculate_rfm_object:)     {Object}    r_value                       Giá trị khoảng thời gian tương tác gần nhất
@apiParam   (value_calculate_rfm_object:)     {String}    r_value.min                   Ngày bắt đầu.
@apiParam   (value_calculate_rfm_object:)     {String}    r_value.max                   Ngày kết thúc.
@apiParam   (value_calculate_rfm_object:)     {Float}     m_average                     Giá trị trung bình mua hàng
@apiParam   (value_calculate_rfm_object:)     {String}    r_last_interactive            Ngày tương tác cuối cùng.

@apiParam   (value_calculate_rfm_object:)     {Integer}   total_profile                 Số lượng profile thỏa mãn
@apiParam   (value_calculate_rfm_object:)     {Integer}   total_reachable_profile       Reachable profile.
@apiParam   (value_calculate_rfm_object:)     {String}    bucket_code                   Bucket code
@apiParam   (value_calculate_rfm_object:)     {Integer}   segment_id                    segment id

@apiSuccessExample {json} value_calculate_rfm_object
{
    "f_value": {
        "max": 2,
        "min": 1
    },
    "r_value": {
        "min": "2023-04-20T08:18:44.1681953524Z",
        "max":  "2023-01-20T08:18:44.1681953524Z",
    } 
    "m_average": 3000,
    "r_last_interactive": "2023-07-27 08:41:04.448",
    "total_profile": 3000,
    "total_reachable_profile": 3000,
    "segment_id": 4080,
    "bucket_code": "ABOUT_TO_SLEEP",
}
"""


# RFM paging page and perpage
"""
@apiDefine rfm_paging_page_and_perpage

@apiParam                (paging:)                      {Object}                     paging                                    Pagination.
@apiParam                (paging:)                      {Number}                     paging.page                               Vị trí page
@apiParam                (paging:)                      {Number}                     paging.per_page                           Số bản ghi 1 trang.
@apiParam                (paging:)                      {Number}                     paging.total_items                        Tổng bản ghi.
@apiParam                (paging:)                      {Number}                     paging.total_page                         Tổng số page.

@apiSuccessExample {json} paging_page
{
  "paging": {
    "page": 1,
    "per_page": 10,
    "total_items": 105,
    "total_page": 11
  }
}
"""

# Define rfm category
"""
@apiDefine category_response
@apiParam   (category_object:)          {Integer}                       id                                         ID category
@apiParam   (category_object:)          {String}                        merchant_id                                ID merchant
@apiParam   (category_object:)          {String}                        name                                       tên category
@apiParam   (category_object:)          {String}                        status                                     Trạng thái 
@apiParam   (category_object:)          {Boolean}                       is_base                                    Có phải bộ chỉ số mặc định không
@apiParam   (category_object:)          {ArrayObject}                   r_score                                    Khoảng thời gian khách hàng mua gần nhất, Điểm càng cao thời gian mua hàng càng gần
@apiParam   (category_object:)          {ArrayObject}                   r_score.max_outliner                       Giới hạn trên
@apiParam   (category_object:)          {String}                        r_score.max_outliner.operator_key          key 
@apiParam   (category_object:)          {ArrayInt}                      r_score.max_outliner.values                Giá trị so sánh, tối đa 2 phần tử, nếu kiểu so sánh là "OP_IS_GREATER_EQUAL_AND_LESS" thì values có 2 phần tử là 2 đầu mút, các kiểu so sánh còn lại thì values có 1 phần tử
@apiParam   (category_object:)          {ArrayObject}                   r_score.min_outliner                     Giới hạn dưới
@apiParam   (category_object:)          {String}                        r_score.min_outliner.operator_key        key 
@apiParam   (category_object:)          {ArrayInt}                      r_score.min_outliner.values              giá trị so sánh, tối đa 2 phần tử, nếu kiểu so sánh là "OP_IS_GREATER_EQUAL_AND_LESS" thì values có 2 phần tử là 2 đầu mút, các kiểu so sánh còn lại thì values có 1 phần tử
@apiParam   (category_object:)          {ArrayObject}                   r_score.scores                             Điểm
@apiParam   (category_object:)          {String}                        r_score.scores.score                       Điểm, giá trị từ 1 > 5
@apiParam   (category_object:)          {String}                        r_score.scores.operator_key                key 
@apiParam   (category_object:)          {ArrayInt}                      r_score.scores.values                      giá trị so sánh, tối đa 2 phần tử, nếu kiểu so sánh là "OP_IS_GREATER_EQUAL_AND_LESS" thì values có 2 phần tử là 2 đầu mút, các kiểu so sánh còn lại thì values có 1 phần tử
@apiParam   (category_object:)          {ArrayObject}                   f_score                                    Tần xuất mua hàng của hách hàng mua gần nhất, Điểm càng cao thời gian mua hàng càng gần
@apiParam   (category_object:)          {Object}                        f_score.object                             cấu trúc giống r_core
@apiParam   (category_object:)          {ArrayObject}                   m_score                                    Giá trị giao dịch của hách hàng mua gần nhất, Điểm càng cao giá trị gao dịch càng lớn
@apiParam   (category_object:)          {Object}                        m_score.object                             cấu trúc giống r_core
@apiParam   (category_object:)          {ArrayObject}                   rf_bucket                                  (25 phần tử)
@apiParam   (category_object:)          {Integer}                       rf_bucket.x                                Tọa độ X
@apiParam   (category_object:)          {Integer}                       rf_bucket.y                                Tọa độ Y
@apiParam   (category_object:)          {String}                        rf_bucket.id                               mã code của bucket: <code>CHAMPIONS / LOYAL_CUSTOMERS / POTENTIAL_LOYALISTS / NEW_CUSTOMERS / PROMISING / NEED_ATTENTION / ABOUT_TO_SLEEP / AT_RISK / CANNOT_LOSE_THEM / HIBERNATING </code>
@apiParam   (category_object:)          {String}                        rf_bucket.color                            Mã màu
@apiParam   (category_object:)          {String}                        rf_bucket.label                            Tên hiển thị Bucket
@apiParam   (category_object:)          {ArrayObject}                  rm_bucket                                  
@apiParam   (category_object:)          {Object}                        rm_bucket.object                           giống rf_bucket
@apiParam   (category_object:)          {ArrayObject}                   fm_bucket                                  
@apiParam   (category_object:)          {Object}                        fm_bucket.object                           giống rf_bucket
@apiParam   (category_object:)          {ArrayObject}                   rfm_bucket                                 (125 phần tử)
@apiParam   (category_object:)          {Integer}                       rfm_bucket.x                               Tọa độ X
@apiParam   (category_object:)          {Integer}                       rfm_bucket.y                               Tọa độ y
@apiParam   (category_object:)          {Integer}                       rfm_bucket.z                               Tọa độ Z
@apiParam   (category_object:)          {String}                        rfm_bucket.id                               mã code của bucket: <code>CHAMPIONS / LOYAL_CUSTOMERS / POTENTIAL_LOYALISTS / NEW_CUSTOMERS / PROMISING / NEED_ATTENTION / ABOUT_TO_SLEEP / AT_RISK / CANNOT_LOSE_THEM / HIBERNATING </code>
@apiParam   (category_object:)          {String}                        rfm_bucket.color                            Mã màu
@apiParam   (category_object:)          {String}                        rfm_bucket.label                            Tên hiển thị Bucket
@apiParam   (category_object:)	        {Datetime}                      created_time	                            Thời gian bắt đầu ghi nhận thông tin <code>Định dạng: yyyy-MM-ddTHH:mm:ss.sZ </code>
@apiParam   (category_object:)	        {Datetime}                      updated_time	                                    Thời gian cập nhật thông tin <code>Định dạng: yyyy-MM-ddTHH:mm:ss.sZ </code>
@apiParam   (category_object:)	        {Object}                        fe_config	                                        Cấu hình bacnkend trả về  cho frontend
@apiParam   (category_object:)	        {ArrayObject}                   fe_config.setup_specific_m	                        Cấu hình trường đặc biệt
@apiParam   (category_object:)	        {String}                        fe_config.setup_specific_m.id_setup_specific_m	    Id bucket bị cắt
@apiParam   (category_object:)	        {Object}                        fe_config.setup_specific_m.new_bucket	            Cấu hình của trường đặc biệt. Giống cấu hình của các bucket khác 
@apiParam   (category_object:)	        {Integer}                       fe_config.setup_specific_m.m_value_setup	        Giá trị cắt
@apiParam   (category_object:)	        {String}                       type_analytic_model	                                Loại phân tích: RF, RFM




@apiSuccessExample {json} category_object
{
    "id":1,
    "name":"Tiêu chí RFM mặc định",
    "status":"ACTIVE",
    "merchant_id":"1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "is_base": True,
     "r_score": {
          "max_outliner": {
               "operator_key": "OP_IS_LESS_EQUAL",
               "values": [20]
          },
          "min_outliner": {
               "operator_key": "OP_IS_GREATER_EQUAL",
               "values": [1]
          },
          "scores": [{
                    "score": 1,
                    "operator_key": "OP_IS_GREATER_EQUAL",
                    "values": [13]
               },{
                    "score": 2,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [10, 12]
               },{
                    "score": 3,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [7, 9]
               },{
                    "score": 4,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [5, 6]
               },{
                    "score": 5,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [1, 4]
               }
          ]
     },
     "f_score": {
          "max_outliner": {
               "operator_key": "OP_IS_LESS_EQUAL",
               "values": [20]
          },
          "min_outliner": {
               "operator_key": "OP_IS_GREATER_EQUAL",
               "values": [1]
          },
          "scores": [
               {
                    "score": 1,
                    "operator_key": "OP_IS_LESS_EQUAL",
                    "values": [1]
               },{
                    "score": 2,
                    "operator_key": "OP_IS_LESS_EQUAL",
                    "values": [2]
               },{
                    "score": 3,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [3, 4]
               },{
                    "score": 4,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [5, 9]
               },{
                    "score": 5,
                    "operator_key": "OP_IS_GREATER_EQUAL",
                    "values": [10]
               }
          ]
     },
     "m_score": {},
     "rf_bucket": [
         {
             "x": 5,
             "y": 5,
             "id": "CHAMPIONS",
             "color": "#7abbf7",
             "label": "Champions"
         },
         {
             "x": 5,
             "y": 4,
             "id": "CHAMPIONS",
             "color": "#7abbf7",
             "label": "Champions"
         },
         {
             "x": 4,
             "y": 4,
             "id": "LOYAL_CUSTOMERS",
             "color": "#a19bae",
             "label": "Loyal Customers"
         },
         {
             "x": 4,
             "y": 5,
             "id": "LOYAL_CUSTOMERS",
             "color": "#a19bae",
             "label": "Loyal Customers"
         },
         {
             "x": 3,
             "y": 4,
             "id": "LOYAL_CUSTOMERS",
             "color": "#a19bae",
             "label": "Loyal Customers"
         },
         {
             "x": 3,
             "y": 5,
             "id": "LOYAL_CUSTOMERS",
             "color": "#a19bae",
             "label": "Loyal Customers"
         },
         {
             "x": 5,
             "y": 2,
             "id": "POTENTIAL_LOYALISTS",
             "color": "#78daa6",
             "label": "Potential Loyalists"
         },
         {
             "x": 5,
             "y": 3,
             "id": "POTENTIAL_LOYALISTS",
             "color": "#78daa6",
             "label": "Potential Loyalists"
         },
         {
             "x": 4,
             "y": 2,
             "id": "POTENTIAL_LOYALISTS",
             "color": "#78daa6",
             "label": "Potential Loyalists"
         },
         {
             "x": 4,
             "y": 3,
             "id": "POTENTIAL_LOYALISTS",
             "color": "#78daa6",
             "label": "Potential Loyalists"
         },
         {
             "x": 5,
             "y": 1,
             "id": "NEW_CUSTOMERS",
             "color": "#cce07f",
             "label": "New Customers"
         },
         {
             "x": 4,
             "y": 1,
             "id": "PROMISING",
             "color": "#dcc67d",
             "label": "Promising"
         },
         {
             "x": 3,
             "y": 3,
             "id": "NEED_ATTENTION ",
             "color": "#f8da8c",
             "label": "Need Attention"
         },
         {
             "x": 3,
             "y": 1,
             "id": "ABOUT_TO_SLEEP",
             "color": "#a2d8d9",
             "label": "About to sleep"
         },
         {
             "x": 3,
             "y": 2,
             "id": "ABOUT_TO_SLEEP",
             "color": "#a2d8d9",
             "label": "About to sleep"
         },
         {
             "x": 2,
             "y": 3,
             "id": "AT_RISK",
             "color": "#eea284",
             "label": "At Risk"
         },
         {
             "x": 2,
             "y": 4,
             "id": "AT_RISK",
             "color": "#eea284",
             "label": "At Risk"
         },
         {
             "x": 1,
             "y": 3,
             "id": "AT_RISK",
             "color": "#eea284",
             "label": "At Risk"
         },
         {
             "x": 1,
             "y": 4,
             "id": "AT_RISK",
             "color": "#eea284",
             "label": "At Risk"
         },
         {
             "x": 2,
             "y": 5,
             "id": "CANNOT_LOSE_THEM",
             "color": "#f78088",
             "label": "Cannot Lose Them"
         },
         {
             "x": 1,
             "y": 5,
             "id": "CANNOT_LOSE_THEM",
             "color": "#f78088",
             "label": "Cannot Lose Them"
         },{
            "x":2,
            "y":1,
            "id":"HIBERNATING",
            "color":"#c7d2de",
            "label":"Hibernatings"
        },
        {
            "x":2,
            "y":2,
            "id":"HIBERNATING",
            "color":"#c7d2de",
            "label":"Hibernatings"
        },
        {
            "x":1,
            "y":1,
            "id":"HIBERNATING",
            "color":"#c7d2de",
            "label":"Hibernatings"
        },
        {
            "x":1,
            "y":2,
            "id":"HIBERNATING",
            "color":"#c7d2de",
            "label":"Hibernatings"
        }
     ],
     "rm_bucket": [],
     "fm_bucket": [],
     "rfm_bucket": [],
     "created_time": "2023-04-20T08:18:44.1681953524Z",
     "updated_time": "2023-04-20T08:18:44.1681953524Z",
     "type_analytic_model": 'RFM'
     "fe_config": {
            "setup_specific_m": [
                {
                    "id_setup_specific_m": "CHAMPIONS",
                    "m_value_setup": 1,
                    "new_bucket": {
                        "color": "#f6a4cc",
                        "id": "PRICE_SENSITIVE",
                        "label": "Price Sensitive"
                    }
                },
                {
                    "id_setup_specific_m": "POTENTIAL_LOYALISTS",
                    "m_value_setup": 1,
                    "new_bucket": {
                        "color": "#f6a4cc",
                        "id": "PRICE_SENSITIVE",
                        "label": "Price Sensitive"
                    }
                },
                {
                    "id_setup_specific_m": "LOYAL_CUSTOMERS",
                    "m_value_setup": 1,
                    "new_bucket": {
                        "color": "#f6a4cc",
                        "id": "PRICE_SENSITIVE",
                        "label": "Price Sensitive"
                    }
                }
            ]
        }
}
"""

# ------------------------- Define RFM

"""
@apiDefine rfm_response
@apiParam          (rfm_object)             {Integer}                          id                  
@apiParam          (rfm_object)             {String}                           merchant_id                              id merchant
@apiParam          (rfm_object)             {String}                           name                                     Tên RFM
@apiParam          (rfm_object)             {String}                           keyword                                  Nó là tên đã lower case và bỏ dấu Tiếng Việt phục vụ cho search
@apiParam          (rfm_object)             {String}                           description                              mô tả
@apiParam          (rfm_object)             {String}                           status                                   trạng thái rfm
@apiParam          (rfm_object)             {String}                           analytic_object                          đối tượng phân tích
@apiParam          (rfm_object)             {Integer}                          segment_id                               id segment
@apiParam          (rfm_object)             {String}                           analytic_time                            thời gian phân tích: <ul>
                                                                                                                                                 <li>30_DAYS_AGO</li>
                                                                                                                                                 <li>THIS_MONTH</li>
                                                                                                                                                 <li>LAST_MONTH</li> 
                                                                                                                                                 <li>90_DAYS_AGO</li> 
                                                                                                                                                 <li>180_DAYS_AGO</li> 
                                                                                                                                                 <li>270_DAYS_AGO</li> 
                                                                                                                                                 <li>THIS_YEAR</li> 
                                                                                                                                                 <li>LAST_YEAR</li> 
                                                                                                                                                 <li>CUSTOM_TIME</li></ul>
@apiParam          (rfm_object)             {Datetime}                         start_time                               Thời gian bắt đầu ngày phân tích <code>Định dạng: %Y-%m-%dT%H:%M:%SZ</code>
@apiParam          (rfm_object)             {Datetime}                         end_time                                 Thời gian đến ngày phân tích <code>Định dạng: %Y-%m-%dT%H:%M:%SZ </code>
@apiParam          (rfm_object)             {String}                           analytic_model                           Mô hình phân tích <code> RF, RM, FM, RFM </code>
@apiParam          (rfm_object)             {Integer}                          category_id                              foreign_key với rfm_category
@apiParam          (rfm_object)             {String}                           audience_id                              id audience bộ lọc
@apiParam          (rfm_object)             {Integer}                          total_condition_filter                   số điều kiện bộ lọc
@apiParam          (rfm_object)             {Integer}                          total_profile                            Tổng số profile
@apiParam          (rfm_object)             {Integer}                          total_reachable_profile                  Tổng số profile có khả năng tiếp cận
@apiParam          (rfm_object)             {Integer}                          last_version                             version rfm cuối cùng
@apiParam          (rfm_object)             {Datetime}                         last_query_time                          Thời gian kết thúc tính toán (thành công hoặc lỗi) TH đang tính toán sẽ là null
@apiParam          (rfm_object)             {String}                           created_by                               Được tạo bởi ai
@apiParam          (rfm_object)             {String}                           updated_by                               Được update bởi ai
@apiParam          (rfm_object)             {Datetime}                         created_time                             Thời gian bắt đầu ghi nhận thông tin <code>Định dạng: yyyy-MM-ddTHH:mm:ss.sZ </code>
@apiParam          (rfm_object)             {Datetime}                         updated_time                             Thời gian cập nhật thông tin <code>Định dạng: yyyy-MM-ddTHH:mm:ss.sZ </code>
@apiParam          (rfm_object)             {Boolean}                          allow_analytic_monetary                  chọn phân tích monetary hay không
@apiParam          (rfm_object)             {String}                           type_analytic_monetaries                 Loại phân tích monetary: <ul>
                                                                                                                                                 <li>TOTAL_VALUE</li>
                                                                                                                                                 <li>AVERAGE_VALUE</li>
                                                                                                                                                 <li>GREATEST_VALUE</li> 
                                                                                                                                                 <li>SMALLEST_VALUE</li> 
                                                                                                                                                 <li>FIRST_VALUE</li>
                                                                                                                                                 <li>FINAL_VALUE</li></ul>
@apiParam          (rfm_object)             {String}                           analytic_event_r                         Event Recency
@apiParam          (rfm_object)             {String}                           analytic_event_f                         Event Frequency
@apiParam          (rfm_object)             {String}                           analytic_event_m                         Event Monetary
@apiParam          (rfm_object)	 	        {Datetime}                         [time_remain]	                        Thời điểm được refresh <code>Định dạng: yyyy-MM-ddTHH:mm:ss.sZ </code>
@apiParam          (rfm_object)	 	        {Datetime}                         start_time_calculate	                    Thời điểm bắt đầu tính toán



@apiSuccessExample {json} rfm_object
{
    "id": 9,
    "name": "rfm một",                   
    "keyword": "rfm mot",                
    "description": "mô tả",            
    "status": "PROCESSING",                 
    "analytic_object": "SEGMENT",   
    "segment_id": 2,            
    "analytic_event_r":  "TRANSACTION",     
    "analytic_event_f":  "TRANSACTION",        
    "analytic_event_m":  "TRANSACTION",        
    "analytic_time": "30_DAYS_AGO",
    "start_time": null,
    "end_time": null,
    "allow_analytic_monetary": False,
    "type_analytic_monetary": null,
    "analytic_model": "RF",        
    "category_id": 12,           
    "audience_id": "audience_id_1"          
    "total_condition_filter": 2,
    "total_profile": 3000,         
    "total_reachable_profile": 300
    "last_version": 1,
    "last_query_time": null,
    "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "created_time": "2023-04-20T08:18:44.1681953524Z",
    "updated_time": "2023-04-20T08:18:44.1681953524Z",
    "time_remain": "2023-04-20T06:18:44.1681953524Z",
    "start_time_calculate": "2023-04-20T06:18:44.1681953524Z",

}
"""


# ---------------------------------- Define RFM Version

"""
@apiDefine rfm_version_response
@apiParam          (rfm_version_object)             {Integer}                          id                                                                                               
@apiParam          (rfm_version_object)             {String}                           merchant_id                                id merchant
@apiParam          (rfm_version_object)             {Integer}                          rfm_id                                     id rfm
@apiParam          (rfm_version_object)             {Integer}                          version                                    version
@apiParam          (rfm_version_object)             {String}                           status                                     trạng thái version rfm
@apiParam          (rfm_version_object)             {Object}                           rfm_category                               Vì RFM category đc phép sửa nên cần lưu lại thông tin tại thời điểm tính toán
@apiParam          (rfm_version_object)             {Datetime}                         start_time                                 Thời gian bắt đầu ngày phân tích <code>Định dạng: %Y-%m-%dT%H:%M:%SZ </code>
@apiParam          (rfm_version_object)             {Datetime}                         end_time                                   Thời gian đến ngày phân tích <code>Định dạng: %Y-%m-%dT%H:%M:%SZ </code>
@apiParam          (rfm_version_object)             {Integer}                          total_profile                              Tổng số profile
@apiParam          (rfm_version_object)             {Integer}                          total_reachable_profile                    Tổng số profile có khả năng tiếp cận
@apiParam          (rfm_version_object)             {ArrayObject}                      rfm_segment                                Danh sách segment của rfm
@apiParam          (rfm_version_object)             {String}                           rfm_segment.bucket_code                    1 trong số 11 loại bucket (CHAMPIONS / LOYAL_CUSTOMERS / ...)
@apiParam          (rfm_version_object)             {Integer}                          rfm_segment.segment_id                     id segment
@apiParam          (rfm_version_object)             {Integer}                          rfm_segment.version                        version segment
@apiParam          (rfm_version_object)             {Datetime}                         pending_time                               Thời điểm bắt đầu chờ xử lý
@apiParam          (rfm_version_object)             {Datetime}                         processing_time                            Thời điểm bắt đầu xử lý
@apiParam          (rfm_version_object)             {Datetime}                         finish_time                                Thời điểm xử lý xong (không phân biệt lỗi hay thành công)
@apiParam          (rfm_version_object)             {Json}                             detail_error                               Lưu chi tiết lỗi
@apiParam          (rfm_version_object)             {Datetime}                         created_time                               Thời gian bắt đầu ghi nhận thông tin <code>Định dạng: yyyy-MM-ddTHH:mm:ss.sZ </code>
@apiParam          (rfm_version_object)             {Datetime}                         updated_time                               Thời gian cập nhật thông tin <code>Định dạng: yyyy-MM-ddTHH:mm:ss.sZ </code>

@apiSuccessExample {json} rfm_version_object
{
     "id": 9,
     "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
     "rfm_id": 132,
     "version": 1,
     "status": "FINISH",
     "rfm_category": {category_object},
     "start_time": null,
     "end_time": null,
     "total_profile": 33000,
     "total_reachable_profile": 300,
     "rfm_segment": [{
          "bucket_code": "bucket code",
          "segment_id": 1,
          "version": 2,     
          },{
          "bucket_code": "bucket code 2",
          "segment_id": 2,
          "version": 2,     
          },{
          "bucket_code": "bucket code 3",
          "segment_id": 3,
          "version": 2,     
          },
     ],
     "pending_time": "2023-06-20T08:18:44.1681953524Z",
     "processing_time": "2023-06-20T08:18:44.1681953524Z",
     "finish_time": "2023-07-20T08:18:44.1681953524Z"
     "created_time": "2023-04-20T08:18:44.1681953524Z",
     "updated_time": "2023-04-20T08:18:44.1681953524Z"
}
"""

# Define segment respone
"""
@apiDefine segment_response
@apiParam   (segment_object:)       {Integer}                       id                            ID segment
@apiParam   (segment_object:)       {String}                        merchant_id                     ID merchant
@apiParam   (segment_object:)       {String}                        name                            tên segment
@apiParam   (segment_object:)       {String}                        description                     Mô tả segment
@apiParam   (segment_object:)       {String}                        status                          Trạng thái segment
@apiParam   (segment_object:)       {String}                        audience_id                     Mã id bảng chứa các bộ loc
@apiParam   (segment_object:)       {Integer}                       total_profile                   Tổng số profile
@apiParam   (segment_object:)       {Integer}                       total_reachable_profile         Tổng số profile tiếp cận
@apiParam   (segment_object:)       {Integer}                       reachable_profile               Tổng số profile định danh tiếp cận
@apiParam   (segment_object:)       {Integer}                       reachable_non_profile           Tổng số non profile tiếp cận
@apiParam   (segment_object:)       {ArrayObject}                   channels                        Số lượng profile ở các kênh tương tương tác
@apiParam   (segment_object:)       {String}                        channels.type                   Tên của kênh tương tác
@apiParam   (segment_object:)       {Integer}                       channels.total_profile          Số lượng profile của kênh tương tác
@apiParam   (segment_object:)       {Boolean}                       channels.is_main_channel        Có phải kênh tương tác chính hay không
@apiParam   (segment_object:)       {Integer}                       last_version                    Version mới nhất
@apiParam   (segment_object:)       {Number}                        last_query_time                 Lần cuối truy suất dữ liệu
@apiParam   (segment_object:)       {Boolean}                       is_live_segment                 Có phải live segment không
@apiParam   (segment_object:)       {String}                        type                            Loại segment (STANDARD / SEGMENT_RFM)
@apiParam   (segment_object:)       {Boolean}                       is_hide                         Có ẩn hay không
@apiParam   (segment_object:)       {String}                        created_by                      ID người tạo
@apiParam   (segment_object:)       {String}                        updated_by                      ID người update 
@apiParam   (segment_object:)	 	 {Datetime}                      created_time	               Thời gian bắt đầu ghi nhận thông tin Công ty <code>Định dạng: yyyy-MM-ddTHH:mm:ss.sZ </code>
@apiParam   (segment_object:)	 	 {Datetime}                      updated_time	                Thời gian cập nhật thông tin Công ty <code>Định dạng: yyyy-MM-ddTHH:mm:ss.sZ </code>


@apiSuccessExample {json} segment_object
{
  "id": 9,
  "name": "segment7",
  "description": "test",
  "status": "FINISH",
  "audience_id": "audience_id_1",
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "reachable_non_profile": 0,
  "reachable_profile": 0,
  "total_profile": 0,
  "total_reachable_profile": 0,   
  "channels": [
         {
              "type": "WEBPUSH",
              "total_profile": 6000,
              "is_main_channel": True,
         },  
         {
              "type": "SMS",
              "total_profile": 4000,
              "is_main_channel": True,
         }, 
         {
              "type": "ZALO",
              "total_profile": 2000,
              "is_main_channel": False,
         }, 
  ],    
  "last_version": 1,
  "last_query_time": null,
  "is_live_segment": false,
  "type": "SEGMENT_RFM",
  "is_hide": false,
  "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
  "created_time": "2023-04-20T08:18:44.1681953524Z",
  "updated_time": "2023-04-20T08:18:44.1681953524Z"
}
"""


################### API ##########################

# ----- Danh sách bộ chỉ số RFM-------

"""
@api {GET} /api/v1.0/rfms/list-categories [DONE] Danh sách bộ chỉ số RFM
@apiDescription Danh sách bộ chỉ số RFM 
@apiGroup RFM
@apiVersion 1.0.0
@apiName Danh sách bộ chỉ số RFM 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse category_response

@apisuccess                   {Array{category_object}}                      data                                             Mảng danh sách bộ chỉ số RFM
@apisuccess                   {Integer}                                     code                                              Http code
@apisuccess                   {String}                                      lang                                              Ngôn ngữ
@apisuccess                   {String}                                      message                                           Message

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [category_object],
    "lang": "vi",
    "message": "request thành công."
}
"""


# ----- Danh sách RFM-------

"""
@api {POST} /api/v1.0/rfms/list [DONE] Danh sách RFM
@apiDescription Danh sách RFM 
@apiGroup RFM
@apiVersion 1.0.0
@apiName Danh sách RFM 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse rfm_response
@apiUse rfm_paging_page_and_perpage


@apisuccess                     {Array{rfm_object}}                             data                                             Mảng danh sách bộ chỉ số RFM
@apisuccess                     {Object[paging_page]}                           paging                                            Thông tin phân trang
@apisuccess                     {Integer}                                       code                                              Http code
@apisuccess                     {String}                                        lang                                              Ngôn ngữ
@apisuccess                     {String}                                        message                                           Message

@apiParam	(Body:)			    {Object}	                                    data
@apiParam	(Body:)			    {String}	                                    [data.search]			                    Tìm kiếm segment field trong "field_search", hiện đang search theo tên
@apiParam	(Body:)			    {Integer}	                                    [data.per_page]			                    Số item trên mỗi trang. Default = 30
@apiParam	(Body:)			    {Integer}	                                    [data.page]			                        page muốn lấy, mặc định là page = 1
@apiParam	(Body:)			    {String}	                                    [data.sort]			                        sắp xếp 1 trong các trường: name, total_profile, total_reachable_profile, start_time_calculate, last_query_time. Mặc định tự sắp xếp theo updated_info_time
@apiParam	(Body:)			    {String}	                                    [data.order]			                    sắp xếp theo asc, desc. Mặc định theo desc
@apiParam   (Body:)             {ArrayString}                                   [data.analytic_model]                       Model phân tích
@apiParam   (Body:)             {ArrayString}                                   [data.status]                               Filter theo Trạng thái RFM
@apiParam   (Body:)             {String}                                        [data.start_time]                           Filter theo phạm vi bắt đầu phân tích <code>Định dạng: %Y-%m-%dT%H:%M:%SZ</code>
@apiParam   (Body:)             {String}                                        [data.end_time]                             Filter theo phạm vi kết thúc phân tích <code>Định dạng: %Y-%m-%dT%H:%M:%SZ</code>


@apiParamExample {json} Body example
{
    "data": {
        "search": "segment",
        "per_page":0,
        "page": 0,
        "sort": "name",
        "order": "desc",
        "analytic_model": ["RFM"],
        "status": ["PROCESSING", "FINISH"],
        "start_time": "2023-04-20T08:18:44.1681953524Z",
        "end_time": "2023-04-20T08:18:44.1681953524Z"
    }
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [rfm_object],
    "paging": {paging_page},
    "lang": "vi",
    "message": "request thành công."
}
"""

# ----- Tạo cấu hình RFM -------
"""
@api {POST} /api/v1.0/rfms [DONE] Tạo mới RFM
@apiDescription Tạo mới RFM
@apiGroup RFM
@apiVersion 1.0.0
@apiName AddRFM

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse rfm_response

@apiParam                (Body:)             {Object}                 data                          
@apiParam                (Body:)             {String}                 data.name                                 Tên rfm
@apiParam                (Body:)             {String}                 [data.description]                        Mô tả
@apiParam                (Body:)             {String}                 data.analytic_object                      Đối tượng phân tích
@apiParam                (Body:)             {Integer}                [data.segment_id]                         Segment id
@apiParam                (Body:)             {String}                 data.analytic_event_r                     Phân tích Event Requency
@apiParam                (Body:)             {String}                 data.analytic_event_f                     Phân tích Event Frequency
@apiParam                (Body:)             {String}                 data.analytic_event_m                     Phân tích Event Monetary
@apiParam                (Body:)             {String}                 data.analytic_time                        Thời gian phân tích
@apiParam                (Body:)             {String}                 data.start_time                           Thời gian bắt đầu ngày phân tích
@apiParam                (Body:)             {String}                 data.end_time                             Thời gian đến ngày phân tích
@apiParam                (Body:)             {String}                 data.analytic_model                       Mô hình phân tích
@apiParam                (Body:)             {String}                 data.[audience_id]                        id audience bộ lọc
@apiParam                (Body:)             {Integer}                data.category_id                          id category
@apiParam                (Body:)             {Integer}                data.total_condition_filter               số điều kiện bộ lọc
@apiParam                (Body:)             {Boolean}                [data.allow_analytic_monetary]            Chọn phân tích monetary hay không
@apiParam                (Body:)             {String}                 [data.type_analytic_monetary]             Loại phân tích monetary, Nếu chọn phân tích monetary thì phải có

@apiParamExample {json} Body example
{
    "data": {
          "name": "segment rfm",
          "description": "mô tả",
          "audience_id": "12323",
          "segment_id": 23,
          "analytic_event_r": "TRANSACTION",
          "analytic_event_f": "TRANSACTION",
          "analytic_event_m": "TRANSACTION",
          "analytic_object": "SEGMENT",
          "analytic_time": "THIS_MONTH",
          "start_time": null,
          "end_time": null,
          "analytic_model": "RF",
          "category_id": 123,
          "total_condition_filter": 10,           
    }
}

@apisuccess       {Object{rfm_object}}                          data                                              RFM object
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {rfm_object},
    "lang": "vi",
    "message": "request thành công."
}
"""


# ********************************* Detail RFM ********************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /api/v1.0/rfms/<:rfm_id> [DONE] Detail RFM.
@apiDescription Detail RFM
@apiGroup RFM
@apiVersion 1.0.0
@apiName  Detail RFM


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse rfm_response

@apisuccess       {Object{rfm_response}}                         data                                              Detail RFM
@apisuccess       {Integer}                                      code                                              Http code
@apisuccess       {String}                                       lang                                              Ngôn ngữ
@apisuccess       {String}                                       message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {rfm_object}, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

# ********************************* List Version RFM ********************************
# *************************************************************************************
"""
@api {get} /api/v1.0/rfms/<:rfm_id>/versions [DONE] List Version RFM
@apiDescription  List Version RFM
@apiGroup RFM
@apiVersion 1.0.0
@apiName  List Version RFM

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse rfm_paging_tokens
@apiUse json_header
@apiUse merchant_id_header
@apiUse rfm_version_response
@apiUse category_response                                                       

@apisuccess       {Array{rfm_version_object}}                   data                                              D/s version rfm. version đầu tiên trong ds trả về là version mới nhất
@apisuccess       {Object[paging]}                              paging                                            Thông tin phân trang
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [rfm_version_object],
  "paging": {paging}
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ********************************* Detail Version RFM ********************************                                                                *
# *************************************************************************************
"""
@api {get} /api/v1.0/rfms/<:rfm_id>/versions/<:version> [DONE] Detail Version RFM.
@apiDescription Detail Version RFM
@apiGroup RFM
@apiVersion 1.0.0
@apiName  Detail Version RFM


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse rfm_version_response
@apiUse category_response 

@apisuccess       {Object{rfm_version_response}}                      data                                        Detail version RFM
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {rfm_version_response}, 
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ********************************* Reload RFM ********************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {put} /api/v1.0/rfms/<:rfm_id>/reload [DONE] Reload RFM.
@apiDescription Reload RFM
@apiGroup RFM
@apiVersion 1.0.0
@apiName  Reload RFM


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse rfm_response

@apisuccess       {Object[rfm_object]}                           data                                             RFM object
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message

@apiErrorExample HTTP/1.1 413:
HTTP/1.1 413 
{
  "error": "RFM can only be refreshed when the status is FINISH or ERROR"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {rfm_object}, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

# ********************************* List Segment In RFM ********************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /api/v1.0/rfms/<:rfm_id>/versions/<:version>/list-segment [DONE] List Segment In RFM.
@apiDescription List Segment In RFM
@apiGroup RFM
@apiVersion 1.0.0
@apiName  List Segment In RFM


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse segment_response

@apisuccess       {Object[segment_response]}                          data                                              Data
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
     "list_segment_rfm": [ // Mảng các segment thuộc rfm
          {segment_object},
          {segment_object},
          {segment_object},
     ]
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

# ********************************* Create segment from RFM bucket ********************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {post} /api/v1.0/rfms/<:rfm_id>/versions/<:version>/create-segment [DONE] Create segment from RFM bucket.
@apiDescription Create segment from RFM bucket
@apiGroup RFM
@apiVersion 1.0.0
@apiName  Create segment from RFM bucket


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse segment_response

@apiParam                (Body:)             {Interger}                 segment_id                          Id của segment
@apiParam                (Body:)             {Interger}                 segment_version                     Version của segment
@apiParam                (Body:)             {String}                   name                                Tên của segment 
@apiParam                (Body:)             {String}                   [description]       Mô tả segment

@apiParamExample {json} Body example
{
    "data": {
          "segment_id": 9, 
          "segment_version": 1,    
          "name": "Tên Segment tạo từ rfm bucket",    
          "description": "Mô tả của segment."
    }
}



@apisuccess       {Object[segment_response]}                    data                                              Data
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
     {segment_object},
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

########################### CONST ##########################


# **************************** API CONST ANALYTIC TIME ********************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /api/v1.0/rfms/const/analytic-time [DONE] Danh sách ANALYTIC TIME
@apiDescription Danh sách ANALYTIC TIME
@apiGroup CONST RFM
@apiVersion 1.0.0
@apiName  List analytic time


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apisuccess       {Object}                                      data                                              Data
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [
        {
            "code": "30_DAYS_AGO",
            "name": "30 ngày trước",
            "translate_key": "i18n_30_days_ago"
        },
        {
            "code": "THIS_MONTH",
            "name": "Tháng này",
            "translate_key": "i18n_this_month"
        },
        {
            "code": "LAST_MONTH",
            "name": "Tháng trước",
            "translate_key": "i18n_last_month"
        },
        {
            "code": "90_DAYS_AGO",
            "name": "90 ngày trước",
            "translate_key": "i18n_90_days_ago"
        },
        {
            "code": "CUSTOM_TIME",
            "name": "Tùy chỉnh thời gian",
            "translate_key": "i18n_custom_time"
        },
        {
            "code": "180_DAYS_AGO",
            "name": "180 ngày trước",
            "translate_key": "i18n_180_days_ago"
        },{
            "code": "270_DAYS_AGO",
            "name": "270 ngày trước",
            "translate_key": "i18n_270_days_ago"
        },
        {
            "code": "THIS_YEAR",
            "name": "Năm nay",
            "translate_key": "i18n_this_year_ago"
        },
        {
            "code": "LAST_YEAR",
            "name": "Năm trước",
            "translate_key": "i18n_last_year_ago"
        }
    ], 
  "lang": "vi", 
  "message": "request thành công."
}
"""


# **************************** API CONST ANALYTIC EVENT ********************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /api/v1.0/rfms/const/analytic-event [DONE] Danh sách ANALYTIC EVENT
@apiDescription Danh sách ANALYTIC EVENT
@apiGroup CONST RFM
@apiVersion 1.0.0
@apiName  List analytic event


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam               (analytic-event:)     {Boolean}                 allow_analytic_monetary                      Cho phép tính toán Monetary hay không
@apiParam               (analytic-event:)     {String}                  code                                         Mã Event   
@apiParam               (analytic-event:)     {String}                  name                                         Tên Event
@apiParam               (analytic-event:)     {String}                  translate_key                                key để dịch
@apiParam               (analytic-event:)     {ArrayObject}             type_analytic_monetaries                       Loại phân tích
@apiParam               (analytic-event:)     {String}                  type_analytic_monetaries.code                  Mã loại phân tích
@apiParam               (analytic-event:)     {String}                  type_analytic_monetaries.name                  Tên loại phân tích
@apiParam               (analytic-event:)     {String}                  type_analytic_monetaries.translate_key         key để dịch loại phân tích


@apisuccess       {Object}                                      data                                              Data
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "allow_analytic_monetary": true,
            "code": "TRANSACTION",
            "name": "Giao dịch",
            "translate_key": "i18n_transaction",
            "type_analytic_monetaries": [
                {
                    "code": "TOTAL_VALUE",
                    "name": "Tổng giá trị",
                    "translate_key": "i18n_total_value"
                },
                {
                    "code": "AVERAGE_VALUE",
                    "name": "Giá trị trung bình",
                    "translate_key": "i18n_average_value"
                },
                {
                    "code": "GREATEST_VALUE",
                    "name": "Giá trị lớn nhất",
                    "translate_key": "i18n_greatest_value"
                },
                {
                    "code": "SMALLEST_VALUE",
                    "name": "Giá trị nhỏ nhất",
                    "translate_key": "i18n_smallest_value"
                },
                {
                    "code": "FIRST_VALUE",
                    "name": "Giá trị đầu tiên",
                    "translate_key": "i18n_first_value"
                },
                {
                    "code": "FINAL_VALUE",
                    "name": "Giá trị cuối cùng",
                    "translate_key": "Giá trị cuối cùng"
                }
            ]
        },
        {
            "allow_analytic_monetary": true,
            "code": "E79_VIB_TRANSACTION",
            "name": "Thực hiện giao dịch",
            "translate_key": "i18n_e79_vib_transaction",
            "type_analytic_monetaries": [
                {
                    "code": "TOTAL_VALUE",
                    "name": "Tổng giá trị",
                    "translate_key": "i18n_total_value"
                },
                {
                    "code": "AVERAGE_VALUE",
                    "name": "Giá trị trung bình",
                    "translate_key": "i18n_average_value"
                },
                {
                    "code": "GREATEST_VALUE",
                    "name": "Giá trị lớn nhất",
                    "translate_key": "i18n_greatest_value"
                },
                {
                    "code": "SMALLEST_VALUE",
                    "name": "Giá trị nhỏ nhất",
                    "translate_key": "i18n_smallest_value"
                },
                {
                    "code": "FIRST_VALUE",
                    "name": "Giá trị đầu tiên",
                    "translate_key": "i18n_first_value"
                },
                {
                    "code": "FINAL_VALUE",
                    "name": "Giá trị cuối cùng",
                    "translate_key": "Giá trị cuối cùng"
                }
            ]
        },
        {
            "allow_analytic_monetary": true,
            "code": "E141_VIB_TRANSACTION_DEBIT",
            "name": "Phát sinh giao dịch ghi có",
            "translate_key": "i18n_e141_vib_transaction_debit",
            "type_analytic_monetaries": [
                {
                    "code": "TOTAL_VALUE",
                    "name": "Tổng giá trị",
                    "translate_key": "i18n_total_value"
                },
                {
                    "code": "AVERAGE_VALUE",
                    "name": "Giá trị trung bình",
                    "translate_key": "i18n_average_value"
                },
                {
                    "code": "GREATEST_VALUE",
                    "name": "Giá trị lớn nhất",
                    "translate_key": "i18n_greatest_value"
                },
                {
                    "code": "SMALLEST_VALUE",
                    "name": "Giá trị nhỏ nhất",
                    "translate_key": "i18n_smallest_value"
                },
                {
                    "code": "FIRST_VALUE",
                    "name": "Giá trị đầu tiên",
                    "translate_key": "i18n_first_value"
                },
                {
                    "code": "FINAL_VALUE",
                    "name": "Giá trị cuối cùng",
                    "translate_key": "Giá trị cuối cùng"
                }
            ]
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# **************************** API CONST ANALYTIC OBJECT ******************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /api/v1.0/rfms/const/analytic-object [DONE] Danh sách ANALYTIC OBJECT
@apiDescription Danh sách ANALYTIC OBJECT
@apiGroup CONST RFM
@apiVersion 1.0.0
@apiName  List analytic object


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apisuccess       {Object}                                      data                                              Data
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [
        {
            "code": "ALL_PROFILE",
            "name": "tất cả profile",
            "translate_key": "i18n_all_profile"
        },{
            "code": "SEGMENT",
            "name": "segment",
            "translate_key": "i18n_segment"
        }
    ], 
  "lang": "vi", 
  "message": "request thành công."
}
"""


# **************************** API CONST ANALYTIC MODEL ******************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /api/v1.0/rfms/const/analytic-model [DONE] Danh sách ANALYTIC MODEL
@apiDescription Danh sách ANALYTIC MODEL
@apiGroup CONST RFM
@apiVersion 1.0.0
@apiName  List analytic model


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apisuccess       {Object}                                      data                                              Data
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [
        {
            "code": "RF",
            "name": "RF",
            "translate_key": "i18n_rf"
        },{
            "code": "RM",
            "name": "RM",
            "translate_key": "i18n_rm"
        },{
            "code": "FM",
            "name": "FM",
            "translate_key": "i18n_fm"
        },{
            "code": "RFM",
            "name": "RFM",
            "translate_key": "i18n_rfm"
        }
    ], 
  "lang": "vi", 
  "message": "request thành công."
}
"""

# **************************** API CONST VALUE CONDITION ******************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /api/v1.0/rfms/const/value-condition [DONE] Danh sách VALUE CONDITION
@apiDescription Danh sách VALUE CONDITION
@apiGroup CONST RFM
@apiVersion 1.0.0
@apiName  List value condition


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apisuccess       {Object}                                      data                                              Data
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "code": "OP_IS_GREATER_EQUAL_AND_LESS",
            "name": "Lớn hơn hoặc bằng và nhỏ hơn",
            "translate_key": "i18n_greater_than_equal_and_less"
        },
        {
            "code": "OP_IS_LESS_EQUAL",
            "name": "Nhỏ hơn hoặc bằng",
            "translate_key": "i18n_less_than_or_equal"
        },
        {
            "code": "OP_IS_GREATER_EQUAL",
            "name": "Lớn hơn hoặc bằng",
            "translate_key": "i18n_greater_than_or_equal"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


# ********************************* Update RFM ********************************
# version: 1.0.0                                                               *
# ******************************************************************************
"""
@api {put} /api/v1.0/rfms/<:rfm_id> [DONE] Update RFM.
@apiDescription Update Segment
@apiGroup RFM
@apiVersion 1.0.0
@apiName  Update RFM


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse rfm_response

@apiParam           (Body:)                 {Object}                data                        
@apiParam           (Body:)                 {String}                data.name                        Tên rfm
@apiParam           (Body:)                 {String}                [data.description]               Mô tả rfm

@apiParamExample {json} Body example
{
    "data": {
        "name": "segment rfm",
        "description": "mô tả",  
    }
}
@apisuccess       {Object[rfm_object]}                          data                                              Data rfm
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {rfm_object}, 
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ********************************* Detail RFM category *******************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /api/v1.0/rfms/categories/<:category_id> [DONE] Detail RFM category.
@apiDescription Detail RFM Category
@apiGroup RFM
@apiVersion 1.0.0
@apiName  Detail RFM Category


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse category_response

@apisuccess       {Object{category_response}}                         data                                              Detail RFM Category
@apisuccess       {Integer}                                      code                                              Http code
@apisuccess       {String}                                       lang                                              Ngôn ngữ
@apisuccess       {String}                                       message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {category_response}, 
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ********************************* List Segment for RFM ******************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /api/v1.0/rfms/list-segment [DONE] List Segment.
@apiDescription List Segment
@apiGroup RFM
@apiVersion 1.0.0
@apiName  List Segment


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse rfm_paging_tokens
@apiUse json_header
@apiUse merchant_id_header
@apiUse segment_response

@apiParam	        (Query:)			{String}	                [search]			                              Tìm kiếm segment field trong "field_search", mặc định là tên 

@apisuccess       {Array{segment_response}}                         data                                              Detail RFM Category
@apisuccess       {Object[paging]}                                  paging                                            Thông tin phân trang
@apisuccess       {Integer}                                         code                                              Http code
@apisuccess       {String}                                          lang                                              Ngôn ngữ
@apisuccess       {String}                                          message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [segment_response],
  "paging": {paging} 
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ********************* Detail value RFM in Segment  *******************
# version: 1.0.0                                                       *
# **********************************************************************
"""
@api {post} /api/v1.0/rfms/value-calculate-rfm-v2  [DONE] Value Caculate RFM.
@apiDescription Value Calculate RFM in Segment
@apiGroup RFM
@apiVersion 1.0.0
@apiName  Value Calculate RFM in Segment


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse value_calculate_rfm

@apiParam             (Body:)             {Object}                  data                          
@apiParam             (Body:)             {Integer}                  data.rfm_id                RFM id
@apiParam             (Body:)             {Integer}                  data.version               Version RFM

@apiParamExample {json} Body example
{
    "data": {
        "rfm_id": 12,
        "version": 1
    }
}

@apisuccess       {ArrayObject{value_calculate_rfm_object}}          data                                              Value caculate RFM of Segment
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [value_calculate_rfm_object], 
  "lang": "vi", 
  "message": "request thành công."
}
"""


# ********************************* Create RFM category *******************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {post} /api/v1.0/rfms/categories [DONE] Tạo mới RFM category.
@apiDescription Tạo mới RFM Category
@apiGroup RFM
@apiVersion 1.0.0
@apiName  Tạo mới RFM Category


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse category_response

@apisuccess       {Object{category_response}}                    data                                              RFM Category
@apisuccess       {Integer}                                      code                                              Http code
@apisuccess       {String}                                       lang                                              Ngôn ngữ
@apisuccess       {String}                                       message                                           Message

@apiParam                (Body:)             {Object}                 data                          
@apiParam                (Body:)             {String}                 data.name                                     Tên rfm category
@apiParam                (Body:)             {Object}                 data.r_score                                  Cấu hình điểm R
@apiParam                (Body:)             {Object}                 data.r_score.min_outliner                     Giới hạn dưới điểm R
@apiParam                (Body:)             {String}                 data.r_score.min_outliner.operator_key        Key loại so sánh: Luôn luôn là: OP_IS_GREATER_EQUAL
@apiParam                (Body:)             {Array(Interger)}        data.r_score.min_outliner.values              Giá trị so sánh, trường hợp này là mảng 1 phần tử
@apiParam                (Body:)             {Object}                 data.r_score.max_outliner                     Giới hạn trên điểm R
@apiParam                (Body:)             {String}                 data.r_score.max_outliner.operator_key        Key loại so sánh: Luôn luôn là: OP_IS_LESS_EQUAL
@apiParam                (Body:)             {Array(Interger)}        data.r_score.max_outliner.values              Giá trị so sánh, trường hợp này là mảng 1 phần tử
@apiParam                (Body:)             {Array(Object)}          data.r_score.scores                           Danh sách các cấu hình của điểm R
@apiParam                (Body:)             {Interger}               data.r_score.scores.score                     Điểm R(từ 1 tới 5)
@apiParam                (Body:)             {String}                 data.r_score.scores.operator_key              Key loại so sánh: Hiện tại đang có 3 key: OP_IS_GREATER_EQUAL_AND_LESS: lớn hơn bằng và nhỏ hơn,  OP_IS_GREATER_EQUAL: lớn hơn hoặc bằng, OP_IS_LESS_EQUAL: nhỏ hơn hoặc bằng
@apiParam                (Body:)             {Array(Interger)}        data.r_score.scores.values                    Giá trị so sánh, tối đa 2 phần tử, nếu kiểu so sánh là "OP_IS_GREATER_EQUAL_AND_LESS" thì values có 2 phần tử là 2 đầu mút, các kiểu so sánh còn lại thì values có 1 phần tử
@apiParam                (Body:)             {Object}                 data.f_score                                  Cấu hình điểm f, tương tự như điểm r
@apiParam                (Body:)             {Object}                 [data.m_score]                                  Cấu hình điểm m, tương tự như điểm r




@apiParamExample {json} Body example
{
    "data": {
        "name": "segment rfm",
        "r_score": {
            "max_outliner": {
                "operator_key": "OP_IS_LESS_EQUAL",
                "values": [90]
            },
            "min_outliner": {
                "operator_key": "OP_IS_GREATER_EQUAL",
                "values": [1]
            },
            "scores": [
                {
                    "score": 1,
                    "operator_key": "OP_IS_GREATER_EQUAL",
                    "values": [60]
                }, 
                {
                    "score": 2,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [31, 60]
                }, 
                {
                    "score": 3,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [15, 30]
                }, 
                {
                    "score": 4,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [7, 14]
                }, 
                {
                    "score": 5,
                    "operator_key": "OP_IS_LESS_EQUAL",
                    "values": [7]
                }
            ]
        },
        "f_score": {
            "max_outliner": {
                "operator_key": "OP_IS_LESS_EQUAL",
                "values": [20]
            },
            "min_outliner": {
                "operator_key": "OP_IS_GREATER_EQUAL",
                "values": [1]
            },
            "scores": [
                {
                    "score": 1,
                    "operator_key": "OP_IS_LESS_EQUAL",
                    "values": [1]
                }, 
                {
                    "score": 2,
                    "operator_key": "OP_IS_LESS_EQUAL",
                    "values": [2]
                }, 
                {
                    "score": 3,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [3, 4]
                }, 
                {
                    "score": 4,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [5, 7]
                }, 
                {
                    "score": 5,
                    "operator_key": "OP_IS_GREATER_EQUAL",
                    "values": [8]
                }
            ]
        },
        "m_score": {
            "max_outliner": {
                "operator_key": "OP_IS_LESS_EQUAL",
                "values": [100000]
            },
            "min_outliner": {
                "operator_key": "OP_IS_GREATER_EQUAL",
                "values": [3000000]
            },
            "scores": [
                {
                    "score": 1,
                    "operator_key": "OP_IS_LESS_EQUAL",
                    "values": [200000]
                }, 
                {
                    "score": 2,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [200000,300000]
                }, 
                {
                    "score": 3,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [300000,400000]
                }, 
                {
                    "score": 4,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [400000,500000]
                }, 
                {
                    "score": 5,
                    "operator_key": "OP_IS_GREATER_EQUAL",
                    "values": [500000]
                }
            ]
        },          
    }
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {category_response}, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

# ********************************* Sửa RFM category *******************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {put} /api/v1.0/rfms/categories/<:category_id> [DONE] Sửa RFM category.
@apiDescription Sửa RFM Category
@apiGroup RFM
@apiVersion 1.0.0
@apiName  Sửa RFM Category


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse category_response

@apisuccess       {Object{category_response}}                    data                                              RFM Category
@apisuccess       {Integer}                                      code                                              Http code
@apisuccess       {String}                                       lang                                              Ngôn ngữ
@apisuccess       {String}                                       message                                           Message

@apiParam                (Body:)             {Object}                 data                          
@apiParam                (Body:)             {String}                 [data.name]                                     Tên rfm category
@apiParam                (Body:)             {Object}                 [data.r_score]                                  Cấu hình điểm R
@apiParam                (Body:)             {Object}                 data.r_score.min_outliner                     Giới hạn dưới điểm R
@apiParam                (Body:)             {String}                 data.r_score.min_outliner.operator_key        Key loại so sánh: Luôn luôn là: OP_IS_EQUAL
@apiParam                (Body:)             {Array(Interger)}        data.r_score.min_outliner.values              Giá trị so sánh, trong trường hợp này là mảng 1 phần tử
@apiParam                (Body:)             {Object}                 data.r_score.max_outliner                     Giới hạn trên điểm R
@apiParam                (Body:)             {String}                 data.r_score.max_outliner.operator_key        Key loại so sánh: Luôn luôn là: OP_IS_EQUAL
@apiParam                (Body:)             {Array(Interger)}        data.r_score.max_outliner.values              Giá trị so sánh, trong trường hợp này là mảng 1 phần tử
@apiParam                (Body:)             {Array(Object)}          data.r_score.scores                           Danh sách các cấu hình của điểm R
@apiParam                (Body:)             {Interger}               data.r_score.scores.score                     Điểm R(từ 1 tới 5)
@apiParam                (Body:)             {String}                 data.r_score.scores.operator_key              Key loại so sánh: Hiện tại đang có 3 key: OP_IS_GREATER_EQUAL_AND_LESS: Lớn hơn hoặc bằng và nhỏ hơn, OP_IS_GREATER_EQUAL: lớn hơn hoặc bằng, OP_IS_EQUAL: bằng
@apiParam                (Body:)             {Array(Interger)}        data.r_score.scores.values                    Giá trị so sánh, tối đa 2 phần tử, nếu kiểu so sánh là "OP_IS_GREATER_EQUAL_AND_LESS" thì values có 2 phần tử là 2 đầu mút, các kiểu so sánh còn lại thì values có 1 phần tử
@apiParam                (Body:)             {Object}                 [data.f_score]                                  Cấu hình điểm f, tương tự như điểm r
@apiParam                (Body:)             {Object}                 [data.m_score]                                  Cấu hình điểm m, tương tự như điểm r
@apiParam                (Body:)             {ArrayObject}            [rfm_bucket]                                   (125 phần tử)
@apiParam                (Body:)             {Integer}                rfm_bucket.x                                 Tọa độ X
@apiParam                (Body:)             {Integer}                rfm_bucket.y                                 Tọa độ y
@apiParam                (Body:)             {Integer}                rfm_bucket.z                                 Tọa độ Z
@apiParam                (Body:)             {String}                 rfm_bucket.id                                 mã code của bucket: <code>CHAMPIONS / LOYAL_CUSTOMERS / POTENTIAL_LOYALISTS / NEW_CUSTOMERS / PROMISING / NEED_ATTENTION / ABOUT_TO_SLEEP / AT_RISK / CANNOT_LOSE_THEM / HIBERNATING / PRICE_SENSITIVE </code>
@apiParam                (Body:)             {String}                 rfm_bucket.color                             Mã màu
@apiParam                (Body:)             {String}                 rfm_bucket.label                             Tên hiển thị Bucket
@apiParam                (Body:)             {ArrayObject}            [rf_bucket]                                    (25 phần tử)
@apiParam                (Body:)             {Integer}                rf_bucket.x                                  Tọa độ X
@apiParam                (Body:)             {Integer}                rf_bucket.y                                  Tọa độ Y
@apiParam                (Body:)             {String}                 rf_bucket.id                                 mã code của bucket: <code>CHAMPIONS / LOYAL_CUSTOMERS / POTENTIAL_LOYALISTS / NEW_CUSTOMERS / PROMISING / NEED_ATTENTION / ABOUT_TO_SLEEP / AT_RISK / CANNOT_LOSE_THEM / HIBERNATING </code>
@apiParam                (Body:)             {String}                 rf_bucket.color                              Mã màu
@apiParam                (Body:)             {String}                 rf_bucket.label                              Tên hiển thị Bucket
@apiParam                (Body:)             {ArrayObject }           [rm_bucket]                                  
@apiParam                (Body:)             {Object}                 rm_bucket.object                             giống rf_bucket
@apiParam                (Body:)             {ArrayObject}            [fm_bucket]                                  
@apiParam                (Body:)             {Object}                 fm_bucket.object                             giống rf_bucket




@apiParamExample {json} Body example
{
    "data": {
        "name": "segment rfm",
        "r_score": {
            "max_outliner": {
                "operator_key": "OP_IS_LESS_EQUAL",
                "values": [90]
            },
            "min_outliner": {
                "operator_key": "OP_IS_GREATER_EQUAL",
                "values": [1]
            },
            "scores": [
                {
                    "score": 1,
                    "operator_key": "OP_IS_GREATER_EQUAL",
                    "values": [60]
                }, 
                {
                    "score": 2,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [31, 60]
                }, 
                {
                    "score": 3,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [15, 30]
                }, 
                {
                    "score": 4,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [7, 14]
                }, 
                {
                    "score": 5,
                    "operator_key": "OP_IS_LESS_EQUAL",
                    "values": [7]
                }
            ]
        },
        "f_score": {
            "max_outliner": {
                "operator_key": "OP_IS_LESS_EQUAL",
                "values": [20]
            },
            "min_outliner": {
                "operator_key": "OP_IS_GREATER_EQUAL",
                "values": [1]
            },
            "scores": [
                {
                    "score": 1,
                    "operator_key": "OP_IS_LESS_EQUAL",
                    "values": [1]
                }, 
                {
                    "score": 2,
                    "operator_key": "OP_IS_LESS_EQUAL",
                    "values": [2]
                }, 
                {
                    "score": 3,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [3, 4]
                }, 
                {
                    "score": 4,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [5, 7]
                }, 
                {
                    "score": 5,
                    "operator_key": "OP_IS_GREATER_EQUAL",
                    "values": [8]
                }
            ]
        },
        "m_score": {
            "max_outliner": {
                "operator_key": "OP_IS_LESS_EQUAL",
                "values": [100000]
            },
            "min_outliner": {
                "operator_key": "OP_IS_GREATER_EQUAL",
                "values": [3000000]
            },
            "scores": [
                {
                    "score": 1,
                    "operator_key": "OP_IS_LESS_EQUAL",
                    "values": [200000]
                }, 
                {
                    "score": 2,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [200000,300000]
                }, 
                {
                    "score": 3,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [300000,400000]
                }, 
                {
                    "score": 4,
                    "operator_key": "OP_IS_GREATER_EQUAL_AND_LESS",
                    "values": [400000,500000]
                }, 
                {
                    "score": 5,
                    "operator_key": "OP_IS_GREATER_EQUAL",
                    "values": [500000]
                }
            ]
        },
        "rfm_bucket":[
            {
                "x": 5,
                "y": 5,
                "z": 5,
                "id": "CHAMPIONS",
                "color": "#7abbf7",
                "label": "Champions"
            },
        ],
        "rf_bucket": [
            {
                "x": 5,
                "y": 5,
                "id": "CHAMPIONS",
                "color": "#7abbf7",
                "label": "Champions"
            },
        ]          
    }
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {category_response}, 
  "lang": "vi", 
  "message": "request thành công."
}
"""


# **************************** API CONST CATEGORY BUCKET CODE *************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {get} /api/v1.0/rfms/const/category-bucket-code [DONE] Danh sách CATEGORY BUCKET CODE
@apiDescription Danh sách CATEGORY BUCKET CODE
@apiGroup CONST RFM
@apiVersion 1.0.0
@apiName  List category bucket code


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apisuccess       {Object}                                      data                                              Data
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "code": "CHAMPIONS",
            "color": "4c8cf7",
            "name": "Champions",
            "order": 1,
            "translate_key": "i18n_champions"
        },
        {
            "code": "LOYAL_CUSTOMERS",
            "color": "5e5c66",
            "name": "Loyal Customers",
            "order": 2,
            "translate_key": "i18n_loyal_customers"
        },
        {
            "code": "POTENTIAL_LOYALISTS",
            "color": "00a757",
            "name": "Potential Loyalists",
            "order": 3,
            "translate_key": "i18n_potential_loyalists"
        },
        {
            "code": "NEW_CUSTOMERS",
            "color": "9dd645",
            "name": "New Customers",
            "order": 4,
            "translate_key": "i18n_new_customers"
        },
        {
            "code": "PROMISING",
            "color": "cc9200",
            "name": "Promising",
            "order": 5,
            "translate_key": "i18n_promising"
        },
        {
            "code": "NEED_ATTENTION",
            "color": "ffc533",
            "name": "Need Attention'",
            "order": 6,
            "translate_key": "i18n_need_attention"
        },
        {
            "code": "PRICE_SENSITIVE",
            "color": "f6a4cc",
            "name": "Price Sensitive",
            "order": 7,
            "translate_key": "i18n_price_sensitive"
        },
        {
            "code": "ABOUT_TO_SLEEP",
            "color": "87d2f4",
            "name": "About to sleep",
            "order": 8,
            "translate_key": "i18n_about_to_sleep"
        },
        {
            "code": "AT_RISK",
            "color": "fa8f45",
            "name": "At Risk'",
            "order": 9,
            "translate_key": "i18n_at_risk"
        },
        {
            "code": "CANNOT_LOSE_THEM",
            "color": "eb4e4e",
            "name": "Cannot Lose Them",
            "order": 10,
            "translate_key": "i18n_cannot_lose_them"
        },
        {
            "code": "HIBERNATING",
            "color": "918f99",
            "name": "Hibernating",
            "order": 11,
            "translate_key": "i18n_hibernating"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ********************************* List circle radius ********************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {post} /api/v1.0/rfms/list-circle-radius [DONE] List circle radius
@apiDescription list circle radius
@apiGroup RFM
@apiVersion 1.0.0
@apiName  List circle radius


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam               (list_circle_radius:)     {Float}                   x                      Tọa độ điểm x
@apiParam               (list_circle_radius:)     {Float}                   y                      Tọa độ điểm y
@apiParam               (list_circle_radius:)     {Float}                   z                      Tọa độ điểm z
@apiParam               (list_circle_radius:)     {Float}                   r                      Tỉ lệ theo độ chia 0.5, 1
@apiParam               (list_circle_radius:)     {Integer}                 total_profile          Tổng số profile
@apiParam               (list_circle_radius:)     {String}                  color                  Mã màu
@apiParam               (list_circle_radius:)     {String}                  bucket_code            mã bucket
@apiParam               (list_circle_radius:)     {String}                  label                  tên bucket


@apiParam                (Body:)             {Object}                   data                         
@apiParam                (Body:)             {Interger}                 data.rfm_id                          Id của RFM
@apiParam                (Body:)             {Interger}                 data.version                         Version của RFM
@apiParam                (Body:)             {Float}                    [data.unit]                          Đơn vị độ chia, mặc định là 0.2

@apiParamExample {json} Body example
{
    "data": {
          "rfm_id": 9, 
          "version": 1,    
          "unit": 0.5,    
    }
}


@apisuccess       {ArrayObject}                                 data                                              Data
@apisuccess       {Integer}                                     code                                              Http code
@apisuccess       {String}                                      lang                                              Ngôn ngữ
@apisuccess       {String}                                      message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [{
    "x": 17,5,
    "y": 22,5,
    "z": 4,5,
    "r": 0.05,
    "total_profile": 3000,
    "color": "#4c8cf7"
    "bucket_code": "CHAMPION",
    "label": "champion" 
    }], 
  "lang": "vi", 
  "message": "request thành công."
}
"""

# ********************************* Xóa RFM category *******************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {delete} /api/v1.0/rfms/categories/<:category_id> [DONE] Xóa RFM category.
@apiDescription Xóa RFM Category
@apiGroup RFM
@apiVersion 1.0.0
@apiName  Xóa RFM Category


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse category_response

@apisuccess       {Object{category_response}}                    data                                              RFM Category
@apisuccess       {Integer}                                      code                                              Http code
@apisuccess       {String}                                       lang                                              Ngôn ngữ
@apisuccess       {String}                                       message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {category_response}, 
  "lang": "vi", 
  "message": "request thành công."
}
"""



# ********************************* Xóa RFM  *******************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {delete} /api/v1.0/rfms/<:rfm_id> [DONE] Xóa RFM.
@apiDescription Xóa RFM
@apiGroup RFM
@apiVersion 1.0.0
@apiName  Xóa RFM


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse rfm_response

@apisuccess       {Object{rfm}}                    data                                              RFM Category
@apisuccess       {Integer}                                      code                                              Http code
@apisuccess       {String}                                       lang                                              Ngôn ngữ
@apisuccess       {String}                                       message                                           Message


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {rfm}, 
  "lang": "vi", 
  "message": "request thành công."
}
"""
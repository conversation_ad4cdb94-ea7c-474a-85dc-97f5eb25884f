************************************* Create Pipeline ******************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /api/v1.0/pipeline_config Tạo mới pipeline
@apiGroup Data Flow
@apiVersion 1.0.0
@apiName CreatePipeline

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)     {String}    name                Tên của pipeline
@apiParam   (Body:)     {Object}    source              Data source của pipeline. Xem chi tiết đối tượng <code>Source</code>
@apiParam   (Source)    {String}    [connector]         Tên connector tới Database
@apiParam   (Source)    {String}    [hostname]          Hostname của database
@apiParam   (Source)    {String}    [port]              Port của database
@apiParam   (Source)    {String}    [username]          Ssername của database
@apiParam   (Source)    {String}    [pasword]           Pasword của database
@apiParam   (Source)    {String}    [database_name]     Database name
@apiParam   (Source)    {String}    [schema_name]       Schema name
@apiParam   (Source)    {String}    [table_name]        Bảng của database
@apiParam   (Body:)     {String}    mode                Kiểu chạy của pipleline. Support 2 kiểu <code>batch</code> và <code>streaming</code> 
@apiParam   (Body:)     {Object}    schedule            Schedule cho jobs
@apiParam   (Schedule:) {Object}    type                Có option schedule là <code>day</code>, <code>week</code>, <code>month</code>
@apiParam   (Schedule:) {String}    time_in_day         date pattern <code>hh:mm</code> lựa chọn giờ trong <code>day</code>, <code>week</code>, <code>month</code>
@apiParam   (Schedule:) {Array}     [day_in_week]         là 1 array lựa chọn ngày theo tuần. Có option <code>monday</code>, <code>tuesday</code>, <code>wednesday</code>, <code>thursday</code>, <code>friday</code>, <code>saturday</code>, <code>sunday</code>
@apiParam   (Schedule:) {Array}     [day_in_month]        là 1 array lựa chọn ngày theo tháng. Số ngày sẽ tương ứng với tháng trong năm
@apiParam   (Body:)     {String}    merchant_id         id của merchant
@apiParam   (Body:)     {Object}    connector           Connector. Xem chi tiết đối tượng <code>Connector</code>
@apiParam   (Connector) {String}    [name]              Tên connector

@apiParamExample    {json}  Body:
{
    "name" : "mobio-data-flow-test-1",
    "source" : {
        "connector" : "postgres-cdc",
        "hostname" : "localhost",
        "port" : "5432",
        "username" : "postgres",
        "password" : "",
        "database_name" : "dpmt",
        "schema_name" : "public",
        "table_name" : "jobs",
    },
    "mode" : "streaming",
    "schedule" : {
        "time_in_day": "13:58",
        "day_in_week": [
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
            "saturday",
            "sunday",
        ],
        "day_in_month": [
            1,
            2,
            3,
            4,
            5
        ]
    },
    "merchant_id" : "mobio",
    "connector" : {
        "name": "profiling-profile-test"
    }
}


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Inserted",
    "data": {
        "name" : "mobio-data-flow-test-1",
        "source" : {
            "connector" : "postgres-cdc",
            "hostname" : "localhost",
            "port" : "5432",
            "username" : "postgres",
            "password" : "",
            "database_name" : "dpmt",
            "schema_name" : "public",
            "table_name" : "jobs",
        },
        "mode" : "streaming",
        "schedule" : {
            "time_in_day": "13:58",
            "day_in_week": [
                "monday",
                "tuesday",
                "wednesday",
                "thursday",
                "friday",
                "saturday",
                "sunday",
            ],
            "day_in_month": [
                1,
                2,
                3,
                4,
                5
            ]
        },
        "merchant_id" : "mobio",
        "connector" : {
            "name": "profiling-profile-test"
        }
    }
}
"""

********************************* GetPipeline ******************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {GET} /api/v1.0/pipeline_config/<id> Chi tiết pipeline config
@apiGroup Data Flow
@apiVersion 1.0.0
@apiName GetPipeline

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    id        Pipeline ID <code>required</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "name" : "mobio-data-flow-test-1",
        "source" : {
            "connector" : "postgres-cdc",
            "hostname" : "localhost",
            "port" : "5432",
            "username" : "postgres",
            "password" : "",
            "database_name" : "dpmt",
            "schema_name" : "public",
            "table_name" : "jobs",
        },
        "mode" : "streaming",
        "schedule" : {
            "time_in_day": "13:58",
            "day_in_week": [
                "monday",
                "tuesday",
                "wednesday",
                "thursday",
                "friday",
                "saturday",
                "sunday",
            ],
            "day_in_month": [
                1,
                2,
                3,
                4,
                5
            ]
        },
        "merchant_id" : "mobio",
        "connector" : {
            "name": "profiling-profile-test"
        }
    }
}
"""

********************************* StartPipeline ******************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {PUT} /api/v1.0/pipeline_config/<id>/start Khởi chạy pipeline
@apiGroup Data Flow
@apiVersion 1.0.0
@apiName StartPipeline

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    id        Pipeline ID <code>required</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": {
            "$oid": "6604f6d6f22721594aa0fc3d"
        },
        "pipeline_name": "mobio-data-flow-test-1",
        "session_id": "s20240328115662",
        "start_time": {
            "$date": "2024-03-28T11:49:26.372Z"
        },
        "end_time": null,
        "consume_status": "running",
        "process_status": "running",
        "created_time": {
            "$date": "2024-03-28T04:49:26.372Z"
        },
        "updated_time": {
            "$date": "2024-03-28T04:49:26.372Z"
        }
    },
    "message": "success"
}
"""

********************************* StopPipeline ******************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {PUT} /api/v1.0/pipeline_config/<id>/stop Dừng pipeline
@apiGroup Data Flow
@apiVersion 1.0.0
@apiName StopPipeline

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Param:)    {String}    id        Pipeline ID <code>required</code>

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": {
            "$oid": "6604f6d6f22721594aa0fc3d"
        },
        "pipeline_name": "mobio-data-flow-test-1",
        "session_id": "s20240328115662",
        "start_time": {
            "$date": "2024-03-28T11:49:26.372Z"
        },
        "end_time": null,
        "consume_status": "stop",
        "process_status": "stop",
        "created_time": {
            "$date": "2024-03-28T04:49:26.372Z"
        },
        "updated_time": {
            "$date": "2024-03-28T04:49:26.372Z"
        }
    },
    "message": "success"
}
"""

************************************* Upgrade Pipeline ******************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {PUT} /api/v1.0/pipeline_config/<id> Cập nhập pipeline
@apiGroup Data Flow
@apiVersion 1.0.0
@apiName UpgradePipeline

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)     {String}    name                Tên của pipeline
@apiParam   (Body:)     {Object}    source              Data source của pipeline. Xem chi tiết đối tượng <code>Source</code>
@apiParam   (Source)    {String}    [connector]         Tên connector tới Database
@apiParam   (Source)    {String}    [hostname]          Hostname của database
@apiParam   (Source)    {String}    [port]              Port của database
@apiParam   (Source)    {String}    [username]          Ssername của database
@apiParam   (Source)    {String}    [pasword]           Pasword của database
@apiParam   (Source)    {String}    [database_name]     Database name
@apiParam   (Source)    {String}    [schema_name]       Schema name
@apiParam   (Source)    {String}    [table_name]        Bảng của database
@apiParam   (Body:)     {String}    mode                Kiểu chạy của pipleline. Support 2 kiểu <code>batch</code> và <code>streaming</code> 
@apiParam   (Body:)     {Object}    schedule            Schedule cho jobs
@apiParam   (Schedule:) {Object}    type                Có option schedule là <code>day</code>, <code>week</code>, <code>month</code>
@apiParam   (Schedule:) {String}    time_in_day         date pattern <code>hh:mm</code> lựa chọn giờ trong <code>day</code>, <code>week</code>, <code>month</code>
@apiParam   (Schedule:) {Array}     [day_in_week]         là 1 array lựa chọn ngày theo tuần. Có option <code>monday</code>, <code>tuesday</code>, <code>wednesday</code>, <code>thursday</code>, <code>friday</code>, <code>saturday</code>, <code>sunday</code>
@apiParam   (Schedule:) {Array}     [day_in_month]        là 1 array lựa chọn ngày theo tháng. Số ngày sẽ tương ứng với tháng trong năm
@apiParam   (Body:)     {String}    merchant_id         id của merchant
@apiParam   (Body:)     {Object}    connector           Connector. Xem chi tiết đối tượng <code>Connector</code>
@apiParam   (Connector) {String}    [name]              Tên connector

@apiParamExample    {json}  Body:
{
    "name" : "mobio-data-flow-test-1",
    "source" : {
        "connector" : "postgres-cdc",
        "hostname" : "localhost",
        "port" : "5432",
        "username" : "postgres",
        "password" : "",
        "database_name" : "dpmt",
        "schema_name" : "public",
        "table_name" : "jobs",
    },
    "mode" : "streaming",
    "schedule" : {
        "time_in_day": "13:58",
        "day_in_week": [
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
            "saturday",
            "sunday",
        ],
        "day_in_month": [
            1,
            2,
            3,
            4,
            5
        ]
    },
    "merchant_id" : "mobio",
    "connector" : {
        "name": "profiling-profile-test"
    }
}


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "updated successfully",
}
"""

**************************************** CreateConnector *****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {POST} /api/v1.0/connectors Create connector
@apiGroup Data Flow
@apiVersion 1.0.0
@apiName CreateConnector
@apiDeprecated  Sử dụng api Init(#api-Data_Flow-InitConnector)

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)     {String}    deployment_name                Tên deployment
@apiParam   (Body:)     {String}    name                           Tên connector
@apiParam   (Body:)     {String}    configmap_name                 Tên configmap đã lưu trong kube

@apiParamExample    {json}  Body:
{
    "deployment_name" : "pf-csm-dataflow-upsert-profile",
    "name" : "profiles_api",
    "configmap_name" : "profiling-config"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "deployment_name" : "pf-csm-dataflow-upsert-profile",
        "name" : "profiles_api",
        "configmap_name" : "profiling-config",
        "created_time" : "datetime",
        "updated_time" : "datetime",
    },
    "message": "success"
}

@apiErrorExample     {json}    Response: HTTP/1.1 422 UNPROCESSABLE_ENTITY
{
    "code": 422,
    "message": "Unsupported",
}
"""

********************************* UpgradeConnector ******************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {POST} /api/v1.0/connectors/upgrade Upgrade connector
@apiGroup Data Flow
@apiVersion 1.0.0
@apiName UpgradeConnector
@apiDeprecated  Sử dụng api Init(#api-Data_Flow-InitConnector)

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)     {String}    deployment_name                Tên deployment

@apiParamExample    {json}  Body:
{
    "deployment_name" : "pf-csm-dataflow-upsert-profile"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "upgraded": ["s20240417035535", "s20240417031234"]
    },
    "message": "success"
}

@apiErrorExample     {json}    Response: HTTP/1.1 422 UNPROCESSABLE_ENTITY
{
    "code": 422,
    "message": "Unsupported",
}
"""

**************************************** InitConnector *****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {POST} /api/v1.0/connectors/init Init or reload connector
@apiDescription api khởi tạo connector cho các đầu đăng ký, nếu connector.name đã được tạo đăng ký từ trước, thì connector này sẽ được hiểu là tiến trình upgrade và reload lại các pipeline
@apiGroup Data Flow
@apiVersion 1.0.0
@apiName InitConnector

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)     {String}    deployment_name                Tên deployment
@apiParam   (Body:)     {String}    name                           Tên connector
@apiParam   (Body:)     {String}    configmap_name                 Tên configmap đã lưu trong kube. Tên configmap khuyến khích được đặt với tiền tố df

@apiParamExample    {json}  Body:
{
    "deployment_name" : "pf-csm-dataflow-upsert-profile",
    "name" : "profiles_api",
    "configmap_name" : "df-profiling-config"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "deployment_name" : "pf-csm-dataflow-upsert-profile",
        "name" : "profiles_api",
        "configmap_name" : "df-profiling-config",
        "created_time" : "datetime",
        "updated_time" : "datetime",
    },
    "upgraded": [],
    "message": "success"
}

@apiErrorExample     {json}    Response: HTTP/1.1 422 UNPROCESSABLE_ENTITY
{
    "code": 422,
    "message": "Unsupported",
}
"""
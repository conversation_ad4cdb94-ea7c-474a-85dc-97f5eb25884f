# -*- coding: utf-8 -*-
# ######################## Transaction_2.0.2  ####################
# version: 2.0.0
# update: 2020/Aug/04
#   - add `voucher_status`, `name` to voucher field
#   - add describe to point and rank_point:
#       - value
#       - expired_time
#       - reason
# ======================
# version: 2.0.0
# update: 2019/04/24
#   - business_code trở thành option
#   - bắt buộc cung cấp merchant_id
#   - Thêm payment method
# ################################################################

# ------------------------- Transaction_2.0.1 -------------------
# version: 2.0.2
# update: 2020/Aug/04
# ---------------------------------------------------------------
"""
@api {post} /transaction  E34 - Transaction Common
@apiDescription API import transaction
@apiVersion 2.0.2
@apiGroup Loyalty API
@apiName Transaction Common

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)   {string}            merchant_id         ID của merchant trong hệ thống
@apiParam (Body:)   {array}             data                Data transaction của khách hàng
<li><code>Giới hạn 1000 data mỗi gói tin</code></li>

@apiParam (data:)   {string}            profile_id              UUID profile của khách hàng
@apiParam (data:)   {string}            transaction_id          Mã đơn hàng trong hệ thống
@apiParam (data:)   {string}            transaction_stt         Trạng thái đơn hàng đang xử lý trong hệ thống
@apiParam (data:)   {float}             transaction_amount      Số tiền thực sự khách hàng cần phải thanh toán
@apiParam (data:)   {string}            transaction_code        Mã transaction
@apiParam (data:)   {string}            currency_code           Đơn vị tiền tệ (theo chuẩn ISO 4217). <br>
Refer:<a href="https://en.wikipedia.org/wiki/ISO_4217"> ISO_4217 </a>
<li>Default value: <code>VND</code></li>
@apiParam (data:)   {float}             action_time             Timestamp action xảy ra
@apiParam (data:)   {array}             point                   Điểm tiêu dùng khách hàng
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>value</td>
      <td>float</td>
      <td>Giá trị điểm
        <li><code> > 0</code>: Tặng điểm</li>
        <li><code> = 0</code>: Nothing</li>
        <li><code> < 0</code>: Trừ điểm</li>
      </td>
    </tr>
    <tr>
      <td>expired_time</td>
      <td>float</td>
      <td>timestamp Python, thời gian hết hạn</td>
    </tr>
    <tr>
      <td>reason</td>
      <td>string</td>
      <td>Lý do sử dụng điểm</td>
    </tr>
  </tbody>
</table>
@apiParam (data:)   {array}             rank_point              Điểm xếp hạng của khách hàng
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>value</td>
      <td>float</td>
      <td>Giá trị điểm
        <li><code> > 0</code>: Tặng điểm</li>
        <li><code> = 0</code>: Nothing</li>
        <li><code> < 0</code>: Trừ điểm</li>
      </td>
    </tr>
    <tr>
      <td>expired_time</td>
      <td>float</td>
      <td>timestamp Python, thời gian hết hạn</td>
    </tr>
    <tr>
      <td>reason</td>
      <td>string</td>
      <td>Lý do sử dụng điểm</td>
    </tr>
  </tbody>
</table>
@apiParam (Body:)   {string}            [business_code]         Business Code của khách hàng
@apiParam (data:)   {string}            [profile_type]          Trường xác nhận tập mà khách hàng thuộc về
<li>Allow value: <code>"B2C", "B2C"</code></li>
<li>Default value: <code>"B2C"</code></li>
@apiParam (data:)   {array}             [transaction_tag]       Các nhãn thông tin thêm cho transaction.
<li><code>Nhận một list các string tags</code></li>
@apiParam (data:)   {string}            [transaction_type]      Loại transaction khách hàng tương tác
@apiParam (data:)   {string}            [source]                Tên hệ thống ghi nhân action
<li>Default value: <code>"Offline"</code></code></li>
@apiParam (data:)   {array(string)}     [set_name_supplier]     Danh sách tên các nhà
set_name_supplier
@apiParam (data:)   {object}            [store]                   Các thông tin store mà khách hàng đã phát sinh transaction
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>string</td>
      <td>ID của của hàng trong hệ thống Mobio</td>
    </tr>
    <tr>
      <td>code</td>
      <td>string</td>
      <td>Code nhận diện của hảng</td>
    </tr>
    <tr>
      <td>name</td>
      <td>string</td>
      <td>Tên của hàng</td>
    </tr>
    <tr>
      <td>address<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>Địa chỉ cửa hàng</td>
    </tr>
  </tbody>
</table>

@apiParam (data:)   {array}             [items]                   Danh sách các item khách hàng
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>string</td>
      <td>ID của vật phẩm</td>
    </tr>
    <tr>
      <td>name</td>
      <td>string</td>
      <td>Tên của vật phẩm</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>string array</td>
      <td>Các nhãn thông tin thêm cho vật phẩm</td>
    </tr>
    <tr>
      <td>category</td>
      <td>string</td>
      <td>Danh mục của vật phẩm</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>int</td>
      <td>Số lượng vật phẩm mà khách hàng đã mua</td>
    </tr>
    <tr>
      <td>price</td>
      <td>float</td>
      <td>Đơn giá hiện tại của vật phẩm</td>
    </tr>
    <tr>
      <td>currency_code</td>
      <td>string</td>
      <td>Đơn vị tiền tệ quy chuẩn tương ứng với đơn giá cho vật phẩm. Refer:<a href="https://en.wikipedia.org/wiki/ISO_4217"> ISO_4217 </a>
        <li>Default value: <code>VND</code></li>
      </td>
    </tr>
    <tr>
      <td>supplier<span class="label label-optional">optional</span></td>
      <td></td>
      <td>Thông tin nhà cung cấp, <code>note</code>: <strong>event</strong> and <strong>factory</strong> hiểu là thương hiệu của sản phầm (trademark)
        <table>
          <thead>
            <tr>
              <th style="width: 30%">Field</th>
              <th style="width: 10%">Type</th>
              <th style="width: 60%">Description</th>
            </tr>
            <tbody>
                <tr>
                  <td>id</td>
                  <td>string</td>
                  <td>Id của nhà cung cấp </td>
                </tr>
                <tr>
                  <td>name</td>
                  <td>string</td>
                  <td>Tên của nhà cung cấp </td>
                </tr>
                <tr>
                  <td>code</td>
                  <td>string</td>
                  <td>Code của nhà cung cấp tương ứng trên hệ thống Loyalty </td>
                </tr>
            </tbody>
          </thead>
        </table>
      </td>
    </tr>
  </tbody>
</table>

@apiParam (data:)   {array}             [vouchers]                Danh sách các voucher khách hàng đã sử dụng
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>string</td>
      <td>ID của voucher</td>
    </tr>
    <tr>
      <td>code</td>
      <td>string</td>
      <td>Mã code của voucher</td>
    </tr>
    <tr>
      <td>name</td>
      <td>string</td>
      <td>Tên của voucher</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>int</td>
      <td>Số lượng voucher sử dụng</td>
    </tr>
    <tr>
      <td>expired_time</td>
      <td>float</td>
      <td>Thời gian hết hạn của voucher, lấy theo Python timestamp</td>
    </tr>
    <tr>
      <td>voucher_stt</td>
      <td>string</td>
      <td>Trạng thái sử dụng voucher. Nằm trong các trạng thái sau: </br>
        <li><code>granted</code>: tặng voucher</li>
        <li><code>used</code>: sử dụng voucher</li>
      </td>
    </tr>
  </tbody>
</table>

@apiParam (data:)   {object}            [payment]                   Các thông tin payment mà khách hàng đã phát sinh transaction
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>type<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>Hình thức thanh toán: ATM, cash, MC, VC ...
        <li>Default value: <code>cash</code></li>
      </td>
    </tr>
    <tr>
      <td>name<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM</td>
    </tr>
    <tr>
      <td>code<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card</td>
    </tr>
  </tbody>
</table>


@apiParamExample    {json}      Body example:
{
  "merchant_id": "33322236-0644-4364-bdad-1e73173a886a",
  "business_code": "PINGCOMSHOP",
  "data": [
    {
      "profile_id": "611d2df1-2a13-4174-8988-bed2773be063",
      "profile_type": "B2B",
      "transaction_type": "Buy",
      "transaction_id": "33322236-0644-4364-bdad-1e73173a886a",
      "transaction_stt": "Processing",
      "action_time": 1538703463.440775,
      "transaction_amount": 100000.0,
      "transaction_code": "Code123456",
      "currency_code": "VND",
      "source": "Online",
      "set_name_supplier": ["asdasd", "asdasdas"]
      "store": {
        "id": "33322236-0644-4364-bdad-1e73173a886a",
        "name": "Cua hang chi nhanh Duy Tan",
        "code": "MOBIO_001"
      },
      "items": [
        {
          "id": "SKU_001",
          "name": "san pham 1",
          "category": "category_001",
          "tags": [],
          "quantity": 10,
          "price": 10000.0,
          "currency_code": "VND",
          "supplier": {
            "id": "asdasdasd",
            "name": "nasdasda",
            "code": "asdasdasdasda"
          }
        }
      ],
      "vouchers": [
        {
          "id": "VOUCHER_TET",
          "quantity": 2,
          "code": "ASDFVASWDASD",
          "name": "Voucher Mobio"
          "voucher_stt": "used",
          "expired_time": 1599208183.947325
        }
      ],
      "payment": {
        "type": "ATM",
        "name": "VCB",
        "code": "asc23113512312312312"
      }
      "transaction_tag": ["TET"],
      "point": [
        {
            "value": -1.0,
            "expired_time": 1538703463.440775,
            "reason": "test reason"
        }
      ],
      "rank_point": [
        {
            "value": -1.0,
            "expired_time": 1538703463.440775,
            "reason": "test reason"
        }
      ]
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "code": 200,
   "lang": "vi",
   "message": "request thành công."
}
"""

# ------------------------- Transaction_2.0.1 -------------------
# version: 2.0.1
# ---------------------------------------------------------------
"""
@api {post} /transaction  E34 - Transaction Common
@apiDescription API import transaction
@apiVersion 2.0.1
@apiGroup Loyalty API
@apiName Transaction Common

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)   {string}            merchant_id         ID của merchant trong hệ thống
@apiParam (Body:)   {array}             data                Data transaction của khách hàng
<li><code>Giới hạn 1000 data mỗi gói tin</code></li>

@apiParam (data:)   {string}            profile_id              UUID profile của khách hàng
@apiParam (data:)   {string}            transaction_id          Mã đơn hàng trong hệ thống
@apiParam (data:)   {string}            transaction_stt         Trạng thái đơn hàng đang xử lý trong hệ thống
@apiParam (data:)   {float}             transaction_amount      Số tiền thực sự khách hàng cần phải thanh toán
@apiParam (data:)   {string}            currency_code           Đơn vị tiền tệ (theo chuẩn ISO 4217). <br>
Refer:<a href="https://en.wikipedia.org/wiki/ISO_4217"> ISO_4217 </a>
<li>Default value: <code>VND</code></li>
@apiParam (data:)   {float}             action_time             Timestamp action xảy ra
@apiParam (data:)   {float}             point                   Điểm tiêu dùng khách hàng tích luỹ thông qua các campaign
@apiParam (data:)   {float}             rank_point              Điểm xếp hạng dùng đánh giá hạng thẻ của khách hàng
@apiParam (Body:)   {string}            [business_code]         Business Code của khách hàng
@apiParam (data:)   {string}            [profile_type]          Trường xác nhận tập mà khách hàng thuộc về
<li>Allow value: <code>"B2C", "B2C"</code></li>
<li>Default value: <code>"B2C"</code></li>
@apiParam (data:)   {array}             [transaction_tag]       Các nhãn thông tin thêm cho transaction.
<li><code>Nhận một list các string tags</code></li>
@apiParam (data:)   {string}            [transaction_type]      Loại transaction khách hàng tương tác
@apiParam (data:)   {string}            [source]                Tên hệ thống ghi nhân action
<li>Default value: <code>"Offline"</code></code></li>
@apiParam (data:)   {array(string)}     [set_name_supplier]     Danh sách tên các nhà
set_name_supplier
@apiParam (data:)   {object}            [store]                   Các thông tin store mà khách hàng đã phát sinh transaction
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>string</td>
      <td>ID của của hàng trong hệ thống Mobio</td>
    </tr>
    <tr>
      <td>code</td>
      <td>string</td>
      <td>Code nhận diện của hảng</td>
    </tr>
    <tr>
      <td>name</td>
      <td>string</td>
      <td>Tên của hàng</td>
    </tr>
    <tr>
      <td>address<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>Địa chỉ cửa hàng</td>
    </tr>
  </tbody>
</table>

@apiParam (data:)   {array}             [items]                   Danh sách các item khách hàng
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>string</td>
      <td>ID của vật phẩm</td>
    </tr>
    <tr>
      <td>name</td>
      <td>string</td>
      <td>Tên của vật phẩm</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>string array</td>
      <td>Các nhãn thông tin thêm cho vật phẩm</td>
    </tr>
    <tr>
      <td>category</td>
      <td>string</td>
      <td>Danh mục của vật phẩm</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>int</td>
      <td>Số lượng vật phẩm mà khách hàng đã mua</td>
    </tr>
    <tr>
      <td>price</td>
      <td>float</td>
      <td>Đơn giá hiện tại của vật phẩm</td>
    </tr>
    <tr>
      <td>currency_code</td>
      <td>string</td>
      <td>Đơn vị tiền tệ quy chuẩn tương ứng với đơn giá cho vật phẩm. Refer:<a href="https://en.wikipedia.org/wiki/ISO_4217"> ISO_4217 </a>
        <li>Default value: <code>VND</code></li>
      </td>
    </tr>
    <tr>
      <td>supplier<span class="label label-optional">optional</span></td>
      <td></td>
      <td>Thông tin nhà cung cấp, <code>note</code>: <strong>event</strong> and <strong>factory</strong> hiểu là thương hiệu của sản phầm (trademark)
        <table>
          <thead>
            <tr>
              <th style="width: 30%">Field</th>
              <th style="width: 10%">Type</th>
              <th style="width: 60%">Description</th>
            </tr>
            <tbody>
                <tr>
                  <td>id</td>
                  <td>string</td>
                  <td>Id của nhà cung cấp </td>
                </tr>
                <tr>
                  <td>name</td>
                  <td>string</td>
                  <td>Tên của nhà cung cấp </td>
                </tr>
                <tr>
                  <td>code</td>
                  <td>string</td>
                  <td>Code của nhà cung cấp tương ứng trên hệ thống Loyalty </td>
                </tr>
            </tbody>
          </thead>
        </table>
      </td>
    </tr>
  </tbody>
</table>

@apiParam (data:)   {array}             [vouchers]                Danh sách các voucher khách hàng đã sử dụng
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>string</td>
      <td>ID của voucher</td>
    </tr>
    <tr>
      <td>code</td>
      <td>string</td>
      <td>Mã code của voucher</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>int</td>
      <td>Số lượng voucher sử dụng</td>
    </tr>
  </tbody>
</table>

@apiParam (data:)   {object}            [payment]                   Các thông tin payment mà khách hàng đã phát sinh transaction
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>type<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>Hình thức thanh toán: ATM, cash, MC, VC ...
        <li>Default value: <code>cash</code></li>
      </td>
    </tr>
    <tr>
      <td>name<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM</td>
    </tr>
    <tr>
      <td>code<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card</td>
    </tr>
  </tbody>
</table>


@apiParamExample    {json}      Body example:
{
  "merchant_id": "33322236-0644-4364-bdad-1e73173a886a",
  "business_code": "PINGCOMSHOP",
  "data": [
    {
      "profile_id": "611d2df1-2a13-4174-8988-bed2773be063",
      "profile_type": "B2B",
      "transaction_type": "Buy",
      "transaction_id": "33322236-0644-4364-bdad-1e73173a886a",
      "transaction_stt": "Processing",
      "action_time": 1538703463.440775,
      "transaction_amount": 100000.0,
      "currency_code": "VND",
      "source": "Online",
      "set_name_supplier": ["asdasd", "asdasdas"]
      "store": {
        "id": "33322236-0644-4364-bdad-1e73173a886a",
        "name": "Cua hang chi nhanh Duy Tan",
        "code": "MOBIO_001"
      },
      "items": [
        {
          "id": "SKU_001",
          "name": "san pham 1",
          "category": "category_001",
          "tags": [],
          "quantity": 10,
          "price": 10000.0,
          "currency_code": "VND",
          "supplier": {
            "id": "asdasdasd",
            "name": "nasdasda",
            "code": "asdasdasdasda"
          }
        }
      ],
      "vouchers": [
        {
          "id": "VOUCHER_TET",
          "quantity": 2,
          "code": "ASDFVASWDASD"
        }
      ],
      "payment": {
        "type": "ATM",
        "name": "VCB",
        "code": "asc23113512312312312"
      }
      "transaction_tag": ["TET"],
      "point": -1.0,
      "rank_point": -1.0
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "code": 200,
   "lang": "vi",
   "message": "request thành công."
}
"""

# ------------------------- Transaction_2.0.0 -------------------
# version: 2.0.0
# ---------------------------------------------------------------
"""
@api {post} /transaction  E34 - Transaction Common
@apiDescription API import transaction
@apiVersion 2.0.0
@apiGroup Loyalty API
@apiName Transaction Common

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)   {string}            merchant_id         ID của merchant trong hệ thống
@apiParam (Body:)   {string}            [business_code]     Business Code của khách hàng
@apiParam (Body:)   {array}             data                Data transaction của khách hàng
<li><code>Giới hạn 1000 data mỗi gói tin</code></li>

@apiParam (data:)   {string}            profile_id              UUID profile của khách hàng
@apiParam (data:)   {string}            [profile_type]          Trường xác nhận tập mà khách hàng thuộc về
<li>Allow value: <code>"B2C", "B2C"</code></li>
<li>Default value: <code>"B2C"</code></li>
@apiParam (data:)   {array}             [transaction_tag]       Các nhãn thông tin thêm cho transaction.
<li><code>Nhận một list các string tags</code></li>
@apiParam (data:)   {string}            [transaction_type]      Loại transaction khách hàng tương tác
@apiParam (data:)   {string}            transaction_id          Mã đơn hàng trong hệ thống
@apiParam (data:)   {string}            transaction_stt         Trạng thái đơn hàng đang xử lý trong hệ thống
@apiParam (data:)   {float}             transaction_amount      Số tiền thực sự khách hàng cần phải thanh toán
@apiParam (data:)   {string}            currency_code           Đơn vị tiền tệ (theo chuẩn ISO 4217). <br>
Refer:<a href="https://en.wikipedia.org/wiki/ISO_4217"> ISO_4217 </a>
<li>Default value: <code>VND</code></li>
@apiParam (data:)   {float}             action_time             Timestamp action xảy ra
@apiParam (data:)   {string}            [source]                Tên hệ thống ghi nhân action
<li>Default value: <code>"Offline"</code></code></li>
@apiParam (data:)   {float}             point                   Điểm tiêu dùng khách hàng tích luỹ thông qua các campaign
@apiParam (data:)   {float}             rank_point              Điểm xếp hạng dùng đánh giá hạng thẻ của khách hàng
@apiParam (data:)   {object}            [store]                   Các thông tin store mà khách hàng đã phát sinh transaction
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>string</td>
      <td>ID của của hàng trong hệ thống Mobio</td>
    </tr>
    <tr>
      <td>code</td>
      <td>string</td>
      <td>Code nhận diện của hảng</td>
    </tr>
    <tr>
      <td>name</td>
      <td>string</td>
      <td>Tên của hàng</td>
    </tr>
    <tr>
      <td>address<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>Địa chỉ cửa hàng</td>
    </tr>
  </tbody>
</table>

@apiParam (data:)   {array}             [items]                   Danh sách các item khách hàng
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>string</td>
      <td>ID của vật phẩm</td>
    </tr>
    <tr>
      <td>name</td>
      <td>string</td>
      <td>Tên của vật phẩm</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>string array</td>
      <td>Các nhãn thông tin thêm cho vật phẩm</td>
    </tr>
    <tr>
      <td>category</td>
      <td>string</td>
      <td>Danh mục của vật phẩm</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>int</td>
      <td>Số lượng vật phẩm mà khách hàng đã mua</td>
    </tr>
    <tr>
      <td>price</td>
      <td>float</td>
      <td>Đơn giá hiện tại của vật phẩm</td>
    </tr>
    <tr>
      <td>currency_code</td>
      <td>string</td>
      <td>Đơn vị tiền tệ quy chuẩn tương ứng với đơn giá cho vật phẩm. Refer:<a href="https://en.wikipedia.org/wiki/ISO_4217"> ISO_4217 </a>
        <li>Default value: <code>VND</code></li>
      </td>
    </tr>
  </tbody>
</table>

@apiParam (data:)   {array}             [vouchers]                Danh sách các voucher khách hàng đã sử dụng
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id</td>
      <td>string</td>
      <td>ID của voucher</td>
    </tr>
    <tr>
      <td>code</td>
      <td>string</td>
      <td>Mã code của voucher</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>int</td>
      <td>Số lượng voucher sử dụng</td>
    </tr>
  </tbody>
</table>

@apiParam (data:)   {object}            [payment]                   Các thông tin payment mà khách hàng đã phát sinh transaction
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>type<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>Hình thức thanh toán: ATM, cash, MC, VC ...
        <li>Default value: <code>cash</code></li>
      </td>
    </tr>
    <tr>
      <td>name<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM</td>
    </tr>
    <tr>
      <td>code<span class="label label-optional">optional</span></td>
      <td>string</td>
      <td>Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card</td>
    </tr>
  </tbody>
</table>


@apiParamExample    {json}      Body example:
{
  "merchant_id": "33322236-0644-4364-bdad-1e73173a886a",
  "business_code": "PINGCOMSHOP",
  "data": [
    {
      "profile_id": "611d2df1-2a13-4174-8988-bed2773be063",
      "profile_type": "B2B",
      "transaction_type": "Buy",
      "transaction_id": "33322236-0644-4364-bdad-1e73173a886a",
      "transaction_stt": "Processing",
      "action_time": 1538703463.440775,
      "transaction_amount": 100000.0,
      "currency_code": "VND",
      "source": "Online",
      "store": {
        "id": "33322236-0644-4364-bdad-1e73173a886a",
        "name": "Cua hang chi nhanh Duy Tan",
        "code": "MOBIO_001"
      },
      "items": [
        {
          "id": "SKU_001",
          "name": "san pham 1",
          "category": "category_001",
          "tags": [],
          "quantity": 10,
          "price": 10000.0,
          "currency_code": "VND"
        }
      ],
      "vouchers": [
        {
          "id": "VOUCHER_TET",
          "quantity": 2,
          "code": "ASDFVASWDASD"
        }
      ],
      "payment": {
        "type": "ATM",
        "name": "VCB",
        "code": "asc23113512312312312"
      }
      "transaction_tag": ["TET"],
      "point": -1.0,
      "rank_point": -1.0
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "code": 200,
   "lang": "vi",
   "message": "request thành công."
}
"""

# ------------------------- Transaction_1.0.1 -------------------
# version: 1.0.1
# ---------------------------------------------------------------
"""
@api {post} /transaction  E34 - Transaction Common
@apiDescription API import transaction
@apiVersion 1.0.1
@apiGroup Loyalty API
@apiName Transaction Common

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)   {string}            business_code       Business Code của khách hàng
@apiParam (Body:)   {array}             data                Data transaction của khách hàng
<li><code>Giới hạn 1000 data mỗi gói tin</code></li>

@apiParam (data:)   {string}            profile_id              UUID profile của khách hàng
@apiParam (data:)   {string}            profile_type            Trường xác nhận tập mà khách hàng thuộc về
<li>Allow value: <code>"B2B", "B2C"</code></li>
@apiParam (data:)   {array}             [transaction_tag]       Các nhãn thông tin thêm cho transaction.
<li><code>Nhận một list các string tags</code></li>
@apiParam (data:)   {string}            [transaction_type]      Loại transaction khách hàng tương tác
@apiParam (data:)   {string}            transaction_id          Mã đơn hàng trong hệ thống
@apiParam (data:)   {string}            transaction_stt         Trạng thái đơn hàng đang xử lý trong hệ thống
@apiParam (data:)   {float}             transaction_amount      Số tiền thực sự khách hàng cần phải thanh toán
@apiParam (data:)   {string}            currency_code           Đơn vị tiền tệ (theo chuẩn ISO 4217). <br>
Refer:<a href="https://en.wikipedia.org/wiki/ISO_4217"> ISO_4217 </a>
<li>Default value: <code>VND</code></li>
@apiParam (data:)   {float}             action_time             Timestamp action xảy ra
@apiParam (data:)   {string}            source                  Tên hệ thống ghi nhân action
<li>Allow value: <code>"Offline"</code>, <code>"Online"</code>, <code>"Affilite"</code>, <code>"Wholesales"</code></li>
@apiParam (data:)   {object}            store                   Các thông tin store mà khách hàng đã phát sinh transaction
@apiParam (data:)   {array}             items                   Danh sách các item khách hàng

@apiParam (data:)   {array}             vouchers                Danh sách các voucher khách hàng đã sử dụng
@apiParam (data:)   {float}             point                   Điểm tiêu dùng khách hàng tích luỹ thông qua các campaign
@apiParam (data:)   {float}             rank_point              Điểm xếp hạng dùng đánh giá hạng thẻ của khách hàng

@apiParam (store:)   {string}           id                 ID của của hàng trong hệ thống Mobio
@apiParam (store:)   {string}           code               Code nhận diện của hảng
@apiParam (store:)   {string}           name               Tên của hàng
@apiParam (store:)   {string}           [address]          Địa chỉ cửa hàng

@apiParam (items:)   {string}           id                 ID của vật phẩm
@apiParam (items:)   {string}           name               Tên của vật phẩm
@apiParam (items:)   {array}           [tags]             Các nhãn thông tin thêm cho vật phẩm
<li><code>Nhận một list các string tags</code></li>
@apiParam (items:)   {string}           category           Danh mục của vật phẩm
@apiParam (items:)   {int}              quantity           Số lượng vật phẩm mà khách hàng đã mua
@apiParam (items:)   {float}            price              Đơn giá hiện tại của vật phẩm
@apiParam (items:)   {string}           currency_code           Đơn vị tiền tệ quy chuẩn tương ứng với đơn giá cho vật phẩm
Refer:<a href="https://en.wikipedia.org/wiki/ISO_4217"> ISO_4217 </a>
<li>Default value: <code>VND</code></li>

@apiParam (vouchers:)   {string}            id              ID của voucher
@apiParam (vouchers:)   {string}            code            Mã code của voucher
@apiParam (vouchers:)   {int}               quantity        Số lượng voucher sử dụng

@apiParamExample    {json}      Body example:
{
  "business_code": "PINGCOMSHOP",
  "data": [
    {
      "profile_id": "611d2df1-2a13-4174-8988-bed2773be063",
      "profile_type": "B2B",
      "transaction_type": "Buy",
      "transaction_id": "33322236-0644-4364-bdad-1e73173a886a",
      "transaction_stt": "Processing",
      "action_time": 1538703463.440775,
      "transaction_amount": 100000.0,
      "currency_code": "VND",
      "source": "Online",
      "store": {
        "id": "33322236-0644-4364-bdad-1e73173a886a",
        "name": "Cua hang chi nhanh Duy Tan",
        "code": "MOBIO_001"
      },
      "items": [
        {
          "id": "SKU_001",
          "name": "san pham 1",
          "category": "category_001",
          "tags": [],
          "quantity": 10,
          "price": 10000.0,
          "currency_code": "VND"
        }
      ],
      "vouchers": [
        {
          "id": "VOUCHER_TET",
          "quantity": 2,
          "code": "ASDFVASWDASD"
        }
      ],
      "transaction_tag": ["TET"],
      "point": -1.0,
      "rank_point": -1.0
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "code": 200,
   "lang": "vi",
   "message": "request thành công."
}
"""

# ------------------------- Transaction_1.0.0 -------------------
# version: 1.0.0
# ---------------------------------------------------------------
"""
@api {post} /transaction  Transaction Common
@apiDescription API import transaction
@apiVersion 1.0.0
@apiGroup Loyalty API
@apiName Transaction Common

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam (Body:)   {string}        business_code       Business Code của khách hàng
@apiParam (Body:)   {Object}        data                Data transaction của khách hàng

@apiParam (data:)   {string}        [profile_id]          UUID profile của khách hàng
@apiParam (data:)   {string}        [phone_number]        Số điện thoại của khách
@apiParam (data:)   {string}        [email]               Email của khách hàng
@apiParam (data:)   {string}        [people_id]           CMND của khách hàng
@apiParam (data:)   {string}        [tax_code]            Mã số thuế của khách hàng

@apiParam (data:)   {int}           action_type         Loại action khách hàng tương tác
<li><code>Mua: 1</code></li>
<li><code>Tìm kiếm: 2</code></li>
<li><code>Xem: 3</code></li>
@apiParam (data:)   {float}         action_time         Timestamp action xảy ra
@apiParam (data:)   {int}           amount              Tổng số tiền thanh toán
@apiParam (data:)   {float}         currency_code       Đơn vị tiền tệ (theo chuẩn ISO 4217). <br>
Refer:<a href="https://en.wikipedia.org/wiki/ISO_4217"> ISO_4217 </a>
<li>Default value: <code>VND</code></li>
@apiParam (data:)   {string}        source              Tên hệ thống ghi nhân action
@apiParam (data:)   {array}         items               Danh sách các item khách hàng
@apiParam (data:)   {string}        [name_customer]              Tên khách hàng


@apiParam (items:)   {string}        [id]               ID của item
@apiParam (items:)   {string}        [name]               Tên của item

@apiParamExample    {json}      Body example:
{
  "business_code": "PING_COM_SHOP",
  "datas": [
    {
      "profile_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
      "phone_number": "0985123456",
      "email": "<EMAIL>",
      "people_id": "*********",
      "tax_code": "xxxxxx",
      "action_type": 1,
      "action_time": 1538703463.440775,
      "amount": 100000,
      "currency_code": "VND",
      "source": "Inhouse Syste,",
      "items": [
        {
          "id": "SKU_001",
          "name": "san pham 1"
        },
        ...
      ],
      "name_customer": "Nguyen Van A"
    },
    ...
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
   "code": 200,
   "lang": "vi",
   "message": "request thành công."
}
"""

****************************** List Customer **********************************
* version: 1.0.6                                                              *
* version: 1.0.5                                                              *
* version: 1.0.4                                                              *
* version: 1.0.3                                                              *
* version: 1.0.2                                                              *
* version: 1.0.1                                                              *
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} [HOST]/profiling/etl/api/v1.0/fetch_profile Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng
@apiGroup Profile
@apiVersion 1.0.0
@apiName FetchProfile

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam      (Query:)    {Number}    [per_page]          Số bản ghi trên 1 page. MAX=200, default=50.
@apiParam      (Query:)    {Number}    [start_time]        Thời gian cập nhật profile. So sánh với field updated_time. Kiểu dữ liệu <code>timestamp</code>
@apiParam      (Query:)    {Number}    [end_time]          Thời gian cập nhật profile. So sánh với field updated_time. Kiểu dữ liệu <code>timestamp</code>
@apiParam      (Query:)    {String}    [fields]            Các field muốn lấy; Cách nhau bới dấu <code>,</code>; default: fields=<code>merchant_id,profile_id,created_time,updated_time</code>

@apiSuccess {Array}      data        Danh sách merchant
@apiSuccess {String}     message     Mô tả phản hồi
@apiSuccess {Integer}    code        Mã phản hồi


@apiSuccess (data) {String}     avatar		                Ảnh đại diện
@apiSuccess (data) {Number} 	birth_date				    Ngày sinh
@apiSuccess (data) {Number}     birth_month	                Tháng sinh
@apiSuccess (data) {Number} 	birth_year				    Năm sinh
@apiSuccess (data) {Date}       birthday		            Ngày sinh nhật
@apiSuccess (data) {Array} 	    cards				        Mảng Đối tượng thẻ của khách hàng
@apiSuccess (data) {Long}       clv		                    Tổng số tiền khách hàng giao dịch
@apiSuccess (data) {String} 	company				        Công ty
@apiSuccess (data) {Number}     created_account_type		Kiểu ghi nhận thông tin khách hàng
@apiSuccess (data) {DateTime} 	created_time				Thời điểm tạo
@apiSuccess (data) {DateTime}   customer_created_time		Thời gian tạo profile từ bên thứ 3
@apiSuccess (data) {String} 	customer_id				    ID khách hàng bên thứ 3
@apiSuccess (data) {Number}     degree		                Thông tin trình độ học vấn của profile
@apiSuccess (data) {String} 	district_code				Mã quận huyện
@apiSuccess (data) {String}     driver_license		        Giấy phép lái xe
@apiSuccess (data) {Array} 	    email				        Danh sách các email mà profile này sở hữu
@apiSuccess (data) {String}     face_id		                face_id
@apiSuccess (data) {String} 	fax				            Fax
@apiSuccess (data) {Number}     gender		                Giới tính
<br/><br/>Allowed values:<br/>
<li><code>1: Unknown</code></li>
<li><code>2: Name</code></li>
<li><code>3: Nữ</code></li>
@apiSuccess (data) {Array} 	    hobby				        Sở thích
@apiSuccess (data) {String} 	job				            Nghề nghiệp.
@apiSuccess (data) {Float}      lat		                    Kinh độ
@apiSuccess (data) {Float} 	    lon				            Vĩ độ
@apiSuccess (data) {Number}     marital_status		        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess (data) {String} 	merchant_id				    Mã định danh merchant
@apiSuccess (data) {String}     name		                Tên đầy đủ của Profile
@apiSuccess (data) {Number} 	nation				        Dân tộc
@apiSuccess (data) {Number}     number_transactions		    Số lượng giao dịch
@apiSuccess (data) {Number} 	operation				    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess (data) {Number}     partner_point		        Điểm khách hàng trên hệ thống ngoài
@apiSuccess (data) {String} 	passport				    Passport
@apiSuccess (data) {Number}     point		                Điểm tiêu dùng trên hệ thống Mobio 
@apiSuccess (data) {Object} 	primary_email				Email chính của user
@apiSuccess (data) {Object}     primary_phone		        Điện thoại chính của user
@apiSuccess (data) {Array}      profile_address				Danh sách các địa chỉ của khách hàng
@apiSuccess (data) {Array}      profile_group		        Danh sách các Group mà Staff này quản lý
@apiSuccess (data) {String} 	profile_id				    Mã định danh profile
@apiSuccess (data) {String} 	profile_identify		    Giấy tờ định danh
@apiSuccess (data) {Array}      profile_tags		        Profile tag
@apiSuccess (data) {String} 	province_code				Mã tỉnh thành
@apiSuccess (data) {Array}      push_id		                Danh sách mã push notification 
@apiSuccess (data) {String} 	rank_point				    Xếp hạng điểm trên hệ thống Mobio 
@apiSuccess (data) {Number} 	religiousness				Tôn giáo
@apiSuccess (data) {Array}      secondary_emails		    Các email phụ của user
@apiSuccess (data) {Array} 	    secondary_phones		    Các số điện thoại phụ của user
@apiSuccess (data) {Array}      social_user		            Thông tin người dùng trên mạng xã hội
@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.
@apiSuccess (data) {String} 	source				        Kiểu ghi nhận thông tin khách hàng
@apiSuccess (data) {Array} 	    tags				        Danh sách tag được gán
@apiSuccess (data) {String}     tax_address		            tax_address
@apiSuccess (data) {String} 	tax_code				    tax_code
@apiSuccess (data) {String}     tax_name		            tax_name
@apiSuccess (data) {Array} 	    transaction_event			Thông tin giao dịch của user
@apiSuccess (data) {Datetime} 	updated_time			    Thời gian cập nhật profile
@apiSuccess (data) {String}     vouchers		            Danh sách voucher
@apiSuccess (data) {String} 	ward_code				    Mã phường xã



@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "age": 12,
      "avatar": "https://",
      "birth_date": 19,
      "birth_month": 3,
      "birth_year": 1991,
      "birthday": "1991-03-19",
      "budget_high_threshold": 0,
      "budget_low_threshold": 0,
      "cards": [
          {
              "card_code": "*************",
              "card_id": "187fb2de-9eeb-411e-800e-e78b3f34a855",
              "card_name": "test 26...",
              "id": "a175ec00-3aaf-49b4-9808-b19aa54b3ae0",
              "is_primary": false
          }
      ],
      "cif": [
          "123"
      ],
      "clv": 120.0,
      "company": "CTTNHH Z",
      "created_account_type": 0,
      "created_time": "2020-11-03T05:11:10.087Z",
      "customer_created_time": "2020-11-03T05:11:10.087Z",
      "customer_id": "cid_1",
      "degree": 1,
      "district_code": {
          "id": 122,
          "name": "Huyện Phù Yên"
      },
      "driver_license": [
          "1"
      ],
      "email": [
          "<EMAIL>"
      ],
      "face_id": [
          "1"
      ],
      "fax": "123",
      "gender": 2,
      "hobby": [
          {
              "id": 1,
              "name": "Công nghệ"
          }
      ],
      "income_family": 1,
      "income_high_threshold": 1,
      "income_low_threshold": 1,
      "income_type": 1,
      "job": {
          "id": 10,
          "name": "Ca sỹ"
      },
      "lat": 100.00012,
      "lon": 100.00012,
      "marital_status": {
          "id": 1,
          "name": "Độc thân"
      },
      "merchant_id": [
          "1b99bdcf-d582-4f49-9715-1b61dfff3924"
      ],
      "name": "Hoàng Sơn Tùng",
      "nation": {
          "id": 12,
          "name": "Thái"
      },
      "number_transactions": 12,
      "operation": {
          "id": 1,
          "name": "An ninh - Bảo vệ"
      },
      "partner_point": 123,
      "passport": "123",
      "point": 123,
      "primary_email": {
          "email": "<EMAIL>",
          "status": 0
      },
      "primary_phone": {
          "phone_number": "+84832201234",
          "status": 0
      },
      "profile_address": [
          "abc"
      ],
      "profile_group": [
          "a80f9318-ba71-4e77-b84f-7426b0ae6f98",
          "541005c4-3dff-4063-8e77-66cc140015b8"
      ],
      "profile_id": "161e3611-d02c-488f-bdbb-40a24b6ffee5",
      "profile_identify": [
          {
              "date_verify": "2020-11-03T05:11:10.087Z",
              "identify_type": "identity_card",
              "identify_value": "212121",
              "is_verify": false,
              "verify_by": "test"
          }
      ],
      "profile_tags": [
          {
              "id": "e4cea947-abb8-4d42-b04e-477955e952bf",
              "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
              "tag": "SUB_NEWS_TECH_YES",
              "tag_type": 100
          },
          {
              "id": "1ffac4f5-ffcf-437c-aac3-8a6a311de8b3",
              "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
              "tag": "SUB_PROMOTION_UNIQUE_YES",
              "tag_type": 100
          },
          {
              "id": "a4686880-eb0a-497f-a3d8-f7e7d71f967b",
              "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
              "tag": "SUB_NEWS_GAME_YES",
              "tag_type": 100
          },
          {
              "id": "3b60f0e5-b761-40d7-8986-d12e44b9227d",
              "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
              "tag": "SUB_PROMOTION_WEEK_YES",
              "tag_type": 100
          },
          {
              "id": "01ab55f8-c24a-4825-b5b7-9af25f68cde1",
              "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
              "tag": "SUB_PROMOTION_MONTH_YES",
              "tag_type": 100
          },
          {
              "id": "8b9d99a7-4915-4795-a48c-1d37de32ce48",
              "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
              "tag": "SUB_NEWS_TIPS&TRICKS_YES",
              "tag_type": 100
          },
          {
              "id": "f791f16c-6b6c-4539-9c63-d1afcc35e92c",
              "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
              "tag": "SUB_PROMOTION_LOYALTY_YES",
              "tag_type": 100
          },
          {
              "id": "1bd00d9c-bfb0-453e-9f2b-467e3fbff0a1",
              "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
              "tag": "SUB_EVENT_EXPERIENCE_YES",
              "tag_type": 100
          },
          {
              "id": "4824b95b-c00a-41d5-a77c-4c4449cad257",
              "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
              "tag": "SUB_EVENT_ESPORT_YES",
              "tag_type": 100
          }
      ],
      "province_code": {
          "id": 14,
          "name": "Sơn La"
      },
      "push_id": [
          {
              "count_fail": 0,
              "is_logged": false,
              "os_type": 2,
              "app_id": "MOBIO_APP",
              "last_access": "2020-06-11T03:07:47.000Z",
              "push_id": "rhrTHhQ3tO0Skm9ODaontq"
          }
      ],
      "rank_point": 123,
      "religiousness": {
          "id": 1,
          "name": "Lương giáo"
      },
      "secondary_emails": {
          "secondary": [
              {
                  "email": "+84832201234",
                  "status": 0
              }
          ],
          "secondary_size": 1
      },
      "secondary_phones": {
          "secondary": [
              {
                  "phone_number": "+84832201234",
                  "status": 0
              }
          ],
          "secondary_size": 1
      },
      "social_user": [
          {
              "social_id": "131934164746039",
              "app_id": "123",
              "page_id": "121358061894056",
              "social_id_type": 3,
              "social_type": 1
          }
      ],
      "source": "Nhập Thủ Công",
      "tags": [
          "SUB_NEWS_TECH_YES",
          "SUB_PROMOTION_UNIQUE_YES",
          "SUB_NEWS_GAME_YES",
          "SUB_PROMOTION_WEEK_YES",
          "SUB_PROMOTION_MONTH_YES",
          "SUB_NEWS_TIPS&TRICKS_YES",
          "SUB_PROMOTION_LOYALTY_YES",
          "SUB_EVENT_EXPERIENCE_YES",
          "SUB_EVENT_ESPORT_YES"
      ],
      "tax_address": "123",
      "tax_code": "123",
      "tax_name": "123",
      "transaction_event": [
          {
              "transaction_type": "buy",
              "transaction_time": "2020-09-10T16:00:35.000Z",
              "voucher_ids": [
                  "a08a5469-a35d-4688-8bb6-d51d2b08f320"
              ],
              "first": true,
              "transaction_id": "359e17fb-e378-4db7-ab81-052a23b99117",
              "item_ids": [
                  "5f536fcea7b433c00821335d"
              ],
              "supplier_ids": [
                  "5f597738d2757ab3323b1ad6"
              ],
              "transaction_tag": [
                  "product",
                  "50%",
                  "123"
              ],
              "business_case_id": "541005c4-3dff-4063-8e77-66cc140015b8",
              "lst_category": [
                  "5f33bf65bc17f36d7f8902e6"
              ],
              "transaction_amount": 200000.0,
              "store_id": "5128ad3f-e895-4fe6-84e5-92d916f0a01d",
              "transaction_source": "Offline",
              "last": false,
              "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924"
          }
      ],
      "updated_time": "2020-11-03T05:11:15.238Z",
      "vouchers": [
          {
              "voucher_code": "THN2E5",
              "status": "used",
              "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
              "granted_time": "2020-07-04T08:35:34.000Z",
              "used_time": "2020-07-06T15:35:34.000Z",
              "expired_time": "2020-07-06T15:35:34.000Z",
              "voucher_id": "3134fb05-7845-4dfc-86be-b9f75e6fbbe7",
              "source": "mkt",
              "session_id": "8b3bb7bd-b889-4ec5-86fc-56e1f314fbf10f9af154-d8e3-44d0-88b9-f5ab025ffe51",
              "transaction_id": "123"
          }
      ],
      "ward_code": {
          "name": "Phường Thanh Xuân Trung",
          "id": 355
      }
  },
    ...
  ],
  "paging": {
    ...
  }
}
"""
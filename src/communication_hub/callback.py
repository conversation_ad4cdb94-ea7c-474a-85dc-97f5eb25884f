#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: thong
    Company: M O B I O
    Date Created: 08/04/2025
"""
"""
@api {POST} {public-domain}/partner/communicationhub/external/api/v1.0/callback/config     Cấu hình thông tin callback
@apiGroup CallbackConfig
@apiVersion 1.0.0
@apiName CallbackConfigUpsert

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam	(Body:)			{String}	code			      Mã callback (Thông tin được upsert theo field code)
@apiParam	(Body:)			{String}	feature_code		  Tính năng cần nhận thông tin callback
                                                              <ul>
                                                                <li><code>PUSH_NOTIFY</code> : G<PERSON><PERSON> push app</li>
                                                              </ul>
@apiParam	(Body:)			{Object}	config			      Thông tin cấu hình API callback                                                              
@apiParam	(Body:)			{string}	config.url			  URL                                                              
@apiParam	(Body:)			{string}	[config.method]		  Method API (Default: GET)
                                                              <ul>
                                                                <li><code>GET</code>: GET</li>
                                                                <li><code>POST</code>: POST</li>
                                                                <li><code>PUT</code>: PUT</li>
                                                                <li><code>DELETE</code>: DELETE</li>
                                                                <li><code>PATCH</code>: PATCH</li>
                                                              </ul>
                                                          
@apiParam	(Body:)			{Object}	[config.headers]	  Thông tin Headers                                                              


@apiParamExample {json} Body example
{
    "code": "CAL1",
    "feature_code": "PUSH_NOTIFY",
    "config": {
        "url": "http://callback-data.com.vn/api/v1/callback",
        "method": "POST",
        "headers": {
            "authorization": "Basic kaoiadop-02xl1-mdam"
        }
    }
}

@apiSuccess {String}            tracking_code    Mã tracking api
@apiSuccess {String}            message          Mô tả phản hồi
@apiSuccess {Integer}           code             Mã phản hồi

@apiSuccessExample {json} Response success
{
    "tracking_code": "a2a6aa50-c405-4819-a877-bbfdf918e1be"
    "code": 200,
    "message": "request thành công."
}
"""

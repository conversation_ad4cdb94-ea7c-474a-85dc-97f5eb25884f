#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: thong
    Company: M O B I O
    Date Created: 08/04/2025
"""

"""
@api {POST} {public-domain}/partner/communicationhub/external/api/v1.0/message/push/app Ghi nhận thông tin cần push app 
@apiGroup Message
@apiVersion 1.0.0
@apiName MessagePushApp

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiHeader   (Headers:)   {String}    app-code                    ID app do Mo<PERSON> cung cấp
@apiHeader   (Headers:)   {String}    mobio-signature             Áp dụng thuật toán SHA256(body_request + secret_key), secret_key do Mobio cung cấp

@apiParam	(Body:)		  {ArrayObject}	    data			      danh sách các tin cần push app, limit 20 phần tử
@apiParam	(Body:)		  {String}	        [data.message_id]	  id cho lần gửi tin, mục đích để cập nhật callback kết quả 
@apiParam	(Body:)		  {String}	        data.email	          email của nhân viên 
@apiParam	(Body:)		  {String}	        data.title	          tiêu đề tin nhắn 
@apiParam	(Body:)		  {String}	        data.content	      nội dung tin nhắn, có thể bị giới hạn  
@apiParam	(Body:)		  {Object}	        [data.others]	      nghiệp vụ muốn gửi thêm các thông tin riêng cho app, dạng json key - value 

@apiParam	(Body:)			{Object}	[callback]		    Thông tin đầu nhận callback sau khi xử lý
@apiParam	(Body:)			{String}	callback_info.code	    Mã callback (Mã code đăng ký ở đầu api register callback với feature_code=PUSH_NOTIFY)
@apiParam	(Body:)			{Object}	[callback_info.data_callback]	  Thông tin dữ liệu cần gửi về khi MOBIO gọi đầu callback


@apiParamExample {json} Body example
{
    "data": [
        {
            "message_id": "1234567",
            "email": "<EMAIL>",
            "title": "tiêu đề",
            "content": "nội dung push",
            "others": {
                "number_task": 3,
                "status": "pending" 
            }
        }
    ],
    "callback": {
        "code": "CALLBACK_UPSERT_KHCN",
        "data_callback": {
            "field1": "value1"
        }
    }
}

@apiSuccess {String}            tracking_code    Mã tracking api
@apiSuccess {String}            message          Mô tả phản hồi
@apiSuccess {Integer}           code             Mã phản hồi

@apiSuccessExample {json} Response success
{
    "tracking_code": "a2a6aa50-c405-4819-a877-bbfdf918e1be"
    "code": 200,
    "message": "request thành công."
}

@apiSuccessExample {json} CALLBACK
{
    "message_id": "1234567", // ID message
    "action": "push", // push: đã gửi push, open: Mở message, receive: tiếp nhận yêu cầu 
    "status": "success" // success: Thành công, fail: Thất bại 
    "reason": "Tài khoản chưa có cấu hình push app", // Lý do 
    "data_callback": {  // Thông tin data_callback nhận ở đầu request
        "field1": "value1"
    }
}

"""

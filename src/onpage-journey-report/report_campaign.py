*************************************** Define Report ************************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@apiDefine OnPageJourneyGeneralReportFilter
@apiParam   (Body:)    {String}    campaign_id      ID chiến dịch
@apiParam   (Body:)    {String}    [start_time]     Thời điểm bắt đầu cần lấy dữ liệu bắt đầu. Giá trị gửi lên theo giờ UTC. 
                                                    <br/>Định dạng: <code>%Y-%m-%dT%H:%MZ</code>
@apiParam   (Body:)    {String}    [end_time]       Thời điểm cần kết thúc lấy dữ liệu bắt đầu. Gi<PERSON> trị gửi lên theo giờ UTC. 
                                                    <br/>Định dạng: <code>%Y-%m-%dT%H:%MZ</code>
"""

************************************* Report Access Summary ******************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /campaign/summary-access Tổng Quan Tiếp Cận
@apiGroup ReportGeneralCampaign
@apiVersion 1.0.0
@apiName ReportGeneralCampaignAccessSummary

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse OnPageJourneyGeneralReportFilter
@apiParamExample    {json}  Body:
{
    "campaign_id": "14076142647513735",
    "start_time": "2023-11-05T05:00Z",
    "end_time": "2024-11-05T05:00Z",
}

@apiSuccess   (Description response)    {Number}    profile_visit_source           Profile truy cập trang                  
@apiSuccess   (Description response)    {Number}    profile_approach_journey       Profile tiếp cận hành trình                  
@apiSuccess   (Description response)    {Number}    profile_start_journey          Profile bắt đầu hành trình                  
@apiSuccess   (Description response)    {Number}    profile_in_journey             Profile trong hành trình                 
@apiSuccess   (Description response)    {Number}    profile_finish_journey         Profile kết thúc hành trình                 
                                                                                

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "profile_visit_source": 1840863,
        "profile_approach_journey": 840863,
        "profile_start_journey": 40863,
        "profile_in_journey": 863,
        "profile_finish_journey": 63
    }
}
"""

********************************* Report Interaction Efficiency **************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /campaign/interaction-efficiency Hiệu Quả Tương Tác
@apiGroup ReportGeneralCampaign
@apiVersion 1.0.0
@apiName ReportGeneralCampaignInteractionEfficiency

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse OnPageJourneyGeneralReportFilter
@apiParamExample    {json}  Body:
{
    "campaign_id": "14076142647513735",
    "start_time": "2023-11-05T05:00Z",
    "end_time": "2024-11-05T05:00Z",
}

@apiSuccess   (Description response)    {Number}    total_webpush                      Số lượt gửi thông điệp                  
@apiSuccess   (Description response)    {Number}    total_webpush_send_success         Số lượt gửi thành công              
@apiSuccess   (Description response)    {Number}    total_webpush_interaction          Số lượt tương tác thông điệp               
@apiSuccess   (Description response)    {Number}    total_webpush_skip                 Số lượt bỏ qua thông điệp
                                                                                

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data":
        {
            "total_webpush": 580000,
            "total_webpush_send_success": 48000,
            "total_webpush_interaction": 80000,
            "total_webpush_skip": 23000,
        }
    }
}
"""
************************* Conversion Effectiveness By Webpush All ************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /campaign/conversion-effectiveness-by-webpush/all Hiệu Quả Chuyển Đổi Theo Từng Thông Điệp - All
@apiGroup ReportGeneralCampaign
@apiVersion 1.0.0
@apiName ReportGeneralCampaignConversionEffectivenessAll

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse OnPageJourneyGeneralReportFilter

@apiParamExample    {json}  Body:
{
    "campaign_id": "14076142647513735",
    "start_time": "2023-11-05T05:00Z",
    "end_time": "2024-11-05T05:00Z",
}


@apiSuccess   (Description response)    {String}        popup_id                           ID popup              
@apiSuccess   (Description response)    {Number}        total_webpush                      Số lượt gửi thông điệp                  
@apiSuccess   (Description response)    {Number}        total_webpush_send_success         Số lượt gửi thành công              
@apiSuccess   (Description response)    {Number}        total_webpush_interaction          Số lượt tương tác thông điệp               
@apiSuccess   (Description response)    {Number}        total_webpush_skip                 Số lượt bỏ qua thông điệp
@apiSuccess   (Description response)    {Number}        total_webpush_send_error           Số lượt gửi lỗi

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "popup_id": "627dc568d2eb578f5e7c05ba",
            "total_webpush": 55580000,
            "total_webpush_send_success": 48000,
            "total_webpush_interaction": 80000,
            "total_webpush_skip": 23000,
            "total_webpush_send_error": 99,
        },
        {
            "popup_id": "627dc568d2eb578f5e7c05ba",
            "total_webpush": 55580000,
            "total_webpush_send_success": 48000,
            "total_webpush_interaction": 80000,
            "total_webpush_skip": 23000,
            "total_webpush_send_error": 99,
        }
    ]
}
"""
********************* Conversion Effectiveness By Webpush Timeline ***********************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /campaign/conversion-effectiveness-by-webpush/timeline Hiệu Quả Chuyển Đổi Theo Từng Thông Điệp - TimeLine
@apiGroup ReportGeneralCampaign
@apiVersion 1.0.0
@apiName ReportGeneralCampaignConversionEffectivenessTimeline

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse OnPageJourneyGeneralReportFilter
@apiParam   (Body:)    {String}     report_type     Loại báo cáo. Giá trị: <br/><code>total_webpush</code>=Số lượt gửi
                                                                           <br/><code>total_webpush_send_success</code>= Số lượt gửi thành công
                                                                           <br/><code>total_webpush_interaction</code>= Số lượt tương tác
                                                                           <br/><code>total_webpush_skip</code>= Số lượt bỏ qua
@apiParamExample    {json}  Body:
{
    "campaign_id": "14076142647513735",
    "start_time": "2023-11-05T05:00Z",
    "end_time": "2024-11-05T05:00Z",
    "report_type": "total_webpush_skip"
}


@apiSuccess   (Description response)    {String}        popup_id        Popup ID                  
@apiSuccess   (Description response)    {String}        report_type     Loại báo cáo              
@apiSuccess   (Description response)    {Number}        value           Giá trị               

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
	"data": {
		"2024-05-20": [],
		"2024-05-21": [],
		"2024-05-22": [],
		"2024-05-23": [],
		"2024-05-24": [],
		"2024-05-25": [],
		"2024-05-26": [],
		"2024-05-27": [
			{
				"popup_id": "66503cb4e1cfe1fe72df505a",
				"value": 10
			},
			{
				"popup_id": "76503cb4e1cfe1fe72df505a",
				"value": 20
			}
		],
		"2024-05-28": []
	}
}
"""
*********************************** List Push Webpush Error ********************************
* version: 1.0.0                                                                           *
********************************************************************************************
"""
@api {post} /campaign/list-webpush-send-error List lý do gửi thông điệp không thành công
@apiGroup ReportGeneralCampaign
@apiVersion 1.0.0
@apiName ReportGeneralCampaignListPushWebpushError

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse OnPageJourneyGeneralReportFilter
@apiParam   (Body:)    {String}    popup_id          ID popup         
@apiParam   (Body:)    {String}    [after_token]     Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Body:)    {Number}    [per_page]        Số phần tử trên một page. Mặc định: 10

@apiParamExample    {json}  Body:
{
    "popup_id": "627dc568d2eb578f5e7c05ba",
    "campaign_id": "14076142647513735",
    "start_time": "2023-11-05T05:00Z",
    "end_time": "2024-11-05T05:00Z",
    "after_token": "YXNkaGZha2RoZmFrZGZh",
    "per_page": 10
}
@apiSuccess   (Description response)    {String}       profile_id               ID Profile              
@apiSuccess   (Description response)    {Number}       push_error_reason        Lý do hiển thị không thành công
@apiSuccess   (Description response)    {Number}       push_time                Thời gian gửi thông điệp
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "profile_id": "9259c0b9-13f6-466f-bdd5-2c001bd0ebdf",
            "push_error_reason": "Khác",
            "push_time": "2023-11-05T05:00Z"
        },
        {
            "profile_id": "9259c0b9-13f6-466f-bdd5-2c001bd0ebdf",
            "push_error_reason": "Khác",
            "push_time": "2023-11-05T05:00Z"
        }
    ],
    "paging": {
        "total_items": 10,
        "after_token": "YXNkaGZha2RoZmFrZGZh"
    }
}
"""

*********************************** Total Push Webpush Error ********************************
* version: 1.0.0                                                                           *
********************************************************************************************
"""
@api {post} /campaign/list-webpush-send-error/total Tổng số lý do gửi thông điệp không thành công
@apiGroup ReportGeneralCampaign
@apiVersion 1.0.0
@apiName ReportGeneralCampaignTotalPushWebpushError

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse OnPageJourneyGeneralReportFilter
@apiParam   (Body:)    {String}    popup_id     ID popup         
@apiParamExample    {json}  Body:
{
    "popup_id": "627dc568d2eb578f5e7c05ba",
    "campaign_id": "14076142647513735",
    "start_time": "2023-11-05T05:00Z",
    "end_time": "2024-11-05T05:00Z",
}
@apiSuccess   (Description response)    {Integer}       total_push_error    Tổng số lý do gửi thông điệp không thành công              
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "total_push_error": 456821
    }
}
"""
*********************************** Export Push Webpush Error ******************************
* version: 1.0.0                                                                           *
********************************************************************************************
"""
@api {post} /campaign/list-webpush-send-error/export Export list lý do gửi thông điệp không thành công
@apiGroup ReportGeneralCampaign
@apiVersion 1.0.0
@apiName ReportGeneralCampaignExportListPushWebpushError

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse OnPageJourneyGeneralReportFilter
@apiParam   (Body:)    {String}    popup_id     ID popup         
@apiParamExample    {json}  Body:
{
    "popup_id": "627dc568d2eb578f5e7c05ba",
    "campaign_id": "14076142647513735",
    "start_time": "2023-11-05T05:00Z",
    "end_time": "2024-11-05T05:00Z",
}
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công"
}
"""

****************************** Conversion Effectiveness By Event ***************************
* version: 1.0.0                                                                           *
********************************************************************************************
"""
@api {post} /campaign/conversion-effectiveness-by-event Hiệu Quả Chuyển Đổi Theo Event
@apiGroup ReportGeneralCampaign
@apiVersion 1.0.0
@apiName ReportGeneralCampaignConversionEffectivenessByEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse OnPageJourneyGeneralReportFilter
@apiParam   (Body:)    {ListObject}     event_node               Danh sách event cần lấy báo cáo chuyển đổi
@apiParam   (Body:)    {String}         event_node.event_key     Event key
@apiParam   (Body:)    {String}         event_node.node_id       Node ID

@apiParamExample    {json}  Body:
{
	"campaign_id": "38959480680785161",
	"start_time": "2023-05-20T00:00Z",
	"event_node":  [
		{
			"event_key": "bam_vao_sign_in1_1716344306",
			"node_id": "****************"
		},
		{
			"event_key": "bam_vao_user_more_1717746905",
			"node_id": "****************"
		},
		{
			"event_key": "bam_vao_icon_easy_layout_1714706432",
			"node_id": "****************"
		}
	]
}

@apiSuccess   (Description response)    {String}            event_key                   Event key           
@apiSuccess   (Description response)    {String}            node_id                     Node ID                
@apiSuccess   (Description response)    {Number}            total_conversion            Số lượt chuyển đổi của khối

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "event_key": "bam_va_moi_trang_1715673924",
            "node_id": "14076142647513735",
            "total_conversion": 47221
        },
        {
            "event_key": "bam_senmail_va_trang_tuong_tu_mobio_1_1715596127",
            "node_id": "14076142647513735",
            "total_conversion": 47221
        },
        {
            "event_key": "bam_phai_va_tuong_tu_03_1715336127",
            "node_id": "14076142647513735",
            "total_conversion": 584
        }
    ]
}
"""

********************************** List All Event In Campaign ****************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /campaign/list-event Danh sách all event trong campaign
@apiGroup ReportGeneralCampaign
@apiVersion 1.0.0
@apiName ReportGeneralCampaignListAllEventInCampaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Body:)    {String}     campaign_id           ID campaign

@apiParamExample    {json}  Body:
{
    "campaign_id" : "8044651560693501"
}

@apiSuccess   (Description repsonse)        {String}        event_key       Event key
@apiSuccess   (Description repsonse)        {String}        event_name      Tên event        
@apiSuccess   (Description repsonse)        {String}        node_id         Node ID        
@apiSuccess   (Description repsonse)        {String}        node_name       Tên node        

@apiSuccessExample {json} Response 200
{
	"code": 200,
	"data": [
		{
			"event_key": "bam_phai",
			"event_name": "bấm phải",
			"node_id": "29221447172441626",
			"node_name": "Đối tượng mục tiêu"
		},
		{
			"event_key": "gui_mail",
			"event_name": "Nấm mũi tên gửi mail",
			"node_id": "29221447172441626",
			"node_name": "Đối tượng mục tiêu"
		}
	],
	"lang": "vi",
	"message": "request thành công."
}
"""


************************************* Report Detail Campaign *****************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /campaign/summary-all-node Báo cáo tổng quan số liệu tất cả node của campaign
@apiGroup ReportDetailCampaign
@apiVersion 1.0.0
@apiName ReportDetailCampaignSummaryAllNode

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse OnPageJourneyGeneralReportFilter
@apiParamExample    {json}  Body:
{
    "campaign_id": "14076142647513735",
    "start_time": "2023-11-05T05:00Z",
    "end_time": "2024-11-05T05:00Z",
}
@apiSuccess   (Description response)    {String}     node_id                        ID node    
@apiSuccess   (Description response)    {Number}     profile_innode                 Số profile vào khối    
@apiSuccess   (Description response)    {Number}     profile_finish                 Số profile hoàn thành khối    
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "diagram": {
            "nodes": [{
                    "code": "TARGET",
                    "config": {},
                    "element_type": "TRIGGER",
                    "id": "941dbebc-dba2-11ee-8e61-581cf8f75567",
                    "name": "Đối tượng mục tiêu",
                    "next_id": "941dc10a-dba2-11ee-8e61-581cf8f75567",
                    "pre_id": null,
                    "specific_counter_current_code": 0,
                    "specific_height": 48,
                    "specific_init": true,
                    "specific_width": 165,
                    "specific_x": 709,
                    "specific_y": 97
                },
                {
                    "code": "EXIT",
                    "config": {},
                    "element_type": "EXIT",
                    "id": "941dc10a-dba2-11ee-8e61-581cf8f75567",
                    "name": "Kết thúc hành trình",
                    "next_id": null,
                    "pre_id": "941dbebc-dba2-11ee-8e61-581cf8f75567",
                    "specific_counter_current_code": 0,
                    "specific_height": 48,
                    "specific_init": true,
                    "specific_width": 165,
                    "specific_x": 709,
                    "specific_y": 205
                }
            ]
        },
        "nodes": [{
                "element_type": "OPERATION",
                "node_config": {
                    "wait_time_unit": "MINUTES",
                    "wait_time_value": 123,
                    "wait_type": "AFTER_TIME"
                },
                "node_connection": [{
                    "next_node_id": "44844720195811525",
                    "previous_node_id": [
                        "15011929169379414"
                    ]
                }],
                "node_id": "66780806664235564",
                "node_name": "Chờ 1",
                "node_type": "WAIT",
                "report": {
                    "profile_innode": 88,
                    "profile_finish_node": 25
                }
            },
            {
                "element_type": "EXIT",
                "node_connection": [{
                    "next_node_id": "",
                    "previous_node_id": [
                        "66780806664235564"
                    ]
                }],
                "node_id": "44844720195811525",
                "node_name": "Kết thúc hành trình 4",
                "node_type": "EXIT"
            },
            {
                "element_type": "SPECIFIC_CONDITION",
                "node_config": {
                    "capture_profile_exit": false,
                    "trigger": {
                        "audience_id": "3ec55b8d-9b4d-426f-a0f7-b5015635307d",
                        "triggers": [{
                            "event_key": "cri_olap_pc_bam_vao_racing_1713950737",
                            "trigger_id": "11617806637732049"
                        }]
                    },
                    "waiting_time_after_primary_unit": "MINUTES",
                    "waiting_time_after_primary_value": 10
                },
                "node_connection": [{
                        "next_node_id": "66780806664235564",
                        "previous_node_id": [
                            "57227502858026176"
                        ],
                        "result": "yes"
                    },
                    {
                        "next_node_id": "63784218786108170",
                        "previous_node_id": [
                            "57227502858026176"
                        ],
                        "result": "no"
                    }
                ],
                "node_id": "15011929169379414",
                "node_name": "fgfg",
                "node_type": "CONDITION_EVENT",
                "report": {
                    "profile_innode": 8,
                    "profile_finish_node": 4
                }
            },
            {
                "element_type": "SPECIFIC_CONDITION",
                "node_config": {
                    "profile_audience_id": "e591f7f5-38bd-4cbc-8bb6-9fb807a4d48d",
                    "profile_target_type": "SPECIFIC_CONDITION"
                },
                "node_connection": [{
                        "next_node_id": "37653971356107795",
                        "previous_node_id": [
                            "15011929169379414"
                        ],
                        "result": "yes"

                    },
                    {
                        "next_node_id": "35145404460999254",
                        "previous_node_id": [
                            "15011929169379414"
                        ],
                        "result": "no"
                    }
                ],
                "node_id": "63784218786108170",
                "node_name": "Kiểm tra thông tin Profiles 1",
                "node_type": "CONDITION_FILTER_PROFILE",
                "report": {
                    "profile_innode": 872,
                    "profile_finish_node": 425
                }
            },
            {
                "element_type": "TRIGGER",
                "node_config": {
                    "profile_audience_id": "41feb184-b3c3-4d4e-b214-01e6188aa828",
                    "profile_target_type": "SPECIFIC_CONDITION",
                    "trigger": {
                        "audience_id": "f5390a99-5a5b-4622-809e-8e9932d169f3",
                        "triggers": [{
                            "event_key": "cri_olap_pc_bam_vao_sign_in_1713859497",
                            "trigger_id": "43038133043257497"
                        }]
                    },
                    "waiting_time_after_primary_value": 0
                },
                "node_connection": [{
                    "next_node_id": "57227502858026176",
                    "previous_node_id": []
                }],
                "node_id": "21667365200016454",
                "node_name": "Đối tượng mục tiêu",
                "node_type": "TARGET",
                "report": {
                    "profile_innode": 78,
                    "profile_finish_node": 9
                }
            },
            {
                "element_type": "EXIT",
                "node_connection": [{
                    "next_node_id": "",
                    "previous_node_id": [
                        "63784218786108170"
                    ]
                }],
                "node_id": "35145404460999254",
                "node_name": "Kết thúc hành trình 7",
                "node_type": "EXIT"
            },
            {
                "element_type": "EXIT",
                "node_connection": [{
                    "next_node_id": "",
                    "previous_node_id": [
                        "63784218786108170"
                    ]
                }],
                "node_id": "37653971356107795",
                "node_name": "Kết thúc hành trình 6",
                "node_type": "EXIT",
                "report": {
                    "profile_finish_node": 45
                }
            },
            {
                "element_type": "MESSAGE",
                "node_config": {
                    "popup_id": "66276dc5a73d1ebe6a34979e",
                    "popup_position": "tc"
                },
                "node_connection": [{
                    "next_node_id": "15011929169379414",
                    "previous_node_id": [
                        "21667365200016454"
                    ]
                }],
                "node_id": "57227502858026176",
                "node_name": "popup",
                "node_type": "ACTION_WEBPUSH",
                "report": {
                    "total_webpush_interaction": 5000,
                    "total_webpush_push_success": 12478
                }
            }
        ]
    }
}
"""

******************************** Report Detail Campaign Node *****************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /campaign/detail/node Báo cáo chi tiết node
@apiGroup ReportDetailCampaign
@apiVersion 1.0.0
@apiName ReportDetailCampaignDetailNode

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse OnPageJourneyGeneralReportFilter
@apiParam   (Body:)    {String}    node_id      ID node 

@apiParamExample    {json}  Body:
{
    "campaign_id": "14076142647513735",
    "node_id": "8044651560693501",
    "start_time": "2023-11-05T05:00Z",
    "end_time": "2024-11-05T05:00Z",
}


@apiSuccess   (Description response)    {String}      node_id           ID node    
@apiSuccess   (Description response)    {Number}     node_name         Số profile vào khối
@apiSuccess   (Description response)    {String}      node_type         Kiểu node. Giá trị:
                                                                        <br/><code>TARGET</code>=node đối tượng mục tiêu<br/>
                                                                        <code>CONDITION_EVENT</code>=node kiểm tra event<br/>
                                                                        <code>CONDITION_FILTER_PROFILE</code>=node kiểm tra thông tin profile<br/>
                                                                        <code>ACTION_WEBPUSH</code>=node webpush<br/>
                                                                        <code>WAIT</code>=node chờ<br/>
                                                                        <code>EXIT</code>=node thoát<br/>    
@apiSuccess   (Description response)    {Object}     report_data        Data trả về dựa theo <code>node_type</code>    


@apiSuccess   (TARGET report_data:)    {Number}        profile_innode                 Số profile vào khối    
@apiSuccess   (TARGET report_data:)    {Number}        profile_finish                 Số profile hoàn thành khối    
@apiSuccess   (TARGET report_data:)    {Number}        profile_not_finish             Số profile chưa hoàn thành khối    
@apiSuccess   (TARGET report_data:)    {ListObject}    events                         Danh sách data event
@apiSuccess   (TARGET report_data:)    {String}        events.event_key               Event key           
@apiSuccess   (TARGET report_data:)    {String}        events.event_name              Tên event                 
@apiSuccessExample {json} Response 200 [node_type:TARGET]
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "node_id": "14076142647513735",
        "node_name": "Tên khối",
        "node_type": "TARGET",
        "report_data": {
            "profile_innode": 344953,
            "profile_finish_node": 344953,
            "profile_not_finish_node": 5413,
            "events": [
                {
                    "event_key": "bam_va_moi_trang_1715673924",
                    "event_name": "Bấm và mở trang",
                },
                {
                    "event_key": "bam_senmail_va_trang_tuong_tu_mobio_1_1715596127",
                    "event_name": "Bấm sendmail",
                },
                {
                    "event_key": "bam_phai_va_tuong_tu_03_1715336127",
                    "event_name": "Bấm phải",
                }
            ]
        }
    }
}


@apiSuccess   (CONDITION_EVENT report_data:)    {Number}        profile_innode                      Số profile vào khối    
@apiSuccess   (CONDITION_EVENT report_data:)    {Number}        profile_dropoff                     Số profile hoàn thành khối    
@apiSuccess   (CONDITION_EVENT report_data:)    {Number}        profile_satisfied                   Profile thỏa mãn khối    
@apiSuccess   (CONDITION_EVENT report_data:)    {Number}        profile_not_satisfied               Profile chưa thỏa mãn khối 
@apiSuccess   (CONDITION_EVENT report_data:)    {ListObject}    events                              Danh sách data event
@apiSuccess   (CONDITION_EVENT report_data:)    {String}        events.event_key                    Event key           
@apiSuccess   (CONDITION_EVENT report_data:)    {String}        events.event_name                   Tên event                 
@apiSuccessExample {json} Response 200 [node_type:CONDITION_EVENT]
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "node_id": "14076142647513735",
        "node_name": "Tên khối",
        "node_type": "CONDITION_EVENT",
        "report_data": {
            "profile_innode": 344953,
            "profile_dropoff": 344953,
            "profile_satisfied": 125,
            "profile_not_satisfied": 4589,
            "events": [
                {
                    "event_key": "bam_va_moi_trang_1715673924",
                    "event_name": "Bấm và mở trang"
                },
                {
                    "event_key": "bam_senmail_va_trang_tuong_tu_mobio_1_1715596127",
                    "event_name": "Bấm sendmail"
                },
                {
                    "event_key": "bam_phai_va_tuong_tu_03_1715336127",
                    "event_name": "Bấm phải"
                }
            ]
        }
    }
}

@apiSuccess   (CONDITION_FILTER_PROFILE report_data:)    {Number}        profile_innode                      Số profile vào khối    
@apiSuccess   (CONDITION_FILTER_PROFILE report_data:)    {Number}        profile_satisfied                   Profile thỏa mãn khối    
@apiSuccess   (CONDITION_FILTER_PROFILE report_data:)    {Number}        profile_not_satisfied               Profile chưa thỏa mãn khối 
@apiSuccessExample {json} Response 200 [node_type:CONDITION_FILTER_PROFILE]
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "node_id": "14076142647513735",
        "node_name": "Tên khối",
        "node_type": "CONDITION_FILTER_PROFILE",
        "report_data": {
            "profile_innode": 344953,
            "profile_satisfied": 125,
            "profile_not_satisfied": 4589,
        }
    }
}

@apiSuccess   (ACTION_WEBPUSH report_data:)    {Number}      profile_innode                         Profile vào khối        
@apiSuccess   (ACTION_WEBPUSH report_data:)    {String}      popup_id                               Popup id
@apiSuccess   (ACTION_WEBPUSH report_data:)    {Number}      total_webpush                          Số lượt gửi thông điệp                  
@apiSuccess   (ACTION_WEBPUSH report_data:)    {Number}      total_webpush_send_success             Số lượt gửi thành công              
@apiSuccess   (ACTION_WEBPUSH report_data:)    {Number}      total_webpush_interaction              Số lượt tương tác thông điệp               
@apiSuccess   (ACTION_WEBPUSH report_data:)    {Number}      total_webpush_skip                     Số lượt bỏ qua thông điệp
@apiSuccessExample {json} Response 200 [node_type:ACTION_WEBPUSH] ()
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "node_id": "14076142647513735",
        "node_name": "Tên khối",
        "node_type": "ACTION_WEBPUSH",
        "report_data": {
            "profile_innode": 123254,
            "total_webpush": 123254,
            "total_webpush_send_success": 123254,
            "total_webpush_interaction": 123254,
            "total_webpush_skip": 123254,
            "popup_id": "66387d96bb80bd4a59fbc8d9",
        }
    }
}

@apiSuccess   (WAIT report_data:)       {Number}        profile_innode        Số profile vào khối
@apiSuccess   (WAIT report_data:)       {Number}        profile_finish        Số profile hoàn thành khối    
@apiSuccessExample {json} Response 200 [node_type:WAIT]
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "node_id": "14076142647513735",
        "node_name": "Tên khối",
        "node_type": "WAIT",
        "report_data": {
            "profile_innode": 123254,
            "profile_finish": 545
        }
    }
}

@apiSuccess   (EXIT report_data:)    {Number}         profile_innode        Số profile vào khối    
@apiSuccessExample {json} Response 200 [node_type:EXIT]
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "node_id": "14076142647513735",
        "node_name": "Tên khối",
        "node_type": "EXIT",
        "report_data": {
            "profile_innode": 123254
        }
    }
}
"""

****************************** Report Webpush List Click Msg *****************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /campaign/detail/node/list-click-msg-webpush Báo cáo danh sách lượt nhấp vào thông điệp
@apiGroup ReportDetailCampaign
@apiVersion 1.0.0
@apiName ReportDetailCampaignDetailNodeWebpusnListClickMsg

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiUse OnPageJourneyGeneralReportFilter

@apiParam   (Body:)    {String}    node_id           ID node <code>WEBPUSH</code>
@apiParam   (Body:)    {String}    [after_token]     Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Body:)    {Number}    [per_page]        Số phần tử trên một page. Mặc định: 10

@apiParamExample    {json}  Body:
{
    "node_id"    : "8044651560693501",
    "start_time" : "2023-11-05T05:00Z",
    "end_time"   : "2024-11-05T05:00Z",
    "after_token": "YXNkaGZha2RoZmFrZGZh",
    "per_page"   : 10
}

@apiSuccess   (Description repsonse)        {String}        interacted_position_id      Vị trí tương tác
@apiSuccess   (Description repsonse)        {String}        interacted_type             Loại tương tác        
@apiSuccess   (Description repsonse)        {Number}        total_click                 Số lượt nhấp

@apiSuccessExample {json} Response 200 [node_type:TARGET]
{
    "code"   : 200,
    "message": "Request thành công",
    "data"   : [
        {
            "interacted_position_id": "9259c0b9-13f6-466f-bdd5-2c001bd0ebdf",
            "interacted_type"       : "CLICK",
            "total_click"           : 10
        },
        {
            "interacted_position_id": "9259c0b9-13f6-466f-bdd5-2c001bd0ebdf",
            "interacted_type"       : "CLICK",
            "total_click"           : 10
        }
    ],
    "paging": {
        "total_items": 10,
        "after_token": "YXNkaGZha2RoZmFrZGZh"
    }
}
"""
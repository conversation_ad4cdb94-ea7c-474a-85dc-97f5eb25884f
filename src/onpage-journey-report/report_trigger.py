**************************************** Report Summary By Ids ***************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {POST} /trigger/summary-by-ids     Báo cáo tổng quan trigger theo ids
@apiGroup ReportTrigger
@apiVersion 1.0.0
@apiName ReportTriggerSummaryByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam            (Body:)     {ArrayString} ids           Danh sách <code>ID</code> các trigger cần lấy data
@apiParamExample     {json}      Body example
{
    "ids": ["61601a2acb97585cd950d847", "66bc2d5fff66924c41601207"]
}

@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {ArrayObject}       data                            Dữ liệu trả về
@apiSuccess     {String}            data.id                         <code>ID</code> trigger
@apiSuccess     {String}            data.total_profile              Tổng số profile
@apiSuccess     {String}            data.total_execution            Tổng số lần phát sinh trigger


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "id": "61601a2acb97585cd950d847",
            "total_profile": 1246761,
            "total_execution": 0,
        },
        {
            "id": "66bc2d5fff66924c41601207",
            "total_profile": 23,
            "total_execution": 2451
        },
    ]
}
"""
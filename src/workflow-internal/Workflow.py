**************************** List workflow by email_domain ********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /workflow/list-by-email-domain Danh sách workflow theo email domain
@apiDescription  Sử dụng key service-host = <code>workflow-app-internal-service-host</code> để lấy domain
@apiGroup Workflow
@apiVersion 1.0.0
@apiName ListWorkflowByEmailDomain

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiParam   (Query:)    {String}    email_domain  Domain cần lấy danh sách workflow

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "workflow_id": "f223650b-c2b4-4fe6-978a-409cdeedf946",
            "workflow_name": "workflow 1",
            "status": "PROCESSING",
            "email_inuse": ["<EMAIL>", "<EMAIL>"]
        },
        {
            "workflow_id": "4801f658-32d5-11ef-aea6-38d57a786a3e",
            "workflow_name": "workflow 2",
            "status": "PAUSED",
            "email_inuse": ["<EMAIL>", "<EMAIL>"]
        }
    ]
}
"""
******************* Queue ghi nhận workflow thay đổi trạng thái  *************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {queue} /[queue] Ghi nhận workflow thay đổi trạng thái
@apiGroup WorkflowQueue
@apiVersion 1.0.0
@apiName QueueListenWorkflowStatusChange
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (QueueName:)    {String}    topic_name  wf_listen_workflow_status_change

@apiParam   (QueueData:)    {String}    merchant_id  Mã merchant
@apiParam   (QueueData:)    {String}    workflow_id  Mã workflow
@apiParam   (QueueData:)    {String}    target_type  Đối tượng workflow. Giá trị: <br/>
                                                                                 <code>USER</code>=User<br/>
                                                                                 <code>MAILBOX</code>=Mailbox<br/>
                                                                                 <code>TASK</code>=Task<br/>
                                                                                 <code>PROFILE</code>=Profile<br/>
                                                                                 <code>OPPTY</code>=Oppty<br/>
@apiParam   (QueueData:)    {String}    workflow_type  Loại workflow. Giá trị: <br/><code>EVENT</code>=Khi phát sinh event
                                                                                <br/><code>SCHEDULE</code>=Lịch trình
@apiParam   (QueueData:)    {String}    status  Trạng thái workflow mới được thay đổi. Giá trị: <br/><code>PROCESSING</code>=Đang diễn ra
                                                                                                <br/><code>PROCESS_WAITING</code>=Chờ đến lịch
                                                                                                <br/><code>DRAFT</code>=Nháp
                                                                                                <br/><code>FINISH</code>=Đã hoàn thành
                                                                                                <br/><code>PAUSED</code>=Tạm dừng
@apiParamExample    {json}  QueueData:
{
    "merchant_id": "146940fa-0476-11ee-824a-38d57a786a3d",
    "workflow_id": "146940fa-0476-11ee-824a-38d57a786a3d",
    "target_type": "146940fa-0476-11ee-824a-38d57a786a3d",
    "workflow_type": "146940fa-0476-11ee-824a-38d57a786a3d",
    "status": "PAUSED"
}
"""
************************* Lấy thông tin chi tiết Workflow ********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /workflow/detail Lấy thông tin chi tiết Workflow
@apiGroup Workflow
@apiVersion 1.0.0
@apiName DetailWorkflow

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}    workflow_id  Mã workflow cần lấy thông tin chi tiết

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "created_time": "2023-06-02T09:51:15Z",
    "created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "status": "DRAFT",
    "target_type": "USER",
    "updated_time": "2023-06-02T09:51:15Z",
    "updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "workflow_description": "",
    "workflow_id": "0425e570-012b-11ee-824a-38d57a786a3d",
    "workflow_name": "[HoaTest] Tạo ticket sau khi profile phát sinh cuộc gọi",
    "workflow_type": "EVENT"
}
"""
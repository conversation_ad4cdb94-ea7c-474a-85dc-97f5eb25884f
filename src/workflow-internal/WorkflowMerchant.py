**************************** Setup merchant default data *********************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {post} /merchant/default-data Tạo dữ liệu mặc định cho merchant
@apiDescription  Sử dụng key service-host = <code>workflow-app-internal-service-host</code> để lấy domain
@apiGroup WorkflowMerchant
@apiVersion 1.0.0
@apiName SetupMerchantDefaultData

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header
@apiParam   (Body:)    {String}    [merchant_ids]  Danh sách merchant id cần cấu hình (Nếu có thông tin này sẽ ưu tiên dùng, nếu không sẽ dùng x-merchant-id trên header)
@apiParamExample    {json}  Body:
{
    "merchant_ids": ["4c06480e-0e1e-4d2b-83e1-6660f6b3a2bd"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công"
}
"""
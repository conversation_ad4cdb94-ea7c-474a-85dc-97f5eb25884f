#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 06/08/2024
"""

# ---------- Sắp xếp lại thư mục -----------
"""
@api {POST} {HOST}/knowledge-base/api/v1.0/folders/actions/reorder Sắp xếp thư mục
@apiDescription API cập nhật lại vị trí các thư mục trong cùng 1 thư mục cha.
@apiGroup Folder
@apiVersion 1.0.0
@apiName ReorderFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiParam   (Body:)   [parent_folder_id]   <code>ID</code> thư mục cha. <PERSON><PERSON>u sắp xếp lại các thư mục gốc thì không cần t<PERSON>n field này.
@apiParam   (Body:)   folders   Danh sách object thư mục đ<PERSON><PERSON> s<PERSON>p xếp lại.
@apiParam   (Body:)   folders.id   <code>ID</code> thư mục.
@apiParamExample  {json}  Body example:
{
  "parent_folder_id": "642f85314d4356e983dd7414",
  "folders": [
    {
      "id": "642f85314d4356e983dd7415",
      "order": 1
    },
    {
      "id": "642f85314d4356e983dd7416",
      "order": 2
    },
    {
      "id": "642f85314d4356e983dd7417",
      "order": 3
    },
    {
      "id": "642f85314d4356e983dd7418",
      "order": 4
    }
  ]
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

# ---------- Di chuyển thư mục -----------
"""
@api {POST} {HOST}/knowledge-base/api/v1.0/folders/actions/batch-moving  Chuyển thư mục
@apiDescription API chuyển tất cả cây thư mục (bao gồm: thư mục được chọn, toàn bộ thư mục con và toàn bộ bài viết) tới thư mục đích.
<br/><br/><code>Chú ý:</code> Thư mục đích phải đảm bảo tuân thủ giới hạn về cấp sau khi lưu trữ các thư mục con chuyển tới.
<br/>Ví dụ: Nếu giới hạn cấp của cây là 5, độ sâu lớn nhất của các các thư mục cần chuyển là 3 thì thư mục đích phải ở cấp 2.
@apiGroup Folder
@apiVersion 1.0.0
@apiName MovingBatchFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiParam   (Body:)   {ArrayString}  folder_ids  Danh sách định danh của thư mục cần xóa.
@apiParam   (Body:)   {String}  destination_folder_id   Định danh thư mục đích dùng để chứa các thư mục con và bài viết của các thư mục bị xóa.
@apiParamExample  {json}  Body example:
{
  "folder_ids": [
    "642f85314d4356e983dd7415",
    "642fc65e2af4e1dbe42ea896",
    "6433c0959870fa17a0219613",
    "643ccc4417a634e25f59fe93"
  ],
  "destination_folder_id": "0p1ecc4417a634e25f59pk3g"
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

# ---------- Sắp xếp lại thư mục -----------
"""
@api {POST} {HOST}/knowledge-base/api/v1.0/folders/<folder_id>/actions/reorder    Sắp xếp thư mục con
@apiDescription API cập nhật lại vị trí các thư mục trong cùng 1 thư mục cha.
@apiGroup Folder
@apiVersion 1.0.0
@apiName ReorderFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiParam   (Body:)   folders   Danh sách object thư mục được sắp xếp lại.
@apiParam   (Body:)   folders.id   <code>ID</code> thư mục.
@apiParamExample  {json}  Body example:
{
  "folders": [
    {
      "id": "642f85314d4356e983dd7415",
      "order": 1
    },
    {
      "id": "642f85314d4356e983dd7416",
      "order": 2
    },
    {
      "id": "642f85314d4356e983dd7417",
      "order": 3
    },
    {
      "id": "642f85314d4356e983dd7418",
      "order": 4
    }
  ]
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

# ---------- Lấy danh sách thư mục(search) -----------
"""
@api {post} {HOST}/knowledge-base/api/v1.0/folders/actions/search  Search thư mục
@apiDescription API lấy danh sách tất cả thư mục thỏa mãn điều kiện.
@apiGroup Folder
@apiVersion 1.0.0
@apiName SearchFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query
@apiUse paging_tokens

@apiParam   (Body:)  {String}   [parent_id]  <code>id</code> thư mục cha. Chỉ lấy danh sách thư mục trong thư mục cha.
@apiParam   (Body:)  {String}   [search]   Từ khóa tìm kiếm. Lấy các thư mục mà tên có chứa từ khóa tìm kiếm.
@apiParam   (Body:)  {Int}   [level]   Cấp trong cây thư mục. Lấy các thư mục có cấp nhỏ hơn hoặc bằng <code>level</code> truyền lên.
@apiParam   (Body:)   {Object}  [exclude]   Loại trừ các thư mục khi trả về kết quả.
@apiParam   (Body:)   {ArrayString}   [exclude..folder_ids]   Danh sách id cần loại trừ.
@apiParam   (Body:)   {Bool}   [exclude..is_recursive]   Nếu <code>is_recursive=true</code> kết quả trả về sẽ loại trừ cả các thư mục con của các thư mục trong <code>folder_ids</code>.<br/>Ngoài ra, sẽ chỉ loại trừ các thư mục <code>folder_ids</code>.
@apiParamExample  {json}  Body example:
{
  "parent_id": "642f85314d4356e983dd7415",
  "search": "tài liệu",
  "level": 3,
  "exclude": {
    "folder_ids": [
      "642f85314d4356e983dd7415",
      "642fc65e2af4e1dbe42ea896",
      "6433c0959870fa17a0219613",
      "643ccc4417a634e25f59fe93"
    ],
    "is_recursive": true
  }
}

@apiUse folder_search_response
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":... xem Data Example,
  "paging":... xem Paging example
}
"""

# ---------- Lấy danh sách thư mục(search) -----------
"""
@api {get} {HOST}/knowledge-base/api/v1.0/folders/actions/search  Lấy danh sách thư mục(search) - DEPRECATED
@apiDescription API lấy danh sách tất cả thư mục thỏa mãn điều kiện.
@apiGroup Folder
@apiVersion 1.0.0
@apiName ListFolderSearch

@apiDeprecated  Sử dụng API để thay thế (#Folder:SearchFolder).
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query
@apiUse paging_tokens

@apiParam   (Query:)  {String}   [parent_id]  <code>id</code> thư mục cha. Chỉ lấy danh sách thư mục trong thư mục cha.
@apiParam   (Query:)  {String}   [search]   Từ khóa tìm kiếm. Lấy các thư mục mà tên có chứa từ khóa tìm kiếm.
@apiParam   (Query:)  {Int}   [level]   Cấp trong cây thư mục. Lấy các thư mục có cấp nhỏ hơn hoặc bằng <code>level</code> truyền lên.

@apiUse folder_search_response
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":... xem Data Example,
  "paging":... xem Paging example
}
"""

# ---------- Di chuyển và xóa thư mục -----------
"""
@api {POST} {HOST}/knowledge-base/api/v1.0/folders/actions/batch-move-and-delete  Chuyển và xóa thư mục
@apiDescription API chuyển tất cả thư mục con và bài viết tới thư mục đích. Sau đó sẽ thực hiện xóa các thư mục rỗng.
<br/><br/><code>Chú ý:</code> Thư mục đích phải đảm bảo tuân thủ giới hạn về cấp sau khi lưu trữ các thư mục con chuyển tới.
<br/>Ví dụ: Nếu giới hạn cấp của cây là 5, độ sâu lớn nhất của các thư mục con chuyển đến là 3 thì thư mục đích phải ở cấp 2.
@apiGroup Folder
@apiVersion 1.0.0
@apiName MoveDeleteBatchFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiParam   (Body:)   {ArrayString}  folder_ids  Danh sách định danh của thư mục cần xóa.
@apiParam   (Body:)   {String}  destination_folder_id   Định danh thư mục đích dùng để chứa các thư mục con và bài viết của các thư mục bị xóa.
@apiParamExample  {json}  Body example:
{
  "folder_ids": [
    "642f85314d4356e983dd7415",
    "642fc65e2af4e1dbe42ea896",
    "6433c0959870fa17a0219613",
    "643ccc4417a634e25f59fe93"
  ],
  "destination_folder_id": "0p1ecc4417a634e25f59pk3g"
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

# ---------- Xóa thư mục -----------
"""
@api {POST} {HOST}/knowledge-base/api/v1.0/folders/actions/batch-delete  Xóa thư mục
@apiDescription API xóa thư mục bao gồm các thư mục con và bài viết. Có thể xóa nhiều thư mục cùng 1 lúc.
@apiGroup Folder
@apiVersion 1.0.0
@apiName DeleteBatchFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiParam   (Body:)   {ArrayString}  folder_ids  Danh sách định danh của thư mục cần xóa.
@apiParamExample  {json}  Body example:
{
  "folder_ids": [
    "642f85314d4356e983dd7415",
    "642fc65e2af4e1dbe42ea896",
    "6433c0959870fa17a0219613",
    "643ccc4417a634e25f59fe93"
  ]
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

# ---------- Sửa thư mục -----------
"""
@api {PATCH} {HOST}/knowledge-base/api/v1.0/folders/<folder_id>  Sửa thư mục
@apiDescription API chỉnh sửa thông tin thư mục. Nếu sửa <code>title</code> hoặc <code>description</code> của ngôn ngữ nào thì chỉ gửi ngôn ngữ đấy.<br/>Ngôn ngữ nào không sửa thì không gửi.
@apiGroup Folder
@apiVersion 1.0.0
@apiName UpdateFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiParam   (Body:)   {ArrayObject}   [content]   Thông tin thư mục. <br/>Mỗi một object trong mảng là thông tin theo ngôn ngữ tương ứng.
@apiParam   (Body:)   {String}  [content.title]   Tên thư mục
@apiParam   (Body:)   {String}  [content.description]   Mô tả của thư mục
@apiParam   (Body:)   {String}  content.language_key  Mã ngôn ngữ tương ứng với title & description được nhập.

@apiUse view_type_body
@apiUse icon_body
@apiParamExample  {json}  Body example:
{
  "content": [
    {
      "title": "Thư mục con",
      "description": "Đây là thư mục con 1",
      "language_key": "vi"
    }
  ],
  "view_type": "internal",
  "icon": {
    "id": "",
    "color": "",
    "is_color_custom": false,
    "background": "#ffffff",
    "is_background_custom": false,
    "url": "https://example.com/icon.jpg"
  }
}

@apiUse folder_detail_response
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

# ---------- Chi tiết thư mục -----------
"""
@api {GET} {HOST}/knowledge-base/api/v1.0/folders/<folder_id>  Chi tiết thư mục
@apiDescription API lấy thông tin chi tiết thư mục
@apiGroup Folder
@apiVersion 1.0.0
@apiName DetailFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiUse folder_detail_response
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":... xem Data Example,
}
"""

# ---------- Lấy danh sách thư mục(table) -----------
"""
@api {GET} {HOST}/knowledge-base/api/v1.0/folders/actions/view-table  Lấy danh sách thư mục(table)
@apiDescription API lấy danh sách tất cả thư mục có cùng thư mục cha.
<br/>Hiện tại, api sẽ trả về tất cả thư mục và không phân trang.
@apiGroup Folder
@apiVersion 1.0.0
@apiName ListFolderTable

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiUse kbm_order_sort
@apiUse paging_tokens
@apiParam   (Query:)  {String}   parent_id  <code>id</code> thư mục cha. Nếu muốn lấy danh sách thư mục gốc truyền <code>&parent_id=""</code>

@apiUse folder_table_response
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":... xem Data Example,
  "paging":... xem Paging example
}
"""

# ---------- Lấy danh sách thư mục(tree) -----------
"""
@api {get} {HOST}/knowledge-base/api/v1.0/folders/actions/view-tree  Lấy danh sách thư mục(tree)
@apiDescription API lấy danh sách tất cả thư mục có cùng thư mục cha.
<br/>Hiện tại, api sẽ trả về tất cả thư mục và không phân trang.
@apiGroup Folder
@apiVersion 1.0.0
@apiName ListFolderTree

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiParam   (Query:)  {String}   parent_id  <code>id</code> thư mục cha. Nếu muốn lấy danh sách thư mục gốc truyền <code>&parent_id=""</code>

@apiUse folder_tree_response
@apiUse paging_tokens

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":... xem Data Example
  "paging" .. xem Paging
}
"""

# ---------- Tạo thư mục con -----------
"""
@api {POST} {HOST}/knowledge-base/api/v1.0/folders/<parent_id>/sub-folders Tạo thư mục con
@apiDescription API tạo thư mục con.
@apiGroup Folder
@apiVersion 1.0.0
@apiName CreateFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (PathResource)  {String}  parent_id  Định danh của thư mục cha.

@apiParam   (Body:)   {ArrayObject}   content   Thông tin thư mục cần tạo.<br/>Mỗi một object trong mảng là thông tin theo ngôn ngữ tương ứng.<br/>Bắt buộc phải chứa ngôn ngữ mặc định của tenant. Các ngôn ngữ khác optional, nếu không có hệ thống sẽ tự động sao chép <code>title, description</code> từ ngôn ngữ mặc định.
@apiParam   (Body:)   {String}  content.title   Tên thư mục
@apiParam   (Body:)   {String}  content.description   Mô tả của thư mục
@apiParam   (Body:)   {String}  content.language_key  Mã ngôn ngữ tương ứng với title & description được nhập.

@apiUse view_type_body
@apiUse icon_body
@apiParamExample  {json}  Body example:
{
  "content": [
    {
      "title": "Thư mục con",
      "description": "Đây là thư mục con 1",
      "language_key": "vi"
    }
  ],
  "view_type": "internal",
  "icon": {
    "id": "",
    "color": "",
    "is_color_custom": false,
    "background": "#ffffff",
    "is_background_custom": false,
    "url": "https://example.com/icon.jpg"
  }
}

@apiUse folder_detail_response
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":... xem Data Example
}
"""

# ---------- Tạo thư mục gốc -----------
"""
@api {POST} {HOST}/knowledge-base/api/v1.0/folders Tạo thư mục gốc
@apiDescription API tạo thư mục gốc.
@apiGroup Folder
@apiVersion 1.0.0
@apiName CreateRootFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)   {ArrayObject}   content   Thông tin thư mục gốc cần tạo.<br/>Mỗi một object trong mảng là thông tin theo ngôn ngữ tương ứng.<br/>Bắt buộc phải chứa ngôn ngữ mặc định của tenant. Các ngôn ngữ khác optional, nếu không có hệ thống sẽ tự động sao chép <code>title, description</code> từ ngôn ngữ mặc định.
@apiParam   (Body:)   {String}  content.title   Tên thư mục
@apiParam   (Body:)   {String}  content.description   Mô tả của thư mục
@apiParam   (Body:)   {String}  content.language_key  Mã ngôn ngữ tương ứng với title & description được nhập.

@apiUse view_type_body
@apiUse icon_body
@apiParamExample  {json}  Body example:
{
  "content": [
    {
      "title": "Thư mục gốc",
      "description": "Đây là thư mục gốc",
      "language_key": "vi"
    }
  ],
  "view_type": "internal",
  "icon": {
    "id": "",
    "color": "",
    "is_color_custom": false,
    "background": "#ffffff",
    "is_background_custom": false,
    "url": "https://example.com/icon.jpg"
  }
}

@apiUse folder_detail_response
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data":... xem Data Example
}
"""



# ---------- Lấy breadcrumb thư mục -----------
"""
@api {GET} {HOST}/knowledge-base/api/v1.0/folders/<folder_id>/breadcrumb  Lấy breadcrumb thư mục
@apiDescription API lấy thông tin breadcrumb thư mục
@apiGroup Folder
@apiVersion 1.0.0
@apiName GetBreadcrumbFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {Object}            data                            Dữ liệu trả về
@apiSuccess     {ArrayObject}       breadcrumb                      Thông tin đường dẫn của thư mục
@apiSuccess     {String}            breadcrumb.id                   <code>ID</code> của thư mục.
@apiSuccess     {String}            breadcrumb.title                Tên thư mục.
@apiSuccess     {String}            breadcrumb.level                Level của thư mục. Thư mục gốc có <code>level=1</code>

@apiSuccessExample  {json}  Data Example
{
  "code": 200,
  "message": "Request thành công",
  "data": {
    "breadcrumb": [
      {
        "id": "2345cc4417a634e25f23",
        "title": "Tên cấp 2",
        "level": 2
      },
      {
        "id": "2345cc4417a634e25f24",
        "title": "Tên cấp 1",
        "level": 1
      }
    ]
  }
}
"""

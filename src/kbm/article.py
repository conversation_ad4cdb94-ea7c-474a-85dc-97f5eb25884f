#!/usr/bin/env python
# -*- coding: utf-8 -*-

# ================================================== Create article ================================================== #
"""
@api {POST} {domain}/knowledge-base/api/v1.0/articles   Tạo bài viết
@apiDescription API Tạo bài viết
@apiGroup Article
@apiVersion 1.0.0
@apiName CreateArticle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam       (Body:)     {String}          language_key                                  Mã ngôn ngữ của bài viết đang soạn thảo.
@apiParam       (Body:)     {Object}          config                                        C<PERSON>u hình bài viết
@apiParam       (Body:)     {String}          config.folder_id                              <code>ID</code> thư mục đang chứa bài viết
@apiParam       (Body:)     {ArrayObject}     [config.tags]                                 Danh sách tag của bài viết
@apiParam       (Body:)     {String}          [config.tags.id]                              <code>ID</code> Tag của bài viết. Nếu không truyền <code>ID</code> sẽ tạo tag mới.
@apiParam       (Body:)     {String}          config.tags.value                             Value của tag
@apiParam       (Body:)     {String}          config.view_type                              Chế độ hiển thị bài viết. Lấy chi tiết [tại đây](#api-Constant-DetailTypeViews)
@apiParam       (Body:)     {Object}          [config.label]                                Nhãn hiện thị trên trang của người xem
@apiParam       (Body:)     {String}          config.label.key                              Key của nhãn:
                                                                                                <ul>
                                                                                                    <li><code>new</code>: Mới</li>
                                                                                                </ul>
                                                                                                <ul>
                                                                                                    <li><code>new_update</code>: Vừa mới cập nhật.</li>
                                                                                                </ul>
@apiParam       (Body:)     {Int}             config.label.expire_after_day                  Số ngày nhãn được phép hiển thị trên trang của người đọc sau khi publish bài viết. Tối thiểu là <code>1</code> ngày
@apiParam       (Body:)     {Object}          content                                        Nội dung bài viết
@apiParam       (Body:)     {String}          content.title                                  Title của bài viết
@apiParam       (Body:)     {String}          content.slug                                   Slug của bài viết
@apiParam       (Body:)     {Any}             content.raw_content                            Cấu trúc content của bài viết (do FE định nghĩa)
@apiParam       (Body:)     {String}          content.raw_text                               Raw text của bài việc phục vụ cho việc search
@apiParam       (Body:)     {Object}          [content.extra]                                Các thông tin khác trong content bài viết
@apiParam       (Body:)     {ArrayString}     [content.extra.attachment_ids]                 Các file được đính kèm. Upload file [tại đây](#api-Attachment-AttachmentUploadFile)
@apiParam       (Body:)     {String}          action_type                                    Loại hành động. Tạo version mới tương ứng với loại hành động. Giá trị:
                                                                                               <ul>
                                                                                                   <li><code>auto_save</code> Auto save</li>
                                                                                                   <li><code>publish</code> Xuất bản - Đồng thời sẽ xuất bản bài viết ra trang người đọc</li>
                                                                                                   <li><code>save_draft</code> Lưu nháp</li>
                                                                                               </ul>

@apiParamExample  {json}   Body example
{
    "action_type": "save_draft",
    "language_key": "vi",
    "config": {
        "folder_id": "66bc2c486edc0e977f58e738",
        "status": "draft",
        "view_type": "internal",
        "tags": [
            {
                "id": "66bc2c93ff66924c416011f2",
                "value", "Technical Sale",
            }, 
            {
                "value", "Vay tín dụng", // Tag này sẽ được tạo mới
            }, 
        ],
        "label": {
            "key": "new",
            "expire_after_day": 40,
        }
    }, 
    "content": {
        "title": "Title của bài viết",
        "slug": "title-cua-bai-viet",
        "raw_content": ... // Cấu trúc do FE định nghĩa,
        "raw_text": "Lorem abcxzy",
        "extra": {
            "attachment_ids": ["66bc2ce2ff66924c416011fe", "66bc2d33ff66924c41601202"],
        }
    },
}


@apiUse APISuccessCommon
@apiUse APISuccessDetailArticle

@apiUse APISuccessExampleCommon
@apiUse APISuccessExampleDetailArticle
"""

# ================================================== Get Detail article ================================================== #
"""
@api {GET} {domain}/knowledge-base/api/v1.0/articles/<article_id>       Chi tiết bài viết
@apiGroup Article
@apiVersion 1.0.0
@apiName GetDetailArticle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiUse APISuccessCommon
@apiUse APISuccessDetailArticle

@apiUse APISuccessExampleCommon
@apiUse APISuccessExampleDetailArticle
"""


# ================================================== Auto article  ================================================== #
"""
@api {PATCH} {domain}/knowledge-base/api/v1.0/articles/<article_id>/content/auto-save     Auto save nội dung bài viết

@apiDescription API Auto save nội dung bài viết
                <br/>Trong trường hợp có 2 user chỉnh sửa cùng lúc, lượt save sẽ bị lỗi nếu user gửi lên <code>session_id</code> cũ hơn phiên bản được lưu trên server.
                <br/>Khi save bị lỗi do vênh session, server sẽ gửi lại content mới nhất của bài viết trong response (mô tả dưới Error).
@apiGroup Article
@apiVersion 1.0.0
@apiName AutoSaveContentArticle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse ErrorArticleConflictSessionResponse
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam       (Body:)     {String}            session_id                          Session của bài viết hiện tại
@apiParam       (Body:)     {String}            language_key                        Mã ngôn ngữ của bài viết đang soạn thảo.
@apiParam       (Body:)     {Boolean}           [is_override=false]                 Nếu truyền lên giá trị <code>true</code> thì bỏ qua việc lệch session với hệ thống. Server sẽ ghi nhận nội dung bài viết đang truyền lên là mới nhất (ghi đè).
@apiParam       (Body:)     {String}            [title]                             Title bài viết
@apiParam       (Body:)     {String}            [slug]                              Slug bài viết
@apiParam       (Body:)     {Any}               [raw_content]                       Cấu trúc content của bài viết (do FE định nghĩa)
@apiParam       (Body:)     {String}            [raw_text]                          Raw text của bài việc phục vụ cho việc search. <code>Required</code> nếu truyền <code>raw_context</code>
@apiParam       (Body:)     {Object}            [extra]                             Các thông tin khác trong content bài viết
@apiParam       (Body:)     {ArrayString}       [extra.attachment_ids]              Các file được đính kèm. Upload file [tại đây](#api-Attachment-AttachmentUploadFile)


@apiParamExample  {json}   Body example
{
    "session_id": "6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b",
    "language_key": "vi",
    "status": "draft",
    "title": "Title của bài viết",
    "slug": "title-cua-bai-viet",
    "raw_content": ... // Cấu trúc do FE định nghĩa
    "raw_text": "Lorem Ipsum is simply dummy..."
    "extra": {
        "attachment_ids": ["66bc2c93ff66924c416011ff"]
    }
}

@apiUse APISuccessCommon
@apiUse APISuccessDetailArticle

@apiUse APISuccessExampleCommon
@apiUse APISuccessExampleDetailArticle
"""

# ================================================== Update content article  ================================================== #
"""
@api {PATCH} {domain}/knowledge-base/api/v1.0/articles/<article_id>/content/draft     Lưu nháp nội dung bài viết

@apiDescription API lưu nháp nội dung bài viết
                <br/>Trong trường hợp có 2 user chỉnh sửa cùng lúc, lượt save sẽ bị lỗi nếu user gửi lên <code>session_id</code> cũ hơn phiên bản được lưu trên server.
                <br/>Khi save bị lỗi do vênh session, server sẽ gửi lại content mới nhất của bài viết trong response (mô tả dưới Error).
@apiGroup Article
@apiVersion 1.0.0
@apiName UpdateContentArticle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse ErrorArticleConflictSessionResponse
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam       (Body:)     {String}            session_id                          Session của bài viết hiện tại
@apiParam       (Body:)     {String}            language_key                        Mã ngôn ngữ của bài viết đang soạn thảo.
@apiParam       (Body:)     {Boolean}           [is_override=false]                 Nếu truyền lên giá trị <code>true</code> thì bỏ qua việc lệch session với hệ thống. Server sẽ ghi nhận nội dung bài viết đang truyền lên là mới nhất (ghi đè).
@apiParam       (Body:)     {String}            [title]                             Title bài viết
@apiParam       (Body:)     {String}            [slug]                              Slug bài viết

@apiParam       (Body:)     {Any}               [raw_content]                       Cấu trúc content của bài viết (do FE định nghĩa)
@apiParam       (Body:)     {String}            [raw_text]                          Raw text của bài việc phục vụ cho việc search. <code>Required</code> nếu truyền <code>raw_context</code>
@apiParam       (Body:)     {Object}            [extra]                             Các thông tin khác trong content bài viết
@apiParam       (Body:)     {ArrayString}       [extra.attachment_ids]              Các file được đính kèm. Upload file [tại đây](#api-Attachment-AttachmentUploadFile)


@apiParamExample  {json}   Body example
{
    "session_id": "6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b",
    "language_key": "vi",
    "status": "draft",
    "title": "Title của bài viết",
    "slug": "title-cua-bai-viet",
    "raw_content": ... // Cấu trúc do FE định nghĩa
    "raw_text": "Lorem Ipsum is simply dummy..."
    "extra": {
        "attachment_ids": ["66bc2c93ff66924c416011ff"]
    }
}

@apiUse APISuccessCommon
@apiUse APISuccessDetailArticle

@apiUse APISuccessExampleCommon
@apiUse APISuccessExampleDetailArticle
"""

# ================================================== Config article ================================================== #
"""
@api {PATCH} {domain}/knowledge-base/api/v1.0/articles/<article_id>/config/draft    Lưu nháp cấu hình bài viết

@apiDescription API Lưu nháp cấu hình bài viết.
@apiGroup Article
@apiVersion 1.0.0
@apiName ConfigArticle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam       (Body:)     {String}          language_key                  Mã ngôn ngữ của bài viết đang soạn thảo.
@apiParam       (Body:)     {String}          [folder_id]                   <code>ID</code> thư mục đang chứa bài viết
@apiParam       (Body:)     {ArrayObject}     [tags]                        Danh sách tag của bài viết
@apiParam       (Body:)     {String}          [tags.id]                     <code>ID</code> Tag của bài viết. Nếu không truyền <code>ID</code> sẽ tạo tag mới.
@apiParam       (Body:)     {String}          tags.value                    Value của tag
@apiParam       (Body:)     {String}          [view_type]                   Chế độ hiển thị bài viết. Lấy chi tiết [tại đây](#api-Constant-DetailTypeViews)
@apiParam       (Body:)     {Object}          [label]                       Nhãn hiện thị trên trang của người xem
@apiParam       (Body:)     {String}          label.key                     Key của nhãn:
                                                                                <ul>
                                                                                    <li><code>new</code>: Mới</li>
                                                                                </ul>
                                                                                <ul>
                                                                                    <li><code>new_update</code>: Vừa mới cập nhật.</li>
                                                                                </ul>
@apiParam       (Body:)     {Int}             label.expire_after_day        Số ngày nhãn được phép hiển thị trên trang của người đọc sau khi publish bài viết. Tối thiểu là <code>1</code> ngày


@apiParamExample  {json}   Body example
{
    
    "language_key": "vi",
    "folder_id": "2345cc4417a634e25f23",
    "view_type": "internal",
    "tags": [
        {
            "id": "66bc2c93ff66924c416011f2",
            "value", "Technical Sale",
        }, 
        {
            "value", "Vay tín dụng", // Tag này sẽ được tạo mới
        }, 
    ],
    "label": {}
}


@apiUse APISuccessCommon
@apiUse APISuccessDetailArticle

@apiUse APISuccessExampleCommon
@apiUse APISuccessExampleDetailArticle
"""

# ================================================== List article ================================================== #
"""
@api {POST} {domain}/knowledge-base/api/v1.0/articles/actions/filter Lấy danh sách bài viết
@apiGroup Article
@apiVersion 1.0.0
@apiName FilterListArticle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse kbm_order_sort
@apiUse paging_tokens
@apiUse selected_lang_query
@apiUse merchant_id_header

@apiUse APIParamFilterArticle

@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {ArrayObject}       data                            Dữ liệu trả về
@apiSuccess     {String}            data.id                         <code>ID</code> của bài viết
@apiSuccess     {String}            data.title                      Title của bài viết
@apiSuccess     {String}            data.status                     Trạng thái của bài viết. Giá trị:
                                                                    <ul>
                                                                        <li><code>draft</code>: Bài viết đang ở trạng thái nháp. Người đọc không thể xem bài viết này.</li>
                                                                        <li><code>published</code>: Bài viết đang ở trạng thái public. Người đọc có thể xem bài viết này.</li>
                                                                    </ul>
@apiSuccess     {ArrayObject}       [data.tag_ids]                  <code>ID</code> Tag
@apiSuccess     {String}            data.view_type                  Chế độ hiển thị bài viết. Lấy chi tiết [tại đây](#api-Constant-DetailTypeViews)
@apiSuccess     {Boolean}           data.has_multi_language         Bài viết đã có ngôn ngữ khác ngôn ngữ mặc định
@apiSuccess     {String}            data.last_published_time        Thời gian bài viết được xuất bản. Định dạng: yyyy-MM-ddTHH:mm:ssZ
@apiSuccess     {String}            data.last_published_by          User xuất bản bài viết
@apiSuccess     {String}            data.updated_time               Thời điểm cập nhật lần cuối. Định dạng: yyyy-MM-ddTHH:mm:ssZ
@apiSuccess     {String}            data.created_time               Thời điểm tạo. Định dạng: yyyy-MM-ddTHH:mm:ssZ
@apiSuccess     {String}            data.updated_by                 User cập nhật lần cuối.
@apiSuccess     {String}            data.created_by                 User tạo.


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "id": "61601a2acb97585cd950d847",
            "title": "Lorem 1",
            "status": "draft",
            "has_multi_language": true,
            "view_type": "internal",
            "tag_ids": ["66bc2c93ff66924c416011f2", "66bc2cd6ff66924c416011f7"],
            "last_published_time": null,
            "last_published_by": null,
            "updated_time": "2024-08-06T14:35:00Z",
            "updated_by":  "6a68668e-a550-444e-90f2-fdfd1f5d6bfa",
            "created_time": "2024-08-06T14:35:00Z",
            "created_by":  "6a68668e-a550-444e-90f2-fdfd1f5d6bfa",
        },
        {
            "id": "61601a2acb97585cd950d848",
            "title": "Lorem 2",
            "status": "published",
            "has_multi_language": false,
            "view_type": "internal",
            "tag_ids": ["66bc2c93ff66924c416011f2", "66bc2cd6ff66924c416011f7"],
            "last_published_time": "2024-09-05T14:35:00Z",
            "last_published_by": "6a68668e-a550-444e-90f2-fdfd1f5d6bfa",
            "updated_time": "2024-08-06T14:35:00Z",
            "updated_by":  "6a68668e-a550-444e-90f2-fdfd1f5d6bfa",
            "created_time": "2024-08-06T14:35:00Z",
            "created_by":  "6a68668e-a550-444e-90f2-fdfd1f5d6bfa",
        }
    ],
    "paging": ... xem Paging example
}
"""

# ================================================== Total article with filter ================================================== #
"""
@api {POST} {domain}/knowledge-base/api/v1.0/articles/actions/total/filter      Số lượng bài viết theo filter
@apiGroup Article
@apiVersion 1.0.0
@apiName FilterTotalArticle
@apiDescription API Đếm số lượng bài viết theo bộ lọc


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse selected_lang_query
@apiUse merchant_id_header

@apiUse APIParamFilterArticle

@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {Object}            data                            Dữ liệu trả về
@apiSuccess     {Int}               data.total                      Số lượng bài viết


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": {
        "total": 100
    }
}
"""
# ================================================== List article lang ================================================== #
"""
@api {GET} {domain}/knowledge-base/api/v1.0/articles/<article_id>/languages     Danh sách ngôn ngữ của bài viết
@apiGroup Article
@apiVersion 1.0.0
@apiName ListLangArticle
@apiDescription API lấy danh sách ngôn ngữ của bài viết kèm trạng thái

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {ArrayObject}       data                            Dữ liệu trả về
@apiSuccess     {String}            data.language_key               Mã ngôn ngữ
@apiSuccess     {String}            data.status                     Trạng thái của bài viết. Giá trị:
                                                                        <ul>
                                                                            <li><code>draft</code>: Bài viết đang ở trạng thái nháp. Người đọc không thể xem bài viết này.</li>
                                                                            <li><code>published</code>: Bài viết đang ở trạng thái public. Người đọc có thể xem bài viết này.</li>
                                                                        </ul>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {   
            "language_key": "vi",
            "status": "draft"
        },
        {   
            "language_key": "en",
            "status": "published"
        },
    ]
}
"""

# ================================================== Batch update article ================================================== #
"""
@api {PATCH} {domain}/knowledge-base/api/v1.0/articles/actions/batch-update/view-type       Cập nhật chế độ hiển thị
@apiDescription API Cập nhật chế độ hiển thị bài viết. Có thể cập nhật nhiều bài viết một lúc
@apiGroup Article
@apiVersion 1.0.0
@apiName BatchUpdateViewType

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam       (Body:)     {ArrayString}          article_ids       <code>ID</code> bài viết. Max = <code>100</code>
@apiParam       (Body:)     {String}               view_type         Chế độ hiển thị bài viết. Lấy ds [tại đây](#api-Constant-TypeViews)

@apiParamExample  {json}   Body example
{
    "article_ids": ["66bc2c93ff66924c416011f2"],
    "view_type": "internal"
}
@apiUse APISuccessMessage
"""

# ================================================== Batch update folder id ================================================== #
"""
@api {PATCH} {domain}/knowledge-base/api/v1.0/articles/actions/batch-update/folder       Cập nhật thư mục của bài viết
@apiDescription API Chuyển bài viết sang thư mục khác. Có thể chuyển nhiều bài viết một lúc
@apiGroup Article
@apiVersion 1.0.0
@apiName BatchUpdateFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam       (Body:)     {ArrayString}       article_ids       <code>ID</code> bài viết. Max = <code>100</code>
@apiParam       (Body:)     {String}            folder_id         <code>ID</code> thư mục

@apiParamExample  {json}   Body example
{
    "article_ids": ["66bc2c93ff66924c416011f2"],
    "folder_id": "66bc2c93ff66924c416011f1"
}

@apiUse APISuccessMessage

"""

# ================================================== Unpublish article ================================================== #
"""
@api {POST} {domain}/knowledge-base/api/v1.0/articles/<article_id>/actions/unpublish       Ngừng xuất bản bài viết
@apiDescription API Ngừng xuất bản bài viết. Chuyển bài viết về trạng thái nháp. Người đọc không thể xem bài viết này
@apiGroup Article
@apiVersion 1.0.0
@apiName UnpublishArticle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiUse APISuccessCommon
@apiUse APISuccessDetailArticle

@apiUse APISuccessExampleCommon
@apiUse APISuccessExampleDetailArticle
"""

# ================================================== Publish article ================================================== #
"""
@api {POST} {domain}/knowledge-base/api/v1.0/articles/<article_id>/actions/publish      Xuất bản bài viết
@apiDescription API xuất bản bài viết. Xuất bản nháp mới nhất lên trang của người đọc
@apiGroup Article
@apiVersion 1.0.0
@apiName PublishArticle
@apiUse ErrorArticleConflictSessionResponse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header



@apiParamExample  {json}   Body example
{
    "session_id": "6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b",
}

@apiParam       (Body:)     {String}          session_id                                    Session của bài viết hiện tại
@apiParam       (Body:)     {Boolean}         is_save                                       Lưu lại cấu cấu hình và nội dung của bài viết đang soạn thảo. </br>
                                                                                            <ul>
                                                                                                <li>Value = <code>True</code>:sẽ required <code>config</code>, <code>content</code> và những thông tin cần thiết để bài viết xuất bản được</li>
                                                                                            </ul>
                                                                                            <ul>
                                                                                                <li>Value = <code>False</code> Sẽ lấy thông tin  bản nháp được lưu mới nhất xuất bản</li>
                                                                                            </ul>
@apiParam       (Body:)     {String}          language_key                                  Mã ngôn ngữ của bài viết đang soạn thảo. 
@apiParam       (Body:)     {Object}          [config]                                        Cấu hình bài viết
@apiParam       (Body:)     {String}          config.folder_id                              <code>ID</code> thư mục đang chứa bài viết
@apiParam       (Body:)     {ArrayObject}     [config.tags]                                 Danh sách tag của bài viết
@apiParam       (Body:)     {String}          [config.tags.id]                              <code>ID</code> Tag của bài viết. Nếu không truyền <code>ID</code> sẽ tạo tag mới.
@apiParam       (Body:)     {String}          config.tags.value                             Value của tag
@apiParam       (Body:)     {String}          config.view_type                              Chế độ hiển thị bài viết. Lấy chi tiết [tại đây](#api-Constant-DetailTypeViews)
@apiParam       (Body:)     {Object}          [config.label]                                Nhãn hiện thị trên trang của người xem
@apiParam       (Body:)     {String}          config.label.key                              Key của nhãn:
                                                                                                <ul>
                                                                                                    <li><code>new</code>: Mới</li>
                                                                                                </ul>
                                                                                                <ul>
                                                                                                    <li><code>new_update</code>: Vừa mới cập nhật.</li>
                                                                                                </ul>
@apiParam       (Body:)     {Int}             config.label.expire_after_day                  Số ngày nhãn được phép hiển thị trên trang của người đọc sau khi publish bài viết. Tối thiểu là <code>1</code> ngày
@apiParam       (Body:)     {Object}          [content]                                        Nội dung bài viết
@apiParam       (Body:)     {String}          content.title                                  Title của bài viết
@apiParam       (Body:)     {String}          content.slug                                   Slug của bài viết
@apiParam       (Body:)     {Any}             content.raw_content                            Cấu trúc content của bài viết (do FE định nghĩa)
@apiParam       (Body:)     {String}          content.raw_text                               Raw text của bài việc phục vụ cho việc search
@apiParam       (Body:)     {Object}          [content.extra]                                Các thông tin khác trong content bài viết
@apiParam       (Body:)     {ArrayString}     [content.extra.attachment_ids]                 Các file được đính kèm. Upload file [tại đây](#api-Attachment-AttachmentUploadFile)

@apiParamExample  {json}   Body example
{
    "session_id": "704eac91-7416-497f-a17d-d81cfa2d3211",
    "is_save": True,
    "language_key": "vi",
    "config": {
        "folder_id": "66bc2c486edc0e977f58e738",
        "status": "draft",
        "view_type": "internal",
        "tags": [
            {
                "id": "66bc2c93ff66924c416011f2",
                "value", "Technical Sale",
            }, 
            {
                "value", "Vay tín dụng", // Tag này sẽ được tạo mới
            }, 
        ],
        "label": {
            "key": "new",
            "expire_after_day": 40,
        }
    },
    "content": {
        "title": "Title của bài viết",
        "slug": "title-cua-bai-viet",
        "raw_content": ... // Cấu trúc do FE định nghĩa,
        "raw_text": "Lorem abcxzy",
        "extra": {
            "attachment_ids": ["66bc2ce2ff66924c416011fe", "66bc2d33ff66924c41601202"],
        }
    },
}


@apiUse APISuccessCommon
@apiUse APISuccessDetailArticle

@apiUse APISuccessExampleCommon
@apiUse APISuccessExampleDetailArticle
"""

# ================================================== Delete batch article ================================================== #

"""
@api {POST} {HOST}/knowledge-base/api/v1.0/articles/actions/batch-delete  Xóa bài viết
@apiDescription API xóa bài viết. Có thể xóa nhiều bài viết cùng 1 lúc.
@apiGroup Article
@apiVersion 1.0.0
@apiName DeleteBatchArticle

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiParam   (Body:)   {ArrayString}  article_ids  Danh sách định danh của bài viết cần xóa.
@apiParamExample  {json}  Body example:
{
  "article_ids": [
    "642f85314d4356e983dd7415",
    "642fc65e2af4e1dbe42ea896",
    "6433c0959870fa17a0219613",
    "643ccc4417a634e25f59fe93"
  ]
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

# ================================================== List article version ================================================== #
"""
@api {GET} {domain}/knowledge-base/api/v1.0/articles/<article_id>/versions         Danh sách version của bài viết
@apiGroup ArticleVersion
@apiVersion 1.0.0
@apiName ListVersionArticle
@apiDescription API lấy danh sách version của bài viết. Tối đa <code>5</code> version gần nhất

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse selected_lang_query


@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {ArrayObject}       data                            Dữ liệu trả về
@apiSuccess     {String}            data.id                         <code>ID</code> version bài viết
@apiSuccess     {String}            data.action_type                Loại hành động. Giá trị:
                                                                    <ul>
                                                                        <li><code>auto_save</code> Auto save</li>
                                                                        <li><code>publish</code> Xuất bản</li>
                                                                        <li><code>save_draft</code> Lưu nháp</li>
                                                                    </ul>
@apiSuccess     {String}            data.version_name               Tên version
@apiSuccess     {String}            data.action_by                 User cập nhật lần cuối.
@apiSuccess     {String}            data.action_time               Thời điểm cập nhật lần cuối. Định dạng: yyyy-MM-ddTHH:mm:ssZ

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {   
            "id": "66bde733f992e28961275d86",
            "action_type": "save_draft",
            "version_name": "Version 2024/08/06 14:35",
            "action_time": "2024-08-06T14:35:00Z",
            "action_by":  "6a68668e-a550-444e-90f2-fdfd1f5d6bfa",
        },
        {   
            "id": "66c3104c0e8a8422e8a719b5",
            "action_type": "auto_save",
            "version_name": "Version 2024/08/23 23:35",
            "action_time": "2024-08-23T23:35:00Z",
            "action_by":  "6a68668e-a550-444e-90f2-fdfd1f5d6bfa",
        },
    ]
}
"""

# ================================================== Detail article version ================================================== #
"""
@api {GET} {domain}/knowledge-base/api/v1.0/articles/<article_id>/versions/<article_version_id>     Chi tiết version của bài viết
@apiGroup ArticleVersion
@apiVersion 1.0.0
@apiName DetailVersionArticle
@apiDescription API chi tiết version của bài viết

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse APISuccessCommon
@apiUse APISuccessDetailArticleVersion

@apiUse APISuccessExampleCommon
@apiUse APISuccessExampleDetailArticleVersion
"""


# ================================================== Editing article version ================================================== #
"""
@api {GET} {domain}/knowledge-base/api/v1.0/articles/<article_id>/versions/actions/editing Lấy version đang soạn thảo
@apiGroup ArticleVersion
@apiVersion 1.0.0
@apiName DetailEditingVersionArticle
@apiDescription API Lấy version đang soạn thảo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse APISuccessCommon
@apiUse APISuccessDetailArticleVersion

@apiUse APISuccessExampleCommon
@apiUse APISuccessExampleDetailArticleVersion
"""


# ================================================== Export Article ================================================== #
"""
@api {POST} {domain}/knowledge-base/api/v1.0/articles/<article_id>/actions/export Export bài viết 
@apiGroup Article
@apiVersion 1.0.0
@apiName GetExportArticle
@apiDescription API Export bài viết 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam       (Body:)     {String}          export_type       Dạng file export. Hiện tại chỉ hỗ trợ <code>docx</code>
@apiParamExample  {json}  Body example:
{
  "export_type": "docx"
}

@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {ArrayObject}       data                            Dữ liệu trả về
@apiSuccess     {String}            data.url                        URL truy cập file
@apiSuccess     {String}            data.filename                   Tên của file


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "url": "https://t1.mobio.vn/static/kbm/test.docx",
        "filename": "test.docx",
    }
}

"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 06/08/2024
"""


"""
@apiDefine kbm_order_sort
@apiParam   (Query:)    {String}    [order_by]  <PERSON><PERSON><PERSON> cầu sắp xếp dữ liệu theo tiêu chí. Sử dụng: <code>&sort=field_name</code>(sắp xếp dữ liệu theo <code>field_name</code>).
@apiParam   (Query:)    {String=asc,desc}    [order_type=asc]    Sắp xếp dữ liệu theo chiều tăng dần(<code>asc, A->Z</code>) hoặc theo chiều giảm dần(<code>desc, Z-A</code>)
"""

"""
@apiDefine selected_lang_query
@apiParam   (Query:)    {String}           [selected_language=vi]             Mã ngôn ngữ cần xem dữ liệu.
"""

"""
@apiDefine folder_table_response
@apiSuccess   {String}   id                                    <code>ID</code> c<PERSON><PERSON> thư mục
@apiSuccess   {String}   [parent_id]                           <code>ID</code> của thư mục cha. Nếu là thư mục gốc, <code>parent_id=""</code>
@apiSuccess   {String}   title                                 Tên thư mục
@apiSuccess   {Int}      order                                 Vị trí của thư mục so với các thư mục cùng cấp.
@apiSuccess   {String}   icon_url                              Đường dẫn ảnh của Icon
@apiSuccess   {Int}      level                                 Level của thư mục tính từ thư mục gốc. Thư mục gốc có <code>level=1</code>
@apiSuccess   {Int}      height                                Mức cao nhất của cây thư mục tính từ thư mục hiện tại.
<br/>Nếu <code>height=1</code> tương đương không có thư mục con.
@apiSuccess   {String}   view_type  Chế độ hiển thị thư mục:
<ul>
    <li><code>internal</code>: Chế độ chỉ dành cho nội bộ doanh nghiệp. Người đọc phải login mới thấy thư mục này.</li>
    <li><code>hide</code>: Ẩn thư mục. Khi được thiết lập, người đọc sẽ không nhìn thấy thư mục này và tất cả đối tượng con(thư mục con, bài viết). Mỗi lần setup sẽ cần một khoảng thời gian để hệ thống thực hiện cập nhật</li>
</ul>
@apiSuccess     {String}    updated_time    Thời điểm cập nhật lần cuối. Định dạng: yyyy-MM-ddTHH:mm:ssZ
@apiSuccess     {String}    created_time    Thời điểm tạo. Định dạng: yyyy-MM-ddTHH:mm:ssZ
@apiSuccess     {String}    updated_by      User cập nhật lần cuối.
@apiSuccess     {String}    created_by      User tạo.

@apiSuccessExample  {json}  Data Example
{
    ...
    "data": [
        {
            "id": "643ccc4417a634e25f59fe93",
            "parent_id": "2345cc4417a634e25f23",
            "title": "Tên thư mục",
            "order": 1,
            "level": 1,
            "icon_url": "https://example.com/icon.jpg",
            "view_type": "internal",
            "updated_time": "2024-08-06T14:35:00Z",
            "created_time": "2024-08-06T14:35:00Z",
            "updated_by": "6a68668e-a550-444e-90f2-fdfd1f5d6bfa",
            "created_by": "1eec0f58-0d70-4788-a3c5-4eaecf3e274b",
        }
    ]
}
"""

"""
@apiDefine folder_search_response
@apiSuccess   {String}   id                                    <code>ID</code> của thư mục
@apiSuccess   {String}   [parent_id]                           <code>ID</code> của thư mục cha. Nếu là thư mục gốc, <code>parent_id=""</code>
@apiSuccess   {String}   title                                 Tên thư mục
@apiSuccess   {Int}      order                                 Vị trí của thư mục so với các thư mục cùng cấp.
@apiSuccess   {String}   icon_url                              Đường dẫn ảnh của Icon
@apiSuccess   {Int}      level                                 Level của thư mục tính từ thư mục gốc. Thư mục gốc có <code>level=1</code>
@apiSuccess   {String}   view_type                             Chế độ hiển thị thư mục:
<ul>
    <li><code>internal</code>: Chế độ chỉ dành cho nội bộ doanh nghiệp. Người đọc phải login mới thấy thư mục này.</li>
    <li><code>hide</code>: Ẩn thư mục. Khi được thiết lập, người đọc sẽ không nhìn thấy thư mục này và tất cả đối tượng con(thư mục con, bài viết). Mỗi lần setup sẽ cần một khoảng thời gian để hệ thống thực hiện cập nhật</li>
</ul>
@apiSuccess   {ArrayObject}     breadcrumb  Thông tin đường dẫn từ thư mục gốc đến thư mục cha của thư mục tương ứng.
@apiSuccess     {String}    breadcrumb.id   <code>ID</code> của thư mục.
@apiSuccess     {String}    breadcrumb.title   Tên thư mục.
@apiSuccess     {String}    breadcrumb.level   Level của thư mục tính từ thư mục gốc. Thư mục gốc có <code>level=1</code>

@apiSuccessExample  {json}  Data Example
{
    ...
    "data": [
        {
            "id": "643ccc4417a634e25f59fe93",
            "parent_id": "2345cc4417a634e25f23",
            "title": "Tên thư mục",
            "order": 1,
            "level": 1,
            "icon_url": "https://example.com/icon.jpg",
            "view_type": "internal",
            "breadcrumb": [
                {
                    "id": "2345cc4417a634e25f23",
                    "title": "Tên cấp 2",
                    "level": 2
                },
                {
                    "id": "2345cc4417a634e25f24",
                    "title": "Tên cấp 1"
                    "level": 1
                }
            ]
        }
    ]
}
"""

"""
@apiDefine folder_tree_response
@apiSuccess {String}     message          Mô tả phản hồi
@apiSuccess {Integer}    code             Mã phản hồi
@apiSuccess {ArrayObject} data            Dữ liệu trả về

@apiSuccess {String}     data.id          <code>ID</code> của thư mục
@apiSuccess {String}     data.[parent_id] <code>ID</code> của thư mục cha. Nếu là thư mục gốc, <code>parent_id=""</code>
@apiSuccess {String}     data.title       Tên thư mục
@apiSuccess {Int}        data.order       Vị trí của thư mục so với các thư mục cùng cấp
@apiSuccess {String}     data.icon_url    Đường dẫn ảnh của Icon
@apiSuccess {Int}        data.level       Level của thư mục tính từ thư mục gốc. Thư mục gốc có <code>level=1</code>
@apiSuccess {Int}        data.height      Mức cao nhất của cây thư mục tính từ thư mục hiện tại. Nếu <code>height=1</code> thì không có thư mục con

@apiSuccess {String}     data.view_type   Chế độ hiển thị thư mục:
<ul>
    <li><code>internal</code>: Chỉ dành cho nội bộ doanh nghiệp. Người đọc phải đăng nhập để thấy thư mục này.</li>
    <li><code>hide</code>: Ẩn thư mục. Người đọc sẽ không thấy thư mục và tất cả đối tượng con (thư mục con, bài viết). Mỗi lần thiết lập sẽ cần thời gian để hệ thống cập nhật.</li>
</ul>

@apiSuccessExample  {json}  Data Example
{
    ...
    "data": [
        {
            "id": "643ccc4417a634e25f59fe93",
            "parent_id": "2345cc4417a634e25f23",
            "title": "Tên thư mục",
            "order": 1,
            "level": 1,
            "height": 1,
            "icon_url": "https://example.com/icon.jpg",
            "view_type": "internal"
        }
    ]
}
"""

"""
@apiDefine folder_detail_response
@apiSuccess   {String}   id                                                   <code>ID</code> của thư mục
@apiSuccess   {String}   [parent_id]                                          <code>ID</code> của thư mục cha. Nếu là thư mục gốc thì <code>parent_id=""</code>
@apiSuccess   {ArrayObject}   content                                         Thông tin folder
@apiSuccess   {String}   content.title                                        Tên thư mục
@apiSuccess   {String}   content.description                                  Mô tả của thư mục
@apiSuccess   {String}   content.language_key                                 Mã ngôn ngữ
@apiSuccess   {Int}      order                                                Vị trí của thư mục
@apiSuccess   {Int}      level                                                Level của thư mục tính từ thư mục gốc. Thư mục gốc có <code>level=1</code>
@apiSuccess   {Object}           [icon]                                       Thông tin cấu hình của icon
@apiSuccess   {String}           [icon.id]                                    <code>ID</code> của Icon. ID do FE quản lý từ bộ icon built-in trong source FE.
@apiSuccess   {String}           [icon.color]                                 Màu của Icon
@apiSuccess   {String}           [icon.is_color_custom]                       Mày của Icon có phải do User custom hay không?
@apiSuccess   {String}           [icon.background]                            Màu nền của icon
@apiSuccess   {Bool}             [icon.is_background_custom]                  Màu nền của icon có phải do User custom hay không?
@apiSuccess   {String}           [icon.url]                                   Url hình ảnh
@apiSuccess   {String}   view_type  Chế độ hiển thị thư mục:
<ul>
    <li><code>internal</code>: Chế độ chỉ dành cho nội bộ doanh nghiệp. Người đọc phải login mới thấy thư mục này.</li>
    <li><code>hide</code>: Ẩn thư mục. Khi được thiết lập, người đọc sẽ không nhìn thấy thư mục này và tất cả đối tượng con(thư mục con, bài viết). Mỗi lần setup sẽ cần một khoảng thời gian để hệ thống thực hiện cập nhật</li>
</ul>

@apiSuccessExample  {json}  Data Example
{
    ...
    "data": {
        "id": "643ccc4417a634e25f59fe93",
        "parent_id": "2345cc4417a634e25f23",
        "content": [
            {
                "title": "Tên thư mục",
                "description": "Mô tả thư mục",
                "language_key": "vi"
            }
        ],
        "order": 1,
        "level": 1,
        "icon": {
            "id": "",
            "color": "",
            "is_color_custom": false,
            "background": "#ffffff",
            "is_background_custom": false,
            "url": "https://example.com/icon.jpg"
        },
        "view_type": "internal"
    }
}
"""

"""
@apiDefine icon_response
@apiSuccess     {Object}           [icon]                                       Thông tin cấu hình của icon
@apiSuccess     {String}           [icon.id]                                    <code>ID</code> của Icon. ID do FE quản lý từ bộ icon built-in trong source FE.
@apiSuccess     {String}           [icon.color]                                 Màu của Icon
@apiSuccess     {String}           [icon.is_color_custom]                       Mày của Icon có phải do User custom hay không?
@apiSuccess     {String}           [icon.background]                            Màu nền của icon
@apiSuccess     {Bool}             [icon.is_background_custom]                  Màu nền của icon có phải do User custom hay không?
@apiSuccess     {String}           [icon.url]                                   Url hình ảnh
"""

"""
@apiDefine icon_body
@apiParam   (Body:)     {Object}           [icon]                                       Thông tin cấu hình của icon
@apiParam   (Body:)     {String}           [icon.id]                                    <code>ID</code> của Icon. ID do FE quản lý từ bộ icon built-in trong source FE.
@apiParam   (Body:)     {String}           [icon.color]                                 Màu của Icon
@apiParam   (Body:)     {String}           [icon.is_color_custom]                       Field đánh dấu sử dụng màu custom.
@apiParam   (Body:)     {String}           [icon.background]                            Màu nền của icon
@apiParam   (Body:)     {Bool}             [icon.is_background_custom]                  Field đánh dấu sử dụng background custom.
@apiParam   (Body:)     {String}           [icon.url]                                   Url hình ảnh
"""

"""
@apiDefine view_type_response
@apiSuccess   {String}   data.view_type  Chế độ hiển thị thư mục:
<ul>
    <li><code>internal</code>: Chế độ chỉ dành cho nội bộ doanh nghiệp. Người đọc phải login mới thấy thư mục này.</li>
    <li><code>hide</code>: Ẩn thư mục. Khi được thiết lập, người đọc sẽ không nhìn thấy thư mục này và tất cả đối tượng con(thư mục con, bài viết). Mỗi lần setup sẽ cần một khoảng thời gian để hệ thống thực hiện cập nhật</li>
</ul>
"""

"""
@apiDefine view_type_body
@apiParam     (Body:)   {String}   [view_type=internal]  Chế độ hiển thị thư mục:
<ul>
    <li><code>internal</code>: Chế độ chỉ dành cho nội bộ doanh nghiệp. Người đọc phải login mới thấy thư mục này.</li>
    <li><code>hide</code>: Ẩn thư mục. Khi được thiết lập, người đọc sẽ không nhìn thấy thư mục này và tất cả đối tượng con(thư mục con, bài viết). Mỗi lần setup sẽ cần một khoảng thời gian để hệ thống thực hiện cập nhật</li>
</ul>
"""


# ================================================== ResponseCommonSuccessMessage ================================================== #
"""
@apiDefine APISuccessMessage
@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Object}            data                          Dữ liệu trả về
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
}
"""

# ================================================== ResponseCommonSuccess ================================================== #
"""
@apiDefine APISuccessCommon
@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Object}            data                          Dữ liệu trả về
"""


# ================================================== CommonApiSuccessExample ================================================== #
"""
@apiDefine APISuccessExampleCommon
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": ... // Xem Data Example,
}
"""

# ================================================== ResponseDetailArticle ================================================== #
"""
@apiDefine APISuccessDetailArticle

@apiSuccess   {String}          data.id                                             <code>ID</code> của bài viết
@apiSuccess   {Object}          data.config                                         Cấu hình bài viết
@apiSuccess   {String}          data.config.folder_id                               <code>ID</code> thư mục đang chứa bài viết
@apiSuccess   {ArrayString}     [data.config.tag_ids]                               <code>ID</code> Tag
@apiSuccess   {String}          data.config.status                                  Trạng thái của bài viết. Giá trị:
                                                                                    <ul>
                                                                                        <li><code>draft</code>: Bài viết đang ở trạng thái nháp. Người đọc không thể xem bài viết này.</li>
                                                                                        <li><code>published</code>: Bài viết đang ở trạng thái public. Người đọc có thể xem bài viết này.</li>
                                                                                    </ul>
@apiSuccess   {String}          data.config.view_type                               Chế độ hiển thị thư mục. Lấy chi tiết [tại đây](#api-Constant-DetailTypeViews)
@apiSuccess   {String}          [data.config.public_link]                           Link public của bài viết
@apiSuccess   {Object}          [data.config.label]                                 Nhãn hiện thị trên trang của người xem
@apiSuccess   {String}          data.config.label.key                               Key của nhãn:
                                                                                    <ul>
                                                                                        <li><code>new</code>: Mới</li>
                                                                                    </ul>
                                                                                    <ul>
                                                                                        <li><code>new_update</code>: Vừa mới cập nhật.</li>
                                                                                    </ul>
@apiSuccess   {Int}             data.config.label.expire_after_day                  Số ngày nhãn được phép hiển thị trên trang của người đọc sau khi publish bài viết. Tối thiểu là <code>1</code> ngày
@apiSuccess   {Object}          data.content                                      Nội dung bài viết
@apiSuccess   {String}          data.content.title                                  Title của bài viết
@apiSuccess   {String}          data.content.slug                                   Slug của bài viết
@apiSuccess   {String}          data.content.session_id                             Session content của bài viết hiện tại
@apiSuccess   {Any}             data.content.raw_content                            Cấu trúc content của bài viết (do FE định nghĩa)
@apiSuccess     {Boolean}       data.has_multi_language                             Bài viết đã có ngôn ngữ khác ngôn ngữ mặc định
@apiSuccess     {String}        data.last_published_time                            Thời gian bài viết được xuất bản
@apiSuccess     {String}        data.last_published_by                              User xuất bản bài viết
@apiSuccess     {Object}        [data.editing_version]                              Thông tin phiên bản đang soạn thảo
@apiSuccess     {String}        data.editing_version.id                              <code>ID</code> version bài viết
@apiSuccess     {String}        data.editing_version.action_type                     Loại hành động. Giá trị:
                                                                                    <ul>
                                                                                        <li><code>auto_save</code> Auto save</li>
                                                                                        <li><code>publish</code> Xuất bản</li>
                                                                                        <li><code>unpublish</code> Ngừng xuất bản</li>
                                                                                        <li><code>save_draft</code> Lưu nháp</li>
                                                                                    </ul>
@apiSuccess     {String}        data.editing_version.version_name                   Tên version
@apiSuccess     {String}        data.editing_version.action_time                    Thời điểm soạn thảo cuối. Định dạng: yyyy-MM-ddTHH:mm:ssZ
@apiSuccess     {String}        data.editing_version.action_by                      Người soạn thảo cuối
@apiSuccess     {Object}        [data.viewing_version]                              Thông tin phiên bản đang view
@apiSuccess     {String}        data.viewing_version.id                              <code>ID</code> version bài viết
@apiSuccess     {String}        data.viewing_version.action_type                     Loại hành động. Giá trị:
                                                                                    <ul>
                                                                                        <li><code>auto_save</code> Auto save</li>
                                                                                        <li><code>publish</code> Xuất bản</li>
                                                                                        <li><code>unpublish</code> Ngừng xuất bản</li>
                                                                                        <li><code>save_draft</code> Lưu nháp</li>
                                                                                    </ul>
@apiSuccess     {String}        data.viewing_version.version_name                   Tên version
@apiSuccess     {String}        data.viewing_version.action_time                    Thời điểm soạn thảo cuối. Định dạng: yyyy-MM-ddTHH:mm:ssZ
@apiSuccess     {String}        data.viewing_version.action_by                      Người soạn thảo cuối
@apiSuccess     {String}        data.updated_time                                   Thời điểm cập nhật lần cuối. Định dạng: yyyy-MM-ddTHH:mm:ssZ
@apiSuccess     {String}        data.created_time                                   Thời điểm tạo. Định dạng: yyyy-MM-ddTHH:mm:ssZ
@apiSuccess     {String}        data.updated_by                                     User cập nhật lần cuối.
@apiSuccess     {String}        data.created_by                                     User tạo.
"""

"""
@apiDefine APISuccessExampleDetailArticle
@apiSuccessExample  {json}  Data Example
{
    "data": {
		"id": "66cd576d4927f32185e68abcx",
		"config": {
			"folder_id": "ifolderias212465123",
			"label": {},
			"public_link": "https://t1.mobio.vn/web-public/kbm/?token=66cd576d4927f32185e68abcx",
			"status": "activate",
			"tag_ids": [],
			"view_type": "internal"
		},
		"content": {
			"raw_content": [],
			"session_id": "0eaa5b3e-97f5-4e27-bcc2-17305137cfd7",
			"slug": "bai-viet-5-thieu-field-tra-ve",
			"title": "bai viet 5 thieu field tra ve"
		},
		"created_by": "71c44073-5d29-4882-a5f6-0094d9567137",
		"created_time": "2024-08-27T04:34:53Z",
		"has_multi_language": false,
		"last_published_by": null,
		"last_published_time": null,
        "editing_version": {
			"id": "67122dabdd6805d27ee94080",
			"action_type": "save_draft",
			"version_name": "Version 6",
			"action_time": "2024-10-18T09:43:07Z"
		},
        "viewing_version": {
			"id": "67122dabdd6805d27ee94080",
			"action_type": "save_draft",
			"version_name": "Version 6",
			"action_time": "2024-10-18T09:43:07Z"
		},
		"updated_by": "71c44073-5d29-4882-a5f6-0094d9567137",
		"updated_time": "2024-08-27T04:34:53Z"
	}
}
"""

# ================================================== ErrorArticleConflictSessionResponse ================================================== #
"""
@apiDefine ErrorArticleConflictSessionResponse
@apiErrorExample    {json}  Conflict Session
{
    "code": 4xx,
    "data": {
        "id": "643ccc4417a634e25f59fe93",
        "config": {
            "folder_id": "66bc2c486edc0e977f58e738",
            "status": "draft",
            "label": {
                "key": "new",
                "expire_after_day": 40,
            },
            "view_type": "internal",
            "public_link": "https://help.mobio.io/articles/cap-nhat-giao-dien-tinh-nang-theo-dong-san-pham/",
            "tag_ids": ["66bc2c93ff66924c416011f2", "66bc2cd6ff66924c416011f7"],
            "title": "Title của bài viết",
            "slug": "title-cua-bai-viet",
        }, 
        "content": {
            "session_id": "6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b",
            "raw_content": ... // Cấu trúc do FE định nghĩa
        },
    }
    "message":"Có một phiên bản mới vừa được lưu trong lúc bạn đang chỉnh sửa."
}
"""

"""
@apiDefine APIParamFilterArticle

@apiParam       (Body:)     {String}            [search]                                Từ khóa tìm kiếm bài viết
@apiParam       (Body:)     {ArrayString}       [folder_id]                            Danh sách <code>ID</code> thư mục của bài viết. Chỉ lấy cái bài viết trong ds thư mục này. Nếu không truyền thì sẽ lấy bài viết trong toàn bộ thư mục.
@apiParam       (Body:)     {ArrayString}       [status]                                Danh sách trạng thái bài viết cần tìm. Giá trị:
                                                                                        <ul>
                                                                                            <li><code>draft</code>: Trạng thái nháp, người đọc không thể thấy bài viết này</li>
                                                                                            <li><code>published</code>: Trạng thái public, người đọc có thể thấy bài viết này</li>
                                                                                        </ul>
@apiParam       (Body:)     {ArrayString}       [tag_ids]                               Danh sách <code>ID</code> tag bài viết cần tìm. Lấy danh sách [tại đây](#api-Tag-ListTag)
@apiParam       (Body:)     {ArrayString}       [view_type]                             Danh sách chế độ hiển thị thư mục. Lấy danh sách view type của bài viết [tại đây](#api-Constant-TypeViews)
@apiParam       (Body:)     {ArrayString}       [last_published_by]                     Danh sách ID người tạo xuất bản.
@apiParam       (Body:)     {ArrayString}       [last_published_time]                   Thông tin cần tìm kiếm theo thời gian xuất bản. Giá trị gửi lên theo giờ UTC.  Định dạng: <br/><code>%Y-%m-%dT%H:%MZ</code>
@apiParam       (Body:)     {ArrayString}       [created_time]                          Thông tin cần tìm kiếm theo thời gian tạo bài viết. Giá trị gửi lên theo giờ UTC.  Định dạng: <br/><code>%Y-%m-%dT%H:%MZ</code>
@apiParam       (Body:)     {ArrayString}       [updated_time]                          Thông tin cần tìm kiếm theo thời gian cập nhật bài viết. Giá trị gửi lên theo giờ UTC.  Định dạng: <br/><code>%Y-%m-%dT%H:%MZ</code>
@apiParam       (Body:)     {ArrayString}       [created_by]                            Danh sách ID người tạo bài viết kiếm.
@apiParam       (Body:)     {ArrayString}       [updated_by]                            Danh sách ID người cập nhật bài viết kiếm.


@apiParamExample    {json}  Body:
{
    "search": "Lorem",
    "forder_id": ["643ccc4417a634e25f59fe93"],
    "status": "draft",
    "view_type": ["internal"]
    "created_by": ["eda9de3a-68ce-11ee-9159-38d57a786a3e", "ef6611f6-68ce-11ee-b48f-38d57a786a3e"],
    "created_time": ["2023-11-05T03:00Z", "2023-11-05T010:00Z"]
}
"""

# ================================================== ResponseDetailArticleVersion ================================================== #
"""
@apiDefine APISuccessDetailArticleVersion

@apiSuccess     {ArrayObject}       data                                                 Dữ liệu trả về
@apiSuccess     {String}            data.id                                              <code>ID</code> version bài viết
@apiSuccess     {String}            data.action_type                                     Loại hành động. Giá trị:
                                                                                            <ul>
                                                                                                <li><code>auto_save</code></li>
                                                                                                <li><code>publish</code></li>
                                                                                                <li><code>save_draft</code></li>
                                                                                            </ul>
@apiSuccess     {String}            data.version_name                                     Tên version
@apiSuccess     {String}            data.action_time                                      User cập nhật lần cuối.
@apiSuccess     {String}            data.action_by                                        Thời điểm cập nhật lần cuối. Định dạng: yyyy-MM-ddTHH:mm:ssZ

@apiSuccess     {String}            data.article_data                                     Dữ liệu của bài viết
@apiSuccess     {String}            data.article_data.id                                  <code>ID</code> của bài viết

@apiSuccess     {Object}            data.article_data.config                              Cấu hình bài viết
@apiSuccess     {String}            data.article_data.config.folder_id                    <code>ID</code> thư mục đang chứa bài viết
@apiSuccess     {ArrayString}       [data.article_data.config.tag_ids]                    <code>ID</code> Tag
@apiSuccess     {String}            data.article_data.config.status                       Trạng thái của bài viết. Giá trị:
                                                                                            <ul>
                                                                                                <li><code>draft</code>: Bài viết đang ở trạng thái nháp. Người đọc không thể xem bài viết này.</li>
                                                                                                <li><code>published</code>: Bài viết đang ở trạng thái public. Người đọc có thể xem bài viết này.</li>
                                                                                            </ul>
@apiSuccess     {String}            data.article_data.config.view_type                    Chế độ hiển thị thư mục. Lấy chi tiết [tại đây](#api-Constant-DetailTypeViews)
@apiSuccess     {String}            [data.article_data.config.public_link]                Link public của bài viết
@apiSuccess     {Object}            [data.article_data.config.label]                      Nhãn hiện thị trên trang của người xem
@apiSuccess     {String}            data.article_data.config.label.key                    Key của nhãn:
                                                                                            <ul>
                                                                                                <li><code>new</code>: Mới</li>
                                                                                                <li><code>new_update</code>: Vừa mới cập nhật.</li>
                                                                                            </ul>
@apiSuccess     {Int}               data.article_data.config.label.expire_after_day       Số ngày nhãn được phép hiển thị trên trang của người đọc sau khi publish bài viết. Tối thiểu là <code>1</code> ngày
@apiSuccess     {Object}            data.article_data.content                             Nội dung bài viết
@apiSuccess     {String}            data.article_data.content.title                       Title của bài viết
@apiSuccess     {String}            data.article_data.content.slug                        Slug của bài viết
@apiSuccess     {String}            data.article_data.content.session_id                  Session content của bài viết hiện tại
@apiSuccess     {Any}               data.article_data.content.raw_content                 Cấu trúc content của bài viết (do FE định nghĩa)
@apiSuccess     {Boolean}           data.article_data.has_multi_language                  Bài viết đã có ngôn ngữ khác ngôn ngữ mặc định
@apiSuccess     {String}            data.article_data.last_published_time                 Thời gian bài viết được xuất bản
@apiSuccess     {String}            data.article_data.last_published_by                   User xuất bản bài viết

"""

"""
@apiDefine APISuccessExampleDetailArticleVersion
@apiSuccessExample  {json}  Data Example
{
    "data": {
        "id": "66bde733f992e28961275d86",
        "action_type": "save_draft",
        "version_name": "Version 2024/08/06 14:35",
        "action_time": "2024-08-06T14:35:00Z",
        "action_by":  "6a68668e-a550-444e-90f2-fdfd1f5d6bfa",
        "article_data": {
            "id": "66cd576d4927f32185e68abcx",
            "config": {
                "folder_id": "ifolderias212465123",
                "label": {},
                "public_link": "https://t1.mobio.vn/web-public/kbm/?token=66cd576d4927f32185e68abcx",
                "status": "activate",
                "tag_ids": [],
                "view_type": "internal"
            },
            "content": {
                "raw_content": [],
                "session_id": "0eaa5b3e-97f5-4e27-bcc2-17305137cfd7",
                "slug": "bai-viet-5-thieu-field-tra-ve",
                "title": "bai viet 5 thieu field tra ve"
            },
            "created_by": "71c44073-5d29-4882-a5f6-0094d9567137",
            "created_time": "2024-08-27T04:34:53Z",
            "has_multi_language": false,
            "last_published_by": null,
            "last_published_time": null,
            "updated_by": "71c44073-5d29-4882-a5f6-0094d9567137",
            "updated_time": "2024-08-27T04:34:53Z"
        }
    }
}
"""

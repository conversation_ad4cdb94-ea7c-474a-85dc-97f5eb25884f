#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 06/08/2024
"""

# ---------- Insight thư mục -----------
"""
@api {POST} {HOST}/knowledge-base/api/v1.0/folders/summary  Thống kê nhiều thư mục
@apiDescription API cung cấp thông tin thống kê của nhiều thư mục.
<br/>Hiện tại hỗ trợ 2 chỉ số tổng số lượng thư mục con, tổng số lượng bài viết.
@apiGroup Insight
@apiVersion 1.0.0
@apiName SummaryFolders

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiParam   (Body:)   {ArrayString}   folder_ids  Danh sách id của thư mục cần thống kê.<br/><code>Chú ý:</code> <PERSON><PERSON><PERSON> thư mục này cần có cùng thư mục cha hoặc phải cùng là thư mục gốc.
@apiParam   (Body:)   {Boolean}   [is_recursive=false]  Nếu <code>is_recursive=true</code> tính tổng tất cả thư mục con ở tất cả các cấp. Ngoài ra, chỉ tính thư mục con cấp 1 của thư mục cần tính
@apiParam   (Body:)    {ArrayString}  metrics   Danh sách key số liệu thống kê cần lấy.
<br/><ul>
<li><code>number_of_subfolders</code>: Số lượng thư mục con của thư mục hiện tại.</li>
<li><code>number_of_articles</code>: Số lượng bài viết của thư mục hiện tại.</li>
</ul>
@apiParamExample  {json}  Body Example:
{
  "folder_ids": [
    "642f85314d4356e983dd7415",
    "642fc65e2af4e1dbe42ea896",
    "6433c0959870fa17a0219613",
    "643ccc4417a634e25f59fe93"
  ],
  "metrics": [
    "number_of_subfolders",
    "number_of_articles"
  ],
  "is_recursive": false
}

@apiSuccess   {Int}   number_of_subfolders  Số lượng thư mục con của thư mục hiện tại.
@apiSuccess   {Int}   number_of_articles  Số lượng bài viết của thư mục hiện tại.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "number_of_subfolders": 200,
    "number_of_articles": 2000
  }
}
"""

# ---------- Insight thư mục -----------
"""
@api {POST} {HOST}/knowledge-base/api/v1.0/folders/<folder_id>/insight  Thống kê thư mục
@apiDescription API cung cấp thông tin thống kê của thư mục.
<br/>Hiện tại hỗ trợ 2 chỉ số số lượng thư mục con, số lượng bài viết.
@apiGroup Insight
@apiVersion 1.0.0
@apiName InsightFolder

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiParam   (Body:)   {Boolean}   [is_recursive=false]  Nếu <code>is_recursive=true</code> tính tổng tất cả thư mục con ở tất cả các cấp. Ngoài ra, chỉ tính thư mục con cấp 1 của thư mục cần tính
@apiParam   (Body:)    {ArrayString}  metrics   Danh sách key số liệu thống kê cần lấy.
<br/><ul>
<li><code>number_of_subfolders</code>: Số lượng thư mục con của thư mục hiện tại.</li>
<li><code>number_of_articles</code>: Số lượng bài viết của thư mục hiện tại.</li>
</ul>
@apiParamExample  {json}  Body Example:
{
  "metrics": [
    "number_of_subfolders",
    "number_of_articles"
  ]
}

@apiSuccess   {Int}   number_of_subfolders  Số lượng thư mục con của thư mục hiện tại.
@apiSuccess   {Int}   number_of_articles  Số lượng bài viết của thư mục hiện tại.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": {
    "number_of_subfolders": 200,
    "number_of_articles": 2000
  }
}
"""

# ================================================== List tag ================================================== #
"""
@api {GET} {domain}/knowledge-base/api/v1.0/tags Danh sách tag
@apiGroup Tag
@apiVersion 1.0.0
@apiName ListTag
@apiDescription API lấy danh sách tag đã được sử dụng trong KBM

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens
@apiUse merchant_id_header

@apiParam       (Query:)     {String}            [search]                                Từ khóa tìm kiếm tag
@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {ArrayObject}       data                            Dữ liệu trả về
@apiSuccess     {String}            data.id                         <code>ID</code> tag
@apiSuccess     {String}            data.value                      Value tag

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công",
    "data": [
        {
            "id": "61601a2acb97585cd950d847",
            "value": "Vay nợ tín dụng không cần trả",
        },
        {
            "id": "66bc2d5fff66924c41601207",
            "value": "Mua ô tô",
        },
    ],
    "paging": ... xem Paging example
}
"""

# ================================================== List tag by ids ================================================== #
"""
@api {POST} {domain}/knowledge-base/api/v1.0/tags/actions/get-by-ids    Lấy danh sách tag theo ids
@apiGroup Tag
@apiDescription Lấy danh sách detail tag theo ids
@apiVersion 1.0.0
@apiName ListTagByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam            (Body:)     {ArrayString} ids           Danh sách <code>ID</code> các tag cần lấy data
@apiParamExample     {json}      Body example
{
    "ids": ["61601a2acb97585cd950d847", "66bc2d5fff66924c41601207"]
}

@apiSuccess     {String}            message                         Mô tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {ArrayObject}       data                            Dữ liệu trả về
@apiSuccess     {String}            data.id                         <code>ID</code> tag
@apiSuccess     {String}            data.value                      Value tag


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "id": "61601a2acb97585cd950d847",
            "value": "Vay nợ tín dụng không cần trả",
        },
        {
            "id": "66bc2d5fff66924c41601207",
            "value": "Mua ô tô",
        },
    ]
}
"""
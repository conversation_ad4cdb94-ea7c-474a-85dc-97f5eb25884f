
# ================================================== Upload file ================================================== #
"""
@api {POST} {domain}/knowledge-base/api/v1.0/attachments/actions/batch-upload     Upload batch file
@apiGroup Attachment
@apiVersion 1.0.0
@apiName AttachmentUploadFile
@apiDescription API Upload nhiều file. Tối đa <b><code>5</code></b> file 1 lần upload

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (FormData:)   {ArrayFile}       files       Danh sách File cần upload. Tối đa <b><code>5</code></b> file 1 lần upload


@apiSuccess     {String}            message                         <PERSON><PERSON> tả phản hồi
@apiSuccess     {Integer}           code                            Mã phản hồi
@apiSuccess     {ArrayObject}       data                            Dữ liệu trả về
@apiSuccess     {String}            data.id                         <code>ID</code> file attachment
@apiSuccess     {String}            data.url                        URL truy cập file
@apiSuccess     {String}            data.filename                   Tên của file
@apiSuccess     {String}            data.mimetype                   Loại định dạng của file

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "id": "643ccc4417a634e25f59fe93",
        "url": "https://t1.mobio.vn/static/kbm/vi.png",
        "filename": "vi.png",
        "mimetype": "image/png"
    }]
}
"""
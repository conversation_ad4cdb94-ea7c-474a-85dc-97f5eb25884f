# ================================================== List KBM ================================================== #
"""
@api {get} {HOST}/knowledge-base/api/v1.0/knowledge-bases    Lấy danh sách Knowledge Base

@apiDescription API này dùng để lấy danh sách các Knowledge Base có sẵn trong hệ thống.
@apiGroup KnowledgeBase
@apiVersion 1.0.0
@apiName GetListKB

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header

@apiSuccess    {Number}               code                                          Mã trạng thái HTTP của phản hồi.
@apiSuccess    {String}               message                                       Thông điệp mô tả kết quả của yêu cầu.
@apiSuccess    {ArrayObject}          data                                          Danh sách Knowledge Base.
@apiSuccess    {String}               data.id                                       <code>ID</code> của Knowledge Base.
@apiSuccess    {String}               data.view_type                                Loại hiển thị Knowledge Base.
@apiSuccess    {String}               data.title                                    Tiêu đề của Knowledge Base.
@apiSuccess    {String}               data.domain                                   Domain Knowledge Base.
@apiSuccess    {String}               data.language_key                             Mã ngôn ngữ của Knowledge Base (ví dụ: <code>"vi"</code>).
@apiSuccess    {String}               data.slug                                     Slug của Knowledge Base.
@apiSuccess    {String}               data.site_theme_key                           Mã chủ đề của trang.
@apiSuccess    {String}               data.article_theme_key                        Mã chủ đề của bài viết. 

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "id": "66e9402697cb7310103a57b6",
            "view_type": "internal",
            "title": "Knowledge Base",
            "domain": "https://test28.kbm.io",
            "language_key": "vi",
            "slug": "kbm",
            "site_theme_key": "default",
            "article_theme_key": "default"
        }
    ]
}
"""

# ================================================== Detail KBM ================================================== #
"""
@api {GET} {HOST}/knowledge-base/api/v1.0/knowledge-bases/<knowledge_base_id>       Lấy chi tiết Knowledge Base

@apiDescription API này dùng để lấy  chi tiết Knowledge Base
@apiGroup KnowledgeBase
@apiVersion 1.0.0
@apiName GetDetailKB

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse json_header
@apiUse merchant_id_header


@apiSuccess    {Number}               code                                          Mã trạng thái HTTP của phản hồi.
@apiSuccess    {String}               message                                       Thông điệp mô tả kết quả của yêu cầu.
@apiSuccess    {Object}               data                                          Dữ liệu phản hồi.
@apiSuccess    {String}               data.id                                       <code>ID</code> của cấu hình Knowledge Base.
@apiSuccess    {String}               data.title                                    Tiêu đề của Knowledge Base.
@apiSuccess    {String}               data.view_type                                Chế độ hiển thị.
@apiSuccess    {String}               data.domain                                   Domain Knowledge Base.
@apiSuccess    {String}               data.language_key                             Mã ngôn ngữ của Knowledge Base (ví dụ: <code>"vi"</code>).
@apiSuccess    {String}               data.slug                                     Slug dùng để định danh Knowledge Base.
@apiSuccess    {Object}               data.selecting_site_theme                     Thông tin về chủ đề (theme) của trang Knowledge Base.
@apiSuccess    {Object}               data.selecting_site_theme.config              Cấu hình của theme. Cấu trúc do FE định nghĩa
@apiSuccess    {String}               data.selecting_site_theme.theme_key           Key dùng để phân biệt theme
@apiSuccess    {Object}               data.selecting_article_theme                  Thông tin về chủ đề (theme) cho bài viết.
@apiSuccess    {Object}               data.selecting_article_theme.config           Cấu hình của theme. Cấu trúc do FE định nghĩa.
@apiSuccess    {String}               data.selecting_article_theme.theme_key        Key dùng để phân biệt theme

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "id": "66e9402697cb7310103a57b6",
        "title": "Knowledge Base",
        "view_type": "internal",
        "domain": "https://test28.kbm.io",
        "language_key": "vi",
        "slug": "kbm",
        "selecting_site_theme": {
            "config": {},
        	"theme_key": "default"
        },
        "selecting_article_theme": {
            "config": {},
        	"theme_key": "default"
        }
    }
}
"""

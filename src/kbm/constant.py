#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 07/08/2024
"""

# ---------- L<PERSON><PERSON> danh sách ngôn ngữ -----------
"""
@api {GET} {domain}/knowledge-base/api/v1.0/languages                 L<PERSON>y danh sách ngôn ngữ
@apiGroup Constant
@apiVersion 1.0.0
@apiName Languages

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Array}             data                          Danh sách ngôn ngữ
@apiSuccess     {String}            data.key                      <code>key</code> của ngôn ngữ
@apiSuccess     {String}            data.name                     Tên ngôn ngữ
@apiSuccess     {String}            data.icon                     Thông tin icon của ngôn ngữ
@apiSuccess     {Bool}              data.is_default               Ngôn ngữ này có phải mặc định của merchant hay không?
                                                                  <ul>
                                                                    <li>true: đúng</li>
                                                                    <li>false: không</li>
                                                                  </ul>


@apiSuccessExample {json} Response:
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "key": "vi",
            "name": "Tiếng Việt",
            "icon": "vietnamese.png",
            "is_default": false
       }
    ]
}
"""

# ---------- Lấy danh sách kiểu xem -----------
"""
@api {GET} {domain}/knowledge-base/api/v1.0/view-types                 Lấy danh sách kiểu xem
@apiDescription Lấy danh sách kiểu xem theo ngôn ngữ của Web.
@apiGroup Constant
@apiVersion 1.0.0
@apiName TypeViews

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query

@apiParam   (Query:)    {String}           [use_for=folder]      Đối tượng sử dụng. Bao gồm giá trị: <code>folder</code>, <code>article</code>


@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Array}             data                          Danh sách kiểu xem
@apiSuccess     {String}            data.key                      <code>key</code> của kiểu xem
@apiSuccess     {String}            data.name                     Tên kiểu xem
@apiSuccess     {String}            data.description              Mô tả kiểu xem
@apiSuccess     {String}            data.text_display_status      Mô tả trạng thái của kiểu xem


@apiSuccessExample {json} Response:
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "key": "internal_enterprise",
            "name": "Nội bộ doanh nghiệp",
            "description": "Người xem cần đăng nhập"
            "text_display_status": "Hiển thị cho người xem trong nội bộ doanh nghiệp",
        }
    ]
}
"""
# ---------- Chi tiết kiểu xem by key -----------
"""
@api {GET} {domain}/knowledge-base/api/v1.0/view-types/<view_type_key>                 Lấy thông tin chi tiết kiểu xem bởi key
@apiDescription Lấy thông tin chi tiết kiểu xem bởi key
@apiGroup Constant
@apiVersion 1.0.0
@apiName DetailTypeViews

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse selected_lang_query
@apiParam   (Query:)    {String}           [use_for=folder]      Đối tượng sử dụng. Bao gồm giá trị: <code>folder</code>, <code>article</code>

@apiSuccess     {String}            message                       Mô tả phản hồi
@apiSuccess     {Integer}           code                          Mã phản hồi
@apiSuccess     {Object}             data                          Danh sách kiểu xem
@apiSuccess     {String}            data.key                      <code>key</code> của kiểu xem
@apiSuccess     {String}            data.name                     Tên kiểu xem
@apiSuccess     {String}            data.description              Mô tả kiểu xem
@apiSuccess     {String}            data.text_display_status      Mô tả trạng thái của kiểu xem


@apiSuccessExample {json} Response:
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "key": "internal_enterprise",
        "name": "Nội bộ doanh nghiệp",
        "description": "Người xem cần đăng nhập",
        "text_display_status": "Hiển thị cho người xem trong nội bộ doanh nghiệp",
    }
}
"""
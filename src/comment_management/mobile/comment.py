
"""
@api {POST} {domain}/comment/mobile/api/v1.0/comments              T<PERSON><PERSON> bình luận
@apiGroup Mobile Comment
@apiVersion 1.0.0
@apiName CreateComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {string}  [content]                 Nội dung bình luận.
@apiParam   (Body:) {string}  module                    Tên module (VD: task, note,...).
@apiParam   (Body:) {string}  module_id                 Id đối tượng của module.
@apiParam   (Body:) {string}  [parent_id]               Id của bình luận cha (nếu là bình luận cha thì <code>None</code>).
@apiParam   (Body:) {Array}   [file_attachment_ids]     Danh sách id của file.
@apiParam   (Body:) {Array}   [content_file_ids]        Danh sách id của file trong content.
@apiParam   (Body:) {array}   [mentions]                Danh sách người đ<PERSON>ợc mentions.
@apiParam   (Body:) {String}  [mentions.fe_id]          Id người được mentions được lưu trên FE..
@apiParam   (Body:) {String}  [mentions.account_id]     Id người được mentions.
@apiParam   (Body:) {object}        [position]                          Thông tin vị trí
@apiParam   (Body:) {float}         [position.latitude]                  Thông tin vĩ độ
@apiParam   (Body:) {float}         [position.longitude]                 Thông tin kinh độ


@apiParamExample {json} Body example
{
    "content": "String comment_management",
    "module": "note",
    "module_id": "62c7f7c2e6f54900100a040a",
    "parent_id": None,
    "content_file_ids": [],
    "file_attachment_ids": ["62e23717dcdbd7000fb6646c"],
    "mentions": [
        {
            "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
            "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
        }
    ],
    "position": {
        "latitude":  40.712776,
        "longitude": -74.005974,
    }
}


@apiSuccess {String}            _id                         Id bình luận
@apiSuccess {String}            merchant_id                 Id của merchant
@apiSuccess {String}            file_attachment_ids         Danh sách id file đính kèm.
@apiSuccess {String}            content_file_ids            Danh sách id file trong content.
@apiSuccess {String}            content                     Nội dung bình luận.
@apiSuccess {String}            parent_id                   Id của bình luận cha.
@apiSuccess {String}            path                        Đường dẫn của bình luận
@apiSuccess {String}            module                      Tên module
@apiSuccess {String}            module_id                   Id của đối tượng trong module
@apiSuccess {Boolean}           is_update                   Đã được chỉnh sửa hay chưa? True/False
@apiSuccess {Array}             mentions                    Danh sách người được mentions
@apiSuccess {String}            mentions.fe_id              Id người được mentions được lưu trên FE. được lưu trên FE
@apiSuccess {String}            mentions.account_id         Id người được mentions.
@apiSuccess {String}            created_by                  Được tạo bởi ai
@apiSuccess {String}            updated_by                  Được sửa bởi ai
@apiSuccess {String}            created_time                Thời gian được tạo
@apiSuccess {String}            updated_time                Thời gian cập nhật
@apiSuccess {object}            position                          Thông tin vị trí
@apiSuccess {float}             position.latitude                  Thông tin vĩ độ
@apiSuccess {float}             position.longitude                 Thông tin kinh độ


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "_id": "622ff4b5b5e5e86ec0cf8845",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "content": "String comment_management",
        "file_attachment_ids": ["62e23717dcdbd7000fb6646c"],
        "content_file_ids": ["62e23717dcdbd7000fb6646c"],
        "module_id": "62c7f7c2e6f54900100a040a",
        "is_update": False,
        "path": "622ff4b5b5e5e86ec0cf8845",
        "parent_id": None,
        "module": "note",
        "mentions": [
            {
                "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
                "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
            }
        ],
        "created_by": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "updated_by": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "created_time": "2019-03-27T17:52:46Z",
        "updated_time": "2019-03-27T17:52:46Z"
    }
}
"""

# ---------- Update Comment -----------
"""
@api {PUT} {domain}/comment/mobile/api/v1.0/comments/<comment_id>              Sửa bình luận
@apiGroup Mobile Comment
@apiVersion 1.0.0
@apiName UpdateComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {string}  [content]                 Nội dung bình luận.
@apiParam   (Body:) {Array}   [file_attachment_ids]     Danh sách id của file.
@apiParam   (Body:) {Array}   [content_file_ids]        Danh sách id của file trong content.
@apiParam   (Body:) {array}   [mentions]                Danh sách người được mentions.
@apiParam   (Body:) {String}  [mentions.fe_id]          Id người được mentions được lưu trên FE.
@apiParam   (Body:) {String}  [mentions.account_id]     Id người được mentions.
@apiParam   (Body:) {object}        [position]                          Thông tin vị trí
@apiParam   (Body:) {float}         [position.latitude]                  Thông tin vĩ độ
@apiParam   (Body:) {float}         [position.longitude]                 Thông tin kinh độ

@apiParamExample {json} Body example
{
    "content": "String comment_management",
    "file_attachment_ids": ["62e23717dcdbd7000fb6646c"],
    "content_file_ids": ["62e23717dcdbd7000fb6646c"],
    "mentions": [
        {
            "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
            "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
        }
    ]
}

@apiSuccess {String}            _id                         Id bình luận
@apiSuccess {String}            merchant_id                 Id của merchant
@apiSuccess {String}            file_attachment_ids         Danh sách Id của file đính kèm.
@apiSuccess {String}            content_file_ids            Danh sách Id của file trong content.
@apiSuccess {String}            content                     Nội dung bình luận.
@apiSuccess {String}            parent_id                   Id của bình luận cha.
@apiSuccess {String}            path                        Đường dẫn của bình luận
@apiSuccess {String}            module                      Tên module
@apiSuccess {String}            module_id                   Id của đối tượng trong module
@apiSuccess {Boolean}           is_update                   Đã được chỉnh sửa hay chưa? True/False
@apiSuccess {Array}             mentions                    Danh sách người được mentions
@apiSuccess {String}            mentions.fe_id              Id người được mentions được lưu trên FE.
@apiSuccess {String}            mentions.account_id         Id người được mentions.
@apiSuccess {String}            created_by                  Được tạo bởi ai
@apiSuccess {String}            updated_by                  Được sửa bởi ai
@apiSuccess {String}            created_time                Thời gian được tạo
@apiSuccess {String}            updated_time                Thời gian cập nhật
@apiSuccess {object}            position                          Thông tin vị trí
@apiSuccess {float}             position.latitude                  Thông tin vĩ độ
@apiSuccess {float}             position.longitude                 Thông tin kinh độ

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "_id": "622ff4b5b5e5e86ec0cf8845",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "content": "String comment_management",
        "file_attachment_ids": ["62e23717dcdbd7000fb6646c"],
        "content_file_ids": ["62e23717dcdbd7000fb6646c"],
        "module_id": "62c7f7c2e6f54900100a040a",
        "is_update": False,
        "path": "622ff4b5b5e5e86ec0cf8845",
        "parent_id": None,
        "module": "note",
        "mentions": [
            {
                "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
                "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
            }
        ],
        "created_by": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "updated_by": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "created_time": "2019-03-27T17:52:46Z",
        "updated_time": "2019-03-27T17:52:46Z"
    }
}
"""

# ---------- Delete Comment -----------
"""
@api {DELETE} {domain}/comment/mobile/api/v1.0/comments/<comment_id>              Xóa bình luận
@apiGroup Mobile Comment
@apiVersion 1.0.0
@apiName DeleteComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""


# ---------- Create file -----------
"""
@api {POST} {domain}/comment/mobile/api/v1.0/file-attachment             Thêm file đính kèm vào bình luận
@apiGroup File_attachment
@apiVersion 1.0.0
@apiName  CreateFileComment
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(FORM:)			{Binary}	    file		                Danh sách file upload


@apiSuccess (data)      {String}         id                      <code>ID</code> file upload lên hệ thống
@apiSuccess (data)      {Array}          type_media               Loại Media
@apiSuccess (data)      {String}         title                    Tên file upload
@apiSuccess (data)      {String}         format_file              Định dạng của file
@apiSuccess (data)      {String}         url                      Link file
@apiSuccess (data)      {String}         capacity                 Dung lượng file
@apiSuccess (data)      {String}         local_path               Đường dẫn vậy lý của file
@apiSuccess (data)      {String}         merchant_id              ID của merchant
@apiSuccess (data)      {String}         comment_id               ID của bình luận (mặc định là None)

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "id": "620dc147dfd20bf34ac6954f",
        "action_time": 1645043415.593171,
        "capacity": 1082923,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "filename": "1645068615_0E0A7559.jpg",
        "format_file": "image/jpeg",
        "id": "620dc147dfd20bf34ac6954f",
        "local_path": "/Users/<USER>/workspace/mobio/Module-Ticket/ticket/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "comment_id": None,
        "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# ---------- React Comment -----------
"""
@api {POST} {domain}/comment/mobile/api/v1.0/comments/action/reaction   Tương tác bình luận
@apiGroup Mobile Comment
@apiVersion 1.0.0
@apiName ReactComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {string}   comment_id        id cua comment ban muon react

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "comment_id": "64d1a6a9323667000cc79fca"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
# ---------- Create Comment -----------
"""
@api {POST} {domain}/comment/mobile/api/v1.0/comments              Tạo bình luận
@apiGroup Mobile Comment
@apiVersion 1.0.0
@apiName CreateComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {string}  [content]                 Nội dung bình luận.
@apiParam   (Body:) {string}  module                    Tên module (VD: task, note,...).
@apiParam   (Body:) {string}  module_id                 Id đối tượng của module.
@apiParam   (Body:) {string}  [parent_id]               Id của bình luận cha (nếu là bình luận cha thì <code>None</code>).
@apiParam   (Body:) {Array}   [file_attachment_ids]     Danh sách id của file.
@apiParam   (Body:) {Array}   [content_file_ids]        Danh sách id của file trong content.
@apiParam   (Body:) {array}   [mentions]                Danh sách người được mentions.
@apiParam   (Body:) {String}  [mentions.fe_id]          Id người được mentions được lưu trên FE..
@apiParam   (Body:) {String}  [mentions.account_id]     Id người được mentions.

@apiParamExample {json} Body example
{
    "content": "String comment_management",
    "module": "note",
    "module_id": "62c7f7c2e6f54900100a040a",
    "parent_id": None,
    "content_file_ids": [],
    "file_attachment_ids": ["62e23717dcdbd7000fb6646c"],
    "mentions": [
        {
            "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
            "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
        }
    ]
}


@apiSuccess {String}            id                         Id bình luận
@apiSuccess {String}            merchant_id                 Id của merchant
@apiSuccess {String}            file_attachment_ids         Danh sách id file đính kèm.
@apiSuccess {String}            content_file_ids            Danh sách id file trong content.
@apiSuccess {String}            content                     Nội dung bình luận.
@apiSuccess {String}            parent_id                   Id của bình luận cha.
@apiSuccess {String}            path                        Đường dẫn của bình luận
@apiSuccess {String}            module                      Tên module
@apiSuccess {String}            module_id                   Id của đối tượng trong module
@apiSuccess {Boolean}           is_update                   Đã được chỉnh sửa hay chưa? True/False
@apiSuccess {Array}             mentions                    Danh sách người được mentions
@apiSuccess {String}            mentions.fe_id              Id người được mentions được lưu trên FE. được lưu trên FE
@apiSuccess {String}            mentions.account_id         Id người được mentions.
@apiSuccess {String}            created_by                  Được tạo bởi ai
@apiSuccess {String}            updated_by                  Được sửa bởi ai
@apiSuccess {String}            created_time                Thời gian được tạo
@apiSuccess {String}            updated_time                Thời gian cập nhật


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "_id": "622ff4b5b5e5e86ec0cf8845",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "content": "String comment_management",
        "file_attachment_ids": ["62e23717dcdbd7000fb6646c"],
        "content_file_ids": ["62e23717dcdbd7000fb6646c"],
        "module_id": "62c7f7c2e6f54900100a040a",
        "is_update": False,
        "path": "622ff4b5b5e5e86ec0cf8845",
        "parent_id": None,
        "module": "note",
        "mentions": [
            {
                "fe_id": "39e14d90-da3e-11ec-b87d-acde48001122",
                "account_id": "59490b28-da3e-11ec-b87d-acde48001122"
            }
        ],
        "created_by": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "updated_by": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "created_time": "2019-03-27T17:52:46Z",
        "updated_time": "2019-03-27T17:52:46Z"
    }
}
"""


# ---------- Delete Comment -----------
"""
@api {DELETE} {domain}/comment/mobile/api/v1.0/comments/<comment_id>              Xóa bình luận
@apiGroup Mobile Comment
@apiVersion 1.0.0
@apiName DeleteComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

# ---------- Get list Comment -----------
"""
@api {Post} {domain}/comment/mobile/api/v1.0/comments/action/filter              Lấy danh sách bình luận
@apiGroup Mobile Comment
@apiVersion 1.0.0
@apiName GetListComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:) {string}   module        Tên module.
@apiParam   (Body:) {string}   module_id     Id của đối tượng module.
@apiParam   (Body:) {string}   [parent_id]   Id của comment_management cha.  

@apiParam   (Query:) {Int}      [last_id]     id của comment cũ nhất đã lấy ra (dùng để lấy các comment cũ hơn)
@apiParam   (Query:) {Int}      [per_page]    Số phần tử trên một page. Example: <code>&per_page=5</code>


@apiSuccess {String}            _id                         Id bình luận
@apiSuccess {String}            merchant_id                 Id của merchant
@apiSuccess {String}            file_attachment_ids         Danh sách Id của file đính kèm.
@apiSuccess {String}            content_file_ids            Danh sách Id của file trong content.
@apiSuccess {String}            content                     Nội dung bình luận.
@apiSuccess {String}            parent_id                   Id của bình luận cha.
@apiSuccess {String}            path                        Đường dẫn của bình luận
@apiSuccess {String}            module                      Tên module
@apiSuccess {String}            module_id                   Id của đối tượng trong module
@apiSuccess {Boolean}           is_update                   Đã được chỉnh sửa hay chưa? True/False
@apiSuccess {Array}             mentions                    Danh sách người được mentions
@apiSuccess {String}            mentions.fe_id              Id người được mentions được lưu trên FE
@apiSuccess {String}            mentions.account_id         Id người được mentions.
@apiSuccess {String}            created_by                  Được tạo bởi ai
@apiSuccess {String}            updated_by                  Được sửa bởi ai
@apiSuccess {String}            created_time                Thời gian được tạo
@apiSuccess {String}            updated_time                Thời gian cập nhật


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "_id": "64f58284fbc51d1b0f8365bd",
            "content": "1<div><br></div>",
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2023-09-04T14:08:52Z",
            "file_attachment_ids": [],
            "file_attachments": [],
            "id": "64f58284fbc51d1b0f8365bd",
            "is_update": false,
            "list_account_reacted": [],
            "mentions": [],
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "module": "TASK",
            "module_id": "64f043d3cd1849000da8616e",
            "parent_id": null,
            "path": "64f58284fbc51d1b0f8365bd",
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": "2023-09-04T14:08:52Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "64f58284fbc51d1b0f8365bd",
            "before": ""
        },
        "per_page": 5,
        "total_count": 6,
        "total_page": 2
    }
}
"""

# ---------- Count Comment -----------
"""
@api {POST} {domain}/comment/mobile/api/v1.0/comments/action/count              Đếm số bình luận theo từng đối tượng
@apiGroup Mobile Comment
@apiVersion 1.0.0
@apiName CountComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:) {string}   module        Tên module.
@apiParam   (Query:) {array}   module_ids     Danh sách Id của đôi tượng.


@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [
        {
            "module": "note",
            "module_id": "62c7f7c2e6f54900100a040a",
            "count_comment": 3
        }
    ]
    "message": "request thành công."
}
"""
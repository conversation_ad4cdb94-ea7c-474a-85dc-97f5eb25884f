#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 07/06/2022
"""
"""
@api {post} {domain}/product/bank/api/v1.0/merchant/groups/field Tạo mới một nhóm trường thông tin.

@apiName Create một group
@apiDescription API tạo mới một nhóm trường thông tin.
@apiVersion 1.0.0
@apiGroup GroupFieldBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header
@apiUse lang

@apiParam  (Body:)  {String} group_name Tên của nhóm trường thông tin sẽ được tạo.

@apiParamExample  {json}  Body:
{
	"group_name": "Thông tin người dùng MOBIO"
}

@apiSuccess {int} code Status code cho trạng thái của api(success : 200).
@apiSuccess {string} message Dòng thông báo hiện thị trạng thái thành công.
@apiSuccess {object} data Chứa thông tin.
@apiSuccess {string} data._id Id nhóm trường thông tin.
@apiSuccess {string} data.group_key Key của nhóm trường thông tin.
@apiSuccess {string} data.group_name Tên nhóm thông tin vừa tạo.
@apiSuccess {bool} data.is_base Là nhóm trường mặc định.
@apiSuccess {int} data.group_index Thứ tự của nhóm trong danh sách các nhóm trong hệ thống.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "641a87b0b6f97fd812c09c5a",
        "group_index": 9,
        "group_name": "Nhóm mới 7",
        "group_key": "group_nhom_moi_7",
        "is_base": false
    },
    "lang": "vi",
    "message": "request thành công."
}

"""

"""
@api {get} {domain}/product/bank/api/v1.0/merchant/groups/field Lấy danh sách nhóm trường thông tin.

@apiDescription API lấy ra danh sách thông tin.
@apiName Get list group
@apiGroup GroupFieldBank
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse order_sort
@apiParam   (Query:)    {String}  [sort=created_time]       Yêu cầu sắp xếp dữ liệu theo tiêu chí. Sử dụng: &sort=field_name(sắp xếp dữ liệu theo field_name).
@apiParam   (Query:)    {String}  [order=asc]      Sắp xếp dữ liệu theo chiều tăng dần(asc, A->Z) hoặc theo chiều giảm dần(desc, Z-A)


@apiSuccess {int} code Status code cho trạng thái của api(success : 200).
@apiSuccess {string} message Dòng thông báo hiện thị trạng thái thành công.
@apiSuccess {list} data Danh sách thông tin tất cả các nhóm đang dùng trong hệ thống.
@apiSuccess {string} data._id Id của một nhóm.
@apiSuccess {string} data.group_key Key của nhóm trường thông tin.
@apiSuccess {string} data.group_name Tên nhóm thông tin vừa tạo.
@apiSuccess {bool} data.is_base Là nhóm trường thông tin mặc định không thể xóa.
@apiSuccess {int} data.group_index Thứ tự của nhóm trong danh sách các nhóm trong hệ thống.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "_id": "641a872b8b24b8174e6c2f2c",
            "group_index": 1,
            "group_name": "Thông tin chung",
            "group_key": "information",
            "is_base": true
        },
        {
            "_id": "641a872b8b24b8174e6c2f2d",
            "group_index": 2,
            "group_key": "information_profiles",
            "group_name": "Thông tin sản phẩm theo profiles",
            "is_base": true
        },
        {
            "_id": "641a872b8b24b8174e6c2f2d",
            "group_index": 3,
            "group_key": "dynamic",
            "group_name": "Nhóm tùy biến",
            "is_base": true
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}

"""


"""
@api {put} {domain}/product/bank/api/v1.0/merchant/groups/field Cập nhật lại tên của nhóm trường thông tin.

@apiDescription API cập nhật lại tên nhóm trường thông tin.
@apiVersion 1.0.0
@apiName Update one name group
@apiGroup GroupFieldBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header
@apiUse lang

@apiParam  (Body:)  {String} group_key Key của nhóm trường thông tin.
@apiParam  (Body:)  {String} group_name Tên nhóm trường thông tin sẽ được sửa đổi.

@apiParamExample  {json}  Body:
{
	"group_key": "gdwdffgdw343535",
	"group_name": "Thông tin khách hàng"
}

@apiSuccess {int} code Status code cho trạng thái của api(success : 200).
@apiSuccess {object} data Chứa thông tin.
@apiSuccess {string} data._id Id nhóm trường thông tin.
@apiSuccess {string} data.group_name Tên nhóm mới được sửa.
@apiSuccess {string} data.group_key Key của nhóm trường thông tin.
@apiSuccess {int} data.group_index Index của nhóm trường thông tin.
@apiSuccess {bool} data.is_base là nhóm không thể xóa.
@apiSuccess {string} message Dòng thông báo hiện thị trạng thái thành công.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK

{
    "code": 200,
    "data": {
        "_id": "641a87b0b6f97fd812c09c5a",
        "group_index": 9,
        "group_key": "group_nhom_moi_7",
        "group_name": "Nhóm mới 3",
        "is_base": false
    },
    "lang": "vi",
    "message": "request thành công."
}


"""


"""
@api {delete} {domain}/product/bank/api/v1.0/merchant/groups/field Xóa một hoặc nhiều nhóm trường thông tin.

@apiDescription API xóa một nhóm trường thông tin trong hệ thống.
@apiVersion 1.0.0
@apiName Xóa một nhóm trường thông tin
@apiGroup GroupFieldBank

@apiParam {String} group_key_old Group key cần xóa.
@apiParam {String} [group_key_new] Nếu trong nhóm của group_key_old vẫn còn các field thì đây là nhóm group key sẽ được chuyển sang.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header
@apiUse lang

@apiSuccess {int} code Status code cho trạng thái của api(success : 200).
@apiSuccess {object} data Chứa thông tin số nhóm đã xóa và chưa được xóa.
@apiSuccess {List} data.list_field_not_update Hiển thị số nhóm group không bị xóa.
@apiSuccess {List} data.list_field_update Hiển thị số nhóm group đã bị xóa.
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "list_field_not_update": [],
        "list_field_update": []
    },
    "lang": "vi",
    "message": "request thành công."
}

"""


"""
@api {post} {domain}/product/bank/api/v1.0/merchant/groups/field/action/change-order Xắp xếp thứ tự của group.

@apiName Xắp xếp thứ tự của các group trong hệ thống
@apiDescription API thay đổi lại thứ tự tùy chỉnh của nhóm trường thông tin hiển thị.
@apiVersion 1.0.0
@apiGroup GroupFieldBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header
@apiUse lang

@apiParam  (Body:)  {String} group_key Key của nhóm .
@apiParam  (Body:)  {Int}  group_index Thứ tự các nhóm.
@apiParamExample  {json}  Body:
{
    "groups": [
        {
            "group_index": 1,
            "group_key": "23fdfsdsfafdfsddf"
        },
        {
            "group_index": 2,
            "group_key": "23fdfsdsfafdsddfdf"
        },
        {
            "group_index": 3,
            "group_key": "23fdfsdsfafdfdfsds"
        }
    ]
}

@apiSuccess {int} code Status code cho trạng thái của api(success : 200).
@apiSuccess {string} message Dòng thông báo hiện thị trạng thái thành công.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {},
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {post} {domain}/product/bank/api/v1.0/merchant/fields/action/change-group Thay đổi groups của các field.

@apiDescription API thay đổi groups của các field.
@apiName MerchantBankUpdateGroupOfField
@apiVersion 1.0.0
@apiGroup GroupFieldBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header
@apiUse lang

@apiParam  (Body:)  {String} group_key Key của nhóm trường thông tin.
@apiParam  (Body:)  {List}  fields_key Field_key của các field cần chuyển nhóm trường thông tin.

@apiParamExample  {json}  Body:
{
    "group_key": "information",
    "fields_key": [
		"syh_23244_F32fgdfdafsd",
		"syh_23244_Fdafsddsfd",
		"syh_23244_Fdafssdfsd",
		"syh_23244_Fdwsdafsd"
	]
}

@apiSuccess {int} code Status code cho trạng thái của api(success : 200).
@apiSuccess {object} data Chứa thông tin.
@apiSuccess {string} message Dòng thông báo hiện thị trạng thái thành công.

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "list_field_not_update": [],
        "list_field_update": [
            "_dyn_1_dropdown_multiple_select_1676000270294",
            "_dyn_1_dd_mm_yyyy_hh_mm_1676000943225"
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}

"""


"""
@api {GET} {domain}/product/bank/api/v1.0/merchant/fields/groups-base              Lấy danh sách group base
@apiDescription API lấy danh sách group base.
@apiName MerchantBankListGroupFieldBase
@apiVersion 1.0.0
@apiGroup GroupFieldBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header
@apiUse lang

@apiSuccess {int} code          Status code cho trạng thái của api(success : 200).
@apiSuccess {string} message    Dòng thông báo hiện thị trạng thái thành công.
@apiSuccess {object} data               Danh sách thông tin các nhóm field base.




@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "information": {
            "en": "Information",
            "vi": "Thông tin chung"
        }
    },
    "lang": "vi",
    "message": "request thành công."
}

"""

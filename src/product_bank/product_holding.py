#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 21/06/2022
"""
"""
@api {get} {domain}/product/bank/api/v1.0/transaction_office    Danh sách chi nhánh/ PGD mở
@apiDescription     List Transaction Office
@apiGroup BankTransactionOffice
@apiVersion 1.0.0
@apiName  ListTransactionOffice
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Number} code       Response status    
@apiSuccess {String} message    Response message
@apiSuccess {Array} data       Danh sách chi nhánh/ PGD mở

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [
    "Chi Nhánh Cầu <PERSON>"
  ], 
  "lang": "vi", 
  "message": "request thành công."
}
"""
"""
@api {get} {domain}/product/bank/api/v1.0/product_status    Danh sách trạng thái sản phẩm
@apiDescription     List Product Status
@apiGroup BankProductStatus
@apiVersion 1.0.0
@apiName  ListProductStatus
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess {Number} code       Response status    
@apiSuccess {String} message    Response message
@apiSuccess {Array} data       Danh sách trạng thái sản phẩm

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [
    "Card Ok"
  ], 
  "lang": "vi", 
  "message": "request thành công."
}
"""
"""
@api {get} {domain}/product/bank/api/v1.0/product-holding/status    Danh sách trạng thái sử dụng của dòng sản phẩm/ sản phẩm
@apiDescription     Lấy danh sách trạng thái sử dụng dòng sản phẩm/ sản phẩm
@apiGroup BankProductStatus
@apiVersion 1.0.0
@apiName  ListStatusProductHolding
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (QUERY:) {string}    [type=product,product_line]               Loại muốn lấy danh sách trạng thái sử dụng dòng sản phẩm/ sản phẩm

@apiSuccess {Number} code       Response status    
@apiSuccess {String} message    Response message
@apiSuccess {Array} data        Danh sách trạng thái
@apiSuccess {String} data.key   Key của trạng thái
@apiSuccess {String} data.name  Tên của trạng thái

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": [
    {
      "key": "activate",
      "name": "Đang sử dụng"
    },
    {
      "key": "expired",
      "name": "Hết hạn"
    },
    {
      "key": "unused",
      "name": "Chưa sử dụng"
    }
  ], 
  "lang": "vi", 
  "message": "request thành công."
}
"""

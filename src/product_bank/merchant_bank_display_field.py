#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 08/06/2022
"""
# ******************************** L<PERSON><PERSON> danh sách field thuộc dòng sản phẩm trong dataflow ******************
# version: 1.0.0                                                                       *
# **************************************************************************************
"""
@api {GET} {domain}/product/bank/api/v1.0/merchant/field/list/data-flow             Lấy danh sách field thuộc dòng sản phẩm trong dataflow
@apiDescription         Lấy danh sách field thuộc dòng sản phẩm trong dataflow
@apiGroup BankDisplayDynamicField
@apiVersion 1.0.0
@apiName  MerchantBankFieldListInDataFlow
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Query:)     {String}     product_line_code     <code>Mã</code> dòng sản phẩm cần lấy cấu hình hiển thị 

@apiParamExample    {Param}      Query:
{
    ?product_line_code=KHACHHANG_eb1680bc
}


@apiSuccess   (Response:)     {Array}     data         Dữ liệu trả về.
@apiSuccess   (Response:)     {String}    data.group   Group Dynamic Field
@apiSuccess   (Response:)     {Array}     data.fields       Danh sách field
@apiSuccess   (Response:)     {String}    data.fields.field_name         Tên field.
@apiSuccess   (Response:)     {String}    data.fields.field_name         Tên field.
@apiSuccess   (Response:)     {String}    data.fields.field_key          Key định danh của field.
@apiSuccess   (Response:)     {Number}    data.fields.field_property            Kiểu dữ liệu của field.
@apiSuccess   (Response:)     {Bool}      data.fields.display_in_form         Hiển thị field này ở form chọn dữ liệu?
@apiSuccess   (Response:)     {Number}    data.fields.order         thứ tự sắp xếp.
@apiSuccess   (Response:)     {String}    data.fields.group         Nhóm của field.
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_form_input         Hiển thị field này ở form add/update Product?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_form_input     Disable remove field ở form add Product?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_form_update     Disable remove field ở form update Product?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_list     Disable remove field ở danh sách Product?
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_import_file     Hiển thị field trong import file?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_form_add  Không cho phép xóa ở form thêm
@apiSuccess   (Response:)     {Bool}    data.fields.disable_required_form_add     Không cho phép required ở form thêm
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_form_update     Không cho phép xóa ở form sửa
@apiSuccess   (Response:)     {Bool}    data.fields.disable_required_form_update     Không cho phép required ở form sửa
@apiSuccess   (Response:)     {Bool}    data.fields.disable_status_display_color     Không cho phép trạng thái hiển thị màu
@apiSuccess   (Response:)     {Bool}    data.fields.display_detail     Hiển thị ở chi tiết Product
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_filter     Hiển thị ở bộ lọc
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_form_add     Hiển thị ở bộ lọc form add
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_form_add_selected     Hiển thị ở bộ lọc form add
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_form_update     Hiển thị ở bộ lọc form update
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_form_update_selected     Hiển thị ở bộ lọc form update
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_import_file     Hiển thị ở phần import file
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_list_field     Hiển thị ở danh sách filed
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_list_field_selected     Hiển thị ở danh sách filed
@apiSuccess   (Response:)     {Bool}    data.fields.display_type     Kiểu hiển thị
@apiSuccess   (Response:)     {Bool}    data.fields.enable_data_color     Bật sử dụng màu
@apiSuccess   (Response:)     {Array}   data.fields.list_display_area     Danh sách khu vực hiển thị được thao tác bật tắt hiển thị
                                                                      <ul>
                                                                            <li><code>list_product</code>: Danh sách sản phẩm</li>
                                                                            <li><code>information_product</code>: Thông tin sản phẩm</li>
                                                                            <li><code>product_filter</code>: Bộ lọc sản phẩm</li>
                                                                            <li><code>product_holding</code>: Product Holding</li>
                                                                            <li><code>product_holding_filter</code>: Bộ lọc Product Holding</li>
                                                                      </ul>
@apiSuccess   (Response:)     {String}  data.fields.status_display_area_list_product     Trạng thái hiển thị ở khu vực Danh sách sản phẩm
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>                                                                      
@apiSuccess   (Response:)     {String}  data.fields.status_display_area_information_product     Trạng thái hiển thị ở khu vực Thông tin sản phẩm
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiSuccess   (Response:)     {String}  data.fields.status_display_area_product_filter          Trạng thái hiển thị ở khu vực Bộ lọc sản phẩm
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiSuccess   (Response:)     {String}  data.fields.status_display_area_product_holding_filter  Trạng thái hiển thị ở khu vực Bộ lọc Product Holding
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiSuccess   (Response:)     {String}  data.fields.status_display_area_product_holding  Trạng thái hiển thị ở khu vực Product Holding
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiSuccess   (Response:)     {Number}    data.fields.order_area_list_product  Thứ tự sắp xếp ở khu vực Danh sách sản phẩm. <code>Trong trường hợp bị ẩn thì order = -99</code>
@apiSuccess   (Response:)     {Number}    data.fields.order_area_information_product  Thứ tự sắp xếp ở khu vực Thông tin sản phẩm. <code>Trong trường hợp bị ẩn thì order = -99</code>
@apiSuccess   (Response:)     {Number}    data.fields.order_area_product_holding_filter  Thứ tự sắp xếp ở khu vực Bộ lọc Product Holding. <code>Trong trường hợp bị ẩn thì order = -99</code>
@apiSuccess   (Response:)     {Number}    data.fields.order_area_product_filter  Thứ tự sắp xếp ở khu vực Bộ lọc. <code>Trong trường hợp bị ẩn thì order = -99</code>
@apiSuccess   (Response:)     {Number}    data.fields.order_area_product_holding  Thứ tự sắp xếp ở khu vực Product Holding. <code>Trong trường hợp bị ẩn thì order = -99</code>
@apiSuccess   (Response:)     {Bool}    data.fields.required_area_list_product  Bắt buộc hiển thị ở khu vực Danh sách sản phẩm 
@apiSuccess   (Response:)     {Bool}    data.fields.required_area_information_product  Bắt buộc hiển thị ở khu vực Thông tin sản phẩm 
@apiSuccess   (Response:)     {Bool}    data.fields.required_area_product_holding_filter  Bắt buộc hiển thị ở khu vực Bộ lọc Product Holding
@apiSuccess   (Response:)     {Bool}    data.fields.required_area_product_filter  Bắt buộc hiển thị ở bộ lọc sản phẩm
@apiSuccess   (Response:)     {Bool}    data.fields.required_area_product_holding  Bắt buộc hiển thị ở Product Holding
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_area_list_product     Disable remove field ở khu vực Danh sách sản phẩm?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_area_information_product     Disable remove field ở khu vực Thông tin sản phẩm?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_area_product_holding_filter     Disable remove field ở khu vực Bộ lọc Product Holding?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_area_product_filter     Disable remove field ở khu vực Bộ lọc Product?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_area_product_holding     Disable remove field ở khu vực Product Holding?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_un_select_require_area_list_product  Disable bỏ chon bắt buộc hiển thị ở khu vực Danh sách sản phẩm 
@apiSuccess   (Response:)     {Bool}    data.fields.disable_un_select_require_area_information_product  Disable bỏ chon bắt buộc hiển thị ở khu vực Thông tin sản phẩm 
@apiSuccess   (Response:)     {Bool}    data.fields.disable_un_select_require_area_product_holding_filter  Disable bỏ chon bắt buộc hiển thị ở khu vực  Bộ lọc Product Holding
@apiSuccess   (Response:)     {Bool}    data.fields.disable_un_select_require_area_product_filter  Disable bỏ chon bắt buộc hiển thị ở khu vực  bộ lọc sản phẩm
@apiSuccess   (Response:)     {Bool}    data.fields.disable_un_select_require_area_product_holding  Disable bỏ chon bắt buộc hiển thị ở khu vực  Product Holding
@apiSuccess   (Response:)     {Bool}   data.fields.enable_data_color     Bật sử dụng màu
@apiSuccess   (Response:)     {String}    data.fields.format      Định dạng
@apiSuccess   (Response:)     {Array}    data.fields.history      Lịch sử cập nhật trường dynamic
@apiSuccess   (Response:)     {Bool}    data.fields.is_base     Field này có phải field của hệ thống hay không?
@apiSuccess   (Response:)     {Bool}    data.fields.is_change_group     Có được thay đổi group hay không ?
@apiSuccess   (Response:)     {String}    data.fields.type_save_data     Kiểu lưu dữ liệu


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "group":"information",
      "fields":[
        "allow_add_data_select": true,
        "allow_change_position": false,
        "allow_edit_description": true,
        "allow_edit_name": true,
        "allow_edit_position_display": true,
        "allow_use_color": false,
        "created_time": **********.806242,
        "data_select_position_pin": [],
        "data_selected": null,
        "description": "",
        "disable_remove_form_add": false,
        "disable_remove_form_input": false,
        "disable_remove_form_update": false,
        "disable_remove_list": false,
        "disable_required_form_add": false,
        "disable_required_form_input": false,
        "disable_required_form_update": false,
        "disable_status_display_color": false,
        "display_detail": true,
        "display_in_filter": true,
        "display_in_form_add": true,
        "display_in_form_add_selected": true,
        "display_in_form_update": true,
        "display_in_form_update_selected": true,
        "display_in_import_file": true,
        "display_in_list_field": true,
        "display_in_list_field_selected": true,
        "display_type": "single_line",
        "enable_data_color": false,
        "field_key": "_dyn_test_**********806",
        "field_name": "Test",
        "field_property": 2,
        "format": null,
        "group": "dynamic",
        "history": [
            {
                "created_time": "Wed, 20 Apr 2022 10:13:48 GMT",
                "fullname": "Nguy\u1ec5n V\u0103n An",
                "staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                "username": "admin@pingcomshop"
            }
        ],
        "is_base": false,
        "is_change_group": true,
        "is_encrypt": null,
        "last_update_by": {
            "created_time": "Wed, 20 Apr 2022 10:13:48 GMT",
            "fullname": "Nguy\u1ec5n V\u0103n An",
            "staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "username": "admin@pingcomshop"
        },
        "order": 138,
        "order_form_add": 203,
        "order_form_update": 52,
        "order_list_field": 281,
        "permission_remove_field": true,
        "required_form_add": false,
        "required_form_update": false,
        "status": 1,
        "list_display_area": [
            "list_product",
            "information_product",
            "product_filter",
            "product_holding",
            "product_holding_filter"
        ],
        "status_display_area_list_product": "no_display",
        "status_display_area_information_product": "no_display",
        "status_display_area_product_filter": "hidden",
        "status_display_area_product_holding": "display",
        "status_display_area_product_holding_filter": "no_display",
        "order_area_list_product": 1,
        "order_area_information_product": 1,
        "order_area_product_holding_filter": 1,
        "order_area_product_holding": 1,
        "order_area_product_filter": 1,
        "order_area_information_product": 1,
        "required_area_list_product": true,
        "required_area_information_product": true,
        "required_area_product_holding_filter": true,
        "required_area_product_filter": true,
        "required_area_product_holding": true,
        "disable_remove_area_list_product": true,
        "disable_remove_area_product_holding_filter": true,
        "disable_remove_area_product_filter": true,
        "disable_remove_area_product_holding": true,
        "disable_un_select_require_area_list_product": false,
        "disable_un_select_require_area_product_holding_filter": false,
        "disable_un_select_require_area_product_filter": false,
        "disable_un_select_require_area_product_holding": false,
        "status_display_color": true,
        "support_sort": false,
        "translate_key": null,
        "type_save_data": "text",
        "updated_time": **********.806214,
      ]
    },
    {
      "group": "information_profiles",
      "fields":[]
    },
    {
      "group":"dynamic",
      "fields":[
      ]
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""
# ******************************** Merchant Bank Field Config Display ******************
# version: 1.0.0                                                                       *
# **************************************************************************************
"""
@api {post} {domain}/product/bank/api/v1.0/merchant/field/display/action/filter             Merchant Bank Action Filter Display
@apiDescription         Merchant Bank Action Filter Display
@apiGroup BankDisplayDynamicField
@apiVersion 1.0.0
@apiName  MerchantBankDisplayActionFilter
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Body:)     {String}     product_line_code     <code>Mã</code> dòng sản phẩm cần lấy cấu hình hiển thị 

@apiParamExample    {json}      Body:
{
    "product_line_code": "KHACHHANG_eb1680bc"
}


@apiSuccess   (Response:)     {Array}     data         Dữ liệu trả về.
@apiSuccess   (Response:)     {String}    data.group   Group Dynamic Field
@apiSuccess   (Response:)     {Array}     data.fields       Danh sách field
@apiSuccess   (Response:)     {String}    data.fields.field_name         Tên field.
@apiSuccess   (Response:)     {String}    data.fields.field_name         Tên field.
@apiSuccess   (Response:)     {String}    data.fields.field_key          Key định danh của field.
@apiSuccess   (Response:)     {Number}    data.fields.field_property            Kiểu dữ liệu của field.
@apiSuccess   (Response:)     {Bool}      data.fields.display_in_form         Hiển thị field này ở form chọn dữ liệu?
@apiSuccess   (Response:)     {Number}    data.fields.order         thứ tự sắp xếp.
@apiSuccess   (Response:)     {String}    data.fields.group         Nhóm của field.
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_form_input         Hiển thị field này ở form add/update Product?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_form_input     Disable remove field ở form add Product?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_form_update     Disable remove field ở form update Product?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_list     Disable remove field ở danh sách Product?
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_import_file     Hiển thị field trong import file?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_form_add  Không cho phép xóa ở form thêm
@apiSuccess   (Response:)     {Bool}    data.fields.disable_required_form_add     Không cho phép required ở form thêm
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_form_update     Không cho phép xóa ở form sửa
@apiSuccess   (Response:)     {Bool}    data.fields.disable_required_form_update     Không cho phép required ở form sửa
@apiSuccess   (Response:)     {Bool}    data.fields.disable_status_display_color     Không cho phép trạng thái hiển thị màu
@apiSuccess   (Response:)     {Bool}    data.fields.display_detail     Hiển thị ở chi tiết Product
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_filter     Hiển thị ở bộ lọc
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_form_add     Hiển thị ở bộ lọc form add
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_form_add_selected     Hiển thị ở bộ lọc form add
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_form_update     Hiển thị ở bộ lọc form update
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_form_update_selected     Hiển thị ở bộ lọc form update
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_import_file     Hiển thị ở phần import file
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_list_field     Hiển thị ở danh sách filed
@apiSuccess   (Response:)     {Bool}    data.fields.display_in_list_field_selected     Hiển thị ở danh sách filed
@apiSuccess   (Response:)     {Bool}    data.fields.display_type     Kiểu hiển thị
@apiSuccess   (Response:)     {Bool}    data.fields.enable_data_color     Bật sử dụng màu
@apiSuccess   (Response:)     {Array}   data.fields.list_display_area     Danh sách khu vực hiển thị được thao tác bật tắt hiển thị
                                                                      <ul>
                                                                            <li><code>list_product</code>: Danh sách sản phẩm</li>
                                                                            <li><code>information_product</code>: Thông tin sản phẩm</li>
                                                                            <li><code>product_filter</code>: Bộ lọc sản phẩm</li>
                                                                            <li><code>product_holding</code>: Product Holding</li>
                                                                            <li><code>product_holding_filter</code>: Bộ lọc Product Holding</li>
                                                                      </ul>
@apiSuccess   (Response:)     {String}  data.fields.status_display_area_list_product     Trạng thái hiển thị ở khu vực Danh sách sản phẩm
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>                                                                      
@apiSuccess   (Response:)     {String}  data.fields.status_display_area_information_product     Trạng thái hiển thị ở khu vực Thông tin sản phẩm
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiSuccess   (Response:)     {String}  data.fields.status_display_area_product_filter          Trạng thái hiển thị ở khu vực Bộ lọc sản phẩm
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiSuccess   (Response:)     {String}  data.fields.status_display_area_product_holding_filter  Trạng thái hiển thị ở khu vực Bộ lọc Product Holding
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiSuccess   (Response:)     {String}  data.fields.status_display_area_product_holding  Trạng thái hiển thị ở khu vực Product Holding
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiSuccess   (Response:)     {Number}    data.fields.order_area_list_product  Thứ tự sắp xếp ở khu vực Danh sách sản phẩm. <code>Trong trường hợp bị ẩn thì order = -99</code>
@apiSuccess   (Response:)     {Number}    data.fields.order_area_information_product  Thứ tự sắp xếp ở khu vực Thông tin sản phẩm. <code>Trong trường hợp bị ẩn thì order = -99</code>
@apiSuccess   (Response:)     {Number}    data.fields.order_area_product_holding_filter  Thứ tự sắp xếp ở khu vực Bộ lọc Product Holding. <code>Trong trường hợp bị ẩn thì order = -99</code>
@apiSuccess   (Response:)     {Number}    data.fields.order_area_product_filter  Thứ tự sắp xếp ở khu vực Bộ lọc. <code>Trong trường hợp bị ẩn thì order = -99</code>
@apiSuccess   (Response:)     {Number}    data.fields.order_area_product_holding  Thứ tự sắp xếp ở khu vực Product Holding. <code>Trong trường hợp bị ẩn thì order = -99</code>
@apiSuccess   (Response:)     {Bool}    data.fields.required_area_list_product  Bắt buộc hiển thị ở khu vực Danh sách sản phẩm 
@apiSuccess   (Response:)     {Bool}    data.fields.required_area_information_product  Bắt buộc hiển thị ở khu vực Thông tin sản phẩm 
@apiSuccess   (Response:)     {Bool}    data.fields.required_area_product_holding_filter  Bắt buộc hiển thị ở khu vực Bộ lọc Product Holding
@apiSuccess   (Response:)     {Bool}    data.fields.required_area_product_filter  Bắt buộc hiển thị ở bộ lọc sản phẩm
@apiSuccess   (Response:)     {Bool}    data.fields.required_area_product_holding  Bắt buộc hiển thị ở Product Holding
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_area_list_product     Disable remove field ở khu vực Danh sách sản phẩm?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_area_information_product     Disable remove field ở khu vực Thông tin sản phẩm?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_area_product_holding_filter     Disable remove field ở khu vực Bộ lọc Product Holding?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_area_product_filter     Disable remove field ở khu vực Bộ lọc Product?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_remove_area_product_holding     Disable remove field ở khu vực Product Holding?
@apiSuccess   (Response:)     {Bool}    data.fields.disable_un_select_require_area_list_product  Disable bỏ chon bắt buộc hiển thị ở khu vực Danh sách sản phẩm 
@apiSuccess   (Response:)     {Bool}    data.fields.disable_un_select_require_area_information_product  Disable bỏ chon bắt buộc hiển thị ở khu vực Thông tin sản phẩm 
@apiSuccess   (Response:)     {Bool}    data.fields.disable_un_select_require_area_product_holding_filter  Disable bỏ chon bắt buộc hiển thị ở khu vực  Bộ lọc Product Holding
@apiSuccess   (Response:)     {Bool}    data.fields.disable_un_select_require_area_product_filter  Disable bỏ chon bắt buộc hiển thị ở khu vực  bộ lọc sản phẩm
@apiSuccess   (Response:)     {Bool}    data.fields.disable_un_select_require_area_product_holding  Disable bỏ chon bắt buộc hiển thị ở khu vực  Product Holding
@apiSuccess   (Response:)     {Bool}   data.fields.enable_data_color     Bật sử dụng màu
@apiSuccess   (Response:)     {String}    data.fields.format      Định dạng
@apiSuccess   (Response:)     {Array}    data.fields.history      Lịch sử cập nhật trường dynamic
@apiSuccess   (Response:)     {Bool}    data.fields.is_base     Field này có phải field của hệ thống hay không?
@apiSuccess   (Response:)     {Bool}    data.fields.is_change_group     Có được thay đổi group hay không ?
@apiSuccess   (Response:)     {String}    data.fields.type_save_data     Kiểu lưu dữ liệu


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "group":"information",
      "fields":[
        "allow_add_data_select": true,
        "allow_change_position": false,
        "allow_edit_description": true,
        "allow_edit_name": true,
        "allow_edit_position_display": true,
        "allow_use_color": false,
        "created_time": **********.806242,
        "data_select_position_pin": [],
        "data_selected": null,
        "description": "",
        "disable_remove_form_add": false,
        "disable_remove_form_input": false,
        "disable_remove_form_update": false,
        "disable_remove_list": false,
        "disable_required_form_add": false,
        "disable_required_form_input": false,
        "disable_required_form_update": false,
        "disable_status_display_color": false,
        "display_detail": true,
        "display_in_filter": true,
        "display_in_form_add": true,
        "display_in_form_add_selected": true,
        "display_in_form_update": true,
        "display_in_form_update_selected": true,
        "display_in_import_file": true,
        "display_in_list_field": true,
        "display_in_list_field_selected": true,
        "display_type": "single_line",
        "enable_data_color": false,
        "field_key": "_dyn_test_**********806",
        "field_name": "Test",
        "field_property": 2,
        "format": null,
        "group": "dynamic",
        "history": [
            {
                "created_time": "Wed, 20 Apr 2022 10:13:48 GMT",
                "fullname": "Nguy\u1ec5n V\u0103n An",
                "staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                "username": "admin@pingcomshop"
            }
        ],
        "is_base": false,
        "is_change_group": true,
        "is_encrypt": null,
        "last_update_by": {
            "created_time": "Wed, 20 Apr 2022 10:13:48 GMT",
            "fullname": "Nguy\u1ec5n V\u0103n An",
            "staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "username": "admin@pingcomshop"
        },
        "order": 138,
        "order_form_add": 203,
        "order_form_update": 52,
        "order_list_field": 281,
        "permission_remove_field": true,
        "required_form_add": false,
        "required_form_update": false,
        "status": 1,
        "list_display_area": [
            "list_product",
            "information_product",
            "product_filter",
            "product_holding",
            "product_holding_filter"
        ],
        "status_display_area_list_product": "no_display",
        "status_display_area_information_product": "no_display",
        "status_display_area_product_filter": "hidden",
        "status_display_area_product_holding": "display",
        "status_display_area_product_holding_filter": "no_display",
        "order_area_list_product": 1,
        "order_area_information_product": 1,
        "order_area_product_holding_filter": 1,
        "order_area_product_holding": 1,
        "order_area_product_filter": 1,
        "order_area_information_product": 1,
        "required_area_list_product": true,
        "required_area_information_product": true,
        "required_area_product_holding_filter": true,
        "required_area_product_filter": true,
        "required_area_product_holding": true,
        "disable_remove_area_list_product": true,
        "disable_remove_area_product_holding_filter": true,
        "disable_remove_area_product_filter": true,
        "disable_remove_area_product_holding": true,
        "disable_un_select_require_area_list_product": false,
        "disable_un_select_require_area_product_holding_filter": false,
        "disable_un_select_require_area_product_filter": false,
        "disable_un_select_require_area_product_holding": false,
        "status_display_color": true,
        "support_sort": false,
        "translate_key": null,
        "type_save_data": "text",
        "updated_time": **********.806214,
      ]
    },
    {
      "group": "information_profiles",
      "fields":[]
    },
    {
      "group":"dynamic",
      "fields":[
      ]
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""
"""
@api {put} {domain}/product/bank/api/v1.0/merchant/field/display/action/update             Merchant Bank Action Update Display
@apiDescription         Merchant Bank Action Update Display
@apiGroup BankDisplayDynamicField
@apiVersion 1.0.0
@apiName  MerchantBankDisplayActionUpdate
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Body:)     {String}     product_line_code     <code>Mã</code> dòng sản phẩm cần lấy cấu hình hiển thị 
@apiParam   (Body:)     {Array}     fields     Danh sách field cập nhập

@apiParam   (Body:)     {String}  [fields.field_key]     Field key
@apiParam   (Body:)     {String}  [fields.status_display_area_list_product]     Trạng thái hiển thị ở khu vực Danh sách sản phẩm
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiParam   (Body:)     {String}  [fields.status_display_area_information_product]     Trạng thái hiển thị ở khu vực Thông tin sản phẩm
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiParam   (Body:)     {String}  [fields.status_display_area_product_filter]          Trạng thái hiển thị ở khu vực Bộ lọc sản phẩm
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiParam   (Body:)     {String}  [fields.status_display_area_product_holding_filter]  Trạng thái hiển thị ở khu vực Bộ lọc Product Holding
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiParam   (Body:)     {String}  [fields.status_display_area_product_holding]  Trạng thái hiển thị ở khu vực Product Holding
                                                                                    <ul>
                                                                                        <li><code>no_display</code>: Không hiển thị</li>
                                                                                        <li><code>display</code>: Hiển thị</li>
                                                                                        <li><code>hidden</code>: Ẩn</li>
                                                                                    </ul>
@apiParam   (Body:)     {Number}    [fields.order_area_list_product]  Thứ tự sắp xếp ở khu vực Danh sách sản phẩm. <code>Trong trường hợp bị ẩn thì order = -99</code>                            
@apiParam   (Body:)     {Number}    [fields.order_area_information_product]  Thứ tự sắp xếp ở khu vực Thông tin sản phẩm. <code>Trong trường hợp bị ẩn thì order = -99</code>                            
@apiParam   (Body:)     {Number}    [fields.order_area_product_holding_filter]  Thứ tự sắp xếp ở khu vực Bộ lọc Product Holding. <code>Trong trường hợp bị ẩn thì order = -99</code>                            
@apiParam   (Body:)     {Number}    [fields.order_area_product_filter]  Thứ tự sắp xếp ở khu vực Bộ lọc. <code>Trong trường hợp bị ẩn thì order = -99</code>                            
@apiParam   (Body:)     {Number}    [fields.order_area_product_holding]  Thứ tự sắp xếp ở khu vực Product Holding. <code>Trong trường hợp bị ẩn thì order = -99</code>
@apiParam   (Body:)     {Bool}      [fields.required_area_list_product]  Bắt buộc hiển thị ở khu vực Danh sách sản phẩm 
@apiParam   (Body:)     {Bool}      [fields.required_area_information_product]  Bắt buộc hiển thị ở khu vực Thông tin sản phẩm 
@apiParam   (Body:)     {Bool}      [fields.required_area_product_holding_filter]  Bắt buộc hiển thị ở khu vực Bộ lọc Product Holding
@apiParam   (Body:)     {Bool}      [fields.required_area_product_filter]  Bắt buộc hiển thị ở bộ lọc sản phẩm
@apiParam   (Body:)     {Bool}    [fields.required_area_product_holding]  Bắt buộc hiển thị ở Product Holding
@apiParam   (Body:)     {Bool}    [fields.disable_remove_area_list_product]     Disable remove field ở khu vực Danh sách sản phẩm?
@apiParam   (Body:)     {Bool}    [fields.disable_remove_area_information_product]     Disable remove field ở khu vực Thông tin sản phẩm?
@apiParam   (Body:)     {Bool}    [fields.disable_remove_area_product_holding_filter]     Disable remove field ở khu vực Bộ lọc Product Holding?
@apiParam   (Body:)     {Bool}    [fields.disable_remove_area_product_filter]     Disable remove field ở khu vực Bộ lọc Product?
@apiParam   (Body:)     {Bool}    [fields.disable_remove_area_product_holding]     Disable remove field ở khu vực Product Holding?
@apiParam   (Body:)     {Bool}    [fields.disable_un_select_require_area_list_product]  Disable bỏ chon bắt buộc hiển thị ở khu vực Danh sách sản phẩm 
@apiParam   (Body:)     {Bool}    [fields.disable_un_select_require_area_information_product]  Disable bỏ chon bắt buộc hiển thị ở khu vực Thông tin sản phẩm 
@apiParam   (Body:)     {Bool}    [fields.disable_un_select_require_area_product_holding_filter]  Disable bỏ chon bắt buộc hiển thị ở khu vực  Bộ lọc Product Holding
@apiParam   (Body:)     {Bool}    [fields.disable_un_select_require_area_product_filter]  Disable bỏ chon bắt buộc hiển thị ở khu vực  bộ lọc sản phẩm
@apiParam   (Body:)     {Bool}    [fields.disable_un_select_require_area_product_holding]  Disable bỏ chon bắt buộc hiển thị ở khu vực  Product Holding

@apiParamExample    {json}      Body:
{
    "product_line_code": "KHACHHANG_eb1680bc"
    "fields": [
        {
            "field_key": "_dyn_name_1558406233387",
            "status_display_area_information_product": "no_display",
            "order_area_information_product": 10,
            "required_area_product_holding": false
        }
    
    ]
}                            

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công."
}
"""

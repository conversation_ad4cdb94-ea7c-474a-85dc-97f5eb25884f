#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 15/02/2023
"""
"""
@api {POST} {domain}/product/bank/api/v1.0/products/import/excel        Thêm product bank bằng file excel
@apiGroup ImportProductBank
@apiVersion 1.0.0
@apiName ImportProductBankExcel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(Form:)			{File}	    file		            File excel cần import
@apiParam	(Form:)			{String}	[time_zone]		        Múi giờ điều chỉnh (VD: quy chuẩn VN là GMT+7 thì cần gửi value "+7" <code>Default giờ VN</code>: "+7")
@apiParam	(Form:)			{Array}	    rules		            Mapping cột excel và field Product
                                                                
@apiParam	(Form:)			{String}	rules.field_key		    Key định danh của field Ticket
@apiParam	(Form:)			{Number}	rules.column_index	    Vị trí cột trong file excel (Bắt đầu từ 1)
                                                                <code>Chú ý: Cột index là 0 sẽ không được mapping với field vì cột đó mặc định sẽ là vị trí gắn product.</code>

@apiParamExample {json} Form example
file: (binary)
time_zone: +7
rules: [
    {
        "field_key": "name",
        "column_index": 1
    },
    {
        "field_key": "_dyn_mo_ta_1576722642875",
        "column_index": 2
    },
    {
        "field_key": "phone_number",
        "column_index": 3
    }
]

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

"""
@api {post} {domain}/product/bank/api/v1.0/categories/import/excel         Import cây danh mục bank bởi file excel
@apiGroup ImportProductBank
@apiVersion 1.0.0
@apiName  CategoriesImportExcel
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(FORM:)			{Binary}	    file		                File excel cần import
@apiParam	(FORM:)			{String}	    [time_zone]		            Múi giờ điều chỉnh 
                (VD: quy chuẩn VN là GMT+7 thì cần gửi value "+7" <code>default giờ VN</code>: "+7")

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

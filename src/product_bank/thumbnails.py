#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 12/06/2023
"""
"""
@apiDefine Response

@apiSuccess {Number} code       Response status
@apiSuccess {String} message    Response message
"""
# ******************************** Thêm 1 file *******************************************
# version: 1.0.0                                                                      *
# **************************************************************************************
"""
@api {post} {domain}/product/bank/api/v1.0/multi/thumbnails             Thêm file thumbnail
@apiDescription Thêm file
@apiGroup ProductBankAvatar
@apiVersion 1.0.0
@apiName  AddMultiFileThumbnails
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
                                                                        
@apiParam	(FORM:)			{Binary}	    avatar		                File được chọn là ảnh đại diện
@apiParam	(FORM:)			{Binary}	    file		                File upload


@apiUse Response
@apiSuccess  {Array}   data_success          Danh sách thông tin của file upload thành công
@apiSuccess  {Array}   data_fail             Danh sách tên file upload thất bại


@apiSuccess (data_success)      {String}         id                       <code>ID</code> file upload lên hệ thống
@apiSuccess (data_success)      {Boolean}        is_select_avatar         Ảnh được chọn làm ảnh đại diện
                                                                  <ul>
                                                                    <li><code>true</code>: Được chọn</li>
                                                                    <li><code>false</code>: Không được chọn</li>
                                                                  </ul>  
@apiSuccess (data_success)      {String}         title                    Tên file upload
@apiSuccess (data_success)      {String}         format_file              Định dạng của file
@apiSuccess (data_success)      {String}         url                      URL file
@apiSuccess (data_success)      {String}         capacity                 Dung lượng file
@apiSuccess (data_success)      {String}         local_path               Đường dẫn vậy lý của file
@apiSuccess (data_success)      {String}         merchant_id              Định danh tenant upload file

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data_success": 
        [
            {
                "_id": "620dc147dfd20bf34ac6954f",
                "action_time": 1645043415.593171,
                "capacity": 1082923,
                "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                "filename": "1645068615_0E0A7559.jpg",
                "format_file": "image/jpeg",
                "id": "620dc147dfd20bf34ac6954f",
                "local_path": "/Users/<USER>/workspace/mobio/Module-Product/product/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg",
                "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
                "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg"
            },
        ],
    "data_fail": [],
    "lang": "vi",
    "message": "request thành công."
}
"""

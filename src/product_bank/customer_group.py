#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 07/06/2022
"""
# ******************************** Merchant List Field **********************************
# version: 1.0.0                                                                      *
# **************************************************************************************
"""
@api {get} {domain}/product/bank/api/v1.0/customer-group             Lấy danh sách nhóm khách hàng
@apiDescription Get List Customer Group
@apiGroup BankCustomerGroup
@apiVersion 1.0.0
@apiName  MerchantBankListCustomerGroup
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
        "group_name" : "Khách hàng Ưu tiên",
        "group_customer_code" : "KHACHHANG_eb1680bc",
        "translate_key": "",
        "is_default": true,
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""

"""
@api {put} {domain}/product/bank/api/v1.0/customer-group/<customer_group_code>             Cập nhật hiển thị nhóm khách hàng 
@apiDescription     Cập nhật hiển thị nhóm khách hàng
@apiGroup BankCustomerGroup
@apiVersion 1.0.0
@apiName  MerchantBankSetViewDefaultCustomerGroup
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:) {Boolean}  is_default           Mã nhóm khách hàng
@apiParamExample {json} Body example
{
    "is_default": true
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
        "group_name" : "Khách hàng Ưu tiên",
        "group_customer_code" : "KHACHHANG_eb1680bc",
        "translate_key": "",
        "is_default": true,
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""
# ---- Lưu trạng thái đang chọn của nhóm khách hàng -----
"""
@api {PUT} {domain}/product/bank/api/v1.0/customer-group/<customer_group_code>/last-view        Lưu trạng thái xem cuối cùng nhóm khách hàng
@apiDescription         Lưu trạng thái xem cuối cùng nhóm khách hàng
@apiGroup BankCustomerGroup
@apiVersion 1.0.0
@apiName UpdateLastViewBankCustomerGroup

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

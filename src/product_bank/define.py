#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 01/06/2022
"""
"""
@apiDefine BodyFieldDynamic
@apiParam   (Body:)    {String}         field_name        Tên field mới
@apiParam   (Body:)     {Number=1:Int,2:String,3:DateTime}    field_property            Kiểu dữ liệu của field.
@apiParam   (Body:)     {String}    display_type            Kiểu hiển thị dữ liệu.
                                                            <ul>
                                                                <li><code>single_line: Single Line</code></li>
                                                                <li><code>multi_line: Multi Line</code></li>
                                                                <li><code>dropdown_single_line: Dropdown Single Line</code></li>
                                                                <li><code>dropdown_multi_line: Dropdown Multi Line</code></li>
                                                                <li><code>radio: Radio</code></li>
                                                                <li><code>checkbox: Checkbox</code></li>
                                                                <li><code>date_picker: Date Picker</code></li>
                                                            </ul>
@apiParam   (Body:)     {Array}    [data_selected]         Dữ liệu hiển thị.
@apiParam   (Body:)     {String='dd/mm','dd/mm/yyyy','dd/mm/yyyy hh:mm','dd-mm-yyyy','dd-mm-yyyy hh:mm','mm/dd/yyyy',
                        'mm/dd/yyyy hh:mm'}   [format] Data format.
@apiParam   (Body:)     {String}    description            Mô tả ngắn.
@apiParam   (Body:)     {String}    group                  Group của field
@apiParam   (Body:)     {String="object", "text"}          [type_save_data="text"]         Kiểu lưu dữ liệu

@apiParam   (Body:)     {Bool}    display_in_form         Hiển thị field này ở form chọn dữ liệu?
@apiParam   (Body:)     {Bool}    display_detail          Hiển thị field này ở chi tiết Product?
@apiParam   (Body:)     {Bool}    display_in_form_add     Hiển thị field này ở popup tạo mới Product?
@apiParam   (Body:)     {Bool}    display_in_form_update  Hiển thị field này ở popup sửa Product?
@apiParam   (Body:)     {Bool}    display_in_list_field   Hiển thị field này ở popup chọn thông tin hiển thị trong danh sách Product?
@apiParam   (Body:)     {Bool}    display_in_filter       Hiển thị field này ở bộ lọc Product?
@apiParam   (Body:)     {Bool}    display_in_import_file  Hiện thị field này khi import file?
@apiParam   (Body:)     {Bool}    disable_remove_form_add  Không cho phép xóa ở form thêm
@apiParam   (Body:)     {Bool}    disable_required_form_add  Không cho phép required ở form thêm
@apiParam   (Body:)     {Bool}    disable_remove_form_update  Không cho phép xóa ở form sửa
@apiParam   (Body:)     {Bool}    disable_required_form_update  Không cho phép required ở form sửa
@apiParam   (Body:)     {Bool}    [allow_use_color]  Cho phép sử dụng màu chỉ áp dụng cho
@apiParam   (Body:)     {Bool}    [status_display_color]  Trạng thái hiển thị màu
@apiParam   (Body:)     {Bool}    [allow_change_position]  Cho phép thay đổi vị trí
@apiParam   (Body:)     {Bool}    [allow_edit_name]  Cho phép thay đổi tên
@apiParam   (Body:)     {Bool}    [allow_edit_description]  Cho phép thay đổi mô tả 
@apiParam   (Body:)     {Bool}    [allow_edit_position_display]  Cho phép thay đổi vị trí hiển thị
@apiParam   (Body:)     {Bool}    [allow_add_data_select]  Cho phép thêm giá trị
@apiParam   (Body:)     {Bool}    [allow_change_position]  Cho phép thay đổi vị trí
@apiParam   (Body:)     {Bool}    [permission_remove_field]  Quyền xoá field
@apiParam   (Body:)     {Array}    [data_select_position_pin]  Vị trí ghim của các data select



@apiParam (data_selected)   {string}       id                           Định danh của giá trị chọn
@apiParam (data_selected)   {String}    value                           Giá trị 
@apiParam (data_selected)   {String}    color                           Màu 
@apiParam (data_selected)   {int}       enable                          Trường này được bật hay không ?
@apiParam (data_selected)   {int}       order                           Vị trí
@apiParam (data_selected)   {Boolean}   allow_change_order=true         Quyền được thay đổi vị trí
                                                                        <ul>
                                                                            <li><code>true</code>: được quyền thay đổi vị trí</li>
                                                                            <li><code>false</code>: không được quyền thay đổi vị trí</li>
                                                                        </ul>
@apiParam (data_selected)   {Boolean}   enable_data_color=true          Có được bật tắt màu hay không ?
                                                                        <ul>
                                                                            <li><code>true</code>: bật hiển thị màu</li>
                                                                            <li><code>false</code>: tắt hiển thị màu</li>
                                                                        </ul>
@apiParam (data_selected)   {Boolean}   allow_edit_value=true           Có được thay đổi giá trị không?
                                                                        <ul>
                                                                            <li><code>true</code>: có</li>
                                                                            <li><code>false</code>: không</li>
                                                                        </ul>
@apiParam (data_selected)   {Boolean}   allow_remove_value=true         Có được xoá giá trị không?
                                                                        <ul>
                                                                            <li><code>true</code>: có</li>
                                                                            <li><code>false</code>: không</li>
                                                                        </ul>
@apiParam (data_selected)   {Boolean}   allow_edit_status_display_value=true         Có được thay đổi quyền hiển thị của giá trị không?
                                                                                    <ul>
                                                                                        <li><code>true</code>: có</li>
                                                                                        <li><code>false</code>: không</li>
                                                                                    </ul>
@apiParam (data_selected)   {Int}   status                              Trạng thái của giá trị
                                                                        <ul>
                                                                            <li><code>1</code>: hoạt động</li>
                                                                            <li><code>-1</code>: xoá</li>
                                                                        </ul>
@apiParam (data_selected)   {String}   [translate_key]                     Translate_key của value
"""

"""
@apiDefine ParamExampleBodyDynamic
@apiParamExample [json] Body example text single line:
{
  "field_name": "field 1",
  "group": "dynamic",
  "field_property": 1,
  "type_save_data": "text",
  "display_type": "single_line",
  "description": "mo ta ngan cua field",
  "display_in_form": true,
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_list_field": true,
  "display_in_filter": false,
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true
}

@apiParamExample [json] Body example text multi line:
{
  "field_name": "field 1",
  "group": "dynamic",
  "field_property": 1,
  "type_save_data": "text",
  "display_type": "multi_line",
  "display_in_form": true,
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_list_field": true,
  "display_in_filter": false,
  "description": "mo ta ngan cua field",
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true
}

@apiParamExample [json] Body example dropdown single line:
{
  "field_name": "field 1",
  "group": "dynamic",
  "field_property": 1,
  "type_save_data": "text",
  "display_type": "dropdown_single_line",
  "data_selected": [
    ],
  "display_in_form": true,
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_list_field": true,
  "display_in_filter": false
  "description": "mo ta ngan cua field",
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true,
  "allow_use_color": true,
  "data_select_position_pin": [],
  "type_save_data": "object",
  "allow_use_color": True,  # Cho phép sử dùng màu
  "status_display_color": True,
  "allow_change_position": True,
  "allow_add_data_select": True,
  "permission_remove_field": True
}

@apiParamExample [json] Body example dropdown multi line:
{
  "field_name": "field 1",
  "group": "dynamic",
  "field_property": 1,
  "type_save_data": "text",
  "display_type": "dropdown_multi_line",
  "data_selected": [
    ],
  "display_in_form": true,
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_list_field": true,
  "display_in_filter": false,
  "description": "mo ta ngan cua field",
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true,
  "data_select_position_pin": []
  "allow_use_color": True,  # Cho phép sử dùng màu
  "status_display_color": True,
  "allow_change_position": True,
  "allow_add_data_select": True,
  "permission_remove_field": True
}

@apiParamExample [json] Body example radio:
{
  "field_name": "field 1",
  "group": "dynamic",
  "field_property": 1,
  "type_save_data": "text",
  "display_type": "radio",
  "data_selected": [
    ],
  "display_in_form": true,
  "description": "mo ta ngan cua field",
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true,
  "data_select_position_pin": [],
  "allow_use_color": True,  # Cho phép sử dùng màu
  "status_display_color": True,
  "allow_change_position": True,
  "allow_add_data_select": True,
  "permission_remove_field": True
}

@apiParamExample [json] Body example checkbox:
{
  "field_name": "field 1",
  "field_property": 1,
  "type_save_data": "text",
  "display_type": "checkbox",
  "group": "dynamic",
  "data_selected": [
    ],
  "display_in_form": true,
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_list_field": true,
  "display_in_filter": false,
  "description": "mo ta ngan cua field",
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true,
  "data_select_position_pin": [],
  "allow_use_color": True,  # Cho phép sử dùng màu
  "status_display_color": True,
  "allow_change_position": True,
  "allow_add_data_select": True,
  "permission_remove_field": True
}

@apiParamExample [json] Body example date_picker:
{
  "field_name": "field 1",
  "group": "dynamic",
  "type_save_data": "text",
  "field_property": 1,
  "display_type": "date_picker",
  "format": "dd/mm/yyyy",
  "display_in_form": true,
  "display_detail": true,
  "display_in_form_add": true,
  "display_in_form_update": true,
  "display_in_list_field": true,
  "display_in_filter": false,
  "description": "mo ta ngan cua field",
  "display_in_import_file": false,
  "disable_remove_form_add": true,
  "disable_required_form_add": true,
  "disable_remove_form_update": true,
  "disable_required_form_update": true
}
"""

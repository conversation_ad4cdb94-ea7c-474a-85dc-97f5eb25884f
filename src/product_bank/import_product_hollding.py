#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 01/12/2023
"""

"""
@api {get} {domain}/product/bank/api/v1.0/import-product-holding/product-line             L<PERSON><PERSON> danh sách dòng sản phẩm phục vụ việc import product holding
@apiDescription L<PERSON>y danh sách dòng sản phẩm phục vụ việc import product holding
@apiGroup ImportProductHolding
@apiVersion 1.0.0
@apiName  ListProductLineImportProductHolding
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)   {String}      object                         Đ<PERSON><PERSON> tượng muốn lấy danh sách dòng sản phẩm
                                                                    <ul>
                                                                        <li>profile: <PERSON><PERSON><PERSON><PERSON> hàng cá nhân</li>
                                                                        <li>company: <PERSON><PERSON><PERSON><PERSON> hàng do<PERSON>h nghiệp</li>
                                                                    </ul>

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
        "_id": "62a00f93fd729f519d5c87ce",
        "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "code": "kanh_test_123_code",
        "created_time": **********.305598,
        "customer_group_code": "KHACHHANG_77b2081f",
        "id": "62a00f93fd729f519d5c87ce",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "name": "kanh test 123",
        "updated_time": **********.305604
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""

"""
@api {get} {domain}/product/bank/api/v1.0/import-product-holding/template-import-files             Lấy danh sách template import file product holding
@apiDescription Lấy danh sách template import file product holding
@apiGroup ImportProductHolding
@apiVersion 1.0.0
@apiName  ListTemplateImportProductHolding
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)   {String}      product_line_id                <code>ID</code> dòng sản phẩm muốn lấy template

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    "https://t1.mobio.vn/static/merchant_id/template.excel"
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""
"""
@api {get} {domain}/product/bank/api/v1.0/import-product-holding/template-import-files             Lấy danh sách template import file product holding
@apiDescription Lấy danh sách template import file product holding
@apiGroup ImportProductHolding
@apiVersion 1.0.0
@apiName  ListTemplateImportProductHolding
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)   {String}      product_line_id                <code>ID</code> dòng sản phẩm muốn lấy template

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    "https://t1.mobio.vn/static/merchant_id/template.excel"
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""

"""
@api {get} {domain}/product/bank/api/v1.0/import-product-holding/fields           Lấy danh sách field hiển thị trong phần import product holding
@apiDescription Lấy danh sách field hiển thị trong phần import product holding
@apiGroup ImportProductHolding
@apiVersion 1.0.0
@apiName  ListFieldImportProductHolding
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)   {String}      product_line_id                <code>ID</code> dòng sản phẩm muốn lấy template


@apiSuccess {Array}             data                          Thông tin các field đc cập nhật
@apiSuccess {Array}             data.fields                   Danh sách field
@apiSuccess {String}            data.group                    Thông tin group.
@apiSuccess {Bool}              data.fields.required_mapping_import_product_holding  <code>Định nghĩa có bắt buộc phải mapping không?</code>
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
        "fields": [
            {
                "display_type": "single_line",
                "field_key": "product_line",
                "field_name": "Dòng sản phẩm",
                "`required_mapping_import_product_holding`": true
            }
        ]
        "group": "information"
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""

"""
@api {POST} {domain}/product/bank/api/v1.0/import-product-holding/upload        Upload file import product holding
@apiGroup ImportProductHolding
@apiVersion 1.0.0
@apiName UploadFileImportProductHolding

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)   {String}      object                         Đối tượng muốn lấy danh sách dòng sản phẩm
                                                                    <ul>
                                                                        <li>profile: Khách hàng cá nhân</li>
                                                                        <li>company: Khách hàng doanh nghiệp</li>
                                                                    </ul>

@apiParam	(Form:)			{String}	  product_line_id		  <code>ID</code> dòng sản phẩm
@apiParam	(Form:)			{File}	    file		            File excel cần import
@apiParam	(Form:)			{Array}	    rules		            Mapping cột excel và field Product Bank
@apiParam	(Form:)			{String}	rules.field_key		    Key định danh của field Product Bank
@apiParam	(Form:)			{Number}	rules.column_index	    Vị trí cột trong file excel (Bắt đầu từ 0)
@apiParam	(Form:)			{String=product}	rules.module_name	    Tên module

@apiParamExample {json} Form example
product_line_id: "***************"
file: (binary)
time_zone: +7
rules: [
    {
        "column_index": 0,
        "field_key": "product_line",
        "module_name": "product"
    }
]

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""

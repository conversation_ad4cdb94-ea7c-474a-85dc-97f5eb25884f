#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 01/06/2022
"""
# ********************************* Merchant Add Field ********************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {post} {domain}/product/bank/api/v1.0/merchant/fields/add    Merchant Bank Add New Field.
@apiDescription Merchant Add New Field
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  MerchantBankAddNewField
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse BodyFieldDynamic                                                                        

@apiUse ParamExampleBodyDynamic

@apiSuccess   (Response:)     {String}    field_name         Tên field.
@apiSuccess   (Response:)     {String}    field_key          Key định danh của field.
@apiSuccess   (Response:)     {Number}    field_property            Kiểu dữ liệu của field.
@apiSuccess   (Response:)     {Bool}    display_in_form         Hiển thị field này ở form chọn dữ liệu?
@apiSuccess   (Response:)     {Number}    order         thứ tự sắp xếp.
@apiSuccess   (Response:)     {String}    group         Nhóm của field.
@apiSuccess   (Response:)     {Bool}    display_in_form_input         Hiển thị field này ở form add/update Product?
@apiSuccess   (Response:)     {Bool}    disable_remove_form_input     Disable remove field ở form add Product?
@apiSuccess   (Response:)     {Bool}    disable_remove_form_update     Disable remove field ở form update Product?
@apiSuccess   (Response:)     {Bool}    disable_remove_list     Disable remove field ở danh sách Product?
@apiSuccess   (Response:)     {Bool}    display_in_import_file     Hiển thị field trong import file?
@apiSuccess   (Response:)     {Bool}    disable_remove_form_add  Không cho phép xóa ở form thêm
@apiSuccess   (Response:)     {Bool}    disable_required_form_add     Không cho phép required ở form thêm
@apiSuccess   (Response:)     {Bool}    disable_remove_form_update     Không cho phép xóa ở form sửa
@apiSuccess   (Response:)     {Bool}    disable_required_form_update     Không cho phép required ở form sửa
@apiSuccess   (Response:)     {Bool}    disable_status_display_color     Không cho phép trạng thái hiển thị màu
@apiSuccess   (Response:)     {Bool}    display_detail     Hiển thị ở chi tiết Product
@apiSuccess   (Response:)     {Bool}    display_in_filter     Hiển thị ở bộ lọc
@apiSuccess   (Response:)     {Bool}    display_in_form_add     Hiển thị ở bộ lọc form add
@apiSuccess   (Response:)     {Bool}    display_in_form_add_selected     Hiển thị ở bộ lọc form add
@apiSuccess   (Response:)     {Bool}    display_in_form_update     Hiển thị ở bộ lọc form update
@apiSuccess   (Response:)     {Bool}    display_in_form_update_selected     Hiển thị ở bộ lọc form update
@apiSuccess   (Response:)     {Bool}    display_in_import_file     Hiển thị ở phần import file
@apiSuccess   (Response:)     {Bool}    display_in_list_field     Hiển thị ở danh sách filed
@apiSuccess   (Response:)     {Bool}    display_in_list_field_selected     Hiển thị ở danh sách filed
@apiSuccess   (Response:)     {Bool}    display_type     Kiểu hiển thị
@apiSuccess   (Response:)     {Bool}    enable_data_color     Bật sử dụng màu
@apiSuccess   (Response:)     {String}    format      Định dạng
@apiSuccess   (Response:)     {Array}    history      Lịch sử cập nhật trường dynamic
@apiSuccess   (Response:)     {Bool}    is_base     Field này có phải field của hệ thống hay không?
@apiSuccess   (Response:)     {Bool}    is_change_group     Có được thay đổi group hay không ?
@apiSuccess   (Response:)     {String}    type_save_data     Kiểu lưu dữ liệu

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "allow_add_data_select": true,
    "allow_change_position": false,
    "allow_edit_description": true,
    "allow_edit_name": true,
    "allow_edit_position_display": true,
    "allow_use_color": false,
    "created_time": **********.806242,
    "data_select_position_pin": [],
    "data_selected": null,
    "description": "",
    "disable_remove_form_add": false,
    "disable_remove_form_input": false,
    "disable_remove_form_update": false,
    "disable_remove_list": false,
    "disable_required_form_add": false,
    "disable_required_form_input": false,
    "disable_required_form_update": false,
    "disable_status_display_color": false,
    "display_detail": true,
    "display_in_filter": true,
    "display_in_form_add": true,
    "display_in_form_add_selected": true,
    "display_in_form_update": true,
    "display_in_form_update_selected": true,
    "display_in_import_file": true,
    "display_in_list_field": true,
    "display_in_list_field_selected": true,
    "display_type": "single_line",
    "enable_data_color": false,
    "field_key": "_dyn_test_**********806",
    "field_name": "Test",
    "field_property": 2,
    "format": null,
    "group": "dynamic",
    "history": [
        {
            "created_time": "Wed, 20 Apr 2022 10:13:48 GMT",
            "fullname": "Nguy\u1ec5n V\u0103n An",
            "staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "username": "admin@pingcomshop"
        }
    ],
    "is_base": false,
    "is_change_group": true,
    "is_encrypt": null,
    "last_update_by": {
        "created_time": "Wed, 20 Apr 2022 10:13:48 GMT",
        "fullname": "Nguy\u1ec5n V\u0103n An",
        "staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "username": "admin@pingcomshop"
    },
    "order": 138,
    "order_form_add": 203,
    "order_form_update": 52,
    "order_list_field": 281,
    "permission_remove_field": true,
    "required_form_add": false,
    "required_form_update": false,
    "status": 1,
    "status_display_color": true,
    "support_sort": false,
    "translate_key": null,
    "type_save_data": "text",
    "updated_time": **********.806214
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

# ******************************** Merchant List Field **********************************
# version: 1.0.0                                                                      *
# **************************************************************************************
"""
@api {get} {domain}/product/bank/api/v1.0/merchant/field/list             Merchant Bank List Field.
@apiDescription Merchant Bank List Field
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  MerchantBankListField
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse order_sort
@apiParam   (Query:)    {String}  [search]     Tìm kiếm fields theo tên.
@apiParam   (Query:)    {String}  [area=setup_abac_product_holding]     Khu vực lấy field
@apiParam   (Query:)    {String}  [group]      Lọc field theo nhóm, các nhóm cách nhau bởi dấu <code>;</code>, không truyền thì mặc định lấy hết các nhóm: vd: group = information;dynamic
@apiParam   (Query:)    {String}  [sort]       Yêu cầu sắp xếp dữ liệu theo tiêu chí. Sử dụng: &sort=field_name(sắp xếp dữ liệu theo field_name).
@apiParam   (Query:)    {String}  [order]      Sắp xếp dữ liệu theo chiều tăng dần(asc, A->Z) hoặc theo chiều giảm dần(desc, Z-A)

Giá trị mặc định: asc

Giá trị chấp nhận: asc, desc

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
        {
          "fields": [],
          "group": "information_profiles",
          "group_index": 1,
          "group_key": "information_profiles",
          "group_name": "Thông tin sản phẩm theo profiles",
          "is_base": true,
          "translate_key": "i18n_information_profiles"
        },
        {
          "fields": [
            {
            "allow_add_data_select": true,
            "allow_change_position": true,
            "allow_use_color": true,
            "choose_display_form_input": true,
            "created_time": 1647464388.337557,
            "data_select_position_pin": [],
            "data_selected": [
                {
                    "allow_change_order": false,
                    "allow_edit_status_display_value": false,
                    "allow_edit_value": false,
                    "allow_remove_value": false,
                    "color": "#C1C1C1",
                    "enable": 1,
                    "enable_data_color": false,
                    "id": 1,
                    "order": 0,
                    "status": 1,
                    "translate_key": "i18n_type_ticket_support_information",
                    "value": "Hỗ trợ thông tin"
                }
            ],
            "description": "Kiểu Ticket",
            "disable_remove_form_add": false,
            "disable_remove_form_input": true,
            "disable_remove_form_update": false,
            "disable_required_form_add": false,
            "disable_required_form_update": false,
            "display_detail": true,
            "display_in_filter": true,
            "display_in_form_add": true,
            "display_in_form_update": true,
            "display_in_import_file": true,
            "display_in_list_field": true,
            "display_type": "dropdown_single_line",
            "field_key": "type_ticket_id",
            "field_name": "type_ticket_id",
            "field_property": 2,
            "group": "information",
            "history": [],
            "is_base": false,
            "permission_remove_field": true,
            "required": false,
            "status": 1,
            "status_display_color": true,
            "translate_key": "i18n_type_ticket",
            "type_save_data": "object",
            "updated_time": 1647464388.337565
}
          ],
          "group": "information",
          "group_index": 1,
          "group_key": "information",
          "group_name": "Thông tin chung",
          "is_base": true,
          "translate_key": "i18n_information"
        },
        {
          "fields": [],
          "group": "dynamic",
          "group_index": 1,
          "group_key": "dynamic",
          "group_name": "Trường tùy biến",
          "is_base": true,
          "translate_key": "i18n_dynamic"
        }
    ],
  "lang": "vi",
  "message": "request thành công."
}
"""
# **************************************************************************************
"""
@api {PUT} {domain}/product/bank/api/v1.0/merchant/field/delete/list-field Merchant Bank Delete List Field.
@apiDescription Xóa danh sách Field
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  MerchantBankDeleteListField
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:) {Array}  fields-key           Danh sách field-key cần xóa
@apiParamExample {json} Body example
{
    "fields-key": [
        "_dyn_single_line_text_so_1578106918635", 
        "name"
    ]
}


@apiSuccess {Number} code       Response status    
@apiSuccess {String} message    Response message
@apiSuccess {Object} data       Thông tin khi xóa field
@apiSuccess {Object} data.delete_field_success    Danh sách field xóa thành công
@apiSuccess {Object} data.delete_field_fail       Danh sách field xóa không thành công

@apiSuccessExample Response success:
{
    "data": {
        "delete_field_success": [
            "_dyn_single_line_text_so_1578106918635"
        ],
        "delete_field_fail": [
            "name"
        ]
    },
    "code": 200,
    "message": "request successfully",
}
"""

# ******************************** Merchant Delete Field **********************************
# version: 1.0.0                                                                      *
# **************************************************************************************
"""
@api {delete} {domain}/product/bank/api/v1.0/merchant/field/<field_key> Merchant Bank Delete Field.
@apiDescription Merchant Bank Delete Field
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  MerchantBankDeleteField
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "message": "request thành công."
}
"""
# ******************************** Merchant Update Field *******************************
# version: 1.0.0                                                                      *
# **************************************************************************************
"""
@api {put} {domain}/product/bank/api/v1.0/merchant/field/update/<field_key>          Merchant Bank Update Field.
@apiDescription Merchant Bank Update Field
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  MerchantBankUpdateField
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse BodyFieldDynamic                                                                    

@apiUse ParamExampleBodyDynamic

@apiSuccess   (Response:)     {String}    field_name         Tên field.
@apiSuccess   (Response:)     {String}    field_key          Key định danh của field.
@apiSuccess   (Response:)     {Number}    field_property            Kiểu dữ liệu của field.
@apiSuccess   (Response:)     {Bool}    display_in_form         Hiển thị field này ở form chọn dữ liệu?
@apiSuccess   (Response:)     {Number}    order         thứ tự sắp xếp.
@apiSuccess   (Response:)     {String}    group         Nhóm của field.
@apiSuccess   (Response:)     {Bool}    display_in_form_input         Hiển thị field này ở form add/update ProductBank?
@apiSuccess   (Response:)     {Bool}    disable_remove_form_input     Disable remove field ở form add ProductBank?
@apiSuccess   (Response:)     {Bool}    disable_remove_form_update     Disable remove field ở form update ProductBank?
@apiSuccess   (Response:)     {Bool}    disable_remove_list     Disable remove field ở danh sách ProductBank?
@apiSuccess   (Response:)     {Bool}    display_in_import_file     Hiển thị field trong import file?
@apiSuccess   (Response:)     {Bool}    disable_remove_form_add  Không cho phép xóa ở form thêm
@apiSuccess   (Response:)     {Bool}    disable_required_form_add     Không cho phép required ở form thêm
@apiSuccess   (Response:)     {Bool}    disable_remove_form_update     Không cho phép xóa ở form sửa
@apiSuccess   (Response:)     {Bool}    disable_required_form_update     Không cho phép required ở form sửa
@apiSuccess   (Response:)     {Bool}    disable_status_display_color     Không cho phép trạng thái hiển thị màu
@apiSuccess   (Response:)     {Bool}    display_detail     Hiển thị ở chi tiết ProductBank
@apiSuccess   (Response:)     {Bool}    display_in_filter     Hiển thị ở bộ lọc
@apiSuccess   (Response:)     {Bool}    display_in_form_add     Hiển thị ở bộ lọc form add
@apiSuccess   (Response:)     {Bool}    display_in_form_add_selected     Hiển thị ở bộ lọc form add
@apiSuccess   (Response:)     {Bool}    display_in_form_update     Hiển thị ở bộ lọc form update
@apiSuccess   (Response:)     {Bool}    display_in_form_update_selected     Hiển thị ở bộ lọc form update
@apiSuccess   (Response:)     {Bool}    display_in_import_file     Hiển thị ở phần import file
@apiSuccess   (Response:)     {Bool}    display_in_list_field     Hiển thị ở danh sách filed
@apiSuccess   (Response:)     {Bool}    display_in_list_field_selected     Hiển thị ở danh sách filed
@apiSuccess   (Response:)     {Bool}    display_type     Kiểu hiển thị
@apiSuccess   (Response:)     {Bool}    enable_data_color     Bật sử dụng màu
@apiSuccess   (Response:)     {String}    format      Định dạng
@apiSuccess   (Response:)     {Array}    history      Lịch sử cập nhật trường dynamic
@apiSuccess   (Response:)     {Bool}    is_base     Field này có phải field của hệ thống hay không?
@apiSuccess   (Response:)     {Bool}    is_change_group     Có được thay đổi group hay không ?
@apiSuccess   (Response:)     {String}    type_save_data     Kiểu lưu dữ liệu

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "field_name": "field_1",
    "type_save_data": "text",
    "field_key": "_dyn_name_1558406233387",
    "field_property": 1,
    "display_type": "date_picker",
    "format": "dd/mm/yyyy",
    "type_save_data": "text",
    "display_detail": true,
    "display_in_filter": true,
    "display_in_form_add": true,
    "display_in_form_update": true,
    "display_in_import_file": true,
    "display_in_list_field": true,
    "order": 15,
    "group": "dynamic",
    "display_in_form_input": false,
    "display_in_dashboard": false,
    "disable_remove_form_input": false,
    "is_base": false,
    "status": 1,
    "history": [
      {
        "created_time": "2019-03-27T17:52:46Z",
        "staff_id": "7df46d3b-98bd-4bf6-a8ac-1b7210297e54",
        "fullname": "MobioTest",
        "username": "admin@mobiotest"
      }
    ],
    "description": "mo ta ngan cua field",
    "created_time": "2019-03-27T17:52:46Z",
    "updated_time": "2019-03-27T17:52:46Z" 
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""
# ******************************** Merchant List Field Product Line ********************
# version: 1.0.0                                                                      *
# **************************************************************************************
"""
@api {get} {domain}/product/bank/api/v1.0/merchant/field/list/product-line             Merchant Bank List Field By Product Line
@apiDescription Merchant Bank List Field By Product Line
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  MerchantBankListFieldByProductLine
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse order_sort
@apiParam   (Query:)    {String}  [search]     Tìm kiếm fields theo tên.
@apiParam   (Query:)    {String}  [group]      Lọc field theo nhóm, các nhóm cách nhau bởi dấu <code>;</code>, không truyền thì mặc định lấy hết các nhóm: vd: group = information;dynamic
@apiParam   (Query:)    {String}  [sort]       Yêu cầu sắp xếp dữ liệu theo tiêu chí. Sử dụng: &sort=field_name(sắp xếp dữ liệu theo field_name).
@apiParam   (Query:)    {String=asc, desc}  [order=asc]      Sắp xếp dữ liệu theo chiều tăng dần(asc, A->Z) hoặc theo chiều giảm dần(desc, Z-A)
@apiParam   (Query:)    {String}  product_line_code      Mã của dòng sản phầm cần lấy danh sách Field
@apiParam   (Query:)    {String}  product_line_id           <code>ID</code> của dòng sản phẩm

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "group":"information",
      "fields":[]
    },
    {
      "group": "information_profiles",
      "fields":[]
    },
    {
      "group":"dynamic",
      "fields":[
      ]
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""
# ******************************** Merchant Add Dynamic Field In Product Line **********
# version: 1.0.0                                                                       *
# **************************************************************************************
"""
@api {post} {domain}/product/bank/api/v1.0/field-add-to-product-line             Merchant Bank Add Field To Product Line
@apiDescription Merchant Bank Add Field To Product Line
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  MerchantBankAddFieldToProductLine
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Body:)     {Array}     lst_product_line_code     Danh sách mã dòng sản phẩm
@apiParam   (Body:)     {Array}     lst_field_key             Danh sách field key cần thêm vào dòng sản phẩm

@apiParamExample    {json}      Body:
{
    "lst_product_line_code": [],
    "lst_field_key": []
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "message": "request thành công."
}
"""

"""
@api {get} {domain}/product/bank/api/v1.0/merchant/field/list/product-line/internal     Get list field by product line internal        
@apiDescription         Get list field by product line internal
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  MerchantBankGetListFieldByProductLineInternal
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Query:)     {String}     product_line_code     <code>Mã</code> dòng sản phẩm cần lấy cấu hình hiển thị 
@apiParam   (Query:)     {String}     [area_code]     Mã khu vực hiện thị
                                                      <ul>
                                                            <li><code>information_product</code>: Thông tin sản phẩm</li>
                                                            <li><code>product_filter</code>: Bộ lọc sản phẩm</li>
                                                            <li><code>product_holding</code>: Product Holding</li>
                                                            <li><code>product_holding_filter</code>: Bộ lọc Product Holding</li>
                                                      </ul>
                                                      Nếu không truyền lên thì lấy tất cả.  
@apiParam   (Query:)     {String}     fields     Danh sách field cần lấy ngăn cách nhau bởi dấu phây. Ví dụ: field_key,field_name 


@apiParamExample    {json}      Query example:
{
    ?product_line_code=KHACHHANG_eb1680bc&fields=field_key,field_name
}                            

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "data": [
    {
        "field_name": "field_1",
        "field_key": "_dyn_name_1558406233387",
    },
    {
        "field_name": "field_2",
        "field_key": "_dyn_name_1558406233389",
    }
  ],
  "message": "request thành công."
}
"""

# ******************************** Merchant config status use product line ********************
# version: 1.0.0                                                                      *
# **************************************************************************************
"""
@api {get} {domain}/product/bank/api/v1.0/merchant/rule-config/status/product-line             Merchant Bank luật cấu hình trạng thái sử dụng dòng sản phẩm
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  MerchantBankConfigStatusUseProductLine
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}  product_line_id     ID dòng sản phẩm

@apiSuccess   (Response:)     {String}    product_line              ID dòng sản phẩm.
@apiSuccess   (Response:)     {String}    field_check               Field cần so sánh
@apiSuccess   (Response:)     {Object}    info_field_check          Field cần so sánh
@apiSuccess   (Response:)     {Object}    info_field_key            Field cần so sánh
@apiSuccess   (Response:)     {String}    field_key                 Field lấy giá trị
@apiSuccess   (Response:)     {String}    operator_condition        Chuỗi string parse để so sánh check thời gian hết hạn.
@apiSuccess   (Response:)     {String}    operator_key              Chuỗi điều kiện: VD: "op_in_gt", "op_is_in",..
@apiSuccess   (Response:)     {Integer}   code_number               Mã trạng thái thẻ
@apiSuccess   (Response:)     {String}    type                      Tên loại trạng thái
@apiSuccess   (Response:)     {Array}     values                    Danh sách giá trị


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
         {
            "field_check": "today",
            "field_key": "settlement_date",
            "info_field_check": {
                "display_type": "date_picker",
                "field_property": 3,
                "format": "dd/mm/yyyy",
                "name": "today"
            },
            "info_field_key": {
                "display_type": "date_picker",
                "field_property": 3,
                "format": "dd/mm/yyyy",
                "name": "settlement_date"
            },
            "operator_condition": "settlement_date > today",
            "operator_key": "op_is_greater",
            "product_line": "62a93c67a8772cd90c3b53b7",
            "values": []
        },
        {
            "field_check": "time_now",
            "field_key": "due_date",
            "info_field_check": {
                "display_type": "date_picker",
                "field_property": 3,
                "format": "dd/mm/yyyy",
                "name": "time_now"
            },
            "info_field_key": {
                "display_type": "date_picker",
                "field_property": 3,
                "format": "dd/mm/yyyy",
                "name": "due_date"
            },
            "operator_condition": "due_date > time_now",
            "operator_key": "op_is_greater",
            "product_line": "62c3a7147128b9000ff1fd41",
            "values": [
                "2022-07-11T00:00:00.000000Z"
            ]
        }
    ],
  "lang": "vi",
  "message": "request thành công."
}
"""

"""
@api {get} {domain}/product/bank/api/v1.0/all/field/internal     Get list all field internal        
@apiDescription         Get list all field internal 
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  BankAllField
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)     {String}     field_keys    Danh sách field cần lấy ngăn cách nhau bởi dấu phây. Ví dụ: field_key,field_name. 
                                                    Nếu lấy all thì không cần truyền 


@apiParamExample    {json}      Query example:
{
    ?field_keys=field_key,field_name
}                            

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "data": [
    {
        "field_name": "field_1",
        "field_key": "_dyn_name_1558406233387",
    },
    {
        "field_name": "field_2",
        "field_key": "_dyn_name_1558406233389",
    }
  ],
  "message": "request thành công."
}
"""
"""
@api {post} {domain}/product/bank/api/v1.0/field-by-attribute     Lấy danh sách field của những dòng sản phẩm theo thuộc tính của field      
@apiDescription         Get list field in multi product line by attribute field 
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  BankFieldInProductLineByAttribute
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 404
@apiUse 401
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)     {Array}      field_attributes    Danh sách thuộc tính cần lấy ngăn cách nhau bởi dấu phây. Ví dụ: field_key,field_name. 
@apiParam   (Body:)     {Object}     field_attribute_filter     Bộ lọc thuộc tính của field. <code>Chỉ lấy những field thoả mãn bộ lọc này.</code>
@apiParam   (Body:)     {Array}      lst_product_line_id      Danh sách <code>ID</code> dòng sản phẩm cần lấy field.
                                                              
                                        


@apiParamExample    {json}      Body example:
{
    "field_attributes": ["field_key"],
    "field_attribute_filter": {
        "allow_calculate_sum": True
    },
    "lst_product_line_id": ["product_line_id_1", "product_line_id_2"],
}                            

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "data": {
        "product_line_id_1": {
            "product_line_id": "product_line_id_1"
            "product_line_code": "tai_khoan",
            "lst_field": ["current_balance_original_currency"]
        },
        "product_line_id_2": {
            "product_line_id": "product_line_id_2",
            "product_line_code": "trai_phieu",
            "lst_field": ["current_balance_original_currency"]
        }
  },
  "message": "request thành công."
}
"""
"""
@api {get} {domain}/product/bank/api/v1.0/merchant/fields-with-group     Lấy danh sách field và cấu hình group        
@apiDescription         Lấy danh sách field và cấu hình group 
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  BankFieldWithGroups
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}     [field_keys]=translate_key,format,field_key,display_type,field_property,field_name,group,is_base    Danh sách field cần lấy ngăn cách nhau bởi dấu phây. Ví dụ: field_key,field_name.


@apiParamExample    {json}      Query example:
{
    ?field_keys=field_key,field_name
}    

@apiSuccess   (Response:)     {Object}    data                  Dữ liệu trả về
@apiSuccess   (Response:)     {Array}    data.fields           Danh sách field
@apiSuccess   (Response:)     {Array}    data.group_configs    Danh sách cấu hình group
                         
@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "data": {
	"fields": [
		{
		  "translate_key": "i18n_product_name",
	      "format": null,
	      "field_key": "product_name",
	      "display_type": "dropdown_single_line",
	      "field_property": 2,
	      "field_name": "Tên sản phẩm",
	      "group": "information"
		},
		....
	],
	"group_configs": [
	    {
	      "_id": "64352f134b12b8c26eeac48b",
	      "group_name": "Nhân khẩu học",
	      "is_base": true,
	      "group_index": 1,
	      "group_key": "demographic",
	      "created_time": "2023-04-11T09:57:39.099000Z",
	      "updated_time": "2023-04-11T09:57:39.099000Z",
	      "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46"
	    }
  	]	

    },
  "message": "request thành công."
}
"""

"""
@api {GET} {domain}/product/bank/api/v1.0/merchant/fields/list-without-group     Lấy danh sách field không bao gồm cấu hình group        
@apiDescription         Lấy danh sách field không bao gồm cấu hình group
@apiGroup BankDynamicField
@apiVersion 1.0.0
@apiName  BankFieldWithOutGroups
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}     [attributes=field_key,field_name,field_property,display_type]     Danh sách field cần lấy ngăn cách nhau bởi dấu phẩy. Ví dụ: field_key,field_name.
@apiParam   (Query:)     {String}     [field_keys]                                                      Danh sách <code>field_key</code> cần lấy dữ liệu. 
                                                                                                        <code>Trong trường hợp không truyền thì sẽ mặc định lấy tất cả</code>
@apiParamExample    {json}      Query example:
{
    ?attributes=field_key,field_name&field_keys=name,code
}    

@apiSuccess   (Response:)     {Array}    data                  Dữ liệu trả về
                         
@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "data": [
    {
      "field_key": "name",
      "field_property": 2,
      "display_type": "single_line"
    }
  ],
  "message": "request thành công."
}
"""

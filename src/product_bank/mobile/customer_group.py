#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" 
    Author: tungdd
    Company: MobioVN
    Date created: 18/10/2023
"""
"""
@api {get} {domain}/product/mobile/bank/api/v1.0/customer-group             L<PERSON><PERSON>nh s<PERSON>ch nhóm khách hàng
@apiDescription Get List Customer Group
@apiGroup MobileBankCustomerGroup
@apiVersion 1.0.0
@apiName  MerchantBankListCustomerGroup
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
        "group_name" : "Khách hàng Ưu tiên",
        "group_customer_code" : "KHACHHANG_eb1680bc",
        "translate_key": "",
        "is_default": true,
    }
  ]
  "lang": "vi",
  "message": "request thành công."
}
"""

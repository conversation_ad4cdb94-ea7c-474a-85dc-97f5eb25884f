#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 06/12/2022
"""
"""
@api {get} {domain}/product/mobile/bank/api/v1.0/product-type                  Danh sách loại sản phẩm theo dòng sản phẩm
@apiDescription Lấy danh sách loại sản phẩm, <PERSON><PERSON> thêm các option product_line_id(Không bắt buộc), search và có chia page.
@apiGroup MobileProductTypeBank
@apiVersion 1.0.0
@apiName ListProductType

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging_tokens

@apiParam   (Query:)   {String}    [product_line_id]                <code>ID</code> dòng sản phẩm
@apiParam   (Query:)   {String}    [search]                         Tên hoặc mã danh mục cần tìm kiếm
@apiParam   (Query)    {Int}       [type_get]                       Ki<PERSON>u lấy danh mục. <code>Nếu không truyền thì sẽ lấy cả sub</code> 
                                                                    <ul>
                                                                        <li><code>root</code> chỉ lấy danh mục cấp 1</li>
                                                                    </ul>
                                                                    

@apiSuccess {Number} code       Response status    
@apiSuccess {String} message    Response message
@apiSuccess {Array} data        Danh sách dòng sản phẩm   

@apiSuccess   (Response:)     {String}    data.name                      Tên danh mục 
@apiSuccess   (Response:)     {String}    data.code                      Mã danh mục
@apiSuccess   (Response:)     {String}    data.index                     Vị trí cấp của danh mục
@apiSuccess   (Response:)     {String}    data.path                      Đường dẫn danh mục
@apiSuccess   (Response:)     {String}    data.parent_code               Mã của danh mục cha
@apiSuccess   (Response:)     {String}    data.root_code                 Mã của danh mục gốc
@apiSuccess   (Response:)     {String}    data.product_line_id           Id của dòng sản phẩm
@apiSuccess   (Response:)     {Array}     data.sub                       Danh sách các danh mục cấp con

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id": "62a6dbfffd729f446d65beff",
            "code": "DANHMUC_1_a",
            "created_time": **********.295672,
            "id": "62a6dbfffd729f446d65beff",
            "index": 0,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_a",
            "parent_code": "DANHMUC_1_a",
            "parent_id": "62a6dbfffd729f446d65beff",
            "path": "DANHMUC_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "sub": [
                {
                    "_id": "62a6dbfffd729f446d65bf00",
                    "code": "DANHMUC1_1_a",
                    "created_time": **********.295709,
                    "index": 1,
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "cat_1_1_a",
                    "parent_code": "DANHMUC_1_a",
                    "parent_id": "62a6dbfffd729f446d65beff",
                    "path": "DANHMUC_1_a#DANHMUC1_1_a",
                    "product_line_id": "62a165a6fd729f1b02e3f8c9",
                    "root_code": "DANHMUC_1_a",
                    "sub": [
                        {
                            "_id": "62a6dbfffd729f446d65bf01",
                            "code": "DANHMUC1_1_1_a",
                            "created_time": **********.295735,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_1_1_1_a",
                            "parent_code": "DANHMUC1_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf00",
                            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_1_1_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_1_a",
                            "sub": [],
                            "updated_time": **********.295736
                        },
                        {
                            "_id": "62a6dbfffd729f446d65bf02",
                            "code": "DANHMUC1_1_3_a",
                            "created_time": **********.295757,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_1_1_3_4_5_a",
                            "parent_code": "DANHMUC1_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf00",
                            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_1_3_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_1_a",
                            "sub": [],
                            "updated_time": **********.295758
                        },
                        {
                            "_id": "62a6dbfffd729f446d65bf05",
                            "code": "DANHMUC1_2_2_a",
                            "created_time": **********.295811,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_1_2_2_a",
                            "parent_code": "DANHMUC1_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf00",
                            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_2_2_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_1_a",
                            "sub": [],
                            "updated_time": **********.295812
                        }
                    ],
                    "updated_time": **********.29571
                },
                {
                    "_id": "62a6dbfffd729f446d65bf03",
                    "code": "DANHMUC1_2_a",
                    "created_time": **********.295776,
                    "index": 1,
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "cat_1_2_a",
                    "parent_code": "DANHMUC_1_a",
                    "parent_id": "62a6dbfffd729f446d65beff",
                    "path": "DANHMUC_1_a#DANHMUC1_2_a",
                    "product_line_id": "62a165a6fd729f1b02e3f8c9",
                    "root_code": "DANHMUC_1_a",
                    "sub": [
                        {
                            "_id": "62a6e9f0fd729f5d140f40c8",
                            "code": "DANHMUC1_2_3_a",
                            "created_time": 1655106032.82096,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_1_2_3_a",
                            "parent_code": "DANHMUC1_2_a",
                            "parent_id": "62a6dbfffd729f446d65bf03",
                            "path": "DANHMUC_1_a#DANHMUC1_2_a#DANHMUC1_2_3_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_1_a",
                            "sub": [],
                            "updated_time": 1655106032.820975
                        }
                    ],
                    "updated_time": **********.295777
                }
            ],
            "updated_time": **********.295677
        },
        {
            "_id": "62a6dbfffd729f446d65bf06",
            "code": "DANHMUC_2_a",
            "created_time": **********.29582,
            "id": "62a6dbfffd729f446d65bf06",
            "index": 0,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_a",
            "parent_code": "DANHMUC_2_a",
            "parent_id": "62a6dbfffd729f446d65bf06",
            "path": "DANHMUC_2_a",
            "product_line_id": "63be368e1ac7f8a21f0790c7",
            "root_code": "DANHMUC_2_a",
            "sub": [
                {
                    "_id": "62a6dbfffd729f446d65bf07",
                    "code": "DANHMUC2_1_a",
                    "created_time": **********.295837,
                    "index": 1,
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "cat_2_1_a",
                    "parent_code": "DANHMUC_2_a",
                    "parent_id": "62a6dbfffd729f446d65bf06",
                    "path": "DANHMUC_2_a#DANHMUC2_1_a",
                    "product_line_id": "63be368e1ac7f8a21f0790c7",
                    "root_code": "DANHMUC_2_a",
                    "sub": [
                        {
                            "_id": "62a6dbfffd729f446d65bf08",
                            "code": "DANHMUC2_1_1_a",
                            "created_time": **********.295853,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_2_1_1_a",
                            "parent_code": "DANHMUC2_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf07",
                            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_1_a",
                            "product_line_id": "63be368e1ac7f8a21f0790c7",
                            "root_code": "DANHMUC_2_a",
                            "sub": [],
                            "updated_time": **********.295854
                        },
                        {
                            "_id": "62a6dbfffd729f446d65bf09",
                            "code": "DANHMUC2_1_3_a",
                            "created_time": **********.295873,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_2_1_3_4_5_a",
                            "parent_code": "DANHMUC2_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf07",
                            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_3_a",
                            "product_line_id": "63be368e1ac7f8a21f0790c7",
                            "root_code": "DANHMUC_2_a",
                            "sub": [],
                            "updated_time": **********.295874
                        },
                        {
                            "_id": "62a6dbfffd729f446d65bf0b",
                            "code": "DANHMUC2_2_1_a",
                            "created_time": **********.295907,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_2_2_1_a",
                            "parent_code": "DANHMUC2_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf07",
                            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_2_1_a",
                            "product_line_id": "63be368e1ac7f8a21f0790c7",
                            "root_code": "DANHMUC_2_a",
                            "sub": [],
                            "updated_time": **********.295908
                        }
                    ],
                    "updated_time": **********.295837
                },
                {
                    "_id": "62a6dbfffd729f446d65bf0a",
                    "code": "DANHMUC2_2_a",
                    "created_time": **********.295891,
                    "index": 1,
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "cat_2_2_a",
                    "parent_code": "DANHMUC_2_a",
                    "parent_id": "62a6dbfffd729f446d65bf06",
                    "path": "DANHMUC_2_a#DANHMUC2_2_a",
                    "product_line_id": "63be368e1ac7f8a21f0790c7",
                    "root_code": "DANHMUC_2_a",
                    "sub": [
                        {
                            "_id": "62a6dbfffd729f446d65bf0c",
                            "code": "DANHMUC2_2_2_a",
                            "created_time": **********.295924,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_2_2_2_a",
                            "parent_code": "DANHMUC2_2_a",
                            "parent_id": "62a6dbfffd729f446d65bf0a",
                            "path": "DANHMUC_2_a#DANHMUC2_2_a#DANHMUC2_2_2_a",
                            "product_line_id": "63be368e1ac7f8a21f0790c7",
                            "root_code": "DANHMUC_2_a",
                            "sub": [],
                            "updated_time": **********.295924
                        }
                    ],
                    "updated_time": **********.295891
                }
            ],
            "updated_time": **********.295821
        }
    ],
    "lang": "vi",
    "message": "request success"
}

"""
"""
@api {post} {domain}/product/mobile/bank/api/v1.0/product-type/action/get-by-ids        Lấy danh sách chi tiết loại sản phẩm theo ids
@apiGroup MobileProductTypeBank
@apiVersion 1.0.0
@apiName  ListDetailProductTypeBankByIds
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Body:)	        {Array}        ids                   Danh sách id loại sản phẩm 
@apiParam	(Body:)	        {Array}        [fields]                Danh sách field hiển thị


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "_id" : "629f787cfd729f242766d54a",
            "name" : "cat_1",
            "code" : "DANHMUC_1",
            "root_code" : "DANHMUC_1",
            "path" : "DANHMUC_1",
            "index" : 0,
            "parent_code" : "DANHMUC_1",
            "product_line_id" : "629d6a1a6990defe80df75bf",
            "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "created_time" : **********.55202,
            "updated_time" : **********.55202
        },
        {
            "_id" : "629f787cfd729f242766d54b",
            "name" : "cat_1_1",
            "code" : "DANHMUC1_1",
            "root_code" : "DANHMUC_1",
            "path" : "DANHMUC_1#DANHMUC1_1",
            "index" : 1,
            "parent_code" : "DANHMUC_1",
            "product_line_id" : "629d6a1a6990defe80df75bf",
            "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "created_time" : **********.55204,
            "updated_time" : **********.55204
        }
    ]
    "message": "request thành công."
}
"""

"""
@api {get} {domain}/product/mobile/bank/api/v1.0/product-type/action/get-list-root        Lấy danh sách loại sản phẩm gốc
@apiGroup MobileProductTypeBank
@apiVersion 1.0.0
@apiName  ListRootProductType
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Query:)	        {string}        [search]                   Tên loại sản phẩm cà search 

@apiUse paging_tokens

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "_id" : "629f787cfd729f242766d54a",
            "name" : "cat_1",
            "code" : "DANHMUC_1",
            "root_code" : "DANHMUC_1",
            "path" : "DANHMUC_1",
            "index" : 0,
            "parent_code" : "DANHMUC_1",
            "product_line_id" : "629d6a1a6990defe80df75bf",
            "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "created_time" : **********.55202,
            "updated_time" : **********.55202,
            "customer_group": "KHACH_HANG_123tu3u",
            "mapping_customer_group_block_customer": "KHCN"
        },
        {
            "_id" : "629f787cfd729f242766d54b",
            "name" : "cat_1_1",
            "code" : "DANHMUC1_1",
            "root_code" : "DANHMUC_1",
            "path" : "DANHMUC_1#DANHMUC1_1",
            "index" : 1,
            "parent_code" : "DANHMUC_1",
            "product_line_id" : "629d6a1a6990defe80df75bf",
            "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "created_time" : **********.55204,
            "updated_time" : **********.55204,
            "customer_group": "KHACH_HANG_123tu3u",
            "mapping_customer_group_block_customer": "KHCN"
        }
    ]
    "message": "request thành công."
}
"""

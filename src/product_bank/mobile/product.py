#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 06/12/2022
"""
"""
@api {POST} {domain}/product/mobile/bank/api/v1.0/product/actions/list-by-line-type                  L<PERSON><PERSON> danh sách sản phẩm theo loại sản phẩm hoặc dòng sản phẩm
@apiGroup MobileProductBank
@apiVersion 1.0.0
@apiName GetProductBankByProductLineType

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging_tokens

@apiParam   (Body:)   {String}      search                              Tên sản phẩm cần tìm kiếm
@apiParam   (Query:)   {String}      [sort]                              Tên field trường thông tin cần sắp xếp
@apiParam   (Query:)   {String}      [order=desc]                             Kiểu sắp xếp ( asc: sắp xếp tăng dần desc: sắp xếp gi<PERSON>m dần(default) )
@apiParam   (Body:)   {Int=0,1}     take_only_get_product_end_tree=0    Chỉ lấy sản phẩm cuối cùng của cây
                                                                        <ul>
                                                                            <li>0: lấy dữ liệu ở nhánh bất kỳ</li>
                                                                            <li>1: lấy dữ liệu ở nhánh cuối cùng của cây sản phẩm</li>
                                                                        </ul>

@apiParam   (Body:)   {Array}        [category_ids]                  Danh sách id loại sản phẩm cần lấy dữ liệu
@apiParam   (Body:)   {String}       [product_line_id]                  Id dòng sản phẩm cần tìm
@apiParam   (Body:)   {Boolean}      [status_display_product]           Trạng thái sản phẩm hiển thị(True/False) 
@apiParam   (Body:)   {Array}        [fields]                           Danh sách field cần hiển thị

@apiSuccess {Number} code       Response status    
@apiSuccess {String} message    Response message
@apiSuccess {Array} data        Danh sách sản phẩm

@apiSuccess   (Response:)     {Float}       data.created_time              Thời gian tạo sản phẩm 
@apiSuccess   (Response:)     {String}      data.customer_group            Nhóm khách hàng
@apiSuccess   (Response:)     {String}      data.id                        <code>ID</code> sản phẩm
@apiSuccess   (Response:)     {String}      data.merchant_id               Định danh merchant 
@apiSuccess   (Response:)     {String}      data.name                      Tên sản phẩm
@apiSuccess   (Response:)     {String}      data.product_code              Mã sản phẩm
@apiSuccess   (Response:)     {String}      data.product_line              Định danh dòng sản phẩm 
@apiSuccess   (Response:)     {Array}       data.product_type              Danh sách loại sản phẩm 
@apiSuccess   (Response:)     {Array}       data.customer_group            Mã nhóm khách hàng 
@apiSuccess   (Response:)     {Array}       data.mapping_customer_group_block_customer            Mapping giữa khối khách hàng của từng user với cả nhóm khách hàng sản phẩm 
@apiSuccess   (Response:)     {Int}         data.status                    Trạng thái sản phẩm 
@apiSuccess   (Response:)     {Float}       data.updated_time              Thời gian cập nhật sản phẩm 



@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "created_time": 1654766101.45016,
            "customer_group": "KHACHHANG_77b2081f",
            "id": "62a1ba15fd729f8060fd8001",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "product 45678",
            "product_code": "product_456",
            "product_line": "62a16682fd729f1bf1484019",
            "product_type": [
                "629f787cfd729f242766d54b",
                "629f787cfd729f242766d54c",
                "629f787cfd729f242766d54d"
            ],
            "status": 1,
            "customer_group": "KHACHHANG_5fe59512",
            "mapping_customer_group_block_customer": "KHCN",
            "updated_time": 1654766101.45016
        },
        {
            "created_time": **********.642189,
            "customer_group": "KHACHHANG_77b2081f",
            "id": "62a1b9f1fd729f8060fd8000",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "product 123",
            "product_code": "product_123",
            "product_line": "62a16682fd729f1bf1484019",
            "product_type": [
                "629f787cfd729f242766d54b",
                "629f787cfd729f242766d54c",
                "629f787cfd729f242766d54d"
            ],
            "status": 1,
            "customer_group": "KHACHHANG_5fe59512",
            "mapping_customer_group_block_customer": "KHCN",
            "updated_time": **********.642199
        }
    ],
    "lang": "vi",
    "message": "request success"
}

"""
"""
@api {POST} {domain}/product/mobile/bank/api/v1.0/product/actions/get-list-directly-attached-product-line                  Lấy danh sách sản phẩm gắn trực tiếp vào dòng sản phẩm
@apiGroup MobileProductBank
@apiVersion 1.0.0
@apiName GetListProductDirectlyAttachedProductLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging_tokens

@apiParam   (Body:)   {String}      search                              Tên sản phẩm cần tìm kiếm
@apiParam   (Query:)   {String}      [sort]                              Tên field trường thông tin cần sắp xếp
@apiParam   (Query:)   {String}      [order=desc]                             Kiểu sắp xếp ( asc: sắp xếp tăng dần desc: sắp xếp giảm dần(default) )
@apiParam   (Body:)   {String}       product_line_id                  Id dòng sản phẩm cần tìm
@apiParam   (Body:)   {Array}        [fields]                           Danh sách field cần hiển thị

@apiSuccess {Number} code       Response status    
@apiSuccess {String} message    Response message
@apiSuccess {Array} data        Danh sách sản phẩm

@apiSuccess   (Response:)     {Float}       data.created_time              Thời gian tạo sản phẩm 
@apiSuccess   (Response:)     {String}      data.customer_group            Nhóm khách hàng
@apiSuccess   (Response:)     {String}      data.id                        <code>ID</code> sản phẩm
@apiSuccess   (Response:)     {String}      data.merchant_id               Định danh merchant 
@apiSuccess   (Response:)     {String}      data.name                      Tên sản phẩm
@apiSuccess   (Response:)     {String}      data.product_code              Mã sản phẩm
@apiSuccess   (Response:)     {String}      data.product_line              Định danh dòng sản phẩm 
@apiSuccess   (Response:)     {Array}       data.product_type              Danh sách loại sản phẩm 
@apiSuccess   (Response:)     {Array}       data.customer_group            Mã nhóm khách hàng 
@apiSuccess   (Response:)     {Array}       data.mapping_customer_group_block_customer            Mapping giữa khối khách hàng của từng user với cả nhóm khách hàng sản phẩm 
@apiSuccess   (Response:)     {Int}         data.status                    Trạng thái sản phẩm 
@apiSuccess   (Response:)     {Float}       data.updated_time              Thời gian cập nhật sản phẩm 



@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "created_time": 1654766101.45016,
            "customer_group": "KHACHHANG_77b2081f",
            "id": "62a1ba15fd729f8060fd8001",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "product 45678",
            "product_code": "product_456",
            "product_line": "62a16682fd729f1bf1484019",
            "product_type": [
                "629f787cfd729f242766d54b",
                "629f787cfd729f242766d54c",
                "629f787cfd729f242766d54d"
            ],
            "status": 1,
            "customer_group": "KHACHHANG_5fe59512",
            "mapping_customer_group_block_customer": "KHCN",
            "updated_time": 1654766101.45016
        },
        {
            "created_time": **********.642189,
            "customer_group": "KHACHHANG_77b2081f",
            "id": "62a1b9f1fd729f8060fd8000",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "product 123",
            "product_code": "product_123",
            "product_line": "62a16682fd729f1bf1484019",
            "product_type": [
                "629f787cfd729f242766d54b",
                "629f787cfd729f242766d54c",
                "629f787cfd729f242766d54d"
            ],
            "status": 1,
            "customer_group": "KHACHHANG_5fe59512",
            "mapping_customer_group_block_customer": "KHCN",
            "updated_time": **********.642199
        }
    ],
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {post} {domain}/product/mobile/bank/api/v1.0/product/action/get-by-ids        Lấy danh sách chi tiết sản phẩm theo ids
@apiGroup MobileProductBank
@apiVersion 1.0.0
@apiName  DetailProductBankByIds
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Body:)	        {Array}        ids                   Danh sách id sản phẩm
@apiParam	(Body:)	        {Array}        [fields]                Danh sách field hiển thị

@apiSuccess (Data)      {String}         product_line                 Id dòng sản phẩm  
@apiSuccess (Data)      {String}         name                         Tên sản phẩm
@apiSuccess (Data)      {String}         product_code                 Mã sản phẩm
@apiSuccess (Data)      {String}         customer_group               Mã nhóm khách hàng
@apiSuccess (Data)      {Array}          mapping_customer_group_block_customer            Mapping giữa khối khách hàng của từng user với cả nhóm khách hàng sản phẩm
@apiSuccess (Data)      {Array}          product_type                 Danh sách Id loại sản phẩm




@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "_id" : "62a1b9f1fd729f8060fd8000",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "id" : "62a1b9f1fd729f8060fd8000",
            "created_time" : **********.64219,
            "updated_time" : **********.6422,
            "product_line" : "62a16682fd729f1bf1484019",
            "name" : "product 123",
            "product_code" : "product_123",
            "customer_group" : "KHACHHANG_77b2081f",
            "status_display_product": True,
            "product_type" : [ 
                "629f787cfd729f242766d54b", 
                "629f787cfd729f242766d54c", 
                "629f787cfd729f242766d54d"
            ],
            "mapping_customer_group_block_customer": "KHCN"
        },
        {
            "_id" : "62a1ba15fd729f8060fd8001",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "id" : "62a1ba15fd729f8060fd8001",
            "created_time" : 1654766101.45016,
            "updated_time" : 1654766101.45016,
            "product_line" : "62a16682fd729f1bf1484019",
            "name" : "product 45678",
            "product_code" : "product_456",
            "customer_group" : "KHACHHANG_77b2081f",
            "status_display_product": True,
            "mapping_customer_group_block_customer": "KHCN",
            "product_type" : [ 
                "629f787cfd729f242766d54b", 
                "629f787cfd729f242766d54c", 
                "629f787cfd729f242766d54d"
            ]
        }
    ]
    "message": "request thành công."
}
"""
"""
@api {PUT} {domain}/product/mobile/bank/api/v1.0/action/file-attachment/<file_id>             Cập nhật file đính kèm
@apiGroup FileAttachment
@apiVersion 1.0.0
@apiName  UpdateFileAttachment
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Body:)	        {String}        name                   File name mới cần thay đổi
@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
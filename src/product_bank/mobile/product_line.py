#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 06/12/2022
"""
"""
@apiDefine paging_tokens
@apiVersion 1.0.0
@apiParam   (Query:)    {Number}    [per_page] Số phần tử trên một page.<br/> Example: <code>&per_page=5</code>.
                                    <br>Nếu muốn lấy toàn bộ dữ liệu thì cần truyền <code>per_page=-1</code></br>
@apiParam   (Query:)    {String}    [after_token] Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)    {String}    [before_token] Token để request lấy dữ liệu trang trước đó. Nếu trong danh sách tham số gửi lên có <code>after_token</code> thì ưu tiên xử lý <code>after_token</code>

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
@apiSuccess     {Object}    [paging.cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging.cursors.after]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging.cursors.before]    Token để lấy dữ liệu trang trước đó.
@apiSuccess     {Object}    [paging.per_page]          Số lượng phần tử trên 1 page
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh",
            "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 1,
    }
}
"""
"""
@api {POST} {domain}/product/mobile/bank/api/v1.0/product-line/actions/filter                  Lấy danh sách dòng sản phẩm
@apiGroup MobileProductLineBank
@apiVersion 1.0.0
@apiName ListProductLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse paging_tokens

@apiParam   (Body:)    {String}     [search]                     Tìm kiếm tên dòng sản phẩm
@apiParam   (Body:)    {Array}      [fields]                     Danh sách trường cần lấy thông tin
@apiParam   (Body:)    {String}     [customer_group_code]        Mã nhóm Khách hàng
@apiParam   (Query:)   {String}     [sort=updated_time]          Field cần sắp xếp
@apiParam   (Query:)   {String}     [order=desc]                 Sắp xếp theo thứ tự
                                                                 <ul>
                                                                    <li><code>asc</code>: sắp xếp tăng dần</li>
                                                                    <li><code>desc</code>: sắp xếp giảm dần(default)</li>
                                                                 </ul>
                                                                 
@apiSuccess {Number} code       Response status    
@apiSuccess {String} message    Response message
@apiSuccess {Array} data        Danh sách dòng sản phẩm                                                                                                                                 

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id" : "62a93c67a8772cd90c3b53b1",
            "code" : "tai_khoan_39fcdcd2",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "account_id" : null,
            "created_by" : null,
            "created_time" : **********.70823,
            "customer_group" : "KHACHHANG_e51dc007",
            "name" : "TÀI KHOẢN",
            "name_ascii" : "tai khoan",
            "updated_by" : null,
            "updated_time" : **********.70824,
            "count_product" : 14
        }
    ]
    "lang": "vi",
    "message": "request success"
}

"""
"""
@api {get} {domain}/product/mobile/bank/api/v1.0/product-line                  Chi tiết dòng sản phẩm
@apiGroup MobileProductLineBank
@apiVersion 1.0.0
@apiName DetailMobileProductLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {String}    id           id dòng sản phẩm

@apiSuccess   (Response:)     {String}    name                      Tên dòng sản phẩm
@apiSuccess   (Response:)     {String}    code                      Mã dòng sản phẩm
@apiSuccess   (Response:)     {String}    customer_group_code       Mã nhóm khách hàng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "name": "",
        "group_customer_code": "",
        "code": ""
    }
    "lang": "vi",
    "message": "request success"
}

"""
"""
@api {get} {domain}/product/mobile/bank/api/v1.0/product-line/<product-line-id>/list-product-type                 Lấy danh sách loại sản phẩm của dòng sản phẩm
@apiDescription Lấy danh sách loại sản phẩm của 1 dòng sản phẩm.
@apiGroup MobileProductLineBank
@apiVersion 1.0.0
@apiName GetListProductTypeOfProductLineId

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {String}    [search]           tên hoặc mã danh mục cần tìm kiếm

@apiSuccess   (Response:)     {String}    name                      Tên danh mục 
@apiSuccess   (Response:)     {String}    code                      Mã danh mục
@apiSuccess   (Response:)     {String}    index                     Vị trí cấp của danh mục
@apiSuccess   (Response:)     {String}    path                      Đường dẫn danh mục
@apiSuccess   (Response:)     {String}    parent_code               Mã của danh mục cha
@apiSuccess   (Response:)     {String}    root_code                 Mã của danh mục gốc
@apiSuccess   (Response:)     {String}    product_line_id           Id của dòng sản phẩm
@apiSuccess   (Response:)     {Array}     sub                       Danh sách các danh mục cấp con

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id": "62a6dbfffd729f446d65beff",
            "code": "DANHMUC_1_a",
            "created_time": **********.295672,
            "id": "62a6dbfffd729f446d65beff",
            "index": 0,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_a",
            "parent_code": "DANHMUC_1_a",
            "parent_id": "62a6dbfffd729f446d65beff",
            "path": "DANHMUC_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "sub": [
                {
                    "_id": "62a6dbfffd729f446d65bf00",
                    "code": "DANHMUC1_1_a",
                    "created_time": **********.295709,
                    "index": 1,
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "cat_1_1_a",
                    "parent_code": "DANHMUC_1_a",
                    "parent_id": "62a6dbfffd729f446d65beff",
                    "path": "DANHMUC_1_a#DANHMUC1_1_a",
                    "product_line_id": "62a165a6fd729f1b02e3f8c9",
                    "root_code": "DANHMUC_1_a",
                    "sub": [
                        {
                            "_id": "62a6dbfffd729f446d65bf01",
                            "code": "DANHMUC1_1_1_a",
                            "created_time": **********.295735,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_1_1_1_a",
                            "parent_code": "DANHMUC1_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf00",
                            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_1_1_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_1_a",
                            "sub": [],
                            "updated_time": **********.295736
                        },
                        {
                            "_id": "62a6dbfffd729f446d65bf02",
                            "code": "DANHMUC1_1_3_a",
                            "created_time": **********.295757,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_1_1_3_4_5_a",
                            "parent_code": "DANHMUC1_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf00",
                            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_1_3_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_1_a",
                            "sub": [],
                            "updated_time": **********.295758
                        },
                        {
                            "_id": "62a6dbfffd729f446d65bf05",
                            "code": "DANHMUC1_2_2_a",
                            "created_time": **********.295811,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_1_2_2_a",
                            "parent_code": "DANHMUC1_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf00",
                            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_2_2_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_1_a",
                            "sub": [],
                            "updated_time": **********.295812
                        }
                    ],
                    "updated_time": **********.29571
                },
                {
                    "_id": "62a6dbfffd729f446d65bf03",
                    "code": "DANHMUC1_2_a",
                    "created_time": **********.295776,
                    "index": 1,
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "cat_1_2_a",
                    "parent_code": "DANHMUC_1_a",
                    "parent_id": "62a6dbfffd729f446d65beff",
                    "path": "DANHMUC_1_a#DANHMUC1_2_a",
                    "product_line_id": "62a165a6fd729f1b02e3f8c9",
                    "root_code": "DANHMUC_1_a",
                    "sub": [
                        {
                            "_id": "62a6e9f0fd729f5d140f40c8",
                            "code": "DANHMUC1_2_3_a",
                            "created_time": 1655106032.82096,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_1_2_3_a",
                            "parent_code": "DANHMUC1_2_a",
                            "parent_id": "62a6dbfffd729f446d65bf03",
                            "path": "DANHMUC_1_a#DANHMUC1_2_a#DANHMUC1_2_3_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_1_a",
                            "sub": [],
                            "updated_time": 1655106032.820975
                        }
                    ],
                    "updated_time": **********.295777
                }
            ],
            "updated_time": **********.295677
        },
        {
            "_id": "62a6dbfffd729f446d65bf06",
            "code": "DANHMUC_2_a",
            "created_time": **********.29582,
            "id": "62a6dbfffd729f446d65bf06",
            "index": 0,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_a",
            "parent_code": "DANHMUC_2_a",
            "parent_id": "62a6dbfffd729f446d65bf06",
            "path": "DANHMUC_2_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "sub": [
                {
                    "_id": "62a6dbfffd729f446d65bf07",
                    "code": "DANHMUC2_1_a",
                    "created_time": **********.295837,
                    "index": 1,
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "cat_2_1_a",
                    "parent_code": "DANHMUC_2_a",
                    "parent_id": "62a6dbfffd729f446d65bf06",
                    "path": "DANHMUC_2_a#DANHMUC2_1_a",
                    "product_line_id": "62a165a6fd729f1b02e3f8c9",
                    "root_code": "DANHMUC_2_a",
                    "sub": [
                        {
                            "_id": "62a6dbfffd729f446d65bf08",
                            "code": "DANHMUC2_1_1_a",
                            "created_time": **********.295853,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_2_1_1_a",
                            "parent_code": "DANHMUC2_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf07",
                            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_1_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_2_a",
                            "sub": [],
                            "updated_time": **********.295854
                        },
                        {
                            "_id": "62a6dbfffd729f446d65bf09",
                            "code": "DANHMUC2_1_3_a",
                            "created_time": **********.295873,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_2_1_3_4_5_a",
                            "parent_code": "DANHMUC2_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf07",
                            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_3_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_2_a",
                            "sub": [],
                            "updated_time": **********.295874
                        },
                        {
                            "_id": "62a6dbfffd729f446d65bf0b",
                            "code": "DANHMUC2_2_1_a",
                            "created_time": **********.295907,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_2_2_1_a",
                            "parent_code": "DANHMUC2_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf07",
                            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_2_1_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_2_a",
                            "sub": [],
                            "updated_time": **********.295908
                        }
                    ],
                    "updated_time": **********.295837
                },
                {
                    "_id": "62a6dbfffd729f446d65bf0a",
                    "code": "DANHMUC2_2_a",
                    "created_time": **********.295891,
                    "index": 1,
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "cat_2_2_a",
                    "parent_code": "DANHMUC_2_a",
                    "parent_id": "62a6dbfffd729f446d65bf06",
                    "path": "DANHMUC_2_a#DANHMUC2_2_a",
                    "product_line_id": "62a165a6fd729f1b02e3f8c9",
                    "root_code": "DANHMUC_2_a",
                    "sub": [
                        {
                            "_id": "62a6dbfffd729f446d65bf0c",
                            "code": "DANHMUC2_2_2_a",
                            "created_time": **********.295924,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_2_2_2_a",
                            "parent_code": "DANHMUC2_2_a",
                            "parent_id": "62a6dbfffd729f446d65bf0a",
                            "path": "DANHMUC_2_a#DANHMUC2_2_a#DANHMUC2_2_2_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_2_a",
                            "sub": [],
                            "updated_time": **********.295924
                        }
                    ],
                    "updated_time": **********.295891
                }
            ],
            "updated_time": **********.295821
        }
    ],
    "lang": "vi",
    "message": "request success"
}

"""
"""
@api {get} {domain}/product/mobile/bank/api/v1.0/product-line/action/filter                  Lấy danh sách dòng sản phẩm theo bộ lọc
@apiGroup MobileProductLineBank
@apiVersion 1.0.0
@apiName GetListProductLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {String}     [customer_group]                Mã nhóm khách hàng
@apiParam   (Query:)   {String}     [igore_product_line_ids]        Danh sách dòng sản phẩm id bỏ qua khi lấy thông tin. Nếu nhiều thì trường dạng string và ngăn cách nhau bởi dấu ",".
@apiParam   (Query:)   {String}     [search]                     Tìm kiếm tên dòng sản phẩm
@apiParam   (Query:)   {Int}        [page]                          Tìm kiếm page (page = -1, tìm tất cả)
@apiParam   (Query:)   {Int}        [per_page]                      Số phần tử của page
@apiParam   (Query:)   {String}        [fields]                     Trường thông tin cần hiển thị(cách nhau dấu ",")
@apiParam   (Query:)   {Int}        [product_line_default]       Cờ check Nhóm khách hàng Cá nhân nếu = 1 "KHACHHANG_e51dc007"
@apiParam   (Query:)   {String}     [product_line_type]          Option lấy dữ liệu dòng sản phẩm dựa trên nhóm khách hàng
                                                                 <ul>
                                                                    <li><code>KHCN</code>: lấy dòng sản phẩm của nhóm khách hàng cá nhân. Call khi muốn lấy dữ liệu của Profile</li>
                                                                    <li><code>KHDN</code>: lấy dòng sản phẩm của nhóm khách hàng doanh nghiệp. Call khi muốn lấy dữ liệu của Company</li>
                                                                 </ul>
@apiParam   (Query:)   {String}     [sort]                       Field cần sắp xếp
@apiParam   (Query:)   {String}     [order]                      Sắp xếp theo thứ tự
                                                                    <ul>
                                                                        <li><code>asc</code>: sắp xếp tăng dần</li>
                                                                        <li><code>desc</code>: sắp xếp giảm dần(default)</li>
                                                                    </ul>
@apiParam   (Query:)   {String}     [fields]                     Trường thông tin cần hiển thị (cách nhau dấu ",")

@apiSuccess   (Response:)     {String}    name                      Tên dòng sản phẩm
@apiSuccess   (Response:)     {String}    code                      Mã dòng sản phẩm
@apiSuccess   (Response:)     {String}    customer_group            Mã nhóm khách hàng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id" : "62a93c67a8772cd90c3b53b1",
            "code" : "tai_khoan_39fcdcd2",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "account_id" : null,
            "created_by" : null,
            "created_time" : **********.70823,
            "customer_group" : "KHACHHANG_e51dc007",
            "name" : "TÀI KHOẢN",
            "name_ascii" : "tai khoan",
            "updated_by" : null,
            "updated_time" : **********.70824,
            "count_product" : 14
        }
    ]
    "lang": "vi",
    "message": "request success"
}

"""

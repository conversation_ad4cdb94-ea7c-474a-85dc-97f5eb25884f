#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: tungdd
    Company: MobioVN
    Date created: 07/06/2022
"""
# Lấy ra danh sách các thẻ hiển thị ProductHolding
"""
@api {GET} {domain}/product/bank/api/v1.0/save-view-configs   Danh sách cấu hình thẻ hiển thị ProductHolding
@apiDescription     Danh sách cấu hình thẻ hiển thị ProductHolding
@apiGroup BankSaveViewConfig
@apiVersion 1.0.0
@apiName BankSaveViewConfigs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam        (Query:)     {string}     customer_group_code         Mã nhóm khách hàng
@apiParam        (Query:)     {Bool}       filter_is_pin               Lọc theo thẻ đươc ghim. Nếu không truyền lên mặc định lấy all.

@apiParamExample {Query:} Query example
{
    customer_group_code=KHACH_HANG_DOANH_NGHIEP_1233
}

@apiSuccess {Number} code                   Response status
@apiSuccess {String} message                Response message

@apiSuccess {Array}  data                   Danh sách thẻ hiển thị Ticket
@apiSuccess {String} data.filter            Cấu hình bộ lọc
@apiSuccess {String} data.id                Id bản ghi
@apiSuccess {Boolean} data.is_default        Là thẻ mặc định hay không
@apiSuccess {Boolean} data.is_pin            Được ghim hay không
@apiSuccess {String} data.name              Tên thẻ hiển thị
@apiSuccess {String} data.code              Mã code thẻ hiển thị
@apiSuccess {String} data.last_view         Thẻ xem mới nhất

@apiSuccessExample {json} Response
{
    "data": [
        {
            "code": "TAI_KHOAN_1233",
            "filter": [
                {
                    "criteria_key": "cri_product_line",
                    "operator_key": "op_is_in",
                    "values": [
                        "TAI_KHOAN_1233"
                    ]
                },
                {
                    "criteria_key": "cri_customer_group",
                    "operator_key": "op_is_in",
                    "values": [
                        "KHACH_HANG_DOANH_NGHIEP_1233"
                    ]
                }
            ],
            "id": "620ccdc3ef4011d2bfceb2c0",
            "is_default": true,
            "is_pin": true,
            "name": "",
            "last_view": true,
            "order": 0
        }
    ],
    "code": 200,
    "message": "request thành công."
}
"""

# ---- Cập nhật thẻ hiển thị ProductHolding -----
"""
@api {PUT} {domain}/product/bank/api/v1.0/save-view-config        Cập nhật thẻ hiển thị ProductHolding
@apiDescription         Cập nhật thẻ hiển thị ProductHolding
@apiGroup BankSaveViewConfig
@apiVersion 1.0.0
@apiName UpdateBankSaveViewConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Body:) {String}   id                   ID thẻ hiển thị ProductHolding
@apiParam (Body:) {Boolean}  [is_default]         Đặt làm mặc định hay không
@apiParam (Body:) {Boolean}  [default_new_id]     <code>ID</code> Định danh thẻ mặc định mới 
@apiParam (Body:) {Boolean}  [is_pin]             Ghim hay không

@apiParamExample {json} Body example
{   
    "is_pin": false,
    "is_default": true
}

@apiSuccess {Number} code                               Response status
@apiSuccess {String} message                            Response message
@apiSuccess {Array}  data                               Danh sách thẻ hiển thị Ticket
@apiSuccess {String} data.filter                        Cấu hình bộ lọc
@apiSuccess {String} data.id                            Id bản ghi
@apiSuccess {Boolean} data.is_default                    Là thẻ mặc định hay không
@apiSuccess {Boolean} data.is_pin                        Được ghim hay không
@apiSuccess {String} data.name                          Tên thẻ hiển thị
@apiSuccess {String} data.order                         Sắp xếp

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "code": "TAI_KHOAN_1233",
            "filter": [
                {
                    "criteria_key": "cri_product_line",
                    "operator_key": "op_is_in",
                    "values": [
                        "TAI_KHOAN_1233"
                    ]
                },
                {
                    "criteria_key": "cri_customer_group",
                    "operator_key": "op_is_in",
                    "values": [
                        "KHACH_HANG_DOANH_NGHIEP_1233"
                    ]
                }
            ],
            "id": "620ccdc3ef4011d2bfceb2c0",
            "is_default": true,
            "is_pin": true,
            "name": "",
            "last_view": true,
            "order": 0
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
# ---- Lưu trạng đang chọn của thẻ hiển thị -----
"""
@api {PUT} {domain}/product/bank/api/v1.0/save-view-config/<save-view-config-id>/last-view        Lưu trạng thái xem cuối cùng của thẻ hiển thị
@apiDescription         Lưu trạng thái xem cuối cùng của thẻ hiển thị
@apiGroup BankSaveViewConfig
@apiVersion 1.0.0
@apiName UpdateLastViewBankSaveViewConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
# ---- Lưu trạng đang chọn của thẻ hiển thị -----
"""
@api {PUT} {domain}/product/bank/api/v1.0/save-view-config/<save-view-config-id>/status-filter        Lưu trạng thái đóng mở bộ lọc
@apiDescription         Lưu trạng thái đóng mở bộloojc
@apiGroup BankSaveViewConfig
@apiVersion 1.0.0
@apiName UpdateStausFilterBankSaveViewConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Body:) {String}  status_filter=open,close      Trạng thái bộ lọc

@apiParamExample {json} Body example
{   
    "status_filter": "close"
}


@apiSuccessExample {json} Response
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 16/08/2023
"""

"""
@api {post} {domain}/product/bank/api/v1.0/merchant/upsert/config-option-etl                  Upsert cấu hình etl
@apiGroup ETL
@apiVersion 1.0.0
@apiName MerchantUpsertConfigOptionETL

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}      table                       Tên table ETL cấu hình.
@apiParam   (Body:)   {Int=1,-1}    status                      Trạng thái của bản ghi
                                                                <ul>
                                                                    <li>1: hoạt động</li>
                                                                    <li>-1: không hoạt động</li>
                                                                </ul>
@apiParam   (Body:)   {String=insert,update,upsert,check_exist} action=upsert       Hành động thực hiện
                                                                                    <ul>
                                                                                        <li><code>insert</code> thực hiện việc insert dữ liệu</li>
                                                                                        <li><code>update</code> thực hiện việc update dữ liệu</li>
                                                                                        <li><code>upsert</code> thực hiện việc upsert dữ liệu</li>
                                                                                        <li><code>check_exist</code> thực hiện việc check_exist dữ liệu</li>
                                                                                    </ul>                      
@apiParam   (Body:)   {Object}      [options]                   Tuỳ chọn cấu hình với bảng etl
                                                                Ví dụ:
                                                                    + product_line :: cấu hình dòng sản phẩm mặc định khi etl từ bảng này.


@apiParamExample {json} Body example
{
    "table": "lending",
    "action": "upsert",
    "status": 1,
    "options": {
        "product_line": ""
    }
}       


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "lang": "vi",
    "message": "request success"
}

"""
"""
    Author: KIEUANH
    Company: MOBIO
    Date Created: 03/06/2022
"""

"""
@api {post} {domain}/product/bank/api/v1.0/action/product-line                  Tạo dòng sản phẩm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName CreateProductLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}    name                      Tên dòng sản phẩm
@apiParam   (Body:)   {String}    code                      Mã dòng sản phẩm
@apiParam   (Body:)   {String}    customer_group_code       Mã nhóm khách hàng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "_id": "62a00f93fd729f519d5c87ce",
        "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "code": "kanh_test_123_code",
        "created_time": **********.305598,
        "customer_group_code": "KHACHHANG_77b2081f",
        "id": "62a00f93fd729f519d5c87ce",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "name": "kanh test 123",
        "updated_time": **********.305604
    },
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {get} {domain}/product/bank/api/v1.0/action/filter/product-line                  Lấy danh sách dòng sản phẩm theo bộ lọc
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetListProductLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {String}     [customer_group]        Mã nhóm khách hàng
@apiParam   (Query:)   {String}     [search]                     Tìm kiếm tên dòng sản phẩm
@apiParam   (Query:)   {Int}        [page]                          Tìm kiếm page (page = -1, tìm tất cả)
@apiParam   (Query:)   {Int}        [per_page]                      Số phần tử của page
@apiParam   (Query:)   {String}        [fields]                     Trường thông tin cần hiển thị(cách nhau dấu ",")
@apiParam   (Query:)   {Int}        [product_line_default]       Cờ check Nhóm khách hàng Cá nhân nếu = 1 "KHACHHANG_e51dc007"
@apiParam   (Query:)   {String}     [product_line_type]          Option lấy dữ liệu dòng sản phẩm dựa trên nhóm khách hàng
                                                                 <ul>
                                                                    <li><code>KHCN</code>: lấy dòng sản phẩm của nhóm khách hàng cá nhân. Call khi muốn lấy dữ liệu của Profile</li>
                                                                    <li><code>KHDN</code>: lấy dòng sản phẩm của nhóm khách hàng doanh nghiệp. Call khi muốn lấy dữ liệu của Company</li>
                                                                 </ul>
@apiParam   (Query:)   {String}     [sort]                       Field cần sắp xếp
@apiParam   (Query:)   {String}     [order]                      Sắp xếp theo thứ tự
                                                                    <ul>
                                                                        <li><code>asc</code>: sắp xếp tăng dần</li>
                                                                        <li><code>desc</code>: sắp xếp giảm dần(default)</li>
                                                                    </ul>
@apiParam   (Query:)   {String}     [fields]                     Trường thông tin cần hiển thị (cách nhau dấu ",")
                                                            
@apiSuccess   (Response:)     {String}    name                      Tên dòng sản phẩm
@apiSuccess   (Response:)     {String}    code                      Mã dòng sản phẩm
@apiSuccess   (Response:)     {String}    customer_group            Mã nhóm khách hàng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id" : "62a93c67a8772cd90c3b53b1",
            "code" : "tai_khoan_39fcdcd2",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "account_id" : null,
            "created_by" : null,
            "created_time" : **********.70823,
            "customer_group" : "KHACHHANG_e51dc007",
            "name" : "TÀI KHOẢN",
            "name_ascii" : "tai khoan",
            "updated_by" : null,
            "updated_time" : **********.70824,
            "count_product" : 14
        }
    ]
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {get} {domain}/product/bank/api/v1.0/product-line/action/filter                  Lấy danh sách dòng sản phẩm theo bộ lọc (UPDATE NEW)
@apiDescription Lấy danh sách dòng theo bộ lọc (Sử dụng thay api GET action/filter/product-line)
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName ShowListProductLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {String}     [customer_group]        Mã nhóm khách hàng
@apiParam   (Query:)   {String}     [search]                     Tìm kiếm tên dòng sản phẩm
@apiParam   (Query:)   {Int}        [page]                          Tìm kiếm page (page = -1, tìm tất cả)
@apiParam   (Query:)   {Int}        [per_page]                      Số phần tử của page
@apiParam   (Query:)   {String}     [fields]                     Trường thông tin cần hiển thị(cách nhau dấu ",")
@apiParam   (Query:)   {Int}        [product_line_default]       Cờ check Nhóm khách hàng Cá nhân nếu = 1 "KHACHHANG_e51dc007"
@apiParam   (Query:)   {String}        [product_line_type]          Option lấy dữ liệu dòng sản phẩm dựa trên nhóm khách hàng
                                                                 <ul>
                                                                    <li><code>KHCN</code>: lấy dòng sản phẩm của nhóm khách hàng cá nhân. Call khi muốn lấy dữ liệu của Profile</li>
                                                                    <li><code>KHDN</code>: lấy dòng sản phẩm của nhóm khách hàng doanh nghiệp. Call khi muốn lấy dữ liệu của Company</li>
                                                                 </ul>
@apiParam   (Query:)   {String}     [sort]                       Field cần sắp xếp
@apiParam   (Query:)   {String}     [order]                      Sắp xếp theo thứ tự
                                                                    <ul>
                                                                        <li><code>asc</code>: sắp xếp tăng dần</li>
                                                                        <li><code>desc</code>: sắp xếp giảm dần(default)</li>
                                                                    </ul>
@apiParam   (Query:)   {String}     [fields]                     Trường thông tin cần hiển thị (cách nhau dấu ",")

@apiSuccess   (Response:)     {String}    name                      Tên dòng sản phẩm
@apiSuccess   (Response:)     {String}    code                      Mã dòng sản phẩm
@apiSuccess   (Response:)     {String}    customer_group            Mã nhóm khách hàng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id" : "62a93c67a8772cd90c3b53b1",
            "code" : "tai_khoan_39fcdcd2",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "account_id" : null,
            "created_by" : null,
            "created_time" : **********.70823,
            "customer_group" : "KHACHHANG_e51dc007",
            "name" : "TÀI KHOẢN",
            "name_ascii" : "tai khoan",
            "updated_by" : null,
            "updated_time" : **********.70824,
            "count_product" : 14
        }
    ]
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {post} {domain}/product/bank/api/v1.0/product-line/action/filter          Lấy danh sách dòng sản phẩm theo bộ lọc (Ignore)
@apiDescription Lấy danh sách dòng sản phẩm filter ignore danh sách id dòng sản phẩm (nếu có)
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetListProductLineFilter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)   {String}     [search]                     Tìm kiếm tên dòng sản phẩm
@apiParam   (Query:)   {Int}        [page]                       Tìm kiếm page (page = -1, tìm tất cả)
@apiParam   (Query:)   {Int}        [per_page]                   Số phần tử của page
@apiParam   (Query:)   {Int}        [product_line_default]       Cờ check Nhóm khách hàng Cá nhân nếu = 1 "KHACHHANG_e51dc007"


@apiParam   (Body:)   {String}      [customer_group]              Mã nhóm khách hàng
@apiParam   (Body:)   {String}     [sort]                        Field cần sắp xếp
@apiParam   (Body:)   {String}     [order]                      Sắp xếp theo thứ tự
                                                                    <ul>
                                                                        <li><code>asc</code>: sắp xếp tăng dần</li>
                                                                        <li><code>desc</code>: sắp xếp giảm dần(default)</li>
                                                                    </ul>
@apiParam   (Body:)   {Array}     [fields]                      Trường thông tin cần hiển thị (Default: id,name,code)
@apiParam   (Body:)   {Array}     [list_product_line_ignore]    Danh sách id dòng sản phẩm ignore

@apiSuccess   (Response:)     {String}    name                      Tên dòng sản phẩm
@apiSuccess   (Response:)     {String}    code                      Mã dòng sản phẩm
@apiSuccess   (Response:)     {String}    customer_group            Mã nhóm khách hàng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id" : "62a93c67a8772cd90c3b53b1",
            "code" : "tai_khoan_39fcdcd2",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "account_id" : null,
            "created_by" : null,
            "created_time" : **********.70823,
            "customer_group" : "KHACHHANG_e51dc007",
            "name" : "TÀI KHOẢN",
            "name_ascii" : "tai khoan",
            "updated_by" : null,
            "updated_time" : **********.70824,
            "count_product" : 14
        }
    ]
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {put} {domain}/product/bank/api/v1.0/action/product-line                  Sửa dòng sản phẩm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName UpdateProductLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)   {String}    id                      id dòng sản phẩm

@apiParam   (Body:)   {String}    name                      Tên dòng sản phẩm
@apiParam   (Body:)   {String}    customer_group           Mã nhóm khách hàng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "name": "",
        "customer_group": "",
        "code": ""
    }
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {get} {domain}/product/bank/api/v1.0/action/product-line                  Chi tiết dòng sản phẩm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetDetailProductLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {String}    id           id dòng sản phẩm

@apiSuccess   (Response:)     {String}    name                      Tên dòng sản phẩm
@apiSuccess   (Response:)     {String}    code                      Mã dòng sản phẩm
@apiSuccess   (Response:)     {String}    customer_group_code       Mã nhóm khách hàng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "name": "",
        "group_customer_code": "",
        "code": ""
    }
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {get} {domain}/product/bank/api/v1.0/product-line/actions/detail-by-code                  Chi tiết dòng sản phẩm bởi mã dòng sản phẩm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetDetailProductLineByCode

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {String}    code           Mã dòng sản phẩm dòng sản phẩm. Trong trường hợp muốn lấy nhiều thì truyền code ngăn cách nhau bởi dấu ,

@apiSuccess   (Response:)     {String}    id                        ID dòng sản phẩm
@apiSuccess   (Response:)     {String}    name                      Tên dòng sản phẩm
@apiSuccess   (Response:)     {String}    code                      Mã dòng sản phẩm
@apiSuccess   (Response:)     {String}    customer_group_code       Mã nhóm khách hàng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id": "66961fea2595edfa318bb46e",
            "code": "KHDN_BAOLANH",
            "code_ascii": "khdn_baolanh",
            "count_product": 0,
            "created_by": null,
            "created_time": **********.487702,
            "id": "66961fea2595edfa318bb46e",
            "merchant_id": "b18106d8-3397-11ef-9186-036799276e51",
            "name": "khdn_baolanh",
            "name_ascii": "khdn_baolanh",
            "product_line_type": "KHDN",
            "updated_by": null,
            "updated_time": **********.487715
        }
    ]
    "lang": "vi",
    "message": "request success"
}
"""

"""
@api {post} {domain}/product/bank/api/v1.0/action/category                  Tạo danh mục (loại sản phẩm)
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName CreateCategoryBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}    product_line_id                   Id dòng sản phẩm
@apiParam   (Body:)   {Array}     sub                     Danh sách danh mục
@apiParam   (Body:)   {String}    name                Tên danh mục
@apiParam   (Body:)   {String}    code                Mã danh mục   

@apiParamExample {json} Body example
{
    "product_line_id": "62a165a6fd729f1b02e3f8c9",
    "list_data": [
        {
            "name": "cat_1_a",
            "code": "DANHMUC_1_a",
            "sub": [
                {
                    "name": "cat_1_1_a",
                    "code": "DANHMUC1_1_a",
                    "sub": [
                        {
                            "name": "cat_1_1_1_a",
                            "code": "DANHMUC1_1_1_a"
                        },
                        {
                            "name": "cat_1_1_3_4_5_a",
                            "code": "DANHMUC1_1_3_a"
                        }
                    ]
                },
                {
                    "name": "cat_1_2_a",
                    "code": "DANHMUC1_2_a",
                    "sub": [
                        {
                            "name": "cat_1_2_1_a",
                            "code": "DANHMUC1_2_1_a"
                        },
                        {
                            "name": "cat_1_2_2_a",
                            "code": "DANHMUC1_2_2_a"
                        }
                    ]
                }
            ]
        },
        {
            "name": "cat_2_a",
            "code": "DANHMUC_2_a",
            "sub": [
                {
                    "name": "cat_2_1_a",
                    "code": "DANHMUC2_1_a",
                    "sub": [
                        {
                            "name": "cat_2_1_1_a",
                            "code": "DANHMUC2_1_1_a"
                        },
                        {
                            "name": "cat_2_1_3_4_5_a",
                            "code": "DANHMUC2_1_3_a"
                        }
                    ]
                },
                {
                    "name": "cat_2_2_a",
                    "code": "DANHMUC2_2_a",
                    "sub": [
                        {
                            "name": "cat_2_2_1_a",
                            "code": "DANHMUC2_2_1_a"
                        },
                        {
                            "name": "cat_2_2_2_a",
                            "code": "DANHMUC2_2_2_a"
                        }
                    ]
                }
            ]
        }
    ]
}

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "_id": "62a6dbfffd729f446d65beff",
            "code": "DANHMUC_1_a",
            "created_time": **********.295672,
            "id": "62a6dbfffd729f446d65beff",
            "index": 0,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_a",
            "parent_code": "DANHMUC_1_a",
            "parent_id": "62a6dbfffd729f446d65beff",
            "path": "DANHMUC_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.295677
        },
        {
            "_id": "62a6dbfffd729f446d65bf00",
            "code": "DANHMUC1_1_a",
            "created_time": **********.295709,
            "id": "62a6dbfffd729f446d65bf00",
            "index": 1,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_1_a",
            "parent_code": "DANHMUC_1_a",
            "parent_id": "62a6dbfffd729f446d65beff",
            "path": "DANHMUC_1_a#DANHMUC1_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.29571
        },
        {
            "_id": "62a6dbfffd729f446d65bf01",
            "code": "DANHMUC1_1_1_a",
            "created_time": **********.295735,
            "id": "62a6dbfffd729f446d65bf01",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_1_1_a",
            "parent_code": "DANHMUC1_1_a",
            "parent_id": "62a6dbfffd729f446d65bf00",
            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_1_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.295736
        },
        {
            "_id": "62a6dbfffd729f446d65bf02",
            "code": "DANHMUC1_1_3_a",
            "created_time": **********.295757,
            "id": "62a6dbfffd729f446d65bf02",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_1_3_4_5_a",
            "parent_code": "DANHMUC1_1_a",
            "parent_id": "62a6dbfffd729f446d65bf00",
            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_1_3_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.295758
        },
        {
            "_id": "62a6dbfffd729f446d65bf03",
            "code": "DANHMUC1_2_a",
            "created_time": **********.295776,
            "id": "62a6dbfffd729f446d65bf03",
            "index": 1,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_2_a",
            "parent_code": "DANHMUC_1_a",
            "parent_id": "62a6dbfffd729f446d65beff",
            "path": "DANHMUC_1_a#DANHMUC1_2_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.295777
        },
        {
            "_id": "62a6dbfffd729f446d65bf04",
            "code": "DANHMUC1_2_1_a",
            "created_time": **********.295795,
            "id": "62a6dbfffd729f446d65bf04",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_2_1_a",
            "parent_code": "DANHMUC1_2_a",
            "parent_id": "62a6dbfffd729f446d65bf03",
            "path": "DANHMUC_1_a#DANHMUC1_2_a#DANHMUC1_2_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.295795
        },
        {
            "_id": "62a6dbfffd729f446d65bf05",
            "code": "DANHMUC1_2_2_a",
            "created_time": **********.295811,
            "id": "62a6dbfffd729f446d65bf05",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_2_2_a",
            "parent_code": "DANHMUC1_2_a",
            "parent_id": "62a6dbfffd729f446d65bf03",
            "path": "DANHMUC_1_a#DANHMUC1_2_a#DANHMUC1_2_2_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.295812
        },
        {
            "_id": "62a6dbfffd729f446d65bf06",
            "code": "DANHMUC_2_a",
            "created_time": **********.29582,
            "id": "62a6dbfffd729f446d65bf06",
            "index": 0,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_a",
            "parent_code": "DANHMUC_2_a",
            "parent_id": "62a6dbfffd729f446d65bf06",
            "path": "DANHMUC_2_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295821
        },
        {
            "_id": "62a6dbfffd729f446d65bf07",
            "code": "DANHMUC2_1_a",
            "created_time": **********.295837,
            "id": "62a6dbfffd729f446d65bf07",
            "index": 1,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_1_a",
            "parent_code": "DANHMUC_2_a",
            "parent_id": "62a6dbfffd729f446d65bf06",
            "path": "DANHMUC_2_a#DANHMUC2_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295837
        },
        {
            "_id": "62a6dbfffd729f446d65bf08",
            "code": "DANHMUC2_1_1_a",
            "created_time": **********.295853,
            "id": "62a6dbfffd729f446d65bf08",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_1_1_a",
            "parent_code": "DANHMUC2_1_a",
            "parent_id": "62a6dbfffd729f446d65bf07",
            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295854
        },
        {
            "_id": "62a6dbfffd729f446d65bf09",
            "code": "DANHMUC2_1_3_a",
            "created_time": **********.295873,
            "id": "62a6dbfffd729f446d65bf09",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_1_3_4_5_a",
            "parent_code": "DANHMUC2_1_a",
            "parent_id": "62a6dbfffd729f446d65bf07",
            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_3_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295874
        },
        {
            "_id": "62a6dbfffd729f446d65bf0a",
            "code": "DANHMUC2_2_a",
            "created_time": **********.295891,
            "id": "62a6dbfffd729f446d65bf0a",
            "index": 1,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_2_a",
            "parent_code": "DANHMUC_2_a",
            "parent_id": "62a6dbfffd729f446d65bf06",
            "path": "DANHMUC_2_a#DANHMUC2_2_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295891
        },
        {
            "_id": "62a6dbfffd729f446d65bf0b",
            "code": "DANHMUC2_2_1_a",
            "created_time": **********.295907,
            "id": "62a6dbfffd729f446d65bf0b",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_2_1_a",
            "parent_code": "DANHMUC2_2_a",
            "parent_id": "62a6dbfffd729f446d65bf0a",
            "path": "DANHMUC_2_a#DANHMUC2_2_a#DANHMUC2_2_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295908
        },
        {
            "_id": "62a6dbfffd729f446d65bf0c",
            "code": "DANHMUC2_2_2_a",
            "created_time": **********.295924,
            "id": "62a6dbfffd729f446d65bf0c",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_2_2_a",
            "parent_code": "DANHMUC2_2_a",
            "parent_id": "62a6dbfffd729f446d65bf0a",
            "path": "DANHMUC_2_a#DANHMUC2_2_a#DANHMUC2_2_2_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295924
        }
    ]
    "message": "request success"
}
"""

"""
@api {put} {domain}/product/bank/api/v1.0/action/category                  Sửa danh mục (loại sản phẩm)
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName UpdateCategoryBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}    product_line_id                   Id dòng sản phẩm
@apiParam   (Body:)   {Array}     list_data                   Danh sách danh mục 
@apiParam   (Body:)   {Array}     sub                     Danh sách danh mục
@apiParam   (Body:)   {String}    name                Tên danh mục
@apiParam   (Body:)   {String}    code                Mã danh mục   

@apiParamExample {json} Body example
{
    "product_line_id": "62a165a6fd729f1b02e3f8c9",
    "list_data": [
        {
            "id": "62a6dbfffd729f446d65beff",
            "name": "cat_1_a",
            "code": "DANHMUC_1_a",
            "sub": [
                {
                    "id": "62a6dbfffd729f446d65bf00",
                    "name": "cat_1_1_a",
                    "code": "DANHMUC1_1_a",
                    "sub": [
                        {
                            "id": "62a6dbfffd729f446d65bf01",
                            "name": "cat_1_1_1_a",
                            "code": "DANHMUC1_1_1_a"
                        },
                        {
                            "id": "62a6dbfffd729f446d65bf02",
                            "name": "cat_1_1_3_4_5_a",
                            "code": "DANHMUC1_1_3_a"
                        },
                        {
                            "id": "62a6dbfffd729f446d65bf05",
                            "name": "cat_1_2_2_a",
                            "code": "DANHMUC1_2_2_a"
                        }
                    ]
                },
                {
                    "id": "62a6dbfffd729f446d65bf03",
                    "name": "cat_1_2_a",
                    "code": "DANHMUC1_2_a",
                    "sub": [
                        {
                            "name": "cat_1_2_3_a",
                            "code": "DANHMUC1_2_3_a"
                        }
                    ]
                }
            ]
        },
        {
            "id": "62a6dbfffd729f446d65bf06",
            "name": "cat_2_a",
            "code": "DANHMUC_2_a",
            "sub": [
                {
                    "id": "62a6dbfffd729f446d65bf07",
                    "name": "cat_2_1_a",
                    "code": "DANHMUC2_1_a",
                    "sub": [
                        {
                            "id": "62a6dbfffd729f446d65bf08",
                            "name": "cat_2_1_1_a",
                            "code": "DANHMUC2_1_1_a"
                        },
                        {
                            "id": "62a6dbfffd729f446d65bf09",
                            "name": "cat_2_1_3_4_5_a",
                            "code": "DANHMUC2_1_3_a"
                        },
                        
                        {
                            "id": "62a6dbfffd729f446d65bf0b",
                            "name": "cat_2_2_1_a",
                            "code": "DANHMUC2_2_1_a"
                        }
                    ]
                },
                {
                    "id": "62a6dbfffd729f446d65bf0a",
                    "name": "cat_2_2_a",
                    "code": "DANHMUC2_2_a",
                    "sub": [
                        {
                            "id": "62a6dbfffd729f446d65bf0c",
                            "name": "cat_2_2_2_a",
                            "code": "DANHMUC2_2_2_a"
                        }
                    ]
                }
            ]
        }
    ]
}

@apiSuccess   {Number}    path      đường dẫn code danh mục                                                          

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "_id": "62a6dbfffd729f446d65beff",
            "code": "DANHMUC_1_a",
            "created_time": **********.727455,
            "id": "62a6dbfffd729f446d65beff",
            "index": 0,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_a",
            "parent_code": "DANHMUC_1_a",
            "parent_id": "62a6dbfffd729f446d65beff",
            "path": "DANHMUC_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.727459
        },
        {
            "_id": "62a6dbfffd729f446d65bf00",
            "code": "DANHMUC1_1_a",
            "created_time": **********.727487,
            "id": "62a6dbfffd729f446d65bf00",
            "index": 1,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_1_a",
            "parent_code": "DANHMUC_1_a",
            "parent_id": "62a6dbfffd729f446d65beff",
            "path": "DANHMUC_1_a#DANHMUC1_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.727488
        },
        {
            "_id": "62a6dbfffd729f446d65bf01",
            "code": "DANHMUC1_1_1_a",
            "created_time": **********.727506,
            "id": "62a6dbfffd729f446d65bf01",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_1_1_a",
            "parent_code": "DANHMUC1_1_a",
            "parent_id": "62a6dbfffd729f446d65bf00",
            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_1_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.727507
        },
        {
            "_id": "62a6dbfffd729f446d65bf02",
            "code": "DANHMUC1_1_3_a",
            "created_time": **********.727522,
            "id": "62a6dbfffd729f446d65bf02",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_1_3_4_5_a",
            "parent_code": "DANHMUC1_1_a",
            "parent_id": "62a6dbfffd729f446d65bf00",
            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_1_3_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.727523
        },
        {
            "_id": "62a6dbfffd729f446d65bf05",
            "code": "DANHMUC1_2_2_a",
            "created_time": **********.727536,
            "id": "62a6dbfffd729f446d65bf05",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_2_2_a",
            "parent_code": "DANHMUC1_1_a",
            "parent_id": "62a6dbfffd729f446d65bf00",
            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_2_2_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.727537
        },
        {
            "_id": "62a6dbfffd729f446d65bf03",
            "code": "DANHMUC1_2_a",
            "created_time": **********.72755,
            "id": "62a6dbfffd729f446d65bf03",
            "index": 1,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_2_a",
            "parent_code": "DANHMUC_1_a",
            "parent_id": "62a6dbfffd729f446d65beff",
            "path": "DANHMUC_1_a#DANHMUC1_2_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.727551
        },
        {
            "_id": "62a6f113fd729f6820026065",
            "code": "DANHMUC1_2_3_a",
            "created_time": **********.727576,
            "id": "62a6f113fd729f6820026065",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_2_3_a",
            "parent_code": "DANHMUC1_2_a",
            "parent_id": "62a6dbfffd729f446d65bf03",
            "path": "DANHMUC_1_a#DANHMUC1_2_a#DANHMUC1_2_3_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "updated_time": **********.727577
        },
        {
            "_id": "62a6dbfffd729f446d65bf06",
            "code": "DANHMUC_2_a",
            "created_time": **********.727734,
            "id": "62a6dbfffd729f446d65bf06",
            "index": 0,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_a",
            "parent_code": "DANHMUC_2_a",
            "parent_id": "62a6dbfffd729f446d65bf06",
            "path": "DANHMUC_2_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.727736
        },
        {
            "_id": "62a6dbfffd729f446d65bf07",
            "code": "DANHMUC2_1_a",
            "created_time": **********.727757,
            "id": "62a6dbfffd729f446d65bf07",
            "index": 1,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_1_a",
            "parent_code": "DANHMUC_2_a",
            "parent_id": "62a6dbfffd729f446d65bf06",
            "path": "DANHMUC_2_a#DANHMUC2_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.727758
        },
        {
            "_id": "62a6dbfffd729f446d65bf08",
            "code": "DANHMUC2_1_1_a",
            "created_time": **********.727772,
            "id": "62a6dbfffd729f446d65bf08",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_1_1_a",
            "parent_code": "DANHMUC2_1_a",
            "parent_id": "62a6dbfffd729f446d65bf07",
            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.727773
        },
        {
            "_id": "62a6dbfffd729f446d65bf09",
            "code": "DANHMUC2_1_3_a",
            "created_time": **********.727786,
            "id": "62a6dbfffd729f446d65bf09",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_1_3_4_5_a",
            "parent_code": "DANHMUC2_1_a",
            "parent_id": "62a6dbfffd729f446d65bf07",
            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_3_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.727787
        },
        {
            "_id": "62a6dbfffd729f446d65bf0b",
            "code": "DANHMUC2_2_1_a",
            "created_time": **********.727799,
            "id": "62a6dbfffd729f446d65bf0b",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_2_1_a",
            "parent_code": "DANHMUC2_1_a",
            "parent_id": "62a6dbfffd729f446d65bf07",
            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_2_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.7278
        },
        {
            "_id": "62a6dbfffd729f446d65bf0a",
            "code": "DANHMUC2_2_a",
            "created_time": **********.727813,
            "id": "62a6dbfffd729f446d65bf0a",
            "index": 1,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_2_a",
            "parent_code": "DANHMUC_2_a",
            "parent_id": "62a6dbfffd729f446d65bf06",
            "path": "DANHMUC_2_a#DANHMUC2_2_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.727814
        },
        {
            "_id": "62a6dbfffd729f446d65bf0c",
            "code": "DANHMUC2_2_2_a",
            "created_time": **********.727826,
            "id": "62a6dbfffd729f446d65bf0c",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_2_2_a",
            "parent_code": "DANHMUC2_2_a",
            "parent_id": "62a6dbfffd729f446d65bf0a",
            "path": "DANHMUC_2_a#DANHMUC2_2_a#DANHMUC2_2_2_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.727827
        }
    ]
    "message": "request success"
}

"""

"""
@api {get} {domain}/product/bank/api/v1.0/action/category                  Chi tiết danh mục của dòng sản phẩm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetDetailCategoryBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {String}    [id]                         ID dòng sản phẩm
@apiParam   (Query:)   {String}    [product_line_ids]           Danh sách <code>ID</code> dòng sản phẩm
@apiParam   (Query:)   {String}    [search]           tên hoặc mã danh mục cần tìm kiếm
@apiParam   (Query:)   {String}    [page]           Vị trí page cần lấy dữ liệu. Set page=-1 nếu muốn lấy tất cả dữ liệu
@apiParam   (Query:)   {String}    [per_page]           Số phần tử trên một page.

@apiSuccess   (Response:)     {String}    name                      Tên danh mục 
@apiSuccess   (Response:)     {String}    code                      Mã danh mục
@apiSuccess   (Response:)     {String}    index                     Vị trí cấp của danh mục
@apiSuccess   (Response:)     {String}    path                      Đường dẫn danh mục
@apiSuccess   (Response:)     {String}    parent_code               Mã của danh mục cha
@apiSuccess   (Response:)     {String}    root_code                 Mã của danh mục gốc
@apiSuccess   (Response:)     {String}    product_line_id           Id của dòng sản phẩm
@apiSuccess   (Response:)     {Array}     sub                       Danh sách các danh mục cấp con

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id": "62a6dbfffd729f446d65beff",
            "code": "DANHMUC_1_a",
            "created_time": **********.295672,
            "id": "62a6dbfffd729f446d65beff",
            "index": 0,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_1_a",
            "parent_code": "DANHMUC_1_a",
            "parent_id": "62a6dbfffd729f446d65beff",
            "path": "DANHMUC_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_1_a",
            "sub": [
                {
                    "_id": "62a6dbfffd729f446d65bf00",
                    "code": "DANHMUC1_1_a",
                    "created_time": **********.295709,
                    "index": 1,
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "cat_1_1_a",
                    "parent_code": "DANHMUC_1_a",
                    "parent_id": "62a6dbfffd729f446d65beff",
                    "path": "DANHMUC_1_a#DANHMUC1_1_a",
                    "product_line_id": "62a165a6fd729f1b02e3f8c9",
                    "root_code": "DANHMUC_1_a",
                    "sub": [
                        {
                            "_id": "62a6dbfffd729f446d65bf01",
                            "code": "DANHMUC1_1_1_a",
                            "created_time": **********.295735,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_1_1_1_a",
                            "parent_code": "DANHMUC1_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf00",
                            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_1_1_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_1_a",
                            "sub": [],
                            "updated_time": **********.295736
                        },
                        {
                            "_id": "62a6dbfffd729f446d65bf02",
                            "code": "DANHMUC1_1_3_a",
                            "created_time": **********.295757,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_1_1_3_4_5_a",
                            "parent_code": "DANHMUC1_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf00",
                            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_1_3_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_1_a",
                            "sub": [],
                            "updated_time": **********.295758
                        },
                        {
                            "_id": "62a6dbfffd729f446d65bf05",
                            "code": "DANHMUC1_2_2_a",
                            "created_time": **********.295811,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_1_2_2_a",
                            "parent_code": "DANHMUC1_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf00",
                            "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_2_2_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_1_a",
                            "sub": [],
                            "updated_time": **********.295812
                        }
                    ],
                    "updated_time": **********.29571
                },
                {
                    "_id": "62a6dbfffd729f446d65bf03",
                    "code": "DANHMUC1_2_a",
                    "created_time": **********.295776,
                    "index": 1,
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "cat_1_2_a",
                    "parent_code": "DANHMUC_1_a",
                    "parent_id": "62a6dbfffd729f446d65beff",
                    "path": "DANHMUC_1_a#DANHMUC1_2_a",
                    "product_line_id": "62a165a6fd729f1b02e3f8c9",
                    "root_code": "DANHMUC_1_a",
                    "sub": [
                        {
                            "_id": "62a6e9f0fd729f5d140f40c8",
                            "code": "DANHMUC1_2_3_a",
                            "created_time": 1655106032.82096,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_1_2_3_a",
                            "parent_code": "DANHMUC1_2_a",
                            "parent_id": "62a6dbfffd729f446d65bf03",
                            "path": "DANHMUC_1_a#DANHMUC1_2_a#DANHMUC1_2_3_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_1_a",
                            "sub": [],
                            "updated_time": 1655106032.820975
                        }
                    ],
                    "updated_time": **********.295777
                }
            ],
            "updated_time": **********.295677
        },
        {
            "_id": "62a6dbfffd729f446d65bf06",
            "code": "DANHMUC_2_a",
            "created_time": **********.29582,
            "id": "62a6dbfffd729f446d65bf06",
            "index": 0,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_a",
            "parent_code": "DANHMUC_2_a",
            "parent_id": "62a6dbfffd729f446d65bf06",
            "path": "DANHMUC_2_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "sub": [
                {
                    "_id": "62a6dbfffd729f446d65bf07",
                    "code": "DANHMUC2_1_a",
                    "created_time": **********.295837,
                    "index": 1,
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "cat_2_1_a",
                    "parent_code": "DANHMUC_2_a",
                    "parent_id": "62a6dbfffd729f446d65bf06",
                    "path": "DANHMUC_2_a#DANHMUC2_1_a",
                    "product_line_id": "62a165a6fd729f1b02e3f8c9",
                    "root_code": "DANHMUC_2_a",
                    "sub": [
                        {
                            "_id": "62a6dbfffd729f446d65bf08",
                            "code": "DANHMUC2_1_1_a",
                            "created_time": **********.295853,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_2_1_1_a",
                            "parent_code": "DANHMUC2_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf07",
                            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_1_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_2_a",
                            "sub": [],
                            "updated_time": **********.295854
                        },
                        {
                            "_id": "62a6dbfffd729f446d65bf09",
                            "code": "DANHMUC2_1_3_a",
                            "created_time": **********.295873,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_2_1_3_4_5_a",
                            "parent_code": "DANHMUC2_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf07",
                            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_3_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_2_a",
                            "sub": [],
                            "updated_time": **********.295874
                        },
                        {
                            "_id": "62a6dbfffd729f446d65bf0b",
                            "code": "DANHMUC2_2_1_a",
                            "created_time": **********.295907,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_2_2_1_a",
                            "parent_code": "DANHMUC2_1_a",
                            "parent_id": "62a6dbfffd729f446d65bf07",
                            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_2_1_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_2_a",
                            "sub": [],
                            "updated_time": **********.295908
                        }
                    ],
                    "updated_time": **********.295837
                },
                {
                    "_id": "62a6dbfffd729f446d65bf0a",
                    "code": "DANHMUC2_2_a",
                    "created_time": **********.295891,
                    "index": 1,
                    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                    "name": "cat_2_2_a",
                    "parent_code": "DANHMUC_2_a",
                    "parent_id": "62a6dbfffd729f446d65bf06",
                    "path": "DANHMUC_2_a#DANHMUC2_2_a",
                    "product_line_id": "62a165a6fd729f1b02e3f8c9",
                    "root_code": "DANHMUC_2_a",
                    "sub": [
                        {
                            "_id": "62a6dbfffd729f446d65bf0c",
                            "code": "DANHMUC2_2_2_a",
                            "created_time": **********.295924,
                            "index": 2,
                            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                            "name": "cat_2_2_2_a",
                            "parent_code": "DANHMUC2_2_a",
                            "parent_id": "62a6dbfffd729f446d65bf0a",
                            "path": "DANHMUC_2_a#DANHMUC2_2_a#DANHMUC2_2_2_a",
                            "product_line_id": "62a165a6fd729f1b02e3f8c9",
                            "root_code": "DANHMUC_2_a",
                            "sub": [],
                            "updated_time": **********.295924
                        }
                    ],
                    "updated_time": **********.295891
                }
            ],
            "updated_time": **********.295821
        }
    ],
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {post} {domain}/product/bank/api/v1.0/action/product                  Tạo sản phẩm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName CreateProductBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}      name                        Tên sản phẩm
@apiParam   (Body:)   {String}      code                        Mã sản phẩm
@apiParam   (Body:)   {String}      customer_group         Mã nhóm khách hàng
@apiParam   (Body:)   {String}      product_line             Id dòng sản phẩm
@apiParam   (Body:)   {Array}       product_type                  Danh sách id danh mục
@apiParam   (Body:)   {Array}       avatar_ids                  Danh sách ảnh avatar
@apiParam   (Body:)   {Boolean}     status_display_product                  Trạng thái sản phẩm


@apiParamExample {json} Body example
{
    "product_line": "62a16682fd729f1bf1484019",
    "name": "product 456",
    "product_code": "product_456",
    "customer_group": "KHACHHANG_77b2081f",
    "status_display_product": True,
    "product_type": ["629f787cfd729f242766d54b", "629f787cfd729f242766d54c", "629f787cfd729f242766d54d"],
    "avatar_ids": []
}       


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "_id": "62a1ba15fd729f8060fd8001",
        "id": "62a1ba15fd729f8060fd8001",
        "created_time": "2022-06-09T16:15:01Z",
        "customer_group": "KHACHHANG_77b2081f",
        "id": "62a1ba15fd729f8060fd8001",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "name": "product 456",
        "product_code": "product_456",
        "product_line": "62a16682fd729f1bf1484019",
        "product_type": [
            "629f787cfd729f242766d54b",
            "629f787cfd729f242766d54c",
            "629f787cfd729f242766d54d"
        ],
        "avatar_ids": [],
        "status_display_product": True,
        "updated_time": "2022-06-09T16:15:01Z"
    },
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {post} {domain}/product/bank/api/v1.0/upsert/product/internal                  Tạo sản phẩm bank nội bộ
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName UpsertProductBankInternal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}      [table]                 Key phân biệt bảng WAY4 và LENDING, chỉ nhập 2 giá trị
                                                            <ul>
                                                                <li><code>way4_contract</code></li>
                                                                <li><code>lending</code></li>
                                                            </ul> 
@apiParam   (Body:)   {String}      product_code             Mã sản phẩm
@apiParam   (Body:)   {String}      [customer_group]         Mã nhóm khách hàng (default: key của Khách hàng cá nhân)
@apiParam   (Query:)   {String}     [product_line_type]          Option lấy dữ liệu dòng sản phẩm dựa trên nhóm khách hàng
                                                                 <ul>
                                                                    <li><code>KHCN</code>: lấy dòng sản phẩm của nhóm khách hàng cá nhân. Call khi muốn lấy dữ liệu của Profile</li>
                                                                    <li><code>KHDN</code>: lấy dòng sản phẩm của nhóm khách hàng doanh nghiệp. Call khi muốn lấy dữ liệu của Company</li>
                                                                 </ul>
@apiParam   (Body:)   {String}      [product_line]             Id dòng sản phẩm
@apiParam   (Body:)   {Array}       [product_type]                  Danh sách id danh mục
@apiParam   (Body:)   {Boolean}     status_display_product                  Trạng thái sản phẩm 


@apiParamExample {json} Body example
{
    "table": "lending",
    "product_line": "62a16682fd729f1bf1484019",
    "name": "product 456",
    "product_code": "product_456",
    "customer_group": "KHACHHANG_77b2081f",
    "status_display_product": True,
    "product_type": ["629f787cfd729f242766d54b", "629f787cfd729f242766d54c", "629f787cfd729f242766d54d"]
}       


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "_id": "62a1ba15fd729f8060fd8001",
        "id": "62a1ba15fd729f8060fd8001",
        "created_time": "2022-06-09T16:15:01Z",
        "customer_group": "KHACHHANG_77b2081f",
        "id": "62a1ba15fd729f8060fd8001",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "name": "product 456",
        "product_code": "product_456",
        "product_line": "62a16682fd729f1bf1484019",
        "product_type": [
            "629f787cfd729f242766d54b",
            "629f787cfd729f242766d54c",
            "629f787cfd729f242766d54d"
        ],
        "status_display_product": True,
        "updated_time": "2022-06-09T16:15:01Z"
    },
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {post} {domain}/product/bank/api/v1.0/action/filter/product                  Lấy danh sách sản phẩm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetFilterProductBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}      search                      Tìm kiếm tên sản phẩm
@apiParam   (Body:)   {Array}       filters                     Danh sách các điều kiện bộ lọc 
@apiParam   (Body:)   {Array}       fields                      Danh sách field cần trả về

@apiParam   (filters:)   {Array}       values                      Danh sách field cần trả về
@apiParam   (filters:)   {String}       criteria_key               Các key để filter:
                                                                    <ul>
                                                                        <li><code>cri_product_line</code>: Dòng sản phẩm</li> 
                                                                        <li><code>cri_name</code>: Tên sản phẩm</li> 
                                                                        <li><code>cri_code</code>: Mã sản phẩm</li> 
                                                                        <li><code>cri_customer_group</code>: nhóm khách hàng</li> 
                                                                        <li><code>cri_status</code>: trạng thái</li> 
                                                                        <li><code>cri_created_time</code>: thời gian tạo</li> 
                                                                        <li><code>cri_updated_time</code>: thời gian cập nhật</li> 
                                                                    </ul>
@apiParam   (filters:)   {String}       operator_key                      Danh sách field cần trả về

@apiParam   (Query:)   {String}      [sort]                     Tên field trường thông tin cần sắp xếp
@apiParam   (Query:)   {String}       [order]                    Kiểu sắp xếp 
                                                                        (
                                                                            <code>asc</code>: sắp xếp tăng dần
                                                                            <code>desc</code>: sắp xếp giảm dần(default)
                                                                        )
@apiParam   (Query:)   {String}       [after_token]              Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)   {String}       [before_token]              Token để request lấy dữ liệu trang trước đó. 
                                                                    Nếu trong danh sách tham số gửi lên có <code>after_token</code> 
                                                                    thì ưu tiên xử lý <code>after_token</code>
@apiParam   (Query:)   {Integer}       [per_page]                 Số phần tử trên một page


@apiParamExample {json} Body example
{
    "product_line_id": "",
    "search": "",
    "filters": [
        {
            "criteria_key" : "cri_product_line",
            "operator_key" : "op_is_in",
            "values" : [ 
                "kanh_123_code"
            ]
        }, 
        {
            "criteria_key" : "cri_customer_group",
            "operator_key" : "op_is_in",
            "values" : [ 
                ""
            ]
        }
    ],
    "fields": ["name", "code"]
}        



@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id" : "62a02206fd729f6f66b43643",
            "id" : "62a02206fd729f6f66b43643",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "created_time" : **********.23213,
            "updated_time" : **********.23214,
            "product_line" : "62a01b32fd729f64b2175e97",
            "name" : "san pham",
            "product_code" : "san_pham",
            "customer_group" : "KHACHHANG_77b2081f",
            "status_display_product": True,
            "avatar_ids": []
        },
        ...
    ],
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {post} {domain}/product/bank/api/v1.0/products/action/filter                  Lấy danh sách sản phẩm theo bộ lọc
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetProductBankFilter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}      [search]                      Tìm kiếm tên sản phẩm
@apiParam   (Body:)   {Array}       [filters]                     Danh sách các điều kiện bộ lọc 
@apiParam   (Body:)   {Array}       [list_product_ignore]         Danh sách id sản phẩm ignore  
@apiParam   (Body:)   {Array}       [fields]                      Danh sách field cần trả về

@apiParam   (filters:)   {Array}       values                      Danh sách field cần trả về
@apiParam   (filters:)   {String}       criteria_key               Các key để filter:
                                                                    <ul>
                                                                        <li><code>cri_product_line</code>: Dòng sản phẩm</li> 
                                                                        <li><code>cri_name</code>: Tên sản phẩm</li> 
                                                                        <li><code>cri_code</code>: Mã sản phẩm</li> 
                                                                        <li><code>cri_customer_group</code>: nhóm khách hàng</li> 
                                                                        <li><code>cri_status</code>: trạng thái</li> 
                                                                        <li><code>cri_created_time</code>: thời gian tạo</li> 
                                                                        <li><code>cri_updated_time</code>: thời gian cập nhật</li> 
                                                                    </ul>
@apiParam   (filters:)   {String}       operator_key                      Danh sách field cần trả về

@apiParam   (Query:)   {String}      [sort]                     Tên field trường thông tin cần sắp xếp
@apiParam   (Query:)   {String}       [order]                    Kiểu sắp xếp 
                                                                        (
                                                                            <code>asc</code>: sắp xếp tăng dần
                                                                            <code>desc</code>: sắp xếp giảm dần(default)
                                                                        )
@apiParam   (Query:)   {String}       [after_token]              Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)   {String}       [before_token]              Token để request lấy dữ liệu trang trước đó. 
                                                                    Nếu trong danh sách tham số gửi lên có <code>after_token</code> 
                                                                    thì ưu tiên xử lý <code>after_token</code>
@apiParam   (Query:)   {Integer}       [per_page]                 Số phần tử trên một page


@apiParamExample {json} Body example
{
    "product_line_id": "",
    "search": "",
    "filters": [
        {
            "criteria_key" : "cri_product_line",
            "operator_key" : "op_is_in",
            "values" : [ 
                "kanh_123_code"
            ]
        }, 
        {
            "criteria_key" : "cri_customer_group",
            "operator_key" : "op_is_in",
            "values" : [ 
                ""
            ]
        }
    ],
    "fields": ["name", "code"]
}        



@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "_id" : "62a02206fd729f6f66b43643",
            "id" : "62a02206fd729f6f66b43643",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "created_time" : **********.23213,
            "updated_time" : **********.23214,
            "product_line" : "62a01b32fd729f64b2175e97",
            "name" : "san pham",
            "product_code" : "san_pham",
            "customer_group" : "KHACHHANG_77b2081f",
            "status_display_product": True,
            "avatar_ids": []
        },
        ...
    ],
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {put} {domain}/product/bank/api/v1.0/action/product                  Sửa sản phẩm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName UpdateProductBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {String}      product_id                        Id sản phẩm cần cập nhật

@apiParam   (Body:)   {String}      name                        Tên sản phẩm
@apiParam   (Body:)   {Array}       product_type                Danh sách id danh mục
@apiParam   (Body:)   {Array}       avatar_ids                     Danh sách ảnh mới
@apiParam   (Body:)   {Boolean}     status_display_product                      Trạng thái sản phẩm 


@apiParamExample {json} Body example
{
    "name": "cat_1",
    "product_type": ["629d6a1a6990defe80df75bf"],
    "status_display_product": True,
    "avatar_ids": []
}        


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "_id": "629f787cfd729f242766d54a",
        "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "id" : "62a1ba15fd729f8060fd8001",
        "created_time" : 1654766101.45016,
        "updated_time" : 1654766101.45016,
        "product_line" : "62a16682fd729f1bf1484019",
        "name" : "product 45678",
        "product_code" : "product_456",
        "customer_group" : "KHACHHANG_77b2081f",
        "status_display_product": True,
        "product_type" : [ 
            "629f787cfd729f242766d54b", 
            "629f787cfd729f242766d54c", 
            "629f787cfd729f242766d54d"
        ],
        "avatar_ids": []
    }
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {post} {domain}/product/bank/api/v1.0/action/change-status                  Chuyển trạng thái list sản phẩm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName UpdateStatusProductBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {Array}       ids                 Danh sách id sản phẩm
@apiParam   (Body:)   {Boolean}     status_display_product              Trạng thái cần chuyển
                                                            <ul>
                                                                <li><code>True</code>: hiển thị</li>
                                                                <li><code>False</code>: ẩn</li>
                                                            </ul>


@apiParamExample {json} Body example
{
    "ids": ["62a02206fd729f6f66b43643", "62a02314fd729f714730a100"],
    "status_display_product": True
}        


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "status_display_product": True
    }
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {POST} {domain}/product/bank/api/v1.0/action/get_product_by_category                  Lấy danh sách sản phẩm theo danh mục
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetProductBankByCategory

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)   {String}      [search]                           Tên sản phẩm cần tìm kiếm
@apiParam   (Query:)   {Int}         [per_page]                         Số phần tử của trang
@apiParam   (Query:)   {String}      [sort]                             Tên field trường thông tin cần sắp xếp
@apiParam   (Query:)   {String}      [after_token]                      Token để request lấy dữ liệu trang tiếp theo
@apiParam   (Query:)   {String}      [order]                            Kiểu sắp xếp ( asc: sắp xếp tăng dần desc: sắp xếp giảm dần(default) )
@apiParam   (Query:)   {String}      [get_product_end]                  Nếu = 1 thì sẽ lấy sản phẩm của danh mục cấp nhỏ nhất

@apiParam   (Body:)   {Array}        [category_ids]                     Danh sách id danh mục cần tìm sản phẩm
@apiParam   (Body:)   {Array}        [list_product_ignore]              Danh sách id sản phẩm ignore
@apiParam   (Body:)   {String}       [product_line_id]                  Id dòng sản phẩm cần tìm
@apiParam   (Body:)   {Array}        [product_line_ids]                 Danh sách ID dòng sản phẩm cần tìm
@apiParam   (Body:)   {Boolean}      [status_display_product]           Trạng thái sản phẩm hiển thị(True/False) 
@apiParam   (Body:)   {Array}        [fields]                           Danh sách field cần hiển thị


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "created_time": 1654766101.45016,
            "customer_group": "KHACHHANG_77b2081f",
            "id": "62a1ba15fd729f8060fd8001",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "product 45678",
            "product_code": "product_456",
            "product_line": "62a16682fd729f1bf1484019",
            "product_type": [
                "629f787cfd729f242766d54b",
                "629f787cfd729f242766d54c",
                "629f787cfd729f242766d54d"
            ],
            "avatar_ids": [],
            "status": 1,
            "updated_time": 1654766101.45016
        },
        {
            "created_time": **********.642189,
            "customer_group": "KHACHHANG_77b2081f",
            "id": "62a1b9f1fd729f8060fd8000",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "product 123",
            "product_code": "product_123",
            "product_line": "62a16682fd729f1bf1484019",
            "product_type": [
                "629f787cfd729f242766d54b",
                "629f787cfd729f242766d54c",
                "629f787cfd729f242766d54d"
            ],
            "avatar_ids": [],
            "status": 1,
            "updated_time": **********.642199
        }
    ],
    "lang": "vi",
    "message": "request success",
    "paging": {
        "page": 1,
        "per_page": 3,
        "total_item": 2,
        "total_page": 1
    }
}

"""

"""
@api {post} {domain}/product/bank/api/v1.0/action/file-attachment             Thêm một file đính kèm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName  CreateFileAttachment
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(FORM:)	        {String}        product_id                   <code>ID</code> định danh sản phẩm                                                                        
@apiParam	(FORM:)			{Binary}	    file		                File upload


@apiSuccess (data)      {String}         id                       <code>ID</code> file upload lên hệ thống
@apiSuccess (data)      {Array}          type_media               Loại Media
@apiSuccess (data)      {String}         title                    Tên file upload
@apiSuccess (data)      {String}         format_file              Định dạng của file
@apiSuccess (data)      {String}         url                      URL file
@apiSuccess (data)      {String}         capacity                 Dung lượng file
@apiSuccess (data)      {String}         local_path               Đường dẫn vậy lý của file
@apiSuccess (data)      {String}         merchant_id              Định danh tenant upload file
@apiSuccess (data)      {String}         product_id               Định danh sản phẩm

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "620dc147dfd20bf34ac6954f",
        "action_time": 1645043415.593171,
        "capacity": 1082923,
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "filename": "1645068615_0E0A7559.jpg",
        "format_file": "image/jpeg",
        "id": "620dc147dfd20bf34ac6954f",
        "local_path": "/Users/<USER>/workspace/mobio/Module-Ticket/ticket/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "product_id": "",
        "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/1645068615_0E0A7559.jpg"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {get} {domain}/product/bank/api/v1.0/action/file-attachment             Lấy file đính kèm theo sản phẩm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName  GetListFileAttachment
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Query:)	        {String}        product_id                   <code>ID</code> định danh sản phẩm

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "_id": "62ab103bfd729f8898b4b7ad",
            "action_time": **********.853671,
            "created_by": "",
            "filename": "s8high_800x600.jpg",
            "format_file": "image/jpeg",
            "id": "62ab103bfd729f8898b4b7ad",
            "local_path": "/home/<USER>/PycharmProject/product/static/972e6e1d-8891-4fdb-9d02-8a7855393298/upload/1655377978_s8high_800x600.jpg",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "product_id": "62aad8e1a46c7b001029cd55",
            "status": 1,
            "url": "https://t1.mobio.vn/static/972e6e1d-8891-4fdb-9d02-8a7855393298/upload/1655377978_s8high_800x600.jpg"
        },
        ...
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {delete} {domain}/product/bank/api/v1.0/action/file-attachment             Xoá một file đính kèm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName  DeleteFileAttachment
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Query:)	        {String}        ids                   Danh sách id file (cách nhau dấu ",")

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {post} {domain}/product/bank/api/v1.0/action/get-category-by-ids        Lấy danh sách chi tiết loại sản phẩm theo ids
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName  GetListCategoryBankByIds
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Body:)	        {Array}        data_ids                   Danh sách id loại sản phẩm 
@apiParam	(Body:)	        {Array}        [fields]                Danh sách field hiển thị


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "_id" : "629f787cfd729f242766d54a",
            "name" : "cat_1",
            "code" : "DANHMUC_1",
            "root_code" : "DANHMUC_1",
            "path" : "DANHMUC_1",
            "index" : 0,
            "parent_code" : "DANHMUC_1",
            "product_line_id" : "629d6a1a6990defe80df75bf",
            "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "created_time" : **********.55202,
            "updated_time" : **********.55202
        },
        {
            "_id" : "629f787cfd729f242766d54b",
            "name" : "cat_1_1",
            "code" : "DANHMUC1_1",
            "root_code" : "DANHMUC_1",
            "path" : "DANHMUC_1#DANHMUC1_1",
            "index" : 1,
            "parent_code" : "DANHMUC_1",
            "product_line_id" : "629d6a1a6990defe80df75bf",
            "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "created_time" : **********.55204,
            "updated_time" : **********.55204
        }
    ]
    "message": "request thành công."
}
"""

"""
@api {post} {domain}/product/bank/api/v1.0/action/get-product-by-ids        Lấy danh sách chi tiết sản phẩm theo ids
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName  GetListProductBankByIds
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)   {Int}         [per_page]                         Số phần tử của trang
@apiParam   (Query:)   {String}      [sort]                             Tên field trường thông tin cần sắp xếp
@apiParam   (Query:)   {String}      [page]                      Vị trí trang cần hiển thị
@apiParam   (Query:)   {String}      [order]                            Kiểu sắp xếp ( asc: sắp xếp tăng dần desc: sắp xếp giảm dần(default) )

@apiParam	(Body:)	        {Array}        ids                   Danh sách id sản phẩm
@apiParam	(Body:)	        {Array}        [fields]                Danh sách field hiển thị

@apiSuccess (Data)      {String}         product_line                 Id dòng sản phẩm  
@apiSuccess (Data)      {String}         name                         Tên sản phẩm
@apiSuccess (Data)      {String}         product_code                 Mã sản phẩm
@apiSuccess (Data)      {String}         customer_group               Mã nhóm khách hàng
@apiSuccess (Data)      {Array}          product_type                 Danh sách Id loại sản phẩm



@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "data": [
        {
            "_id" : "62a1b9f1fd729f8060fd8000",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "id" : "62a1b9f1fd729f8060fd8000",
            "created_time" : **********.64219,
            "updated_time" : **********.6422,
            "product_line" : "62a16682fd729f1bf1484019",
            "name" : "product 123",
            "product_code" : "product_123",
            "customer_group" : "KHACHHANG_77b2081f",
            "status_display_product": True,
            "product_type" : [ 
                "629f787cfd729f242766d54b", 
                "629f787cfd729f242766d54c", 
                "629f787cfd729f242766d54d"
            ]
        },
        {
            "_id" : "62a1ba15fd729f8060fd8001",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "id" : "62a1ba15fd729f8060fd8001",
            "created_time" : 1654766101.45016,
            "updated_time" : 1654766101.45016,
            "product_line" : "62a16682fd729f1bf1484019",
            "name" : "product 45678",
            "product_code" : "product_456",
            "customer_group" : "KHACHHANG_77b2081f",
            "status_display_product": True,
            "product_type" : [ 
                "629f787cfd729f242766d54b", 
                "629f787cfd729f242766d54c", 
                "629f787cfd729f242766d54d"
            ]
        }
    ]
    "message": "request thành công."
}
"""

"""
@api {get} {domain}/product/bank/api/v1.0/action/get-tree-product        Lấy danh sách cây sản phẩm theo ID sản phẩm, ID loại sp
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName  GetTreeProductBank
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Query:)	        {String}        product_id                   Id sản phẩm 
@apiParam	(Query:)	        {String}        product_type_id              Id loại sản phẩm

@apiSuccess (Data)      {String}         product_line_id              Id dòng sản phẩm  
@apiSuccess (Data)      {String}         name                         Tên sản phẩm
@apiSuccess (Data)      {String}         parent_code                  Mã danh mục cha
@apiSuccess (Data)      {String}         root_code                    Mã danh mục nguồn
@apiSuccess (Data)      {String}         path                         Đường dẫn danh mục



@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "data": {
        "_id": "62a2be63fd729f8b72d45428",
        "code": "dm_1",
        "created_time": **********.022718,
        "id": "62a2be63fd729f8b72d45428",
        "index": 0,
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "name": "Danh mục cấp 1",
        "parent_code": "dm_1",
        "path": "dm_1",
        "product_line_id": "62a16682fd729f1bf1484019",
        "root_code": "dm_1",
        "sub": [
            {
                "_id": "62a2be63fd729f8b72d45429",
                "code": "dm_1_1",
                "created_time": **********.022752,
                "index": 1,
                "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                "name": "Danh mục cấp 1.1",
                "parent_code": "dm_1",
                "path": "dm_1#dm_1_1",
                "product_line_id": "62a16682fd729f1bf1484019",
                "root_code": "dm_1",
                "sub": [
                    {
                        "_id": "62a2be63fd729f8b72d4542a",
                        "code": "dm_1_1_1",
                        "created_time": **********.022769,
                        "index": 2,
                        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
                        "name": "Danh mục cấp 1.1.1",
                        "parent_code": "dm_1_1",
                        "path": "dm_1#dm_1_1#dm_1_1_1",
                        "product_line_id": "62a16682fd729f1bf1484019",
                        "root_code": "dm_1",
                        "sub": [],
                        "updated_time": **********.02277
                    }
                ],
                "updated_time": **********.022754
            }
        ],
        "updated_time": **********.022721
    }
    "message": "request thành công."
}
"""

"""
@api {post} {domain}/product/bank/api/v1.0/action/get-product-line-by-ids                  Lấy danh sách dòng sản phẩm theo ids
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetListProductLineByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header



@apiParam   (Body:)   {Array}     [fields]                   Trường thông tin cần hiển thị
@apiParam   (Body:)   {Array}     data_ids                        Danh sách id dòng sản phẩm cần tìm

@apiSuccess   (Response:)     {String}    name                      Tên dòng sản phẩm
@apiSuccess   (Response:)     {String}    code                      Mã dòng sản phẩm
@apiSuccess   (Response:)     {String}    customer_group_code       Mã nhóm khách hàng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "name": "",
            "group_customer_code": "",
            "code": ""
        }
    ]
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {get} {domain}/product/bank/api/v1.0/action/get-list-sub-category                  Lấy danh sách danh mục con theo danh mục cha
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetSubCategoryChildByParent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header



@apiParam   (Query:)   {String}     category_id                 Id danh mục cha
@apiParam   (Query:)   {String}     product_line_id             Id dòng sản phẩm


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
         {
            "_id": "62a6dbfffd729f446d65bf07",
            "code": "DANHMUC2_1_a",
            "created_time": **********.295837,
            "id": "62a6dbfffd729f446d65bf07",
            "index": 1,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_1_a",
            "parent_code": "DANHMUC_2_a",
            "parent_id": "62a6dbfffd729f446d65bf06",
            "path": "DANHMUC_2_a#DANHMUC2_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295837
        },
        {
            "_id": "62a6dbfffd729f446d65bf08",
            "code": "DANHMUC2_1_1_a",
            "created_time": **********.295853,
            "id": "62a6dbfffd729f446d65bf08",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_1_1_a",
            "parent_code": "DANHMUC2_1_a",
            "parent_id": "62a6dbfffd729f446d65bf07",
            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295854
        },
        {
            "_id": "62a6dbfffd729f446d65bf09",
            "code": "DANHMUC2_1_3_a",
            "created_time": **********.295873,
            "id": "62a6dbfffd729f446d65bf09",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_1_3_4_5_a",
            "parent_code": "DANHMUC2_1_a",
            "parent_id": "62a6dbfffd729f446d65bf07",
            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_3_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295874
        },
        {
            "_id": "62a6dbfffd729f446d65bf0b",
            "code": "DANHMUC2_2_1_a",
            "created_time": **********.295907,
            "id": "62a6dbfffd729f446d65bf0b",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_2_1_a",
            "parent_code": "DANHMUC2_1_a",
            "parent_id": "62a6dbfffd729f446d65bf07",
            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_2_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295908
        }
    ]
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {get} {domain}/product/bank/api/v1.0/category-sub/<category_id>/category-parent    Lấy danh sách danh mục cha theo id danh mục con
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetListCategoryParentBySub

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)   {String}     [fields]                 Danh sách field hiển thị (Cách nhau dấu ,)
@apiParam   (Query:)   {String}     [ignore_self]            <code>on:</code> Không trả về data category request,
                                                            <code>off:</code> Ngược lại (<code>Default</code>) 

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
         {
            "_id": "62a6dbfffd729f446d65bf07",
            "code": "DANHMUC2_1_a",
            "created_time": **********.295837,
            "id": "62a6dbfffd729f446d65bf07",
            "index": 1,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_1_a",
            "parent_code": "DANHMUC_2_a",
            "parent_id": "62a6dbfffd729f446d65bf06",
            "path": "DANHMUC_2_a#DANHMUC2_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295837
        },
        {
            "_id": "62a6dbfffd729f446d65bf08",
            "code": "DANHMUC2_1_1_a",
            "created_time": **********.295853,
            "id": "62a6dbfffd729f446d65bf08",
            "index": 2,
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "cat_2_1_1_a",
            "parent_code": "DANHMUC2_1_a",
            "parent_id": "62a6dbfffd729f446d65bf07",
            "path": "DANHMUC_2_a#DANHMUC2_1_a#DANHMUC2_1_1_a",
            "product_line_id": "62a165a6fd729f1b02e3f8c9",
            "root_code": "DANHMUC_2_a",
            "updated_time": **********.295854
        }
    ]
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {post} {domain}/product/bank/api/v1.0/action/get-detail-by-ids-codes                  Lấy thông tin danh mục hoặc sản phẩm hoặc dòng sản phẩm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetDetailInfoBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header



@apiParam   (Body:)   {Array}     list_id               Danh sách id cần tìm
@apiParam   (Body:)   {Array}    list_code              Danh sách code cần tìm

@apiSuccess   (Response:)     {Array}    products                      Tên dòng sản phẩm
@apiSuccess   (Response:)     {Array}    product_lines                      Mã dòng sản phẩm
@apiSuccess   (Response:)     {Array}    product_types       Mã nhóm khách hàng

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "products": [
                {
                    "_id": "62a3006bfd729fc042dfd7d6",
                    "customer_group": "KHACHHANG_77b2081f",
                    "id": "62a3006bfd729fc042dfd7d6",
                    "name": "product_321",
                    "product_code": "product_321",
                    "product_line": "62a16682fd729f1bf1484019",
                    "product_type": [
                        "62a2be63fd729f8b72d45428",
                        "62a2be63fd729f8b72d45429",
                        "62a2be63fd729f8b72d4542a"
                    ],
                    "status_display_product": True,
                }
            ]
        },
        {
            "product_lines": [
                {
                    "_id": "62a703d7894cb30010e818db",
                    "code": "ttttttt",
                    "customer_group": "KHACHHANG_6d49bcef",
                    "id": "62a703d7894cb30010e818db",
                    "name": "tttt",
                    "name_ascii": "tttt"
                }
            ]
        },
        {
            "product_types": [
                {
                    "_id": "62a6dbfffd729f446d65bf00",
                    "code": "DANHMUC1_1_a",
                    "id": "62a6dbfffd729f446d65bf00",
                    "index": 1,
                    "name": "cat_1_1_a",
                    "parent_code": "DANHMUC_1_a",
                    "parent_id": "62a6dbfffd729f446d65beff",
                    "path": "DANHMUC_1_a#DANHMUC1_1_a",
                    "product_line_id": "62a165a6fd729f1b02e3f8c9",
                    "root_code": "DANHMUC_1_a"
                },
                {
                    "_id": "62a6dbfffd729f446d65bf01",
                    "code": "DANHMUC1_1_1_a",
                    "id": "62a6dbfffd729f446d65bf01",
                    "index": 2,
                    "name": "cat_1_1_1_a",
                    "parent_code": "DANHMUC1_1_a",
                    "parent_id": "62a6dbfffd729f446d65bf00",
                    "path": "DANHMUC_1_a#DANHMUC1_1_a#DANHMUC1_1_1_a",
                    "product_line_id": "62a165a6fd729f1b02e3f8c9",
                    "root_code": "DANHMUC_1_a"
                }
            ]
        }
    ]
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {get} {domain}/product/bank/api/v1.0/action/get-detail-path-product        Lấy thông tin chi tiết đường dẫn product
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName  GetDetailPathProductBank
@apiUse merchant_id_header
@apiUse json_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Query:)	        {String}        product_id                   Id sản phẩm 

@apiSuccess (Data)      {Object}         product_line               Thông tin dòng sản phẩm  
@apiSuccess (Data)      {String}         customer_group             Nhóm khách hàng 
 
@apiSuccess (Data)      {Object}         product_type               Thông tin loại sản phẩm
@apiSuccess (Data)      {String}         parent_id                  Id danh mục cha 
@apiSuccess (Data)      {String}         root_code                  Mã danh mục gốc
@apiSuccess (Data)      {String}         path                       Chuỗi đường dẫn danh mục
@apiSuccess (Data)      {Integer}        index                      Vị trí cấp danh mục
@apiSuccess (Data)      {String}         product_line_id            Id dòng sản phẩm
@apiSuccess (Data)      {Object}         product                    Thông tin sản phẩm
@apiSuccess (Data)      {String}         product_code               Mã sản phẩm
@apiSuccess (Data)      {Boolean}        status_display_product     Trạng thái sản phẩm




@apiSuccessExample json Response: HTTP/1.1 200 OK
{
    "code": 200,
    "lang": "vi",
    "data": {
        "product_line": {
            "_id" : "62aab22d8de5ef000fe7e7f8",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "created_time" : 1655353901.29157,
            "updated_time" : 1655353901.29158,
            "customer_group" : "KHACHHANG_e51dc007",
            "name" : "Ánh 3 ",
            "code" : "anh3",
            "name_ascii" : "anh 3",
            "created_by" : "0f5be325-bd84-420b-9e03-8a7161404233",
            "updated_by" : "0f5be325-bd84-420b-9e03-8a7161404233"
        },
        "product_type": [
            {       //cấp 1 
                "_id" : "62aab383d9098d000f88fd63",
                "name" : "SP cấp 1",
                "code" : "cap1",
                "parent_id" : "62aab383d9098d000f88fd63",
                "root_code" : "cap1",
                "path" : "cap1",
                "index" : 0,
                "parent_code" : "cap1.3",
                "product_line_id" : "62aab22d8de5ef000fe7e7f8",
                "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
                "created_time" : 1655354243.62389,
                "updated_time" : 1655354243.6239
            },
            {       // cấp 2
                "_id" : "62aab383d9098d000f88fd64",
                "name" : "SP cấp 1.1",
                "code" : "cap1.1",
                "parent_id" : "62aab383d9098d000f88fd63",
                "root_code" : "cap1",
                "path" : "cap1#cap1.1",
                "index" : 1,
                "parent_code" : "cap1",
                "product_line_id" : "62aab22d8de5ef000fe7e7f8",
                "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
                "created_time" : 1655354243.62389,
                "updated_time" : 1655354243.6239
            }
        ],
        "product": {
            "_id" : "62a1b9f1fd729f8060fd8000",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "id" : "62a1b9f1fd729f8060fd8000",
            "created_time" : **********.64219,
            "updated_time" : **********.6422,
            "product_line" : "62aab22d8de5ef000fe7e7f8",
            "name" : "product 123",
            "product_code" : "product_123",
            "customer_group" : "KHACHHANG_77b2081f",
            "status_display_product": True,
            "product_type" : [ 
                "62aab383d9098d000f88fd63", 
                "62aab383d9098d000f88fd64"
            ]
        }
    }
    "message": "request thành công."
}
"""

"""
@api {post} {domain}/product/bank/api/v1.0/action/filter/check-product-exist                  Check danh sách sản phẩm tồn tại
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName CheckExistProductBank

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Query:)   {String}      [sort]                             Tên field trường thông tin cần sắp xếp
@apiParam   (Query:)   {String}      [after_token]                      Token để request lấy dữ liệu trang tiếp theo
@apiParam   (Query:)   {String}      [per_page]                         Số phần tử của trang
@apiParam   (Query:)   {String}      [order]                            Kiểu sắp xếp ( asc: sắp xếp tăng dần desc: sắp xếp giảm dần(default) )


@apiParam   (Body:)   {Array}       product_ids                 Danh sách id sản phẩm cần check 
@apiParam   (Body:)   {Array}       filters                     Danh sách các điều kiện bộ lọc 
@apiParam   (Body:)   {Array}       fields                      Danh sách field cần trả về

@apiParam   (filters:)   {Array}       values                      Danh sách field cần trả về
@apiParam   (filters:)   {String}       criteria_key               Các key để filter:
                                                                    <ul>
                                                                        <li><code>cri_product_line</code>: Dòng sản phẩm</li> 
                                                                        <li><code>cri_name</code>: Tên sản phẩm</li> 
                                                                        <li><code>cri_product_code</code>: Mã sản phẩm</li> 
                                                                        <li><code>cri_customer_group</code>: nhóm khách hàng</li> 
                                                                        <li><code>cri_status_display_product</code>: trạng thái</li> 
                                                                        <li><code>cri_created_time</code>: thời gian tạo</li> 
                                                                        <li><code>cri_updated_time</code>: thời gian cập nhật</li> 
                                                                    </ul>
@apiParam   (filters:)   {String}       operator_key                      Danh sách field cần trả về


@apiParamExample {json} Body example
{
    "product_line_id": "",
    "search": "",
    "filters": [
        {
            "criteria_key" : "cri_product_line",
            "operator_key" : "op_is_in",
            "values" : [ 
                "kanh_123_code"
            ]
        }, 
        {
            "criteria_key" : "cri_customer_group",
            "operator_key" : "op_is_in",
            "values" : [ 
                ""
            ]
        }
    ],
    "fields": ["name", "code"],
    "product_ids": ["62a02206fd729f6f66b43643"]
}        



@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": True,
    "lang": "vi",
    "message": "request success"
}

"""

# ******************************** Get List Product Line Have Total Loan ********************
# version: 1.0.0                                                                      *
# **************************************************************************************
"""
@api {get} {domain}/product/bank/api/v1.0/action/get-product-line-by-total-balance             Lấy danh sách dòng sản phẩm của bộ Tổng dư nợ
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName  GetListProductLineByTotalBalance

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)   {String}      [search]                   Tên hoặc mã dòng sản phẩm                     

@apiSuccess   (Response:)     {String}    code                  Mã dòng sản phẩm.
@apiSuccess   (Response:)     {String}    customer_group        Mã nhóm khách hàng
@apiSuccess   (Response:)     {String}    name                  Tên dòng sản phẩm

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
         {
            "_id": "62a93c67a8772cd90c3b53b1",
            "account_id": null,
            "code": "tai_khoan_39fcdcd2",
            "created_by": null,
            "created_time": **********.855524,
            "customer_group": "KHACHHANG_e51dc007",
            "id": "62a93c67a8772cd90c3b53b1",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "TÀI KHOẢN",
            "name_ascii": "TAI KHOAN",
            "updated_by": null,
            "updated_time": **********.855532
        },
        {
            "_id": "62a93c67a8772cd90c3b53b7",
            "account_id": null,
            "code": "tien_gui_tiet_kiem_975feecc",
            "created_by": null,
            "created_time": **********.855563,
            "customer_group": "KHACHHANG_e51dc007",
            "id": "62a93c67a8772cd90c3b53b7",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "TIỀN GỬI TIẾT KIỆM",
            "name_ascii": "TIEN GUI TIET KIEM",
            "updated_by": null,
            "updated_time": **********.855564
        },
        {
            "_id": "62a93c67a8772cd90c3b53bc",
            "account_id": null,
            "code": "cho_vay_9de90792",
            "created_by": null,
            "created_time": **********.855586,
            "customer_group": "KHACHHANG_e51dc007",
            "id": "62a93c67a8772cd90c3b53bc",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "CHO VAY",
            "name_ascii": "CHO VAY",
            "updated_by": null,
            "updated_time": **********.855587
        },
        {
            "_id": "62b427d6f14474ca83255933",
            "account_id": null,
            "code": "trai_phieu_4e93d9ea",
            "created_by": null,
            "created_time": **********.855703,
            "customer_group": "KHACHHANG_e51dc007",
            "id": "62b427d6f14474ca83255933",
            "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "name": "TRÁI PHIẾU",
            "name_ascii": "TRAI PHIEU",
            "updated_by": null,
            "updated_time": **********.855705
        }
    ],
  "lang": "vi",
  "message": "request thành công."
}
"""

# ******************************** Get List Product Line By Key ********************
# version: 1.0.0                                                                      *
# **************************************************************************************
"""
@api {get} {domain}/product/bank/api/v1.0/product-line-default             Lấy thông tin dòng sản phẩm mặc định
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName  GetProductLineDefault

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)   {String}      product_line_code                  code dòng sản phẩm mặc định cách nhau dấu ","
                                                                    (<code>tai_khoan_39fcdcd2</code>,
                                                                    <code>tien_gui_tiet_kiem_975feecc</code>,
                                                                    <code>cho_vay_9de90792</code>,
                                                                    <code>the_ghi_no_d662f858</code>,
                                                                    <code>the_tin_dung_e98f0a7a</code>,
                                                                    <code>bao_hiem_d97331b0</code>,
                                                                    <code>trai_phieu_4e93d9ea</code>,
                                                                    <code>ebank_7e4220c4</code>)
@apiParam   (Query:)   {String}      [fields]             Danh sách field cần trả về cách nhau dấu ","
                     

@apiSuccess   (Response:)     {String}    _id                  Id dòng sản phẩm.
@apiSuccess   (Response:)     {String}    code                  Mã dòng sản phẩm.
@apiSuccess   (Response:)     {String}    customer_group        Mã nhóm khách hàng
@apiSuccess   (Response:)     {String}    name                  Tên dòng sản phẩm
@apiSuccess   (Response:)     {String}    name_ascii                  Tên ascii dòng sản phẩm
@apiSuccess   (Response:)     {Int}       count_product           Số lượng sản phẩm của dòng

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
      {
            "_id" : "62de3b88c47883001088e3fe",
            "merchant_id" : "972e6e1d-8891-4fdb-9d02-8a7855393298",
            "count_product" : 5,
            "created_time" : **********.85109,
            "updated_time" : **********.85111,
            "customer_group" : "KHACHHANG_e51dc007",
            "name" : "Dòng ánh test 1",
            "code" : "Dòng ánh test 1",
            "name_ascii" : "dong anh test 1",
            "created_by" : "0f5be325-bd84-420b-9e03-8a7161404233",
            "updated_by" : "0f5be325-bd84-420b-9e03-8a7161404233"
        }
    ],
  "lang": "vi",
  "message": "request thành công."
}
"""

"""
@api {post} {domain}/product/bank/api/v1.0/upsert/tree/product-line                  Upsert cây dòng sản phẩm
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName UpsertTreeProductLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}    customer_group                      Mã nhóm khách hàng
                                            <ul>
                                                <li><code>khach_hang_doanh_nghiep</code> Khách hàng Doanh nghiệp</li>
                                                <li><code>khach_hang_ca_nhan</code> Khách hàng Cá nhân</li>
                                                <li><code>khach_hang_uu_tien</code> Khách hàng Ưu tiên</li>
                                            </ul>
@apiParam   (Body:)   {String}    product_line_name                   Tên dòng sản phẩm
@apiParam   (Body:)   {String}    product_line_code                   Mã dòng sản phẩm
@apiParam   (Body:)   {Array}     [product_type]                        Loại sản phẩm 
@apiParam   (Body:)   {String}    product_type.name                   Tên loại sản phẩm 
@apiParam   (Body:)   {String}    product_type.code                   Mã loại sản phẩm 
@apiParam   (Body:)   {Array}     [product_type.sub]                    Danh sách loại sản phẩm con 
@apiParam   (Body:)   {Bool}      [product_type.move_category]           <code>True: </code> di chuyển danh mục
@apiParam   (Body:)   {Array}     [product_type.move_product]          <code>Code</code> của danh mục/dòng cần chuyển product. 
                                                                        <code>Sử dụng:</code>
                                            <ul>
                                                <li>Nhập product_line_code nếu danh mục chưa tồn tại</li>
                                                <li>Chỉ sử dụng cho danh mục cấp thấp nhất</li>
                                                <li>Nhập product_type code nếu danh mục đó đã tồn tại</li>
                                                <li>Trường hợp chưa đối tượng nào tồn tại thì có thể nhập code bất kì (để pass validate)</li>
                                            </ul>
@apiParam   (Body:)   {Array}     [product_type.products]               Danh sách sản phẩm
@apiParam   (Body:)   {String}    product_type.products.name          Tên sản phẩm 
@apiParam   (Body:)   {String}    product_type.products.code          Mã sản phẩm 
 

@apiParamExample {json} Body example
[
    {
        "customer_group": "khach_hang_ca_nhan",
        "product_line_name": "kta",
        "product_line_code": "kta_code",
        "products": [               // Sản phẩm chỉ được gán vào dòng khi không có danh mục nào
            {
                "name": "name_sp_lv_1.1.1",
                "code": "code_sp_lv_1.1.1"
            }
        ],
        "product_type": [
            {
                "name": "name_loai_cap_1",
                "code": "code_loai_cap_1",
                "sub": [
                    {
                        "code": "code_loai_cap_1.1",
                        "name": "name_loai_cap_1.1",
                        "move_category": True,
                        "sub": [
                            {
                                "code": "code_loai_cap_1.1.1",
                                "name": "name_loai_cap_1.1.1",
                                "move_product": "code_loai_cap_1.1",           // Chỉ áp dụng ở danh mục cuối
                                "products": [
                                    {
                                        "name": "name_sp_lv_1.1.1",
                                        "code": "code_sp_lv_1.1.1"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }
]        
    

@apiSuccess   {Number}    path      đường dẫn code danh mục                                                          

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "lang": "vi",
    "message": "request success"
}

"""

"""
@api {post} {domain}/product/bank/api/v1.0/action/get_product_line_fields                  Lấy thông tin dòng sản phẩm và field của dòng
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName GetInformationProductHoldingAndFields

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Body:)     {Array}     product_line_ids     Danh sách <code>ID</code> của dòng sản phẩm cần lấy dữ liệu. 
@apiParam   (Body:)     {Array}     product_line_keys     Danh sách <code>key</code> của dòng sản phẩm cần lấy dữ liệu. Ví dụ: ["name", "code"] 
@apiParam   (Body:)     {String}    [area_code]          Mã khu vực hiện thị
                                                         <ul>
                                                            <li><code>information_product</code>: Thông tin sản phẩm</li>
                                                            <li><code>product_filter</code>: Bộ lọc sản phẩm</li>
                                                            <li><code>product_holding</code>: Product Holding</li>
                                                            <li><code>product_holding_filter</code>: Bộ lọc Product Holding</li>
                                                         </ul>
                                                        <code>Nếu không truyền lên thì lấy tất cả.</code>  
@apiParam   (Body:)     {Array}     type_fields     Danh sách loại field cần lấy. Ví dụ: field_key,field_name


@apiParamExample    {json}      Body example:
{
    "product_line_ids": [],
    "product_line_keys": ["name", "code"],
    "area_code": "product_holding",
    "type_fields": ["field_key", "field_name"],
}

@apiSuccess   (Response:)     {Number}      code            Response status    
@apiSuccess   (Response:)     {String}      message         Response message
@apiSuccess   (Response:)     {Array}       data            Dữ liệu trả về
@apiSuccess   (Response:)     {String}      data.id         <code>ID</code> dòng sản phẩm
@apiSuccess   (Response:)     {String}      data.name       Tên dòng sản phẩm
@apiSuccess   (Response:)     {String}      data.code       Mã dòng sản phẩm
@apiSuccess   (Response:)     {Array}       data.fields     Danh sách field của dòng sản phẩm 

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "lang": "vi",
  "data": [
    {   
        "id": "62a165a6fd729f1b02e3f8c9",
        "name": "Dòng test 1",
        "code": "dong test 1",
        "fields": [
            {
                "field_name": "field_2",
                "field_key": "_dyn_name_1558406233389",
            },
            {
                "field_name": "field_3",
                "field_key": "_dyn_name_1558406233399",
            }
        ]
    }
  ],
  "message": "request thành công."
}
"""

"""
@api {post} {domain}/product/bank/api/v1.0/upsert/product-etl                  Upsert sản phẩm từ phía ETL
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName UpsertProductBankByETL

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}      table                     Tên table ETL. Phần này sẽ dùng để kiểm tra xem kiểu thao tác vào dữ liệu của Product đó từ bảng ETL là gì. 
                                                                Phần này sẽ được phía Product cấu hình bằng tay.                                                            
@apiParam   (Body:)   {String}      product_code                Mã sản phẩm
@apiParam   (Body:)   {String}      [customer_group]            Mã nhóm khách hàng (default: key của Khách hàng cá nhân)
@apiParam   (Body:)   {String}      [product_line_type]         Option lấy dữ liệu dòng sản phẩm dựa trên nhóm khách hàng
                                                                 <ul>
                                                                    <li><code>KHCN</code>: lấy dòng sản phẩm của nhóm khách hàng cá nhân. Call khi muốn lấy dữ liệu của Profile</li>
                                                                    <li><code>KHDN</code>: lấy dòng sản phẩm của nhóm khách hàng doanh nghiệp. Call khi muốn lấy dữ liệu của Company</li>
                                                                 </ul>
                                                                 <code>Default: key của Khách hàng cá nhân</code>
@apiParam   (Body:)   {String}      [product_line]              Id dòng sản phẩm
@apiParam   (Body:)   {Array}       [product_type]              Danh sách id danh mục
@apiParam   (Body:)   {Boolean}     status_display_product      Trạng thái sản phẩm 


@apiParamExample {json} Body example
{
    "table": "lending",
    "product_line": "62a16682fd729f1bf1484019",
    "name": "product 456",
    "product_code": "product_456",
    "customer_group": "KHACHHANG_77b2081f",
    "status_display_product": True,
    "product_type": ["629f787cfd729f242766d54b", "629f787cfd729f242766d54c", "629f787cfd729f242766d54d"]
}       


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "action": "update/add",
        "_id": "62a1ba15fd729f8060fd8001",
        "id": "62a1ba15fd729f8060fd8001",
        "created_time": "2022-06-09T16:15:01Z",
        "customer_group": "KHACHHANG_77b2081f",
        "id": "62a1ba15fd729f8060fd8001",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "name": "product 456",
        "product_code": "product_456",
        "product_line": "62a16682fd729f1bf1484019",
        "product_type": [
            "629f787cfd729f242766d54b",
            "629f787cfd729f242766d54c",
            "629f787cfd729f242766d54d"
        ],
        "status_display_product": True,
        "updated_time": "2022-06-09T16:15:01Z"
    },
    "lang": "vi",
    "message": "request success"
}

"""
"""
@api {post} {domain}/product/bank/api/v1.0/action/upsert/product-line               API upsert dòng sản phẩm         
@apiDescription          API này sẽ kiểm tra mã dòng sản phẩm có trong hệ thống kê. Nếu không thì insert còn nếu có thì bỏ qua trả về dữ liệu của dòng đang lưu.
@apiGroup ProductLine
@apiVersion 1.0.0
@apiName UpsertProductLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (Body:)   {String}    [name]                          Tên dòng sản phẩm
@apiParam   (Body:)   {String}    code                          Mã dòng sản phẩm
@apiParam   (Body:)   {String}    [customer_group_code]         Mã nhóm khách hàng
@apiParam   (Body:)   {String}    [product_line_type]         Option lấy dữ liệu dòng sản phẩm dựa trên nhóm khách hàng
                                                                 <ul>
                                                                    <li><code>KHCN</code>: lấy dòng sản phẩm của nhóm khách hàng cá nhân. Call khi muốn lấy dữ liệu của Profile</li>
                                                                    <li><code>KHDN</code>: lấy dòng sản phẩm của nhóm khách hàng doanh nghiệp. Call khi muốn lấy dữ liệu của Company</li>
                                                                 </ul>
                                                                 <code>Default: key của Khách hàng cá nhân</code>

@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": {
        "_id": "62a00f93fd729f519d5c87ce",
        "account_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "code": "kanh_test_123_code",
        "created_time": **********.305598,
        "customer_group_code": "KHACHHANG_77b2081f",
        "id": "62a00f93fd729f519d5c87ce",
        "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
        "name": "kanh test 123",
        "updated_time": **********.305604
    },
    "lang": "vi",
    "message": "request success"
}

"""

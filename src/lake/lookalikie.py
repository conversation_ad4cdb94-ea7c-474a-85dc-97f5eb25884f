************************ Find lookalike customer ******************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} https://{domain}/lake/v1.0/ads/find_lookalike Find customer lookalike 
@apiDescription API find lookalike customer base on an csv file
@apiGroup Ads
@apiVersion 1.0.0
@apiName FindLookalike

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body: form-data) {string} webhook      Webhook to sent lookalike data. (Ex: Ads url)
@apiParam (Body: form-data) {number} limit        Maximum customer retrieve in lake. If <code>limit > 100.000</code>, so <code>limit = 100.000</code>
@apiParam (Body: form-data) {string} hook_token   Token authentication for webhook.
@apiParam (Body: form-data) {string} session_id   Ads session
@apiParam (Body: form-data) {file} train_file     CSV file to train. Extension: <code>csv</code>

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công"
}
"""
""" Author: hoannk
    Company: MobioVN
    Created Date: 17/02/2022
"""
#########################   List Group MCC by ids    ###########################
# version: 1.0.0
#################################################################
"""
@api {post} /api/v1.0/mcc-group/detail-by-ids [POST] Danh sách list Group MCC by ids
@apiDescription Danh sách list Group MCC by ids
@apiGroup MCC
@apiVersion 1.0.0
@apiName MCCGroupListByIds

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiUse lang
@apiParam      (Body:)             {Array(String)}    group_ids      Mảng string group_id

@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {Array[group_object]}          data                   Danh sách mcc group
@apiSuccess                 {Object[paging]}               paging                 Thông tin phân trang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [group_object],
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccess   (group_object:)    {String}     id                    Id
@apiSuccess   (group_object:)    {String}     name                  Tên tệp
@apiSuccess   (group_object:)    {String}     state                 Trạng thái
@apiSuccess   (group_object:)    {String}     total                 Số lượng MCC
@apiSuccess   (group_object:)    {String}     created_merchant_id   Merchant tạo
@apiSuccess   (group_object:)    {String}     updated_merchant_id   Merchant cập nhật
@apiSuccess   (group_object:)    {String}     created_user          Người tạo
@apiSuccess   (group_object:)    {String}     updated_user          Người cập nhật
@apiSuccess   (group_object:)    {String}     created_type          Kiểu tạo
@apiSuccess   (group_object:)    {String}     created_time          Thời gian tạo tệp. Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ (UTC)</code>. <br/>Example: <code>2021-09-07T03:20:01Z</code>
@apiSuccess   (group_object:)    {String}     updated_time          Thời gian cập nhật tệp. Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ (UTC)</code>. <br/>Example: <code>2021-09-07T03:20:01Z</code>

@apiSuccessExample {json} group_object
{
    "id": "98a0bc6b-5242-4608-a8b6-8a0690defca0",
    "created_merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
    "created_time": "2022-03-03T11:22:43.749000Z",
    "created_type": 1,
    "created_user": "704eac91-7416-497f-a17d-d81cfa2d3211",
    "name": "Ánh test file MCC",
    "state": 1,
    "total": 20,
    "updated_merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
    "updated_time": "2022-03-03T11:22:48.109000Z",
    "updated_user": "704eac91-7416-497f-a17d-d81cfa2d3211"
}

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""

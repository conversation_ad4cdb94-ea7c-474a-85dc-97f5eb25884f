""" Author: hoannk
    Company: MobioVN
    Created Date: 18/02/2022
"""
#########################   List Group MCC    ###########################
# version: 1.0.0
#################################################################
"""
@api {get} /api/v1.0/mcc-group/list/common [GET] Danh sách list Group MCC Common
@apiDescription Danh sách Group MCC Common
@apiGroup MCC
@apiVersion 1.0.0
@apiName MCCGroupListCommon

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiUse lang
@apiParam      (Body:)             {String}    [search]             Tìm kiếm
@apiParam      (Body:)             {Number}    [per_page]           S<PERSON> lượng item trên page. <br/>Example: <code>&per_page=5</code>
@apiParam      (Body:)             {Number}    [after_token]        Token cho page tiếp theo.

@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {Array[group_object]}          data                   Danh sách mcc group
@apiSuccess                 {Object[paging]}               paging                 Thông tin phân trang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [group_object],
    "paging": {paging},
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccess   (group_object:)    {String}     id                    Id
@apiSuccess   (group_object:)    {String}     name                  Tên tệp
@apiSuccess   (group_object:)    {String}     state                 Trạng thái
@apiSuccess   (group_object:)    {String}     total                 Số lượng MCC

@apiSuccessExample {json} group_object
{
    "id": "98a0bc6b-5242-4608-a8b6-8a0690defca0",
    "name": "Ánh test file MCC",
    "state": 1,
    "total": 20
}

@apiSuccess     (paging:)          {Object}    [cursors]                   Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     (paging:)          {String}    [cursors..after]            Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     (paging:)          {String}    [cursors..before]           Token để lấy dữ liệu trang trước đó.
@apiSuccess     (paging:)          {Number}    [per_page]                  Số lượng yêu cầu trên 1 page.
@apiSuccess     (paging:)          {Number}    [total_items]               Tổng số lượng

@apiSuccessExample {json} paging
{
    "cursors": {
        "after": "eydhY3Rpb25fdGltZSc6IDE2MjYwNTA2MTkuMjE1MTI3LCAnaGFzaCc6ICcwMzkyOWQ2YjRlZmY0MDE4OTI0NTcwOWY4ODViNmI4NSd9",
        "before": null
    },
    "per_page": 20,
    "total_items": 4
}

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""

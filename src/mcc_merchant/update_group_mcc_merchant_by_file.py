""" Author: hoannk
    Company: MobioVN
    Created Date: 11/02/2022
"""
#########################   Update group MCC-MERCHANT by File    ###########################
# version: 1.0.0
#################################################################
"""
@api {post} /api/v1.0/mcc_merchant_group_update_list_by_file?group_id={group_id} [POST] Sửa tệp MCC-MERCHANT bằng File
@apiDescription Sửa tệp MCC-MERCHANT bằng File
@apiGroup MCC
@apiVersion 1.0.0
@apiName MccMerchantGroupByFileUpdate

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiParam      (FormData:)             {String}        [group_name]                 Tên tệp
@apiParam      (FormData:)             {File}        [file_include]                 File mcc/merchant thêm
@apiParam      (FormData:)             {File}        [file_exclude]                 File mcc/merchant loại trừ

@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {String}                       data                   Trạng thái xử lý

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "id": "801ad9a6-a5f6-418b-9d96-c39aea9a7f83",
        "state": 1
    },
    "lang": "vi",
    "message": "request thành công."
}

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""
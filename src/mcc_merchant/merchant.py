*************************************  Danh sách bank merchant_by_ids  *****************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /api/v1.0/merchant/detail-by-ids Lấy chi tiết merchant bằng danh sách merchant_id
@apiDescription Lấy chi tiết merchant bằng danh sách merchant_id
@apiGroup Merchant
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName GetMerchantDetailByIDs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body) {String} merchant_ids Danh sách merchant_id

@apiParamExample {Json}  Json example:
{
    "merchant_ids" : [
        "5f7e5854-1fe9-4bdf-8b34-278389636d7c", 
        "5f7e5854-1fe9-4bdf-8b34-278389636d7d"
    ]
}

@apiSuccess {Array}   data    Danh sách merchant_id
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
  "data": [
    {
      "id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000c",
      "name": "Merchant 1",
      "code": "123"
    },
    {
      "id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000d",
      "name": "Merchant 2",
      "code": "234"
    }
  ],
  "code": 200,
  "message": "request thành công"
}
"""
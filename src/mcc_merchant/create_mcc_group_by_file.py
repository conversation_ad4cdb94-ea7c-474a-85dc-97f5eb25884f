""" Author: hoannk
    Company: MobioVN
    Created Date: 11/02/2022
"""
#########################   Create group MCC by File    ###########################
# version: 1.0.0
#################################################################
"""
@api {post} /api/v1.0/mcc-group/file [POST] Tạo tệp MCC bằng File
@apiDescription Tạo tệp MCC bằng File
@apiGroup MCC
@apiVersion 1.0.0
@apiName MCCGroupByFileCreate

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>multipart/form-data</code>

@apiParam      (FormData:)             {File}        file           File upload
@apiParam      (FormData:)             {String}      name           T<PERSON><PERSON> tệ<PERSON>

@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {String}                       data                   Trạng thái xử lý

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "id": "801ad9a6-a5f6-418b-9d96-c39aea9a7f83",
        "state": 1
    },
    "lang": "vi",
    "message": "request thành công."
}

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""
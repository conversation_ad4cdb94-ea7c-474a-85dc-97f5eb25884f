""" Author: hoannk
    Company: MobioVN
    Created Date: 18/02/2022
"""
#########################   Sửa tệp MCC-MERCHANT thủ công    ###########################
# version: 1.0.0
#################################################################
"""
@api {get} /api/v1.0/mcc_merchant_group_update_list_manual?group_id={group_id} [POST] Sửa tệp MCC-MERCHANT thủ công
@apiDescription Sửa tệp MCC-MERCHANT thủ công
@apiGroup MCC
@apiVersion 1.0.0
@apiName MccMerchantUpdateGroupManual

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiUse lang
@apiParam      (Body:)             {String}    [group_name]             Tên group
@apiParam      (Body:)             {Array}     [list_ids]                Danh sách mcc id hoặc merchant id thêm.


@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {String}                       data                   Trạng thái xử lý

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "id": "801ad9a6-a5f6-418b-9d96-c39aea9a7f83",
        "state": 1
    },
    "lang": "vi",
    "message": "request thành công."
}


@apiSuccess   (group_object:)    {String}     id                    Id
@apiSuccess   (group_object:)    {String}     state                 Trạng thái


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""

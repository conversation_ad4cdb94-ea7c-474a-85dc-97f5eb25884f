*************************************  Danh sách mcc_ids  *************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/mcc/list Danh sách mcc_id
@apiDescription Danh sách mcc
@apiGroup MCC
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName GetMCCIDs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)    {String}    [search]       Từ khóa tìm kiếm
@apiParam   (Query:)    {Number}    [per_page]     Số bản ghỉ trên 1 trang. Mặc định không truyền <code>per_page=20</code>. <code>limit: 20000; per_page phải là số nguyên dương</code>
@apiParam   (Query:)    {String}    [after_token]        Param gửi lên để lấy trang kế tiếp

@apiSuccess {Array}   data    Danh sách mcc_id
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
  "data": [
    "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000c", 
    "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000d"
  ],
  "code": 200,
  "message": "request thành công"
}
"""

*************************************  Danh sách bank mcc_by_ids  **********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /api/v1.0/mcc/detail-by-ids Lấy chi tiết mcc bằng danh sách mcc_id
@apiDescription Lấy chi tiết MCC bằng danh sách mcc_id
@apiGroup MCC
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiName GetMCCDetailByIDs

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body) {String} mcc_ids Danh sách mcc_id

@apiParamExample {Json}  Json example:
{
    "mcc_ids" : [
        "5f7e5854-1fe9-4bdf-8b34-278389636d7c", 
        "5f7e5854-1fe9-4bdf-8b34-278389636d7d"
    ]
}

@apiSuccess {Array}   data    Danh sách merchant_id
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
  "data": [
    {
      "id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000c",
      "name": "MCC 1",
      "code": "123"
    },
    {
      "id": "35ed843a-3e37-4a7b-a4ba-9a7ef4f2000d",
      "name": "MCC 2",
      "code": "234"
    }
  ],
  "code": 200,
  "message": "request thành công"
}
"""
""" Author: hoannk
    Company: MobioVN
    Created Date: 11/02/2022
"""
#########################   List Merchant Core    ###########################
# version: 1.0.0
#################################################################
"""
@api {get} /api/v1.0/merchant-core [GET] Danh sách Merchant trong hệ thống
@apiDescription Danh sách Merchant trong hệ thống
@apiGroup Merchant
@apiVersion 1.0.0
@apiName MerchantCoreList

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiUse lang
@apiParam      (Query:)             {String}    [search]      Tìm kiếm
@apiParam      (Query:)             {Integer}    [per_page]    Số bản tin trên 1 trang; <code>max: 20.000; default: 20<code>; <code>per_page phải là số nguyên d<PERSON>ơng</code>
@apiParam      (Query:)             {String}    [after_token] Token cho page tiếp theo.

@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {Array[merchant_object]}       data                   Danh sách Merchant
@apiSuccess                 {Object[paging]}               paging                 Thông tin phân trang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [merchant_object],
    "paging": {paging},
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccess   (group_object:)    {String}     id                    Id
@apiSuccess   (group_object:)    {String}     code                  Merchant code
@apiSuccess   (group_object:)    {String}     name                  Merchant name

@apiSuccessExample {json} group_object
{
    "id": "",
    "code": "",
    "name": ""
}

@apiSuccess     (paging:)          {Object}    [cursors]                   Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     (paging:)          {String}    [cursors..after]            Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     (paging:)          {String}    [cursors..before]           Token để lấy dữ liệu trang trước đó.
@apiSuccess     (paging:)          {Number}    [per_page]                  Số lượng yêu cầu trên 1 page.
@apiSuccess     (paging:)          {Number}    [total_items]               Tổng số lượng

@apiSuccessExample {json} paging
{
    "cursors": {
        "after": "eydhY3Rpb25fdGltZSc6IDE2MjYwNTA2MTkuMjE1MTI3LCAnaGFzaCc6ICcwMzkyOWQ2YjRlZmY0MDE4OTI0NTcwOWY4ODViNmI4NSd9",
        "before": null
    },
    "per_page": 20,
    "total_items": 4
}

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""
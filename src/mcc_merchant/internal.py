*********************************** Upsert MCC     ************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} /internal/api/v1.0/mcc/upsert Upsert MCC
@apiDescription Upsert MCC
@apiGroup Internal
@apiVersion 1.0.0
@apiName UpsertMCC

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Integer}   code              Mã MCC của VIB
@apiParam       (Body:)     {String}    name              T<PERSON><PERSON> c<PERSON><PERSON> s<PERSON> ph<PERSON>

@apiParamExample  {json} Body request example
{
    "code": 123,
    "name": "MCC 1"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "5e901a7afb403a80e29678e7",
        "created_time": "2020-03-24T11:54:22.000Z",
        "name": "MCC 1",
        "code": 123,
        "status": 1,
        "updated_time": "2020-04-09T05:14:46.000Z"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

*********************************** Upsert Merchant ***********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} /internal/api/v1.0/merchant/upsert Upsert Merchant
@apiDescription Upsert Merchant
@apiGroup Internal
@apiVersion 1.0.0
@apiName UpsertMerchant

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Integer}    code             Mã của merchant
@apiParam       (Body:)     {String}    name              Tên của merchant

@apiParamExample  {json} Body request example
{
    "name": "Merchant 1",
    "code": 123
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "5e901a7afb403a80e29678e7",
        "created_time": "2020-03-24T11:54:22.000Z",
        "name": "Merchant 1",
        "status": 1,
        "updated_time": "2020-04-09T05:14:46.000Z",
        "code": 123 
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
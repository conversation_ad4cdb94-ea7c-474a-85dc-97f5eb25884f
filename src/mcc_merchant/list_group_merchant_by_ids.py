""" Author: hoannk
    Company: MobioVN
    Created Date: 17/02/2022
"""
#########################   List Group Merchant by ids    ###########################
# version: 1.0.0
#################################################################
"""
@api {post} /api/v1.0/merchant-group/detail-by-ids [POST] Danh sách list Group Merchant by ids
@apiDescription Danh sách list Group Merchant by ids
@apiGroup Merchant
@apiVersion 1.0.0
@apiName MerchantGroupListByIds

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiUse lang
@apiParam      (Body:)             {Array(String)}    group_ids     Mảng string group_id

@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {Array[group_object]}          data                   Danh sách Merchant group
@apiSuccess                 {Object[paging]}               paging                 Thông tin phân trang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [group_object],
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccess   (group_object:)    {String}     id                    Id
@apiSuccess   (group_object:)    {String}     name                  Tên tệp
@apiSuccess   (group_object:)    {String}     state                 Trạng thái
@apiSuccess   (group_object:)    {String}     total                 Số lượng Merchant
@apiSuccess   (group_object:)    {String}     created_merchant_id   Merchant tạo
@apiSuccess   (group_object:)    {String}     updated_merchant_id   Merchant cập nhật
@apiSuccess   (group_object:)    {String}     created_user          Người tạo
@apiSuccess   (group_object:)    {String}     updated_user          Người cập nhật
@apiSuccess   (group_object:)    {String}     created_type          Kiểu tạo
@apiSuccess   (group_object:)    {String}     created_time          Thời gian tạo tệp. Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ (UTC)</code>. <br/>Example: <code>2021-09-07T03:20:01Z</code>
@apiSuccess   (group_object:)    {String}     updated_time          Thời gian cập nhật tệp. Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ (UTC)</code>. <br/>Example: <code>2021-09-07T03:20:01Z</code>

@apiSuccessExample {json} group_object
{
    "id": "43a4eee1-d138-4648-8be1-39c87d346dbf",
    "created_merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
    "created_time": "2022-03-03T03:20:38.637000Z",
    "created_type": 0,
    "created_user": "704eac91-7416-497f-a17d-d81cfa2d3211",
    "name": "Test BL + bộ merchant cũ copy Thỏa mãn bộ lọc 1646277621",
    "state": 1,
    "total": 6,
    "updated_merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
    "updated_time": "2022-03-03T03:20:38.637000Z",
    "updated_user": "704eac91-7416-497f-a17d-d81cfa2d3211"
}

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""
""" Author: hoannk
    Company: MobioVN
    Created Date: 11/02/2022
"""
#########################   List MCC by Group    ###########################
# version: 1.0.0
#################################################################
"""
@api {get} /api/v1.0/mcc-group/{group_id} [GET] Danh sách MCC trong tệp
@apiDescription Danh sách MCC trong tệp
@apiGroup MCC
@apiVersion 1.0.0
@apiName MCCListByGroup

@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiUse lang
@apiParam      (Query:)             {String}    [search]      Tìm kiếm
@apiParam      (Param:)             {String}    group_id      Id tệp
@apiParam      (Query:)             {String}    [start_time]  Thời gian bắt đầu. <PERSON><PERSON><PERSON> dạng: <code>yyyy-MM-ddTHH:mm:ssZ (UTC)</code>. <br/>Example: <code>2021-09-07T03:20:01Z</code>
@apiParam      (Query:)             {Number}    [per_page]    Số lượng item trên page. <br/>Example: <code>&per_page=5</code>
@apiParam      (Query:)             {Number}    [after_token] Token cho page tiếp theo.

@apiSuccess                 {Integer}                      code                   Http code
@apiSuccess                 {String}                       lang                   Ngôn ngữ
@apiSuccess                 {String}                       message                Message
@apiSuccess                 {Array[mcc_object]}            data                   Danh sách mcc
@apiSuccess                 {Object[paging]}               paging                 Thông tin phân trang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [mcc_object],
    "paging": {paging},
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccess   (group_object:)    {String}     id                    MCC Core Id
@apiSuccess   (group_object:)    {String}     code                  Merchant code
@apiSuccess   (group_object:)    {String}     name                  Merchant name
@apiSuccess   (group_object:)    {String}     created_time          Thời gian tạo

@apiSuccessExample {json} group_object
{
    "code": "5735",
    "created_time": "2022-03-03T11:22:47.258000Z",
    "id": "5eba840d81803ffc359371a2",
    "name": "Record Shops."
}

@apiSuccess     (paging:)          {Object}    [cursors]                   Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     (paging:)          {String}    [cursors..after]            Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     (paging:)          {String}    [cursors..before]           Token để lấy dữ liệu trang trước đó.
@apiSuccess     (paging:)          {Number}    [per_page]                  Số lượng yêu cầu trên 1 page.
@apiSuccess     (paging:)          {Number}    [total_items]               Tổng số lượng

@apiSuccessExample {json} paging
{
    "cursors": {
        "after": "eydhY3Rpb25fdGltZSc6IDE2MjYwNTA2MTkuMjE1MTI3LCAnaGFzaCc6ICcwMzkyOWQ2YjRlZmY0MDE4OTI0NTcwOWY4ODViNmI4NSd9",
        "before": null
    },
    "per_page": 20,
    "total_items": 4
}

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
"""
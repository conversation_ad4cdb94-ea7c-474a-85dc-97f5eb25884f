"""
@api {get} /templates/<str:id> Request Detail Template
@apiName GetDetailTemplate
@apiGroup Template
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500


@apiParam {String} id id of template
@apiParamExample Param Example:
https://{domain}/template/api/v2.0/templates/d91d6a3e-c2fd-4fee-980a-16481b198855

@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Object} data data trả về là template cần get

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "name": "New Year Gift Card",
        "id": "d91d6a3e-c2fd-4fee-980a-16481b198855",
        "thumb_link": "https://{domain}/template/api/v2.0/templates/getthumb/<token>/<id>"
    }
}
"""


"""
@api {get} /templates Request List templates
@apiName GetListTemplate
@apiGroup Template
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Object} data danh sách các template.

@apiParam {String} [search]  Chuỗi tìm kiếm theo template name. Nếu tên của template có chứa param được truyền vào search thì trả về template đó.
@apiParam {String} [type]  Lọc theo điều kiện. Nếu giá trị type được truyền vào:
<li><code>&type=tenant</code> hoặc <code>&type=all</code>: Chỉ hiển thị template của riêng tenant hoặc hiển thị template loại Public</li>

@apiParam {String} [status]  Lọc theo điều kiện. Nếu giá trị status được truyền vào:
<li><code>status=-1,0,1,2</code>: Các giá trị status này được phân cách bởi dấu phẩy, có thể lọc một lúc nhiều loại status.</li>
@apiParam {String} [page]  Page number. Request <code>page=-1</code> if need get all items.
<code>MIN_VALUE=1</code>
Example: <code>&page=2</code>
Default value: <code>1</code>

@apiParam {String} [per_page]  Number of item on a page.
Example: <code>&per_page=5</code>

@apiParam {String} [sort]  Sắp xếp theo tiêu chí cụ thể
Example:
 <li><code>sort=date</code> Sắp xếp theo thứ tự ngày tạo</li>
 <li><code>sort=name</code> Sắp xếp theo thứ tự tên theo Alphabet</li>

@apiParam {String} [order] order results <br>
Default value: <code>asc</code> <br>
Allowed values: <code>asc</code>, <code>desc</code>

@apiSuccessExample Response Success:
{
    "code": 200,
    "message": "request successfully",
    "data": [
        {
            "name": "New Year Gift Card",
            "id": "d91d6a3e-c2fd-4fee-980a-16481b198855",
            "thumb_link": "https://{domain}/template/api/v2.0/templates/getthumb/<token>/<id>"
        },
        {
            "name": "Old Year Gift Card",
            "id": "d91d6a3e-c2fd-4fee-980a-16481b198855",
            "thumb_link": "https://{domain}/template/api/v2.0/templates/getthumb/<token>/<id>"
        },
        ...
    ]
}
"""

"""
@api {post} /templates Create template
@apiName CreateTemplate
@apiGroup Template
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500


@apiParam (Body) {String} body <p>Khi người dùng import lên 1 file HTML để tạo hay sửa template thì body chính là phần content của file HTML đó</p>

@apiParam (Body) {String} type <p>Giá trị type dùng để xác định xem user muốn dùng template đó cho riêng tenant hay cho toàn bộ các user khác đều có thể xem</p>
<li><code>type="tenant"</code> Template này chỉ có tenant mà user đó thuộc về được quyền xem và quyền sửa</li>
<li><code>type="all"</code> Template này chỉ có tenant mà user đó thuộc về được quyền sửa, còn lại chỉ có quyền xem</li>

@apiParam (Body) {int} status <p>Giá trị status dùng để xác định trạng thái template đó</p>
<li><code>status=-1</code> Template này đang ở trạng thái đã bị xoá</li>
<li><code>status=0</code> Template này đang ở trạng thái đã hoàn thành nhưng chưa muốn Public, chỉ có tenant mà user thuộc về mới được xem</li>
<li><code>status=1</code> Template này đang ở trạng thái được chia sẻ</li>
<li><code>status=2</code> Template này đang ở trạng thái nháp, chỉ có người đang tạo mới có quyền xem</li>

@apiParamExample Body example:
{
    "name": "Gift Card",
    "body": "<h1>Create new thumb</h1>",
    "type": "tenant",
    "status": 1
}

@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Object} data List of template

@apiSuccessExample Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "name": "Gift Card",
        "id": "d91d6a3e-c2fd-4fee-980a-16481b198855",
        "thumb_link": "https://{domain}/template/api/v2.0/templates/getthumb/<token>/<id>"
    }
}
"""


"""
@api {patch} /templates/<str:id> Update template
@apiName ModifyTemplate
@apiGroup Template
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500


@apiParam {String} id id của template mà user cần sửa
@apiParamExample id param example:
https://{domain}/template/api/v2.0/templates/d91d6a3e-c2fd-4fee-980a-16481b198855
@apiParam (Body) {String} [body] User import file html có chứa nội dung template cần sửa, body chính là phần nội dung đó.
@apiParam (Body) {String} [type] <p>Giá trị type dùng để xác định xem user muốn dùng template đó cho riêng tenant hay cho toàn bộ các user khác đều có thể xem</p>
<li><code>type="tenant"</code> Template này chỉ có tenant mà user đó thuộc về được quyền xem và quyền sửa</li>
<li><code>type="all"</code> Template này chỉ có tenant mà user đó thuộc về được quyền sửa, còn lại chỉ có quyền xem</li>

@apiParam (Body) {int} [status] <p>Giá trị status dùng để xác định trạng thái template đó</p>
<li><code>status=-1</code> Template này đang ở trạng thái đã bị xoá</li>
<li><code>status=0</code> Template này đang ở trạng thái đã hoàn thành nhưng chưa muốn Public, chỉ có tenant mà user thuộc về mới được xem</li>
<li><code>status=1</code> Template này đang ở trạng thái được chia sẻ</li>
<li><code>status=2</code> Template này đang ở trạng thái nháp, chỉ có người đang tạo mới có quyền xem</li>
@apiParamExample Body example:
{
    "name": "Gift Card",
    "body": "<h1>Modify an existing template</h1>",
    "type": "tenant",
    "status": 2
}

@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Object} data List of template

@apiSuccessExample Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request successfully",
    "data": {
        "name": "Gift Card",
        "id": "d91d6a3e-c2fd-4fee-980a-16481b198855",
        "thumb_link": "https://{domain}/template/api/v2.0/templates/getthumb/<token>/<id>"
    }
}
"""


"""
@api {delete} /templates Delete template
@apiName DeleteTemplate
@apiGroup Template
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Param) {String} ids ids của template mà user cần xoá. Nếu muốn nhiều template thì truyền id phân tách nhau bởi dấu phẩy
@apiParamExample (Param) Single id example:
https://{domain}/template/api/v2.0/templates?ids=d91d6a3e-c2fd-4fee-980a-16481b198855
@apiParamExample (Param) Multiple ids example:
https://{domain}/template/api/v2.0/templates?ids=d91d6a3e-c2fd-4fee-980a-16481b198855,d91d6a3e-c2fd-4fee-980a-16481b198851,d91d6a3e-c2fd-4fee-980a-16481b198856

@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message

@apiSuccessExample Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "deleted successfully",
}

MULTI-STATUS 207
@apiSuccess (MULTI-STATUS 207) {Number} code Xảy ra trong tường hợp các ids được truyền vào để xoá các template có cả trường hợp thành công và không thành công
@apiSuccess (MULTI-STATUS 207) {List} data Danh sách cụ thể từng trường hợp xoá template thành công hoặc không thành công

@apiSuccessExample Response: HTTP/1.1 207 MULTI-STATUS
{
    "code": 207,
    "data": [
        {
            "id": "d91d6a3e-c2fd-4fee-980a-16481b198855"
            "code": 200,
            "message": "deleted successfully",
        },
        {
            "id": "d91d6a3e-c2fd-4fee-980a-16481b198851"
            "code": 404,
            "message": "Not found",
        },
        {
            "id": "d91d6a3e-c2fd-4fee-980a-16481b198852"
            "code": 405,
            "message": "Not allowed",
        },
    ]
}
"""

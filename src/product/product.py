################### DEFINE ##########################

"""
@apiDefine product_filter
@apiParam	(product_filter:)  {Array}	    values			      Danh sách các giá trị cần filter.
                                                     
@apiParam	(product_filter:)  {String}	criteria_key		  Các key để filter đơn hàng.
                                                              Alow values:
                                                              <ul>
                                                                <li><code>cri_sku </code>: mã sku sản phẩm </li>
                                                                <li><code>cri_price_value </code>: giá tiền sản phẩm</li>
                                                                <li><code>cri_price_currency_unit </code>: Đơn vị giá tiền</li>
                                                                <li><code>cri_total </code>: số lượng sản phẩm trong kho </li>
                                                                <li><code>cri_tag </code>: Tag sản phẩm </li>
                                                                <li><code>cri_expiry_time </code>: Vòng đời sản phẩm </li>
                                                                <li><code>cri_store </code>: kho hàng</li>
                                                                <li><code>cri_product_of_store </code>: Tình trạng sản phẩm trong kho</li>
                                                                <li><code>cri_category </code>: danh mục sản phẩm</li>
                                                                <li><code>cri_supplier </code>: Nhà cung cấp</li>
                                                                <li><code>cri_channel </code>:  Khu vực hiển thị
                                                                                                <code>1: Web, 2: Mobile App </code></li>
                                                              </ul>
                                                                                                                  
@apiParam	(product_filter:)  {String}	operator_key		  Các toán tử để filter sản phẩm.
                                                              Alow value:
                                                              <code> 
                                                                "op_is_between", "op_is_greater_equal", "op_is_in",
                                                                 "op_is_equal", "op_is_greater", "op_is_has", 
                                                                 "op_is_has_not", "op_is_less_equal", "op_is_less",
                                                                 "op_is_empty", "op_is_not_empty", "op_is_multiple"
                                                              </code>       
"""

"""
@apiDefine after_paging_tokens
@apiVersion 1.0.0
@apiParam   (Query:)    {Number}    [per_page] Number of item per page.(<code>MAX per_page = 10000</code>)<br/> Example: <code>&per_page=5</code>
@apiParam   (Query:)    {Number}    [after_token] Token for requesting next page.

@apiSuccess     {Paging}    [paging]    Pagination.
<li><code>per_page:</code>Number of items on each page</li>
<li><code>total_page:</code>Number of pages</li>
<li><code>total_count:</code>Number of items</li>
@apiSuccess     {Object}    [paging..cursors]    Token for requesting next page or previous page.
@apiSuccess     {Object}    [paging..cursors..after]    Token for requesting next page
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh"
        },
        'per_page': per_page,
        'page_count': page_count,
        'total_count': total_count
    }
}
"""

# ********************************************************************
# ********************** Lấy danh sách sản phẩm **********************
# ********************************************************************

# v1.1.0
"""
@api {POST} /products Lấy danh sách sản phẩm
@apiDescription Lấy danh sách sản phẩm theo tenant một vài trường không được trả về
@apiName GetProductList
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging_tokens
@apiUse product_filter
@apiParam   (BODY:)  		{string}    merchant_id           id của sub merchant
@apiParam	(BODY:)			{String}	[search]			  Tìm kiếm sản phẩm theo tên
@apiParam	(BODY:)			{Array}	    [product_filter]	  Danh sách các điều kiện lọc sản phẩm
@apiParam	(BODY:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả. 
                                                              VD: ["name","code"]. Trường hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính
@apiParam (BODY:)     {Array}     [ignore_ids]    Danh sách các id product sẽ không hiển thị ở danh sách trả về.               
@apiParam	(Query:)		{String}	[search_category]   Dùng để tìm các sản phẩm nằm trong các danh mục thuộc danh mục cấp 1 có tên chưa search_category            
@apiParam (Query:)    {String}  [sort]              Tên field trường thông tin cần sắp xếp            
@apiParam	(Query:)		{String}	[order]   	          Kiểu sắp xếp:
                                                              <ul>
                                                                <li><code>asc: </code> sắp xếp tăng dần</li>
                                                                <li><code>desc: </code> sắp xếp giảm dần</li>
                                                              </ul>               
                                                              alow value: asc, desc      

@apiSuccess {Array} data Danh sách sản phẩm
@apiSuccess {string} data.id UUID sản phẩm
@apiSuccess {Object} data.name_product Tên sản phẩm
@apiSuccess {string} data.code Mã sản phẩm
@apiSuccess {number= 0-HIDEN 1-DISPLAY} data.status Trạng thái của sản phẩm.<code>HIDEN: Ẩn</code>, <code>DISPLAY: Hiển thị</code>
@apiSuccess {Object}      data.price              Giá của sản phẩm
@apiSuccess {String}      data.created_time       Thời gian tạo sản phẩm
@apiSuccess {Object}      data.web_image                Thông tin ảnh hiển thị ở web
@apiSuccess {Object}      data.web_image.url            Link ảnh avatar hiển thị trên web
@apiSuccess {Array}       data.web_image.descript_images Danh sách link ảnh mô tả hiển thị trên web
@apiSuccess {Object}      data.mobile_app_image             Thông tin ảnh hiển thị ở mobile
@apiSuccess {String}      data.mobile_app_image.url         Link ảnh avatar hiển thị trên mobile
@apiSuccess {Array}       data.mobile_app_image.descript_images         Danh sách link ảnh mô tả hiển thị trên mobile
@apiSuccess {Array}       data.supplier           DS ID nhà cung cấp
@apiSuccess {Array}       data.category           DS ID danh mục
@apiSuccess {number}      data.channel            Khu vực hiển thị <code>1: Web, 2: Mobile App </code>
@apiSuccess {Object}      data.expiry_date        Vòng đời sản phẩm

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
           "id": "c7229324-ffe3-4090-8acb-27093003f703",
            "name_product": {
                  "en": "Au beef 1",
                  "vi": "Bò úc 1"
                }
            ,
            "sku": {
                    "vi":"SKU123"
                  },
            "status": 0,
            "mobile_app_image": {
              "status": -1,
              "url": "/static/884670b5-cd97-4770-a6e7-edc518e559a1.jpg",
              "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
              },
            "web_image": {
              "status": 1,
              "url": "/static/afd6608d-2e14-4c4d-b043-fc2fea19e5ad.jpg",
              "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
              },
            "price":{
                "VND":100,
                "USD":20
            },
            "expiry_date": {
                "current_type_date": "day",
                "value": 3
            },
            "supplier": "c7229324-ffe3-4090-8acb-225573a69",
            "category": ["25366a2-ffe3-4090-8acb-225573a69"],
            "channel": [
                1,2
            ],
            "created_time": "2019-08-11T12:00:00Z"
        }
    ],
    "message": "request thành công."
}

"""

# ********************************************************************
# ********************** Lấy danh sách sản phẩm **********************
# ********************************************************************

# v1.1.0
"""
@api {POST} /products/actions/get-by-category-ids           Lấy danh sách sản phẩm theo danh mục  
@apiDescription Lấy danh sách sản phẩm theo danh mục
@apiName GetProductListByCategoryIds
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiParam   (BODY:)     {Array}     category_ids       Danh sách id danh mục sản phẩm
@apiParam   (BODY:)     {string}    [search]           Truyền lên sku hoặc là tên sản phẩm cần tìm kiếm

@apiSuccess {Array} data Danh sách sản phẩm
@apiSuccess {string} data.id UUID sản phẩm
@apiSuccess {Object} data.name_product Tên sản phẩm
@apiSuccess {string} data.code Mã sản phẩm
@apiSuccess {number= 0-HIDEN 1-DISPLAY} data.status Trạng thái của sản phẩm.<code>HIDEN: Ẩn</code>, <code>DISPLAY: Hiển thị</code>
@apiSuccess {Object}      data.price              Giá của sản phẩm
@apiSuccess {String}      data.created_time       Thời gian tạo sản phẩm
@apiSuccess {Object}      data.web_image                Thông tin ảnh hiển thị ở web
@apiSuccess {Object}      data.web_image.url            Link ảnh avatar hiển thị trên web
@apiSuccess {Array}       data.web_image.descript_images Danh sách link ảnh mô tả hiển thị trên web
@apiSuccess {Object}      data.mobile_app_image             Thông tin ảnh hiển thị ở mobile
@apiSuccess {String}      data.mobile_app_image.url         Link ảnh avatar hiển thị trên mobile
@apiSuccess {Array}       data.mobile_app_image.descript_images         Danh sách link ảnh mô tả hiển thị trên mobile
@apiSuccess {Array}       data.supplier           DS ID nhà cung cấp
@apiSuccess {Array}       data.category           DS ID danh mục
@apiSuccess {number}      data.channel            Khu vực hiển thị <code>1: Web, 2: Mobile App </code>
@apiSuccess {Object}      data.expiry_date        Vòng đời sản phẩm

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
           "id": "c7229324-ffe3-4090-8acb-27093003f703",
            "name_product": {
                  "en": "Au beef 1",
                  "vi": "Bò úc 1"
                }
            ,
            "sku": {
                    "vi":"SKU123"
                  },
            "status": 0,
            "mobile_app_image": {
              "status": -1,
              "url": "/static/884670b5-cd97-4770-a6e7-edc518e559a1.jpg",
              "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
              },
            "web_image": {
              "status": 1,
              "url": "/static/afd6608d-2e14-4c4d-b043-fc2fea19e5ad.jpg",
              "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
              },
            "price":{
                "VND":100,
                "USD":20
            },
            "expiry_date": {
                "current_type_date": "day",
                "value": 3
            },
            "supplier": "c7229324-ffe3-4090-8acb-225573a69",
            "category": ["25366a2-ffe3-4090-8acb-225573a69"],
            "channel": [
                1,2
            ],
            "created_time": "2019-08-11T12:00:00Z"
        }
    ],
    "message": "request thành công."
}

"""


# ****************************************************************************
# ********************** Lấy danh sách số lượng sản phẩm **********************
# ****************************************************************************
"""
@api {POST} /products/actions/count Lấy danh sách số lượng sản phẩm
@apiDescription Lấy danh sách số lượng sản phẩm
@apiName GetCountProducts
@apiGroup Product
@apiUse merchant_id_header
@apiVersion 1.0.0
@apiParam   (Body:)  		{string}    merchant_id           id của sub merchant
@apiParam	(BODY:)			{int}	[channel]				  Loại channel
																<code> 0:web ; 1:mobile; 2:all (default)</code>
@apiParam	(BODY:)			{String}	[category]		  	  Danh mục sản phẩm
@apiParam	(BODY:)			{String}	[supplier]		  Nhà cung cấp
@apiParam	(BODY:)			{String}	[search]			  Tìm kiếm sản phẩm theo tên
@apiParam	(BODY:)			{Array}	    [product_filter]	  Danh sách các điều kiện lọc sản phẩm
@apiParam	(BODY:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả. 
                                                              VD: ["name","code"]. Trường hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính
@apiParam	(Query:)		{String}	[sort]   	          Tên field trường thông tin cần sắp xếp                                                                             
@apiParam	(Query:)		{String}	[order]   	          Kiểu sắp xếp:
                                                              <ul>
                                                                <li><code>asc: </code> sắp xếp tăng dần</li>
                                                                <li><code>desc: </code> sắp xếp giảm dần</li>
                                                              </ul>               
                                                              alow value: asc, desc      


@apiSuccess {Array} data               Danh sách số lượng sản phẩm theo trạng thái
@apiSuccess {number= 0-HIDEN 1-DISPLAY} data.status Trạng thái của sản phẩm.<code>HIDEN: Ẩn</code>, <code>DISPLAY: Hiển thị</code>
@apiSuccess {number} data.count        Số lượng

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
	"merchant_id":"c7229324-ffe3-4090-8acb-225573a69",
    "code": 200,
    "data": [
        {
            "status": 1,
            "count": 50
        },
        {
            "status": 0,
            "count": 10
        }
    ]
    "message": "request thành công."
}
"""

# ********************************************************************
# ********************** Ẩn hiện sản phẩm **********************
# ********************************************************************
"""
@api {POST} /products/actions/status Ẩn hiện sản phẩm
@apiDescription Ẩn hiện sản phẩm
@apiName UpdateStatusProduct
@apiGroup Product
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiParam   (Body:)  		{string}    merchant_id    id của sub merchant
@apiParam	(BODY:)			{int}	[channel]		   Loại channel 
														<code> 0:web ; 1:mobile; 2:all (default)</code>
														<code> Nếu để 2 thì thay đổi của cả web và mobile</code>
@apiParam      (Body:)     {Array}    ids              Tập hợp các ID UUID của sản phẩm.
@apiParam      (Body:)     {number}   status           Trạng thái sản phảm. 0-HIDEN 1-DISPLAY

@apiParamExample {json} Body example
{
	"merchant_id":"c7229324-ffe3-4090-8acb-225573a69",
	"ids": [
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703"
	],
	"status": 1
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}

"""

# ********************************************************************
# ********************** Xóa sản phẩm **********************
# ********************************************************************
"""
@api {DELETE} /products Xóa sản phẩm
@apiDescription Xóa sản phẩm, sản phẩm vẫn còn dữ liệu nhưng không xuất hiện trong danh sách các sản phẩm. 1 khi đã xóa không khôi phục lại được
@apiUse merchant_id_header
@apiName DeleteProduct
@apiGroup Product
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiParam      (Query:)     {String}    ids              Tập hợp các UUID của sản phẩm.

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}

"""
# ********************************************************************
# ********************** Tạo sản phẩm **********************
# ********************************************************************

"""
@api {POST} /products/actions/create   Tạo mới sản phẩm
@apiDescription Tạo mới sản phẩm. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiName CreateProduct
@apiUse merchant_id_header
@apiGroup Product
@apiVersion 2.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {File}      [avatar]                           File ảnh đại diện  sản phẩm
@apiParam      (Body:)     {File}      [images]                           Danh sách file ảnh đại diện  sản phẩm
@apiParam      (Body:)     {File}      [avatar_mobile]                    File ảnh đại diện  sản phẩm
@apiParam      (Body:)     {File}      [images_mobile]                   Danh sách file ảnh đại diện  sản phẩm
@apiParam      (Body:)     {String}    data                            Thông tin  sản phẩm
@apiParam    (Body:) {String}    [data.supplier]                    Mã thương hiệu
@apiParam    (Body:) {Object}    data.name_product                      Tên sản phẩm                    
@apiParam    (Body:) {Object}    data.sku                     Mã sản phẩm
@apiParam    (Body:) {Object}    [data.price]             Giá sản phẩm
@apiParam    (Body:) {String}    [data.category]                Category id
@apiParam    (Body:) {Array}     [data.tags]                  Danh sách id tags gợi ý
@apiParam    (Body:) {Object}    data.channel               Trạng thái sản phẩm  <code> allow value: 1- web, 2- mobile app </code>
@apiParam    (Body:) {number= 0-HIDEN 1-DISPLAY}     data.channel.web           Trạng thái sản phẩm web
@apiParam    (Body:) {number= 0-HIDEN 1-DISPLAY}     data.status.mobile         Trạng thái sản phẩm mobile
@apiParam    (Body:) {Object}    [data.description]           Mô tả sản phẩm bằng html   
@apiParam    (Body:) {Object}    [data.expiry_date]           Vòng đời sản phẩm, ví dụ như hạn sử dụng của sản phẩm, thời gian sản phẩm còn giá trị
                                                              <code> allow value date: day, month, year</code>
        

@apiParamExample [json] Body example:
{
    "supplier": "5e68bbc95d3ff967f76bc8d8",
    "name_product": {
        "vi": "Bò úc",
        "en": "Au beef"
    },
    "sku": {
      "vi":"SKU123"
    },
    "price": {
        "VND": 100,
        "USD": 20
    },
    "category":[ "5e6845da52532be3d76fd0e1"],
    "tag": [
        "1234-1234-abcd-abcd",
        "2321-1234-abcd-abcc"
    ],
    "channel": [1,2],
    "description": {
        "vi": "<html>123</html>",
        "en": "<html>123</html>"
    },
    "expiry_date": {
        "day": 3
    },
    "hehe":3,
    "_dyn_single_line_number_1584202691323":{"vi":1},
    "_dyn_multi_line_text_1584202781173":[{"vi":"text1"},{"vi":"text2"}],
    "_dyn_single_line_text_1584202706167":{"vi":"text"},
    "_dyn_dropdown_single_line_number_1584202837453":{"vi":"text"},
    "_dyn_radio_1584203064374":{"vi":"text"},
    "_dyn_dropdown_multi_line_1584202942901":[{"vi":"text"},{"vi":"text2"}],
    "_dyn_date_picker1_1584204468546":"23/12/1972 23:30",
    "_dyn_checkbox1_1584203098239":[{"vi":"checkbox1"},{"vi":"checkbox2"},]
}


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "_dyn_checkbox1_1584203098239": [
      {
        "en": "gao",
        "vi": "cuồng phong"
      },
      {
        "en": "optimus prime",
        "vi": "optimus"
      }
    ],
    "_dyn_date_picker1_1584204468546": "23/12/1972 23:30",
    "_dyn_dropdown_multi_line2_1584202942901": [
      {
        "en": "gao2",
        "vi": "cuồng phong2"
      },
      {
        "en": "optimus prime2",
        "vi": "optimus2"
      }
    ],
    "_dyn_dropdown_single_line1_1584202837453": {
      "en": "gao",
      "vi": "cuồng phong"
    },
    "_dyn_multi_line2_1584202781173": [
      {
        "vi": "text1"
      },
      {
        "vi": "text2"
      }
    ],
    "_dyn_radio2_1584203064374": {
      "en": "optimus prime12",
      "vi": "optimus12"
    },
    "_dyn_single_line1_1584202691323": 1,
    "_dyn_single_line2_1584202706167": {
      "vi": "text"
    },
    "brand_ids": [
      "1b99bdcf-d582-4f49-9715-1b61dfff3924"
    ],
    "category": [
      "5e6845da52532be3d76fd0e1"
    ],
    "channel": [
      {
        "vi": "Web"
      },
      {
        "en": "Mobile App"
      }
    ],
    "created_by": null,
    "created_merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "created_time": "2020-03-15 17:47:54.920650",
    "description": {
      "en": "<html>123</html>",
      "vi": "<html>123</html>"
    },
    "expiry_date": {
      "day": 3
    },
    "hehe": 3,
    "id": "5e6e6a4a8b385212833e3406",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "name_product": {
      "en": "Au beef",
      "vi": "Bò úc"
    },
    "price": {
      "USD": 20,
      "VND": 100
    },
    "sku": {
      "vi":"SKU123"
    },
    "source_created": "web",
    "status": 1,
    "supplier": "5e68bbc95d3ff967f76bc8d8",
    "tag": [
      "1234-1234-abcd-abcd",
      "2321-1234-abcd-abcc"
    ],
    "mobile_app_image": {
              "status": -1,
              "url": "/static/884670b5-cd97-4770-a6e7-edc518e559a1.jpg",
              "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
              },
    "web_image": {
              "status": 1,
              "url": "/static/afd6608d-2e14-4c4d-b043-fc2fea19e5ad.jpg",
              "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
              },
  },
  "message": "request thành công."
}
"""

# ********************************************************************
# ********************** Tạo sản phẩm **********************
# ********************************************************************

"""
@api {POST} /products/actions/create   Tạo mới sản phẩm
@apiDescription Tạo mới sản phẩm. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiName CreateProduct
@apiUse merchant_id_header
@apiGroup Product
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {File}      [avatar]                          	File ảnh đại diện  sản phẩm
@apiParam      (Body:)     {File}      [images]                          	Danh sách file ảnh đại diện  sản phẩm
@apiParam      (Body:)     {File}      [avatar_mobile]                    File ảnh đại diện  sản phẩm
@apiParam      (Body:)     {File}      [images_mobile]                   Danh sách file ảnh đại diện  sản phẩm
@apiParam      (Body:)     {String}    data                            Thông tin  sản phẩm
@apiParam	   (Body:) {String}    [data.supplier]                    Mã thương hiệu
@apiParam	   (Body:) {Object}	   data.name_product					            Tên sản phẩm				            
@apiParam	   (Body:) {String}	   data.SKU					            Mã sản phẩm
@apiParam	   (Body:) {Object}	   [data.price]							Giá sản phẩm
@apiParam	   (Body:) {String}	   [data.category]				        Category id
@apiParam	   (Body:) {Array}     [data.tags]					        Danh sách id tags gợi ý
@apiParam	   (Body:) {Object}	   data.channel						    Trạng thái sản phẩm 
@apiParam	   (Body:) {number= 0-HIDEN 1-DISPLAY}	   data.channel.web						Trạng thái sản phẩm web
@apiParam	   (Body:) {number= 0-HIDEN 1-DISPLAY}	   data.status.mobile					Trạng thái sản phẩm mobile
@apiParam	   (Body:) {Object}    [data.description]				    Mô tả sản phẩm bằng html   
@apiParam	   (Body:) {Object}    [data.expiry_date]				    Vòng đời sản phẩm, ví dụ như hạn sử dụng của sản phẩm, thời gian sản phẩm còn giá trị
		    

@apiParamExample [json] Body example:
{	
	"supplier":  "5e68bbc95d3ff967f76bc8d8",
	"name_product":{
        	"vi":"Bò úc",
          "en":"Au beef"
          },
	"sku": {
      "vi":"SKU123"
    },
	"price":{
                "VND":100,
                "USD":20
                },
	"category":  ["5e68bbc95d3ff967f76bc8d8"],
	"tags": ["1234-1234-abcd-abcd",
			 "2321-1234-abcd-abcc"],
	"channel": {
			"web":1,
			"mobile":1
	},
	"description":{
                    	"vi":"<html>123</html>",

                    	"en":"<html>123</html>"
                },

	"expiry_date": {
			"day":3
      	},
    "stores": [
              {
                "name": {
                          "vi":"Quán bún bò",
                          "en":"Beef"
                          },
                "total": 1
              },
              {
                "name": {
                          "vi":"Quán cơm ",
                          "en":"Rice"
                          },
                "total": 1209
              }
            ]
}


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
	"code": 200,
    "data": {	
				"id": "c7229324-ffe3-4090-8acb-27093003f703",
				"sku": {
          "vi":"SKU123"
          },
				"mobile_app_image": {
              "status": -1,
              "url": "/static/884670b5-cd97-4770-a6e7-edc518e559a1.jpg",
              "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
              },
        "web_image": {
              "status": 1,
              "url": "/static/afd6608d-2e14-4c4d-b043-fc2fea19e5ad.jpg",
              "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
              },
				"created_time": "2019-08-11T12:00:00Z",
				"supplier":  "5e68bbc95d3ff967f76bc8d8",
				"merchant_id": "1234-1234-abcd-abcd",
				"name_product":{
          "vi":"Bò úc",
          "en":"Au beef"
          },
				"sku": {
          "vi":"SKU123"
          },
				"price":{
                "VND":100,
                "USD":20
                },
				"category":  ["5e68bbc95d3ff967f76bc8d8"],
				"tags":["1234-1234-abcd-abcd",
						"2321-1234-abcd-abcc"],
				"channel": {
						"web":1,
						"mobile":1
				},
				"description":{
                      "vi":"<html>123</html>",

                      "en":"<html>123</html>"
                },
				"expiry_date": {
            						"day":3
        						},
        "stores": [
				              {
				                "name": {
				                          "vi":"Quán bún bò",
				                          "en":"Beef"
				                          },
				                "total": 1
				              },
				              {
				                "name": {
				                          "vi":"Quán cơm ",
				                          "en":"Rice"
				                          },
				                "total": 1209
				              }
            				],
				"status":1
			},
	"message": "request thành công."
}
"""


# ********************************************************************
# ********************** Cập nhập sản phẩm **********************
# ********************************************************************

"""
@api {PUT} /products/<product_id>  Cập nhập sản phẩm
@apiDescription Cập nhập sản phẩm. Dịch vụ gửi lên request dạng <code>form-data</code>
@apiName UpdateProduct
@apiUse merchant_id_header
@apiGroup Product
@apiVersion 1.0.0

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)     {File}      [avatar]                          	File ảnh đại diện  sản phẩm trên web
																		<code>truyền ảnh mới nếu muốn up ảnh đại diện</code>
@apiParam      (Body:)     {File}      [images]                          	Danh sách file ảnh đại diện  sản phẩm trên web
																		<code>truyền file lên nếu muốn thêm ảnh mô tả vào chỗ ảnh mô tả hiện tại</code>
@apiParam      (Body:)     {File}      [avatar_mobile]                    File ảnh đại diện  sản phẩm trên mobile
																		<code>truyền lên nếu muốn thay ảnh đại diện</code>
																		<code>truyền url nếu muốn đổi ảnh đại diện</code>
@apiParam      (Body:)     {File}      [images_mobile]                  Danh sách file ảnh đại diện  sản phẩm trên mobile
																		<code>truyền file lên nếu muốn thêm ảnh mô tả vào chỗ ảnh mô tả hiện tại</code>
@apiParam      (Body:)     {Object}    data                            Thông tin  sản phẩm
@apiParam      (Body:)     {int}	   data.type                             Loại cập nhập sản phẩm
                                                              			<ul>
                                                                			<li><code>1 </code>: sửa nhanh (default)</li> 
																			<li><code>2 </code>: sửa chi tiết</li>
																		</ul>
@apiParam      (Body:)     {String}	   [data.avatar]                      url ảnh đại diện  sản phẩm trên web
																		<code>truyền url nếu muốn đổi ảnh đại diện có sẵn không truyền lên nếu thay bằng ảnh upload</code>
@apiParam      (Body:)     {String}	   [data.avatar_mobile]               url ảnh đại diện  sản phẩm trên mobile
																		<code>truyền url nếu muốn đổi ảnh đại diện có sẵn không truyền lên nếu thay bằng ảnh upload</code>
@apiParam      (Body:)     {Array}	   [data.delete_web_image]                 url các ảnh cần xóa
																		<code>truyền url các ảnh cần xóa</code>	
@apiParam      (Body:)     {Array}	   [data.delete_mobile_app_image]          url các ảnh cần xóa
																		<code>truyền url các ảnh cần xóa</code>						
@apiParam      (Body:)	{String}    [data.supplier]                    Mã thương hiệu
@apiParam	   (Body:)	{Object}	   data.name_product				Tên sản phẩm		            
@apiParam	   (Body:)	{String}	   data.SKU					        Mã sản phẩm
@apiParam	   (Body:)	{Object}	   [data.price]						Giá sản phẩm			
@apiParam	   (Body:)	{String}	   [data.category]				    Category id
@apiParam	   (Body:)	{Array}     [data.tags]					        Danh sách id tags gợi ý
@apiParam	   (Body:)	{Object}	   data.channel						Trạng thái sản phẩm 
@apiParam	   (Body:) {number= 0-HIDEN 1-DISPLAY}	   data.channel.web						Trạng thái sản phẩm web
@apiParam	   (Body:) {number= 0-HIDEN 1-DISPLAY}	   data.channel.mobile					Trạng thái sản phẩm mobile
@apiParam	   (Body:)	{Object}    [data.description]				    Mô tả sản phẩm bằng html
@apiParam	   (Body:) {Object}    [data.expiry_date]				    Vòng đời sản phẩm, ví dụ như hạn sử dụng của sản phẩm, thời gian sản phẩm còn giá trị	    
@apiParam	   (Body:)	{String}    [data.images]				        Danh sách id tương ứng với id của images update
																		<code>danh sách id muốn gán cho ảnh, tự gen id nếu không truyền lên</code>
@apiParam	   (Body:)	{arraystring}    [data.delete_images]			Danh sách file mô tả của ảnh được xóa khi update
@apiParam	   (Body:)	{Object}    [data.related_product]				Sản phẩm liên quan
																		<code> chỉ áp dụng cho sửa chi tiết </code>
@apiParam	   (Body:)	{Object}    [data.stores]							Thông tin về kho																
@apiParam	   (Body:)	{String}    data.stores.name					Tên kho
@apiParam	   (Body:)	{int}       data.stores.total						Số lượng sản phẩm trong kho


@apiParamExample [json] Body example for update quick:
{	
	"supplier":  "5e68bbc95d3ff967f76bc8d8",
	"merchant_id": "1234-1234-abcd-abcd",
	"name_product":{
          "vi":"Bò úc",
          "en":"Au beef"
          },
	"sku": {
      "vi":"SKU123"
    },
	"price":{
                "VND":100,
                "USD":20
                },
	"category":  ["5e68bbc95d3ff967f76bc8d8"],
	"tags": ["1234-1234-abcd-abcd",
			 "2321-1234-abcd-abcc"],
	"channel": {
			"web":1,
			"mobile":1
	},
	"description":{
                      "vi":"<html>123</html>",

                      "en":"<html>123</html>"
                },
	"expiry_date": {
      "day":3
        },
  "delete_web_image":["/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg","/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg"],
  "delete_mobile_app_image":["/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg","/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg"],
	"avatar":"/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg",
	"avatar_mobile":"/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg"
}

@apiParamExample [json] Body example for update detail:
{	
	"supplier":  "5e68bbc95d3ff967f76bc8d8",
	"merchant_id": "1234-1234-abcd-abcd",
	"name_product":{
          "vi":"Bò úc",
          "en":"Au beef"
          },
	"sku": {
      "vi":"SKU123"
    },
	"price":{
                "VND":100,
                "USD":20
                },
	"category":  ["5e68bbc95d3ff967f76bc8d8"],
	"tags": ["1234-1234-abcd-abcd",
			 "2321-1234-abcd-abcc"],
	"channel": {
			"web":1,
			"mobile":1
	},
	"description":{
                      "vi":"<html>123</html>",

                      "en":"<html>123</html>"
                },
	"expiry_date": {
      "day":3
        },
	"related_product": {
      "common_product": [
        "5e82fdf44803c3e9349e7458",
        "5e82fe4a8a7a13d4479e745b"
      ],
      "relate_product": [
        "5e82feeeec34aae3bf9e7458"
      ],
      "suggest_product": [
        "5e83008c8a7a13d4479e745c",
        "5e8302a64803c3e9349e7459"
      ]
    },
	"stores": [
              {
                "name": {
                          "vi":"Quán bún bò",
                          "en":"Beef"
                          },
                "total": 1
              },
              {
                "name": {
                          "vi":"Quán cơm ",
                          "en":"Rice"
                          },
                "total": 1209
              }
            ],
  "delete_images":["/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg","/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg"],
	"delete_web_image":["/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg","/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg"],
 	"delete_mobile_app_image":["/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg","/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg"],
	"avatar":"/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg",
	"avatar_mobile":"/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg"
}


@apiSuccessExample {json} Response for update quick: HTTP/1.1 200 OK
{
	"code": 200,
  "data": 
    {
      "sku": {
        "vi":"SKU123"
        },
        "delete_web_image":
          ["/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg","/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg"],
        "delete_mobile_app_image":
          ["/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg","/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg"],
        "avatar":"/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg",
        "avatar_mobile":"/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg",
        "created_time": "2019-08-11T12:00:00Z",
        "supplier":  "5e68bbc95d3ff967f76bc8d8",
        "merchant_id": "1234-1234-abcd-abcd",
        "name_product":{
          "vi":"Bò úc",
          "en":"Au beef"
          },
        "sku": {
          "vi":"SKU123"
          },
        "price":{
                "VND":100,
                "USD":20
                },
        "category":  ["5e68bbc95d3ff967f76bc8d8"],
        "tags":["1234-1234-abcd-abcd", "2321-1234-abcd-abcc"],
        "channel": {
          "web":1,
          "mobile":1
          },
        "description":{
          "vi":"<html>123</html>",
          "en":"<html>123</html>"
          },
        "expiry_date": {
          "day":3
          },
        "mobile_app_image": {
          "status": -1,
          "url": "/static/884670b5-cd97-4770-a6e7-edc518e559a1.jpg",
          "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
          },
        "web_image": {
          "status": 1,
          "url": "/static/afd6608d-2e14-4c4d-b043-fc2fea19e5ad.jpg",
          "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
          }
        }
    },
	"message": "request thành công."
}

@apiSuccessExample {json} Response for update detail: HTTP/1.1 200 OK
{    
	"code": 200,
  data": 
    {
      "delete_web_image":
          ["/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg","/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg"],
      "delete_mobile_app_image":
          ["/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg","/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg"],
      "avatar":"/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg",
      "avatar_mobile":"/static/13467253-3ec0-4fcf-925c-d200d20095cf.jpeg",
      "created_time": "2019-08-11T12:00:00Z",
      "supplier":  "5e68bbc95d3ff967f76bc8d8",
      "merchant_id": "1234-1234-abcd-abcd",
      "name_product":{
          "vi":"Bò úc",
          "en":"Au beef"
          },
      "sku": {
          "vi":"SKU123"
          },
      "price":{
                "VND":100,
                "USD":20
                },
      "category":  ["5e68bbc95d3ff967f76bc8d8"],
      "tags":["1234-1234-abcd-abcd", "2321-1234-abcd-abcc"],
      "channel": {
          "web":1,
          "mobile":1
        },
      "description":{
          "vi":"<html>123</html>",
          "en":"<html>123</html>"
          },
      "expiry_date": {
          "day":3
          },
      "related_product": {
          "common_product": [ "5e82fdf44803c3e9349e7458", "5e82fe4a8a7a13d4479e745b"],
          "relate_product": [ "5e82feeeec34aae3bf9e7458"],
          "suggest_product": [ "5e83008c8a7a13d4479e745c", "5e8302a64803c3e9349e7459"]
          },
      "stores": [
          {
            "name": {
              "vi":"Quán bún bò",
              "en":"Beef"
              },
            "total": 1
          },
          {
            "name": {
              "vi":"Quán cơm ",
              "en":"Rice"
              },
            "total": 1209
          }
        ],
      "mobile_app_image": {
          "status": -1,
          "url": "/static/884670b5-cd97-4770-a6e7-edc518e559a1.jpg",
          "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
          },
      "web_image": {
          "status": 1,
          "url": "/static/afd6608d-2e14-4c4d-b043-fc2fea19e5ad.jpg",
          "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
        }
    },
	"message": "request thành công."
}
"""


# ********************************************************************
# ********************** Lấy chi tiết sản phẩm **********************
# ********************************************************************
"""
@api {GET} /products/<product_id> Lấy chi tiết sản phẩm
@apiDescription Lấy chi tiết sản phẩm 
@apiName GetDetailProduct
@apiUse merchant_id_header
@apiGroup Product
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess {string} data.id      UUID sản phẩm
@apiSuccess {Object} data.name_product    Tên sản phẩm
@apiSuccess {string} data.SKU    Mã sản phẩm
@apiSuccess {Object}      data.price              Giá của sản phẩm
@apiSuccess {String}      data.created_time       Thời gian tạo sản phẩm
@apiSuccess {String}      data.supplier        UUID thương hiệu sản phẩm
@apiSuccess {String}      data.category        UUID danh mục sản phẩm
@apiSuccess {Object}      data.description         Mô tả sản phẩm
@apiSuccess {Array}			data.tags                     Danh sách tags sản phẩm
@apiSuccess {string} data.tags.id                UUID tags
@apiSuccess {string} data.tags.name              Tên tags
@apiSuccess {Object}      data.web_image                Thông tin ảnh hiển thị ở web
@apiSuccess {Object}      data.web_image.url            Link ảnh avatar hiển thị trên web
@apiSuccess {Array}       data.web_image.descript_images Danh sách link ảnh mô tả hiển thị trên web
@apiSuccess {Object}      data.mobile_app_image             Thông tin ảnh hiển thị ở mobile
@apiSuccess {String}      data.mobile_app_image.url         Link ảnh avatar hiển thị trên mobile
@apiSuccess {Array}       data.mobile_app_image.descript_images         Danh sách link ảnh mô tả hiển thị trên mobile
@apiSuccess	   {Object}	   data.channel						    Trạng thái sản phẩm 
@apiSuccess	   {number= 0-HIDEN 1-DISPLAY}	   data.channel.web						Trạng thái sản phẩm web
@apiSuccess	   {number= 0-HIDEN 1-DISPLAY}	   data.channel.mobile					Trạng thái sản phẩm mobile
@apiSuccess	 {Object}    [data.stores]							Thông tin về kho
																		<code> chỉ áp dụng cho sửa chi tiết </code>																		
@apiSuccess	   	{String}    data.stores.name						Tên kho
@apiSuccess	   	{int}       data.stores.total						Số lượng sản phẩm trong kho
@apiSuccess	    {Object}    [data.expiry_date]				    Vòng đời sản phẩm, ví dụ như hạn sử dụng của sản phẩm, thời gian sản phẩm còn giá trị
	

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "_dyn_45_1584502360366": null,
    "_dyn_checkboxes_1584513695553": [
      {
        "vi": "CheckboxesCheckboxes"
      }
    ],
    "_dyn_dd_mm_1584513719971": "23/12",
    "_dyn_dd_mm_yyyy_1584513739843": null,
    "_dyn_dd_mm_yyyy_hh_mm_1584513769693": null,
    "_dyn_dropdown_multiple_select_1584507243166": [
      {
        "vi": "Dropdown multiple select Dropdown multiple select"
      },
      {
        "vi": "Dropdown multiple select"
      }
    ],
    "_dyn_dropdown_single_select_1584507212116": {
      "vi": "Những đóa hoa hướng dương hệt như ánh mặt trời rực rỡ - chính vậy mà hướng dương được xem là loài hoa tượng trưng cho sự đam mê mãnh liệt.Một loài hoa tươi sáng và vui vẻ, mạnh mẽ nhưng cũng đầy dịu dàng và đam mê -Hướng Dương chính là một món quà bày tỏ sự quan tâm ấm áp nhất."
    },
    "_dyn_haha_1584502311640": {
      "vi": "bobo"
    },
    "_dyn_khong_chi_la_loai_hoa_duoc_tran_quy_vi_cung_cap_ng_1584506940484": null,
    "_dyn_multiple_line_text_chu_1584507153172": [
      {
        "en": "kiki",
        "vi": "lalala"
      },
      {
        "vi": "chipu"
      }
    ],
    "_dyn_multiple_line_text_so_1584507168820": [
      2321,
      29127
    ],
    "_dyn_radio_button_1584507274164": {
      "vi": "Radio button Radio button"
    },
    "_dyn_single_line_text_chu_1584507104804": null,
    "_dyn_single_line_text_so_1584507128460": 23,
    "category": [
      {
        "en": "mỹ phẩm3",
        "vi": "mỹ phẩm3"
      }
    ],
    "channel": [
      "Web"
    ],
    "created_time": "2020-03-20T02:40:42Z",
    "description": {
      "en": "<html>1234</html>",
      "vi": "<html>123</html>"
    },
    "expiry_date": {
      "year": 10
    },
    "id": "5e742d2a03be90a30470971e",
    "mobile_app_image": {
          "status": -1,
          "url": "/static/884670b5-cd97-4770-a6e7-edc518e559a1.jpg",
          "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
          },
    "web_image": {
          "status": 1,
          "url": "/static/afd6608d-2e14-4c4d-b043-fc2fea19e5ad.jpg",
          "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
        },
    "name_product": {
      "en": "cuongcung",
      "vi": "cuong"
    },
    "price": {
      "USD": 696900,
      "VND": 1000
    },
    "sku": {
      "vi": "SKU1234"
    },
    "store": null
  },
  "message": "request thành công."
}

"""


# ********************************************************************
# ********************** Lấy danh sách chi tiết sản phẩm **********************
# ********************************************************************
"""
@api {POST} /products/actions/detail Lấy danh sách chi tiết sản phẩm
@apiDescription Lấy danh sách chi tiết sản phẩm 
@apiName GetListDetailProduct
@apiUse merchant_id_header
@apiGroup Product
@apiVersion 1.0.0

@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiParam   (Body:)  		{string}    merchant_id           id của sub merchant
@apiParam   (Body:)  		{Array}    [ids]           Danh sách sản phẩm lấy chi tiết
@apiParam   (Body:)     {Array}    [sku_list]       Danh sách danh mục sản phẩm cần tìm 
@apiParam      (Body:)  {string}      merchant_id                         id của sub merchant
@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess {string} data.id      UUID sản phẩm
@apiSuccess {Object} data.name_product    Tên sản phẩm
@apiSuccess {string} data.SKU    Mã sản phẩm
@apiSuccess {Object}      data.price              Giá của sản phẩm
@apiSuccess {String}      data.created_time       Thời gian tạo sản phẩm
@apiSuccess {String}      data.supplier        UUID thương hiệu sản phẩm
@apiSuccess {String}      data.category        UUID danh mục sản phẩm
@apiSuccess {Object}      data.description         Mô tả sản phẩm
@apiSuccess {Array}			data.tags                     Danh sách tags sản phẩm
@apiSuccess {string} data.tags.id                UUID tags
@apiSuccess {string} data.tags.name              Tên tags
@apiSuccess {Object}      data.web_image                Thông tin ảnh hiển thị ở web
@apiSuccess {Object}      data.web_image.url            Link ảnh avatar hiển thị trên web
@apiSuccess {Array}       data.web_image.descript_images Danh sách link ảnh mô tả hiển thị trên web
@apiSuccess {Object}      data.mobile_app_image             Thông tin ảnh hiển thị ở mobile
@apiSuccess {String}      data.mobile_app_image.url         Link ảnh avatar hiển thị trên mobile
@apiSuccess {Array}       data.mobile_app_image.descript_images         Danh sách link ảnh mô tả hiển thị trên mobile
@apiSuccess	   {Object}	   data.channel						    Trạng thái sản phẩm 
@apiSuccess	   {number= 0-HIDEN 1-DISPLAY}	   data.channel.web						Trạng thái sản phẩm web
@apiSuccess	   {number= 0-HIDEN 1-DISPLAY}	   data.channel.mobile					Trạng thái sản phẩm mobile
@apiSuccess	   	{Object}    [data.stores]							Thông tin về kho
																		<code> chỉ áp dụng cho sửa chi tiết </code>																		
@apiSuccess	   	{String}    data.stores.name						Tên kho
@apiSuccess	   	{int}       data.stores.total						Số lượng sản phẩm trong kho
@apiSuccess	    {Object}    [data.expiry_date]				    Vòng đời sản phẩm, ví dụ như hạn sử dụng của sản phẩm, thời gian sản phẩm còn giá trị


@apiParamExample {json} Body example
{
	"merchant_id":"c7229324-ffe3-4090-8acb-225573a69",
	"ids": [
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703",
        "c7229324-ffe3-4090-8acb-27093003f703"
	],
  "sku":["SKU1","SKU2"]
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": 
        [
           {
           "id": "c7229324-ffe3-4090-8acb-27093003f703",
           "sku": {
                  "vi":"SKU123"
                },
            "name_product":
                    {
                      "vi":"Bình sữa cho Lộc cà ná",
                      "en":"Milk bottle for Loc",
                    },
            "channel": {
            	"web":1,
            	"mobile":1
            	},
           "mobile_app_image": {
              "status": -1,
              "url": "/static/884670b5-cd97-4770-a6e7-edc518e559a1.jpg",
              "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
              },
            "web_image": {
              "status": 1,
              "url": "/static/afd6608d-2e14-4c4d-b043-fc2fea19e5ad.jpg",
              "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
              },
            "price":{
                "VND":100,
                "USD":20
                },
            "supplier":  "5e68bbc95d3ff967f76bc8d8",
            "category":  ["5e68bbc95d3ff967f76bc8d8"],
            "description":{
                      "vi":"<html>123</html>",

                      "en":"<html>123</html>"
                },
            "tags":[
                {
                    "id": "af25367-ffe3-4090-8acb-27093003f703",
                    "name": "Bình sữa"
                }
            ],
            "created_time": "2019-08-11T12:00:00Z",
            "status":1
            "expiry_date": {
                "day":3
                  }
            "related_product": {
              "common_product": [
                "5e82fdf44803c3e9349e7458",
                "5e82fe4a8a7a13d4479e745b"
              ],
              "relate_product": [
                "5e82feeeec34aae3bf9e7458"
              ],
              "suggest_product": [
                "5e83008c8a7a13d4479e745c",
                "5e8302a64803c3e9349e7459"
              ]
            },
            "stores": [
            	{
            		"name": {
                          "vi":"Quán bún bò",
                          "en":"Beef"
                          },
            		"total": 1
            	},
            	{
            		"name": {
                          "vi":"Quán cơm ",
                          "en":"Rice"
                          },
            		"total": 1209
            	}
            ],
        },
        ]
    ,
    "message": "request thành công."
}

"""

# ********************************************************************
# ********************** Lấy danh sách tên sản phẩm **********************
# ********************************************************************
"""
@api {GET} /products/name Lấy danh sách tên sản phẩm theo id sản phẩm
@apiDescription Lấy danh sách tên sản phẩm theo id sản phẩm
@apiName GetListNameProduct
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiParam   (Query:)     {string}    ids           Danh sách sản phẩm lấy chi tiết. Cách nhau bởi dấu "," vd: ids: "akdowad-2312mxca,ldiawjdoakwdocijwamc431"
@apiParam   (Query:)     {string}    [fields]   Danh sách field_key của các trường muốn trả về (các fields trả về cách nhau bởi dấu ",")
@apiSuccess {Object}   data  Nội dung trả về
@apiSuccess {Object}   data.field Giá trị của field yêu cầu trả về
@apiSuccess {Object}   data.expiry_date Vòng đời sản phẩm 
@apiSuccess {Object}   data.id_ecm  id sản phẩm bên ecm
@apiSuccess {Object}   data.id_product  id sản phẩm bên product
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "_dyn_thoi_gian_het_han____tinh_bang_ngay_1596032968425": {
                "vi": "23"
            },
            "expiry_date": {},
            "id": "5f222c1d5560a54bb9dd551a",
            "id_ecm": "5e05dfcf-08ab-45fc-ab9f-fb1f30e6fd12",
            "id_product": "5f222c1d5560a54bb9dd551a",
            "mobile_app_image": {
                "status": -1
            },
            "name_product": {
                "vi": "sptest"
            },
            "web_image": {
                "status": -1
            }
        },
        {
            "_dyn_thoi_gian_het_han____tinh_bang_ngay_1596032968425": {},
            "expiry_date": {},
            "id": "5f222c895560a54bb9dd551b",
            "id_ecm": "e012e4b9-010f-46cc-acb6-0f547d9467fd",
            "id_product": "5f222c895560a54bb9dd551b",
            "mobile_app_image": {
                "status": -1
            },
            "name_product": {
                "vi": "test k có thời gian"
            },
            "web_image": {
                "status": -1
            }
        }
    ],
    "message": "request thành công."
}
"""

# ********************************************************************
# ********************** Lấy danh sách sản phẩm theo danh mục ********
# ********************************************************************
"""
@api {GET} /products/action/categories Lấy danh sách sản phẩm theo danh mục
@apiDescription Lấy danh sách sản phẩm theo danh mục
@apiName GetListProductByCategory
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging
@apiParam   (Query:)     {string}    [merchant_ids]    Danh sách merchant_id cần tìm kiếm <code> cách nhau bởi dấu ","</code>
@apiParam   (Query:)     {string}    category_id           id danh muc san pham can tim kiếm
@apiParam   (Query:)     {string}    [search]           Truyền lên sku hoặc là tên sản phẩm cần tìm kiếm
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data":
        {
            "category_id": "uuid",
            "name":{
                      "vi":"Mẹ và bé",
                      "en":"Mom and baby"
                    },
            "products":[
              {
                "id": "5f222c1d5560a54bb9dd551a",
                "name_product": {
                  "vi": "sptest"
                  },
                "web_image": {
                  "status": 1,
                  "url": "/static/afd6608d-2e14-4c4d-b043-fc2fea19e5ad.jpg",
                  "descript_images": ["/static/f9298471-06ef-4459-a88e-b2a0b14a4021.jpeg",…]
                  },
                "sku": {
                  "vi":"SKU123"
                }  
              },
              ....
            ],
            "paging":{
                "page":1,
                "per_page":15,
                "total_page":1,
                "total_item":10
            }
        },
    "message": "request thành công."
}
"""


# ********************************************************************
# ********************** Upsert sản phẩm  ****************************
# ********************************************************************
"""
@api {POST} /products/actions/upsert Upsert sản phẩm
@apiDescription Thêm sản phẩm theo trường base, nếu sản phẩm đã tồn tại rồi thì trả lại trường base
@apiName UpsertProduct
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiParam    (Body:) {Object}    merchant_id                     ID của merchant
@apiParam    (Body:) {Object}    name_product                      Tên sản phẩm                    
@apiParam    (Body:) {Object}    sku                     Mã sản phẩm
@apiParam    (Body:) {Object}    channel               Trạng thái sản phẩm  <code> allow value: 1- web, 2- mobile app </code>
@apiParam    (Body:) {Object}    [supplier]             Thương hiệu
@apiParam    (Body:) {Object}    [price]             Giá sản phẩm
@apiParam    (Body:) {String}    [category]                Category id
@apiParam    (Body:) {Array}     [tags]                  Danh sách id tags gợi ý
@apiParam    (Body:) {Object}    [description]           Mô tả sản phẩm bằng html   
@apiParam    (Body:) {Object}    [expiry_date]           Vòng đời sản phẩm, ví dụ như hạn sử dụng của sản phẩm, thời gian sản phẩm còn giá trị
                                                              <code> allow value date: day, month, year</code>
@apiSuccess {Integer}  code    Mã phản hồi

@apiParamExample [json] Body example:
{
	"merchant_id": "uuid"
    "name_product": {
        "vi": "Bò úc",
        "en": "Au beef"
    },
    "sku": {
      "vi":"SKU123"
    },
    "price": {
        "VND": 100,
        "USD": 20
    },
    "category":[ "5e6845da52532be3d76fd0e1"],
    "tag": [
        "1234-1234-abcd-abcd",
        "2321-1234-abcd-abcc"
    ],
    "channel": [1,2],
    "description": {
        "vi": "<html>123</html>",
        "en": "<html>123</html>"
    },
    "expiry_date": {
        "day": 3
    },
    "supplier": {
    	"name": HL Tower ,
    	"code": 82 Duy Tân
    }
}

@apiSuccess {Integer}  code    Mã phản hồi
@apiSuccess    {Object}    merchant_id                     ID của merchant
@apiSuccess    {Object}    name_product                      Tên sản phẩm                    
@apiSuccess    {Object}    sku                     Mã sản phẩm
@apiSuccess    {Object}    channel               Trạng thái sản phẩm  <code> allow value: 1- web, 2- mobile app </code>
@apiSuccess    {String}    supplier                    Mã thương hiệu
@apiSuccess    {Object}    price             Giá sản phẩm
@apiSuccess    {String}    category                Category id
@apiSuccess    {Array}     tags                  Danh sách id tags gợi ý
@apiSuccess    {Object}    description           Mô tả sản phẩm bằng html   
@apiSuccess    {Object}    expiry_date           Vòng đời sản phẩm, ví dụ như hạn sử dụng của sản phẩm, thời gian sản phẩm còn giá trị
                                                              <code> allow value date: day, month, year</code>

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
  	"merchant_id": "uuid",
  	"id": "5e6e6a4a8b385212833e3406",
  	"name_product": {
      "en": "Au beef",
      "vi": "Bò úc"
    },  
    "sku": {
      "vi":"SKU123"
    },
    "category": [
      "5e6845da52532be3d76fd0e1"
    ],
    "channel": [
      {
        "vi": "Web"
      },
      {
        "en": "Mobile App"
      }
    ],
    "description": {
      "en": "<html>123</html>",
      "vi": "<html>123</html>"
    },
    "expiry_date": {
      "day": 3
    },
    "price": {
      "USD": 20,
      "VND": 100
    },
    "supplier": "5e68bbc95d3ff967f76bc8d8",
    "tag": [
      "1234-1234-abcd-abcd",
      "2321-1234-abcd-abcc"
    ]
  },
  "message": "request thành công."
}
"""
# ********************************************************************
# ********************** Upsert sản phẩm  ****************************
# ********************************************************************
"""
@api {POST} /products/actions/bulk_upsert Bulk upsert sản phẩm
@apiDescription Thêm danh sach sản phẩm theo trường base, nếu sản phẩm đã tồn tại rồi thì trả lại trường base
@apiName BulkUpsertProduct
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiParam    (Body:) {Object}    merchant_id                     ID của merchant
@apiParam    (Body:) {Object}    name_product                      Tên sản phẩm                    
@apiParam    (Body:) {Object}    sku                     Mã sản phẩm
@apiParam    (Body:) {Object}    channel               Trạng thái sản phẩm  <code> allow value: 1- web, 2- mobile app </code>
@apiParam    (Body:) {Object}    [supplier]             Thương hiệu
@apiParam    (Body:) {Object}    [price]             Giá sản phẩm
@apiParam    (Body:) {String}    [category]                Category id
@apiParam    (Body:) {Array}     [tags]                  Danh sách id tags gợi ý
@apiParam    (Body:) {Object}    [description]           Mô tả sản phẩm bằng html   
@apiParam    (Body:) {Object}    [expiry_date]           Vòng đời sản phẩm, ví dụ như hạn sử dụng của sản phẩm, thời gian sản phẩm còn giá trị
                                                              <code> allow value date: day, month, year</code>
@apiSuccess {Integer}  code    Mã phản hồi

@apiParamExample [json] Body example:
[
    {
        "merchant_id": "uuid",
        "name_product": {
            "vi": "Bò úc",
            "en": "Au beef"
        },
        "sku": {
          "vi":"SKU123"
        },
        "price": {
           "VND": 100,
           "USD": 20
        },
        "category":[ "5e6845da52532be3d76fd0e1"],
        "tag": [
           "1234-1234-abcd-abcd",
           "2321-1234-abcd-abcc"
        ],
        "channel": [1,2],
        "description": {
           "vi": "<html>123</html>",
           "en": "<html>123</html>"
        },
        "expiry_date": {
           "day": 3
        },
        "supplier": {
           "name": "HL Tower" ,
           "code": "82 Duy Tân"
        }
    },
    {
        "merchant_id": "uuid",
        "name_product": {
            "vi": "Bò úc 2",
            "en": "Au beef 2"
        },
        "sku": {
          "vi":"SKU1234"
        },
        "price": {
           "VND": 1001,
           "USD": 201
        },
        "category":[ "5e6845da52532be3d76fd0e1"],
        "tag": [
           "1234-1234-abcd-abcd",
           "2321-1234-abcd-abcc"
        ],
        "channel": [1,2],
        "description": {
           "vi": "<html>123</html>",
           "en": "<html>123</html>"
        },
        "expiry_date": {
           "day": 3
        },
        "supplier": {
           "name": "HL Tower" ,
           "code": "82 Duy Tân"
        }
    }
    ,...
]


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""

# ********************************************************************
# ********************** Lấy danh sách ids của sản phẩm        ********
# ********************************************************************
"""
@api {GET} /products/actions/get_ids Lấy danh sách ids của sản phẩm
@apiDescription Lấy danh sách ids của sản phẩm
@apiName GetListIdsProduct
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse paging
@apiParam   (Query:)        {Number}    [page]   Vị trí page cần lấy dữ liệu. MIN_VALUE=1 Example: &page=2 Giá trị mặc định: 1            
@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "product_ids": [
        "5ea6ed0c78b6f0d32e325676",
        "5ea6ed0c78b6f0d32e32567c",
        "5ea6ed0c78b6f0d32e32567f",
        "5ea6ed0c78b6f0d32e325682",
        "5ea6ed0c78b6f0d32e325688"
    ]
}
"""
# ********************************************************************
# ********************** Lấy danh sách ids của sản phẩm        ********
# ********************************************************************
"""
@api {GET} /products/ids                   Lấy danh sách ids của sản phẩm mới
@apiDescription Lấy danh sách ids của sản phẩm mới
@apiName GetListIdsProductNew
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse after_paging_tokens

@apiParam (Query:)     {String}     [search]   Từ khoá muốn tìm kiếm. Từ khoá này sẽ tìm kiếm giá trị của name và sku.
            
@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "product_ids": [
        "5ea6ed0c78b6f0d32e325676",
        "5ea6ed0c78b6f0d32e32567c",
        "5ea6ed0c78b6f0d32e32567f",
        "5ea6ed0c78b6f0d32e325682",
        "5ea6ed0c78b6f0d32e325688"
    ]
}
"""

# ********************************************************************
# ********************** Lấy info sản phẩm theo ids        ********
# ********************************************************************
"""
@api {POST} products/actions/get_ids_detail Lấy info sản phẩm theo ids
@apiDescription Lấy info sản phẩm theo ids
@apiName GetListProductInfoFromIds
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParamExample [json] Body example:
{
    "ids": ["5ea6ed0c78b6f0d32e325676",
        "5ea6ed0c78b6f0d32e32567c",
        "5ea6ed0c78b6f0d32e32567f",
        "5ea6ed0c78b6f0d32e325682",
        "5ea6ed0c78b6f0d32e325688"]
}

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "id": "5ea6ed0c78b6f0d32e325676",
            "name_product": {
                "vi": "Mocha"
            }
        },
        {
            "id": "5ea6ed0c78b6f0d32e32567c",
            "name_product": {
                "vi": "Túi nắp da lộn"
            }
        },
        {
            "id": "5ea6ed0c78b6f0d32e32567f",
            "name_product": {
                "vi": "Cắt và tạo kiểu cho trẻ em"
            }
        },
        {
            "id": "5ea6ed0c78b6f0d32e325682",
            "name_product": {
                "vi": "Mũ trưng bày"
            }
        },
        {
            "id": "5ea6ed0c78b6f0d32e325688",
            "name_product": {
                "vi": "Triệt ria mép"
            }
        }
    ],
    "message": "request thành công."
}

"""

# ********************************************************************
# ********************** Lấy info media của sản phẩm        **********
# ********************************************************************
"""
@api {GET} products/<product_id>/media Lấy info media của sản phẩm
@apiDescription Lấy info media của sản phẩm
@apiName GetListMediaProduct
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiParam (Query:)     {String}     [merchant_id]   merchant_id của sản phẩm            

@apiSuccess {String}  filename    tên của file 
@apiSuccess {String}  format     định dạng của file
@apiSuccess {String}  url    link


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "id": "uuid"
    "data": [
        {    
            "filename": "", 
            "format": "", 
            "url": "" 

        },
        {    
            "filename": "", 
            "format": "",
            "url": "" 

        }

    ],
    "message": "request thành công."
}

"""

# ********************************************************************
# ********************** Thêm media của sản phẩm                 *****
# ********************************************************************
"""
@api {POST} products/<product_id>/media Thêm media cho sản phẩm
@apiDescription Thêm media cho sản phẩm
@apiName AddMediaProduct
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiParam (Query:)     {String}    [merchant_id]   merchant_id của sản phẩm            
@apiSuccess {file}  media   file muốn upload


@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "id": "uuid",
    "merchant_id": "uuid"
    "data":
        {    
            "filename": "", 
            "format": "", 
            "url": "" 

        },
    "message": "request thành công."
}

"""

# ********************************************************************
# ********************** Xóa media của sản phẩm                 *****
# ********************************************************************
"""
@api {Delete} products/<product_id>/media Xóa media cho sản phẩm
@apiDescription Xóa media cho sản phẩm
@apiName DeleteMediaProduct
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiParam (Query:)     {String}    [merchant_id]   merchant_id của sản phẩm  

@apiParam (Query:)     {String}     url  url của media cần xóa               

@apiSuccessExample {Body} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}

"""

# ********************************************************************
# ********************** Validate filter products               *****
# ********************************************************************
"""
@api {Post} /product_validate_filter Validate filter products 
@apiDescription Validate filter products 
@apiName ValidateFilterProducts
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiParam   (input:)    {Object}  callback     Cấu hình callback
@apiParam   (input:)    {String}  callback.queue_name    Tên queue callback
@apiParam   (input:)    {Object}  callback.data     data trả về qua callback
@apiParam   (input:)    {Array} fields    Danh sách field cần lấy 
@apiParam   (input:)    {Object}  audiences_filter   Thông tin bộ lọc


@apiParamExample {Input} Body example
{
    "merchant_id": "",
    "product_ids": ["", ...],
    "fields": ["name", "email", "phone_number"],
    "audiences_filter": [
        {
            "product_filter": [
                {
                  "operator_key": "op_is_not_empty",
                  "criteria_key": "cri_category",
                  "display": [],
                  "values": [
                    "op_is_not_empty"
                  ]
                }
              ],
            "position": 0,
            "operator": null
        },
        {
            "product_filter": [
                {
                  "operator_key": "op_is_not_empty",
                  "criteria_key": "cri_category",
                  "display": [],
                  "values": [
                    "op_is_not_empty"
                  ]
                }
              ],
            "position": 1,
            "operator": "and"
        },
        {
            "product_filter": [
                {
                  "operator_key": "op_is_not_empty",
                  "criteria_key": "cri_category",
                  "display": [],
                  "values": [
                    "op_is_not_empty"
                  ]
                }
              ],
            "position": 2,
            "operator": "exclude"
        },
    ],
    "callback": {
        "queue_name": "jb-queue-callback",
        "data": {
            "field_1": "123",
            "field_2": "321"
        }
    }
}

@apiSuccessExample {Body} Callback Queue
{
    "code": 200,
    "status": True,
    "data_input": {
        "product_ids": ["fa10d9c2-027d-47c8-9d32-814809c90f3a"],
        "merchant_id": ""
    },
    "data": [{
        "category": ["616ce498389aac493839ebd2"],
        "channel": [],
        "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "created_merchant_id": "bdee9565-3cad-40ae-b2dc-421121d70279",
        "created_time": "2021-10-19T08:54:03Z",
        "id": "616e87abad32c5d2d37df0e8",
        "merchant_id": "bdee9565-3cad-40ae-b2dc-421121d70279",
        "name_product": {"vi": "QĐ cung cấp dịch vụ tư vấn tài chính dành cho KHCN có nhu cầu mua Trái phiếu"},
        "price": None,
        "related_product": {"common_product": ["616e87ce34f12f8b46e7313e"]},
        "sku": {"vi": "QĐ cung cấp dịch vụ tư vấn tài chính dành cho KHCN có nhu cầu mua Trái phiếu"},
    },....]
}
"""

# ********************************************************************
# ********************** get list product by ids and filter field dyn *****
# ********************************************************************
"""
@api {Post} /products/actions/list_product_by_ids Lấy danh sách sản phẩm và filter hiển thị trường tùy biến 
@apiDescription Lấy danh sách sản phẩm và filter hiển thị trường tùy biến 
@apiName FindListProductByIdsAndFilter
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Body:) {array} ids                                       danh sách id của sản phẩm
@apiParam (Body:) {array} list_field_dyn                          danh sách trường tùy biến cần hiển thị


@apiParamExample {Input} Body example
{
    "ids": ["61977794e691eaa63087394b"],
    "list_field_dyn": ["_dyn_dti_theo_quy_dinh_san_pham_1636427140410", "_dyn_han_muc_vay_von_toi_da_1636427140407"]
}

@apiSuccessExample {Body} Callback Queue
{
    "code": 200,
    "data": [
        {
            "_dyn_dti_theo_quy_dinh_san_pham_1636427140410": [
                {
                    "language": "vi",
                    "value": "4"
                }
            ],
            "_dyn_han_muc_vay_von_toi_da_1636427140407": [
                {
                    "language": "vi",
                    "value": "4"
                }
            ],
            "id": "61977794e691eaa63087394b",
            "merchant_id": "bdee9565-3cad-40ae-b2dc-421121d70279",
            "name_product": {
                "vi": "Cho vay trả góp không TSBĐ dành cho KHCN là CBNV tại các trường học ngoài công lập trong thời kỳ dịch bệnh viêm phổi cấp NCOV"
            }
        }
    ],
    "message": "request thành công."
}
"""
# ********************************************************************
# ******************* Cập nhật sản phẩm related_product *****
# ********************************************************************
"""
@api {PUT} /products/<product_id>/related_product/actions/update         Cập nhật thông tin sản phẩm liên quan
@apiDescription   Cập nhật thông tin sản phẩm liên quan
@apiName UpdateReleatedProductWithOneProduct
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Body:) {array} related_product_ids                     Dánh sách product_id của các sản phẩm được gán là liên quan
@apiParam (Body:) {string} related_type                           Kiểu gắn sản phẩm liên quan
                                                                  <ul>
                                                                    <li><code>common_product</code>: Sản phẩm tương tự</li>
                                                                    <li><code>relate_product</code>: Sản phẩm bán gia tăng</li>
                                                                    <li><code>suggest_product</code>: Sản phẩm bán chéo</li>
                                                                  </ul>


@apiParamExample {Input} Body example
{
    "related_product_ids": ["61977794e691eaa63087394b"],
    "related_type": "suggest_product"
}

@apiSuccessExample {Body} Callback Queue
{
    "code": 200,
    "message": "request thành công."
}
"""
# ********************************************************************
# ******************* Lấy danh sách sản phẩm liên quan theo field *****
# ********************************************************************
"""
@api {POST} /products/related_product/actions/filter         Lấy danh sách sản phẩm liên quan.
@apiDescription   Lấy danh sách sản phẩm liên quan.
@apiName GetReleatedProductActionFilter
@apiGroup Product
@apiVersion 1.0.0
@apiUse merchant_id_header
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Body:) {array}   product_ids                    Danh sách id sản phẩm cần lấy thông tin liên quan
@apiParam (Body:) {string}  related_type                   Kiểu sản phẩm liên quan
                                                            <ul>
                                                              <li><code>common_product</code>: Sản phẩm tương tự</li>
                                                              <li><code>relate_product</code>: Sản phẩm bán gia tăng</li>
                                                              <li><code>suggest_product</code>: Sản phẩm bán chéo</li>
                                                            </ul>
@apiParam (Body:) {array}   field_keys                    Danh sách field key cần lấy dữ liệu
                                                          <ul>
                                                            <li>name_product :: Tên sản phẩm</li>
                                                            <li>price :: Giá sản phẩm</li>
                                                            <li>purchase_link :: Link mua hàng</li>
                                                            <li>web_image :: Link ảnh trên web</li>
                                                            <li>image_mobile :: Link ảnh trên mobile</li>
                                                          </ul>
@apiParam (Body:) {int}     [number_select_random=-1]       Số lượng bản tin được trả về của sản phẩm liên quan. Trong trường hợp =-1 thì sẽ trả về tất cả.


@apiParamExample {Input} Body example
{
    "product_ids": ["61977794e691eaa63087394b"],
    "related_type": "suggest_product",
    "field_keys": ["name_product", "price", "purchase_link"],
    "number_select_random": -1
}


@apiSuccess {Array}       data Danh sách sản phẩm
@apiSuccess {string}      data.id                         ID của sản phẩm gốc
@apiSuccess {string}      data.name_product               Tên sản phẩm
@apiSuccess {string}      data.sku                       Mã sản phẩm
@apiSuccess {Float}       data.price                      Giá của sản phẩm
@apiSuccess {String}      data.purchase_link              Link mua hàng

@apiSuccess {Array}       data.related_products           Danh sách thông tin sản phẩm liên quan
@apiSuccess {string}      data.related_products.id                         ID của sản phẩm gốc
@apiSuccess {String}      data.related_products.name_product               Tên sản phẩm
@apiSuccess {string}      data.related_products.sku                       Mã sản phẩm
@apiSuccess {float}       data.related_products.price                      Giá của sản phẩm

@apiSuccess {Object}      data.related_products.web_image                  Thông tin ảnh hiển thị ở web
@apiSuccess {Object}      data.related_products.web_image.url              Link ảnh avatar hiển thị trên web
@apiSuccess {Object}      data.related_products.mobile_app_image             Thông tin ảnh hiển thị ở mobile
@apiSuccess {String}      data.related_products.mobile_app_image.url         Link ảnh avatar hiển thị trên mobile
@apiSuccess {String}      data.related_products.supplier           DS ID nhà cung cấp
@apiSuccess {Array}       data.related_products.category           DS ID danh mục
@apiSuccess {Array}       data.related_products.channel            Khu vực hiển thị <code>1: Web, 2: Mobile App </code>
@apiSuccess {Object}      data.related_products.expiry_date        Vòng đời sản phẩm

@apiSuccessExample {Body} Callback Queue
{
    "code": 200,
    "message": "request thành công.", 
    "data": [
      {
        "id": "61977794e691eaa63087394b",
        "name_product": "SP 01",
        "sku": "sp01",
        "price": 123,
        "related_products": [
          {
            "id": "61977794e691eaa63087394b",
            "name_product": "SP 01",
            "sku": "sp01",
            "price": 1234,
            "web_image": {
              "url": "https://mobio.vn/435gdrg.jpg"
            },
            "mobile_app_image": {
              "url": "https://mobio.vn/435gdrg.jpg"
            },
            "supplier": "",
            "category": [
              "danh mục" 1
            ],
            "channel: [
              1,
              2
            ],
            "expiry_date": {
                "value": 1231,
                "current_type_date": "day",
                "default_date_value": 1231
            }   
          }
        ]
      }
    ]
}
"""


# ******************************************************************************
# ******************* Lấy danh sách sản phẩm liên quan theo field qua kafa *****
# ******************************************************************************
"""
@api {QUEUE} topic_name:product-get-list-relate-product-by-filter         Lấy danh sách sản phẩm liên quan qua queue
@apiDescription   Lấy danh sách sản phẩm liên quan qua queue
@apiName GetReleatedProductByFilterQueue
@apiGroup Product
@apiVersion 1.0.0
@apiUse lang
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Body:) {object}  filter                                Thông tin filter
@apiParam (Body:) {string}  filter.merchant_id                    Thông tin merchant cần lấy dữ liệu
@apiParam (Body:) {array}   filter.product_ids                    Danh sách id sản phẩm cần lấy thông tin liên quan
@apiParam (Body:) {string}  filter.related_type                   Kiểu sản phẩm liên quan
                                                                  <ul>
                                                                    <li><code>common_product</code>: Sản phẩm tương tự</li>
                                                                    <li><code>relate_product</code>: Sản phẩm bán gia tăng</li>
                                                                    <li><code>suggest_product</code>: Sản phẩm bán chéo</li>
                                                                  </ul>
@apiParam (Body:) {array}   filter.field_keys                     Danh sách field key cần lấy dữ liệu
                                                                  <ul>
                                                                    <li>name_product :: Tên sản phẩm</li>
                                                                    <li>price :: Giá sản phẩm</li>
                                                                    <li>purchase_link :: Link mua hàng</li>
                                                                    <li>web_image :: Link ảnh trên web</li>
                                                                    <li>image_mobile :: Link ảnh trên mobile</li>
                                                                  </ul>
@apiParam (Body:) {int}     [filter.number_select_random=-1]       Số lượng bản tin được trả về của sản phẩm liên quan. Trong trường hợp =-1 thì sẽ trả về tất cả.
@apiParam (Body:) {Object}   callback                                     Cấu hình callback
@apiParam (Body:) {Object}   callback.queue_config                        Cấu hình queue
@apiParam (Body:) {String}   callback.queue_config.key                    Key của queue
@apiParam (Body:) {String}   callback.queue_config.target                 Tên topic cần bắn message vào
@apiParam (Body:) {Object}   callback.data                                Data trả về qua callback




@apiParamExample {Input} Callback Queue
{
  "filter": {
    "merchant_id": "61977794e691eaa63087394b",
    "product_ids": ["61977794e691eaa63087394b"],
    "related_type": "suggest_product",
    "field_keys": ["name_product", "price", "purchase_link"],
    "number_select_random": -1
  },
  "callback": {
    "queue_config": {
      "key": "product",
      "target": "product"
    },
    "data": {
      "id": "61977794e691eaa63087394b",
      "name_product": "product_name",
      "price": 100000,
      "purchase_link": ""
    }
  }
}


@apiSuccess {Object}       data_callback                            Thông tin dữ liệu trả về qua callback
@apiSuccess {Array}       data               Dữ liệu sản phẩm
@apiSuccess {string}      data.id                         ID của sản phẩm gốc
@apiSuccess {string}      data.name_product               Tên sản phẩm
@apiSuccess {string}      data.sku                        Mã sản phẩm
@apiSuccess {Float}       data.price                      Giá của sản phẩm
@apiSuccess {String}      data.purchase_link              Link mua hàng
@apiSuccess {Array}       data.related_products           Danh sách thông tin sản phẩm liên quan
@apiSuccess {string}      data.related_products.id                         ID của sản phẩm gốc
@apiSuccess {String}      data.related_products.name_product               Tên sản phẩm
@apiSuccess {string}      data.related_products.sku                       Mã sản phẩm
@apiSuccess {float}       data.related_products.price                      Giá của sản phẩm
@apiSuccess {Object}      data.related_products.web_image                  Thông tin ảnh hiển thị ở web
@apiSuccess {Object}      data.related_products.web_image.url              Link ảnh avatar hiển thị trên web
@apiSuccess {Object}      data.related_products.mobile_app_image             Thông tin ảnh hiển thị ở mobile
@apiSuccess {String}      data.related_products.mobile_app_image.url         Link ảnh avatar hiển thị trên mobile
@apiSuccess {String}      data.related_products.supplier           DS ID nhà cung cấp
@apiSuccess {Array}       data.related_products.category           DS ID danh mục
@apiSuccess {Array}       data.related_products.channel            Khu vực hiển thị <code>1: Web, 2: Mobile App </code>
@apiSuccess {Object}      data.related_products.expiry_date        Vòng đời sản phẩm

@apiSuccessExample {Body} Callback Queue
{
  "data": [
    {
      "id": "61977794e691eaa63087394b",
      "name_product": "SP 01",
      "sku": "sp01",
      "price": 123,
      "related_products": [
        {
          "id": "61977794e691eaa63087394b",
          "name_product": "SP 01",
          "sku": "sp01",
          "price": 1234,
          "web_image": {
            "url": "https://mobio.vn/435gdrg.jpg"
          },
          "mobile_app_image": {
            "url": "https://mobio.vn/435gdrg.jpg"
          },
          "supplier": "",
          "category": [
            "danh mục"1
          ],
          "channel": [
            1,
            2
          ],
          "expiry_date": {
            "value": 1231,
            "current_type_date": "day",
            "default_date_value": 1231
          }
        }
      ]
    }
  ],
  "data_callback": {
    "session_id": "6777b4d761799e42a1f81821",
    "voucher_id": "60605a25-fd9e-41ce-b594-c922d92f50c1",
    "campaign_id": "82642a33-920b-4ae3-ae68-7e1cbffa22d0",
    "profile_id": "00005bb7-4513-4ba9-9325-a7e6225079b3",
    "code_history_id": "5fa0c535-ced7-41dc-a5ca-f7f23d3b15fc"
  }
}
"""

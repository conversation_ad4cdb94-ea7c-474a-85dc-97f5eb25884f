*************************************** Thêm column vào table đã có ** ****************************
* version: 1.0.0 *
****************************************************************************************************
"""
@api {post} /internal/api/v1.0/alter_table/add_column Thêm cột vào bảng đã có
@apiDescription Thêm cột vào bảng đã có
@apiGroup Alter Table
@apiVersion 1.0.0
@apiName InternalAddColumn

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Body:) {String} database_name    Tên database
@apiParam (Body:) {String} table_name       Tên table
@apiParam (Body:) {String} column_name      Tên column
@apiParam (Body:) {String} data_type        Kiểu dữ liệu. Nhận các giá trị: </br>
                                            <li><code>"STRING", "BIGINT", "DOUBLE", "DATE", "DATETIME", "BOOLEAN", "JSON"
                                            <li>"LIST_STRING", "LIST_BIGINT", "LIST_DOUBLE"</code>
                                            <li><code>VARCHAR_x, LIST_VARCHAR_x</code> trong đó x là số ký tự
                                            <li><code>DECIMAL, LIST_DECIMAL</code> Nhận giá trị mặc định DECIMAL(23,4)
                                            <li><code>DECIMAL_x_y, LIST_DECIMAL_x_y</code> trong đó x là số chữ số trước và sau dấu phẩy, y là số chữ số sau dấu phẩy</br>
@apiParam (Body:) {Bool} [not_null] Có null không
@apiParam (Body:) {Bool} [unique] có unique không
@apiParam (Body:) {Anuy} [default] giá trị mặc định

@apiSuccess {Array}   data                          Kết quả
@apiSuccess {String}  data.field_already_exists     Field đã tồn tại trước đó chưa, true => đã tồn tại, false => chưa tồn tại


@apiParamExample {json} Body request example
{
    "database_name": "profiling",
    "table_name": "profile_1b99bdcf_d582_4f49_9715_1b61dfff3924",
    "column_name": "field3",
    "data_type": "DATETIME",
    "not_null": true,
    "unique": false,
    "default": "CURRENT_TIMESTAMP"
}


@apiSuccessExample {json} Response example
{
    "code": 200,
    "data": {
        "field_already_exists": true
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

# *************************** orc1-alter-table-starrock ****************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {TOPIC} /orc1-alter-table-starrock ORC1 Xử lý yêu cầu tạo hoặc sửa bảng starrock.
@apiDescription DataDictionary alter table
@apiGroup Consumer
@apiVersion 1.0.0
@apiName  DataDictionaryAlterTable

@apiParam (Message:)     {String}               [source_type]                                   Nguồn đẩy message, dùng trong TH cần custom riêng nghiệp vụ.
@apiParam (Message:)     {String}               action_type                                     Hành động là gì. Nhận các giá trị "CREATE_TABLE", "ADD_COLUMN"
@apiParam (Message:)     {String}               [merchant_id]                                   Merchant id
@apiParam (Message:)     {Object}               data                                            Thông tin data sẽ tuỳ theo action_type
@apiParam (Message:)     {Object}               callback                                        Thông tin callback
@apiParam (Message:)     {String}               callback.topic                                  Topic trả callback
@apiParam (Message:)     {String}               callback.key                                    Key trả callback
@apiParam (Message:)     {Object}               callback.data_callback                          Data trả callback, dữ liệu bên trong do bên gọi tự định nghĩa

@apiParam (data_create:)     {String}               database_name                               Tên database
@apiParam (data_create:)     {String}               table_name                                  Tên table cần tạo. Lưu ý không quá 256 ký tự
@apiParam (data_create:)     {Array(String)}        primary_key                                 Danh sách primary key của bảng
@apiParam (data_create:)     {Array(String)}        [distributed_by_hash]                       Phân phối dữ liệu trên field nào. Nếu không truyền sẽ set bằng field trong primary_key. </br>
                                                                                                Doc: <a>https://docs.starrocks.io/docs/sql-reference/sql-statements/table_bucket_part_index/CREATE_TABLE/#distribution_desc</a>
@apiParam (data_create:)     {String}               [partition_desc]                            Partition dựa trên điều kiện nào. Nếu không truyền sẽ không xử lý partition </br>
                                                                                                Doc: <a>https://docs.starrocks.io/docs/sql-reference/sql-statements/table_bucket_part_index/CREATE_TABLE/#partition_desc</a>
@apiParam (data_create:)     {Object}               [properties]                                Setup điều kiện khác của bảng. Nếu không truyền sẽ lấy giá trị mặc định. Nếu truyền sẽ lấy giá trị truyền thêm + giá trị mặc định, nếu trùng config sẽ lấy giá trị mặc định.
                                                                                                Doc: <a>https://docs.starrocks.io/docs/sql-reference/sql-statements/table_bucket_part_index/CREATE_TABLE/#properties</a>
                                                                                                </br>
                                                                                                Giá trị mặc định:
                                                                                                <code>
                                                                                                    {</br>
                                                                                                        "storage_medium": "SSD", </br>
                                                                                                        "replication_num": <<Lấy cấu hình trong env OLAP_STARROCKS_REPLICATION_NUM>>,</br>
                                                                                                        "in_memory": "false",</br>
                                                                                                        "enable_persistent_index": "true",</br>
                                                                                                        "replicated_storage": "true",</br>
                                                                                                        "compression": "LZ4"</br>
                                                                                                    }
                                                                                                </code>

@apiParam (data_create:)     {Array(Object)}        columns                                     Danh sách collum cần tạo
@apiParam (data_create:)     {String}               columns.type                                Action với collum. Nhận giá trị "ADD"
@apiParam (data_create:)     {String}               columns.column_name                         Tên collum. Lưu ý không quá 1024 ký tự
@apiParam (data_create:)     {String}               columns.data_type                           Kiểu dữ liệu. Nhận các giá trị: </br>
                                                                                                <li><code>"STRING", "BIGINT", "DOUBLE", "DATE", "DATETIME", "BOOLEAN", "JSON"
                                                                                                <li>"LIST_STRING", "LIST_BIGINT", "LIST_DOUBLE"</code>
                                                                                                <li><code>VARCHAR_x, LIST_VARCHAR_x</code> trong đó x là số ký tự
                                                                                                <li><code>DECIMAL, LIST_DECIMAL</code> Nhận giá trị mặc định DECIMAL(23,4)
                                                                                                <li><code>DECIMAL_x_y, LIST_DECIMAL_x_y</code> trong đó x là số chữ số trước và sau dấu phẩy, y là số chữ số sau dấu phẩy</br>
@apiParam (data_create:)     {Bool}                 [columns.not_null]                          Có được null không?
@apiParam (data_create:)     {Bool}                 [columns.unique]                            Có đánh index unique không?
@apiParam (data_create:)     {String}               [columns.default]                           Giá trị mặc định. Lưu ý:
                                                                                                <ul>
                                                                                                  <li>Giá trị mặc định của các kiểu dữ liệu khác nhau nên convert về string</li>
                                                                                                  <li>Với kiểu dữ liệu DATETIME, để giá trị default là "CURRENT_TIMESTAMP" nếu muốn set giá trị là thời điểm insert dữ liệu.</li>
                                                                                                  <li>Format DATETIME: <code>yyyy-MM-dd HH:mm:ss.s</code> Ex: 2024-05-20 03:13:17.09076</li>
                                                                                                  <li>Format DATE: <code>yyyy-MM-dd</code> Ex: 2024-05-20</li>
                                                                                                  <li>Ex DECIMAL_23_4: '123.4453'</li>
                                                                                                </ul>

@apiParamExample {json} Message example
{
    "source_type": "BEHAVIOR_EVENT",
    "action_type": "CREATE_TABLE",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "data": { Data tuỳ theo action_type },
    "callback": {
        "topic": "data-dict-orchestration-init-callback",
        "key": "adsfasdf_1715936456",
        "data_callback": {
            "event_behavior_id": "66471cc89555e171e56865ed"
        }
    }
}

@apiParamExample {json} Data create table example
{
    "database_name": "profiling",
    "table_name": "behavior_event_1b99bdcf_d582_4f49_9715_1b61dfff3924_adsfasdf_1715936456",
    "primary_key": ["ed_action_time", "profile_id"],
    "distributed_by_hash": ["profile_id"],
    "partition_desc": "PARTITION BY date_trunc('month', ed_action_time)"
    "properties": {
        "bloom_filter_columns": "profile_id",
        "colocate_with": "profile_event1",
        ...
    }
    "columns": [
        {
            "type": "ADD",
            "column_name": "ed_action_time",
            "data_type": "DATETIME",
            "not_null": true
        },
        {
            "type": "ADD",
            "column_name": "profile_id",
            "data_type": "STRING",
            "not_null": true
        },
        {
            "type": "ADD",
            "column_name": "device_id",
            "data_type": "BIGINT",
            "not_null": false
        },
        {
            "type": "ADD",
            "column_name": "event_key",
            "data_type": "STRING",
            "not_null": false
        },
        {
            "type": "ADD",
            "column_name": "connector_id",
            "data_type": "STRING",
            "not_null": false
        },
        {
            "type": "ADD",
            "column_name": "created_time",
            "data_type": "DATETIME",
            "not_null": false
        }
    ]
}

@apiParamExample {json} Data add field example
{
    "database_name": "profiling",
    "table_name": "behavior_event_1b99bdcf_d582_4f49_9715_1b61dfff3924_adsfasdf_1715936456",
    "columns": [
        {
            "type": "ADD",
            "column_name": "field_1",
            "data_type": "DATETIME",
            "not_null": true
        },
        {
            "type": "ADD",
            "column_name": "field_2",
            "data_type": "STRING",
            "not_null": false
        }
    ]
}
"""

****************************  Event Danh sách dữ liệu gợi ý theo field của Event  ******************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/master_data/<event_key>/values/<field_key> Event danh sách master data (Done)
@apiDescription Danh sách dữ liệu gợi ý theo field của Event
@apiGroup Master Data
@apiVersion 1.0.0
@apiName MasterData

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} [search]  Chuỗi tìm kiếm
@apiParam (Query:) {Integer} [per_page] Số bản tin trên 1 trang; <code>default: 20</code>; <code>per_page phải là số nguyên dương</code>
<code>max per_page: 200</code>
@apiParam (Query:) {String} [after_token] token để lấy dữ liệu trang tiếp theo;
@apiParam (Query:) {String} event_key event_key
@apiParam (Query:) {String} field_key field_key của event

@apiSuccess {Array}   data    Danh sách
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	created_time		Thời gian tạo
@apiSuccess (data) {String} 	value				Giá trị: <code>VD: ECOM, PAYMENT</code>

@apiSuccessExample {json} Response example
{
  "data": [
        {
            "name": "Thanh toán",
            "value": "PAYMENT",
            "created_time" : "2021-07-20T07:54:07.424Z"
        }
  ],
  "paging": {
        "cursors": {
            "after": "MjAyMS0xMC0wN1QwNzoyOToyNS4yNDYwMDBa",
            "before": ""
        }
        "per_page": 20,
        "total_items": 100
  },
  "code": 200,
  "message": "request thành công"
}
"""


****************************  Event Chi tiết master data  ******************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /api/v1.0/master_data/detail Event chi tiết master data (Done)
@apiDescription Chi tiết master data
@apiGroup Master Data
@apiVersion 1.0.0
@apiName MasterDataDetail

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Body:) {String} event_key event key
@apiParam (Body:) {String} field_key field key
@apiParam (Body:) {Array} field_values Value của master data; <code>max: 200 phần tử</code>

@apiSuccess {Array}   data    Danh sách
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	created_time		Thời gian tạo
@apiSuccess (data) {String} 	name				Name
@apiSuccess (data) {String} 	value				Value

@apiParamExample {json} Body request example
{
  "event_key": "transaction_behavior_sample",
  "field_key": "channel",
  "field_values": ["POS", "ONLINE"]
}  

@apiSuccessExample {json} Response example
{
  "data": [
        {
            "name": "POS",
            "value": "POS",
            "created_time" : "2021-07-20T07:54:07.424Z"
        },
        {
            "name": "ONLINE",
            "value": "ONLINE",
            "created_time" : "2021-07-20T07:54:07.424Z"
        }
  ],
  "code": 200,
  "message": "request thành công"
}
"""


****************************  Profile Danh sách master data theo field  ****************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/master_data/<object> Profile danh sách master data
@apiDescription Profile danh sách master data
@apiGroup Master Data
@apiVersion 1.0.0
@apiName ObjectMasterData

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} object Đối tượng: <code>profile, company, sale ...</code>

@apiParam (Query:) {String} [search]  Chuỗi tìm kiếm
@apiParam (Query:) {Integer} [per_page] Số bản tin trên 1 trang; <code>default: 20</code>; <code>per_page phải là số nguyên dương</code>
<code>max per_page: 200</code>
@apiParam (Query:) {String} [after_token] token để lấy dữ liệu trang tiếp theo;
@apiParam (Query:) {String} field field của <code>object</code>
@apiParam (Query:) {String} parent_field field cha  của <code>field</code>; Nếu là field cấp 1 thì parent_field = null; 
@apiParam (Query:) {Integer} level level field 

@apiSuccess {Array}   data    Danh sách
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	created_time		Thời gian tạo
@apiSuccess (data) {String} 	value				Giá trị: <code>VD: ECOM, PAYMENT</code>

@apiSuccessExample {json} Response example
{
  "data": [
        {
            "name": "Nam",
            "value": 1,
            "created_time" : "2021-07-20T07:54:07.424Z"
        },
        {
            "name": "Nữ",
            "value": 2,
            "created_time" : "2021-07-20T07:54:07.424Z"
        },
        {
            "name": "Không xác định",
            "value": 3,
            "created_time" : "2021-07-20T07:54:07.424Z"
        }
  ],
  "paging": {
        "cursors": {
            "after": "MjAyMS0xMC0wN1QwNzoyOToyNS4yNDYwMDBa",
            "before": ""
        }
        "per_page": 20,
        "total_items": 100
  },
  "code": 200,
  "message": "request thành công"
}
"""

****************************  Profile Chi tiết master data theo field  *****************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /api/v1.0/master_data/<object>/detail Profile chi tiết master data
@apiDescription Profile chi tiết master data
@apiGroup Master Data
@apiVersion 1.0.0
@apiName ObjectMasterDataDetail

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} object Đối tượng: <code>profile, company, sale ...</code>

@apiParam (Body:) {String} field field của <code>object</code>
@apiParam (Body:) {String} parent_field field cha  của <code>field</code>; Nếu là field cấp 1 thì parent_field = null; 
@apiParam (Body:) {Integer} level level field 
@apiParam (Body:) {Array} field_values <code>max length: 200</code>

@apiSuccess {Array}   data    Danh sách
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	created_time		Thời gian tạo
@apiSuccess (data) {String} 	name				Tên của master data
@apiSuccess (data) {String} 	value				Giá trị master data

@apiParamExample {json} Body request example
{
  "field": "gender",
  "parent_field": null,
  "level": 1,
  "field_values": [1, 2, 3]
}  

@apiSuccessExample {json} Response example
{
  "data": [
        {
            "name": "Nam",
            "value": 1,
            "created_time" : "2021-07-20T07:54:07.424Z"
        },
        {
            "name": "Nữ",
            "value": 2,
            "created_time" : "2021-07-20T07:54:07.424Z"
        },
        {
            "name": "Không xác định",
            "value": 3,
            "created_time" : "2021-07-20T07:54:07.424Z"
        }
  ],
  "code": 200,
  "message": "request thành công"
}
"""
***************************************  Danh sách dữ event behavior  ******************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/dynamic_segment/events Danh sách Event Behavior (Done)
@apiDescription Danh sách Event Behavior
@apiGroup Event Behavior
@apiVersion 1.0.0
@apiName EventBehaviorList

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} [search]  Chuỗi tìm kiếm
@apiParam (Query:) {String} [group]  filter event behavior theo group, các group cách nhau bởi dấu <code>,</code>; 
          default: <code>dynamic_event</code>; example: <code>group=dynamic_event,onpage_journey</code>
@apiParam (Query:) {String} [connector_ids] filter event behavior theo connector_id, các connector_id cách nhau bởi dấu <code>,</code>
@apiParam (Query:) {Integer} [per_page] Số bản tin trên 1 trang; <code>default: 20</code>; <code>per_page phải là số nguyên dương</code>
<code>max per_page: 200</code>
@apiParam (Query:) {String} [after_token] token để lấy dữ liệu trang tiếp theo;

@apiSuccess {Array}   data    Danh sách
@apiSuccess {String}  data._id    Event ID
@apiSuccess {String}  data.name   Tên event
@apiSuccess {String}  data.event_key    Event Key
@apiSuccess {String}  data.fields   Danh sách field của event
@apiSuccess {String}  data.created_time   Thời gian tạo event
@apiSuccess {String}  data.updated_time   Thời gian cập nhật event

@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
  "data": [
        {
            "_id": "654b384877281ddd42dd21e1",
            "name": "Transaction behavior",
            "event_key": "transaction_behavior_sample",
            "fields": [
                {
                    "field_name" : "Action Time",
                    "field_property" : "datetime",
                    "field_key" : "action_time",
                    "master_data": false,
                    "position" : 0,
                    "is_action_time": true,
                    "is_calculator": false
                },
                {
                    "field_name" : "Tran Date",
                    "field_property" : "date",
                    "field_key" : "tran_date",
                    "master_data": false,
                    "position" : 1,
                    "is_calculator": false
                },
                {
                    "field_name" : "Int",
                    "field_property" : "int",
                    "field_key" : "int",
                    "master_data": false,
                    "position" : 2,
                    "is_calculator": true
                },
                {
                    "field_name" : "Double",
                    "field_property" : "double",
                    "field_key" : "double",
                    "master_data": false,
                    "position" : 3,
                    "is_calculator": true
                },
                {
                    "field_name" : "Trường string",
                    "field_property" : "string",
                    "field_key" : "truong_string",
                    "master_data": true,
                    "position" : 4,
                    "is_calculator": false
                }
            ],
            "updated_time" : "2021-07-20T07:54:07.424Z",
            "created_time" : "2021-07-20T07:54:07.424Z"
        }
  ],
  "paging": {
        "cursors": {
            "after": "MjAyMS0xMC0wN1QwNzoyOToyNS4yNDYwMDBa",
            "before": ""
        }
        "per_page": 20,
        "total_items": 100
  },
  "code": 200,
  "message": "request thành công"
}
"""


***************************************  Chi tiết Event Behavior by event_ids  *********************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/dynamic_segment/events/detail Chi tiết Event Behavior (Done)
@apiDescription Chi tiết Event Behavior
@apiGroup Event Behavior
@apiVersion 1.0.0
@apiName EventBehaviorDetail

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} event_ids  Danh sách event_ids <code>max: 200 event_id</code> 
                            <br>Mảng event_id được phân cách nhau bởi dấu phẩy (,). Example: event_id_1,event_id_2
                            
@apiSuccess {Array}   data    Danh sách Event Behavior
@apiSuccess {String}  data._id    Event ID
@apiSuccess {String}  data.name   Tên event
@apiSuccess {String}  data.event_key   Event key
@apiSuccess {String}  data.fields   Danh sách field của event
@apiSuccess {String}  data.status   Trạng thái của event <code>1: ACTIVE; 2: WAITING_INIT_EVENT; 3: INIT_EVENT_ERROR; 4: DELETED; 5: STOP</code>
@apiSuccess {String}  data.created_time   Thời gian tạo event
@apiSuccess {String}  data.updated_time   Thời gian cập nhật event

@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
  "data": [
        {
            "_id": "654b384877281ddd42dd21e1",
            "name": "Transaction behavior sample",
            "status": 1,
            "event_key": "transaction_behavior_sample",
            "fields": [
                {
                    "field_name" : "Action Time",
                    "field_property" : "datetime",
                    "field_key" : "action_time",
                    "master_data": false,
                    "position" : 0,
                    "is_action_time": true,
                    "is_calculator": false
                },
                {
                    "field_name" : "Tran Date",
                    "field_property" : "date",
                    "field_key" : "tran_date",
                    "master_data": false,
                    "position" : 1,
                    "is_calculator": false
                },
                {
                    "field_name" : "Int",
                    "field_property" : "int",
                    "field_key" : "int",
                    "master_data": false,
                    "position" : 2,
                    "is_calculator": true
                },
                {
                    "field_name" : "Double",
                    "field_property" : "double",
                    "field_key" : "double",
                    "master_data": false,
                    "position" : 3,
                    "is_calculator": true
                },
                {
                    "field_name" : "Trường string",
                    "field_property" : "string",
                    "field_key" : "truong_string",
                    "master_data": true,
                    "position" : 4,
                    "is_calculator": false
                }
            ],
            "updated_time" : "2021-07-20T07:54:07.424Z",
            "created_time" : "2021-07-20T07:54:07.424Z"
        }
  ],
  "code": 200,
  "message": "request thành công"
}
"""

******************************  Chi tiết Event Behavior by event_keys  *****************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/dynamic_segment/events/detail_by_key Chi tiết Event Behavior By Event Key (Done)
@apiDescription Chi tiết Event Behavior
@apiGroup Event Behavior
@apiVersion 1.0.0
@apiName EventBehaviorDetailByKey

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} event_keys  Danh sách event_keys <code>max: 200 event_key</code> 
                            <br>Mảng event_key được phân cách nhau bởi dấu phẩy (,). Example: event_key_1,event_key_2
@apiParam (Query:) {String} group Nhóm event; <code>dynamic_event</code>; <code>onpage_journey</code>
@apiParam (Query:) {String} connector_id Connector ID

@apiSuccess {Array}   data    Danh sách Event Behavior
@apiSuccess {String}  data._id    Event ID
@apiSuccess {String}  data.name   Tên event
@apiSuccess {String}  data.event_key   Event key
@apiSuccess {String}  data.fields   Danh sách field của event
@apiSuccess {String}  data.status   Trạng thái của event <code>1: ACTIVE; 2: WAITING_INIT_EVENT; 3: INIT_EVENT_ERROR; 4: DELETED; 5: STOP</code>
@apiSuccess {String}  data.created_time   Thời gian tạo event
@apiSuccess {String}  data.updated_time   Thời gian cập nhật event

@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
  "data": [
        {
            "_id": "654b384877281ddd42dd21e1",
            "name": "Transaction behavior sample",
            "status": 1,
            "event_key": "transaction_behavior_sample",
            "fields": [
                {
                    "field_name" : "Action Time",
                    "field_property" : "datetime",
                    "field_key" : "ed_action_time",
                    "field_key_origin" : "action_time",
                    "master_data": false,
                    "position" : 0,
                    "is_action_time": true,
                    "is_calculator": false
                },
                {
                    "field_name" : "Tran Date",
                    "field_property" : "date",
                    "field_key" : "ed_tran_date",
                    "field_key_origin" : "tran_date",
                    "master_data": false,
                    "position" : 1,
                    "is_calculator": false
                },
                {
                    "field_name" : "Int",
                    "field_property" : "int",
                    "field_key" : "ed_int",
                    "field_key_origin" : "int",
                    "master_data": false,
                    "position" : 2,
                    "is_calculator": true
                },
                {
                    "field_name" : "Double",
                    "field_property" : "double",
                    "field_key" : "ed_double",
                    "field_key_origin" : "double",
                    "master_data": false,
                    "position" : 3,
                    "is_calculator": true
                },
                {
                    "field_name" : "Trường string",
                    "field_property" : "string",
                    "field_key" : "ed_truong_string",
                    "field_key_origin" : "truong_string",
                    "master_data": true,
                    "position" : 4,
                    "is_calculator": false
                }
            ],
            "updated_time" : "2021-07-20T07:54:07.424Z",
            "created_time" : "2021-07-20T07:54:07.424Z"
        }
  ],
  "code": 200,
  "message": "request thành công"
}
"""



******************************  INTERNAL Chi tiết Event Behavior by event_keys  ********************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /internal/api/v1.0/dynamic_segment/events/detail_by_key INTERNAL Chi tiết Event Behavior By Event Key (Done)
@apiDescription Chi tiết Event Behavior qua đầu internal
@apiGroup Event Behavior
@apiVersion 1.0.0
@apiName InternalEventBehaviorDetailByKey

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} event_keys  Danh sách event_keys <code>max: 200 event_key</code> 
                            <br>Mảng event_key được phân cách nhau bởi dấu phẩy (,). Example: event_key_1,event_key_2

@apiSuccess {Array}   data    Danh sách Event Behavior
@apiSuccess {String}  data._id    Event ID
@apiSuccess {String}  data.name   Tên event
@apiSuccess {String}  data.event_key   Event key
@apiSuccess {String}  data.fields   Danh sách field của event
@apiSuccess {String}  data.status   Trạng thái của event <code>1: ACTIVE; 2: WAITING_INIT_EVENT; 3: INIT_EVENT_ERROR; 4: DELETED; 5: STOP</code>
@apiSuccess {String}  data.source_db   Database OLAP lưu dữ liệu event
@apiSuccess {String}  data.source_table   Table OLAP lưu dữ liệu event
@apiSuccess {String}  data.created_time   Thời gian tạo event
@apiSuccess {String}  data.updated_time   Thời gian cập nhật event

@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
  "data": [
        {
            "_id": "654b384877281ddd42dd21e1",
            "name": "Transaction behavior sample",
            "status": 1,
            "event_key": "transaction_behavior_sample_0239578293875",
            "fields": [
                {
                    "field_name" : "Action Time",
                    "field_property" : "datetime",
                    "field_key" : "ed_action_time",
                    "field_key_origin" : "action_time",
                    "master_data": false,
                    "position" : 0,
                    "is_action_time": true,
                    "is_calculator": false
                },
                {
                    "field_name" : "Tran Date",
                    "field_property" : "date",
                    "field_key" : "ed_tran_date",
                    "field_key_origin" : "tran_date",
                    "master_data": false,
                    "position" : 1,
                    "is_calculator": false
                },
                {
                    "field_name" : "Int",
                    "field_property" : "int",
                    "field_key" : "ed_int",
                    "field_key_origin" : "int",
                    "master_data": false,
                    "position" : 2,
                    "is_calculator": true
                },
                {
                    "field_name" : "Double",
                    "field_property" : "double",
                    "field_key" : "ed_double",
                    "field_key_origin" : "double",
                    "master_data": false,
                    "position" : 3,
                    "is_calculator": true
                },
                {
                    "field_name" : "Trường string",
                    "field_property" : "string",
                    "field_key" : "ed_truong_string",
                    "field_key_origin" : "truong_string",
                    "master_data": true,
                    "position" : 4,
                    "is_calculator": false
                }
            ],
            "source_db" : "profiling",
            "source_table" : "behavior_event_1b99bdcf_d582_4f49_9715_1b61dfff3924_transaction_behavior_sample_0239578293875"
            "updated_time" : "2021-07-20T07:54:07.424Z",
            "created_time" : "2021-07-20T07:54:07.424Z"
        }
  ],
  "code": 200,
  "message": "request thành công"
}
"""


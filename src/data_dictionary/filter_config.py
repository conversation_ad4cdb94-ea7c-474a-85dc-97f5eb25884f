***************************************  <PERSON>h sách dữ event behaviort  *****************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/dynamic_segment/filter_configs Filter Config (Done)
@apiDescription Filter Config
@apiGroup DE Filter Config
@apiVersion 1.0.0
@apiName DEFilterConfig

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Array}  filter_config <PERSON><PERSON><PERSON> hình bộ lọc
@apiSuccess {Array}  field_config C<PERSON>u hình field

@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
    "code": 200,
    "field_config" : [
        {
            "field_property" : "int",
            "master_data" : false,
            "operators" : [
                {
                    "key" : "op_is_equal",
                    "name" : "Bằng"
                },
                {
                    "key" : "op_is_between",
                    "name" : "Giữa"
                },
                {
                    "key" : "op_is_greater",
                    "name" : "Lớn hơn"
                },
                {
                    "key" : "op_is_greater_equal",
                    "name" : "Lớn hơn hoặc bằng"
                },
                {
                    "key" : "op_is_less",
                    "name" : "Nhỏ hơn"
                },
                {
                    "key" : "op_is_less_equal",
                    "name" : "Nhỏ hơn hoặc bằng"
                },
                {
                    "key" : "op_is_not_empty",
                    "name" : "Có thông tin",
                    "no_content" : true
                },
                {
                    "key" : "op_is_empty",
                    "name" : "Chưa có thông tin",
                    "no_content" : true
                }
            ]
        },
        {
            "field_property" : "double",
            "master_data" : false,
            "operators" : [
                {
                    "key" : "op_is_equal",
                    "name" : "Bằng"
                },
                {
                    "key" : "op_is_between",
                    "name" : "Giữa"
                },
                {
                    "key" : "op_is_greater",
                    "name" : "Lớn hơn"
                },
                {
                    "key" : "op_is_greater_equal",
                    "name" : "Lớn hơn hoặc bằng"
                },
                {
                    "key" : "op_is_less",
                    "name" : "Nhỏ hơn"
                },
                {
                    "key" : "op_is_less_equal",
                    "name" : "Nhỏ hơn hoặc bằng"
                },
                {
                    "key" : "op_is_not_empty",
                    "name" : "Có thông tin",
                    "no_content" : true
                },
                {
                    "key" : "op_is_empty",
                    "name" : "Chưa có thông tin",
                    "no_content" : true
                }
            ]
        },
        {
            "field_property" : "datetime",
            "master_data" : false,
            "operators" : [
                {
                    "key" : "op_is_equal",
                    "name" : "Vào thời gian"
                },
                {
                    "key" : "op_is_less",
                    "name" : "Trước thời gian"
                },
                {
                    "key" : "op_is_greater",
                    "name" : "Sau thời gian"
                },
                {
                    "key" : "op_is_between",
                    "name" : "Trong khoảng thời gian"
                }
            ]
        },
        {
            "field_property" : "date",
            "master_data" : false,
            "operators" : [
                {
                    "key" : "op_is_equal",
                    "name" : "Vào thời gian"
                },
                {
                    "key" : "op_is_less",
                    "name" : "Trước thời gian"
                },
                {
                    "key" : "op_is_greater",
                    "name" : "Sau thời gian"
                },
                {
                    "key" : "op_is_between",
                    "name" : "Trong khoảng thời gian"
                }
            ]
        },
        {
            "field_property" : "string",
            "master_data" : false,
            "operators" : [
                {
                    "key" : "op_is_has",
                    "name" : "Có nội dung này"
                },
                {
                    "key" : "op_is_has_not",
                    "name" : "Không có nội dung này"
                },
                {
                    "key" : "op_is_not_empty",
                    "name" : "Có thông tin",
                    "no_content" : true
                },
                {
                    "key" : "op_is_empty",
                    "name" : "Chưa có thông tin",
                    "no_content" : true
                }
            ]
        },
        {
            "field_property" : "string",
            "master_data" : true,
            "operators" : [
                {
                    "key" : "op_is_in",
                    "name" : "Chứa 1 trong các giá trị được chọn"
                },
                {
                    "key" : "op_is_not_in",
                    "name" : "Không chứa 1 trong các giá trị được chọn"
                },
                {
                    "key" : "op_is_has",
                    "name" : "Có nội dung này"
                },
                {
                    "key" : "op_is_has_not",
                    "name" : "Không có nội dung này"
                },
                {
                    "key" : "op_is_not_empty",
                    "name" : "Có thông tin",
                    "no_content" : true
                },
                {
                    "key" : "op_is_empty",
                    "name" : "Chưa có thông tin",
                    "no_content" : true
                }
            ]
        },
        {
            "field_property" : "boolean",
            "master_data" : false,
            "accept_values" : [
                {
                    "key" : true,
                    "label" : "True"
                },
                {
                    "key" : false,
                    "label" : "False"
                }
            ],
            "operators" : [
                {
                    "key" : "op_is_equal",
                    "name" : "Là"
                },
                {
                    "key" : "op_is_not_empty",
                    "name" : "Có thông tin",
                    "no_content" : true
                },
                {
                    "key" : "op_is_empty",
                    "name" : "Chưa có thông tin",
                    "no_content" : true
                }
            ]
        }
    ],
    "filter_config" : [
        {
            "field_name" : "Số lần phát sinh",
            "criteria_key" : "cri_olap_pc_count",
            "criteria_type" : "count",
            "operators" : [
                {
                    "key" : "op_is_equal",
                    "name" : "Bằng",
                    "content_type" : "single_line",
                    "single_line" : {
                        "field_property" : "int",
                        "min" : 0,
                        "max" : null,
                        "units" : [
                            {
                                "key" : "count",
                                "name" : "Lần"
                            }
                        ],
                        "selected_unit" : "count"
                    }
                },
                {
                    "key" : "op_is_greater_equal",
                    "name" : "Tối thiểu",
                    "content_type" : "single_line",
                    "single_line" : {
                        "field_property" : "int",
                        "min" : 0,
                        "max" : null,
                        "units" : [
                            {
                                "key" : "count",
                                "name" : "Lần"
                            }
                        ],
                        "selected_unit" : "count"
                    }
                },
                {
                    "key" : "op_is_less_equal",
                    "name" : "Tối đa",
                    "content_type" : "single_line",
                    "single_line" : {
                        "field_property" : "int",
                        "min" : 0,
                        "max" : null,
                        "units" : [
                            {
                                "key" : "count",
                                "name" : "Lần"
                            }
                        ],
                        "selected_unit" : "count"
                    }
                }
            ]
        },
        {
            "field_name" : "",
            "criteria_key" : "cri_olap_pc_action_time",
            "is_action_time" : true,
            "criteria_type" : "filter",
            "operators" : [
                {
                    "key" : "op_in_the_last",
                    "name" : "Trong N ngày gần nhất",
                    "content_type" : "single_line",
                    "single_line" : {
                        "field_property" : "int",
                        "min" : 0,
                        "max" : null,
                        "units" : [
                            {
                                "key" : "day",
                                "name" : "Ngày"
                            }
                        ],
                        "selected_unit" : "day"
                    }
                },
                {
                    "key" : "op_is_between",
                    "name" : "Trong khoảng thời gian",
                    "content_type" : "date_picker",
                    "is_action_time" : true,
                    "date_picker" : {
                        "field_property" : "multiple"
                    }
                }
            ]
        },
        {
            "field_name" : "",
            "criteria_key" : "cri_olap_pc_and_having",
            "criteria_type" : "calculation",
            "display_type" : "dropdown_single_line",
            "operator_key" : "op_is_equal",
            "accept_values" : [
                {
                    "key" : "aggregation",
                    "label" : "Tổng hợp"
                },
                {
                    "key" : "change",
                    "label" : "Thay đổi"
                },
                {
                    "key" : "percentage_change",
                    "label" : "Tỉ lệ thay đổi"
                }
            ]
        },
        {
            "field_name" : "",
            "display_type" : "dropdown_single_line",
            "criteria_key" : "cri_olap_pc_option",
            "criteria_type" : "calculation",
            "operator_key" : "op_is_equal",
            "accept_values" : [
                {
                    "key" : "SUM",
                    "label" : "Tổng"
                },
                {
                    "key" : "AVG",
                    "label" : "Giá trị trung bình"
                },
                {
                    "key" : "MIN",
                    "label" : "Giá trị nhỏ nhất"
                },
                {
                    "key" : "MAX",
                    "label" : "Giá trị lớn nhất"
                }
            ]
        },
        {
            "field_name" : "",
            "criteria_key" : "cri_olap_pc_and_combine",
            "criteria_type" : "calculation",
            "display_type" : "combine_text",
            "combine_text" : "của"
        },
        {
            "field_name" : "",
            "criteria_key" : "cri_olap_pc_field_key",
            "operator_key" : "op_is_equal",
            "criteria_type" : "calculation"
        },
        {
            "field_name" : "",
            "display_type" : "single_line",
            "criteria_key" : "cri_olap_pc_compare",
            "criteria_type" : "calculation",
            "operators" : [
                {
                    "key" : "op_is_equal",
                    "name" : "Bằng"
                },
                {
                    "key" : "op_is_between",
                    "name" : "Giữa"
                },
                {
                    "key" : "op_is_greater",
                    "name" : "Lớn hơn"
                },
                {
                    "key" : "op_is_greater_equal",
                    "name" : "Lớn hơn hoặc bằng"
                },
                {
                    "key" : "op_is_less",
                    "name" : "Nhỏ hơn"
                },
                {
                    "key" : "op_is_less_equal",
                    "name" : "Nhỏ hơn hoặc bằng"
                }
            ]
        }
    ],
    "master_data_audience_limit": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""

***************************************  Onpage journey config filter  *****************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/onpage_journey/filter_configs Onpage Journey Filter Config (Done)
@apiDescription Filter Config
@apiGroup DE Filter Config
@apiVersion 1.0.0
@apiName OnpageJourneyFilterConfig

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Array}  field_config Cấu hình field

@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccessExample {json} Response example
{
    "code": 200,
    "field_config" : [
        {
            "field_property" : "int",
            "master_data" : false,
            "operators" : [
                {
                    "key" : "op_is_equal",
                    "name" : "Bằng"
                },
                {
                    "key" : "op_is_between",
                    "name" : "Giữa"
                },
                {
                    "key" : "op_is_greater",
                    "name" : "Lớn hơn"
                },
                {
                    "key" : "op_is_greater_equal",
                    "name" : "Lớn hơn hoặc bằng"
                },
                {
                    "key" : "op_is_less",
                    "name" : "Nhỏ hơn"
                },
                {
                    "key" : "op_is_less_equal",
                    "name" : "Nhỏ hơn hoặc bằng"
                },
                {
                    "key" : "op_is_not_empty",
                    "name" : "Có thông tin",
                    "no_content" : true
                },
                {
                    "key" : "op_is_empty",
                    "name" : "Chưa có thông tin",
                    "no_content" : true
                }
            ]
        },
        {
            "field_property" : "double",
            "master_data" : false,
            "operators" : [
                {
                    "key" : "op_is_equal",
                    "name" : "Bằng"
                },
                {
                    "key" : "op_is_between",
                    "name" : "Giữa"
                },
                {
                    "key" : "op_is_greater",
                    "name" : "Lớn hơn"
                },
                {
                    "key" : "op_is_greater_equal",
                    "name" : "Lớn hơn hoặc bằng"
                },
                {
                    "key" : "op_is_less",
                    "name" : "Nhỏ hơn"
                },
                {
                    "key" : "op_is_less_equal",
                    "name" : "Nhỏ hơn hoặc bằng"
                },
                {
                    "key" : "op_is_not_empty",
                    "name" : "Có thông tin",
                    "no_content" : true
                },
                {
                    "key" : "op_is_empty",
                    "name" : "Chưa có thông tin",
                    "no_content" : true
                }
            ]
        },
        {
            "field_property" : "datetime",
            "master_data" : false,
            "operators" : [
                {
                    "key" : "op_is_equal",
                    "name" : "Vào thời gian"
                },
                {
                    "key" : "op_is_less",
                    "name" : "Trước thời gian"
                },
                {
                    "key" : "op_is_greater",
                    "name" : "Sau thời gian"
                },
                {
                    "key" : "op_is_between",
                    "name" : "Trong khoảng thời gian"
                }
            ]
        },
        {
            "field_property" : "date",
            "master_data" : false,
            "operators" : [
                {
                    "key" : "op_is_equal",
                    "name" : "Vào thời gian"
                },
                {
                    "key" : "op_is_less",
                    "name" : "Trước thời gian"
                },
                {
                    "key" : "op_is_greater",
                    "name" : "Sau thời gian"
                },
                {
                    "key" : "op_is_between",
                    "name" : "Trong khoảng thời gian"
                }
            ]
        },
        {
            "field_property" : "string",
            "master_data" : false,
            "operators" : [
                {
                    "key" : "op_is_has",
                    "name" : "Có nội dung này"
                },
                {
                    "key" : "op_is_has_not",
                    "name" : "Không có nội dung này"
                },
                {
                    "key" : "op_is_not_empty",
                    "name" : "Có thông tin",
                    "no_content" : true
                },
                {
                    "key" : "op_is_empty",
                    "name" : "Chưa có thông tin",
                    "no_content" : true
                }
            ]
        },
        {
            "field_property" : "string",
            "master_data" : true,
            "operators" : [
                {
                    "key" : "op_is_in",
                    "name" : "Chứa 1 trong các giá trị được chọn"
                },
                {
                    "key" : "op_is_not_in",
                    "name" : "Không chứa 1 trong các giá trị được chọn"
                },
                {
                    "key" : "op_is_has",
                    "name" : "Có nội dung này"
                },
                {
                    "key" : "op_is_has_not",
                    "name" : "Không có nội dung này"
                },
                {
                    "key" : "op_is_not_empty",
                    "name" : "Có thông tin",
                    "no_content" : true
                },
                {
                    "key" : "op_is_empty",
                    "name" : "Chưa có thông tin",
                    "no_content" : true
                }
            ]
        },
        {
            "field_property" : "boolean",
            "master_data" : false,
            "accept_values" : [
                {
                    "key" : true,
                    "label" : "True"
                },
                {
                    "key" : false,
                    "label" : "False"
                }
            ],
            "operators" : [
                {
                    "key" : "op_is_equal",
                    "name" : "Là"
                },
                {
                    "key" : "op_is_not_empty",
                    "name" : "Có thông tin",
                    "no_content" : true
                },
                {
                    "key" : "op_is_empty",
                    "name" : "Chưa có thông tin",
                    "no_content" : true
                }
            ]
        }
    ],
    "master_data_audience_limit": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""
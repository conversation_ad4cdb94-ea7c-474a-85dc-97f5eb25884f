****************************  Register Event Behavior từ Onpage  ***********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /internal/api/v1.0/event_behavior/register Đăng ký event behavior
@apiDescription Đăng ký event behavior; Tạo mới Event Behavior + đăng ký cấu trúc với module orchestration; <PERSON><PERSON> call_back về cho Onpage
@apiGroup Event Behavior OnPage
@apiVersion 1.0.0
@apiName EventBehaviorOnPageRegister

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Body:) {String} source <code>onpage_journey</code>, <code>dynamic_event</code>
@apiParam (Body:) {Object} data Thông tin của event behavior
@apiParam (Body:) {String} [data.connector_id] Connector ID; Để xác định event thuộc domain nào
@apiParam (Body:) {String} data.event_name Tên của event
@apiParam (Body:) {String} data.event_key Key của event; <code>Lưu ý: Đầu data_dictionary sẽ định danh event theo event_key này</code>
@apiParam (Body:) {String} [data.event_id] Event id: source <code>dynamic_event</code> cần truyền lên 
@apiParam (Body:) {Array} data.fields Danh sách field của event

@apiParam (fields:) {String} field_name Tên của field
@apiParam (fields:) {String} field_key Key của field
@apiParam (fields:) {String} field_property Kiểu dữ liệu của field; <code>string; int; decimal_19; boolean; date; datetime</code>
@apiParam (fields:) {Boolean} master_data Cờ để xác định field này có Lưu và hiển thì danh sách khi lọc hay không (suggest list)

@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiParamExample {json} Body request example
{
    "source": "onpage_journey",
    "data": {
        "connector_id": "654b384877281ddd42dd21e1",
        "event_name": "Event từ OnPage Journey",
        "event_key": "event_tu_onpage_journey_842173623",
        "fields": [
            {   
              "field_name": "Thời gian phát sinh event", // required
              "field_key": "action_time",
              "field_property": "datetime",
              "master_data": false
            },
            {   
              "field_name": "Field string",
              "field_key": "field_string",
              "field_property": "string",
              "master_data": true
            },
            {   
              "field_name": "Field boolean",
              "field_key": "field_boolean",
              "field_property": "boolean",
              "master_data": false
            },
            {   
              "field_name": "Field date",
              "field_key": "field_date",
              "field_property": "date",
              "master_data": false
            },
            {   
              "field_name": "Field decimal 19", // Field dạng số thập phân
              "field_key": "field_decimal 19",
              "field_property": "decimal_19",
              "master_data": false
            },
            {   
              "field_name": "Field int",
              "field_key": "field_int",
              "field_property": "int",
              "master_data": false
            }
        ]
    },
    "callback": {
        "topic": "Topic trả kết quả",
        "key": "key để chia partition", // nếu không truyển thì random
        "data": {
            "event_key": "event_key_111222333"
        }
    }
}

@apiSuccessExample {json} Response success API
{
  "code": 200,
  "message": "request thành công"
}

@apiSuccessExample {json} Response validate API
{
  "code": 413,
  "message": "Event key đã tồn tại!"
}

@apiSuccessExample {json} Callback Kafka
{
    "status": "FALSE", // SUCCESS/FALSE
    "message": "Đăng ký không thành công",
    "data_callback": {
        "event_key": "event_key_111222333"
    }
}
"""


****************************  Xóa Event Behavior từ Onpage  ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /internal/api/v1.0/event_behavior/delete Xóa event behavior
@apiDescription Xóa event behavior
@apiGroup Event Behavior OnPage
@apiVersion 1.0.0
@apiName EventBehaviorOnPageDelete

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Body:) {String} source Nguồn
@apiParam (Body:) {String} [connector_id] connector_id
@apiParam (Body:) {String} event_key Event Key

@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiParamExample {json} Body request example
{
    "source": "onpage_journey",
    "connector_id": "654b384877281ddd42dd21e1",
    "event_key": "event_key_111222333"
}

@apiSuccessExample {json} Response success API
{
  "code": 200,
  "message": "request thành công"
}
"""



****************************  Xóa Event Behavior từ Onpage  ****************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /internal/api/v1.0/event_behavior/re_register Đăng ký lại event behavior
@apiDescription Đăng ký lại event behavior trong trường hợp trước đó đănng ký bị lỗi
@apiGroup Event Behavior OnPage
@apiVersion 1.0.0
@apiName EventBehaviorOnPageReRegister

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Body:) {String} source Nguồn
@apiParam (Body:) {String} [connector_id] connector_id
@apiParam (Body:) {String} event_key Event Key

@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiParamExample {json} Body request example
{
    "source": "onpage_journey",
    "connector_id": "654b384877281ddd42dd21e1",
    "event_key": "event_key_111222333",
}

@apiSuccessExample {json} Response success API
{
  "code": 200,
  "message": "request thành công"
}

@apiSuccessExample {json} Callback Kafka
{
    "status": "FALSE", // SUCCESS/FALSE
    "message": "Đăng ký không thành công",
    "data_callback": {
        "event_key": "event_key_111222333"
    }
}
"""
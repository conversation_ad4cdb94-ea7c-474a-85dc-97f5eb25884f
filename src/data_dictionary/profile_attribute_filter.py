****************************  <PERSON>h sách bộ lọc Profile Attribute  **********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /api/v1.0/filter/profile_attribute Danh sách bộ lọc profile attribute
@apiDescription Danh sách bộ lọc profile attribute
@apiGroup Filter Profile Attribute
@apiVersion 1.0.0
@apiName FilterProfileAttributeList

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam (Query:) {String} [search]  Tìm kiếm theo filter_name

@apiSuccess {Array}   data    Danh sách
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String} 	criteria_key	key của bộ lọc
@apiSuccess (data) {String} 	filter_name		Tên của bộ lọc
@apiSuccess (data) {String} 	field_name		Tên field
@apiSuccess (data) {String} 	field_key		Field key
@apiSuccess (data) {String} 	field_pii		Field PII
@apiSuccess (data) {String} 	group_key		Xác định nhóm field

@apiSuccessExample {json} Response example
{
  "data": [
        {
            "criteria_key" : "cri_olap_pa_birthday",
            "filter_name" : "Bộ lọc sinh nhật",
            "field_name": "Sinh nhật",
            "field_key": "birthday",
            "group_key" : "demographic",
            "field_pii": false
        },
        {
            "criteria_key" : "cri_olap_pa_gender",
            "filter_name" : "Giới tính",
            "field_name": "Giới tính",
            "field_key": "gender",
            "group_key" : "information",
            "field_pii": false
        },
        {
            "criteria_key": "cri_olap_pa_primary_email",
            "filter_name": "Bộ lọc Email",
            "field_name": "Email",
            "field_key": "primary_email",
            "group_key": "information",
            "field_pii": true
        },
        {
            "criteria_key" : "cri_olap_pa_phone_number",
            "filter_name" : "Số điện thoại",
            "field_name": "Số điện thoại",
            "field_key": "phone_number",
            "group_key" : "information",
            "field_pii": true
        },
        {
            "criteria_key" : "cri_olap_pa_source",
            "filter_name" : "Nguồn ghi nhận thông tin",
            "field_name": "Nguồn ghi nhận thông tin",
            "field_key": "source",
            "group_key" : "information",
            "field_pii": false
        },
        {
            "criteria_key" : "cri_olap_pa_tags",
            "filter_name" : "Bộ lọc tag",
            "field_name": "Tag",
            "field_key": "tags",
            "group_key" : "information",
            "field_pii": false
        }
  ],,
  "code": 200,
  "message": "request thành công"
}
"""

****************************  Chi tiết bộ lọc Profile Attribute  ***********************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {post} /api/v1.0/filter/profile_attribute/detail Chi tiết bộ lọc profile attribute
@apiDescription Chi tiết bộ lọc profile attribute
@apiGroup Filter Profile Attribute
@apiVersion 1.0.0
@apiName FilterProfileAttributeDetail

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiSuccess {Array}   data    Danh sách
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (body) {Array} 	criteria_keys 	key của bộ lọc

@apiSuccess (data) {String} 	criteria_key 	key của bộ lọc
@apiSuccess (data) {String} 	filter_name		Tên của bộ lọc
@apiSuccess (data) {String} 	field_name		Tên field
@apiSuccess (data) {String} 	field_key		Field key
@apiSuccess (data) {String} 	group_key		Xác định nhóm field
@apiSuccess (data) {Array} 	    operators		Danh sách operator
@apiSuccess (data) {String} 	default_operator_key		operator_key default
@apiSuccess (data) {Object} 	master_data		Xác định field này có master_data hay không

@apiParamExample {json} Body request example
{
  "criteria_keys": [
    "cri_olap_pa_gender",
    "cri_olap_pa_primary_email"
  ]
}  

@apiSuccessExample {json} Response example
{
  "data": [
        {
            "criteria_key" : "cri_olap_pa_gender",
            "filter_name" : "Giới tính",
            "field_name": "Giới tính",
            "field_key": "gender",
            "field_pii": false,
            "group_key" : "information",
            "operators": [
                {
                    "key": "op_is_in",
                    "content_type": "" // Cần FE gửi thông tin
                }
            ],
            "default_operator_key": "op_is_in",
            "master_data": { // cần FE gửi thông tin
            }
        },
        {
            "field_name": "Email",
            "field_key": "primary_email",
            "filter_name": "Bộ lọc Email",
            "group_key": "information",
            "criteria_key": "cri_olap_pa_primary_email",
            "field_pii": true,
            "default_operator_key": "op_is_multiple",
            "attributes": [
                {
                    "filter_name": "",
                    "criteria_key": "cri__exists",
                    "operators": [
                        {
                            "key": "op_is_empty",
                            "content_type": ""
                        },
                        {
                            "key": "op_is_not_empty",
                            "content_type": ""
                        }
                    ]
                },
                {
                    "op_relations": [
                        {
                            "criteria_key": "cri__exists",
                            "values": ["op_is_not_empty"]
                        }
                    ], 
                    "field_name": "Trạng thái",
                    "field_key": "status",
                    "filter_name": "Trạng thái",
                    "criteria_key": "cri_status",
                    "master_data": {},
                    "operators": [
                        {
                            "key": "op_is_in",
                            "content_type": ""
                        }
                    ]
                }
            ]
        }
  ],
  "code": 200,
  "message": "request thành công"
}
"""
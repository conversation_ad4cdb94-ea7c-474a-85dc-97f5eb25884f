#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: nguyenthong
    Date Created: 13/01/2021
"""

# ************************************
#   Cập nhật Voucher
# ************************************
"""   
@api {post} /loyalty/api/v2.1/codes  Thêm danh sách mã voucher
@apiVersion 1.0.0              
@apiDescription Thêm danh sách mã voucher   
@apiName AddVoucherCodes   
@apiGroup Voucher Code

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)      {String}    voucher_id     ID Voucher được thêm mã. 
                                                    Trường hợp thêm mã vào kho mã chung thì
                                                    <code> voucher_id = COMMON </code>  
@apiParam   (Body:)      {Array}     [codes]        Danh sách mã voucher 
                                                     <br/>
                                                     Max list: <code> 200 </code>                                                 

@apiSuccess {Object}   data  Nội dung trả về  
@apiSuccess {String}   message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiParamExample    {Json}      Body example:

{
    "voucher_id": "574903b4-92b3-458e-a939-fbd279301fb9",
    "codes": [
        "PUGG6X",
        "VNPVRA"
    ]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK                                                                                             
{     
  "code": 200,
  "message": "request thành công."                                                                                                           
}     
"""
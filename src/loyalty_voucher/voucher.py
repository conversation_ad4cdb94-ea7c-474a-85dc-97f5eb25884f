# ********************** <PERSON><PERSON><PERSON> danh sách voucher  **********************
#                         * version: 1.0.5
# ********************************************************************

"""
@api {get} /loyalty/api/v2.1/vouchers L<PERSON>y danh sách voucher
@apiVersion 1.0.5
@apiDescription Lấy danh sách voucher
@apiName GetVouches
@apiGroup Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam (Query:) {string} [name] Tìm kiếm voucher theo tên 
@apiParam (Query:) {number} [effective] Trạng thái hiệu lực của voucher : <code> 1: <PERSON><PERSON><PERSON> l<PERSON> </code>
@apiParam (Query:) {number} [availability] Trạng thái khả dụng của voucher : <code> 1: khả dụng </code>
@apiParam (Query:) {string} [category] Danh sách danh mục muốn lọc. VD: <code>&category=MEVABE;CHAMSOCSACDEP</code>
@apiParam (Query:) {string} [start_time] Thời gian bắt đầu filter .Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam (Query:) {string} [end_time] Thời gian kết thúc filter .Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam (Query:) {string} status Filter theo trạng thái của voucher. VD: <code>status=0;1</code><br/>Allow values: <code>-1-DEACTIVE 0-DRAFT 1-ACTIVE</code>
@apiParam (Query:) {string} [order_by] Sắp xếp theo thuộc tính nào. Giá trị: <b>1</b>: start_time, <b>2</b>:end_time
@apiParam (Query:) {string} [order_type] Kiểu sắp xếp: Giá trị: <b>1</b>:Tăng dần, <b></b>2 Giảm dần
@apiParam (Query:) {string} [merchant_ids] Danh sách định danh tenant sở hữu voucher.
VD merchant_ids=c5d3b0d8-01e4-4acb-8505-3ff009125a6f;1b99bdcf-d582-4f49-9715-1b61dfff3924
@apiParam (Query:) {Number} [bill_invoice_status]  Áp dụng tặng theo giá trị hóa đơn <code> 0: NOT_USE, 1: USE </code>.
@apiParam (Query:) {string} [store_ids] Danh sách cửa hàng sở hữu voucher
VD store_ids=c5d3b0d8-01e4-4acb-8505-3ff009125a6f;1b99bdcf-d582-4f49-9715-1b61dfff3924
@apiParam (Query:) {string} [source]  Module gọi đến api <code>VD: mkt </code> 

@apiSuccess {Array}   data    Danh sách voucher
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (datas:) {string} id UUID voucher
@apiSuccess (datas:) {string} name Tên voucher
@apiSuccess (datas:) {string} avatar Link ảnh đại diện cho voucher
@apiSuccess (datas:) {number} discount Số lượng giảm
@apiSuccess (datas:) {string=PERCENT MONEY} discount_type Kiểu giảm giá
@apiSuccess (datas:) {string=VND USD} [discount_unit] Đơn vị giảm giá. Trả về khi <code>discount_type=MONEY</code>
@apiSuccess (datas:) {string} start_time Thời gian bắt đầu voucher. Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess (datas:) {string} end_time Thời gian kết thúc voucher. Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess (datas:) {number=-1-DEACTIVE 0-DRAFT 1-ACTIVE} status Trạng thái của voucher. <code>DEACTIVE: Ẩn</code>, <code>ACTIVE: Hiển thị</code>, <code>DRAFT: nháp</code>
@apiSuccess (datas:) {string} gen_code_type Kiểu mã voucher <code>MANUAL: Lấy từ kho mã voucher </code>, <code>AUTO: Tự động</code>
@apiSuccess (datas:) {string} [merchant_id] Merchant_id áp dụng voucher
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "data": [
    {
      "id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
      "avatar":"https://mobio.vn/logo.png",
      "name": "[HCM] - Sakuko Japanese Store Ưu đãi",
      "discount": 20000,
      "discount_type": "MONEY",
      "discount_unit": "VND",
      "start_time": "2018-07-26T12:00:00Z",
      "end_time": "2019-08-11T12:00:00Z",
      "status": 1,
      "gen_code_type": "AUTO",
      "merchant_id": "1b7d4e3c-a105-48f1-978b-74527cb5271d"
    }
  ]
}
"""


# ********************************************************************
# ********************** Lấy danh sách danh mục categories  **********************
# ********************************************************************

"""
@api {get} /loyalty/api/v2.1/categories Lấy danh sách categories
@apiVersion 1.0.0
@apiDescription Lấy danh sách categories
@apiName GetCategories
@apiGroup Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess {Array}   datas    Danh sách category
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (datas:) {string} id UUID category
@apiSuccess (datas:) {Object} name Tên category
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công.",
  "datas": [
    {
      "id": "1b7d4e3c-a105-48f1-978b-74527cb5271d",
      "name": {
      "default":"Mẹ và bé",
      "vi":"Mẹ và bé ",
      "en":"Mother and baby "
      }
    },
    ...
  ]
}
"""


# ********************************************************************
# **********************Xóa  Voucher   **********************
# ********************************************************************

"""   
@api {delete} /loyalty/api/v2.1/vouchers/delete  Xóa vouchers                                                                                           
@apiVersion 1.0.0              
@apiDescription Xóa Voucher      
@apiName DeleteVoucher 
@apiGroup Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500     
@apiUse lang
@apiUse merchant_id_header
      
@apiParam   (Query:)  {string}   voucher_ids       Danh sách các id của voucher cần delete  

VD: voucher_ids=uuid1,uuid2,uuid3    
    
@apiSuccessExample {json} Response: HTTP/1.1 200 OK                                                                                             
{     
  "code": 200,
  "message": "request thành công."                                                                                                              
}     
"""

# ********************** Thêm  Voucher **********************
#                   * Version: 1.0.1                                          *
#                   * Version: 1.0.0                                          *
# ***********************************************************
"""
@api {post} /loyalty/api/v2.1/vouchers/add  Thêm  Voucher
@apiVersion 1.0.1
@apiDescription Thêm  Voucher    Request dạng <code>form_file</code>
@apiName AddVoucher  
@apiGroup Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Form:)  {string}   name       Tên voucher  
@apiParam   (Form:)  {array}   [category_ids]       Uuid của Category tương ứng  
VD: "category_ids": ["uuid1","uuid2",...]
@apiParam   (Form:)  {string}   [discount_type]      <ul> Kiểu giảm giá:</ul> 
<li><b>PERCENT</b>-Phần trăm</li> 
<li><b>MONEY</b>-Giảm tiền</li>  

@apiParam   (Form:)  {string}   [discount_unit]       <ul>Đơn vị của giá trị giảm giá:</ul>
<li><b> % </b>-Phần trăm </li>
<li><b>VND </b> Việt Nam đồng </li>
<li><b>USD </b></li>
@apiParam   (Form:)  {File}   [avatar] file avatar của voucher   
@apiParam   (Form:)  {array}   merchant_ids Danh sách các merchant_id được áp dụng voucher 
VD: "merchant_ids": ["uuid1","uuid2",...]

@apiParam   (Form:)   {array} [store_ids] danh sách các cửa hàng được áp dụng voucher  
VD: "store_ids": ["uuid1","uuid2",...]

@apiParam   (Form:)  {int}   [discount]       Giá trị  giảm 
@apiParam   (Form:)  {int}   [price]       <ul>Số điểm để đổi </ul> 
<li>Nếu price =0 thì phát hành miễn phí </li> 
<li>Nếu price >0 thì phát hành kiểu đổi điểm </li> 

@apiParam   (Form:)  {string} [content] Nội dung của voucher   
@apiParam   (Form:)  {string} [tags] Gợi ý tìm kiếm voucher. VD: tags = 100k;tết    
@apiParam   (Form:)  {Files}  [brand_url] Danh sách file của ảnh mô tả cho voucher 
@apiParam   (Form:)  {string}  [gen_code_type] <ul>Kiểu tạo mã của voucher</ul>
<li><b>AUTO</b>  sinh mã tự động </li>
<li><b>MANUAL</b>   mã tự nhập bằng tay  </li>
 
@apiParam   (Form:)  {int}  [max_code]  Số lượng mã code toàn chương trình 
@apiParam   (Form:)  {int}  [max_code_per_day]  Số lượng mã code phát hành trên 1 ngày 
@apiParam   (Form:)  {int}  [max_code_per_profile]  Số lượng mã code phát hành tối đa cho 1 profile 

@apiParam   (Form:)  {int}  [time_get_per_profile]  Thời gian giữa 2 lần phát hành code trên 1 profile 
(Đơn vị tính bằng giây) 
@apiParam   (Form:)  {int}  [valid_time_in_seconds] Thời gian hiệu thực của mã voucher (Đơn vị tính bằng giây) 
@apiParam   (Form:)  {string}  [valid_time_in_seconds_type]<p> Kiểu thời gian hiệu lực của mã 
vd : <code>minute</code> phút  <code>hour</code> -giờ <code>day</code> - ngày </p>
@apiParam   (Form:)  {string}  [time_get_per_profile_type] <p> kiểu thời gian giữa 2 lần phát hành code trên 1 profile 
vd : <code>minute</code> phút  <code>hour</code> -giờ <code>day</code> - ngày </p>

@apiParam   (Form:)   {string} [start_time]  Thời gian bắt đầu phát hành voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam   (Form:)   {string} [end_time]  Thời gian kết thúc  phát hành voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam   (Form:)   {string} [display_start_time ] Thời gian bắt đầu hiển thị  voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam   (Form:)   {string} [display_end_time] Thời gian bắt đầu hiển thị  voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam   (Form:)   {string}  [audience_id] Id của tập khách hàng áp dụng cho voucher 
@apiParam   (Form:)   {int}  [bill_invoice_status] Áp dụng tặng theo giá trị hóa đơn <code> 0 : NOT_USE , 1: USE </code>

@apiSuccess {Object}   data  Nội dung trả về  
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:) {string}  id Uuid của voucher 
@apiSuccess (data:) {string}  name Tên của voucher 

@apiParamExample    {FormData}      Body example:

  "name":"Voucher giảm giá hóa đơn từ 100K",
  "category_ids":["6afff536-1fe1-4b89-86d9-401a6b899a14",...]
  "discount_type":"PERCENT",
  "discount_unit:" "%",
  "price": "20"



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{     
  "code": 200,
  "message": "request thành công."
  "data":{
    "id":"b057d528-dbd3-4fcc-87ed-f931b4e6b461"
    "name":"Voucher giảm giá hóa đơn từ 100K"
  }
}
"""

# ************************************
#   Cập nhật Voucher
# ************************************
"""   
@api {post} /loyalty/api/v2.1/vouchers/<voucher_id>/update   Cập nhật Voucher
@apiVersion 1.0.0              
@apiDescription Cập nhật Voucher    
@apiName UpdateVoucher   
@apiGroup Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Form:)  {string}   [name]       Tên voucher 
@apiParam   (Form:)  {array}   category_ids       Uuid của Category tương ứng 
VD: "category_ids": ["uuid1","uuid2",...]
 
@apiParam   (Form:)  {string}   [discount_type]      <ul> Kiểu giảm giá:</ul> 
<li><b>PERCENT</b>-Phần trăm</li> 
<li><b>MONEY</b>-Giảm tiền</li>  

@apiParam   (Form:)  {string}   [discount_unit]       <ul>Đơn vị của giá trị giảm giá:</ul>
<li><b> % </b>-Phần trăm </li>
<li><b>VND </b> Việt Nam đồng </li>
<li><b>USD </b></li>

@apiParam   (Form:)  {File}   [avatar] file avatar của voucher   
@apiParam   (Form:)  {array}   [merchant_ids] Danh sách các merchant_id được áp dụng voucher 
VD: "merchant_ids" : ["uuid1","uuid2",...]
@apiParam   (Form:)   {array} [store_ids] danh sách các cửa hàng được áp dụng voucher  
VD: "store_ids":  ["uuid1","uuid2",...]

@apiParam   (Form:)  {int}   price       <ul>Số điểm để đổi </ul> 
<li>Nếu price =0 thì phát hành miễn phí </li> 
<li>Nếu price >0 thì phát hành kiểu đổi điểm </li> 

@apiParam   (Form:)  {string} [content] Nội dung của voucher   
@apiParam   (Form:)  {string} [tags] Gợi ý tìm kiếm voucher. VD: tags = 100k;tết    
@apiParam   (Form:)  {Files}  [brand_url] Danh sách file của ảnh mô tả thêm mới cho voucher 
@apiParam   (Form:)  {arraystring}  [delete_brand_url] Danh sách file của ảnh mô tả được xóa khi update 
@apiParam   (Form:)  {string}  [gen_code_type] <ul>Kiểu tạo mã của voucher</ul>
<li><b>AUTO</b>  sinh mã tự động </li>
<li><b>MANUAL</b>   mã tự nhập bằng tay  </li>
 
@apiParam   (Form:)  {int}  [max_code]  Số lượng mã code toàn chương trình 
@apiParam   (Form:)  {int}  [max_code_per_day]  Số lượng mã code phát hành trên 1 ngày 
@apiParam   (Form:)  {int}  [max_code_per_profile]  Số lượng mã code phát hành tối đa cho 1 profile 

@apiParam   (Form:)  {int}  [time_get_per_profile]  Thời gian giữa 2 lần phát hành code trên 1 profile 
(Đơn vị tính bằng giây) 
@apiParam   (Form:)  {int}  [valid_time_in_seconds] Thời gian hiệu thực của mã voucher (Đơn vị tính bằng giây) 
@apiParam   (Form:)  {string}  [valid_time_in_seconds_type]<p> Kiểu thời gian hiệu lực của mã 
vd : <code>minute</code> phút  <code>hour</code> -giờ <code>day</code> - ngày </p>
@apiParam   (Form:)  {string}  [time_get_per_profile_type] <p> kiểu thời gian giữa 2 lần phát hành code trên 1 profile 
vd : <code>minute</code> phút  <code>hour</code> -giờ <code>day</code> - ngày </p>

@apiParam   (Form:)   {string} [start_time]  Thời gian bắt đầu phát hành voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam   (Form:)   {string} [end_time]  Thời gian kết thúc  phát hành voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam   (Form:)   {string} [display_start_time ] Thời gian bắt đầu hiển thị  voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiParam   (Form:)   {string} [display_end_time] Thời gian bắt đầu hiển thị  voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>

@apiParam   (Form:)   {string}  [audience_id] Id của tập khách hàng áp dụng cho voucher 
@apiParam   (Form:)   {int}  [bill_invoice_status] Áp dụng tặng theo giá trị hóa đơn <code> 0 : NOT_USE , 1: USE </code>

@apiSuccess {Object}   data  Nội dung trả về  
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data:) {string}  id Uuid của voucher 
@apiSuccess (data:) {string}  name Tên của voucher 

@apiParamExample    {FormData}      Body example:
  "name":"Voucher giảm giá hóa đơn từ 100K",
  "avatar": File
  "category_ids":["6afff536-1fe1-4b89-86d9-401a6b899a14",...]
  "discount_type":"PERCENT",
  "discount_unit:" "%",
  "price": "20",
  "content":"Nội dung của voucher ",
  "brand_url": [File,File...],
  "delete_brand_url": [url,url...],
  "tags":"tết;100k",
  "gen_code_type": "AUTO",
  "max_code":1000,
  "max_code_per_day":100,
  "max_code_per_profile":1,
  "time_get_per_profile": 360,
  "valid_time_in_seconds": 3600,
  "start_time": "2018-07-26T12:00:00Z",
  "end_time": "2019-08-11T12:00:00Z",
  "display_start_time": "2018-07-26T12:00:00Z",
  "display_end_time": "2019-08-11T12:00:00Z",
  "store_ids":["e5cfc5a9-e72e-4cea-942b-f27906677a0f","d018c7a4-9cd3-4afe-b204-ea08242d7bca",..],
  "audience_id":"e969f293-b126-42a8-90e4-0fcbd0c9f89e"


@apiSuccessExample {json} Response: HTTP/1.1 200 OK                                                                                             
{     
  "code": 200,
  "message": "request thành công."
  "data":{
    "id":"b057d528-dbd3-4fcc-87ed-f931b4e6b461"
    "name":"Voucher giảm giá hóa đơn từ 100K"
  }                                                                                                              
}     
"""

# ********************** Chi tiết  Voucher ***************************
# * version: 1.0.0                                                   *
# ********************************************************************
"""
@api {get} /loyalty/api/v2.1/vouchers/<voucher_id>/detail  Lấy chi tiết Voucher
@apiVersion 1.0.1
@apiDescription Lấy chi tiết Voucher
@apiName DetailVoucher  
@apiGroup Voucher

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess {Object}   data  Nội dung trả về  
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess   (data:)  {string}   [name]       Danh sách các id của voucher cần delete  
@apiSuccess   (data:)  {array}   [category_ids]       Uuid của Category tương ứng  
 VD: "category_ids" : ["uuid1","uuid2",...]
@apiSuccess   (data:)  {string}   [discount_type]      <ul> Kiểu giảm giá:</ul> 
<li><b>PERCENT</b>-Phần trăm</li> 
<li><b>MONEY</b>-Giảm tiền</li>  

@apiSuccess   (data:)  {string}   [discount_unit]       <ul>Đơn vị của giá trị giảm giá:</ul>
<li><b> % </b>-Phần trăm </li>
<li><b>VND </b> Việt Nam đồng </li>
<li><b>USD </b></li>
@apiSuccess   (data:)  {string}   [avatar] Link avatar của voucher   
@apiSuccess   (data:)  {array}   [merchant_ids] Danh sách các merchant_id được áp dụng voucher 
VD: "merchant_ids" : ["uuid1","uuid2",...]
@apiSuccess   (data:)   {array} [store_ids] danh sách các cửa hàng được áp dụng voucher  
VD: "store_ids" : ["uuid1","uuid2",...]

@apiSuccess   (data:)  {int}   price       <ul>Số điểm để đổi </ul> 
<li>Nếu price =0 thì phát hành miễn phí </li> 
<li>Nếu price >0 thì phát hành kiểu đổi điểm </li>  
@apiSuccess   (data:)  {string} [content] Nội dung của voucher   
@apiSuccess   (data:)  {string} [tags] Gợi ý tìm kiếm voucher. VD: tags = 100k;tết    
@apiSuccess   (data:)  {array}  [brand_url] Danh sách link của ảnh mô tả cho voucher 
@apiSuccess   (data:)  {string}  [gen_code_type] <ul>Kiểu tạo mã của voucher</ul>
<li><b>AUTO</b>  sinh mã tự động </li>
<li><b>MANUAL</b>   mã tự nhập bằng tay  </li>
 
@apiSuccess   (data:)  {int}  [max_code]  Số lượng mã code toàn chương trình 
@apiSuccess   (data:)  {int}  [max_code_per_day]  Số lượng mã code phát hành trên 1 ngày 
@apiSuccess   (data:)  {int}  [max_code_per_profile]  Số lượng mã code phát hành tối đa cho 1 profile 

@apiSuccess   (data:)  {int}  [time_get_per_profile]  Thời gian giữa 2 lần phát hành code trên 1 profile 
(Đơn vị tính bằng giây) 
@apiSuccess   (data:)  {int}  [valid_time_in_seconds] Giá trị thời gian hiệu thực của mã voucher 
@apiSuccess   (data:)  {string}  [valid_time_in_seconds_type]<p> Kiểu thời gian hiệu lực của mã 
vd : <code>minute</code> phút  <code>hour</code> -giờ <code>day</code> - ngày </p>
@apiSuccess   (data:)  {string}  [time_get_per_profile_type] <p> kiểu thời gian giữa 2 lần phát hành code trên 1 profile 
vd : <code>minute</code> phút  <code>hour</code> -giờ <code>day</code> - ngày </p>

@apiSuccess   (data:)   {string} [start_time]  Thời gian bắt đầu phát hành voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess   (data:)   {string} [end_time]  Thời gian kết thúc  phát hành voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess   (data:)   {string} [display_start_time ] Thời gian bắt đầu hiển thị  voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>
@apiSuccess   (data:)   {string} [display_end_time] Thời gian bắt đầu hiển thị  voucher 
Định dạng: <code>yyyy-MM-ddTHH:mm:ssZ</code>

@apiSuccess   (data:)   {float}  [avg_rated] Điểm trung bình đánh giá 
@apiSuccess   (data:)   {int}  [code_length] Độ dài tối đa của mã voucher 
@apiSuccess   (data:)   {string}  [created_time] Thời gian tạo voucher 
@apiSuccess   (data:)   {string}  [created_user] Uuid của account tạo voucher
@apiSuccess   (data:)   {float}  [discount] Giá trị của voucher 
@apiSuccess   (data:)   {string}  [updated_time] Thời gian cập nhật voucher 
@apiSuccess   (data:)   {int}  [time_get_per_profile] Thời gian giữa hai lần cấp cho một profile  
@apiSuccess   (data:)   {string}  [time_get_per_profile_type] Kiểu thời gian VD: minute, hour, day 
@apiSuccess   (data:)   {string}  [price_unit] Đơn vị đổi của voucher nếu có 
 
@apiSuccess   (data:)   {int}  [status] Trạng thái của voucher   
@apiSuccess   (data:)   {int}  [tick_max] Tổng số lượng tick tối đa   
@apiSuccess   (data:)   {string}  [source_created] Nguồn tạo VD: từ CEM hay máy POF  
@apiSuccess   (data:)   {string}  [updated_user] Uuid của account cập nhật voucher   



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{     
  "code": 200,
  "message": "request thành công."    
  "data":{
      "name":"Voucher giảm giá hóa đơn từ 100K",
      "avatar": "https://mobio.vn/logo.png",
      "merchant_ids": ["6afff536-1fe1-4b89-86d9-401a6b899a14",...],
      "category_ids":["6afff536-1fe1-4b89-86d9-401a6b899a14",...]
      "discount_type":"PERCENT",
      "discount_unit:" "%",
      "price": "20",
      "content":"Nội dung của voucher ",
      "brand_url":["https://mobio.bn/static/logo.png",...],
      "tags":"tết;100k",
      "gen_code_type": "AUTO",
      "max_code":1000,
      "max_code_per_day":100,
      "max_code_per_profile":1,
      "time_get_per_profile": 360,
      "valid_time_in_seconds": 3600,
      "start_time": "2018-07-26T12:00:00Z",
      "end_time": "2019-08-11T12:00:00Z",
      "display_start_time": "2018-07-26T12:00:00Z",
      "display_end_time": "2019-08-11T12:00:00Z",
      "store_ids":["e5cfc5a9-e72e-4cea-942b-f27906677a0f","d018c7a4-9cd3-4afe-b204-ea08242d7bca",..],
      "audience_id":"e969f293-b126-42a8-90e4-0fcbd0c9f89e"
  }
}
"""

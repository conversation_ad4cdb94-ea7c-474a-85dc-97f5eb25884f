#!/usr/bin/python
# -*- coding: utf8 -*-
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Upload Excel ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {post} [HOST]/api/v1.0/upload Upload Excel.
@apiDescription Upload Excel
@apiGroup Upload Excel
@apiVersion 1.0.0
@apiName  UploadExcel
@apiHeader (Headers:) {String} Content-Type <code>application/form-data</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)     {File}         file          File Excel cần import.
@apiParam   (Body:)     {String}       course_id     <code>ID</code> của khoá học.
@apiParam   (Body:)     {String}       product_id     <code>ID</code> của sản phẩm.
@apiParam   (Body:)     {String=lesson,exam}       [file_type_upload]     Kiểu dữ liệu của file upload.
@apiParam   (Body:)     {String=classroom,onsite,online,shoplist}        course_type    Hình thức học
@apiParam   (Body:)     {String}       [tag]           Tag Samsung sẽ gắn cho kết quả của File excel.
@apiParam   (Body:)     {String}       [tag]           Tag Samsung sẽ gắn cho kết quả của File excel.
@apiParam   (Body:)     {Array[]}       [emails]       Danh sách email gửi thông báo khi hoàn thành việc xử lý file. Mặc định sẽ không truyền lên.
@apiParam   (Body:)     {string}       [lesson_id]     <code>ID</code> của bài học
@apiParam   (Body:)     {string}       [exam_id]       <code>ID</code> của bài thi


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "lang": "vi", 
  "success": true,
  "message": "request thành công."
}
"""


***************************************** Báo cáo thống kê theo V2 ** *******************************************
* version: 2.0.0 *
****************************************************************************************************************
"""
@api {post} [HOST]/api/v1.0/covering/fsm/actions/filter Báo cáo thống kê theo FSM 
@apiDescription Báo cáo thống kê theo FSM
@apiGroup Báo cáo thống kê v2
@apiVersion 2.0.0
@apiName fsm_statistical_v2 
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query:) {String} statistical_by                                 Đối tượng được lấy ra để tính toán (bảng): 
                                                                            - region
                                                                            - region_ss
                                                                            - shop_grade
                                                                            - sell_in_type
                                                                            - customer
@apiParam (Query:) {String} group                                          Nhóm các giá trị được lấy ra (cột)
                                                                            - actual: cột FSM actual
                                                                            - classroom: cột actual của hình thức học classroom
                                                                            - onsite: cột actual của hình thức học onsite
                                                                            - online: cột actual của hình thức học online
                                                                            - target_and_distribution: cột FSM distribution và các cột target của các hình thức học
                                                                            (classroom, onsite, online)


@apiParam (body:)           {string}            course_id                           id khóa học
@apiParam (body:)           {object}            filter                              Đối tượng chứa những trường tìm kiếm của bộ lọc
@apiParam (body:)           {datetime}          [filter.start_date]                 Ngày bắt đầu
@apiParam (body:)           {datetime}          [filter.end_date]                   Ngày kết thúc
@apiParam (body:)           {Array[string]}     [filter.region_ids]                 Danh sách id khu vực muốn tìm kiếm 
@apiParam (body:)           {Array[string]}     [filter.region_ss_ids]              Danh sách id region_ss muốn tìm kiếm
@apiParam (body:)           {Array[string]}     [filter.product_ids]                Danh sách id sản phẩm muốn tìm kiếm
@apiParam (body:)           {Array[string]}     [filter.sell_in_type_ids]           Danh sách id sell_in_type muốn tìm kiếm

@apiParam (body:)           {object}            row                                 Đối tượng chứa những thông tin của 1 dòng cần gửi xuống backend
@apiParam (body:)           {string}            row.object_id                       id của region 



@apiParamExample    {json}    Body example:
{
    "course_id": "c326f557-22c0-4aa5-bb56-9f14bcbbbd7f",
    "filter": {
        "course_id": "c326f557-22c0-4aa5-bb56-9f14bcbbbd7f",
        "end_date": "2021/09/30",
        "start_date": "2020/10/01"
        "sell_in_type_id": "..." # Nếu statistical_by = customer
        ...
    },
    "row": {
        "object_id": "e0f3e217-72a2-4d97-a24c-3159d2cb010c"
    }
}

@apiSuccess (data) {Int}            value                  Giá trị fsm actual theo khu vực
@apiSuccess (data) {Int}            row_id                 ID row của bảng

@apiSuccessExample {json} Response: HTTP/1.1 200 OK: Nếu group: [actual, classroom, onsite, online]
{
    "code": 200,
    "data": {
        "value": 3994,
        "row_id": "e0f3e217-72a2-4d97-a24c-3159d2cb010c"
    },
    "lang": "vi",
    "message": "request thành công."
}


@apiSuccess (data) {Int}            classroom                  Giá trị target của hỉnh thức học classroom
@apiSuccess (data) {Int}            distribution               Giá trị FSM distribuition
@apiSuccess (data) {Int}            online                     Giá trị target của hỉnh thức học online
@apiSuccess (data) {Int}            onsite                     Giá trị target của hỉnh thức học onsite
@apiSuccess (data) {Int}            row_id                     ID row của bảng

@apiSuccessExample {json} Response: HTTP/1.1 200 OK: Nếu group: [statistical_by]
{
    "code": 200,
    "data": {
        "classroom": 1206,
        "distribution": 4242,
        "online": 3460,
        "onsite": 3998,
        "row_id": "e0f3e217-72a2-4d97-a24c-3159d2cb010c"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {post} [HOST]/api/v1.0/covering/store/actions/filter Báo cáo thống kê theo Store 
@apiDescription Báo cáo thống kê theo Store
@apiGroup Báo cáo thống kê v2
@apiVersion 2.0.0
@apiName store_statistical_v2 
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query:) {String} statistical_by                                 Đối tượng được lấy ra để tính toán (bảng): 
                                                                            - region
                                                                            - region_ss
                                                                            - shop_grade
                                                                            - sell_in_type
                                                                            - customer
@apiParam (Query:) {String} group                                          Nhóm các giá trị được lấy ra (cột)
                                                                            - actual: cột Store actual
                                                                            - classroom: cột actual của hình thức học classroom
                                                                            - onsite: cột actual của hình thức học onsite
                                                                            - online: cột actual của hình thức học online
                                                                            - target_and_distribution: cột Store distribution và các cột target của các hình thức học
                                                                            (classroom, onsite, online)


@apiParam (body:)           {string}            course_id                           id khóa học
@apiParam (body:)           {object}            filter                              Đối tượng chứa những trường tìm kiếm của bộ lọc
@apiParam (body:)           {datetime}          [filter.start_date]                 Ngày bắt đầu
@apiParam (body:)           {datetime}          [filter.end_date]                   Ngày kết thúc
@apiParam (body:)           {Array[string]}     [filter.region_ids]                 Danh sách id khu vực muốn tìm kiếm 
@apiParam (body:)           {Array[string]}     [filter.region_ss_ids]              Danh sách id region_ss muốn tìm kiếm
@apiParam (body:)           {Array[string]}     [filter.product_ids]                Danh sách id sản phẩm muốn tìm kiếm
@apiParam (body:)           {Array[string]}     [filter.sell_in_type_ids]           Danh sách id sell_in_type muốn tìm kiếm

@apiParam (body:)           {object}            row                                 Đối tượng chứa những thông tin của 1 dòng cần gửi xuống backend
@apiParam (body:)           {string}            row.object_id                       id của region 



@apiParamExample    {json}    Body example:
{
    "course_id": "c326f557-22c0-4aa5-bb56-9f14bcbbbd7f",
    "filter": {
        "course_id": "c326f557-22c0-4aa5-bb56-9f14bcbbbd7f",
        "end_date": "2021/09/30",
        "start_date": "2020/10/01",
        "sell_in_type_id": "..." # Nếu statistical_by = customer
        ...
    },
    "row": {
        "object_id": "e0f3e217-72a2-4d97-a24c-3159d2cb010c"
    }
}

@apiSuccess (data) {Int}            value                  Giá trị store actual theo khu vực
@apiSuccess (data) {Int}            row_id                 ID row của bảng

@apiSuccessExample {json} Response: HTTP/1.1 200 OK: Nếu group: [actual, classroom, onsite, online]
{
    "code": 200,
    "data": {
        "value": 3994,
        "row_id": "e0f3e217-72a2-4d97-a24c-3159d2cb010c"
    },
    "lang": "vi",
    "message": "request thành công."
}


@apiSuccess (data) {Int}            classroom                  Giá trị target của hỉnh thức học classroom
@apiSuccess (data) {Int}            distribution               Giá trị Store distribuition
@apiSuccess (data) {Int}            online                     Giá trị target của hỉnh thức học online
@apiSuccess (data) {Int}            onsite                     Giá trị target của hỉnh thức học onsite
@apiSuccess (data) {Int}            row_id                     ID row của bảng

@apiSuccessExample {json} Response: HTTP/1.1 200 OK: Nếu group: [statistical_by]
{
    "code": 200,
    "data": {
        "classroom": 1206,
        "distribution": 4242,
        "online": 3460,
        "onsite": 3998,
        "row_id": "e0f3e217-72a2-4d97-a24c-3159d2cb010c"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""
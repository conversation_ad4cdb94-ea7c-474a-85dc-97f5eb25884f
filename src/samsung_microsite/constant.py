** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *List Region ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {GET} [HOST]/api/v1.0/region Region
@apiDescription Region
@apiGroup Constant
@apiVersion 1.0.0
@apiName region

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (data:)           {string}        name     khu vực
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [    
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044",
            "name": "NORTH_1",
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180045",
            "name": "NORTH_2",
        }

        ]
}
"""
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *List City ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {GET} [HOST]/api/v1.0/city City
@apiDescription City
@apiGroup Constant
@apiVersion 1.0.0
@apiName city

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (data:)           {string}        name    tên  thành phố
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [    
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044",
            "name": "BAC NINH",
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180045",
            "name": "BAC GIANG",
        }           
        ]
}
"""
* ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *List District ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {GET} [HOST]/api/v1.0/district District
@apiDescription District
@apiGroup Constant
@apiVersion 1.0.0
@apiName district

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (data:)           {string}        name    tên huyện

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044",
            "name": "BINH THANH"
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180045",
            "name": "HO CHI MINH"
        }
    ]
}
"""
* ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *List Sell in type ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {GET} [HOST]/api/v1.0/sell_in_type Sell In Type
@apiDescription Sell In Type
@apiGroup Constant
@apiVersion 1.0.0
@apiName sell_in_type

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (data:)           {string}        name    sell in type

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [    
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044",
            "name": "ORG"
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180045",
            "name": "ORF"
        }
    ]
}
"""

* ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *List customer ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {GET} [HOST]/api/v1.0/customer Customer
@apiDescription Customer
@apiGroup Constant
@apiVersion 1.0.0
@apiName customer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (data:)           {string}        name    sell in type

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [    
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044",
            "name": "HHP - Son Nga BG"
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180045",
            "name": "HHP - Son Nga BA"
        }
    ]
}
"""
* ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *List Region SS ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {GET} [HOST]/api/v1.0/region_ss RegionSS
@apiDescription RegionSS
@apiGroup Constant
@apiVersion 1.0.0
@apiName region_ss

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (data:)           {string}        name    sell in type

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [    
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044",
            "name": "N. EAST"
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180045",
            "name": "HA NOI"
        }
    ]
}
"""


* ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *List Shopgrade ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {GET} [HOST]/api/v1.0/shop_grade ShopGrade
@apiDescription ShopGrade
@apiGroup Constant
@apiVersion 1.0.0
@apiName shop_grade

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess (data:)           {string}        name    

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [    
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044",
            "name": "A"
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180045",
            "name": "B"
        }
    ]
}

"""

***************************************** Danh sách customers bởi sell in type *********************************
* version: 2.0.0 *
****************************************************************************************************************
"""
@api {get} [HOST]/api/v1.0/sell_in_type/customers Lấy danh sách customer by sell in type
@apiDescription Lấy danh sách customer by sell in type
@apiGroup Constant
@apiVersion 1.0.0
@apiName customer_by_sell_in_type 
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query:) {String} object_id                                   sell_in_type_id 

@apiSuccess (data) {object}            customers                   Danh sách các customers được lấy ra bởi sell_in_type
@apiSuccess (data) {String}            customers.id                Định danh customer
@apiSuccess (data) {String}            customers.name              Tên customer
@apiSuccess (data) {String}            customers.code              Mã customer
@apiSuccess (data) {String}            row_id                      sell_in_type_id được gửi xuống 

@apiSuccessExample {json} Response: HTTP/1.1 200 OK:
{
    "code": 200,
    "data": {
        "customers": [
            {
                "code": "hhp_cong_ty_tnhh_the_gioi_di_dong",
                "id": "61d2a6f6-52d0-48ab-a79b-349712722976",
                "name": "HHP - Công Ty TNHH Thế Giới Di Động"
            },
            {
                "code": "hhp_viettel_sieu_thi",
                "id": "e6d59331-d3d6-436e-9cf9-75579e95c630",
                "name": "HHP-Viettel Sieu Thi"
            },
            {
                "code": "hhp_fpt_retail_shops",
                "id": "dea8730b-3692-4716-9799-f2ad38d4b1f1",
                "name": "HHP - FPT Retail Shops"
            }
        ],
        "row_id": "03624ce5-8a2e-40d5-8a9a-7b97885bb992"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


#!/usr/bin/python
# -*- coding: utf8 -*-
************************************* Thêm khóa học ** **********************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {post} [HOST]/api/v1.0/courses Thêm khóa học
@apiDescription Thêm khóa học
@apiGroup Quản lý khóa học
@apiVersion 1.0.0
@apiName  AddCourse
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Body:)         {String}        name                        Tên khóa học
@apiParam   (Body:)         {String}        product_ids                 Danh sách ID của các product trong khóa học
@apiParam   (Body:)         {Array}         course_types                Đối tượng chứa thông tin của các kiểu khóa học tương ứng với khóa học
@apiParam   (Body:)         {String}        course_types.key            Key của kiểu khóa học [CLASSROOM, ONSITE, ONLINE]
@apiParam   (Body:)         {String}        course_types.name           Tên của kiểu khóa học
@apiParam   (Body:)         {String}        course_types.start          Thời gian bắt đầu của khóa học với kiểu khóa học tương ứng. Format: DD/MM/YYYY
@apiParam   (Body:)         {String}        course_types.end            Thời gian kết thúc của khóa học với kiểu khóa học tương ứng. Format: DD/MM/YYYY


@apiParamExample    {json}  Body example:
{
    "name": "",
    "product_ids": [],
    "course_types": [
        {
            "key": "CLASSROOM",
            "name": "Classroom",
            "start": "2021/02/09",
            "end": "2021/03/08"
        },
        {
            "key": "ONLINE",
            "name": "Online",
            "start": "2021/04/05",
            "end": "2021/06/07"
        },
        {
            "key": "ONSITE",
            "name": "Onsite",
            "start": "2021/04/08",
            "end": "2021/08/09"
        }
    ]
}


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "message": "request thành công."
}
"""


************************************* Cập nhật khóa học ** **********************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {patch} [HOST]/api/v1.0/courses/<course_id> Cập nhật khóa học
@apiDescription Cập nhật khóa học
@apiGroup Quản lý khóa học
@apiVersion 1.0.0
@apiName  UpdateCourse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resources:)      {String}        course_id                   Định danh của khóa học

@apiParam   (Body:)         {String}        name                        Tên khóa học
@apiParam   (Body:)         {String}        product_ids                 Danh sách ID của các product trong khóa học
@apiParam   (Body:)         {Array}         course_types                Đối tượng chứa thông tin của các kiểu khóa học tương ứng với khóa học
@apiParam   (Body:)         {String}        course_types.key            Key của kiểu khóa học [CLASSROOM, ONSITE, ONLINE]
@apiParam   (Body:)         {String}        course_types.name           Tên của kiểu khóa học
@apiParam   (Body:)         {String}        course_types.start          Thời gian bắt đầu của khóa học với kiểu khóa học tương ứng. Format: DD/MM/YYYY
@apiParam   (Body:)         {String}        course_types.end            Thời gian kết thúc của khóa học với kiểu khóa học tương ứng. Format: DD/MM/YYYY


@apiParamExample    {json}  Body example:
{
    "name": "",
    "product_ids": [],
    "course_types": [
        {
            "key": "CLASSROOM",
            "name": "Classroom",
            "start": "2021/02/09",
            "end": "2021/03/08"
        },
        {
            "key": "ONLINE",
            "name": "Online",
            "start": "2021/04/05",
            "end": "2021/06/07"
        },
        {
            "key": "ONSITE",
            "name": "Onsite",
            "start": "2021/04/08",
            "end": "2021/08/09"
        }
    ]
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""


************************************* Danh sách khóa học ** **********************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {get} [HOST]/api/v1.0/courses Danh sách khóa học
@apiDescription Danh sách khóa học
@apiGroup Quản lý khóa học
@apiVersion 1.0.0
@apiName ListCourse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query:)   {String}    [search]  Nội dung cần tìm kiếm (encode trước khi gửi).
@apiParam (Query:)   {String=name,created_time,updated_time}    [sort]  Sắp xếp theo các trường chỉ định.
@apiParam (Query:)   {String=asc,desc}    order  Kiểu sắp xếp.

@apiUse paging
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044"
            "name": "Khóa học A"
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            "name": "Khóa học B"
        }
    ]
}
"""


************************************* Thông tin chi tiết khóa học ** **********************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {get} [HOST]/api/v1.0/courses/<course_id> Thông tin chi tiết khóa học
@apiDescription Thông tin chi tiết khóa học
@apiGroup Quản lý khóa học
@apiVersion 1.0.0
@apiName  DetailCourse

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resources:)     {String}    course_id   Định danh của khóa học

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "name": "Khóa học A",
        "product_ids": [],
        "course_types": [
            {
                "key": "CLASSROOM",
                "name": "Classroom",
                "start": "2021/02/09",
                "end": "2021/03/08"
            },
            {
                "key": "ONLINE",
                "name": "Online",
                "start": "2021/04/05",
                "end": "2021/06/07"
            },
            {
                "key": "ONSITE",
                "name": "Onsite",
                "start": "2021/04/08",
                "end": "2021/08/09"
            }
        ]
    }
}
"""

************************************* Đồng bộ dữ liệu ** **********************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {GET} [HOST]/api/v1.0/course/datatypes Danh sách kiểu dữ liệu
@apiDescription Danh sách kiểu dữ liệu
@apiGroup Đồng bộ dữ liệu
@apiVersion 1.0.0
@apiName ListData

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (data:)           {string}       name     tên loại dữ liệu

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [    
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044"
            "name": "Samsung+"
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            "name": "Classroom"
        }
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            "name": "Onsite"
        }
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
            "name": "Shoplist"
        }
        
    ]
}
"""


************************************* Danh sách bài học ** *****************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {get} [HOST]/api/v1.0/lessons Danh sách bài học
@apiDescription Danh sách bài học
@apiGroup Đồng bộ dữ liệu
@apiVersion 1.0.0
@apiName  Listlesson

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)   {String}        search                   Nội dung cần tìm kiếm (encode trước khi gửi).
@apiParam   (Query:)   {String}        course_id                Định danh của khóa học
@apiParam   (Query:)   {String}        product_id               Định danh của sản phẩm


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "id": "897389743498",
            "name": "Bài học 1"
        },
        {
            "id": "897389743499",
            "name": "Bài học 2"
        },
        {
            "id": "897389743497",
            "name": "Bài học 3"
        }
    ]
}
"""


************************************* Thêm bài học ** **********************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {post} [HOST]/api/v1.0/lessons Thêm bài học
@apiDescription Thêm bài học
@apiGroup Đồng bộ dữ liệu
@apiVersion 1.0.0
@apiName  Addlesson

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resources:)      {String}        course_id   Định danh của khóa học

@apiParam   (Body:)         {String}        course_id                ID khóa học
@apiParam   (Body:)         {String}        product_id               ID sản phẩm
@apiParam   (Body:)         {Object}        lessons                 Đối tượng chứa thuộc tính bài học 
@apiParam   (Body:)         {Array}         lessons.name            Danh sách tên của bài học được lưu 


@apiParamExample    {json}  Body example:
{
    "course_id": "",
    "product_id": "",
    "lessons": [
        {
            "name": "Bài học 1"
        },
        {
            "name": "Bài học 2"
        }
    ]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""


************************************* Danh sách bài thi ** *****************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {get} [HOST]/api/v1.0/exams Danh sách bài thi
@apiDescription Danh sách bài thi
@apiGroup Đồng bộ dữ liệu
@apiVersion 1.0.0
@apiName  ListExam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)   {String}        search                   Nội dung cần tìm kiếm (encode trước khi gửi).
@apiParam   (Query:)   {String}        course_id                Định danh của khóa học
@apiParam   (Query:)   {String}        product_id               Định danh của sản phẩm


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "id": "897389743498",
            "name": "Bài thi 1"
        },
        {
            "id": "897389743499",
            "name": "Bài thi 2"
        },
        {
            "id": "897389743497",
            "name": "Bài thi 3"
        }
    ]
}
"""

************************************* Thêm bài thi ** **********************************
* version: 1.0.0 *
***************************************************************************************
"""
@api {post} [HOST]/api/v1.0/exams Thêm bài thi
@apiDescription Thêm bài thi
@apiGroup Đồng bộ dữ liệu
@apiVersion 1.0.0
@apiName  AddExam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resources:)      {String}        course_id   Định danh của khóa học

@apiParam   (Body:)         {String}        course_id                ID khóa học
@apiParam   (Body:)         {String}        product_id               ID sản phẩm
@apiParam   (Body:)         {Object}        exams                    Đối tượng chứa thuộc tính bài thi 
@apiParam   (Body:)         {Array}         exams.name               Danh sách tên của bài thi được lưu 


@apiParamExample    {json}  Body example:
{
    "course_id": "",
    "product_id": "",
    "exams": [
        {
            "name": "Bài thi 1"
        },
        {
            "name": "Bài thi 2"
        }
    ]
}


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công."
}
"""

#!/usr/bin/python
# -*- coding: utf8 -*-
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Ti<PERSON>n
đ<PERSON>
thời
gian ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {get} [HOST]/api/v1.0/insights/time_progress Tiến độ thời gian
@apiDescription Báo cáo tiến độ thời gian
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName TimeProgress
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:) {String}    [current_time]                    Thời gian hiện tại (now). Nếu không truyền lên mặc định BE sẽ lấy là thời điểm hiện tại.
@apiParam   (Query:) {String}    course_id                    ID khoá học.


@apiSuccess (Data:) {Object}     onsite, online, classroom       Key của kiểu khóa học
@apiSuccess (Data:) {Datetime}   start                           Thời gian bắt đầu của kiểu khóa học lọc theo khóa học
@apiSuccess (Data:) {Datetime}   end                             Thời gian kết thúc của kiểu khóa học lọc theo khóa học
@apiSuccess (Data:) {Float}      percent                         Giá trị trung bình (Tính theo %)

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "key": "onsite"
            "start": "2021/06/01",
            "end": "2021/06/08",
            "percent": "100"
        },
        {
            "key": "online"
            "start": "2021/06/11",
            "end": "2021/06/20",
            "percent": "80"
        },
        {
            "key": "classroom"
            "start": "2021/06/01",
            "end": "2021/06/30",
            "percent": "60"
        }
    ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Độ
bao
phủ ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {POST} [HOST]/api/v1.0/insights/coverage/actions/filter Độ bao phủ
@apiDescription Báo cáo độ bao phủ
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName Coverage
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Body:) {String} course_id                                 ID khóa học
@apiParam (Body:) {Datetime} time_start                              Thời gian bắt đầu khóa học 
@apiParam (Body:) {Datetime} time_end                                Thời gian kết thúc khóa học
@apiParam (Body:) {Array[]} [product_ids]                               Danh sách ID sản phẩm  
@apiParam (Body:) {Array[]} [region_ids]                                Danh sách ID khu vực 
@apiParam (Body:) {Array[]} [sell_in_type_ids]                          Danh sách ID sell in type   
@apiParam (Body:) {Array[]} [customer_ids]                              Danh sách ID khách hàng    
@apiParam (Body:) {Array[]} [city_ids]                                  Danh sách ID thành phố    
@apiParam (Body:) {Array[]} [district_ids]                              Danh sách ID quận (huyện)    

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "key": "store",
            "total": 98,
            "a_b": 99
        },
        {
            "key": "fsm", 
            "total": 98,
            "a_b": 99
        }
    ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Tần
suất
training ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {POST} [HOST]/api/v1.0/insights/frequency/actions/filter Tần suất training
@apiDescription Báo cáo tần suất training
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName TrainingFrequency
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Body:) {String} course_id                                 ID khóa học
@apiParam (Body:) {Datetime} time_start                              Thời gian bắt đầu khóa học 
@apiParam (Body:) {Datetime} time_end                                Thời gian kết thúc khóa học
@apiParam (Body:) {Array[]} [product_ids]                               Danh sách ID sản phẩm  
@apiParam (Body:) {Array[]} [region_ids]                                Danh sách ID khu vực 
@apiParam (Body:) {Array[]} [sell_in_type_ids]                          Danh sách ID sell in type   
@apiParam (Body:) {Array[]} [customer_ids]                              Danh sách ID khách hàng    
@apiParam (Body:) {Array[]} [city_ids]                                  Danh sách ID thành phố    
@apiParam (Body:) {Array[]} [district_ids]                              Danh sách ID quận (huyện)    


@apiSuccess (Data:) {Object}     data                      Thông tin của dữ liệu training.
@apiSuccess (Data:) {Object=store,fsm}     data.entity                      Thông tin của dữ liệu training.Entity tương ứng với <code>store, fsm</code>.
@apiSuccess (Data:) {string}     data.entity.key                      Đối tượng chứa số lần đã được training.
                                                            <ul>
                                                                <li><code>3x</code>: Tần suất training >= 3 lần</li>
                                                                <li><code>2x</code>: Tần suất training 2 lần</li>
                                                                <li><code>1x</code>: Tần suất training 1 lần</li>
                                                                <li><code>info_frequency_site</code>: Thông tin của training của Store</li>
                                                                <li><code>info_frequency_fsm</code>: Thông tin của training của FSM</li>
                                                            <ul>
@apiSuccess (Data:) {Int}        data.entity.value                           Giá trị thực tế 
@apiSuccess (Data:) {Float}      data.entity.percent                         Giá trị trung bình (%)


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "fsm": [
            {
                "key": "3x",
                "percent": 100.0,
                "value": 3
            },
            {
                "key": "2x",
                "percent": 0.0,
                "value": 0
            },
            {
                "key": "1x",
                "percent": 0.0,
                "value": 0
            },
            {
                "key": "info_frequency_fsm",
                "percent_fsm": 100.0,
                "times_fsm": 3.0,
                "times_fsm_ab": 3
            }
        ],
        "site": [
            {
                "key": "3x",
                "percent": 100.0,
                "value": 3
            },
            {
                "key": "2x",
                "percent": 0.0,
                "value": 0
            },
            {
                "key": "1x",
                "percent": 0.0,
                "value": 0
            },
            {
                "key": "info_frequency_site",
                "percent_site": 100.0,
                "times_site": 3.0,
                "times_site_ab": 3.0
            }
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Tiến
độ
training
theo
cửa
hàng ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {POST} [HOST]/api/v1.0/insights/shop_progress_training/actions/filter Tiến độ training theo cửa hàng
@apiDescription Báo cáo độ tiến độ training theo cửa hàng
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName ProgressTrainingByShop
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Body:) {String} course_id                                 ID khóa học
@apiParam (Body:) {Datetime} time_start                              Thời gian bắt đầu khóa học 
@apiParam (Body:) {Datetime} time_end                                Thời gian kết thúc khóa học
@apiParam (Body:) {Array[]} [product_ids]                               Danh sách ID sản phẩm  
@apiParam (Body:) {Array[]} [region_ids]                                Danh sách ID khu vực 
@apiParam (Body:) {Array[]} [sell_in_type_ids]                          Danh sách ID sell in type   
@apiParam (Body:) {Array[]} [customer_ids]                              Danh sách ID khách hàng    
@apiParam (Body:) {Array[]} [city_ids]                                  Danh sách ID thành phố    
@apiParam (Body:) {Array[]} [district_ids]                              Danh sách ID quận (huyện)    

@apiSuccess (Data:) {Object}     plan, distribution                     Đối tượng chart được hiển thị
@apiSuccess (Data:) {Object}     total, onsite, classroom, online       Đối tượng chứa dữ liệu thống kê
@apiSuccess (Data:) {Int}        store_plan                             Giá trị ước tính của cửa hàng theo kế hoạch
@apiSuccess (Data:) {Int}        store_actual                           Giá trị thực tế đạt được của cửa hàng
@apiSuccess (Data:) {Int}        percent_vs_plan                        Giá trị trung bình đạt được so với kế hoạch được đặt ra

@apiSuccess (Data:) {Object}     total, onsite, classroom, online       Đối tượng chứa dữ liệu thống kê
@apiSuccess (Data:) {Int}        distribution                           Giá trị ước tính các cửa hàng của nhà phân phối theo kế hoạch
@apiSuccess (Data:) {Int}        store_actual                           Giá trị thực tế các cửa hàng đạt được của của các nhà phân phối
@apiSuccess (Data:) {Int}        percent_vs_distribution                Giá trị trung bình đạt được so với kế hoạch được đặt ra

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "plan": [
            {
                "key": "total",
                "store_plan": 4231,
                "store_actual": 4000,
                "percent_vs_plan": 96
            },
            {
                "key": "onsite",
                "store_plan": 4000,
                "store_actual": 3111,
                "percent_vs_plan": 96
            },
            {
                "key": "classroom",
                "store_plan": 2500,
                "store_actual": 2121,
                "percent_vs_plan": 88
            },
            {
                "key": "online",
                "store_plan": 5000,
                "store_actual": 4333,
                "percent_vs_plan": 77
            }
        ],
        "distribution": [
            {
                "key": "total"
                "distribution": 4231,
                "store_actual": 4000,
                "percent_vs_distribution": 96
            },
            {
                "key": "onsite"
                "distribution": 4000,
                "store_actual": 3111,
                "percent_vs_distribution": 91
            },
            {
                "key": "classroom"
                "distribution": 2500,
                "store_actual": 2121,
                "percent_vs_distribution": 87
            },
            {
                "key": "online"
                "distribution": 5000,
                "store_actual": 4543,
                "percent_vs_distribution": 75
            }
        ]
    }
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Tiến
độ
training
theo
FSM ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0
.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {POST} [HOST]/api/v1.0/insights/fsm_progress_training/actions/filter Tiến độ training theo FSM
@apiDescription Báo cáo độ tiến độ training theo FSM
@apiGroup Dashboard
@apiVersion 1.0.0
@apiName ProgressTrainingByFSM
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Body:) {String} course_id                                 ID khóa học
@apiParam (Body:) {Datetime} time_start                              Thời gian bắt đầu khóa học 
@apiParam (Body:) {Datetime} time_end                                Thời gian kết thúc khóa học
@apiParam (Body:) {Array[]} [product_ids]                               Danh sách ID sản phẩm  
@apiParam (Body:) {Array[]} [region_ids]                                Danh sách ID khu vực 
@apiParam (Body:) {Array[]} [sell_in_type_ids]                          Danh sách ID sell in type   
@apiParam (Body:) {Array[]} [customer_ids]                              Danh sách ID khách hàng    
@apiParam (Body:) {Array[]} [city_ids]                                  Danh sách ID thành phố    
@apiParam (Body:) {Array[]} [district_ids]                              Danh sách ID quận (huyện)    

@apiSuccess (Data:) {Object}     plan, distribution                     Đối tượng chart được hiển thị
@apiSuccess (Data:) {Object}     total, onsite, classroom, online       Đối tượng chứa dữ liệu thống kê
@apiSuccess (Data:) {Int}        store_plan                             Giá trị ước tính FSM của các cửa hàng theo kế hoạch
@apiSuccess (Data:) {Int}        store_actual                           Giá trị thực tế FSM đạt được của cửa hàng
@apiSuccess (Data:) {Int}        percent_vs_plan                        Giá trị trung bình FSM đạt được so với kế hoạch được đặt ra

@apiSuccess (Data:) {Object}     total, onsite, classroom, online       Đối tượng chứa dữ liệu thống kê
@apiSuccess (Data:) {Int}        distribution                           Giá trị FSM ước tính trong các cửa hàng của nhà phân phối theo kế hoạch
@apiSuccess (Data:) {Int}        store_actual                           Giá trị FSM thực tế trong các cửa hàng đạt được của của các nhà phân phối
@apiSuccess (Data:) {Int}        percent_vs_distribution                Giá trị trung bình đạt được so với kế hoạch được đặt ra

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "plan": [
            {
                "key": "total",
                "store_plan": 4231,
                "store_actual": 4000,
                "percent_vs_plan": 96
            },
            {
                "key": "onsite",
                "store_plan": 4000,
                "store_actual": 3111,
                "percent_vs_plan": 96
            },
            {
                "key": "classroom",
                "store_plan": 2500,
                "store_actual": 2121,
                "percent_vs_plan": 88
            },
            {
                "key": "online",
                "store_plan": 5000,
                "store_actual": 4333,
                "percent_vs_plan": 77
            }
        ],
        "distribution": [
            {
                "key": "total"
                "distribution": 4231,
                "store_actual": 4000,
                "percent_vs_distribution": 96
            },
            {
                "key": "onsite"
                "distribution": 4000,
                "store_actual": 3111,
                "percent_vs_distribution": 91
            },
            {
                "key": "classroom"
                "distribution": 2500,
                "store_actual": 2121,
                "percent_vs_distribution": 87
            },
            {
                "key": "online"
                "distribution": 5000,
                "store_actual": 4543,
                "percent_vs_distribution": 75
            }
        ]
    }
}
"""

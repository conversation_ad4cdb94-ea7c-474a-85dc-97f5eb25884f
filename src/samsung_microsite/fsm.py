#!/usr/bin/python
# -*- coding: utf8 -*-
************************************* Danh sách học viên ** **********************************
* version: 1.0.0 *
*********************************************************************************************
"""
@api {get} [HOST]/api/v1.0/fsm Danh sách học viên
@apiDescription Danh sách học viên
@apiGroup Học viên
@apiVersion 1.0.0
@apiName   ListProfile
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query:)    {String}          search            Nội dung cần tìm kiếm (encode trước khi gửi).

@apiUse paging

@apiSuccess (data)    {int}            total_fsm          Tổng số học viên có trong fsm
@apiSuccess (data)    {String}         id                 Định danh học viên
@apiSuccess (data)    {String}         code               Mã số nhân viên
@apiSuccess (data)    {String}         id_s               Mã số học viên
@apiSuccess (data)    {String}         full_name          Họ tên học viên
@apiSuccess (data)    {Boolean}        is_sam             True:có /False: không 
@apiSuccess (data)    {int}            total_fsm          Tổng số học viên
@apiSuccess (data)    {String}         site_code          Mã cửa hàng
@apiSuccess (data)    {String}         site_name          Tên cửa hàng
@apiSuccess (data)    {String}         customer_id        ID khách hàng của Samsung 
@apiSuccess (data)    {String}         region_id          ID Khu vực
@apiSuccess (data)    {String}         city_id            ID thành phố




@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "total_fsm": 43508,
    "data": [
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044",
            "id_s":"158ADF"
            "code": "ABC123",
            "full_name": "Nguyễn Văn A",
            "is_sam": True,
            "total_fsm": 4380,
            "site_code": "C09093",
            "site_name": "Điện máy xanh Mini",
            "customer_id": "T125385",
            "region_id": "asdfasdfsdf12",
            "city_id": "112223ddfasdf"
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180011",
            "code": "ABC124",
            "id_s":"158ADF"
            "full_name": "Nguyễn Văn B",
            "is_sam": False,
            "total_fsm": 4380,
            "site_code": "C09093",
            "site_name": "Thế giới di động",
            "customer_id": "T125385",
            "region_id": "asdfasdfsdf12",
            "city_id": "112223ddfasdf"
        },
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180045",
            "code": "ABC125",
            "id_s":"158ADF"
            "full_name": "Nguyễn Văn C",
            "is_sam": True,
            "total_fsm": 4380,
            "site_code": "C09093",
            "site_name": "Điện máy xanh Mini",
            "customer_id": "T125385",
            "region_id": "asdfasdfsdf12",
            "city_id": "112223ddfasdf"
        }
    ]
}
"""


************************************* Thông tin chi tiết học viên ** **********************************
* version: 1.0.0 *
******************************************************************************************************
"""
@api {get} [HOST]/api/v1.0/fsm/<fsm_id>/overview/<site_code> Thông tin tổng quan của học viên
@apiDescription Thông tin chi tiết học viên
@apiGroup Học viên
@apiVersion 1.0.0
@apiName   overview
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam (Resources:)  {String}            fsm_id                                      Định danh của học viên
@apiParam (Resources:)  {String}            site_code                                   Định danh cửa hàng nơi học viên trực thuộc

@apiParam (query:)      {datetime}          start_date                                  Thời điểm bắt đầu muốn tìm kiếm
@apiParam (query:)      {datetime}          end_date                                    Thời điểm kết thúc muốn tìm kiếm

@apiUse   paging

@apiSuccess (data)      {Object}            profile_info                                Thông tin của học viên
@apiSuccess (data)      {Object}            profile_info.general                        Thông tin chung
@apiSuccess (data)      {String}            profile_info.general.code                   Mã số nhân viên
@apiSuccess (data)      {String}            profile_info.general.id_s                   Mã số học viên
@apiSuccess (data)      {Boolean}           profile_info.general.status                 Trạng thái làm viêc của học viên, True:đang làm việc. False: Đã nghỉ việc
@apiSuccess (data)      {String}            profile_info.general.email                  Email học viên
@apiSuccess (data)      {String}            profile_info.general.region                 Khu vực
@apiSuccess (data)      {String}            profile_info.general.district               Huyện (Quận)
@apiSuccess (data)      {datetime}          profile_info.general.updated_time           Thời gian nhân viên được upload lên hê thống
@apiSuccess (data)      {Boolean}           profile_info.general.is_sam                 Học viên có phải là sambassador hay không (true/false)
@apiSuccess (data)      {String}            profile_info.general.site_name              Tên cửa hàng
@apiSuccess (data)      {String}            profile_info.general.site_code              Mã cửa hàng
@apiSuccess (data)      {String}            profile_info.general.customer               Khách hàng của
@apiSuccess (data)      {String}            profile_info.general.shop_grade             Cấp của cửa hàng
@apiSuccess (data)      {String}            profile_info.general.s_poin                 s_point của học viên
@apiSuccess (data)      {String}            profile_info.general.last_seen              Lần cuối cùng đăng nhập vào hệ thống s+ của học viên
@apiSuccess (data)      {String}            profile_info.general.id_facebook            Mã tài khoản facebook của học viên
@apiSuccess (data)      {String}            profile_info.general.id_facebook            Mã tài khoản zalo của học viên
@apiSuccess (data)      {String}            profile_info.general.full_name              Tên học viên
@apiSuccess (data)      {String}            profile_info.general.phone_number           SĐT của học viên

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "general": {
            "full_name:"Nguyễn Văn A",
            "code": "",
            "phone_number": "036453466",
            "email": "<EMAIL>",
            "region": "R3",
            "city": "TAY NINH",
            "district": "HOA THANH",
            "last_seen": "05/04/2021 08:00",
            "status": True,
            "id_facebook": "0123fb43",
            "id_zalo": "0123zb43",
            "is_sam": True,
            "s_poin": 760,
            "site_name": "Thế giới di động",
            "site_code": "C011012",
            "customer": "HHP - Công Ty TNHH Thế Giới Di Động",
            "shop_grade": "A",
            "updated_time","05/04/2021 08:00"
        },
    }
}
"""

************************************* Thông tin chi tiết học viên ** **********************************
* version: 1.0.0 *
******************************************************************************************************
"""
@api {get} [HOST]/api/v1.0/fsm/<fsm_id>/detail/<site_code> Thông tin chi tiết học viên
@apiDescription Thông tin chi tiết học viên
@apiGroup Học viên
@apiVersion 1.0.0
@apiName   Detail
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam (Resources:)      {String}         fsm_id                                    Định danh của học viên
@apiParam (Resources:)      {String}         site_code                                 Định danh cửa hàng nơi học viên trực thuộc

@apiParam (query:)          {datetime}       start_date                              Thời điểm bắt đầu muốn tìm kiếm
@apiParam (query:)          {datetime}       end_date                                Thời điểm kết thúc muốn tìm kiếm



@apiSuccess (data)          {Object}         profile_info.courses                    Danh sách các khóa học
@apiSuccess (data)          {Object}         profile_info.courses.id                 id của khóa học
@apiSuccess (data)          {String}         profile_info.courses.type               Kiểu khóa học
@apiSuccess (data)          {String}         profile_info.courses.name               Tên khóa học
@apiSuccess (data)          {String}         profile_info.courses.product_ids        Sản phẩn được training trong khóa học
@apiSuccess (data)          {String}         profile_info.courses.content            Nội dung khóa học
@apiSuccess (data)          {String}         profile_info.courses.status             Trang thái học viên tham gia khóa học ( Đã tham gia / Chưa tham gia)
@apiSuccess (data)          {String}         profile_info.courses.time               Thời gian hoàn thành khóa học
@apiSuccess (data)          {String}         profile_info.courses.score              Điển của học viên tương ứng với khóa học


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "courses": [
            {
                "id":"fghjkl789-232323rfdfsd"
                "type": "Classroom",
                "name": "202103_GALAXY A32 | A52 | A72",
                "product_ids": "rtyuiop456852",
                "content": "Mass Training",
                "status": "Đã tham gia",
                "time": "2021/03/18 09:09:41",
                "score": 10
            },
            {
                "id":"fghjkl789-232323ddd---rfdfsd"
                "type": "Samsung+",
                "name": "202103_GALAXY A32 | A52 | A72",
                "product_ids": "rtyuiop456852",
                "content": "Mass Training",
                "status": "Đã tham gia",
                "time": "2021/03/18 09:09:41",
                "score": 9
            },
            {
                "id":"fghjkl789-232323892dde333rfdfsd"
                "type": "Onsite",
                "name": "202101_GALAXY S21",
                "product_ids": "rtyuiop456852",
                "content": "Bài thi Online đợt 1",
                "status": "Chưa tham gia ",
                "time": "2021/03/18 09:09:41",
                "score": null
            }
    ]
}
"""
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Danh sách cửa hàng ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {POST} [HOST]/api/v1.0/stores/actions/filter Danh sách cửa hàng
@apiDescription Danh sách cửa hàng
@apiGroup Store
@apiVersion 1.0.0
@apiName   stores
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging


@apiParam (Query:)      {String}          search                              Nội dung cần tìm kiếm (encode trước khi gửi).
@apiParam (Query:)      {String=asc,desc}          sort                                asc/desc: xếp tăng dần/xếp giảm dần.
@apiParam (Query:)      {String=code}          field                               <code>Field</code> trường thông tin cần sắp xếp

@apiParam (Query:)       {String}          course_id                           ID khóa học
@apiParam (Body:)       {Array[string]}   [product_ids]                       Danh sách ID sản phẩm  
@apiParam (Body:)       {Array[string]}   [region_ids]                        Danh sách ID khu vực 
@apiParam (Body:)       {Array[string]}   [sell_in_type_ids]                  Danh sách ID sell in type   
@apiParam (Body:)       {Array[string]}   [customer_ids]                      Danh sách ID khách hàng   
@apiParam (Body:)       {Array[string]}   [city_ids]                          Danh sách ID thành phố    
@apiParam (Body:)       {Array[string]}   [district_ids]                      Danh sách ID quận (huyện)    
@apiParam (Body:)       {Int}             [status_train]                      Trạng thái train của học viên. <li><code>0:</code> trạng thái chưa train</li><li><code>1:</code> trạng thái đã train</code></li>  
@apiParam (Body:)       {Int}             [is_distribution]                   Kiểu distribution. <li><code>0:</code> không nằm trong distribution</li><li><code>1:</code> nằm trong distribution</code></li>  

@apiParam (data:)       {string}          site_code                           sitecode của cửa hàng 
@apiParam (data:)       {string}          site_name                           Đia điểm chi tiết nơi đặt cửa hàng
@apiParam (data:)       {string}          code                                Mã kho
@apiParam (data:)       {string}          title                               Tên siêu thị
@apiParam (data:)       {string}          region                              Khu vực nơi đặt cửa hàng 
@apiParam (data:)       {string}          city                                Thành phố nơi đặt cửa hàng
@apiParam (data:)       {string}          district                            Huyện nơi đặt cửa hàng
@apiParam (data:)       {string}          shop_grade                          Điểm đáng giá của cửa hàng, loại A,B,C...
@apiParam (data:)       {string}          sell_in_type                        Hình thức bán hàng của cửa hàng vd:ORG
@apiParam (data:)       {string}          customer                            Tên công ty/hệ thống chủ quản của cửa hàng
@apiParam (data:)       {int}             number_fsm                          Tổng số FSM trực thuộc cửa hàng
@apiParam (data:)       {float}           fsm_trained                         Tổng số FSM đã được train của tất cả các cửa hàng)/(Tổng số FSM trực thuộc cửa hàng)*100



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044",
            "site_code": "C09093",
            "site_name": "Điện Máy Xanh Mini (TGDĐ) NT 119 Đường 21/8 Khu 9 Phước Mỹ",            
            "code": "ABC123",
            "name_store": "MT_BD_MINI_PHUOC MY",
            "region": "R2",
            "city": "HCM",
            "district":"Quan 1",  
            "shop_grade":"A",
            "sell_in_type": "ORG"        
            "customer": "HHP - Công Ty TNHH Thế Giới Di Động",
            "number_fsm":8,
            "fsm_trained": 50.0                    
        }
    ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Danh sách cửa hàng ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {POST} [HOST]/api/v1.0/information_store_train/actions/filter Thông tin số lượng cửa hàng và học viên.
@apiDescription Thông tin số lượng cửa hàng và học viên.
@apiGroup Store
@apiVersion 1.0.0
@apiName   Information store train
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header




@apiParam (Body:)          {String}          course_id                                ID khóa học
@apiParam (Body:)          {Array[]}         [product_ids]                            Danh sách ID sản phẩm  
@apiParam (Body:)          {Array[]}         [region_ids]                             Danh sách ID khu vực 
@apiParam (Body:)          {Array[]}         [sell_in_type_ids]                       Danh sách ID sell in type   
@apiParam (Body:)          {Array[]}         [customer_ids]                           Danh sách ID khách hàng   
@apiParam (Body:)          {Array[]}         [city_ids]                               Danh sách ID thành phố    
@apiParam (Body:)          {Array[]}         [district_ids]                           Danh sách ID quận (huyện)    
@apiParam (Body:)          {Int}             [status_train]                           Trạng thái train của học viên. <li><code>0:</code> trạng thái chưa train</li><li><code>1:</code> trạng thái đã train</code></li>  
@apiParam (Body:)          {Int}             [is_distribution]                        Kiểu distribution. <li><code>0:</code> không nằm trong distribution</li><li><code>1:</code> nằm trong distribution</code></li> 
@apiParam (Query:)         {String}          search                                   Nội dung cần tìm kiếm (encode trước khi gửi).



@apiParam (data:)          {int}             total_store_distribute                   Tổng số Store nằm trong distribution
@apiParam (data:)          {int}             total_store_covered                      Số store đã train
@apiParam (data:)          {int}             total_fsm                                Tổng số fsm
@apiParam (data:)          {int}             total_fsm_trained                        Số fsm đã train

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "total_store_distribute": 4128,
        "total_store_covered": 500,
        "total_fsm": 25000,
        "total_fsm_trained": 8000
    }
}


"""
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Thông tin theo cửa hàng ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {GET} [HOST]/api/v1.0/stores/<store_id> Thông tin chung của store
@apiDescription Thông tin chung
@apiGroup Store
@apiVersion 1.0.0
@apiName   DetailStore
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Resources:)      {String}        store_id            Định danh của cửa hàng
@apiParam (Query:)          {String}        course_id           Khoá học ID
@apiParam (Query:)          {String}        [product_id]        Danh sách product_id, phân cách nhau bởi dấu phẩy.
@apiParam (Query:)          {datetime}      [start_time]        Thời gian bắt đầu
@apiParam (Query:)          {datetime}      [end_time]          Thời gian kết thúc

@apiParam (data:)           {datetime}      updated_time        Thời gian cập nhật cuối cùng
@apiParam (data:)           {int}           number_fsm          Tổng số FSM của 1 cưa hàng (vd:8)
@apiParam (data:)           {int}           number_ps           Tổng số FSM của 1 cưa hàng (vd:2)
@apiParam (data:)           {int}           is_distribution     Store có nằm trong distribution không ? <li><code>0:</code> không nằm trong distribution</li><li><code>1:</code> nằm trong distribution</li>



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "updated_time": "30-06-2021T00:00Z",
        "is_distribution": 1,
        "number_fsm": 8,
        "number_ps": 2
    }
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Thông tin theo cửa hàng ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {GET} [HOST]/api/v1.0/stores/<store_id>/overview Thông tin theo khóa học tổng quan
@apiDescription Thông tin theo khóa học tổng quan
@apiGroup Store
@apiVersion 1.0.0
@apiName   DetailOverviewing
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam (Resources:)    {String}                store_id                                   Định danh của cửa hàng

@apiParam (Query:)        {Array[string]}         [course_id]                                Khoá học ID
@apiParam (Query:)        {Array[string]}         [product_id]                               Danh sách product_id, phân cách nhau bởi dấu phẩy.
@apiParam (Query:)        {datetime}              start_time                                 Thời gian bắt đầu
@apiParam (Query:)        {datetime}              end_time                                   Thời gian kết thúc




@apiSuccess (data:)         {int}                   frequency_training_store                   Tổng số lần được train của 1 store của cả 3 hình thức (Classroom,onsite , online)
@apiSuccess (data:)         {int}                   number_fsm_trained                         Tổng số FSM được train của 1 store
@apiSuccess (data:)         {Array[]}               fsm                                        Danh sách học viên của cửa hàng
@apiSuccess (data:)         {string}                fsm.id_s                                   ID S+ Samsung
@apiSuccess (data:)         {string}                fsm.name                                   Tên nhân viên
@apiSuccess (data:)         {string}                fsm.phone                                  Số điện thoại của nhân viên
@apiSuccess (data:)         {Object}                fsm.social_id  <code>ID</code>             Mạng xã hội
@apiSuccess (data:)         {string}                fsm.social_id.zalo  <code>ID</code>        Mạng xã hội zalo
@apiSuccess (data:)         {string}                fsm.social_id.fb  <code>ID</code>          Mạng xã hội facebook
@apiSuccess (data:)         {boolean}               fsm.status_train  Trạng thái.              False chưa được train. True đã được train
@apiSuccess (data:)         {int}                   fsm.number_train                           Số lần được train
@apiSuccess (data:)         {int}                   fsm.number_train_classroom                 Số lần được train hình thức classroom
@apiSuccess (data:)         {int}                   fsm.number_train_onsite                    Số lần được train hình thức onsite
@apiSuccess (data:)         {int}                   fsm.number_train_online                    Số lần được train hình thức online


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "frequency_training_store": 1,
        "number_fsm_trained": 5,
        "fsm": [
            {
                "id_s": "MANV123",
                "name": "Nguyen Van A",
                "phone": "0123456789",
                "social_id": {
                    "zalo": "152376731276317831",
                    "fb": ""
                },
                "status_train": 1,
                "number_train": 4,
                "number_train_classroom": 1,
                "number_train_onsite": 2,
                "number_train_online": 1
            }
        ]
    }
}
"""
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Thông tin theo cửa hàng ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {GET} [HOST]/api/v1.0/stores/<store_id>/classroom Thông tin theo khóa học theo hình thức học "classroom"
@apiDescription Thông tin theo khóa học thức học "classroom"
@apiGroup Store
@apiVersion 1.0.0
@apiName   DetailClassroom
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse paging

@apiParam (Resources:)      {String}               store_id                Định danh theo hình thức học "classroom" cửa từng cửa hàng

@apiParam (Query:)          {Array[string]}        course_id               Khoá học ID
@apiParam (Query:)          {Array[string]}        [product_ids]           Danh sách product_id, phân cách nhau bởi dấu phẩy.
@apiParam (Query:)          {datetime}             start_time              Thời gian bắt đầu
@apiParam (Query:)          {datetime}             end_time                Thời gian kết thúc




@apiSuccess (data:)         {string}                  id_s                                   ID S+ Samsung
@apiSuccess (data:)           {string}                code                    Mã nhân viên
@apiSuccess (data:)           {string}                name                    Tên nhân viên
@apiSuccess (data:)           {int}                   is_sambassador          Nhân viên có phải là đại sứ SamSung không ? <li><code>0:</code> Không</li><li><code>1:</code> Có</li>
@apiSuccess (data:)           {string}                phone                   Số điện thoại của nhân viên
@apiSuccess (data:)           {string}                survey_code             Mã bài khảo sát
@apiSuccess (data:)           {float}                 score                   Điểm
@apiSuccess (data:)           {datetime}              joining_date            Ngày tham dự




@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {   
            "code": "97202",
            "name": "Ngyễn Văn Lợi",
            "is_sambassador": True,
            "phone": "036453466",
            "survey_code": "14R3",
            "score": 10.0,
            "joining_date": "2021/08/03"
        }
    ]
}
"""
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Thông tin theo cửa hàng ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {GET} [HOST]/api/v1.0/stores/<store_id>/onsite Thông tin theo khóa học hình thức "onstie"
@apiDescription Thông tin theo khóa học hình thức "onstie"
@apiGroup Store
@apiVersion 1.0.0
@apiName   DetailOnstie
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam (Resources:)      {String}               store_id                 Định danh theo hình thức học "onsite" cửa từng cửa hàng

@apiParam (Query:)          {Array[string]}        [course_ids]             Khoá học ID
@apiParam (Query:)          {Array[string]}        [product_ids]            Danh sách product_id, phân cách nhau bởi dấu phẩy.
@apiParam (Query:)          {datetime}             start_time               Thời gian bắt đầu
@apiParam (Query:)          {datetime}             end_time                 Thời gian kết thúc

@apiSuccess (data:)        {string}        id_s                    ID S+ của SamSung
@apiSuccess (data:)           {string}               code                     Mã nhân viên
@apiSuccess (data:)           {string}               name                     Tên nhân viên
@apiSuccess (data:)           {boolean}              is_sambassador           Nhân viên có phải là đại sứ SamSung không,False: Không,True: Có
@apiSuccess (data:)           {string}               phone                    Số điện thoại của nhân viên
@apiSuccess (data:)           {int}                  number_train             Số lần train
@apiSuccess (data:)           {float}                average_score            Điểm trung bình




@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [          
               {
                "code": "97202",
                "name":"Ngyễn Văn Lợi",
                "is_sambassador": True,
                "phone": "036453466",
                "number_train": 2,
                "average_score": 10.0,
            }
    ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *Thông tin theo cửa hàng ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
"""
@api {GET} [HOST]/api/v1.0/stores/<store_id>/samsung Thông tin theo khóa học theo hình thức học "samsung+"
@apiDescription Thông tin theo khóa học theo hình thức học "samsung+"
@apiGroup Store
@apiVersion 1.0.0
@apiName   DetailSamsung
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam (Resources:)   {String}        store_id                Định danh theo hình thức học "samsung+" cửa từng cửa hàng

@apiParam (Query:)       {String}        [course_ids]            Khoá học ID
@apiParam (Query:)       {String}        [product_ids]           Danh sách product_id, phân cách nhau bởi dấu phẩy.
@apiParam (Query:)       {datetime}      start_time              Thời gian bắt đầu
@apiParam (Query:)       {datetime}      end_time                Thời gian kết thúc


@apiSuccess (data:)        {string}        id_s                    ID S+ của SamSung
@apiSuccess (data:)        {string}        code                    Mã nhân viên
@apiSuccess (data:)        {string}        name                    Tên nhân viên
@apiSuccess (data:)        {int}           is_sambassador          Nhân viên có phải là đại sứ SamSung không ? <li><code>0:</code> Không</li><li><code>1:</code> Có</li>
@apiSuccess (data:)        {string}        phone                   Số điện thoại của nhân viên
@apiSuccess (data:)        {string}        product_id              Số lần train
@apiSuccess (data:)        {string}        content                 Content bài học hoặc bài thi
@apiSuccess (data:)        {float}         score                   Điểm

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
                {
                    "code": "97202",
                    "name":"Ngyễn Văn Lợi",
                    "is_sambassador":False,
                    "phone": "036453466",
                    "product_id":"1278378128371",
                    "content":"Snapcard KPS Galaxy A52",
                    "score": 90.0
                }   
    ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** * BẢNG THỐNG KÊ CHI TIẾT ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {POST} [HOST]/api/v1.0/covering/store_region/actions/filter Độ bao phủ theo cửa hàng "region"
@apiDescription Độ bao phủ theo cửa hàng theo "region"
@apiGroup BẢNG THỐNG KÊ CHI TIẾT
@apiVersion 1.0.0
@apiName store_region

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam (body:)           {string}            course_id                           id khóa học muốn tìm kiếm 
@apiParam (body:)           {Array[string]}     [product_ids]                       Danh sách id những sản phẩm muốn tìm kiếm
@apiParam (body:)           {Array[string]}     [region_ss_ids]                     Danh sách id những khu vực theo định danh của Sam Sung 
@apiParam (body:)           {Array[string]}     [region_ids]                        Danh sách id những mã khu vực  
@apiParam (body:)           {Array[string]}     [city_ids]                          Danh sách id thành phố muốn tìm kiếm
@apiParam (body:)           {datetime}          time_start                          Ngày bắt đầu cho bộ tìm kiếm
@apiParam (body:)           {datetime}          time_end                            Ngày kết thúc cho bộ tìm kiếm

@apiParam (data:)           {String}            region                              Khu vực
@apiParam (data:)           {String}            distribution                        Tổng số lượng store là distribution theo khu vực "cần" được train của cả 3 hình thức học
@apiParam (data:)           {String}            actual                              Số lượng store là distribution "đã" được train theo từng khu vực của cả 3 hình thức học
@apiParam (data:)           {String}            distribution_ratio                  Phần trăm của <code>actual</code> so với <code>distribution</code> của khu vực tương ứng của cả 3 hình thức học

@apiParam (data:)           {object}            onsite                              Loại hình khóa học là "Onsite"
@apiParam (data:)           {String}            onsite.target                       Tổng số lượng store là distribution "cần" được train của hình thức học "Onsite"
@apiParam (data:)           {String}            onsite.actual                       Tổng số lượng store là distribution "đã" được train của hình thức học "Onsite" 
@apiParam (data:)           {String}            onsite.target_ratio                 Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Onsite"
@apiParam (data:)           {String}            onsite.distribution_ratio           Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Onsite"

@apiParam (data:)           {object}            classroom                           Loại hình khóa học là "Classroom"
@apiParam (data:)           {String}            classroom.target                    Tổng số lượng store là distribution "cần" được train của hình thức học "Classroom" 
@apiParam (data:)           {String}            classroom.actual                    Tổng số lượng store là distribution "đã" được train của hình thức học "Classroom"
@apiParam (data:)           {String}            classroom.target_ratio              Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Classroom"
@apiParam (data:)           {String}            classroom.distribution_ratio        Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Classroom"


@apiParam (data:)           {object}            online                              Loại hình khóa học là "Online"
@apiParam (data:)           {String}            online.target                       Tổng số lượng store là distribution "cần" được train của hình thức học "Online"
@apiParam (data:)           {String}            online.actual                       Tổng số lượng store là distribution "đã" được train của hình thức học "Online" 
@apiParam (data:)           {String}            online.target_ratio                 Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Online"
@apiParam (data:)           {String}            online.distribution_ratio           Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Online"


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "region": "R1",
            "distribution": "1439",
            "actual": "1439",
            "distribution_ratio": "100",
            "onsite": {
                "target": "1400",
                "actual": "1400",
                "target_ratio": "100",
                "distribution_ratio": "97"
            },
            "class_room": {
                "target": "1070",
                "actual": "613",
                "target_ratio": "57",
                "distribution_ratio": "43"
            },
            "online": {
                "target": "1321",
                "actual": "1194",
                "target_ratio": "90",
                "distribution_ratio": "83"
            }
        }
    ]
}
"""


** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** * BẢNG THỐNG KÊ CHI TIẾT ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {POST} [HOST]/api/v1.0/covering/store_region_ss/actions/filter  Độ bao phủ theo cửa hàng theo "region_SS"
@apiDescription Độ bao phủ theo cửa hàng theo "region_SS"
@apiGroup BẢNG THỐNG KÊ CHI TIẾT
@apiVersion 1.0.0
@apiName store_region_ss

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (body:)           {string}         course_id                          id khóa học muốn tìm kiếm 
@apiParam (body:)           {Array[string]}  [product_ids]                      Danh sách id những sản phẩm muốn tìm kiếm
@apiParam (body:)           {Array[string]}  [region_ss_ids]                    Danh sách id những khu vực theo định danh của Sam Sung 
@apiParam (body:)           {Array[string]}  [region_ids]                       Danh sách id những mã khu vực  
@apiParam (body:)           {Array[string]}  [city_ids]                         Danh sách id thành phố muốn tìm kiếm
@apiParam (body:)           {datetime}       time_start                         Ngày bắt đầu cho bộ tìm kiếm
@apiParam (body:)           {datetime}       time_end                           Ngày kết thúc cho bộ tìm kiếm

@apiParam (data:)           {String}        region_ss                           Khu vực theo định danh của Samsung
@apiParam (data:)           {String}        distribution                        Tổng số lượng store là distribution "cần" được train của cả 3 hình thức học
@apiParam (data:)           {String}        actual                              Số lượng store là distribution "đã" được train của cả 3 hình thức học
@apiParam (data:)           {String}        distribution_ratio                  Phần trăm của <code>actual</code> so với <code>distribution</code> tương ứng của cả 3 hình thức học

@apiParam (data:)           {object}        onsite                              Loại hình khóa học là "Onsite"
@apiParam (data:)           {String}        onsite.target                       Tổng số lượng store là distribution "cần" được train của hình thức học "Onsite"
@apiParam (data:)           {String}        onsite.actual                       Tổng số lượng store là distribution "đã" được train của hình thức học "Onsite" 
@apiParam (data:)           {String}        onsite.target_ratio                 Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Onsite"
@apiParam (data:)           {String}        onsite.distribution_ratio           Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Onsite"

@apiParam (data:)           {object}        classroom                           Loại hình khóa học là "Classroom"
@apiParam (data:)           {String}        classroom.target                    Tổng số lượng store là distribution "cần" được train của hình thức học "Classroom" 
@apiParam (data:)           {String}        classroom.actual                    Tổng số lượng store là distribution "đã" được train của hình thức học "Classroom"
@apiParam (data:)           {String}        classroom.target_ratio              Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Classroom"
@apiParam (data:)           {String}        classroom.distribution_ratio        Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Classroom"


@apiParam (data:)           {object}        online                              Loại hình khóa học là "Online"
@apiParam (data:)           {String}        online.target                       Tổng số lượng store là distribution "cần" được train của hình thức học "Online"
@apiParam (data:)           {String}        online.actual                       Tổng số lượng store là distribution "đã" được train của hình thức học "Online" 
@apiParam (data:)           {String}        online.target_ratio                 Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Online"
@apiParam (data:)           {String}        online.distribution_ratio           Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Online"

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "region_ss": "HO CHI MINH",
            "distribution": "1439",
            "actual": "1439",
            "distribution_ratio": "100",
            "onsite": {
                "target": "1400",
                "actual": "1400",
                "target_ratio": "100",
                "distribution_ratio": "97"
            },
            "class_room": {
                "target": "1070",
                "actual": "613",
                "target_ratio": "57",
                "distribution_ratio": "43"
            },
            "online": {
                "target": "1321",
                "actual": "1194",
                "target_ratio": "90",
                "distribution_ratio": "83"
            }
        }
    ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** * BẢNG THỐNG KÊ CHI TIẾT ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {POST} [HOST]/api/v1.0/covering/store_shop_grade/actions/filter   Độ bao phủ theo cửa hàng theo "shop_grade"
@apiDescription  Độ bao phủ theo cửa hàng theo "shop_grade"
@apiGroup BẢNG THỐNG KÊ CHI TIẾT
@apiVersion 1.0.0
@apiName store_shop_grade

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (body:)           {string}                course_id                           id khóa học muốn tìm kiếm 
@apiParam (body:)           {Array[string]}         [product_ids]                       Danh sách id những sản phẩm muốn tìm kiếm
@apiParam (body:)           {Array[string]}         [region_ss_ids]                     Danh sách id những khu vực theo định danh của Sam Sung 
@apiParam (body:)           {Array[string]}         [region_ids]                        Danh sách id những mã khu vực  
@apiParam (body:)           {Array[string]}         [city_ids]                          Danh sách id thành phố muốn tìm kiếm
@apiParam (body:)           {datetime}              time_start                          Ngày bắt đầu cho bộ tìm kiếm
@apiParam (body:)           {datetime}              time_end                            Ngày kết thúc cho bộ tìm kiếm

@apiParam (data:)           {String=A,B,C,D}        shop_grade                          Điểm đánh giá của từng cửa hàng loại A,B,C
@apiParam (data:)           {String}                distribution                        Tổng số lượng store là distribution của cả 3 hình thức học
@apiParam (data:)           {String}                actual                              Số lượng store là distribution "đã" được train của cả 3 hình thức học
@apiParam (data:)           {String}                distribution_ratio                  (actual/distribution)*100 của cả 3 hình thức học

@apiParam (data:)           {object}                onsite                              Loại hình khóa học là "Onsite"
@apiParam (data:)           {String}                onsite.target                       Tổng số lượng store là distribution "cần" được train của hình thức học "Onsite"
@apiParam (data:)           {String}                onsite.actual                       Tổng số lượng store là distribution "đã" được train Đạt loại của hình thức học "Onsite" 
@apiParam (data:)           {String}                onsite.target_ratio                 Phần trăm <code>Actual</code> so với <code>Target</code> trong hình thức học "Onsite"
@apiParam (data:)           {String}                onsite.distribution_ratio           Phần trăm <code>Actual</code> so với <code>Distribution</code> trong hình thức học "Onsite"

@apiParam (data:)           {object}                classroom                           Loại hình khóa học là "Classroom"
@apiParam (data:)           {String}                classroom.target                    Tổng số lượng store là distribution của hình thức học "Classroom" 
@apiParam (data:)           {String}                classroom.actual                    Tổng số lượng store là distribution của hình thức học "Classroom"
@apiParam (data:)           {String}                classroom.target_ratio              Phần trăm <code>Actual</code> so với <code>Target</code>trong hình thức học "Classroom"
@apiParam (data:)           {String}                classroom.distribution_ratio        Phần trăm <code>Actual</code> so với <code>Distribution</code> trong hình thức học "Classroom"


@apiParam (data:)           {object}                online                              Loại hình khóa học là "Online"
@apiParam (data:)           {String}                online.target                       Tổng số lượng store là distribution của hình thức học  "Online"
@apiParam (data:)           {String}                online.actual                       Tổng số lượng store là distribution của hình thức học "Online" 
@apiParam (data:)           {String}                online.target_ratio                 Phần trăm <code>Actual</code> so với <code>Target</code> trong hình thức học "Online"
@apiParam (data:)           {String}                online.distribution_ratio           Phần trăm <code>Actual</code> so với <code>Distribution</code> trong hình thức học "Online"

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "shop_grade": "A",
            "distribution": "1439",
            "actual": "1439",
            "distribution_ratio": "100",
            "onsite": {
                "target": "1400",
                "actual": "1400",
                "target_ratio": "100",
                "distribution_ratio": "97"
            },
            "class_room": {
                "target": "1070",
                "actual": "613",
                "target_ratio": "57",
                "distribution_ratio": "43"
            },
            "online": {
                "target": "1321",
                "actual": "1194",
                "target_ratio": "90",
                "distribution_ratio": "83"
            }
        }
    ]
}
"""
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** * BẢNG THỐNG KÊ CHI TIẾT ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {POST} [HOST]/api/v1.0/covering/store_customer/actions/filter  Độ bao phủ theo cửa hàng "store_customer"
@apiDescription Độ bao phủ theo cửa hàng "store_customer"
@apiGroup BẢNG THỐNG KÊ CHI TIẾT
@apiVersion 1.0.0
@apiName store_customer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (body:)           {string}                course_id                           id khóa học muốn tìm kiếm 
@apiParam (body:)           {Array[string]}         [product_ids]                       Danh sách id những sản phẩm muốn tìm kiếm
@apiParam (body:)           {Array[string]}         [region_ss_ids]                     Danh sách id những khu vực theo định danh của Sam Sung 
@apiParam (body:)           {Array[string]}         [region_ids]                        Danh sách id những mã khu vực  
@apiParam (body:)           {Array[tring]}          [city_ids]                          Danh sách id thành phố muốn tìm kiếm
@apiParam (body:)           {datetime}              time_start                          Ngày bắt đầu cho bộ tìm kiếm
@apiParam (body:)           {datetime}              time_end                            Ngày kết thúc cho bộ tìm kiếm

@apiParam (data:)           {String}                customer                            Tên công ty/hệ thống chủ quản của cửa hàng
@apiParam (data:)           {String}                distribution                        Tổng số lượng store "cần" được train là distribution thuộc customer của cả 3 hình thức học
@apiParam (data:)           {String}                actual                              Số lượng store là distribution "đã" được train thuộc customer của cả 3 hình thức học
@apiParam (data:)           {String}                distribution_ratio                  Phần trăm <code>actual</code> so với <code>distribution</code> của cả 3 hình thức học

@apiParam (data:)           {object}                onsite                              Loại hình khóa học là "Onsite"
@apiParam (data:)           {String}                onsite.target                       Tổng số lượng store là distribution "cần" được train của hình thức học "Onsite"
@apiParam (data:)           {String}                onsite.actual                       Tổng số lượng store là distribution "đã" được train của hình thức học "Onsite" 
@apiParam (data:)           {String}                onsite.target_ratio                 Phần trăm <code>actual</code> so với <code>target</code> trong hình thức học "Onsite"
@apiParam (data:)           {String}                onsite.distribution_ratio           Phần trăm <code>actual</code> so với <code>distribution</code> trong hình thức học "Onsite"

@apiParam (data:)           {object}                classroom                           Loại hình khóa học là "Classroom"
@apiParam (data:)           {String}                classroom.target                    Tổng số lượng store là distribution của hình thức học "Classroom" 
@apiParam (data:)           {String}                classroom.actual                    Tổng số lượng store là distribution của hình thức học "Classroom"
@apiParam (data:)           {String}                classroom.target_ratio              Phần trăm <code>actual</code> so với <code>target</code> trong hình thức học "Classroom"
@apiParam (data:)           {String}                classroom.distribution_ratio        Phần trăm <code>actual</code> so với <code>distribution</code> trong hình thức học "classroom"


@apiParam (data:)           {object}                online                              Loại hình khóa học là "Online"
@apiParam (data:)           {String}                online.target                       Tổng số lượng store là distribution của hình thức học  "Online"
@apiParam (data:)           {String}                online.actual                       Tổng số lượng store là distribution của hình thức học "Online" 
@apiParam (data:)           {String}                online.target_ratio                 Phần trăm <code>actual</code> so với <code>target</code> trong hình thức học "Online"
@apiParam (data:)           {String}                online.distribution_ratio           Phần trăm <code>actual</code> so với <code>distribution</code> trong hình thức học "Online"



@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "customer": "TGDD",
            "distribution": "1439",
            "actual": "1439",
            "distribution_ratio": "100",
            "onsite": {
                "target": "1400",
                "actual": "1400",
                "target_ratio": "100",
                "distribution_ratio": "97"
            },
            "class_room": {
                "target": "1070",
                "actual": "613",
                "target_ratio": "57",
                "distribution_ratio": "43"
            },
            "online": {
                "target": "1321",
                "actual": "1194",
                "target_ratio": "90",
                "distribution_ratio": "83"
            }
        }
    ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** * BẢNG THỐNG KÊ CHI TIẾT ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {POST} [HOST]/api/v1.0/covering/fsm_region/actions/filter  Độ bao phủ theo FSM "region"
@apiDescription Độ bao phủ theo FSM theo "region"
@apiGroup BẢNG THỐNG KÊ CHI TIẾT
@apiVersion 1.0.0
@apiName fsm_region

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (body:)           {string}                course_id                       id khóa học muốn tìm kiếm 
@apiParam (body:)           {Array[string]}         [product_ids]                   Danh sách id những sản phẩm muốn tìm kiếm
@apiParam (body:)           {Array[string]}         [region_ss_ids]                 Danh sách id những khu vực theo định danh của Sam Sung 
@apiParam (body:)           {Array[string]}         [region_ids]                    Danh sách id những mã khu vực  
@apiParam (body:)           {Array[tring]}          [city_ids]                      Danh sách id thành phố muốn tìm kiếm
@apiParam (body:)           {datetime}              time_start                      Ngày bắt đầu cho bộ tìm kiếm
@apiParam (body:)           {datetime}              time_end                        Ngày kết thúc cho bộ tìm kiếm

@apiParam (data:)           {String}                region                          Khu vực theo định danh (vd:R1) 
@apiParam (data:)           {String}                distribution                    Tổng số lượng FSM là distribution "cần" được train của cả 3 hình thức học
@apiParam (data:)           {String}                actual                          Số lượng FSM là distribution "đã" được train của cả 3 hình thức học
@apiParam (data:)           {String}                distribution_ratio              Phần trăm của <code>actual</code> so với <code>distribution</code> tương ứng của cả 3 hình thức học

@apiParam (data:)           {object}                onsite                          Loại hình khóa học là "Onsite"
@apiParam (data:)           {String}                onsite.target                   Tổng số lượng FSM là distribution "cần" được train của hình thức học "Onsite"
@apiParam (data:)           {String}                onsite.actual                   Tổng số lượng FSM là distribution "đã" được train của hình thức học "Onsite" 
@apiParam (data:)           {String}                onsite.target_ratio             Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Onsite"
@apiParam (data:)           {String}                onsite.distribution_ratio       Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Onsite"

@apiParam (data:)           {object}                classroom                       Loại hình khóa học là "Classroom"
@apiParam (data:)           {String}                classroom.target                Tổng số lượng FSM là distribution "cần" được train của hình thức học "Classroom" 
@apiParam (data:)           {String}                classroom.actual                Tổng số lượng FSM là distribution "đã" được train của hình thức học "Classroom"
@apiParam (data:)           {String}                classroom.target_ratio          Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Classroom"
@apiParam (data:)           {String}                classroom.distribution_ratio    Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Classroom"


@apiParam (data:)           {object}                online                          Loại hình khóa học là "Online"
@apiParam (data:)           {String}                online.target                   Tổng số lượng FSM là distribution "cần" được train của hình thức học "Online"
@apiParam (data:)           {String}                online.actual                   Tổng số lượng FSM là distribution "đã" được train của hình thức học "Online" 
@apiParam (data:)           {String}                online.target_ratio             Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Online"
@apiParam (data:)           {String}                online.distribution_ratio       Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Online"


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "region": "R1",
            "distribution": "1439",
            "actual": "1439",
            "distribution_ratio": "100",
            "onsite": {
                "target": "1400",
                "actual": "1400",
                "target_ratio": "100",
                "distribution_ratio": "97"
            },
            "class_room": {
                "target": "1070",
                "actual": "613",
                "target_ratio": "57",
                "distribution_ratio": "43"
            },
            "online": {
                "target": "1321",
                "actual": "1194",
                "target_ratio": "90",
                "distribution_ratio": "83"
            }
        }
    ]
}
"""


** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** * BẢNG THỐNG KÊ CHI TIẾT ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {POST} [HOST]/api/v1.0/covering/store_region_ss/actions/filter  Độ bao phủ theo FSM theo "region_SS"
@apiDescription Độ bao phủ theo FSM theo "region_ss"
@apiGroup BẢNG THỐNG KÊ CHI TIẾT
@apiVersion 1.0.0
@apiName fsm_region_ss

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (body:)           {string}                    course_id                       id khóa học muốn tìm kiếm 
@apiParam (body:)           {Array[string]}             [product_ids]                   Danh sách id những sản phẩm muốn tìm kiếm
@apiParam (body:)           {Array[string]}             [region_ss_ids]                 Danh sách id những khu vực theo định danh của Sam Sung 
@apiParam (body:)           {Array[string]}             [region_ids]                    Danh sách id những mã khu vực  
@apiParam (body:)           {Array[tring]}              [city_ids]                      Danh sách id thành phố muốn tìm kiếm
@apiParam (body:)           {datetime}                  time_start                      Ngày bắt đầu cho bộ tìm kiếm
@apiParam (body:)           {datetime}                  time_end                        Ngày kết thúc cho bộ tìm kiếm

@apiParam (data:)           {String}                    region_ss                       Khu vực theo định danh của Samsung (vd:HO CHI MINH)
@apiParam (data:)           {String}                    distribution                    Tổng số lượng FSM là distribution "cần" được train của cả 3 hình thức học
@apiParam (data:)           {String}                    actual                          Số lượng FSM là distribution "đã" được train của cả 3 hình thức học
@apiParam (data:)           {String}                    distribution_ratio              Phần trăm của <code>actual</code> so với <code>distribution</code> tương ứng của cả 3 hình thức học

@apiParam (data:)           {object}                    onsite                          Loại hình khóa học là "Onsite"
@apiParam (data:)           {String}                    onsite.target                   Tổng số lượng FSM là distribution "cần" được train của hình thức học "Onsite"
@apiParam (data:)           {String}                    onsite.actual                   Tổng số lượng FSM là distribution "đã" được train của hình thức học "Onsite" 
@apiParam (data:)           {String}                    onsite.target_ratio             Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Onsite"
@apiParam (data:)           {String}                    onsite.distribution_ratio       Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Onsite"

@apiParam (data:)           {object}                    classroom                       Loại hình khóa học là "Classroom"
@apiParam (data:)           {String}                    classroom.target                Tổng số lượng FSM là distribution "cần" được train của hình thức học "Classroom" 
@apiParam (data:)           {String}                    classroom.actual                Tổng số lượng FSM là distribution "đã" được train của hình thức học "Classroom"
@apiParam (data:)           {String}                    classroom.target_ratio          Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Classroom"
@apiParam (data:)           {String}                    classroom.distribution_ratio    Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Classroom"


@apiParam (data:)           {object}                    online                          Loại hình khóa học là "Online"
@apiParam (data:)           {String}                    online.target                   Tổng số lượng FSM là distribution "cần" được train của hình thức học "Online"
@apiParam (data:)           {String}                    online.actual                   Tổng số lượng FSM là distribution "đã" được train của hình thức học "Online" 
@apiParam (data:)           {String}                    online.target_ratio             Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Online"
@apiParam (data:)           {String}                    online.distribution_ratio       Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Online"


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "region_ss": "HO CHI MINH",
            "fms_distribution": "1439",
            "actual": "1439",
            "distribution_ratio": "100",
            "onsite": {
                "target": "1400",
                "actual": "1400",
                "target_ratio": "100",
                "distribution_ratio": "97"
            },
            "class_room": {
                "target": "1070",
                "actual": "613",
                "target_ratio": "57",
                "distribution_ratio": "43"
            },
            "online": {
                "target": "1321",
                "actual": "1194",
                "target_ratio": "90",
                "distribution_ratio": "83"
            }
        }
    ]
}
"""

** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** * BẢNG THỐNG KÊ CHI TIẾT ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {POST} [HOST]/api/v1.0/covering/store_shop_grade/actions/filter  Độ bao phủ theo FSM theo "shop_grade"
@apiDescription Độ bao phủ theo FSM theo "shop_grade"
@apiGroup BẢNG THỐNG KÊ CHI TIẾT
@apiVersion 1.0.0
@apiName fsm_shop_grade

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (body:)           {string}                        course_id                       id khóa học muốn tìm kiếm 
@apiParam (body:)           {Array[string]}                 [product_ids]                   Danh sách id những sản phẩm muốn tìm kiếm
@apiParam (body:)           {Array[string]}                 [region_ss_ids]                 Danh sách id những khu vực theo định danh của Sam Sung 
@apiParam (body:)           {Array[string]}                 [region_ids]                    Danh sách id những mã khu vực  
@apiParam (body:)           {Array[tring]}                  [city_ids]                      Danh sách id thành phố muốn tìm kiếm
@apiParam (body:)           {datetime}                      time_start                      Ngày bắt đầu cho bộ tìm kiếm
@apiParam (body:)           {datetime}                      time_end                        Ngày kết thúc cho bộ tìm kiếm

@apiParam (data:)           {String=A,B,C,D}                shop_grade                      Điểm đánh giá của từng cửa hàng loại A,B,C,D
@apiParam (data:)           {String}                        distribution                    Tổng số lượng FSM là distribution "cần" được train của cả 3 hình thức học
@apiParam (data:)           {String}                        actual                          Số lượng FSM là distribution "đã" được train của cả 3 hình thức học
@apiParam (data:)           {String}                        distribution_ratio              Phần trăm của <code>actual</code> so với <code>distribution</code> tương ứng của cả 3 hình thức học

@apiParam (data:)           {object}                        onsite                          Loại hình khóa học là "Onsite"
@apiParam (data:)           {String}                        onsite.target                   Tổng số lượng FSM là distribution "cần" được train của hình thức học "Onsite"
@apiParam (data:)           {String}                        onsite.actual                   Tổng số lượng FSM là distribution "đã" được train của hình thức học "Onsite" 
@apiParam (data:)           {String}                        onsite.target_ratio             Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Onsite"
@apiParam (data:)           {String}                        onsite.distribution_ratio       Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Onsite"

@apiParam (data:)           {object}                        classroom                       Loại hình khóa học là "Classroom"
@apiParam (data:)           {String}                        classroom.target                Tổng số lượng FSM là distribution "cần" được train của hình thức học "Classroom" 
@apiParam (data:)           {String}                        classroom.actual                Tổng số lượng FSM là distribution "đã" được train của hình thức học "Classroom"
@apiParam (data:)           {String}                        classroom.target_ratio          Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Classroom"
@apiParam (data:)           {String}                        classroom.distribution_ratio    Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Classroom"


@apiParam (data:)           {object}        online     Loại hình khóa học là "Online"
@apiParam (data:)           {String}        online.target  Tổng số lượng FSM là distribution "cần" được train của hình thức học "Online"
@apiParam (data:)           {String}        online.actual    Tổng số lượng FSM là distribution "đã" được train của hình thức học "Online" 
@apiParam (data:)           {String}        online.target_ratio   Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Online"
@apiParam (data:)           {String}        online.distribution_ratio  Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Online"

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "shop_grade": "A",
            "distribution": "1439",
            "actual": "1439",
            "distribution_ratio": "100",
            "onsite": {
                "target": "1400",
                "actual": "1400",
                "target_ratio": "100",
                "distribution_ratio": "97"
            },
            "class_room": {
                "target": "1070",
                "actual": "613",
                "target_ratio": "57",
                "distribution_ratio": "43"
            },
            "online": {
                "target": "1321",
                "actual": "1194",
                "target_ratio": "90",
                "distribution_ratio": "83"
            }
        }
    ]
}
"""
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** * BẢNG THỐNG KÊ CHI TIẾT ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** **
*version: 1.0.0 *
** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** ** *
"""
@api {POST} [HOST]/api/v1.0/covering/store_customer/actions/filter  Độ bao phủ theo FSM "store_customer"
@apiDescription Độ bao phủ theo FSM "customer"
@apiGroup BẢNG THỐNG KÊ CHI TIẾT
@apiVersion 1.0.0
@apiName fsm_customer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (body:)           {string}            course_id                       id khóa học muốn tìm kiếm 
@apiParam (body:)           {Array[string]}     [product_ids]                   Danh sách id những sản phẩm muốn tìm kiếm
@apiParam (body:)           {Array[string]}     [region_ss_ids]                 Danh sách id những khu vực theo định danh của Sam Sung 
@apiParam (body:)           {Array[string]}     [region_ids]                    Danh sách id những mã khu vực  
@apiParam (body:)           {Array[tring]}      [city_ids]                      Danh sách id thành phố muốn tìm kiếm
@apiParam (body:)           {datetime}          time_start                      Ngày bắt đầu cho bộ tìm kiếm
@apiParam (body:)           {datetime}          time_end                        Ngày kết thúc cho bộ tìm kiếm

@apiParam (data:)           {String}            customer                        Tên công ty/hệ thống chủ quản của cửa hàng (vd:TGDD)
@apiParam (data:)           {String}            distribution                    Tổng số lượng FSM là distribution "cần" được train của cả 3 hình thức học
@apiParam (data:)           {String}            actual                          Số lượng FSM là distribution "đã" được train của cả 3 hình thức học
@apiParam (data:)           {String}            distribution_ratio              Phần trăm của <code>actual</code> so với <code>distribution</code> tương ứng của cả 3 hình thức học

@apiParam (data:)           {object}            onsite                          Loại hình khóa học là "Onsite"
@apiParam (data:)           {String}            onsite.target                   Tổng số lượng FSM là distribution "cần" được train của hình thức học "Onsite"
@apiParam (data:)           {String}            onsite.actual                   Tổng số lượng FSM là distribution "đã" được train của hình thức học "Onsite" 
@apiParam (data:)           {String}            onsite.target_ratio             Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Onsite"
@apiParam (data:)           {String}            onsite.distribution_ratio       Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Onsite"

@apiParam (data:)           {object}            classroom                       Loại hình khóa học là "Classroom"
@apiParam (data:)           {String}            classroom.target                Tổng số lượng FSM là distribution "cần" được train của hình thức học "Classroom" 
@apiParam (data:)           {String}            classroom.actual                Tổng số lượng FSM là distribution "đã" được train của hình thức học "Classroom"
@apiParam (data:)           {String}            classroom.target_ratio          Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Classroom"
@apiParam (data:)           {String}            classroom.distribution_ratio    Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Classroom"


@apiParam (data:)           {object}            online                          Loại hình khóa học là "Online"
@apiParam (data:)           {String}            online.target                   Tổng số lượng FSM là distribution "cần" được train của hình thức học "Online"
@apiParam (data:)           {String}            online.actual                   Tổng số lượng FSM là distribution "đã" được train của hình thức học "Online" 
@apiParam (data:)           {String}            online.target_ratio             Phần trăm của <code>actual</code> so với <code>target</code> trong hình thức học "Online"
@apiParam (data:)           {String}            online.distribution_ratio       Phần trăm của <code>actual</code> so với <code>distrubution</code> trong hình thức học "Online"

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "customer": "TGDD",
            "distribution": "1439",
            "actual": "1439",
            "distribution_ratio": "100",
            "onsite": {
                "target": "1400",
                "actual": "1400",
                "target_ratio": "100",
                "distribution_ratio": "97"
            },
            "class_room": {
                "target": "1070",
                "actual": "613",
                "target_ratio": "57",
                "distribution_ratio": "43"
            },
            "online": {
                "target": "1321",
                "actual": "1194",
                "target_ratio": "90",
                "distribution_ratio": "83"
            }
        }
    ]
}
"""
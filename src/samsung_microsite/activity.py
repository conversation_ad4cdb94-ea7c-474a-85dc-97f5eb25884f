#!/usr/bin/python
# -*- coding: utf8 -*-
************************************* Danh sách các hoạt động của nhân viên ** **********************************
* version: 1.0.0 *
****************************************************************************************************************
"""
@api {get} [HOST]/api/v1.0/activities Danh sách các hoạt động của nhân viên
@apiDescription Danh sách các hoạt động của nhân viên
@apiGroup Activity
@apiVersion 1.0.0
@apiName   ListActivity
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query:)   {Int}   count_notify=25  Số thông báo muốn hiển thị

@apiSuccess (data) {String}         merchant_id                  Định danh merchant
@apiSuccess (data) {String}         description                  Mô tả
@apiSuccess (data) {Int=0,1}            status                       Trạng thái của hoạt động ( 1 - Đã xem, 0 - Chưa xem )
@apiSuccess (data) {Datetime}       time                         Thời gian thực hiện hoạt động


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "data": [
                {
                    "merchant_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044",
                    "description": "Đã thêm khóa học Samsung S21",
                    "status": 1,
                    "time": "05/04/2021 11:00:00"
                },
                {
                    "merchant_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180044",
                    "description": "Đã chỉnh sửa khóa học Samsung S21",
                    "status": 0,
                    "time": "05/04/2021 12:00:00"
                }
        ]

}
"""

#!/usr/bin/python
# -*- coding: utf8 -*-

****************************** Criterial Key **********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@apiDefine critetia_key_body
@apiVersion 1.0.0
@apiParam   (profile_filter:) {String}    criteria_key    Các criteria key để filter khách hàng. Allowed values:<br/>
<li><code>cri_address</code>: Address
<li><code>cri_age</code>: Age
<li><code>cri_birthday</code>: Birthday
<li><code>cri_birthday_period</code>: <PERSON>h nhật theo chu kỳ.
<li><code>cri_business_case</code>: Business case
<li><code>cri_card_level</code>: Loại thẻ
<li><code>cri_card_status</code>: Customer card status
<li><code>cri_city</code>: City
<li><code>cri_created_account_type</code>: Nguồn ghi nhận khách hàng
<li><code>cri_gender</code>: gender
<li><code>cri_hobby</code>: Hobby
<li><code>cri_job</code>: Job
<li><code>cri_marital_status</code>: Tình trạng hôn nhân
<li><code>cri_mkt_action_value</code>: Marketing action value
<li><code>cri_mkt_business_case_id</code>: MKT Business ID
<li><code>cri_mkt_campaign_id</code>: MKT Campaign ID
<li><code>cri_mkt_process_type</code>: Process type
<li><code>cri_mkt_root_process</code>: MKT root_process_id
<li><code>cri_mkt_step</code>: Loại khách hàng marketing
<li><code>cri_operation</code>: Lĩnh vực hoạt động
<li><code>cri_region</code>: Region
<li><code>cri_tags</code>: Lọc theo tags
"""

****************************** Operator Key ***********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@apiDefine operator_key_body
@apiVersion 1.0.0
@apiParam   (profile_filter:) {String="op_is_between","op_is_greater_equal","op_is_in","op_is_equal","op_is_greater","op_is_has","op_is_has_not","op_is_less_equal","op_is_less","op_is_regex"}    operator_key    Các toán tử để filter khách hàng.
"""

***************************************** BIDV Create Profile ***********************************
* version: 1.0.0                                                                                *
*************************************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/profile/actions/create Create Profile
@apiDescription API Tạo khách hàng.
@apiGroup Profile
@apiVersion 1.0.0
@apiName CreateProfile
@apiIgnore not Support

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)        {Object}    profile                            Thông tin khách hàng
@apiParam   (Body:)        {Object}    [cards]                            Thông tin thẻ khách hàng.
@apiParam   (profile:)     {Array}    [relationship_data]                       ID của cha/mẹ Profile (Khi muốn add thêm Child vào Profile).
@apiParam   (profile:)     {String}    [customer_id]                            Mã khách hàng trên hệ thống Partner.
@apiParam   (profile:)     {String}    [customer_created_time]                            Ngày tạo mã khách hàng trên hệ thống Partner. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {String}    [partner_point]                            Số điểm tích lũy trên hệ thống Partner.
@apiParam   (profile:)     {Number}    [degree]                            Trình độ.
@apiParam   (profile:)     {Number}    [income_family]                            Tổng thu nhập cả gia đình.
@apiParam   (profile:)     {Number}    [relation_with_childs]                            Mỗi quan hệ giữa profile với các con.
<br/><br/>Allowed values:<br/>
<li><code>1: Mẹ</code></li>
<li><code>2: Bố</code></li>
@apiParam   (profile:)     {Array}    [childs]                            Danh sách con.
@apiParam   (profile:)     {String}    name                            Tên khách hàng
@apiParam   (profile:)     {String}    [phone_number_1]                          Số điện thoại chính của khách hàng.
@apiParam   (profile:)     {String}    [email_1]                                   Email chính của khách hàng.
@apiParam   (profile:)     {List}    [phone_number_2]                        Số điện thoại phụ của khách hàng.
@apiParam   (profile:)     {List}    [email_2]                               Email phụ của khách hàng.
@apiParam   (profile:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (profile:)     {Array}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (profile:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (profile:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (profile:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (profile:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (profile:)     {String}    [tags]                        Nhãn gợi ý tìm kiếm. Format: "tags1;tags2"
@apiParam   (profile:)     {String}    [company]                     Công ty.
@apiParam   (profile:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (profile:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (profile:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (profile:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (profile:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (profile:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (profile:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.
@apiParam   (profile:)     {Object}    [social_user]                             Thông tin người dùng trên mạng xã hội

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.
@apiParam   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiParam   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube

@apiParam   (childs:)     {String}  name                                  Tên con.
@apiParam   (childs:)     {String}    [birthday]                              Ngày sinh của con. Format <code>YYYY-mm-DD</code>.
@apiParam   (childs:)     {Number}  [gender]                                  Giới tính của con.
@apiParam   (childs:)     {Number}  [nth]                                  Con thứ mấy trong gia đình.

@apiParam   (relationship_data:)     {Number}  relationship_type                                  Mối quan hệ với profile đang được khởi tạo.
<br/><br/>Allowed values:<br/>
<li><code>1: Mẹ</code></li>
<li><code>2: Bố</code></li>
@apiParam   (relationship_data:)     {Number}  profile_id                                  Id của Profile có mỗi quan hệ với profile đang được khởi tạo.

@apiSuccess {Object}    profile_info     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    cards        Dữ liệu thẻ khách hàng
@apiSuccess {Object}    process_type        Kiểu thực thi Profile.
<br/>
<li><code>add: Add Profile/ Tạo Profile</code></li>
<li><code>update: Update Profile / Cập nhật Profile</code></li>
<li><code>merge: Merge Profile / Hợp nhất Profile</code></li>

@apiSuccess (profile_info:)     {String}     id                               ID khách hàng
@apiSuccess   (profile_info:)     {String}      name                            Tên khách hàng
@apiSuccess   (profile_info:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (profile_info:)     {Object}     primary_email                     Email chính của khách hàng.
@apiSuccess   (profile_info:)     {Object}     secondary_emails                     Các email phụ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (profile_info:)     {Number}    gender                            Giới tính
@apiSuccess   (profile_info:)     {Array}     address                           Địa chỉ
@apiSuccess   (profile_info:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (profile_info:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (profile_info:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (profile_info:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (profile_info:)     {String}    birthday                          Ngày sinh
@apiSuccess   (profile_info:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (profile_info:)     {Number}    income                            Mức thu nhập
@apiSuccess   (profile_info:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess   (profile_info:)     {Object}    social_user                             Thông tin người dùng trên mạng xã hội

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccess   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiSuccess   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube 

@apiParamExample [json] Body example:
{
  "profile": {
    "created_account_type": 2,
    "phone_number": ["**********"],
    "email": ["<EMAIL>"],
    "social_user": {"social_id": "*********", "social_type": 1, "social_name": "andrew"}
  }
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "address": [], 
      "answers": [], 
      "association": [], 
      "avatar": null, 
      "avg_monthly_consumption": null, 
      "avg_rate": null, 
      "birthday": null, 
      "budget_high_threshold": null, 
      "budget_low_threshold": null, 
      "company": null, 
      "created_account_type": 0, 
      "created_time": "2019-03-27T17:52:46Z", 
      "description": null,
      "relationship_data": [{"profile_id": "71199f63-1870-41fa-995d-b8534cd182f6", "relationship_type":1}]
      "district_code": null, 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "face_id": [], 
      "frequently_demands": [], 
      "gender": 2, 
      "hobby": [], 
      "income_high_threshold": null, 
      "income_low_threshold": null, 
      "income_type": null, 
      "is_non_profile": false, 
      "job": null, 
      "last_time_active": null, 
      "last_time_transaction": null, 
      "lat": null, 
      "location": null, 
      "lon": null, 
      "marital_status": null, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "Giang", 
      "nation": null, 
      "number_interactive": null, 
      "number_transaction": null, 
      "operation": null, 
      "people_id": null, 
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "position": null, 
      "predict": [], 
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      }, 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "province_code": null, 
      "religiousness": null, 
      "salary": null, 
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "social_user": [], 
      "source_id": null, 
      "source_type": null, 
      "state": null, 
      "tags": [], 
      "total_rate": null, 
      "updated_time": "2019-03-27T17:52:46Z", 
      "ward_code": null, 
      "website": null, 
      "workplace": null
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

***************************************** BIDV Update Profile ***********************************
* version: 1.0.0                                                                                *
*************************************************************************************************
"""
@api {put} [HOST]/profiling/v2.0/profile/actions/update/<profile_id> Update Profile
@apiDescription API cập nhật khách hàng.
@apiGroup Profile
@apiVersion 1.0.0
@apiName UpdateProfile
@apiIgnore not Support

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    profile_id                             ID của profile.

@apiParam   (Body:)     {Object}    profile                            Thông tin khách hàng
@apiParam   (Body:)     {Object}    [cards]                               Thông tin thẻ khách hàng.

@apiParam   (profile:)     {String}    [customer_id]                            Mã khách hàng trên hệ thống Partner.
@apiParam   (profile:)     {String}    [customer_created_time]                            Ngày tạo mã khách hàng trên hệ thống Partner. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {String}    [partner_point]                            Số điểm tích lũy trên hệ thống Partner.
@apiParam   (profile:)     {Number}    [degree]                            Trình độ.
@apiParam   (profile:)     {Number}    [income_family]                            Tổng thu nhập cả gia đình.
@apiParam   (profile:)     {String}    name                            Tên khách hàng
@apiParam   (profile:)     {String}    [phone_number_1]                          Số điện thoại chính của khách hàng.
@apiParam   (profile:)     {String}    [email_1]                                   Email chính của khách hàng.
@apiParam   (profile:)     {List}    [phone_number_2]                        Số điện thoại phụ của khách hàng.
@apiParam   (profile:)     {List}    [email_2]                               Email phụ của khách hàng.
@apiParam   (profile:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (profile:)     {Array}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (profile:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (profile:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (profile:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (profile:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (profile:)     {String}    [tags]                        Nhãn gợi ý tìm kiếm. Format: "tags1;tags2"
@apiParam   (profile:)     {String}    [company]                        Công ty.

@apiParam   (profile:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (profile:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (profile:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (profile:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (profile:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (profile:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (profile:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.
@apiParam   (profile:)     {Object}    [social_user]                             Thông tin người dùng trên mạng xã hội

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.
@apiParam   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiParam   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube


@apiSuccess {Object}    profile_info     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    cards        Dữ liệu thẻ khách hàng
@apiSuccess {Object}    process_type        Kiểu thực thi Profile.
<br/>
<li><code>add: Add Profile/ Tạo Profile</code></li>
<li><code>update: Update Profile / Cập nhật Profile</code></li>
<li><code>merge: Merge Profile / Hợp nhất Profile</code></li>

@apiSuccess (profile_info:)     {String}     id                               ID khách hàng
@apiSuccess   (profile_info:)     {String}      name                            Tên khách hàng
@apiSuccess   (profile_info:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (profile_info:)     {Object}     primary_email                     Email chính của khách hàng.
@apiSuccess   (profile_info:)     {Object}     secondary_emails                     Các email phụ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (profile_info:)     {Number}    gender                            Giới tính
@apiSuccess   (profile_info:)     {Array}     address                           Địa chỉ
@apiSuccess   (profile_info:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (profile_info:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (profile_info:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (profile_info:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (profile_info:)     {String}    birthday                          Ngày sinh
@apiSuccess   (profile_info:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (profile_info:)     {Number}    income                            Mức thu nhập
@apiSuccess   (profile_info:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess   (profile_info:)     {Object}    social_user                             Thông tin người dùng trên mạng xã hội

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccess   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiSuccess   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube 

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "address": [], 
      "answers": [], 
      "association": [], 
      "avatar": null, 
      "relationship_data": [{"profile_id": "71199f63-1870-41fa-995d-b8534cd182f6", "relationship_type":1}]
      "avg_monthly_consumption": null, 
      "avg_rate": null, 
      "birthday": null, 
      "budget_high_threshold": null, 
      "budget_low_threshold": null, 
      "company": null, 
      "created_account_type": 0, 
      "created_time": "2019-03-27T17:52:46Z", 
      "description": null, 
      "district_code": null, 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "face_id": [], 
      "frequently_demands": [], 
      "gender": 2, 
      "hobby": [], 
      "income_high_threshold": null, 
      "income_low_threshold": null, 
      "income_type": null, 
      "is_non_profile": false, 
      "job": null, 
      "last_time_active": null, 
      "last_time_transaction": null, 
      "lat": null, 
      "location": null, 
      "lon": null, 
      "marital_status": null, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "Giang", 
      "nation": null, 
      "number_interactive": null, 
      "number_transaction": null, 
      "operation": null, 
      "people_id": null, 
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "position": null, 
      "predict": [], 
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      }, 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "province_code": null, 
      "religiousness": null, 
      "salary": null, 
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "social_user": [], 
      "source_id": null, 
      "source_type": null, 
      "state": null, 
      "tags": [], 
      "total_rate": null, 
      "updated_time": "2019-03-27T17:52:46Z", 
      "ward_code": null, 
      "website": null, 
      "workplace": null
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

******************************** Social Upsert Profile ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/profile/actions/social/upsert Social Upsert Profile.
@apiDescription Social Upsert Profile
@apiGroup Profile
@apiVersion 1.0.0
@apiName SocialUpsertProfile
@apiIgnore not Support

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.

@apiParam   (Body:)    {Object}         social_user        Thông tin xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)     {Number}    created_account_type                    Nguồn tạo tài khoản.
<li><code>2: FACEBOOK</code></li>
<li><code>6: ZALO</code></li>
<li><code>7: INSTAGRAM</code></li>
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (Body:)    {String}          [avatar]             Ảnh đại diện.
@apiParam   (Body:)    {Array}          [email]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {Array}          [phone_number]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {String}          [address]             Địa chỉ.
@apiParam   (Body:)    {String}          [birthday]             Ngày sinh.

@apiParam   (Social_User:)    {String}         social_id        Id của mạng mạng xã hội.
@apiParam   (Social_User:)    {String}          social_name             Tên Social của Profile.
@apiParam   (Social_User:)    {Int}            social_type      Mạng xã hội.
<li><code>1: FACEBOOK</code></li>
<li><code>2: ZALO</code></li>
<li><code>3: INSTAGRAM</code></li>
<li><code>4: YOUTUBE</code></li>
@apiParamExample {json} SocialUser-Example:
{"social_id": "*********", "social_type": 1, "social_name": "andrew"}

@apiParamExample [json] Body example:
{
  "created_account_type": 2,
  "gender": 2,
  "social_user": {"social_id": "*********", "social_type": 1, "social_name": "andrew"},
  "avatar: "https://fb.com/images/avatar.jpg"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "avatar": null, 
      "birthday": null, 
      "created_account_type": 2, 
      "created_time": "2019-03-27T17:52:46Z", 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "gender": 2, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "andrew",
      "social_name": [{"id":1,"name": "andrew"}],
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "social_user": [{"social_id": "*********", "social_type": 1}], 
      "updated_time": "2019-03-27T17:52:46Z", 
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

******************************** Social Update Profile ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/v2.0/profile/actions/social/update/<profile_id> Social Update Profile.
@apiDescription Social Update Profile
@apiGroup Profile
@apiVersion 1.0.0
@apiIgnore not Support

@apiName SocialUpdateProfile

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.

@apiParam   (Body:)    {Array}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (Body:)    {Array}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (Body:)    {Object}         [social_user]        Thông tin xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (Body:)    {String}          [avatar]             Ảnh đại diện.
@apiParam   (Body:)    {Array}          [email]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {Array}          [phone_number]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {String}          [address]             Địa chỉ.
@apiParam   (Body:)    {String}          [birthday]             Ngày sinh.

@apiParamExample {json} SocialUser-Example:
{"social_id": "*********", "social_type": 1, "social_name": "andrew"}

@apiParamExample [json] Body example:
{
  "gender": 2,
  "phone_number": ["**********"],
  "email": ["<EMAIL>"],
  "social_user": {"social_id": "*********", "social_type": 1, "social_name": "andrew"},
  "avatar: "https://fb.com/images/avatar.jpg"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Cập nhật khách hàng thành công.", 
    "process_type": "update", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "avatar": null, 
      "birthday": null, 
      "created_account_type": 2, 
      "created_time": "2019-03-27T17:52:46Z", 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "gender": 2, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "andrew", 
      "social_name": [{"id":1,"name": "andrew"}],
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "social_user": [{"social_id": "*********", "social_type": 1}], 
      "updated_time": "2019-03-27T17:52:46Z", 
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

********************** GET USER INFO BY FIELD AND FILTER ************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/systems/mkt_strategy/users Lấy danh sách khách hàng.
@apiDescription API lấy danh sách khách hàng theo field và bộ lọc
@apiGroup Customers
@apiVersion 1.0.0
@apiName MKTGetUsers
@apiIgnore not Support

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiUse paging_tokens

@apiParam (Query:) {String} merchant_id                                 ID merchant
@apiParam (Query:) {String} [business_case_id]                          ID business case

@apiParam      (Body:)     {String}    search                           Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]                 Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Body:)     {Array}     fields                           Danh sách các field cần lấy. Gồm:<br/>
Allowed values: <br/>
<li><code>phone_number</code>
<li><code>email</code>
<li><code>name</code>
<li><code>social_user</code>
<li><code>gender</code>
<li><code>address</code>
<li><code>source_type</code>
<li><code>source_id</code>
<li><code>birthday</code>



@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                  Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "fields": ["name", "phone_number", "email", "social_user", "gender", "address", "source_type", "source_id", "birthday"],
  "search": "search_str",
  "profile_filter": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách thông tin user
@apiSuccess       {String}                                 signature         Chữ ký theo dữ liệu trả về

@apiSuccess  (data)      {String}        merchant_id                   Id của merchant
@apiSuccess  (data)      {String}        profile_id                    Id của user
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        source_type                  Loại nguồn import user. <code>1: From Audience</code>
@apiSuccess  (data)       {String}        source_id                    ID nguồn ghi nhận user. <code>source_type = 1: Audience id</code>
@apiSuccess  (data)      {Array}         social_user                   Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:GooglePlus</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Zalo</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "birthday": "1981-07-28",
      "created_time": "2018-07-27T09:53:21Z",
      "name": "Vũ Thị thọ 123",
      "email": "['<EMAIL>', '<EMAIL>']",
      "gender": 3,
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "phone_number": ["+841215150001"],
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "updated_time": "2018-07-28T04:57:35Z",
      "source_type": 1,
      "source_id": "435ecfeb-e229-4076-806f-982580475e88"
    },
    ...
  ],
  "signature": "data signature",
}

"""
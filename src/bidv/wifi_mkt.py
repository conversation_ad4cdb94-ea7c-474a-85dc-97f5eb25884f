********************************* Import Profile  *********************************
* version: 1.0                                                                    *
***********************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/imports/wifi-data Import data
@apiDescription Dịch vụ import dữ liệu profile từ wifi marketing vào hệ thống.
@apiGroup Wifi marketing
@apiVersion 1.0.0
@apiName ImportWifiData

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {String}  business  Chuỗi query nhận diện business. Example: Mỗi chi nh<PERSON>h là một mã khác nhau hỗ trợ việc segmentation profiles.
@apiParam   (Body:)   {String}  source  Nguồn tạo profile.
@apiParam   (Body:)   {Array}   profiles  Danh sách thông tin các profile từ wifi marketing.

@apiParam   (profile:)  {String}  identify_id   Định danh của profile trên hệ thống wifi. Nếu không có thông tin định danh của profile(số điện thoại, email) có thể sử dụng địa chỉ MAC để làm định danh tạm thời.
@apiParam   (profile:)  {String}  [name]   Họ và tên.
@apiParam   (profile:)  {String}  [phone_number_1]   Số điện thoại chính.
@apiParam   (profile:)  {String}  [primary_email]   Địa chỉ email chính.
@apiParam   (profile:)  {StringArray}  [phone_number_2]   Danh sách số điện thoại phụ.
@apiParam   (profile:)  {StringArray}  [secondary_emails]   Danh sách email phụ.
@apiParam   (profile:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (profile:)     {Array}    [address]                               Danh sách địa chỉ cụ thể của khách hàng.
@apiParam   (profile:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (profile:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (profile:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (profile:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (profile:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {String}    [tags]                        Nhãn gợi ý tìm kiếm. Format: "tags1;tags2"
@apiParam   (profile:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (profile:)     {Number}    [operation]                             Lĩnh vực kinh doanh

@apiParamExample  {json}  Body example
{
  "business": "WIFI_CNCAUGIAY",
  "source": "Wifi"
  "profiles": [
    {
      "identify_id": "6814018bd9c1",
      "name": "Nguyễn Văn A",
      "phone_number_1": "0904123456",
      "primary_email": "<EMAIL>",
      "phone_number_2": ["0904123457", "0904123458"],
      "secondary_emails": ["<EMAIL>", "<EMAIL>"],
      "gender": 1,
      "profile_address": [""],
      "province_code": 1,
      "district_code": 1,
      "ward_code": 1,
      "marital_status": 1,
      "birthday": "",
      "tags": "tag1;tag2",
      "mac": "6814018bd9c1",
      "job": 1,
      "operation": 1
    },
    {
      "identify_id": "acd7a8ad6a7b",
      "name": "Nguyễn Văn B",
      "phone_number_1": "0904654321",
      "primary_email": "<EMAIL>",
      "phone_number_2": ["0904754321", "0904854321"],
      "secondary_emails": ["<EMAIL>", "<EMAIL>"],
      "gender": 1,
      "profile_address": [""],
      "province_code": 1,
      "district_code": 1,
      "ward_code": 1,
      "marital_status": 1,
      "birthday": "",
      "tags": "tag1;tag2",
      "mac": "acd7a8ad6a7b",
      "job": 1,
      "operation": 1
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
}
"""
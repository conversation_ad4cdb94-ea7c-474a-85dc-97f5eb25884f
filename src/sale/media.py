#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# API Media Deal detail
"""

# ---------- <PERSON><PERSON> sách File upload trong Deal -----------
"""
@api {GET} {domain}/sale/api/v1.0/deals/<deal_id>/media               Lấy danh sách các file media của Deal 
@apiGroup Media
@apiVersion 1.0.0
@apiName ListMediaDetailDeal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (QUERY:) {String=identification,other,all}  type_media      Loại media
                                                                        <ul>
                                                                            <li><code>identification:</code>Thông tin định danh</li>
                                                                            <li><code>other:</code>Khác</li>
                                                                            <li><code>all:</code><PERSON><PERSON><PERSON> t<PERSON>t c<PERSON></li>
                                                                        </ul>
                                                                    
                                                                    

@apiSuccess {String}            message                       <PERSON><PERSON> tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách tương ứng các file.

@apiSuccess {String}            data.id                       <code>ID</code> của file upload lên hệ thống Sale
@apiSuccess {Array}            data.type_media               Loại Media
@apiSuccess {String}            data.title                    Tên file upload
@apiSuccess {String}            data.format_file              Định dạng của file
@apiSuccess {String}            data.url                      URL file


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "id": "9a9f73fc-c24d-4be7-a1dd-019ace63b283",
            "type_media": ["identification"],
            "title": "cmnd.png",
            "format_file": "image/png",
            "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/cmnd.png"
        },
        {
            "id": "9a9f73fc-c24d-4be7-a1dd-019ace63b298",
            "type_media": ["other"],
            "title": "video.mp4",
            "format_file": "video/mp4",
            "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/video.mp4"
        },
    ]
}
"""

# ---------- Thêm Media vào Deal -----------
"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/media               Thêm media vào deal
@apiGroup Media
@apiVersion 1.0.0
@apiName AddMediaDetailDeal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(BODY:)			{Array}	    type_media		    Danh sách các loại Media cần upload.
                                                                <ul>
                                                                    <li><code>identification</code>: Thông tin định danh</li>
                                                                    <li><code>other</code>: Khác </li>
                                                                </ul>   
@apiParam	(BODY:)			{Array}	     list_info_upload	Danh sách thông tin file upload

@apiParam	(BODY:)			{String}	 list_info_upload.url		       URL upload
@apiParam	(BODY:)			{String}	 list_info_upload.dist_path		   Thư mục lưu trữ file
@apiParam	(BODY:)			{String}	 list_info_upload.filename		   Tên file upload
@apiParam	(BODY:)			{String}	 list_info_upload.mimetype		   Định dạng file upload
                                                                

@apiParamExample {json} Body example
{
    "type_media": ["other"],
    "list_info_upload": [
        {
            "url": "https://t1.mobio.vn/static/abcxyz/image.png",
            "dist_path": "/Usr/local/bin/image.png",
            "filename": "iamge.png",
            "mimetype": "image/png"
        },
        {
            "url": "https://t1.mobio.vn/static/abcxyz/image.png",
            "dist_path": "/Usr/local/bin/image.png",
            "filename": "iamge.png",
            "mimetype": "image/png"
        }
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}            data                          Thông tin file Upload

@apiSuccess {String}            data.id                       <code>ID</code> của file upload lên hệ thống Sale
@apiSuccess {Array}            data.type_media               Loại Media
@apiSuccess {String}            data.title                    Tên file upload
@apiSuccess {String}            data.format_file              Định dạng của file
@apiSuccess {String}            data.url                      URL file

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": [{
        "id": "9a9f73fc-c24d-4be7-a1dd-019ace63b283",
        "type_media": ["identification", "other"],
        "title": "cmnd.png",
        "format_file": "image/png",
        "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/cmnd.png"
    }],
    "message": "request thành công."
}
"""

# ---------- Sửa file Media trong Deal -----------
"""
@api {PUT} {domain}/sale/api/v1.0/deals/<deal_id>/media/<media_id>           Sửa file media trong deal
@apiGroup Media
@apiVersion 1.0.0
@apiName UpdateMediaDetailDeal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(Form:)			{File}	    file		        File update
@apiParam	(Form:)			{String}	    type_media		    Danh sách các loại Media cần upload. Ngăn cách nhau bởi dấu phẩy
                                                                <ul>
                                                                    <li><code>identification</code>: Thông tin định danh</li>
                                                                    <li><code>other</code>: Khác </li>
                                                                </ul>   

@apiParamExample {json} Form example
file: (binary)
type_media: "identification,other"

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thông tin file Upload

@apiSuccess {Array}            data.type_media               Loại Media
@apiSuccess {String}            data.title                    Tên file upload
@apiSuccess {String}            data.format_file              Định dạng của file
@apiSuccess {String}            data.url                      URL file

@apiSuccessExample {json} Response 
{
    "code": 200,
    "data": {
        "id": "9a9f73fc-c24d-4be7-a1dd-019ace63b283",
        "type_media": ["identification", "other"],
        "title": "cmnd.png",
        "format_file": "image/png",
        "url": "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/cmnd.png"
    },
    "message": "request thành công."
}
"""

# ---------- Gỡ file Media trong Deal -----------
"""
@api {DELETE} {domain}/sale/api/v1.0/deals/<deal_id>/media           Gỡ file media trong deal
@apiGroup Media
@apiVersion 1.0.0
@apiName RemoveMediaDetailDeal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(Param:)			{String}     media_ids             Danh sách các file cần gỡ. Cách nhau bởi dấu phẩy
   
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}
"""
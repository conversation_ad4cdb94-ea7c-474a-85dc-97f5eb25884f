#!/usr/bin/python
# -*- coding: utf8 -*-

"""
@api {GET} {domain}/sale/api/v1.0/team-process    <PERSON><PERSON> sách quy trình bán hàng theo team
@apiGroup Team
@apiVersion 1.0.0
@apiName TeamProcessGet
@apiDescription API lấy danh sách quy trình bán hàng bởi team_id

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam (Query:) {String}     team_id    team id, nhiều id cách nhau dấu ,

@apiSuccess {Object} team_id                  quy trình id 

@apiSuccess {String} message    Response message

@apiSuccessExample Response success:
{
    "code": 200,
    "data": [
        {
            "team_id": "1a330b28-e99d-47b2-8596-9f5c6979a064",
            "process_id": [
                "5fcb0fb7f465bcf2dd8e4999",
                "60ec1eab5e39d30027b30dd3",
                "60fe8f5a3601f65b0a3da235",
                "6103638cae161c13049fbafe",
                "6119e853e363eee927d77f5e",
                "6141c04c2c39d967753a2e13"
            ],
        },
        {
            "team_id": "2f831383-9698-4088-85f5-c7365b8ed363",
            "process_id": [
                "5f448d7e4ebe38989bb65a40",
                "615e783cd27f1f0bdbd3c0e2"
            ],
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {POST} {domain}/sale/api/v1.0/team-process    Gán quy trình bán hàng cho Team
@apiGroup Team
@apiVersion 1.0.0
@apiName TeamProcessCreate
@apiDescription  Gán quy trình bán hàng cho Team, Nếu chưa có cấu hình quy trình bán hàng thì thêm mới

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Body:) {String}  team_id             Id Team
@apiParam (Body:) {Array}   process_id    Danh sách id quy trình bán hàng cập nhật cho team_id

@apiParamExample {json} Body example
{
    "team_id": "ba44f15a-bb54-44cb-9613-e4a4c877b531",
    "process_id": [
        "d04df7b9-e7a2-4a08-b6ef-7b7e4fe60793", 
        "f5f179e7-2407-4586-8af3-88a6f55cd7a7"
    ]
}

@apiSuccessExample Response success:
{
    "code": 200,
    "message": "request successfully",
}
"""

"""
@api {POST} {domain}/sale/api/v1.0/team-process/list-sale-process/<team_id>    Lấy ra danh sách quy trình team được tham gia
@apiGroup Team
@apiVersion 1.0.0
@apiName TeamProcessListSaleProcess
@apiDescription  Lấy ra danh sách quy trình team được tham gia

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccessExample Response success:
{
    "code": 200,
    "data": [
        {
            "_id": "626b509f42b88aac29bbb61a",
            "code": "HDU18Z",
            "id": "626b509f42b88aac29bbb61a",
            "name": "Vay Tín dụng KHDN",
            "name_ascii": "Vay Tin dung KHDN"
        },
        {
            "_id": "626b50f08d71307ed34455e4",
            "code": "1KW5TG",
            "id": "626b50f08d71307ed34455e4",
            "name": "KHCN - Mở tài khoản tiết kiệm",
            "name_ascii": "KHCN - Mo tai khoan tiet kiem"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
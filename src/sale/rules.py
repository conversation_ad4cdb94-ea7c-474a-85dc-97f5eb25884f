#!/usr/bin/python
# -*- coding: utf8 -*-

"""
    # C<PERSON>u hình luật phân công
"""

"""
@api {GET} {domain}/sale/api/v1.0/rules              Danh sách luật phân công thu hồi
@apiGroup SaleRules
@apiVersion 1.0.0
@apiName ListRuleConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query:) {String}     [type]     Loại luật
                                                <ul>
                                                    <li><code>assignment</code>: <PERSON>ân công</li>
                                                    <li><code>revoke</code>: <PERSON>hu hồi</li>
                                                </ul>   
@apiParam (Query:) {String}     [apply_type]    Loại áp dụng  
                                                <ul>
                                                    <li><code>team</code>: Luật áp dụng theo team</li>
                                                    <li><code>internal_team</code>: <PERSON><PERSON> dụng theo nội bộ team</li>
                                                </ul>   
                                                
@apiSuccess {Number} code       Response status
@apiSuccess {String} message    Response message
@apiSuccess {Array}  data       Danh sách các luật
@apiSuccess {String}  data.type             Loại luật
                                                <ul>
                                                    <li><code>assignment</code>: Phân công</li>
                                                    <li><code>revoke</code>: Thu hồi</li>
                                                </ul>  
@apiSuccess {String}  data.apply_type       Loại áp dụng  
                                            <ul>
                                                <li><code>team</code>: Luật áp dụng theo team</li>
                                                <li><code>internal_team</code>: Áp dụng theo nội bộ team</li>
                                            </ul>    
@apiSuccess {String}  data.code             Mã luật
@apiSuccess {String}  data.description      Mô tả
@apiSuccess {String}  data.id               ID luật
@apiSuccess {String}  data.priority         Độ ưu tiên

@apiSuccessExample Response success:
{
    "code": 200,
    "data": [
        {
            "apply_type": "internal_team",
            "code": "TEST",
            "description": "hhhh",
            "id": "6109147e1b5baa49a2fbadfc",
            "priority": 1,
            "type": "assignment",
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# --------- Danh sách Team đã được config luật phân công, thu hồi ------

"""
@api {GET} {domain}/sale/api/v1.0/rules/team-exists-config  Danh sách team có config luật
@apiGroup SaleRules
@apiVersion 1.0.0
@apiName RuleTeamExistsConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query:) {String}     [type]          Loại luật
                                                <ul>
                                                    <li><code>assignment</code>: Phân công</li>
                                                    <li><code>revoke</code>: Thu hồi</li>
                                                </ul>   

@apiSuccess {Number} code       Response status
@apiSuccess {String} message    Response message
@apiSuccess {Array}  data       Danh sách các luật
@apiSuccess {String}  data.team_id          ID Team  
@apiSuccess {String}  data.team_name        Tên team

@apiSuccessExample Response success:
{
    "code": 200,
    "data": [
        {
            "team_id": "5148aca1-bb2b-411a-a207-4b172ff25abc",
            "team_name": "Team 1"
        },
        {
            "team_id": "df1d6319-2721-4d5e-afaf-d7ff050cb498",
            "team_name": "Team 2"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} {domain}/sale/api/v1.0/block-rule    Danh sách khối luật 
@apiGroup SaleRules
@apiVersion 1.0.0
@apiName BlockRuleGet  
@apiDescription Danh sách khối luật phân công 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiSuccess {String} message    Response message

@apiSuccessExample Response success:
{
    "code": 200,
    "data": {
        "condition": [
            "CONDITION_DEAL",
            "CONDITION_PROFILE",
            "CONDITION_PRODUCT"
        ],
        "staff": [
            "STAFF_RANDOM",
            "STAFF_LEAST_DEAL",
            "STAFF_PRODUCT",
            "STAFF_SKILL_BASE",
            "STAFF_OWNER",
            "STAFF_LAST_OWNER",
            "STAFF_KPI",
            "STAFF_AREA"
        ],
        "team": [
            "TEAM_CHOOSE",
            "TEAM_AREA",
            "TEAM_LAST",
            "TEAM_TAG"
        ]
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

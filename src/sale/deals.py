#!/usr/bin/python
# -*- coding: utf8 -*-
"""
Author: ThongNV
Company: MobioVN
Created Date: 25/11/2019
"""
################### DEFINE ##########################

"""
@apiDefine sale_filters
@apiParam	(sale_filters:)  {Array}	    values			  Danh sách các giá trị cần filter.
                                                              Chi tiết bộ lọc xem (https://mobiojsc.sg.larksuite.com/wiki/BIx0wIr6nimCOYktG1klsKL0gwe)
                                                     
@apiParam	(sale_filters:)  {String}	criteria_key		  Các key để filter đơn hàng.
                                                              Alow values:
                                                              <ul>
                                                                <li><code>cri_sale_value </code>: Giá trị đơn hàng</li>
                                                                <li><code>cri_id </code>: ID đơn hàng</li>
                                                                <li><code>cri_type_create </code>: <PERSON><PERSON><PERSON> t<PERSON>o đơn hàng</li>
                                                                <li><code>cri_reason_success </code>:Lý do chốt đơn hàng thành công </li>
                                                                <li><code>cri_reason_fail </code>: Lý do chốt đơn hàng thất bại</li>
                                                                <li><code>cri_description </code>: Mô tả đơn hàng</li>
                                                                <li><code>cri_assignee_id </code>: Người phụ trách</li>
                                                                <li><code>cri_name </code>: Tên đơn hàng</li>
                                                                <li><code>cri_updated_time </code>: Thời gian cập nhật đơn hàng</li>
                                                                <li><code>cri_estimate_time </code>: Thời gian chốt đơn hàng</li>
                                                                <li><code>cri_created_time </code>: Thời gian tạo đơn hàng</li>
                                                                <li><code>cri_state_ratio </code>: Tỷ lệ thành công</li>
                                                                <li><code>cri_state_code </code>: Trạng thái đơn hàng</li> 
                                                                <li><code>cri_contract_time </code>: Thời gian cần liên hệ</li>
                                                                <li><code>cri_sale_process_id </code>: Quy trình bán hàng</li>
                                                                <li><code>cri_tag_ids </code>: Tag phân công công việc</li>
                                                                <li><code>cri_team_id </code>: Team phụ trách</li>
                                                                <li><code>cri_products </code>: Sản phẩm bán trong đơn hàng</li>
                                                                <li><code>cri_product_sku </code>: Mã sản phẩm / danh mục sản phẩm</li>
                                                                <li><code>cri_source </code>: Nguồn ghi nhận đơn hàng</li>
                                                                <li><code>cri_supporter_ids </code>: Deal supporter</li>
                                                                <li><code>cri_assignment_status </code>: Trạng thái phân công đơn hàng</li>
                                                                <li><code>cri_product_categories</code>: Danh mục sản phẩm gắn vào đơn hàng</li>
                                                                <li><code>cri_scope_code</code>: Phân quyền hiển thị</li>
                                                                <li><code>cri_products_bank</code>: Sản phẩm bank</li>
                                                                <li><code>cri_product_line</code>: Dòng sản phẩm</li>
                                                                <li><code>cri_product_types</code>: Loại sản phẩm</li>
                                                                <li><code>cri_product_sku_bank</code>: Mã sản phẩm / dòng sản phẩm</li>
                                                                <li><code>cri_product_bank_v1_product_bank_multiple</code>: Sản phẩm version bank</li>
                                                                <li><code>cri_state_code_over_time_multiple</code>: Trạng thái đơn hàng theo thời gian</li>
                                                                <li><code>cri_scope_code_multiple</code>: Mã cấp quản lý theo cấp</li>
                                                              </ul>
                                                                                                                  
@apiParam	(sale_filters:)  {String}	operator_key		  Các toán tử để filter đơn hàng.ime
                                                              Alow value:ultiple
                                                              <code> 
                                                                "op_is_between", "op_is_greater_equal", "op_is_in",
                                                                 "op_is_equal", "op_is_greater", "op_is_has", 
                                                                 "op_is_has_not", "op_is_less_equal", "op_is_less",
                                                                 "op_is_has_in" , "op_is_multiple", "op_is_in_recent_period"
                                                              </code>       
                                                              <ul>
                                                                <li><code>op_is_between</code>: Thỏa mãn khoảng giá trị </li>
                                                                <li><code>op_is_greater_equal</code>: Lớn hơn hoặc bằng giá trị values[0]</li>
                                                                <li><code>op_is_in</code>: Bằng các giá trị nằm trong values</li>
                                                                <li><code>op_is_equal</code>: Bằng các giá trị values[0]</li>
                                                                <li><code>op_is_greater</code>: lớn hơn giá trị values[0]</li>
                                                                <li><code>op_is_has</code>: So sánh contains chuỗi với giá trị values[0]</li>
                                                                <li><code>op_is_less_equal</code>: nhỏ hơn hoặc bằng giá trị values[0]</li>
                                                                <li><code>op_is_less</code>: nhỏ hơn giá trị values[0]</li>
                                                                <li><code>op_is_has_in</code>: So sánh contains chuỗi với các giá trị nằm trong values</li>
                                                                <li><code>op_is_multiple</code>: Kiểm tra tất cả các điều kiện của giá trị trong values</li>
                                                                <li><code>op_is_in_recent_period</code>: Thỏa mãn khoảng thời gian gần đây, giá trị values là object {"unit": "day", "value": 30}</li>
                                                              </ul>       
"""
####################################################################################################
# THÊM MỚI ĐƠN HÀNG
# version: 1.0.2                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals  Thêm mới đơn hàng
@apiDescription Dịch vụ thêm mới đơn hàng bằng tay
@apiGroup Deal
@apiVersion 1.0.2
@apiName DealAdd

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam	(Body:)			{int}		[template]			  Version của base_field
@apiParam	(Body:)			{String}	merchant_id			  Id của merchant người tạo
@apiParam	(Body:)			{String}	name			      Tên đơn hàng
@apiParam	(Body:)			{String}	sale_process_id		  ID Quy trình bán hàng
@apiParam	(Body:)			{String}	state_code		      Mã code trạng thái đơn hàng
@apiParam	(Body:)			{string}	assignee_id		      ID người phụ trách đơn hàng
@apiParam	(Body:)			{Array}	    [supporter_ids]		  Danh sách ID nhân viên hỗ trợ xử lý đơn hàng
@apiParam	(Body:)			{number}	[sale_value]		  Giá trị đơn hàng
@apiParam	(Body:)			{string}	[description]		  Mô tả
@apiParam	(Body:)			{datetime}	[estimate_time]		  Thời gian chốt đơn hàng
                                                              <code>Định dạng: dd/mm/yyyy </code>
@apiParam	(Body:)			{Array}	    profiles		      Danh sách profile 
@apiParam	(Body:)			{Array}	    [products]		      Danh sách sản phẩm   
@apiParam	(Body:)			{Array}	    [tickets]		      Danh sách ticket       
@apiParam	(Body:)			{Array}	    [companies]		      Danh sách công ty                                                   
@apiParam	(Body:)			{string}	[reason_success]	  Lý do chốt đơn thành công
@apiParam	(Body:)			{string}	[reason_fail]	      Lý do chốt đơn thất bại
@apiParam	(Body:)			{Integer}	ignore_duplicate	  <code>1- update kể cả trùng,0- gửi lại thông báo nếu trùng</code>

@apiParamExample {json} Body example
{
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "name":"Tên đơn hàng 1",
    "sale_process_id":"5d7f150be6481e870a8ce0ad",
    "state_code" : "LEAD",
    "sale_value":  1569000,
    "description": "Mô tả đơn hàng",
    "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
    "supporter_ids": [
        "c56fab40-e099-448c-95c3-211d6d5b2b8d"
    ],
    "estimate_time":"24/12/2020",
    "reason_success":"",
    "reason_fail":"",
    "profiles":[
        "45042df5-2202-4964-b05f-d53e21f5f895",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894"
    ],
    "tickets":[
        "45042df5-2202-4964-b05f-d53e21f5f890",
        "45042df5-2202-4964-b05f-d53e21f5f820"
    ],
    "companies":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ]
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "data": {
        "_id":"5dde29fda2596203036b12c4",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name":"Tên đơn hàng 1",
        "code": "NGHK8385",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "sale_process_id":"5d7f150be6481e870a8ce0ad",
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
        "supporter_ids": [
            "c56fab40-e099-448c-95c3-211d6d5b2b8d"
        ],
        "estimate_time":"2019-11-26T12:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
        "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
        "updated_time":"2019-11-26T12:00:00Z",
        "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
        "deal_new": 1, //1:mới, 0: hết mới,
        "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
    },
    "code": 200,
    "message": "request thành công."
}

@apiSuccessExample {json} Response duplicate deal
{
     "data": [
    	{
    		"name":"Trư bát giới",
    		"code":"ZECQQQ271219",
    		"assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895","45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    	{
    		"name":"Đường tăng",
    		"code":"GCFJIR271219",
    		"assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891"]
    	},
    	{
    		"name":"Bạch cốt tinh",
    		"code":"VXWTED271219",
    		"assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    ]
    "code": 413,
    "message": "i18n_message_valid_product_profiles_order_exist"
}
"""

####################################################################################################
# THÊM MỚI ĐƠN HÀNG
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals  Thêm mới đơn hàng
@apiDescription Dịch vụ thêm mới đơn hàng bằng tay
@apiGroup Deal
@apiVersion 1.0.1
@apiName DealAdd

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam	(Body:)			{int}		[template]			  Version của base_field
@apiParam	(Body:)			{String}	merchant_id			  Id của merchant người tạo
@apiParam	(Body:)			{String}	name			      Tên đơn hàng
@apiParam	(Body:)			{String}	sale_process_id		  ID Quy trình bán hàng
@apiParam	(Body:)			{String}	state_code		      Mã code trạng thái đơn hàng
@apiParam	(Body:)			{string}	assignee_id		  ID người phụ trách đơn hàng
@apiParam	(Body:)			{number}	[sale_value]		  Giá trị đơn hàng
@apiParam	(Body:)			{string}	[description]		  Mô tả
@apiParam	(Body:)			{datetime}	[estimate_time]		  Thời gian chốt đơn hàng
                                                              <code>Định dạng: dd/mm/yyyy </code>
@apiParam	(Body:)			{Array}	    profiles		      Danh sách profile 
@apiParam	(Body:)			{Array}	    [products]		      Danh sách sản phẩm   
@apiParam	(Body:)			{Array}	    [tickets]		      Danh sách ticket       
@apiParam	(Body:)			{Array}	    [companies]		      Danh sách công ty                                                   
@apiParam	(Body:)			{string}	[reason_success]	  Lý do chốt đơn thành công
@apiParam	(Body:)			{string}	[reason_fail]	      Lý do chốt đơn thất bại
@apiParam	(Body:)			{Integer}	ignore_duplicate	  <code>1- update kể cả trùng,0- gửi lại thông báo nếu trùng</code>

@apiParamExample {json} Body example
{
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "name":"Tên đơn hàng 1",
    "sale_process_id":"5d7f150be6481e870a8ce0ad",
    "state_code" : "LEAD",
    "sale_value":  1569000,
    "description": "Mô tả đơn hàng",
    "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
    "estimate_time":"24/12/2020",
    "reason_success":"",
    "reason_fail":"",
    "profiles":[
        "45042df5-2202-4964-b05f-d53e21f5f895",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894"
    ],
    "tickets":[
        "45042df5-2202-4964-b05f-d53e21f5f890",
        "45042df5-2202-4964-b05f-d53e21f5f820"
    ],
    "companies":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ]
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "data": {
        "_id":"5dde29fda2596203036b12c4",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name":"Tên đơn hàng 1",
        "code": "NGHK8385",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "sale_process_id":"5d7f150be6481e870a8ce0ad",
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
        "estimate_time":"2019-11-26T12:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
        "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
        "updated_time":"2019-11-26T12:00:00Z",
        "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
        "deal_new": 1, //1:mới, 0: hết mới,
        "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
    },
    "code": 200,
    "message": "request thành công."
}

@apiSuccessExample {json} Response duplicate deal
{
    "data": [
    	{
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895","45042df5-2202-4964-b05f-d53e21f5f892"],
    		"product":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    	{
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895"],
    		"product":["45042df5-2202-4964-b05f-d53e21f5f891"]
    	},
    	{
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f892"],
    		"product":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    ],
    "code": 413,
    "message": "i18n_message_valid_product_profiles_order_exist"
}
"""
####################################################################################################
# Sửa đơn hàng
# version: 1.0.3                                                                                   #
####################################################################################################
"""
@api {PUT} {domain}/sale/api/v1.0/deals/<deal_id>  Cập nhật thông tin đơn hàng
@apiGroup Deal
@apiVersion 1.0.3
@apiName DealUpdate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)         {String}    [type_update]           <code>'default'- update deal chi tiết, 'quick'- update deal nhanh</code>
@apiParam   (Body:)         {int}       [template]            Version của base_field
@apiParam   (Body:)         {String}    sale_process_id         ID Quy trình bán hàng
@apiParam	(Body:)			{Array}	    [supporter_ids]		  Danh sách ID nhân viên hỗ trợ xử lý đơn hàng
@apiParam   (Body:)         {String}    state_code              Mã code trạng thái đơn hàng
@apiParam   (Body:)         {number}    sale_value              Giá trị đơn hàng
@apiParam   (Body:)         {string}    description             Mô tả
@apiParam   (Body:)         {string}    assignee_id             ID người phụ trách đơn hàng
@apiParam   (Body:)         {datetime}  estimate_time           Thời gian chốt đơn hàng
                                                                 <code>Định dạng: dd/mm/yyyy </code>
@apiParam   (Body:)         {string}    reason_success          Lý do chốt đơn thành công
@apiParam   (Body:)         {string}    reason_fail             Lý do chốt đơn thất bại
@apiParam   (Body:)         {Array}     profiles                Danh sách profile
@apiParam   (Body:)         {Array}     products                Danh sách sản phẩm
@apiParam   (Body:)         {Array}     tickets                 Danh sách ticket
@apiParam   (Body:)         {Array}     companies               Danh sách công ty
@apiParam   (Body:)         {Integer}   ignore_duplicate        <code>1- update kể cả trùng,0- gửi lại thông báo nếu trùng</code>

@apiParamExample {json} Body example
{
    "name":"Tên đơn hàng 1",
    "ignore_duplicate":1,
    "sale_process_id":"5d7f150be6481e870a8ce0ad",
    "state_code" : "LEAD",
    "sale_value":  1569000,
    "description": "Mô tả đơn hàng",
    "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
    "supporter_ids": [
        "c56fab40-e099-448c-95c3-211d6d5b2b8d"
    ],
    "estimate_time":"24/12/2022",
    "reason_success":"",
    "reason_fail":"",
    "profiles":[
        "45042df5-2202-4964-b05f-d53e21f5f895",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894"
    ],
    "tickets":[
        "45042df5-2202-4964-b05f-d53e21f5f890",
        "45042df5-2202-4964-b05f-d53e21f5f820"
    ],
    "companies":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    type_update:"quick"
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {datetime}          estimate_time           Thời gian chốt đơn hàng
                                                                 <code>Định dạng: "yyyy-mm-dd-Th:m:sZ" </code>
@apiSuccessExample {json} Response success
{
    "code":200,
    "data":{
        "id": "5de61bb97dfdcfe3cbb5d89e",
        "name":"Tên đơn hàng 1",
        "ignore_duplicate":1,
        "sale_process_id":"5d7f150be6481e870a8ce0ad",
        "supporter_ids": [
            "c56fab40-e099-448c-95c3-211d6d5b2b8d"
        ],
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
        "estimate_time":"2022-12-24T00:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ]
    },
    "lang": "vi",
    "message": "request thành công."
    
}

@apiSuccessExample {json} Response duplicate deal
{
    "data": [
    	{
    		"name":"Trư bát giới",
    		"code":"ZECQQQ271219",
    		"assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895","45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    	{
    		"name":"Đường tăng",
    		"code":"GCFJIR271219",
    		"assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891"]
    	},
    	{
    		"name":"Bạch cốt tinh",
    		"code":"VXWTED271219",
    		"assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    ]
    "code": 413,
    "message": "i18n_message_valid_product_profiles_order_exist"
}
"""
####################################################################################################
# Sửa đơn hàng
# version: 1.0.2                                                                                   #
####################################################################################################
"""
@api {PUT} {domain}/sale/api/v1.0/deals/<deal_id>  Cập nhật thông tin đơn hàng
@apiGroup Deal
@apiVersion 1.0.2
@apiName DealUpdate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)         {String}    [type_update]           <code>'default'- update deal chi tiết, 'quick'- update deal nhanh</code>
@apiParam   (Body:)         {int}       [template]            Version của base_field
@apiParam   (Body:)         {String}    sale_process_id         ID Quy trình bán hàng
@apiParam   (Body:)         {String}    state_code              Mã code trạng thái đơn hàng
@apiParam   (Body:)         {number}    sale_value              Giá trị đơn hàng
@apiParam   (Body:)         {string}    description             Mô tả
@apiParam   (Body:)         {string}    assignee_id             ID người phụ trách đơn hàng
@apiParam   (Body:)         {datetime}  estimate_time           Thời gian chốt đơn hàng
                                                                 <code>Định dạng: dd/mm/yyyy </code>
@apiParam   (Body:)         {string}    reason_success          Lý do chốt đơn thành công
@apiParam   (Body:)         {string}    reason_fail             Lý do chốt đơn thất bại
@apiParam   (Body:)         {Array}     profiles                Danh sách profile
@apiParam   (Body:)         {Array}     products                Danh sách sản phẩm
@apiParam   (Body:)         {Array}     tickets                 Danh sách ticket
@apiParam   (Body:)         {Array}     companies               Danh sách công ty
@apiParam   (Body:)         {Integer}   ignore_duplicate        <code>1- update kể cả trùng,0- gửi lại thông báo nếu trùng</code>

@apiParamExample {json} Body example
{
    "name":"Tên đơn hàng 1",
    "ignore_duplicate":1,
    "sale_process_id":"5d7f150be6481e870a8ce0ad",
    "state_code" : "LEAD",
    "sale_value":  1569000,
    "description": "Mô tả đơn hàng",
    "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
    "estimate_time":"24/12/2022",
    "reason_success":"",
    "reason_fail":"",
    "profiles":[
        "45042df5-2202-4964-b05f-d53e21f5f895",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894"
    ],
    "tickets":[
        "45042df5-2202-4964-b05f-d53e21f5f890",
        "45042df5-2202-4964-b05f-d53e21f5f820"
    ],
    "companies":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    type_update:"quick"
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {datetime}          estimate_time           Thời gian chốt đơn hàng
                                                                 <code>Định dạng: "yyyy-mm-dd-Th:m:sZ" </code>
@apiSuccessExample {json} Response success
{
    "code":200,
    "data":{
        "id": "5de61bb97dfdcfe3cbb5d89e",
        "name":"Tên đơn hàng 1",
        "ignore_duplicate":1,
        "sale_process_id":"5d7f150be6481e870a8ce0ad",
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
        "estimate_time":"2022-12-24T00:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ]
    },
    "lang": "vi",
    "message": "request thành công."
    
}

@apiSuccessExample {json} Response duplicate deal
{
    "data": [
        {
            "profiles":["45042df5-2202-4964-b05f-d53e21f5f895","45042df5-2202-4964-b05f-d53e21f5f892"],
            "product":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
        },
        {
            "profiles":["45042df5-2202-4964-b05f-d53e21f5f895"],
            "product":["45042df5-2202-4964-b05f-d53e21f5f891"]
        },
        {
            "profiles":["45042df5-2202-4964-b05f-d53e21f5f892"],
            "product":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
        },
    ],
    "code": 413,
    "message": "i18n_message_valid_product_profiles_order_exist"
}
"""

####################################################################################################
# Sửa đơn hàng
# version: 1.0.1                                                                                   #
####################################################################################################
"""
@api {PUT} {domain}/sale/api/v1.0/deals/<deal_id>  Cập nhật thông tin đơn hàng
@apiGroup Deal
@apiVersion 1.0.1
@apiName DealUpdate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam	(Body:)			{int}		[template]			  Version của base_field
@apiParam	(Body:)			{String}	sale_process_id		  	ID Quy trình bán hàng
@apiParam	(Body:)			{String}	state_code		      	Mã code trạng thái đơn hàng
@apiParam	(Body:)			{number}	sale_value		  		Giá trị đơn hàng
@apiParam	(Body:)			{string}	description		  		Mô tả
@apiParam	(Body:)			{string}	assignee_id		  		ID người phụ trách đơn hàng
@apiParam	(Body:)			{datetime}	estimate_time		  	Thời gian chốt đơn hàng
                                                              	 <code>Định dạng: dd/mm/yyyy </code>
@apiParam	(Body:)			{string}	reason_success	  		Lý do chốt đơn thành công
@apiParam	(Body:)			{string}	reason_fail	      		Lý do chốt đơn thất bại
@apiParam	(Body:)			{Array}	    profiles		      	Danh sách profile
@apiParam	(Body:)			{Array}	    products		      	Danh sách sản phẩm
@apiParam	(Body:)			{Array}	    tickets		      		Danh sách ticket
@apiParam	(Body:)			{Array}	    companies		      	Danh sách công ty
@apiParam	(Body:)			{Integer}	ignore_duplicate	  	<code>1- update kể cả trùng,0- gửi lại thông báo nếu trùng</code>

@apiParamExample {json} Body example
{
	"name":"Tên đơn hàng 1",
	"ignore_duplicate":1,
    "sale_process_id":"5d7f150be6481e870a8ce0ad",
    "state_code" : "LEAD",
    "sale_value":  1569000,
    "description": "Mô tả đơn hàng",
    "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
    "estimate_time":"24/12/2022",
    "reason_success":"",
    "reason_fail":"",
    "profiles":[
        "45042df5-2202-4964-b05f-d53e21f5f895",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894"
    ],
    "tickets":[
        "45042df5-2202-4964-b05f-d53e21f5f890",
        "45042df5-2202-4964-b05f-d53e21f5f820"
    ],
    "companies":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ]
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess	{datetime}			estimate_time		  	Thời gian chốt đơn hàng
                                                              	 <code>Định dạng: "yyyy-mm-dd-Th:m:sZ" </code>
@apiSuccessExample {json} Response success
{
	"code":200,
	"data":{
		"id": "5de61bb97dfdcfe3cbb5d89e",
		"name":"Tên đơn hàng 1",
		"ignore_duplicate":1,
	    "sale_process_id":"5d7f150be6481e870a8ce0ad",
	    "state_code" : "LEAD",
	    "sale_value":  1569000,
	    "description": "Mô tả đơn hàng",
	    "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
	    "estimate_time":"2022-12-24T00:00:00Z",
	    "reason_success":"",
	    "reason_fail":"",
	    "profiles":[
	        "45042df5-2202-4964-b05f-d53e21f5f895",
	        "45042df5-2202-4964-b05f-d53e21f5f892"
	    ],
	    "products":[
	        "45042df5-2202-4964-b05f-d53e21f5f891",
	        "45042df5-2202-4964-b05f-d53e21f5f894"
	    ],
	    "tickets":[
	        "45042df5-2202-4964-b05f-d53e21f5f890",
	        "45042df5-2202-4964-b05f-d53e21f5f820"
	    ],
	    "companies":[
	        "45042df5-2202-4964-b05f-d53e21f5f891",
	        "45042df5-2202-4964-b05f-d53e21f5f892"
	    ]
	},
	"lang": "vi",
  	"message": "request thành công."
    
}

@apiSuccessExample {json} Response duplicate deal
{
    "data": [
    	{
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895","45042df5-2202-4964-b05f-d53e21f5f892"],
    		"product":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    	{
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895"],
    		"product":["45042df5-2202-4964-b05f-d53e21f5f891"]
    	},
    	{
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f892"],
    		"product":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    ],
    "code": 413,
    "message": "i18n_message_valid_product_profiles_order_exist"
}
"""

####################################################################################################
# Xóa đơn hàng
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {DELETE} {domain}/sale/api/v1.0/deals  Xóa đơn hàng
@apiGroup Deal
@apiVersion 1.0.1
@apiName DealDelete

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(PARAMS:)			{String}	    deal_ids			  Danh sách ID đơn hàng, id đơn hàng cách nhau bởi dấu <code>;</code>

@apiParamExample {String} Param example
/deals?deal_ids=5dde29fda2596203036b12c4;5dde29fda2596203036b12c2

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}           data_delete                     Danh sách deal_ids xóa thành công

@apiSuccessExample {json} Response
{
    "code": 200,
    "data_delete" : [
        "5dde43d3424812febe79ed18",
        "5dde43d3424812febe79ed01"
    ],
    "message": "request thành công."
}
"""

####################################################################################################
# Xóa danh sách CHB
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/action/delete  Xóa danh sách CHB
@apiGroup Deal
@apiVersion 1.0.1
@apiName DealsActionDelete

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(BODY:)			{Array}	    deal_ids	      Danh sách ID đơn hàng (Tối đa 100 phần tử)

@apiParamExample {json} Body example
{
    "deal_ids": [
        "654c7bd9d0c005cc50d61970"
    ]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}           data_delete                     Danh sách deal_ids xóa thành công

@apiSuccessExample {json} Response
{
    "code": 200,
    "data_delete" : [
        "654c7bd9d0c005cc50d61970"
    ],
    "message": "request thành công."
}
"""

####################################################################################################
# Danh sách đơn hàng
# version: 1.0.2                                                                                   #
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/actions/list  Danh sách đơn hàng
@apiGroup Deal
@apiVersion 1.0.2
@apiName DealList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header


@apiParam	(BODY:)			{String}    view_type   	      Kiểu hiển thị đơn hàng
                                                              <code>
                                                                <ul>
                                                                    <li>KANBAN: hiển thị dạng cột </li>
                                                                    <li>TABLE:  hiển thị dạng bảng </li>
                                                                </ul>
                                                                default: KANBAN
                                                              </code>

@apiParam	(BODY:)			{String}	sale_process_id		  Quy trình bán hàng
@apiParam	(BODY:)			{String}	state_code		      Mã code trạng thái đơn hàng
                                                              <code> - Trường hợp view_type = KANBAN:
                                                                     state_code là bắt buộc
                                                                     - Trường hợp view_type = TABLE
                                                                     state_code không bắt buộc  
                                                              </code>

@apiParam	(BODY:)			{String}	search			      Tìm kiếm đơn hàng theo tên đơn hàng
@apiParam	(BODY:)			{Array}	    [sale_filters]		  danh sách các điều kiện lọc đơn hàng
@apiParam	(BODY:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả. 
                                                              VD: ["name","code"]. Trường hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính
                                                              
@apiUse sale_filters                                                              
                                                            
@apiParam	(Query:)		{String}	[sort]   	          Tên field trường thông tin cần sắp xếp                                                                             
@apiParam	(Query:)		{String}	[order]   	          Kiểu sắp xếp:
                                                              <ul>
                                                                <li><code>asc: </code> sắp xếp tăng dần</li>
                                                                <li><code>desc: </code> sắp xếp giảm dần</li>
                                                              </ul>               
                                                              alow value: asc, desc      
                                                                                                                                                                            
@apiUse paging_tokens

@apiParamExample {json} Body example
{
    "view_type": "KANBAN",
    "sale_process_id": "5de5f623b64d2a30d5e552e6",
    "state_code": "STT002",
    "search": "",
    "fields": [
        "code",
        "name"
    ],
    "sale_filters": [
        {
            "criteria_key": "cri_reason_success",
            "operator_key": "op_is_has",
            "values": [
                "khách hàng chốt đơn"
            ]
        },
        {
            "criteria_key": "cri_estimate_time",
            "operator_key": "op_is_between",
            "values": [
                "2019-01-01",
                "2019-10-10"
            ]
        }
    ]
}


@apisuccess {Array}             data                          Danh sách đơn hàng
@apisuccess {number}            total_sale_value              Tổng doanh thu đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response KANBAN View
{
    "data": [
        {
            "state_code": "",
            "name":"Tên đơn hàng 1",
            "code": "NGHK8385",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "sale_process_id":"5d7f150be6481e870a8ce0ad",
            "process_name":"Quy trình 1",
            "state_name":"Có Lead",
            "state_ratio":10,
            "sale_value":  1569000,
            "description": "Mô tả đơn hàng",
            "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
            "estimate_time":"2019-11-26T12:00:00Z",
            "reason_success":"",
            "reason_fail":"",
            "profiles":[
                "uuid1",
                "uuid2"
            ],
            "products":[
                "uuid1",
                "uuid2"
            ],
            "tickets":[
                "uuid1",
                "uuid2"
            ],
            "companies":[
                "uuid1",
                "uuid2"
            ],
            "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
            "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
            "updated_time":"2019-11-26T12:00:00Z",
            "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
            "deal_new": 1 //1:mới, 0: hết mới,
            "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
        },
        ...
    ]
    "code": 200,
    "message": "request thành công.",
    "total_sale_value": 1560000
}

@apiSuccessExample {json} Response TABLE View
{
    "data": [
        {
            "name":"Tên đơn hàng 1",
            "code": "NGHK8385",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "sale_process_id":"5d7f150be6481e870a8ce0ad",
            "state_code" : "LEAD",
            "sale_value":  1569000,
            "description": "Mô tả đơn hàng",
            "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
            "estimate_time":"2019-11-26T12:00:00Z",
            "reason_success":"",
            "reason_fail":"",
            "profiles":[
                "45042df5-2202-4964-b05f-d53e21f5f895",
                "45042df5-2202-4964-b05f-d53e21f5f892"
            ],
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
            ],
            "tickets":[
                "45042df5-2202-4964-b05f-d53e21f5f890",
                "45042df5-2202-4964-b05f-d53e21f5f820"
            ],
            "companies":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f892"
            ],
            "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
            "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
            "updated":[
                {
                    "updated_time":"2019-11-26T12:00:00Z",
                    "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
                }
            ],
            "deal_new": 1, //1:mới, 0: hết mới,
            "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
        }
    ]
    "code": 200,
    "message": "request thành công.",
    "total_sale_value": 1560000
}

"""

"""
@api {GET} {domain}/sale/api/v1.0/deals  Danh sách đơn hàng
@apiGroup Deal
@apiVersion 1.0.1
@apiName DealList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(PARAMS:)			{String}	view_type   		  Kiểu hiển thị đơn hàng
                                                              <code>
                                                                <ul>
                                                                    <li>KANBAN: hiển thị dạng cột </li>
                                                                    <li>TABLE:  hiển thị dạng bảng </li>
                                                                </ul>
                                                                default: KANBAN
                                                              </code>
                        
@apiParam	(PARAMS:)			{String}	sale_process_id		  Quy trình bán hàng
@apiParam	(PARAMS:)			{String}	state_code		      Mã code trạng thái đơn hàng
                                                              <code> - Trường hợp view_type = KANBAN:
                                                                     state_code là bắt buộc
                                                                     - Trường hợp view_type = TABLE
                                                                     state_code không bắt buộc  
                                                              </code>

@apiParam	(PARAMS:)			{String}	[name]			      Tìm kiếm đơn hàng theo tên đơn hàng
@apiParam	(PARAMS:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả. 
                                                              VD: ["name","code"]. Trường hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính
@apiUse paging_tokens

@apisuccess {Array}             data                          Danh sách đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response KANBAN View
{
    "data": [
        {
            "state_code": "",
            "name":"Tên đơn hàng 1",
            "code": "NGHK8385",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "sale_process_id":"5d7f150be6481e870a8ce0ad",
            "process_name":"Quy trình 1",
            "state_name":"Có Lead",
            "state_ratio":10,
            "sale_value":  1569000,
            "description": "Mô tả đơn hàng",
            "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
            "estimate_time":"2019-11-26T12:00:00Z",
            "reason_success":"",
            "reason_fail":"",
            "profiles":[
                "uuid1",
                "uuid2"
            ],
            "products":[
                "uuid1",
                "uuid2"
            ],
            "tickets":[
                "uuid1",
                "uuid2"
            ],
            "companies":[
                "uuid1",
                "uuid2"
            ],
            "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
            "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
            "updated_time":"2019-11-26T12:00:00Z",
            "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
            "deal_new": 1 //1:mới, 0: hết mới,
            "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
        },
        ...
    ]
    "code": 200,
    "message": "request thành công."
}

@apiSuccessExample {json} Response TABLE View
{
    "data": [
        {
            "name":"Tên đơn hàng 1",
            "code": "NGHK8385",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "sale_process_id":"5d7f150be6481e870a8ce0ad",
            "state_code" : "LEAD",
            "sale_value":  1569000,
            "description": "Mô tả đơn hàng",
            "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
            "estimate_time":"2019-11-26T12:00:00Z",
            "reason_success":"",
            "reason_fail":"",
            "profiles":[
                "45042df5-2202-4964-b05f-d53e21f5f895",
                "45042df5-2202-4964-b05f-d53e21f5f892"
            ],
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
            ],
            "tickets":[
                "45042df5-2202-4964-b05f-d53e21f5f890",
                "45042df5-2202-4964-b05f-d53e21f5f820"
            ],
            "companies":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f892"
            ],
            "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
            "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
            "updated":[
                {
                    "updated_time":"2019-11-26T12:00:00Z",
                    "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
                }
            ],
            "deal_new": 1, //1:mới, 0: hết mới,
            "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
        }
    ]
    "code": 200,
    "message": "request thành công."
}

"""

####################################################################################################
# Chi tiết đơn hàng
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/sale/api/v1.0/deals/<deal_id>  Chi tiết đơn hàng
@apiGroup Deal
@apiVersion 1.0.1
@apiName DealDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(PARAMS:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả. 
                                                              VD: ["name","code"]. Trường hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính

@apisuccess {Array}             data                          Thông tin chi tiết của đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response detail deal
{
    "data": {
        "_id": uuid,
        "name": "Tên của đơn hàng đang cần lấy thông tin chi tiết",
        "code": "NGHK8385",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "sale_process_id":"45042df5-2202-4964-b05f-d53e21f5f896",
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
        "estimate_time":"2019-11-26T12:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
        "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
        "updated_time":"2019-11-26T12:00:00Z",
        "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
        "deal_new": 1, //1:mới, 0: hết mới,
        "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động,
        "status" : 1 // 1: Active; -1: Delete
    }
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# Đổi trạng thái đơn hàng
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/actions/change_state   Thay đổi trạng thái Deal
@apiGroup Deal
@apiVersion 1.0.1
@apiName ChangeStateDeal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header


@apiParam (Body) {String} state_code Mã Trạng thái muốn chuyển tới
@apiParam (Body) {String} before_deal_id <code>deal_id</code> phía trên (trong cột <code>state_code</code>).
                                    Nếu không truyền before_deal_id thì deal tự động được đẩy lên đầu danh sách.

@apiParamExample {json} Body example
{
    "state_code" : "5da9725e6a6aae17ad96f7b4",
    "before_deal_id" : "5da972766a6aae17ad96f7b5"
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật trạng thái thành công."
}
"""

####################################################################################################
# Đổi trạng thái đơn hàng
# version: 1.0.2                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/actions/change_state   Thay đổi trạng thái Deal
@apiGroup Deal
@apiVersion 1.0.2
@apiName ChangeStateDeal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header


@apiParam (Body) {String} state_code Mã Trạng thái muốn chuyển tới
@apiParam (Body) {Object} deal_info  Thông tin field đơn hàng cấu hình chuyển trạng thái đơn hàng
@apiParam (Body) {String} before_deal_id <code>deal_id</code> phía trên (trong cột <code>state_code</code>).
                                    Nếu không truyền before_deal_id thì deal tự động được đẩy lên đầu danh sách.

@apiParamExample {json} Body example
{
    "state_code" : "5da9725e6a6aae17ad96f7b4",
    "before_deal_id" : "5da972766a6aae17ad96f7b5",
    "deal_info": {
        "name": "Tên đơn hàng 1",
        "scope_code": "AREA_5##19000"
    }
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật trạng thái thành công."
}
"""

####################################################################################################
# Đổi trạng thái đơn hàng
# version: 1.0.3                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/actions/change_state   Thay đổi trạng thái Deal
@apiGroup Deal
@apiVersion 1.0.3
@apiName ChangeStateDeal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header


@apiParam (Body) {String} state_code Mã Trạng thái muốn chuyển tới
@apiParam (Body) {Object} deal_info  Thông tin field đơn hàng cấu hình chuyển trạng thái đơn hàng
@apiParam (Body) {Object} [note_info]  Thông tin khởi tạo ghi chú <code>Bắt buộc khi trạng thái có cấu hình chuyển bước</code></br>
                                       Tham khảo docs API tạo ghi chú : <a href="https://dev.mobio.vn/docs/note/#api-Note-AddNote">Tạo ghi chú mới</a>
@apiParam (Body) {Object} [task_info]  Thông tin khởi tạo Công việc <code>Bắt buộc khi trạng thái có cấu hình chuyển bước</code></br>
                                       Tham khảo docs API tạo mới công việc : <a href="https://dev.mobio.vn/docs/task/#api-Task_Upgrade-AddTask">Tạo mới Công việc</a>
                                                                            
@apiParam (Body) {String} before_deal_id <code>deal_id</code> phía trên (trong cột <code>state_code</code>).
                                    Nếu không truyền before_deal_id thì deal tự động được đẩy lên đầu danh sách.

@apiParamExample {json} Body example
{
    "state_code" : "5da9725e6a6aae17ad96f7b4",
    "before_deal_id" : "5da972766a6aae17ad96f7b5",
    "deal_info": {
        "name": "Tên đơn hàng 1",
        "scope_code": "AREA_5##19000"
    },
    "note_info": {
      "description": "<div>Test body tạo ghi chú thôi chứ ko có gì</div>",
      "attachment_ids": [
        
      ],
      "information": {
        "type": "CALL",
        "profiles_contact": [
          "344fcf09-14a7-439d-9f4f-3fff0b093ade"
        ],
        "time_contact": "2023-10-03T03:16Z",
        "result": "REFUSE_ANSWER"
      },
      "source": "SALE",
      "object_id": "643522c4cf7c7a81d1f64d2f",
      "follow_task": {
        "status": 1,
        "title": "công việc tiếp nối",
        "deadline": {
          "type": "time_slot",
          "start_time": "2023-10-03 04:00",
          "end_time": "2023-10-03 05:23"
        }
      },
      "related_to": {
        "company_ids": [
          
        ],
        "order_ids": [
          "643522c4cf7c7a81d1f64d2f"
        ],
        "profile_ids": [
          
        ],
        "ticket_ids": [
          
        ]
      },
      "related_to_not_delete": {
        "company_ids": [
          
        ],
        "order_ids": [
          "643522c4cf7c7a81d1f64d2f"
        ],
        "profile_ids": [
          
        ],
        "ticket_ids": [
          
        ]
      },
      "mentions": [
        
      ],
      "description_attachment_ids": [
        
      ]
    },
    "task_info": {
      "assign_ids": [
        "ff6fba38-5f3d-43a7-ad65-e9c235a34633"
      ],
      "assign_type": "STAFF",
      "description": "<div>Test công việc mà thôi</div>",
      "title": "Tên công việc test mà thôi",
      "deadline_type": "time_slot",
      "deadline_start_time": "2023-10-04T21:33Z",
      "deadline_end_time": "2023-10-04T22:00Z",
      "profile_ids": [
        
      ],
      "company_ids": [
        
      ],
      "order_ids": [
        "643522c4cf7c7a81d1f64d2f"
      ],
      "ticket_ids": [
        
      ],
      "related_to_not_delete": {
        "company_ids": [
          
        ],
        "order_ids": [
          "643522c4cf7c7a81d1f64d2f"
        ],
        "profile_ids": [
          
        ],
        "ticket_ids": [
          
        ]
      },
      "task_type": "GENERAL",
      "notification_config_type": "NO_REMINDER",
      "priority_level": 1,
      "source": "SALE",
      "object_id": "643522c4cf7c7a81d1f64d2f",
      "mentions": [
        
      ],
      "description_attachment_ids": [
        
      ],
      "status_process": 1,
      "planned_start_time": "2023-10-03T03:55Z",
      "compulsory_completion": false,
      "follow_task": {
        "status": 1,
        "title": "Công việc tiếp nối mà thôi",
        "deadline_type": "time_slot",
        "deadline_start_time": "2023-10-03 04:00",
        "deadline_end_time": "2023-10-03 06:00"
      }
    }
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật trạng thái thành công."
}
"""

####################################################################################################
# Gán profile vào Deal
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/actions/add_profile  Gán thêm profile cho Deal
@apiGroup Deal
@apiVersion 1.0.1
@apiName dealAddProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam (Body) {Array} profiling_profile_ids Danh sách profile_id

@apiParamExample {json} Body example
{
    "profiling_profile_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"]
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật deal thành công."
}
"""

####################################################################################################
# Gán profile vào Deal
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/actions/remove_profile  Xóa profile khỏi Deal
@apiGroup Deal
@apiVersion 1.0.1
@apiName dealRemoveProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam (Body) {Array} profiling_profile_ids Danh sách profiling_profile_ids

@apiParamExample {json} Body example
{
    "profiling_profile_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"]
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật deal thành công."
}
"""

####################################################################################################
# Gán company vào Deal
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/actions/add_company  Gán công ty vào Deal
@apiGroup Deal
@apiVersion 1.0.1
@apiName dealAddCompany

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam (Body) {Array} company_ids Danh sách company_id
@apiParam (Body) {string} [staff_id] Định danh nhân viên thực hiện thao tác

@apiParamExample {json} Body example
{
    "company_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"],
    "staff_id": ""
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật deal thành công."
}
"""

####################################################################################################
# Xóa công ty khỏi Deal
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/actions/remove_company Xóa công ty khỏi Deal
@apiGroup Deal
@apiVersion 1.0.1
@apiName dealRemoveCompany

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam (Body) {Array} company_ids Danh sách company_id
@apiParam (Body) {string} [staff_id] Định danh nhân viên thực hiện thao tác

@apiParamExample {json} Body example
{
    "company_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"],
    "staff_id" : "5da972766a6aae"
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật deal thành công."
}
"""

####################################################################################################
# Gán sản phẩm vào Deal
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/actions/add_product  Gán sản phẩm vào Deal
@apiGroup Deal
@apiVersion 1.0.1
@apiName dealAddProduct

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam (Body) {Array} product_ids Danh sách product_id

@apiParamExample {json} Body example
{
    "product_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"]
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật deal thành công."
}
"""

####################################################################################################
# Xóa sản phẩm khỏi Deal
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/actions/remove_product Xóa sản phẩm khỏi Deal
@apiGroup Deal
@apiVersion 1.0.1
@apiName dealRemoveProduct

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam (Body) {Array} product_ids Danh sách product_id

@apiParamExample {json} Body example
{
    "product_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"]
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật deal thành công."
}
"""

####################################################################################################
# Gán ticket vào Deal
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/actions/add_ticket  Gán ticket vào Deal
@apiGroup Deal
@apiVersion 1.0.1
@apiName dealAddTicket

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam (Body) {Array} ticket_ids Danh sách ticket_id

@apiParamExample {json} Body example
{
    "ticket_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"]
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật deal thành công."
}
"""

####################################################################################################
# Xóa sản phẩm khỏi Deal
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/actions/remove_ticket Xóa ticket khỏi Deal
@apiGroup Deal
@apiVersion 1.0.1
@apiName dealRemoveTicket

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam (Body) {Array} ticket_ids Danh sách ticket_id

@apiParamExample {json} Body example
{
    "ticket_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"]
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật deal thành công."
}
"""

####################################################################################################
# IMPORT ĐƠN HÀNG BẰNG FILE
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/actions/upload_excel  Upload File Excel danh sách deal
@apiGroup Deal
@apiVersion 1.0.1
@apiName DealUploadFile
@apiDeprecated  Sử dụng api Import deal by excel (#ImportDeal:ImportDealExcel)


@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse form_header

@apiParam (Form)        {file}         file             File Exel upload
@apiParam (Form)        {array}        rules            Index column file excel

@apiParam (rules)       {string}       field_key        Mã định danh của fields
@apiParam (rules)       {integer}      column_index     Vị trí cột trên file excel


@apiParamExample {json} rules example
{
    "rules": [
        {
                "column_index": 0
                "field_key":"ABC" 
        },
        {
                "column_index": 1
                "field_key":"BCD"
        }
    ]
}

@apiSuccessExample {json} Response
{   
    "code": 200,
    "message": "Cập nhật deal thành công."
}
"""
####################################################################################################
# Tổng số đơn hàng, doanh thu theo state
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/sale/api/v1.0/deals/insights/<sale_process_id>/analytic_sale_process Tổng số đơn hàng, doanh thu theo state của quy trình bán hàng
@apiGroup Deal
@apiVersion 1.0.1
@apiName analytic_sale_process

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apisuccess {Array}             state_code                          Mã code trạng thái đơn hàng
@apiSuccess {Integer}           total_deal                          Tổng số đơn hàng trong state
@apiSuccess {Integer}           total_value                         Tổng doanh thu của state

@apiSuccessExample {json} Response 
{
    "data": [
        {
            "state_code": "STT001",
            "total_deal":1000,
            "total_value":300000000
        },
        ...
    ]
    "code": 200,
    "message": "request thành công."
}

"""
####################################################################################################
# Tổng đơn hàng của quy trình
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/insights/total_in_process Tổng số đơn hàng của 1 quy trình 
@apiGroup Deal
@apiVersion 1.0.1
@apiName TotalDealInProcess

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)           {String}    sale_process_id           Quy trình bán hàng
@apiParam	(BODY:)			  {String}	  [search]			        Tìm kiếm đơn hàng theo tên đơn hàng
@apiParam   (BODY:)           {Array}     [sale_filters]            Danh sách các điều kiện lọc đơn hàng
@apiUse sale_filters

@apiSuccess {Integer}           total_deals                         Tổng số đơn hàng trong quy trình

@apiSuccessExample {json} Response 
{
    "total_deals":10000000,
    "code": 200,
    "message": "request thành công."
}

"""

####################################################################################################
# Un new deal
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/deals/actions/un_new Loại bỏ trạng thái đơn hàng mới
@apiGroup Deal
@apiVersion 1.0.1
@apiName dealUnNew

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam (Body) {Array} deal_ids Danh sách deal_id

@apiParamExample {json} Body example
{
    "deal_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"]
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật thành công."
}
"""

####################################################################################################
# Lấy danh sách Deal bới danh sách ids
# version: 1.0.1                                                                                   #
####################################################################################################
"""
@api {POST} {domain}/sale/api/v1.0/deals/list_by_ids  Danh sách đơn hàng bởi ids
@apiGroup Deal
@apiVersion 1.0.1
@apiName DealListByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apisuccess {Array}             data                          Danh sách đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiParam (Body) {Array} deal_ids Danh sách deal_id
@apiParam (Body) {Array} [fields] Danh sách field cần lấy dữ liệu

@apiParamExample {json} Body example
{
    "deal_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"]
}


@apiSuccessExample {json} Response
{
    "data": [
        {
            "name":"Tên đơn hàng 1",
            "code": "NGHK8385",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "sale_process_id":"5d7f150be6481e870a8ce0ad",
            "state_code" : "LEAD",
            "sale_value":  1569000,
            "description": "Mô tả đơn hàng",
            "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
            "estimate_time":"2019-11-26T12:00:00Z",
            "reason_success":"",
            "reason_fail":"",
            "profiles":[
                "45042df5-2202-4964-b05f-d53e21f5f895",
                "45042df5-2202-4964-b05f-d53e21f5f892"
            ],
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
            ],
            "tickets":[
                "45042df5-2202-4964-b05f-d53e21f5f890",
                "45042df5-2202-4964-b05f-d53e21f5f820"
            ],
            "companies":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f892"
            ],
            "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
            "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
            "updated":[
                {
                    "updated_time":"2019-11-26T12:00:00Z",
                    "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
                }
            ],
            "deal_new": 1, //1:mới, 0: hết mới,
            "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
        }
    ]
    "code": 200,
    "message": "request thành công."
}
"""

# Version 1.0.2
"""
@api {POST} {domain}/sale/api/v1.0/deals/list_by_ids  Danh sách đơn hàng bởi ids
@apiGroup Deal
@apiVersion 1.0.2
@apiName DealListByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apisuccess {Array}             data                          Danh sách đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiParam (Body) {Array} deal_ids Danh sách deal_id
@apiParam (Body) {Array} [fields] Danh sách field cần lấy dữ liệu

@apiParamExample {json} Body example
{
    "deal_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"]
}

@apiSuccess {Integer}   status  Trạng thái đơn hàng (status = 1 'Đang hoạt động', status = 2 'Lưu trữ')


@apiSuccessExample {json} Response
{
    "data": [
        {
            "name":"Tên đơn hàng 1",
            "code": "NGHK8385",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "sale_process_id":"5d7f150be6481e870a8ce0ad",
            "state_code" : "LEAD",
            "sale_value":  1569000,
            "description": "Mô tả đơn hàng",
            "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
            "estimate_time":"2019-11-26T12:00:00Z",
            "status": 1 // với 1 = đang hoạt động, 2 = Lưu trữ, 3 = Đang phân công
        }
    ]
    "code": 200,
    "message": "request thành công."
}

"""

# Version 1.0.3
"""
@api {POST} {domain}/sale/api/v1.0/deals/list_by_ids  Danh sách đơn hàng bởi ids
@apiGroup Deal
@apiVersion 1.0.3
@apiName DealListByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apisuccess {Array}             data                          Danh sách đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiParam (Body) {Array} deal_ids Danh sách deal_id
@apiParam (Body) {Array} [fields] Danh sách field cần lấy dữ liệu

@apiParamExample {json} Body example
{
    "deal_ids" : ["6466db1a60cbf136e1a856b1"]
}


@apiSuccess {Array}             data                          Danh sách đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "_dyn_trang_thai_phe_duyet_ho_so_yeu_cau_bao_hiem_1658911598189": null,
            "_id": "6466db1a60cbf136e1a856b1",
            "assignee_id": "de97719f-916e-4bcd-900b-94a142e4023e",
            "brand_ids": [
                "10350f12-1bd7-4369-9750-46d35c416a46"
            ],
            "code": "IZJSCNUF190523",
            "companies": [],
            "contract_time": "",
            "created_by": "de97719f-916e-4bcd-900b-94a142e4023e",
            "created_merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
            "created_time": "2023-05-19T02:12:42Z",
            "currency": "VNĐ",
            "customer_type": "ADD_CUSTOMER",
            "deal_new": 0,
            "description": null,
            "estimate_time": "",
            "id": "6466db1a60cbf136e1a856b1",
            "last_time_assign": **********.433851,
            "last_time_change_state": **********.433837,
            "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
            "name": "Test",
            "order": **********.364041,
            "pin_to_top": 0,
            "process_name": "Quy trình Chung",
            "product_categories": null,
            "product_line": "641ab777cd7e9a000c866d99",
            "product_sku_bank": [
                "NHBH_CN",
                "NHBH_PNT_CN",
                "PNT_CN4"
            ],
            "product_types": [
                "641ab777cd7e9a000c866d9e"
            ],
            "products_bank": "641ac0f2175caa000e362333",
            "profiles": [
                "96fa5331-5c59-43d3-8b7e-938fa8c78a0c"
            ],
            "reason_fail": null,
            "reason_success": null,
            "sale_process_id": "640709ac3e2247bcc1509a6c",
            "sale_value": null,
            "scope_code": null,
            "source": "Nhập thủ công",
            "state_code": "JXV95DZ2",
            "state_name": "Có thông tin Leads",
            "state_ratio": 10,
            "status": 1,
            "supporter_ids": [],
            "tag_ids": null,
            "team_id": "d2500fd4-76b1-11ed-8cad-aac18c65aa24",
            "tickets": [],
            "type_create": "MANUAL",
            "updated_by": "14b290ce-73d3-4ff9-8018-30daa672e386",
            "updated_merchant_id": null,
            "updated_time": "2023-05-19T02:20:27Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ----- Danh sách đơn hàng theo deal_ids rút gọn ----

# Version 1.0.3
"""
@api {POST} {domain}/sale/api/v1.0/deals/list_by_ids/shortened  Danh sách đơn hàng bởi ids rút gọn
@apiGroup Deal
@apiVersion 1.0.1
@apiName DealListByIdsShortened

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header

@apiParam (Body) {Array} deal_ids Danh sách ID đơn hàng. Tối đa (100 đơn hàng)

@apiParamExample {json} Body example
{
    "deal_ids": [
        "643e7a55e0d9f9b4eeb7f733",
        "643d130456eeee6541da4467"
    ]
}

@apiSuccess {Array}             data                          Danh sách đơn hàng (giới hạn các field: id, code, name, merchant_id)
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "code": "OXPXRBHH170423",
            "id": "643d130456eeee6541da4467",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "5465"
        },
        {
            "code": "BQUFLWLM180423",
            "id": "643e7a55e0d9f9b4eeb7f733",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "don hàng của phuong ( api)"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

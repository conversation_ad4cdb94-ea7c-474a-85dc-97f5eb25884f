#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: thong
    Company: M O B I O
    Date Created: 24/05/2023
"""
#--------- IMPORT ĐƠN HÀNG BẰNG EXCEL ----

# -- Define --
"""
@apiDefine SaleImportDealExcelAbac

@apiParam	(Form:)			{File}	    file		            File excel cần import
@apiParam	(Form:)			{Array}	    rules		            Mapping cột excel và field đơn hàng
@apiParam	(Form:)			{String}	rules.field_key		    Key định danh của field đơn hàng
@apiParam	(Form:)			{Number}	rules.column_index	    Vị trí cột trong file excel (Bắt đầu từ 0)
@apiParam	(Form:)			{String}	rules.module_name	    Tên module sở hữu field đấy
                                                                <ul>
                                                                    <li><code>sale</code> : Module Sale</li>
                                                                    <li><code>profiling</code> : Module profile</li>
                                                                    <li><code>company</code> : Module company</li>
                                                                </ul>

@apiParamExample {json} Form example
file: (binary)
rules: [
    {
        "field_key": "name",
        "column_index": 0,
        "module_name": "sale"
    },
    {
        "field_key": "_dyn_mo_ta_1576722642875",
        "column_index": 1,
        "module_name": "sale"
    },
    {
        "field_key": "phone_number",
        "column_index": 2,
        "module_name": "profiling"
    },
    {
        "field_key": "name",
        "column_index": 3,
        "module_name": "company"
    },
    {
        "field_key": "tax_identification_number",
        "column_index": 4,
        "module_name": "company"
    }
]

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
}

"""
# -- End Define --

# -- ABAC import đơn hàng bằng file excel --
"""
@api {POST} {domain}/sale/api/v1.0/deal/import/excel/abac-sale [ImportDeal] Thêm đơn hàng bằng file excel
@apiGroup ABAC-SALE
@apiVersion 1.0.2
@apiName AbacSaleImportDealExcel

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse SaleImportDealExcelAbac
"""

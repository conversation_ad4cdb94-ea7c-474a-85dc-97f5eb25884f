#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: thong
    Company: M O B I O
    Date Created: 23/05/2023
"""
# --------------------------------------------------
# THÊM MỚI ĐƠN HÀNG ABAC
# --------------------------------------------------

# -- DEFINE ---
"""
@apiDefine SaleDealAddAbac

@apiParam	(Body:)			{int}		[template]			  Version của base_field
@apiParam	(Body:)			{String}	merchant_id			  Id của merchant người tạo
@apiParam	(Body:)			{String}	name			      Tên đơn hàng
@apiParam	(Body:)			{String}	sale_process_id		  ID Quy trình bán hàng
@apiParam	(Body:)			{String}	state_code		      Mã code trạng thái đơn hàng
@apiParam	(Body:)			{Array}	    assignee		      Người phụ trách đơn hàng
@apiParam	(Body:)			{string}	assignee.assignee_id  ID người phụ trách
@apiParam	(Body:)			{string}	assignee.permission	  Quyền trên đơn hàng
                                                              <ul>
                                                                <li><code>owner</code> : Deal owner</li>
                                                                <li><code>supporter</code> : Deal supporter</li>
                                                              </ul>
@apiParam	(Body:)			{number}	[sale_value]		  Giá trị đơn hàng
@apiParam	(Body:)			{string}	[description]		  Mô tả
@apiParam	(Body:)			{datetime}	[estimate_time]		  Thời gian chốt đơn hàng
                                                              <code>Định dạng: dd/mm/yyyy </code>
@apiParam	(Body:)			{Array}	    profiles		      Danh sách profile 
@apiParam	(Body:)			{Array}	    [products]		      Danh sách sản phẩm   
@apiParam	(Body:)			{Array}	    [tickets]		      Danh sách ticket       
@apiParam	(Body:)			{Array}	    [companies]		      Danh sách công ty                                                   
@apiParam	(Body:)			{string}	[reason_success]	  Lý do chốt đơn thành công
@apiParam	(Body:)			{string}	[reason_fail]	      Lý do chốt đơn thất bại
@apiParam	(Body:)			{Integer}	ignore_duplicate	  <code>1- update kể cả trùng,0- gửi lại thông báo nếu trùng</code>

@apiParamExample {json} Body example
{
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "name":"Tên đơn hàng 1",
    "sale_process_id":"5d7f150be6481e870a8ce0ad",
    "state_code" : "LEAD",
    "sale_value":  1569000,
    "description": "Mô tả đơn hàng",
    "assignee": [
        {
            "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
            "permission": "owner"
        },
        {
            "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
            "permission": "owner"
        }
    ],
    "estimate_time":"24/12/2020",
    "reason_success":"",
    "reason_fail":"",
    "profiles":[
        "45042df5-2202-4964-b05f-d53e21f5f895",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894"
    ],
    "tickets":[
        "45042df5-2202-4964-b05f-d53e21f5f890",
        "45042df5-2202-4964-b05f-d53e21f5f820"
    ],
    "companies":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ]
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "data": {
        "_id":"5dde29fda2596203036b12c4",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "name":"Tên đơn hàng 1",
        "code": "NGHK8385",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "sale_process_id":"5d7f150be6481e870a8ce0ad",
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee": [
            {
                "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                "permission": "owner"
            },
            {
                "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                "permission": "owner"
            }
        ],
        "estimate_time":"2019-11-26T12:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
        "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
        "updated_time":"2019-11-26T12:00:00Z",
        "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
        "deal_new": 1, //1:mới, 0: hết mới,
        "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
    },
    "code": 200,
    "message": "request thành công."
}

@apiSuccessExample {json} Response duplicate deal
{
     "data": [
    	{
    		"name":"Trư bát giới",
    		"code":"ZECQQQ271219",
    		"assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895","45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    	{
    		"name":"Đường tăng",
    		"code":"GCFJIR271219",
    		"assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891"]
    	},
    	{
    		"name":"Bạch cốt tinh",
    		"code":"VXWTED271219",
            "assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	},
    ]
    "code": 413,
    "message": "i18n_message_valid_product_profiles_order_exist"
}

"""
# -- End Define --

# -- ABAC Thêm mới đơn hàng từ Sale --

"""
@api {POST} {domain}/sale/api/v1.0/deals/action/add/abac-sale  [AddDeal] - Thêm mới đơn hàng từ module Sale
@apiDescription ABAC - Thêm mới đơn hàng từ màn hình module Sale
@apiGroup ABAC-SALE
@apiVersion 1.0.3
@apiName AbacSaleDealAddV2

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiUse     SaleDealAddAbac

"""

# -- ABAC thêm mới đơn hàng từ profile --

"""
@api {POST} {domain}/sale/api/v1.0/deals/action/add/abac-profile  [AddDeal] - Thêm mới đơn hàng từ Profile
@apiDescription ABAC - Thêm mới đơn hàng từ cột 3 profile
@apiGroup ABAC-SALE
@apiVersion 1.0.3
@apiName AbacProfileDealAddV2

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiUse     SaleDealAddAbac

"""

# -- ABAC thêm mới đơn hàng từ Công ty --

"""
@api {POST} {domain}/sale/api/v1.0/deals/action/add/abac-company  [AddDeal] - Thêm mới đơn hàng từ Company
@apiDescription ABAC - Thêm mới đơn hàng từ cột 3 chi tiết công ty
@apiGroup ABAC-SALE
@apiVersion 1.0.3
@apiName AbacCompanyDealAddV2

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiUse     SaleDealAddAbac

"""

# -- ABAC thêm mới đơn hàng từ Ticket --

"""
@api {POST} {domain}/sale/api/v1.0/deals/action/add/abac-ticket  [AddDeal] - Thêm mới đơn hàng từ Ticket
@apiDescription ABAC - Thêm mới đơn hàng từ cột 3 chi tiết Ticket
@apiGroup ABAC-SALE
@apiVersion 1.0.3
@apiName AbacTicketDealAddV2

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiUse     SaleDealAddAbac

"""

# -- ABAC thêm mới đơn hàng từ Tổng đài --

"""
@api {POST} {domain}/sale/api/v1.0/deals/action/add/abac-call-center  [AddDeal] - Thêm mới đơn hàng từ tổng đài
@apiDescription ABAC - Thêm mới đơn hàng từ popup tổng đài
@apiGroup ABAC-SALE
@apiVersion 1.0.3
@apiName AbacCallCenterDealAddV2

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiUse     SaleDealAddAbac

"""

# -- ABAC thêm mới đơn hàng từ Social --

"""
@api {POST} {domain}/sale/api/v1.0/deals/action/add/abac-social  [AddDeal] - Thêm mới đơn hàng từ Social
@apiDescription ABAC - Thêm mới đơn hàng từ Social
@apiGroup ABAC-SALE
@apiVersion 1.0.3
@apiName AbacSocialDealAddV2

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiUse     SaleDealAddAbac

"""

# --------------------------------------------------
# XEM CHI TIẾT ĐƠN HÀNG ABAC
# --------------------------------------------------

# -- DEFINE --
"""
@apiDefine SaleDealDetailAbac

@apiParam	(PARAMS:)			{Array}	    [fields]	      Danh sách thuộc tính cần trả. 
                                                              VD: ["name","code"]. Trường hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính

@apisuccess {Array}             data                          Thông tin chi tiết của đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apisuccess {Array}             data.product_categories       Danh sách danh mục sản phẩm

@apiSuccessExample {json} Response detail deal
{
    "data": {
        "_id": uuid,
        "name": "Tên của đơn hàng đang cần lấy thông tin chi tiết",
        "code": "NGHK8385",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "sale_process_id":"45042df5-2202-4964-b05f-d53e21f5f896",
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee": [
            {
                "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                "permission": "owner"
            },
            {
                "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                "permission": "owner"
            }
        ],
        "estimate_time":"2019-11-26T12:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "product_categories": [
            "5eafe2b8e2a37eb592100e4b",
            "5eafe2f4ae89793519483d10"
        ],
        "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
        "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
        "updated_time":"2019-11-26T12:00:00Z",
        "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
        "deal_new": 1, //1:mới, 0: hết mới,
        "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động,
        "status" : 1 // 1: Active; -1: Delete
    }
    "code": 200,
    "message": "request thành công."
}
"""
# -- END DEFINE --

# -- Xem chi tiết đơn hàng từ Sale --
"""
@api {GET} {domain}/sale/api/v1.0/deals/<deal_id>/detail/abac-sale [DealDetail] Chi tiết đơn hàng từ Sale
@apiGroup ABAC-SALE
@apiVersion 1.0.2
@apiName DealDetailAbacSale

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse SaleDealDetailAbac
"""

# -- Xem chi tiết đơn hàng từ Profile
"""
@api {GET} {domain}/sale/api/v1.0/deals/<deal_id>/detail/abac-profile [DealDetail] Chi tiết đơn hàng từ Profile
@apiGroup ABAC-SALE
@apiVersion 1.0.2
@apiName DealDetailAbacProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse SaleDealDetailAbac
"""

# -- Xem chi tiết đơn hàng từ Công ty
"""
@api {GET} {domain}/sale/api/v1.0/deals/<deal_id>/detail/abac-company [DealDetail] Chi tiết đơn hàng từ công ty 
@apiGroup ABAC-SALE
@apiVersion 1.0.2
@apiName DealDetailAbacCompany

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse SaleDealDetailAbac
"""

# -- Xem chi tiết đơn hàng từ Ticket
"""
@api {GET} {domain}/sale/api/v1.0/deals/<deal_id>/detail/abac-ticket [DealDetail] Chi tiết đơn hàng từ Ticket 
@apiGroup ABAC-SALE
@apiVersion 1.0.2
@apiName DealDetailAbacTicket

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse SaleDealDetailAbac
"""

# -- Xem chi tiết đơn hàng từ Module khác
"""
@api {GET} {domain}/sale/api/v1.0/deals/<deal_id>/detail/abac-other [DealDetail] Chi tiết đơn hàng từ module khác
@apiGroup ABAC-SALE
@apiVersion 1.0.2
@apiName DealDetailAbacOther

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse SaleDealDetailAbac
"""

# ---------------------------------------------------
# Sửa đơn hàng ABAC
# ---------------------------------------------------

# -- DEFINE --
"""
@apiDefine SaleDealUpdateAbac

@apiParam   (Body:)         {String}    [type_update]           <code>'default'- update deal chi tiết, 'quick'- update deal nhanh</code>
@apiParam   (Body:)         {int}       [template]            Version của base_field
@apiParam	(Body:)			{Array}	    assignee		      Người phụ trách đơn hàng
@apiParam	(Body:)			{string}	assignee.assignee_id  ID người phụ trách
@apiParam	(Body:)			{string}	assignee.permission	  Quyền trên đơn hàng
                                                              <ul>
                                                                <li><code>owner</code> : Deal owner</li>
                                                                <li><code>supporter</code> : Deal supporter</li>
                                                              </ul>
@apiParam   (Body:)         {String}    state_code              Mã code trạng thái đơn hàng
@apiParam   (Body:)         {number}    sale_value              Giá trị đơn hàng
@apiParam   (Body:)         {string}    description             Mô tả
@apiParam   (Body:)         {string}    assignee_id             ID người phụ trách đơn hàng
@apiParam   (Body:)         {datetime}  estimate_time           Thời gian chốt đơn hàng
                                                                 <code>Định dạng: dd/mm/yyyy </code>
@apiParam   (Body:)         {string}    reason_success          Lý do chốt đơn thành công
@apiParam   (Body:)         {string}    reason_fail             Lý do chốt đơn thất bại
@apiParam   (Body:)         {Array}     profiles                Danh sách profile
@apiParam   (Body:)         {Array}     products                Danh sách sản phẩm
@apiParam   (Body:)         {Array}     tickets                 Danh sách ticket
@apiParam   (Body:)         {Array}     companies               Danh sách công ty
@apiParam   (Body:)         {Array}     product_categories      Danh sách danh mục sản phẩm
@apiParam   (Body:)         {Integer}   ignore_duplicate        <code>1- update kể cả trùng,0- gửi lại thông báo nếu trùng</code>

@apiParamExample {json} Body example
{
    "name":"Tên đơn hàng 1",
    "ignore_duplicate":1,
    "sale_process_id":"5d7f150be6481e870a8ce0ad",
    "state_code" : "LEAD",
    "sale_value":  1569000,
    "description": "Mô tả đơn hàng",
    "assignee": [
        {
            "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
            "permission": "owner"
        },
        {
            "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
            "permission": "owner"
        }
    ],
    "estimate_time":"24/12/2022",
    "reason_success":"",
    "reason_fail":"",
    "profiles":[
        "45042df5-2202-4964-b05f-d53e21f5f895",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894"
    ],
    "tickets":[
        "45042df5-2202-4964-b05f-d53e21f5f890",
        "45042df5-2202-4964-b05f-d53e21f5f820"
    ],
    "companies":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f892"
    ],
    "product_categories": [
        "5eafe2b8e2a37eb592100e4b",
        "5eafe2f4ae89793519483d10"
    ]
    type_update:"quick"
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {datetime}          estimate_time           Thời gian chốt đơn hàng
                                                                 <code>Định dạng: "yyyy-mm-dd-Th:m:sZ" </code>
@apiSuccessExample {json} Response success
{
    "code":200,
    "data":{
        "id": "5de61bb97dfdcfe3cbb5d89e",
        "name":"Tên đơn hàng 1",
        "ignore_duplicate":1,
        "assignee": [
            {
                "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                "permission": "owner"
            },
            {
                "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                "permission": "owner"
            }
        ],
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
        "estimate_time":"2022-12-24T00:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "product_categories": [
            "5eafe2b8e2a37eb592100e4b",
            "5eafe2f4ae89793519483d10"
        ],
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ]
    },
    "lang": "vi",
    "message": "request thành công."

}

@apiSuccessExample {json} Response duplicate deal
{
    "data": [
    	{
    		"name":"Trư bát giới",
    		"code":"ZECQQQ271219",
            "assignee": [
                {
                    "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                    "permission": "owner"
                },
                {
                    "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                    "permission": "owner"
                }
            ],
    		"created_time":"2019-11-26T12:00:00Z",
    		"profiles":["45042df5-2202-4964-b05f-d53e21f5f895","45042df5-2202-4964-b05f-d53e21f5f892"],
    		"products":["45042df5-2202-4964-b05f-d53e21f5f891","45042df5-2202-4964-b05f-d53e21f5f894"]
    	}
    ]
    "code": 413,
    "message": "i18n_message_valid_product_profiles_order_exist"
}
"""
# -- END DEFINE --

# --- Sửa nhanh từ module Sale --
"""
@api {PUT} {domain}/sale/api/v1.0/deals/<deal_id>/update/quick/abac-sale [UpdateDeal] Đơn hàng - Sửa nhanh từ module Sale
@apiGroup ABAC-SALE
@apiVersion 1.0.5
@apiName DealUpdateV2QuickAbacSale

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiUse SaleDealUpdateAbac
"""

# --- Sửa tất cả từ module Sale --
"""
@api {PUT} {domain}/sale/api/v1.0/deals/<deal_id>/update/all/abac-sale [UpdateDeal] Đơn hàng - Sửa tất cả từ module Sale
@apiGroup ABAC-SALE
@apiVersion 1.0.5
@apiName DealUpdateV2AllAbacSale

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiUse SaleDealUpdateAbac
"""

# ------------------------
# Xóa đơn hàng
# ------------------------

# --- DEFINE ----
"""
@apiDefine DeleteDealAbac

@apiParam	(PARAMS:)			{String}	    deal_ids			  Danh sách ID đơn hàng, id đơn hàng cách nhau bởi dấu <code>;</code>

@apiParamExample {String} Param example
/deals?deal_ids=5dde29fda2596203036b12c4;5dde29fda2596203036b12c2

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}           data_delete                     Danh sách deal_ids xóa thành công

@apiSuccessExample {json} Response
{
    "code": 200,
    "data_delete" : [
        "5dde43d3424812febe79ed18",
        "5dde43d3424812febe79ed01"
    ],
    "message": "request thành công."
}
"""
# --- END DEFINE ---

"""
@api {DELETE} {domain}/sale/api/v1.0/deals/abac [DeleteDeal] - Xóa đơn hàng
@apiGroup ABAC-SALE
@apiVersion 1.0.1
@apiName DealDeleteAbac

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse DeleteDealAbac
"""

# --------------------------------------------------
# XEM NHANH TỪ CÁC MODULE KHÁC
# --------------------------------------------------

# -- DEFINE --
"""
@apiDefine SaleDealQuickDetailAbac

@apiParam	(PARAMS:)			{Array}	    [fields]	      Danh sách thuộc tính cần trả cách nhau bởi dấu , 
                                                              VD: name,code
                                                              <ul>
                                                                <li>Không gửi : Mặc định trả về name, code, id </li>
                                                                <li>-1 : Trả về all field </li>
                                                              </ul>
                                                              
@apisuccess {Array}             data                          Thông tin chi tiết của đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response detail deal
{
    "data": {
        "_id": uuid,
        "name": "Tên của đơn hàng đang cần lấy thông tin chi tiết",
        "code": "NGHK8385",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "sale_process_id":"45042df5-2202-4964-b05f-d53e21f5f896",
        "state_code" : "LEAD",
        "sale_value":  1569000,
        "description": "Mô tả đơn hàng",
        "assignee": [
            {
                "assignee_id": "45042df5-2202-4964-b05f-d53e21f5f895",
                "permission": "owner"
            },
            {
                "assignee_id": "c56fab40-e099-448c-95c3-211d6d5b2b8d",
                "permission": "owner"
            }
        ],
        "estimate_time":"2019-11-26T12:00:00Z",
        "reason_success":"",
        "reason_fail":"",
        "profiles":[
            "45042df5-2202-4964-b05f-d53e21f5f895",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "tickets":[
            "45042df5-2202-4964-b05f-d53e21f5f890",
            "45042df5-2202-4964-b05f-d53e21f5f820"
        ],
        "companies":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f892"
        ],
        "product_categories": [
            "5eafe2b8e2a37eb592100e4b",
            "5eafe2f4ae89793519483d10"
        ],
        "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
        "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
        "updated_time":"2019-11-26T12:00:00Z",
        "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
        "deal_new": 1, //1:mới, 0: hết mới,
        "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động,
        "status" : 1 // 1: Active; -1: Delete
    }
    "code": 200,
    "message": "request thành công."
}
"""
# -- END DEFINE --

# - Xem nhanh chi tiết đơn hàng từ module khác
"""
@api {GET} {domain}/sale/api/v1.0/deals/<deal_id>/detail/quick/abac-other [DealQuickDetail] Xem nhanh chi tiết đơn hàng theo id 
@apiGroup ABAC-SALE
@apiVersion 1.0.2
@apiName DealDetailQuickAbacOther

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiUse SaleDealQuickDetailAbac
"""
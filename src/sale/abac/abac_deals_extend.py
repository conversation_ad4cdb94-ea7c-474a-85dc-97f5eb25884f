#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: thong
    Company: M O B I O
    Date Created: 25/05/2023
"""
# ------------------------------------------
# ABAC - <PERSON>h sách đơn hàng theo merchant_id
# ------------------------------------------

# -- DEFINE --
"""
@apiDefine SaleDealListMerchantAbac

@apiParam	(BODY:)			{string}	[search]		      Chuỗi tìm kiếm theo tên đơn hàng
@apiParam	(BODY:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả. 
                                                              VD: ["name","code"]. Tr<PERSON><PERSON><PERSON> hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính
@apiParam	(BODY:)			{Array}	    [ignore_ids]	      Danh sách Id đơn hàng không trả về

@apiParamExample {json} Body example
{   
    "search": "",
    "fields": [
        "name",
        "state_code",
        "_dyn_quan_huyen_1638763459405",
        "_dyn_phuong_xa_1647601215895",
        "merchant_id",
        "assignee"
    ],
    "ignore_ids": [
         "61d65f0ead7de0e2c9191a67", "61d65f0ead7de0e2c9191a67"
    ]
}


@apisuccess {Array}             data                          Danh sách đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "data": [
        {
            "_id": "61d65f0fad7de0e2c9191a69",
            "assignee": [
                {
                    "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "permission": "owner"
                },
                {
                    "assignee_id": "16ba7c74-4405-4840-9389-888326e195ad",
                    "permission": "supporter"
                }
            ],
            "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "currency": "VNĐ",
            "id": "61d65f0fad7de0e2c9191a69",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "Thêm bằng file - Đơn 5",
            "process_name": "Quy trình của THONGNV",
            "sale_process_id": "61c30d5ed6e33b2a7670c602",
            "state_code": "RZBREEXX",
            "state_name": "Có thông tin Leads",
            "state_ratio": 10,
            "supporter_ids": [
                "16ba7c74-4405-4840-9389-888326e195ad"
            ]
        },
        {
            "_id": "62724e226cebec3242796bf6",
            "assignee": [
                {
                    "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "permission": "owner"
                }
            ],
            "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "currency": "VNĐ",
            "id": "62724e226cebec3242796bf6",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "thanh 118",
            "process_name": "Quy trình của THONGNV",
            "sale_process_id": "61c30d5ed6e33b2a7670c602",
            "state_code": "RZBREEXX",
            "state_name": "Có thông tin Leads",
            "state_ratio": 10,
            "supporter_ids": []
        },
        {
            "_id": "62737a3956827c900d93b09b",
            "assignee": [
                {
                    "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "permission": "owner"
                },
                {
                    "assignee_id": "16ba7c74-4405-4840-9389-888326e195ad",
                    "permission": "supporter"
                }
            ],
            "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "currency": "VNĐ",
            "id": "62737a3956827c900d93b09b",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "đơn Testertest 2275 05/05/2022 14:18",
            "process_name": "Quy trình của THONGNV",
            "sale_process_id": "61c30d5ed6e33b2a7670c602",
            "state_code": "3DPEIPK4",
            "state_name": "Thành công",
            "state_ratio": 100,
            "supporter_ids": [
                "16ba7c74-4405-4840-9389-888326e195ad"
            ]
        },
        {
            "_id": "6273849b7553eb894b9cf55e",
            "assignee": [
                {
                    "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                    "permission": "owner"
                },
                {
                    "assignee_id": "16ba7c74-4405-4840-9389-888326e195ad",
                    "permission": "supporter"
                }
            ],
            "assignee_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "currency": "VNĐ",
            "id": "6273849b7553eb894b9cf55e",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "name": "Hà mã tấu 3",
            "process_name": "Quy trình của THONGNV",
            "sale_process_id": "61c30d5ed6e33b2a7670c602",
            "state_code": "ESKWS7Q0",
            "state_name": "Liên lạc",
            "state_ratio": 20,
            "supporter_ids": [
                "16ba7c74-4405-4840-9389-888326e195ad"
            ]
        },
    ]
    "code": 200,
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "WzE2NzMyNTE4NTYuODM0NDE1LCAiNjNiYmNjMTFjOWEzNzZhMzQyOWNkZWQ1Il0=",
            "before": ""
        },
        "page_count": 1460,
        "per_page": 10,
        "total_count": 14591
    }
}

"""
# -- END DEFINE --

"""
@api {POST} {domain}/sale/api/v1.0/internal/deals/actions/merchant_ids/abac-profile [ListDeal] - Danh sách đơn hàng khi search tại profile
@apiGroup ABAC-SALE
@apiVersion 1.0.1
@apiName DealsByMerchantIdsAbacProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header
@apiUse paging_tokens

@apiUse SaleDealListMerchantAbac
"""

"""
@api {POST} {domain}/sale/api/v1.0/internal/deals/actions/merchant_ids/abac-company  [ListDeal] - Danh sách đơn hàng khi search tại company
@apiGroup ABAC-SALE
@apiVersion 1.0.1
@apiName DealsByMerchantIdsAbacCompany

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header
@apiUse paging_tokens

@apiUse SaleDealListMerchantAbac
"""

"""
@api {POST} {domain}/sale/api/v1.0/internal/deals/actions/merchant_ids/abac-ticket  [ListDeal] - Danh sách đơn hàng khi search tại ticket
@apiGroup ABAC-SALE
@apiVersion 1.0.1
@apiName DealsByMerchantIdsAbacTicket

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header
@apiUse paging_tokens

@apiUse SaleDealListMerchantAbac
"""

"""
@api {POST} {domain}/sale/api/v1.0/internal/deals/actions/merchant_ids/abac-related  [ListDeal] - Danh sách đơn hàng khi search gắn liên quan tới
@apiGroup ABAC-SALE
@apiVersion 1.0.1
@apiName DealsByMerchantIdsAbacRelated

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header
@apiUse paging_tokens

@apiUse SaleDealListMerchantAbac
"""

# -----------------------------------------------------
# Lấy danh sách đơn hàng theo danh sách profile_ids
# -----------------------------------------------------

# -- DEFINE --
"""
@apiDefine DealGetProfileIdsAbac

@apiParam	(BODY:)			{Array}	    profile_ids		      Danh sách profile_id
@apiParam	(BODY:)			{string}	search		          Chuỗi tìm kiếm theo tên đơn hàng
@apiParam	(BODY:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả. 
                                                              VD: ["name","code"]. Trường hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính

@apiParamExample {json} Body example
{
    "search": "tên đơn hàng 1",
    "profile_ids": [
        {
            "profile_id": "0e551516-25a5-4d5b-9946-5ec4a4bc3144",
            "merchant_id": ["1b99bdcf-d582-4f49-9715-1b61dfff3924"]
        },
        {
            "profile_id": "1e551516-25a5-4d5b-9946-5ec4a4bc3122",
            "merchant_id": ["1b99bdcf-d582-4f49-9715-1b61dfff3924"]
        }
    ],
    "fields": [
        "code",
        "name"
    ]
}


@apisuccess {Array}             data                          Danh sách đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Integer}           status                        Trạng thái đơn hàng (1 = đang hoạt động, 2 = lưu trữ)

@apiSuccessExample {json} Response 
{
    "data": [
        {
            "name":"Tên đơn hàng 1",
            "code": "NGHK8385",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "sale_process_id":"5d7f150be6481e870a8ce0ad",
            "state_code" : "LEAD",
            "sale_value":  1569000,
            "description": "Mô tả đơn hàng",
            "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
            "estimate_time":"2019-11-26T12:00:00Z",
            "reason_success":"",
            "reason_fail":"",
            "profiles":[
                "45042df5-2202-4964-b05f-d53e21f5f895",
                "45042df5-2202-4964-b05f-d53e21f5f892"
            ],
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
            ],
            "tickets":[
                "45042df5-2202-4964-b05f-d53e21f5f890",
                "45042df5-2202-4964-b05f-d53e21f5f820"
            ],
            "companies":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f892"
            ],
            "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
            "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
            "updated":[
                {
                    "updated_time":"2019-11-26T12:00:00Z",
                    "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
                }
            ],
            "deal_new": 1, //1:mới, 0: hết mới,
            "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
        }
    ]
    "code": 200,
    "message": "request thành công."
}
"""
# -- END DEFINE --

"""
@api {POST} {domain}/sale/api/v1.0/deals/actions/list/get/profile_ids/abac [ListQuick] - Danh sách đơn hàng theo profile
@apiGroup ABAC-SALE
@apiVersion 1.0.2
@apiName DealListProfileAbac

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiUse DealGetProfileIdsAbac
"""

# ---------------------------------------------------
# ABAC - Lấy danh sách đơn hàng theo danh sách ticket
# ---------------------------------------------------

# -- DEFINE --
"""
@apiDefine DealGetTicketAbac

@apiParam	(BODY:)			{Array}	    tickets		          Danh sách ticket
@apiParam	(BODY:)			{string}	search		          Chuỗi tìm kiếm theo tên đơn hàng
@apiParam	(BODY:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả. 
                                                              VD: ["name","code"]. Trường hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính

@apiParamExample {json} Body example
{
    "search": "tên đơn hàng 1",
    "tickets": [
        {
            "ticket_id": "0e551516-25a5-4d5b-9946-5ec4a4bc3144",
            "merchant_id": ["1b99bdcf-d582-4f49-9715-1b61dfff3924"]
        },
        {
            "ticket_id": "1e551516-25a5-4d5b-9946-5ec4a4bc3122",
            "merchant_id": ["1b99bdcf-d582-4f49-9715-1b61dfff3924"]
        }
    ],
    "fields": [
        "code",
        "name"
    ]
}

@apisuccess {Array}             data                          Danh sách đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response 
{
    "data": [
        {
            "name":"Tên đơn hàng 1",
            "code": "NGHK8385",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "sale_process_id":"5d7f150be6481e870a8ce0ad",
            "state_code" : "LEAD",
            "sale_value":  1569000,
            "description": "Mô tả đơn hàng",
            "assignee_id":"45042df5-2202-4964-b05f-d53e21f5f895",
            "estimate_time":"2019-11-26T12:00:00Z",
            "reason_success":"",
            "reason_fail":"",
            "profiles":[
                "45042df5-2202-4964-b05f-d53e21f5f895",
                "45042df5-2202-4964-b05f-d53e21f5f892"
            ],
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
            ],
            "tickets":[
                "45042df5-2202-4964-b05f-d53e21f5f890",
                "45042df5-2202-4964-b05f-d53e21f5f820"
            ],
            "companies":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f892"
            ],
            "created_time":"2019-11-26T12:00:00Z", // Thời gian tạo
            "created_by":"45042df5-2202-4964-b05f-d53e21f5f895", // Người tạo
            "updated":[
                {
                    "updated_time":"2019-11-26T12:00:00Z",
                    "updated_by":"45042df5-2202-4964-b05f-d53e21f5f895"
                }
            ],
            "deal_new": 1, //1:mới, 0: hết mới,
            "type_create": "MANUAL" //MANUAL: Tạo bằng tay, AUTO: tạo tự động
        }
    ]
    "code": 200,
    "message": "request thành công."
}
"""
# -- END DEFINE --

"""
@api {POST} {domain}/sale/api/v1.0/deals/actions/list/get/tickets/abac [ListQuick] - Danh sách đơn hàng theo ticket
@apiGroup ABAC-SALE
@apiVersion 1.0.1
@apiName DealListTicketAbac

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens
@apiUse DealGetTicketAbac

"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: thong
    Company: M O B I O
    Date Created: 29/05/2023
"""

# ----- DEAL EVENT ----

# -- DEFINE --
"""
@apiDefine DealEventAbac

@apiParam   (QUERY:) {string=EMAIL,SOCIAL,SMS,CALLCENTER,OTHER}                  [source_event]         Tab muốn lấy danh sách event. Mặc định khi đứng ở tab <code>Tất cả</code> thì không cần truyền lên giá trị này.
                                                                                                        <ul>
                                                                                                            <li><code>EMAIL</code>: Tab Email</li>
                                                                                                            <li><code>SOCIAL</code>: Tab Online và mạng xã hội</li>
                                                                                                            <li><code>SMS</code>: Tab SMS</li>
                                                                                                            <li><code>CALLCENTER</code>: Tab cuộc gọi</li>
                                                                                                            <li><code>OTHER</code>: Tab khác</li>
                                                                                                        </ul> 

@apiParam   (BODY:) {Array}                     brand_ids              Danh sách các thương hiệu cần tìm kiếm
@apiParam   (BODY:) {Array}                     staff_ids              Danh sách nhân viên tương tác với Profile
@apiParam   (BODY:) {string}                    start_time             Thời gian bắt đầu tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {string}                    end_time               Thời gian kết thúc tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {Array}                     activities             Danh sách hoạt động cần tìm kiếm
                                                                        <ul>
                                                                            <li><code>UpdateDeal</code>: Cập nhật thông tin đơn hàng</li>
                                                                            <li><code>AssignmentForwardDeal</code>: Phân công chuyển tiếp đơn hàng</li>
                                                                            <li><code>Email</code>: E-mail</li>
                                                                            <li><code>Facebook</code>: Tương tác qua Facebook</li>
                                                                            <li><code>Zalo</code>: Tương tác qua Zalo</li>
                                                                            <li><code>SMS</code>: SMS</li>
                                                                            <li><code>Callcenter</code>: Gọi điện</li>
                                                                            <li><code>Other</code>: Khác</li>
                                                                        </ul>

@apiParamExample {json} Body example
{
    "brand_ids": [].
    "staff_ids": [],
    "start_time": "2021-10-15",
    "end_time": "2022-10-15",
    "activity": ["UpdateDeal"]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách các Event tương ứng.

@apiSuccess {String}            data.merchant_id              Định danh merchant.
@apiSuccess {String}            data.deal_id                  Định danh đơn hàng.
@apiSuccess {String}            data.event_id                 Định danh Event 
@apiSuccess {String}            data.source_event               Loại Event
                                                              <ul>
                                                                    <li><code>DEAL</code>: Loại event phát sinh của đơn hàng</li>
                                                                    <li><code>EMAIL</code>: Loại event E-mail</li>
                                                                    <li><code>SOCIAL</code>: Loại event phát sinh từ mạng xã hội</li>
                                                                    <li><code>SMS</code>: Loại event phát sinh từ SMS</li>
                                                                    <li><code>CALLCENTER</code>: Loại event phát sinh từ Gọi điện</li>
                                                                    <li><code>OTHER</code>: Khác</li>
                                                              </ul>
@apiSuccess {String}            data.event_type             Loại của chi tiết Event
                                                            <ul>Source event là <code>DEAL</code>
                                                                <li><code>InitDeal</code>: Tạo đơn hàng</li>
                                                                <li>
                                                                    <code>UpdateProfile</code>: Cập nhật liên hệ profile và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li>
                                                                    <code>UpdateCompanies</code>: Cập nhật liên hệ công ty và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li><code>UpdateTicket</code>: Cập nhật liên hệ ticket và đơn hàng</li>
                                                                <li><code>AddProductCategoriesMain</code>: Gắn liên hệ đơn hàng và danh mục chính</li>
                                                                <li><code>AddProductCategoriesSub</code>: Gắn liên hệ đơn hàng và danh mục phụ</li>
                                                                <li><code>RemoveProductCategoriesMain</code>: Gỡ liên hệ đơn hàng và danh mục chính</li>
                                                                <li><code>AddProductBank</code>: Gắn liên hệ giữa cơ hội bán và sản phẩm bank</li>
                                                                <li><code>RemoveProductBank</code>: Gỡ liên hệ giữa cơ hội bán và sản phẩm bank</li>
                                                                <li><code>AddProductLine</code>: Gắn liên hệ giữa cơ hội bán và dòng sản phẩm</li>
                                                                <li><code>RemoveProductLine</code>: Gỡ liên hệ giữa cơ hội bán và dòng sản phẩm</li>
                                                                <li><code>AddProductType</code>: Gắn liên hệ giữa cơ hội bán và loại sản phẩm</li>
                                                                <li><code>RemoveProductType</code>: Gỡ liên hệ giữa cơ hội bán và loại sản phẩm</li>
                                                                <li>
                                                                    <code>UpdateMediaDeal</code>: Cập nhật liên hệ media và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                            </ul>
                                                            <ul>Source event là <code>SOCIAL</code>
                                                                <li><code>1</code>: "Facebook"</li>
                                                                <li><code>2</code>: "Zalo"</li>
                                                                <li><code>3</code>: "Instagram"</li>
                                                                <li><code>4</code>: "Youtube"</li>
                                                                <li><code>5</code>: "App"</li>
                                                                <li><code>6</code>: "Line"</li>
                                                                <li><code>7</code>: "Mobio_chat_tool"</li>
                                                            </ul>
                                                            <ul>Source event là <code>CALLCENTER</code></ul>



@apiSuccess {String}            data.staff_update_deal        Định danh nhân viên cập nhật thông tin
@apiSuccess {String}            data.line_event               Event tạo từ hệ thống hoặc người dùng: merchant, profile
@apiSuccess {String}            data.body                     Thông tin của Event
@apiSuccess {Object}            data.action_time              Thời gian diễn ra Event
@apiSuccess {Object}            data.created_time             Thời điểm tạo


@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateDeal
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+84323456789",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : 1635230358.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "information": [
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "description",
                "field_name": "description",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [
                    "333"
                ],
                "change": [],
                "remove": []
            },
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "reason_fail",
                "field_name": "reason_fail",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [],
                "change": [
                    {
                        "from": "buồn quá không mua",
                        "to": "buồn quá không mua a"
                    }
                ],
                "remove": []
            }
        ],
        "dynamic": [
            {
                "field_name": "Radio button",
                "field_key": "_dyn_radio_button_1633599591661",
                "field_property": 2,
                "display_type": "radio",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    "ba"
                ],
                "change": [],
                "remove": []
            },
            {
                "field_name": "Single-line text chữ",
                "field_key": "_dyn_single_line_text_chu_1631458349843",
                "field_property": 2,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [],
                "change": [
                    {
                        "from": "123123123123",
                        "to": "12312312399"
                    }
                ],
                "remove": []
            },
            {
                "field_name": "Single-line text số",
                "field_key": "_dyn_single_line_text_so_1631181218494",
                "field_property": 1,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    12312312
                ],
                "change": [],
                "remove": []
            }
        ]
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateProfile
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+84323456789",
        "username" : "admin@pingcomshop"
    },
    "source_event": "DEAL",
    "event_type": "UpdateProfile",
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : 1635230358.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateTicket
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateTicket",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+84323456789",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : 1635230358.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateCompanies
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateCompanies",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+84323456789",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : 1635230358.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}
@apiSuccessExample {json} Response source_event: DEAL, event_type: InitDeal
{
    "_id" : "6177a299ffdea8e0c58fe305",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "6177a296573c1357e0a67460",
    "event_id" : "6177a298ffdea8e0c58fe303",
    "source_event" : "DEAL",
    "event_type" : "InitDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+84323456789",
        "username" : "admin@pingcomshop"
    },
    "line_event" : "merchant",
    "source" : "SALE",
    "action_time" : 1635230358.890302,
    "created_time" : "2021-10-15T15:05:18.856",
    "body" : {
        "profiles" : [
            {
                "id" : "6afa7308-ccf3-4974-8478-3a0f4b44ade0",
                "name" : "Cam Edit"
            }
        ],
        "assignee_id" : "01aa5dc3-36a6-42b1-b026-e7cdba3751cc",
        "state_code" : "QQ6CYXWM",
        "sale_process_id" : "61768dad74f73e51a119ea2b",
        "sale_process_name" : "Quy trình hoa hướng dương (không sửa xóa)",
        "state_name" : "Có thông tin Leads",
        "state_ratio" : 10
    }
}

@apiSuccessExample {json} Response source_event: SOCIAL
{
    "_id" : "61790f31df1001de4bd72334",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "be8bde12-c514-4788-bef8-7a85ff0d2b55",
    "event_id" : ObjectId("61790f28df1001de4bd72332"),
    "source_event" : "SOCIAL",
    "event_type" : 1,
    "staff_update_deal" : {
        "id" : "823ed8f0-83b2-491d-936d-32c2accfa15b",
        "email" : "<EMAIL>",
        "fullname" : "Admin24",
        "phone_number" : "+84986490388",
        "username" : "admin24@pingcomshop"
    },
    "line_event" : "merchant",
    "source" : "SOCIAL",
    "action_time" : 1635323521.467509,
    "created_time" : ISODate("2021-10-27T15:34:48.861+07:00"),
    "body" : {
        "profile_data" : {
            "profile_id" : "f78ebd1c-0854-46b0-b85b-a4cf9ddb0298",
            "id" : "f78ebd1c-0854-46b0-b85b-a4cf9ddb0298",
            "name" : "Đỗ Hòa"
        },
        "page_social_id" : "858617877680201",
        "content" : "Tùng Dịch",
        "page_name" : "Thiết bị văn phòng",
        "user_social_id" : "3551827758269419"
    }
}

@apiSuccessExample {json} Response, SourceEvent:Email:
{
    "id": "",
    "merchant_id": "",
    "deal_id": "",
    "event_id": "",
    "source_event": "Email",
    "event_type":"",
    "staff_update_deal" : {
            "id" : "d7e63da1-a47e-43e2-9b78-44d43433f7ec",
            "email" : "<EMAIL>",
            "fullname" : "sale",
            "phone_number" : "+84978348445",
            "username" : "sale@thanhtest11"
    },
    "action_time" : 1636613856.258556,
    "source": "EMAIL",
    "body": {
        "profile_data": profile_data,
        "from_email": ""		// email gửi
        "to_email": [""]		// danh sách người nhận
        "cc_email": [""]
        "bcc_email": [""]
        "subject": ""		// tiêu đề email 
        "content": ""		// nội dung email 
        "sender_domain": "mobio.vn", // có đối với kiểu email_mkt
        "related_to": {
            "company_ids": [], // Danh sách company có liên quan tới
            "ticket_ids": [], // Danh sách ticket có liên quan tới
            "profile_ids": [], // Danh sách Profile có liên quan tới
            "order_ids": [], // Danh sách các deal có liên quan tới
        },
        "attachments": [""] // Danh sách url file media 
        "status": {
            "type": "success", // Trạng thái của email đang gửi, đã gửi, gửi lỗi: pending, success, failed
            "message": "", // Trong trường hợp gửi mail lỗi thì cần gửi kèm nguyên nhân lỗi trong field này
        },
        "action_time": "",
    }
}

@apiSuccessExample {json} Response, SourceEvent: Callcenter:
{
    "_id" : "619b0507291638612986860d",
    "unique_value" : "call-vn-1-99V79H284Q-1637519225215",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "6185fc94d3145f3fb2dac5cf",
    "event_id" : "619b0507291638612986860b",
    "source_event" : "CALLCENTER",
    "event_type" : "SaleCallcenter",
    "staff_update_deal" : {
        "id" : "f48aad19-cbe9-4c49-baa2-b3d3e8a29bd4",
        "email" : "<EMAIL>",
        "fullname" : "Ngọc Ánh",
        "phone_number" : "+84334303858",
        "username" : "anhttn@pingcomshop"
    },
    "line_event" : "merchant",
    "activity" : "Callcenter",
    "source" : "CALLCENTER",
    "action_time" : 1637549229
    "created_time" : "2021-11-22T09:48:39.395Z,
    "body" : {
        "profile_data" : {
            "profile_id" : "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
            "profile_phone_number" : "0328914167",
            "id" : "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
            "name" : "Ngoan "
        },
        "switchboard" : "842473005768",
        "call_id" : "call-vn-1-99V79H284Q-1637519225215",
        "call_status" : 7,
        "so_may_le" : "106",
        "hot_line" : null,
        "attachments" : [
            {
                "url" : "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/call-vn-1-99V79H284Q-1637519225215.mp3",
                "local_path" : "/media/data/public_resources/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/call-vn-1-99V79H284Q-1637519225215.mp3",
                "filename" : "call-vn-1-99V79H284Q-1637519225215.mp3",
                "format" : "audio/mpeg"
            }
        ],
        "related_to" : {
            "profile_ids" : [
                "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
                "137c56e5-d56d-4dbc-9729-fa997e0294c3"
            ],
            "ticket_ids" : [
                "d8b34170-fb3b-401a-9d25-623844b4ef15"
            ],
            "order_ids" : [
                "6185fc94d3145f3fb2dac5cf"
            ]
        }
    }
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductCategoriesMain
{
	"_id": "627cadbed757dbd1ceb87ab9",
	"action_time": 1652338109.170179,
	"activity": "UpdateDeal",
	"body": {
		"add": [{
			"id": "626b50c62d0663bd2efb4707",
			"name": {
				"vi": "Danh m\u1ee5c c\u1ea5p 2"
			}
		}, {
			"id": "6278888ac7048b4e346a69f5",
			"name": {
				"vi": "Danh m\u1ee5c ch\u00ednh"
			}
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T06:48:29.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cadbed757dbd1ceb87ab7",
	"event_type": "AddProductCategoriesMain",
	"id": "627cadbed757dbd1ceb87ab9",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cadbd21e0c966a9a4bdd1"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductCategoriesSub
{
	"_id": "627cadbed757dbd1ceb87abc",
	"action_time": 1652338109.170179,
	"activity": "UpdateDeal",
	"body": {
		"add": [{
			"root_category": {
				"id": "626b50c62d0663bd2efb4707",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			},
			"sub_categories": [{
				"id": "626b51772d0663bd2efb4708",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			}, {
				"id": "626b51772d0663bd2efb4709",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 3"
				}
			}]
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T06:48:29.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cadbed757dbd1ceb87aba",
	"event_type": "AddProductCategoriesSub",
	"id": "627cadbed757dbd1ceb87abc",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cadbd21e0c966a9a4bdd1"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductCategoriesMain
{
	"_id": "627cbbb0d757dbd1ceb87bb3",
	"action_time": 1652341679.769229,
	"activity": "UpdateDeal",
	"body": {
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"remove": [{
			"id": "6278888ac7048b4e346a69f5",
			"name": {
				"vi": "Danh m\u1ee5c ch\u00ednh"
			}
		}],
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T07:47:59.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cbbb0d757dbd1ceb87bb1",
	"event_type": "RemoveProductCategoriesMain",
	"id": "627cbbb0d757dbd1ceb87bb3",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cbbaf1b36ae850ead6167"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductCategoriesMain
{
	"_id": "627cbdbbd757dbd1ceb87bd0",
	"action_time": 1652342202.881001,
	"activity": "UpdateDeal",
	"body": {
		"remove": [{
			"root_category": {
				"id": "626b50c62d0663bd2efb4707",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			},
			"sub_categories": [{
				"id": "626b51772d0663bd2efb4708",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			}]
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T07:56:43.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cbdbbd757dbd1ceb87bce",
	"event_type": "RemoveProductCategoriesSub",
	"id": "627cbdbbd757dbd1ceb87bd0",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cbdba9d69765958672e4f"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductType
{
    "_id": "62b139801298bda9228463d1",
    "action_time": 1655781759.375529,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "tags",
        "field_key": "product_types",
        "field_name": "product_types",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "product_line": { // Thông tin dòng sản phẩm
                    "id": "62aa7b0b181e77000e3afe29", 
                    "name": "[Test] Tùng 01"
                },
                "product_types": [
                    {
                        "id": "62aa96843af16c000d1dfaf8",
                        "level": 1, // Cấp loại sản phẩm
                        "name": "a" # Tên loại sản phẩm
                    }
                ]
            }
        ],
        "required": false,
        "translate_key": "i18n_subcategories"
    },
    "created_time": "2022-06-21T03:22:39.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b139801298bda9228463cf",
    "event_type": "RemoveProductType",
    "id": "62b139801298bda9228463d1",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1397f76bf3e9d77331fce"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductLine
{
    "_id": "62b1397f1298bda9228463cd",
    "action_time": 1655781759.375529,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "dropdown_single_line",
        "field_key": "product_line",
        "field_name": "product_line",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "id": "62aa7b0b181e77000e3afe29",
                "name": "[Test] Tùng 01"
            }
        ],
        "required": false,
        "translate_key": "i18n_product_category"
    },
    "created_time": "2022-06-21T03:22:39.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b1397f1298bda9228463cb",
    "event_type": "RemoveProductLine",
    "id": "62b1397f1298bda9228463cd",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1397f76bf3e9d77331fce"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductBank
{
    "_id": "62b138fd1298bda9228463b9",
    "action_time": **********.971797,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "dropdown_single_line",
        "field_key": "products_bank",
        "field_name": "products_bank",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "id": "62b03b964ef591000fd000a2",
                "name": "sản phẩm 1"
            }
        ],
        "required": false,
        "translate_key": "i18n_product"
    },
    "created_time": "2022-06-21T03:20:29.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b138fd1298bda9228463b7",
    "event_type": "RemoveProductBank",
    "id": "62b138fd1298bda9228463b9",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b138fca918469ec15424c0"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductType
{
    "_id": "62b137771298bda922846383",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "product_line": {
                    "id": "62aa7b0b181e77000e3afe29",
                    "name": "[Test] Tùng 01"
                },
                "product_types": [
                    {
                        "id": "62aa96843af16c000d1dfaf8",
                        "level": 1,
                        "name": "a"
                    }
                ]
            }
        ],
        "display_type": "tags",
        "field_key": "product_types",
        "field_name": "product_types",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_subcategories"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137771298bda922846381",
    "event_type": "AddProductType",
    "id": "62b137771298bda922846383",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductLine
{
    "_id": "62b137761298bda92284637f",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "id": "62aa7b0b181e77000e3afe29",
                "name": "[Test] Tùng 01"
            }
        ],
        "display_type": "dropdown_single_line",
        "field_key": "product_line",
        "field_name": "product_line",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_product_category"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137761298bda92284637d",
    "event_type": "AddProductLine",
    "id": "62b137761298bda92284637f",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: Sale, EventType: UpdateMediaDeal
{
    "_id": "62de6dcbb483efaabc8e5fd8",
    "action_time": 1658744255.607341,
    "activity": "Other",
    "body": {
        "add": [
            {
                "_id": "62de6dbf812bca51bd423890",
                "created_time": "2022-07-25T10:17:35Z",
                "entity_id": "62d1315f788e91d857f24115",
                "entity_type": "detail_deal",
                "id": "62de6dbf812bca51bd423890",
                "info": {
                    "format_file": "image/jpeg",
                    "local_path": "/media/data/public_resources/static/87d9e1f3-6da0-415f-b418-9ba9f4fe5523/Dr-Strange-Wallpapers.jpeg",
                    "title": "Dr-Strange-Wallpapers.jpeg",
                    "type_media": [
                        "identification"
                    ],
                    "url": "https://t1.mobio.vn/static/87d9e1f3-6da0-415f-b418-9ba9f4fe5523/Dr-Strange-Wallpapers.jpeg"
                },
                "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
                "updated_time": "2022-07-25T10:17:35Z"
            }
        ],
        "remove": []
    },
    "deal_id": "62d1315f788e91d857f24115",
    "event_type": "UpdateMediaDeal",
    "id": "62de6dcbb483efaabc8e5fd8",
    "line_event": "merchant",
    "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
    "source": "SALE",
    "source_event": "Deal",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin Owner",
        "id": "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737",
        "phone_number": "+***********",
        "username": "admin@msbeb"
    }
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductBank
{
    "_id": "62b137761298bda92284637b",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "id": "62b03b964ef591000fd000a2",
                "name": "sản phẩm 1"
            }
        ],
        "display_type": "dropdown_single_line",
        "field_key": "products_bank",
        "field_name": "products_bank",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_product"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137761298bda922846379",
    "event_type": "AddProductBank",
    "id": "62b137761298bda92284637b",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}
"""
# -- END DEFINE --

# ---------- Danh sách Event -----------
"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/action/filter/activity/abac [DealEvent] - Lấy danh sách event của Deal
@apiGroup ABAC-SALE
@apiVersion 1.0.0
@apiName ListEventAbac

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse json_header
@apiUse merchant_id_header
@apiUse DealEventAbac

"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: thong
    Company: M O B I O
    Date Created: 08/11/2022
"""

# ------------- <PERSON><PERSON><PERSON> thông tin profile theo danh sách ID profile --------
# Support cho case deal_owner xem đ<PERSON><PERSON><PERSON> thông tin profile ở cột 3

"""
@api {post} {domain}/sale/api/v1.0/other/search-profile-by-ids  <PERSON><PERSON><PERSON> thông tin profiles theo id profile
@apiDescription L<PERSON>y danh sách thông tin profiles theo ids khi đứng từ đơn hàng, check thêm quyền scope_code
@apiGroup Other
@apiVersion 1.0.0
@apiName SaleGetProfileByIds

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}   deal_id       ID đơn hàng
@apiParam       (Body:)     {Array}    profile_ids   Danh sách các Profile Ids. Max = 100

@apiParamExample  {json} Body request example
{
    "deal_id": "636882d074f92936ebb098eb",
    "profile_ids": [
        "4b1d793d-e999-4066-9614-6f0bf4fd3c9e"
    ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
        {
            "avatar": "https://graph.facebook.com/737323523274672/picture?type=large&access_token=EAAENpMUscdQBAHmFJwuZASXrdCBOOcH4CWKglGTduP9S5OuRdckZCSqbtz5Aw2XnsZCoCMaMzHeWVdZAktop2FfHBopl4fs0NySUhZCdGD5oxFKiI2yNAsd0sgjXqx3sHx4fhQEUuY3kWp9lOocvkfiNZCl1HbLX8iJCx5aYDjkgZDZD",
            "name": "Nguyễn Thị Thùy",
            "profile_id": "4b1d793d-e999-4066-9614-6f0bf4fd3c9e",
            "social_name": {
                "id": 1,
                "name": "Nguyễn Thị Thùy"
            },
            "social_user": {
                "social_id": "100007552803457",
                "social_type": 1
            }
        }
    ]
}
"""

# ------------- Lấy thông tin Companies theo danh sách ID companies --------
# Support cho case deal_owner xem được thông tin companies ở cột 3

"""
@api {post} {domain}/sale/api/v1.0/other/company-list-by-ids Lấy thông tin company theo danh sách ID
@apiDescription  Lấy danh sách company theo danh sách ID, Đứng từ cột 3 đơn hàng, có check theo scope_code 
@apiGroup Other
@apiVersion 1.0.0
@apiName  OtherListCompany
@apiUse merchant_id_header

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Body:)			{String}	    deal_id		     Id đơn hàng
@apiParam	(Body:)			{Array}	        company_ids		 Tìm kiếm công ty theo id

@apiParamExample {json} Body example
{
    "deal_id": "63180fdd00cba901dceada77",
    "company_ids": ["6167a65ea5434e3edfd1bfe8", "616640299015b5071be5997a"]
}

@apisuccess       {Array[company_object]}              data                D/s company


@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [{
    "_id": "614c4193b6cf16c1becdda0f",
    "staff_id": "bcde3ade-190b-4f04-8f21-a609911a2ccb",
    "merchant_id": "cdb63720-9628-5ef6-bbca-2e5ce6094f3c",
    "status": "ACTIVE",
    "deleted_time": null,
    "name": "Công ty Du lịch",
    "tax_identification_number": "**********",
    "branches": [
      "7th Flr, toà nhà Minh Long, 17 Bà Huyện Thanh Quan, quận 3, Hồ Chí Minh, Việt Nam",
    ],
    "email": "<EMAIL>",
    "other_email": [
      "<EMAIL>",
      "<EMAIL>"
    ]
    "description": "Vài dòng mô tả",
    "operation": [
      1, 2
    ],
    "logo": "https://mobio.vn/wp-content/uploads/2019/08/Xanh_trans-e1565606866395-768x222.png",
    "license_time": "2021-10-02T00:00:00.000Z",
    "active_time": "2021-10-02T00:00:00.000Z",
    "legal_representative": "Mrs. Hoa",
    "owner_of_company": "614c4198b6cf16c1becdda10",
    "phone_number": "*********",
    "other_phone_number": [
      "*********",
      "*********"
    ],
    "province_code": [
      1,
      2
    ],
    "headquarter": "6/82, Duy Tân, Dịch Vọng Hậu, Cầu Giấy, Hà Nội",
    "websites": [
      "https://congty.com",
    ],
    "created_time": "2021-10-01T18:30:52.052Z",
    "updated_time": "2021-10-01T18:30:52.052Z",
  }],
  "lang": "vi",
  "message": "request thành công."
}
"""

#!/usr/bin/python
# -*- coding: utf8 -*-

"""
Danh sách cấu hình luật SLA theo danh sách quy trình
"""

"""
@api {GET} {domain}/sale/api/v1.0/sla-configs         Danh sách cấu hình SLA theo quy trình bán hàng
@apiGroup SlaConfig
@apiVersion 1.0.0
@apiName GetSlaConfigBySaleProcess

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Param:)  {String}  search  Tìm theo tên quy trình bán hàng
@apiParam   (Param:)  {String}  status  Trạng thái bật tắt cấu hình SLA <br>
                                        <li><code>enable</code>: Đang hoạt động</li>
                                        <li><code>disable</code>: <PERSON>hông hoạt động</li>
@apiParam   (Param:)  {String}  start_time  Thời gian cập nhật bắt đầu từ (format: <code>DD-MM-YYYY</code>)
@apiParam   (Param:)  {String}  end_time  Thời gian cập nhật kết thúc từ (format: <code>DD-MM-YYYY</code>)

@apiSuccess {Number}  code       Response status
@apiSuccess {String}  message    Response message
@apiSuccess {Array}   data       Danh sách quy trình có chưa thông tin cấu hình SLA
@apiSuccess {String}  data.sale_process_id             ID quy trình bán hàng
@apiSuccess {String}  data.states                      Danh sách trạng thái quy trình bán hàng
@apiSuccess {String}  data.sale_process_name           Tên quy trình bán hàng
@apiSuccess {Array[String]}   data.team_ids            Danh sách team ID được tham gia quy trình bán hàng
@apiSuccess {Object}  data.sla_config                  Cấu hình SLA đi theo từng quy trình (<code>Nếu object rỗng có nghĩa là chưa cấu hình</code>)
@apiUse sla_config_response
@apiUse condition_apply_response
@apiUse target_response
@apiUse notify_config_response
@apiUse condition_groups_response
@apiUse conditions_response
@apiUse detail_config_response

@apiUse paging
@apiSuccessExample Response success:
{
    "code": 200,
    "data": [
        {
            "sale_process_id": "61ba184975f7bd58ef96d89b",
            "sale_process_name": "DEMO ( KHỒNG SỬA, TẠO ĐƠN HÀNG)",
            "sla_config": {
                "_id": "66ac85a82b415909d41deb13",
                "condition_apply": {
                    "condition_groups": [
                        {
                            "conditions": [
                                {
                                    "code": "other_dynamic_field",
                                    "display_type": "date_picker",
                                    "field_key": "_dyn_datepicker_dd_mm_1703233295138",
                                    "format": "dd/mm",
                                    "type": "in_time",
                                    "value_from": "2104-04-04T00:00:00.000Z",
                                    "value_to": ""
                                }
                            ],
                            "group": 1,
                            "operator": null,
                            "order": 1
                        },
                        {
                            "conditions": [
                                {
                                    "code": "other_dynamic_field",
                                    "display_type": "multi_line",
                                    "field_key": "_dyn_t__mul_am_duong_1704960819824",
                                    "field_property": 88,
                                    "type": "eq_value",
                                    "value": "2323243.0"
                                }
                            ],
                            "group": 2,
                            "operator": "and",
                            "order": 2
                        }
                    ],
                    "deal_apply": "all",
                    "sale_process_ids": [],
                    "states_code": [],
                    "trigger_code": "sales_opportunity_first_employee"
                },
                "sla_policy": "apply_the_current_sla_policy",
                "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                "created_time": "2024-08-02T07:07:20.000Z",
                "description": "Cấu hình SLA test",
                "id": "66ac85a82b415909d41deb13",
                "notify_configs": [
                    {
                        "detail_configs": [
                            {
                                "other_receivers": [
                                    "<EMAIL>",
                                    "<EMAIL>"
                                ],
                                "receivers": [
                                    "opportunity_supporter",
                                    "opportunity_owner"
                                ],
                                "status": "enable",
                                "type": "email"
                            }
                        ],
                        "type": "resolution_response",
                        "reminder_notification": {
                            "value": 15,
                            "unit": "minute",
                            "instant_notify": "enable"
                        }
                    }
                ],
                "sale_process_id": "61ba184975f7bd58ef96d89b",
                "status": "enable",
                "targets": [
                    {
                        "configs": [
                            {
                                "state_code": "JKDSHJ",
                                "time_apply": "any_time",
                                "time_slot_id": "",
                                "status": "enable"
                                "hour": 10,
                                "minute": 29,
                                "day": 10
                            },
                            {
                                "state_code": "DSKDDD",
                                "time_apply": "any_time",
                                "time_slot_id": "",
                                "status": "disable",
                                "hour": 10,
                                "minute": 29,
                                "day": 10
                            }
                        ],
                        "target_type": "policy_sales_opportunity_status"
                    },
                    {
                        "configs": [
                            {
                                "state_code": "",
                                "time_apply": "any_time",
                                "time_slot_id": "",
                                "hour": 10,
                                "minute": 29,
                                "day": 10
                            }
                        ],
                        "target_type": "sales_opportunity_lifecycle"
                    }
                ]
                "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                "updated_time": "2024-08-02T07:07:27.000Z"
            },
            "states": [
                {
                    "duplicateRatio": false,
                    "emptyRatio": false,
                    "name": "Có thông tin Leads",
                    "ratio": 10,
                    "state_code": "DX9K9WED",
                    "status": 1
                },
                {
                    "name": "Liên lạc",
                    "ratio": 20,
                    "state_code": "PWCUM4BX",
                    "status": -1
                },
                {
                    "duplicateRatio": false,
                    "emptyRatio": false,
                    "name": "Liên hệ",
                    "ratio": 20,
                    "state_code": "ZHTOE80L",
                    "status": 1
                },
                {
                    "duplicateRatio": false,
                    "emptyRatio": false,
                    "name": "Báo giá",
                    "ratio": 40,
                    "state_code": "UM0O5IAA",
                    "status": 1
                },
                {
                    "duplicateRatio": false,
                    "emptyRatio": false,
                    "name": "Chờ duyệt hồ sơ",
                    "ratio": 70,
                    "state_code": "K4MGEFZJ",
                    "status": 1
                },
                {
                    "duplicateRatio": false,
                    "emptyRatio": false,
                    "name": "Thành công",
                    "ratio": 100,
                    "state_code": "0K9AS1NJ",
                    "status": 1
                },
                {
                    "duplicateRatio": false,
                    "emptyRatio": false,
                    "name": "Thất bại",
                    "ratio": 0,
                    "state_code": "3C73HJ5Z",
                    "status": 1
                }
            ],
            "team_ids": [
                "06ec90b8-f04e-11ec-ba15-bad0a884d44a",
                "0ef9664f-b052-4782-9e1a-39fce73899b7",
                "2b8e2474-13de-4c81-b231-1aac73605a87",
                "c9e0eb7e-d9ea-11ed-88b6-ca2b5a446da2",
                "ce223d0b-5a90-4f77-8638-dba97ae9b9d4",
                "d29b174a-7a86-4046-8019-4c7ce5a91fcb",
                "f72cab1a-c3bd-11ed-a8c1-7abf8f643a23"
            ]
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "page": -1,
        "per_page": 15,
        "total_count": 121,
        "total_page": 9
    }
}
"""

# ----- Cập nhật cấu hình SLA -------

"""
@api {POST} {domain}/sale/api/v1.0/sla-config/<sale_process_id> Cập nhật, Thêm mới cấu hình SLA
@apiDescription Cập nhật, Thêm mới cấu hình SLA
@apiGroup SlaConfig
@apiVersion 1.0.0
@apiName UpdateSlaConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Params Path) {String} sale_process_id Id quy trình bán hàng


@apiUse sla_config_param
@apiUse condition_apply_param
@apiUse target_param
@apiUse notify_config_param
@apiUse condition_groups_param
@apiUse conditions_param
@apiUse detail_config_param
@apiUse response_check_total_deal

@apiParamExample {json} Body example
{   
    "status": "enable",
    "description": "Cấu hình SLA test",
    "sla_policy": "apply_the_current_sla_policy",
    "is_check_total_deal": False,
    "condition_apply": {
         "trigger_code": "sales_opportunity_first_employee",
         "sale_process_ids": [],
         "states_code": [],
         "deal_apply": "all",
         "condition_groups": [
            {
                "group": 1, 
                "order": 1, 
                "conditions": [ 
                    {
                        "code": "other_dynamic_field", 
                        "type": "in_time", 
                        "display_type": "date_picker", 
                        "field_key": "_dyn_datepicker_dd_mm_1703233295138", 
                        "format": "dd/mm", 
                        "value_from": "2104-04-04T00:00:00.000Z", 
                        "value_to": "" 
                    }
                ],
                "operator": null 
            },
            {
                "group": 2,
                "order": 2,
                "conditions": [
                    {
                        "code": "other_dynamic_field",
                        "value": "2323243.0",
                        "type": "eq_value",
                        "display_type": "multi_line",
                        "field_key": "_dyn_t__mul_am_duong_1704960819824",
                        "field_property": 88
                    }
                ],
                "operator": "and" 
            }
         ]
    },
    "sale_process_id": "61ba184975f7bd58ef96d89b",
    "status": "enable",
    "targets": [
        {
            "configs": [
                {
                    "state_code": "JKDSHJ",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "status": "enable"
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                },
                {
                    "state_code": "DSKDDD",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "status": "disable",
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                }
            ],
            "status": "enable",
            "target_type": "policy_sales_opportunity_status"
        },
        {
            "configs": [
                {
                    "state_code": "",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                }
            ],
            "status": "disable",
            "target_type": "sales_opportunity_lifecycle"
        }
    ]
    "notify_configs": [
        {
            "type": "resolution_response",
            "reminder_notification": {
                "value": 15,
                "unit": "minute",
                "instant_notify": "enable"
            }
            "detail_configs": [
                {
                    "status": "enable",
                    "receivers": ["opportunity_supporter", "opportunity_owner"],
                    "type": "email",
                    "other_receivers": ["<EMAIL>", "<EMAIL>"]
                }
            ]
        }
    ]
}

@apiSuccess {Number} code                   Response status
@apiSuccess {String} message                Response message
@apiSuccess {Obbject}  data                 Cấu hình SLA


@apiSuccessExample {json} Response
{   
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}

@apiSuccessExample {json} Response Check Total Deal When Update SLA
{
    "code": 413,
    "data": {
        "code": 413,
        "total_deal": 87
    },
    "message": "Tồn tại Cơ hội bán đang nằm trong SLA"
}

@apiUse sla_config_response
@apiUse condition_apply_response
@apiUse target_response
@apiUse notify_config_response
@apiUse condition_groups_response
@apiUse conditions_response
@apiUse detail_config_response
"""

# ----- Cập nhật trạng thái cấu hình SLA -------

"""
@api {PUT} {domain}/sale/api/v1.0/sla-config/<sale_process_id>/update-status Cập nhật trạng thái cấu hình SLA
@apiDescription Cập nhật trạng thái cấu hình SLA
@apiGroup SlaConfig
@apiVersion 1.0.0
@apiName UpdateStatusSlaConfig

@apiParam (Params Path) {String} sale_process_id Id quy trình bán hàng

@apiParam  (Body:)   {String}  status  Trạng thái </br>
                                                 <li><code>enable</code>: Bật cấu hình</li>
                                                 <li><code>disable</code>: Tắt cấu hình</li>
@apiParamExample {json} Body example
{
    "status": "enable"
}

@apiParamExample {json} Body example
{
    "data": {}
}
"""

# ----- Xoá cấu hình SLA -------

"""
@api {DELETE} {domain}/sale/api/v1.0/sla-config/<sale_process_id> Xoá cấu hình SLA
@apiDescription Xoá cấu hình SLA
@apiGroup SlaConfig
@apiVersion 1.0.0
@apiName DeleteSlaConfig

@apiParam (Params Path) {String} sale_process_id Id quy trình bán hàng

@apiParamExample {json} Body example
{
    "data": {}
}
"""



# ----- Xoá cấu hình SLA Theo ID-------

"""
@api {DELETE} {domain}/sale/api/v1.0/config/sla-config/<sla_config_id> Xoá cấu hình SLA theo ID
@apiDescription Xoá cấu hình SLA theo ID
@apiGroup SlaConfig
@apiVersion 1.0.0
@apiName SlaConfigDelete

@apiParam (Params Path) {String} sla_config_id ID Cấu hình SLA

@apiParamExample {json} Body example
{
    "data": {}
}
"""


# ----- Cập nhật trạng thái cấu hình SLA -------

"""
@api {PUT} {domain}/sale/api/v1.0/config/sla-config/<sla_config_id>/update-status Cập nhật trạng thái cấu hình SLA theo ID
@apiDescription Cập nhật trạng thái cấu hình SLA theo ID
@apiGroup SlaConfig
@apiVersion 1.0.0
@apiName SlaConfigUpdateStatus

@apiParam (Params Path) {String} sla_config_id ID Cấu hình SLA

@apiParam  (Body:)   {String}  status  Trạng thái </br>
                                                 <li><code>enable</code>: Bật cấu hình</li>
                                                 <li><code>disable</code>: Tắt cấu hình</li>
@apiParamExample {json} Body example
{
    "status": "enable"
}

@apiParamExample {json} Body example
{
    "data": {}
}
"""


# ----- Thêm mới cấu hình SLA -------

"""
@api {POST} {domain}/sale/api/v1.0/config/sla-config Thêm mới cấu hình SLA
@apiDescription Thêm mới cấu hình SLA
@apiGroup SlaConfig
@apiVersion 1.0.0
@apiName SlaConfigAdd

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Sla Config object) {String} sale_process_id Id quy trình bán hàng


@apiUse sla_config_param
@apiUse condition_apply_param
@apiUse target_param
@apiUse notify_config_param
@apiUse condition_groups_param
@apiUse conditions_param
@apiUse detail_config_param
@apiUse response_check_total_deal

@apiParamExample {json} Body example
{   
    "status": "enable",
    "target_type": "policy_sales_opportunity_status"
    "description": "Cấu hình SLA test",
    "sla_policy": "apply_the_current_sla_policy",
    "is_check_total_deal": False,
    "condition_apply": {
         "trigger_code": "sales_opportunity_first_employee",
         "sale_process_ids": [],
         "states_code": [],
         "deal_apply": "all",
         "condition_groups": [
            {
                "group": 1, 
                "order": 1, 
                "conditions": [ 
                    {
                        "code": "other_dynamic_field", 
                        "type": "in_time", 
                        "display_type": "date_picker", 
                        "field_key": "_dyn_datepicker_dd_mm_1703233295138", 
                        "format": "dd/mm", 
                        "value_from": "2104-04-04T00:00:00.000Z", 
                        "value_to": "" 
                    }
                ],
                "operator": null 
            },
            {
                "group": 2,
                "order": 2,
                "conditions": [
                    {
                        "code": "other_dynamic_field",
                        "value": "2323243.0",
                        "type": "eq_value",
                        "display_type": "multi_line",
                        "field_key": "_dyn_t__mul_am_duong_1704960819824",
                        "field_property": 88
                    }
                ],
                "operator": "and" 
            }
         ]
    },
    "sale_process_id": "61ba184975f7bd58ef96d89b",
    "status": "enable",
    "targets": [
        {
            "configs": [
                {
                    "state_code": "JKDSHJ",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "status": "enable"
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                },
                {
                    "state_code": "DSKDDD",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "status": "disable",
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                }
            ],
            "status": "enable",
            "target_type": "policy_sales_opportunity_status"
        },
        {
            "configs": [
                {
                    "state_code": "",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                }
            ],
            "status": "disable",
            "target_type": "sales_opportunity_lifecycle"
        }
    ]
    "notify_configs": [
        {
            "type": "resolution_response",
            "reminder_notification": {
                "value": 15,
                "unit": "minute",
                "instant_notify": "enable"
            }
            "detail_configs": [
                {
                    "status": "enable",
                    "receivers": ["opportunity_supporter", "opportunity_owner"],
                    "type": "email",
                    "other_receivers": ["<EMAIL>", "<EMAIL>"]
                }
            ]
        }
    ]
}

@apiSuccess {Number} code                   Response status
@apiSuccess {String} message                Response message
@apiSuccess {Obbject}  data                 Cấu hình SLA


@apiSuccessExample {json} Response
{   
    "_id": "",
    "status": "enable",
    "description": "",
    "updated_by": "",
    "updated_time": "",
    "sla_policy": "apply_the_current_sla_policy",
    "condition_apply": {
         "trigger_code": "",
         "sale_process_ids": [],
         "states_code: [],
         "deal_apply": "all",
         "condition_groups: [
            {
                "group": 1, // thứ tự group
                "order": 1, // thứ tự order
                "conditions": [ // nhóm condtions trong một group
                    {
                        "code": "other_dynamic_field", // mã code condition
                        "type": "in_time", // kiểu so sánh
                        "display_type": "date_picker", // kiểu hiển thị
                        "field_key": "_dyn_datepicker_dd_mm_1703233295138", // field key
                        "format": "dd/mm", // định dạng thờig gian
                        "value_from": "2104-04-04T00:00:00.000Z", // giá trị bắt đầu
                        "value_to": "" // giá trị kết thúc
                    }
                ],
                "operator": null // đối với bản ghi đầu tiên thì sẽ không có operator vì chưa kết hợp với thông tin điều kiện 
            },
            {
                "group": 2,
                "order": 2,
                "conditions": [
                    {
                        "code": "other_dynamic_field",
                        "value": "2323243.0",
                        "type": "eq_value",
                        "display_type": "multi_line",
                        "field_key": "_dyn_t__mul_am_duong_1704960819824",
                        "field_property": 88
                    }
                ],
                "operator": "and" // toán tử kết hợp là and với điều kiện trước
            },
         ]
    },
    "sale_process_id": "61ba184975f7bd58ef96d89b",
    "targets": [
        {
            "configs": [
                {
                    "state_code": "JKDSHJ",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "status": "enable"
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                },
                {
                    "state_code": "DSKDDD",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "status": "disable",
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                }
            ],
            "status": "enable",
            "target_type": "policy_sales_opportunity_status"
        },
        {
            "configs": [
                {
                    "state_code": "",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                }
            ],
            "status": "disable",
            "target_type": "sales_opportunity_lifecycle"
        }
    ]
    "notify_configs": [
        {
            "type": "resolution_response",
            "reminder_notification": {
                "value": 15,
                "unit": "minute",
                "instant_notify": "enable"
            }
            "detail_config": [
                {
                    "status": "",
                    "receivers": ["opportunity_supporter", "opportunity_owner"],
                    "type": "email",
                    "other_receivers": ["<EMAIL>", "<EMAIL>"]
                }
            ]
        }
    ]
}

@apiUse sla_config_response
@apiUse condition_apply_response
@apiUse target_response
@apiUse notify_config_response
@apiUse condition_groups_response
@apiUse conditions_response
@apiUse detail_config_response
"""


# ----- Cập nhật cấu hình SLA theo ID -------

"""
@api {PUT} {domain}/sale/api/v1.0/config/sla-config/<sla_config_id> Cập nhật cấu hình SLA theo ID
@apiDescription Cập nhật cấu hình SLA theo ID
@apiGroup SlaConfig
@apiVersion 1.0.0
@apiName SlaConfigUpdate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam (Sla Config object) {String} sale_process_id Id quy trình bán hàng


@apiUse sla_config_param
@apiUse condition_apply_param
@apiUse target_param
@apiUse notify_config_param
@apiUse condition_groups_param
@apiUse conditions_param
@apiUse detail_config_param
@apiUse response_check_total_deal

@apiParamExample {json} Body example
{   
    "status": "enable",
    "target_type": "policy_sales_opportunity_status"
    "description": "Cấu hình SLA test",
    "sla_policy": "apply_the_current_sla_policy",
    "is_check_total_deal": False,
    "condition_apply": {
         "trigger_code": "sales_opportunity_first_employee",
         "sale_process_ids": [],
         "states_code": [],
         "deal_apply": "all",
         "condition_groups": [
            {
                "group": 1, 
                "order": 1, 
                "conditions": [ 
                    {
                        "code": "other_dynamic_field", 
                        "type": "in_time", 
                        "display_type": "date_picker", 
                        "field_key": "_dyn_datepicker_dd_mm_1703233295138", 
                        "format": "dd/mm", 
                        "value_from": "2104-04-04T00:00:00.000Z", 
                        "value_to": "" 
                    }
                ],
                "operator": null 
            },
            {
                "group": 2,
                "order": 2,
                "conditions": [
                    {
                        "code": "other_dynamic_field",
                        "value": "2323243.0",
                        "type": "eq_value",
                        "display_type": "multi_line",
                        "field_key": "_dyn_t__mul_am_duong_1704960819824",
                        "field_property": 88
                    }
                ],
                "operator": "and" 
            }
         ]
    },
    "sale_process_id": "61ba184975f7bd58ef96d89b",
    "status": "enable",
    "targets": [
        {
            "configs": [
                {
                    "state_code": "JKDSHJ",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "status": "enable"
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                },
                {
                    "state_code": "DSKDDD",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "status": "disable",
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                }
            ],
            "status": "enable",
            "target_type": "policy_sales_opportunity_status"
        },
        {
            "configs": [
                {
                    "state_code": "",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                }
            ],
            "status": "disable",
            "target_type": "sales_opportunity_lifecycle"
        }
    ]
    "notify_configs": [
        {
            "type": "resolution_response",
            "reminder_notification": {
                "value": 15,
                "unit": "minute",
                "instant_notify": "enable"
            }
            "detail_configs": [
                {
                    "status": "enable",
                    "receivers": ["opportunity_supporter", "opportunity_owner"],
                    "type": "email",
                    "other_receivers": ["<EMAIL>", "<EMAIL>"]
                }
            ]
        }
    ]
}

@apiSuccess {Number} code                   Response status
@apiSuccess {String} message                Response message
@apiSuccess {Obbject}  data                 Cấu hình SLA


@apiSuccessExample {json} Response
{   
    "_id": "",
    "status": "enable",
    "description": "",
    "updated_by": "",
    "updated_time": "",
    "sla_policy": "apply_the_current_sla_policy",
    "condition_apply": {
         "trigger_code": "",
         "sale_process_ids": [],
         "states_code: [],
         "deal_apply": "all",
         "condition_groups: [
            {
                "group": 1, // thứ tự group
                "order": 1, // thứ tự order
                "conditions": [ // nhóm condtions trong một group
                    {
                        "code": "other_dynamic_field", // mã code condition
                        "type": "in_time", // kiểu so sánh
                        "display_type": "date_picker", // kiểu hiển thị
                        "field_key": "_dyn_datepicker_dd_mm_1703233295138", // field key
                        "format": "dd/mm", // định dạng thờig gian
                        "value_from": "2104-04-04T00:00:00.000Z", // giá trị bắt đầu
                        "value_to": "" // giá trị kết thúc
                    }
                ],
                "operator": null // đối với bản ghi đầu tiên thì sẽ không có operator vì chưa kết hợp với thông tin điều kiện 
            },
            {
                "group": 2,
                "order": 2,
                "conditions": [
                    {
                        "code": "other_dynamic_field",
                        "value": "2323243.0",
                        "type": "eq_value",
                        "display_type": "multi_line",
                        "field_key": "_dyn_t__mul_am_duong_1704960819824",
                        "field_property": 88
                    }
                ],
                "operator": "and" // toán tử kết hợp là and với điều kiện trước
            },
         ]
    },
    "sale_process_id": "61ba184975f7bd58ef96d89b",
    "targets": [
        {
            "configs": [
                {
                    "state_code": "JKDSHJ",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "status": "enable"
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                },
                {
                    "state_code": "DSKDDD",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "status": "disable",
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                }
            ],
            "status": "enable",
            "target_type": "policy_sales_opportunity_status"
        },
        {
            "configs": [
                {
                    "state_code": "",
                    "time_apply": "any_time",
                    "time_slot_id": "",
                    "hour": 10,
                    "minute": 29,
                    "day": 10
                }
            ],
            "status": "disable",
            "target_type": "sales_opportunity_lifecycle"
        }
    ]
    "notify_configs": [
        {
            "type": "resolution_response",
            "reminder_notification": {
                "value": 15,
                "unit": "minute",
                "instant_notify": "enable"
            }
            "detail_config": [
                {
                    "status": "",
                    "receivers": ["opportunity_supporter", "opportunity_owner"],
                    "type": "email",
                    "other_receivers": ["<EMAIL>", "<EMAIL>"]
                }
            ]
        }
    ]
}

@apiSuccessExample {json} Response Check Total Deal When Update SLA
{
    "code": 413,
    "data": {
        "code": 413,
        "total_deal": 87
    },
    "message": "Tồn tại Cơ hội bán đang nằm trong SLA"
}

@apiUse sla_config_response
@apiUse condition_apply_response
@apiUse target_response
@apiUse notify_config_response
@apiUse condition_groups_response
@apiUse conditions_response
@apiUse detail_config_response
"""


"""
Danh sách cấu hình SLA theo ID quy trình
"""

"""
@api {GET} {domain}/sale/api/v1.0/config/sla-configs/<sale_process_id>        Danh sách cấu hình SLA theo ID quy trình
@apiGroup SlaConfig
@apiVersion 1.0.0
@apiName SlaConfigListByProcessId

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Params) {String} sale_process_id Id quy trình bán hàng


@apiSuccess {Number}  code       Response status
@apiSuccess {String}  message    Response message
@apiSuccess {Array}   data       Danh sách cấu hình SLA
@apiSuccess {Array[String]}     data.team_ids            Danh sách team ID được tham gia quy trình bán hàng
@apiUse sla_config_response
@apiUse condition_apply_response
@apiUse target_response
@apiUse notify_config_response
@apiUse condition_groups_response
@apiUse conditions_response
@apiUse detail_config_response

@apiSuccessExample Response success:
{
    "code": 200,
    "data": [
        {
            "_id": "67ac77e25663e2b669af87c5",
            "condition_apply": {
                "deal_apply": "all",
                "trigger_code": "sales_opportunity_first_employee"
            },
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2025-02-12T10:28:50.000Z",
            "description": "2121",
            "is_check_total_deal": false,
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notify_configs": [
                {
                    "detail_configs": [],
                    "reminder_notification": {
                        "instant_notify": "disable",
                        "reminder_type": "reminder_15min",
                        "unit": "day",
                        "value": 0
                    },
                    "type": "resolution_response"
                }
            ],
            "sale_process_id": "674d41221f706f89a9032812",
            "sla_policy": "apply_the_current_sla_policy",
            "status": "disable",
            "targets": [
                {
                    "configs": [
                        {
                            "day": 1,
                            "hour": 0,
                            "minute": 0,
                            "status": "disable",
                            "time_apply": "any_time",
                            "unit": "minute",
                            "value": 1440
                        }
                    ],
                    "status": "enable",
                    "target_type": "sales_opportunity_lifecycle"
                },
                {
                    "configs": [
                        {
                            "day": 1,
                            "hour": 0,
                            "minute": 0,
                            "ratio": 10,
                            "state_code": "EXUFWMJO",
                            "status": "enable",
                            "time_apply": "any_time",
                            "unit": "minute",
                            "value": 1440
                        }
                    ],
                    "status": "enable",
                    "target_type": "policy_sales_opportunity_status"
                }
            ],
            "team_ids": [],
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": "2025-02-12T10:37:21.000Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""



"""
@api {POST} {domain}/sale/api/v1.0/config/sla-configs/by-sale-process-ids        Danh sách cấu hình SLA theo Danh sách ID quy trình bán
@apiGroup SlaConfig
@apiVersion 1.0.0
@apiName SlaConfigListBySaleProcessIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Body:) {Array[String]} [sale_process_ids]   Danh sách ID quy trình bán hàng
@apiParam (Body:) {String} [target_type]        Kiểu cấu hình nội dung <br>
                                                        <li><code>sales_opportunity_lifecycle</code>: Áp dụng toàn vòng đời CHB</li>
                                                        <li><code>policy_sales_opportunity_status</code>: Áp dụng chính sách theo từng trạng thái của Cơ hội bán</li>


@apiSuccess {Number}  code       Response status
@apiSuccess {String}  message    Response message
@apiSuccess {Array}   data       Danh sách cấu hình SLA
@apiUse sla_config_response
@apiUse condition_apply_response
@apiUse target_response
@apiUse notify_config_response
@apiUse condition_groups_response
@apiUse conditions_response
@apiUse detail_config_response

@apiSuccessExample Response success:
{
    "code": 200,
    "data": [
        {
            "_id": "67ac77e25663e2b669af87c5",
            "condition_apply": {
                "deal_apply": "all",
                "trigger_code": "sales_opportunity_first_employee"
            },
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2025-02-12T10:28:50.000Z",
            "description": "2121",
            "is_check_total_deal": false,
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notify_configs": [
                {
                    "detail_configs": [],
                    "reminder_notification": {
                        "instant_notify": "disable",
                        "reminder_type": "reminder_15min",
                        "unit": "day",
                        "value": 0
                    },
                    "type": "resolution_response"
                }
            ],
            "sale_process_id": "674d41221f706f89a9032812",
            "sla_policy": "apply_the_current_sla_policy",
            "status": "disable",
            "targets": [
                {
                    "configs": [
                        {
                            "day": 1,
                            "hour": 0,
                            "minute": 0,
                            "status": "disable",
                            "time_apply": "any_time",
                            "unit": "minute",
                            "value": 1440
                        }
                    ],
                    "status": "enable",
                    "target_type": "sales_opportunity_lifecycle"
                },
                {
                    "configs": [
                        {
                            "day": 1,
                            "hour": 0,
                            "minute": 0,
                            "ratio": 10,
                            "state_code": "EXUFWMJO",
                            "status": "enable",
                            "time_apply": "any_time",
                            "unit": "minute",
                            "value": 1440
                        }
                    ],
                    "status": "enable",
                    "target_type": "policy_sales_opportunity_status"
                }
            ],
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": "2025-02-12T10:37:21.000Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {POST} {domain}/sale/api/v1.0/config/sla-configs/by-ids        Danh sách cấu hình SLA theo danh sách ID 
@apiGroup SlaConfig
@apiVersion 1.0.0
@apiName SlaConfigListByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Body:) {Array[String]} sla_config_ids    Danh sách ID SLA
@apiParam (Body:) {String} [target_type]            Kiểu cấu hình nội dung <br>
                                                        <li><code>sales_opportunity_lifecycle</code>: Áp dụng toàn vòng đời CHB</li>
                                                        <li><code>policy_sales_opportunity_status</code>: Áp dụng chính sách theo từng trạng thái của Cơ hội bán</li>


@apiSuccess {Number}  code       Response status
@apiSuccess {String}  message    Response message
@apiSuccess {Array}   data       Danh sách cấu hình SLA
@apiUse sla_config_response
@apiUse condition_apply_response
@apiUse target_response
@apiUse notify_config_response
@apiUse condition_groups_response
@apiUse conditions_response
@apiUse detail_config_response

@apiSuccessExample Response success:
{
    "code": 200,
    "data": [
        {
            "_id": "67ac77e25663e2b669af87c5",
            "condition_apply": {
                "deal_apply": "all",
                "trigger_code": "sales_opportunity_first_employee"
            },
            "created_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2025-02-12T10:28:50.000Z",
            "description": "2121",
            "is_check_total_deal": false,
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "notify_configs": [
                {
                    "detail_configs": [],
                    "reminder_notification": {
                        "instant_notify": "disable",
                        "reminder_type": "reminder_15min",
                        "unit": "day",
                        "value": 0
                    },
                    "type": "resolution_response"
                }
            ],
            "sale_process_id": "674d41221f706f89a9032812",
            "sla_policy": "apply_the_current_sla_policy",
            "status": "disable",
            "targets": [
                {
                    "configs": [
                        {
                            "day": 1,
                            "hour": 0,
                            "minute": 0,
                            "status": "disable",
                            "time_apply": "any_time",
                            "unit": "minute",
                            "value": 1440
                        }
                    ],
                    "status": "enable",
                    "target_type": "sales_opportunity_lifecycle"
                },
                {
                    "configs": [
                        {
                            "day": 1,
                            "hour": 0,
                            "minute": 0,
                            "ratio": 10,
                            "state_code": "EXUFWMJO",
                            "status": "enable",
                            "time_apply": "any_time",
                            "unit": "minute",
                            "value": 1440
                        }
                    ],
                    "status": "enable",
                    "target_type": "policy_sales_opportunity_status"
                }
            ],
            "updated_by": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "updated_time": "2025-02-12T10:37:21.000Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

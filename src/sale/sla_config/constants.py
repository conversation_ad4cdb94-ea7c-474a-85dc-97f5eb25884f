"""
@apiDefine sla_config_response
@apiVersion 1.0.0
@apiSuccess   (Sla Config object)  {String}  [_id]  ID định danh cấu hình SLA
@apiSuccess   (Sla Config object)  {String}  status   Trạng thái cấu hình SLA </br>
                                               <ul>
                                                    <li><code>enable</code>: <PERSON><PERSON><PERSON> cấu hình</li>
                                                    <li><code>disable</code>: Tắt cấu hình</li>
                                               </ul>
@apiSuccess   (Sla Config object)  {String}    name  Tên chính sách SLA
@apiSuccess   (Sla Config object)  {String}    description  Mô tả
@apiSuccess   (Sla Config object)  {String}  sla_policy   Cấu hình áp dụng SLA ngay lập tức cho danh sách CHB đang thoả mãn SLA? <br>
                                                          <ul>
                                                             <li><code>apply_the_current_sla_policy</code>: Á<PERSON> dụng chính sách SLA hiện tại</li>
                                                             <li><code>apply_the_new_sla_policy</code>: <PERSON><PERSON> dụng chính sách SLA mới</li>
                                                          </ul>

@apiSuccess   (Sla Config object)  {String}  updated_by   Id nhân viên cập nhật lần cuối
@apiSuccess   (Sla Config object)  {String}  updated_time   Thời gian cập nhật lần cuối
@apiSuccess   (Sla Config object)  {Object}  condition_apply  Cấu hình điều kiện SLA
@apiSuccess   (Sla Config object)  {Array[Object]}  targets  Danh sách cấu hình nội dung SLA
@apiSuccess   (Sla Config object)  {Array}   notify_config  Cấu hình thông báo SLA
"""

"""
@apiDefine condition_apply_response
@apiVersion 1.0.0

@apiSuccess   (Condition apply)  {String}  trigger_code Thời điểm bắt đầu áp dụng <br>
                                            <li><code>sales_opportunity_first_employee</code>: Phân công cho nhân viên đầu tiên </li>
                                            <li><code>sales_opportunity_status_changed</code>: Khi cơ hội bán được thay đổi trạng thái xử lý </li>
                                            <li><code>sales_opportunity_transferred_process</code>: Khi cơ hội bán được chuyển từ quy trình khác sang quy trình hiện tại </li>
                                            <li><code>sales_opportunity_change_staff_owner</code>: Khi Oppty Owner của CHB được thay đổi </li>
@apiSuccess   (Condition apply)  {Array[String]}  sale_process_ids Danh sách ID quy trình bán (<code>Bắt buộc khi <b>trigger_code</b>: <b>sales_opportunity_transferred_process</b></code>)
@apiSuccess   (Condition apply)  {Array[String]}  states_code  Danh sách mã code trạng thái xử lý
@apiSuccess   (Condition apply)  {String}  deal_apply  Cơ hội bán được áp dụng SLA <br>
                                                       <li><code>all</code>: Tất cả các Cơ hội bán</li>
                                                       <li><code>condition</code>: Theo điều kiện bộ lọc</li>
@apiSuccess   (Condition apply)  {Array[Object]}  condition_groups  Danh sách nhóm điều kiện bộ lọc
"""

"""
@apiDefine condition_groups_response
@apiVersion 1.0.0

@apiSuccess   (Condition groups)  {Number}  group  ID group
@apiSuccess   (Condition groups)  {Number}  order  Sắp xếp
@apiSuccess   (Condition groups)  {Array[Object]}  conditions  Danh sách điều kiện
@apiSuccess   (Condition groups)  {String}  [operator]  Toán tử kết hợp các nhóm điều kiện (<code>Lưu ý: Đối với điều kiện đầu tiên sẽ không có operator</code>)</br>
                                                     <li><code>or</code>: Hoặc</li>
                                                     <li><code>and</code>: Và</li>
"""

"""
@apiDefine conditions_response
@apiVersion 1.0.0

@apiSuccess   (Condition Object)  {String}  code  Mã điều kiện
@apiSuccess   (Condition Object)  {Number|String}  value  Giá trị
@apiSuccess   (Condition Object)  {String}  type   Loại toán tử so sánh
@apiSuccess   (Condition Object)  {String}  display_type   Kiểu hiển thị
@apiSuccess   (Condition Object)  {String}  field_key   Field key kiểm tra điều kiện
@apiSuccess   (Condition Object)  {Number}  field_property   Cấu hình kiểu dữ liệu
"""


"""
@apiDefine target_response
@apiVersion 1.0.0

@apiSuccess   (Target Object)  {String}  target_type  Kiểu cấu hình nội dung <br>
                                            <li><code>sales_opportunity_lifecycle</code>: Áp dụng toàn vòng đời CHB</li>
                                            <li><code>policy_sales_opportunity_status</code>: Áp dụng chính sách theo từng trạng thái của Cơ hội bán</li>
@apiSuccess   (Target Object)  {String}  status  Trạng thái cấu hình 
                                       <li><code>enable</code>: Bật</li>
                                       <li><code>disable</code>: Tắt</li>
@apiSuccess   (Target Object)  {Array[Object]}  configs  Danh sách cấu hình thời hạn hoàn tất xử lý
@apiSuccess   (Target Object)  {String}  [configs.state_code]  Danh sách mã code trạng thái Cơ hội bán (<code> bắt buộc với <b>target_type</b>:</b>policy_sales_opportunity_status</b></code>)
@apiSuccess   (Target Object)  {String}  [configs.time_slot_id]  ID khung giờ làm việc (<code> bắt buộc với <b>time_apply</b>:</b>in_assign_time_frame</b></code>)
@apiSuccess   (Target Object)  {Array[String]}  [configs.time_slot_ids]  Danh sách ID khung thời gian làm việc (<code> bắt buộc với <b>time_apply</b>:</b>in_assign_time_frame</b></code>)
@apiSuccess   (Target Object)  {String}  configs.time_apply  Khung giờ áp dụng SLA <br>
                                                             <li><code>any_time</code>: Áp dụng 24/7</li>
                                                             <li><code>in_assign_time_frame</code>: Trong khung thời gian làm việc của Sale</li>

@apiSuccess   (Target Object)  {Number}  [configs.day]       Số ngày cấu hình 
@apiSuccess   (Target Object)  {Number}  [configs.hour]      Số giờ cấu hình
@apiSuccess   (Target Object)  {Number}  [configs.minute]    Số phút cấu hình
"""

"""
@apiDefine notify_config_response
@apiVersion 1.0.0

@apiSuccess   (Notify Config)  {String}  type  Loại thông báo (<code>resolution_response</code>: <b>Cảnh báo trước khi đến hạn hoàn tất xử lý theo SLA</b>)
@apiSuccess   (Notify Config)  {Object}  reminder_notification  Cấu hình thời gian nhắc nhở sắp đến hạn SLA
@apiSuccess   (Notify Config)  {String}  reminder_notification.reminder_type Kiểu cấu hình thời gian nhắc nhở <br>
                                                <li><code>reminder_15min</code>: Nhắc nhở trước 15 phút</li>
                                                <li><code>reminder_30min</code>: Nhắc nhở trước 30 phút</li>
                                                <li><code>reminder_1hour</code>: Nhắc nhở trước 1 giờ</li>
                                                <li><code>reminder_1day</code>: Nhắc nhở trước 1 ngày</li>
                                                <li><code>on_time_completion</code>: Đúng thời hạn hoàn thành</li>
                                                <li><code>customize_time</code>: Tuỳ chỉnh thời gian</li>
@apiSuccess   (Notify Config)  {Number}  reminder_notification.value Giá trị thời gian cấu hình nhắc nhở
@apiSuccess   (Notify Config)  {String}  reminder_notification.unit  Đơn vị thời gian <br>
                                             <li><code>day</code>: Ngày</li>
                                             <li><code>hour</code>: Giờ</li>
                                             <li><code>minute</code>: Phút</li>
@apiSuccess   (Notify Config)  {String}  reminder_notification.instant_notify       Gửi thông báo ngay khi CHB thỏa mãn điều kiện áp dụng <br>
                                             <li><code>enable</code>: Bật</li>
                                             <li><code>disable</code>: Tắt</li>
@apiSuccess   (Notify Config)  {Array[Object]}  detail_configs  Danh sách cấu hình loại thông báo
"""

"""
@apiDefine detail_config_response
@apiVersion 1.0.0

@apiSuccess   (Detail Config Object)  {String}  type  Loại thông báo </br>
                                               <li><code>email</code>: Email</li>
                                               <li><code>webpush</code>: Web push</li>
                                               <li><code>mobile</code>: Mobile App</li>
@apiSuccess   (Detail Config Object)  {String}  status  Trạng thái </br>
                                                 <li><code>enable</code>: Bật cấu hình</li>
                                                 <li><code>disable</code>: Tắt cấu hình</li>
@apiSuccess   (Detail Config Object)  {Array[String]}  receivers Danh sách đối tượng nhận thông báo <br>
                                                 <li><code>opportunity_supporter</code>: Oppty supporter</li>
                                                 <li><code>opportunity_owner</code>: Optty owner</li>
                                                 <li><code>opportunity_leader_team</code>: Quản lý Team của Oppty Owner</li>
                                                 <li><code>others_receiver</code>: E-mail cụ thể</li>
                                                 <li><code>similar_webpush</code>: Tương đương cấu hình webpush</li>
@apiSuccess   (Detail Config Object)  {Array[String]}  [other_receivers] Danh sách Email khác (<code>Bắt buộc khi receivers có thêm giá trị <b>others_receiver</b></code>)
"""


"""
@apiDefine response_check_total_deal
@apiVersion 1.0.0

@apiSuccess   (ResponseCheckTotalDeal)  {Number}  code  Mã code lỗi
@apiSuccess   (ResponseCheckTotalDeal)  {Object}  data  Thông tin kiểm tra
@apiSuccess   (ResponseCheckTotalDeal)  {Number}  data.total_deal  Số lượng CHB
@apiSuccess   (ResponseCheckTotalDeal)  {Number}  data.code        Mã code xử lý
"""


############################
# API Param
############################
"""
@apiDefine sla_config_param
@apiVersion 1.0.0
@apiParam   (Sla Config object)  {String}  status   Trạng thái cấu hình SLA </br>
                                               <ul>
                                                    <li><code>enable</code>: Bật cấu hình</li>
                                                    <li><code>disable</code>: Tắt cấu hình</li>
                                               </ul>
@apiParam   (Sla Config object)  {String}  name  Tên chính sách SLA
@apiParam   (Sla Config object)  {String}  description  Mô tả
@apiParam   (Sla Config object)  {Object}  condition_apply  Cấu hình điều kiện SLA
@apiParam   (Sla Config object)  {Array[Object]}  targets  Danh sách cấu hình nội dung SLA
@apiParam   (Sla Config object)  {Array}   notify_config  Cấu hình thông báo SLA
@apiParam   (Sla Config object)  {Boolean}  is_check_total_deal  Kiểm tra CHB </br>
                                           <ul>
                                                <li><code>true</code>: Kiểm tra số lượng CHB đang áp dụng SLA</li>
                                                <li><code>false</code>: Không kiểm tra số lượng CHB đang áp dụng SLA</li>
                                           </ul>
@apiParam   (Sla Config object)  {String}  sla_policy   Cấu hình áp dụng SLA ngay lập tức cho danh sách CHB đang thoả mãn SLA? <br>
                                                          <ul>
                                                             <li><code>apply_the_current_sla_policy</code>: Áp dụng chính sách SLA hiện tại</li>
                                                             <li><code>apply_the_new_sla_policy</code>: Áp dụng chính sách SLA mới</li>
                                                          </ul>
"""

"""
@apiDefine condition_apply_param
@apiVersion 1.0.0

@apiParam   (Condition apply)  {String}  trigger_code Thời điểm bắt đầu áp dụng <br>
                                            <li><code>sales_opportunity_first_employee</code>: Phân công cho nhân viên đầu tiên </li>
                                            <li><code>sales_opportunity_status_changed</code>: Khi cơ hội bán được thay đổi trạng thái xử lý </li>
                                            <li><code>sales_opportunity_transferred_process</code>: Khi cơ hội bán được chuyển từ quy trình khác sang quy trình hiện tại </li>
                                            <li><code>sales_opportunity_change_staff_owner</code>: Khi Oppty Owner của CHB được thay đổi </li>
@apiParam   (Condition apply)  {Array[String]}  sale_process_ids Danh sách ID quy trình bán (<code>Bắt buộc khi <b>trigger_code</b>: <b>sales_opportunity_transferred_process</b></code>)
@apiParam   (Condition apply)  {Array[String]}  states_code  Danh sách mã code trạng thái xử lý
@apiParam   (Condition apply)  {String}  deal_apply  Cơ hội bán được áp dụng SLA <br>
                                                       <li><code>all</code>: Tất cả các Cơ hội bán</li>
                                                       <li><code>condition</code>: Theo điều kiện bộ lọc</li>
@apiParam   (Condition apply)  {Array[Object]}  condition_groups  Danh sách nhóm điều kiện bộ lọc
"""

"""
@apiDefine condition_groups_param
@apiVersion 1.0.0

@apiParam   (Condition groups)  {Number}  group  ID group
@apiParam   (Condition groups)  {Number}  order  Sắp xếp
@apiParam   (Condition groups)  {Array[Object]}  conditions  Danh sách điều kiện
@apiParam   (Condition groups)  {String}  [operator]  Toán tử kết hợp các nhóm điều kiện (<code>Lưu ý: Đối với điều kiện đầu tiên sẽ không có operator</code>)</br>
                                                     <li><code>or</code>: Hoặc</li>
                                                     <li><code>and</code>: Và</li>
"""

"""
@apiDefine conditions_param
@apiVersion 1.0.0

@apiParam   (Condition Object)  {String}  code  Mã điều kiện
@apiParam   (Condition Object)  {Number|String}  value  Giá trị
@apiParam   (Condition Object)  {String}  type   Loại toán tử so sánh
@apiParam   (Condition Object)  {String}  display_type   Kiểu hiển thị
@apiParam   (Condition Object)  {String}  field_key   Field key kiểm tra điều kiện
@apiParam   (Condition Object)  {Number}  field_property   Cấu hình kiểu dữ liệu
"""

"""
@apiDefine target_param
@apiVersion 1.0.0

@apiParam   (Target Object)  {String}  target_type  Kiểu cấu hình nội dung <br>
                                            <li><code>sales_opportunity_lifecycle</code>: Áp dụng toàn vòng đời CHB</li>
                                            <li><code>policy_sales_opportunity_status</code>: Áp dụng chính sách theo từng trạng thái của Cơ hội bán</li>
@apiParam   (Target Object)  {String}  status  Trạng thái cấu hình 
                                       <li><code>enable</code>: Bật</li>
                                       <li><code>disable</code>: Tắt</li>
@apiParam   (Target Object)  {Array[Object]}  configs  Danh sách cấu hình thời hạn hoàn tất xử lý
@apiParam   (Target Object)  {String}  [configs.state_code]  Danh sách mã code trạng thái Cơ hội bán (<code> bắt buộc với <b>target_type</b>:</b>policy_sales_opportunity_status</b></code>)
@apiParam   (Target Object)  {String}  [configs.time_slot_id]  ID khung giờ làm việc (<code> bắt buộc với <b>time_apply</b>:</b>in_assign_time_frame</b></code>)
@apiParam   (Target Object)  {String}  configs.time_apply  Khung giờ áp dụng SLA <br>
                                                       <li><code>any_time</code>: Áp dụng 24/7</li>
                                                       <li><code>in_assign_time_frame</code>: Trong khung thời gian làm việc của Sale</li>

@apiParam   (Target Object)  {Number}  configs.day  Số ngày cấu hình
@apiParam   (Target Object)  {Number}  configs.hour  Số giờ cấu hình
@apiParam   (Target Object)  {Number}  configs.minute  Số phút cấu hình
"""

"""
@apiDefine notify_config_param
@apiVersion 1.0.0

@apiParam   (Notify Config)  {String}  type  Loại thông báo (<code>resolution_response</code>: <b>Cảnh báo trước khi đến hạn hoàn tất xử lý theo SLA</b>)
@apiParam   (Notify Config)  {Object}  reminder_notification  Cấu hình thời gian nhắc nhở sắp đến hạn SLA
@apiParam   (Notify Config)  {Number}  reminder_notification.value Giá trị thời gian cấu hình nhắc nhở
@apiParam   (Notify Config)  {String}  reminder_notification.unit  Đơn vị thời gian <br>
                                             <li><code>day</code>: Ngày</li>
                                             <li><code>hour</code>: Giờ</li>
                                             <li><code>minute</code>: Phút</li>
@apiParam   (Notify Config)  {String}  reminder_notification.instant_notify       Gửi thông báo ngay khi CHB thỏa mãn điều kiện áp dụng <br>
                                             <li><code>enable</code>: Bật</li>
                                             <li><code>disable</code>: Tắt</li>
@apiParam   (Notify Config)  {String}  reminder_notification.reminder_type Kiểu cấu hình thời gian nhắc nhở <br>
                                                <li><code>reminder_15min</code>: Nhắc nhở trước 15 phút</li>
                                                <li><code>reminder_30min</code>: Nhắc nhở trước 30 phút</li>
                                                <li><code>reminder_1hour</code>: Nhắc nhở trước 1 giờ</li>
                                                <li><code>reminder_1day</code>: Nhắc nhở trước 1 ngày</li>
                                                <li><code>on_time_completion</code>: Đúng thời hạn hoàn thành</li>
                                                <li><code>customize_time</code>: Tuỳ chỉnh thời gian</li>
@apiParam   (Notify Config)  {Array[Object]}  detail_configs  Danh sách cấu hình loại thông báo
"""

"""
@apiDefine detail_config_param
@apiVersion 1.0.0

@apiParam   (Detail Config Object)  {String}  type  Loại thông báo </br>
                                               <li><code>email</code>: Email</li>
                                               <li><code>webpush</code>: Web push</li>
                                               <li><code>mobile</code>: Web push</li>
@apiParam   (Detail Config Object)  {String}  status  Trạng thái </br>
                                                 <li><code>enable</code>: Bật cấu hình</li>
                                                 <li><code>disable</code>: Tắt cấu hình</li>
@apiParam   (Detail Config Object)  {Array[String]}  receivers Danh sách đối tượng nhận thông báo <br>
                                                 <li><code>opportunity_supporter</code>: Oppty supporter</li>
                                                 <li><code>opportunity_owner</code>: Optty owner</li>
                                                 <li><code>opportunity_leader_team</code>: Quản lý Team của Oppty Owner</li>
                                                 <li><code>others_receiver</code>: E-mail cụ thể</li>
                                                 <li><code>similar_webpush</code>: Tương đương cấu hình webpush</li>
@apiParam   (Detail Config Object)  {Array[String]}  [other_receivers] Danh sách Email khác (<code>Bắt buộc khi receivers có thêm giá trị <b>others_receiver</b></code>)
"""


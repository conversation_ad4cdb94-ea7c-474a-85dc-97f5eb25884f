#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TungDD
    Company: Mobio
    Date Created: 15/10/2021
"""
# ---------- Danh sách Event -----------
"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/action/filter/activity      Lấy danh sách event của Deal
@apiGroup Event
@apiVersion 1.0.0
@apiName ListEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=EMAIL,SOCIAL,SMS,CALLCENTER,OTHER}                  [source_event]         Tab muốn lấy danh sách event. Mặc định khi đứng ở tab <code>Tất cả</code> thì không cần truyền lên giá trị này.
                                                                                                        <ul>
                                                                                                            <li><code>EMAIL</code>: Tab <PERSON></li>
                                                                                                            <li><code>SOCIAL</code>: Tab Online và mạng xã hội</li>
                                                                                                            <li><code>SMS</code>: Tab SMS</li>
                                                                                                            <li><code>CALLCENTER</code>: Tab cuộc gọi</li>
                                                                                                            <li><code>OTHER</code>: Tab khác</li>
                                                                                                        </ul> 

@apiParam   (BODY:) {Array}                     brand_ids              Danh sách các thương hiệu cần tìm kiếm
@apiParam   (BODY:) {Array}                     staff_ids              Danh sách nhân viên tương tác với Profile
@apiParam   (BODY:) {string}                    start_time             Thời gian bắt đầu tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {string}                    end_time               Thời gian kết thúc tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {Array}                     activities             Danh sách hoạt động cần tìm kiếm
                                                                        <ul>
                                                                            <li><code>UpdateDeal</code>: Cập nhật thông tin đơn hàng</li>
                                                                            <li><code>AssignmentForwardDeal</code>: Phân công chuyển tiếp đơn hàng</li>
                                                                            <li><code>Email</code>: E-mail</li>
                                                                            <li><code>Facebook</code>: Tương tác qua Facebook</li>
                                                                            <li><code>Zalo</code>: Tương tác qua Zalo</li>
                                                                            <li><code>SMS</code>: SMS</li>
                                                                            <li><code>Callcenter</code>: Gọi điện</li>
                                                                            <li><code>Other</code>: Khác</li>
                                                                            <li><code>UpdateCallReport</code>: Cập nhật call report</li>
                                                                        </ul>

@apiParamExample {json} Body example
{
    "brand_ids": [].
    "staff_ids": [],
    "start_time": "2021-10-15",
    "end_time": "2022-10-15",
    "activity": ["UpdateDeal"]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách các Event tương ứng.

@apiSuccess {String}            data.merchant_id              Định danh merchant.
@apiSuccess {String}            data.deal_id                  Định danh đơn hàng.
@apiSuccess {String}            data.event_id                 Định danh Event 
@apiSuccess {String}            data.source_event               Loại Event
                                                              <ul>
                                                                    <li><code>DEAL</code>: Loại event phát sinh của đơn hàng</li>
                                                                    <li><code>EMAIL</code>: Loại event E-mail</li>
                                                                    <li><code>SOCIAL</code>: Loại event phát sinh từ mạng xã hội</li>
                                                                    <li><code>SMS</code>: Loại event phát sinh từ SMS</li>
                                                                    <li><code>CALLCENTER</code>: Loại event phát sinh từ Gọi điện</li>
                                                                    <li><code>OTHER</code>: Khác</li>
                                                              </ul>
@apiSuccess {String}            data.event_type             Loại của chi tiết Event
                                                            <ul>Source event là <code>DEAL</code>
                                                                <li><code>InitDeal</code>: Tạo đơn hàng</li>
                                                                <li>
                                                                    <code>UpdateProfile</code>: Cập nhật liên hệ profile và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li>
                                                                    <code>UpdateCompanies</code>: Cập nhật liên hệ công ty và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li><code>UpdateTicket</code>: Cập nhật liên hệ ticket và đơn hàng</li>
                                                                <li><code>AddProductCategoriesMain</code>: Gắn liên hệ đơn hàng và danh mục chính</li>
                                                                <li><code>AddProductCategoriesSub</code>: Gắn liên hệ đơn hàng và danh mục phụ</li>
                                                                <li><code>RemoveProductCategoriesMain</code>: Gỡ liên hệ đơn hàng và danh mục chính</li>
                                                                <li><code>AddProductBank</code>: Gắn liên hệ giữa cơ hội bán và sản phẩm bank</li>
                                                                <li><code>RemoveProductBank</code>: Gỡ liên hệ giữa cơ hội bán và sản phẩm bank</li>
                                                                <li><code>AddProductLine</code>: Gắn liên hệ giữa cơ hội bán và dòng sản phẩm</li>
                                                                <li><code>RemoveProductLine</code>: Gỡ liên hệ giữa cơ hội bán và dòng sản phẩm</li>
                                                                <li><code>AddProductType</code>: Gắn liên hệ giữa cơ hội bán và loại sản phẩm</li>
                                                                <li><code>RemoveProductType</code>: Gỡ liên hệ giữa cơ hội bán và loại sản phẩm</li>
                                                                <li>
                                                                    <code>UpdateMediaDeal</code>: Cập nhật liên hệ media và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li><code>UpdateCallReport</code>: Cập nhật call report</li>
                                                            </ul>
                                                            <ul>Source event là <code>SOCIAL</code>
                                                                <li><code>1</code>: "Facebook"</li>
                                                                <li><code>2</code>: "Zalo"</li>
                                                                <li><code>3</code>: "Instagram"</li>
                                                                <li><code>4</code>: "Youtube"</li>
                                                                <li><code>5</code>: "App"</li>
                                                                <li><code>6</code>: "Line"</li>
                                                                <li><code>7</code>: "Mobio_chat_tool"</li>
                                                            </ul>
                                                            <ul>Source event là <code>CALLCENTER</code></ul>
                                                            
                                                            
                                                            
@apiSuccess {String}            data.staff_update_deal        Định danh nhân viên cập nhật thông tin
@apiSuccess {String}            data.line_event               Event tạo từ hệ thống hoặc người dùng: merchant, profile
@apiSuccess {String}            data.body                     Thông tin của Event
@apiSuccess {Object}            data.action_time              Thời gian diễn ra Event
@apiSuccess {Object}            data.created_time             Thời điểm tạo
@apiSuccess {Object}            data.data_approval            Thông tin duyệt (call report)
@apiSuccess {String}            data.data_approval.action             Hành động
@apiSuccess {String}            data.data_approval.opinion            Ý kiến
@apiSuccess {String}            data.data_approval.participants_enterprise_customer_relationship         Tên QHKHDN
@apiSuccess {String}            data.data_approval.participants_staff_code       Mã nhân viên 
@apiSuccess {String}            data.data_approval.participants_title            Chức danh
@apiSuccess {Array}            data.data_approval.accounts_to            Danh sách người nhận
                      

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateDeal
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "information": [
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "description",
                "field_name": "description",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [
                    "333"
                ],
                "change": [],
                "remove": []
            },
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "reason_fail",
                "field_name": "reason_fail",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [],
                "change": [
                    {
                        "from": "buồn quá không mua",
                        "to": "buồn quá không mua a"
                    }
                ],
                "remove": []
            }
        ],
        "dynamic": [
            {
                "field_name": "Radio button",
                "field_key": "_dyn_radio_button_1633599591661",
                "field_property": 2,
                "display_type": "radio",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    "ba"
                ],
                "change": [],
                "remove": []
            },
            {
                "field_name": "Single-line text chữ",
                "field_key": "_dyn_single_line_text_chu_1631458349843",
                "field_property": 2,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [],
                "change": [
                    {
                        "from": "123123123123",
                        "to": "12312312399"
                    }
                ],
                "remove": []
            },
            {
                "field_name": "Single-line text số",
                "field_key": "_dyn_single_line_text_so_1631181218494",
                "field_property": 1,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    12312312
                ],
                "change": [],
                "remove": []
            }
        ]
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateProfile
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "source_event": "DEAL",
    "event_type": "UpdateProfile",
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateTicket
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateTicket",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateCompanies
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateCompanies",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}
@apiSuccessExample {json} Response source_event: DEAL, event_type: InitDeal
{
    "_id" : "6177a299ffdea8e0c58fe305",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "6177a296573c1357e0a67460",
    "event_id" : "6177a298ffdea8e0c58fe303",
    "source_event" : "DEAL",
    "event_type" : "InitDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event" : "merchant",
    "source" : "SALE",
    "action_time" : **********.890302,
    "created_time" : "2021-10-15T15:05:18.856",
    "body" : {
        "profiles" : [
            {
                "id" : "6afa7308-ccf3-4974-8478-3a0f4b44ade0",
                "name" : "Cam Edit"
            }
        ],
        "assignee_id" : "01aa5dc3-36a6-42b1-b026-e7cdba3751cc",
        "state_code" : "QQ6CYXWM",
        "sale_process_id" : "61768dad74f73e51a119ea2b",
        "sale_process_name" : "Quy trình hoa hướng dương (không sửa xóa)",
        "state_name" : "Có thông tin Leads",
        "state_ratio" : 10
    }
}

@apiSuccessExample {json} Response source_event: SOCIAL
{
    "_id" : "61790f31df1001de4bd72334",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "be8bde12-c514-4788-bef8-7a85ff0d2b55",
    "event_id" : ObjectId("61790f28df1001de4bd72332"),
    "source_event" : "SOCIAL",
    "event_type" : 1,
    "staff_update_deal" : {
        "id" : "823ed8f0-83b2-491d-936d-32c2accfa15b",
        "email" : "<EMAIL>",
        "fullname" : "Admin24",
        "phone_number" : "+***********",
        "username" : "admin24@pingcomshop"
    },
    "line_event" : "merchant",
    "source" : "SOCIAL",
    "action_time" : 1635323521.467509,
    "created_time" : ISODate("2021-10-27T15:34:48.861+07:00"),
    "body" : {
        "profile_data" : {
            "profile_id" : "f78ebd1c-0854-46b0-b85b-a4cf9ddb0298",
            "id" : "f78ebd1c-0854-46b0-b85b-a4cf9ddb0298",
            "name" : "Đỗ Hòa"
        },
        "page_social_id" : "858617877680201",
        "content" : "Tùng Dịch",
        "page_name" : "Thiết bị văn phòng",
        "user_social_id" : "3551827758269419"
    }
}

@apiSuccessExample {json} Response, SourceEvent:Email:
{
    "id": "",
    "merchant_id": "",
    "deal_id": "",
    "event_id": "",
    "source_event": "Email",
    "event_type":"",
    "staff_update_deal" : {
            "id" : "d7e63da1-a47e-43e2-9b78-44d43433f7ec",
            "email" : "<EMAIL>",
            "fullname" : "sale",
            "phone_number" : "+84978348445",
            "username" : "sale@thanhtest11"
    },
    "action_time" : 1636613856.258556,
    "source": "EMAIL",
    "body": {
        "profile_data": profile_data,
        "from_email": ""		// email gửi
        "to_email": [""]		// danh sách người nhận
        "cc_email": [""]
        "bcc_email": [""]
        "subject": ""		// tiêu đề email 
        "content": ""		// nội dung email 
        "sender_domain": "mobio.vn", // có đối với kiểu email_mkt
        "related_to": {
            "company_ids": [], // Danh sách company có liên quan tới
            "ticket_ids": [], // Danh sách ticket có liên quan tới
            "profile_ids": [], // Danh sách Profile có liên quan tới
            "order_ids": [], // Danh sách các deal có liên quan tới
        },
        "attachments": [""] // Danh sách url file media 
        "status": {
            "type": "success", // Trạng thái của email đang gửi, đã gửi, gửi lỗi: pending, success, failed
            "message": "", // Trong trường hợp gửi mail lỗi thì cần gửi kèm nguyên nhân lỗi trong field này
        },
        "action_time": "",
    }
}

@apiSuccessExample {json} Response, SourceEvent: Callcenter:
{
    "_id" : "619b0507291638612986860d",
    "unique_value" : "call-vn-1-99V79H284Q-1637519225215",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "6185fc94d3145f3fb2dac5cf",
    "event_id" : "619b0507291638612986860b",
    "source_event" : "CALLCENTER",
    "event_type" : "SaleCallcenter",
    "staff_update_deal" : {
        "id" : "f48aad19-cbe9-4c49-baa2-b3d3e8a29bd4",
        "email" : "<EMAIL>",
        "fullname" : "Ngọc Ánh",
        "phone_number" : "+84334303858",
        "username" : "anhttn@pingcomshop"
    },
    "line_event" : "merchant",
    "activity" : "Callcenter",
    "source" : "CALLCENTER",
    "action_time" : 1637549229
    "created_time" : "2021-11-22T09:48:39.395Z,
    "body" : {
        "profile_data" : {
            "profile_id" : "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
            "profile_phone_number" : "0328914167",
            "id" : "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
            "name" : "Ngoan "
        },
        "switchboard" : "842473005768",
        "call_id" : "call-vn-1-99V79H284Q-1637519225215",
        "call_status" : 7,
        "so_may_le" : "106",
        "hot_line" : null,
        "attachments" : [
            {
                "url" : "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/call-vn-1-99V79H284Q-1637519225215.mp3",
                "local_path" : "/media/data/public_resources/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/call-vn-1-99V79H284Q-1637519225215.mp3",
                "filename" : "call-vn-1-99V79H284Q-1637519225215.mp3",
                "format" : "audio/mpeg"
            }
        ],
        "related_to" : {
            "profile_ids" : [
                "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
                "137c56e5-d56d-4dbc-9729-fa997e0294c3"
            ],
            "ticket_ids" : [
                "d8b34170-fb3b-401a-9d25-623844b4ef15"
            ],
            "order_ids" : [
                "6185fc94d3145f3fb2dac5cf"
            ]
        }
    }
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductCategoriesMain
{
	"_id": "627cadbed757dbd1ceb87ab9",
	"action_time": 1652338109.170179,
	"activity": "UpdateDeal",
	"body": {
		"add": [{
			"id": "626b50c62d0663bd2efb4707",
			"name": {
				"vi": "Danh m\u1ee5c c\u1ea5p 2"
			}
		}, {
			"id": "6278888ac7048b4e346a69f5",
			"name": {
				"vi": "Danh m\u1ee5c ch\u00ednh"
			}
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T06:48:29.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cadbed757dbd1ceb87ab7",
	"event_type": "AddProductCategoriesMain",
	"id": "627cadbed757dbd1ceb87ab9",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cadbd21e0c966a9a4bdd1"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductCategoriesSub
{
	"_id": "627cadbed757dbd1ceb87abc",
	"action_time": 1652338109.170179,
	"activity": "UpdateDeal",
	"body": {
		"add": [{
			"root_category": {
				"id": "626b50c62d0663bd2efb4707",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			},
			"sub_categories": [{
				"id": "626b51772d0663bd2efb4708",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			}, {
				"id": "626b51772d0663bd2efb4709",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 3"
				}
			}]
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T06:48:29.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cadbed757dbd1ceb87aba",
	"event_type": "AddProductCategoriesSub",
	"id": "627cadbed757dbd1ceb87abc",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cadbd21e0c966a9a4bdd1"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductCategoriesMain
{
	"_id": "627cbbb0d757dbd1ceb87bb3",
	"action_time": 1652341679.769229,
	"activity": "UpdateDeal",
	"body": {
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"remove": [{
			"id": "6278888ac7048b4e346a69f5",
			"name": {
				"vi": "Danh m\u1ee5c ch\u00ednh"
			}
		}],
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T07:47:59.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cbbb0d757dbd1ceb87bb1",
	"event_type": "RemoveProductCategoriesMain",
	"id": "627cbbb0d757dbd1ceb87bb3",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cbbaf1b36ae850ead6167"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductCategoriesMain
{
	"_id": "627cbdbbd757dbd1ceb87bd0",
	"action_time": 1652342202.881001,
	"activity": "UpdateDeal",
	"body": {
		"remove": [{
			"root_category": {
				"id": "626b50c62d0663bd2efb4707",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			},
			"sub_categories": [{
				"id": "626b51772d0663bd2efb4708",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			}]
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T07:56:43.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cbdbbd757dbd1ceb87bce",
	"event_type": "RemoveProductCategoriesSub",
	"id": "627cbdbbd757dbd1ceb87bd0",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cbdba9d69765958672e4f"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductType
{
    "_id": "62b139801298bda9228463d1",
    "action_time": 1655781759.375529,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "tags",
        "field_key": "product_types",
        "field_name": "product_types",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "product_line": { // Thông tin dòng sản phẩm
                    "id": "62aa7b0b181e77000e3afe29", 
                    "name": "[Test] Tùng 01"
                },
                "product_types": [
                    {
                        "id": "62aa96843af16c000d1dfaf8",
                        "level": 1, // Cấp loại sản phẩm
                        "name": "a" # Tên loại sản phẩm
                    }
                ]
            }
        ],
        "required": false,
        "translate_key": "i18n_subcategories"
    },
    "created_time": "2022-06-21T03:22:39.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b139801298bda9228463cf",
    "event_type": "RemoveProductType",
    "id": "62b139801298bda9228463d1",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1397f76bf3e9d77331fce"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductLine
{
    "_id": "62b1397f1298bda9228463cd",
    "action_time": 1655781759.375529,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "dropdown_single_line",
        "field_key": "product_line",
        "field_name": "product_line",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "id": "62aa7b0b181e77000e3afe29",
                "name": "[Test] Tùng 01"
            }
        ],
        "required": false,
        "translate_key": "i18n_product_category"
    },
    "created_time": "2022-06-21T03:22:39.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b1397f1298bda9228463cb",
    "event_type": "RemoveProductLine",
    "id": "62b1397f1298bda9228463cd",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1397f76bf3e9d77331fce"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductBank
{
    "_id": "62b138fd1298bda9228463b9",
    "action_time": **********.971797,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "dropdown_single_line",
        "field_key": "products_bank",
        "field_name": "products_bank",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "id": "62b03b964ef591000fd000a2",
                "name": "sản phẩm 1"
            }
        ],
        "required": false,
        "translate_key": "i18n_product"
    },
    "created_time": "2022-06-21T03:20:29.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b138fd1298bda9228463b7",
    "event_type": "RemoveProductBank",
    "id": "62b138fd1298bda9228463b9",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b138fca918469ec15424c0"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductType
{
    "_id": "62b137771298bda922846383",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "product_line": {
                    "id": "62aa7b0b181e77000e3afe29",
                    "name": "[Test] Tùng 01"
                },
                "product_types": [
                    {
                        "id": "62aa96843af16c000d1dfaf8",
                        "level": 1,
                        "name": "a"
                    }
                ]
            }
        ],
        "display_type": "tags",
        "field_key": "product_types",
        "field_name": "product_types",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_subcategories"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137771298bda922846381",
    "event_type": "AddProductType",
    "id": "62b137771298bda922846383",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductLine
{
    "_id": "62b137761298bda92284637f",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "id": "62aa7b0b181e77000e3afe29",
                "name": "[Test] Tùng 01"
            }
        ],
        "display_type": "dropdown_single_line",
        "field_key": "product_line",
        "field_name": "product_line",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_product_category"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137761298bda92284637d",
    "event_type": "AddProductLine",
    "id": "62b137761298bda92284637f",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: Sale, EventType: UpdateMediaDeal
{
    "_id": "62de6dcbb483efaabc8e5fd8",
    "action_time": 1658744255.607341,
    "activity": "Other",
    "body": {
        "add": [
            {
                "_id": "62de6dbf812bca51bd423890",
                "created_time": "2022-07-25T10:17:35Z",
                "entity_id": "62d1315f788e91d857f24115",
                "entity_type": "detail_deal",
                "id": "62de6dbf812bca51bd423890",
                "info": {
                    "format_file": "image/jpeg",
                    "local_path": "/media/data/public_resources/static/87d9e1f3-6da0-415f-b418-9ba9f4fe5523/Dr-Strange-Wallpapers.jpeg",
                    "title": "Dr-Strange-Wallpapers.jpeg",
                    "type_media": [
                        "identification"
                    ],
                    "url": "https://t1.mobio.vn/static/87d9e1f3-6da0-415f-b418-9ba9f4fe5523/Dr-Strange-Wallpapers.jpeg"
                },
                "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
                "updated_time": "2022-07-25T10:17:35Z"
            }
        ],
        "remove": []
    },
    "deal_id": "62d1315f788e91d857f24115",
    "event_type": "UpdateMediaDeal",
    "id": "62de6dcbb483efaabc8e5fd8",
    "line_event": "merchant",
    "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
    "source": "SALE",
    "source_event": "Deal",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin Owner",
        "id": "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737",
        "phone_number": "+***********",
        "username": "admin@msbeb"
    }
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductBank
{
    "_id": "62b137761298bda92284637b",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "id": "62b03b964ef591000fd000a2",
                "name": "sản phẩm 1"
            }
        ],
        "display_type": "dropdown_single_line",
        "field_key": "products_bank",
        "field_name": "products_bank",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_product"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137761298bda922846379",
    "event_type": "AddProductBank",
    "id": "62b137761298bda92284637b",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}
"""

# ---------- Danh sách Event -----------
"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/action/filter/activity      Lấy danh sách event của Deal
@apiGroup Event
@apiVersion 1.0.1
@apiName ListEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=EMAIL,SOCIAL,SMS,CALLCENTER,OTHER}                  [source_event]         Tab muốn lấy danh sách event. Mặc định khi đứng ở tab <code>Tất cả</code> thì không cần truyền lên giá trị này.
                                                                                                        <ul>
                                                                                                            <li><code>EMAIL</code>: Tab Email</li>
                                                                                                            <li><code>SOCIAL</code>: Tab Online và mạng xã hội</li>
                                                                                                            <li><code>SMS</code>: Tab SMS</li>
                                                                                                            <li><code>CALLCENTER</code>: Tab cuộc gọi</li>
                                                                                                            <li><code>OTHER</code>: Tab khác</li>
                                                                                                        </ul> 

@apiParam   (BODY:) {Array}                     brand_ids              Danh sách các thương hiệu cần tìm kiếm
@apiParam   (BODY:) {Array}                     staff_ids              Danh sách nhân viên tương tác với Profile
@apiParam   (BODY:) {string}                    start_time             Thời gian bắt đầu tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {string}                    end_time               Thời gian kết thúc tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {Array}                     activities             Danh sách hoạt động cần tìm kiếm
                                                                        <ul>
                                                                            <li><code>UpdateDeal</code>: Cập nhật thông tin đơn hàng</li>
                                                                            <li><code>AssignmentForwardDeal</code>: Phân công chuyển tiếp đơn hàng</li>
                                                                            <li><code>Email</code>: E-mail</li>
                                                                            <li><code>Facebook</code>: Tương tác qua Facebook</li>
                                                                            <li><code>Zalo</code>: Tương tác qua Zalo</li>
                                                                            <li><code>SMS</code>: SMS</li>
                                                                            <li><code>Callcenter</code>: Gọi điện</li>
                                                                            <li><code>Other</code>: Khác</li>
                                                                            <li><code>UpdateCallReport</code>: Cập nhật call report</li>
                                                                        </ul>

@apiParamExample {json} Body example
{
    "brand_ids": [].
    "staff_ids": [],
    "start_time": "2021-10-15",
    "end_time": "2022-10-15",
    "activity": ["UpdateDeal"]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách các Event tương ứng.

@apiSuccess {String}            data.merchant_id              Định danh merchant.
@apiSuccess {String}            data.deal_id                  Định danh đơn hàng.
@apiSuccess {String}            data.event_id                 Định danh Event 
@apiSuccess {String}            data.source_event               Loại Event
                                                              <ul>
                                                                    <li><code>DEAL</code>: Loại event phát sinh của đơn hàng</li>
                                                                    <li><code>EMAIL</code>: Loại event E-mail</li>
                                                                    <li><code>SOCIAL</code>: Loại event phát sinh từ mạng xã hội</li>
                                                                    <li><code>SMS</code>: Loại event phát sinh từ SMS</li>
                                                                    <li><code>CALLCENTER</code>: Loại event phát sinh từ Gọi điện</li>
                                                                    <li><code>OTHER</code>: Khác</li>
                                                              </ul>
@apiSuccess {String}            data.group_configs          Nhóm trường thông tin (<b>Dùng thông tin này để hiển thị nhóm trường thông tin thay đổi của field</b>)
@apiSuccess {String}            data.group_configs._id              ID nhóm trường thông tin
@apiSuccess {String}            data.group_configs.group_index      Thứ tự nhóm trường thông tin
@apiSuccess {String}            data.group_configs.group_key        Mã key nhóm trường thôn tin
@apiSuccess {String}            data.group_configs.group_name       Tên nhóm trường thông tin
@apiSuccess {String}            data.group_configs.is_base          <code>True</code> Là nhóm field mặc định <code>False</code> Nhóm field do người dùng tạo

@apiSuccess {String}            data.event_type             Loại của chi tiết Event
                                                            <ul>Source event là <code>DEAL</code>
                                                                <li><code>InitDeal</code>: Tạo đơn hàng</li>
                                                                <li>
                                                                    <code>UpdateProfile</code>: Cập nhật liên hệ profile và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li>
                                                                    <code>UpdateCompanies</code>: Cập nhật liên hệ công ty và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li><code>UpdateTicket</code>: Cập nhật liên hệ ticket và đơn hàng</li>
                                                                <li><code>AddProductCategoriesMain</code>: Gắn liên hệ đơn hàng và danh mục chính</li>
                                                                <li><code>AddProductCategoriesSub</code>: Gắn liên hệ đơn hàng và danh mục phụ</li>
                                                                <li><code>RemoveProductCategoriesMain</code>: Gỡ liên hệ đơn hàng và danh mục chính</li>
                                                                <li><code>AddProductBank</code>: Gắn liên hệ giữa cơ hội bán và sản phẩm bank</li>
                                                                <li><code>RemoveProductBank</code>: Gỡ liên hệ giữa cơ hội bán và sản phẩm bank</li>
                                                                <li><code>AddProductLine</code>: Gắn liên hệ giữa cơ hội bán và dòng sản phẩm</li>
                                                                <li><code>RemoveProductLine</code>: Gỡ liên hệ giữa cơ hội bán và dòng sản phẩm</li>
                                                                <li><code>AddProductType</code>: Gắn liên hệ giữa cơ hội bán và loại sản phẩm</li>
                                                                <li><code>RemoveProductType</code>: Gỡ liên hệ giữa cơ hội bán và loại sản phẩm</li>
                                                                <li>
                                                                    <code>UpdateMediaDeal</code>: Cập nhật liên hệ media và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li><code>UpdateCallReport</code>: Cập nhật call report</li>
                                                            </ul>
                                                            <ul>Source event là <code>SOCIAL</code>
                                                                <li><code>1</code>: "Facebook"</li>
                                                                <li><code>2</code>: "Zalo"</li>
                                                                <li><code>3</code>: "Instagram"</li>
                                                                <li><code>4</code>: "Youtube"</li>
                                                                <li><code>5</code>: "App"</li>
                                                                <li><code>6</code>: "Line"</li>
                                                                <li><code>7</code>: "Mobio_chat_tool"</li>
                                                            </ul>
                                                            <ul>Source event là <code>CALLCENTER</code></ul>



@apiSuccess {String}            data.staff_update_deal        Định danh nhân viên cập nhật thông tin
@apiSuccess {String}            data.line_event               Event tạo từ hệ thống hoặc người dùng: merchant, profile
@apiSuccess {String}            data.body                     Thông tin của Event
@apiSuccess {Object}            data.action_time              Thời gian diễn ra Event
@apiSuccess {Object}            data.created_time             Thời điểm tạo
@apiSuccess {Object}            data.data_approval            Thông tin duyệt (call report)
@apiSuccess {String}            data.data_approval.action             Hành động
@apiSuccess {String}            data.data_approval.opinion            Ý kiến
@apiSuccess {String}            data.data_approval.participants_enterprise_customer_relationship         Tên QHKHDN
@apiSuccess {String}            data.data_approval.participants_staff_code       Mã nhân viên 
@apiSuccess {String}            data.data_approval.participants_title            Chức danh
@apiSuccess {Array}            data.data_approval.accounts_to            Danh sách người nhận


@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateDeal
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "information": [
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "description",
                "field_name": "description",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [
                    "333"
                ],
                "change": [],
                "remove": []
            },
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "reason_fail",
                "field_name": "reason_fail",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [],
                "change": [
                    {
                        "from": "buồn quá không mua",
                        "to": "buồn quá không mua a"
                    }
                ],
                "remove": []
            }
        ],
        "dynamic": [
            {
                "field_name": "Radio button",
                "field_key": "_dyn_radio_button_1633599591661",
                "field_property": 2,
                "display_type": "radio",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    "ba"
                ],
                "change": [],
                "remove": []
            },
            {
                "field_name": "Single-line text chữ",
                "field_key": "_dyn_single_line_text_chu_1631458349843",
                "field_property": 2,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [],
                "change": [
                    {
                        "from": "123123123123",
                        "to": "12312312399"
                    }
                ],
                "remove": []
            },
            {
                "field_name": "Single-line text số",
                "field_key": "_dyn_single_line_text_so_1631181218494",
                "field_property": 1,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    12312312
                ],
                "change": [],
                "remove": []
            }
        ]
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateProfile
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "source_event": "DEAL",
    "event_type": "UpdateProfile",
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateTicket
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateTicket",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateCompanies
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateCompanies",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}
@apiSuccessExample {json} Response source_event: DEAL, event_type: InitDeal
{
    "_id" : "6177a299ffdea8e0c58fe305",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "6177a296573c1357e0a67460",
    "event_id" : "6177a298ffdea8e0c58fe303",
    "source_event" : "DEAL",
    "event_type" : "InitDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event" : "merchant",
    "source" : "SALE",
    "action_time" : **********.890302,
    "created_time" : "2021-10-15T15:05:18.856",
    "body" : {
        "profiles" : [
            {
                "id" : "6afa7308-ccf3-4974-8478-3a0f4b44ade0",
                "name" : "Cam Edit"
            }
        ],
        "assignee_id" : "01aa5dc3-36a6-42b1-b026-e7cdba3751cc",
        "state_code" : "QQ6CYXWM",
        "sale_process_id" : "61768dad74f73e51a119ea2b",
        "sale_process_name" : "Quy trình hoa hướng dương (không sửa xóa)",
        "state_name" : "Có thông tin Leads",
        "state_ratio" : 10
    }
}

@apiSuccessExample {json} Response source_event: SOCIAL
{
    "_id" : "61790f31df1001de4bd72334",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "be8bde12-c514-4788-bef8-7a85ff0d2b55",
    "event_id" : ObjectId("61790f28df1001de4bd72332"),
    "source_event" : "SOCIAL",
    "event_type" : 1,
    "staff_update_deal" : {
        "id" : "823ed8f0-83b2-491d-936d-32c2accfa15b",
        "email" : "<EMAIL>",
        "fullname" : "Admin24",
        "phone_number" : "+***********",
        "username" : "admin24@pingcomshop"
    },
    "line_event" : "merchant",
    "source" : "SOCIAL",
    "action_time" : 1635323521.467509,
    "created_time" : ISODate("2021-10-27T15:34:48.861+07:00"),
    "body" : {
        "profile_data" : {
            "profile_id" : "f78ebd1c-0854-46b0-b85b-a4cf9ddb0298",
            "id" : "f78ebd1c-0854-46b0-b85b-a4cf9ddb0298",
            "name" : "Đỗ Hòa"
        },
        "page_social_id" : "858617877680201",
        "content" : "Tùng Dịch",
        "page_name" : "Thiết bị văn phòng",
        "user_social_id" : "3551827758269419"
    }
}

@apiSuccessExample {json} Response, SourceEvent:Email:
{
    "id": "",
    "merchant_id": "",
    "deal_id": "",
    "event_id": "",
    "source_event": "Email",
    "event_type":"",
    "staff_update_deal" : {
            "id" : "d7e63da1-a47e-43e2-9b78-44d43433f7ec",
            "email" : "<EMAIL>",
            "fullname" : "sale",
            "phone_number" : "+84978348445",
            "username" : "sale@thanhtest11"
    },
    "action_time" : 1636613856.258556,
    "source": "EMAIL",
    "body": {
        "profile_data": profile_data,
        "from_email": ""		// email gửi
        "to_email": [""]		// danh sách người nhận
        "cc_email": [""]
        "bcc_email": [""]
        "subject": ""		// tiêu đề email 
        "content": ""		// nội dung email 
        "sender_domain": "mobio.vn", // có đối với kiểu email_mkt
        "related_to": {
            "company_ids": [], // Danh sách company có liên quan tới
            "ticket_ids": [], // Danh sách ticket có liên quan tới
            "profile_ids": [], // Danh sách Profile có liên quan tới
            "order_ids": [], // Danh sách các deal có liên quan tới
        },
        "attachments": [""] // Danh sách url file media 
        "status": {
            "type": "success", // Trạng thái của email đang gửi, đã gửi, gửi lỗi: pending, success, failed
            "message": "", // Trong trường hợp gửi mail lỗi thì cần gửi kèm nguyên nhân lỗi trong field này
        },
        "action_time": "",
    }
}

@apiSuccessExample {json} Response, SourceEvent: Callcenter:
{
    "_id" : "619b0507291638612986860d",
    "unique_value" : "call-vn-1-99V79H284Q-1637519225215",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "6185fc94d3145f3fb2dac5cf",
    "event_id" : "619b0507291638612986860b",
    "source_event" : "CALLCENTER",
    "event_type" : "SaleCallcenter",
    "staff_update_deal" : {
        "id" : "f48aad19-cbe9-4c49-baa2-b3d3e8a29bd4",
        "email" : "<EMAIL>",
        "fullname" : "Ngọc Ánh",
        "phone_number" : "+84334303858",
        "username" : "anhttn@pingcomshop"
    },
    "line_event" : "merchant",
    "activity" : "Callcenter",
    "source" : "CALLCENTER",
    "action_time" : 1637549229
    "created_time" : "2021-11-22T09:48:39.395Z,
    "body" : {
        "profile_data" : {
            "profile_id" : "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
            "profile_phone_number" : "0328914167",
            "id" : "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
            "name" : "Ngoan "
        },
        "switchboard" : "842473005768",
        "call_id" : "call-vn-1-99V79H284Q-1637519225215",
        "call_status" : 7,
        "so_may_le" : "106",
        "hot_line" : null,
        "attachments" : [
            {
                "url" : "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/call-vn-1-99V79H284Q-1637519225215.mp3",
                "local_path" : "/media/data/public_resources/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/call-vn-1-99V79H284Q-1637519225215.mp3",
                "filename" : "call-vn-1-99V79H284Q-1637519225215.mp3",
                "format" : "audio/mpeg"
            }
        ],
        "related_to" : {
            "profile_ids" : [
                "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
                "137c56e5-d56d-4dbc-9729-fa997e0294c3"
            ],
            "ticket_ids" : [
                "d8b34170-fb3b-401a-9d25-623844b4ef15"
            ],
            "order_ids" : [
                "6185fc94d3145f3fb2dac5cf"
            ]
        }
    }
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductCategoriesMain
{
	"_id": "627cadbed757dbd1ceb87ab9",
	"action_time": 1652338109.170179,
	"activity": "UpdateDeal",
	"body": {
		"add": [{
			"id": "626b50c62d0663bd2efb4707",
			"name": {
				"vi": "Danh m\u1ee5c c\u1ea5p 2"
			}
		}, {
			"id": "6278888ac7048b4e346a69f5",
			"name": {
				"vi": "Danh m\u1ee5c ch\u00ednh"
			}
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T06:48:29.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cadbed757dbd1ceb87ab7",
	"event_type": "AddProductCategoriesMain",
	"id": "627cadbed757dbd1ceb87ab9",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cadbd21e0c966a9a4bdd1"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductCategoriesSub
{
	"_id": "627cadbed757dbd1ceb87abc",
	"action_time": 1652338109.170179,
	"activity": "UpdateDeal",
	"body": {
		"add": [{
			"root_category": {
				"id": "626b50c62d0663bd2efb4707",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			},
			"sub_categories": [{
				"id": "626b51772d0663bd2efb4708",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			}, {
				"id": "626b51772d0663bd2efb4709",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 3"
				}
			}]
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T06:48:29.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cadbed757dbd1ceb87aba",
	"event_type": "AddProductCategoriesSub",
	"id": "627cadbed757dbd1ceb87abc",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cadbd21e0c966a9a4bdd1"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductCategoriesMain
{
	"_id": "627cbbb0d757dbd1ceb87bb3",
	"action_time": 1652341679.769229,
	"activity": "UpdateDeal",
	"body": {
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"remove": [{
			"id": "6278888ac7048b4e346a69f5",
			"name": {
				"vi": "Danh m\u1ee5c ch\u00ednh"
			}
		}],
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T07:47:59.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cbbb0d757dbd1ceb87bb1",
	"event_type": "RemoveProductCategoriesMain",
	"id": "627cbbb0d757dbd1ceb87bb3",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cbbaf1b36ae850ead6167"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductCategoriesMain
{
	"_id": "627cbdbbd757dbd1ceb87bd0",
	"action_time": 1652342202.881001,
	"activity": "UpdateDeal",
	"body": {
		"remove": [{
			"root_category": {
				"id": "626b50c62d0663bd2efb4707",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			},
			"sub_categories": [{
				"id": "626b51772d0663bd2efb4708",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			}]
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T07:56:43.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cbdbbd757dbd1ceb87bce",
	"event_type": "RemoveProductCategoriesSub",
	"id": "627cbdbbd757dbd1ceb87bd0",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cbdba9d69765958672e4f"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductType
{
    "_id": "62b139801298bda9228463d1",
    "action_time": 1655781759.375529,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "tags",
        "field_key": "product_types",
        "field_name": "product_types",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "product_line": { // Thông tin dòng sản phẩm
                    "id": "62aa7b0b181e77000e3afe29", 
                    "name": "[Test] Tùng 01"
                },
                "product_types": [
                    {
                        "id": "62aa96843af16c000d1dfaf8",
                        "level": 1, // Cấp loại sản phẩm
                        "name": "a" # Tên loại sản phẩm
                    }
                ]
            }
        ],
        "required": false,
        "translate_key": "i18n_subcategories"
    },
    "created_time": "2022-06-21T03:22:39.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b139801298bda9228463cf",
    "event_type": "RemoveProductType",
    "id": "62b139801298bda9228463d1",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1397f76bf3e9d77331fce"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductLine
{
    "_id": "62b1397f1298bda9228463cd",
    "action_time": 1655781759.375529,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "dropdown_single_line",
        "field_key": "product_line",
        "field_name": "product_line",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "id": "62aa7b0b181e77000e3afe29",
                "name": "[Test] Tùng 01"
            }
        ],
        "required": false,
        "translate_key": "i18n_product_category"
    },
    "created_time": "2022-06-21T03:22:39.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b1397f1298bda9228463cb",
    "event_type": "RemoveProductLine",
    "id": "62b1397f1298bda9228463cd",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1397f76bf3e9d77331fce"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductBank
{
    "_id": "62b138fd1298bda9228463b9",
    "action_time": **********.971797,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "dropdown_single_line",
        "field_key": "products_bank",
        "field_name": "products_bank",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "id": "62b03b964ef591000fd000a2",
                "name": "sản phẩm 1"
            }
        ],
        "required": false,
        "translate_key": "i18n_product"
    },
    "created_time": "2022-06-21T03:20:29.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b138fd1298bda9228463b7",
    "event_type": "RemoveProductBank",
    "id": "62b138fd1298bda9228463b9",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b138fca918469ec15424c0"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductType
{
    "_id": "62b137771298bda922846383",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "product_line": {
                    "id": "62aa7b0b181e77000e3afe29",
                    "name": "[Test] Tùng 01"
                },
                "product_types": [
                    {
                        "id": "62aa96843af16c000d1dfaf8",
                        "level": 1,
                        "name": "a"
                    }
                ]
            }
        ],
        "display_type": "tags",
        "field_key": "product_types",
        "field_name": "product_types",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_subcategories"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137771298bda922846381",
    "event_type": "AddProductType",
    "id": "62b137771298bda922846383",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductLine
{
    "_id": "62b137761298bda92284637f",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "id": "62aa7b0b181e77000e3afe29",
                "name": "[Test] Tùng 01"
            }
        ],
        "display_type": "dropdown_single_line",
        "field_key": "product_line",
        "field_name": "product_line",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_product_category"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137761298bda92284637d",
    "event_type": "AddProductLine",
    "id": "62b137761298bda92284637f",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: Sale, EventType: UpdateMediaDeal
{
    "_id": "62de6dcbb483efaabc8e5fd8",
    "action_time": 1658744255.607341,
    "activity": "Other",
    "body": {
        "add": [
            {
                "_id": "62de6dbf812bca51bd423890",
                "created_time": "2022-07-25T10:17:35Z",
                "entity_id": "62d1315f788e91d857f24115",
                "entity_type": "detail_deal",
                "id": "62de6dbf812bca51bd423890",
                "info": {
                    "format_file": "image/jpeg",
                    "local_path": "/media/data/public_resources/static/87d9e1f3-6da0-415f-b418-9ba9f4fe5523/Dr-Strange-Wallpapers.jpeg",
                    "title": "Dr-Strange-Wallpapers.jpeg",
                    "type_media": [
                        "identification"
                    ],
                    "url": "https://t1.mobio.vn/static/87d9e1f3-6da0-415f-b418-9ba9f4fe5523/Dr-Strange-Wallpapers.jpeg"
                },
                "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
                "updated_time": "2022-07-25T10:17:35Z"
            }
        ],
        "remove": []
    },
    "deal_id": "62d1315f788e91d857f24115",
    "event_type": "UpdateMediaDeal",
    "id": "62de6dcbb483efaabc8e5fd8",
    "line_event": "merchant",
    "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
    "source": "SALE",
    "source_event": "Deal",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin Owner",
        "id": "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737",
        "phone_number": "+***********",
        "username": "admin@msbeb"
    }
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductBank
{
    "_id": "62b137761298bda92284637b",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "id": "62b03b964ef591000fd000a2",
                "name": "sản phẩm 1"
            }
        ],
        "display_type": "dropdown_single_line",
        "field_key": "products_bank",
        "field_name": "products_bank",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_product"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137761298bda922846379",
    "event_type": "AddProductBank",
    "id": "62b137761298bda92284637b",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}
"""

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/action/filter/activity      Lấy danh sách event của Deal
@apiGroup Event
@apiVersion 1.0.2
@apiName ListEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=EMAIL,SOCIAL,SMS,CALLCENTER,OTHER}                  [source_event]         Tab muốn lấy danh sách event. Mặc định khi đứng ở tab <code>Tất cả</code> thì không cần truyền lên giá trị này.
                                                                                                        <ul>
                                                                                                            <li><code>EMAIL</code>: Tab Email</li>
                                                                                                            <li><code>SOCIAL</code>: Tab Online và mạng xã hội</li>
                                                                                                            <li><code>SMS</code>: Tab SMS</li>
                                                                                                            <li><code>CALLCENTER</code>: Tab cuộc gọi</li>
                                                                                                            <li><code>OTHER</code>: Tab khác</li>
                                                                                                        </ul> 

@apiParam   (BODY:) {Array}                     brand_ids              Danh sách các thương hiệu cần tìm kiếm
@apiParam   (BODY:) {Array}                     staff_ids              Danh sách nhân viên tương tác với Profile
@apiParam   (BODY:) {string}                    start_time             Thời gian bắt đầu tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {string}                    end_time               Thời gian kết thúc tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {Array}                     activities             Danh sách hoạt động cần tìm kiếm
                                                                        <ul>
                                                                            <li><code>UpdateDeal</code>: Cập nhật thông tin đơn hàng</li>
                                                                            <li><code>AssignmentForwardDeal</code>: Phân công chuyển tiếp đơn hàng</li>
                                                                            <li><code>Email</code>: E-mail</li>
                                                                            <li><code>Facebook</code>: Tương tác qua Facebook</li>
                                                                            <li><code>Zalo</code>: Tương tác qua Zalo</li>
                                                                            <li><code>SMS</code>: SMS</li>
                                                                            <li><code>Callcenter</code>: Gọi điện</li>
                                                                            <li><code>Other</code>: Khác</li>
                                                                            <li><code>UpdateCallReport</code>: Cập nhật call report</li>
                                                                        </ul>

@apiParamExample {json} Body example
{
    "brand_ids": [].
    "staff_ids": [],
    "start_time": "2021-10-15",
    "end_time": "2022-10-15",
    "activity": ["UpdateDeal"]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách các Event tương ứng.

@apiSuccess {String}            data.merchant_id              Định danh merchant.
@apiSuccess {String}            data.deal_id                  Định danh đơn hàng.
@apiSuccess {String}            data.event_id                 Định danh Event 
@apiSuccess {String}            data.source_event               Loại Event
                                                              <ul>
                                                                    <li><code>DEAL</code>: Loại event phát sinh của đơn hàng</li>
                                                                    <li><code>EMAIL</code>: Loại event E-mail</li>
                                                                    <li><code>SOCIAL</code>: Loại event phát sinh từ mạng xã hội</li>
                                                                    <li><code>SMS</code>: Loại event phát sinh từ SMS</li>
                                                                    <li><code>CALLCENTER</code>: Loại event phát sinh từ Gọi điện</li>
                                                                    <li><code>OTHER</code>: Khác</li>
                                                              </ul>
@apiSuccess {String}            data.event_type             Loại của chi tiết Event
                                                            <ul>Source event là <code>DEAL</code>
                                                                <li><code>InitDeal</code>: Tạo đơn hàng</li>
                                                                <li>
                                                                    <code>UpdateProfile</code>: Cập nhật liên hệ profile và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li>
                                                                    <code>UpdateCompanies</code>: Cập nhật liên hệ công ty và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li><code>UpdateTicket</code>: Cập nhật liên hệ ticket và đơn hàng</li>
                                                                <li><code>AddProductCategoriesMain</code>: Gắn liên hệ đơn hàng và danh mục chính</li>
                                                                <li><code>AddProductCategoriesSub</code>: Gắn liên hệ đơn hàng và danh mục phụ</li>
                                                                <li><code>RemoveProductCategoriesMain</code>: Gỡ liên hệ đơn hàng và danh mục chính</li>
                                                                <li><code>AddProductBank</code>: Gắn liên hệ giữa cơ hội bán và sản phẩm bank</li>
                                                                <li><code>RemoveProductBank</code>: Gỡ liên hệ giữa cơ hội bán và sản phẩm bank</li>
                                                                <li><code>AddProductLine</code>: Gắn liên hệ giữa cơ hội bán và dòng sản phẩm</li>
                                                                <li><code>RemoveProductLine</code>: Gỡ liên hệ giữa cơ hội bán và dòng sản phẩm</li>
                                                                <li><code>AddProductType</code>: Gắn liên hệ giữa cơ hội bán và loại sản phẩm</li>
                                                                <li><code>RemoveProductType</code>: Gỡ liên hệ giữa cơ hội bán và loại sản phẩm</li>
                                                                <li>
                                                                    <code>UpdateMediaDeal</code>: Cập nhật liên hệ media và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li><code>UpdateCallReport</code>: Cập nhật call report</li>
                                                            </ul>
                                                            <ul>Source event là <code>SOCIAL</code>
                                                                <li><code>1</code>: "Facebook"</li>
                                                                <li><code>2</code>: "Zalo"</li>
                                                                <li><code>3</code>: "Instagram"</li>
                                                                <li><code>4</code>: "Youtube"</li>
                                                                <li><code>5</code>: "App"</li>
                                                                <li><code>6</code>: "Line"</li>
                                                                <li><code>7</code>: "Mobio_chat_tool"</li>
                                                            </ul>
                                                            <ul>Source event là <code>CALLCENTER</code></ul>


@apiSuccess {String}            data.action_type              Loại event <b>Dùng cho event_type <code>UpdateCallReport</code> </b></br>
                                                              <b>Trường hợp <b>event_type</b> là <code>UpdateCallReport</code> mà <b>action_type</b> không nằm trong danh sách dưới
                                                              thì là một event cập nhật thông tin <b>Call Report</b></br>
                                                              <code>ApprovalBU</code> Trình ĐVKD duyệt </br>
                                                              <code>UpdateStatusBU</code> Cập nhật trạng thái duyệt tại ĐVKD </br>
                                                              <code>ApprovalHO</code> Trình hội sở duyệt </br>
                                                              <code>UpdateStatusHO</code> Cập nhật trạng thái duyệt tại Hội sở </br>
@apiSuccess {String}            data.staff_update_deal        Định danh nhân viên cập nhật thông tin
@apiSuccess {String}            data.line_event               Event tạo từ hệ thống hoặc người dùng: merchant, profile
@apiSuccess {String}            data.body                     Thông tin của Event
@apiSuccess {Object}            data.action_time              Thời gian diễn ra Event
@apiSuccess {Object}            data.created_time             Thời điểm tạo
@apiSuccess {Object}            data.data_approval            Thông tin duyệt <code>UpdateCallReport</code>
@apiSuccess {String}            data.data_approval.action             Hành động
@apiSuccess {String}            data.data_approval.opinion            Ý kiến
@apiSuccess {String}            data.data_approval.participants_enterprise_customer_relationship         Tên QHKHDN
@apiSuccess {String}            data.data_approval.participants_staff_code       Mã nhân viên 
@apiSuccess {String}            data.data_approval.participants_title            Chức danh
@apiSuccess {Array}            data.data_approval.accounts_to            Danh sách người nhận


@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateDeal
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "information": [
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "description",
                "field_name": "description",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [
                    "333"
                ],
                "change": [],
                "remove": []
            },
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "reason_fail",
                "field_name": "reason_fail",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [],
                "change": [
                    {
                        "from": "buồn quá không mua",
                        "to": "buồn quá không mua a"
                    }
                ],
                "remove": []
            }
        ],
        "dynamic": [
            {
                "field_name": "Radio button",
                "field_key": "_dyn_radio_button_1633599591661",
                "field_property": 2,
                "display_type": "radio",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    "ba"
                ],
                "change": [],
                "remove": []
            },
            {
                "field_name": "Single-line text chữ",
                "field_key": "_dyn_single_line_text_chu_1631458349843",
                "field_property": 2,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [],
                "change": [
                    {
                        "from": "123123123123",
                        "to": "12312312399"
                    }
                ],
                "remove": []
            },
            {
                "field_name": "Single-line text số",
                "field_key": "_dyn_single_line_text_so_1631181218494",
                "field_property": 1,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    12312312
                ],
                "change": [],
                "remove": []
            }
        ]
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateProfile
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "source_event": "DEAL",
    "event_type": "UpdateProfile",
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateTicket
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateTicket",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateCompanies
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateCompanies",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}
@apiSuccessExample {json} Response source_event: DEAL, event_type: InitDeal
{
    "_id" : "6177a299ffdea8e0c58fe305",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "6177a296573c1357e0a67460",
    "event_id" : "6177a298ffdea8e0c58fe303",
    "source_event" : "DEAL",
    "event_type" : "InitDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event" : "merchant",
    "source" : "SALE",
    "action_time" : **********.890302,
    "created_time" : "2021-10-15T15:05:18.856",
    "body" : {
        "profiles" : [
            {
                "id" : "6afa7308-ccf3-4974-8478-3a0f4b44ade0",
                "name" : "Cam Edit"
            }
        ],
        "assignee_id" : "01aa5dc3-36a6-42b1-b026-e7cdba3751cc",
        "state_code" : "QQ6CYXWM",
        "sale_process_id" : "61768dad74f73e51a119ea2b",
        "sale_process_name" : "Quy trình hoa hướng dương (không sửa xóa)",
        "state_name" : "Có thông tin Leads",
        "state_ratio" : 10
    }
}

@apiSuccessExample {json} Response source_event: SOCIAL
{
    "_id" : "61790f31df1001de4bd72334",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "be8bde12-c514-4788-bef8-7a85ff0d2b55",
    "event_id" : ObjectId("61790f28df1001de4bd72332"),
    "source_event" : "SOCIAL",
    "event_type" : 1,
    "staff_update_deal" : {
        "id" : "823ed8f0-83b2-491d-936d-32c2accfa15b",
        "email" : "<EMAIL>",
        "fullname" : "Admin24",
        "phone_number" : "+***********",
        "username" : "admin24@pingcomshop"
    },
    "line_event" : "merchant",
    "source" : "SOCIAL",
    "action_time" : 1635323521.467509,
    "created_time" : ISODate("2021-10-27T15:34:48.861+07:00"),
    "body" : {
        "profile_data" : {
            "profile_id" : "f78ebd1c-0854-46b0-b85b-a4cf9ddb0298",
            "id" : "f78ebd1c-0854-46b0-b85b-a4cf9ddb0298",
            "name" : "Đỗ Hòa"
        },
        "page_social_id" : "858617877680201",
        "content" : "Tùng Dịch",
        "page_name" : "Thiết bị văn phòng",
        "user_social_id" : "3551827758269419"
    }
}

@apiSuccessExample {json} Response, SourceEvent:Email:
{
    "id": "",
    "merchant_id": "",
    "deal_id": "",
    "event_id": "",
    "source_event": "Email",
    "event_type":"",
    "staff_update_deal" : {
            "id" : "d7e63da1-a47e-43e2-9b78-44d43433f7ec",
            "email" : "<EMAIL>",
            "fullname" : "sale",
            "phone_number" : "+84978348445",
            "username" : "sale@thanhtest11"
    },
    "action_time" : 1636613856.258556,
    "source": "EMAIL",
    "body": {
        "profile_data": profile_data,
        "from_email": ""		// email gửi
        "to_email": [""]		// danh sách người nhận
        "cc_email": [""]
        "bcc_email": [""]
        "subject": ""		// tiêu đề email 
        "content": ""		// nội dung email 
        "sender_domain": "mobio.vn", // có đối với kiểu email_mkt
        "related_to": {
            "company_ids": [], // Danh sách company có liên quan tới
            "ticket_ids": [], // Danh sách ticket có liên quan tới
            "profile_ids": [], // Danh sách Profile có liên quan tới
            "order_ids": [], // Danh sách các deal có liên quan tới
        },
        "attachments": [""] // Danh sách url file media 
        "status": {
            "type": "success", // Trạng thái của email đang gửi, đã gửi, gửi lỗi: pending, success, failed
            "message": "", // Trong trường hợp gửi mail lỗi thì cần gửi kèm nguyên nhân lỗi trong field này
        },
        "action_time": "",
    }
}

@apiSuccessExample {json} Response, SourceEvent: Callcenter:
{
    "_id" : "619b0507291638612986860d",
    "unique_value" : "call-vn-1-99V79H284Q-1637519225215",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "6185fc94d3145f3fb2dac5cf",
    "event_id" : "619b0507291638612986860b",
    "source_event" : "CALLCENTER",
    "event_type" : "SaleCallcenter",
    "staff_update_deal" : {
        "id" : "f48aad19-cbe9-4c49-baa2-b3d3e8a29bd4",
        "email" : "<EMAIL>",
        "fullname" : "Ngọc Ánh",
        "phone_number" : "+84334303858",
        "username" : "anhttn@pingcomshop"
    },
    "line_event" : "merchant",
    "activity" : "Callcenter",
    "source" : "CALLCENTER",
    "action_time" : 1637549229
    "created_time" : "2021-11-22T09:48:39.395Z,
    "body" : {
        "profile_data" : {
            "profile_id" : "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
            "profile_phone_number" : "0328914167",
            "id" : "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
            "name" : "Ngoan "
        },
        "switchboard" : "842473005768",
        "call_id" : "call-vn-1-99V79H284Q-1637519225215",
        "call_status" : 7,
        "so_may_le" : "106",
        "hot_line" : null,
        "attachments" : [
            {
                "url" : "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/call-vn-1-99V79H284Q-1637519225215.mp3",
                "local_path" : "/media/data/public_resources/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/call-vn-1-99V79H284Q-1637519225215.mp3",
                "filename" : "call-vn-1-99V79H284Q-1637519225215.mp3",
                "format" : "audio/mpeg"
            }
        ],
        "related_to" : {
            "profile_ids" : [
                "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
                "137c56e5-d56d-4dbc-9729-fa997e0294c3"
            ],
            "ticket_ids" : [
                "d8b34170-fb3b-401a-9d25-623844b4ef15"
            ],
            "order_ids" : [
                "6185fc94d3145f3fb2dac5cf"
            ]
        }
    }
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductCategoriesMain
{
	"_id": "627cadbed757dbd1ceb87ab9",
	"action_time": 1652338109.170179,
	"activity": "UpdateDeal",
	"body": {
		"add": [{
			"id": "626b50c62d0663bd2efb4707",
			"name": {
				"vi": "Danh m\u1ee5c c\u1ea5p 2"
			}
		}, {
			"id": "6278888ac7048b4e346a69f5",
			"name": {
				"vi": "Danh m\u1ee5c ch\u00ednh"
			}
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T06:48:29.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cadbed757dbd1ceb87ab7",
	"event_type": "AddProductCategoriesMain",
	"id": "627cadbed757dbd1ceb87ab9",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cadbd21e0c966a9a4bdd1"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductCategoriesSub
{
	"_id": "627cadbed757dbd1ceb87abc",
	"action_time": 1652338109.170179,
	"activity": "UpdateDeal",
	"body": {
		"add": [{
			"root_category": {
				"id": "626b50c62d0663bd2efb4707",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			},
			"sub_categories": [{
				"id": "626b51772d0663bd2efb4708",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			}, {
				"id": "626b51772d0663bd2efb4709",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 3"
				}
			}]
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T06:48:29.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cadbed757dbd1ceb87aba",
	"event_type": "AddProductCategoriesSub",
	"id": "627cadbed757dbd1ceb87abc",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cadbd21e0c966a9a4bdd1"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductCategoriesMain
{
	"_id": "627cbbb0d757dbd1ceb87bb3",
	"action_time": 1652341679.769229,
	"activity": "UpdateDeal",
	"body": {
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"remove": [{
			"id": "6278888ac7048b4e346a69f5",
			"name": {
				"vi": "Danh m\u1ee5c ch\u00ednh"
			}
		}],
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T07:47:59.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cbbb0d757dbd1ceb87bb1",
	"event_type": "RemoveProductCategoriesMain",
	"id": "627cbbb0d757dbd1ceb87bb3",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cbbaf1b36ae850ead6167"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductCategoriesMain
{
	"_id": "627cbdbbd757dbd1ceb87bd0",
	"action_time": 1652342202.881001,
	"activity": "UpdateDeal",
	"body": {
		"remove": [{
			"root_category": {
				"id": "626b50c62d0663bd2efb4707",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			},
			"sub_categories": [{
				"id": "626b51772d0663bd2efb4708",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			}]
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T07:56:43.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cbdbbd757dbd1ceb87bce",
	"event_type": "RemoveProductCategoriesSub",
	"id": "627cbdbbd757dbd1ceb87bd0",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cbdba9d69765958672e4f"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductType
{
    "_id": "62b139801298bda9228463d1",
    "action_time": 1655781759.375529,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "tags",
        "field_key": "product_types",
        "field_name": "product_types",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "product_line": { // Thông tin dòng sản phẩm
                    "id": "62aa7b0b181e77000e3afe29", 
                    "name": "[Test] Tùng 01"
                },
                "product_types": [
                    {
                        "id": "62aa96843af16c000d1dfaf8",
                        "level": 1, // Cấp loại sản phẩm
                        "name": "a" # Tên loại sản phẩm
                    }
                ]
            }
        ],
        "required": false,
        "translate_key": "i18n_subcategories"
    },
    "created_time": "2022-06-21T03:22:39.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b139801298bda9228463cf",
    "event_type": "RemoveProductType",
    "id": "62b139801298bda9228463d1",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1397f76bf3e9d77331fce"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductLine
{
    "_id": "62b1397f1298bda9228463cd",
    "action_time": 1655781759.375529,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "dropdown_single_line",
        "field_key": "product_line",
        "field_name": "product_line",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "id": "62aa7b0b181e77000e3afe29",
                "name": "[Test] Tùng 01"
            }
        ],
        "required": false,
        "translate_key": "i18n_product_category"
    },
    "created_time": "2022-06-21T03:22:39.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b1397f1298bda9228463cb",
    "event_type": "RemoveProductLine",
    "id": "62b1397f1298bda9228463cd",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1397f76bf3e9d77331fce"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductBank
{
    "_id": "62b138fd1298bda9228463b9",
    "action_time": **********.971797,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "dropdown_single_line",
        "field_key": "products_bank",
        "field_name": "products_bank",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "id": "62b03b964ef591000fd000a2",
                "name": "sản phẩm 1"
            }
        ],
        "required": false,
        "translate_key": "i18n_product"
    },
    "created_time": "2022-06-21T03:20:29.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b138fd1298bda9228463b7",
    "event_type": "RemoveProductBank",
    "id": "62b138fd1298bda9228463b9",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b138fca918469ec15424c0"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductType
{
    "_id": "62b137771298bda922846383",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "product_line": {
                    "id": "62aa7b0b181e77000e3afe29",
                    "name": "[Test] Tùng 01"
                },
                "product_types": [
                    {
                        "id": "62aa96843af16c000d1dfaf8",
                        "level": 1,
                        "name": "a"
                    }
                ]
            }
        ],
        "display_type": "tags",
        "field_key": "product_types",
        "field_name": "product_types",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_subcategories"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137771298bda922846381",
    "event_type": "AddProductType",
    "id": "62b137771298bda922846383",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductLine
{
    "_id": "62b137761298bda92284637f",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "id": "62aa7b0b181e77000e3afe29",
                "name": "[Test] Tùng 01"
            }
        ],
        "display_type": "dropdown_single_line",
        "field_key": "product_line",
        "field_name": "product_line",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_product_category"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137761298bda92284637d",
    "event_type": "AddProductLine",
    "id": "62b137761298bda92284637f",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: Sale, EventType: UpdateMediaDeal
{
    "_id": "62de6dcbb483efaabc8e5fd8",
    "action_time": 1658744255.607341,
    "activity": "Other",
    "body": {
        "add": [
            {
                "_id": "62de6dbf812bca51bd423890",
                "created_time": "2022-07-25T10:17:35Z",
                "entity_id": "62d1315f788e91d857f24115",
                "entity_type": "detail_deal",
                "id": "62de6dbf812bca51bd423890",
                "info": {
                    "format_file": "image/jpeg",
                    "local_path": "/media/data/public_resources/static/87d9e1f3-6da0-415f-b418-9ba9f4fe5523/Dr-Strange-Wallpapers.jpeg",
                    "title": "Dr-Strange-Wallpapers.jpeg",
                    "type_media": [
                        "identification"
                    ],
                    "url": "https://t1.mobio.vn/static/87d9e1f3-6da0-415f-b418-9ba9f4fe5523/Dr-Strange-Wallpapers.jpeg"
                },
                "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
                "updated_time": "2022-07-25T10:17:35Z"
            }
        ],
        "remove": []
    },
    "deal_id": "62d1315f788e91d857f24115",
    "event_type": "UpdateMediaDeal",
    "id": "62de6dcbb483efaabc8e5fd8",
    "line_event": "merchant",
    "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
    "source": "SALE",
    "source_event": "Deal",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin Owner",
        "id": "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737",
        "phone_number": "+***********",
        "username": "admin@msbeb"
    }
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductBank
{
    "_id": "62b137761298bda92284637b",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "id": "62b03b964ef591000fd000a2",
                "name": "sản phẩm 1"
            }
        ],
        "display_type": "dropdown_single_line",
        "field_key": "products_bank",
        "field_name": "products_bank",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_product"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137761298bda922846379",
    "event_type": "AddProductBank",
    "id": "62b137761298bda92284637b",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport
{
    "_id": "648ace0a4c5a1e64615a1557",
    "action_time": **********.324215,
    "action_type": null,
    "activity": "UpdateCallReport",
    "body": [
        {
            "financial_situation_sub": [
                {
                    "add": [],
                    "change": [],
                    "display_type": "nested",
                    "field_key": "financial_situation",
                    "field_name": "Tình hình tài chính",
                    "field_property": 2,
                    "group": "financial_situation_sub",
                    "paths": [
                        {
                            "data": [
                                {
                                    "add": [
                                        2323
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "owners_equity",
                                    "field_name": "Vốn chủ sở hữu (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 5,
                                    "order_list": 28,
                                    "order_preview": 24,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        2323
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "profit_after_tax",
                                    "field_name": "Lợi nhuận sau thuế  (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 6,
                                    "order_list": 29,
                                    "order_preview": 25,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        "3232"
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "roa",
                                    "field_name": "ROA (%)",
                                    "field_property": 2,
                                    "group": "financial_situation_sub",
                                    "order": 9,
                                    "order_list": 32,
                                    "order_preview": 28,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        232323
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "total_assets",
                                    "field_name": "Tổng tài sản (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 4,
                                    "order_list": 27,
                                    "order_preview": 23,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        23232
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "total_debt_at_credit_institutions",
                                    "field_name": "Tổng nợ vay tại các TCTD (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 7,
                                    "order_list": 30,
                                    "order_preview": 26,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        32323
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "total_revenue",
                                    "field_name": "Tổng doanh thu (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 3,
                                    "order_list": 26,
                                    "order_preview": 22,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        "3232"
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "working_capital_turnover",
                                    "field_name": "Vòng quay VLĐ",
                                    "field_property": 2,
                                    "group": "financial_situation_sub",
                                    "order": 8,
                                    "order_list": 31,
                                    "order_preview": 27,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                }
                            ],
                            "report_year": 2023,
                            "type_report": "TAX_REPORT"
                        }
                    ],
                    "remove": []
                }
            ]
        },
        {
            "credit_organization": [
                {
                    "add": [],
                    "change": [],
                    "display_type": "nested",
                    "field_key": "relationship_between_other_credit_institutions",
                    "field_name": "Tổ chức tín dụng",
                    "field_property": 2,
                    "group": "credit_organization",
                    "paths": [
                        {
                            "data": [
                                {
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "nested",
                                    "field_key": "credit_information",
                                    "field_name": "Thông tin tín dụng",
                                    "field_property": 2,
                                    "group": "credit_organization",
                                    "order": 2,
                                    "order_list": 36,
                                    "order_preview": 32,
                                    "paths": [
                                        {
                                            "data": [],
                                            "period": "short_term"
                                        }
                                    ],
                                    "require": false,
                                    "status": 1
                                }
                            ],
                            "id": 1,
                            "name_of_credit_institution": null
                        }
                    ],
                    "remove": []
                }
            ]
        }
    ],
    "call_report_id": "648acd3146edb4c03a1cf2f4",
    "created_time": "2023-06-15T08:38:34.000Z",
    "deal_id": "648ac2146aa132e46f818dd6",
    "event_id": "648ace0a4c5a1e64615a1555",
    "event_type": "UpdateCallReport",
    "group_configs": [
        {
            "group_index": 2.1,
            "group_key": "financial_situation_sub",
            "group_name": "Tình hình tài chính"
        },
        {
            "group_index": 3.1,
            "group_key": "credit_organization",
            "group_name": "Tổ chức tín dụng"
        }
    ],
    "id": "648ace0a4c5a1e64615a1557",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Mai Doãn",
        "id": "de97719f-916e-4bcd-900b-94a142e4023e",
        "phone_number": "+***********",
        "username": "Maidt@hdb"
    },
    "unique_value": "648ace09c01cc8fd2e87065e"
}

@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport, action_type:ApprovalBU (Trình ĐVKD duyệt)
{
    "_id": "648acdd54c5a1e64615a1552",
    "action_time": **********.943061,
    "action_type": "ApprovalBU",
    "activity": "UpdateCallReport",
    "body": null,
    "call_report_id": "648acd3146edb4c03a1cf2f4",
    "created_time": "2023-06-15T08:37:41.000Z",
    "data_approval": {
        "accounts_to": [
            {
                "email": "<EMAIL>",
                "fullname": "MAI THỊ ÁI",
                "participants_staff_code": "HD001903",
                "participants_title": "Kiểm soát viên",
                "username": "aimt@hdb"
            }
        ],
        "action": "approval_bu",
        "opinion": null,
        "participants_enterprise_customer_relationship": "Test Mobio",
        "participants_staff_code": "SMT05",
        "participants_title": null
    },
    "deal_id": "648ac2146aa132e46f818dd6",
    "event_id": "648acdd54c5a1e64615a1550",
    "event_type": "ApprovalBU",
    "id": "648acdd54c5a1e64615a1552",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Test Mobio",
        "id": "4e7c97b4-9fb3-49ea-a1f9-a6c0a831413d",
        "phone_number": "+***********",
        "username": "smt05@hdb"
    },
    "unique_value": "648acdce169f2bd4449fbef4"
}

@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport, action_type:UpdateStatusBU (Cập nhật trạng thái duyệt tại ĐVKD)
{
    "_id": "648ace594c5a1e64615a155d",
    "action_time": **********.161219,
    "action_type": "UpdateStatusBU",
    "activity": "UpdateCallReport",
    "body": null,
    "call_report_id": "648acd3146edb4c03a1cf2f4",
    "created_time": "2023-06-15T08:39:53.000Z",
    "data_approval": {
        "accounts_to": [],
        "action": "additional_information_bu",
        "opinion": null,
        "participants_enterprise_customer_relationship": "Mai Doãn",
        "participants_staff_code": "NVMANUAL123",
        "participants_title": null,
        "status_approval": "provide_additional_information"
    },
    "deal_id": "648ac2146aa132e46f818dd6",
    "event_id": "648ace594c5a1e64615a155b",
    "event_type": "UpdateStatusBU",
    "id": "648ace594c5a1e64615a155d",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {},
    "unique_value": "648ace54169f2bd4449fbef6"
}


@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport, action_type:UpdateStatusHO (Cập nhật trạng thái duyệt tại Hội sở )
{
    "_id": "648c3e38bd0862ff25783ff1",
    "action_time": **********.791481,
    "action_type": "UpdateStatusHO",
    "activity": "UpdateCallReport",
    "body": null,
    "call_report_id": "648c32d10da8e13b7cef5eed",
    "created_time": "2023-06-16T10:49:27.000Z",
    "data_approval": {
        "accounts_to": [],
        "action": "approve_ho",
        "opinion": null,
        "participants_enterprise_customer_relationship": "Hoàng Nguyễn Châu Phương Nam",
        "participants_staff_code": "",
        "participants_title": null,
        "status_approval": "approve"
    },
    "deal_id": "648c31681afaffa942747095",
    "event_id": "648c3e37bd0862ff25783fef",
    "event_type": "UpdateStatusHO",
    "id": "648c3e38bd0862ff25783ff1",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {},
    "unique_value": "648c3e3752a953ed4916cb7d"
}
@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport, action_type:ApprovalHO (Trình hội sở duyệt)
{
    "_id": "648c3dfdbd0862ff25783fec",
    "action_time": **********.346201,
    "action_type": "ApprovalHO",
    "activity": "UpdateCallReport",
    "body": null,
    "call_report_id": "648c32d10da8e13b7cef5eed",
    "created_time": "2023-06-16T10:48:29.000Z",
    "data_approval": {
        "accounts_to": [
            {
                "email": "<EMAIL>",
                "fullname": "Hoàng Nguyễn Châu Phương Nam",
                "participants_staff_code": "",
                "participants_title": null,
                "username": "datpt@hdb"
            }
        ],
        "action": "approval_ho",
        "opinion": null,
        "participants_enterprise_customer_relationship": "Mai Doãn",
        "participants_staff_code": "NVMANUAL123",
        "participants_title": null
    },
    "deal_id": "648c31681afaffa942747095",
    "event_id": "648c3dfdbd0862ff25783fea",
    "event_type": "ApprovalHO",
    "id": "648c3dfdbd0862ff25783fec",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Mai Doãn",
        "id": "de97719f-916e-4bcd-900b-94a142e4023e",
        "phone_number": "+***********",
        "username": "Maidt@hdb"
    },
    "unique_value": "648c3dfb52a953ed4916cb7b"
}
"""


"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/action/filter/activity      Lấy danh sách event của Deal
@apiGroup Event
@apiVersion 1.0.3
@apiName ListEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=EMAIL,SOCIAL,SMS,CALLCENTER,OTHER}                  [source_event]         Tab muốn lấy danh sách event. Mặc định khi đứng ở tab <code>Tất cả</code> thì không cần truyền lên giá trị này.
                                                                                                        <ul>
                                                                                                            <li><code>EMAIL</code>: Tab Email</li>
                                                                                                            <li><code>SOCIAL</code>: Tab Online và mạng xã hội</li>
                                                                                                            <li><code>SMS</code>: Tab SMS</li>
                                                                                                            <li><code>CALLCENTER</code>: Tab cuộc gọi</li>
                                                                                                            <li><code>OTHER</code>: Tab khác</li>
                                                                                                        </ul> 

@apiParam   (BODY:) {Array}                     brand_ids              Danh sách các thương hiệu cần tìm kiếm
@apiParam   (BODY:) {Array}                     staff_ids              Danh sách nhân viên tương tác với Profile
@apiParam   (BODY:) {string}                    start_time             Thời gian bắt đầu tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {string}                    end_time               Thời gian kết thúc tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {Array}                     activities             Danh sách hoạt động cần tìm kiếm
                                                                        <ul>
                                                                            <li><code>UpdateDeal</code>: Cập nhật thông tin đơn hàng</li>
                                                                            <li><code>AssignmentForwardDeal</code>: Phân công chuyển tiếp đơn hàng</li>
                                                                            <li><code>Email</code>: E-mail</li>
                                                                            <li><code>Facebook</code>: Tương tác qua Facebook</li>
                                                                            <li><code>Zalo</code>: Tương tác qua Zalo</li>
                                                                            <li><code>SMS</code>: SMS</li>
                                                                            <li><code>Callcenter</code>: Gọi điện</li>
                                                                            <li><code>Other</code>: Khác</li>
                                                                            <li><code>UpdateCallReport</code>: Cập nhật call report</li>
                                                                            <li><code>SaleMemo</code>: Sale Memo</li>
                                                                        </ul>

@apiParamExample {json} Body example
{
    "brand_ids": [].
    "staff_ids": [],
    "start_time": "2021-10-15",
    "end_time": "2022-10-15",
    "activity": ["UpdateDeal"]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách các Event tương ứng.

@apiSuccess {String}            data.merchant_id              Định danh merchant.
@apiSuccess {String}            data.deal_id                  Định danh đơn hàng.
@apiSuccess {String}            data.event_id                 Định danh Event 
@apiSuccess {String}            data.source_event               Loại Event
                                                              <ul>
                                                                    <li><code>DEAL</code>: Loại event phát sinh của đơn hàng</li>
                                                                    <li><code>EMAIL</code>: Loại event E-mail</li>
                                                                    <li><code>SOCIAL</code>: Loại event phát sinh từ mạng xã hội</li>
                                                                    <li><code>SMS</code>: Loại event phát sinh từ SMS</li>
                                                                    <li><code>CALLCENTER</code>: Loại event phát sinh từ Gọi điện</li>
                                                                    <li><code>OTHER</code>: Khác</li>
                                                              </ul>
@apiSuccess {String}            data.event_type             Loại của chi tiết Event
                                                            <ul>Source event là <code>DEAL</code>
                                                                <li><code>InitDeal</code>: Tạo đơn hàng</li>
                                                                <li>
                                                                    <code>UpdateProfile</code>: Cập nhật liên hệ profile và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li>
                                                                    <code>UpdateCompanies</code>: Cập nhật liên hệ công ty và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li><code>UpdateTicket</code>: Cập nhật liên hệ ticket và đơn hàng</li>
                                                                <li><code>AddProductCategoriesMain</code>: Gắn liên hệ đơn hàng và danh mục chính</li>
                                                                <li><code>AddProductCategoriesSub</code>: Gắn liên hệ đơn hàng và danh mục phụ</li>
                                                                <li><code>RemoveProductCategoriesMain</code>: Gỡ liên hệ đơn hàng và danh mục chính</li>
                                                                <li><code>AddProductBank</code>: Gắn liên hệ giữa cơ hội bán và sản phẩm bank</li>
                                                                <li><code>RemoveProductBank</code>: Gỡ liên hệ giữa cơ hội bán và sản phẩm bank</li>
                                                                <li><code>AddProductLine</code>: Gắn liên hệ giữa cơ hội bán và dòng sản phẩm</li>
                                                                <li><code>RemoveProductLine</code>: Gỡ liên hệ giữa cơ hội bán và dòng sản phẩm</li>
                                                                <li><code>AddProductType</code>: Gắn liên hệ giữa cơ hội bán và loại sản phẩm</li>
                                                                <li><code>RemoveProductType</code>: Gỡ liên hệ giữa cơ hội bán và loại sản phẩm</li>
                                                                <li>
                                                                    <code>UpdateMediaDeal</code>: Cập nhật liên hệ media và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li><code>UpdateCallReport</code>: Cập nhật call report</li>
                                                            </ul>
                                                            <ul>Source event là <code>SOCIAL</code>
                                                                <li><code>1</code>: "Facebook"</li>
                                                                <li><code>2</code>: "Zalo"</li>
                                                                <li><code>3</code>: "Instagram"</li>
                                                                <li><code>4</code>: "Youtube"</li>
                                                                <li><code>5</code>: "App"</li>
                                                                <li><code>6</code>: "Line"</li>
                                                                <li><code>7</code>: "Mobio_chat_tool"</li>
                                                            </ul>
                                                            <ul>Source event là <code>CALLCENTER</code></ul>


@apiSuccess {String}            data.action_type              Loại event <b>Dùng cho event_type <code>UpdateCallReport</code> </b></br>
                                                              <b>Trường hợp <b>event_type</b> là <code>UpdateCallReport</code> mà <b>action_type</b> không nằm trong danh sách dưới
                                                              thì là một event cập nhật thông tin <b>Call Report</b></br>
                                                              <code>ApprovalBU</code> Trình ĐVKD duyệt </br>
                                                              <code>UpdateStatusBU</code> Cập nhật trạng thái duyệt tại ĐVKD </br>
                                                              <code>ApprovalHO</code> Trình hội sở duyệt </br>
                                                              <code>UpdateStatusHO</code> Cập nhật trạng thái duyệt tại Hội sở </br>
@apiSuccess {String}            data.staff_update_deal        Định danh nhân viên cập nhật thông tin
@apiSuccess {String}            data.line_event               Event tạo từ hệ thống hoặc người dùng: merchant, profile
@apiSuccess {String}            data.body                     Thông tin của Event
@apiSuccess {Object}            data.action_time              Thời gian diễn ra Event
@apiSuccess {Object}            data.created_time             Thời điểm tạo
@apiSuccess {Object}            data.data_approval            Thông tin duyệt <code>UpdateCallReport</code>
@apiSuccess {String}            data.data_approval.action             Hành động
@apiSuccess {String}            data.data_approval.opinion            Ý kiến
@apiSuccess {String}            data.data_approval.participants_enterprise_customer_relationship         Tên QHKHDN
@apiSuccess {String}            data.data_approval.participants_staff_code       Mã nhân viên 
@apiSuccess {String}            data.data_approval.participants_title            Chức danh
@apiSuccess {Array}            data.data_approval.accounts_to            Danh sách người nhận


@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateDeal
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "information": [
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "description",
                "field_name": "description",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [
                    "333"
                ],
                "change": [],
                "remove": []
            },
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "reason_fail",
                "field_name": "reason_fail",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [],
                "change": [
                    {
                        "from": "buồn quá không mua",
                        "to": "buồn quá không mua a"
                    }
                ],
                "remove": []
            }
        ],
        "dynamic": [
            {
                "field_name": "Radio button",
                "field_key": "_dyn_radio_button_1633599591661",
                "field_property": 2,
                "display_type": "radio",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    "ba"
                ],
                "change": [],
                "remove": []
            },
            {
                "field_name": "Single-line text chữ",
                "field_key": "_dyn_single_line_text_chu_1631458349843",
                "field_property": 2,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [],
                "change": [
                    {
                        "from": "123123123123",
                        "to": "12312312399"
                    }
                ],
                "remove": []
            },
            {
                "field_name": "Single-line text số",
                "field_key": "_dyn_single_line_text_so_1631181218494",
                "field_property": 1,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    12312312
                ],
                "change": [],
                "remove": []
            }
        ]
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateProfile
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "source_event": "DEAL",
    "event_type": "UpdateProfile",
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateTicket
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateTicket",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateCompanies
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateCompanies",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}
@apiSuccessExample {json} Response source_event: DEAL, event_type: InitDeal
{
    "_id" : "6177a299ffdea8e0c58fe305",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "6177a296573c1357e0a67460",
    "event_id" : "6177a298ffdea8e0c58fe303",
    "source_event" : "DEAL",
    "event_type" : "InitDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event" : "merchant",
    "source" : "SALE",
    "action_time" : **********.890302,
    "created_time" : "2021-10-15T15:05:18.856",
    "body" : {
        "profiles" : [
            {
                "id" : "6afa7308-ccf3-4974-8478-3a0f4b44ade0",
                "name" : "Cam Edit"
            }
        ],
        "assignee_id" : "01aa5dc3-36a6-42b1-b026-e7cdba3751cc",
        "state_code" : "QQ6CYXWM",
        "sale_process_id" : "61768dad74f73e51a119ea2b",
        "sale_process_name" : "Quy trình hoa hướng dương (không sửa xóa)",
        "state_name" : "Có thông tin Leads",
        "state_ratio" : 10
    }
}

@apiSuccessExample {json} Response source_event: SOCIAL
{
    "_id" : "61790f31df1001de4bd72334",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "be8bde12-c514-4788-bef8-7a85ff0d2b55",
    "event_id" : ObjectId("61790f28df1001de4bd72332"),
    "source_event" : "SOCIAL",
    "event_type" : 1,
    "staff_update_deal" : {
        "id" : "823ed8f0-83b2-491d-936d-32c2accfa15b",
        "email" : "<EMAIL>",
        "fullname" : "Admin24",
        "phone_number" : "+***********",
        "username" : "admin24@pingcomshop"
    },
    "line_event" : "merchant",
    "source" : "SOCIAL",
    "action_time" : 1635323521.467509,
    "created_time" : ISODate("2021-10-27T15:34:48.861+07:00"),
    "body" : {
        "profile_data" : {
            "profile_id" : "f78ebd1c-0854-46b0-b85b-a4cf9ddb0298",
            "id" : "f78ebd1c-0854-46b0-b85b-a4cf9ddb0298",
            "name" : "Đỗ Hòa"
        },
        "page_social_id" : "858617877680201",
        "content" : "Tùng Dịch",
        "page_name" : "Thiết bị văn phòng",
        "user_social_id" : "3551827758269419"
    }
}

@apiSuccessExample {json} Response, SourceEvent:Email:
{
    "id": "",
    "merchant_id": "",
    "deal_id": "",
    "event_id": "",
    "source_event": "Email",
    "event_type":"",
    "staff_update_deal" : {
            "id" : "d7e63da1-a47e-43e2-9b78-44d43433f7ec",
            "email" : "<EMAIL>",
            "fullname" : "sale",
            "phone_number" : "+84978348445",
            "username" : "sale@thanhtest11"
    },
    "action_time" : 1636613856.258556,
    "source": "EMAIL",
    "body": {
        "profile_data": profile_data,
        "from_email": ""		// email gửi
        "to_email": [""]		// danh sách người nhận
        "cc_email": [""]
        "bcc_email": [""]
        "subject": ""		// tiêu đề email 
        "content": ""		// nội dung email 
        "sender_domain": "mobio.vn", // có đối với kiểu email_mkt
        "related_to": {
            "company_ids": [], // Danh sách company có liên quan tới
            "ticket_ids": [], // Danh sách ticket có liên quan tới
            "profile_ids": [], // Danh sách Profile có liên quan tới
            "order_ids": [], // Danh sách các deal có liên quan tới
        },
        "attachments": [""] // Danh sách url file media 
        "status": {
            "type": "success", // Trạng thái của email đang gửi, đã gửi, gửi lỗi: pending, success, failed
            "message": "", // Trong trường hợp gửi mail lỗi thì cần gửi kèm nguyên nhân lỗi trong field này
        },
        "action_time": "",
    }
}

@apiSuccessExample {json} Response, SourceEvent: Callcenter:
{
    "_id" : "619b0507291638612986860d",
    "unique_value" : "call-vn-1-99V79H284Q-1637519225215",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "6185fc94d3145f3fb2dac5cf",
    "event_id" : "619b0507291638612986860b",
    "source_event" : "CALLCENTER",
    "event_type" : "SaleCallcenter",
    "staff_update_deal" : {
        "id" : "f48aad19-cbe9-4c49-baa2-b3d3e8a29bd4",
        "email" : "<EMAIL>",
        "fullname" : "Ngọc Ánh",
        "phone_number" : "+84334303858",
        "username" : "anhttn@pingcomshop"
    },
    "line_event" : "merchant",
    "activity" : "Callcenter",
    "source" : "CALLCENTER",
    "action_time" : 1637549229
    "created_time" : "2021-11-22T09:48:39.395Z,
    "body" : {
        "profile_data" : {
            "profile_id" : "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
            "profile_phone_number" : "0328914167",
            "id" : "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
            "name" : "Ngoan "
        },
        "switchboard" : "842473005768",
        "call_id" : "call-vn-1-99V79H284Q-1637519225215",
        "call_status" : 7,
        "so_may_le" : "106",
        "hot_line" : null,
        "attachments" : [
            {
                "url" : "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/call-vn-1-99V79H284Q-1637519225215.mp3",
                "local_path" : "/media/data/public_resources/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/call-vn-1-99V79H284Q-1637519225215.mp3",
                "filename" : "call-vn-1-99V79H284Q-1637519225215.mp3",
                "format" : "audio/mpeg"
            }
        ],
        "related_to" : {
            "profile_ids" : [
                "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
                "137c56e5-d56d-4dbc-9729-fa997e0294c3"
            ],
            "ticket_ids" : [
                "d8b34170-fb3b-401a-9d25-623844b4ef15"
            ],
            "order_ids" : [
                "6185fc94d3145f3fb2dac5cf"
            ]
        }
    }
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductCategoriesMain
{
	"_id": "627cadbed757dbd1ceb87ab9",
	"action_time": 1652338109.170179,
	"activity": "UpdateDeal",
	"body": {
		"add": [{
			"id": "626b50c62d0663bd2efb4707",
			"name": {
				"vi": "Danh m\u1ee5c c\u1ea5p 2"
			}
		}, {
			"id": "6278888ac7048b4e346a69f5",
			"name": {
				"vi": "Danh m\u1ee5c ch\u00ednh"
			}
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T06:48:29.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cadbed757dbd1ceb87ab7",
	"event_type": "AddProductCategoriesMain",
	"id": "627cadbed757dbd1ceb87ab9",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cadbd21e0c966a9a4bdd1"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductCategoriesSub
{
	"_id": "627cadbed757dbd1ceb87abc",
	"action_time": 1652338109.170179,
	"activity": "UpdateDeal",
	"body": {
		"add": [{
			"root_category": {
				"id": "626b50c62d0663bd2efb4707",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			},
			"sub_categories": [{
				"id": "626b51772d0663bd2efb4708",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			}, {
				"id": "626b51772d0663bd2efb4709",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 3"
				}
			}]
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T06:48:29.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cadbed757dbd1ceb87aba",
	"event_type": "AddProductCategoriesSub",
	"id": "627cadbed757dbd1ceb87abc",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cadbd21e0c966a9a4bdd1"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductCategoriesMain
{
	"_id": "627cbbb0d757dbd1ceb87bb3",
	"action_time": 1652341679.769229,
	"activity": "UpdateDeal",
	"body": {
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"remove": [{
			"id": "6278888ac7048b4e346a69f5",
			"name": {
				"vi": "Danh m\u1ee5c ch\u00ednh"
			}
		}],
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T07:47:59.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cbbb0d757dbd1ceb87bb1",
	"event_type": "RemoveProductCategoriesMain",
	"id": "627cbbb0d757dbd1ceb87bb3",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cbbaf1b36ae850ead6167"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductCategoriesMain
{
	"_id": "627cbdbbd757dbd1ceb87bd0",
	"action_time": 1652342202.881001,
	"activity": "UpdateDeal",
	"body": {
		"remove": [{
			"root_category": {
				"id": "626b50c62d0663bd2efb4707",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			},
			"sub_categories": [{
				"id": "626b51772d0663bd2efb4708",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			}]
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T07:56:43.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cbdbbd757dbd1ceb87bce",
	"event_type": "RemoveProductCategoriesSub",
	"id": "627cbdbbd757dbd1ceb87bd0",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cbdba9d69765958672e4f"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductType
{
    "_id": "62b139801298bda9228463d1",
    "action_time": 1655781759.375529,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "tags",
        "field_key": "product_types",
        "field_name": "product_types",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "product_line": { // Thông tin dòng sản phẩm
                    "id": "62aa7b0b181e77000e3afe29", 
                    "name": "[Test] Tùng 01"
                },
                "product_types": [
                    {
                        "id": "62aa96843af16c000d1dfaf8",
                        "level": 1, // Cấp loại sản phẩm
                        "name": "a" # Tên loại sản phẩm
                    }
                ]
            }
        ],
        "required": false,
        "translate_key": "i18n_subcategories"
    },
    "created_time": "2022-06-21T03:22:39.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b139801298bda9228463cf",
    "event_type": "RemoveProductType",
    "id": "62b139801298bda9228463d1",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1397f76bf3e9d77331fce"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductLine
{
    "_id": "62b1397f1298bda9228463cd",
    "action_time": 1655781759.375529,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "dropdown_single_line",
        "field_key": "product_line",
        "field_name": "product_line",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "id": "62aa7b0b181e77000e3afe29",
                "name": "[Test] Tùng 01"
            }
        ],
        "required": false,
        "translate_key": "i18n_product_category"
    },
    "created_time": "2022-06-21T03:22:39.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b1397f1298bda9228463cb",
    "event_type": "RemoveProductLine",
    "id": "62b1397f1298bda9228463cd",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1397f76bf3e9d77331fce"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductBank
{
    "_id": "62b138fd1298bda9228463b9",
    "action_time": **********.971797,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "dropdown_single_line",
        "field_key": "products_bank",
        "field_name": "products_bank",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "id": "62b03b964ef591000fd000a2",
                "name": "sản phẩm 1"
            }
        ],
        "required": false,
        "translate_key": "i18n_product"
    },
    "created_time": "2022-06-21T03:20:29.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b138fd1298bda9228463b7",
    "event_type": "RemoveProductBank",
    "id": "62b138fd1298bda9228463b9",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b138fca918469ec15424c0"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductType
{
    "_id": "62b137771298bda922846383",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "product_line": {
                    "id": "62aa7b0b181e77000e3afe29",
                    "name": "[Test] Tùng 01"
                },
                "product_types": [
                    {
                        "id": "62aa96843af16c000d1dfaf8",
                        "level": 1,
                        "name": "a"
                    }
                ]
            }
        ],
        "display_type": "tags",
        "field_key": "product_types",
        "field_name": "product_types",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_subcategories"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137771298bda922846381",
    "event_type": "AddProductType",
    "id": "62b137771298bda922846383",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductLine
{
    "_id": "62b137761298bda92284637f",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "id": "62aa7b0b181e77000e3afe29",
                "name": "[Test] Tùng 01"
            }
        ],
        "display_type": "dropdown_single_line",
        "field_key": "product_line",
        "field_name": "product_line",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_product_category"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137761298bda92284637d",
    "event_type": "AddProductLine",
    "id": "62b137761298bda92284637f",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: Sale, EventType: UpdateMediaDeal
{
    "_id": "62de6dcbb483efaabc8e5fd8",
    "action_time": 1658744255.607341,
    "activity": "Other",
    "body": {
        "add": [
            {
                "_id": "62de6dbf812bca51bd423890",
                "created_time": "2022-07-25T10:17:35Z",
                "entity_id": "62d1315f788e91d857f24115",
                "entity_type": "detail_deal",
                "id": "62de6dbf812bca51bd423890",
                "info": {
                    "format_file": "image/jpeg",
                    "local_path": "/media/data/public_resources/static/87d9e1f3-6da0-415f-b418-9ba9f4fe5523/Dr-Strange-Wallpapers.jpeg",
                    "title": "Dr-Strange-Wallpapers.jpeg",
                    "type_media": [
                        "identification"
                    ],
                    "url": "https://t1.mobio.vn/static/87d9e1f3-6da0-415f-b418-9ba9f4fe5523/Dr-Strange-Wallpapers.jpeg"
                },
                "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
                "updated_time": "2022-07-25T10:17:35Z"
            }
        ],
        "remove": []
    },
    "deal_id": "62d1315f788e91d857f24115",
    "event_type": "UpdateMediaDeal",
    "id": "62de6dcbb483efaabc8e5fd8",
    "line_event": "merchant",
    "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
    "source": "SALE",
    "source_event": "Deal",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin Owner",
        "id": "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737",
        "phone_number": "+***********",
        "username": "admin@msbeb"
    }
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductBank
{
    "_id": "62b137761298bda92284637b",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "id": "62b03b964ef591000fd000a2",
                "name": "sản phẩm 1"
            }
        ],
        "display_type": "dropdown_single_line",
        "field_key": "products_bank",
        "field_name": "products_bank",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_product"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137761298bda922846379",
    "event_type": "AddProductBank",
    "id": "62b137761298bda92284637b",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport
{
    "_id": "648ace0a4c5a1e64615a1557",
    "action_time": **********.324215,
    "action_type": null,
    "activity": "UpdateCallReport",
    "body": [
        {
            "financial_situation_sub": [
                {
                    "add": [],
                    "change": [],
                    "display_type": "nested",
                    "field_key": "financial_situation",
                    "field_name": "Tình hình tài chính",
                    "field_property": 2,
                    "group": "financial_situation_sub",
                    "paths": [
                        {
                            "data": [
                                {
                                    "add": [
                                        2323
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "owners_equity",
                                    "field_name": "Vốn chủ sở hữu (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 5,
                                    "order_list": 28,
                                    "order_preview": 24,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        2323
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "profit_after_tax",
                                    "field_name": "Lợi nhuận sau thuế  (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 6,
                                    "order_list": 29,
                                    "order_preview": 25,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        "3232"
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "roa",
                                    "field_name": "ROA (%)",
                                    "field_property": 2,
                                    "group": "financial_situation_sub",
                                    "order": 9,
                                    "order_list": 32,
                                    "order_preview": 28,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        232323
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "total_assets",
                                    "field_name": "Tổng tài sản (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 4,
                                    "order_list": 27,
                                    "order_preview": 23,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        23232
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "total_debt_at_credit_institutions",
                                    "field_name": "Tổng nợ vay tại các TCTD (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 7,
                                    "order_list": 30,
                                    "order_preview": 26,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        32323
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "total_revenue",
                                    "field_name": "Tổng doanh thu (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 3,
                                    "order_list": 26,
                                    "order_preview": 22,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        "3232"
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "working_capital_turnover",
                                    "field_name": "Vòng quay VLĐ",
                                    "field_property": 2,
                                    "group": "financial_situation_sub",
                                    "order": 8,
                                    "order_list": 31,
                                    "order_preview": 27,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                }
                            ],
                            "report_year": 2023,
                            "type_report": "TAX_REPORT"
                        }
                    ],
                    "remove": []
                }
            ]
        },
        {
            "credit_organization": [
                {
                    "add": [],
                    "change": [],
                    "display_type": "nested",
                    "field_key": "relationship_between_other_credit_institutions",
                    "field_name": "Tổ chức tín dụng",
                    "field_property": 2,
                    "group": "credit_organization",
                    "paths": [
                        {
                            "data": [
                                {
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "nested",
                                    "field_key": "credit_information",
                                    "field_name": "Thông tin tín dụng",
                                    "field_property": 2,
                                    "group": "credit_organization",
                                    "order": 2,
                                    "order_list": 36,
                                    "order_preview": 32,
                                    "paths": [
                                        {
                                            "data": [],
                                            "period": "short_term"
                                        }
                                    ],
                                    "require": false,
                                    "status": 1
                                }
                            ],
                            "id": 1,
                            "name_of_credit_institution": null
                        }
                    ],
                    "remove": []
                }
            ]
        }
    ],
    "call_report_id": "648acd3146edb4c03a1cf2f4",
    "created_time": "2023-06-15T08:38:34.000Z",
    "deal_id": "648ac2146aa132e46f818dd6",
    "event_id": "648ace0a4c5a1e64615a1555",
    "event_type": "UpdateCallReport",
    "group_configs": [
        {
            "group_index": 2.1,
            "group_key": "financial_situation_sub",
            "group_name": "Tình hình tài chính"
        },
        {
            "group_index": 3.1,
            "group_key": "credit_organization",
            "group_name": "Tổ chức tín dụng"
        }
    ],
    "id": "648ace0a4c5a1e64615a1557",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Mai Doãn",
        "id": "de97719f-916e-4bcd-900b-94a142e4023e",
        "phone_number": "+***********",
        "username": "Maidt@hdb"
    },
    "unique_value": "648ace09c01cc8fd2e87065e"
}

@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport, action_type:ApprovalBU (Trình ĐVKD duyệt)
{
    "_id": "648acdd54c5a1e64615a1552",
    "action_time": **********.943061,
    "action_type": "ApprovalBU",
    "activity": "UpdateCallReport",
    "body": null,
    "call_report_id": "648acd3146edb4c03a1cf2f4",
    "created_time": "2023-06-15T08:37:41.000Z",
    "data_approval": {
        "accounts_to": [
            {
                "email": "<EMAIL>",
                "fullname": "MAI THỊ ÁI",
                "participants_staff_code": "HD001903",
                "participants_title": "Kiểm soát viên",
                "username": "aimt@hdb"
            }
        ],
        "action": "approval_bu",
        "opinion": null,
        "participants_enterprise_customer_relationship": "Test Mobio",
        "participants_staff_code": "SMT05",
        "participants_title": null
    },
    "deal_id": "648ac2146aa132e46f818dd6",
    "event_id": "648acdd54c5a1e64615a1550",
    "event_type": "ApprovalBU",
    "id": "648acdd54c5a1e64615a1552",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Test Mobio",
        "id": "4e7c97b4-9fb3-49ea-a1f9-a6c0a831413d",
        "phone_number": "+***********",
        "username": "smt05@hdb"
    },
    "unique_value": "648acdce169f2bd4449fbef4"
}

@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport, action_type:UpdateStatusBU (Cập nhật trạng thái duyệt tại ĐVKD)
{
    "_id": "648ace594c5a1e64615a155d",
    "action_time": **********.161219,
    "action_type": "UpdateStatusBU",
    "activity": "UpdateCallReport",
    "body": null,
    "call_report_id": "648acd3146edb4c03a1cf2f4",
    "created_time": "2023-06-15T08:39:53.000Z",
    "data_approval": {
        "accounts_to": [],
        "action": "additional_information_bu",
        "opinion": null,
        "participants_enterprise_customer_relationship": "Mai Doãn",
        "participants_staff_code": "NVMANUAL123",
        "participants_title": null,
        "status_approval": "provide_additional_information"
    },
    "deal_id": "648ac2146aa132e46f818dd6",
    "event_id": "648ace594c5a1e64615a155b",
    "event_type": "UpdateStatusBU",
    "id": "648ace594c5a1e64615a155d",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {},
    "unique_value": "648ace54169f2bd4449fbef6"
}


@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport, action_type:UpdateStatusHO (Cập nhật trạng thái duyệt tại Hội sở )
{
    "_id": "648c3e38bd0862ff25783ff1",
    "action_time": **********.791481,
    "action_type": "UpdateStatusHO",
    "activity": "UpdateCallReport",
    "body": null,
    "call_report_id": "648c32d10da8e13b7cef5eed",
    "created_time": "2023-06-16T10:49:27.000Z",
    "data_approval": {
        "accounts_to": [],
        "action": "approve_ho",
        "opinion": null,
        "participants_enterprise_customer_relationship": "Hoàng Nguyễn Châu Phương Nam",
        "participants_staff_code": "",
        "participants_title": null,
        "status_approval": "approve"
    },
    "deal_id": "648c31681afaffa942747095",
    "event_id": "648c3e37bd0862ff25783fef",
    "event_type": "UpdateStatusHO",
    "id": "648c3e38bd0862ff25783ff1",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {},
    "unique_value": "648c3e3752a953ed4916cb7d"
}
@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport, action_type:ApprovalHO (Trình hội sở duyệt)
{
    "_id": "648c3dfdbd0862ff25783fec",
    "action_time": **********.346201,
    "action_type": "ApprovalHO",
    "activity": "UpdateCallReport",
    "body": null,
    "call_report_id": "648c32d10da8e13b7cef5eed",
    "created_time": "2023-06-16T10:48:29.000Z",
    "data_approval": {
        "accounts_to": [
            {
                "email": "<EMAIL>",
                "fullname": "Hoàng Nguyễn Châu Phương Nam",
                "participants_staff_code": "",
                "participants_title": null,
                "username": "datpt@hdb"
            }
        ],
        "action": "approval_ho",
        "opinion": null,
        "participants_enterprise_customer_relationship": "Mai Doãn",
        "participants_staff_code": "NVMANUAL123",
        "participants_title": null
    },
    "deal_id": "648c31681afaffa942747095",
    "event_id": "648c3dfdbd0862ff25783fea",
    "event_type": "ApprovalHO",
    "id": "648c3dfdbd0862ff25783fec",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Mai Doãn",
        "id": "de97719f-916e-4bcd-900b-94a142e4023e",
        "phone_number": "+***********",
        "username": "Maidt@hdb"
    },
    "unique_value": "648c3dfb52a953ed4916cb7b"
}

@apiSuccessExample {json} Response, SourceEvent: SaleMemo, action_type=ADD
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "unique_value": "64f8279f967809ee6200497f",
  "object_id": "64d3419ec8cb7026f771380b",
  "object_type": "COMPANY",
  "event_id": "64f827a1f4bc88bb06dfcf6a",
  "source_event": "SaleMemo",
  "event_type": "InitSaleMemo",
  "line_event": "merchant",
  "activity": "SaleMemo",
  "source": "SaleMemo",
  "action_time": 1693984671.909901,
  "action_type": "ADD",
  "sale_memo_id": "64f8279f967809ee6200497e",
  "created_time": "2023-09-06T07:17:53.000Z",
  "staff_update_deal": {
    "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "email": "<EMAIL>",
    "fullname": "Admin pingcomshop",
    "phone_number": "+***********",
    "username": "admin@pingcomshop"
  },
  "group_configs": [
    {
      "group_name": "Nhu c\\u1ea7u c\\u1ee7a kh\\u00e1ch h\\u00e0ng",
      "group_key": "customer_need",
      "group_index": 2,
      "group_parent_key": null
    },
    {
      "group_name": "\\u0110\\u1ed1i th\\u1ee7 c\\u1ea1nh tranh",
      "group_key": "rivals",
      "group_index": 3,
      "group_parent_key": null
    },
    {
      "group_name": "Th\\u00f4ng tin chung",
      "group_key": "information_sub",
      "group_index": 1.1,
      "group_parent_key": "information"
    },
    {
      "group_name": "Th\\u00e0nh ph\\u1ea7n tham gia",
      "group_key": "participating_members",
      "group_index": 1.2,
      "group_parent_key": "participating_members"
    },
    {
      "group_name": "Ph\\u01b0\\u01a1ng \\u00e1n",
      "group_key": "plan",
      "group_index": 4,
      "group_parent_key": null
    }
  ],
  "body": [
    {
      "customer_need": [
        {
          "field_name": "Nhu c\\u1ea7u c\\u1ee7a kh\\u00e1ch h\\u00e0ng",
          "field_key": "customer_needs",
          "display_type": "nested",
          "group": "customer_need",
          "paths": [
            {
              "type": "customer_total_demand",
              "data": [
                {
                  "field_name": "Vay v\\u1ed1n",
                  "field_key": "borrowing",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 12,
                  "order_list": 12,
                  "order_preview": 12,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_borrowing",
                  "merchant_id": "DEFAULT",
                  "add": [
                    32312
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "B\\u1ea3o l\\u00e3nh",
                  "field_key": "surety",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 13,
                  "order_list": 13,
                  "order_preview": 13,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_surety",
                  "merchant_id": "DEFAULT",
                  "add": [
                    32312
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "L/C",
                  "field_key": "letter_of_credit",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 14,
                  "order_list": 14,
                  "order_preview": 14,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_letter_of_credit",
                  "merchant_id": "DEFAULT",
                  "add": [
                    32312
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "Phi t\\u00edn d\\u1ee5ng",
                  "field_key": "non_creditworthy",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 15,
                  "order_list": 15,
                  "order_preview": 15,
                  "character_limit": 500,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 2,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_non_creditworthy",
                  "merchant_id": "DEFAULT",
                  "add": [
                    "32.312"
                  ],
                  "remove": [

                  ]
                }
              ]
            },
            {
              "type": "bank_customer_needs",
              "data": [
                {
                  "field_name": "Vay v\\u1ed1n",
                  "field_key": "borrowing",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 12,
                  "order_list": 12,
                  "order_preview": 12,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_borrowing",
                  "merchant_id": "DEFAULT",
                  "add": [
                    32312
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "B\\u1ea3o l\\u00e3nh",
                  "field_key": "surety",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 13,
                  "order_list": 13,
                  "order_preview": 13,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_surety",
                  "merchant_id": "DEFAULT",
                  "add": [
                    32312
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "L/C",
                  "field_key": "letter_of_credit",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 14,
                  "order_list": 14,
                  "order_preview": 14,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_letter_of_credit",
                  "merchant_id": "DEFAULT",
                  "add": [
                    32312
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "Phi t\\u00edn d\\u1ee5ng",
                  "field_key": "non_creditworthy",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 15,
                  "order_list": 15,
                  "order_preview": 15,
                  "character_limit": 500,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 2,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_non_creditworthy",
                  "merchant_id": "DEFAULT",
                  "add": [
                    "32.312"
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "N\\u1ed9i dung chi ti\\u1ebft",
                  "field_key": "content_details",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 16,
                  "order_list": 16,
                  "order_preview": 16,
                  "is_text_area": true,
                  "character_limit": 2000,
                  "group": "customer_need",
                  "require": false,
                  "field_property": 2,
                  "paths": [

                  ],
                  "status": 1,
                  "view_type_only": "bank_customer_needs",
                  "translate_key": "i18n_content_details",
                  "merchant_id": "DEFAULT",
                  "add": [
                    "32.312"
                  ],
                  "remove": [

                  ]
                }
              ]
            }
          ],
          "field_property": 2,
          "translate_key": "i18n_customer_needs",
          "add": [

          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "rivals": [
        {
          "field_name": "\\u0110\\u1ed1i th\\u1ee7 c\\u1ea1nh tranh c\\u1ee7a MSB",
          "field_key": "rivals",
          "display_type": "nested",
          "group": "rivals",
          "field_property": 2,
          "paths": [
            {
              "id": null,
              "credit_organization_name": "32.312",
              "data": [
                {
                  "field_name": "Th\\u00f4ng tin s\\u1ea3n ph\\u1ea9m",
                  "field_key": "product_info",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_in_event": true,
                  "display_type": "nested",
                  "order": 19,
                  "order_list": 19,
                  "order_preview": 19,
                  "group": "rivals",
                  "require": false,
                  "field_property": 2,
                  "merchant_id": "DEFAULT",
                  "translate_key": "i18n_product_info",
                  "status": 1,
                  "add": [
                    [
                      {
                        "product_name": "32.312",
                        "balance": 32312
                      }
                    ]
                  ],
                  "remove": [

                  ]
                }
              ]
            }
          ],
          "translate_key": "i18n_rivals",
          "add": [
            {
              "product_info": [
                {
                  "product_name": "32.312",
                  "balance": 32312
                }
              ],
              "credit_organization_name": "32.312"
            }
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "\\u0110\\u1ed1i th\\u1ee7 c\\u1ea1nh tranh c\\u1ee7a MSB",
          "field_key": "rivals",
          "display_type": "nested",
          "group": "rivals",
          "field_property": 2,
          "paths": [
            {
              "id": null,
              "credit_organization_name": "32.312",
              "data": [
                {
                  "field_name": "Th\\u00f4ng tin s\\u1ea3n ph\\u1ea9m",
                  "field_key": "product_info",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_in_event": true,
                  "display_type": "nested",
                  "order": 19,
                  "order_list": 19,
                  "order_preview": 19,
                  "group": "rivals",
                  "require": false,
                  "field_property": 2,
                  "merchant_id": "DEFAULT",
                  "translate_key": "i18n_product_info",
                  "status": 1,
                  "add": [
                    [
                      {
                        "product_name": "32.312",
                        "balance": 32312
                      }
                    ]
                  ],
                  "remove": [

                  ]
                }
              ]
            }
          ],
          "translate_key": "i18n_rivals",
          "add": [
            {
              "product_info": [
                {
                  "product_name": "32.312",
                  "balance": 32312
                }
              ],
              "credit_organization_name": "32.312"
            }
          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "information_sub": [
        {
          "field_name": "K\\u1ebft qu\\u1ea3 meeting",
          "field_key": "meeting_result",
          "display_type": "dropdown_single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_meeting_result",
          "add": [
            "customer_acceptance"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Profiles",
          "field_key": "profile",
          "display_type": "tags",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_profile",
          "add": [
            "2d9703f9-3b7d-4f3e-9366-714360a9021c"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Ng\\u01b0\\u1eddi nh\\u1eadn th\\u00f4ng tin",
          "field_key": "recipients",
          "display_type": "tags",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_recipients",
          "add": [
            "<EMAIL>",
            "nam"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Kinh nghi\\u1ec7m SXKD",
          "field_key": "business_production_experience",
          "display_type": "single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_business_production_experience",
          "add": [
            "qweqwe"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "M\\u1ed1i quan h\\u1ec7 trong ng\\u00e0nh",
          "field_key": "industry_relationships",
          "display_type": "single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_industry_relationships",
          "add": [
            "qweqwe"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "T\\u00ecnh h\\u00ecnh t\\u00e0i ch\\u00ednh",
          "field_key": "financial_condition",
          "display_type": "single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_financial_situation",
          "add": [
            "qweqweq"
          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "participating_members": [
        {
          "field_name": "Tham d\\u1ef1 ph\\u00eda kh\\u00e1ch h\\u00e0ng",
          "field_key": "customer_participants",
          "display_type": "multi_line",
          "group": "participating_members",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_customer_participants",
          "add": [
            "weqweq"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Tham d\\u1ef1 ph\\u00eda MSB",
          "field_key": "bank_participants",
          "display_type": "multi_line",
          "group": "participating_members",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_bank_participants",
          "add": [
            "qweqwe"
          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "plan": [
        {
          "field_name": "Ph\\u01b0\\u01a1ng \\u00e1n khai th\\u00e1c kh\\u00e1ch h\\u00e0ng",
          "field_key": "customer_exploitation_plan",
          "display_type": "single_line",
          "group": "plan",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_customer_exploitation_plan",
          "add": [
            "32.312"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Ph\\u01b0\\u01a1ng \\u00e1n vay v\\u1ed1n c\\u1ee7a kh\\u00e1ch h\\u00e0ng",
          "field_key": "customer_loan_plan",
          "display_type": "single_line",
          "group": "plan",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_customer_loan_plan",
          "add": [
            "32.312"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Ph\\u01b0\\u01a1ng \\u00e1n t\\u00e0i s\\u1ea3n b\\u1ea3o \\u0111\\u1ea3m",
          "field_key": "asset_collateral_plan",
          "display_type": "single_line",
          "group": "plan",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_asset_collateral_plan",
          "add": [
            "32.312"
          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    }
  ]
}

@apiSuccessExample {json} Response, SourceEvent: SaleMemo, action_type=UPDATE
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "unique_value": "64f82919f92657402f6faa5a",
  "object_id": "64d3419ec8cb7026f771380b",
  "object_type": "COMPANY",
  "event_id": "64f82ddc7b4962c9a8bb1adc",
  "source_event": "SaleMemo",
  "event_type": "UpdateSaleMemo",
  "line_event": "merchant",
  "activity": "SaleMemo",
  "source": "SaleMemo",
  "action_time": 1693985049.972594,
  "action_type": "UPDATE",
  "sale_memo_id": "64f8279f967809ee6200497e",
  "created_time": "2023-09-06T07:44:28.000Z",
  "staff_update_deal": {
    "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "email": "<EMAIL>",
    "fullname": "Admin pingcomshop",
    "phone_number": "+***********",
    "username": "admin@pingcomshop"
  },
  "group_configs": [
    {
      "group_name": "Nhu c\\u1ea7u c\\u1ee7a kh\\u00e1ch h\\u00e0ng",
      "group_key": "customer_need",
      "group_index": 2,
      "group_parent_key": null
    },
    {
      "group_name": "\\u0110\\u1ed1i th\\u1ee7 c\\u1ea1nh tranh",
      "group_key": "rivals",
      "group_index": 3,
      "group_parent_key": null
    },
    {
      "group_name": "Th\\u00f4ng tin chung",
      "group_key": "information_sub",
      "group_index": 1.1,
      "group_parent_key": "information"
    },
    {
      "group_name": "Th\\u00e0nh ph\\u1ea7n tham gia",
      "group_key": "participating_members",
      "group_index": 1.2,
      "group_parent_key": "participating_members"
    }
  ],
  "body": [
    {
      "customer_need": [
        {
          "field_name": "Nhu c\\u1ea7u c\\u1ee7a kh\\u00e1ch h\\u00e0ng",
          "field_key": "customer_needs",
          "display_type": "nested",
          "group": "customer_need",
          "paths": [
            {
              "type": "customer_total_demand",
              "data": [
                {
                  "field_name": "Vay v\\u1ed1n",
                  "field_key": "borrowing",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 12,
                  "order_list": 12,
                  "order_preview": 12,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_borrowing",
                  "merchant_id": "DEFAULT",
                  "add": [
                    54
                  ],
                  "remove": [
                    32312
                  ]
                },
                {
                  "field_name": "B\\u1ea3o l\\u00e3nh",
                  "field_key": "surety",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 13,
                  "order_list": 13,
                  "order_preview": 13,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_surety",
                  "merchant_id": "DEFAULT",
                  "add": [
                    54
                  ],
                  "remove": [
                    32312
                  ]
                },
                {
                  "field_name": "L/C",
                  "field_key": "letter_of_credit",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 14,
                  "order_list": 14,
                  "order_preview": 14,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_letter_of_credit",
                  "merchant_id": "DEFAULT",
                  "add": [
                    34345
                  ],
                  "remove": [
                    32312
                  ]
                }
              ]
            },
            {
              "type": "bank_customer_needs",
              "data": [
                {
                  "field_name": "Vay v\\u1ed1n",
                  "field_key": "borrowing",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 12,
                  "order_list": 12,
                  "order_preview": 12,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_borrowing",
                  "merchant_id": "DEFAULT",
                  "add": [
                    23
                  ],
                  "remove": [
                    32312
                  ]
                },
                {
                  "field_name": "B\\u1ea3o l\\u00e3nh",
                  "field_key": "surety",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 13,
                  "order_list": 13,
                  "order_preview": 13,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_surety",
                  "merchant_id": "DEFAULT",
                  "add": [
                    665
                  ],
                  "remove": [
                    32312
                  ]
                },
                {
                  "field_name": "L/C",
                  "field_key": "letter_of_credit",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 14,
                  "order_list": 14,
                  "order_preview": 14,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_letter_of_credit",
                  "merchant_id": "DEFAULT",
                  "add": [
                    46
                  ],
                  "remove": [
                    32312
                  ]
                }
              ]
            }
          ],
          "field_property": 2,
          "translate_key": "i18n_customer_needs",
          "add": [

          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "rivals": [
        {
          "field_name": "\\u0110\\u1ed1i th\\u1ee7 c\\u1ea1nh tranh c\\u1ee7a MSB",
          "field_key": "rivals",
          "display_type": "nested",
          "group": "rivals",
          "field_property": 2,
          "paths": [
            {
              "id": 1,
              "credit_organization_name": "32.312",
              "data": [
                {
                  "field_name": "Th\\u00f4ng tin s\\u1ea3n ph\\u1ea9m",
                  "field_key": "product_info",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_in_event": true,
                  "display_type": "nested",
                  "order": 19,
                  "order_list": 19,
                  "order_preview": 19,
                  "group": "rivals",
                  "require": false,
                  "field_property": 2,
                  "merchant_id": "DEFAULT",
                  "translate_key": "i18n_product_info",
                  "status": 1,
                  "paths": [
                    {
                      "id": null,
                      "data": [
                        {
                          "field_name": "T\\u00ean s\\u1ea3n ph\\u1ea9m",
                          "field_key": "product_name",
                          "display_in_form_list": true,
                          "display_in_form_add": true,
                          "display_in_form_update": true,
                          "display_in_form_preview": true,
                          "disable_form_add": false,
                          "disable_form_update": false,
                          "display_in_event": true,
                          "is_border": true,
                          "display_type": "single_line",
                          "order": 20,
                          "order_list": 20,
                          "order_preview": 20,
                          "group": "rivals",
                          "character_limit": 128,
                          "require": true,
                          "field_property": 2,
                          "paths": [

                          ],
                          "status": 1,
                          "translate_key": "i18n_product_name",
                          "merchant_id": "DEFAULT",
                          "add": [

                          ],
                          "remove": [
                            "32.312"
                          ]
                        },
                        {
                          "field_name": "Nh\\u1eadp s\\u1ed1 d\\u01b0",
                          "field_key": "balance",
                          "display_in_form_list": true,
                          "display_in_form_add": true,
                          "display_in_form_update": true,
                          "display_in_form_preview": true,
                          "disable_form_add": false,
                          "disable_form_update": false,
                          "display_in_event": true,
                          "is_border": true,
                          "display_type": "single_line",
                          "order": 21,
                          "order_list": 21,
                          "order_preview": 21,
                          "group": "rivals",
                          "require": true,
                          "field_property": 1,
                          "paths": [

                          ],
                          "status": 1,
                          "translate_key": "i18n_balance",
                          "merchant_id": "DEFAULT",
                          "add": [

                          ],
                          "remove": [
                            32312
                          ]
                        }
                      ]
                    }
                  ]
                },
                {
                  "field_name": "T\\u00ean t\\u1ed5 ch\\u1ee9c t\\u00edn d\\u1ee5ng",
                  "field_key": "credit_organization_name",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_in_event": true,
                  "is_border": true,
                  "display_type": "single_line",
                  "order": 18,
                  "order_list": 18,
                  "order_preview": 18,
                  "group": "rivals",
                  "require": true,
                  "field_property": 2,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_credit_organization_name",
                  "merchant_id": "DEFAULT",
                  "add": [

                  ],
                  "remove": [
                    "32.312"
                  ]
                }
              ]
            },
            {
              "id": 2,
              "credit_organization_name": "32.312",
              "data": [
                {
                  "field_name": "Th\\u00f4ng tin s\\u1ea3n ph\\u1ea9m",
                  "field_key": "product_info",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_in_event": true,
                  "display_type": "nested",
                  "order": 19,
                  "order_list": 19,
                  "order_preview": 19,
                  "group": "rivals",
                  "require": false,
                  "field_property": 2,
                  "merchant_id": "DEFAULT",
                  "translate_key": "i18n_product_info",
                  "status": 1,
                  "paths": [
                    {
                      "id": 1,
                      "data": [
                        {
                          "field_name": "T\\u00ean s\\u1ea3n ph\\u1ea9m",
                          "field_key": "product_name",
                          "display_in_form_list": true,
                          "display_in_form_add": true,
                          "display_in_form_update": true,
                          "display_in_form_preview": true,
                          "disable_form_add": false,
                          "disable_form_update": false,
                          "display_in_event": true,
                          "is_border": true,
                          "display_type": "single_line",
                          "order": 20,
                          "order_list": 20,
                          "order_preview": 20,
                          "group": "rivals",
                          "character_limit": 128,
                          "require": true,
                          "field_property": 2,
                          "paths": [

                          ],
                          "status": 1,
                          "translate_key": "i18n_product_name",
                          "merchant_id": "DEFAULT",
                          "add": [
                            "32.312"
                          ],
                          "remove": [

                          ]
                        },
                        {
                          "field_name": "Nh\\u1eadp s\\u1ed1 d\\u01b0",
                          "field_key": "balance",
                          "display_in_form_list": true,
                          "display_in_form_add": true,
                          "display_in_form_update": true,
                          "display_in_form_preview": true,
                          "disable_form_add": false,
                          "disable_form_update": false,
                          "display_in_event": true,
                          "is_border": true,
                          "display_type": "single_line",
                          "order": 21,
                          "order_list": 21,
                          "order_preview": 21,
                          "group": "rivals",
                          "require": true,
                          "field_property": 1,
                          "paths": [

                          ],
                          "status": 1,
                          "translate_key": "i18n_balance",
                          "merchant_id": "DEFAULT",
                          "add": [
                            32312
                          ],
                          "remove": [

                          ]
                        }
                      ]
                    },
                    {
                      "id": 2,
                      "data": [
                        {
                          "field_name": "T\\u00ean s\\u1ea3n ph\\u1ea9m",
                          "field_key": "product_name",
                          "display_in_form_list": true,
                          "display_in_form_add": true,
                          "display_in_form_update": true,
                          "display_in_form_preview": true,
                          "disable_form_add": false,
                          "disable_form_update": false,
                          "display_in_event": true,
                          "is_border": true,
                          "display_type": "single_line",
                          "order": 20,
                          "order_list": 20,
                          "order_preview": 20,
                          "group": "rivals",
                          "character_limit": 128,
                          "require": true,
                          "field_property": 2,
                          "paths": [

                          ],
                          "status": 1,
                          "translate_key": "i18n_product_name",
                          "merchant_id": "DEFAULT",
                          "add": [
                            "San pham 2"
                          ],
                          "remove": [

                          ]
                        },
                        {
                          "field_name": "Nh\\u1eadp s\\u1ed1 d\\u01b0",
                          "field_key": "balance",
                          "display_in_form_list": true,
                          "display_in_form_add": true,
                          "display_in_form_update": true,
                          "display_in_form_preview": true,
                          "disable_form_add": false,
                          "disable_form_update": false,
                          "display_in_event": true,
                          "is_border": true,
                          "display_type": "single_line",
                          "order": 21,
                          "order_list": 21,
                          "order_preview": 21,
                          "group": "rivals",
                          "require": true,
                          "field_property": 1,
                          "paths": [

                          ],
                          "status": 1,
                          "translate_key": "i18n_balance",
                          "merchant_id": "DEFAULT",
                          "add": [
                            32312
                          ],
                          "remove": [

                          ]
                        }
                      ]
                    }
                  ]
                },
                {
                  "field_name": "T\\u00ean t\\u1ed5 ch\\u1ee9c t\\u00edn d\\u1ee5ng",
                  "field_key": "credit_organization_name",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_in_event": true,
                  "is_border": true,
                  "display_type": "single_line",
                  "order": 18,
                  "order_list": 18,
                  "order_preview": 18,
                  "group": "rivals",
                  "require": true,
                  "field_property": 2,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_credit_organization_name",
                  "merchant_id": "DEFAULT",
                  "add": [
                    "32.312"
                  ],
                  "remove": [

                  ]
                }
              ]
            }
          ],
          "translate_key": "i18n_rivals",
          "add": [

          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "information_sub": [
        {
          "field_name": "Th\\u1eddi gian g\\u1eb7p",
          "field_key": "meeting_time",
          "display_type": "date_picker",
          "format": "dd/mm/yyyy hh:mm",
          "group": "information_sub",
          "paths": [

          ],
          "translate_key": "i18n_time_meet",
          "add": [
            "2023-09-06T04:15:00.000Z"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Ng\\u01b0\\u1eddi nh\\u1eadn th\\u00f4ng tin",
          "field_key": "recipients",
          "display_type": "tags",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_recipients",
          "add": [
            "<EMAIL>"
          ],
          "change": [

          ],
          "remove": [
            "nam"
          ]
        },
        {
          "field_name": "Kinh nghi\\u1ec7m SXKD",
          "field_key": "business_production_experience",
          "display_type": "single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_business_production_experience",
          "add": [

          ],
          "change": [
            {
              "from": "qweqwe",
              "to": "ds"
            }
          ],
          "remove": [

          ]
        },
        {
          "field_name": "M\\u1ed1i quan h\\u1ec7 trong ng\\u00e0nh",
          "field_key": "industry_relationships",
          "display_type": "single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_industry_relationships",
          "add": [

          ],
          "change": [
            {
              "from": "qweqwe",
              "to": "ds"
            }
          ],
          "remove": [

          ]
        },
        {
          "field_name": "T\\u00ecnh h\\u00ecnh t\\u00e0i ch\\u00ednh",
          "field_key": "financial_condition",
          "display_type": "single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_financial_situation",
          "add": [

          ],
          "change": [
            {
              "from": "qweqweq",
              "to": "d"
            }
          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "participating_members": [
        {
          "field_name": "Tham d\\u1ef1 ph\\u00eda kh\\u00e1ch h\\u00e0ng",
          "field_key": "customer_participants",
          "display_type": "multi_line",
          "group": "participating_members",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_customer_participants",
          "add": [
            "fd"
          ],
          "change": [

          ],
          "remove": [
            "weqweq"
          ]
        },
        {
          "field_name": "Tham d\\u1ef1 ph\\u00eda MSB",
          "field_key": "bank_participants",
          "display_type": "multi_line",
          "group": "participating_members",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_bank_participants",
          "add": [
            "fd"
          ],
          "change": [

          ],
          "remove": [
            "qweqwe"
          ]
        }
      ]
    }
  ]
}

@apiSuccessExample {json} Response, SourceEvent: SaleMemo, action_type=REMOVE
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "unique_value": "64f831792f5b2d41f415ed6c",
  "object_id": "64d3419ec8cb7026f771380b",
  "object_type": "COMPANY",
  "event_id": "64f83179f4bc88bb06dfcf6f",
  "source_event": "SaleMemo",
  "event_type": "RemoveSaleMemo",
  "line_event": "merchant",
  "activity": "SaleMemo",
  "source": "SaleMemo",
  "action_time": **********.080557,
  "action_type": "REMOVE",
  "sale_memo_id": "64f82b05967809ee62004981",
  "created_time": "2023-09-06T07:59:53.000Z",
  "meeting_result": ""
  "staff_update_deal": {
    "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "email": "<EMAIL>",
    "fullname": "Admin pingcomshop",
    "phone_number": "+***********",
    "username": "admin@pingcomshop"
  },
  "body": null
}
"""

"""
@api {POST} {domain}/sale/api/v1.0/deals/<deal_id>/action/filter/activity      Lấy danh sách event của Deal
@apiGroup Event
@apiVersion 1.0.4
@apiName ListEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (QUERY:) {string=EMAIL,SOCIAL,SMS,CALLCENTER,OTHER}                  [source_event]         Tab muốn lấy danh sách event. Mặc định khi đứng ở tab <code>Tất cả</code> thì không cần truyền lên giá trị này.
                                                                                                        <ul>
                                                                                                            <li><code>EMAIL</code>: Tab Email</li>
                                                                                                            <li><code>SOCIAL</code>: Tab Online và mạng xã hội</li>
                                                                                                            <li><code>SMS</code>: Tab SMS</li>
                                                                                                            <li><code>CALLCENTER</code>: Tab cuộc gọi</li>
                                                                                                            <li><code>OTHER</code>: Tab khác</li>
                                                                                                        </ul> 

@apiParam   (BODY:) {Array}                     brand_ids              Danh sách các thương hiệu cần tìm kiếm
@apiParam   (BODY:) {Array}                     staff_ids              Danh sách nhân viên tương tác với Profile
@apiParam   (BODY:) {string}                    start_time             Thời gian bắt đầu tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {string}                    end_time               Thời gian kết thúc tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {Array}                     activities             Danh sách hoạt động cần tìm kiếm
                                                                        <ul>
                                                                            <li><code>UpdateDeal</code>: Cập nhật thông tin đơn hàng</li>
                                                                            <li><code>AssignmentForwardDeal</code>: Phân công chuyển tiếp đơn hàng</li>
                                                                            <li><code>Email</code>: E-mail</li>
                                                                            <li><code>Facebook</code>: Tương tác qua Facebook</li>
                                                                            <li><code>Zalo</code>: Tương tác qua Zalo</li>
                                                                            <li><code>SMS</code>: SMS</li>
                                                                            <li><code>Callcenter</code>: Gọi điện</li>
                                                                            <li><code>Other</code>: Khác</li>
                                                                            <li><code>UpdateCallReport</code>: Cập nhật call report</li>
                                                                            <li><code>SaleMemo</code>: Sale Memo</li>
                                                                        </ul>

@apiParamExample {json} Body example
{
    "brand_ids": [].
    "staff_ids": [],
    "start_time": "2021-10-15",
    "end_time": "2022-10-15",
    "activity": ["UpdateDeal"]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách các Event tương ứng.

@apiSuccess {String}            data.merchant_id              Định danh merchant.
@apiSuccess {String}            data.deal_id                  Định danh đơn hàng.
@apiSuccess {String}            data.event_id                 Định danh Event 
@apiSuccess {String}            data.source_event               Loại Event
                                                              <ul>
                                                                    <li><code>DEAL</code>: Loại event phát sinh của đơn hàng</li>
                                                                    <li><code>EMAIL</code>: Loại event E-mail</li>
                                                                    <li><code>SOCIAL</code>: Loại event phát sinh từ mạng xã hội</li>
                                                                    <li><code>SMS</code>: Loại event phát sinh từ SMS</li>
                                                                    <li><code>CALLCENTER</code>: Loại event phát sinh từ Gọi điện</li>
                                                                    <li><code>OTHER</code>: Khác</li>
                                                              </ul>
@apiSuccess {String}            data.event_type             Loại của chi tiết Event
                                                            <ul>Source event là <code>DEAL</code>
                                                                <li><code>InitDeal</code>: Tạo đơn hàng</li>
                                                                <li>
                                                                    <code>UpdateProfile</code>: Cập nhật liên hệ profile và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li>
                                                                    <code>UpdateCompanies</code>: Cập nhật liên hệ công ty và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li><code>UpdateTicket</code>: Cập nhật liên hệ ticket và đơn hàng</li>
                                                                <li><code>AddProductCategoriesMain</code>: Gắn liên hệ đơn hàng và danh mục chính</li>
                                                                <li><code>AddProductCategoriesSub</code>: Gắn liên hệ đơn hàng và danh mục phụ</li>
                                                                <li><code>RemoveProductCategoriesMain</code>: Gỡ liên hệ đơn hàng và danh mục chính</li>
                                                                <li><code>AddProductBank</code>: Gắn liên hệ giữa cơ hội bán và sản phẩm bank</li>
                                                                <li><code>RemoveProductBank</code>: Gỡ liên hệ giữa cơ hội bán và sản phẩm bank</li>
                                                                <li><code>AddProductLine</code>: Gắn liên hệ giữa cơ hội bán và dòng sản phẩm</li>
                                                                <li><code>RemoveProductLine</code>: Gỡ liên hệ giữa cơ hội bán và dòng sản phẩm</li>
                                                                <li><code>AddProductType</code>: Gắn liên hệ giữa cơ hội bán và loại sản phẩm</li>
                                                                <li><code>RemoveProductType</code>: Gỡ liên hệ giữa cơ hội bán và loại sản phẩm</li>
                                                                <li>
                                                                    <code>UpdateMediaDeal</code>: Cập nhật liên hệ media và đơn hàng
                                                                    <p><b>Note:</b> Trong 1 event loại này đảm bảo chỉ có duy nhất 1 action (Gắn hoặc gỡ xảy ra)</p>
                                                                </li>
                                                                <li><code>UpdateCallReport</code>: Cập nhật call report</li>
                                                                <li><code>SlaOnTime</code>: Đúng hạn SLA</li>
                                                                <li><code>SlaOverdue</code>: Quá hạn SLA</li>
                                                            </ul>
                                                            <ul>Source event là <code>SOCIAL</code>
                                                                <li><code>1</code>: "Facebook"</li>
                                                                <li><code>2</code>: "Zalo"</li>
                                                                <li><code>3</code>: "Instagram"</li>
                                                                <li><code>4</code>: "Youtube"</li>
                                                                <li><code>5</code>: "App"</li>
                                                                <li><code>6</code>: "Line"</li>
                                                                <li><code>7</code>: "Mobio_chat_tool"</li>
                                                            </ul>
                                                            <ul>Source event là <code>CALLCENTER</code></ul>


@apiSuccess {String}            data.action_type              Loại event <b>Dùng cho event_type <code>UpdateCallReport</code> </b></br>
                                                              <b>Trường hợp <b>event_type</b> là <code>UpdateCallReport</code> mà <b>action_type</b> không nằm trong danh sách dưới
                                                              thì là một event cập nhật thông tin <b>Call Report</b></br>
                                                              <code>ApprovalBU</code> Trình ĐVKD duyệt </br>
                                                              <code>UpdateStatusBU</code> Cập nhật trạng thái duyệt tại ĐVKD </br>
                                                              <code>ApprovalHO</code> Trình hội sở duyệt </br>
                                                              <code>UpdateStatusHO</code> Cập nhật trạng thái duyệt tại Hội sở </br>
@apiSuccess {Object}            data.staff_update_deal        Định danh nhân viên cập nhật thông tin
@apiSuccess {String}            data.line_event               Event tạo từ hệ thống hoặc người dùng: merchant, profile
@apiSuccess {String}            data.body                     Thông tin của Event
@apiSuccess {Object}            data.action_time              Thời gian diễn ra Event
@apiSuccess {Object}            data.created_time             Thời điểm tạo
@apiSuccess {Object}            data.data_approval            Thông tin duyệt <code>UpdateCallReport</code>
@apiSuccess {String}            data.data_approval.action             Hành động
@apiSuccess {String}            data.data_approval.opinion            Ý kiến
@apiSuccess {String}            data.data_approval.participants_enterprise_customer_relationship         Tên QHKHDN
@apiSuccess {String}            data.data_approval.participants_staff_code       Mã nhân viên 
@apiSuccess {String}            data.data_approval.participants_title            Chức danh
@apiSuccess {Array}             data.data_approval.accounts_to            Danh sách người nhận
@apiSuccess {Object}            data.agent                    Tác nhân thực hiện event
@apiSuccess {String}            data.agent.type               Loại tác nhân
                                                              <ul>
                                                                <li><code>USER_UPDATE</code>: Người dùng cập nhật CHB</li>
                                                                <li><code>USER_UPLOAD_FILE</code>: Người dùng upload file</li>
                                                                <li><code>DEAL_AUTO</code>: Luật tự động</li>
                                                                <li><code>DEAL_ASSIGNMENT_AUTO</code>: Luật phân công tự động</li>
                                                                <li><code>DEAL_ASSIGNMENT_GENERAL</code>: Luật phân công theo cấu hình phân công chung</li>
                                                                <li><code>DEAL_SLA</code>: SLA CHB</li>
                                                                <li><code>MODULE_JB</code>: Từ Module Journey Builder</li>
                                                                <li><code>MODULE_WF</code>: Từ Module Workflow</li>
                                                                <li><code>EXTERNAL_SYSTEM</code>: Từ hệ thống ngoài</li>
                                                                <li><code>DATA_FLOW</code>: Từ Data Flow</li>
                                                                <li><code>DEAL_REVOKE</code>: Thu hồi CHB</li>
                                                                <li><code>CONFIG_TAG</code>: Cập nhật theo cấu hình gắn tag</li>
                                                              </ul>
@apiSuccess {Object}            data.agent.detail             Thông tin chi tiết theo loại tác nhân
                                                             <table>
                                                              <thead>
                                                                <tr>
                                                                  <th style="width: 30%">Agent type</th>
                                                                  <th style="width: 60%">Agent detail</th>
                                                                </tr>
                                                              </thead>
                                                              <tbody>
                                                                <tr>
                                                                  <td><code>USER_UPDATE</code></td>
                                                                  <td>
                                                                  {
                                                                        "id": "ID USER",
                                                                        "email": "email user",
                                                                        "phone_number": "số điện thoại user",
                                                                        "username": "Tên đăng nhập user",
                                                                        "avatar": "Ảnh đại diện"
                                                                    }
                                                                  </td>
                                                                </tr>
                                                                <tr>
                                                                  <td><code>USER_UPLOAD_FILE</code></td>
                                                                  <td>
                                                                    {
                                                                        "id": "ID USER",
                                                                        "email": "email user",
                                                                        "phone_number": "số điện thoại user",
                                                                        "username": "Tên đăng nhập user",
                                                                        "avatar": "Ảnh đại diện",
                                                                        "file_name": "Tên file upload"
                                                                    }
                                                                  </td>
                                                                </tr>
                                                                <tr>
                                                                  <td><code>DEAL_AUTO</code></td>
                                                                  <td>
                                                                    {
                                                                        "sale_process_id": "ID quy trình",
                                                                        "sale_process_name": "Tên quy trình",
                                                                        "rule_id": "ID luật tự động",
                                                                        "rule_name": "Tên luật tự động"
                                                                    }
                                                                  </td>
                                                                </tr>
                                                                <tr>
                                                                  <td><code>DEAL_ASSIGNMENT_AUTO</code></td>
                                                                  <td>
                                                                    {
                                                                        "sale_process_id": "ID quy trình",
                                                                        "sale_process_name": "Tên quy trình"
                                                                    }
                                                                  </td>
                                                                </tr>
                                                                <tr>
                                                                  <td><code>DEAL_ASSIGNMENT_GENERAL</code></td>
                                                                </tr>
                                                                <tr>
                                                                  <td><code>DEAL_SLA</code></td>
                                                                  <td>
                                                                    {
                                                                        "sale_process_id": "ID quy trình",
                                                                        "sale_process_name": "Tên quy trình"
                                                                    }
                                                                  </td>
                                                                </tr>
                                                                <tr>
                                                                  <td><code>MODULE_JB</code></td>
                                                                  <td>
                                                                    {
                                                                        "id": "ID Journey",
                                                                        "name": "Tên Journey",
                                                                        "version": 4.0
                                                                    }
                                                                  </td>
                                                                </tr>
                                                                <tr>
                                                                  <td><code>MODULE_WF</code></td>
                                                                  <td>
                                                                    {
                                                                        "id": "ID WorkFlow",
                                                                        "name": "Tên WorkFlow"
                                                                    }
                                                                  </td>
                                                                </tr>
                                                                <tr>
                                                                  <td><code>EXTERNAL_SYSTEM</code></td>
                                                                  <td>
                                                                  </td>
                                                                </tr>
                                                                <tr>
                                                                  <td><code>DATA_FLOW</code></td>
                                                                  <td>
                                                                    {
                                                                        "connector_type": "Loại connector",
                                                                        "connector_name": "Tên connector"
                                                                    }
                                                                  </td>
                                                                </tr>
                                                                <tr>
                                                                  <td><code>DEAL_REVOKE</code></td>
                                                                  <td>
                                                                    {
                                                                        "team_id": "ID Team",
                                                                        "team_name": "Tên team"
                                                                    }
                                                                  </td>
                                                                </tr>
                                                                <tr>
                                                                  <td><code>CONFIG_TAG</code></td>
                                                                  <td>
                                                                  </td>
                                                                </tr>
                                                              </tbody>
                                                            </table>

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateDeal
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "information": [
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "description",
                "field_name": "description",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [
                    "333"
                ],
                "change": [],
                "remove": []
            },
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "reason_fail",
                "field_name": "reason_fail",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [],
                "change": [
                    {
                        "from": "buồn quá không mua",
                        "to": "buồn quá không mua a"
                    }
                ],
                "remove": []
            }
        ],
        "dynamic": [
            {
                "field_name": "Radio button",
                "field_key": "_dyn_radio_button_1633599591661",
                "field_property": 2,
                "display_type": "radio",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    "ba"
                ],
                "change": [],
                "remove": []
            },
            {
                "field_name": "Single-line text chữ",
                "field_key": "_dyn_single_line_text_chu_1631458349843",
                "field_property": 2,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [],
                "change": [
                    {
                        "from": "123123123123",
                        "to": "12312312399"
                    }
                ],
                "remove": []
            },
            {
                "field_name": "Single-line text số",
                "field_key": "_dyn_single_line_text_so_1631181218494",
                "field_property": 1,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    12312312
                ],
                "change": [],
                "remove": []
            }
        ]
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateProfile
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "source_event": "DEAL",
    "event_type": "UpdateProfile",
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateTicket
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateTicket",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}

@apiSuccessExample {json} Response source_event: DEAL, event_type: UpdateCompanies
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "source_event": "DEAL",
    "event_type": "UpdateCompanies",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "source": "SALE",
    "action_time" : **********.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "add": [],
        "remove": []
    }
}
@apiSuccessExample {json} Response source_event: DEAL, event_type: InitDeal
{
    "_id" : "6177a299ffdea8e0c58fe305",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "6177a296573c1357e0a67460",
    "event_id" : "6177a298ffdea8e0c58fe303",
    "source_event" : "DEAL",
    "event_type" : "InitDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+***********",
        "username" : "admin@pingcomshop"
    },
    "line_event" : "merchant",
    "source" : "SALE",
    "action_time" : **********.890302,
    "created_time" : "2021-10-15T15:05:18.856",
    "body" : {
        "profiles" : [
            {
                "id" : "6afa7308-ccf3-4974-8478-3a0f4b44ade0",
                "name" : "Cam Edit"
            }
        ],
        "assignee_id" : "01aa5dc3-36a6-42b1-b026-e7cdba3751cc",
        "state_code" : "QQ6CYXWM",
        "sale_process_id" : "61768dad74f73e51a119ea2b",
        "sale_process_name" : "Quy trình hoa hướng dương (không sửa xóa)",
        "state_name" : "Có thông tin Leads",
        "state_ratio" : 10
    }
}

@apiSuccessExample {json} Response source_event: SOCIAL
{
    "_id" : "61790f31df1001de4bd72334",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "be8bde12-c514-4788-bef8-7a85ff0d2b55",
    "event_id" : ObjectId("61790f28df1001de4bd72332"),
    "source_event" : "SOCIAL",
    "event_type" : 1,
    "staff_update_deal" : {
        "id" : "823ed8f0-83b2-491d-936d-32c2accfa15b",
        "email" : "<EMAIL>",
        "fullname" : "Admin24",
        "phone_number" : "+***********",
        "username" : "admin24@pingcomshop"
    },
    "line_event" : "merchant",
    "source" : "SOCIAL",
    "action_time" : 1635323521.467509,
    "created_time" : ISODate("2021-10-27T15:34:48.861+07:00"),
    "body" : {
        "profile_data" : {
            "profile_id" : "f78ebd1c-0854-46b0-b85b-a4cf9ddb0298",
            "id" : "f78ebd1c-0854-46b0-b85b-a4cf9ddb0298",
            "name" : "Đỗ Hòa"
        },
        "page_social_id" : "858617877680201",
        "content" : "Tùng Dịch",
        "page_name" : "Thiết bị văn phòng",
        "user_social_id" : "3551827758269419"
    }
}

@apiSuccessExample {json} Response, SourceEvent:Email:
{
    "id": "",
    "merchant_id": "",
    "deal_id": "",
    "event_id": "",
    "source_event": "Email",
    "event_type":"",
    "staff_update_deal" : {
            "id" : "d7e63da1-a47e-43e2-9b78-44d43433f7ec",
            "email" : "<EMAIL>",
            "fullname" : "sale",
            "phone_number" : "+84978348445",
            "username" : "sale@thanhtest11"
    },
    "action_time" : 1636613856.258556,
    "source": "EMAIL",
    "body": {
        "profile_data": profile_data,
        "from_email": ""		// email gửi
        "to_email": [""]		// danh sách người nhận
        "cc_email": [""]
        "bcc_email": [""]
        "subject": ""		// tiêu đề email 
        "content": ""		// nội dung email 
        "sender_domain": "mobio.vn", // có đối với kiểu email_mkt
        "related_to": {
            "company_ids": [], // Danh sách company có liên quan tới
            "ticket_ids": [], // Danh sách ticket có liên quan tới
            "profile_ids": [], // Danh sách Profile có liên quan tới
            "order_ids": [], // Danh sách các deal có liên quan tới
        },
        "attachments": [""] // Danh sách url file media 
        "status": {
            "type": "success", // Trạng thái của email đang gửi, đã gửi, gửi lỗi: pending, success, failed
            "message": "", // Trong trường hợp gửi mail lỗi thì cần gửi kèm nguyên nhân lỗi trong field này
        },
        "action_time": "",
    }
}

@apiSuccessExample {json} Response, SourceEvent: Callcenter:
{
    "_id" : "619b0507291638612986860d",
    "unique_value" : "call-vn-1-99V79H284Q-1637519225215",
    "merchant_id" : "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id" : "6185fc94d3145f3fb2dac5cf",
    "event_id" : "619b0507291638612986860b",
    "source_event" : "CALLCENTER",
    "event_type" : "SaleCallcenter",
    "staff_update_deal" : {
        "id" : "f48aad19-cbe9-4c49-baa2-b3d3e8a29bd4",
        "email" : "<EMAIL>",
        "fullname" : "Ngọc Ánh",
        "phone_number" : "+84334303858",
        "username" : "anhttn@pingcomshop"
    },
    "line_event" : "merchant",
    "activity" : "Callcenter",
    "source" : "CALLCENTER",
    "action_time" : 1637549229
    "created_time" : "2021-11-22T09:48:39.395Z,
    "body" : {
        "profile_data" : {
            "profile_id" : "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
            "profile_phone_number" : "0328914167",
            "id" : "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
            "name" : "Ngoan "
        },
        "switchboard" : "842473005768",
        "call_id" : "call-vn-1-99V79H284Q-1637519225215",
        "call_status" : 7,
        "so_may_le" : "106",
        "hot_line" : null,
        "attachments" : [
            {
                "url" : "https://t1.mobio.vn/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/call-vn-1-99V79H284Q-1637519225215.mp3",
                "local_path" : "/media/data/public_resources/static/1b99bdcf-d582-4f49-9715-1b61dfff3924/upload/call-vn-1-99V79H284Q-1637519225215.mp3",
                "filename" : "call-vn-1-99V79H284Q-1637519225215.mp3",
                "format" : "audio/mpeg"
            }
        ],
        "related_to" : {
            "profile_ids" : [
                "4c443ad9-0d82-48dc-8b65-f902ec84fc9e",
                "137c56e5-d56d-4dbc-9729-fa997e0294c3"
            ],
            "ticket_ids" : [
                "d8b34170-fb3b-401a-9d25-623844b4ef15"
            ],
            "order_ids" : [
                "6185fc94d3145f3fb2dac5cf"
            ]
        }
    }
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductCategoriesMain
{
	"_id": "627cadbed757dbd1ceb87ab9",
	"action_time": 1652338109.170179,
	"activity": "UpdateDeal",
	"body": {
		"add": [{
			"id": "626b50c62d0663bd2efb4707",
			"name": {
				"vi": "Danh m\u1ee5c c\u1ea5p 2"
			}
		}, {
			"id": "6278888ac7048b4e346a69f5",
			"name": {
				"vi": "Danh m\u1ee5c ch\u00ednh"
			}
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T06:48:29.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cadbed757dbd1ceb87ab7",
	"event_type": "AddProductCategoriesMain",
	"id": "627cadbed757dbd1ceb87ab9",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cadbd21e0c966a9a4bdd1"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductCategoriesSub
{
	"_id": "627cadbed757dbd1ceb87abc",
	"action_time": 1652338109.170179,
	"activity": "UpdateDeal",
	"body": {
		"add": [{
			"root_category": {
				"id": "626b50c62d0663bd2efb4707",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			},
			"sub_categories": [{
				"id": "626b51772d0663bd2efb4708",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			}, {
				"id": "626b51772d0663bd2efb4709",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 3"
				}
			}]
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T06:48:29.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cadbed757dbd1ceb87aba",
	"event_type": "AddProductCategoriesSub",
	"id": "627cadbed757dbd1ceb87abc",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cadbd21e0c966a9a4bdd1"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductCategoriesMain
{
	"_id": "627cbbb0d757dbd1ceb87bb3",
	"action_time": 1652341679.769229,
	"activity": "UpdateDeal",
	"body": {
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"remove": [{
			"id": "6278888ac7048b4e346a69f5",
			"name": {
				"vi": "Danh m\u1ee5c ch\u00ednh"
			}
		}],
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T07:47:59.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cbbb0d757dbd1ceb87bb1",
	"event_type": "RemoveProductCategoriesMain",
	"id": "627cbbb0d757dbd1ceb87bb3",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cbbaf1b36ae850ead6167"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductCategoriesMain
{
	"_id": "627cbdbbd757dbd1ceb87bd0",
	"action_time": 1652342202.881001,
	"activity": "UpdateDeal",
	"body": {
		"remove": [{
			"root_category": {
				"id": "626b50c62d0663bd2efb4707",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			},
			"sub_categories": [{
				"id": "626b51772d0663bd2efb4708",
				"name": {
					"vi": "Danh m\u1ee5c c\u1ea5p 2"
				}
			}]
		}],
		"display_type": "tags",
		"field_key": "product_categories",
		"field_name": "product_categories",
		"field_property": 2,
		"group": "other",
		"is_base": true,
		"required": false,
		"translate_key": "i18n_product_category"
	},
	"created_time": "2022-05-12T07:56:43.000Z",
	"deal_id": "627cadbd21e0c966a9a4bdd0",
	"event_id": "627cbdbbd757dbd1ceb87bce",
	"event_type": "RemoveProductCategoriesSub",
	"id": "627cbdbbd757dbd1ceb87bd0",
	"line_event": "merchant",
	"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
	"source": "SALE",
	"source_event": "SALE",
	"staff_update_deal": {
		"email": "<EMAIL>",
		"fullname": "Nguy\u1ec5n V\u0103n An 411",
		"id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
		"phone_number": "+84323456781",
		"username": "admin@pingcomshop"
	},
	"unique_value": "627cbdba9d69765958672e4f"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductType
{
    "_id": "62b139801298bda9228463d1",
    "action_time": 1655781759.375529,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "tags",
        "field_key": "product_types",
        "field_name": "product_types",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "product_line": { // Thông tin dòng sản phẩm
                    "id": "62aa7b0b181e77000e3afe29", 
                    "name": "[Test] Tùng 01"
                },
                "product_types": [
                    {
                        "id": "62aa96843af16c000d1dfaf8",
                        "level": 1, // Cấp loại sản phẩm
                        "name": "a" # Tên loại sản phẩm
                    }
                ]
            }
        ],
        "required": false,
        "translate_key": "i18n_subcategories"
    },
    "created_time": "2022-06-21T03:22:39.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b139801298bda9228463cf",
    "event_type": "RemoveProductType",
    "id": "62b139801298bda9228463d1",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1397f76bf3e9d77331fce"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductLine
{
    "_id": "62b1397f1298bda9228463cd",
    "action_time": 1655781759.375529,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "dropdown_single_line",
        "field_key": "product_line",
        "field_name": "product_line",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "id": "62aa7b0b181e77000e3afe29",
                "name": "[Test] Tùng 01"
            }
        ],
        "required": false,
        "translate_key": "i18n_product_category"
    },
    "created_time": "2022-06-21T03:22:39.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b1397f1298bda9228463cb",
    "event_type": "RemoveProductLine",
    "id": "62b1397f1298bda9228463cd",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1397f76bf3e9d77331fce"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: RemoveProductBank
{
    "_id": "62b138fd1298bda9228463b9",
    "action_time": **********.971797,
    "activity": "UpdateDeal",
    "body": {
        "display_type": "dropdown_single_line",
        "field_key": "products_bank",
        "field_name": "products_bank",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "remove": [
            {
                "id": "62b03b964ef591000fd000a2",
                "name": "sản phẩm 1"
            }
        ],
        "required": false,
        "translate_key": "i18n_product"
    },
    "created_time": "2022-06-21T03:20:29.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b138fd1298bda9228463b7",
    "event_type": "RemoveProductBank",
    "id": "62b138fd1298bda9228463b9",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b138fca918469ec15424c0"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductType
{
    "_id": "62b137771298bda922846383",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "product_line": {
                    "id": "62aa7b0b181e77000e3afe29",
                    "name": "[Test] Tùng 01"
                },
                "product_types": [
                    {
                        "id": "62aa96843af16c000d1dfaf8",
                        "level": 1,
                        "name": "a"
                    }
                ]
            }
        ],
        "display_type": "tags",
        "field_key": "product_types",
        "field_name": "product_types",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_subcategories"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137771298bda922846381",
    "event_type": "AddProductType",
    "id": "62b137771298bda922846383",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductLine
{
    "_id": "62b137761298bda92284637f",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "id": "62aa7b0b181e77000e3afe29",
                "name": "[Test] Tùng 01"
            }
        ],
        "display_type": "dropdown_single_line",
        "field_key": "product_line",
        "field_name": "product_line",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_product_category"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137761298bda92284637d",
    "event_type": "AddProductLine",
    "id": "62b137761298bda92284637f",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: Sale, EventType: UpdateMediaDeal
{
    "_id": "62de6dcbb483efaabc8e5fd8",
    "action_time": 1658744255.607341,
    "activity": "Other",
    "body": {
        "add": [
            {
                "_id": "62de6dbf812bca51bd423890",
                "created_time": "2022-07-25T10:17:35Z",
                "entity_id": "62d1315f788e91d857f24115",
                "entity_type": "detail_deal",
                "id": "62de6dbf812bca51bd423890",
                "info": {
                    "format_file": "image/jpeg",
                    "local_path": "/media/data/public_resources/static/87d9e1f3-6da0-415f-b418-9ba9f4fe5523/Dr-Strange-Wallpapers.jpeg",
                    "title": "Dr-Strange-Wallpapers.jpeg",
                    "type_media": [
                        "identification"
                    ],
                    "url": "https://t1.mobio.vn/static/87d9e1f3-6da0-415f-b418-9ba9f4fe5523/Dr-Strange-Wallpapers.jpeg"
                },
                "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
                "updated_time": "2022-07-25T10:17:35Z"
            }
        ],
        "remove": []
    },
    "deal_id": "62d1315f788e91d857f24115",
    "event_type": "UpdateMediaDeal",
    "id": "62de6dcbb483efaabc8e5fd8",
    "line_event": "merchant",
    "merchant_id": "87d9e1f3-6da0-415f-b418-9ba9f4fe5523",
    "source": "SALE",
    "source_event": "Deal",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin Owner",
        "id": "ce5b23cc-c962-4d02-bbfb-8f3a3eec1737",
        "phone_number": "+***********",
        "username": "admin@msbeb"
    }
}

@apiSuccessExample {json} Response, SourceEvent: Sale: EventType: AddProductBank
{
    "_id": "62b137761298bda92284637b",
    "action_time": **********.885625,
    "activity": "UpdateDeal",
    "body": {
        "add": [
            {
                "id": "62b03b964ef591000fd000a2",
                "name": "sản phẩm 1"
            }
        ],
        "display_type": "dropdown_single_line",
        "field_key": "products_bank",
        "field_name": "products_bank",
        "field_property": 2,
        "group": "other",
        "is_base": true,
        "required": false,
        "translate_key": "i18n_product"
    },
    "created_time": "2022-06-21T03:13:58.000Z",
    "deal_id": "62b136e08d06893560a23096",
    "event_id": "62b137761298bda922846379",
    "event_type": "AddProductBank",
    "id": "62b137761298bda92284637b",
    "line_event": "merchant",
    "merchant_id": "972e6e1d-8891-4fdb-9d02-8a7855393298",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Admin",
        "id": "0f5be325-bd84-420b-9e03-8a7161404233",
        "phone_number": "+***********",
        "username": "admin@msb"
    },
    "unique_value": "62b1377540fc60bf230d0339"
}

@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport
{
    "_id": "648ace0a4c5a1e64615a1557",
    "action_time": **********.324215,
    "action_type": null,
    "activity": "UpdateCallReport",
    "body": [
        {
            "financial_situation_sub": [
                {
                    "add": [],
                    "change": [],
                    "display_type": "nested",
                    "field_key": "financial_situation",
                    "field_name": "Tình hình tài chính",
                    "field_property": 2,
                    "group": "financial_situation_sub",
                    "paths": [
                        {
                            "data": [
                                {
                                    "add": [
                                        2323
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "owners_equity",
                                    "field_name": "Vốn chủ sở hữu (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 5,
                                    "order_list": 28,
                                    "order_preview": 24,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        2323
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "profit_after_tax",
                                    "field_name": "Lợi nhuận sau thuế  (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 6,
                                    "order_list": 29,
                                    "order_preview": 25,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        "3232"
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "roa",
                                    "field_name": "ROA (%)",
                                    "field_property": 2,
                                    "group": "financial_situation_sub",
                                    "order": 9,
                                    "order_list": 32,
                                    "order_preview": 28,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        232323
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "total_assets",
                                    "field_name": "Tổng tài sản (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 4,
                                    "order_list": 27,
                                    "order_preview": 23,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        23232
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "total_debt_at_credit_institutions",
                                    "field_name": "Tổng nợ vay tại các TCTD (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 7,
                                    "order_list": 30,
                                    "order_preview": 26,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        32323
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "total_revenue",
                                    "field_name": "Tổng doanh thu (VNĐ)",
                                    "field_property": 1,
                                    "group": "financial_situation_sub",
                                    "order": 3,
                                    "order_list": 26,
                                    "order_preview": 22,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                },
                                {
                                    "add": [
                                        "3232"
                                    ],
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "single_line",
                                    "field_key": "working_capital_turnover",
                                    "field_name": "Vòng quay VLĐ",
                                    "field_property": 2,
                                    "group": "financial_situation_sub",
                                    "order": 8,
                                    "order_list": 31,
                                    "order_preview": 27,
                                    "paths": [],
                                    "remove": [],
                                    "require": false,
                                    "status": 1
                                }
                            ],
                            "report_year": 2023,
                            "type_report": "TAX_REPORT"
                        }
                    ],
                    "remove": []
                }
            ]
        },
        {
            "credit_organization": [
                {
                    "add": [],
                    "change": [],
                    "display_type": "nested",
                    "field_key": "relationship_between_other_credit_institutions",
                    "field_name": "Tổ chức tín dụng",
                    "field_property": 2,
                    "group": "credit_organization",
                    "paths": [
                        {
                            "data": [
                                {
                                    "disable_form_add": false,
                                    "disable_form_update": false,
                                    "display_in_event": true,
                                    "display_in_form_add": true,
                                    "display_in_form_list": true,
                                    "display_in_form_preview": true,
                                    "display_in_form_update": true,
                                    "display_type": "nested",
                                    "field_key": "credit_information",
                                    "field_name": "Thông tin tín dụng",
                                    "field_property": 2,
                                    "group": "credit_organization",
                                    "order": 2,
                                    "order_list": 36,
                                    "order_preview": 32,
                                    "paths": [
                                        {
                                            "data": [],
                                            "period": "short_term"
                                        }
                                    ],
                                    "require": false,
                                    "status": 1
                                }
                            ],
                            "id": 1,
                            "name_of_credit_institution": null
                        }
                    ],
                    "remove": []
                }
            ]
        }
    ],
    "call_report_id": "648acd3146edb4c03a1cf2f4",
    "created_time": "2023-06-15T08:38:34.000Z",
    "deal_id": "648ac2146aa132e46f818dd6",
    "event_id": "648ace0a4c5a1e64615a1555",
    "event_type": "UpdateCallReport",
    "group_configs": [
        {
            "group_index": 2.1,
            "group_key": "financial_situation_sub",
            "group_name": "Tình hình tài chính"
        },
        {
            "group_index": 3.1,
            "group_key": "credit_organization",
            "group_name": "Tổ chức tín dụng"
        }
    ],
    "id": "648ace0a4c5a1e64615a1557",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Mai Doãn",
        "id": "de97719f-916e-4bcd-900b-94a142e4023e",
        "phone_number": "+***********",
        "username": "Maidt@hdb"
    },
    "unique_value": "648ace09c01cc8fd2e87065e"
}

@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport, action_type:ApprovalBU (Trình ĐVKD duyệt)
{
    "_id": "648acdd54c5a1e64615a1552",
    "action_time": **********.943061,
    "action_type": "ApprovalBU",
    "activity": "UpdateCallReport",
    "body": null,
    "call_report_id": "648acd3146edb4c03a1cf2f4",
    "created_time": "2023-06-15T08:37:41.000Z",
    "data_approval": {
        "accounts_to": [
            {
                "email": "<EMAIL>",
                "fullname": "MAI THỊ ÁI",
                "participants_staff_code": "HD001903",
                "participants_title": "Kiểm soát viên",
                "username": "aimt@hdb"
            }
        ],
        "action": "approval_bu",
        "opinion": null,
        "participants_enterprise_customer_relationship": "Test Mobio",
        "participants_staff_code": "SMT05",
        "participants_title": null
    },
    "deal_id": "648ac2146aa132e46f818dd6",
    "event_id": "648acdd54c5a1e64615a1550",
    "event_type": "ApprovalBU",
    "id": "648acdd54c5a1e64615a1552",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Test Mobio",
        "id": "4e7c97b4-9fb3-49ea-a1f9-a6c0a831413d",
        "phone_number": "+***********",
        "username": "smt05@hdb"
    },
    "unique_value": "648acdce169f2bd4449fbef4"
}

@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport, action_type:UpdateStatusBU (Cập nhật trạng thái duyệt tại ĐVKD)
{
    "_id": "648ace594c5a1e64615a155d",
    "action_time": **********.161219,
    "action_type": "UpdateStatusBU",
    "activity": "UpdateCallReport",
    "body": null,
    "call_report_id": "648acd3146edb4c03a1cf2f4",
    "created_time": "2023-06-15T08:39:53.000Z",
    "data_approval": {
        "accounts_to": [],
        "action": "additional_information_bu",
        "opinion": null,
        "participants_enterprise_customer_relationship": "Mai Doãn",
        "participants_staff_code": "NVMANUAL123",
        "participants_title": null,
        "status_approval": "provide_additional_information"
    },
    "deal_id": "648ac2146aa132e46f818dd6",
    "event_id": "648ace594c5a1e64615a155b",
    "event_type": "UpdateStatusBU",
    "id": "648ace594c5a1e64615a155d",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {},
    "unique_value": "648ace54169f2bd4449fbef6"
}


@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport, action_type:UpdateStatusHO (Cập nhật trạng thái duyệt tại Hội sở )
{
    "_id": "648c3e38bd0862ff25783ff1",
    "action_time": **********.791481,
    "action_type": "UpdateStatusHO",
    "activity": "UpdateCallReport",
    "body": null,
    "call_report_id": "648c32d10da8e13b7cef5eed",
    "created_time": "2023-06-16T10:49:27.000Z",
    "data_approval": {
        "accounts_to": [],
        "action": "approve_ho",
        "opinion": null,
        "participants_enterprise_customer_relationship": "Hoàng Nguyễn Châu Phương Nam",
        "participants_staff_code": "",
        "participants_title": null,
        "status_approval": "approve"
    },
    "deal_id": "648c31681afaffa942747095",
    "event_id": "648c3e37bd0862ff25783fef",
    "event_type": "UpdateStatusHO",
    "id": "648c3e38bd0862ff25783ff1",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {},
    "unique_value": "648c3e3752a953ed4916cb7d"
}
@apiSuccessExample {json} Response, SourceEvent: CALL_REPORT: EventType: UpdateCallReport, action_type:ApprovalHO (Trình hội sở duyệt)
{
    "_id": "648c3dfdbd0862ff25783fec",
    "action_time": **********.346201,
    "action_type": "ApprovalHO",
    "activity": "UpdateCallReport",
    "body": null,
    "call_report_id": "648c32d10da8e13b7cef5eed",
    "created_time": "2023-06-16T10:48:29.000Z",
    "data_approval": {
        "accounts_to": [
            {
                "email": "<EMAIL>",
                "fullname": "Hoàng Nguyễn Châu Phương Nam",
                "participants_staff_code": "",
                "participants_title": null,
                "username": "datpt@hdb"
            }
        ],
        "action": "approval_ho",
        "opinion": null,
        "participants_enterprise_customer_relationship": "Mai Doãn",
        "participants_staff_code": "NVMANUAL123",
        "participants_title": null
    },
    "deal_id": "648c31681afaffa942747095",
    "event_id": "648c3dfdbd0862ff25783fea",
    "event_type": "ApprovalHO",
    "id": "648c3dfdbd0862ff25783fec",
    "line_event": "merchant",
    "merchant_id": "10350f12-1bd7-4369-9750-46d35c416a46",
    "source": "CALL_REPORT",
    "source_event": "CALL_REPORT",
    "staff_update_deal": {
        "email": "<EMAIL>",
        "fullname": "Mai Doãn",
        "id": "de97719f-916e-4bcd-900b-94a142e4023e",
        "phone_number": "+***********",
        "username": "Maidt@hdb"
    },
    "unique_value": "648c3dfb52a953ed4916cb7b"
}

@apiSuccessExample {json} Response, SourceEvent: SaleMemo, action_type=ADD
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "unique_value": "64f8279f967809ee6200497f",
  "object_id": "64d3419ec8cb7026f771380b",
  "object_type": "COMPANY",
  "event_id": "64f827a1f4bc88bb06dfcf6a",
  "source_event": "SaleMemo",
  "event_type": "InitSaleMemo",
  "line_event": "merchant",
  "activity": "SaleMemo",
  "source": "SaleMemo",
  "action_time": 1693984671.909901,
  "action_type": "ADD",
  "sale_memo_id": "64f8279f967809ee6200497e",
  "created_time": "2023-09-06T07:17:53.000Z",
  "staff_update_deal": {
    "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "email": "<EMAIL>",
    "fullname": "Admin pingcomshop",
    "phone_number": "+***********",
    "username": "admin@pingcomshop"
  },
  "group_configs": [
    {
      "group_name": "Nhu c\\u1ea7u c\\u1ee7a kh\\u00e1ch h\\u00e0ng",
      "group_key": "customer_need",
      "group_index": 2,
      "group_parent_key": null
    },
    {
      "group_name": "\\u0110\\u1ed1i th\\u1ee7 c\\u1ea1nh tranh",
      "group_key": "rivals",
      "group_index": 3,
      "group_parent_key": null
    },
    {
      "group_name": "Th\\u00f4ng tin chung",
      "group_key": "information_sub",
      "group_index": 1.1,
      "group_parent_key": "information"
    },
    {
      "group_name": "Th\\u00e0nh ph\\u1ea7n tham gia",
      "group_key": "participating_members",
      "group_index": 1.2,
      "group_parent_key": "participating_members"
    },
    {
      "group_name": "Ph\\u01b0\\u01a1ng \\u00e1n",
      "group_key": "plan",
      "group_index": 4,
      "group_parent_key": null
    }
  ],
  "body": [
    {
      "customer_need": [
        {
          "field_name": "Nhu c\\u1ea7u c\\u1ee7a kh\\u00e1ch h\\u00e0ng",
          "field_key": "customer_needs",
          "display_type": "nested",
          "group": "customer_need",
          "paths": [
            {
              "type": "customer_total_demand",
              "data": [
                {
                  "field_name": "Vay v\\u1ed1n",
                  "field_key": "borrowing",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 12,
                  "order_list": 12,
                  "order_preview": 12,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_borrowing",
                  "merchant_id": "DEFAULT",
                  "add": [
                    32312
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "B\\u1ea3o l\\u00e3nh",
                  "field_key": "surety",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 13,
                  "order_list": 13,
                  "order_preview": 13,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_surety",
                  "merchant_id": "DEFAULT",
                  "add": [
                    32312
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "L/C",
                  "field_key": "letter_of_credit",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 14,
                  "order_list": 14,
                  "order_preview": 14,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_letter_of_credit",
                  "merchant_id": "DEFAULT",
                  "add": [
                    32312
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "Phi t\\u00edn d\\u1ee5ng",
                  "field_key": "non_creditworthy",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 15,
                  "order_list": 15,
                  "order_preview": 15,
                  "character_limit": 500,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 2,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_non_creditworthy",
                  "merchant_id": "DEFAULT",
                  "add": [
                    "32.312"
                  ],
                  "remove": [

                  ]
                }
              ]
            },
            {
              "type": "bank_customer_needs",
              "data": [
                {
                  "field_name": "Vay v\\u1ed1n",
                  "field_key": "borrowing",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 12,
                  "order_list": 12,
                  "order_preview": 12,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_borrowing",
                  "merchant_id": "DEFAULT",
                  "add": [
                    32312
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "B\\u1ea3o l\\u00e3nh",
                  "field_key": "surety",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 13,
                  "order_list": 13,
                  "order_preview": 13,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_surety",
                  "merchant_id": "DEFAULT",
                  "add": [
                    32312
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "L/C",
                  "field_key": "letter_of_credit",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 14,
                  "order_list": 14,
                  "order_preview": 14,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_letter_of_credit",
                  "merchant_id": "DEFAULT",
                  "add": [
                    32312
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "Phi t\\u00edn d\\u1ee5ng",
                  "field_key": "non_creditworthy",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 15,
                  "order_list": 15,
                  "order_preview": 15,
                  "character_limit": 500,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 2,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_non_creditworthy",
                  "merchant_id": "DEFAULT",
                  "add": [
                    "32.312"
                  ],
                  "remove": [

                  ]
                },
                {
                  "field_name": "N\\u1ed9i dung chi ti\\u1ebft",
                  "field_key": "content_details",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 16,
                  "order_list": 16,
                  "order_preview": 16,
                  "is_text_area": true,
                  "character_limit": 2000,
                  "group": "customer_need",
                  "require": false,
                  "field_property": 2,
                  "paths": [

                  ],
                  "status": 1,
                  "view_type_only": "bank_customer_needs",
                  "translate_key": "i18n_content_details",
                  "merchant_id": "DEFAULT",
                  "add": [
                    "32.312"
                  ],
                  "remove": [

                  ]
                }
              ]
            }
          ],
          "field_property": 2,
          "translate_key": "i18n_customer_needs",
          "add": [

          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "rivals": [
        {
          "field_name": "\\u0110\\u1ed1i th\\u1ee7 c\\u1ea1nh tranh c\\u1ee7a MSB",
          "field_key": "rivals",
          "display_type": "nested",
          "group": "rivals",
          "field_property": 2,
          "paths": [
            {
              "id": null,
              "credit_organization_name": "32.312",
              "data": [
                {
                  "field_name": "Th\\u00f4ng tin s\\u1ea3n ph\\u1ea9m",
                  "field_key": "product_info",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_in_event": true,
                  "display_type": "nested",
                  "order": 19,
                  "order_list": 19,
                  "order_preview": 19,
                  "group": "rivals",
                  "require": false,
                  "field_property": 2,
                  "merchant_id": "DEFAULT",
                  "translate_key": "i18n_product_info",
                  "status": 1,
                  "add": [
                    [
                      {
                        "product_name": "32.312",
                        "balance": 32312
                      }
                    ]
                  ],
                  "remove": [

                  ]
                }
              ]
            }
          ],
          "translate_key": "i18n_rivals",
          "add": [
            {
              "product_info": [
                {
                  "product_name": "32.312",
                  "balance": 32312
                }
              ],
              "credit_organization_name": "32.312"
            }
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "\\u0110\\u1ed1i th\\u1ee7 c\\u1ea1nh tranh c\\u1ee7a MSB",
          "field_key": "rivals",
          "display_type": "nested",
          "group": "rivals",
          "field_property": 2,
          "paths": [
            {
              "id": null,
              "credit_organization_name": "32.312",
              "data": [
                {
                  "field_name": "Th\\u00f4ng tin s\\u1ea3n ph\\u1ea9m",
                  "field_key": "product_info",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_in_event": true,
                  "display_type": "nested",
                  "order": 19,
                  "order_list": 19,
                  "order_preview": 19,
                  "group": "rivals",
                  "require": false,
                  "field_property": 2,
                  "merchant_id": "DEFAULT",
                  "translate_key": "i18n_product_info",
                  "status": 1,
                  "add": [
                    [
                      {
                        "product_name": "32.312",
                        "balance": 32312
                      }
                    ]
                  ],
                  "remove": [

                  ]
                }
              ]
            }
          ],
          "translate_key": "i18n_rivals",
          "add": [
            {
              "product_info": [
                {
                  "product_name": "32.312",
                  "balance": 32312
                }
              ],
              "credit_organization_name": "32.312"
            }
          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "information_sub": [
        {
          "field_name": "K\\u1ebft qu\\u1ea3 meeting",
          "field_key": "meeting_result",
          "display_type": "dropdown_single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_meeting_result",
          "add": [
            "customer_acceptance"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Profiles",
          "field_key": "profile",
          "display_type": "tags",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_profile",
          "add": [
            "2d9703f9-3b7d-4f3e-9366-714360a9021c"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Ng\\u01b0\\u1eddi nh\\u1eadn th\\u00f4ng tin",
          "field_key": "recipients",
          "display_type": "tags",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_recipients",
          "add": [
            "<EMAIL>",
            "nam"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Kinh nghi\\u1ec7m SXKD",
          "field_key": "business_production_experience",
          "display_type": "single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_business_production_experience",
          "add": [
            "qweqwe"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "M\\u1ed1i quan h\\u1ec7 trong ng\\u00e0nh",
          "field_key": "industry_relationships",
          "display_type": "single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_industry_relationships",
          "add": [
            "qweqwe"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "T\\u00ecnh h\\u00ecnh t\\u00e0i ch\\u00ednh",
          "field_key": "financial_condition",
          "display_type": "single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_financial_situation",
          "add": [
            "qweqweq"
          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "participating_members": [
        {
          "field_name": "Tham d\\u1ef1 ph\\u00eda kh\\u00e1ch h\\u00e0ng",
          "field_key": "customer_participants",
          "display_type": "multi_line",
          "group": "participating_members",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_customer_participants",
          "add": [
            "weqweq"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Tham d\\u1ef1 ph\\u00eda MSB",
          "field_key": "bank_participants",
          "display_type": "multi_line",
          "group": "participating_members",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_bank_participants",
          "add": [
            "qweqwe"
          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "plan": [
        {
          "field_name": "Ph\\u01b0\\u01a1ng \\u00e1n khai th\\u00e1c kh\\u00e1ch h\\u00e0ng",
          "field_key": "customer_exploitation_plan",
          "display_type": "single_line",
          "group": "plan",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_customer_exploitation_plan",
          "add": [
            "32.312"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Ph\\u01b0\\u01a1ng \\u00e1n vay v\\u1ed1n c\\u1ee7a kh\\u00e1ch h\\u00e0ng",
          "field_key": "customer_loan_plan",
          "display_type": "single_line",
          "group": "plan",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_customer_loan_plan",
          "add": [
            "32.312"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Ph\\u01b0\\u01a1ng \\u00e1n t\\u00e0i s\\u1ea3n b\\u1ea3o \\u0111\\u1ea3m",
          "field_key": "asset_collateral_plan",
          "display_type": "single_line",
          "group": "plan",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_asset_collateral_plan",
          "add": [
            "32.312"
          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    }
  ]
}

@apiSuccessExample {json} Response, SourceEvent: SaleMemo, action_type=UPDATE
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "unique_value": "64f82919f92657402f6faa5a",
  "object_id": "64d3419ec8cb7026f771380b",
  "object_type": "COMPANY",
  "event_id": "64f82ddc7b4962c9a8bb1adc",
  "source_event": "SaleMemo",
  "event_type": "UpdateSaleMemo",
  "line_event": "merchant",
  "activity": "SaleMemo",
  "source": "SaleMemo",
  "action_time": 1693985049.972594,
  "action_type": "UPDATE",
  "sale_memo_id": "64f8279f967809ee6200497e",
  "created_time": "2023-09-06T07:44:28.000Z",
  "staff_update_deal": {
    "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "email": "<EMAIL>",
    "fullname": "Admin pingcomshop",
    "phone_number": "+***********",
    "username": "admin@pingcomshop"
  },
  "group_configs": [
    {
      "group_name": "Nhu c\\u1ea7u c\\u1ee7a kh\\u00e1ch h\\u00e0ng",
      "group_key": "customer_need",
      "group_index": 2,
      "group_parent_key": null
    },
    {
      "group_name": "\\u0110\\u1ed1i th\\u1ee7 c\\u1ea1nh tranh",
      "group_key": "rivals",
      "group_index": 3,
      "group_parent_key": null
    },
    {
      "group_name": "Th\\u00f4ng tin chung",
      "group_key": "information_sub",
      "group_index": 1.1,
      "group_parent_key": "information"
    },
    {
      "group_name": "Th\\u00e0nh ph\\u1ea7n tham gia",
      "group_key": "participating_members",
      "group_index": 1.2,
      "group_parent_key": "participating_members"
    }
  ],
  "body": [
    {
      "customer_need": [
        {
          "field_name": "Nhu c\\u1ea7u c\\u1ee7a kh\\u00e1ch h\\u00e0ng",
          "field_key": "customer_needs",
          "display_type": "nested",
          "group": "customer_need",
          "paths": [
            {
              "type": "customer_total_demand",
              "data": [
                {
                  "field_name": "Vay v\\u1ed1n",
                  "field_key": "borrowing",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 12,
                  "order_list": 12,
                  "order_preview": 12,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_borrowing",
                  "merchant_id": "DEFAULT",
                  "add": [
                    54
                  ],
                  "remove": [
                    32312
                  ]
                },
                {
                  "field_name": "B\\u1ea3o l\\u00e3nh",
                  "field_key": "surety",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 13,
                  "order_list": 13,
                  "order_preview": 13,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_surety",
                  "merchant_id": "DEFAULT",
                  "add": [
                    54
                  ],
                  "remove": [
                    32312
                  ]
                },
                {
                  "field_name": "L/C",
                  "field_key": "letter_of_credit",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 14,
                  "order_list": 14,
                  "order_preview": 14,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_letter_of_credit",
                  "merchant_id": "DEFAULT",
                  "add": [
                    34345
                  ],
                  "remove": [
                    32312
                  ]
                }
              ]
            },
            {
              "type": "bank_customer_needs",
              "data": [
                {
                  "field_name": "Vay v\\u1ed1n",
                  "field_key": "borrowing",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 12,
                  "order_list": 12,
                  "order_preview": 12,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_borrowing",
                  "merchant_id": "DEFAULT",
                  "add": [
                    23
                  ],
                  "remove": [
                    32312
                  ]
                },
                {
                  "field_name": "B\\u1ea3o l\\u00e3nh",
                  "field_key": "surety",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 13,
                  "order_list": 13,
                  "order_preview": 13,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_surety",
                  "merchant_id": "DEFAULT",
                  "add": [
                    665
                  ],
                  "remove": [
                    32312
                  ]
                },
                {
                  "field_name": "L/C",
                  "field_key": "letter_of_credit",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "display_in_event": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_type": "single_line",
                  "order": 14,
                  "order_list": 14,
                  "order_preview": 14,
                  "group": "customer_need",
                  "require": true,
                  "field_property": 1,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_letter_of_credit",
                  "merchant_id": "DEFAULT",
                  "add": [
                    46
                  ],
                  "remove": [
                    32312
                  ]
                }
              ]
            }
          ],
          "field_property": 2,
          "translate_key": "i18n_customer_needs",
          "add": [

          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "rivals": [
        {
          "field_name": "\\u0110\\u1ed1i th\\u1ee7 c\\u1ea1nh tranh c\\u1ee7a MSB",
          "field_key": "rivals",
          "display_type": "nested",
          "group": "rivals",
          "field_property": 2,
          "paths": [
            {
              "id": 1,
              "credit_organization_name": "32.312",
              "data": [
                {
                  "field_name": "Th\\u00f4ng tin s\\u1ea3n ph\\u1ea9m",
                  "field_key": "product_info",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_in_event": true,
                  "display_type": "nested",
                  "order": 19,
                  "order_list": 19,
                  "order_preview": 19,
                  "group": "rivals",
                  "require": false,
                  "field_property": 2,
                  "merchant_id": "DEFAULT",
                  "translate_key": "i18n_product_info",
                  "status": 1,
                  "paths": [
                    {
                      "id": null,
                      "data": [
                        {
                          "field_name": "T\\u00ean s\\u1ea3n ph\\u1ea9m",
                          "field_key": "product_name",
                          "display_in_form_list": true,
                          "display_in_form_add": true,
                          "display_in_form_update": true,
                          "display_in_form_preview": true,
                          "disable_form_add": false,
                          "disable_form_update": false,
                          "display_in_event": true,
                          "is_border": true,
                          "display_type": "single_line",
                          "order": 20,
                          "order_list": 20,
                          "order_preview": 20,
                          "group": "rivals",
                          "character_limit": 128,
                          "require": true,
                          "field_property": 2,
                          "paths": [

                          ],
                          "status": 1,
                          "translate_key": "i18n_product_name",
                          "merchant_id": "DEFAULT",
                          "add": [

                          ],
                          "remove": [
                            "32.312"
                          ]
                        },
                        {
                          "field_name": "Nh\\u1eadp s\\u1ed1 d\\u01b0",
                          "field_key": "balance",
                          "display_in_form_list": true,
                          "display_in_form_add": true,
                          "display_in_form_update": true,
                          "display_in_form_preview": true,
                          "disable_form_add": false,
                          "disable_form_update": false,
                          "display_in_event": true,
                          "is_border": true,
                          "display_type": "single_line",
                          "order": 21,
                          "order_list": 21,
                          "order_preview": 21,
                          "group": "rivals",
                          "require": true,
                          "field_property": 1,
                          "paths": [

                          ],
                          "status": 1,
                          "translate_key": "i18n_balance",
                          "merchant_id": "DEFAULT",
                          "add": [

                          ],
                          "remove": [
                            32312
                          ]
                        }
                      ]
                    }
                  ]
                },
                {
                  "field_name": "T\\u00ean t\\u1ed5 ch\\u1ee9c t\\u00edn d\\u1ee5ng",
                  "field_key": "credit_organization_name",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_in_event": true,
                  "is_border": true,
                  "display_type": "single_line",
                  "order": 18,
                  "order_list": 18,
                  "order_preview": 18,
                  "group": "rivals",
                  "require": true,
                  "field_property": 2,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_credit_organization_name",
                  "merchant_id": "DEFAULT",
                  "add": [

                  ],
                  "remove": [
                    "32.312"
                  ]
                }
              ]
            },
            {
              "id": 2,
              "credit_organization_name": "32.312",
              "data": [
                {
                  "field_name": "Th\\u00f4ng tin s\\u1ea3n ph\\u1ea9m",
                  "field_key": "product_info",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_in_event": true,
                  "display_type": "nested",
                  "order": 19,
                  "order_list": 19,
                  "order_preview": 19,
                  "group": "rivals",
                  "require": false,
                  "field_property": 2,
                  "merchant_id": "DEFAULT",
                  "translate_key": "i18n_product_info",
                  "status": 1,
                  "paths": [
                    {
                      "id": 1,
                      "data": [
                        {
                          "field_name": "T\\u00ean s\\u1ea3n ph\\u1ea9m",
                          "field_key": "product_name",
                          "display_in_form_list": true,
                          "display_in_form_add": true,
                          "display_in_form_update": true,
                          "display_in_form_preview": true,
                          "disable_form_add": false,
                          "disable_form_update": false,
                          "display_in_event": true,
                          "is_border": true,
                          "display_type": "single_line",
                          "order": 20,
                          "order_list": 20,
                          "order_preview": 20,
                          "group": "rivals",
                          "character_limit": 128,
                          "require": true,
                          "field_property": 2,
                          "paths": [

                          ],
                          "status": 1,
                          "translate_key": "i18n_product_name",
                          "merchant_id": "DEFAULT",
                          "add": [
                            "32.312"
                          ],
                          "remove": [

                          ]
                        },
                        {
                          "field_name": "Nh\\u1eadp s\\u1ed1 d\\u01b0",
                          "field_key": "balance",
                          "display_in_form_list": true,
                          "display_in_form_add": true,
                          "display_in_form_update": true,
                          "display_in_form_preview": true,
                          "disable_form_add": false,
                          "disable_form_update": false,
                          "display_in_event": true,
                          "is_border": true,
                          "display_type": "single_line",
                          "order": 21,
                          "order_list": 21,
                          "order_preview": 21,
                          "group": "rivals",
                          "require": true,
                          "field_property": 1,
                          "paths": [

                          ],
                          "status": 1,
                          "translate_key": "i18n_balance",
                          "merchant_id": "DEFAULT",
                          "add": [
                            32312
                          ],
                          "remove": [

                          ]
                        }
                      ]
                    },
                    {
                      "id": 2,
                      "data": [
                        {
                          "field_name": "T\\u00ean s\\u1ea3n ph\\u1ea9m",
                          "field_key": "product_name",
                          "display_in_form_list": true,
                          "display_in_form_add": true,
                          "display_in_form_update": true,
                          "display_in_form_preview": true,
                          "disable_form_add": false,
                          "disable_form_update": false,
                          "display_in_event": true,
                          "is_border": true,
                          "display_type": "single_line",
                          "order": 20,
                          "order_list": 20,
                          "order_preview": 20,
                          "group": "rivals",
                          "character_limit": 128,
                          "require": true,
                          "field_property": 2,
                          "paths": [

                          ],
                          "status": 1,
                          "translate_key": "i18n_product_name",
                          "merchant_id": "DEFAULT",
                          "add": [
                            "San pham 2"
                          ],
                          "remove": [

                          ]
                        },
                        {
                          "field_name": "Nh\\u1eadp s\\u1ed1 d\\u01b0",
                          "field_key": "balance",
                          "display_in_form_list": true,
                          "display_in_form_add": true,
                          "display_in_form_update": true,
                          "display_in_form_preview": true,
                          "disable_form_add": false,
                          "disable_form_update": false,
                          "display_in_event": true,
                          "is_border": true,
                          "display_type": "single_line",
                          "order": 21,
                          "order_list": 21,
                          "order_preview": 21,
                          "group": "rivals",
                          "require": true,
                          "field_property": 1,
                          "paths": [

                          ],
                          "status": 1,
                          "translate_key": "i18n_balance",
                          "merchant_id": "DEFAULT",
                          "add": [
                            32312
                          ],
                          "remove": [

                          ]
                        }
                      ]
                    }
                  ]
                },
                {
                  "field_name": "T\\u00ean t\\u1ed5 ch\\u1ee9c t\\u00edn d\\u1ee5ng",
                  "field_key": "credit_organization_name",
                  "display_in_form_list": true,
                  "display_in_form_add": true,
                  "display_in_form_update": true,
                  "display_in_form_preview": true,
                  "disable_form_add": false,
                  "disable_form_update": false,
                  "display_in_event": true,
                  "is_border": true,
                  "display_type": "single_line",
                  "order": 18,
                  "order_list": 18,
                  "order_preview": 18,
                  "group": "rivals",
                  "require": true,
                  "field_property": 2,
                  "paths": [

                  ],
                  "status": 1,
                  "translate_key": "i18n_credit_organization_name",
                  "merchant_id": "DEFAULT",
                  "add": [
                    "32.312"
                  ],
                  "remove": [

                  ]
                }
              ]
            }
          ],
          "translate_key": "i18n_rivals",
          "add": [

          ],
          "change": [

          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "information_sub": [
        {
          "field_name": "Th\\u1eddi gian g\\u1eb7p",
          "field_key": "meeting_time",
          "display_type": "date_picker",
          "format": "dd/mm/yyyy hh:mm",
          "group": "information_sub",
          "paths": [

          ],
          "translate_key": "i18n_time_meet",
          "add": [
            "2023-09-06T04:15:00.000Z"
          ],
          "change": [

          ],
          "remove": [

          ]
        },
        {
          "field_name": "Ng\\u01b0\\u1eddi nh\\u1eadn th\\u00f4ng tin",
          "field_key": "recipients",
          "display_type": "tags",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_recipients",
          "add": [
            "<EMAIL>"
          ],
          "change": [

          ],
          "remove": [
            "nam"
          ]
        },
        {
          "field_name": "Kinh nghi\\u1ec7m SXKD",
          "field_key": "business_production_experience",
          "display_type": "single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_business_production_experience",
          "add": [

          ],
          "change": [
            {
              "from": "qweqwe",
              "to": "ds"
            }
          ],
          "remove": [

          ]
        },
        {
          "field_name": "M\\u1ed1i quan h\\u1ec7 trong ng\\u00e0nh",
          "field_key": "industry_relationships",
          "display_type": "single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_industry_relationships",
          "add": [

          ],
          "change": [
            {
              "from": "qweqwe",
              "to": "ds"
            }
          ],
          "remove": [

          ]
        },
        {
          "field_name": "T\\u00ecnh h\\u00ecnh t\\u00e0i ch\\u00ednh",
          "field_key": "financial_condition",
          "display_type": "single_line",
          "group": "information_sub",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_financial_situation",
          "add": [

          ],
          "change": [
            {
              "from": "qweqweq",
              "to": "d"
            }
          ],
          "remove": [

          ]
        }
      ]
    },
    {
      "participating_members": [
        {
          "field_name": "Tham d\\u1ef1 ph\\u00eda kh\\u00e1ch h\\u00e0ng",
          "field_key": "customer_participants",
          "display_type": "multi_line",
          "group": "participating_members",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_customer_participants",
          "add": [
            "fd"
          ],
          "change": [

          ],
          "remove": [
            "weqweq"
          ]
        },
        {
          "field_name": "Tham d\\u1ef1 ph\\u00eda MSB",
          "field_key": "bank_participants",
          "display_type": "multi_line",
          "group": "participating_members",
          "field_property": 2,
          "paths": [

          ],
          "translate_key": "i18n_bank_participants",
          "add": [
            "fd"
          ],
          "change": [

          ],
          "remove": [
            "qweqwe"
          ]
        }
      ]
    }
  ]
}

@apiSuccessExample {json} Response, SourceEvent: SaleMemo, action_type=REMOVE
{
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "unique_value": "64f831792f5b2d41f415ed6c",
  "object_id": "64d3419ec8cb7026f771380b",
  "object_type": "COMPANY",
  "event_id": "64f83179f4bc88bb06dfcf6f",
  "source_event": "SaleMemo",
  "event_type": "RemoveSaleMemo",
  "line_event": "merchant",
  "activity": "SaleMemo",
  "source": "SaleMemo",
  "action_time": **********.080557,
  "action_type": "REMOVE",
  "sale_memo_id": "64f82b05967809ee62004981",
  "created_time": "2023-09-06T07:59:53.000Z",
  "meeting_result": ""
  "staff_update_deal": {
    "id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
    "email": "<EMAIL>",
    "fullname": "Admin pingcomshop",
    "phone_number": "+***********",
    "username": "admin@pingcomshop"
  },
  "body": null
}

@apiSuccessExample {json} Response, SourceEvent: Sale, event_type=SlaOnTime (Đúng hạn SLA)
{
    "_id": "66b8c010c6df2b0e1eade50b",
    "action_time": 1723383450.660481,
    "activity": "DealSla",
    "body": {
        "sale_process_info": { // Thông tin quy trình bán hàng
            "id": "66b318b58a2bc72c3d566f0e",
            "name": "Quy tr\u00ecnh c\u1ee7a linh ( k s\u1eeda x\u00f3a v\u00e0 s\u1eed d\u1ee5ng)"
        },
        "state": { // trạng thái quy trình bán 
            "name": "a1",
            "ratio": 5,
            "state_code": "0ZLHWYVS"
        },
        "sla_config": {
            "target_type": "sales_opportunity_lifecycle" // với: sales_opportunity_lifecycle = áp dụng toàn vòng đời, policy_sales_opportunity_status = Áp dụng chính sách theo từng trạng thái của Cơ hội bán
        }
    },
    "deal_id": "66b8bde4563f1f459b3472bd",
    "event_id": "66b8bfdcc6df2b0e1eade508",
    "event_type": "SlaOnTime", // Đúng hạn SLA
    "id": "66b8c010c6df2b0e1eade50b",
    "line_event": "merchant",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {}
}

@apiSuccessExample {json} Response, SourceEvent: Sale, event_type=SlaOverdue (Quá hạn SLA)
{
    "_id": "66b8c010c6df2b0e1eade50b",
    "action_time": 1723383450.660481,
    "activity": "DealSla",
    "body": {
        "sale_process_info": { // Thông tin quy trình bán hàng
            "id": "66b318b58a2bc72c3d566f0e",
            "name": "Quy tr\u00ecnh c\u1ee7a linh ( k s\u1eeda x\u00f3a v\u00e0 s\u1eed d\u1ee5ng)"
        },
        "state": { // trạng thái quy trình bán 
            "name": "a1",
            "ratio": 5,
            "state_code": "0ZLHWYVS"
        },
        "sla_config": {
            "target_type": "sales_opportunity_lifecycle" // với: sales_opportunity_lifecycle = áp dụng toàn vòng đời, policy_sales_opportunity_status = Áp dụng chính sách theo từng trạng thái của Cơ hội bán
        }
    },
    "deal_id": "66b8bde4563f1f459b3472bd",
    "event_id": "66b8bfdcc6df2b0e1eade508",
    "event_type": "SlaOverdue", // Quá hạn SLA
    "id": "66b8c010c6df2b0e1eade50b",
    "line_event": "merchant",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "source": "SALE",
    "source_event": "SALE",
    "staff_update_deal": {}
}
"""
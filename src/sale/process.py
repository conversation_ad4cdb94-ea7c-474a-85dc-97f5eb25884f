#!/usr/bin/python
# -*- coding: utf8 -*-
"""
Author: ThinhPC
Company: MobioVN
Created Date: 28/11/2019
"""

#########################################################################################
# DANH SÁCH STATE THEO QUY TRÌNH BÁN HÀNG												#
# version: 1.0.1                                                                        #
#########################################################################################


"""
@api {GET} {domain}/sale/api/v1.0/sale_processes/<sale_process_id>/states  Danh sách state theo quy trình bán hàng
@apiDescription Danh sách state theo quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName StateList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess {String}            sale_process_id				Id của quy trình bán hàng
@apiSuccess {String}            code						Mã state
@apiSuccess {String}            name                       	Tên state
@apiSuccess {Integer}           ratio                      	Phần trăm thành công của state
@apiSuccess {Integer}           status                     	Trạng thái của state
                                                            <code> -USED: 1, UNUSED: 0, DELETED:-1
                                                            </code>

@apiSuccessExample {json} Response
{
    "data": {
        "sale_process_id":"5d7f150be6481e870a8ce0ad",
        "states":[
            {
                "state_code":"LEAD",
                "name":"Có thông tin lead",
                "ratio":10,
            },
            {
                "state_code":"FINISH",
                "name":"Thành công",
                "ratio":100,
            },
            {
                "state_code":"FAIL",
                "name":"Thất bại",
                "ratio":0,
            }
        ]
    },
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# XÓA STATE
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {DELETE} {domain}/sale/api/v1.0/sale_processes/<sale_process_id>/states  Xóa state
@apiDescription Xóa state
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName DeleteState

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header

@apiParam   (Query :)         {String}       state_codes             Danh sách state_code cần xóa của quy trình
                                                                    <li><code>state_codes:</code>uuid1,uuid2,...</li>
@apiParam   (Query :)         {String}       merchant_id             Merchant ID của Quy trình bán hàng
@apiParam   (Query :)         {String}      [next_state_code]        UUID của trạng thái mà các đơn hàng đang thuộc trạng thái hiện tại sẽ chuyển sang trước khi xoá trạng thái hiện tại

@apiSuccess {Array}             data                          Chi tiết kết quả các hành động vừa thực hiện
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response
{

    "data": {
        "sale_process_id": "45042df5-2202-4964-b05f-d53e21f5f896",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "states": [
            {
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f896",
                "delete_result": "success" //success: xoá thành công, fail: xoá thất bại
            },
            {
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f869",
                "delete_result": "fail" //success: xoá thành công, fail: xoá thất bại
            }
        ],
        "next_state_code": "1b99bdcf-d582-4f49-9715-1b61dfff3925" //Trả về nếu chuyển trạng thái thành công, nếu không thành công trả về string rỗng
    },
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# SET MẶC ĐỊNH QUY TRÌNH BÁN HÀNG
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/sale_processes/<sale_process_id>/set_default  Set mặc định quy trình bán hàng
@apiDescription Set mặc định quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName ProcessDefault

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParamExample {json} Body example
{
    "name": "Tên quy trình bán hàng số 01",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", // merchant_id của quy trình
    "is_default": 1 // 1: mặc định, 0: không mặc định
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "_id": "5dde29fda2596203036b12c5",
        "code": "QTBHSO0001"
        "name": "Tên quy trình bán hàng số 01",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
        "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop"
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "states": [
            {
                "name": "Có thông tin Leads",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                "ratio": 10
            },
            {
                "name": "Liên lạc",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                "ratio": 30
            },
            {
                "name": "Giới thiệu",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f882",
                "ratio": 40
            },
            {
                "name": "Gửi báo giá",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f883",
                "ratio": 50
            },
            {
                "name": "Đàm phán",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                "ratio": 70
            },
            {
                "name": "Thành công",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                "ratio": 100
            },
            {
                "name": "Thất bại",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                "ratio": 0
            }
        ]
    }
}
"""

####################################################################################################
# THÊM MỚI QUY TRÌNH BÁN HÀNG
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/sale_processes Thêm mới quy trình bán hàng
@apiDescription Thêm mới quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName SaleProcessAdd

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)         {String}    name                  Tên quy trình bán hàng
@apiParam   (Body:)         {String}    merchant_id              UUID thương hiệu mà quy trình đó thuộc về.
@apiParam   (Body:)         {int}       is_default            Chọn xem đây có phải là quy trình bán hàng mặc định hay không.
                                                              <li><code>0:</code>Không phải là quy trình bán hàng mặc định</li>
                                                              <li><code>1:</code>Là quy trình bán hàng mặc định</li>
@apiParam   (Body:)         {String}    [description]         Mô tả về quy trình bán hàng
@apiParam   (Body:)         {Array}     [products]            Danh sách các UUID các sản phẩm của đơn hàng.
@apiParam   (Body:)         {ArrayObject}  states             Các bước của quy trình bán hàng.

@apiParamExample {json} Body example
{
    "name": "Tên quy trình bán hàng số 01",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
    "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop"
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894"
    ],
    "states": [
        {
            "name": "Có thông tin Leads",
            "ratio": 10
        },
        {
            "name": "Liên lạc",
            "ratio": 30
        },
        {
            "name": "Giới thiệu",
            "ratio": 40
        },
        {
            "name": "Gửi báo giá",
            "ratio": 50
        },
        {
            "name": "Đàm phán",
            "ratio": 70
        },
        {
            "name": "Thành công",
            "ratio": 100
        },
        {
            "name": "Thất bại",
            "ratio": 0
        }
    ]
}

@apiSuccess {Array}             data                          Thông tin quy trình bán hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": {
        "_id": "5dde29fda2596203036b12c5",
        "code": "QTBHSO0001"
        "name": "Tên quy trình bán hàng số 01",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
        "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop"
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "states": [
            {
                "name": "Có thông tin Leads",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                "ratio": 10
            },
            {
                "name": "Liên lạc",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                "ratio": 30
            },
            {
                "name": "Giới thiệu",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f882",
                "ratio": 40
            },
            {
                "name": "Gửi báo giá",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f883",
                "ratio": 50
            },
            {
                "name": "Đàm phán",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                "ratio": 70
            },
            {
                "name": "Thành công",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                "ratio": 100
            },
            {
                "name": "Thất bại",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                "ratio": 0
            }
        ]
    },
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# THÊM MỚI QUY TRÌNH BÁN HÀNG
# version: 1.0.2                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/sale_processes Thêm mới quy trình bán hàng
@apiDescription Thêm mới quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.2
@apiName SaleProcessAdd

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)         {String}    name                  Tên quy trình bán hàng
@apiParam   (Body:)         {String}    merchant_id              UUID thương hiệu mà quy trình đó thuộc về.
@apiParam   (Body:)         {int}       is_default            Chọn xem đây có phải là quy trình bán hàng mặc định hay không.
                                                              <li><code>0:</code>Không phải là quy trình bán hàng mặc định</li>
                                                              <li><code>1:</code>Là quy trình bán hàng mặc định</li>
@apiParam   (Body:)         {String}    [description]         Mô tả về quy trình bán hàng
@apiParam   (Body:)         {Array}     [products]            Danh sách các UUID các sản phẩm của đơn hàng.
@apiParam   (Body:)         {ArrayObject}  states             Các bước của quy trình bán hàng.
@apiParam   (Body:)         {Object}    [config_product_tree]     Cấu hình Dòng sản phẩm gắn với quy trình bán hàng
@apiParam   (Body:)         {String}    config_product_tree.group_customer   Nhóm khách hàng
@apiParam   (Body:)         {Array}     config_product_tree.product_lines    Danh sách dòng sản phẩm
@apiParam   (Body:)         {Boolean}    config_product_tree.status    Trạng thái cấu hình </br>
                                                                      (<code>true</code> Bật cấu hình)</br>
                                                                      (<code>false</code> Tắt cấu hình)</br>

@apiParamExample {json} Body example
{
    "name": "Tên quy trình bán hàng số 01",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
    "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop"
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894"
    ],
    "states": [
        {
            "name": "Có thông tin Leads",
            "ratio": 10
        },
        {
            "name": "Liên lạc",
            "ratio": 30
        },
        {
            "name": "Giới thiệu",
            "ratio": 40
        },
        {
            "name": "Gửi báo giá",
            "ratio": 50
        },
        {
            "name": "Đàm phán",
            "ratio": 70
        },
        {
            "name": "Thành công",
            "ratio": 100
        },
        {
            "name": "Thất bại",
            "ratio": 0
        }
    ],
    "config_product_tree": {
        "group_customer": "KHDN",
        "product_lines": [
            "6492708349194100103ffd7f",
            "64673fd960587a0010b0c25f"
        ],
        "status": true
    }
}

@apiSuccess {Array}             data                          Thông tin quy trình bán hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": {
        "_id": "5dde29fda2596203036b12c5",
        "code": "QTBHSO0001"
        "name": "Tên quy trình bán hàng số 01",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
        "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop"
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "states": [
            {
                "name": "Có thông tin Leads",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                "ratio": 10
            },
            {
                "name": "Liên lạc",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                "ratio": 30
            },
            {
                "name": "Giới thiệu",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f882",
                "ratio": 40
            },
            {
                "name": "Gửi báo giá",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f883",
                "ratio": 50
            },
            {
                "name": "Đàm phán",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                "ratio": 70
            },
            {
                "name": "Thành công",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                "ratio": 100
            },
            {
                "name": "Thất bại",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                "ratio": 0
            }
        ],
        "config_product_tree": {
            "group_customer": "KHDN",
            "product_lines": [
                "6492708349194100103ffd7f",
                "64673fd960587a0010b0c25f"
            ],
            "status": true
        }
    },
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# SỬA QUY TRÌNH BÁN HÀNG
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {PUT} {domain}/sale/api/v1.0/sale_processes/<sale_process_id> Sửa một quy trình bán hàng
@apiDescription Sửa một quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName SaleProcessUpdate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)         {String}    name                  Tên quy trình bán hàng
@apiParam   (Body:)         {String}    merchant_id           UUID thương hiệu mà quy trình đó thuộc về.
@apiParam   (Body:)         {int}       is_default            Chọn xem đây có phải là quy trình bán hàng mặc định hay không.
                                                              <li><code>0:</code>Không phải là quy trình bán hàng mặc định</li>
                                                              <li><code>1:</code>Là quy trình bán hàng mặc định</li>
@apiParam   (Body:)         {String}    [description]         Mô tả về quy trình bán hàng
@apiParam   (Body:)         {Array}     [products]            Danh sách các UUID các sản phẩm của đơn hàng.
@apiParam   (Body:)         {ArrayObject}  states             Các bước của quy trình bán hàng.

@apiParamExample {json} Body example
{
    "name": "Tên quy trình bán hàng số 02",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
    "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop"
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894",
        "45042df5-2202-4964-b05f-d53e21f5f895",
    ],
    "states": [
        {
            "name": "Có thông tin Leads",
            "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
            "ratio": 10
        },
        {
            "name": "Liên lạc",
            "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
            "ratio": 30
        },
        {
            "name": "Đàm phán",
            "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
            "ratio": 70
        },
        {
            "name": "Đàm phán", //Trường hợp gửi lên không có "code" được tính là tạo mới trạng thái của quy trình
            "ratio": 80
        },
        {
            "name": "Đàm phán", //Trường hợp gửi lên không có "code" được tính là tạo mới trạng thái của quy trình
            "ratio": 90
        },
        {
            "name": "Thành công",
            "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
            "ratio": 100
        },
        {
            "name": "Thất bại",
            "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
            "ratio": 0
        }
    ]
}

@apiSuccess {Array}             data                          Thông tin quy trình bán hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": {
        "_id": "5dde29fda2596203036b12c4",
        "code": "QTBHSO0001"
        "name": "Tên quy trình bán hàng số 02",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
        "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop"
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "states": [
            {
                "name": "Có thông tin Leads",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                "ratio": 10
            },
            {
                "name": "Liên lạc",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                "ratio": 30
            },
            {
                "name": "Đàm phán",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                "ratio": 70
            },
            {
                "name": "Đàm phán",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f898", // Code này mới được tạo sau khi update
                "ratio": 80
            },
            {
                "name": "Đàm phán",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f899", // Code này mới được tạo sau khi update
                "ratio": 90
            },
            {
                "name": "Thành công",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                "ratio": 100
            },
            {
                "name": "Thất bại",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                "ratio": 0
            }
        ]
    },
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# SỬA QUY TRÌNH BÁN HÀNG
# version: 1.0.2                                                                                   #
####################################################################################################

"""
@api {PUT} {domain}/sale/api/v1.0/sale_processes/<sale_process_id> Sửa một quy trình bán hàng
@apiDescription Sửa một quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.2
@apiName SaleProcessUpdate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam   (Body:)         {String}    name                  Tên quy trình bán hàng
@apiParam   (Body:)         {String}    merchant_id           UUID thương hiệu mà quy trình đó thuộc về.
@apiParam   (Body:)         {int}       is_default            Chọn xem đây có phải là quy trình bán hàng mặc định hay không.
                                                              <li><code>0:</code>Không phải là quy trình bán hàng mặc định</li>
                                                              <li><code>1:</code>Là quy trình bán hàng mặc định</li>
@apiParam   (Body:)         {String}    [description]         Mô tả về quy trình bán hàng
@apiParam   (Body:)         {Array}     [products]            Danh sách các UUID các sản phẩm của đơn hàng.
@apiParam   (Body:)         {ArrayObject}  states             Các bước của quy trình bán hàng.
@apiParam   (Body:)         {Object}    [config_product_tree]    Cấu hình dòng sản phẩm vào quy trình bán hàng
@apiParam   (Body:)         {String}    config_product_tree.group_customer   Nhóm khách hàng
@apiParam   (Body:)         {Array}     config_product_tree.product_lines    Danh sách dòng sản phẩm
@apiParam   (Body:)         {Boolean}    config_product_tree.status    Trạng thái cấu hình </br>
                                                                      (<code>true</code> Bật cấu hình)</br>
                                                                      (<code>false</code> Tắt cấu hình)</br>

@apiParamExample {json} Body example
{
    "name": "Tên quy trình bán hàng số 02",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
    "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop"
    "products":[
        "45042df5-2202-4964-b05f-d53e21f5f891",
        "45042df5-2202-4964-b05f-d53e21f5f894",
        "45042df5-2202-4964-b05f-d53e21f5f895",
    ],
    "states": [
        {
            "name": "Có thông tin Leads",
            "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
            "ratio": 10
        },
        {
            "name": "Liên lạc",
            "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
            "ratio": 30
        },
        {
            "name": "Đàm phán",
            "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
            "ratio": 70
        },
        {
            "name": "Đàm phán", //Trường hợp gửi lên không có "code" được tính là tạo mới trạng thái của quy trình
            "ratio": 80
        },
        {
            "name": "Đàm phán", //Trường hợp gửi lên không có "code" được tính là tạo mới trạng thái của quy trình
            "ratio": 90
        },
        {
            "name": "Thành công",
            "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
            "ratio": 100
        },
        {
            "name": "Thất bại",
            "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
            "ratio": 0
        }
    ],
    "config_product_tree": {
        "group_customer": "KHDN",
        "product_lines": [
            "6492708349194100103ffd7f",
            "64673fd960587a0010b0c25f"
        ],
        "status": true
    }
}

@apiSuccess {Array}             data                          Thông tin quy trình bán hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": {
        "_id": "5dde29fda2596203036b12c4",
        "code": "QTBHSO0001"
        "name": "Tên quy trình bán hàng số 02",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
        "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop"
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "states": [
            {
                "name": "Có thông tin Leads",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                "ratio": 10
            },
            {
                "name": "Liên lạc",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                "ratio": 30
            },
            {
                "name": "Đàm phán",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                "ratio": 70
            },
            {
                "name": "Đàm phán",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f898", // Code này mới được tạo sau khi update
                "ratio": 80
            },
            {
                "name": "Đàm phán",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f899", // Code này mới được tạo sau khi update
                "ratio": 90
            },
            {
                "name": "Thành công",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                "ratio": 100
            },
            {
                "name": "Thất bại",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                "ratio": 0
            }
        ],
        "config_product_tree": {
            "group_customer": "KHDN",
            "product_lines": [
                "6492708349194100103ffd7f",
                "64673fd960587a0010b0c25f"
            ],
            "status": true
        }
    },
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# XOÁ QUY TRÌNH BÁN HÀNG
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {DELETE} {domain}/sale/api/v1.0/sale_processes Xoá một hoặc nhiều quy trình bán hàng
@apiDescription Xoá một hoặc nhiều quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName SaleProcessDelete

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Param:)         {String}    sale_process_ids           Danh sách ID các quy trình bán hàng phân cách bởi dấu phẩy.
                                                                    <li><code>sale_process_ids:</code>id1,id2,...</li>

@apiSuccess {Array}             data                          Thông tin các quy trình bán hàng được xoá
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": [
        {
            "sale_process_id": "5dde29fda2596203036b12c4",
            "status": "SUCCESS" // Xoá thành công
        },
        {
            "sale_process_id": "5dde29fda2596203036b12c5",
            "status": "FAIL"  // Xoá thất bại
        }
    ]
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# CHI TIẾT QUY TRÌNH BÁN HÀNG
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/sale/api/v1.0/sale_processes/<sale_process_id> Chi tiết một quy trình bán hàng
@apiDescription Chi tiết một quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName SaleProcessDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess {Array}             data                          Thông tin chi tiết quy trình bán hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": {
        "_id": "5dde29fda2596203036b12c4",
        "code": "QTBHSO0001"
        "name": "Tên quy trình bán hàng số 02",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
        "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop",
        "created_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người tạo quy trình bán hàng
        "created_time": "2019-11-26T12:00:00Z" // Thời gian tạo quy trình bán hàng
        "created_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người tạo quy trình thuộc về
        "updated_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người cập nhật quy trình bán hàng cuối cùng
        "updated_time": "2019-11-26T12:00:00Z" // Thời gian cập nhật quy trình bán hàng cuối cùng
        "updated_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người cập nhật quy trình cuối cùng thuộc về
        "products":[
            "45042df5-2202-4964-b05f-d53e21f5f891",
            "45042df5-2202-4964-b05f-d53e21f5f894"
        ],
        "states": [
            {
                "name": "Có thông tin Leads",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                "ratio": 10
            },
            {
                "name": "Liên lạc",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                "ratio": 30
            },
            {
                "name": "Đàm phán 1",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                "ratio": 70
            },
            {
                "name": "Đàm phán 2 ",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f898",
                "ratio": 80
            },
            {
                "name": "Đàm phán 3",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f899",
                "ratio": 90
            },
            {
                "name": "Thành công",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                "ratio": 100
            },
            {
                "name": "Thất bại",
                "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                "ratio": 0
            }
        ]
    },
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# DANH SÁCH QUY TRÌNH BÁN HÀNG
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/sale/api/v1.0/sale_processes Danh sách quy trình bán hàng
@apiDescription Danh sách quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName SaleProcessList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Param:)         {String}  [merchant_id]       Lọc danh sách quy trình bán hàng theo thương hiệu. Nếu không truyền vào thì trả về danh sách dựa vào X-Merchant-ID
@apiParam   (Param:)         {String}  [search_text]       Lọc danh sách quy trình bán hàng theo tên
@apiParam   (Param:)         {String}  [get_all]           <ul>
                                                                <li><code>1</code>: Lấy tất cả quy trình của merchant </li>
                                                                <li>Mặc định lấy theo cấu hình của tài khoản đăng nhập</li>
                                                            </ul>
@apiParam   (Param:)         {String}  [type]              Lọc danh sách quy trình bán hàng theo loại
                                                           <li><code>sample:</code> Quy trình mẫu có sẵn</li>
                                                           <li><code>real:</code> Quy trình thực do người dùng tạo</li>
@apiUse paging

@apiSuccess {Array}          data                          Thông tin chi tiết quy trình bán hàng
@apiSuccess {String}         message                       Mô tả phản hồi
@apiSuccess {Integer}        code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": [
        {
            "_id": "5dde29fda2596203036b12c4",
            "code": "QTBHSO0001"
            "name": "Tên quy trình bán hàng số 02",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
            "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop",
            "created_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người tạo quy trình bán hàng
            "created_time": "2019-11-26T12:00:00Z" // Thời gian tạo quy trình bán hàng
            "created_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người tạo quy trình thuộc về
            "updated_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người cập nhật quy trình bán hàng cuối cùng
            "updated_time": "2019-11-26T12:00:00Z" // Thời gian cập nhật quy trình bán hàng cuối cùng
            "updated_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người cập nhật quy trình cuối cùng thuộc về
            "count_rule_auto_active": 0, // số bộ luật tự động đang hoạt động
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
                ],
            "states": [
                {
                    "name": "Có thông tin Leads",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                    "ratio": 10
                },
                {
                    "name": "Liên lạc",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                    "ratio": 30
                },
                {
                    "name": "Đàm phán 1",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                    "ratio": 70
                },
                {
                    "name": "Đàm phán 2",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f898",
                    "ratio": 80
                },
                {
                    "name": "Đàm phán 3",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f899",
                    "ratio": 90
                },
                {
                    "name": "Thành công",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                    "ratio": 100
                },
                {
                    "name": "Thất bại",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                    "ratio": 0
                }
            ]
        },
        {
            "_id": "5dde29fda2596203036b12c5",
            "code": "QTBHSO0006"
            "name": "Tên quy trình bán hàng số 01",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "is_default": 0, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
            "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop",
            "created_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người tạo quy trình bán hàng
            "created_time": "2019-11-26T12:00:00Z" // Thời gian tạo quy trình bán hàng
            "created_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người tạo quy trình thuộc về
            "updated_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người cập nhật quy trình bán hàng cuối cùng
            "updated_time": "2019-11-26T12:00:00Z" // Thời gian cập nhật quy trình bán hàng cuối cùng
            "updated_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người cập nhật quy trình cuối cùng thuộc về
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
                ],
            "states": [
                {
                    "name": "Có thông tin Leads",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                    "ratio": 10
                },
                {
                    "name": "Liên lạc",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                    "ratio": 30
                },
                {
                    "name": "Đàm phán 1 ",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                    "ratio": 70
                },
                {
                    "name": "Đàm phán 2 ",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f898",
                    "ratio": 80
                },
                {
                    "name": "Đàm phán 3",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f899",
                    "ratio": 90
                },
                {
                    "name": "Thành công",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                    "ratio": 100
                },
                {
                    "name": "Thất bại",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                    "ratio": 0
                }
            ]
        },
        ...
    ],
    "code": 200,
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 2,
        "total_count": 11,
        "total_page": 4
    }
}
"""

####################################################################################################
# DANH SÁCH QUY TRÌNH BÁN HÀNG
# version: 1.0.2                                                                                  #
####################################################################################################

"""
@api {GET} {domain}/sale/api/v1.0/sale_processes Danh sách quy trình bán hàng
@apiDescription Danh sách quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.2
@apiName SaleProcessList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Param:)         {String}  [merchant_id]       Lọc danh sách quy trình bán hàng theo thương hiệu. Nếu không truyền vào thì trả về danh sách dựa vào X-Merchant-ID
@apiParam   (Param:)         {String}  [search_text]       Lọc danh sách quy trình bán hàng theo tên
@apiParam   (Param:)         {String}  [product_line]      Dòng sản phẩm
@apiParam   (Param:)         {String}  [get_all]           <ul>
                                                                <li><code>1</code>: Lấy tất cả quy trình của merchant </li>
                                                                <li>Mặc định lấy theo cấu hình của tài khoản đăng nhập</li>
                                                            </ul>
@apiParam   (Param:)         {String}  [type]              Lọc danh sách quy trình bán hàng theo loại
                                                           <li><code>sample:</code> Quy trình mẫu có sẵn</li>
                                                           <li><code>real:</code> Quy trình thực do người dùng tạo</li>
@apiUse paging

@apiSuccess {Array}          data                          Thông tin chi tiết quy trình bán hàng
@apiSuccess {String}         message                       Mô tả phản hồi
@apiSuccess {Integer}        code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": [
        {
            "_id": "5dde29fda2596203036b12c4",
            "code": "QTBHSO0001"
            "name": "Tên quy trình bán hàng số 02",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
            "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop",
            "created_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người tạo quy trình bán hàng
            "created_time": "2019-11-26T12:00:00Z" // Thời gian tạo quy trình bán hàng
            "created_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người tạo quy trình thuộc về
            "updated_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người cập nhật quy trình bán hàng cuối cùng
            "updated_time": "2019-11-26T12:00:00Z" // Thời gian cập nhật quy trình bán hàng cuối cùng
            "updated_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người cập nhật quy trình cuối cùng thuộc về
            "count_rule_auto_active": 0, // số bộ luật tự động đang hoạt động
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
                ],
            "states": [
                {
                    "name": "Có thông tin Leads",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                    "ratio": 10
                },
                {
                    "name": "Liên lạc",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                    "ratio": 30
                },
                {
                    "name": "Đàm phán 1",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                    "ratio": 70
                },
                {
                    "name": "Đàm phán 2",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f898",
                    "ratio": 80
                },
                {
                    "name": "Đàm phán 3",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f899",
                    "ratio": 90
                },
                {
                    "name": "Thành công",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                    "ratio": 100
                },
                {
                    "name": "Thất bại",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                    "ratio": 0
                }
            ]
        }
        ...
    ],
    "code": 200,
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 2,
        "total_count": 11,
        "total_page": 4
    }
}
"""

####################################################################################################
# DANH SÁCH QUY TRÌNH BÁN HÀNG
# version: 1.0.3                                                                                  #
####################################################################################################

"""
@api {GET} {domain}/sale/api/v1.0/sale_processes Danh sách quy trình bán hàng
@apiDescription Danh sách quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.3
@apiName SaleProcessList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Param:)         {String}  [merchant_id]       Lọc danh sách quy trình bán hàng theo thương hiệu. Nếu không truyền vào thì trả về danh sách dựa vào X-Merchant-ID
@apiParam   (Param:)         {String}  [search_text]       Lọc danh sách quy trình bán hàng theo tên
@apiParam   (Param:)         {String}  [product_line]      Dòng sản phẩm
@apiParam   (Param:)         {String}  [include_team_id]      Lấy ra danh sách team theo quy trình </br>
                                                                <ul>
                                                                    <li><code>1</code>: Lấy thêm danh sách team </li>
                                                                    <li><code>0</code>: Không lấy , <b>(mặc định không gửi là 0)</b> </li>
                                                                </ul>
@apiParam   (Param:)         {String}  [include_sla_count]      Lấy ra số lượng cấu hình SLA theo quy trình? </br>
                                                                <ul>
                                                                    <li><code>1</code>: Lấy thêm số lượng cấu hình SLA </li>
                                                                    <li><code>0</code>: Không lấy cấu hình, <b>(mặc định không gửi là 0)</b> </li>
                                                                </ul>
@apiParam   (Param:)         {String}  [get_all]           <ul>
                                                                <li><code>1</code>: Lấy tất cả quy trình của merchant </li>
                                                                <li>Mặc định lấy theo cấu hình của tài khoản đăng nhập</li>
                                                            </ul>
@apiParam   (Param:)         {String}  [type]              Lọc danh sách quy trình bán hàng theo loại
                                                           <li><code>sample:</code> Quy trình mẫu có sẵn</li>
                                                           <li><code>real:</code> Quy trình thực do người dùng tạo</li>
@apiUse paging

@apiSuccess {Array}          data                          Thông tin chi tiết quy trình bán hàng
@apiSuccess {String}         message                       Mô tả phản hồi
@apiSuccess {Integer}        code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": [
        {
            "_id": "5dde29fda2596203036b12c4",
            "code": "QTBHSO0001"
            "name": "Tên quy trình bán hàng số 02",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
            "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop",
            "created_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người tạo quy trình bán hàng
            "created_time": "2019-11-26T12:00:00Z" // Thời gian tạo quy trình bán hàng
            "created_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người tạo quy trình thuộc về
            "updated_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người cập nhật quy trình bán hàng cuối cùng
            "updated_time": "2019-11-26T12:00:00Z" // Thời gian cập nhật quy trình bán hàng cuối cùng
            "updated_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người cập nhật quy trình cuối cùng thuộc về
            "count_rule_auto_active": 0, // số bộ luật tự động đang hoạt động
            "team_ids": [],
            "sla_count": 1,
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
                ],
            "states": [
                {
                    "name": "Có thông tin Leads",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                    "ratio": 10
                },
                {
                    "name": "Liên lạc",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                    "ratio": 30
                },
                {
                    "name": "Đàm phán 1",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                    "ratio": 70
                },
                {
                    "name": "Đàm phán 2",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f898",
                    "ratio": 80
                },
                {
                    "name": "Đàm phán 3",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f899",
                    "ratio": 90
                },
                {
                    "name": "Thành công",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                    "ratio": 100
                },
                {
                    "name": "Thất bại",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                    "ratio": 0
                }
            ]
        }
        ...
    ],
    "code": 200,
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 2,
        "total_count": 11,
        "total_page": 4
    }
}
"""

####################################################################################################
# Đếm số đơn hàng mới theo quy trình bán hàng
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/sale/api/v1.0/sale_processes/<sale_process_id>/count_deal Đếm số đơn hàng mới theo quy trình bán hàng
@apiDescription Đếm số đơn hàng mới theo quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName CountDealBySaleProcess

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query) {String} merchant_id Merchant ID của Quy trình bán hàng

@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Array} data Dữ liệu bộ đếm trả về

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "deal_count": 6,
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "sale_process_id": "5deb1684007adde163d8b8ed"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

####################################################################################################
# Đếm số đơn hàng theo quy trình bán hàng
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/sale/api/v1.0/sale_processes/<sale_process_id>/count Đếm số đơn hàng theo quy trình bán hàng
@apiDescription Đếm số đơn hàng theo quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName CountDealSaleProcess

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query) {String} merchant_id Merchant ID của Quy trình bán hàng

@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Array} data Dữ liệu bộ đếm trả về

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "deal_count": [
            {
                "assignee_id": "1b99bdcf-d582-4f49-9715-1b61dfff3999",
                "count": 10
            },
            {
                "assignee_id": "other",
                "count": 15
            }
        ],
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "sale_process_id": "5deb1684007adde163d8b8ed"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

####################################################################################################
# Đếm số đơn hàng theo một trạng thái của một quy trình bán hàng
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/sale/api/v1.0/sale_processes/<sale_process_id>/states/<state_code>/count Đếm số đơn hàng theo một trạng thái của một quy trình bán hàng
@apiDescription Đếm số đơn hàng theo một trạng thái của một quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName CountDealBySaleProcessState

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header

@apiParam (Query) {String} merchant_id Merchant ID của Quy trình bán hàng

@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Array} data Dữ liệu bộ đếm trả về

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "deal_count": [
            {
                "assignee_id": "1b99bdcf-d582-4f49-9715-1b61dfff3999",
                "count": 10
            },
            {
                "assignee_id": "other",
                "count": 15
            }
        ],
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "sale_process_id": "5deb1684007adde163d8b8ed",
        "state_code": "1a270a69-7f3c-45e9-92c8-2913f9b4230b"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

####################################################################################################
# Chuyển trạng thái của nhiều đơn hàng
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/sale_processes/change_state Chuyển trạng thái của nhiều đơn hàng
@apiDescription Chuyển trạng thái của nhiều đơn hàng
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName ChangeStateDeals

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse merchant_id_header
@apiUse json_header


@apiSuccess {Number} code Response status
@apiSuccess {String} message Response message
@apiSuccess {Array} data Dữ liệu bộ đếm trả về

@apiParamExample {json} Body example
{
    "sale_process_id": "5deb1684007adde163d8b8ed", // ID của Quy trình bán hàng
    "current_state_code": "1a270a69-7f3c-45e9-92c8-2913f9b4230b", // state_code hiện tại
    "next_state_code": "1a270a69-7f3c-45e9-92c8-2913f9b42301" // state_code sẽ chuyển mới
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924" // Merchant ID.
}

@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "deal_id": "5deb1684007adde163d8b8ed",
            "deal_name": "Đơn hàng 01"
        },
        {
            "deal_id": "5deb1684007adde163d8b8ea",
            "deal_name": "Đơn hàng 02"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

####################################################################################################
# DANH SÁCH QUY TRÌNH BÁN HÀNG THEO IDS
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/api/v1.0/sale_processes/list_by_ids Danh sách quy trình bán hàng theo danh sách ids
@apiDescription Danh sách quy trình bán hàng
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName SaleProcessListByIds

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiSuccess {Array}          data                          Thông tin chi tiết quy trình bán hàng
@apiSuccess {String}         message                       Mô tả phản hồi
@apiSuccess {Integer}        code                          Mã phản hồi

@apiParam (Body) {Array} sale_process_ids Danh sách id của quy trình

@apiParamExample {json} Body example
{
    "sale_process_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"]
}

@apiSuccessExample {json} Response
{
    "data": [
        {
            "_id": "5dde29fda2596203036b12c4",
            "code": "QTBHSO0001"
            "name": "Tên quy trình bán hàng số 02",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
            "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop",
            "created_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người tạo quy trình bán hàng
            "created_time": "2019-11-26T12:00:00Z" // Thời gian tạo quy trình bán hàng
            "created_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người tạo quy trình thuộc về
            "updated_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người cập nhật quy trình bán hàng cuối cùng
            "updated_time": "2019-11-26T12:00:00Z" // Thời gian cập nhật quy trình bán hàng cuối cùng
            "updated_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người cập nhật quy trình cuối cùng thuộc về
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
                ],
            "states": [
                {
                    "name": "Có thông tin Leads",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                    "ratio": 10
                },
                {
                    "name": "Liên lạc",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                    "ratio": 30
                },
                {
                    "name": "Đàm phán 1",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                    "ratio": 70
                },
                {
                    "name": "Đàm phán 2",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f898",
                    "ratio": 80
                },
                {
                    "name": "Đàm phán 3",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f899",
                    "ratio": 90
                },
                {
                    "name": "Thành công",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                    "ratio": 100
                },
                {
                    "name": "Thất bại",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                    "ratio": 0
                }
            ]
        },
        {
            "_id": "5dde29fda2596203036b12c5",
            "code": "QTBHSO0006"
            "name": "Tên quy trình bán hàng số 01",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "is_default": 0, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
            "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop",
            "created_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người tạo quy trình bán hàng
            "created_time": "2019-11-26T12:00:00Z" // Thời gian tạo quy trình bán hàng
            "created_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người tạo quy trình thuộc về
            "updated_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người cập nhật quy trình bán hàng cuối cùng
            "updated_time": "2019-11-26T12:00:00Z" // Thời gian cập nhật quy trình bán hàng cuối cùng
            "updated_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người cập nhật quy trình cuối cùng thuộc về
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
                ],
            "states": [
                {
                    "name": "Có thông tin Leads",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                    "ratio": 10
                },
                {
                    "name": "Liên lạc",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                    "ratio": 30
                },
                {
                    "name": "Đàm phán 1 ",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                    "ratio": 70
                },
                {
                    "name": "Đàm phán 2 ",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f898",
                    "ratio": 80
                },
                {
                    "name": "Đàm phán 3",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f899",
                    "ratio": 90
                },
                {
                    "name": "Thành công",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                    "ratio": 100
                },
                {
                    "name": "Thất bại",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                    "ratio": 0
                }
            ]
        },
        ...
    ],
    "code": 200,
    "message": "request thành công."
}
"""

#########################################################################################
# DANH SÁCH STATE THEO QUY TRÌNH BÁN HÀNG												#
# version: 1.0.1                                                                        #
#########################################################################################


"""
@api {POST} {domain}/sale/api/v1.0/sale-process-list/info Danh sách thông tin quy trình
@apiDescription Danh sách thông tin quy trình
@apiGroup SaleProcess
@apiVersion 1.0.1
@apiName SaleProcessInfo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(BODY:) {Array}     fields                      Danh sách field cần lấy (Không truyền gì lên mặc định lấy _id)
@apiSuccess {String}            _id         				Id của quy trình bán hàng
@apiSuccess {String}            code						Mã state
@apiSuccess {String}            name                       	Tên state
@apiSuccess {Integer}           ratio                      	Phần trăm thành công của state
@apiSuccess {Integer}           status                     	Trạng thái của state
                                                            <code> -USED: 1, UNUSED: 0, DELETED:-1
                                                            </code>

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "_id": "6139cb8ff13e400a5de4f561",
            "name": "Quy Trình mặc định"
        },
        {
            "_id": "6141a5fc2c39d967753a2e0a",
            "name": "quy trinh 2"
        },
        {
            "_id": "6142cddcba665d3c4ec27fe4",
            "name": "abcdff"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

####################################################################################################
# CHI TIẾT QUY TRÌNH BÁN HÀNG THEO MÃ CODE
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/sale/api/v1.0/sale_processes/detail-by-code/<sale-process-code> Chi tiết một quy trình bán hàng theo mã(code) Quy trình
@apiDescription Chi tiết một quy trình bán hàng theo mã(code) Quy trình
@apiGroup SaleProcess
@apiVersion 1.0.0
@apiName SaleProcessDetailByCode

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Query:) {String}     [fields]                      Danh sách field thông tin quy trình bán hàng, cách nhau bởi dấu phẩy (,). (<code>Mặc định nếu như không truyền lên sẽ lấy field: <b>id, code, name</b></code>)</br>
                                                              <li><code>id</code>: ID Quy trình bán hàng</li>
                                                              <li><code>code</code>: Mã Quy trình bán hàng</li>
                                                              <li><code>name</code>: Tên Quy trình bán hàng</li>
                                                              <li><code>states</code>: Danh sách các bước của Quy trình</li>

@apiSuccess {Object}            data                          Thông tin chi tiết quy trình bán hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccess {String}            data.id                       ID quy trình bán hàng
@apiSuccess {String}            data.code                     Mã code quy trình bán hàng
@apiSuccess {String}            data.name                     Tên quy trình bán hàng
@apiSuccess {String}            data.updated_by               ID User Cập nhật quy trình bán hàng gần nhất
@apiSuccess {String}            data.created_by               ID User Khởi tạo quy trình bán hàng gần nhất
@apiSuccess {String}            data.created_time             Thời gian tạo mới quy trình bán hàng
@apiSuccess {String}            data.updated_time             Thời gian cập nhật quy trình bán hàng gần nhất

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "_id": "6598b94a62c4338b24186eda",
        "code": "PDA8RR",
        "id": "6598b94a62c4338b24186eda",
        "name": "Thanh test thay đổi quy trình"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

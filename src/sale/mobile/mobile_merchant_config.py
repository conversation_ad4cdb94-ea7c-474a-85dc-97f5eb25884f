#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: ThongNV
    Company: M O B I O
    Date Created: 11/17/21
"""

# =========== Danh sách ngành nghề =====================

"""
@api {GET} {domain}/sale/mobile/api/v1.0/configs/job  Danh sách ngành nghề
@apiDescription L<PERSON>y danh sách ngành nghề, đư<PERSON>c config trong dynamic field profile
@apiGroup Mobile Merchant Config
@apiVersion 1.0.1
@apiName MobileMerchantConfigJob

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {Array}             data.id                       ID
@apiSuccess {Array}             data.name                     Tên ngành nghề
@apiSuccess {String}            message                       M<PERSON> tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "code": 200,
    "data": [
        {
            "id": 1,
            "name": "Sale"
        },
        {
            "id": 2,
            "name": "Làm ruộng"
        },
        {
            "id": 3,
            "name": "Bác sỹ"
        },
        {
            "id": 4,
            "name": "Thủy thủ"
        },
        {
            "id": 5,
            "name": "Vệ sinh môi trường"
        },
        {
            "id": 6,
            "name": "Khác"
        },
        {
            "id": 7,
            "name": "IT"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# =========== Lấy giá trị config field của module profile =====================

"""
@api {GET} {domain}/sale/mobile/api/v1.0/configs/mapping/field-profile  List Value Config Field Profile
@apiDescription Lấy giá trị config field của module profile
@apiGroup Mobile Merchant Config
@apiVersion 1.0.1
@apiName ConfigMappingFieldProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(Query:)			{String}	    field_key	  Field cần lấy giá trị mặc định
                                                              <ul>
                                                                <li><code>job</code> : Ngành nghề</li>
                                                                <li><code>field_of_activity</code> : Lĩnh vực hoạt động</li>
                                                              </ul>

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {Array}             data.id                       ID
@apiSuccess {Array}             data.name                     Tên giá trị tương ứng với key
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "code": 200,
    "data": [
        {
            "id": 1,
            "name": "Sale"
        },
        {
            "id": 2,
            "name": "Làm ruộng"
        },
        {
            "id": 3,
            "name": "Bác sỹ"
        },
        {
            "id": 4,
            "name": "Thủy thủ"
        },
        {
            "id": 5,
            "name": "Vệ sinh môi trường"
        },
        {
            "id": 6,
            "name": "Khác"
        },
        {
            "id": 7,
            "name": "IT"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
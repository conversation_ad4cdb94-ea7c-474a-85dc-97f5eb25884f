#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TungDD
    Company: Mobio
    Date Created: 19/11/2021
"""
# ------------------ <PERSON><PERSON><PERSON> danh sách các bước bán hàng của deal --------------------
"""
@api {GET} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/sale_steps   Danh sách các bước bán hàng của Deal
@apiGroup MobileSaleProcess
@apiVersion 1.0.2
@apiName ListSaleStepsDeal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(QUERY:)			{string}	    sale_process_id		        Định danh quy trình bán hàng

@apiSuccess {String}            message                       M<PERSON> tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          D<PERSON> liệu


@apiSuccess {String}            data.state_name               T<PERSON><PERSON> b<PERSON><PERSON><PERSON> bán hàng
@apiSuccess {String}            data.states_code              Mã của bước bán hàng
@apiSuccess {Number}            data.ratio                    Tỷ lệ thành công tương ứng.
                                                              <ul>
                                                                <li><code>0</code>: Thất bại</li>
                                                                <li><code>100</code>: Thành công</li>
                                                              </ul>
                                                              
@apiSuccess {Object}            [data.result]                       Thông tin kết quả của bước bán hàng.
@apiSuccess {Object}            [data.result.status]                Trạng thái của bước bán hàng.
                                                                    <ul>
                                                                        <li><code>0</code>: Thất bại</li>
                                                                        <li><code>1</code>: Thành công</li>
                                                                    </ul>
@apiSuccess {Object}            [data.result.reason]                       Lý do


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "name": "Thất bại",
            "ratio": 0,
            "state_code": "47de7430-67dc-4bc4-935c-eb4d5199d072"
        },
        {
            "name": "Có thông tin Leads đơn hàng của",
            "ratio": 10,
            "state_code": "56f27e81-3f10-49b1-b0ec-1faab0715282"
        },
        {
            "name": "Liên lạc",
            "ratio": 20,
            "state_code": "bcaa317c-165e-4863-89d1-46e83325aa53",
            "result": {
                "status": 0,
                "reason": "Buồn nên không mua."
            }
        },
        {
            "name": "Giới thông tin, báo giá",
            "ratio": 30,
            "state_code": "391fc012-19c5-4708-9bd5-e8e922c95832"
        },
        {
            "name": "Thuyết phục",
            "ratio": 40,
            "state_code": "0dcefe09-3b37-457d-a255-4817a668ec75"
        },
        {
            "name": "Thành công",
            "ratio": 100,
            "state_code": "7270bac1-c9a1-470b-a2db-0878f9b7a381"
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""

# ------------------ Cập nhật bước bán hàng của deal --------------------
"""
@api {PUT} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/change/steps           Cập nhật bước bán hàng của deal         
@apiGroup MobileSaleProcess
@apiVersion 1.0.2
@apiName UpdateSaleStepsDeal

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(BODY:)			{string}	    state_code		                Mã của bước bán hàng mới.
@apiParam	(BODY:)			{object}	    result_before_step		        Kết quả của bước trước.
@apiParam	(BODY:)			{integer}	    result_after_step.status		Trạng thái của bước trước   
                                                                            <ul>
                                                                                <li><code>0</code>: Thất bại</li>
                                                                                <li><code>1</code>: Thành công</li>
                                                                            </ul>
@apiParam	(BODY:)			{String}	    [result_after_step.reason]		Lý do

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Dữ liệu




@apiSuccessExample {json} Response Example
{
    "code": 200,
    "lang": "vi",
    "message": "request thành công."
}
"""


####################################################################################################
# DANH SÁCH QUY TRÌNH BÁN HÀNG
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/sale/api/v1.0/sale_processes Danh sách quy trình bán hàng
@apiDescription Danh sách quy trình bán hàng
@apiGroup MobileSaleProcess
@apiVersion 1.0.1
@apiName SaleProcessMobileList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Param:)         {String}  [merchant_id]       Lọc danh sách quy trình bán hàng theo thương hiệu. Nếu không truyền vào thì trả về danh sách dựa vào X-Merchant-ID
@apiParam   (Param:)         {String}  [search_text]       Lọc danh sách quy trình bán hàng theo tên
@apiParam   (Param:)         {String}  [get_all]           <ul>
                                                                <li><code>1</code>: Lấy tất cả quy trình của merchant </li>
                                                                <li>Mặc định lấy theo cấu hình của tài khoản đăng nhập</li>
                                                            </ul>
@apiParam   (Param:)         {String}  [type]              Lọc danh sách quy trình bán hàng theo loại
                                                           <li><code>sample:</code> Quy trình mẫu có sẵn</li>
                                                           <li><code>real:</code> Quy trình thực do người dùng tạo</li>
@apiUse paging

@apiSuccess {Array}          data                          Thông tin chi tiết quy trình bán hàng
@apiSuccess {String}         message                       Mô tả phản hồi
@apiSuccess {Integer}        code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": [
        {
            "_id": "5dde29fda2596203036b12c4",
            "code": "QTBHSO0001"
            "name": "Tên quy trình bán hàng số 02",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
            "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop",
            "created_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người tạo quy trình bán hàng
            "created_time": "2019-11-26T12:00:00Z" // Thời gian tạo quy trình bán hàng
            "created_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người tạo quy trình thuộc về
            "updated_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người cập nhật quy trình bán hàng cuối cùng
            "updated_time": "2019-11-26T12:00:00Z" // Thời gian cập nhật quy trình bán hàng cuối cùng
            "updated_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người cập nhật quy trình cuối cùng thuộc về
            "count_rule_auto_active": 0, // số bộ luật tự động đang hoạt động
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
                ],
            "states": [
                {
                    "name": "Có thông tin Leads",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                    "ratio": 10
                },
                {
                    "name": "Liên lạc",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                    "ratio": 30
                },
                {
                    "name": "Đàm phán 1",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                    "ratio": 70
                },
                {
                    "name": "Đàm phán 2",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f898",
                    "ratio": 80
                },
                {
                    "name": "Đàm phán 3",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f899",
                    "ratio": 90
                },
                {
                    "name": "Thành công",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                    "ratio": 100
                },
                {
                    "name": "Thất bại",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                    "ratio": 0
                }
            ]
        },
        {
            "_id": "5dde29fda2596203036b12c5",
            "code": "QTBHSO0006"
            "name": "Tên quy trình bán hàng số 01",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "is_default": 0, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
            "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop",
            "created_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người tạo quy trình bán hàng
            "created_time": "2019-11-26T12:00:00Z" // Thời gian tạo quy trình bán hàng
            "created_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người tạo quy trình thuộc về
            "updated_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người cập nhật quy trình bán hàng cuối cùng
            "updated_time": "2019-11-26T12:00:00Z" // Thời gian cập nhật quy trình bán hàng cuối cùng
            "updated_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người cập nhật quy trình cuối cùng thuộc về
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
                ],
            "states": [
                {
                    "name": "Có thông tin Leads",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                    "ratio": 10
                },
                {
                    "name": "Liên lạc",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                    "ratio": 30
                },
                {
                    "name": "Đàm phán 1 ",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                    "ratio": 70
                },
                {
                    "name": "Đàm phán 2 ",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f898",
                    "ratio": 80
                },
                {
                    "name": "Đàm phán 3",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f899",
                    "ratio": 90
                },
                {
                    "name": "Thành công",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                    "ratio": 100
                },
                {
                    "name": "Thất bại",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                    "ratio": 0
                }
            ]
        },
        ...
    ],
    "code": 200,
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 2,
        "total_count": 11,
        "total_page": 4
    }
}
"""


####################################################################################################
# DANH SÁCH QUY TRÌNH BÁN HÀNG
# version: 1.0.2                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/sale/api/v1.0/sale_processes Danh sách quy trình bán hàng
@apiDescription Danh sách quy trình bán hàng
@apiGroup MobileSaleProcess
@apiVersion 1.0.2
@apiName SaleProcessMobileList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Param:)         {String}  [merchant_id]       Lọc danh sách quy trình bán hàng theo thương hiệu. Nếu không truyền vào thì trả về danh sách dựa vào X-Merchant-ID
@apiParam   (Param:)         {String}  [search_text]       Lọc danh sách quy trình bán hàng theo tên
@apiParam   (Param:)         {String}  [product_line]      Dòng sản phẩm
@apiParam   (Param:)         {String}  [get_all]           <ul>
                                                                <li><code>1</code>: Lấy tất cả quy trình của merchant </li>
                                                                <li>Mặc định lấy theo cấu hình của tài khoản đăng nhập</li>
                                                            </ul>
@apiParam   (Param:)         {String}  [type]              Lọc danh sách quy trình bán hàng theo loại
                                                           <li><code>sample:</code> Quy trình mẫu có sẵn</li>
                                                           <li><code>real:</code> Quy trình thực do người dùng tạo</li>
@apiUse paging

@apiSuccess {Array}          data                          Thông tin chi tiết quy trình bán hàng
@apiSuccess {String}         message                       Mô tả phản hồi
@apiSuccess {Integer}        code                          Mã phản hồi

@apiSuccessExample {json} Response
{
    "data": [
        {
            "_id": "5dde29fda2596203036b12c4",
            "code": "QTBHSO0001"
            "name": "Tên quy trình bán hàng số 02",
            "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "is_default": 1, //0: Đây không phải là quy trình mặc định. 1: Đây là quy trình mặc định.
            "description": "Quy trình sử dụng cho nhân viên bán nhà của Pingcomshop",
            "created_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người tạo quy trình bán hàng
            "created_time": "2019-11-26T12:00:00Z" // Thời gian tạo quy trình bán hàng
            "created_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người tạo quy trình thuộc về
            "updated_by": "45042df5-2202-4964-b05f-d53e21f5f891" // UUID của người cập nhật quy trình bán hàng cuối cùng
            "updated_time": "2019-11-26T12:00:00Z" // Thời gian cập nhật quy trình bán hàng cuối cùng
            "updated_merchant_id": "45042df5-2202-4964-b05f-d53e21f5f892" // merchant_id mà người cập nhật quy trình cuối cùng thuộc về
            "count_rule_auto_active": 0, // số bộ luật tự động đang hoạt động
            "products":[
                "45042df5-2202-4964-b05f-d53e21f5f891",
                "45042df5-2202-4964-b05f-d53e21f5f894"
            ],
            "states": [
                {
                    "name": "Có thông tin Leads",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f880",
                    "ratio": 10
                },
                {
                    "name": "Liên lạc",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f881",
                    "ratio": 30
                },
                {
                    "name": "Đàm phán 1",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f884",
                    "ratio": 70
                },
                {
                    "name": "Đàm phán 2",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f898",
                    "ratio": 80
                },
                {
                    "name": "Đàm phán 3",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f899",
                    "ratio": 90
                },
                {
                    "name": "Thành công",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f885",
                    "ratio": 100
                },
                {
                    "name": "Thất bại",
                    "state_code": "45042df5-2202-4964-b05f-d53e21f5f886",
                    "ratio": 0
                }
            ]
        }
        ...
    ],
    "code": 200,
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 2,
        "total_count": 11,
        "total_page": 4
    }
}
"""
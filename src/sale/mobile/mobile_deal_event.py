#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TungDD
    Company: Mobio
    Date Created: 08/11/2021
"""
# ---------- <PERSON>h sách Event -----------
"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/action/filter/activity      Lấy danh sách event 
@apiGroup Mobile Event
@apiVersion 1.0.0
@apiName MobileListEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (BODY:) {Array}                     brand_ids              Danh sách các thương hiệu cần tìm kiếm
@apiParam   (BODY:) {Array}                     staff_ids              Danh sách nhân viên tương tác
@apiParam   (BODY:) {string}                    start_time             Thời gian bắt đầu tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {string}                    end_time               Thời gian kết thúc tìm kiếm. Định dạng: <code>"%Y-%m-%d"</code>
@apiParam   (BODY:) {Array=UpdateDeal,Email,Other}                     activities             Danh sách hoạt động cần tìm kiếm
                                                                        <ul>
                                                                            <li><code>UpdateDeal</code>: Cập nhật thông tin đơn hàng</li>
                                                                            <li><code>Email</code>: E-mail</li>
                                                                            <li><code>Other</code>: Khác</li>
                                                                        </ul>
@apiParam   (BODY:) {Array=merchant,profile}                    line_event               Theo chiều nhân viên hay chiều khách hàng.

@apiParamExample {json} Body example
{
    "brand_ids": [].
    "staff_ids": [],
    "start_time": "2021-10-15",
    "end_time": "2022-10-15",
    "activities": ["UpdateDeal", "Email", "Other"],
    "line_event": ["merchant", "profile"]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Array}             data                          Danh sách các Event tương ứng.

@apiSuccess {String}            data.merchant_id              Định danh merchant.
@apiSuccess {String}            data.deal_id                  Định danh đơn hàng.
@apiSuccess {String}            data.event_id                 Định danh Event 
@apiSuccess {String}            data.event_type               Loại của chi tiết Event
                                                                <ul>
                                                                    <li><code>InitDeal</code>: Tạo đơn hàng</li>
                                                                    <li><code>UpdateDeal</code>: Cập nhật thông tin đơn hàng</li>
                                                                    <li><code>Email</code>: Event email</li>
                                                                    <li><code>UpdateMediaDeal</code>: Cập nhật thông tin file đính kèm.</li>
                                                                </ul>
@apiSuccess {String}            data.staff_update_deal        Định danh nhân viên cập nhật thông tin
@apiSuccess {String}            data.activity                 Định danh loại Event
@apiSuccess {String}            data.line_event               Event tạo từ hệ thống hoặc người dùng: merchant, profile
@apiSuccess {String}            data.body                     Thông tin của Event
@apiSuccess {Object}            data.action_time              Thời gian diễn ra Event
@apiSuccess {Object}            data.created_time             Thời điểm tạo


@apiSuccessExample {json} Response, SourceEvent:Deal, EventType:UpdateDeal, Activity: UpdateDeal:
{
    "id": "6169367e3677675c6e7eb08f",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "deal_id": "6169036b354fa21ed10743da",
    "event_id": "6169363e3677675c6e7eb08d",
    "event_type": "UpdateDeal",
    "staff_update_deal" : {
        "id" : "7fc0a33c-baf5-11e7-a7c2-0242ac180003",  
        "email" : "<EMAIL>",
        "fullname" : "Nguyễn Văn A",
        "phone_number" : "+84323456789",
        "username" : "admin@pingcomshop"
    },
    "line_event": "merchant",
    "action_time" : 1635230358.890302,
    "created_time": "2021-10-15T15:05:18.856",
    "body": {
        "information": [
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "description",
                "field_name": "description",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [
                    "333"
                ],
                "change": [],
                "remove": []
            },
            {
                "is_base": true,
                "display_type": "single_line",
                "field_key": "reason_fail",
                "field_name": "reason_fail",
                "required": false,
                "field_property": 2,
                "group": "information",
                "add": [],
                "change": [
                    {
                        "from": "buồn quá không mua",
                        "to": "buồn quá không mua a"
                    }
                ],
                "remove": []
            }
        ],
        "dynamic": [
            {
                "field_name": "Radio button",
                "field_key": "_dyn_radio_button_1633599591661",
                "field_property": 2,
                "display_type": "radio",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    "ba"
                ],
                "change": [],
                "remove": []
            },
            {
                "field_name": "Single-line text chữ",
                "field_key": "_dyn_single_line_text_chu_1631458349843",
                "field_property": 2,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [],
                "change": [
                    {
                        "from": "123123123123",
                        "to": "12312312399"
                    }
                ],
                "remove": []
            },
            {
                "field_name": "Single-line text số",
                "field_key": "_dyn_single_line_text_so_1631181218494",
                "field_property": 1,
                "display_type": "single_line",
                "is_base": false,
                "group": "dynamic",
                "required": false,
                "add": [
                    12312312
                ],
                "change": [],
                "remove": []
            }
        ]
    }
}

@apiSuccessExample {json} Response, SourceEvent:Deal, Activity: Other, EventType:UpdateMediaDeal:
{
    {
        "id" : "618cbee0767233e9c954590c",
        "merchant_id" : "232e7ad3-2af0-4256-9089-3f62c392b0c1",
        "deal_id" : "618a4b706b73be7e3bc39536",
        "source_event" : "Deal",
        "activity" : "Other",
        "source" : "SALE",
        "event_type" : "UpdateMediaDeal",
        "line_event": "merchant",
        "staff_update_deal" : {
            "id" : "d7e63da1-a47e-43e2-9b78-44d43433f7ec",
            "email" : "<EMAIL>",
            "fullname" : "sale",
            "phone_number" : "+84978348445",
            "username" : "sale@thanhtest11"
        },
        "action_time" : 1636613856.258556,
        "body" : {
            "add" : [],
            "remove" : [
                {
                    "type_media" : [
                        "identification"
                    ],
                    "url" : "https://t1.mobio.vn/static/232e7ad3-2af0-4256-9089-3f62c392b0c1/upload/1636613822_test_demo.jpeg",
                    "format_file" : "image/jpeg",
                    "title" : "1636613822_test_demo.jpeg",
                    "local_path" : "/media/data/public_resources/static/232e7ad3-2af0-4256-9089-3f62c392b0c1/upload/1636613822_test_demo.jpeg"
                }
            ]
        }
    }
}
@apiSuccessExample {json} Response, SourceEvent:Email, Activity: Email, EventType:Email:
{
    "id": "",
    "merchant_id": "",
    "deal_id": "",
    "event_id": "",
    "source_event": "Email",
    "event_type":"",
    "staff_update_deal" : {
            "id" : "d7e63da1-a47e-43e2-9b78-44d43433f7ec",
            "email" : "<EMAIL>",
            "fullname" : "sale",
            "phone_number" : "+84978348445",
            "username" : "sale@thanhtest11"
    },
    "action_time" : 1636613856.258556,
    "source": "EMAIL",
    "body": {
        "profile_data": profile_data,
        "from_email": ""		// email gửi
        "to_email": [""]		// danh sách người nhận
        "cc_email": [""]
        "bcc_email": [""]
        "subject": ""		// tiêu đề email 
        "content": ""		// nội dung email 
        "sender_domain": "mobio.vn", // có đối với kiểu email_mkt
        "related_to": {
            "company_ids": [], // Danh sách company có liên quan tới
            "ticket_ids": [], // Danh sách ticket có liên quan tới
            "profile_ids": [], // Danh sách Profile có liên quan tới
            "order_ids": [], // Danh sách các deal có liên quan tới
        },
        "attachments": [""] // Danh sách url file media 
        "status": {
            "type": "success", // Trạng thái của email đang gửi, đã gửi, gửi lỗi: pending, success, failed
            "message": "", // Trong trường hợp gửi mail lỗi thì cần gửi kèm nguyên nhân lỗi trong field này
        },
        "action_time": "",
    }
}
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
               ..
              ( '`<
               )(
        ( ----'  '.
        (         ;
         (_______,' 
    ~^~^~^~^~^~^~^~^~^~^~
    Author: ThongNV
    Company: M O B I O
    Date Created: 9/27/21
"""

####################################################################################################
# Số lượng đơn hàng theo trạng thái
# version: 1.0.1                                                                                  #
####################################################################################################

"""
@api {POST} {domain}/sale/mobile/api/v1.0/report/states   Báo cáo số lượng đơn hàng theo trạng thái
@apiGroup Mobile Deal
@apiVersion 1.0.2
@apiName ReportStates

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(BODY:)			{Array}	    sale_process_ids		Danh sách ID quy trình bán hàng
@apiParam	(BODY:)			{Array}	    [source]		        Danh sách tên campaign
@apiParam	(BODY:)			{String} 	[start_time]	        Thời gian bắt đầu (Format: YYYY-mm-dd)
@apiParam	(BODY:)			{String} 	[end_time]	            Thời gian kết thúc (Format: YYYY-mm-dd)
                                                   
@apiParamExample {json} Body example
{
    "sale_process_ids": [
        "615a7ee7ea2adc757afac4cc"
    ],
    "source": [
        "ViettelPost1",
        "ViettelPost2"
    ],
    "start_time": "2021-09-01",
    "end_time": "2021-09-30"   

}

@apisuccess {Array}             data                          Danh sách đơn hàng
@apisuccess {String}            data.state_name               Tên trạng thái đơn hàng
@apisuccess {Array}             data.states_code              Danh sách trạng thái đơn hàng tương ứng với tên
@apisuccess {Number}            data.number_deal              Số lượng đơn hàng tương ứng theo trạng thái
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "data": [
        {
            "state_name": "Lead mới phân bổ",
            "states_code": [
                "AQH910",
                "BCN019"
            ],
            "number_deal": 500
        }
    ]
    "code": 200,
    "message": "request thành công"
}
"""

####################################################################################################
# Danh sách campaign
# version: 1.0.1                                                                                  #
####################################################################################################

"""
@api {GET} {domain}/sale/mobile/api/v1.0/campaigns   Danh sách Campaign
@apiGroup Mobile Deal
@apiVersion 1.0.2
@apiName Campaigns

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apisuccess {Array}             data                          Danh sách campaign
@apisuccess {String}            data.name                     Tên campaign
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "data": [
        {
            "name": "ViettelPost1"
        },
        {
            "name": "ViettelPost2"
        }
    ]
    "code": 200,
    "message": "request thành công"
}

"""


####################################################################################################
# Danh sách campaign theo User
# version: 1.0.1                                                                                  #
####################################################################################################

"""
@api {GET} {domain}/sale/mobile/api/v1.0/campaigns/assignee   Danh sách Campaign theo nhân viên
@apiGroup Mobile Deal
@apiVersion 1.0.2
@apiName CampaignsAssignee

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apisuccess {Array}             data                          Danh sách campaign
@apisuccess {String}            data.name                     Tên campaign
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "data": [
        {
            "name": "ViettelPost1"
        },
        {
            "name": "ViettelPost2"
        }
    ]
    "code": 200,
    "message": "request thành công"
}

"""

####################################################################################################
# Thống kê đơn hàng theo Campaign
# version: 1.0.1                                                                                  #
####################################################################################################

"""
@api {GET} {domain}/sale/mobile/api/v1.0/report/campaign   Thống kê đơn hàng theo Campaign
@apiGroup Mobile Deal
@apiVersion 1.0.2
@apiName Report Campaign

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apisuccess {Array}             data                          Danh sách thống kê đơn hàng theo campaign
@apisuccess {String}            data.name                     Tên campaign
@apisuccess {Integer}           data.total_lead               Tổng số đơn hàng
@apisuccess {Integer}           data.total_mined              Tổng số đơn hàng đã khai thác
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "data": [
        {
            "name": "Nhập có tổ chức",
            "total_lead": 1,
            "total_mined": 0
        }
    ],
    "lang": "vi",
    "code": 200,
    "message": "request thành công"
}

"""

###################################################################################################
# Thông tin KPI của nhân viên
# version: 1.0.1                                                                                  #
###################################################################################################

"""
@api {POST} {domain}/sale/mobile/api/v1.0/kpi/list   Danh sách KPI của nhân viên
@apiGroup Mobile Deal
@apiVersion 1.0.2
@apiName KpiList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(Query:)			{String} 	start_time	  Thời gian bắt đầu (Format: mm/YYYY). VD: 2/2021
@apiParam	(Query:)			{String} 	end_time	  Thời gian kết thúc (Format: mm/YYYY) VD: 3/2021

@apisuccess {Array}             data                      Danh sách KPI
@apisuccess {String}            data.name                 Tên chỉ số
@apisuccess {String}            data.code                 Mã chỉ số
@apisuccess {Number}            data.actual_value         Chỉ số thực hiện        
@apisuccess {Number}            data.target_value         Chỉ số kế hoạch               
@apisuccess {Number}            data.completion_rate              Tỷ lệ % hoàn thành               
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response Example
{
    "data": [
        {
            "name": "Tổng điểm",
            "code": "TOTAL",
            "actual_value": 1500,
            "target_value": 3000
            "completion_rate": 50
        },
        {
            "name": "Điểm tín dụng",
            "code": "DTD",
            "actual_value": 73,
            "target_value": 146
            "completion_rate": 50
        },
    ]
    "code": 200,
    "message": "request thành công"
}

"""

####################################################################################################
# Danh sách đơn hàng Upgrade V2
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/action/filter  Danh sách đơn hàng
@apiGroup Mobile Deal
@apiVersion 1.0.1
@apiName DealListFilter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(BODY:)			{String}    view_type   	      Kiểu hiển thị đơn hàng
                                                              <ul>
                                                                    <li><code>KANBAN</code>: hiển thị dạng cột </li>
                                                                    <li><code>TABLE</code>:  hiển thị dạng bảng </li>
                                                              </ul>
                                                              default: KANBAN


@apiParam	(BODY:)			{Array}	    [sale_process_id]     ID quy trình
@apiParam	(BODY:)			{Array}	    states_code		      Danh sách mã code trạng thái đơn hàng
                                                              <ul>
                                                                <li>
                                                                    Trường hợp <code> view_type = KANBAN</code> :
                                                                     state_code là bắt buộc 
                                                                </li>
                                                                <li>
                                                                    Trường hợp <code>view_type = TABLE</code>
                                                                     state_code không bắt buộc  
                                                                </li>
                                                              </ul>
                
@apiParam	(BODY:)			{String}	search			      Tìm kiếm đơn hàng theo tên đơn hàng, mã đơn hàng
@apiParam	(BODY:)			{Array}	    [sale_filters]		  danh sách các điều kiện lọc đơn hàng
@apiParam	(BODY:)			{String}	[source]		      Tên campaign / sub campaing
@apiParam	(BODY:)			{Number}	[is_new]		      Đơn hàng mới?
                                                             <ul>
                                                                <li><code>1</code>: Đơn hàng mới</li>
                                                                <li><code>0</code>: Đơn hàng cũ</li>
                                                             </ul>

@apiParam	(BODY:)			{Array}	    [fields]			  Danh sách thuộc tính cần trả. 
                                                              VD: ["name","code"]. Trường hợp không có fields thì sẽ 
                                                              trả về tất cả thuộc tính
@apiUse     sale_filters                                                              
@apiParam	(Query:)		{String}	[sort]   	          Tên field trường thông tin cần sắp xếp                                                                             
@apiParam	(Query:)		{String}	[order]   	          Kiểu sắp xếp:
                                                              <ul>
                                                                <li><code>asc: </code> sắp xếp tăng dần</li>
                                                                <li><code>desc: </code> sắp xếp giảm dần</li>
                                                              </ul>               
                                                              alow value: asc, desc      

@apiUse paging_tokens

@apiParamExample {json} Body example
{
    "view_type": "KANBAN",
    "states_code": ["STT002"],
    "is_new": 1,
    "search": "",
    "fields": [
        "code",
        "name"
    ],
    "sale_filters": [
        {
            "criteria_key": "cri_reason_success",
            "operator_key": "op_is_has",
            "values": [
                "khách hàng chốt đơn"
            ]
        },
        {
            "criteria_key": "cri_estimate_time",
            "operator_key": "op_is_between",
            "values": [
                "2019-01-01",
                "2019-10-10"
            ]
        }
    ]
}

@apisuccess {Array}             data                          Danh sách đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response KANBAN View
{
    "code": 200,
    "data": [
        {
            "_id": "123131312312312312341241234",
            "assignee": [],
            "code": "EULZJO041021",
            "currency": "VNĐ",
            "deal_new": 0,
            "name": "tungnm3 06",
            "process_name": "PVCOMBANK",
            "profiles": [
                "ebc41cff-6907-4f78-baf6-4d6efe8bd009"
            ],
            "profiles_info": [
                {
                    "address": [],
                    "email": [
                        "<EMAIL>"
                    ],
                    "job": null,
                    "name": "Bạn nữ 2",
                    "phone_number": [
                        "+***********"
                    ],
                    "profile_id": "ebc41cff-6907-4f78-baf6-4d6efe8bd009"
                }
            ],
            "sale_process_id": "6141da39bd77bb76ec717417",
            "sale_value": 6,
            "state_code": "********-f1c9-42b3-bed0-cbdfb137a9a8",
            "state_name": "THU THẬP HỒ SƠ",
            "state_ratio": 40
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "****************************************************************",
            "before": ""
        },
        "page_count": 2,
        "per_page": 25,
        "total_count": 42
    }
}

@apiSuccessExample {json} Response TABLE View
{
    "code": 200,
    "data": [
        {
            "_id": "123131312312312312341241234",
            "assignee": [],
            "code": "EULZJO041021",
            "currency": "VNĐ",
            "deal_new": 0,
            "name": "tungnm3 06",
            "process_name": "PVCOMBANK",
            "profiles": [
                "ebc41cff-6907-4f78-baf6-4d6efe8bd009"
            ],
            "profiles_info": [
                {
                    "address": [],
                    "email": [
                        "<EMAIL>"
                    ],
                    "job": null,
                    "name": "Bạn nữ 2",
                    "phone_number": [
                        "+***********"
                    ],
                    "profile_id": "ebc41cff-6907-4f78-baf6-4d6efe8bd009"
                }
            ],
            "sale_process_id": "6141da39bd77bb76ec717417",
            "sale_value": 6,
            "state_code": "********-f1c9-42b3-bed0-cbdfb137a9a8",
            "state_name": "THU THẬP HỒ SƠ",
            "state_ratio": 40
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "****************************************************************",
            "before": ""
        },
        "page_count": 2,
        "per_page": 25,
        "total_count": 42
    }
}
"""

####################################################################################################
# Số lượng đơn hàng theo trạng thái FILTER
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/mobile/api/v1.0/states/filter Số lượng đơn hàng theo trạng thái ở danh sách
@apiGroup Mobile Deal
@apiVersion 1.0.1
@apiName StatesFilter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam	(BODY:)			{Array}	    [sale_process_id]     ID quy trình
@apiParam	(BODY:)			{String}	search			      Tìm kiếm đơn hàng theo tên đơn hàng, mã đơn hàng
@apiParam	(BODY:)			{Array}	    [sale_filters]		  danh sách các điều kiện lọc đơn hàng
@apiParam	(BODY:)			{String}	[source]		      Tên campaign / sub campaing

@apiUse     sale_filters                                                              

@apiParamExample {json} Body example
{
    "search": "",
    "sale_filters": [
        {
            "criteria_key": "cri_reason_success",
            "operator_key": "op_is_has",
            "values": [
                "khách hàng chốt đơn"
            ]
        },
        {
            "criteria_key": "cri_estimate_time",
            "operator_key": "op_is_between",
            "values": [
                "2019-01-01",
                "2019-10-10"
            ]
        }
    ]
}

@apisuccess {Array}             data                          Danh sách đơn hàng
@apisuccess {String}            data.state_name               Tên trạng thái đơn hàng
@apisuccess {Array}             data.states_code              Danh sách trạng thái đơn hàng tương ứng với tên
@apisuccess {Number}            data.number_deal              Số lượng đơn hàng tương ứng theo trạng thái
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi


@apiSuccessExample {json} Response Example
{
    "code": 200,
    "data": [
        {
            "number_deal": 387,
            "state_name": "GỌI",
            "states_code": [
                "GQHVL7TU",
                "JYHUEN43",
                "045SHVYF"
            ],
            "states_ratio": [
                10
            ]
        },
        {
            "number_deal": 27,
            "state_name": "TƯ VẤN",
            "states_code": [
                "c3152ffa-a707-4d23-90d8-e30ea40931dd",
                "RQYEF2MD",
                "V2UXUCS7"
            ],
            "states_ratio": [
                20
            ]
        },
        {
            "number_deal": 34,
            "state_name": "THU THẬP HỒ SƠ",
            "states_code": [
                "********-f1c9-42b3-bed0-cbdfb137a9a8",
                "5RTX99QD",
                "ZCJROZTQ"
            ],
            "states_ratio": [
                40
            ]
        },
        {
            "number_deal": 23,
            "state_name": "THẨM ĐỊNH",
            "states_code": [
                "384d5570-e340-4e26-9bc2-d293aae49d0b",
                "Y6N45GEO",
                "MG141GH1"
            ],
            "states_ratio": [
                60
            ]
        },
        {
            "number_deal": 20,
            "state_name": "PHÊ DUYỆT",
            "states_code": [
                "f2e43731-2ccf-453b-a793-b496800db110",
                "HDN0AJ5E",
                "8FJ5V8NV"
            ],
            "states_ratio": [
                80
            ]
        },
        {
            "number_deal": 20,
            "state_name": "CHỐT BÁN",
            "states_code": [
                "cd830e7b-9bfe-4754-bd6c-5cf0b2d8bc55",
                "XNH3UWNB",
                "M87LWH62"
            ],
            "states_ratio": [
                100
            ]
        },
        {
            "number_deal": 26,
            "state_name": "Thất bại",
            "states_code": [
                "9fbfdda2-8330-4345-ac54-997e7864acba",
                "BWICGHT1",
                "YCEZJUXZ"
            ],
            "states_ratio": [
                0
            ]
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}

"""

####################################################################################################
# Chi tiết đơn hàng
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {GET} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/detail  Chi tiết đơn hàng
@apiGroup Mobile Deal
@apiVersion 1.0.1
@apiName DealDetail

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apisuccess {Array}             data                          Thông tin chi tiết của đơn hàng
@apisuccess {Array}             data.profiles_info            Danh sách thông tin khách hàng
@apisuccess {Array}             data.assignee                 Thông tin nhân viên phụ trách
@apisuccess {String}            data.assignee.assignee_id     ID nhân viên
@apisuccess {String}            data.assignee.permission      Quyền của nhân viên trên đơn hàng
                                                              <ul>
                                                                <li><code>owner</code>: Nhân viên phụ trách chính 
                                                                (Chỉ 1 người phụ trách chính)</li>
                                                                <li><code>supporter</code>: Nhân viên hỗ trợ xử lý đơn hàng</li>
                                                              </ul>       

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response detail deal
{
    "code": 200,
    "data": {
        "_id": "618e3609916180df8914bda3",
        "assignee": [
            {
                "assignee_id": "e9e7a69a-1981-45b9-971f-a2f52a37d112",
                "permission": "owner"
            }
        ],
        "assignee_id": "e9e7a69a-1981-45b9-971f-a2f52a37d112",
        "brand_ids": [
            "bdee9565-3cad-40ae-b2dc-421121d70279"
        ],
        "code": "GNMPPT121121",
        "companies": [],
        "contract_time": "",
        "created_by": "cb18ab04-a673-4dce-99c6-091c1ef189ce",
        "created_merchant_id": "bdee9565-3cad-40ae-b2dc-421121d70279",
        "created_time": "2021-11-12T16:38:16Z",
        "currency": "VNĐ",
        "deal_new": 0,
        "description": "",
        "estimate_time": "",
        "id": "618e3609916180df8914bda3",
        "merchant_id": "bdee9565-3cad-40ae-b2dc-421121d70279",
        "name": "Mình là Linh nè các bạn",
        "order": **********.899148,
        "pin_to_top": 0,
        "process_name": "PVCOMBANK",
        "product_sku": null,
        "products": [],
        "profiles": [
            "61820b1e-0aa0-42b7-99d9-ae98e530a240"
        ],
        "profiles_info": [
            {
                "address": [],
                "cif": [],
                "email": [],
                "gender": null,
                "job": null,
                "name": "Bạn nam 5",
                "phone_number": [
                    "+***********"
                ],
                "profile_id": "61820b1e-0aa0-42b7-99d9-ae98e530a240",
                "profile_identify": [],
                "salary": null
            }
        ],
        "reason_fail": "",
        "reason_success": "",
        "sale_process_id": "6141da39bd77bb76ec717417",
        "sale_value": null,
        "source": "Nhập thủ công",
        "state_code": "GQHVL7TU",
        "state_name": "GỌI",
        "state_ratio": 10,
        "status": 1,
        "tag_ids": null,
        "team_id": "430e50bc-9c09-4c81-a688-c0b82c5a9805",
        "third_party_created_time": "",
        "third_party_updated_time": "",
        "tickets": [],
        "type_create": "MANUAL",
        "updated_by": "e9e7a69a-1981-45b9-971f-a2f52a37d112",
        "updated_merchant_id": "bdee9565-3cad-40ae-b2dc-421121d70279",
        "updated_time": "2021-11-12T16:55:18Z"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

####################################################################################################
# THÊM MỚI ĐƠN HÀNG VỚI KH DOANH NGHIỆP
# version: 1.0.3                                                                                #
####################################################################################################

"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/action/add/personal_customers  Thêm mới đơn hàng khách hàng cá nhân
@apiDescription             Dịch vụ thêm mới đơn hàng khách hàng cá nhân bằng tay cho mobile
@apiGroup Mobile Deal
@apiVersion 1.0.1
@apiName DealAddPersonalCustomers

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header


@apiParam	(Body:)			{String}	name			      Tên đơn hàng
@apiParam	(Body:)			{String}	sale_process_id		  ID Quy trình bán hàng
@apiParam	(Body:)			{String}	[state_code]		  Mã code trạng thái đơn hàng. Trường hợp không có thì sẽ 
                                                              lấy trạng thái đầu tiên của quy trình
@apiParam	(Body:)			{Object}	profile			      Thông tin profile
@apiParam	(Body:)			{String}	profile.name	      Tên khách hàng
@apiParam	(Body:)			{String}	profile.phone_number  Số điện thoại khách hàng
@apiParam	(Body:)			{String}	[profile.email]       Email khách hàng
@apiParam	(Body:)			{String}	[profile.address]     Địa chỉ khách hàng
@apiParam	(Body:)			{String}	[profile.job]         Ngành nghề
@apiParam	(Body:)			{Array}	    [products]		      Danh sách ID sản phẩm   
@apiParam	(Body:)			{string}	[description]		  Mô tả
@apiParam	(Body:)			{string}	[source]		      Nguồn tạo đơn hàng

@apiParamExample {json} Body example
{
    "name":"Đơn hàng demo",
    "sale_process_id":"5d7f150be6481e870a8ce0ad",
    "state_code" : "LEAD",
    "description": "Mô tả đơn hàng",
    "profile": {
        "name": "Nguyễn Văn Thông",
        "phone_number": "**********",
        "email": "<EMAIL>",
        "address": "82 Duy Tân, Cầu Giấy",
        "job": "IT",
    },
    "products": [
        "59301a75-898c-4aa0-8cd3-61798b72f038"
    ]
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "data": {
        "_id":"5dde29fda2596203036b12c4",
        "name":"Đơn hàng demo",
        "sale_process_id":"5d7f150be6481e870a8ce0ad",
        "state_code" : "LEAD",
        "description": "Mô tả đơn hàng",
        "profile": {
            "name": "Nguyễn Văn Thông",
            "phone_number": "**********",
            "email": "<EMAIL>",
            "address": "82 Duy Tân, Cầu Giấy",
            "job": "IT",
        },
        "products": [
            "59301a75-898c-4aa0-8cd3-61798b72f038"
        ]
    },
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# Thêm mới đơn hàng với khách hàng doanh nghiệp
# version: 1.0.3                                                                                #
####################################################################################################

"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/action/add/business_customers  Thêm mới đơn hàng khách hàng doanh nghiệp

@apiDescription             Dịch vụ thêm mới đơn hàng khách hàng doanh nghiệp bằng tay cho mobile
@apiGroup Mobile Deal
@apiVersion 1.0.1
@apiName DealAddBusinessCustomers

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header


@apiParam	(Body:)			{String}	name			      Tên đơn hàng
@apiParam	(Body:)			{String}	sale_process_id		  ID Quy trình bán hàng
@apiParam	(Body:)			{String}	[state_code]		  Mã code trạng thái đơn hàng. Trường hợp không có thì sẽ 
                                                              lấy trạng thái đầu tiên của quy trình
@apiParam	(Body:)			{Object}	business			      Thông tin doanh nghiệp
@apiParam	(Body:)			{String}	business.name	      Tên khách hàng doanh nghiệp
@apiParam	(Body:)			{String}	business.phone_number	      Số điện thoại của khách hàng doanh nghiệp
@apiParam	(Body:)			{String}	[business.business_identify]    Số giấy tờ của khách hàng doanh nghiệp
@apiParam	(Body:)			{String}	[business.address]     Địa chỉ khách hàng doanh nghiệp 
@apiParam	(Body:)			{String}	[business.field_of_activity]         Lĩnh vực hoạt động
@apiParam	(Body:)			{Array}	    [products]		      Danh sách ID sản phẩm   
@apiParam	(Body:)			{string}	[description]		  Mô tả
@apiParam	(Body:)			{string}	[source]		      Nguồn tạo đơn hàng

@apiParamExample {json} Body example
{
    "name":"Mobio **********",
    "sale_process_id":"619b497d89b4b34820aa57a3",
    "state_code" : "T2PHIYUN",
    "description": "Mô tả đơn hàng",
    "business": {
        "name": "Mobio",
        "phone_number": "**********",
        "address": "82 Duy Tân, Cầu Giấy",
        "business_identify": "1232913912391",
        "field_of_activity": "Lĩnh vực khác"
    },
    "products": [
        "59301a75-898c-4aa0-8cd3-61798b72f038"
    ]
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "data": {
        "_id": "619bb470acb25a0db31d118d",
        "business": {
            "_dyn_linh_vuc_hoat_dong_1637556719603": "Lĩnh vực khác",
            "address": "82 Duy Tân, Cầu Giấy",
            "business_identify": "1232913912391",
            "name": "Mobio",
            "phone_number": "**********"
        },
        "description": "Mô tả đơn hàng",
        "name": "Mobio **********",
        "products": [
            "59301a75-898c-4aa0-8cd3-61798b72f038"
        ],
        "sale_process_id": "619b497d89b4b34820aa57a3",
        "state_code": "T2PHIYUN"
    },
    "code": 200,
    "message": "request thành công."
}
"""

####################################################################################################
# Sửa đơn hàng cá nhân
# version: 1.0.4                                                                                   #
####################################################################################################
"""
@api {PUT} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/update/personal_customers  Cập nhật thông tin đơn hàng cá nhân
@apiGroup Mobile Deal
@apiVersion 1.0.1
@apiName DealUpdatePersonalCustomers

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam	(Body:)			{String}	name			      Tên đơn hàng
@apiParam	(Body:)			{String}	sale_process_id		  ID Quy trình bán hàng
@apiParam	(Body:)			{String}	[assignee_id]		      Id cán bộ bán hàng
@apiParam	(Body:)			{String}	[state_code]		  Mã code trạng thái đơn hàng. Trường hợp không có thì sẽ 
                                                              lấy trạng thái đầu tiên của quy trình
@apiParam	(Body:)			{Object}	profile			      Thông tin profile
@apiParam	(Body:)			{String}	profile.name	      Tên khách hàng
@apiParam	(Body:)			{String}	profile.phone_number  Số điện thoại khách hàng
@apiParam	(Body:)			{String}	[profile.email]       Email khách hàng
@apiParam	(Body:)			{String}	[profile.address]     Địa chỉ khách hàng
@apiParam	(Body:)			{String}	[profile.job]         Ngành nghề
@apiParam	(Body:)			{Integer}	[profile.salary]      Thu nhập
@apiParam	(Body:)			{Integer}	[profile.gender]      Giới tính
                                                             <ul>
                                                                <li><code>1</code>: Không xác định</li>
                                                                <li><code>2</code>: Nam</li>
                                                                <li><code>3</code>: Nữ</li>
                                                             </ul>
@apiParam	(Body:)			{Array}	[profile.profile_identify]     Giấy tờ cá nhân
@apiParam	(Body:)			{String}	profile.profile_identify.identify_type     Loại giấy tờ
                                                            <ul>
                                                                <li><code>driving_license</code>: Giấy phép lái xe</li>
                                                                <li><code>passport</code>: Hộ chiếu</li>
                                                                <li><code>identity_card</code>: Chứng minh nhân dân</li>
                                                                <li><code>citizen_identity</code>: Căn cước công dân</li>
                                                                <li><code>identity_card_army</code>: Chứng minh thư quân đội</li>
                                                             </ul>
@apiParam	(Body:)			{String}	profile.profile_identify.identify_value    Số giấy tờ tương ứng            
@apiParam	(Body:)			{Array}	    [products]		      Danh sách ID sản phẩm   
@apiParam	(Body:)			{string}	[description]		  Mô tả
@apiParam	(Body:)			{string}	[source]		      Nguồn tạo đơn hàng

@apiParamExample {json} Body example
{
    "name":"Thông test tạo đơn hàng Update 01",
    "sale_process_id": "6141da39bd77bb76ec717417",
    "state_code" : "LEAD",
    "assignee_id": "3ea7d0ec-b79a-4b1d-afe5-5d921f5a0689",
    "description": "Mô tả đơn hàng",
    "profile": {
        "name": "Mobio",
        "phone_number": "**********",
        "email": "<EMAIL>",
        "address": "82 Duy Tân, Cầu Giấy",
        "job": "IT",
        "gender": 1,
        "salary": 100000,
        "profile_identify": [
            {
                "identify_type": "driving_license",
                "identify_value": "112213123123123"
            }
        ]
    },
    "products": [
        "6197785ae691eaa630873960"
    ]
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "code": 200,
    "data": {
        "_id": "61a78d221f89749b7421a2d0",
        "description": "Mô tả đơn hàng",
        "assignee_id": "3ea7d0ec-b79a-4b1d-afe5-5d921f5a0689",
        "name": "Thông test tạo đơn hàng Update 01",
        "products": [
            "6197785ae691eaa630873960"
        ],
        "profile": {
            "_dyn_nganh_nghe_1637120947763": [
                "IT"
            ],
            "address": "82 Duy Tân, Cầu Giấy",
            "email": "<EMAIL>",
            "gender": 1,
            "name": "Mobio",
            "phone_number": "**********",
            "profile_identify": [
                {
                    "identify_type": "driving_license",
                    "identify_value": "112213123123123"
                }
            ],
            "salary": 100000.0,
            "source": "Sale"
        },
        "sale_process_id": "6141da39bd77bb76ec717417",
        "state_code": "GQHVL7TU"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""


####################################################################################################
# Sửa đơn hàng doanh nghiệp
# version: 1.0.4                                                                                   #
####################################################################################################
"""
@api {PUT} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/update/business_customers  Cập nhật thông tin đơn hàng khách hàng doanh nghiệp
@apiGroup Mobile Deal
@apiVersion 1.0.1
@apiName DealUpdateBusinessCustomers

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header


@apiParam	(Body:)			{String}	name			      Tên đơn hàng
@apiParam	(Body:)			{String}	sale_process_id		  ID Quy trình bán hàng
@apiParam	(Body:)			{String}	[assignee_id]		      Id cán bộ bán hàng
@apiParam	(Body:)			{String}	[state_code]		  Mã code trạng thái đơn hàng. Trường hợp không có thì sẽ 
                                                              lấy trạng thái đầu tiên của quy trình
@apiParam	(Body:)			{Object}	business			      Thông tin doanh nghiệp
@apiParam	(Body:)			{String}	business.name	      Tên khách hàng doanh nghiệp
@apiParam	(Body:)			{String}	business.phone_number	      Số điện thoại của khách hàng doanh nghiệp
@apiParam	(Body:)			{String}	[business.email]      Email
@apiParam	(Body:)			{String}	[business.business_identify]    Số giấy tờ của khách hàng doanh nghiệp
@apiParam	(Body:)			{String}	[business.address]     Địa chỉ khách hàng doanh nghiệp 
@apiParam	(Body:)			{String}	[business.field_of_activity]         Lĩnh vực hoạt động
@apiParam	(Body:)			{String}	[business.customer_classification]   Phân loại khách hàng
@apiParam	(Body:)			{String}	[business.deputy]                    Người đại diện
@apiParam	(Body:)			{String}	[business.position]                  Chức vụ
@apiParam	(Body:)			{Array}	    [products]		      Danh sách ID sản phẩm   
@apiParam	(Body:)			{string}	[description]		  Mô tả
@apiParam	(Body:)			{string}	[source]		      Nguồn tạo đơn hàng

@apiParamExample {json} Body example
{
    "name":"Mobio **********",
    "sale_process_id":"619b497d89b4b34820aa57a3",
    "assignee_id": "3ea7d0ec-b79a-4b1d-afe5-5d921f5a0689",
    "state_code" : "T2PHIYUN",
    "description": "Mô tả đơn hàng",
    "business": {
        "name": "Mobio",
        "phone_number": "**********",
        "address": "82 Duy Tân, Cầu Giấy",
        "business_identify": "1232913912391",
        "field_of_activity": "Lĩnh vực khác"
    },
    "products": [
        "59301a75-898c-4aa0-8cd3-61798b72f038"
    ]
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "data": {
        "_id": "619bb470acb25a0db31d118d",
        "assignee_id": "3ea7d0ec-b79a-4b1d-afe5-5d921f5a0689",
        "business": {
            "_dyn_linh_vuc_hoat_dong_1637556719603": "Lĩnh vực khác",
            "address": "82 Duy Tân, Cầu Giấy",
            "business_identify": "1232913912391",
            "name": "Mobio",
            "phone_number": "**********"
        },
        "description": "Mô tả đơn hàng",
        "name": "Mobio **********",
        "products": [
            "59301a75-898c-4aa0-8cd3-61798b72f038"
        ],
        "sale_process_id": "619b497d89b4b34820aa57a3",
        "state_code": "T2PHIYUN"
    },
    "code": 200,
    "message": "request thành công."
}
"""


####################################################################################################
# Gán profile vào Deal
# version: 1.0.1                                                                                   #
####################################################################################################

"""
@api {POST} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/actions/add_profile  Gán thêm profile cho Deal
@apiGroup Mobile Deal
@apiVersion 1.0.1
@apiName dealAddProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header

@apiParam (Body) {Array} profiling_profile_ids Danh sách profile_id

@apiParamExample {json} Body example
{
    "profiling_profile_ids" : ["5da972766a6aae17ad96f7b5", "5da9725e6a6aae17ad96f7b4"]
}

@apiSuccessExample {json} Response
{
    "code": 200,
    "message": "Cập nhật deal thành công."
}
"""

####################################################################################################
# Sửa đơn hàng
# version: 1.0.4                                                                                   #
####################################################################################################
"""
@api {PUT} {domain}/sale/mobile/api/v1.0/deals/<deal_id>/update/customers  Cập nhật thông tin đơn hàng v2(khôn phân biệt doanh nghiệp, cá nhân)
@apiGroup Mobile Deal
@apiVersion 1.0.1
@apiName DealUpdateCustomers

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse json_header


@apiParam	(Body:)			{String}	name			      Tên đơn hàng
@apiParam	(Body:)			{String}	sale_process_id		  ID Quy trình bán hàng
@apiParam	(Body:)			{String}	[assignee_id]		  Id cán bộ bán hàng
@apiParam	(Body:)			{String}	[state_code]		  Mã code trạng thái đơn hàng. Trường hợp không có thì sẽ 
                                                              lấy trạng thái đầu tiên của quy trình
@apiParam	(Body:)			{Object}	profile			      Thông tin doanh nghiệp
@apiParam	(Body:)			{String}	profile.name	      Tên khách hàng doanh nghiệp
@apiParam	(Body:)			{String}	profile.phone_number  Số điện thoại của khách hàng doanh nghiệp
@apiParam	(Body:)			{String}	[profile.email]       Email
@apiParam	(Body:)			{String}	[profile.cif]         Cif
@apiParam	(Body:)			{String}	[profile.address]     Địa chỉ khách hàng doanh nghiệp 
@apiParam	(Body:)			{String}	[profile.profile_identify]     Số giấy tờ
@apiParam	(Body:)			{String}	profile.profile_identify.identify_type     Loại giấy tờ
                                                            <ul>
                                                                <li><code>driving_license</code>: Giấy phép lái xe</li>
                                                                <li><code>passport</code>: Hộ chiếu</li>
                                                                <li><code>identity_card</code>: Chứng minh nhân dân</li>
                                                                <li><code>citizen_identity</code>: Căn cước công dân</li>
                                                                <li><code>identity_card_army</code>: Chứng minh thư quân đội</li>
                                                             </ul>
@apiParam	(Body:)			{int}	    [profile.income]           Thu nhập
@apiParam	(Body:)			{int}	[profile.gender]           Giới tính
@apiParam	(Body:)			{String}	[profile.job]              Ngành nghề
@apiParam	(Body:)			{String}	[profile.tax_code]         Mã số thuế
@apiParam	(Body:)			{int}	    [profile.revenue]          Doanh thu
@apiParam	(Body:)			{String}	[profile.field_of_activity]         Lĩnh vực hoạt động
@apiParam	(Body:)			{String}	[profile.customer_classification]   Phân loại khách hàng
@apiParam	(Body:)			{String}	[profile.deputy]                    Người đại diện
@apiParam	(Body:)			{String}	[profile.position]                  Chức vụ
@apiParam	(Body:)			{Array}	    [products]		      Danh sách ID sản phẩm   
@apiParam	(Body:)			{String}	[description]		  Mô tả
@apiParam	(Body:)			{String}	[source]		      Nguồn tạo đơn hàng

@apiParamExample {json} Body example
{
    "name": "KHCN + mailto:0987548484",
    "sale_process_id": "6141da39bd77bb76ec717417",
    "assignee_id": "6a27038b-b829-4844-b1b8-ba9fa3517bd2",
    "profile": {
        "name": "Nguyen Danh Luong",
        "phone_number": "+***********",
        "email": "<EMAIL>",
        "cif": "4307204530",
        "address": "My dinh",
        "paper_number": "new",
        "income": 8374664,
        "gender": 1,
        "job": "Thủy thủ",
        "revenue": 1000000,
        "field_of_activity": "coder",
        "customer_classification": "business",
        "deputy": "Nguyen Luong",
        "position": "Nhan vien"
    },
    "products": [
        "619daf75c9ff6cfd880c9880"
    ],
    "description": "",
    "source": "Nhập thủ công",
    "state_code": "GQHVL7TU"
}

@apiSuccess {Array}             data                          Thông tin đơn hàng
@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi

@apiSuccessExample {json} Response success
{
    "code": 200,
    "data": {
        "_id": "61b9b9d8fcd5a497d359987e",
        "assignee_id": "6a27038b-b829-4844-b1b8-ba9fa3517bd2",
        "description": "",
        "name": "KHCN + mailto:0987548484",
        "products": [
            "619daf75c9ff6cfd880c9880"
        ],
        "profile": {
            "address": "My dinh",
            "cif": "4307204530",
            "customer_classification": "business",
            "deputy": "Nguyen Luong",
            "email": "<EMAIL>",
            "field_of_activity": "coder",
            "gender": 1,
            "income": 8374664,
            "job": "Thủy thủ",
            "name": "Nguyen Danh Luong",
            "paper_number": "new",
            "phone_number": "+***********",
            "position": "Nhan vien",
            "revenue": 1000000
        },
        "sale_process_id": "6141da39bd77bb76ec717417",
        "source": "Nhập thủ công",
        "state_code": "GQHVL7TU"
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

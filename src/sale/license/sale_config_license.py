#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: nguyenthong
    Company: M O B I O
    Date Created: 25/01/2024
"""
# --------------- <PERSON><PERSON><PERSON> hình thông tin license ----

"""
@api {GET} {domain}/sale/api/v1.0/license/config L<PERSON>y danh sách cấu hình license của Sale
@apiGroup Sale License
@apiVersion 1.0.0
@apiName SaleLicenseConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam  (Param)     {String}    code            Mã config
                                                   <ul>
                                                        <li><code>deal_auto_trigger</code> : Cho phép user sử dụng 1 số trigger</li>
                                                        <li><code>deal_assignment_node</code> : Giới hạn số node đ<PERSON><PERSON><PERSON> sử dụng trong luật</li>
                                                   </ul>
            
@apiSuccess {Object}            data               data báo cáo
@apiSuccess {String}            message            <PERSON><PERSON> tả phản hồi
@apiSuccess {Integer}           code               Mã phản hồi
@apiSuccess (data)     {Array}  config_data        Thông tin được dữ liệu được phép sử dụng (Tương ứng với code)
@apiSuccess (data)     {Array}  config_data.code   Mã code
@apiSuccess (data)     {Number}  config_data.status   Trạng thái
                                                      <ul>
                                                        <li><code>1</code>: Allow</li>
                                                        <li><code>0</code>: Denied</li>
                                                      <ul>


@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "config_data": [
            {
                "code": 'trigger_deal_created',
                "status": 1,
            },
            {
                "code": 'trigger_close_deal_fail_reason',
                "status": 0,
            }
        ]
    }
    "lang": "vi",
    "message": "request thành công."
}
"""
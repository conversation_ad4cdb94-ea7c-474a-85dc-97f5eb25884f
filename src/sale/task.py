#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
'''
"""
@api {POST} {domain}/sale/api/v1.0/task/actions/filter    L<PERSON><PERSON> danh sách các Task
@apiGroup Task
@apiVersion 1.0.0
@apiName ListTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header
@apiUse paging

@apiParam (Param)    {String=asc,desc} [order]        Ki<PERSON><PERSON> sắp xếp
@apiParam (Param)    {String=title, created_time, updated_time}   [sort] Sắp xếp theo các trường chỉ định. 

@apiParam  (Body:)     {String}     [search_text]      Text cần tìm kiếm.
@apiParam  (Body:)     {Array[]}    [status_ids]             L<PERSON><PERSON> danh sách Task theo <code>status</code>
@apiParam  (Body:)     {Array[]}    [priority_ids]             <PERSON><PERSON><PERSON> da<PERSON> sách Task theo <code><PERSON><PERSON> <PERSON>u tiên</code>
@apiParam  (Body:)     {Array[]}    [assignee_ids]             L<PERSON><PERSON> danh sách Task theo <code>người được assignee</code>
@apiParam  (Body:)     {String}    [start_time]        Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%M:%SZ</code>)
@apiParam  (Body:)     {String}    [end_time]          Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%M:%SZ</code>)

@apiSuccess {Object}             data              Danh sách các Task
@apiSuccess {String}            message            Mô tả phản hồi
@apiSuccess {Integer}           code               Mã phản hồi


@apiSuccess (data)  {String}     id                              <code>ID</code> của Task
@apiSuccess (data)  {String}    title                           Tiêu đề của Task
@apiSuccess (data)  {String}    description                     Nội dung của Task
@apiSuccess (data)  {Array[]}   assignee_ids                    Danh sách staff_id được assignee
@apiSuccess (data)  {String}    estimate_time                   Thời gian dự kiến hoàn thành Task (Format: <code>%Y-%m-%dT%H:%M:%SZ</code>)
@apiSuccess (data)  {String}    status_id                       Trạng thái của Task
@apiSuccess (data)  {String}    priority_id                     Độ ưu tiên của Task
@apiSuccess (data)  {String}    type_id                         Kiểu Task
@apiSuccess (data)  {String}    deal_ids                        Danh sách các đơn hàng được gán.
@apiSuccess (data)  {String}    updated_time                    Thời gian cập nhật 

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "id": "60ffb75601c97db90579ae17",
            "title" "Task",
            "description": "",
            "assignee_ids": [],
            "estimate_time": "2021-07-27T07:35:50Z",
            "status_id": "60ffac984748c0f6c8d352ad",
            "priority_id": "60ffac984748c0f6c8d352bc",
            "type_id": "60ffac984748c0f6c8d352ef",
            "deal_ids" : ["60fe92a04748c0f6c818f6cc", "60fe92a04748c0f6c818f6c4"],
            "created_time": "2021-07-27T07:35:50Z", 
            "updated_time": "2021-07-27T07:35:50Z"
        }
    ]
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {POST} {domain}/sale/api/v1.0/tasks    Tạo Task
@apiGroup Task
@apiVersion 1.0.0
@apiName CreateTask

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam (Body:)  {String}    title                           Tiêu đề của Task
@apiParam (Body:)  {String}    description                     Nội dung của Task
@apiParam (Body:)  {Array[]}   assignee_ids                    Danh sách staff_id được assignee
@apiParam (Body:)  {String}    estimate_time                   Thời gian dự kiến hoàn thành Task (Format: <code>%Y-%m-%dT%H:%M:%SZ</code>)
@apiParam (Body:)  {String}    status_id                       Trạng thái của Task
@apiParam (Body:)  {String}    priority_id                     Độ ưu tiên của Task
@apiParam (Body:)  {String}    type_id                         Kiểu Task
@apiParam (Body:)  {String}    deal_ids                        Danh sách các đơn hàng được gán. 

@apiSuccess {Object}             data              Thông tin của Task được tạo.
@apiSuccess {String}            message            Mô tả phản hồi
@apiSuccess {Integer}           code               Mã phản hồi


@apiSuccessExample {json} Response
{
    "code": 200,
    "data": {
        "created_time": "2021-07-27T07:35:50Z",
        "id": "60ffb75601c97db90579ae17"
    }
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} {domain}/sale/api/v1.0/task_state    Lấy danh sách state của Task
@apiGroup Task
@apiVersion 1.0.0
@apiName TaskState

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess {Array[]}             data             Danh sách State của Task.
@apiSuccess {String}            message            Mô tả phản hồi
@apiSuccess {Integer}           code               Mã phản hồi

@apiSuccess (data)  {String}   deal_total.code                Mã trạng thái
@apiSuccess (data)  {String}   deal_total.name                Tên trạng thái

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "id": "60ffb75601c97db90579ae17",
            "name": {
                "vi": "Hoàn thành",
                "en": "Success"
            }
        }
    ]
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} {domain}/sale/api/v1.0/task_state    Lấy danh sách state của Task
@apiGroup Task
@apiVersion 1.0.0
@apiName TaskState

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess {Array[]}             data             Danh sách State của Task.
@apiSuccess {String}            message            Mô tả phản hồi
@apiSuccess {Integer}           code               Mã phản hồi

@apiSuccess (data)  {String}   id                  Id của State
@apiSuccess (data)  {Object}   name                Tên của State
@apiSuccess (data)  {String}   name.language       Tên đa ngôn ngữ của State

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "id": "60ffb75601c97db90579ae17",
            "name": {
                "vi": "Hoàn thành",
                "en": "Success"
            }
        }
    ]
    "lang": "vi",
    "message": "request thành công."
}
"""


"""
@api {GET} {domain}/sale/api/v1.0/task_type    Lấy danh sách type của Task
@apiGroup Task
@apiVersion 1.0.0
@apiName TaskType

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess {Array[]}             data             Danh sách Type của Task.
@apiSuccess {String}            message            Mô tả phản hồi
@apiSuccess {Integer}           code               Mã phản hồi

@apiSuccess (data)  {String}   id                  Id của Type
@apiSuccess (data)  {String}   staff_id                  Id của Nhân viên
@apiSuccess (data)  {Object}   name                Tên của Type
@apiSuccess (data)  {String}   name.language       Tên đa ngôn ngữ của Type

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "id": "60ffb75601c97db90579ae17",
            "name": {
                "vi": "Sale",
                "en": "Sale"
            }
        }
    ]
    "lang": "vi",
    "message": "request thành công."
}
"""

"""
@api {GET} {domain}/sale/api/v1.0/task_priority    Lấy danh sách độ ưu tiên của Task
@apiGroup Task
@apiVersion 1.0.0
@apiName TaskPriority

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess {Array[]}             data             Danh sách Priority của Task.
@apiSuccess {String}            message            Mô tả phản hồi
@apiSuccess {Integer}           code               Mã phản hồi

@apiSuccess (data)  {String}   id                  Id của Priority
@apiSuccess (data)  {Object}   name                Tên của Priority
@apiSuccess (data)  {String}   name.language       Tên đa ngôn ngữ của Priority
@apiSuccess (data)  {String}   ma_color            Mã màu của Độ ưu tiên    

@apiSuccessExample {json} Response
{
    "code": 200,
    "data": [
        {
            "id": "60ffb75601c97db90579ae17",
            "name": {
                "vi": "High",
                "en": "Sale"
            },
            "ma_color": "#12361"
        }
    ]
    "lang": "vi",
    "message": "request thành công."
}
"""
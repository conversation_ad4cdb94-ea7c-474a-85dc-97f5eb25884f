*********************************** Create Audience ************************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {post} /audiences.json Create Audience
@apiGroup Audiences
@apiVersion 1.0.0
@apiName CreateAudience
@apiDescription Tạo Audience.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {string} ads_acc_ids   Mảng danh sách Ads Account Ids được sử dụng audience
@apiParam (Body:)  {string} name   Tên của audience
@apiParam (Body:)  {string} type   Loại audience: custom, lookalike
@apiParam (Body:)  {string} source   Nguồn audience: file, attributes, marketing
@apiParam (Body:)  {int} estimate_number   Số lượng profile ước tính của audience
@apiParam (Body:)  {object} file   File được upload trong trường hợp type là custom và source là file
@apiParam (Body:)  {string} audience_id   Profiling Audience ID trong trường hợp type là custom và source là attributes hoặc marketing
@apiParam (Body:)  {string} origin_audience_id   Ads Audience ID trong trường hợp  type là lookalike
@apiParam (Body:)  {int} ratio   Tỉ lệ tăng trong trường hợp type là lookalike
@apiParam (Body:)  {string} mkt_campaign_id   ID của chiến dịch marketing
@apiParam (Body:)  {string} mkt_campaign_name   Tên của chiến dịch marketing
@apiParam (Body:)  {object} mkt_campaign_filter_data   Dữ liệu bộ lọc của chiến dịch marketing
@apiParam (Body:)  {int} mkt_campaign_status   Trạng thái của chiến dịch marketing

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "audience": {
            "audience_type": "custom",
            "created_staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2019-07-23T04:11:38Z",
            "estimate_number": "3000",
            "id": "aab07ba3-781d-4b41-98f9-387799c6d903",
            "last_updated_staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "name": "test1",
            "session_id": null,
            "source_type": "file",
            "status": "created",
            "tenant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "updated_time": null
        }
    "message": "request thành công."
}
"""


*********************************** Update Audiences ************************************
* version: 1.1.0                                                                       *
****************************************************************************************
"""
@api {put} /audiences.json Update Audience
@apiGroup Audiences
@apiVersion 1.1.0
@apiName UpdateAudience
@apiDescription Sửa Audience bởi Marketing Module.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {string} type   Loại audience: custom, lookalike
@apiParam (Body:)  {string} source   Nguồn audience: file, attributes, marketing
@apiParam (Body:)  {string} mkt_campaign_id   ID của chiến dịch marketing
@apiParam (Body:)  {int} mkt_campaign_status   Trạng thái của chiến dịch marketing

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {}
    "message": "request thành công."
}
"""


*********************************** Update Audience ************************************
* version: 1.1.0                                                                       *
****************************************************************************************
"""
@api {get} /audiences/<audience_id>.json Get Audience
@apiGroup Audience
@apiVersion 1.1.0
@apiName UpdateAudience
@apiDescription Sửa Audience.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {string} ads_acc_ids   Mảng danh sách Ads Account Ids được sử dụng audience
@apiParam (Body:)  {string} type   Loại audience: custom, lookalike
@apiParam (Body:)  {string} source   Nguồn audience: file, attributes, marketing

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {}
    "message": "request thành công."
}
"""


************************************ Get Audiences *************************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {get} /audiences.json Get Audiences
@apiGroup Audiences
@apiVersion 1.0.0
@apiName GetAudiences
@apiDescription Lấy danh sách Audience.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "audiences": [
            {
                "audience_type": "custom",
                "created_staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                "created_time": "Tue, 23 Jul 2019 04:10:34 GMT",
                "estimate_number": 3000,
                "id": "07760442-072e-48d0-989c-9b1d89489f1c",
                "last_updated_staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                "name": "test1",
                "source_type": "file",
                "status": "processing",
                "updated_time": null
            },
            {
                "audience_type": "custom",
                "created_staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                "created_time": "Tue, 23 Jul 2019 04:10:56 GMT",
                "estimate_number": 3000,
                "id": "69bdd908-f17b-4445-a749-00491d1aa8e7",
                "last_updated_staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                "name": "test1",
                "source_type": "file",
                "status": "processing",
                "updated_time": null
            },
            {
                "audience_type": "custom",
                "created_staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                "created_time": "Tue, 23 Jul 2019 04:11:38 GMT",
                "estimate_number": 3000,
                "id": "aab07ba3-781d-4b41-98f9-387799c6d903",
                "last_updated_staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
                "name": "test1",
                "source_type": "file",
                "status": "processing",
                "updated_time": null
            }
        ]
    },
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 15,
        "total_count": 2,
        "total_page": 1
    }
}
"""

************************************ Get Audience *************************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {get} /audiences/<ads_acc_id>.json Get Audience
@apiGroup Audiences
@apiVersion 1.0.0
@apiName GetAudience
@apiDescription Lấy chi tiết Audience.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    audience_id                              ID của audience.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "audience": {
            "audience_type": "custom",
            "created_staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "created_time": "2019-07-23T04:10:34Z",
            "estimate_number": 3000,
            "id": "07760442-072e-48d0-989c-9b1d89489f1c",
            "last_updated_staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
            "name": "test1",
            "session_id": null,
            "source_type": "file",
            "status": "processing",
            "tenant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
            "updated_time": null
        }
    },
    "message": "request thành công."
}
"""

********************************** Delete Audiences ************************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {delete} /audiences.json Delete Audiences
@apiGroup Audiences
@apiVersion 1.0.0
@apiName DeleteAudiences
@apiDescription Xóa audience.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {object} audience_ids   Mảng danh sách Audience Ids

@apiParamExample    {json}  Body example:
{
	"audience_ids": ["07760442-072e-48d0-989c-9b1d89489f1c"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "result": True,
    "message": "request thành công."
}
"""

******************************** Add Audience Profiles *********************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {post} /audiences/<session_id>/profiles.json Add Audience Profiles
@apiGroup Audiences
@apiVersion 1.0.0
@apiName AddAudienceProfiles
@apiDescription Profiling and Lake add more profiles by session id.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    session_id                              ID của audience.

@apiParamExample    {json}  Body example:
{
  "profiles": [
      {
        "profile_id": "126a1b55-64af-419c-a549-34d2465e02e7",
        "merchant_id": "4aad837e-9eb0-4015-a0b4-f4a5d12dcb4b",
        "email": [
            "<EMAIL>",
            "<EMAIL>"
        ],
        "phone_number": [
            "+84329809111",
            "+84832201222"
        ],
        "primary_email": {
            "email": "<EMAIL>",
            "last_check": "2019-07-17T06:46:50.000Z",
            "status": 1
        },
        "primary_phone": {
            "last_verify": "2019-08-07T03:57:28.000Z",
            "phone_number": "+84329809111",
            "status": 0
        },
        "secondary_emails": {
            "secondary": [
                "<EMAIL>",
                "<EMAIL>"
            ],
            "secondary_size": 2
        },
        "secondary_phones": {
            "secondary": [
                "+84377873333",
                "+84354520444"
            ],
            "secondary_size": 2
        },
        "social_user": [
          {
            "social_id": "1234567",
            "social_type": 1
          }
        ]
      },
      {
        "profile_id": "94305062-5a35-4b16-a1a3-af814a533b1e",
        "merchant_id": "4aad837e-9eb0-4015-a0b4-f4a5d12dcb4b",
        "email": "<EMAIL>",
        "phone_number": "0978888888"
        "social_user": [
          {
            "social_id": "12345678",
            "social_type": 1
          }
        ]
      }
    ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công."
}
"""

************************************ Get Audience Assignee *****************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {get} /audiences/<audience_id>/assignees.json Get Audience Assignee
@apiGroup Audiences
@apiVersion 1.0.0
@apiName GetAudienceAssignee
@apiDescription Lấy thông tin tài khoản quảng cáo gắn với Audience

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    audience_id                              ID của audience.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "assignee_ids": [
      "act_369651644"
    ],
    "assignees": [
      {
        "account_status": "ACTIVE",
        "ads_acc_id": "act_369651644",
        "ads_type": "facebook",
        "audience_sync_status": "done", //Đồng bộ dữ liệu audience lên facebook: "created": Đang xử lý, "done": Thành công, "create_to_fb_failed": Thất bại
        "business_id": "****************",
        "created_time": "24/09/2019 11:29",
        "disable_reason": "NONE",
        "name": "Le Hung",
        "status": "ENABLED",
        "token_valid": true,
        "tos_accepted": true,
        "updated_time": "12/02/2020 16:52"
      }
    ]
  },
  "message": "request thành công."
}
"""

**************************** Listen Trigger Audience Exclusion ************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {post} /audiences/<audience_id>/exclusion.json Listen Trigger Audience Exxclusion
@apiGroup Audiences
@apiVersion 1.0.0
@apiName ListenTriggerAudienceExxclusion
@apiDescription Nhận thông tin Profile có chuyển đổi để cập nhật vào tập Audience Exclusion

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    audience_id                              ID của audience.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "assignee_ids": [
      "act_369651644"
    ],
    "assignees": [
      {
        "account_status": "ACTIVE",
        "ads_acc_id": "act_369651644",
        "ads_type": "facebook",
        "audience_sync_status": "done", //Đồng bộ dữ liệu audience lên facebook: "created": Đang xử lý, "done": Thành công, "create_to_fb_failed": Thất bại
        "business_id": "****************",
        "created_time": "24/09/2019 11:29",
        "disable_reason": "NONE",
        "name": "Le Hung",
        "status": "ENABLED",
        "token_valid": true,
        "tos_accepted": true,
        "updated_time": "12/02/2020 16:52"
      }
    ]
  },
  "message": "request thành công."
}
"""

************************************ Check if audience can be deleted or not ***********
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {get} /audiences/<audience_id>/checking.json Check if audience can be deleted or not
@apiGroup Audiences
@apiVersion 1.0.0
@apiName CheckAudienceDeleted
@apiDescription Kiểm tra xem tập Audience có được phép xóa hay không

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "available": false,
}
"""

******************************** Add Audience Get Profiles Estimate Number *********************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {post} /audiences/estimate Get Profiles Est
@apiGroup Audiences
@apiVersion 1.0.0
@apiName ProfileEstNumber
@apiDescription Get profiles est number by profile_filter.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParamExample    {json}  Body example:
{
  "audiences_filter": [
    {
      "profile_filter": [
        {
          "criteria_key": "cri_identify_code",
          "operator_key": "op_is_not_empty",
          "values": [
          ]
        },
        {
          "criteria_key": "cri_profile_group",
          "operator_key": "op_is_in",
          "values": [
            "541005c4-3dff-4063-8e77-66cc140015b8"
          ]
        }
      ],
      
      "position": 0,
      "operator": null
    }
  ],
  "profile_scan_filter": null,
  "version": 2
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "estimate": 187,
    "lang": "en",
    "message": "request successful."
}
"""

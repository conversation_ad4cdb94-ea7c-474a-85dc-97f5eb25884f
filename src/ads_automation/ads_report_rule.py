********************************* Get Report Rule **************************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {get} /ads_report_rule/fb/list.json Get Ads Report Rule
@apiGroup AdsReportRule
@apiVersion 1.0.1
@apiName GetAdsReportRule
@apiDescription Lấy danh sách báo cáo quy tắc.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_account_id   Mã định danh tài khoản quảng cáo
@apiParam   (Query:)  {string}  [fb_rule_id]   <PERSON><PERSON> quy tắc cần lấy
@apiParam   (Query:)  {string}    [after]    Token để lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)  {string}    [before]    Token để lấy dữ liệu trang trước đó.
@apiParam   (Query:)  {int}  limit   Giới hạn số bản ghi trên mỗi trang

@apiSuccess {string} check_time     Thời điểm kiểm tra
@apiSuccess {string} fb_rule_id     Mã định danh quy tắc
@apiSuccess {string} rule_ads_objective_type     Loại đối tượng quảng cáo đc áp dụng trong rule
@apiSuccess {string} rule_content_action     Nội dung hành động của quy tắc
@apiSuccess {string} rule_content_condition     Nội dung điều kiện của quy tắc
@apiSuccess {string} rule_name     Tên quy tắc
@apiSuccess {string} rule_result     Kết quả của quy tắc
@apiSuccess {string} total_changed     Tổng số đối tượng thay đôi
@apiSuccess {string} total_not_changed     Tổng số đối tượng không thay đổi
@apiSuccess {array}  objectives             Danh sách chi tiết các đối tượng áp dụng trong rule
@apiSuccess {string} objectives..object_id  Mã đối tượng
@apiSuccess {string} objectives..object_name  Tên đối tượng
@apiSuccess {string} objectives..object_result  Kết quả kiểm tra
@apiSuccess {string} objectives..object_status  Trạng thái đối tượng

@apiSuccessExample     {json}    Response Report rule: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "check_time": "2019-08-20T11:30:27Z",
            "fb_rule_id": "6141992275155",
            "objectives": [],
            "rule_ads_objective_type": "adset",
            "rule_content_action": "",
            "rule_content_condition": "",
            "rule_name": "Test 20/08/2019",
            "rule_result": "PAUSE",
            "total_changed": 0,
            "total_not_changed": 0
        },
        {
            "check_time": "2019-08-20T11:00:28Z",
            "fb_rule_id": "6141992275155",
            "objectives": [],
            "rule_ads_objective_type": "adset",
            "rule_content_action": "",
            "rule_content_condition": "",
            "rule_name": "Test 20/08/2019",
            "rule_result": "PAUSE",
            "total_changed": 0,
            "total_not_changed": 0
        },
        {
            "check_time": "2019-08-20T10:30:46Z",
            "fb_rule_id": "6141992275155",
            "objectives": [
                {
                    "object_id": "6069644894555",
                    "object_name": "Post: \"90% Phụ Nữ thích \"BÓP NHẸ\".... 10% còn lại thích...\"",
                    "object_result": "PAUSED",
                    "object_status": "unactive"
                },
                {
                    "object_id": "6068846450955",
                    "object_name": "Post: \"\"Của vợ anh.... nó cỡ em\"!!!\"",
                    "object_result": "PAUSED",
                    "object_status": "unactive"
                },
                {
                    "object_id": "6068425319555",
                    "object_name": "Post: \"Điiiii mà.... Anh!!!\"",
                    "object_result": "PAUSED",
                    "object_status": "unactive"
                }
            ],
            "rule_ads_objective_type": "adset",
            "rule_content_action": "",
            "rule_content_condition": "",
            "rule_name": "Test 20/08/2019",
            "rule_result": "PAUSE",
            "total_changed": 3,
            "total_not_changed": 0
        }
    ],
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "****************************",
            "before": "**********************"
        }
    }
}
"""
=====================
"""
@api {get} /ads_report_rule/fb/list.json Get Ads Report Rule
@apiGroup AdsReportRule
@apiVersion 1.0.1
@apiName GetAdsReportRule
@apiDescription Lấy danh sách báo cáo quy tắc.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_account_id   Mã định danh tài khoản quảng cáo
@apiParam   (Query:)  {string}  [fb_rule_id]   Mã quy tắc cần lấy
@apiParam   (Query:)  {string}    [after]    Token để lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)  {string}    [before]    Token để lấy dữ liệu trang trước đó.
@apiParam   (Query:)  {int}  limit   Giới hạn số bản ghi trên mỗi trang

@apiSuccess {string} check_time     Thời điểm kiểm tra
@apiSuccess {string} fb_rule_id     Mã định danh quy tắc
@apiSuccess {string} rule_ads_objective_type     Loại đối tượng quảng cáo đc áp dụng trong rule
@apiSuccess {string} rule_content     Nội dung quy tắc
@apiSuccess {string} rule_name     Tên quy tắc
@apiSuccess {string} rule_result     Kết quả của quy tắc
@apiSuccess {string} total_changed     Tổng số đối tượng thay đôi
@apiSuccess {string} total_not_changed     Tổng số đối tượng không thay đổi
@apiSuccess {array}  objectives             Danh sách chi tiết các đối tượng áp dụng trong rule
@apiSuccess {string} objectives..object_id  Mã đối tượng
@apiSuccess {string} objectives..object_name  Tên đối tượng
@apiSuccess {string} objectives..object_result  Kết quả kiểm tra
@apiSuccess {string} objectives..object_status  Trạng thái đối tượng

@apiSuccessExample     {json}    Response Report rule: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "check_time": "2019-08-20T11:30:27Z",
            "fb_rule_id": "6141992275155",
            "objectives": [],
            "rule_ads_objective_type": "adset",
            "rule_content": "",
            "rule_name": "Test 20/08/2019",
            "rule_result": "PAUSE",
            "total_changed": 0,
            "total_not_changed": 0
        },
        {
            "check_time": "2019-08-20T11:00:28Z",
            "fb_rule_id": "6141992275155",
            "objectives": [],
            "rule_ads_objective_type": "adset",
            "rule_content": "",
            "rule_name": "Test 20/08/2019",
            "rule_result": "PAUSE",
            "total_changed": 0,
            "total_not_changed": 0
        },
        {
            "check_time": "2019-08-20T10:30:46Z",
            "fb_rule_id": "6141992275155",
            "objectives": [
                {
                    "object_id": "6069644894555",
                    "object_name": "Post: \"90% Phụ Nữ thích \"BÓP NHẸ\".... 10% còn lại thích...\"",
                    "object_result": "PAUSED",
                    "object_status": "unactive"
                },
                {
                    "object_id": "6068846450955",
                    "object_name": "Post: \"\"Của vợ anh.... nó cỡ em\"!!!\"",
                    "object_result": "PAUSED",
                    "object_status": "unactive"
                },
                {
                    "object_id": "6068425319555",
                    "object_name": "Post: \"Điiiii mà.... Anh!!!\"",
                    "object_result": "PAUSED",
                    "object_status": "unactive"
                }
            ],
            "rule_ads_objective_type": "adset",
            "rule_content": "",
            "rule_name": "Test 20/08/2019",
            "rule_result": "PAUSE",
            "total_changed": 3,
            "total_not_changed": 0
        }
    ],
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "****************************",
            "before": "**********************"
        }
    }
}
"""
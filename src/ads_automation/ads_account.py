********************************* Get Ads Accounts *************************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {get} /ads-accounts.json Get Ads Accounts
@apiGroup AdsAccounts
@apiVersion 1.0.0
@apiName GetAdsAccounts
@apiDescription Lấy danh sách tài khoản quảng cáo.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_type   Loại quảng cáo
@apiParam   (Query:)  {string}  valid_state   Trạng thái hợp lệ
@apiParam   (Query:)  {string}  ads_acc_status  Trạng thái tài khoản quảng cáo
@apiParam   (Query:)  {string}  merchant_ids   Danh sách Tenant ID
@apiParam   (Query:)  {string}  key_value   Từ khóa quảng cáo
@apiParam   (Query:)  {int}  order_by   Trường dùng để  sắp xếp
@apiParam   (Query:)  {int}  order_type   Thứ tự dùng để  sắp xếp
@apiParam   (Query:)  {int}  page   Trang
@apiParam   (Query:)  {int}  limit   Giới hạn số tài khoản trên mỗi trang


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "ads_accounts": [
            {
                "account_status": "ACTIVE",
                "ads_acc_id": "act_49755564",
                "business_id": null,
                "created_time": "Tue, 09 Jul 2019 09:30:53 GMT",
                "disable_reason": "NONE",
                "fb_user_name": "Tên tài khoản facebook",
                "name": "Tên tài khoản quảng cáo",
                "status": "ENABLED",
                "token_valid": true,
                "tos_accepted": true,
                "updated_time": null
            }
        ]
    },
    "message": "request thành công."
}
"""

********************************* Count Ads Accounts ***********************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {get} /ads-accounts/count.json Count Ads Accounts
@apiGroup AdsAccounts
@apiVersion 1.0.0
@apiName CountAdsAccounts
@apiDescription Đếm số tài khoản quảng cáo.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_type   Loại quảng cáo
@apiParam   (Query:)  {string}  key_value   Từ khóa quảng cáo
@apiParam   (Query:)  {string}  valid_state   Trạng thái hợp lệ
@apiParam   (Query:)  {string}  ads_acc_status  Trạng thái tài khoản quảng cáo
@apiParam   (Query:)  {string}  merchant_ids   Danh sách Tenant ID


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "count": 1
    },
    "message": "request thành công."
}
"""

******************************* Get Ads Account Detail *********************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {get} /ads-accounts/<ads_acc_id>.json Get Ads Account Detail
@apiGroup AdsAccounts
@apiVersion 1.0.0
@apiName GetAdsAccountDetail
@apiDescription Lấy chi tiết tài khoản quảng cáo.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    ads_acc_id                              ID tài khoản quảng cáo.

@apiParam   (Query:)  {string}  ads_type   Loại quảng cáo


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "ads_account": {
            "account_status": "ACTIVE",
            "ads_acc_id": "act_49755564",
            "business_id": null,
            "created_time": "2019-07-09T09:30:53Z",
            "disable_reason": "INVALID_STATUS",
            "id": "d39e838f-704e-42f9-9f25-adec8a2489f5",
            "name": "Name Test",
            "status": "ENABLED",
            "token_valid": true,
            "tos_accepted": true,
            "updated_time": null,
            "user_id": "*****************",
            "currency": "Đồng Việt Nam",
            "timezone": "(GMT -07:00) America/Los_Angeles",
            "ads_type": "facebook",
            "tenant_name": "PingcomShop"
        }
    },
    "message": "request thành công."
}
"""

**************************** Get Pages by Ads Account *****************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {get} /ads-accounts/<ads_acc_id>/pages.json Get pages by ads account
@apiGroup AdsAccounts
@apiVersion 1.0.0
@apiName GetAdsAccountPages
@apiDescription Lấy danh sách pages thông qua tài khoản quảng cáo.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    ads_acc_id                              ID tài khoản quảng cáo.

@apiParam   (Query:)  {string}  ads_type   Loại quảng cáo


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        pages": [
            {
                "avatar": "https://graph.facebook.com/***************/picture",
                "id": "***************",
                "name": "page 1"
            },
            {
                "avatar": "https://graph.facebook.com/***************/picture",
                "id": "***************",
                "name": "page 2"
            }
        ]
    },
    "message": "request thành công."
}
"""

****************************** Update Ads Accounts Status *******************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {put} /ads-accounts/status.json Update Ads Accounts Status
@apiGroup AdsAccounts
@apiVersion 1.0.0
@apiName UpdateAdsAccountsStatus
@apiDescription Cập nhật trạng thái tài khoản quảng cáo.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_type   Loại quảng cáo

@apiParam (Body:)  {object} ads_account_ids   Mảng danh sách Ads Account Ids
@apiParam (Body:)  {int} status   Trạng thái muốn cập nhật

@apiParamExample    {json}  Body example:
{
	"ads_account_ids": ["act_49755564"],
	"status": 1
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "ads_accounts": [
            {
                "account_status": "ACTIVE",
                "ads_acc_id": "act_49755564",
                "business_id": null,
                "created_time": "Tue, 09 Jul 2019 09:30:53 GMT",
                "disable_reason": "NONE",
                "name": "Name Test",
                "status": "ENABLED",
                "token_valid": true,
                "tos_accepted": true,
                "updated_time": "Wed, 10 Jul 2019 11:47:30 GMT"
            }
        ]
    },
    "message": "request thành công."
}
"""

********************************* Delete Ads Accounts **********************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {put} /ads-accounts.json Delete Ads Accounts
@apiGroup AdsAccounts
@apiVersion 1.0.0
@apiName DeleteAdsAccounts
@apiDescription Xóa tài khoản quảng cáo.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_type   Loại quảng cáo

@apiParam (Body:)  {object} ads_account_ids   Mảng danh sách Ads Account Ids

@apiParamExample    {json}  Body example:
{
	"ads_account_ids": ["act_49755564"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "result": True,
    "message": "request thành công."
}
"""

******************************** Validate Ads Account **********************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {put} /ads-accounts/<ads_acc_id>/validation.json Validate Ads Account
@apiGroup AdsAccounts
@apiVersion 1.0.0
@apiName ValidateAdsAccount
@apiDescription Kiểm tra tình trạng hợp lệ của tài khoản quảng cáo.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    ads_acc_id                              ID tài khoản quảng cáo.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "ads_account": {
            "account_status": "ACTIVE",
            "ads_acc_id": "act_49755564",
            "business_id": null,
            "created_time": "2019-07-09T09:30:53Z",
            "disable_reason": "NONE",
            "name": "Hà Huy",
            "status": "ENABLED",
            "token_valid": true,
            "tos_accepted": true,
            "updated_time": "2019-07-10T11:47:30Z",
            "user_id": "*****************"
        }
    },
    "message": "request thành công."
}
"""

******************************* Get Ads Account By IDs *********************************
* version: 1.5                                                                         *
****************************************************************************************
"""
@api {get} /ads-accounts/get_by_ids.json Get Ads Account By IDs
@apiGroup AdsAccounts
@apiVersion 1.0.0
@apiName GetAdsAccountByIDs
@apiDescription Lấy chi tiết các tài khoản quảng cáo theo IDs

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_type   Loại quảng cáo
@apiParam   (Query:)  {string}  ads_account_ids   Danh sách IDs tài khoản quảng cáo phân cách bởi dấu phẩy ","


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "ads_account": {
        "account_status": "ACTIVE",
        "ads_acc_id": "act_725653827816785",
        "ads_type": "facebook",
        "business_id": null,
        "created_time": "23/10/2019 17:48",
        "currency": "Đồng Việt Nam (VNĐ)",
        "disable_reason": "INVALID_STATUS",
        "fb_user_name": "Lê Na",
        "id": "1c1f556e-69d0-4d40-80ac-a860758fc72b",
        "name": "Lê Na",
        "status": "DELETED",
        "tenant_name": "PingcomShop",
        "timezone": "(GMT +07:00) Giờ TP Hồ Chí Minh",
        "token_valid": true,
        "tos_accepted": true,
        "updated_time": "17/02/2020 16:12",
        "user_id": "***************"
      }
    },
    {
      "ads_account": {
        "account_status": "ACTIVE",
        "ads_acc_id": "act_369651644",
        "ads_type": "facebook",
        "business_id": "****************",
        "created_time": "24/09/2019 11:29",
        "currency": "Đồng Việt Nam (VNĐ)",
        "disable_reason": "INVALID_STATUS",
        "fb_user_name": "Lê Na",
        "id": "61ca7e8f-54d6-416c-8ed7-6d1527d7824b",
        "name": "Le Hung",
        "status": "ENABLED",
        "tenant_name": "PingcomShop",
        "timezone": "(GMT +07:00) Giờ TP Hồ Chí Minh",
        "token_valid": true,
        "tos_accepted": true,
        "updated_time": "17/02/2020 16:46",
        "user_id": "***************"
      }
    }
  ],
  "message": "request thành công."
}
"""

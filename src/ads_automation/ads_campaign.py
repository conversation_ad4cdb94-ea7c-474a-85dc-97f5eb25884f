*********************************** Get List Field *************************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {get} /merchant/field/list Get List Field
@apiGroup Campaign
@apiVersion 1.5.0
@apiName GetListField
@apiDescription Lấy danh sách field hiển thị (theo staff)

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    {
  "code": 200,
  "default_language": [
    "vi",
    "en"
  ],
  "groups": [
    {
      "group": "setup",
      "group_index": 2,
      "translate_key": "i18n_label_setting"
      "dashboard_left": true,
      "fields": [
        {
          "dashboard_left": true,
          "dashboard_order": 2,
          "dashboard_right": true,
          "description": "Trạng thái phân phối",
          "disable_remove_dashboard": true,
          "disable_remove_export": true,
          "display_type": "single_line",
          "export_left_input": true,
          "export_order": 2,
          "export_right_input": true,
          "field_key": "delivery_status",
          "field_name": {
            "en": "Delivery status",
            "vi": "Trạng thái phân phối"
          },
          "field_property": 2,
          "group": "effective",
          "group_index": 1,
          "is_base": true,
          "order": 2,
          "required": true,
          "status": 1,
          "support_sort": false,
          "view_all": true
        },
        {
          "dashboard_left": true,
          "dashboard_order": 10,
          "dashboard_right": true,
          "description": "Chi phí trên mỗi người nhấp vào quảng cáo",
          "disable_remove_dashboard": false,
          "disable_remove_export": false,
          "display_type": "single_line",
          "export_left_input": true,
          "export_order": 3,
          "export_right_input": true,
          "field_key": "cost_per_unique_click",
          "field_name": {
            "en": "Cost per person click to advertising",
            "vi": "Chi phí trên mỗi người nhấp vào quảng cáo"
        }
    }
    ]
}
"""

******************************* Update Field Display by Staff  *************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {post} /merchant/field/display Update Field Display by Staff
@apiGroup Campaign
@apiVersion 1.5.0
@apiName UpdateFieldDisplaybyStaff
@apiDescription Update Field Display by Staff

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParamExample   {json}  Body
{
  "fields": [
    {
      "field_key": "name",
      "dashboard_order": 1
    },
    {
      "field_key": "cost_reach_1000_people",
      "dashboard_order": 2
    },
    {
      "field_key": "cost_per_person_clicks",
      "dashboard_order": 3
    },
    {
      "field_key": "total_click",
      "dashboard_order": 4
    },
    {
      "field_key": "cost_per_click",
      "dashboard_order": 5
    },
    {
      "field_key": "cost_per_lead",
      "dashboard_order": 6
    },
    {
      "field_key": "cost_per_1000_display",
      "dashboard_order": 7
    },
    {
      "field_key": "cost_per_sale",
      "dashboard_order": 8
    },
    {
      "field_key": "click_per_display_ratio",
      "dashboard_order": 9
    },
    {
      "field_key": "lead",
      "dashboard_order": 10
    },
    {
      "field_key": "budget_remain",
      "dashboard_order": 11
    },
    {
      "field_key": "amount_spent_ratio",
      "dashboard_order": 12
    }
  ]
}


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "lang": "vi",
}
"""

*********************************** Get Campaigns **************************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {post} /campaigns.json Get Campaigns
@apiGroup Campaign
@apiVersion 1.5.0
@apiName GetCampaigns
@apiDescription Get Campaigns

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Param:) {String} ads_account_id Lọc danh sách chiến dịch theo ads_account_id
@apiParam (Param:) {String} ads_type Loại tài khoản quảng cáo: <code>facebook</code> or <code>google</code>
@apiParam (Param:) {String} [order_by] Sắp xếp theo các field key
@apiParam (Param:) {String} [order_type] <code>asc</code> or <code>desc</code>
@apiParam (Body:)  {Array} [ads_filters] Danh sách điều kiện bộ lọc chiến dịch
@apiParam (Body:) {String} [start_time] Lọc danh sách theo thời gian bắt đầu thu thập báo cáo
@apiParam (Body:) {String} [end_time] Lọc danh sách theo thời gian kết thúc thu thập báo cáo
@apiParam (Body:) {String} [search] Lọc danh sách theo tên hoặc ID của chiến dịch
@apiParam (Body:) {Bool} [export] Xuất file báo cáo excel (true = xuất file, còn lại là hiển thị danh sách)
@apiParam (Body:) {Bool} [export_fields] Danh sách và thứ các fields sẽ được xuất báo cáo excel


@apiParam   (ads_filters:)  {Array}        values                Danh sách các giá trị cần filter.
@apiParam   (ads_filters:)  {String}   criteria_key          Các key để filter chiến dịch ADS.
                                                              Alow values:
                                                              <ul>
                                                                <li><code>cri_ads_delivery_status </code>: Phân phối chiến dịch</li>
                                                                <li><code>cri_ads_start_time </code>: Thời gian bắt đầu</li>
                                                                <li><code>cri_ads_stop_time </code>: Thời gian kết thúc</li>
                                                                <li><code>cri_ads_clicks </code>: Clicks (chiến dịch)</li>
                                                                <li><code>cri_ads_cpa </code>: CPA (Chiến dịch)</li>
                                                                <li><code>cri_ads_cpc </code>: CPC (Chiến dịch)</li>
                                                                <li><code>cri_ads_cpm </code>: CPM (Chiến dịch)</li>
                                                                <li><code>cri_ads_cpl </code>: CPL (Chiến dịch)</li>
                                                                <li><code>cri_ads_cps </code>: CPS (Chiến dịch)</li>
                                                                <li><code>cri_ads_ctr </code>: CTR (Chiến dịch)</li>
                                                                <li><code>cri_ads_lead </code>: Lead</li>
                                                                <li><code>cri_ads_impressions </code>: Số lần hiển thị (Chiến dịch)</li>
                                                                <li><code>cri_ads_reach </code>: Số người tiếp cận (Chiến dịch)</li>
                                                                <li><code>cri_ads_spend </code>: Số tiền chi tiêu trọn đời (Chiến dịch)</li>
                                                                <li><code>cri_ads_onl_to_off </code>: Tỉ lệ chuyển đổi Online thành Offline</li>
                                                              </ul>
@apiParam   (ads_filters:)  {String}   operator_key          Các toán tử để filter đơn hàng.
                                                              Alow value:
                                                              <code>
                                                                "op_is_between", "op_is_greater_equal", "op_is_in",
                                                                 "op_is_equal", "op_is_greater", "op_is_has",
                                                                 "op_is_has_not", "op_is_less_equal", "op_is_less",
                                                                 "op_is_empty", "op_is_not_empty"
                                                              </code>

@apiParamExample {json} Body example
{
    "ads_filters": [
        {
        "criteria_key": "cri_ads_impressions",
        "operator_key": "op_is_greater",
        "values": [
                2200
            ]
        }
    ],
    "start_time": "2020-03-15 00:00:01",
    "end_time": "2020-03-15 23:59:01",
    "search": "Lead gen",
    "export": true,
    "export_fields": [
        {
          "field_key": "name",
          "export_order": 1
        },
        {
          "field_key": "cost_reach_1000_people",
          "export_order": 2
        },
        {
          "field_key": "cost_per_person_clicks",
          "export_order": 3
        },
        {
          "field_key": "total_click",
          "export_order": 4
        }
    ]
}

@apiUse paging

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "lang": "vi",
    "data": [
        {
            "id": "5d93295478cee0acaf16919c",
            "budget_type": "adset_budget",
            "ads_type": "facebook", // Loại tài khoản quảng cáo facebook hoặc google
            "currency": "VND", // Tiền tệ
            "cpp": "135612.208259", // CPP
            "cost_per_unique_click": "1258.933333", // Chi phí trên mỗi người nhấp vào quảng cáo
            "clicks": "60", //Clicks
            "cpc": "1258.933333", // CPC
            "cpl": "1258.933333", // CPL
            "cpm": "131138.888889", // CPM
            "cps": "1258.933333", // CPS
            "ctr": "10.416667", // CTR
            "lead": "699", // Lead
            "budget_remaining": null,
            "amount_spent_ratio": "35%", // Phần trăm số tiền đã chi tiêu
            "impressions": "576", // Số lần hiển thị
            "reach": "588", // SỐ người tiếp cận
            "spend": "75536", // Số tiền đã chi tiêu
            "delivery_status": "delivered", // Trạng thái phân phối
            "unique_clicks": "60", // Unique Clicks
            "unique_ctr": "59", // Unique CTR
            "campaign_id": "6067688497755", // ID chiến dịch
            "budget": null,
            "campaign_name": "Lead gen", // Tên chiến dịch
            "start_time": "2016-10-10", // Thời gian bắt đầu
            "updated_time": "2019-11-07 17:30:00", // Thời gian cập nhật gần nhất
            "stop_time": "2016-10-10", // Thời gian kết thúc
            "created_time": "2019-11-07 17:30:00", // Thời gian tạo
            "onl_to_off": "50%" // Tỉ lệ chuyển đổi online to offline,
            "external_fields": {
                "is_view": true,
                "marketing_campaign_id": "",
                "profile_collection": {},
                "profile_groups": [],
                "tag_ids": []
            }
        }
    ],
    "paging": {
        "page": 2,
        "per_page": 5,
        "total_count": 17,
        "total_page": 4
    }
}
"""


*********************************** Get List Campaign **********************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {get} /campaign/<ads_account_id>/list Get List Campaign
@apiGroup Campaign
@apiVersion 1.5.0
@apiName GetListCampaign
@apiDescription Get List Campaign

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Query:) {int} [filter_type] Lọc danh sách theo: <code>1</code>: Đã gắn chiến dịch marketing
                                                           <code>2</code>: Chưa gắn chiến dịch marketing
                                                           <code>3</code>: Tất cả 2 loại trên
@apiParam (Query:) {string} [start_start_time] Mốc đầu khoảng thời gian bắt đầu chiến dịch
@apiParam (Query:) {string} [end_start_time] Mốc cuối khoảng thời gian bắt đầu chiến dịch
@apiParam (Query:) {string} [start_end_time] Mốc đầu khoảng thời gian kết thúc chiến dịch
@apiParam (Query:) {string} [end_end_time] Mốc đầu khoảng thời gian kết thúc chiến dịch
@apiUse paging

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "campaign_id": "6142281164555",
      "campaign_name": "Tìm kiếm khách hàng tiềm năng",
      "id": "5d93295478cee0acaf16919c"
    },
    {
      "campaign_id": "6158225304755",
      "campaign_name": "Lead generation ad - 12/16/2019 9:59 PM",
      "id": "5e17f4f28f8e7e98ebe9b95e"
    },
    {
      "campaign_id": "6156900517755",
      "campaign_name": "Lead Gen from ades",
      "id": "5e17f4f28f8e7e98ebe9b962"
    }
  ],
  "message": "request thành công.",
  "paging": {
    "page": -1,
    "per_page": 15,
    "total_count": 2,
    "total_page": 1
  }
}
"""


*********************************** Set Profiles Config ********************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {post} /campaign/<campaign_id>/profiles/config Set Profiles Config
@apiGroup Campaign
@apiVersion 1.5.0
@apiName SetProfilesConfig
@apiDescription Set Profiles Config

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    campaign_id                              ID của audience.

@apiParamExample    {json}      Body example:
{
	"type": "facebook",
  "tags": [
    {
      "tag_id": "123",
      "tag_name": "tag 1"
    },
    {
      "tag_name": "tag 2"
    }
  ],
  "profile_groups": ["group_id_1", "group_id_2", "group_id_3"],
  "profile_collection": {
    "does_save": True
    "id": "123"
    "name": "Profile Collection"
  }
}
"""


*********************************** Update Profiles Config ******************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {put} /campaign/<campaign_id>/profiles/config Update Profiles Config
@apiGroup Campaign
@apiVersion 1.5.0
@apiName UpdateProfilesConfig
@apiDescription Update Profiles Config

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    campaign_id                              ID của audience.

@apiParamExample    {json}      Body example:
{
	"type": "facebook",
  "tags": [
    {
      "tag_id": "123",
      "tag_name": "tag 1"
    },
    {
      "tag_name": "tag 2"
    }
  ],
  "profile_groups": ["group_id_1", "group_id_2", "group_id_3"],
  "profile_collection": {
    "does_save": True
    "id": "123"
    "name": "Profile Collection"
  }
}
"""


*********************************** Set Marketing Config *******************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {post} /campaign/<campaign_id>/marketing-campaign/config Set Marketing Campaign Config
@apiGroup Campaign
@apiVersion 1.5.0
@apiName SetMarketingCampaignConfig
@apiDescription Set Marketing Campaign Config

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    campaign_id                              ID của audience.
@apiParam      (Resources:)     {String}    type                              Type của audience: facebook.

@apiParamExample    {json}      Body example:
{
	"type": "facebook",
  "marketing_id": "123"
}
"""


*********************************** ACTIVATE Marketing *******************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {post} /campaign/<campaign_id>/marketing-campaign/activate Activate Marketing Campaign
@apiGroup Campaign
@apiVersion 1.5.0
@apiName ActivateMarketingCampaign
@apiDescription Activate Marketing Campaign

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    type                              Type của audience: facebook.
@apiParam      (Resources:)     {Array}     root_message_ids                  Root message ids của MKT.
@apiParam      (Resources:)     {String}    master_campaign_id                Master campaign id của MKT.
@apiParam      (Resources:)     {Int}    status                               Trạng thái của MKT.

@apiParamExample    {json}      Body example:
{
	"type": "facebook"
  "root_message_id": ["some ids here"],
  "master_campaign_id": "some id here",
  "status": 2
}
"""


*********************************** DELETE Marketing Config *******************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {delete} /campaign/<campaign_id>/marketing-campaign/config Delete Marketing Campaign Config
@apiGroup Campaign
@apiVersion 1.5.0
@apiName DeleteMarketingCampaignConfig
@apiDescription Delete Marketing Campaign Config

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    type                              Type của audience: facebook.

@apiParamExample    {json}      Body example:
{
	"type": "facebook"
}
"""


*********************************** Get Detail Campaign **************************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {get} /campaign/<campaign_id>/detail Get Detail Campaign
@apiGroup Campaign
@apiVersion 1.5.0
@apiName GetDetailCampaign
@apiDescription Get Detail Campaign

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "campaign_id": "6142281164555",
        "campaign_name": "Tìm kiếm khách hàng tiềm năng",
        "id": "5d93295478cee0acaf16919c", // Dùng id này để truyền vào campaign_id
        "status": "DELETED"
    },
    "message": "request thành công.",
}
"""

*********************************** Get List Excel Field *******************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {get} /merchant/field/export Get List Excel Field
@apiGroup Campaign
@apiVersion 1.5.0
@apiName GetListExcelField
@apiDescription Lấy danh sách field xuất excel (theo staff)

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "default_language": [
    "vi",
    "en"
  ],
  "groups": [
    {
      "export_left_input": true,
      "group": "setup",
      "group_index": 2,
      "translate_key": "i18n_label_setting"
      "fields": [
        {
          "dashboard_left": true,
          "dashboard_order": 2,
          "dashboard_right": true,
          "description": "Trạng thái phân phối",
          "disable_remove_dashboard": true,
          "disable_remove_export": true,
          "display_type": "single_line",
          "export_left_input": true,
          "export_order": 2,
          "export_right_input": true,
          "field_key": "delivery_status",
          "field_name": {
            "en": "Delivery status",
            "vi": "Trạng thái phân phối"
          },
          "field_property": 2,
          "group": "effective",
          "group_index": 1,
          "is_base": true,
          "order": 2,
          "required": true,
          "status": 1,
          "support_sort": false,
          "view_all": true
        },
        {
          "dashboard_left": true,
          "dashboard_order": 3,
          "dashboard_right": true,
          "description": "Chi phí trên mỗi người nhấp vào quảng cáo",
          "disable_remove_dashboard": false,
          "disable_remove_export": false,
          "display_type": "single_line",
          "export_left_input": true,
          "export_order": 3,
          "export_right_input": true,
          "field_key": "cost_per_unique_click",
          "field_name": {
            "en": "Cost per person click to advertising",
            "vi": "Chi phí trên mỗi người nhấp vào quảng cáo"
          },
          "field_property": 1,
          "group": "effective",
          "group_index": 1,
          "is_base": true,
          "order": 3,
          "required": true,
          "status": 1,
          "support_sort": true,
          "view_all": true
        }
    }
    ]
}
"""

*********************************** Get Detail Campaign By IDs**************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {post} /campaigns/get_by_ids Get Detail Campaign By IDs
@apiGroup Campaign
@apiVersion 1.5.0
@apiName GetDetailCampaignByIDs
@apiDescription Get Detail Campaign By IDs

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Body:)         {array}      campaign_ids      Danh sách campaign_ids (dùng dạng string Object của Mongo)

@apiParamExample {json} Body example
{
    "campaign_ids": [
            "5e74a939b6c2e9130676fc1c",
            "5e74a939b6c2e9130676fc1d"
        ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "campaign_id": "6175910405755",
      "campaign_name": "Test 20/3",
      "id": "5e74a939b6c2e9130676fc1c",
      "status": "PAUSED"
    },
    {
      "campaign_id": "6175551372755",
      "campaign_name": "Tìm kiếm khách hàng tiềm năng_ngay18/03",
      "id": "5e74a939b6c2e9130676fc1d",
      "status": "PAUSED"
    }
  ],
  "message": "request thành công."
}
"""


*********************************** Get Top Fields Ads Filter **************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {get} /campaigns/get_top_fields_ads_filter.json Get Top Fields Ads Filter
@apiGroup Campaign
@apiVersion 1.5.0
@apiName GetTopFieldsAdsFilter
@apiDescription Get Top Fields Ads Filter

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam (Param:) {int} [nums_of_field]  Số fields được lọc nhiều nhất, default bằng <code>4</code>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    "cri_ads_cps",
    "cri_ads_stop_time",
    "cri_ads_spend",
    "cri_ads_lead"
  ],
  "message": "request thành công."
}
"""

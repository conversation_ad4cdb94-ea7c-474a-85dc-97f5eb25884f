************************************ Get Latest Connected User *************************************
* version: 1.0.0                                                                                   *
****************************************************************************************************
"""
@api {get} /user/latest-connection.json Get Latest Connected User
@apiGroup User
@apiVersion 1.0.0
@apiName GetLatestConnection
@apiDescription Trường hợp ads_type là Facebook lấy tài khoản Facebook kết nối gần nhất của staff_id được lấy từ jwt

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_type   Loại quảng cáo

@apiParam (Body:)  {object} user   Người dùng

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "user": {
        "fb_user_id": "*****************",
        "id": "9f402c99-1f24-4317-91d2-39785645d991",
        "staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
    }
}
"""

************************************ Get Resources *************************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {get} /user/resources.json Get Resources
@apiGroup User
@apiVersion 1.0.0
@apiName GetResources
@apiDescription Trường hợp ads_type là Facebook lấy tài khoản quảng cáo và pages của tài khoản Facebook kết nối gần nhất của staff_id được lấy từ jwt

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_type   Loại quảng cáo

@apiParam (Body:)  {object} user   Người dùng

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "fb_ads_accounts": [
        {
            "account_id": "********",
            "account_status": "ACTIVE",
            "amount_spent": "0",
            "disable_reason": "NONE",
            "id": "act_********",
            "is_used": false,
            "name": "Somehow",
            "token_valid": true,
            "tos_accepted": true,
            "currency": "VND",
            "timezone_name": "America/Los_Angeles",
            "timezone_offset_hours_utc": -7
        }
    ],
    "fb_pages": [
        {
            "id": "***************",
            "is_used": false,
            "name": "Page Name",
            "tasks": [
                "ANALYZE",
                "ADVERTISE",
                "MODERATE",
                "CREATE_CONTENT",
                "MANAGE"
            ]
        }
    ],
    "message": "request thành công."
}
"""

************************************ Register User *************************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {post} /users.json Register User
@apiGroup User
@apiVersion 1.0.0
@apiName RegisterUser
@apiDescription Đăng ký / Cập nhật người dùng

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_type   Loại quảng cáo

@apiParam (Body:)  {object} user   Người dùng

@apiParam (user:)  {string} user_id   Facebook User Id
@apiParam (user:)  {string} token   Facebook short live token

@apiParamExample    {json}  Body example:
{
	"ads_type": "facebook",
    "user_id": "*****************",
    "token": "EAAMR6DB2EBAKfvRj56qG0wiuTyGy78HKmiav8AHCxRg1ZAtcaHf7vkmOMoqMGGuOs3ey0ZAm3nTbb8ZACkK0oZAZAZCnYglFSiNPlW2s16ig4EYqDPkM1zFY44p36Jgmf1CcIgV664mI8M8tXFJjz5r7MLKzbnjfsZCINm0JZBwgU6VGrZBTEcwPpqiMMArwQgZD"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "fb_ads_accounts": [
        {
            "account_id": "********",
            "account_status": "ACTIVE",
            "amount_spent": "0",
            "disable_reason": "NONE",
            "id": "act_********",
            "is_used": false,
            "name": "Somehow",
            "token_valid": true,
            "tos_accepted": true,
            "currency": "VND",
            "timezone_name": "America/Los_Angeles",
            "timezone_offset_hours_utc": -7
        }
    ],
    "fb_pages": [
        {
            "id": "***************",
            "is_used": false,
            "name": "Page Name",
            "tasks": [
                "ANALYZE",
                "ADVERTISE",
                "MODERATE",
                "CREATE_CONTENT",
                "MANAGE"
            ]
        }
    ],
    "message": "request thành công."
}
"""

********************************* Register Resources ***********************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {post} /user/resources.json Register Resources
@apiGroup User
@apiVersion 1.0.0
@apiName RegisterResources
@apiDescription Đăng ký / Cập nhật tài nguyên: tài khoản quảng cáo, pages.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_type   Loại quảng cáo

@apiParam (Body:)  {object} user   Người dùng

@apiParam (user:)  {string} user_id   Facebook User Id
@apiParam (user:)  {object} fb_ads_accounts   Danh sách tài khoản đăng ký
@apiParam (user:)  {object} fb_pages   Danh sách page đăng ký

@apiParam (fb_ads_accounts:)  {string} account_id  account id của tài khoản quảng cáo
@apiParam (fb_ads_accounts:)  {string} account_status  trạng thái của tài khoản quảng cáo
@apiParam (fb_ads_accounts:)  {string} amount_spent  số tiền đã chi tiêu của tài khoản quảng cáo
@apiParam (fb_ads_accounts:)  {string} disable_reason  lí do bị khóa của tài khoản quảng cáo
@apiParam (fb_ads_accounts:)  {string} id  id của tài khoản quảng cáo
@apiParam (fb_ads_accounts:)  {bool} is_used  trạng thái sử dụng của tài khoản quảng cáo
@apiParam (fb_ads_accounts:)  {string} name  tên của tài khoản quảng cáo
@apiParam (fb_ads_accounts:)  {bool} token_valid  trạng thái hợp lệ của access token của tài khoản quảng cáo
@apiParam (fb_ads_accounts:)  {bool} tos_accepted  trạng thái đồng ý với các điều khoản Facebook Marketing của tài khoản quảng cáo
@apiParam (fb_ads_accounts:)  {string} currency  đơn vị tiền tệ được sử dụng bởi tài khoản quảng cáo FB
@apiParam (fb_ads_accounts:)  {string} timezone_name  tên của timezone được sử dụng bởi tài khoản quảng cáo FB
@apiParam (fb_ads_accounts:)  {int} timezone_offset_hours_utc  giá trị offset theo timezone

@apiParam (fb_pages:)  {string} id  Page Id của page
@apiParam (fb_pages:)  {bool} is_used  Trạng thái sử dụng của page
@apiParam (fb_pages:)  {string} name  Tên của page
@apiParam (fb_pages:)  {list} tasks  Danh sách quyền với page của tài khoản quảng cáo

@apiParamExample    {json}  Body example:
{
    "fb_user_id": *****************,
    "fb_ads_accounts": [
        {
            "account_id": "********",
            "account_status": "ACTIVE",
            "amount_spent": "0",
            "disable_reason": "NONE",
            "id": "act_********",
            "is_used": false,
            "name": "User test",
            "token_valid": true,
            "tos_accepted": true,
            "currency": "VND",
            "timezone_name": "America/Los_Angeles",
            "timezone_offset_hours_utc": -7
        }
    ],
    "fb_pages": [
        {
            "id": "***************",
            "is_used": false,
            "name": "Page test 1",
            "tasks": [
                "ANALYZE",
                "ADVERTISE",
                "MODERATE",
                "CREATE_CONTENT",
                "MANAGE"
            ]
        }
    ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "request thành công.",
    "user": {
        "ads_accounts": [
            {
                "business_id": "***************",
                "created_time": "Tue, 09 Jul 2019 03:59:59 GMT",
                "disable_reason": "NONE",
                "ads_acc_id": "act_********",
                "account_status": "ACTIVE",
                "id": "4f52a684-d662-4e66-b6ac-870adc8cc2ef",
                "name": "User test",
                "status": "ENABLED",
                "token_valid": True,
                "tos_accepted": true,
                "updated_time": null,
                "currency": "VND",
                "timezone_name": "America/Los_Angeles",
                "timezone_offset_hours_utc": -7
            }
        ],
        "pages": [
            {
                "created_time": "Tue, 09 Jul 2019 03:59:59 GMT",
                "fb_page_id": "***************",
                "id": "94f5e97b-a593-430e-b35e-aaa92895e831",
                "name": "Page test 1",
                "updated_time": null
            }
        ]
    }
}
"""

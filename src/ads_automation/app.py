************************** Thêm App cho tenant ***************************                                                        *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api {post} /apps.json Thêm App cho tenant
@apiDescription Dịch vụ dùng để thêm App theo tenant
@apiGroup App
@apiVersion 1.0.0
@apiName AddApp

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:) {String}    client_id       Mã định danh ứng dụng trên mạng xã hội.
@apiParam   (Body:) {String}    client_secret   Mã bí mật của ứng dụng trên mạng xã hội.
@apiParam   (Body:) {Number}    social_type     Kiểu mạng xã hội.
@apiParam   (Body:) {String}    app_name        Tên ứng dụng
@apiParamExample    {json}      Body example:
{
  "apps":[
    {
      "client_id": "1407393336188886",
      "client_secret": "b4af286f8dd86030d88e03ffd2044fbf",
      "social_type": 1,
      "app_name": "iMember"
    },
    {
      "client_id": "390397049587188442",
      "client_secret": "0C6OSqD0iPaVHmoHS8dH",
      "social_type": 2,
      "app_name": "MobioTest"
    }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code":"200",
  "message":"Request thành công"
}
"""

*********************** Lấy danh sách App cho tenant *********************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api {get} /apps.json Lấy danh sách App cho tenant
@apiDescription Dịch vụ dùng để lấy danh sách App theo tenant
@apiGroup App
@apiVersion 1.0.0
@apiName GetApp

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiSuccess    {String}    client_id       Mã định danh ứng dụng trên mạng xã hội.
@apiSuccess    {String}    client_secret   Mã bí mật của ứng dụng trên mạng xã hội.
@apiSuccess    {String}    social_type     Kiểu mạng xã hội.
@apisuccess    {String}    app_name        Tên ứng dụng
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code":"200",
  "message":"Request thành công",
  "datas": [
    {
      "client_id": "1407393336188886",
      "client_secret": "b4af286f8dd86030d88e03ffd2044fbf",
      "social_type": 1,
      "app_name": "iMember"
    },
    {
      "client_id": "390397049587188442",
      "client_secret": "0C6OSqD0iPaVHmoHS8dH",
      "social_type": 2,
      "app_name": "MobioTest"
    }
  ]
}
"""

********************************* Get Ads Objective ************************************
* version: 1.0.2                                                                       *
* version: 1.0.1                                                                       *
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {get} /ads-objective.json Get Ads Objective
@apiGroup AdsObjective
@apiVersion 1.0.2
@apiName GetAdsObjective
@apiDescription Lấy danh sách các đối tượng của tài khoản quảng cáo.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_account_id   Mã định danh tài khoản quảng cáo
@apiParam   (Query:)  {number}  ads_objective_type   Loại đối tượng cần lấy. Giá trị: <br/><code>campaign</code><br/><code>adset</code><br/><code>ads</code>
@apiParam   (Query:)  {string}  [status]   Trạng thái phân phối cần lọc. Giá trị: <br/><code>active=Hoạt động</code><br/><code>unactive=Không hoạt động</code>
@apiParam   (Query:)  {string}  [search_text]     Text cần tìm kiếm
@apiParam   (Query:)  {String}    [after]    Token để lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)  {String}    [before]    Token để lấy dữ liệu trang trước đó.
@apiParam   (Query:)  {int}  limit   Giới hạn số tài khoản trên mỗi trang
@apiParam   (Query:)  {string}  ads_objective_ids   Các mã định danh đối tượng cần lấy. Example: *************,*************


@apiSuccessExample     {json}    Response Campaign: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
            {   
                "ads_objective_id": "*************",
                "ads_account_id": "",
                "ads_objective_name": "",
                "status": "ON"
            }
        ]
    },
    "paging": {
        "cursors": {
            "after": "QVFIUjE3RklVMmJLTTVtLTY3WXNzUEdRMTdnUHRvX2RRZA0YwM3cwMzNBTnNMc2pHckJTa09MVDQ3d1ZAONE1XeURaMW9MdFlqOFR2bUZANNFpmMjFwSzN1LUZA3",
            "before": "QVFIUmtSVnRubjAyQ3M5OHBXeEFVeTQyZATg2YnEzeVhPcWt0VThCcGJZAZAzd0dXh0bUVjeUpJcExIek12ZAkZAqSTg2Wm5KNmxKRDFaOGZA2cWNta193UmVYdzNB"
        }
    },
    "message": "request thành công."
}
@apiSuccessExample     {json}    Response Adset: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
            {   
                "ads_objective_id": "*************",
                "ads_account_id": "",
                "ads_objective_name": "",
                "ads_campaign_id": "",
                "ads_campaign_name": "",
                "status": "ON"
            }
        ]
    },
    "paging": {
        "cursors": {
            "after": "QVFIUjE3RklVMmJLTTVtLTY3WXNzUEdRMTdnUHRvX2RRZA0YwM3cwMzNBTnNMc2pHckJTa09MVDQ3d1ZAONE1XeURaMW9MdFlqOFR2bUZANNFpmMjFwSzN1LUZA3",
            "before": "QVFIUmtSVnRubjAyQ3M5OHBXeEFVeTQyZATg2YnEzeVhPcWt0VThCcGJZAZAzd0dXh0bUVjeUpJcExIek12ZAkZAqSTg2Wm5KNmxKRDFaOGZA2cWNta193UmVYdzNB"
        }
    },
    "message": "request thành công."
}
@apiSuccessExample     {json}    Response Ad: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
            {   
                "ads_objective_id": "*************",
                "ads_account_id": "",
                "ads_objective_name": "",
                "ads_campaign_id": "",
                "ads_campaign_name": "",
                "ads_adset_id": "",
                "ads_adset_name": "",
                "status": "ON"
            }
        ]
    },
    "paging": {
        "cursors": {
            "after": "QVFIUjE3RklVMmJLTTVtLTY3WXNzUEdRMTdnUHRvX2RRZA0YwM3cwMzNBTnNMc2pHckJTa09MVDQ3d1ZAONE1XeURaMW9MdFlqOFR2bUZANNFpmMjFwSzN1LUZA3",
            "before": "QVFIUmtSVnRubjAyQ3M5OHBXeEFVeTQyZATg2YnEzeVhPcWt0VThCcGJZAZAzd0dXh0bUVjeUpJcExIek12ZAkZAqSTg2Wm5KNmxKRDFaOGZA2cWNta193UmVYdzNB"
        }
    },
    "message": "request thành công."
}
"""
=====================
"""
@api {get} /ads-objective.json Get Ads Objective
@apiGroup AdsObjective
@apiVersion 1.0.1
@apiName GetAdsObjective
@apiDescription Lấy danh sách các đối tượng của tài khoản quảng cáo.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_account_id   Mã định danh tài khoản quảng cáo
@apiParam   (Query:)  {number}  ads_objective_type   Loại đối tượng cần lấy. Giá trị: <br/><code>campaign</code><br/><code>adset</code><br/><code>ads</code>
@apiParam   (Query:)  {string}  [status]   Trạng thái phân phối cần lọc. Giá trị: <br/><code>active=Hoạt động</code><br/><code>unactive=Không hoạt động</code>
@apiParam   (Query:)  {string}  [search_text]     Text cần tìm kiếm
@apiParam   (Query:)  {String}    [after]    Token để lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)  {String}    [before]    Token để lấy dữ liệu trang trước đó.
@apiParam   (Query:)  {int}  limit   Giới hạn số tài khoản trên mỗi trang


@apiSuccessExample     {json}    Response Campaign: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
            {   
                "ads_objective_id": "*************",
                "ads_account_id": "",
                "ads_objective_name": "",
                "status": "ON"
            }
        ]
    },
    "paging": {
        "cursors": {
            "after": "QVFIUjE3RklVMmJLTTVtLTY3WXNzUEdRMTdnUHRvX2RRZA0YwM3cwMzNBTnNMc2pHckJTa09MVDQ3d1ZAONE1XeURaMW9MdFlqOFR2bUZANNFpmMjFwSzN1LUZA3",
            "before": "QVFIUmtSVnRubjAyQ3M5OHBXeEFVeTQyZATg2YnEzeVhPcWt0VThCcGJZAZAzd0dXh0bUVjeUpJcExIek12ZAkZAqSTg2Wm5KNmxKRDFaOGZA2cWNta193UmVYdzNB"
        }
    },
    "message": "request thành công."
}
@apiSuccessExample     {json}    Response Adset: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
            {   
                "ads_objective_id": "*************",
                "ads_account_id": "",
                "ads_objective_name": "",
                "ads_campaign_id": "",
                "ads_campaign_name": "",
                "status": "ON"
            }
        ]
    },
    "paging": {
        "cursors": {
            "after": "QVFIUjE3RklVMmJLTTVtLTY3WXNzUEdRMTdnUHRvX2RRZA0YwM3cwMzNBTnNMc2pHckJTa09MVDQ3d1ZAONE1XeURaMW9MdFlqOFR2bUZANNFpmMjFwSzN1LUZA3",
            "before": "QVFIUmtSVnRubjAyQ3M5OHBXeEFVeTQyZATg2YnEzeVhPcWt0VThCcGJZAZAzd0dXh0bUVjeUpJcExIek12ZAkZAqSTg2Wm5KNmxKRDFaOGZA2cWNta193UmVYdzNB"
        }
    },
    "message": "request thành công."
}
@apiSuccessExample     {json}    Response Ad: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
            {   
                "ads_objective_id": "*************",
                "ads_account_id": "",
                "ads_objective_name": "",
                "ads_campaign_id": "",
                "ads_campaign_name": "",
                "ads_adset_id": "",
                "ads_adset_name": "",
                "status": "ON"
            }
        ]
    },
    "paging": {
        "cursors": {
            "after": "QVFIUjE3RklVMmJLTTVtLTY3WXNzUEdRMTdnUHRvX2RRZA0YwM3cwMzNBTnNMc2pHckJTa09MVDQ3d1ZAONE1XeURaMW9MdFlqOFR2bUZANNFpmMjFwSzN1LUZA3",
            "before": "QVFIUmtSVnRubjAyQ3M5OHBXeEFVeTQyZATg2YnEzeVhPcWt0VThCcGJZAZAzd0dXh0bUVjeUpJcExIek12ZAkZAqSTg2Wm5KNmxKRDFaOGZA2cWNta193UmVYdzNB"
        }
    },
    "message": "request thành công."
}
"""
=====================
"""
@api {get} /ads-objective.json Get Ads Objective
@apiGroup AdsObjective
@apiVersion 1.0.0
@apiName GetAdsObjective
@apiDescription Lấy danh sách các đối tượng của tài khoản quảng cáo.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  ads_account_id   Mã định danh tài khoản quảng cáo
@apiParam   (Query:)  {number}  ads_objective_type   Loại đối tượng cần lấy. Giá trị: <code>1=Campaign</code><br/><code>2=Adset</code><br/><code>3=Ad</code>
@apiParam   (Query:)  {string}  [status]   Trạng thái phân phối cần lọc. Giá trị: <br/><code>ON=Hoạt động</code><br/><code>OFF=Không hoạt động</code>
@apiParam   (Query:)  {string}  [search_text]     Text cần tìm kiếm
@apiParam   (Query:)  {String}    [after]    Token để lấy dữ liệu trang tiếp theo.
@apiParam   (Query:)  {String}    [before]    Token để lấy dữ liệu trang trước đó.
@apiParam   (Query:)  {int}  limit   Giới hạn số tài khoản trên mỗi trang


@apiSuccessExample     {json}    Response Campaign: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
            {   
                "ads_objective_id": "*************",
                "ads_account_id": "",
                "ads_objective_name": "",
                "status": "ON"
            }
        ]
    },
    "paging": {
        "cursors": {
            "after": "QVFIUjE3RklVMmJLTTVtLTY3WXNzUEdRMTdnUHRvX2RRZA0YwM3cwMzNBTnNMc2pHckJTa09MVDQ3d1ZAONE1XeURaMW9MdFlqOFR2bUZANNFpmMjFwSzN1LUZA3",
            "before": "QVFIUmtSVnRubjAyQ3M5OHBXeEFVeTQyZATg2YnEzeVhPcWt0VThCcGJZAZAzd0dXh0bUVjeUpJcExIek12ZAkZAqSTg2Wm5KNmxKRDFaOGZA2cWNta193UmVYdzNB"
        }
    },
    "message": "request thành công."
}
@apiSuccessExample     {json}    Response Adset: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
            {   
                "ads_objective_id": "*************",
                "ads_account_id": "",
                "ads_objective_name": "",
                "ads_campaign_id": "",
                "ads_campaign_name": "",
                "status": "ON"
            }
        ]
    },
    "paging": {
        "cursors": {
            "after": "QVFIUjE3RklVMmJLTTVtLTY3WXNzUEdRMTdnUHRvX2RRZA0YwM3cwMzNBTnNMc2pHckJTa09MVDQ3d1ZAONE1XeURaMW9MdFlqOFR2bUZANNFpmMjFwSzN1LUZA3",
            "before": "QVFIUmtSVnRubjAyQ3M5OHBXeEFVeTQyZATg2YnEzeVhPcWt0VThCcGJZAZAzd0dXh0bUVjeUpJcExIek12ZAkZAqSTg2Wm5KNmxKRDFaOGZA2cWNta193UmVYdzNB"
        }
    },
    "message": "request thành công."
}
@apiSuccessExample     {json}    Response Ad: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
            {   
                "ads_objective_id": "*************",
                "ads_account_id": "",
                "ads_objective_name": "",
                "ads_campaign_id": "",
                "ads_campaign_name": "",
                "ads_adset_id": "",
                "ads_adset_name": "",
                "status": "ON"
            }
        ]
    },
    "paging": {
        "cursors": {
            "after": "QVFIUjE3RklVMmJLTTVtLTY3WXNzUEdRMTdnUHRvX2RRZA0YwM3cwMzNBTnNMc2pHckJTa09MVDQ3d1ZAONE1XeURaMW9MdFlqOFR2bUZANNFpmMjFwSzN1LUZA3",
            "before": "QVFIUmtSVnRubjAyQ3M5OHBXeEFVeTQyZATg2YnEzeVhPcWt0VThCcGJZAZAzd0dXh0bUVjeUpJcExIek12ZAkZAqSTg2Wm5KNmxKRDFaOGZA2cWNta193UmVYdzNB"
        }
    },
    "message": "request thành công."
}
"""
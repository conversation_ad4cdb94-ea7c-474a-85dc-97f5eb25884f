************************** Thêm FB Rule cho  *****************************                                                        *
* version: 1.0.0                                                         *
**************************************************************************
"""
@api {post} /fb/rules.json Thêm Rule
@apiDescription Dịch vụ dùng để thêm Rule
@apiGroup Rule
@apiVersion 1.0.0
@apiName AddRule

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParamExample    {json}      Body example:
{
	"rule": {
		"ads_account_id": "act_369651644",
		"name": "abc",
		"objectives": {
			"type": "campaign",
			"ids": ["*************"]
		},
		"group_actions": [
			{
				"action": {
					"type": "enable"
				},
				"conditions": [
					{
						"key": "spent",
						"object": "adset",
						"time": "yesterday",
						"operator": "greater_than_or_equal",
						"value": "100000"
					}
				]
			}
		],
		"attribution_window": {
			"view_time": 0,
			"click_time": 0
		},
		"apply_time": {
			"schedule": {
				"type": "continuously",
				"value": [0.5]
			},
			"start_time": {
				"type": "immediately"
			},
			"end_time": {
				"type": "immediately"
			}
		},
		"notify": {
			"email": {
				"is_using": true,
				"emails": []
			}
		}
	}
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "_id": "5d564dd1cc9171ce1ddf3548",
        "ads_account_id": "act_369651644",
        "apply_time": {
            "end_time": {
                "type": "immediately"
            },
            "schedule": {
                "type": "continuously",
                "value": [
                    0.5
                ]
            },
            "start_time": {
                "type": "immediately"
            }
        },
        "attribution_window": {
            "click_time": 0,
            "view_time": 0
        },
        "created_time": "Fri, 16 Aug 2019 13:31:45 GMT",
        "group_actions": [
            {
                "action": {
                    "type": "enable"
                },
                "conditions": [
                    {
                        "key": "spent",
                        "object": "adset",
                        "operator": "greater_than_or_equal",
                        "time": "yesterday",
                        "value": "100000"
                    }
                ]
            }
        ],
        "name": "abc",
        "notify": {
            "email": {
                "emails": [],
                "is_using": true
            }
        },
        "objectives": {
            "ids": [
                "*************"
            ],
            "type": "campaign"
        },
        "staff_id": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
        "tenant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924"
    },
    "message": "request thành công."
}
"""


*********************** Lấy danh sách Rule cho tenant ********************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api {get} /fb/rules.json Lấy danh sách Rule cho tenant
@apiDescription Dịch vụ dùng để lấy danh sách Rule theo tenant
@apiGroup Rule
@apiVersion 1.0.0
@apiName GetRules

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Query:)  {string}  rule_statuses   Danh sách trạng thái lọc
@apiParam   (Query:)  {string}  objectives   Danh sách đối tượng áp dụng. Cách nhau bằng ;
@apiParam   (Query:)  {string}  key_value   Từ khóa quảng cáo
@apiParam   (Query:)  {int}  order_by   Trường dùng để  sắp xếp
@apiParam   (Query:)  {int}  order_type   Thứ tự dùng để  sắp xếp
@apiParam   (Query:)  {int}  page   Trang
@apiParam   (Query:)  {int}  limit   Giới hạn số tài khoản trên mỗi trang
@apiParam   (Query:)  {string}  ads_account_id   Danh sách ID tài khoản quảng cáo. Cách nhau bằng ;


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "rules": [
            {
                "ads_account_id": "act_369651644",
                "apply_time": {
                    "end_time": {
                        "type": "immediately"
                    },
                    "schedule": {
                        "type": "continuously",
                        "value": [
                            0.5
                        ]
                    },
                    "start_time": {
                        "type": "immediately"
                    }
                },
                "attribution_window": {
                    "click_time": 0,
                    "view_time": 0
                },
                "created_time": "19/08/2019 22:01",
                "group_actions": [
                    {
                        "action": {
                            "type": "enable"
                        },
                        "conditions": [
                            {
                                "key": "spent",
                                "object": "adset",
                                "operator": "greater_than_or_equal",
                                "time": "yesterday",
                                "value": "100000"
                            }
                        ]
                    }
                ],
                "id": "5d5a5750de95835a721b3f30",
                "name": "abc",
                "notify": {
                    "email": {
                        "emails": [],
                        "is_using": true
                    }
                },
                "objectives": {
                    "ids": [
                        "*************"
                    ],
                    "type": "campaign"
                },
                "status": "enabled",
                "updated_time": null
            },
            {
                "ads_account_id": "act_369651644",
                "apply_time": {
                    "end_time": {
                        "type": "immediately"
                    },
                    "schedule": {
                        "type": "continuously",
                        "value": [
                            0.5
                        ]
                    },
                    "start_time": {
                        "type": "immediately"
                    }
                },
                "attribution_window": {
                    "click_time": 0,
                    "view_time": 0
                },
                "created_time": "19/08/2019 22:01",
                "group_actions": [
                    {
                        "action": {
                            "type": "enable"
                        },
                        "conditions": [
                            {
                                "key": "spent",
                                "object": "adset",
                                "operator": "greater_than_or_equal",
                                "time": "yesterday",
                                "value": "100000"
                            }
                        ]
                    }
                ],
                "id": "5d5a5756de95835a721b3f31",
                "name": "abc",
                "notify": {
                    "email": {
                        "emails": [],
                        "is_using": true
                    }
                },
                "objectives": {
                    "ids": [
                        "*************"
                    ],
                    "type": "campaign"
                },
                "status": "enabled",
                "updated_time": null
            },
            
        ]
    },
    "message": "request thành công.",
    "paging": {
        "page": 1,
        "per_page": 15,
        "total_count": 8,
        "total_page": 1
    }
}
"""


*********************** Lấy chi tiết Rule cho tenant *********************
* version: 1.0.0                                                         *
**************************************************************************
"""
@api {get} /fb/rules/<rule_id>.json Lấy chi tiết Rule
@apiDescription Dịch vụ dùng để lấy chi tiết Rule
@apiGroup Rule
@apiVersion 1.0.0
@apiName GetRule

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    rule_id                              ID của rule.


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "rule": {
            "ads_account_id": "act_369651644",
            "apply_time": {
                "end_time": {
                    "type": "immediately"
                },
                "schedule": {
                    "type": "continuously",
                    "value": [
                        0.5
                    ]
                },
                "start_time": {
                    "type": "immediately"
                }
            },
            "attribution_window": {
                "click_time": 0,
                "view_time": 0
            },
            "created_time": "19/08/2019 22:30",
            "group_actions": [
                {
                    "action": {
                        "type": "enable"
                    },
                    "conditions": [
                        {
                            "key": "spent",
                            "object": "adset",
                            "operator": "greater_than_or_equal",
                            "time": "yesterday",
                            "value": "100000"
                        }
                    ]
                }
            ],
            "id": "5d5a5e2747ee29f3c25f554b",
            "name": "rule 1",
            "notify": {
                "email": {
                    "emails": [],
                    "is_using": true
                }
            },
            "objectives": {
                "ids": [
                    "*************"
                ],
                "type": "campaign"
            },
            "status": "enabled",
            "updated_time": null
        }
    },
    "message": "request thành công."
}
"""


************************************ Delete Rules **************************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {delete} /fb/rules.json Delete Rules
@apiGroup Rule
@apiVersion 1.0.0
@apiName DeleteRules
@apiDescription Xóa rule.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {object} rule_ids   Mảng danh sách Rule Ids

@apiParamExample    {json}  Body example:
{
	"rule_ids": ["5d5a5750de95835a721b3f30", "5d5a5e2747ee29f3c25f554b"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "ids": [
            "5d5a5750de95835a721b3f30",
            "5d5a5e2747ee29f3c25f554b"
        ]
    },
    "message": "request thành công."
}
"""


************************************ Delete Rule ***************************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {delete} /fb/rule/<rule_id>.json Delete Rule
@apiGroup Rule
@apiVersion 1.0.0
@apiName DeleteRule
@apiDescription Xóa rule.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resources:)     {String}    rule_id                              ID của rule.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "id": "5d5a5750de95835a721b3f30"
    },
    "message": "request thành công."
}
"""

********************************* Update Rules Status **********************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {put} /fb/rules/status.json Update Rules Status
@apiGroup Rule
@apiVersion 1.0.0
@apiName UpdateRulesStatus
@apiDescription Cập nhật trạng thái rules.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:)  {object} rule_ids   Mảng danh sách Rule Ids
@apiParam (Body:)  {object} status   Trạng thái mới

@apiParamExample    {json}  Body example:
{
	"rule_ids": ["5d5a5750de95835a721b3f30", "5d5a5e2747ee29f3c25f554b"],
	"status": "disabled"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "ids": [
            "5d5a5750de95835a721b3f30",
            "5d5a5e2747ee29f3c25f554b"
        ]
    },
    "message": "request thành công."
}
"""


********************************* Update Rule Status **********************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {put} /fb/rules/<rule_id>/status.json Update Rule Status
@apiGroup Rule
@apiVersion 1.0.0
@apiName UpdateRuleStatus
@apiDescription Cập nhật trạng thái rule.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Resources:)     {String}    rule_id                              ID của rule.
@apiParam       (Body:)  {object} status   Trạng thái mới

@apiParamExample    {json}  Body example:
{
	"status": "disabled"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "id": "5d5a5750de95835a721b3f30"
    },
    "message": "request thành công."
}
"""


********************************* Update Rule Objectives **********************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {put} /fb/rules/<rule_id>/objectives.json Update Rule Objectives
@apiGroup Rule
@apiVersion 1.5.0
@apiName UpdateRuleObjectives
@apiDescription Cập nhật đối tượng áp dụng của rule.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Resources:)     {String}    rule_id                              ID của rule.
@apiParam       (Body:)  {Array} objective_ids   Đối tượng áp dụng mới
@apiParam       (Body:)  {String} objective_type   Loại đối tượng áp dụng (campaign, adset, ads)

@apiParamExample    {json}  Body example:
{
	"objective_ids": ["id1", "id2"],
    "objective_type": "campaign"
}
"""


********************************* Apply Rules for Objective **********************************
* version: 1.5.0                                                                       *
****************************************************************************************
"""
@api {put} /fb/rules/objective.json Apply Rules for Objective
@apiGroup Rule
@apiVersion 1.5.0
@apiName ApplyRulesForObjective
@apiDescription Cập nhật đối tượng áp dụng của nhiều rules.

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)  {Array} rule_ids          Danh sách rule được áp dụng
@apiParam       (Body:)  {String} objective_id     Đối tượng áp dụng mới
@apiParam       (Body:)  {String} objective_type   Loại đối tượng áp dụng (campaign, adset, ads)

@apiParamExample    {json}  Body example:
{
    "rule_ids": ["rule_id1", "rule_id2"],
	"objective_id": "objective_id1",
    "objective_type": "campaign"
}
"""

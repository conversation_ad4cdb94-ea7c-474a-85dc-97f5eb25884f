#!/usr/bin/python
# -*- coding: utf8 -*-

******************************* Get profile by face_id ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/merchants/<merchant-id>/user/face-id Get profile by face_id.
@apiDescription Get list profile by list face id
@apiGroup User
@apiVersion 1.0.0
@apiName GetProfileByFaceID

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String[]}    face_ids       Mảng face_id

@apiParamExample  {json} Body request example
{
    "face_ids": ["27aeaa12-2d72-4441-b888-b7c26d6903a0", "27aeaa12-2d72-4441-b888-b7c26d6903a1"]
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "data":[
        {
            "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
            "merchant_id": "4a9f6012-5627-4239-b281-4adb85c00827",
            "address": "",
            "avatar": "",
            "birthday": "1993-10-10T00:00:00",
            "birth_year": 1993,
            "email": ("<EMAIL>"),
            "fax": "",
            "gender": 1,
            "hobby": [""],
            "job": [""],
            "marital_status": [""],
            "name": "",
            "nation": [""],
            "phone_number": (""),
            "province_code": [""],
            "tax_code": "",
            "ward_code": [""],
            "workplace": ""
        }
    ]
}
"""
************************************ Upsert Profile ***********************************
* version: 1.0.0                                                                      *
* version: 1.0.1                                                                      *
* version: 1.0.2                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/customers/actions/upsert Upsert Profile.
@apiDescription Upsert Profile
@apiGroup Customers
@apiVersion 1.0.0
@apiName UpsertProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.

@apiParam   (Body_Bussiness:)    {int=0,1}   is_company=0         Xác định profile này là Business hay Individual
<li><code>0:  Individual</code></li>
<li><code>1:  Business</code></li>
@apiParam   (Body_Bussiness:)    {String[]}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (Body_Bussiness:)    {Array}         [social_user]        Danh sách các mạng xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body_Bussiness:)    {String[]}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (Body_Bussiness:)    {String}        tax_code             Mã số thuế của Business.
@apiParam   (Body_Bussiness:)    {String}         fullname             Tên của Business.
@apiParam   (Body_Bussiness:)    {String}         [address]             Địa chỉ.
@apiParam   (Body_Bussiness:)    {String}         [tax_name]             Tên người nộp thuế.
@apiParam   (Body_Bussiness:)    {String}         [tax_address]          Địa chỉ kê khai nộp thuế.
@apiParam   (Body_Bussiness:)    {String}         [business_license]          Số đăng ký kinh doanh.
@apiParam   (Body_Bussiness:)    {int}            [business_type]          Loại hình doanh nghiệp.
<li><code>5:	SME (Doanh nghiệp vừa và nhỏ)</code></li>
<li><code>6:	Doanh nghiệp nước ngoài</code></li>
<li><code>7:	Nhà nước nói chung</code></li>
<li><code>8:	Doanh nghiệp NGO(non-governmental organization)</code></li>
<li><code>9:	Hành chính công (các VP nhà nước: UBND phường/xã...)</code></li>
@apiParam   (Body_Bussiness:)    {String}         [fax]          Số Fax.

@apiParam   (Body:)    {int=0,1}   is_company=0         Xác định profile này là Business hay Individual
<li><code>0:  Individual</code></li>
<li><code>1:  Business</code></li>
@apiParam   (Body:)    {String[]}      [face_id]            face_id
@apiParam   (Body:)    {String[]}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (Body:)    {Array}         [social_user]        Danh sách các mạng xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)    {String[]}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (Body:)    {Int}           created_account_type             Nguồn import Profile.
<li><code>15: PHONGVU_ODOO</code></li>
<li><code>16: PHONGVU_MAGENTO</code></li>
<li><code>17: PHONGVU_INHOUSE</code></li>
<li><code>18: PHONGVU_ASIA</code></li>
@apiParam   (Body:)    {Array}          [bank_acc]             Danh sách các tài khoản ngân hàng mà profile này sở hữu. Xem Data_Bank_Account bên dưới.
@apiParam   (Body:)    {Int}          [gender]             Giới tính.
<li><code>1: UNKNOWN</code></li>
<li><code>2: MALE</code></li>
<li><code>3: FEMALE</code></li>
@apiParam   (Body:)    {String}          [birthday]             Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (Body:)    {String}          fullname             Tên đầy đủ.
@apiParam   (Body:)    {String}          [address]       Địa chỉ.
@apiParam   (Body:)     {Int}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {Int}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (Body:)     {Int}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>


@apiParam   (Data_Social_User:)    {String}         social_id        Id của mạng mạng xã hội.
@apiParam   (Data_Social_User:)    {Int}            social_type      Mạng xã hội.
<li><code>1: FACEBOOK</code></li>
<li><code>2: ZALO</code></li>
<li><code>3: INSTAGRAM</code></li>
<li><code>4: YOUTUBE</code></li>
@apiParam   (Data_Social_User:)    {String}         [access_token]      Access_Token của mạng xã hội.
@apiParamExample {json} SocialUser-Example:
[{"social_id": "social_id", "social_type": 1, "access_token": "access_token"}]

@apiParam   (Data_Bank_Account:)    {String}         [bank_code]        Mã nhận dạng của ngân hàng.
@apiParam   (Data_Bank_Account:)    {String}         [bank_name]        Tên của ngân hàng.
@apiParam   (Data_Bank_Account:)    {String}         [acc_name]        Tên tài khoản ngân hàng.
@apiParam   (Data_Bank_Account:)    {String}         [acc_code]        Mã tài khoản ngân hàng.
@apiParam   (Data_Bank_Account:)    {String}         [branch_name]        Tên của chi nhánh thuộc ngân hàng.
@apiParamExample {json} Data_Bank_Account-Example:
[{"bank_code": "BFTVVNVX", "bank_name": "Bank for Foreign Trade of Vietnam", "branch_name": "Hochiminh Branch", "acc_name": "Nguyen Van A", "acc_code": "*************"}]


@apiParamExample [json] Body example:
{
    "fullname": "andrew",
    "is_company": 0,
    "phone_number": ["**********"],
    "created_account_type": 15,
    "email": ["<EMAIL>"],
    "address": "Hà Nội",
    "gender": 2,
    "birthday": "1989-12-21"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "data": {
        "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
        "merchant_id": "4a9f6012-5627-4239-b281-4adb85c00827",
        "address": "",
        "avatar": "",
        "birthday": "1993-10-10T00:00:00",
        "birth_year": 1993,
        "email": ["<EMAIL>"],
        "fax": "",
        "gender": 1,
        "hobby": [""],
        "job": [""],
        "marital_status": 1,
        "name": "andrew",
        "nation": [""],
        "phone_number": (""),
        "province_code": [""],
        "tax_code": "",
        "ward_code": [""],
        "workplace": ""
    }
}
"""

"""
@api {post} [HOST]/profiling/v3.0/customers/actions/upsert Upsert Profile.
@apiDescription Upsert Profile
@apiGroup Customers
@apiVersion 1.0.1
@apiName UpsertProfile
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.
@apiParam   (Query:)    {String}  [group]     Chuỗi query nhận diện group của Merchant.

@apiParam   (Body:)    {String[]}      [face_id]            face_id
@apiParam   (Body:)    {String[]}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (Body:)    {Array}         [social_user]        Danh sách các mạng xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)    {String[]}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (Body:)    {Int}          [gender]             Giới tính.
<li><code>1: UNKNOWN</code></li>
<li><code>2: MALE</code></li>
<li><code>3: FEMALE</code></li>
@apiParam   (Body:)    {String}          [birthday]             Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (Body:)    {String}          fullname             Tên đầy đủ.
@apiParam   (Body:)    {String}          [address]       Địa chỉ.
@apiParam   (Body:)     {Int}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {Int}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (Body:)     {Int}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>


@apiParam   (Data_Social_User:)    {String}         social_id        Id của mạng mạng xã hội.
@apiParam   (Data_Social_User:)    {Int}            social_type      Mạng xã hội.
<li><code>1: FACEBOOK</code></li>
<li><code>2: ZALO</code></li>
<li><code>3: INSTAGRAM</code></li>
<li><code>4: YOUTUBE</code></li>
@apiParam   (Data_Social_User:)    {String}         [access_token]      Access_Token của mạng xã hội.
@apiParamExample {json} SocialUser-Example:
[{"social_id": "social_id", "social_type": 1, "access_token": "access_token"}]

@apiParamExample [json] Body example:
{
    "fullname": "andrew",
    "is_company": 0,
    "phone_number": ["**********"],
    "created_account_type": 15,
    "email": ["<EMAIL>"],
    "address": "Hà Nội",
    "gender": 2,
    "birthday": "1989-12-21"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "data": {
        "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
        "merchant_id": "4a9f6012-5627-4239-b281-4adb85c00827",
        "address": "",
        "avatar": "",
        "birthday": "1993-10-10T00:00:00",
        "birth_year": 1993,
        "email": ["<EMAIL>"],
        "fax": "",
        "gender": 1,
        "hobby": [""],
        "job": [""],
        "marital_status": 1,
        "name": "andrew",
        "nation": [""],
        "phone_number": (""),
        "province_code": [""],
        "tax_code": "",
        "ward_code": [""],
        "workplace": ""
    }
}
"""

"""
@api {post} [HOST]/profiling/v3.0/customers/actions/upsert Upsert Profile.
@apiDescription Upsert Profile
@apiGroup Customers
@apiVersion 1.0.2
@apiName UpsertProfile
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.
@apiParam   (Query:)    {String}  [group]     Chuỗi query nhận diện group của Merchant.

@apiParam   (Body:)    {String[]}      [face_id]            face_id
@apiParam   (Body:)    {String[]}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (Body:)    {Array}         [social_user]        Danh sách các mạng xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)    {String[]}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (Body:)    {Int}          [gender]             Giới tính.
<li><code>1: UNKNOWN</code></li>
<li><code>2: MALE</code></li>
<li><code>3: FEMALE</code></li>
@apiParam   (Body:)    {String}          [birthday]             Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (Body:)    {String}          name             Tên đầy đủ.
@apiParam   (Body:)    {String}          [source]         Nguồn ghi nhận Profile.
@apiParam   (Body:)    {Int}          created_account_type             Kiểu tạo tài khoản.
<br/><br/>Allowed values:<br/>
<li><code>0: Web CEM</code></li>
<li><code>21: Chat Tool</code></li>

@apiParam   (Body:)    {String}          [address]       Địa chỉ.
@apiParam   (Body:)     {Int}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {Int}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (Body:)     {Int}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>


@apiParam   (Data_Social_User:)    {String}         social_id        Id của mạng mạng xã hội.
@apiParam   (Data_Social_User:)    {Int}            social_type      Mạng xã hội.
<li><code>1: FACEBOOK</code></li>
<li><code>2: ZALO</code></li>
<li><code>3: INSTAGRAM</code></li>
<li><code>4: YOUTUBE</code></li>
@apiParam   (Data_Social_User:)    {String}         [access_token]      Access_Token của mạng xã hội.
@apiParamExample {json} SocialUser-Example:
[{"social_id": "social_id", "social_type": 1, "access_token": "access_token"}]

@apiParamExample [json] Body example:
{
    "fullname": "andrew",
    "is_company": 0,
    "phone_number": ["**********"],
    "source": "Pos",
    "created_account_type": 0,
    "email": ["<EMAIL>"],
    "address": "Hà Nội",
    "gender": 2,
    "birthday": "1989-12-21"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "data": {
        "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
        "merchant_id": "4a9f6012-5627-4239-b281-4adb85c00827",
        "address": "",
        "avatar": "",
        "birthday": "1993-10-10T00:00:00",
        "birth_year": 1993,
        "email": ["<EMAIL>"],
        "fax": "",
        "gender": 1,
        "hobby": [""],
        "job": [""],
        "marital_status": 1,
        "name": "andrew",
        "nation": [""],
        "phone_number": (""),
        "province_code": [""],
        "tax_code": "",
        "ward_code": [""],
        "workplace": ""
    }
}
"""

"""
@api {post} [HOST]/profiling/v3.0/customers/actions/upsert Upsert Profile.
@apiDescription Upsert Profile
@apiGroup Customers
@apiVersion 1.0.3
@apiName UpsertProfile
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [group]     Chuỗi query nhận diện group của Merchant.


@apiParam   (Body:)    {String}          name             Tên đầy đủ.

@apiParam   (Body:)    {String}      [primary_email]              Email chính của khách hàng.
@apiParam   (Body:)    {String}      [primary_phone]       Số điện thoại chính của khách hàng.
@apiParam   (Body:)    {String}      [customer_id]         ID của khách hàng.
@apiParam   (Body:)    {String[]}      [secondary_emails]                     Các email phụ của khách hàng.
@apiParam   (Body:)    {String[]}      [secondary_phones]                     Các số điện thoại phụ của khách hàng.
@apiParam   (Body:)    {String}      [ewallet_id]           ID của ví.
@apiParam   (Body:)    {String}      [nationality]          Quốc gia của khách hàng.
@apiParam   (Body:)    {String}      [gender]               Giới tính của khách hàng. 
<li><code>UNKNOWN</code></li>
<li><code>MALE</code></li>
<li><code>FEMALE</code></li>
@apiParam   (Body:)    {String}      [birthday]             Ngày sinh của khách hàng. Format <code>YYYY-mm-DD</code>.
@apiParam   (Body:)    {String}      [province_code]        Quận/Huyện của khách hàng.
@apiParam   (Body:)    {String}      [source]               Nguồn tạo tài khoản của khách hàng.
@apiParam   (Body:)     {Int}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>


@apiParamExample [json] Body example:
{ 
  "name": "andrew",
  "primary_phone": "0914123456",
  "primary_email": "<EMAIL>",
  "secondary_phones": ["**********"],
  "secondary_emails": ["<EMAIL>"],
  "address": "Cầu Giấy, Hà Nội",
  "gender": "MALE",
  "birthday": "1989-01-22",
  "marital_status":1,
  "province_code":"Hà Nội",
  "_dyn_product_12_1579107600": ["laptop asus"],
  "_dyn_last_login_1579107600": "2020-02-12 10:55:51",
  "_dyn_total_amount_1579107600": ********
}


@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
  "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
  "profile_info": {
    "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
    "merchant_id": "4a9f6012-5627-4239-b281-4adb85c00827",
    "address": "",
    "avatar": "",
    "birthday": "1989-01-22T00:00:00",
    "birth_year": 1989,
    "email": ["<EMAIL>", "<EMAIL>"],
    "fax": "",
    "gender": 1,
    "hobby": [""],
    "job": [""],
    "marital_status": 1,
    "name": "andrew",
    "nation": [""],
    "phone_number": ["+84914123456", "+***********"],
    "province_code": [""],
    "tax_code": "",
    "ward_code": [""],
    "workplace": ""
  },
  "message": "Tạo mới profile thành công",
  "process_type": "ADD",
}
"""

************************************Bulk Upsert Profile********************************
* version: 1.0.0                                                                      *
* version: 1.0.1                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/customers/actions/bulk_upsert Bulk Upsert Profile.
@apiDescription Api hỗ trợ upsert nhiều profile. Tối đa 1 lần upsert 50 Profiles. Sử dụng Api lấy Danh sách fields để upsert profiles.
@apiGroup Customers
@apiVersion 1.0.0
@apiName BulkUpsertProfile
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [group]     Chuỗi query nhận diện group của Merchant.

@apiParam   (Body:)    {Array}      data                 Danh sách các profiles cần upsert. MAX: 50 rows
@apiParam   (data:)    {String}          name             Tên đầy đủ.
@apiParam   (data:)    {String[]}      [face_id]            face_id
@apiParam   (data:)    {String[]}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (data:)    {String[]}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (data:)    {Int}          [gender]             Giới tính.
<li><code>1: UNKNOWN</code></li>
<li><code>2: MALE</code></li>
<li><code>3: FEMALE</code></li>
@apiParam   (data:)    {String}          [birthday]             Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (data:)    {String}          [address]       Địa chỉ.

@apiParamExample [json] Body example:
{ 
  "data":[
      {
          "name": "andrew",
          "phone_number": ["**********"],
          "email": ["<EMAIL>"],
          "address": "Hà Nội",
          "gender": 2,
          "birthday": "1989-01-22",
          "_dyn_product_12_1579107600": ["laptop asus"],
          "_dyn_last_login_1579107600": "2020-02-12 10:55:51",
          "_dyn_total_amount_1579107600": ********
      }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công."
}
"""

"""
@api {post} /profiling/v3.0/customers/actions/bulk_upsert Bulk Upsert Profile.
@apiDescription Api hỗ trợ upsert nhiều profile. Tối đa 1 lần upsert 200 Profiles.
@apiGroup Customers
@apiVersion 1.0.1
@apiName BulkUpsertProfile
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [group]     Chuỗi query nhận diện group của Merchant.

@apiParam   (Body:)    {Array}      data                 Danh sách các profiles cần upsert. MAX: 200 rows
@apiParam   (data:)    {String}          name             Tên đầy đủ.

@apiParam   (data:)    {String}      [primary_email]              Email chính của khách hàng.
@apiParam   (data:)    {String}      [primary_phone]       Số điện thoại chính của khách hàng.
@apiParam   (data:)    {String}      [customer_id]         ID của khách hàng.
@apiParam   (data:)    {String[]}      [secondary_emails]                     Các email phụ của khách hàng.
@apiParam   (data:)    {String[]}      [secondary_phones]                     Các số điện thoại phụ của khách hàng.
@apiParam   (data:)    {String}      [ewallet_id]           ID của ví.
@apiParam   (data:)    {String}      [nationality]          Quốc gia của khách hàng.
@apiParam   (data:)    {String}      [gender]               Giới tính của khách hàng. 
<li><code>UNKNOWN</code></li>
<li><code>MALE</code></li>
<li><code>FEMALE</code></li>
@apiParam   (data:)    {String}      [birthday]             Ngày sinh của khách hàng. Format <code>YYYY-mm-DD</code>.
@apiParam   (data:)    {String}      [province_code]        Quận/Huyện của khách hàng.
@apiParam   (data:)    {String}      [source]               Nguồn tạo tài khoản của khách hàng.


@apiParamExample [json] Body example:
{ 
  "data":[
      {
          "name": "andrew",
          "primary_phone": "0914123456",
          "primary_email": "<EMAIL>",
          "secondary_phones": ["**********"],
          "secondary_emails": ["<EMAIL>"],
          "address": "Hà Nội",
          "gender": "MALE",
          "birthday": "1989-01-22",
          "_dyn_product_12_1579107600": ["laptop asus"],
          "_dyn_last_login_1579107600": "2020-02-12 10:55:51",
          "_dyn_total_amount_1579107600": ********
      }
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công."
}
"""



********************************** Send email to profile ******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/actions/send/email Send email to profile
@apiGroup Customers
@apiVersion 1.0.0
@apiName SendEmailToProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String}      merchant_id                 ID merchant
@apiParam   (Body:)    {String}      profile_id                     ID user
@apiParam   (Body:)    {String}      staff_id                    ID staff gửi email
@apiParam   (Body:)    {String}      from                        Email gửi
@apiParam   (Body:)    {String}      [from_name]                 Tên người gửi
@apiParam   (Body:)    {Array}       to                          Mảng email được gửi.
@apiParam   (Body:)    {Array}       [cc]                        Mảng email được cc. Maximum <code>5</code> email.
@apiParam   (Body:)    {Array}       [bcc]                       Mảng email được bcc. Maximum <code>5</code> email.
@apiParam   (Body:)    {String}      title                       Tiêu đề email
@apiParam   (Body:)    {String}      body                        Nội dung email


@apiParamExample [json] Body example:
{
  "merchant_id": "4a9f6012-5627-4239-b281-4adb85c00827",
  "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
  "staff_id": "89a79fbe-5a69-405c-a0ab-e2cb72ad3818",
  "from": "<EMAIL>",
  "from_name": "",
  "to": [
    "<EMAIL>"
  ],
  "cc": [
    "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"
  ],
  "bcc": [
    "<EMAIL>"
  ],
  "title": "Title 1",
  "body": "<h1>hello email</h1>"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Request thành công."
}
"""

************************************** Create Profile *********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/merchants/<merchant_id>/customers/actions/create Create Profile
@apiDescription API Tạo khách hàng.
@apiGroup Customers
@apiVersion 1.0.0
@apiName CreateCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam   (Body:)     {Object}    customer                            Thông tin khách hàng
@apiParam   (Body:)     {Object}    [cards]                               Thông tin thẻ khách hàng.

@apiParam   (customer:)     {String}    name                            Tên khách hàng
@apiParam   (customer:)     {String}    [primary_phone]                          Số điện thoại chính của khách hàng.
@apiParam   (customer:)     {String}    [primary_email]                                   Email chính của khách hàng.
@apiParam   (customer:)     {Array}    [secondary_phones]                        Số điện thoại phụ của khách hàng.
@apiParam   (customer:)     {Array}    [secondary_emails]                               Email phụ của khách hàng.
@apiParam   (customer:)     {Number=1:Unknown 2:Nam 3:Nữ}    gender             Giới tính.
@apiParam   (customer:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (customer:)     {Array}    [profile_address]                        Danh sách các địa chỉ của khách hàng.
@apiParam   (customer:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (customer:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (customer:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (customer:)     {String}    [tags]                        Nhãn gợi ý tìm kiếm. Format: "tags1;tags2"
@apiParam   (customer:)     {String}    [company]                        Công ty.
@apiParam   (customer:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (customer:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (customer:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (customer:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (customer:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (customer:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (customer:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (customer:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (customer:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.
@apiParam   (customer:)     {Object}    [social_user]                             Thông tin người dùng trên mạng xã hội

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.

@apiParam   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiParam   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube 

@apiSuccess {Object}    profile_info     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    cards        Dữ liệu thẻ khách hàng
@apiSuccess {Object}    process_type        Kiểu thực thi tài Profile.
<br/>
<li><code>add: Add Profile/ Tạo Profile</code></li>
<li><code>update: Update Profile / Cập nhật Profile</code></li>
<li><code>merge: Merge Profile / Hợp nhất Profile</code></li>

@apiSuccess   (profile_info:)     {String}     id                               ID khách hàng
@apiSuccess   (profile_info:)     {String}      name                            Tên khách hàng
@apiSuccess   (profile_info:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (profile_info:)     {Object}     primary_email                     Email chính của khách hàng.
@apiSuccess   (profile_info:)     {Object}     secondary_emails                     Các email phụ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    source                            Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (profile_info:)     {Number}    gender                            Giới tính
@apiSuccess   (profile_info:)     {String}     address                           Địa chỉ
@apiSuccess   (profile_info:)     {Array}    profile_address                        Danh sách các địa chỉ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (profile_info:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (profile_info:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (profile_info:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (profile_info:)     {String}    birthday                          Ngày sinh
@apiSuccess   (profile_info:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (profile_info:)     {Number}    income                            Mức thu nhập
@apiSuccess   (profile_info:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess   (profile_info:)     {Object}    social_user                             Thông tin người dùng trên mạng xã hội

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccess   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiSuccess   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube 

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "address": "dia chi 1",
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ] 
      "answers": [], 
      "association": [], 
      "avatar": null, 
      "avg_monthly_consumption": null, 
      "avg_rate": null, 
      "birthday": null, 
      "budget_high_threshold": null, 
      "budget_low_threshold": null, 
      "company": null, 
      "created_account_type": 0, 
      "created_time": "2019-03-27T17:52:46Z", 
      "description": null, 
      "district_code": null, 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "face_id": [], 
      "frequently_demands": [], 
      "gender": 2, 
      "hobby": [], 
      "income_high_threshold": null, 
      "income_low_threshold": null, 
      "income_type": null, 
      "is_non_profile": false, 
      "job": null, 
      "last_time_active": null, 
      "last_time_transaction": null, 
      "lat": null, 
      "location": null, 
      "lon": null, 
      "marital_status": null, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "Giang", 
      "nation": null, 
      "number_interactive": null, 
      "number_transaction": null, 
      "operation": null, 
      "people_id": null, 
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "position": null, 
      "predict": [], 
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      }, 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "province_code": null, 
      "religiousness": null, 
      "salary": null, 
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "social_user": [], 
      "source_id": null, 
      "source_type": null, 
      "state": null, 
      "tags": [], 
      "total_rate": null, 
      "updated_time": "2019-03-27T17:52:46Z", 
      "ward_code": null, 
      "website": null, 
      "workplace": null
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

************************************** Create Profile *********************************
* version: 1.0.1                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/merchants/<merchant_id>/customers/actions/create Create Profile
@apiDescription API Tạo khách hàng.
@apiGroup Customers
@apiVersion 1.0.1
@apiName CreateCustomer
@apiUse merchant_id_header
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.
@apiParam   (Query:)    {String}  [group]     Chuỗi query nhận diện group của Merchant.
@apiParam   (Body:)     {Object}    customer                            Thông tin khách hàng

@apiParam   (customer:)     {String}    name                            Tên khách hàng
@apiParam   (customer:)     {String}    [phone_number_1]                          Số điện thoại chính của khách hàng.
@apiParam   (customer:)     {String}    [email_1]                                   Email chính của khách hàng.
@apiParam   (customer:)     {Array}    [phone_number_2]                        Số điện thoại phụ của khách hàng.
@apiParam   (customer:)     {Array}    [email_2]                               Email phụ của khách hàng.
@apiParam   (customer:)     {Number=1:Unknown 2:Nam 3:Nữ}    gender             Giới tính.
@apiParam   (customer:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (customer:)     {Array}    [profile_address]                        Danh sách các địa chỉ của khách hàng.
@apiParam   (customer:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (customer:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (customer:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (customer:)     {String}    [tags]                        Nhãn gợi ý tìm kiếm. Format: "tags1;tags2"
@apiParam   (customer:)     {String}    [company]                        Công ty.
@apiParam   (customer:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (customer:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (customer:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (customer:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (customer:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (customer:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (customer:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (customer:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (customer:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.
@apiParam   (customer:)     {Object}    [social_user]                             Thông tin người dùng trên mạng xã hội

@apiParam   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiParam   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube 

@apiSuccess {Object}    profile_info     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    process_type        Kiểu thực thi tài khoản Profile.
<br/>
<li><code>add: Add Profile/ Tạo Profile</code></li>
<li><code>update: Update Profile / Cập nhật Profile</code></li>
<li><code>merge: Merge Profile / Hợp nhất Profile</code></li>

@apiSuccess   (profile_info:)     {String}     id                               ID khách hàng
@apiSuccess   (profile_info:)     {String}      name                            Tên khách hàng
@apiSuccess   (profile_info:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (profile_info:)     {Object}     primary_email                     Email chính của khách hàng.
@apiSuccess   (profile_info:)     {Object}     secondary_emails                     Các email phụ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    source                            Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (profile_info:)     {Number}    gender                            Giới tính
@apiSuccess   (profile_info:)     {String}     address                           Địa chỉ
@apiSuccess   (profile_info:)     {Array}    profile_address                        Danh sách các địa chỉ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (profile_info:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (profile_info:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (profile_info:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (profile_info:)     {String}    birthday                          Ngày sinh
@apiSuccess   (profile_info:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (profile_info:)     {Number}    income                            Mức thu nhập
@apiSuccess   (profile_info:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess   (profile_info:)     {Object}    social_user                             Thông tin người dùng trên mạng xã hội

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccess   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiSuccess   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube 

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "address": "dia chi 1",
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ] 
      "answers": [], 
      "association": [], 
      "avatar": null, 
      "avg_monthly_consumption": null, 
      "avg_rate": null, 
      "birthday": null, 
      "budget_high_threshold": null, 
      "budget_low_threshold": null, 
      "company": null, 
      "created_account_type": 0, 
      "created_time": "2019-03-27T17:52:46Z", 
      "description": null, 
      "district_code": null, 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "face_id": [], 
      "frequently_demands": [], 
      "gender": 2, 
      "hobby": [], 
      "income_high_threshold": null, 
      "income_low_threshold": null, 
      "income_type": null, 
      "is_non_profile": false, 
      "job": null, 
      "last_time_active": null, 
      "last_time_transaction": null, 
      "lat": null, 
      "location": null, 
      "lon": null, 
      "marital_status": null, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "Giang", 
      "nation": null, 
      "number_interactive": null, 
      "number_transaction": null, 
      "operation": null, 
      "people_id": null, 
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "position": null, 
      "predict": [], 
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      }, 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "province_code": null, 
      "religiousness": null, 
      "salary": null, 
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "social_user": [], 
      "source_id": null, 
      "source_type": null, 
      "state": null, 
      "tags": [], 
      "total_rate": null, 
      "updated_time": "2019-03-27T17:52:46Z", 
      "ward_code": null, 
      "website": null, 
      "workplace": null
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

**************************** Update Profile ***********************************
* version: 1.0.0                                                              *                                                            *
* version: 1.0.1                                                              *
*******************************************************************************
"""
@api {patch} [HOST]/profiling/v2.0/merchants/<merchant_id>/customers/actions/update/<customer_id> Update Profile
@apiDescription API update thông tin khách hàng.
@apiGroup Customers
@apiVersion 1.0.0
@apiName UpdateCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.
@apiParam      (Resource:)    {String}    customer_id                             ID của khách hàng.

@apiParam   (Body:)     {Object}    [customer]                            Thông tin khách hàng
@apiParam   (Body:)     {Object}    [cards]                               Thông tin thẻ khách hàng.

@apiParam   (customer:)     {String}    [name]                            Tên khách hàng
@apiParam   (customer:)     {String}    [phone_number_1]                          Số điện thoại chính của khách hàng.
@apiParam   (customer:)     {String}    [email_1]                                   Email chính của khách hàng.
@apiParam   (customer:)     {List}    [phone_number_2]                        Các số điện thoại phụ của khách hàng.
@apiParam   (customer:)     {List}    [email_2]                               Các email phụ của khách hàng.
@apiParam   (customer:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (customer:)     {Array}    [profile_address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (customer:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (customer:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (customer:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (customer:)     {String}    [tags]                        Nhãn gợi ý tìm kiếm. Format: "tags1;tags2"
@apiParam   (customer:)     {String}    [company]                        Công ty.
@apiParam   (customer:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (customer:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (customer:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (customer:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (customer:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (customer:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (customer:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (customer:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (customer:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.

@apiSuccess {Object}    profile_info     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    cards        Dữ liệu thẻ khách hàng
@apiSuccess {Object}    process_type        Kiểu thực thi tài Profile.
<br/>
<li><code>add: Add Profile/ Tạo Profile</code></li>
<li><code>update: Update Profile / Cập nhật Profile</code></li>
<li><code>merge: Merge Profile / Hợp nhất Profile</code></li>

@apiSuccess (profile_info:)     {String}     id                               ID khách hàng
@apiSuccess   (profile_info:)     {String}      name                            Tên khách hàng
@apiSuccess   (profile_info:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (profile_info:)     {Object}     primary_email                     Email chính của khách hàng.
@apiSuccess   (profile_info:)     {Object}     secondary_emails                     Các email phụ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    source                            Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (profile_info:)     {Number}    gender                            Giới tính
@apiSuccess   (profile_info:)     {String}    address                           Địa chỉ
@apiSuccess   (profile_info:)     {Array}    profile_address                    Danh sách các địa chỉ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (profile_info:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (profile_info:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (profile_info:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (profile_info:)     {String}    birthday                          Ngày sinh
@apiSuccess   (profile_info:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (profile_info:)     {Number}    income                            Mức thu nhập
@apiSuccess   (profile_info:)     {Number}    budget                            Mức tiêu dùng

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     user_card_id                     Id khách hàng thẻ
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Cập nhật khách hàng thành công.", 
    "process_type": "update", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "address": 'dia chi 1', 
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ]
      "answers": [], 
      "association": [], 
      "avatar": null, 
      "avg_monthly_consumption": null, 
      "avg_rate": null, 
      "birthday": null, 
      "budget_high_threshold": null, 
      "budget_low_threshold": null, 
      "company": null, 
      "created_account_type": 0, 
      "created_time": "2019-03-27T17:52:46Z", 
      "description": null, 
      "district_code": null, 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "face_id": [], 
      "frequently_demands": [], 
      "gender": 2, 
      "hobby": [], 
      "income_high_threshold": null, 
      "income_low_threshold": null, 
      "income_type": null, 
      "is_non_profile": false, 
      "job": null, 
      "last_time_active": null, 
      "last_time_transaction": null, 
      "lat": null, 
      "location": null, 
      "lon": null, 
      "marital_status": null, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "Giang", 
      "nation": null, 
      "number_interactive": null, 
      "number_transaction": null, 
      "operation": null, 
      "people_id": null, 
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "position": null, 
      "predict": [], 
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      }, 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "province_code": null, 
      "religiousness": null, 
      "salary": null, 
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "social_user": [], 
      "source_id": null, 
      "source_type": null, 
      "state": null, 
      "tags": [], 
      "total_rate": null, 
      "updated_time": "2019-03-27T17:52:46Z", 
      "ward_code": null, 
      "website": null, 
      "workplace": null
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

"""
@api {patch} [HOST]/profiling/v3.0/merchants/<merchant_id>/customers/actions/update/<customer_id> Update Profile
@apiDescription API update thông tin khách hàng.
@apiGroup Customers
@apiVersion 1.0.1
@apiName UpdateCustomer

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.
@apiParam      (Resource:)    {String}    customer_id                             ID của khách hàng.

@apiParam   (Body:)     {Object}    [customer]                            Thông tin khách hàng

@apiParam   (customer:)     {String}    [name]                            Tên khách hàng
@apiParam   (customer:)     {String}    [phone_number_1]                          Số điện thoại chính của khách hàng.
@apiParam   (customer:)     {String}    [email_1]                                   Email chính của khách hàng.
@apiParam   (customer:)     {List}    [phone_number_2]                        Các số điện thoại phụ của khách hàng.
@apiParam   (customer:)     {List}    [email_2]                               Các email phụ của khách hàng.
@apiParam   (customer:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (customer:)     {Array}    [profile_address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (customer:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (customer:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (customer:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (customer:)     {String}    [tags]                        Nhãn gợi ý tìm kiếm. Format: "tags1;tags2"
@apiParam   (customer:)     {String}    [company]                        Công ty.
@apiParam   (customer:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (customer:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (customer:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (customer:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (customer:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (customer:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (customer:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (customer:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (customer:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.

@apiSuccess {Object}    profile_info     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    process_type        Kiểu thực thi tài Profile.
<br/>
<li><code>add: Add Profile/ Tạo Profile</code></li>
<li><code>update: Update Profile / Cập nhật Profile</code></li>
<li><code>merge: Merge Profile / Hợp nhất Profile</code></li>

@apiSuccess (profile_info:)     {String}     id                               ID khách hàng
@apiSuccess   (profile_info:)     {String}      name                            Tên khách hàng
@apiSuccess   (profile_info:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (profile_info:)     {Object}     primary_email                     Email chính của khách hàng.
@apiSuccess   (profile_info:)     {Object}     secondary_emails                     Các email phụ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    source                            Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (profile_info:)     {Number}    gender                            Giới tính
@apiSuccess   (profile_info:)     {String}    address                           Địa chỉ
@apiSuccess   (profile_info:)     {Array}    profile_address                    Danh sách các địa chỉ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (profile_info:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (profile_info:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (profile_info:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (profile_info:)     {String}    birthday                          Ngày sinh
@apiSuccess   (profile_info:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (profile_info:)     {Number}    income                            Mức thu nhập
@apiSuccess   (profile_info:)     {Number}    budget                            Mức tiêu dùng

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     user_card_id                     Id khách hàng thẻ
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Cập nhật khách hàng thành công.", 
    "process_type": "update", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "address": 'dia chi 1', 
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ]
      "answers": [], 
      "association": [], 
      "avatar": null, 
      "avg_monthly_consumption": null, 
      "avg_rate": null, 
      "birthday": null, 
      "budget_high_threshold": null, 
      "budget_low_threshold": null, 
      "company": null, 
      "created_account_type": 0, 
      "created_time": "2019-03-27T17:52:46Z", 
      "description": null, 
      "district_code": null, 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "face_id": [], 
      "frequently_demands": [], 
      "gender": 2, 
      "hobby": [], 
      "income_high_threshold": null, 
      "income_low_threshold": null, 
      "income_type": null, 
      "is_non_profile": false, 
      "job": null, 
      "last_time_active": null, 
      "last_time_transaction": null, 
      "lat": null, 
      "location": null, 
      "lon": null, 
      "marital_status": null, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "Giang", 
      "nation": null, 
      "number_interactive": null, 
      "number_transaction": null, 
      "operation": null, 
      "people_id": null, 
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "position": null, 
      "predict": [], 
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      }, 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "province_code": null, 
      "religiousness": null, 
      "salary": null, 
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "social_user": [], 
      "source_id": null, 
      "source_type": null, 
      "state": null, 
      "tags": [], 
      "total_rate": null, 
      "updated_time": "2019-03-27T17:52:46Z", 
      "ward_code": null, 
      "website": null, 
      "workplace": null
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

****************************** List Customer **********************************
* version: 1.0.8                                                              *
* version: 1.0.7                                                              *
* version: 1.0.6                                                              *
* version: 1.0.5                                                              *
* version: 1.0.4                                                              *
* version: 1.0.3                                                              *
* version: 1.0.2                                                              *
* version: 1.0.1                                                              *
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.7
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm.
@apiParam      (Body:)     {Array}    profile_group                       Danh sách các Group mà Staff này quản lý. VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Body:)     {Array}    search_with_fields                  Danh sách field search với value search_text [name , email, phone_number, tags, devices, customer_id, cif, social_name,profile_identify] không truyền thì search hết
@apiParam      (Body:)     {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "fields": ["profile_id","name"],
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {Array}        phone_number                  Mảng điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán
@apiSuccess  (data)      {Array}         profile_address              Mảng địa chỉ của user
@apiSuccess  (data)      {Object}        primary_email                Email chính của user
@apiSuccess  (data)      {Object}        secondary_emails             Các email phụ của user
@apiSuccess  (data)      {Object}        primary_phone                Điện thoại chính của user
@apiSuccess  (data)      {Object}        secondary_phones             Các số điện thoại phụ của user
@apiSuccess  (data)      {Number}        degree                       Thông tin trình độ học vấn của profile
@apiSuccess  (data)      {Number}        income_family                Thông tin thu nhập gia đình
@apiSuccess  (data)      {Number}        number_childs                Thông tin số lượng con của user
@apiSuccess  (data)      {Array}         childs                       Thông tin con của user
@apiSuccess  (data)      {Array}         transaction_event            Thông tin giao dịch của user
@apiSuccess  (data)      {Array}         answers                      Mảng câu trả lời của user
@apiSuccess  (data)      {Number}        created_account_type         Nguồn ghi nhận thông tin khách hàng
@apiSuccess  (data)      {String}        source                       Nguồn ghi nhận thông tin khách hàng
@apiSuccess  (data)      {Number}        salary                       Mức lương của user
@apiSuccess  (data)      {String}        workplace                    Nơi làm việc
@apiSuccess  (data)      {String}        position                     Chức vụ của user
@apiSuccess  (data)      {Array}         tags                         Tag tìm kiếm

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_id_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Youtube</code></li>
<li><code>6:Line</code></li>

@apiSuccess (primary_email)       {Number}     primary_email       Status mail<br/>
Allowed values:<br/>
<li><code>-1:Invalid</code></li>
<li><code>1:Valid</code></li>
<li><code>0:Uncheck</code></li>
<li><code>2:UNKNOW</code></li>
@apiSuccess (push_id)       {Number}     os_type       Loại push<br/>
Allowed values:<br/>
<li><code>1:IOS</code></li>
<li><code>2:ANDROID</code></li>
<li><code>3:VIB_APP</code></li>
<li><code>4:ICHECK_APP</code></li>
<li><code>5:WEB_PUSH_FIREBASE</code></li>
<li><code>6:WEB_PUSH_APNS</code></li>
<li><code>7:MSB_PLUS</code></li>
<li><code>8:MSB_MBANK</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=0: "Khóa thẻ",1: "Đã duyệt",2: "Chờ duyệt",3: "Hết hạn",4: "Chưa có thẻ",-1: "Hủy thẻ"}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data": [
        {
            "address": "Tỉnh Thanh Hóa",
            "answers": [],
            "avatar": null,
            "birthday": "1981-07-28T00:00:00Z",
            "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
            "created_account_type": 0,
            "created_time": "2018-07-27T09:53:21Z",
            "display_name": "Vũ Thị thọ 123",
            "district_code": null,
            "email": [
                "<EMAIL>",
                "<EMAIL>"
            ],
            "frequently_demands": [
                {
                    "id": 2,
                    "name": "Mua sắm online"
                }
            ],
            "gender": 3,
            "hobby": null,
            "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
            "income_high_threshold": ********,
            "income_low_threshold": 5000000,
            "income_type": 1,
            "job": null,
            "location": null,
            "marital_status": null,
            "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
            "mkt_step": 1,
            "nation": null,
            "people_id": null,
            "phone_number": [
                "+************"
            ],
            "position": null,
            "predict": [
                {
                    "key": "phone_number",
                    "value": "+************",
                    "confidence": 0.****************
                }
            ],
            "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
            "province_code": 38,
            "religiousness": null,
            "salary": null,
            "social_user": [
                {
                    "id_social": "2139374173003427",
                    "social_type": 1
                }
            ],
            "trusted_level": 100,
            "updated_time": "2018-07-28T04:57:35Z",
            "validation": 32,
            "verify_status": null,
            "ward_code": null,
            "workplace": null,
            "cards": [
                {
                    "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
                    "code": "124364",
                    "status": 1,
                    "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
                }
            ],
            "primary_email": {
                "last_check": "Wed, 27 Mar 2019 17:52:46 GMT",
                "email": "g*****<EMAIL>",
                "encrypt": "+bN6iOUdmr6E0yQPuav9Bdl2JDI6ctoFqP7IjeMpkRN3tTKUudwLvC6L64565a3103120bd931d68ed69aadd3e2",
                "status": 0
            },
            "primary_phone": {
                "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT",
                "phone_number": "+***********",
                "encrypt": "yGXDlgPnjzrGkuvzPET/4W2noT1zIAd6e4197e0213fe98dbf079c7089e8dc7",
                "status": 0
            },
            "secondary_emails": {
                "secondary": [
                    {
                        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT",
                        "email": "<EMAIL>",
                        "status": 0
                    }
                ],
                "secondary_size": 1
            },
            "secondary_phones": {
                "secondary": [
                    {
                        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT",
                        "phone_number": "+***********",
                        "status": 0
                    }
                ],
                "secondary_size": 1
            },
            "profile_address": [
                "dia chi 1",
                "dia chi 2"
            ],
            "tags": [],
            "degree": 1,
            "income_family": 0,
            "number_childs": 0
        },
      ...
    ],
    "c_data": [
        {
            "card_status": {
                "id": 1,
                "name": "************ng"
            },
            "district_code": {
                "id": 4,
                "name": "**********Biên"
            },
            "gender": {
                "id": 3,
                "name": "****ữ"
            },
            "job": {
                "id": 34,
                "name": "****IT"
            },
            "marital_status": {
                "id": 1,
                "name": "*****hân"
            },
            "nation": {
                "id": 1,
                "name": "****nh"
            },
            "operation": {
                "id": 2,
                "name": "*******************nh"
            },
            "profile_id": "**********************************7f",
            "province_code": {
                "id": 1,
                "name": "****Nội"
            },
            "religiousness": {
                "id": 2,
                "name": "*******áo"
            }
        }
    ],
    "paging": {
        ...
    }
}
"""

"""
@api {post} [HOST]/profiling/v3.1/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.8
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam       (Param:)     {String}    [info_paging]      info_paging=True khi cần thông tin để chia page như total_count, page_count, mặc định False

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}    profile_group                       Danh sách các Group mà Staff này quản lý. VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Body:)     {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Body:)     {Array}    search_with_fields                  Danh sách field search với value search_text [name , email, phone_number, tags, devices, customer_id, cif, social_name,profile_identify] không truyền thì search hết
@apiParam      (Body:)     {String}    [get_all_data]   True lấy hết dữ liệu giới hạn field, không check quyền
@apiParam      (Body:)     {Array}    [ignore_profile_id]   Loại bỏ đi các profiile_id trong mảng
@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "fields": ["profile_id","name"],
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {Array}        phone_number                  Mảng điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán
@apiSuccess  (data)      {Array}         profile_address              Mảng địa chỉ của user
@apiSuccess  (data)      {Object}        primary_email                Email chính của user
@apiSuccess  (data)      {Object}        secondary_emails             Các email phụ của user
@apiSuccess  (data)      {Object}        primary_phone                Điện thoại chính của user
@apiSuccess  (data)      {Object}        secondary_phones             Các số điện thoại phụ của user
@apiSuccess  (data)      {Number}        degree                       Thông tin trình độ học vấn của profile
@apiSuccess  (data)      {Number}        income_family                Thông tin thu nhập gia đình
@apiSuccess  (data)      {Number}        number_childs                Thông tin số lượng con của user
@apiSuccess  (data)      {Array}         childs                       Thông tin con của user
@apiSuccess  (data)      {Array}         transaction_event            Thông tin giao dịch của user
@apiSuccess  (data)      {Array}         answers                      Mảng câu trả lời của user
@apiSuccess  (data)      {Number}        created_account_type         Nguồn ghi nhận thông tin khách hàng
@apiSuccess  (data)      {String}        source                       Nguồn ghi nhận thông tin khách hàng
@apiSuccess  (data)      {Number}        salary                       Mức lương của user
@apiSuccess  (data)      {String}        workplace                    Nơi làm việc
@apiSuccess  (data)      {String}        position                     Chức vụ của user
@apiSuccess  (data)      {Array}         tags                         Tag tìm kiếm

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_id_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Youtube</code></li>
<li><code>6:Line</code></li>

@apiSuccess (primary_email)       {Number}     primary_email       Status mail<br/>
Allowed values:<br/>
<li><code>-1:Invalid</code></li>
<li><code>1:Valid</code></li>
<li><code>0:Uncheck</code></li>
<li><code>2:UNKNOW</code></li>
@apiSuccess (push_id)       {Number}     os_type       Loại push<br/>
Allowed values:<br/>
<li><code>1:IOS</code></li>
<li><code>2:ANDROID</code></li>
<li><code>3:VIB_APP</code></li>
<li><code>4:ICHECK_APP</code></li>
<li><code>5:WEB_PUSH_FIREBASE</code></li>
<li><code>6:WEB_PUSH_APNS</code></li>
<li><code>7:MSB_PLUS</code></li>
<li><code>8:MSB_MBANK</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=0: "Khóa thẻ",1: "Đã duyệt",2: "Chờ duyệt",3: "Hết hạn",4: "Chưa có thẻ",-1: "Hủy thẻ"}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data": [
        {
            "address": "Tỉnh Thanh Hóa",
            "answers": [],
            "avatar": null,
            "birthday": "1981-07-28T00:00:00Z",
            "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
            "created_account_type": 0,
            "created_time": "2018-07-27T09:53:21Z",
            "display_name": "Vũ Thị thọ 123",
            "district_code": null,
            "email": [
                "<EMAIL>",
                "<EMAIL>"
            ],
            "frequently_demands": [
                {
                    "id": 2,
                    "name": "Mua sắm online"
                }
            ],
            "gender": 3,
            "hobby": null,
            "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
            "income_high_threshold": ********,
            "income_low_threshold": 5000000,
            "income_type": 1,
            "job": null,
            "location": null,
            "marital_status": null,
            "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
            "mkt_step": 1,
            "nation": null,
            "people_id": null,
            "phone_number": [
                "+************"
            ],
            "position": null,
            "predict": [
                {
                    "key": "phone_number",
                    "value": "+************",
                    "confidence": 0.****************
                }
            ],
            "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
            "province_code": 38,
            "religiousness": null,
            "salary": null,
            "social_user": [
                {
                    "id_social": "2139374173003427",
                    "social_type": 1
                }
            ],
            "trusted_level": 100,
            "updated_time": "2018-07-28T04:57:35Z",
            "validation": 32,
            "verify_status": null,
            "ward_code": null,
            "workplace": null,
            "cards": [
                {
                    "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
                    "code": "124364",
                    "status": 1,
                    "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
                }
            ],
            "primary_email": {
                "last_check": "Wed, 27 Mar 2019 17:52:46 GMT",
                "email": "g*****<EMAIL>",
                "encrypt": "+bN6iOUdmr6E0yQPuav9Bdl2JDI6ctoFqP7IjeMpkRN3tTKUudwLvC6L64565a3103120bd931d68ed69aadd3e2",
                "status": 0
            },
            "primary_phone": {
                "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT",
                "phone_number": "+***********",
                "encrypt": "yGXDlgPnjzrGkuvzPET/4W2noT1zIAd6e4197e0213fe98dbf079c7089e8dc7",
                "status": 0
            },
            "secondary_emails": {
                "secondary": [
                    {
                        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT",
                        "email": "<EMAIL>",
                        "status": 0
                    }
                ],
                "secondary_size": 1
            },
            "secondary_phones": {
                "secondary": [
                    {
                        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT",
                        "phone_number": "+***********",
                        "status": 0
                    }
                ],
                "secondary_size": 1
            },
            "profile_address": [
                "dia chi 1",
                "dia chi 2"
            ],
            "tags": [],
            "degree": 1,
            "income_family": 0,
            "number_childs": 0
        },
      ...
    ],
    "c_data": [
        {
            "card_status": {
                "id": 1,
                "name": "************ng"
            },
            "district_code": {
                "id": 4,
                "name": "**********Biên"
            },
            "gender": {
                "id": 3,
                "name": "****ữ"
            },
            "job": {
                "id": 34,
                "name": "****IT"
            },
            "marital_status": {
                "id": 1,
                "name": "*****hân"
            },
            "nation": {
                "id": 1,
                "name": "****nh"
            },
            "operation": {
                "id": 2,
                "name": "*******************nh"
            },
            "profile_id": "**********************************7f",
            "province_code": {
                "id": 1,
                "name": "****Nội"
            },
            "religiousness": {
                "id": 2,
                "name": "*******áo"
            }
        }
    ],
    "paging": {
        ...
    }
}
"""
*********
"""
@api {post} [HOST]/profiling/v3.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.6
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}    profile_group                       Danh sách các Group mà Staff này quản lý. VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Body:)     {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "fields": ["profile_id","name"],
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {Array}        phone_number                  Mảng điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán
@apiSuccess  (data)      {Array}         profile_address              Mảng địa chỉ của user
@apiSuccess  (data)      {Object}        primary_email                Email chính của user
@apiSuccess  (data)      {Object}        secondary_emails             Các email phụ của user
@apiSuccess  (data)      {Object}        primary_phone                Điện thoại chính của user
@apiSuccess  (data)      {Object}        secondary_phones             Các số điện thoại phụ của user
@apiSuccess  (data)      {Number}        degree                       Thông tin trình độ học vấn của profile
@apiSuccess  (data)      {Number}        income_family                Thông tin thu nhập gia đình
@apiSuccess  (data)      {Number}        number_childs                Thông tin số lượng con của user
@apiSuccess  (data)      {Array}         childs                       Thông tin con của user
@apiSuccess  (data)      {Array}         transaction_event            Thông tin giao dịch của user
@apiSuccess  (data)      {Array}         answers                      Mảng câu trả lời của user
@apiSuccess  (data)      {Number}        created_account_type         Nguồn ghi nhận thông tin khách hàng
@apiSuccess  (data)      {String}        source                       Nguồn ghi nhận thông tin khách hàng
@apiSuccess  (data)      {Number}        salary                       Mức lương của user
@apiSuccess  (data)      {String}        workplace                    Nơi làm việc
@apiSuccess  (data)      {String}        position                     Chức vụ của user
@apiSuccess  (data)      {Array}         tags                         Tag tìm kiếm

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_id_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Youtube</code></li>
<li><code>6:Line</code></li>

@apiSuccess (primary_email)       {Number}     primary_email       Status mail<br/>
Allowed values:<br/>
<li><code>-1:Invalid</code></li>
<li><code>1:Valid</code></li>
<li><code>0:Uncheck</code></li>
<li><code>2:UNKNOW</code></li>
@apiSuccess (push_id)       {Number}     os_type       Loại push<br/>
Allowed values:<br/>
<li><code>1:IOS</code></li>
<li><code>2:ANDROID</code></li>
<li><code>3:VIB_APP</code></li>
<li><code>4:ICHECK_APP</code></li>
<li><code>5:WEB_PUSH_FIREBASE</code></li>
<li><code>6:WEB_PUSH_APNS</code></li>
<li><code>7:MSB_PLUS</code></li>
<li><code>8:MSB_MBANK</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=0: "Khóa thẻ",1: "Đã duyệt",2: "Chờ duyệt",3: "Hết hạn",4: "Chưa có thẻ",-1: "Hủy thẻ"}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": ['<EMAIL>', '<EMAIL>'],
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": ["+************"],
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ],
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "email": "g*****<EMAIL>",
        "encrypt": "+bN6iOUdmr6E0yQPuav9Bdl2JDI6ctoFqP7IjeMpkRN3tTKUudwLvC6L64565a3103120bd931d68ed69aadd3e2",
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********",
        "encrypt": "yGXDlgPnjzrGkuvzPET/4W2noT1zIAd6e4197e0213fe98dbf079c7089e8dc7",
        "status": 0
      },
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "email": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      },
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ],
      "tags": [],
      "degree": 1,
      "income_family": 0,
      "number_childs": 0
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
*********
"""
@api {post} [HOST]/profiling/v3.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.5
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}    profile_group                       Danh sách các Group mà Staff này quản lý. VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Body:)     {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "fields": ["profile_id","name"],
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {Array}        phone_number                  Mảng điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán
@apiSuccess  (data)      {Array}         profile_address              Mảng địa chỉ của user
@apiSuccess  (data)      {Object}        primary_email                Email chính của user
@apiSuccess  (data)      {Object}        secondary_emails             Các email phụ của user
@apiSuccess  (data)      {Object}        primary_phone                Điện thoại chính của user
@apiSuccess  (data)      {Object}        secondary_phones             Các số điện thoại phụ của user
@apiSuccess  (data)      {Number}        degree                       Thông tin trình độ học vấn của profile
@apiSuccess  (data)      {Number}        income_family                Thông tin thu nhập gia đình
@apiSuccess  (data)      {Number}        number_childs                Thông tin số lượng con của user
@apiSuccess  (data)      {Array}         childs                       Thông tin con của user
@apiSuccess  (data)      {Array}         transaction_event            Thông tin giao dịch của user
@apiSuccess  (data)      {Array}         answers                      Mảng câu trả lời của user
@apiSuccess  (data)      {Number}        created_account_type         Nguồn ghi nhận thông tin khách hàng
@apiSuccess  (data)      {String}        source                       Nguồn ghi nhận thông tin khách hàng
@apiSuccess  (data)      {Number}        salary                       Mức lương của user
@apiSuccess  (data)      {String}        workplace                    Nơi làm việc
@apiSuccess  (data)      {String}        position                     Chức vụ của user
@apiSuccess  (data)      {Array}         tags                         Tag tìm kiếm

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:Youtube</code></li>
<li><code>6:Line</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=0: "Khóa thẻ",1: "Đã duyệt",2: "Chờ duyệt",3: "Hết hạn",4: "Chưa có thẻ",-1: "Hủy thẻ"}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": ['<EMAIL>', '<EMAIL>'],
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": ["+************"],
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ],
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      },
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      },
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ],
      "tags": [],
      "degree": 1,
      "income_family": 0,
      "number_childs": 0
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
*********
"""
@api {post} [HOST]/profiling/v2.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.4
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Body:)     {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "fields": ["profile_id","name"],
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {Array}        phone_number                  Mảng điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán
@apiSuccess  (data)      {Array}         profile_address              Mảng địa chỉ của user
@apiSuccess  (data)      {Object}        primary_email                Email chính của user
@apiSuccess  (data)      {Object}        secondary_emails             Các email phụ của user
@apiSuccess  (data)      {Object}        primary_phone                Điện thoại chính của user
@apiSuccess  (data)      {Object}        secondary_phones             Các số điện thoại phụ của user
@apiSuccess  (data)      {Number}        degree                       Thông tin trình độ học vấn của profile
@apiSuccess  (data)      {Number}        income_family                Thông tin thu nhập gia đình
@apiSuccess  (data)      {Number}        number_childs                Thông tin số lượng con của user
@apiSuccess  (data)      {Array}         childs                       Thông tin con của user
@apiSuccess  (data)      {Array}         transaction_event            Thông tin giao dịch của user
@apiSuccess  (data)      {Array}         answers                      Mảng câu trả lời của user
@apiSuccess  (data)      {Number}        created_account_type         Nguồn ghi nhận thông tin khách hàng
@apiSuccess  (data)      {String}        source                       Nguồn ghi nhận thông tin khách hàng
@apiSuccess  (data)      {Number}        salary                       Mức lương của user
@apiSuccess  (data)      {String}        workplace                    Nơi làm việc
@apiSuccess  (data)      {String}        position                     Chức vụ của user
@apiSuccess  (data)      {Array}         tags                         Tag tìm kiếm

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=0: "Khóa thẻ",1: "Đã duyệt",2: "Chờ duyệt",3: "Hết hạn",4: "Chưa có thẻ",-1: "Hủy thẻ"}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": ['<EMAIL>', '<EMAIL>'],
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": ["+************"],
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ],
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      },
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      },
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ],
      "tags": [],
      "degree": 1,
      "income_family": 0,
      "number_childs": 0
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
*********
"""
@api {post} [HOST]/profiling/v2.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.3
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=1:Pending 2:Approval 3:Cancelled}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": "['<EMAIL>', '<EMAIL>']",
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": "+************",
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ]
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
*********
"""
@api {post} [HOST]/profiling/v2.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.2
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=1:Pending 2:Approval 3:Cancelled}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": "['<EMAIL>', '<EMAIL>']",
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": "+************",
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ]
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
*********
"""
@api {post} [HOST]/profiling/v2.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.1
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=1:Pending 2:Approval 3:Cancelled}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": "['<EMAIL>', '<EMAIL>']",
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": "+************",
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ]
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
************
"""
@api {post} [HOST]/profiling/v2.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.0
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}
@apiParamExample    {json}    Body example Reachable:
{
    "fields": ["profile_id","name"],
    "profile_filter": [
      {
        "criteria_key": "cri_social_id_type",
        "operator_key": "op_is_equal",
        "values": [
          1 #1=fb 2=zalo
        ]
      },
      {
        "criteria_key": "cri_email",
        "operator_key": "op_is_not_empty",
        "values": [
          "op_is_not_empty", 
          1, 0, 2 #co mail Uncheck = 0 Valid = 1 UNKNOW = 2
        ]
      },
      {
        "criteria_key": "cri_phone",
        "operator_key": "op_is_not_empty",
        "values": [
          "op_is_not_empty" #ton tai sdt
        ]
      },
       {
        "criteria_key": "cri_mobile_os_type",
        "operator_key": "op_is_in",
        "values": [
          1,2,3,4,7,8 #app #web 5,6
        ]
      }
    ],
    "profile_group": [
      "11f16bb8-8ff6-4ca0-b7b4-082cf34dc7b4",
      "afb8c487-ee05-488b-9a3f-a5f461d4da96"
    ]
  }
@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (primary_email)       {Number}     primary_email       Status mail<br/>
Allowed values:<br/>
<li><code>-1:Invalid</code></li>
<li><code>1:Valid</code></li>
<li><code>0:Uncheck</code></li>
<li><code>2:UNKNOW</code></li>
@apiSuccess (push_id)       {Number}     os_type       Loại push<br/>
Allowed values:<br/>
<li><code>1:IOS</code></li>
<li><code>2:ANDROID</code></li>
<li><code>3:VIB_APP</code></li>
<li><code>4:ICHECK_APP</code></li>
<li><code>5:WEB_PUSH_FIREBASE</code></li>
<li><code>6:WEB_PUSH_APNS</code></li>
<li><code>7:MSB_PLUS</code></li>
<li><code>8:MSB_MBANK</code></li>
@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": "['<EMAIL>', '<EMAIL>']",
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": "+************",
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null
    },
    ...
  ],
  "paging": {
    ...
  }
}
@apiSuccessExample     {json}   Reachable Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "address": null,
            "avatar": "https://api-test1.mobio.vn/social/static/user/131934164746039",
            "created_time": "2020-05-09T07:43:59.896Z",
            "id": "0628ce17-125d-4f91-aa1a-7b844675a401",
            "is_non_profile": false,
            "merchant_id": [
                "1cb2a489-b34a-41b3-aa7d-f1efc580d687"
            ],
            "name": "VU HOANG NAM",
            "predict": [],
            "profile_address": [],
            "profile_id": "0628ce17-125d-4f91-aa1a-7b844675a401",
            "profile_tags": [],
            "social_name": [
                {
                    "id": 1,
                    "name": "Huyền Khánh",
                    "social_id": "131934164746039"
                }
            ],
            "social_user": [
                {
                    "access_token": null,
                    "social_id": "131934164746039",
                    "social_id_type": 1,
                    "social_type": 1
                }
            ],
            "tags": [],
            "updated_time": "2020-10-20T08:46:53.740Z"
        }
    ],
    "lang": "vi",
    "message": "request thành công.",
    "paging": {
        "cursors": {
            "after": "",
            "before": ""
        },
        "page": 1,
        "page_count": 1,
        "per_page": 20,
        "total_count": 1
    }
}
"""

******************************* Get profile by profile_id *****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/merchants/<merchant-id>/user/profile-id Get profile by profile_id.
@apiDescription Get list profile by list profile id
@apiGroup User
@apiVersion 1.0.0
@apiName GetProfileByProfileId

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String[]}    profile_ids       Mảng profile_id
@apiParam      (Body:)     {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]


@apiParamExample  {json} Body request example
{
    "profile_ids": ["27aeaa12-2d72-4441-b888-b7c26d6903a0", "27aeaa12-2d72-4441-b888-b7c26d6903a1"],
    "fields": ["profile_id","name","birthday"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": ['<EMAIL>', '<EMAIL>'],
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": ["+************"],
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ],
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      },
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      },
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ],
      "tags": [],
      "degree": 1,
      "income_family": 0,
      "number_childs": 0
    }
  ]
}
"""

******************************* Get profile 'for' loyalty *******************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/systems/loyalty/users Get profile for loyalty.
@apiDescription Get list profile for loyalty
@apiGroup User
@apiVersion 1.0.0
@apiName GetProfileForLoyalty

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    search_value        Giá trị tìm kiếm
@apiParam       (Body:)     {String}    search_field        Tên trường tìm kiếm. Giá trị: primary_phone, primary_email
@apiParam       (Body:)     {Array}     fields              Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]


@apiParamExample  {json} Body request example
{
    "search_value": "**********",
    "search_field": "primary_phone"
    "fields": ["profile_id","name","birthday"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": ['<EMAIL>', '<EMAIL>'],
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": ["+************"],
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ],
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      },
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      },
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ],
      "tags": [],
      "degree": 1,
      "income_family": 0,
      "number_childs": 0
    }
  ]
}
"""
******************************* Get profile 'for' social ******************************
* version: 1.0.2                                                                      *
* version: 1.0.1                                                                      *
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v2.0/systems/social/users Get profile for social.
@apiDescription Get list profile for social
@apiGroup User
@apiVersion 1.0.0
@apiName GetProfileForSocial

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    search              Giá trị tìm kiếm
@apiParam       (Body:)     {String}    page_social_id      Mã định danh page trên mxh
@apiParam       (Body:)     {Integer}   social_type         Kiểu mạng xã hội. Giá trị: 1-Facebook;2-Zalo;3-Instagram;4-Youtube
@apiParam       (Body:)     {Integer}   object_type         Loại đối tượng cần tìm kiếm. Giá trị: 1-Comment;2-Message
@apiParam       (Body:)     {Integer}   [per_page]          SỐ bản ghi trên 1 trang
@apiParam       (Body:)     {String}    [after_token]         Token để lấy dữ liệu trang tiếp theo.


@apiParamExample  {json} Body request example
{
    "search": "Nguyễn",
    "page_social_id": "121358061894056",
    "social_type": 1,
    "object_type": 1,
    "per_page": 10
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
        {
            "avatar": "https://graph.facebook.com/737323523274672/picture?type=large&access_token=EAAENpMUscdQBAHmFJwuZASXrdCBOOcH4CWKglGTduP9S5OuRdckZCSqbtz5Aw2XnsZCoCMaMzHeWVdZAktop2FfHBopl4fs0NySUhZCdGD5oxFKiI2yNAsd0sgjXqx3sHx4fhQEUuY3kWp9lOocvkfiNZCl1HbLX8iJCx5aYDjkgZDZD",
            "name": "Nguyễn Thị Thùy",
            "profile_id": "4b1d793d-e999-4066-9614-6f0bf4fd3c9e",
            "social_name": {
                "id": 1,
                "name": "Nguyễn Thị Thùy"
            },
            "social_user": {
                "social_id": "100007552803457",
                "social_type": 1
            }
        }
    ]
}

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
<li><code>total_page:</code>Tổng số page.</li>
<li><code>total_count:</code>Tổng số phần tử</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before]    Token để lấy dữ liệu trang trước đó.
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh",
            "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': per_page,
        'page_count': page_count,
        'total_count': total_count
    }
}
"""

"""
@api {post} [HOST]/profiling/v3.0/systems/social/users Get profile for social.
@apiDescription Get list profile for social
@apiGroup User
@apiVersion 1.0.1
@apiName GetProfileForSocial

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    search              Giá trị tìm kiếm
@apiParam       (Body:)     {String}    page_social_id      Mã định danh page trên mxh
@apiParam       (Body:)     {Integer}   social_type         Kiểu mạng xã hội. Giá trị: 1-Facebook;2-Zalo;3-Instagram;4-Youtube
@apiParam       (Body:)     {Integer}   object_type         Loại đối tượng cần tìm kiếm. Giá trị: 1-Comment;2-Message
@apiParam       (Body:)     {Integer}   [per_page]          SỐ bản ghi trên 1 trang
@apiParam       (Body:)     {String}    [after_token]         Token để lấy dữ liệu trang tiếp theo.


@apiParamExample  {json} Body request example
{
    "search": "Nguyễn",
    "page_social_id": "121358061894056",
    "social_type": 1,
    "object_type": 1,
    "per_page": 10
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
        {
            "avatar": "https://graph.facebook.com/737323523274672/picture?type=large&access_token=EAAENpMUscdQBAHmFJwuZASXrdCBOOcH4CWKglGTduP9S5OuRdckZCSqbtz5Aw2XnsZCoCMaMzHeWVdZAktop2FfHBopl4fs0NySUhZCdGD5oxFKiI2yNAsd0sgjXqx3sHx4fhQEUuY3kWp9lOocvkfiNZCl1HbLX8iJCx5aYDjkgZDZD",
            "name": "Nguyễn Thị Thùy",
            "profile_id": "4b1d793d-e999-4066-9614-6f0bf4fd3c9e",
            "social_name": {
                "id": 1,
                "name": "Nguyễn Thị Thùy"
            },
            "social_user": {
                "social_id": "100007552803457",
                "social_type": 1
            }
        }
    ]
}

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
<li><code>total_page:</code>Tổng số page.</li>
<li><code>total_count:</code>Tổng số phần tử</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before]    Token để lấy dữ liệu trang trước đó.
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh",
            "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': per_page,
        'page_count': page_count,
        'total_count': total_count
    }
}
"""

"""
@api {post} [HOST]/profiling/v3.1/systems/social/users Get profile for social.
@apiDescription Get list profile for social
@apiGroup User
@apiVersion 1.0.2
@apiName GetProfileForSocial

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Param:)     {String}    [info_paging]      info_paging=True khi cần thông tin để chia page như total_count, page_count, mặc định False
@apiParam       (Body:)     {String}    search              Giá trị tìm kiếm
@apiParam       (Body:)     {String}    page_social_id      Mã định danh page trên mxh
@apiParam       (Body:)     {Integer}   social_type         Kiểu mạng xã hội. Giá trị: 1-Facebook;2-Zalo;3-Instagram;4-Youtube
@apiParam       (Body:)     {Integer}   object_type         Loại đối tượng cần tìm kiếm. Giá trị: 1-Comment;2-Message
@apiParam       (Body:)     {Integer}   [per_page]          SỐ bản ghi trên 1 trang
@apiParam       (Body:)     {String}    [after_token]         Token để lấy dữ liệu trang tiếp theo.


@apiParamExample  {json} Body request example
{
    "search": "Nguyễn",
    "page_social_id": "121358061894056",
    "social_type": 1,
    "object_type": 1,
    "per_page": 10
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
        {
            "avatar": "https://graph.facebook.com/737323523274672/picture?type=large&access_token=EAAENpMUscdQBAHmFJwuZASXrdCBOOcH4CWKglGTduP9S5OuRdckZCSqbtz5Aw2XnsZCoCMaMzHeWVdZAktop2FfHBopl4fs0NySUhZCdGD5oxFKiI2yNAsd0sgjXqx3sHx4fhQEUuY3kWp9lOocvkfiNZCl1HbLX8iJCx5aYDjkgZDZD",
            "name": "Nguyễn Thị Thùy",
            "profile_id": "4b1d793d-e999-4066-9614-6f0bf4fd3c9e",
            "social_name": {
                "id": 1,
                "name": "Nguyễn Thị Thùy"
            },
            "social_user": {
                "social_id": "100007552803457",
                "social_type": 1
            }
        }
    ]
}

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
<li><code>total_page:</code>Tổng số page.</li>
<li><code>total_count:</code>Tổng số phần tử</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before]    Token để lấy dữ liệu trang trước đó.
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh",
            "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': per_page,
        'page_count': page_count,
        'total_count': total_count
    }
}
"""


******************************* Get profile card loyalty ******************************
* version: 1.0.1                                                                      *
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/users-card-loyalty Get profile for card loyalty.
@apiDescription Get profile for card loyalty
@apiGroup User
@apiVersion 1.0.0
@apiName GetProfileForCardLoyalty

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    search              Giá trị tìm kiếm
@apiParam       (Body:)     {String}    merchant_id         Mã định danh merchant
@apiParam       (Body:)     {Integer}   [per_page]          SỐ bản ghi trên 1 trang
@apiParam       (Body:)     {String}    [after_token]       Token để lấy dữ liệu trang tiếp theo.


@apiParamExample  {json} Body request example
{
    "search": "tiep",
    "per_page": 5,
    "after_token": "",
    "merchant_id": ""
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data": [
        {
            "avatar": "",
            "cards": [
                {
                    "card_code": "ABCD12345678",
                    "card_id": "6e8b53c9-3d32-4d6f-8844-e44ac5a65ba5",
                    "card_name": "Thẻ Kim Cương",
                    "id": "b3c7904d-0e9c-4c17-a645-30813dcffe7e"
                }
            ],
            "name": "Trần Huy Tiệp",
            "phone_number": "+84363335020",
            "profile_id": "c264ac2b-e4cd-467f-b44e-e0d101e7c5e8"
        }
    ]
}

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
<li><code>total_page:</code>Tổng số page.</li>
<li><code>total_count:</code>Tổng số phần tử</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before]    Token để lấy dữ liệu trang trước đó.
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh",
            "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': per_page,
        'page_count': page_count,
        'total_count': total_count
    }
}
"""
"""
@api {post} [HOST]/profiling/v3.1/users-card-loyalty Get profile for card loyalty.
@apiDescription Get profile for card loyalty
@apiGroup User
@apiVersion 1.0.1
@apiName GetProfileForCardLoyalty

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Param:)     {String}    [info_paging]      info_paging=True khi cần thông tin để chia page như total_count, page_count, mặc định False

@apiParam       (Body:)     {String}    search              Giá trị tìm kiếm
@apiParam       (Body:)     {String}    merchant_id         Mã định danh merchant
@apiParam       (Body:)     {Integer}   [per_page]          SỐ bản ghi trên 1 trang
@apiParam       (Body:)     {String}    [after_token]       Token để lấy dữ liệu trang tiếp theo.


@apiParamExample  {json} Body request example
{
    "search": "tiep",
    "per_page": 5,
    "after_token": "",
    "merchant_id": ""
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data": [
        {
            "avatar": "",
            "cards": [
                {
                    "card_code": "ABCD12345678",
                    "card_id": "6e8b53c9-3d32-4d6f-8844-e44ac5a65ba5",
                    "card_name": "Thẻ Kim Cương",
                    "id": "b3c7904d-0e9c-4c17-a645-30813dcffe7e"
                }
            ],
            "name": "Trần Huy Tiệp",
            "phone_number": "+84363335020",
            "profile_id": "c264ac2b-e4cd-467f-b44e-e0d101e7c5e8"
        }
    ]
}

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
<li><code>total_page:</code>Tổng số page.</li>
<li><code>total_count:</code>Tổng số phần tử</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before]    Token để lấy dữ liệu trang trước đó.
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh",
            "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': per_page,
        'page_count': page_count,
        'total_count': total_count
    }
}
"""
***************************** Get profile 'for' assign loyalty ************************
* version: 1.0.1                                                                      *
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/profile/search-for-assign-loyalty Get list profile for assign loyalty.
@apiDescription Get list profile for assign loyalty
@apiGroup User
@apiVersion 1.0.0
@apiName GetProfileForAssignLoyalty

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {String}    search              Giá trị tìm kiếm
@apiParam       (Body:)     {String}    page_social_id      Mã ứng dụng
@apiParam       (Body:)     {Integer}   social_type         Kiểu mạng xã hội. Giá trị: 5-MobileApp
@apiParam       (Body:)     {Integer}   object_type         Loại đối tượng cần tìm kiếm. Giá trị: <code>1-Comment</code>
@apiParam       (Body:)     {Integer}   [per_page]          SỐ bản ghi trên 1 trang
@apiParam       (Body:)     {String}    [after_token]       Token để lấy dữ liệu trang tiếp theo.


@apiParamExample  {json} Body request example
{
    "search": "Le",
    "page_social_id": "SAKUKO",
    "social_type": 5,
    "object_type": 1,
    "per_page": 10
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
        {
            "avatar": "https://graph.facebook.com/737323523274672/picture?type=large&access_token=EAAENpMUscdQBAHmFJwuZASXrdCBOOcH4CWKglGTduP9S5OuRdckZCSqbtz5Aw2XnsZCoCMaMzHeWVdZAktop2FfHBopl4fs0NySUhZCdGD5oxFKiI2yNAsd0sgjXqx3sHx4fhQEUuY3kWp9lOocvkfiNZCl1HbLX8iJCx5aYDjkgZDZD",
            "name": "Nguyễn Thị Thùy",
            "profile_id": "4b1d793d-e999-4066-9614-6f0bf4fd3c9e",
            "social_name": {
                "id": 1,
                "name": "Nguyễn Thị Thùy"
            },
            "social_user": {
                "social_id": "100007552803457",
                "social_type": 1
            }
        }
    ]
}

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
<li><code>total_page:</code>Tổng số page.</li>
<li><code>total_count:</code>Tổng số phần tử</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before]    Token để lấy dữ liệu trang trước đó.
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh",
            "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': per_page,
        'page_count': page_count,
        'total_count': total_count
    }
}
"""
"""
@api {post} [HOST]/profiling/v3.1/profile/search-for-assign-loyalty Get list profile for assign loyalty.
@apiDescription Get list profile for assign loyalty
@apiGroup User
@apiVersion 1.0.1
@apiName GetProfileForAssignLoyalty

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Param:)     {String}    [info_paging]      info_paging=True khi cần thông tin để chia page như total_count, page_count, mặc định False

@apiParam       (Body:)     {String}    search              Giá trị tìm kiếm
@apiParam       (Body:)     {String}    page_social_id      Mã ứng dụng
@apiParam       (Body:)     {Integer}   social_type         Kiểu mạng xã hội. Giá trị: 5-MobileApp
@apiParam       (Body:)     {Integer}   object_type         Loại đối tượng cần tìm kiếm. Giá trị: <code>1-Comment</code>
@apiParam       (Body:)     {Integer}   [per_page]          SỐ bản ghi trên 1 trang
@apiParam       (Body:)     {String}    [after_token]       Token để lấy dữ liệu trang tiếp theo.


@apiParamExample  {json} Body request example
{
    "search": "Le",
    "page_social_id": "SAKUKO",
    "social_type": 5,
    "object_type": 1,
    "per_page": 10
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
        {
            "avatar": "https://graph.facebook.com/737323523274672/picture?type=large&access_token=EAAENpMUscdQBAHmFJwuZASXrdCBOOcH4CWKglGTduP9S5OuRdckZCSqbtz5Aw2XnsZCoCMaMzHeWVdZAktop2FfHBopl4fs0NySUhZCdGD5oxFKiI2yNAsd0sgjXqx3sHx4fhQEUuY3kWp9lOocvkfiNZCl1HbLX8iJCx5aYDjkgZDZD",
            "name": "Nguyễn Thị Thùy",
            "profile_id": "4b1d793d-e999-4066-9614-6f0bf4fd3c9e",
            "social_name": {
                "id": 1,
                "name": "Nguyễn Thị Thùy"
            },
            "social_user": {
                "social_id": "100007552803457",
                "social_type": 1
            }
        }
    ]
}

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
<li><code>total_page:</code>Tổng số page.</li>
<li><code>total_count:</code>Tổng số phần tử</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before]    Token để lấy dữ liệu trang trước đó.
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh",
            "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': per_page,
        'page_count': page_count,
        'total_count': total_count
    }
}
"""

********************************* Get list profile by ids *****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/profile/search_by_profile_ids Get list profile by ids.
@apiDescription Get list profile by profile ids
@apiGroup User
@apiVersion 1.0.0
@apiName GetProfileByIds

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Array}    profile_ids              Danh sách các Profile Ids. Max = 100

@apiParamExample  {json} Body request example
{
    "profile_ids": [
        "4b1d793d-e999-4066-9614-6f0bf4fd3c9e"
    ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
        {
            "avatar": "https://graph.facebook.com/737323523274672/picture?type=large&access_token=EAAENpMUscdQBAHmFJwuZASXrdCBOOcH4CWKglGTduP9S5OuRdckZCSqbtz5Aw2XnsZCoCMaMzHeWVdZAktop2FfHBopl4fs0NySUhZCdGD5oxFKiI2yNAsd0sgjXqx3sHx4fhQEUuY3kWp9lOocvkfiNZCl1HbLX8iJCx5aYDjkgZDZD",
            "name": "Nguyễn Thị Thùy",
            "profile_id": "4b1d793d-e999-4066-9614-6f0bf4fd3c9e",
            "social_name": {
                "id": 1,
                "name": "Nguyễn Thị Thùy"
            },
            "social_user": {
                "social_id": "100007552803457",
                "social_type": 1
            }
        }
    ]
}
"""

***************************** Get list profile by social ids **************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/profile/search_by_social_ids Get list profile by social ids.
@apiDescription Get list profile by social ids
@apiGroup User
@apiVersion 1.0.0
@apiName GetProfileBySocialIds

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam       (Body:)     {Array}    social_ids              Danh sách các Social Ids. Max = 100

@apiParamExample  {json} Body request example
{
    "social_ids": [
        "100007552803457"
    ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
        {
            "avatar": "https://graph.facebook.com/737323523274672/picture?type=large&access_token=EAAENpMUscdQBAHmFJwuZASXrdCBOOcH4CWKglGTduP9S5OuRdckZCSqbtz5Aw2XnsZCoCMaMzHeWVdZAktop2FfHBopl4fs0NySUhZCdGD5oxFKiI2yNAsd0sgjXqx3sHx4fhQEUuY3kWp9lOocvkfiNZCl1HbLX8iJCx5aYDjkgZDZD",
            "name": "Nguyễn Thị Thùy",
            "profile_id": "4b1d793d-e999-4066-9614-6f0bf4fd3c9e",
            "social_name": {
                "id": 1,
                "name": "Nguyễn Thị Thùy"
            },
            "social_user": {
                "social_id": "100007552803457",
                "social_type": 1
            }
        }
    ]
}
"""

*********************************** API Export Excel khách hàng ********************************************
* version: 1.0.0    *
************************************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/export/users Export Excel khách hàng
@apiDescription API Export excel khách hàng. API hỗ trợ tìm kiếm, sắp xếp, filter danh sách user.
@apiGroup User
@apiVersion 1.0.0
@apiName ExportExcelUser

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)     {String}    [receive_email]                        Email của tài khoản nhận file export.
@apiParam   (Body:)     {Array}    [column_list]                           Danh sách các field cần export.
@apiParam   (Body:)     {String}    [search]                               Chuỗi tìm kiếm. Tìm kiếm theo tên hoặc email hoặc số điện thoại user.
@apiParam   (Body:)     {Array}    [profile_filter]                        Bộ lọc danh sách profile.
@apiParam   (Body:)     {Array}    [profile_group]                         Danh sách các Group mà Staff nv2.0ày quản lý. VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']


@apiSuccess (Column) {String} name                                           Cột <code>fullname</code> trong excel. Tương ứng field <code>display_name</code> table <code>User</code>. Nếu không có dữ liệu thì để trống.
@apiSuccess (Column) {String} social_id                                      Cột <code>social_id</code> trong excel. Tương ứng field <code>id_social</code> table <code>SocialUser</code>.  Nếu không có dữ liệu thì để trống.
@apiSuccess (Column) {Number} social_type                                    Cột <code>social_type</code> trong excel. Tương ứng field <code>social_type</code> table <code>SocialUser</code>.  Nếu không có dữ liệu thì để trống.
@apiSuccess (Column) {String} phone_number                                   Cột <code>phone_number</code> trong excel. Tương ứng field <code>phone_number</code> table <code>User</code>.  Nếu không có dữ liệu thì để trống.
@apiSuccess (Column) {String} email                                          Cột <code>email</code> trong excel. Tương ứng field <code>email</code> table <code>User</code>.  Nếu không có dữ liệu thì để trống.
@apiSuccess (Column) {String} birthday                                       Cột <code>birthday</code> trong excel. Tương ứng field <code>birthday</code> table <code>UserInfo</code>.  Nếu không có dữ liệu thì để trống.
@apiSuccess (Column) {Number=1:Unknown,2:Name,3:Nữ} gender                   Cột <code>gender</code> trong excel. Tương ứng field <code>gender</code> table <code>UserInfo</code>.  Nếu không có dữ liệu thì để trống.

@apiSuccessExample {file} Response: HTTP/1.1 200 OK
File excel khách hàng
"""

*********************************** API ThirdParty Search Profile ********************************************
* version: 1.0.0    *
************************************************************************************************************
"""
@api {get} [HOST]/profiling/v3.0/customers/thirdparty_search_users API Tìm kiếm profile
@apiDescription API dành cho đối tác tìm kiếm Profile theo số điện thoại, email, customer id
@apiGroup Customers
@apiVersion 1.0.0
@apiName ThirdPartySearchProfile

@apiUse merchant_id_header

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (parameters:)     {String}    query                        Chuỗi tìm kiếm theo số điện thoại, email, customer id.

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "data":[
        {
            "profile_id": "1afc7096-9f72-4b6a-8eb4-e85779038f04",
            "merchant_id": "4a9f6012-5627-4239-b281-4adb85c00827",
            "address": "",
            "avatar": "",
            "birthday": "1993-10-10T00:00:00",
            "birth_year": 1993,
            "email": ("<EMAIL>"),
            "fax": "",
            "gender": 1,
            "hobby": [""],
            "job": [""],
            "marital_status": [""],
            "name": "",
            "nation": [""],
            "phone_number": (""),
            "province_code": [""],
            "tax_code": "",
            "ward_code": [""],
            "workplace": ""
        }
    ]
}
"""
******************************* Get profile estimate **********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {POST} [HOST]/profiling/v3.0/merchant/profile/estimate Get profile estimate
@apiDescription Get profile estimate
@apiGroup Customers
@apiVersion 1.0.0
@apiName GetProfileEstimate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}    profile_group                       Danh sách các Group mà Staff này quản lý. VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "profile_number": 1000,  // Số lượng profile định danh
        "non_profile_number": 2000 // Số lượng profile không định danh
    }
}
"""
******************************* Assign profile owner **********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {POST} [HOST]/profiling/v3.0/assign/profile_owner Assign profile owner
@apiDescription Assign profile owner
@apiGroup User
@apiVersion 1.0.0
@apiName AssignProfileOwner

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body Json:)     {Array}    profile_ids            Danh sách các profile id VD: ['dab3d907-b969-4dac-bcf0-96d8244fc6f4', '6a627b8a-71ca-4779-9961-d60978fe0828']
@apiParam      (Body Json:)     {String}    profile_owner_id     Id của tại khoản đăng nhập 

@apiParamExample  {json} Body request example
{
    "profile_ids": [
        "00000052-ab0e-413c-a76f-8c9fb63c3999"
    ],
    "profile_owner_id": "123456999999",
    "replace": false
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "update_fail": 0,
        "update_success": 1
    },
    "lang": "vi",
    "message": "request thành công."
}
"""

******************************* Uncensored Profile Info **********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {POST} [HOST]/profiling/v3.0/profile/uncensored uncensored Profile Information
@apiDescription API lấy dữ liệu đã bị ẩn do policy
@apiGroup User
@apiVersion 1.0.0
@apiName UncensoredProfileInfo

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Body Json:)     {String}    profile_id           id của profile cần lấy thông tin
@apiParam      (Body Json:)     {String}    field     field cần lấy thông tin

@apiParamExample  {json} Body request example
{
    "profile_id": "00000052-ab0e-413c-a76f-8c9fb63c3999",
    "field": "primary_email"
}

@apiSuccessExample  {json}  Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "primary_email": {
            "email": "<EMAIL>",
            "last_check": "Thu, 25 Nov 2021 04:56:14 GMT",
            "status": 1
        }
    },
    "lang": "vi",
    "message": "request thành công.",
    "success": true
}
"""
********************* Get profile by satellite ****************************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/profile/find_by_satellite Search profile by phone, email, customer_id,cif
@apiDescription search profile theo phone, email, customer_id,cif từ mongo
@apiGroup Profile Filter
@apiVersion 1.0.0
@apiName SearchProfileByPhoneEmailCustomerIdCifName

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam      (Body:)        {dict}    condition                Điều kiện tìm kiếm.
@apiParam      (condition:)   {string}    find_by                Tìm theo <code>cif</code>, <code>phone_number</code> hoặc <code>customer_id</code> hoặc <code>email</code>.
@apiParam      (condition:)   {string}     value                 Mảng giá trị tối đa 5 giá trị
@apiParam      (Body:)        {Array}     [fields]               Danh sách các thuộc tính cần trả ra không có trả hết. Ví dụ: ["profile_id","name","birthday"]

@apiParamExample    {json}    Body example:
{
    "condition": {
        "find_by": "phone_number",
        "value": [
            "+84927024348","+84927024348","+84927024349","+84320000003"
        ]
    },
    "fields": [
        "_id", "name", "phone_number", "age", "profile_id", "cif", "email"
    ]
}
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
        {
            "_id": "65728d8631d4d34fcc2c8692",
            "address": null,
            "age": null,
            "cif": [
                "65656"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "9f78cca1-0a64-4b24-8ef0-1db4f65b3748",
            "name": "Trần Thị Lan 014",
            "phone_number": [
                "+84927024348"
            ],
            "profile_address": [],
            "profile_id": "9f78cca1-0a64-4b24-8ef0-1db4f65b3748",
            "profile_tags": [],
            "tags": []
        },
        {
            "_id": "65728d8631d4d34fcc2c8696",
            "address": null,
            "age": null,
            "cif": [
                "65655"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "a6cd9dd7-e6cf-4f9c-9b40-2bf64ba25efa",
            "name": "Trần Thị Lan 015",
            "phone_number": [
                "+84927024349"
            ],
            "profile_address": [],
            "profile_id": "a6cd9dd7-e6cf-4f9c-9b40-2bf64ba25efa",
            "profile_tags": [],
            "tags": []
        },
        {
            "_id": "655436190ee1877c7ab1c01e",
            "address": null,
            "age": null,
            "cif": [
                "65653"
            ],
            "email": [
                "<EMAIL>"
            ],
            "id": "f8633fc3-18c6-401e-9ba6-df162ecfc8ae",
            "name": "Lypt 04",
            "phone_number": [
                "+84320000003"
            ],
            "profile_address": [],
            "profile_id": "f8633fc3-18c6-401e-9ba6-df162ecfc8ae",
            "profile_tags": [],
            "tags": []
        }
    ],
    "lang": "vi",
    "message": "request thành công."
}
"""
******************************* Get profile by profile_id *****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/merchants/<merchant_id>/detail/profile_ids Get profile by profile_id white list ips.
@apiDescription Get list profile by list profile id white list ips
@apiGroup User
@apiVersion 1.0.0
@apiName GetProfileByProfileIdWhiteListIps

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String[]}    profile_ids       Mảng profile_id
@apiParam      (Body:)     {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]


@apiParamExample  {json} Body request example
{
    "profile_ids": ["27aeaa12-2d72-4441-b888-b7c26d6903a0", "27aeaa12-2d72-4441-b888-b7c26d6903a1"],
    "fields": ["profile_id","name","birthday"]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": ['<EMAIL>', '<EMAIL>'],
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": ["+************"],
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ],
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      },
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      },
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ],
      "tags": [],
      "degree": 1,
      "income_family": 0,
      "number_childs": 0
    }
  ],
  "encrypt_data": [
        {
            "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a"
        }
    ]
}
"""


******************************* Delete list profile *****************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/delete/list/profile Delele list profile.
@apiDescription Delele list profile.
@apiGroup User
@apiVersion 1.0.0
@apiName DeleteListProfie

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)    {String[]}    profile_ids       Mảng profile_id
@apiParam      (Body:)     {Array}     reason          Lý do xóa


@apiParamExample  {json} Body request example
{
    "profile_ids": ["27aeaa12-2d72-4441-b888-b7c26d6903a0", "27aeaa12-2d72-4441-b888-b7c26d6903a1"],
    "reason": "reason1"
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "delete_success": 123,
        "delete_fail": 1
    },
    "lang": "vi",
    "message": "request th\u00e0nh c\u00f4ng.",
}
"""
******************************* API Upsert Profile *****************************
* version: 1.0.0                                                                      *
***************************************************************************************

"""
@api {post} [HOST]/profiling/v1.0/merchants/<merchant_id>/users/actions/upsert    Import khách hàng
@apiDescription API lưu trữ thông tin khách hàng. Trường hợp không có thông tin khách hàng thì tạo mới, ngược lại thì update dữ liệu mới.
@apiGroup User
@apiVersion 1.0.0
@apiName UpsertUsers

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện business của merchant.

@apiParam   (Body:)     {String}    name                                    Tên khách hàng
@apiParam   (Body:)     {String}    phone_number                            Số điện thoại của khách hàng.
@apiParam   (Body:)     {String}    email                                   Email của khách hàng.
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    gender             Giới tính.
@apiParam   (Body:)     {String}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (Body:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (Body:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (Body:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (Body:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (Body:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (Body:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (Body:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (Body:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (Body:)     {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    [income_type]          Hình thức thu nhập.
@apiParam   (Body:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.

@apiParam   (Body:)    {Object}    [third_party_info]    Thông tin bổ sung (format <code>JSON</code>) của third party. Thông tin này sẽ được gửi lại cho third party để đối soát thông tin. Value của <code>third_party_info</code> do third party tự định nghĩa.


@apiParamExample [json] Body example:
{
    "name": "andrew",
    "phone_number": "**********",
    "email": "<EMAIL>",
    "address": "Hà Nội",
    "gender": 2,
    "birthday": "01/01/1989",
    "income_low_threshold": 7000000,
    "income_high_threshold": 15000000,
    "third_party_info": {
      "lead_id": "2ccfe4f8-5dab-4c0e-92ab-80fbc30012ce",
      "acquired_time": *************,
      "article_topic": "",
      "timestamp": *************
    }
}


@apiSuccess {Object}    customer     Dữ liệu thông tin khách hàng

@apiSuccess (Body:)     {String}     profile_id                                 ID khách hàng
@apiSuccess   (Body:)     {String}      name                            Tên khách hàng
@apiSuccess   (Body:)     {Array}     phone_number                      Mảng Số điện thoại của khách hàng.
@apiSuccess   (Body:)     {Array}     email                             Mảng Số điện thoại của khách hàng.
@apiSuccess   (Body:)     {Number}    created_account_type              Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (Body:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (Body:)     {Number}    gender                            Giới tính
@apiSuccess   (Body:)     {String}    address                           Địa chỉ
@apiSuccess   (Body:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (Body:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (Body:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (Body:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (Body:)     {String}    birthday                          Ngày sinh
@apiSuccess   (Body:)     {Number}    income_low_threshold                                 Mức thu nhập thấp.
@apiSuccess   (Body:)     {Number}    income_high_threshold                                Mức thu nhập cao.
@apiSuccess   (Body:)     {Number=1:ChuyenKhoan 2:TienMat;3:TuDo}    income_type          Hình thức thu nhập.


@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "profile_id": "90d610b6-cbf4-4624-b3f5-e44973571aab",
  "name": "andrew",
  "phone_number": [
    "+***********",
    "+***********"
  ],
  "email": [
    "<EMAIL>",
    "<EMAIL>"
  ],
  "created_account_type": 1,
  "avatar": "",
  "created_time": "2017-12-12T15:12:28Z",
  "updated_time": "2017-12-12T15:12:28Z",
  "gender": 2,
  "address": "23 ngõ 37/2 Dịch Vọng, Cầu Giấy, Hà Nội",
  "province_code": 1,
  "district_code": 1,
  "ward_code": 1,
  "marital_status": 1,
  "birthday": "1989-09-17",
  "religiousness": 1,
  "nation": 1,
  "job": 39,
  "operation": null,
  "third_party_info": {
      "lead_id": "2ccfe4f8-5dab-4c0e-92ab-80fbc30012ce",
      "acquired_time": *************,
      "article_topic": "",
      "timestamp": *************
    }
}
"""

# ********************************* Check valid phone_number ********************************
# version: 1.0.0                                                                      *
# *************************************************************************************
"""
@api {post} /profiling/v3.0/merchant/validation/phone_number [DONE] Check valid phone_number.
@apiDescription  Check valid phone_number
@apiGroup Validation
@apiVersion 1.0.0
@apiName  Check valid phone_number
@apiUse merchant_id_header
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam	(Body:)			{String}	              phone_number		                                   Số điện thoại cần kiểm tra validateion

@apiParamExample {json} Body example
{
  "phone_number": "+843649591"
}



@apisuccess       {Array}                       data                                              Data
@apisuccess       {Integer}                     code                                              Http code
@apisuccess       {String}                      lang                                              Ngôn ngữ
@apisuccess       {String}                      message                                           Message

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": [
    {
      "is_valid_phone_number": true
    }
  ],
  "lang": "vi",
  "message": "request thành công."
}
"""
#!/usr/bin/python
# -*- coding: utf8 -*-


# ****************************************  Thêm nhiều liên hệ spam  *************************************************
# * version: 1.0.0                                                                                   *
# ****************************************************************************************************


"""
@api {post} /contact/spam Thêm nhiều contact spam 
@apiDescription Dịch vụ thêm nhiều contact spam 
@apiGroup ContactSpam
@apiVersion 1.0.0
@apiName AddContactSpam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam       (Body:)  {array}    contacts            Danh sách liên hệ 
@apiParam       (Body:)  {string}   contacts.contact    Email, phone, user social id
@apiParam       (Body:)  {string}   contacts.contact_type    Loại liên hệ: email, phone, social 
@apiParam       (Body:)  {string}   contacts.staff_id    Nhân viên thêm liên hệ 
@apiParam       (Body:)  {string}   [contacts.social_page_id]     Với loại social thêm social_page_id nếu có 
@apiParam       (Body:)  {string}   [contacts.social_type]     Với loại social: 1: facebook, 2: zalo, 3: instagram, 4: youtube, 5: line, 6: chattool 
@apiParam       (Body:)  {string}   [contacts.social_name]     Với loại social thêm tên người dùng trên social
@apiParam       (Body:)  {string}   [contacts.page_name]     Với loại social thêm tên page  
@apiParam       (Body:)  {string}   [contacts.profile_id]     profile_id Mobio nếu có 


@apiParamExample {json} Body request example
{
  "contacts": [
    {
      "contact": "0371234567",
      "contact_type": "phone",
      "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7"
    },
    {
      "contact": "<EMAIL>",
      "contact_type": "email",
      "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7"
    },
    {
      "contact": "13f9ee70-9294-42f8-917f-a03fa0438d73",
      "contact_type": "social",
      "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "social_page_id": "d59d4779-b707-4558-a594-4eb65f106ccc",
      "social_type": 1,
      "social_name": "Phan Cường",
      "page_name": "Mobio shop",
    },
    ...
  ] 
}


@apiSuccess     {json} data   stringee response.
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
      {
        "contact": "0371234567",
        "contact_type": "phone",
        "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7"
      },
      {
        "contact": "<EMAIL>",
        "contact_type": "email",
        "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7"
      },
    ]
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""



# ****************************************  Danh sách liên hệ spam có phân trang  *******************************************
# * version: 1.0.0                                                                                   *
# ****************************************************************************************************
"""
@api {get} /contact/spam Danh sách liên hệ spam có phân trang 
@apiDescription Dịch vụ lấy danh sách liên hệ spam có phân trang 
@apiGroup ContactSpam
@apiVersion 1.0.0
@apiName ListContactSpam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam          (Query:) {String}        [search_text]   Chuỗi tìm kiếm theo email, phone 
@apiParam          (Query:) {String}        [contact_type]  Loại liên hệ, nhiều loại cách nhau bằng dấu , ("email,phone,social")
@apiParam          (Query:) {String}        [from]  Thời điểm xóa đầu (dd-mm-yyyy)
@apiParam          (Query:) {String}        [to]  Thời điểm xóa cuối (dd-mm-yyyy)
@apiParam          (Query:) {String}        [staff_id]  id nhân viên add contact spam, nhiều id cách nhau bằng dấu phẩy
@apiParam          (Query:) {String}        [social_page_id]  danh sách social_page_id, nhiều id cách nhau bằng dấu phẩy
@apiParam          (Query:) {String}        [order]  Trường sort (create_on,...)
@apiParam          (Query:) {String}        [sort]  Loại sort
<li><code>asc</code>: Tăng dần</li>
<li><code>desc</code>: Giảm dần</li> 
@apiParam          (Query:) {String}        [field]  field trả về, nhiều field cách nhau bằng dấu , ("merchant_id,
contact,contact_type,page_name,social_page_id,social_type,profile_id,staff_id,create_on")

@apiSuccess {Array}   data    Danh sách liên hệ 
@apiSuccess (data) {String} 	contact				Liên hệ là: Email, phone, user social id
@apiSuccess (data) {String} 	contact_type		Loại liên hệ: email,phone,social
@apiSuccess (data) {String} 	staff_id			Nhân viên id
@apiSuccess (data) {String}     merchant_id         Mã định danh merchant
@apiSuccess (data) {String}     create_on         Thời điểm thêm vào spam UTC


@apiSuccessExample {json} Response list contact example
{
  "code": 200,
  "data": [
      {
        "contact": "<EMAIL>",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "contact_type": "email",
        "spam_info": "<EMAIL>",
        "spam_location": "",
        "number_profile": 0,
        "staff_id": "394f9237-48ae-48be-b4da-db469b99ef16",
        "create_on": "2021-01-11 03:48:03"
    },
     {
      "contact": "13f9ee70-9294-42f8-917f-a03fa0438d73",
      "contact_type": "social",
       "spam_info": "Phan Cường: 41390000954564933",
        "spam_location": "MOBIOSHOP",
      "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "social_page_id": "d59d4779-b707-4558-a594-4eb65f106ccc",
      "social_type": 1,
      "number_profile": 0,
      "page_name": "Mobio shop",
      "create_on": "2021-01-11 03:48:03"
    },
     ...
  ],
  "paging": {
    "page": 1,
    "per_page": 20,
    "total_items": 1645,
    "total_pages": 83
  }
  
}

@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


# ****************************************  Xóa nhiều liên hệ spam  *******************************************
# * version: 1.0.0                                                                                   *
# ****************************************************************************************************
"""
@api {post} /contact/delete_spam Xóa nhiều liên hệ spam 
@apiDescription Dịch vụ xóa nhiều contact spam 
@apiGroup ContactSpam
@apiVersion 1.0.0
@apiName RemoveContactSpam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam       (Body:)  {array}    contacts            Danh sách liên hệ 
@apiParam       (Body:)  {string}   contacts.contact    Email, phone, user social id
@apiParam       (Body:)  {string}   contacts.staff_id    Nhân viên thêm liên hệ

@apiParamExample {json} Body request example
{
  "contacts": [
    {
      "contact": "0371234567",
      "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7"
    },
    {
      "contact": "<EMAIL>",
      "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7"
    },...
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": [
      {
        "contact": "0371234567",
        "contact_type": "phone",
        "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7"
      },
      {
        "contact": "<EMAIL>",
        "contact_type": "email",
        "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7"
      },
    ]
}
@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


# ***************************************  Tìm kiếm nhiều liên hệ có trong spam hay không  ********************************************
# * version: 1.0.0                                                                                   *
# ****************************************************************************************************
"""
@api {get} /contact/find_many Tìm kiếm nhiều liên hệ
@apiDescription Tìm kiếm nhiều liên hệ
@apiGroup ContactSpam
@apiVersion 1.0.0
@apiName FindManyContactSpam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam       (Query:)     contact              1 trong giá trị là: email, phone, user social id. nhiều liên hệ cách nhau bằng dấu ,


@apiSuccess {Array}   data    Danh sách liên hệ 
@apiSuccess (data) {String} 	contact				Liên hệ là: Email, phone, user social id
@apiSuccess (data) {String} 	contact_type		Loại liên hệ: email,phone,social
@apiSuccess (data) {String} 	staff_id			Nhân viên id
@apiSuccess (data) {String}     merchant_id         Mã định danh merchant
@apiSuccess (data) {String}     create_on         Thời điểm thêm vào spam UTC


@apiSuccessExample {json} Response list contact example
{
  "code": 200,
  "data": [
      {
        "contact": "<EMAIL>",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "contact_type": "email",
        "staff_id": "394f9237-48ae-48be-b4da-db469b99ef16",
        "create_on": "2021-01-11 03:48:03"
    }, 
    {
      "contact": "13f9ee70-9294-42f8-917f-a03fa0438d73",
      "contact_type": "social",
      "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "social_page_id": "d59d4779-b707-4558-a594-4eb65f106ccc",
      "social_type": "facebook",
      "page_name": "Mobio shop",
      "create_on": "2021-01-11 03:48:03"
    },
    ...
  ]
  
}

@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}


"""



# ***************************************  Kiểm tra phone được phép sử dụng hay không  ********************************************
# * version: 1.0.0                                                                                   *
# ****************************************************************************************************
"""
@api {get} /phone/check Kiểm tra phone được phép sử dụng hay không
@apiDescription Kiểm tra phone được phép sử dụng hay không, kiểm tra theo type_check gửi lên, nếu ko gửi type_check thì kiểm tra cả spam và dnc   
@apiGroup ContactSpam
@apiVersion 1.0.0
@apiName PhoneCheck

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam       (Query:)     phone              số điện thoại cần kiểm tra 
@apiParam       (Query:)     merchant_id        merchant id cần kiểm tra 
@apiParam       (Query:)     type_check        loại kiểm tra 1 trong các giá trị sau(nhiều giá trị thì cách nhau giấu ,): dnc,spam 
<li><code>dnc: Kiểm tra số điện thoại có trong dnc không </code></li>
<li><code>spam: kiểm tra số điện thoại có trong spam không</code></li> 

@apiSuccess {String} 	phone				Số điện thoại kiểm tra 
@apiSuccess {String} 	merchant_id		    merchant id kiểm tra 
@apiSuccess {String} 	allow_call			trạng thái cho phép sử dụng hay không 
<li><code>allow: Cho phép sử dụng </code></li>
<li><code>not_allow: Không cho phép sử dụng</code></li> 


@apiSuccessExample {json} Response list contact example
{
    "code": 200,
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "phone": "0371234567",
    "allow_call": "allow"
  
}

@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}


"""

# ***************************************  socket nhận khi có contact thêm vào spam   ********************************************
# * version: 1.0.0                                                                                   *
# ****************************************************************************************************
"""
@api NONE Socket nhận khi có contact thêm vào spam
@apiDescription Socket nhận khi có contact thêm vào spam
@apiGroup ContactSpam
@apiVersion 1.0.1
@apiName SocketSpam 

@apiSuccess {String} 	contact_spam				email, phone, user social id  
@apiSuccess {String} 	merchant_id		    merchant id 
@apiSuccess {String} 	[page_id]			với social có page id
@apiSuccess {String} 	social_type			với social có các loại: 1: facebook, 2: zalo, 3: instagram, 4: youtube, 5: line, 6: chattool  
@apiSuccess {String} 	socket_type			"CONTACT_SPAM"
@apiSuccess {String} 	action        hành động với contact thêm vào spam, gỡ spam: "add", "remove"		
@apiSuccess {String} 	contact_type        Loại liên hệ: email, phone, social


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "contact_spam": "social_id",
  "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
  "page_id": "13f9ee70-9294-42f8-917f-a03fa0438d73",
  "social_type": 1,
  "action": "add",
  "socket_type": "CONTACT_SPAM",
  "contact_type": "social"
}
"""



# ****************************************  Danh sách liên hệ spam ko phân trang   *******************************************
# * version: 1.0.0                                                                                   *
# ****************************************************************************************************
"""
@api {get} /contact/all_spam Danh sách liên hệ spam không phân trang 
@apiDescription Dịch vụ lấy danh sách liên hệ spam không phân trang 
@apiGroup ContactSpam
@apiVersion 1.0.0
@apiName ListAllContactSpam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam          (Query:) {String}        [contact_type]  Loại liên hệ, nhiều loại cách nhau bằng dấu , ("email,phone,social")
@apiParam          (Query:) {String}        [field]  field trả về, nhiều field cách nhau bằng dấu , ("merchant_id,
contact,contact_type,page_name,social_page_id,social_type,profile_id,staff_id,create_on")


@apiSuccess {Array}   data    Danh sách liên hệ 
@apiSuccess (data) {String} 	contact				Liên hệ là: Email, phone, user social id
@apiSuccess (data) {String} 	contact_type		Loại liên hệ: email,phone,social
@apiSuccess (data) {String} 	staff_id			Nhân viên id
@apiSuccess (data) {String}     merchant_id         Mã định danh merchant
@apiSuccess (data) {String}     create_on         Thời điểm thêm vào spam UTC


@apiSuccessExample {json} Response list contact example
{
  "code": 200,
  "data": [
      {
        "contact": "<EMAIL>",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "contact_type": "email",
        "spam_info": "<EMAIL>",
        "spam_location": "",
        "staff_id": "394f9237-48ae-48be-b4da-db469b99ef16",
        "create_on": "2021-01-11 03:48:03"
    },
     {
      "contact": "13f9ee70-9294-42f8-917f-a03fa0438d73",
      "contact_type": "social",
       "spam_info": "Phan Cường: 41390000954564933",
        "spam_location": "MOBIOSHOP",
      "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "social_page_id": "d59d4779-b707-4558-a594-4eb65f106ccc",
      "social_type": 1,
      "page_name": "Mobio shop",
      "create_on": "2021-01-11 03:48:03"
    },
     ...
  ],
 
  
}

@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}

"""


# ***************************************  Tìm kiếm 1 liên hệ có trong spam hay không  ********************************************
# * version: 1.0.0                                                                                   *
# ****************************************************************************************************
"""
@api {get} /contact/find Tìm kiếm 1 liên hệ 
@apiDescription Tìm kiếm 1 liên hệ
@apiGroup ContactSpam
@apiVersion 1.0.0
@apiName FindContactSpam

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam       (Query:)     contact              1 trong giá trị là: email, phone, user social id


@apiSuccess {Array}   data    Danh sách liên hệ 
@apiSuccess (data) {String} 	contact				Liên hệ là: Email, phone, user social id
@apiSuccess (data) {String} 	contact_type		Loại liên hệ: email,phone,social
@apiSuccess (data) {String} 	staff_id			Nhân viên id
@apiSuccess (data) {String}     merchant_id         Mã định danh merchant
@apiSuccess (data) {String}     create_on         Thời điểm thêm vào spam UTC


@apiSuccessExample {json} Response list contact example
{
  "code": 200,
  "data": {
      "contact": "13f9ee70-9294-42f8-917f-a03fa0438d73",
      "contact_type": "social",
      "staff_id": "c0713108-7449-4bd6-86d5-1198b73495c7",
      "social_page_id": "d59d4779-b707-4558-a594-4eb65f106ccc",
      "social_type": 1,
      "page_name": "Mobio shop",
      "create_on": "2021-01-11 03:48:03"
    },
  
}

@apiSuccessExample {json} Response error 
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": "Request không hợp lệ."
}


"""

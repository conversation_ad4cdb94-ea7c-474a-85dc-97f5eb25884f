************************************* List Trigger For SDK *******************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /trigger/list-for-sdk Danh sách trigger cho SDK
@apiGroup Trigger
@apiVersion 1.0.0
@apiName ListTriggerForSDK

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang


@apiParam   (Query:)    {String}    source_id     Nguồn cần l<PERSON>y chiến dịch


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
	"code": 200,
	"message": "request thành công.",
	"lang": "vi",
	"data": {
		"29086928931889671": {
			"trigger_id": "29086928931889671",
			"status": "ACTIVE",
			"target_text": "",
			"tracking_page_type": "CURRENT_PAGE",
			"target_address": "",
			"target_other_info": {
				"target_location": {
					"href": "https://test48.mobio.vn/#mo-preview",
					"origin": "https://test48.mobio.vn",
					"pathname": "/",
					"title": "Test 48 Mobio"
				}
			},
			"tracking_page_value": [],
			"trigger_type": "PAGE_VIEW",
			"target_name": "",
			"event_key": "linh_01_xem_trang_1713773222",
			"create_event_status": "DONE",
			"using_in_campaign_ids": [],
			"trigger_fields": [
				{
					"field_key": "action_time",
					"field_property": "datetime",
					"master_data": false,
					"event_key": "linh_01_xem_trang_1713773222",
					"trigger_id": 29086928931889671
				}
			],
			"trigger_results": []
		},
		"38475069580696963": {
			"trigger_id": "38475069580696963",
			"tracking_page_type": "CURRENT_PAGE",
			"target_address": "",
			"target_text": "",
			"trigger_type": "PAGE_VIEW",
			"tracking_page_value": [],
			"target_other_info": {
				"target_location": {
					"href": "https://test48.mobio.vn/#mo-preview",
					"origin": "https://test48.mobio.vn",
					"pathname": "/",
					"title": "Test 48 Mobio"
				}
			},
			"target_name": "",
			"status": "ACTIVE",
			"event_key": "event_o_trang_thai_ket_thuc_1713778388",
			"create_event_status": "DONE",
			"using_in_campaign_ids": [
				"72015937008102673"
			],
			"trigger_fields": [
				{
					"field_name": "Thời gian phát sinh event",
					"field_key": "action_time",
					"field_property": "datetime",
					"master_data": false,
					"event_key": "event_o_trang_thai_ket_thuc_1713778388",
					"field_name_raw": "thoi gian phat sinh event",
					"merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
					"trigger_id": 38475069580696963,
					"created_time": "2024-04-22 09:33:09.028000",
					"created_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003",
					"updated_time": "2024-04-22 09:33:09.028000",
					"updated_user": "7fc0a33c-baf5-11e7-a7c2-0242ac180003"
				}
			],
			"trigger_results": []
		},
		"29538376885755862": {
			"trigger_id": "29538376885755862",
			"tracking_page_type": "CURRENT_PAGE",
			"status": "ACTIVE",
			"trigger_type": "PAGE_VIEW",
			"target_address": "",
			"target_other_info": {
				"target_address": "",
				"target_class": "",
				"target_id": "",
				"target_index": 0,
				"target_text": "",
				"target_xpath": "",
				"target_location": {
					"href": "https://test48.mobio.vn/#mo-preview",
					"origin": "https://test48.mobio.vn",
					"pathname": "/",
					"title": "Test 48 Mobio"
				}
			},
			"tracking_page_value": [],
			"target_text": "",
			"event_key": "xem_trang_1713257166",
			"create_event_status": "DONE",
			"using_in_campaign_ids": [],
			"trigger_fields": [
				{
					"field_key": "action_time",
					"field_property": "datetime",
					"master_data": false,
					"event_key": "xem_trang_1713257166",
					"trigger_id": 29538376885755862
				},
				{
					"field_property": "string",
					"target_address": "//*[@id=\"show-now\"]",
					"target_other_info": {
						"target_xpath": "//*[@id=\"show-now\"]",
						"target_address": "//*[@id=\"show-now\"]",
						"target_class": "btn btn-secondary me-2",
						"target_id": "show-now",
						"target_index": 0,
						"target_text": "Shop Now",
						"target_href": "https://test48.mobio.vn/#mo-preview",
						"target_location": {
							"href": "https://test48.mobio.vn/#mo-preview",
							"origin": "https://test48.mobio.vn",
							"pathname": "/",
							"title": "Test 48 Mobio"
						}
					},
					"master_data": true,
					"field_method": "TEXT",
					"event_key": "xem_trang_1713257166",
					"field_key": "nut_shopnow_1713257167",
					"trigger_id": 29538376885755862
				}
			],
			"trigger_results": []
		},
		"48866401529646590": {
			"trigger_id": "48866401529646590",
			"tracking_page_value": [],
			"trigger_type": "ON_CLICK",
			"tracking_page_type": "CURRENT_PAGE",
			"status": "ACTIVE",
			"target_other_info": {
				"target_xpath": "//*[@id=\"show-now\"]",
				"target_address": "//*[@id=\"show-now\"]",
				"target_class": "btn btn-secondary me-2",
				"target_id": "show-now",
				"target_index": 0,
				"target_text": "Shop Now",
				"target_href": "https://test48.mobio.vn/#mo-preview",
				"target_location": {
					"href": "https://test48.mobio.vn/#mo-preview",
					"origin": "https://test48.mobio.vn",
					"pathname": "/",
					"title": "Test 48 Mobio"
				}
			},
			"target_address": "//*[@id=\"show-now\"]",
			"event_key": "bam_vao_button_shop_now_1713753578",
			"create_event_status": "DONE",
			"using_in_campaign_ids": [],
			"trigger_fields": [
				{
					"field_key": "action_time",
					"field_property": "datetime",
					"master_data": false,
					"event_key": "bam_vao_button_shop_now_1713753578",
					"trigger_id": 48866401529646590
				},
				{
					"field_property": "string",
					"field_method": "TEXT",
					"master_data": true,
					"target_other_info": {
						"target_xpath": "/html/body/div[2]/div/div/div[1]/p[1]",
						"target_address": "/html/body/div[2]/div/div/div[1]/p[1]",
						"target_class": "mb-4",
						"target_id": "",
						"target_index": 0,
						"target_text": "Donec vitae odio quis nisl dapibus malesuada. Nullam ac aliquet velit. Aliquam vulputate velit imperdiet dolor tempor tristique.",
						"target_href": "https://test48.mobio.vn/#mo-preview",
						"target_location": {
							"href": "https://test48.mobio.vn/#mo-preview",
							"origin": "https://test48.mobio.vn",
							"pathname": "/",
							"title": "Test 48 Mobio"
						}
					},
					"target_address": "/html/body/div[2]/div/div/div[1]/p[1]",
					"event_key": "bam_vao_button_shop_now_1713753578",
					"field_key": "event_1_1713753579",
					"trigger_id": 48866401529646590
				}
			],
			"trigger_results": []
		}
	}
}
"""
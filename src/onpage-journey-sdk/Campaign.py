************************************* List Campaign For SDK ******************************
* version: 1.0.0                                                                         *
******************************************************************************************
"""
@api {get} /campaign/list-for-sdk L<PERSON>y danh sách chiến dịch cho SDK
@apiGroup Campaign
@apiVersion 1.0.0
@apiName ListCampaignForSDK

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiUse merchant_id_header

@apiParam   (Query:)    {String}    source_id     Nguồn cần lấy chiến dịch
@apiParam   (Query:)    {String}    profile_id     ID profile tương tác
                                                                                
@apiSuccess   (Description response)    {String}    node_type  Kiểu node. Gi<PERSON> trị: <br/><code>TARGET</code>=node đối tượng mục tiêu<br/>
                                                                            <code>CONDITION_EVENT</code>=node kiểm tra event<br/>
                                                                            <code>CONDITION_FILTER_PROFILE</code>=node kiểm tra thông tin profile<br/>
                                                                            <code>ACTION_WEBPUSH</code>=node webpush<br/>
                                                                            <code>WAIT</code>=node chờ<br/>
                                                                            <code>EXIT</code>=node thoát<br/>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
	"code": 200,
	"message": "request thành công.",
	"lang": "vi",
	"data": {
		"campaigns": [
			{
				"source_type": "WEBSITE",
				"source_id": "367fe208-48b1-4ba1-b1c2-c768b05f0ed2",
				"campaign_id": "11408875459569886",
				"status": "PROCESSING",
				"exit_page_sign_status": false,
				"exit_page_sign_config": {},
				"end_time": "2024-04-24 09:29:00",
				"end_type": "AT_TIME",
				"start_time": "2024-04-22 09:30:01.484000",
				"start_type": "IMMEDIATELY",
				"nodes": [
					{
						"node_type": "EXIT",
						"node_id": "49014209329084073",
						"element_type": "EXIT",
						"node_connection": [
							{
								"previous_node_id": [
									"70631882661026939"
								],
								"next_node_id": ""
							}
						],
						"node_config": {}
					},
					{
						"node_type": "TARGET",
						"node_id": "19557151880335057",
						"element_type": "TRIGGER",
						"node_connection": [
							{
								"previous_node_id": [],
								"next_node_id": "70631882661026939"
							}
						],
						"node_config": {
							"waiting_time_after_primary_unit": "HOURS",
							"profile_target_type": "ALL",
							"trigger": {
								"triggers": [
									{
										"event_key": "cri_olap_pc_bam_vao_explore_1713777723",
										"trigger_id": "22820422787366781"
									}
								],
								"audience_id": "18863bea-700a-416f-9c30-eb439ad0a4f2"
							},
							"waiting_time_after_primary_value": 0
						}
					},
					{
						"node_type": "ACTION_WEBPUSH",
						"node_id": "70631882661026939",
						"element_type": "MESSAGE",
						"node_connection": [
							{
								"previous_node_id": [
									"19557151880335057"
								],
								"next_node_id": "49014209329084073"
							}
						],
						"node_config": {
							"popup_position": "tc",
							"popup_id": "660fc690d0ef3f361eb9c131"
						}
					}
				]
			}
		],
		"audience": {
			"18863bea-700a-416f-9c30-eb439ad0a4f2": {
				"id": "18863bea-700a-416f-9c30-eb439ad0a4f2",
				"estimate_customers": 0,
				"type": 1,
				"display_status": 2,
				"audience_type": "PROFILE_FILTER",
				"nodes": [
					{
						"profile_filter": [
							{
								"criteria_key": "cri_olap_pc_bam_vao_explore_1713777723",
								"operator_key": "op_is_not_empty",
								"values": []
							}
						],
						"position": 0,
						"operator": null
					}
				]
			}
		},
        "popup": {
            "25bcba61-8a8e-4c5e-806c-4ff798b8beff": {
                "id": "25bcba61-8a8e-4c5e-806c-4ff798b8beff", // popup id
                "url": "https://mobio.io", // popup url
                "position": "cc" // vị trí popup hiển thị
            }
        },
        "profile": {
            "name": "tên profile",
            "primary_email": {
                "detail": null,
                "email": "<EMAIL>",
                "last_check": "2023-06-20T03:59:05.318Z",
                "state": "success",
                "status": 1
            },
            "primary_phone": {
                "last_verify": "2023-06-05T09:50:36.178Z",
                "phone_number": "+84971443269",
                "status": 0
            }
        }
	}
}

@apiSuccessExample    {json}  TARGET node_config:
{
    "trigger": {
        "audience_id": "18863bea-700a-416f-9c30-eb439ad0a4f2"
        "triggers": [
            {
                "event_key": "cri_olap_pc_bam_vao_explore_1713777723",
                "trigger_id": "22820422787366781"
            }
        ],
    },
    "waiting_time_after_primary_value": 60,
    "waiting_time_after_primary_unit": "MINUTES",
    "profile_target_type": "SPECIFIC_CONDITION",
    "profile_audience_id": "e24aeb9e-d627-11ee-bd41-38d57a786a3e"
}                                       
@apiSuccessExample    {json}  CONDITION_EVENT node_config:
{
    "trigger": {
        "audience_id": "18863bea-700a-416f-9c30-eb439ad0a4f2"
        "triggers": [
            {
                "event_key": "cri_olap_pc_bam_vao_explore_1713777723",
                "trigger_id": "22820422787366781"
            }
        ],
    },
    "waiting_time_after_primary_value": 60,
    "waiting_time_after_primary_unit": "MINUTES",
    "capture_profile_exit": true
}
@apiSuccessExample    {json}  CONDITION_FILTER_PROFILE node_config:
{
    "profile_target_type": "SPECIFIC_CONDITION",
    "profile_audience_id": "0c95bda4-7bfb-11ee-83bf-38d57a786a3e"
}
@apiSuccessExample    {json}  WAIT node_config:
{
    "wait_type": "AFTER_TIME/SPECIFIC_DATETIME",
    "wait_time_value": 2,
    "wait_time_unit": "HOURS",
    "wait_datetime_value": "29/02/2024 07:00"
}
@apiSuccessExample    {json}  ACTION_WEBPUSH node_config:
{
    "popup_id": "51c7359a-df97-4e1a-af6b-c91a2c95c711",
    "popup_position": "lt"
}
"""
********************************************************************************************************************************************
************************************************** API LẤY DANH SÁCH FAVORITE VỀ PRODUCTS **************************************************
********************************************************************************************************************************************
"""
@api {get} /users/<user_id>/favorities/products Lấy danh sách Favorite về products
@apiDescription Lấy danh sách Favorite về products
@apiVersion 1.0.0
@apiGroup Favorite
@apiName GetFavoriteProducts

@apiParam (Resource:) {string} user_id UUID khách hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiSuccess  {string} id UUID sản phẩm
@apiSuccess  {string} name Tên sản phẩm
@apiSuccess  {string} product_represent_image Ảnh đại diện sản phẩm
@apiSuccess  {string} merchant_represent_image Ảnh đại diện nhà cung cấp
@apiSuccess  {string} price Giá sản phẩm
@apiSuccess  {number} point Điểm quy đổi từ giá sản phẩm
@apiSuccess  {number} merchant_type Kiểu nhà cung cấp
@apiSuccess  {number} product_type Kiểu sản phẩm
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "3f99bb25-4fee-4f19-986b-60c788b9b42e",
      "name": "Spaghetti Carbonara",
      "product_represent_image": "http://storage.googleapis.com/test_xloyalty1/images/test/3f99bb25-4fee-4f19-986b-60c788b9b42e?20170926023734",
      "merchant_represent_image": "http://storage.googleapis.com/test_xloyalty1/images/test/9c619bd6-c585-402d-8877-6fc80e4f5ce9?20171106092455",
      "price": "70,000VND",
      "point": 70,
      "merchant_type": 1,
      "product_type": 1
    },
    {
      "id": "3f99bb25-4fee-4f19-986b-60c788b9b42j",
      "name": "pizza Carbonara",
      "product_represent_image": "http://storage.googleapis.com/test_xloyalty1/images/test/3f99bb25-4fee-4f19-986b-60c788b9b42e?20170926023734",
      "merchant_represent_image": "http://storage.googleapis.com/test_xloyalty1/images/test/9c619bd6-c585-402d-8877-6fc80e4f5ce9?20171106092455",
      "price": "120,000VND",
      "point": 120,
      "merchant_type": 1,
      "product_type": 1
    }
  ],
  "paging": {
    ...
  }
}
"""


********************************************************************************************************************************************
************************************************** API LẤY DANH SÁCH FAVORITE VỀ VOUCHERS **************************************************
********************************************************************************************************************************************
"""
@api {get} /users/<user_id>/favorities/vouchers Lấy danh sách Favorite về vouchers
@apiDescription Lấy danh sách Favorite về vouchers
@apiVersion 1.0.0
@apiGroup Favorite
@apiName GetFavoriteVouchers

@apiParam (Resource:) {string} user_id UUID khách hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiSuccess  {string} id UUID sản phẩm
@apiSuccess  {string} name Tên sản phẩm
@apiSuccess  {string} represent_image Ảnh đại diện voucher
@apiSuccess  {string} merchant_represent_image Ảnh đại diện nhà cung cấp
@apiSuccess  {string} price Giá sản phẩm
@apiSuccess  {number} discount_value Số tiền giảm
@apiSuccess  {number} discount_percent Phần trăm giảm
@apiSuccess  {number} merchant_type Kiểu nhà cung cấp
@apiSuccess  {number} voucher_type Kiểu voucher
@apiSuccess  {DateTime} start_discount_time Thời điểm bắt đầu khuyến mãi
@apiSuccess  {DateTime} end_discount_time Thời diểm kết thúc khuyến mãi
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{  
  "data": [
    {
      "id": "e820648a-340d-49bc-82fc-545941bb0deb",
      "represent_image": "http://storage.googleapis.com/test_xloyalty1/images/khuyenmai/e820648a-340d-49bc-82fc-545941bb0deb?20171219025911",
      "merchant_represent_image": "http://storage.googleapis.com/test_xloyalty1/images/nhacungcap/c2207852-cb8a-4749-9a83-cbaca17d693d?20171110071602",
      "price": "Giá: Liên hệ",
      "discount_value": 0,
      "discount_percent": 0,
      "merchant_type": 1,
      "voucher_type": 1,
      "start_discount_time": "2017-08-07T04:02:28.002Z",
      "end_discount_time": "2017-08-07T04:02:28.002Z",
      "ten": "ƯU đãi 171219"
    },
    {
      "id": "e820648a-340d-49bc-82fc-545941bb0dej",
      "represent_image": "http://storage.googleapis.com/test_xloyalty1/images/khuyenmai/e820648a-340d-49bc-82fc-545941bb0deb?20171219025911",
      "merchant_represent_image": "http://storage.googleapis.com/test_xloyalty1/images/nhacungcap/c2207852-cb8a-4749-9a83-cbaca17d693d?20171110071602",
      "price": "Giá: Liên hệ",
      "discount_value": 0,
      "discount_percent": 0,
      "merchant_type": 1,
      "voucher_type": 1,
      "start_discount_time": "2017-08-07T04:02:28.002Z",
      "end_discount_time": "2017-08-07T04:02:28.002Z",
      "ten": "ƯU đãi 171219"
    }
  ],
  "paging": {
    ...
  }
}
"""


*******************************************************************************************************************************
************************************************** API ADD FAVORITE VOUCHERS **************************************************
*******************************************************************************************************************************
"""
@api {post} /users/<user_id>/favorities/vouchers Thêm một item vào mục Favorite phần Vouchers
@apiDescription Thêm một item vào mục Favorite phần Vouchers
@apiGroup Favorite
@apiVersion 1.0.0
@apiName PostFavoriteVouchers

@apiParam (Resource:) {string} user_id UUID khách hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {string} object_id UUID của item
@apiParamExample {json} Body
{
  "object_id ": "e820648a-340d-49bc-82fc-545941bb0dej"
}

@apiSuccess  {string} id UUID voucher
@apiSuccess  {string} name Tên voucher
@apiSuccess  {string} represent_image Ảnh đại diện voucher
@apiSuccess  {string} merchant_represent_image Ảnh đại diện nhà cung cấp
@apiSuccess  {string} price Giá voucher
@apiSuccess  {number} discount_value Số tiền giảm
@apiSuccess  {number} discount_percent Phần trăm giảm
@apiSuccess  {number} merchant_type Kiểu nhà cung cấp
@apiSuccess  {number} voucher_type Kiểu voucher
@apiSuccess  {DateTime} start_discount_time Thời điểm bắt đầu khuyến mãi
@apiSuccess  {DateTime} end_discount_time Thời diểm kết thúc khname mãi
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "e820648a-340d-49bc-82fc-545941bb0dej",
  "represent_image": "http://storage.googleapis.com/test_xloyalty1/images/khuyenmai/e820648a-340d-49bc-82fc-545941bb0deb?20171219025911",
  "merchant_represent_image": "http://storage.googleapis.com/test_xloyalty1/images/nhacungcap/c2207852-cb8a-4749-9a83-cbaca17d693d?20171110071602",
  "price": "Giá: Liên hệ",
  "discount_value": 0,
  "discount_percent": 0,
  "merchant_type": 1,
  "voucher_type": 1,
  "start_discount_time": "20171219025747",
  "end_discount_time": "29991231170000",
  "name": "ƯU đãi 171219"
}
"""


*******************************************************************************************************************************
************************************************** API ADD FAVORITE PRODUCTS **************************************************
*******************************************************************************************************************************
"""
@api {post} /users/<user_id>/favorities/products Thêm một item vào mục Favorite phần Products
@apiDescription Thêm một item vào mục Favorite phần Products
@apiGroup Favorite
@apiVersion 1.0.0
@apiName PostFavoriteProducts

@apiParam (Resource:) {string} user_id UUID khách hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {string} object_id UUID của item
@apiParamExample {json} Body
{
  "object_id ": "e820648a-340d-49bc-82fc-545941bb0dej"
}

@apiSuccess  {string} id UUID sản phẩm
@apiSuccess  {string} name Tên sản phẩm
@apiSuccess  {string} product_represent_image ảnh đại diện sản phẩm
@apiSuccess  {string} merchant_represent_image Ảnh đại diện nhà cung cấp
@apiSuccess  {string} price Giá sản phẩm
@apiSuccess  {number} point Điểm sản phẩm
@apiSuccess  {number} merchant_type Kiểu nhà cung cấp
@apiSuccess  {number} product_type Kiểu sản phẩm
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "3f99bb25-4fee-4f19-986b-60c788b9b42j",
  "name": "pizza Carbonara",
  "product_represent_image": "http://storage.googleapis.com/test_xloyalty1/images/test/3f99bb25-4fee-4f19-986b-60c788b9b42e?20170926023734",
  "merchant_represent_image": "http://storage.googleapis.com/test_xloyalty1/images/test/9c619bd6-c585-402d-8877-6fc80e4f5ce9?20171106092455",
  "price": "120,000VND",
  "point": 120,
  "merchant_type": 1,
  "product_type": 1
}
"""


**************************************************************************************************************************
************************************************** API BỎ FAVORITE ITEM **************************************************
**************************************************************************************************************************
"""
@api {delete} /users/<user_id>/favorities Bỏ item khỏi khỏi mục Favorite
@apiDescription Bỏ item khỏi khỏi mục Favorite
@apiGroup Favorite
@apiVersion 1.0.0
@apiName DeleteFavorite

@apiParam (Resource:) {string} user_id UUID khách hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {string} object_id UUID của item
@apiParam  {number=1:Product 2:Voucher} object_type Kiểu object
@apiParamExample {json} Body
{
  "object_id ": "e820648a-340d-49bc-82fc-545941bb0dej",
  "object_tye": 1
}

@apiSuccess  {string} id UUID sản phẩm
@apiSuccess  {string} name Tên sản phẩm
@apiSuccess  {string} product_represent_image ảnh đại diện sản phẩm
@apiSuccess  {string} merchant_represent_image Ảnh đại diện nhà cung cấp
@apiSuccess  {string} price Giá sản phẩm
@apiSuccess  {number} point Điểm sản phẩm
@apiSuccess  {number} merchant_type Kiểu nhà cung cấp
@apiSuccess  {number} product_type Kiểu sản phẩm
@apiSuccessExample {json} Data product
{
  "id": "3f99bb25-4fee-4f19-986b-60c788b9b42j",
  "name": "pizza Carbonara",
  "product_represent_image": "http://storage.googleapis.com/test_xloyalty1/images/test/3f99bb25-4fee-4f19-986b-60c788b9b42e?20170926023734",
  "merchant_represent_image": "http://storage.googleapis.com/test_xloyalty1/images/test/9c619bd6-c585-402d-8877-6fc80e4f5ce9?20171106092455",
  "price": "120,000VND",
  "point": 120,
  "merchant_type": 1,
  "product_type": 1
}

@apiSuccessExample {json} Data voucher
{
  "id": "e820648a-340d-49bc-82fc-545941bb0dej",
  "represent_image": "http://storage.googleapis.com/test_xloyalty1/images/khuyenmai/e820648a-340d-49bc-82fc-545941bb0deb?20171219025911",
  "merchant_represent_image": "http://storage.googleapis.com/test_xloyalty1/images/nhacungcap/c2207852-cb8a-4749-9a83-cbaca17d693d?20171110071602",
  "price": "Giá: Liên hệ",
  "discount_value": 0,
  "discount_percent": 0,
  "merchant_type": 1,
  "voucher_type": 1,
  "start_discount_time": "20171219025747",
  "end_discount_time": "29991231170000",
  "name": "ƯU đãi 171219"
}
"""
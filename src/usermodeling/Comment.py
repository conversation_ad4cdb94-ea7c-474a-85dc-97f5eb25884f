#!/usr/bin/python
# -*- coding: utf8 -*-

****************************************************************************************************************************
************************************************** API XOÁ MỘT BÌNH LUẬN  **************************************************
****************************************************************************************************************************
"""
@api {delete} /merchants/<merchant_id>/comments/<comment_id> Xoá một bình luận
@apiDescription Xoá một bình luận
@apiVersion 1.0.0
@apiGroup Comment
@apiName DeleteComment

@apiParam (Resource:) {string} merchant_id UUID nhãn hàng
@apiParam (Resource:) {string} comment_id UUID bình luận cần xoá

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {number} replies_number Số lượng comment reply
@apiSuccess  {number} deleted_number Số lượng bản ghi đã bị xoá
@apiSuccess  {string} comment_id UUID bình luận bị xoá
@apiSuccessExample {json} HTTP 200 OK
{
    "replies_number": 5,
    "deleted_number": 6,
    "comment_id": "52a8618b-4436-4bbc-947d-e82edd9b09f4"
}
"""


**********************************************************************************************************************************************
************************************************** API LẤY DANH SÁCH BÌNH LUẬN CỦA MỘT ITEM **************************************************
**********************************************************************************************************************************************
"""
@api {get} /merchants/<merchant_id>/comments/items/<item_id> Lấy danh sách bình luận của một item
@apiDescription Lấy danh sách bình luận của một item
@apiVersion 1.0.0
@apiGroup Comment
@apiName GetListComment

@apiParam (Resource:) {string} item_id UUID của item cần lấy danh sách bình luận

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiSuccess     {Number} [no_replies_number]    Số lượng bình luận nhãn hàng chưa trả lời.
@apiSuccess     {Item[]}  items    Mảng sản phẩm
@apiSuccess     {String}    id  UUID của item
@apiSuccess     {String}    title  Tiêu đề item
@apiSuccess     {Object}    [subtitle]  Phụ đề item
<li><code>type {Number=1-PRICE;2-SINCE_UNTIL}:</code> Kiểu phụ đề</li>
<li><code>price [optional] {Number}:</code> Giá item</li>
<li><code>since [optional] {Datetime}:</code>Thời điểm bán bắt đầu</li>
<li><code>until [optional] {Datetime}:</code>Thời điểm bán kết thúc</li>
@apiSuccess     {String[]}    [images]  Danh sách ảnh trong bình luận
@apiSuccess     {Comment[]}  comments    Danh sách bình luận. Chi tiết:
<li><code>id {String}:</code>UUID bình luận</li>
<li><code>content {String}:</code>Nội dung bình luận</li>
<li><code>posted_time {ISO-Date}:</code>Thời điểm tạo</li>
<li><code>status {Number=1-VISIBLE;2-INVISIBLE;}:</code>Trạng thái bình luận</li>
<li><code>is_replied {Number=1-FALSE;2-TRUE;}: </code>Trạng thái bình luận đã được trả lời hay chưa</li>
<li><code>images {Image[]}: </code>Danh sách ảnh trong bình luận</li>
<br/>
@apiSuccess     {Json}  [owner] Thông tin người gửi bình luận. Chi tiết:<br/>
<li><code>id:</code>Tên truy cập nhân viên nếu <code>type=1</code>. Nếu không, thì là UUID khách hàng</li>
<li><code>name [optional]:</code>Tên người gửi</li>
<li><code>avatar [optional]:</code>Ảnh đại diện người gửi</li>
<li><code>email [optional]:</code>Email người gửi</li>
<li><code>type:</code>Kiểu gửi. Allowed values: <code>1-CUSTOMER,2-MERCHANT</code></li>
<br/>
@apiSuccess     {Json}  [quote] Chi tiết trích dẫn:<br/><br/>
<li><code>comment_id:</code>id bình luận trích dẫn</li>
<li><code>name [optional]:</code>Tên liên kết trích dẫn</li>
<li><code>email [optional]:</code>Thư điện thử liên kết trích dẫn</li>
<li><code>content:</code>Nội dung liên kết trích dẫn</li>
<br/>
@apiSuccessExample {json} HTTP 200 OK
{
  "no_replies_number": 5,
  "data": [
    {
      "id": "8714eb4c-1bc3-4e25-ae42-e303d94827ea",
      "owner": {
        "id": "29c90dbe-d2d9-4a33-ba9b-ac357f700995",
        "name": "KH1",
        "avatar": "http://graph.facebook.com/************200/picture?type=large",
        "type": 1,
        "email": "<EMAIL>"
      },
      "content": "This is comment's content",
      "quote": {
        "comment_id": "",
        "name": "KH2",
        "email": "<EMAIL>",
        "content": "Nhãn hàng mình có cơ sở bên Cầu Giấy ko ad?"
      },
      "posted_time": "2017-08-07T04:02:28.002Z",
      "status": 1,
      "is_replied": 1,
      "images": [
        {
          "id": "adb285f8-6179-4051-8cf0-3d71d8deb2bd",
          "link": "http://graph.facebook.com/************200/picture?type=large"
        },
        {
          "id": "08664801-f2af-47c9-ade6-8559f5134f3c",
          "link": "http://graph.facebook.com/************200/picture?type=large"
        }
      ]
    },
    {
      "id": "9df4f4ad-2ebc-4f25-8fbe-57da38a7df0a",
      "owner": {
        "id": "29c90dbe-d2d9-4a33-ba9b-ac357f700995",
        "name": "KH1",
        "avatar": "http://graph.facebook.com/************200/picture?type=large",
        "type": 2,
        "email": "<EMAIL>"
      },
      "content": "Chào bạn, cảm ơn bạn đã sử dụng dịch vụ của PINGCOMSHOP. Mình sẽ check và phản hồi lại sớm cho bạn.",
      "quote": {
        "comment_id": "8714eb4c-1bc3-4e25-ae42-e303d94827ea",
        "name": "KH1",
        "email": "<EMAIL>",
        "content": "This is comment's content"
      },
      "posted_time": "2017-08-07T04:02:28.002Z",
      "status": 1,
      "is_replied": 0,
      "images": [
        {
          "id": "adb285f8-6179-4051-8cf0-3d71d8deb2bd",
          "link": "http://graph.facebook.com/************200/picture?type=large"
        },
        {
          "id": "08664801-f2af-47c9-ade6-8559f5134f3c",
          "link": "http://graph.facebook.com/************200/picture?type=large"
        }
      ]
    }
  ],
  "paging": {
    ...
  }
}
"""


*************************************************************************************************************************************************
************************************************** API LẤY DANH SÁCH BÌNH LUẬN CỦA TẤT CẢ ITEM **************************************************
*************************************************************************************************************************************************
"""
@api {get} /merchants/<merchant_id>/comments/items Lấy danh sách bình luận của tất cả item
@apiDescription Lấy danh sách bình luận của tất cả item
@apiVersion 1.0.0
@apiGroup Comment
@apiName GetListCommentAllItem

@apiParam (Resource:) {string} merchant_id UUID nhà cung cấp

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam   (Query:)    {Number}    [comments_per_item=5] Số lượng bình luận trên 1 item. Ví dụ: <code>&comments_per_item=3</code>

@apiSuccess     {Number} [no_replies_number]    Số lượng bình luận nhãn hàng chưa trả lời.
@apiSuccess     {Item[]}  items    Mảng sản phẩm
@apiSuccess     {String}    id  UUID của item
@apiSuccess     {String}    title  Tiêu đề item
@apiSuccess     {Object}    [subtitle]  Phụ đề item
<li><code>type {Number=1-PRICE;2-SINCE_UNTIL}:</code> Kiểu phụ đề</li>
<li><code>price [optional] {Number}:</code> Giá item</li>
<li><code>since [optional] {Datetime}:</code>Thời điểm bán bắt đầu</li>
<li><code>until [optional] {Datetime}:</code>Thời điểm bán kết thúc</li>
@apiSuccess     {String[]}    [images]  Danh sách ảnh trong bình luận
@apiSuccess     {Comment[]}  comments    Danh sách bình luận. Chi tiết:
<li><code>id {String}:</code>UUID bình luận</li>
<li><code>content {String}:</code>Nội dung bình luận</li>
<li><code>posted_time {ISO-Date}:</code>Thời điểm tạo</li>
<li><code>status {Number=1-VISIBLE;2-INVISIBLE;}:</code>Trạng thái bình luận</li>
<li><code>is_replied {Number=1-FALSE;2-TRUE;}: </code>Trạng thái bình luận đã được trả lời hay chưa</li>
<li><code>images {Image[]}: </code>Danh sách ảnh trong bình luận</li>
<br/>
@apiSuccess     {Json}  [owner] Thông tin người gửi bình luận. Chi tiết:<br/>
<li><code>id:</code>Tên truy cập nhân viên nếu <code>type=1</code>. Nếu không, thì là UUID khách hàng</li>
<li><code>name [optional]:</code>Tên người gửi</li>
<li><code>avatar [optional]:</code>Ảnh đại diện người gửi</li>
<li><code>email [optional]:</code>Email người gửi</li>
<li><code>type:</code>Kiểu gửi. Allowed values: <code>1-CUSTOMER,2-MERCHANT</code></li>
<br/>
@apiSuccess     {Json}  [quote] Chi tiết trích dẫn:<br/><br/>
<li><code>comment_id:</code>id bình luận trích dẫn</li>
<li><code>name [optional]:</code>Tên liên kết trích dẫn</li>
<li><code>email [optional]:</code>Thư điện thử liên kết trích dẫn</li>
<li><code>content:</code>Nội dung liên kết trích dẫn</li>
<br/>
@apiSuccessExample {json} HTTP 200 OK
{
    "data":{
        "common":{
            "no_replies_number":5
        },
        "items":[
            {
                "id":"33d70307-99c1-4426-a302-56ba09216d23",
                "title":"HCM - Cocasuki...",
                "subtitle":{
                    "type":2,
                    "since":"2017-07-07T04:02:28.002Z",
                    "until":"2017-08-07T04:02:28.002Z"
                }
                "images":[
                    "http://storage.googleapis.com/test_xloyalty1/images/product/001b117e-8441-4cd1-9cd4-f2a6b6fbd2d2?1473758049015"
                ],
                "comments":[
                    {
                        "id":"8714eb4c-1bc3-4e25-ae42-e303d94827ea",
                        "owner":{
                            "id":"29c90dbe-d2d9-4a33-ba9b-ac357f700995",
                            "name":"KH1",
                            "avatar":"http://graph.facebook.com/************200/picture?type=large",
                            "type":1,
                            "email":"<EMAIL>"
                        },
                        "content":"This is comment's content",
                        "quote":{
                            "comment_id":"",
                            "name":"KH2",
                            "email":"<EMAIL>",
                            "content":"Nhãn hàng mình có cơ sở bên Cầu Giấy ko ad?"
                        },
                        "posted_time":"2017-08-07T04:02:28.002Z",
                        "status":1,
                        "is_replied":1,
                        "images":[
                            {
                                "id":"adb285f8-6179-4051-8cf0-3d71d8deb2bd",
                                "link":"http://graph.facebook.com/************200/picture?type=large",
                            },
                            {
                                "id":"08664801-f2af-47c9-ade6-8559f5134f3c",
                                "link":"http://graph.facebook.com/************200/picture?type=large"
                            }
                        ]
                    },
                    {
                        "id":"9df4f4ad-2ebc-4f25-8fbe-57da38a7df0a",
                        "owner":{
                            "id":"29c90dbe-d2d9-4a33-ba9b-ac357f700995",
                            "name":"KH1",
                            "avatar":"http://graph.facebook.com/************200/picture?type=large",
                            "type":2,
                            "email":"<EMAIL>"
                        },
                        "content":"Chào bạn, cảm ơn bạn đã sử dụng dịch vụ của PINGCOMSHOP. Mình sẽ check và phản hồi lại sớm cho bạn.",
                        "quote":{
                            "comment_id":"8714eb4c-1bc3-4e25-ae42-e303d94827ea",
                            "name":"KH1",
                            "email":"<EMAIL>",
                            "content":"This is comment's content"
                        },
                        "posted_time":"2017-08-07T04:02:28.002Z",
                        "status":1,
                        "is_replied":0,
                        "images":[
                            {
                                "id":"adb285f8-6179-4051-8cf0-3d71d8deb2bd",
                                "link":"http://graph.facebook.com/************200/picture?type=large",
                            },
                            {
                                "id":"08664801-f2af-47c9-ade6-8559f5134f3c",
                                "link":"http://graph.facebook.com/************200/picture?type=large"
                            }
                        ]
                    }
                ]
            }
        ]
    }
}
"""


**************************************************************************************************************************************************
************************************************** API LẤY DANH SÁCH BÌNH LUẬN CỦA MỘT MERCHANT **************************************************
**************************************************************************************************************************************************
"""
@api {get} /merchants/<merchant_id>/comments Lấy danh sách bình luận của một merchant
@apiDescription Lấy danh sách bình luận của một merchant
@apiVersion 1.0.0
@apiGroup Comment
@apiName GetListCommentItem

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiSuccess     {Number} [no_replies_number]    Số lượng bình luận nhãn hàng chưa trả lời.
@apiSuccess     {Comment[]}  comments    Danh sách bình luận. Chi tiết:
<li><code>id {String}:</code>UUID bình luận</li>
<li><code>content {String}:</code>Nội dung bình luận</li>
<li><code>posted_time {ISO-Date}:</code>Thời điểm tạo</li>
<li><code>status {Number=1-VISIBLE;2-INVISIBLE;}:</code>Trạng thái bình luận</li>
<li><code>is_replied {Number=1-FALSE;2-TRUE;}: </code>Trạng thái bình luận đã được trả lời hay chưa</li>
<li><code>images {Image[]}: </code>Danh sách ảnh trong bình luận</li>
<br/>
@apiSuccess     {Json}  [owner] Thông tin người gửi bình luận. Chi tiết:<br/>
<li><code>id:</code>Tên truy cập nhân viên nếu <code>type=1</code>. Nếu không, thì là UUID khách hàng</li>
<li><code>name [optional]:</code>Tên người gửi</li>
<li><code>avatar [optional]:</code>Ảnh đại diện người gửi</li>
<li><code>email [optional]:</code>Email người gửi</li>
<li><code>type:</code>Kiểu gửi. Allowed values: <code>1-CUSTOMER,2-MERCHANT</code></li>
<br/>
@apiSuccess     {Json}  [quote] Chi tiết trích dẫn:<br/><br/>
<li><code>comment_id:</code>id bình luận trích dẫn</li>
<li><code>name [optional]:</code>Tên liên kết trích dẫn</li>
<li><code>email [optional]:</code>Thư điện thử liên kết trích dẫn</li>
<li><code>content:</code>Nội dung liên kết trích dẫn</li>
<br/>
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "data":{
        "common":{
            "no_replies_number":5
        },
        "comments":[
            {
                "id":"8714eb4c-1bc3-4e25-ae42-e303d94827ea",
                "owner":{
                    "id":"29c90dbe-d2d9-4a33-ba9b-ac357f700995",
                    "name":"KH1",
                    "avatar":"http://graph.facebook.com/************200/picture?type=large",
                    "type":1,
                    "email":"<EMAIL>"
                },
                "content":"This is comment's content",
                "quote":{
                    "comment_id":"",
                    "name":"KH2",
                    "email":"<EMAIL>",
                    "content":"Nhãn hàng mình có cơ sở bên Cầu Giấy ko ad?"
                },
                "posted_time":"2017-08-07T04:02:28.002Z",
                "status":1,
                "is_replied":1,
                "images":[
                    {
                        "id":"adb285f8-6179-4051-8cf0-3d71d8deb2bd",
                        "link":"http://graph.facebook.com/************200/picture?type=large",
                    },
                    {
                        "id":"08664801-f2af-47c9-ade6-8559f5134f3c",
                        "link":"http://graph.facebook.com/************200/picture?type=large"
                    }
                ]
            },
            {
                "id":"9df4f4ad-2ebc-4f25-8fbe-57da38a7df0a",
                "owner":{
                    "id":"29c90dbe-d2d9-4a33-ba9b-ac357f700995",
                    "name":"KH1",
                    "avatar":"http://graph.facebook.com/************200/picture?type=large",
                    "type":2,
                    "email":"<EMAIL>"
                },
                "content":"Chào bạn, cảm ơn bạn đã sử dụng dịch vụ của PINGCOMSHOP. Mình sẽ check và phản hồi lại sớm cho bạn.",
                "quote":{
                    "comment_id":"8714eb4c-1bc3-4e25-ae42-e303d94827ea",
                    "name":"KH1",
                    "email":"<EMAIL>",
                    "content":"This is comment's content"
                },
                "posted_time":"2017-08-07T04:02:28.002Z",
                "status":1,
                "is_replied":0,
                "images":[
                    {
                        "id":"adb285f8-6179-4051-8cf0-3d71d8deb2bd",
                        "link":"http://graph.facebook.com/************200/picture?type=large",
                    },
                    {
                        "id":"08664801-f2af-47c9-ade6-8559f5134f3c",
                        "link":"http://graph.facebook.com/************200/picture?type=large"
                    }
                ]
            }
        ]
    }
}
"""


**************************************************************************************************************************
************************************************** API ẨN MỘT BÌNH LUẬN **************************************************
**************************************************************************************************************************
"""
@api {post} /merchants/<merchant_id>/comments/<comment_id>/hide Ẩn một bình luận
@apiDescription Ẩn một bình luận
@apiVersion 1.0.0
@apiGroup Comment
@apiName HideComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:) {String}    comment_id  UUID bình luận cần ẩn

@apiSuccess  {string} id UUID bình luận ẩn
@apiSuccess  {number=1:Hiển 2:Ẩn} status Trạng thái bình luận
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id":"52a8618b-4436-4bbc-947d-e82edd9b09f4",
  "status":2
}
"""


*****************************************************************************************************************************
************************************************** API BỎ ẨN MỘT BÌNH LUẬN **************************************************
*****************************************************************************************************************************
"""
@api {post} /merchants/<merchant_id>/comments/<comment_id>/unhide Bỏ ẩn một bình luận
@apiDescription Bỏ ẩn một bình luận
@apiVersion 1.0.0
@apiGroup Comment
@apiName UnHideComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Resource:) {String}    comment_id  UUID bình luận cần ẩn

@apiSuccess  {string} id UUID bình luận ẩn
@apiSuccess  {number=1:Hiển 2:Ẩn} status Trạng thái bình luận
@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "id":"52a8618b-4436-4bbc-947d-e82edd9b09f4",
  "status":1
}
"""


*******************************************************************************************************************************
************************************************** API TRẢ LỜI MỘT BÌNH LUẬN **************************************************
*******************************************************************************************************************************
"""
@api {post} /merchants/<merchant_id>/comments/<comment_id>/reply Trả lời một bình luận
@apiDescription Trả lời một bình luận. Sử dụng phương thức POST với body là <code>content-type: multipart/form-data</code> để upload ảnh và nội dung bình luận.
@apiVersion 1.0.0
@apiGroup Comment
@apiName PostReplyComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Resource:) {string} merchant_id UUID nhà cung cấp
@apiParam (Resource:) {string} comment_id UUID bình luận muốn trả lời

@apiParam (Body: Form-data) {string} content Nội dung bình luận
@apiParam (Body: Form-data) {File[]} [images] Danh sách ảnh đính kèm bình luận.

@apiSuccess     {Number} [no_replies_number]    Số lượng bình luận nhãn hàng chưa trả lời.
@apiSuccess  {object} data Dữ liệu bình luận.

@apiSuccess (data) {string} id UUID bình luận
@apiSuccess (data) {Json} [owner] Thông tin người gửi bình luận. Chi tiết:<br/>
<li><code>id:</code>Tên truy cập nhân viên nếu <code>type=1</code>. Nếu không, thì là UUID khách hàng</li>
<li><code>name [optional]:</code>Tên người gửi</li>
<li><code>avatar [optional]:</code>Ảnh đại diện người gửi</li>
<li><code>email [optional]:</code>Email người gửi</li>
<li><code>type:</code>Kiểu gửi. Allowed values: <code>1-CUSTOMER,2-MERCHANT</code></li>
<br/>
@apiSuccess (data) {string} content Nội dung bình luận.
@apiSuccess (data) {Json} [quote] Chi tiết trích dẫn:<br/><br/>
<li><code>comment_id:</code>id bình luận trích dẫn</li>
<li><code>name [optional]:</code>Tên liên kết trích dẫn</li>
<li><code>email [optional]:</code>Thư điện thử liên kết trích dẫn</li>
<li><code>content:</code>Nội dung liên kết trích dẫn</li>
<br/>
@apiSuccess (data) {datatime} posted_time Thời điểm tạo bình luận
@apiSuccess (data) {number} status Trạng thái hiển thị bình luận
@apiSuccess (data) {Number=1-FALSE 2-TRUE} is_replied Trạng thái được nhãn hàng trả lời
@apiSuccess (data) {Images[]} images Danh sách đối tượng image đính kèm. Chi tiết:<br/>
<li><code>id:</code> UUID đối tượng ảnh</li>
<li><code>link:</code> link ảnh</li>
<br/>
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "no_replies_number": 5,
  "data": {
    "id": "8714eb4c-1bc3-4e25-ae42-e303d94827ea",
    "owner": {
      "id": "29c90dbe-d2d9-4a33-ba9b-ac357f700995",
      "name": "PINGCOMSHOP",
      "avatar": "http://graph.facebook.com/************200/picture?type=large",
      "type": 1,
      "email": "<EMAIL>"
    },
    "content": "This is comment's content",
    "quote": {
      "comment_id": "",
      "name": "KH2",
      "email": "<EMAIL>",
      "content": "Nhãn hàng mình có cơ sở bên Cầu Giấy ko ad?"
    },
    "posted_time": "2017-08-07T04:02:28.002Z",
    "status": 1,
    "is_replied": 1,
    "images": [
      {
        "id": "c9caee85-a7ad-43f0-addd-f5a0e1c3f3f6",
        "link": "http://graph.facebook.com/************200/picture?type=large"
      },
      {
        "id": "82ccf853-235a-48b7-aed5-f362e8bb69a2",
        "link": "http://graph.facebook.com/************200/picture?type=large"
      }
    ]
  }
}
"""


********************************************************************************************************************************
************************************************** API CẬP NHẬT MỘT BÌNH LUẬN **************************************************
********************************************************************************************************************************
"""
@api {patch} /merchants/<merchant_id>/comments/<comment_id> Cập nhật một bình luận
@apiDescription Cập nhật một bình luận. Sử dụng phương thức PATCH với body là <code>content-type: multipart/form-data</code> để upload ảnh và nội dung bình luận.
@apiVersion 1.0.0
@apiGroup Comment
@apiName PatchComment

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Resource:) {string} merchant_id UUID nhà cung cấp
@apiParam (Resource:) {string} comment_id UUID bình luận muốn trả lời

@apiParam (Body: Form-data) {string} content Nội dung bình luận
@apiParam (Body: Form-data) {File[]} [images] Danh sách ảnh đính kèm bình luận.

@apiSuccess  {Number} [no_replies_number]    Số lượng bình luận nhãn hàng chưa trả lời.
@apiSuccess  {object} data Dữ liệu bình luận.

@apiSuccess (data) {string} id UUID bình luận
@apiSuccess (data) {Json} [owner] Thông tin người gửi bình luận. Chi tiết:<br/>
<li><code>id:</code>Tên truy cập nhân viên nếu <code>type=1</code>. Nếu không, thì là UUID khách hàng</li>
<li><code>name [optional]:</code>Tên người gửi</li>
<li><code>avatar [optional]:</code>Ảnh đại diện người gửi</li>
<li><code>email [optional]:</code>Email người gửi</li>
<li><code>type:</code>Kiểu gửi. Allowed values: <code>1-CUSTOMER,2-MERCHANT</code></li>
<br/>
@apiSuccess (data) {string} content Nội dung bình luận.
@apiSuccess (data) {Json} [quote] Chi tiết trích dẫn:<br/><br/>
<li><code>comment_id:</code>id bình luận trích dẫn</li>
<li><code>name [optional]:</code>Tên liên kết trích dẫn</li>
<li><code>email [optional]:</code>Thư điện thử liên kết trích dẫn</li>
<li><code>content:</code>Nội dung liên kết trích dẫn</li>
<br/>
@apiSuccess (data) {datatime} posted_time Thời điểm tạo bình luận
@apiSuccess (data) {number} status Trạng thái hiển thị bình luận
@apiSuccess (data) {Number=1-FALSE 2-TRUE} is_replied Trạng thái được nhãn hàng trả lời
@apiSuccess (data) {Images[]} images Danh sách đối tượng image đính kèm. Chi tiết:<br/>
<li><code>id:</code> UUID đối tượng ảnh</li>
<li><code>link:</code> link ảnh</li>
<br/>
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "no_replies_number": 5,
  "data": {
    "id": "8714eb4c-1bc3-4e25-ae42-e303d94827ea",
    "owner": {
      "id": "29c90dbe-d2d9-4a33-ba9b-ac357f700995",
      "name": "PINGCOMSHOP",
      "avatar": "http://graph.facebook.com/************200/picture?type=large",
      "type": 1,
      "email": "<EMAIL>"
    },
    "content": "This is comment's content",
    "quote": {
      "comment_id": "",
      "name": "KH2",
      "email": "<EMAIL>",
      "content": "Nhãn hàng mình có cơ sở bên Cầu Giấy ko ad?"
    },
    "posted_time": "2017-08-07T04:02:28.002Z",
    "status": 1,
    "is_replied": 1,
    "images": [
      {
        "id": "c9caee85-a7ad-43f0-addd-f5a0e1c3f3f6",
        "link": "http://graph.facebook.com/************200/picture?type=large"
      },
      {
        "id": "82ccf853-235a-48b7-aed5-f362e8bb69a2",
        "link": "http://graph.facebook.com/************200/picture?type=large"
      }
    ]
  }
}
"""


****************************************************************************************************************************
************************************************** API THÊM MỘT BÌNH LUẬN **************************************************
****************************************************************************************************************************
"""
@api {POST} /merchants/<merchant_id>/comments Thêm một bình luận
@apiDescription Dùng App hoặc Web comment một item thì đẩy qua api này. 
@apiVersion 1.0.0
@apiGroup Comment
@apiName PostComment

@apiParam (Resource:) {string} merchant_id UUID nhà cung cấp

@apiParam (Body: Form-data) {string} content Nội dung bình luận
@apiParam (Body: Form-data) {File[]} [images] Danh sách ảnh đính kèm bình luận.
@apiParam (Body: Form-data) {string} item_id UUID của đối tượng nhận comment
@apiParam (Body: Form-data) {number} item_type loại đối tượng, loại được định nghĩa theo object_type trong file <a target="_blank" href="https://drive.google.com/open?id=1BCS2VWvNXJdgdrIVH4sKjB3v7ldgsNa4ekx3wuJkQMg">data_define</a>

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {string} id UUID bình luận
@apiSuccess  {string} content Nội dung bình luận
@apiSuccess  {DateTime} posted_time Thời điểm viết bình luận
@apiSuccess  {number=1:Hiển-thị 2:Ẩn} status Trạng thái bình luận
@apiSuccess  {number=1-FALSE 2-TRUE} is_replied Trạng thái được nhãn hàng trả lời

@apiSuccess (owner) {string} id UUID người viết bình luận
@apiSuccess (owner) {string} name Tên người viết bình luận
@apiSuccess (owner) {string} avatar Ảnh đại diện người viết bình luận
@apiSuccess (owner) {number=1-CUSTOMER 2-MERCHANT} type Kiểu người viết bình luận
@apiSuccess (owner) {string} email Thư điện tử người viết bình luận

@apiSuccess (images) {Image[]} images Danh sách ảnh đính kèm bình luận
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "8714eb4c-1bc3-4e25-ae42-e303d94827ea",
  "owner": {
    "id": "29c90dbe-d2d9-4a33-ba9b-ac357f700995",
    "name": "PINGCOMSHOP",
    "avatar": "http://graph.facebook.com/************200/picture?type=large",
    "type": 1,
    "email": "<EMAIL>"
  },
  "content": "This is comment's content",
  "posted_time": "2017-08-07T04:02:28.002Z",
  "status": 1,
  "is_replied": 1,
  "images": [
    {
      "id": "c9caee85-a7ad-43f0-addd-f5a0e1c3f3f6",
      "link": "http://graph.facebook.com/************200/picture?type=large"
    },
    {
      "id": "82ccf853-235a-48b7-aed5-f362e8bb69a2",
      "link": "http://graph.facebook.com/************200/picture?type=large"
    }
  ]
}
"""
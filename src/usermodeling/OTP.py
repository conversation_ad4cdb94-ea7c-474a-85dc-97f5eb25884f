***************************************************************************************************************************************************
************************************************** API KHÁCH HÀNG YÊU CẦU XÁC THỰC SỐ ĐIỆN THOẠI **************************************************
***************************************************************************************************************************************************
"""
@api {post} /users/<user_id>/otp/sms Khách hàng yêu cầu xác thực số điện thoại
@apiDescription Khách hàng yêu cầu xác thực số điện thoại
@apiVersion 1.0.0
@apiGroup OTP
@apiName PostOTPSMS

@apiParam (Resource:) {string} user_id UUID khách hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {string} phone_number Số điện thoại nhận OTP
@apiParamExample {json} Body example
{
  "phone_number": "0988830588"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request success"
}
"""


********************************************************************************************************************************************
************************************************** API KHÁCH HÀNG XÁC THỰC MÃ OTP CỦA SMS **************************************************
********************************************************************************************************************************************
"""
@api {put} /users/<user_id>/otp/sms Khách hàng xác thực mã OTP của SMS
@apiDescription Khách hàng xác thực mã OTP của SMS
@apiVersion 1.0.0
@apiGroup OTP
@apiName PutOTPSMS

@apiParam (Resource:) {string} user_id UUID khách hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {string} authent_code Mã xác thực khác hàng gửi
@apiParamExample {json} Body example
{
  "authent_code": "068743"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request success"
}
"""


*******************************************************************************************************************************************
************************************************** API KHÁCH HÀNG YÊU CẦU XÁC THỰC EMAIL **************************************************
*******************************************************************************************************************************************
"""
@api {post} /users/<user_id>/otp/email Khách hàng yêu cầu xác thực Email
@apiDescription Khách hàng yêu cầu xác thực Email
@apiVersion 1.0.0
@apiGroup OTP
@apiName PostOTPEmail

@apiParam (Resource:) {string} user_id UUID khách hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {string} email Địa chỉ email cần xác thực
@apiParamExample {json} Body example
{
  "email": "<EMAIL>"
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request success"
}
"""


**********************************************************************************************************************************************
************************************************** API KHÁCH HÀNG XÁC THỰC MÃ OTP CỦA EMAIL **************************************************
**********************************************************************************************************************************************
"""
@api {get} /users/<user_id>/otp/email/<authent_code> Khách hàng xác thực mã OTP của email
@apiDescription Chỉ dùng hàm GET vì khách hàng click link này trong email
@apiVersion 1.0.0
@apiGroup OTP
@apiName GetOPTEmailCode

@apiParam (Resource:) {string} user_id UUID khách hàng
@apiParam (Resource:) {string} authent_code Mã xác thực OTP

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request success"
}
"""
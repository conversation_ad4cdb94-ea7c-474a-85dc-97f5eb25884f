**********************************************************************************************************************************
************************************************** API KHÁCH HÀNG ĐẾN THĂM SHOP **************************************************
**********************************************************************************************************************************
"""
@api {post} /users/<user_id>/shops/<shop_id>/visited Khách hàng đến thăm Shop
@apiDescription Khách hàng đến thăm Shop
@apiVersion 1.0.0
@apiGroup Shop
@apiName PostShopVisited

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Resource:) {string} user_id UUID khách hàng
@apiParam (Resource:) {string} shop_id UUID cửa hàng

@apiParam  {string} authent_code Mã xác nhận đã đến cửa hàng
@apiParam  {string} visited_time Thời điểm thăm cửa hàng, nêu ko truyền lên, hệ thống tự lấy time_now()
@apiParamExample {json} Body Example
{
  "authent_code": "55837GB",
  "visited_time": "20171219025747"
}

@apiSuccess  {string} id UUID
@apiSuccess  {string} authent_code Mã xác thực
@apiSuccess  {string} visited_time Thời điểm ghé thăm. Định dạng: <code>yyyyMMddHHmmss</code>
@apiSuccess  {number=1:foot-traffic 2:non-foot-traffic} type Kiểu ghé thăm
@apiSuccess  {number=1:member 2:non-member} member_status Trạng thái là thành viên của nhãn hàng
@apiSuccess  {string} shop_id UUID cửa hàng ghé thăm
@apiSuccess  {string} user_id UUID khách hàng
@apiSuccess  {string} created_time Thời điểm tạo
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "e820648a-340d-49bc-82fc-545941bb0dej",
  "authent_code": "55837GB",
  "visited_time": "20171219025747",
  "type": 1,
  "member_status": 1,
  "shop_id": "e820648a-340d-49bc-82fc-545941bb0deb",
  "user_id": "9c619bd6-c585-402d-8877-6fc80e4f5ce9",
  "created_time": "20170926023734"
}

"""
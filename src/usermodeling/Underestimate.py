#!/usr/bin/python
# -*- coding: utf8 -*-

*******************************************************************************************************************************************
************************************************** API ĐẶT NGƯỠNG VÀ LÝ DO ĐÁNH GIÁ THẤP **************************************************
*******************************************************************************************************************************************
"""
@api {post} /merchants/<merchant_id>/underestimate/config/reason Đặt ngưỡng và lý do đánh giá thấp
@apiDescription Đặt ngưỡng và lý do đánh giá thấp
@apiVersion 1.0.0
@apiGroup Underestimate
@apiName PostUnderestimateReason

@apiParam (Resource:) {string} merchant_id UUID nhãn hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {number} threshold=2 Ngưỡng đánh giá thấp
@apiParam  {string[]} reasons Danh sách nguyên nhân
@apiParamExample {json} Body
{
  "threshold": 2,
  "reasons": [
    "Nhân viên hỗ trợ không nhiệt tình",
    "Chất lượng sản phẩm không như mong đợi",
    "Giá cả không hợp lý với chất lượng sản phẩm"
  ]
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "Config underestimate's threshold and reason successful"
}
"""


*******************************************************************************************************************************************
************************************************** API LẤY NGƯỠNG VÀ LÝ DO ĐÁNH GIÁ THẤP **************************************************
*******************************************************************************************************************************************
"""
@api {get} /merchants/<merchant_id>/underestimate/config/reason Lấy ngưỡng và lý do đánh giá thấp
@apiDescription Lấy ngưỡng và lý do đánh giá thấp
@apiVersion 1.0.0
@apiGroup Underestimate
@apiName GetUnderestimateReason

@apiParam (Resource:) {string} merchant_id UUID nhãn hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {number} threshold=2 Ngưỡng đánh giá thấp
@apiSuccess  {string[]} reasons Danh sách nguyên nhân
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "threshold": 2,
  "reasons": [
    "Nhân viên hỗ trợ không nhiệt tình",
    "Chất lượng sản phẩm không như mong đợi",
    "Giá cả không hợp lý với chất lượng sản phẩm"
  ]
}
"""


**********************************************************************************************************************************************
************************************************** API ĐẶT CẤU HÌNH THÔNG BÁO ĐÁNH GIÁ THẤP **************************************************
**********************************************************************************************************************************************
"""
@api {post} /merchants/<merchant_id>/underestimate/config/notify Đặt cấu hình thông báo đánh giá thấp
@apiDescription Đặt cấu hình thông báo đánh giá thấp
@apiVersion 1.0.0
@apiGroup Underestimate
@apiName PostUnderestimateNotify

@apiParam (Resource:) {string} merchant_id UUID nhãn hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (emails) {string} values Danh sách email nhận notify khi có đánh giá thấp
@apiParam (emails) {number=1:Khi-có-đánh-giá 2:Cuối-ngày} type Thời điểm nhận thông báo

@apiParam (sms) {string} values Danh sách số điện thoại nhận notify qua sms
@apiParam (sms) {string} time Khoảng thời gian nhận notify qua sms

@apiParam (push-notification) {string} values Danh sách nhân viên nhận notify qua ứng dụng
@apiParam (push-notification) {string} time Khoảng thời gian nhận notify qua ứng dụng

@apiParamExample {json} Body
{
  "emails": {
    "values": "email1;email2",
    "type": 1
  },
  "sms": {
    "values": "0904123456;0984123456",
    "time": "08:00;18:00"
  },
  "push-notification": {
    "values": "staff1@shop;staff2@shop",
    "time": "08:00;18:00"
  }
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Config underestimate's notify successful"
}
"""


**********************************************************************************************************************************************
************************************************** API LẤY CẤU HÌNH THÔNG BÁO ĐÁNH GIÁ THẤP **************************************************
**********************************************************************************************************************************************
"""
@api {get} /merchants/<merchant_id>/underestimate/config/notify Lấy cấu hình thông báo đánh giá thấp
@apiDescription Lấy cấu hình thông báo đánh giá thấp
@apiVersion 1.0.0
@apiGroup Underestimate
@apiName GetUnderestimateNotify

@apiParam (Resource:) {string} merchant_id UUID nhãn hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess (emails) {string} values Danh sách email nhận notify khi có đánh giá thấp
@apiSuccess (emails) {number=1:Khi-có-đánh-giá 2:Cuối-ngày} type Thời điểm nhận thông báo

@apiSuccess (sms) {string} values Danh sách số điện thoại nhận notify qua sms
@apiSuccess (sms) {string} time Khoảng thời gian nhận notify qua sms

@apiSuccess (push-notification) {string} values Danh sách nhân viên nhận notify qua ứng dụng
@apiSuccess (push-notification) {string} time Khoảng thời gian nhận notify qua ứng dụng
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{  
  "emails": {
    "values": "email1;email2",
    "type": 1 //1. Mỗi khi khách hàng đánh giá thấp; 2. Cuối ngày
  },
  "sms": {
    "values": "0904123456;0984123456",
    "time": "08:00;18:00"
  },
  "push-notification": {
    "values": "staff1@shop;staff2@shop",
    "time": "08:00;18:00"
  }
}
"""


***********************************************************************************************************************************************
************************************************** API ĐẶT TẶNG ĐIỂM KHI KHÁCH HÀNG ĐÁNH GIÁ **************************************************
***********************************************************************************************************************************************
"""
@api {post} /merchants/<merchant_id>/underestimate/config/reward Đặt tặng điểm khi khách hàng đánh giá
@apiDescription Đặt tặng điểm khi khách hàng đánh giá
@apiVersion 1.0.0
@apiGroup Underestimate
@apiName PostUnderestimateReward

@apiParam (Resource:) {string} merchant_id UUID nhãn hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {number} reward Điểm tặng được cấu hình
@apiParamExample {json} Body
{
    "reward": 1
}

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
    "message": "Config reward points successful"
}
"""


********************************************************************************************************************************************************
************************************************** API LẤY CẤU HÌNH TẶNG ĐIỂM KHI KHÁCH HÀNG ĐÁNH GIÁ **************************************************
********************************************************************************************************************************************************
"""
@api {get} /merchants/<merchant_id>/underestimate/config/reward Lấy cấu hình tặng điểm khi khách hàng đánh giá
@apiDescription Lấy cấu hình tặng điểm khi khách hàng đánh giá
@apiVersion 1.0.0
@apiGroup Underestimate
@apiName GetUnderestimateReward

@apiParam (Resource:) {string} merchant_id UUID nhãn hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {number} reward Điểm tặng được cấu hình
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "reward": 1
}
"""


***********************************************************************************************************************
************************************************** API ĐÁNH GIÁ ITEM **************************************************
***********************************************************************************************************************
"""
@api {post} /merchants/<merchant_id>/rating Đánh giá item
@apiDescription Đánh giá item (app client gửi đánh giá, dùng authen Digest)
@apiVersion 1.0.0
@apiGroup Underestimate
@apiName PostRating

@apiParam (Resource:) {string} merchant_id UUID nhãn hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam  {string} object_id UUID item đánh giá
@apiParam  {number} object_type Loại item
@apiParam  {number} point Điểm đánh giá
@apiParam  {string} [trading_code] Mã giao dịch, do với cửa hàng đánh giá phát sinh sau khi giao dịch ở cửa hàng, trường này thể hiện đánh giá theo giao dịch phát sinh (trường này có thể null)
@apiParam  {string} [content] nội dung đánh giá(trường này có thể null)
@apiParam  {string} [low_rate_reason_id] UUID của lý do đánh giá thấp.
@apiParam  {string} [shop_id] UUID cửa hàng đã phục vụ khách hàng.
@apiParamExample {json} Body
{
  "object_id ": "e820648a-340d-49bc-82fc-545941bb0dej",
  "object_type": 7,
  "point": 1
}

@apiSuccess  {string} object_id UUID item đánh giá
@apiSuccess  {number} object_type Loại item
@apiSuccess  {string} [trading_code] Mã giao dịch, do với cửa hàng đánh giá phát sinh sau khi giao dịch ở cửa hàng, trường này thể hiện đánh giá theo giao dịch phát sinh (trường này có thể null)
@apiSuccess  {string} [content] nội dung đánh giá(trường này có thể null)
@apiSuccess  {string} [low_rate_reason_id] UUID của lý do đánh giá thấp.
@apiSuccess  {string} [shop_id] UUID cửa hàng đã phục vụ khách hàng.
@apiUse created_time
@apiUse updated_time
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "object_id ": "e820648a-340d-49bc-82fc-545941bb0dej",
  "object_type": 7,
  "point": 1,
  "trading_code": null,
  "content": null,
  "low_rate_reason_id": null,
  "user_id" : "e2352346a-340d-49bc-82fc-545941bb0dej",
  "created_time": "2017-08-07T04:02:28.002Z",
  "updated_time": "2017-08-07T04:02:28.002Z",
}
"""
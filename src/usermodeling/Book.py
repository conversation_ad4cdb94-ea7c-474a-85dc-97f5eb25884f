*********************************************************************************************************************************
************************************************** API KHÁCH HÀNG BOOK DỊCH VỤ **************************************************
*********************************************************************************************************************************
"""
@api {post} /users/<user_id>/booking Khách hàng book dịch vụ
@apiDescription Khách hàng book dịch vụ
@apiVersion 1.0.0
@apiGroup Book
@apiName GetUserBook

@apiParam (Resource:) {string} user_id UUID khách hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:) {string} merchant_id UUID nhà cung cấp
@apiParam (Body:) {string} appointment_time Thời điểm đến dùng dịch vụ
@apiParam (Body:) {string} note Ghi chú thông tin book
@apiParam (Body:) {string} fullname Tên của người cần liên hệ
@apiParam (Body:) {string} phone_number Số điện thoại liên hệ
@apiParam (Body:) {string} shop_id UUID cửa hàng muốn đặt chỗ
@apiParam (Body:) {string} [product_id] UUID sản phẩm
@apiParam (Body:) {number} [person_number] Số người. Tùy vào loại chức năng book sẽ xác định yêu cầu nhập hay không.
@apiParam (Body:) {number} [day_number] Số ngày dùng dịch vụ. Tùy vào loại chức năng book sẽ xác định yêu cầu nhập hay không.
@apiParam (Body:) {number} [room_number] Số lượng phòng cần thuê. Tùy vào loại chức năng book sẽ xác định yêu cầu nhập hay không.
@apiParamExample {json} Response: HTTP/1.1 200 OK
{
  "merchant_id": "3f99bb25-4fee-4f19-986b-60c788b9b42j",
  "appointment_time": "20170926023734",
  "note": "ghi chú về thông tin book",
  "fullname": "Lý Tuấn Anh",
  "phone_number": "0988830843",
  "product_id": "3f94btb25-4fee-4f19-986b-60c788b9avg5",
  "person_number": 5
}

@apiSuccess  {string} id UUID đơn đặt hàng
@apiSuccess  {string} appointment_time Thời điểm đến dùng dịch vụ
@apiSuccess  {string} note Ghi chú thông tin book
@apiSuccess  {string} fullname Tên của người cần liên hệ
@apiSuccess  {string} phone_number Số điện thoại liên hệ
@apiSuccess  {string} [product_id] UUID sản phẩm
@apiSuccess  {number} person_number Số người. Tùy vào loại chức năng book sẽ xác định yêu cầu nhập hay không.
@apiSuccess  {number=1:DichVuSpa 2:DatPhongKaraoke 3:DatPhongKhachSan 4:DatTiecNhaHang} type Kiểu dịch vụ
@apiSuccess  {string} merchant_id UUID nhà cung cấp
@apiSuccess  {string} user_id UUID khách hàng
@apiSuccess  {number} attribute_set_id id danh sách thuộc tính thêm
@apiUse created_time
@apiUse updated_time
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "3f99bb25-4fee-4f19-986b-60c788b9b4241",
  "appointment_time": "20170926023734",
  "note": "ghi chú về thông tin book",
  "fullname": "Lý Tuấn Anh",
  "phone_number": "0988830843",
  "product_id": "3f94btb25-4fee-4f19-986b-60c788b9avg5",
  "person_number": 5,
  "type": 1,
  "merchant_id": "3f99bb25-4fee-4f19-986b-60c788b9b42j",
  "user_id": "3f99bb25-4fee-4f19-986b-60c72836792",
  "attribute_set_id": 8,
  "created_time": "20170926023734",
  "updated_time": "20170926023734"
}
"""


*********************************************************************************************************************************************
************************************************** API LẤY DÁNH SÁCH BOOKING CỦA NHÃN HÀNG **************************************************
*********************************************************************************************************************************************
"""
@api {get} /merchants/<merchant_id>/bookings Lấy dánh sách booking của nhãn hàng
@apiDescription Lấy dánh sách booking của merchant
@apiVersion 1.0.0
@apiGroup Booking
@apiName GetBookings

@apiParam (Resource:) {string} merchant_id UUID nhãn hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort

@apiParam (Query:) {string} [order_since] Filter thời điểm bắt đầu đến dùng dịch vụ (appointment_time)
@apiParam (Query:) {string} [order_until] Filter thời điểm kết thúc theo thời điểm đến dùng dịch vụ (appointment_time)
@apiParam (Query:) {string} [reservation_since] Filter thời điểm bắt đầu theo thời điểm tạo (created_time)
@apiParam (Query:) {string} [reservation_until] Filter thời điểm kết thúc theo thời điểm tạo (created_time)
@apiParam (Query:) {string} [search] Chuỗi tìm kiếm
@apiParam (Query:) {string} [status] Filter trạng thái. Ví dụ: <code>&status=1,2,3</code>

@apiSuccess (data) {string} id UUID đơn đặt hàng
@apiSuccess (data) {string} appointment_time Thời điểm đến dùng dịch vụ
@apiSuccess (data) {string} note Ghi chú thông tin book
@apiSuccess (data) {string} fullname Tên của người cần liên hệ
@apiSuccess (data) {string} phone_number Số điện thoại liên hệ
@apiSuccess (data) {string} shop_id UUID của hàng nhận được đặt chỗ
@apiSuccess (data) {string} shop_name Tên cửa hàng nhận được đặt chỗ
@apiSuccess (data) {string} [product_id] UUID sản phẩm
@apiSuccess (data) {number} person_number Số người. Tùy vào loại chức năng book sẽ xác định yêu cầu nhập hay không.
@apiSuccess (data) {number=1:DichVuSpa 2:DatPhongKaraoke 3:DatPhongKhachSan 4:DatTiecNhaHang} type Kiểu dịch vụ
@apiSuccess (data) {string} merchant_id UUID nhà cung cấp
@apiSuccess (data) {string} user_id UUID khách hàng
@apiSuccess (data) {number} attribute_set_id id danh sách thuộc tính thêm
@apiUse created_time
@apiUse updated_time
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "3f99bb25-4fee-4f19-986b-60c788b9b4241",
      "appointment_time": "20170926023734",
      "note": "ghi chú về thông tin book",
      "reason": "đồng ý",
      "fullname": "Lý Tuấn Anh",
      "phone_number": "0988830843",
      "shop_id": "b3c1e8d3-c4ee-455a-8e12-014048939a4d",
      "shop_name": "CS1 - Lê Đại Hành - Hà Nội",
      "product_id": "3f94btb25-4fee-4f19-986b-60c788b9avg5",
      "person_number": 5,
      "type": 1,
      "status": 1,
      "merchant_id": "3f99bb25-4fee-4f19-986b-60c788b9b42j",
      "user_id": "3f99bb25-4fee-4f19-986b-60c72836792",
      "user_name": "ngoan",
      "user_avatar": "https://...",
      "attribute_set_id": 8,
      "created_time": "20170926023734",
      "updated_time": "20170926023734"
    },
    {
      "id": "3f99bb25-4fee-4f19-986b-60c788b9b4241",
      "appointment_time": "20170926023734",
      "note": "ghi chú về thông tin book",
      "reason": "",
      "fullname": "Lý Tuấn Anh",
      "phone_number": "0988830843",
      "shop_id": "b3c1e8d3-c4ee-455a-8e12-014048939a4d",
      "shop_name": "CS1 - Lê Đại Hành - Hà Nội",
      "product_id": "3f94btb25-4fee-4f19-986b-60c788b9avg5",
      "person_number": 5,
      "type": 1,
      "status": 0,
      "merchant_id": "3f99bb25-4fee-4f19-986b-60c788b9b42j",
      "user_id": "3f99bb25-4fee-4f19-986b-60c72836792",
      "user_name": "ngoan",
      "user_avatar": "https://...",
      "attribute_set_id": 8,
      "created_time": "20170926023734",
      "updated_time": "20170926023734"
    }
  ],
  "paging": {
    ...
  }
}
"""


******************************************************************************************************************************************
************************************************** API CHẤP NHẬN BOOKING CỦA KHÁCH HÀNG **************************************************
******************************************************************************************************************************************
"""
@api {post} /merchants/<merchant_id>/bookings/<booking_id>/actions/accept Chấp nhận Booking của khách hàng
@apiDescription Chấp nhận Booking của khách hàng
@apiVersion 1.0.0
@apiGroup Booking
@apiName PostAcceptBooking

@apiParam (Resource:) {string} merchant_id UUID nhãn hàng
@apiParam (Resource:) {string} booking_id UUID đơn đặt hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {string} id UUID đơn đặt hàng
@apiSuccess  {string} appointment_time Thời điểm đến dùng dịch vụ
@apiSuccess  {string} note Ghi chú thông tin book
@apiSuccess  {string} fullname Tên của người cần liên hệ
@apiSuccess  {string} phone_number Số điện thoại liên hệ
@apiSuccess  {string} shop_id UUID của hàng nhận được đặt chỗ
@apiSuccess  {string} shop_name Tên cửa hàng nhận được đặt chỗ
@apiSuccess  {string} [product_id] UUID sản phẩm
@apiSuccess  {number} person_number Số người. Tùy vào loại chức năng book sẽ xác định yêu cầu nhập hay không.
@apiSuccess  {number=1:DichVuSpa 2:DatPhongKaraoke 3:DatPhongKhachSan 4:DatTiecNhaHang} type Kiểu dịch vụ
@apiSuccess  {string} merchant_id UUID nhà cung cấp
@apiSuccess  {string} user_id UUID khách hàng
@apiSuccess  {number} attribute_set_id id danh sách thuộc tính thêm
@apiUse created_time
@apiUse updated_time
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "3f99bb25-4fee-4f19-986b-60c788b9b4241",
  "appointment_time": "20170926023734",
  "note": "ghi chú về thông tin book",
  "reason": "đồng ý",
  "fullname": "Lý Tuấn Anh",
  "phone_number": "0988830843",
  "shop_id": "b3c1e8d3-c4ee-455a-8e12-014048939a4d",
  "shop_name": "CS1 - Lê Đại Hành - Hà Nội",
  "product_id": "3f94btb25-4fee-4f19-986b-60c788b9avg5",
  "person_number": 5,
  "type": 1,
  "status": 1,
  "merchant_id": "3f99bb25-4fee-4f19-986b-60c788b9b42j",
  "user_id": "3f99bb25-4fee-4f19-986b-60c72836792",
  "user_name": "ngoan",
  "user_avatar": "https://...",
  "attribute_set_id": 8,
  "created_time": "20170926023734",
  "updated_time": "20170926023734"
}
"""


************************************************************************************************************************************************
************************************************** API KHÔNG CHẤP NHẬN BOOKING CỦA KHÁCH HÀNG **************************************************
************************************************************************************************************************************************
"""
@api {post} /merchants/<merchant_id>/bookings/<booking_id>/actions/denied Không chấp nhận booking của khách hàng
@apiDescription Không chấp nhận Booking của khách hàng
@apiVersion 1.0.0
@apiGroup Booking
@apiName PostDeniedBooking

@apiParam (Resource:) {string} merchant_id UUID nhãn hàng
@apiParam (Resource:) {string} booking_id UUID đơn đặt hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiSuccess  {string} id UUID đơn đặt hàng
@apiSuccess  {string} appointment_time Thời điểm đến dùng dịch vụ
@apiSuccess  {string} note Ghi chú thông tin book
@apiSuccess  {string} fullname Tên của người cần liên hệ
@apiSuccess  {string} phone_number Số điện thoại liên hệ
@apiSuccess  {string} shop_id UUID của hàng nhận được đặt chỗ
@apiSuccess  {string} shop_name Tên cửa hàng nhận được đặt chỗ
@apiSuccess  {string} [product_id] UUID sản phẩm
@apiSuccess  {number} person_number Số người. Tùy vào loại chức năng book sẽ xác định yêu cầu nhập hay không.
@apiSuccess  {number=1:DichVuSpa 2:DatPhongKaraoke 3:DatPhongKhachSan 4:DatTiecNhaHang} type Kiểu dịch vụ
@apiSuccess  {string} merchant_id UUID nhà cung cấp
@apiSuccess  {string} user_id UUID khách hàng
@apiSuccess  {number} attribute_set_id id danh sách thuộc tính thêm
@apiUse created_time
@apiUse updated_time
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "3f99bb25-4fee-4f19-986b-60c788b9b4241",
  "appointment_time": "20170926023734",
  "note": "ghi chú về thông tin book",
  "reason": "đồng ý",
  "fullname": "Lý Tuấn Anh",
  "phone_number": "0988830843",
  "shop_id": "b3c1e8d3-c4ee-455a-8e12-014048939a4d",
  "shop_name": "CS1 - Lê Đại Hành - Hà Nội",
  "product_id": "3f94btb25-4fee-4f19-986b-60c788b9avg5",
  "person_number": 5,
  "type": 1,
  "status": 1,
  "merchant_id": "3f99bb25-4fee-4f19-986b-60c788b9b42j",
  "user_id": "3f99bb25-4fee-4f19-986b-60c72836792",
  "user_name": "ngoan",
  "user_avatar": "https://...",
  "attribute_set_id": 8,
  "created_time": "20170926023734",
  "updated_time": "20170926023734"
}
"""


**************************************************************************************************************************************************
************************************************** API LẤY DANH SÁCH BOOKING CỦA MỘT KHÁCH HÀNG **************************************************
**************************************************************************************************************************************************
"""
@api {get} /users/<user_id>/bookings Lấy danh sách booking của một User
@apiDescription Lấy danh sách booking của một User
@apiVersion 1.0.0
@apiGroup Booking
@apiName GetUserBookings

@apiParam (Resource:) {string} user_id UUID khách hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging
@apiUse order_sort

@apiParam (Query:) {string} [order_since] Filter thời điểm bắt đầu đến dùng dịch vụ (appointment_time)
@apiParam (Query:) {string} [order_until] Filter thời điểm kết thúc theo thời điểm đến dùng dịch vụ (appointment_time)
@apiParam (Query:) {string} [reservation_since] Filter thời điểm bắt đầu theo thời điểm tạo (created_time)
@apiParam (Query:) {string} [reservation_until] Filter thời điểm kết thúc theo thời điểm tạo (created_time)
@apiParam (Query:) {string} [search] Chuỗi tìm kiếm
@apiParam (Query:) {string} [status] Filter trạng thái. Ví dụ: <code>&status=1,2,3</code>

@apiSuccess (data) {string} id UUID đơn đặt hàng
@apiSuccess (data) {string} appointment_time Thời điểm đến dùng dịch vụ
@apiSuccess (data) {string} note Ghi chú thông tin book
@apiSuccess (data) {string} fullname Tên của người cần liên hệ
@apiSuccess (data) {string} phone_number Số điện thoại liên hệ
@apiSuccess (data) {string} shop_id UUID của hàng nhận được đặt chỗ
@apiSuccess (data) {string} shop_name Tên cửa hàng nhận được đặt chỗ
@apiSuccess (data) {string} [product_id] UUID sản phẩm
@apiSuccess (data) {number} person_number Số người. Tùy vào loại chức năng book sẽ xác định yêu cầu nhập hay không.
@apiSuccess (data) {number=1:DichVuSpa 2:DatPhongKaraoke 3:DatPhongKhachSan 4:DatTiecNhaHang} type Kiểu dịch vụ
@apiSuccess (data) {string} merchant_id UUID nhà cung cấp
@apiSuccess (data) {string} user_id UUID khách hàng
@apiSuccess (data) {number} attribute_set_id id danh sách thuộc tính thêm
@apiUse created_time
@apiUse updated_time
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "id": "3f99bb25-4fee-4f19-986b-60c788b9b4241",
      "appointment_time": "20170926023734",
      "note": "ghi chú về thông tin book",
      "reason": "đồng ý",
      "fullname": "Lý Tuấn Anh",
      "phone_number": "0988830843",
      "shop_id": "b3c1e8d3-c4ee-455a-8e12-014048939a4d",
      "shop_name": "CS1 - Lê Đại Hành - Hà Nội",
      "product_id": "3f94btb25-4fee-4f19-986b-60c788b9avg5",
      "person_number": 5,
      "type": 1,
      "status": 1,
      "merchant_id": "3f99bb25-4fee-4f19-986b-60c788b9b42j",
      "user_id": "3f99bb25-4fee-4f19-986b-60c72836792",
      "user_name": "ngoan",
      "user_avatar": "https://...",
      "attribute_set_id": 8,
      "created_time": "20170926023734",
      "updated_time": "20170926023734"
    },
    {
      "id": "3f99bb25-4fee-4f19-986b-60c788b9b4241",
      "appointment_time": "20170926023734",
      "note": "ghi chú về thông tin book",
      "reason": "",
      "fullname": "Lý Tuấn Anh",
      "phone_number": "0988830843",
      "shop_id": "b3c1e8d3-c4ee-455a-8e12-014048939a4d",
      "shop_name": "CS1 - Lê Đại Hành - Hà Nội",
      "product_id": "3f94btb25-4fee-4f19-986b-60c788b9avg5",
      "person_number": 5,
      "type": 1,
      "status": 0,
      "merchant_id": "3f99bb25-4fee-4f19-986b-60c788b9b42j",
      "user_id": "3f99bb25-4fee-4f19-986b-60c72836792",
      "user_name": "ngoan",
      "user_avatar": "https://...",
      "attribute_set_id": 8,
      "created_time": "20170926023734",
      "updated_time": "20170926023734"
    }
  ],
  "paging": {
    ...
  }
}
"""


***************************************************************************************************************************************************
************************************************** API CẬP NHẬT THÔNG TIN BOOKING CỦA KHÁCH HÀNG **************************************************
***************************************************************************************************************************************************
"""
@api {patch} /merchants/<merchant_id>/bookings/<booking_id> Cập nhật thông tin Booking của khách hàng
@apiDescription Cập nhật thông tin Booking của khách hàng
@apiVersion 1.0.0
@apiGroup Booking
@apiName PatchMerchantBooking

@apiParam (Resource:) {string} merchant_id UUID nhãn hàng
@apiParam (Resource:) {string} booking_id UUID đơn đặt hàng

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam (Body:) {string} [appointment_time] Thời điểm đến dùng dịch vụ
@apiParam (Body:) {string} [note] Ghi chú thông tin book
@apiParam (Body:) {string} [fullname] Tên của người cần liên hệ
@apiParam (Body:) {string} [phone_number] Số điện thoại liên hệ
@apiParam (Body:) {string} [shop_id] UUID cửa hàng muốn đặt chỗ
@apiParam (Body:) {string} [product_id] UUID sản phẩm
@apiParam (Body:) {number} [person_number] Số người. Tùy vào loại chức năng book sẽ xác định yêu cầu nhập hay không.
@apiParam (Body:) {number} [day_number] Số ngày dùng dịch vụ. Tùy vào loại chức năng book sẽ xác định yêu cầu nhập hay không.
@apiParam (Body:) {number} [room_number] Số lượng phòng cần thuê. Tùy vào loại chức năng book sẽ xác định yêu cầu nhập hay không.

@apiSuccess  {string} id UUID đơn đặt hàng
@apiSuccess  {string} appointment_time Thời điểm đến dùng dịch vụ
@apiSuccess  {string} note Ghi chú thông tin book
@apiSuccess  {string} fullname Tên của người cần liên hệ
@apiSuccess  {string} phone_number Số điện thoại liên hệ
@apiSuccess  {string} shop_id UUID của hàng nhận được đặt chỗ
@apiSuccess  {string} shop_name Tên cửa hàng nhận được đặt chỗ
@apiSuccess  {string} [product_id] UUID sản phẩm
@apiSuccess  {number} person_number Số người. Tùy vào loại chức năng book sẽ xác định yêu cầu nhập hay không.
@apiSuccess  {number=1:DichVuSpa 2:DatPhongKaraoke 3:DatPhongKhachSan 4:DatTiecNhaHang} type Kiểu dịch vụ
@apiSuccess  {string} merchant_id UUID nhà cung cấp
@apiSuccess  {string} user_id UUID khách hàng
@apiSuccess  {number} attribute_set_id id danh sách thuộc tính thêm
@apiUse created_time
@apiUse updated_time
@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
  "id": "3f99bb25-4fee-4f19-986b-60c788b9b4241",
  "appointment_time": "20170926023734",
  "note": "ghi chú về thông tin book",
  "reason": "đồng ý",
  "fullname": "Lý Tuấn Anh",
  "phone_number": "0988830843",
  "shop_id": "b3c1e8d3-c4ee-455a-8e12-014048939a4d",
  "shop_name": "CS1 - Lê Đại Hành - Hà Nội",
  "product_id": "3f94btb25-4fee-4f19-986b-60c788b9avg5",
  "person_number": 5,
  "type": 1,
  "status": 1,
  "merchant_id": "3f99bb25-4fee-4f19-986b-60c788b9b42j",
  "user_id": "3f99bb25-4fee-4f19-986b-60c72836792",
  "user_name": "ngoan",
  "user_avatar": "https://...",
  "attribute_set_id": 8,
  "created_time": "20170926023734",
  "updated_time": "20170926023734"
}
"""
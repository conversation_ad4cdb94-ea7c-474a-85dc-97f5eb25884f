#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 01/06/2024
"""
"""
@apiDefine DataFlowReportPagingTokens
@apiVersion 1.0.0
@apiParam   (QUERY:)    {Number}    [per_page] Số phần tử trên một page.<br/> Example: <code>&per_page=5</code>
@apiParam   (QUERY:)    {String}    [after_token] Token để request lấy dữ liệu trang tiếp theo.
@apiParam   (QUERY:)    {String}    [before_token] Token để request lấy dữ liệu trang trước đó. Nếu trong danh sách tham số gửi lên có <code>after_token</code> thì ưu tiên xử lý <code>after_token</code>

@apiSuccess     {Paging}    [paging]    Thông tin phân trang.
<li><code>page:</code>Vị trí page request</li>
<li><code>per_page:</code>Số lượng phần tử trên một page</li>
@apiSuccess     {Object}    [paging..cursors]    Danh sách token để lấy dữ liệu trang tiếp theo hoặc trang trước đó.
@apiSuccess     {String}    [paging..cursors..after]    Token để lấy dữ liệu trang tiếp theo.
@apiSuccess     {String}    [paging..cursors..before]    Token để lấy dữ liệu trang trước đó.
@apiSuccess     {Object}    [paging..per_page]          Số lượng phần tử trên 1 page
@apiSuccessExample {json} Paging example
{
    ...
    "paging": {
        "cursors": {
            "after": "YXNkaGZha2RoZmFrZGZh",
            "before": "YXNkYTY3NXNhczdkZjVhNzZkczQ="
        },
        'per_page': 15,
    }
}
"""
"""
@apiDefine SnapshotFilter
@apiParam	(QUERY:)			{string}	    [start_time]                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z) 
                                                                            <code>Trong trường hợp tính đến thời điểm hiện tại thì không cần truyền start_time</code>
@apiParam	(QUERY:)			{string}	    end_time                    Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiParam   (QUERY:)            {string=wait_process,processing,done}        [status_sync]               Trạng thái đồng bộ. <code>Nếu không truyền gì thì mặc định lấy tất cả.</code>
                                                                            <ul>
                                                                                <li><code>wait_process</code> :: Chờ xử lý</li>
                                                                                <li><code>processing</code> :: Đang xử lý</li>
                                                                                <li><code>done</code> :: Hoàn thành</li>
                                                                                <li><code>fail</code> :: Xử lý lỗi</li>
                                                                            </ul>
                                                                            <code>Trong trường hợp lấy nhiều thì truyền các giá trị lên ngăn cách nhau bởi dấu ,</code>

"""
"""
@apiDefine DestinationsMicrosoftExcelFilter
@apiParam	(Body:)			{Array}	    [status_process]                Trạng thái xử lý
                                                                        <ul>
                                                                            <li><code>wait_process</code> :: Chờ xử lý</li>
                                                                            <li><code>processing</code> :: Đang xử lý</li>
                                                                            <li><code>done</code> :: Hoàn thành</li>
                                                                            <li><code>stop_process</code> :: Dừng xử lý</li>
                                                                        </ul>
@apiParam	(Body:)			{Object}	[condition_request_time]        Thời gian ghi nhận yêu cầu
@apiParam	(Body:)			{String}	[condition_request_time.start_time]         Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiParam	(Body:)			{String}	[condition_request_time.end_time]           Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)

@apiParam	(Body:)			{Array}	    [account_request_ids]           Danh sách <code>ID</code> người yêu cầu.
@apiParam	(Body:)			{Array}	    [area_codes]                    Danh sách <code>area_code</code>.

"""

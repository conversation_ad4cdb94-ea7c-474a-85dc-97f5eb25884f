#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 14/01/2025
"""

# ---------- <PERSON><PERSON> liệu tổng của báo cáo tổng quan của connector Microsoft Excel -----------
"""
@api {POST} {domain}/market-place/api/v1.0/reports/connectors/destinations/microsoft_excel/overview/totals               Số liệu tổng của báo cáo tổng quan của connector Microsoft Excel
@apiGroup DestinationsMicrosoftExcel
@apiVersion 1.0.0
@apiName TotalOverview

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse DestinationsMicrosoftExcelFilter

@apiSuccess   {Array[Object]}  data                                                     Dữ liệu báo cáo
@apiSuccess   {int}     data.number                                                     Số lượng yêu cầu
@apiSuccess   {string}  data.status_process                                             Trạng thái xử lý tương <PERSON>ng
                                                                                        <ul>
                                                                                            <li><code>wait_process</code> :: Chờ xử lý</li>
                                                                                            <li><code>processing</code> :: Đang xử lý</li>
                                                                                            <li><code>done</code> :: Hoàn thành</li>
                                                                                            <li><code>stop_process</code> :: Dừng xử lý</li>
                                                                                        </ul>


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "number": 1,
            "status_process": "wait_process"
        },
        {
            "number": 2,
            "status_process": "processing"
        },
        {
            "number": 3,
            "status_process": "done"
        },
        {
            "number": 4,
            "status_process": "stop_process"
        }
    ]
}
"""

# ---------- Số liệu yêu cầu xuất dữ liệu connector Microsoft Excel theo ngày -----------
"""
@api {POST} {domain}/market-place/api/v1.0/reports/connectors/destinations/microsoft_excel/overview/request-exports-by-day               Số liệu yêu cầu xuất dữ liệu connector Microsoft Excel theo ngày
@apiGroup DestinationsMicrosoftExcel
@apiVersion 1.0.0
@apiName RequestExportsByDay

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse DestinationsMicrosoftExcelFilter

@apiSuccess   {Array[Object]}  data                                                     Dữ liệu báo cáo
@apiSuccess   {int}         data.number                                                 Số lượng yêu cầu
@apiSuccess   {string}      data.date                                                   Ngày ghi nhận yêu cầu (Format: <code>%Y-%m-%d</code> Ví dụ: 2022-03-21)


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "number": 1,
            "date": "2024-03-21"
        },
        {
            "number": 2,
            "date": "2024-03-22"
        },
        {
            "number": 3,
            "date": "2024-03-23"
        },
        {
            "number": 4,
            "date": "2024-03-24"
        },
        {
            "number": 5,
            "date": "2022-03-25"
        }
    ]
}
"""

# ---------- Tần suất export dữ liệu theo tài khoản -----------
"""
@api {POST} {domain}/market-place/api/v1.0/reports/connectors/destinations/microsoft_excel/overview/frequency/export-by-account               Tần suất export dữ liệu theo tài khoản
@apiGroup DestinationsMicrosoftExcel
@apiVersion 1.0.0
@apiName RequestExportsByAccount

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(QUERY:)		{String}	[sort]   	          Tên field trường thông tin cần sắp xếp                                                                           
@apiParam	(QUERY:)		{String}	[order]   	          Kiểu sắp xếp:
                                                              <ul>
                                                                <li><code>asc: </code> sắp xếp tăng dần</li>
                                                                <li><code>desc: </code> sắp xếp giảm dần</li>
                                                              </ul>               
                                                              allow value: asc, desc 

@apiUse DestinationsMicrosoftExcelFilter
@apiUse DataFlowReportPagingTokens



@apiSuccess   {Array[Object]}  data                                                     Dữ liệu báo cáo
@apiSuccess   {int}         data.number                                                 Số lượng yêu cầu
@apiSuccess   {string}      data.account_id                                             Định danh tài khoản


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "number": 1,
            "account_id": "123456"
        },
        {
            "number": 2,
            "account_id": "789012"
        }
    ]
}
"""

# ---------- Tần suất export dữ liệu theo tài khoản -----------
"""
@api {POST} {domain}/market-place/api/v1.0/reports/connectors/destinations/microsoft_excel/details               Lấy danh sách yêu cầu export dữ liệu của connector Microsoft Excel
@apiGroup DestinationsMicrosoftExcel
@apiVersion 1.0.0
@apiName MicrosoftExcelRequestExportsList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(QUERY:)		{String}	[sort]   	          Tên field trường thông tin cần sắp xếp                                                                           
@apiParam	(QUERY:)		{String}	[order]   	          Kiểu sắp xếp:
                                                              <ul>
                                                                <li><code>asc: </code> sắp xếp tăng dần</li>
                                                                <li><code>desc: </code> sắp xếp giảm dần</li>
                                                              </ul>               
                                                              allow value: asc, desc 

@apiUse DestinationsMicrosoftExcelFilter
@apiUse DataFlowReportPagingTokens



@apiSuccess   {Array[Object]}   data                                                    Dữ liệu báo cáo
@apiSuccess   {string}          data.id                                                 Định danh yêu cầu
@apiSuccess   {string}          data.account_id                                         Định danh tài khoản
@apiSuccess   {string}          data.area_code                                          Khu vực xuất file
@apiSuccess   {object}          data.extract_data_area                                  Thông tin bổ sung của khu vực xuất dữ liệu
@apiSuccess   {string}          data.extract_data_area.object                           Đối tượng xuất dữ liệu
@apiSuccess   {string}          data.extract_data_area.object_id                        Định danh đối tượng xuất dữ liệu
@apiSuccess   {string}          data.extract_data_area.object_name                      Tên đối tượng xuất dữ liệu
@apiSuccess   {string}          data.request_time                                       Thời gian ghi nhận yêu cầu (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiSuccess   {string}          data.status_process                                     Trạng thái xử lý tương ứng
                                                                                        <ul>
                                                                                            <li><code>wait_process</code> :: Chờ xử lý</li>
                                                                                            <li><code>processing</code> :: Đang xử lý</li>
                                                                                            <li><code>done</code> :: Hoàn thành</li>
                                                                                            <li><code>stop_process</code> :: Dừng xử lý</li>
                                                                                        </ul>
@apiSuccess   {int}             data.estimate_number_row                                Số lượng dòng dữ liệu dự kiến
@apiSuccess   {string}          data.estimate_start_time                                Thời gian dự kiến bắt đầu (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiSuccess   {string}          data.estimate_completion_time                           Thời gian dự kiến hoàn thành (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiSuccess   {int}             data.actual_number_row                                  Số lượng dòng dữ liệu thực tế
@apiSuccess   {string}          data.actual_start_time                                  Thời gian bắt đầu thực tế (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiSuccess   {string}          data.actual_completion_time                             Thời gian hoàn thành thực tế (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiSuccess   {object}          data.result_file                                        Kết quả file xuất dữ liệu
@apiSuccess   {string}          data.result_file.url                                    URL file kết quả
@apiSuccess   {string}          data.result_file.filename                               Tên file kết quả
@apiSuccess   {string}          data.result_file.capacity                               Kích thước file kết quả
@apiSuccess   {string}          data.result_file.format                                 Kiểu file
@apiSuccess   {string}          data.result_file.expire_time                            Thời gian hết hạn file kết quả (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiSuccess   {string}          data.created_time                                       Thời gian tạo yêu cầu (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)                                                                                        
@apiSuccess   {string}          data.updated_time                                       Thời gian cập nhật trạng thái (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)                                                                                        
                                                                                        
                                                                                        


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "id": "req001",
            "account_id": "acc1001",
            "area_code": "NA",
            "extract_data_area": {
                "object": "customer",
                "object_id": "obj001",
                "object_name": "Customer Data"
            },
            "request_time": "2023-10-01T08:00:00Z",
            "status_process": "processing",
            "estimate_number_row": 1000,
            "estimate_start_time": "2023-10-01T08:05:00Z",
            "estimate_completion_time": "2023-10-01T08:15:00Z",
            "actual_number_row": 1200,
            "actual_start_time": "2023-10-01T08:05:00Z",
            "actual_completion_time": "2023-10-01T08:15:00Z",
            "result_file": {
                "url": "https://example.com/files/result001.csv",
                "filename": "result001.csv",
                "capacity": "2MB",
                "format": "csv",
                "expire_time": "2023-10-08T08:00:00Z"
            },
            "created_time": "2023-10-01T08:00:00Z",
            "updated_time": "2023-10-01T08:15:00Z"
        },
        {
            "id": "req002",
            "account_id": "acc1002",
            "area_code": "EU",
            "extract_data_area": {
                "object": "transaction",
                "object_id": "obj002",
                "object_name": "Transaction Records"
            },
            "request_time": "2023-10-01T09:00:00Z",
            "status_process": "done",
            "estimate_number_row": 5000,
            "estimate_start_time": "2023-10-01T09:05:00Z",
            "estimate_completion_time": "2023-10-01T09:30:00Z",
            "actual_number_row": 5200,
            "actual_start_time": "2023-10-01T09:05:00Z",
            "actual_completion_time": "2023-10-01T09:30:00Z",
            "result_file": {
                "url": "https://example.com/files/result002.csv",
                "filename": "result002.csv",
                "capacity": "5MB",
                "format": "csv",
                "expire_time": "2023-10-08T09:00:00Z"
            },
            "created_time": "2023-10-01T09:00:00Z",
            "updated_time": "2023-10-01T09:30:00Z"
        },
        {
            "id": "req003",
            "account_id": "acc1003",
            "area_code": "APAC",
            "extract_data_area": {
                "object": "product",
                "object_id": "obj003",
                "object_name": "Product Inventory"
            },
            "request_time": "2023-10-01T10:00:00Z",
            "status_process": "wait_process",
            "estimate_number_row": 2000,
            "estimate_start_time": "2023-10-01T10:10:00Z",
            "estimate_completion_time": "2023-10-01T10:30:00Z",
            "actual_number_row": null,
            "actual_start_time": null,
            "actual_completion_time": null,
            "result_file": null,
            "created_time": "2023-10-01T10:00:00Z",
            "updated_time": "2023-10-01T10:00:00Z"
        },
        {
            "id": "req004",
            "account_id": "acc1004",
            "area_code": "NA",
            "extract_data_area": {
                "object": "sales",
                "object_id": "obj004",
                "object_name": "Sales Data"
            },
            "request_time": "2023-10-01T11:00:00Z",
            "status_process": "processing",
            "estimate_number_row": 1500,
            "estimate_start_time": "2023-10-01T11:05:00Z",
            "estimate_completion_time": "2023-10-01T11:20:00Z",
            "actual_number_row": null,
            "actual_start_time": null,
            "actual_completion_time": null,
            "result_file": null,
            "created_time": "2023-10-01T11:00:00Z",
            "updated_time": "2023-10-01T11:00:00Z"
        },
        {
            "id": "req005",
            "account_id": "acc1005",
            "area_code": "EU",
            "extract_data_area": {
                "object": "customer",
                "object_id": "obj005",
                "object_name": "Customer Feedback"
            },
            "request_time": "2023-10-01T12:00:00Z",
            "status_process": "done",
            "estimate_number_row": 800,
            "estimate_start_time": "2023-10-01T12:05:00Z",
            "estimate_completion_time": "2023-10-01T12:15:00Z",
            "actual_number_row": 850,
            "actual_start_time": "2023-10-01T12:05:00Z",
            "actual_completion_time": "2023-10-01T12:15:00Z",
            "result_file": {
                "url": "https://example.com/files/result005.xlsx",
                "filename": "result005.xlsx",
                "capacity": "3MB",
                "format": "xlsx",
                "expire_time": "2023-10-08T12:00:00Z"
            },
            "created_time": "2023-10-01T12:00:00Z",
            "updated_time": "2023-10-01T12:15:00Z"
        },
        {
            "id": "req006",
            "account_id": "acc1006",
            "area_code": "APAC",
            "extract_data_area": {
                "object": "transaction",
                "object_id": "obj006",
                "object_name": "Transaction History"
            },
            "request_time": "2023-10-01T13:00:00Z",
            "status_process": "stop_process",
            "estimate_number_row": 3000,
            "estimate_start_time": "2023-10-01T13:05:00Z",
            "estimate_completion_time": "2023-10-01T13:30:00Z",
            "actual_number_row": 1500,
            "actual_start_time": "2023-10-01T13:05:00Z",
            "actual_completion_time": "2023-10-01T13:15:00Z",
            "result_file": {
                "url": "https://example.com/files/result006.json",
                "filename": "result006.json",
                "capacity": "4MB",
                "format": "json",
                "expire_time": "2023-10-08T13:00:00Z"
            },
            "created_time": "2023-10-01T13:00:00Z",
            "updated_time": "2023-10-01T13:15:00Z"
        },
        {
            "id": "req007",
            "account_id": "acc1007",
            "area_code": "NA",
            "extract_data_area": {
                "object": "product",
                "object_id": "obj007",
                "object_name": "Product Sales"
            },
            "request_time": "2023-10-01T14:00:00Z",
            "status_process": "wait_process",
            "estimate_number_row": 2500,
            "estimate_start_time": "2023-10-01T14:10:00Z",
            "estimate_completion_time": "2023-10-01T14:30:00Z",
            "actual_number_row": null,
            "actual_start_time": null,
            "actual_completion_time": null,
            "result_file": null,
            "created_time": "2023-10-01T14:00:00Z",
            "updated_time": "2023-10-01T14:00:00Z"
        },
        {
            "id": "req008",
            "account_id": "acc1008",
            "area_code": "EU",
            "extract_data_area": {
                "object": "sales",
                "object_id": "obj008",
                "object_name": "Sales Report"
            },
            "request_time": "2023-10-01T15:00:00Z",
            "status_process": "processing",
            "estimate_number_row": 1800,
            "estimate_start_time": "2023-10-01T15:05:00Z",
            "estimate_completion_time": "2023-10-01T15:20:00Z",
            "actual_number_row": null,
            "actual_start_time": null,
            "actual_completion_time": null,
            "result_file": null,
            "created_time": "2023-10-01T15:00:00Z",
            "updated_time": "2023-10-01T15:00:00Z"
        },
        {
            "id": "req009",
            "account_id": "acc1009",
            "area_code": "APAC",
            "extract_data_area": {
                "object": "customer",
                "object_id": "obj009",
                "object_name": "Customer Survey"
            },
            "request_time": "2023-10-01T16:00:00Z",
            "status_process": "done",
            "estimate_number_row": 1200,
            "estimate_start_time": "2023-10-01T16:05:00Z",
            "estimate_completion_time": "2023-10-01T16:20:00Z",
            "actual_number_row": 1250,
            "actual_start_time": "2023-10-01T16:05:00Z",
            "actual_completion_time": "2023-10-01T16:20:00Z",
            "result_file": {
                "url": "https://example.com/files/result009.csv",
                "filename": "result009.csv",
                "capacity": "2.5MB",
                "format": "csv",
                "expire_time": "2023-10-08T16:00:00Z"
            },
            "created_time": "2023-10-01T16:00:00Z",
            "updated_time": "2023-10-01T16:20:00Z"
        },
        {
            "id": "req010",
            "account_id": "acc1010",
            "area_code": "NA",
            "extract_data_area": {
                "object": "transaction",
                "object_id": "obj010",
                "object_name": "Transaction Details"
            },
            "request_time": "2023-10-01T17:00:00Z",
            "status_process": "stop_process",
            "estimate_number_row": 2000,
            "estimate_start_time": "2023-10-01T17:05:00Z",
            "estimate_completion_time": "2023-10-01T17:30:00Z",
            "actual_number_row": 1000,
            "actual_start_time": "2023-10-01T17:05:00Z",
            "actual_completion_time": "2023-10-01T17:15:00Z",
            "result_file": {
                "url": "https://example.com/files/result010.xlsx",
                "filename": "result010.xlsx",
                "capacity": "3.5MB",
                "format": "xlsx",
                "expire_time": "2023-10-08T17:00:00Z"
            },
            "created_time": "2023-10-01T17:00:00Z",
            "updated_time": "2023-10-01T17:15:00Z"
        }
    ]
}
"""
# ---------- Tần suất export dữ liệu theo tài khoản -----------
"""
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/destinations/microsoft_excel/details/<id>               Lấy chi tiết yêu cầu export dữ liệu của connector Microsoft Excel
@apiGroup DestinationsMicrosoftExcel
@apiVersion 1.0.0
@apiName MicrosoftExcelDetailRequestExports

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess   {Object}          data                                                    Dữ liệu báo cáo
@apiSuccess   {string}          data.id                                                 Định danh yêu cầu
@apiSuccess   {string}          data.account_id                                         Định danh tài khoản
@apiSuccess   {string}          data.area_code                                          Khu vực xuất file
@apiSuccess   {object}          data.extract_data_area                                  Thông tin bổ sung của khu vực xuất dữ liệu
@apiSuccess   {string}          data.extract_data_area.object                           Đối tượng xuất dữ liệu
@apiSuccess   {string}          data.extract_data_area.object_id                        Định danh đối tượng xuất dữ liệu
@apiSuccess   {string}          data.extract_data_area.object_name                      Tên đối tượng xuất dữ liệu
@apiSuccess   {string}          data.request_time                                       Thời gian ghi nhận yêu cầu (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiSuccess   {string}          data.status_process                                     Trạng thái xử lý tương ứng
                                                                                        <ul>
                                                                                            <li><code>wait_process</code> :: Chờ xử lý</li>
                                                                                            <li><code>processing</code> :: Đang xử lý</li>
                                                                                            <li><code>done</code> :: Hoàn thành</li>
                                                                                            <li><code>stop_process</code> :: Dừng xử lý</li>
                                                                                        </ul>
@apiSuccess   {int}             data.processing_speed                                   Tốc độ xử lý (dòng/giờ)
@apiSuccess   {int}             data.estimate_number_row                                Số lượng dòng dữ liệu dự kiến
@apiSuccess   {string}          data.estimate_start_time                                Thời gian dự kiến bắt đầu (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiSuccess   {string}          data.estimate_completion_time                           Thời gian dự kiến hoàn thành (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiSuccess   {int}             data.actual_number_row                                  Số lượng dòng dữ liệu thực tế
@apiSuccess   {string}          data.actual_start_time                                  Thời gian bắt đầu thực tế (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiSuccess   {string}          data.actual_completion_time                             Thời gian hoàn thành thực tế (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiSuccess   {object}          data.condition_filters                                  Điều kiện bộ lọc dữ liệu
@apiSuccess   {object}          data.search_filter                                      Keyword search lọc dữ liệu
@apiSuccess   {object}          data.result_file                                        Kết quả file xuất dữ liệu
@apiSuccess   {string}          data.result_file.url                                    URL file kết quả
@apiSuccess   {string}          data.result_file.filename                               Tên file kết quả
@apiSuccess   {string}          data.result_file.capacity                               Kích thước file kết quả
@apiSuccess   {string}          data.result_file.format                                 Kiểu file
@apiSuccess   {string}          data.result_file.expire_time                            Thời gian hết hạn file kết quả (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiSuccess   {string}          data.created_time                                       Thời gian tạo yêu cầu (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)                                                                                        
@apiSuccess   {string}          data.updated_time                                       Thời gian cập nhật trạng thái (Format: <code>%Y-%m-%dT%H:%M:%SZ</code> Ví dụ: 2022-03-21T23:50Z)                                                                                        
                                                                                        
                                                                                        


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "id": "req001",
        "account_id": "acc1001",
        "area_code": "NA",
        "extract_data_area": {
            "object": "customer",
            "object_id": "obj001",
            "object_name": "Customer Data"
        },
        "request_time": "2023-10-01T08:00:00Z",
        "status_process": "processing",
        "estimate_number_row": 1000,
        "processing_speed": 200,
        "estimate_start_time": "2023-10-01T08:05:00Z",
        "estimate_completion_time": "2023-10-01T08:15:00Z",
        "actual_number_row": 1200,
        "actual_start_time": "2023-10-01T08:05:00Z",
        "actual_completion_time": "2023-10-01T08:15:00Z",
        "result_file": {
            "url": "https://example.com/files/result001.csv",
            "filename": "result001.csv",
            "capacity": "2MB",
            "format": "csv",
            "expire_time": "2023-10-08T08:00:00Z"
        },
        "condition_filters": {
            "filter1": {
                "field": "customer_name",
                "operator": "eq",
                "value": "John Doe"
            },
        },
        "search_filter": "cv",
        "created_time": "2023-10-01T08:00:00Z",
        "updated_time": "2023-10-01T08:15:00Z"
    }
}
"""


# ---------- Khu vực xuất dữ liệu -----------
"""
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/destinations/area-codes        Lấy danh sách khu vực xuất dữ liệu 
@apiGroup DestinationsConst
@apiVersion 1.0.0
@apiName GetListAreaCodes

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiSuccess   {string}      module_name     Tên module xuất file
@apiSuccess   {string}      area_code       Mã khu vực xuất file
@apiSuccess   {string}      area_name       Tên khu vực xuất file
                                                                                        

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "area_code": "PROFILE_LIST",
            "area_name": "Danh sách Profile",
            "module_name": "PROFILING"
        },
        {
            "area_code": "TICKET_LIST",
            "area_name": "Danh sách Ticket",
            "module_name": "TICKET"
        },
        {
            "area_code": "TASK_LIST",
            "area_name": "Danh sách Công việc",
            "module_name": "TASK"
        },
        {
            "area_code": "SURVEY_REPORT_DETAIL",
            "area_name": "Báo cáo chi tiết của Survey",
            "module_name": "SURVEY"
        },
        {
            "area_code": "DEAL_LIST",
            "area_name": "Danh sách Cơ hội bán",
            "module_name": "SALE"
        },
        {
            "area_code": "DEAL_BANK",
            "area_name": "Báo cáo bán hàng",
            "module_name": "SALE"
        },
        {
            "area_code": "SALE_MEMO_REPORT",
            "area_name": "Báo cáo Sale Memo",
            "module_name": "SALE"
        },
        {
            "area_code": "KPI_MANAGEMENT_REPORT",
            "area_name": "Báo cáo KPI",
            "module_name": "KPI_MANAGEMENT"
        }
    ]
}
"""

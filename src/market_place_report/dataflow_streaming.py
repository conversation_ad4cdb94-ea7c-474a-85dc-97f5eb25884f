#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 22/05/2024
"""

"""
@apiDefine StreamingFilter
@apiParam	(QUERY:)			{string}	    [start_time]                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z) 
                                                                            <code>Trong trường hợp tính đến thời điểm hiện tại thì không cần truyền start_time</code>
@apiParam	(QUERY:)			{string}	    end_time                    Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiParam   (QUERY:)            {string=wait_process,processing,done}        [status_sync]               Trạng thái đồng bộ. <code>Nếu không truyền gì thì mặc định lấy tất cả.</code>
                                                                            <ul>
                                                                                <li><code>wait_process</code> :: Ch<PERSON> xử lý</li>
                                                                                <li><code>processing</code> :: Đang xử lý</li>
                                                                                <li><code>done</code> :: Hoàn thành</li>
                                                                            </ul>
                                                                            <code>Trong trường hợp lấy nhiều thì truyền các giá trị lên ngăn cách nhau bởi dấu ,</code>

"""

# ---------- Báo cáo kết quả theo thời gian thực -----------
"""
@apiGroup DataFlow-Streaming
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/streaming/real-time/result               Báo cáo kết quả theo thời gian thực
@apiVersion 1.0.0
@apiName StreamingRealTimeResult

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(QUERY:)			{string}	    [start_time]                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z) 
                                                                            <code>Trong trường hợp tính đến thời điểm hiện tại thì không cần truyền start_time</code>
@apiParam	(QUERY:)			{string}	    end_time                    Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiParam	(QUERY:)			{int}	        time_step                   Thời gian giữa mỗi lần. Sample: 15p thì time_step=15
@apiParam	(QUERY:)			{string}	    time_unit                   Đơn vị.
                                                                            <ul>
                                                                                <li>hour : giờ</li>
                                                                                <li>minute : phút</li>
                                                                                <li>day : ngày</li>
                                                                            </ul>


@apiSuccess   {Array[Object]}  data                                                Dữ liệu báo cáo
@apiSuccess   {string}  data.date                                                  Thời gian. Format: <code>%Y-%m-%d</code> Ví dụ: 2022-03-21
@apiSuccess   {int}  data.total_process_done                                           Tổng số dòng dữ liệu xử lý thành công
@apiSuccess   {int}  data.total_process_fail                                           Tổng số dòng dữ liệu xử lý thất bại

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "date": "2024-03-21",
            "total_process_done": 0,
            "total_process_fail": 0,
        }
    ]
}
"""

# ---------- Báo cáo kết quả thời gian thực theo trạng thái -----------
"""
@apiGroup DataFlow-Streaming
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/streaming/real-time/result-by-status               Báo cáo kết quả thời gian thực theo trạng thái
@apiVersion 1.0.0
@apiName StreamingRealTimeResultByStatus

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(QUERY:)			{string}	    start_time                  Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z) 
@apiParam	(QUERY:)			{string}	    end_time                    Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)


@apiSuccess   {Object}  data                                                Dữ liệu báo cáo
@apiSuccess   {int}  data.total_process_done                                           Tổng số dòng dữ liệu xử lý thành công
@apiSuccess   {int}  data.total_process_fail                                           Tổng số dòng dữ liệu xử lý thất bại
@apiSuccess   {int}  data.total_processing                                           Tổng số dòng dữ liệu đang xử lý
@apiSuccess   {string}  data.firt_time                                           Thời gian ghi nhận bản tin đầu tiên trong ngày
@apiSuccess   {string}  data.last_time                                           Thời gian ghi nhận bản tin gần nhất trong ngày

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "total_process_done": 0,
        "total_process_fail": 0,
        "total_processing": 0,
        "first_time": "2025-02-18 10:20:30",
        "last_time": "2025-02-18 23:20:30"
    }
}
"""

# ---------- Báo cáo kết quả thời gian thực theo đối tượng -----------
"""
@apiGroup DataFlow-Streaming
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/streaming/real-time/result-by-object               Báo cáo kết quả thời gian thực theo đối tượng
@apiVersion 1.0.0
@apiName StreamingRealTimeResultByObject

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(QUERY:)			{string}	    start_time                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z) 
@apiParam	(QUERY:)			{string}	    end_time                  Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)


@apiSuccess   {Array[Object]}  data                                                         Dữ liệu báo cáo
@apiSuccess   {string}  data.object                                                         Đối tượng
                                                                                            <ul>
                                                                                                <li>profile : Profile</li>
                                                                                                <li>company : Công ty</li>
                                                                                                <li>sale : Cơ hội bán</li>
                                                                                                <li>ticket : Ticket</li>
                                                                                            </ul>
                                                                                            
@apiSuccess   {int}  data.total_process_update                                              Tổng số lượng bản ghi cập nhật
@apiSuccess   {int}  data.total_process_add                                                 Tổng số lượng bản ghi thêm mới

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "object": "profile",
            "total_process_update": 0,
            "total_process_add": 0
        }
    ]
}
"""

# ---------- Báo cáo kết quả lịch sử theo dòng dữ liệu -----------
"""
@apiGroup DataFlow-Streaming
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/streaming/history/pipeline/total               Báo cáo kết quả lịch sử thời gian thực theo dòng dữ liệu
@apiVersion 1.0.0
@apiName StreamingHistoryResultByLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(QUERY:)			{string}	    start_time                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z) 
@apiParam	(QUERY:)			{string}	    end_time                  Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiParam	(QUERY:)			{int}	        time_step                   Thời gian giữa mỗi lần. Sample: 15p thì time_step=15
@apiParam	(QUERY:)			{string}	    time_unit                   Đơn vị.
                                                                            <ul>
                                                                                <li>hour : giờ</li>
                                                                                <li>minute : phút</li>
                                                                                <li>day : ngày</li>
                                                                            </ul>


@apiSuccess   {Array[Object]}  data                                                Dữ liệu báo cáo
@apiSuccess   {string}  data.date                                                  Thời gian. Format: <code>%Y-%m-%d</code> Ví dụ: 2022-03-21
@apiSuccess   {int}  data.total_process_done                                           Tổng số dòng dữ liệu xử lý thành công
@apiSuccess   {int}  data.total_process_fail                                           Tổng số dòng dữ liệu xử lý thất bại

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "date": "2024-03-21",
            "total_process_done": 0,
            "total_process_fail": 0,
        }
    ]
}
"""

# ---------- Báo cáo kết quả lịch sử theo đối tượng -----------
"""
@apiGroup DataFlow-Streaming
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/streaming/history/object               Báo cáo kết quả lịch sử theo đối tượng
@apiVersion 1.0.0
@apiName StreamingHistoryResultByObject

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(QUERY:)			{string}	    start_time                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z) 
@apiParam	(QUERY:)			{string}	    end_time                  Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiParam	(QUERY:)			{int}	        time_step                   Thời gian giữa mỗi lần. Sample: 15p thì time_step=15
@apiParam	(QUERY:)			{string}	    time_unit                   Đơn vị.
                                                                            <ul>
                                                                                <li>hour : giờ</li>
                                                                                <li>minute : phút</li>
                                                                                <li>day : ngày</li>
                                                                            </ul>


@apiSuccess   {Array[Object]}  data                                         Dữ liệu báo cáo
@apiSuccess   {string}  data.date                                           Thời gian báo cáo
@apiSuccess   {Array}  data.reports                                         Dữ liệu báo cáo của từng đối tượng
@apiSuccess   {string}  data.reports.object                                         Đối tượng
                                                                            <ul>
                                                                                <li>profile : Profile</li>
                                                                                <li>company : Công ty</li>
                                                                                <li>sale : Cơ hội bán</li>
                                                                                <li>ticket : Ticket</li>
                                                                            </ul>
                                                                                            
@apiSuccess   {int}  data.reports.total_process_add                                Tổng số dòng dữ liệu thêm mới
@apiSuccess   {int}  data.reports.total_process_update                                Tổng số dòng dữ liệu cập nhật

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "date": "2024-03-21",
            "reports": [
                {
                    "object": "profile",
                    "total_process_done": 0,
                    "total_process_fail": 0,
                }
            ]
        }
    ]
}
"""

# ---------- Báo cáo kết quả thời gian thực theo dòng dữ liệu -----------
"""
@apiGroup DataFlow-Streaming
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/streaming/history/pipeline/list               Lấy list lịch sử dòng dữ liệu
@apiVersion 1.0.0
@apiName StreamingHistoryListPipeLine

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(QUERY:)			{string}	    start_time                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z) 
@apiParam	(QUERY:)			{string}	    end_time                  Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiParam	(QUERY:)			{int}	        time_step                   Thời gian giữa mỗi lần. Sample: 15p thì time_step=15
@apiParam	(QUERY:)			{string}	    time_unit                   Đơn vị.
                                                                            <ul>
                                                                                <li>hour : giờ</li>
                                                                                <li>minute : phút</li>
                                                                                <li>day : ngày</li>
                                                                            </ul>
@apiParam    (QUERY:)            {string=-1,1}        [order_by]                 Tiêu chí sắp xếp
@apiParam    (QUERY:)            {string}        [sort_by]                  Sắp xếp theo trường nào. <code>Nếu không truyền gì thì mặc định sắp xếp theo: date</code>
                                                                            <ul>
                                                                                <li>date: ngày</li>
                                                                                <li>total_process_done: Tổng số dòng dữ liệu thành công</li>
                                                                                <li>total_process_fail: Tổng số dòng dữ liệu fail</li>
                                                                                <li>total_profile_add: Tổng số profile được thêm mới</li>
                                                                                <li>total_profile_update: Tổng số profile được cập nhật</li>
                                                                                <li>total_company_add: Tổng số công ty được thêm mới</li>
                                                                                <li>total_company_update: Tổng số company được cập nhật</li>
                                                                                <li>total_sale_add: Tổng số Cơ hội bán được thêm mới</li>
                                                                                <li>total_sale_update: Tổng số Cơ hội bán được cập nhật</li>
                                                                                <li>total_ticket_add: Tổng số ticket được thêm mới</li>
                                                                                <li>total_ticket_update: Tổng số ticket được cập nhật</li>
                                                                            </ul>
                                                                            
@apiUse DataFlowReportPagingTokens                                                                            

@apisuccess   {object}  data                                                 Dữ liệu báo cáo
@apisuccess   {int}  data.date_count                                         số ngày ghi nhận dữ liệu
@apisuccess   {array[object]}  data.streaming_data                                                 Dữ liệu báo cáo chi tiết
@apisuccess   {string}  data.streaming_data.date                                                   Thời gian. format: <code>%y-%m-%d</code> ví dụ: 2022-03-21
@apisuccess   {int}  data.streaming_data.total_process_done                                        Tổng số dòng dữ liệu xử lý thành công
@apisuccess   {int}  data.streaming_data.total_process_fail                                        Tổng số dòng dữ liệu xử lý thất bại
@apisuccess   {int}  data.streaming_data.total_profile_add                                         Tổng số profile được thêm mới
@apisuccess   {int}  data.streaming_data.total_profile_update                                      Tổng số profile được cập nhật
@apisuccess   {int}  data.streaming_data.total_company_add                                         Tổng số company được thêm mới
@apisuccess   {int}  data.streaming_data.total_company_update                                      Tổng số company được cập nhật
@apisuccess   {int}  data.streaming_data.total_sale_add                                            Tổng số cơ hội bán được thêm mới
@apisuccess   {int}  data.streaming_data.total_sale_update                                         Tổng số cơ hội bán được cập nhật
@apisuccess   {int}  data.streaming_data.total_ticket_add                                          Tổng số ticket được thêm mới
@apisuccess   {int}  data.streaming_data.total_ticket_update                                       Tổng số ticket được cập nhật

@apisuccess   {int}  data.streaming_data.total_event_done                                           Tổng số dynamic event thành công
@apisuccess   {int}  data.streaming_data.total_event_fail                                           Tổng số dynamic event thất bại
@apisuccess   {int}  data.streaming_data.total_product_holding_done                                 Tổng số product_holding thành công
@apisuccess   {int}  data.streaming_data.total_product_holding_fail                                 Tổng số product_holding thất bại
@apisuccess   {int}  data.streaming_data.total_product_holding_add                                          Tổng số product_holding được thêm mới
@apisuccess   {int}  data.streaming_data.total_product_holding_update                                       Tổng số product_holding được cập nhật
@apisuccess   {int}  data.streaming_data.total_rows                                       Tổng số dòng dữ liệu
@apisuccess   {int}  data.streaming_data.total_wait_process                                      Tổng số dòng dữ liệu xử lý lỗi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "date_count": 5,
        "streaming_data": [
            {
                "date": "2024-03-21",
                "total_process_done": 0,
                "total_process_fail": 0,
                "total_profile_add": 0,
                "total_profile_update": 0,
                "total_company_add": 0,
                "total_company_update": 0,
                "total_sale_add": 0,
                "total_sale_update": 0,
                "total_ticket_add": 0,
                "total_ticket_update": 0,
                "total_event_done" : 0,
                "total_event_fail" : 0,
                "total_product_holding_done" : 0,
                "total_product_holding_fail" : 0,
                "total_product_holding_add": 0,
                "total_product_holding_update": 0,
                "total_rows": 0,
                "total_wait_process": 0
            }
        ]
    }
}
"""


# ---------- Báo cáo kết quả thời gian thực theo đối tượng event/product holding -----------
"""
@apiGroup DataFlow-Streaming
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/streaming/real-time/result-by-object-event               Báo cáo kết quả thời gian thực theo đối tượng event/ product holding
@apiVersion 1.0.0
@apiName StreamingRealTimeResultByObjectEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(QUERY:)			{string}	    [start_time]                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z) 
                                                                            <code>Trong trường hợp tính đến thời điểm hiện tại thì không cần truyền start_time</code>
@apiParam	(QUERY:)			{string}	    end_time                    Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiParam	(QUERY:)			{int}	        time_step                   Thời gian giữa mỗi lần. Sample: 15p thì time_step=15
@apiParam	(QUERY:)			{string}	    time_unit                   Đơn vị.
                                                                            <ul>
                                                                                <li>hour : giờ</li>
                                                                                <li>minute : phút</li>
                                                                                <li>day : ngày</li>
                                                                            </ul>


@apiSuccess   {Array[Object]}  data                                                  Dữ liệu báo cáo
@apiSuccess   {string}  data.date                                                     Thời gian. Format: <code>%Y-%m-%d %H:%M:%S</code> Ví dụ: 2022-03-21 08:15:00
@apiSuccess   {int}  data.profile_add_count                                           SL profile tạo mới
@apiSuccess   {int}  data.dynamic_event_total_rows_success                                       SL event xử lý thành công
@apiSuccess   {int}  data.dynamic_event_total_rows_error                                           SL event xử lý thất bại
@apiSuccess   {int}  data.product_holding_total_rows_success                                       SL product holding xử lý thành công
@apiSuccess   {int}  data.product_holding_total_rows_error                                           SL product holding xử lý thất bại

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "date": "2025-02-26 08:20:00",
            "profile_add_count": 5,
            "dynamic_event_total_rows_success": 15,
            "dynamic_event_total_rows_error": 1,
            "product_holding_total_rows_success": 20,
            "product_holding_total_rows_error": 2
        }
    ]
}
"""


# ---------- Báo cáo kết quả lịch sử theo đối tượng event/ product holding -----------
"""
@apiGroup DataFlow-Streaming
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/streaming/history/result-by-object-event               Báo cáo kết quả lịch sử theo đối tượng event/product holding
@apiVersion 1.0.0
@apiName StreamingHistoryResultByObjectEvent

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(QUERY:)			{string}	    [start_time]                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z) 
                                                                            <code>Trong trường hợp tính đến thời điểm hiện tại thì không cần truyền start_time</code>
@apiParam	(QUERY:)			{string}	    end_time                    Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)
@apiParam	(QUERY:)			{int}	        time_step                   Thời gian giữa mỗi lần. Sample: 1day thì time_step=1
@apiParam	(QUERY:)			{string}	    time_unit                   Đơn vị.
                                                                            <ul>
                                                                                <li>hour : giờ</li>
                                                                                <li>minute : phút</li>
                                                                                <li>day : ngày</li>
                                                                            </ul>


@apiSuccess   {Array[Object]}  data                                                  Dữ liệu báo cáo
@apiSuccess   {string}  data.date                                                     Thời gian. Format: <code>%Y-%m-%d</code> Ví dụ: 2022-03-21
@apiSuccess   {int}  data.profile_add_count                                           SL profile tạo mới
@apiSuccess   {int}  data.dynamic_event_total_rows_success                                       SL event xử lý thành công
@apiSuccess   {int}  data.dynamic_event_total_rows_error                                           SL event xử lý thất bại
@apiSuccess   {int}  data.product_holding_total_rows_success                                       SL product holding xử lý thành công
@apiSuccess   {int}  data.product_holding_total_rows_error                                           SL product holding xử lý thất bại
@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "date": "2025-02-26",
            "profile_add_count": 5,
            "dynamic_event_total_rows_success": 15,
            "dynamic_event_total_rows_error": 1,
            "product_holding_total_rows_success": 20,
            "product_holding_total_rows_error": 2
        }
    ]
}
"""

# ---------- Báo cáo kết quả lịch sử thống kê từ nguồn -----------
"""
@apiGroup DataFlow-Streaming
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/streaming/history/result-from-source              Báo cáo kết quả lịch sử thống kê từ nguồn
@apiVersion 1.0.0
@apiName StreamingHistoryResultFromSource

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam	(QUERY:)			{string}	    [start_time]                Thời gian bắt đầu (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z) 
                                                                            <code>Trong trường hợp tính đến thời điểm hiện tại thì không cần truyền start_time</code>
@apiParam	(QUERY:)			{string}	    end_time                    Thời gian kết thúc (Format: <code>%Y-%m-%dT%H:%MZ</code> Ví dụ: 2022-03-21T23:50Z)



@apiSuccess   {Object}  data                                                  Dữ liệu báo cáo
@apiSuccess   {int}  data.total_rows                                           Tổng số dòng dữ liệu
@apiSuccess   {int}  data.total_rows_success                                       Tổng số dòng dữ liệu xử lý thành công
@apiSuccess   {int}  data.total_rows_error                                           Tổng số dòng dữ liệu xử lý thất bại
@apiSuccess   {int}  data.profile_add_count                                           Tổng số profile tao mới
@apiSuccess   {int}  data.total_wait_process                                           Tổng số dòng dữ liệu chờ xử lý


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "total_rows": 1532,
        "total_rows_success": 1525,
        "total_rows_error": 7,
        "profile_add_count": 1150,
        "total_wait_process": 0
    }
}
"""
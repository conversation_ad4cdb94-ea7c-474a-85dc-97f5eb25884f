#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 22/05/2024
"""


# ---------- L<PERSON><PERSON> danh sách session của snapshot -----------
"""
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/snapshot/sessions               L<PERSON>y danh sách session của loại snapshot
@apiGroup DataFlow-Snapshot
@apiVersion 1.0.0
@apiName SnapshotListSession

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse SnapshotFilter
@apiParam	(QUERY:)			{string}	    [sort_by]                           Field ghi nhận sắp xếp nhận các giá trị: <code>start_time, end_time, total_row</code>.
                                                                                    Default: <code>start_time</code>
@apiParam	(QUERY:)			{string}	    [order_by]                          Sắp xếp theo thứ tự tăng/giảm dần: <code>1, -1 </code>.
                                                                                    Default: <code>-1</code>
@apiUse DataFlowReportPagingTokens

@apiSuccess   {Array[Object]}  data                                                        Dữ liệu báo cáo
@apiSuccess   {string}  data.session_id                                             <code>ID</code> của session
@apiSuccess   {string}  data.status_sync                                            Trạng thái đồng bộ
                                                                                    <ul>
                                                                                        <li><code>wait_process</code> :: Chờ xử lý</li>
                                                                                        <li><code>processing</code> :: Đang xử lý</li>
                                                                                        <li><code>done</code> :: Hoàn thành</li>
                                                                                    </ul>
@apiSuccess   {string}  data.start_time                                             Thời gian bắt đầu (%Y-%m-%d %H:%M:%S)
@apiSuccess   {string}  data.end_time                                               Thời gian kết thúc (%Y-%m-%d %H:%M:%S)
@apiSuccess   {int}  data.total_row                                              Tổng số dòng dữ liệu
@apiSuccess   {int}  data.total_process_done                                           Tổng số dòng dữ liệu xử lý thành công
@apiSuccess   {int}  data.total_process_fail                                           Tổng số dòng dữ liệu xử lý thất bại
@apiSuccess   {int}  data.total_new_profile                                           Số lượng profile mới
@apiSuccess   {int}  data.processing_rate_min                                           Tốc độ xử lý theo phút
@apiSuccess   {int}  data.processing_rate_hour                                          Tốc độ xử lý theo giờ

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "session_id": "OWN-c8a716de-2bc8-4a4f-8c53-a9680ce689C1",
            "status_sync": "wait_process",
            "start_time": "2022-03-21 23:50:15",
            "end_time": "2022-03-21 23:50:30",
            "total_row": 0,
            "total_process_done": 0,
            "total_process_fail": 0,
            "total_new_profile": 0,
            "processing_rate_min": 30,
            "processing_rate_hour": 800
        }
    ]
}
"""

# ---------- Lấy tổng số session của snapshot -----------
"""
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/snapshot/total-sessions               Lấy tổng số session của loại snapshot
@apiGroup DataFlow-Snapshot
@apiVersion 1.0.0
@apiName SnapshotTotalSession

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse SnapshotFilter

@apiSuccess   {Object}  data                                                        Dữ liệu báo cáo
@apiSuccess   {int}  data.total_wait_process                                           Chờ xử lý
@apiSuccess   {int}  data.total_processing                                             Đang xử lý
@apiSuccess   {int}  data.total_process_done                                                   Hoàn tất
@apiSuccess   {int}  data.total_process_fail                                                   Lỗi

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "total_wait_process": 0,
        "total_processing": 0,
        "total_process_done": 0,
        "total_process_fail": 0
    }
}
"""

# ---------- Lấy chi tiết session của snapshot -----------
"""
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/snapshot/sessions/<session_id>               Lấy chi tiết của session
@apiGroup DataFlow-Snapshot
@apiVersion 1.0.0
@apiName SnapshotDetailSession

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiSuccess   {Object}  data                                                        Dữ liệu báo cáo
@apiSuccess   {string}  data.session_id                                             <code>ID</code> định danh của session
@apiSuccess   {string}  data.start_time                                             Thời gian bắt đầu
@apiSuccess   {string}  data.end_time                                               Thời gian kết thúc
@apiSuccess   {int}  data.total_wait_process                                           Dữ liệu chờ xử lý
@apiSuccess   {int}  data.total_processing                                             Đang xử lý
@apiSuccess   {int}  data.total_process_done                                                   Xử lý thành công
@apiSuccess   {int}  data.total_process_fail                                                   Xử lý thất bại
@apiSuccess   {Array[Object]}  data.report_object                                         Báo cáo theo đối tượng
@apiSuccess   {string}  data.status_sync                                            Trạng thái đồng bộ
                                                                                    <ul>
                                                                                        <li><code>wait_process</code> :: Chờ xử lý</li>
                                                                                        <li><code>processing</code> :: Đang xử lý</li>
                                                                                        <li><code>done</code> :: Hoàn thành</li>
                                                                                    </ul>
@apiSuccess   {string}  data.report_object.object                                                  Đối tượng
                                                                                            <ul>
                                                                                                <li>profile : Profile</li>
                                                                                                <li>company : Công ty</li>
                                                                                                <li>sale : Cơ hội bán</li>
                                                                                                <li>ticket : Ticket</li>
                                                                                            </ul>
@apiSuccess   {int}  data.report_object.total_row_update                                         Tổng số lượng bản ghi cập nhật
@apiSuccess   {int}  data.report_object.total_row_add                                             Tổng số lượng bản ghi thêm mới
@apiSuccess   {int}  data.total_new_profile                                           Số lượng profile mới
@apiSuccess   {int}  data.processing_rate_min                                           Tốc độ xử lý theo phút
@apiSuccess   {int}  data.processing_rate_hour                                          Tốc độ xử lý theo giờ
@apiSuccess   {int}  data.event_error_count                                          Số lượng event/product holding thất bại
@apiSuccess   {int}  data.event_processed_count                                          Số lượng event/product holding thành công
@apiSuccess   {int}  data.object_new_count                                          Số lượng event/product holding thêm mới
@apiSuccess   {int}  data.object_update_count                                          Số lượng event/product holding cập nhật
@apiSuccess   {object}                          data.config_sync_calendar                            Cấu hình thời gian lập lịch sync data
@apiSuccess   {object}            [data.config_sync_calendar.schedule]                 Cấu hình lập lịch chạy
@apiSuccess  {string}            [data.config_sync_calendar.schedule.type]            Kiểu lập lịch
                                                                                                        <ul>
                                                                                                            <li>manually: thủ công</li>
                                                                                                            <li>interval: định kỳ</li>
                                                                                                        </ul>
@apiSuccess    {object}            [data.config_sync_calendar.schedule.config]           Cấu hình thời gian lặp theo định kỳ
@apiSuccess    {object}            [data.config_sync_calendar.schedule.config.hour]      Thời gian đồng bộ. Định dạng (HH:MM). Example (16:49)
@apiSuccess    {object}            [data.config_sync_calendar.schedule.config.type]      Kiểu lịch trình
                                                                                                            <ul>
                                                                                                                <li><code>day</code>: Ngày</li>
                                                                                                                <li><code>week</code>: Tuần</li>
                                                                                                                <li><code>month</code>: Tháng</li>
                                                                                                                <li><code>year</code>: Năm</li>
                                                                                                            </ul>
@apiSuccess   {object}            [data.config_sync_calendar.schedule.config.type_select_day_in_month]      Kiểu chọn ngày trong tháng
                                                                                                        <ul>
                                                                                                            <li><code>exact_day</code>: Ngày cụ thể</li>
                                                                                                            <li><code>flex_day</code>: Ngày linh hoạt</li>
                                                                                                        </ul>                                                                                    
@apiSuccess   {Array}             [data.config_sync_calendar.schedule.config.values]            Cấu hình thông tin lặp vào (Ví dụ: thứ 2, thứ 3)
                                                                                            <ul>
                                                                                                <li> Case: type=<code>day</code> thì <code>values: có thể để mảng rỗng - ví dụ []</code></li>
                                                                                                <li> Case: type=<code>week</code> thì <code>values: danh sách các thứ trong tuần - ví dụ ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]</code></li>
                                                                                                <li> Case: type=<code>month</code> thì <code>values: Danh sách các ngày trong tháng (Mặc định là 31 ngày) - ví dụ [1,2,3,4,5,6,7]</code> hoặc values có thể là : <code>first_of_month</code> (Ngày đầu tháng), <code>last_of_month </code> (Ngày cuối tháng)</li>
                                                                                                <li> Case: type=<code>year</code> thì <code>values: sẽ là mảng chứa giá trị ngày và tháng - ví dụ ["24/04"]</code></li>
                                                                                            </ul>

@apiSuccess    {object}            [data.config_sync_calendar.schedule.config.account_id]           Id của tác nhân đồng bộ nếu type = "manually"

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "session_id": "OWN-c8a716de-2bc8-4a4f-8c53-a9680ce689C1",
        "start_time": "2022-03-21T23:50Z",
        "end_time": "2022-03-21T23:50Z",
        "status_sync": "wait_process",
        "total_wait_process": 0,
        "total_processing": 0,
        "total_process_done": 0,
        "total_process_fail": 0,
        "report_object": [
            {
                "object": "profile",
                "total_row_update": 0,
                "total_row_add": 0
            }
        ],
        "total_new_profile": 0,
        "processing_rate_min": 30,
        "processing_rate_hour": 800,
        "event_error_count": 3,
        "event_processed_count": 60,
        "object_new_count": 15,
        "object_update_count": 20
        "config_sync_calendar": {
            "schedule": {
                "config": {
                    "hour": "16:49",
                    "type": "day",
                    "values": []
                },
                "type": "interval"
            }
        }
    }
}
"""

# ---------- Lấy số lượng records mỗi session đang chạy của snapshot -----------
"""
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/snapshot/running-sessions         Lấy số lượng records mỗi session đang chạy của loại snapshot
@apiGroup DataFlow-Snapshot
@apiVersion 1.0.0
@apiName SnapshotResultRunningSession

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse SnapshotFilter

@apiSuccess   {Array[Object]}  data                                                        Dữ liệu báo cáo
@apiSuccess   {string}  data.session_id                                             <code>ID</code> của session
@apiSuccess   {Array[Object]}  data.detail_data                                            Chi tiết dữ liệu mỗi session
@apiSuccess   {string}  data.detail_data.time_group                                           Thời gian. Format: <code>%Y-%m-%d %H:%M:%S</code> Ví dụ: 2022-03-21 15:30:00
@apiSuccess   {int}  data.detail_data.total_consume                                           Tổng số dòng dữ liệu xử lý

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": [
        {
            "session_id": "20250216043077",
            "detail_data": [
                {
                    "time_group": "2025-02-17 13:30:00",
                    "total_consume": 1423
                }
            }
        }
    ]
}
"""

# ---------- Lấy tổng số session theo trạng thái  từng ngày của snapshot -----------
"""
@api {GET} {domain}/market-place/api/v1.0/reports/connectors/<connector_id>/snapshot/total-sessions/by-date            Lấy tổng số session theo trạng thái từng ngày của loại snapshot
@apiGroup DataFlow-Snapshot
@apiVersion 1.0.0
@apiName SnapshotTotalSessionByStatusEveryDay

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiUse SnapshotFilter


@apiSuccess   {Object}  data                                                        Dữ liệu báo cáo
@apiSuccess   {string}  data.status_sync                                            Trạng thái đồng bộ
                                                                                    <ul>
                                                                                        <li><code>wait_process</code> :: Chờ xử lý</li>
                                                                                        <li><code>processing</code> :: Đang xử lý</li>
                                                                                        <li><code>done</code> :: Hoàn thành</li>
                                                                                    </ul>
@apiSuccess   {string}  data.start_time                                             Thời gian bắt đầu
@apiSuccess   {string}  data.end_time                                               Thời gian kết thúc
@apiSuccess   {Array[Object]}  detail_data 
@apiSuccess   {string}  data.detail_data.date_group                                             Thời gian. Format: <code>%Y-%m-%d: 00:00:00</code> Ví dụ: 2025-01-02 00:00:00
@apiSuccess   {int}  data.detail_data. total_done_sessions                                             Tổng số session đã hoàn thành


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công."
    "data": {
        "status_sync": "done",
        "start_time": "2025-02-10 23:50:00",
        "end_time": "2025-02-17 23:50:00",
        "detail_data": {
            "date_group": "2025-01-02 00:00:00",
            "total_done_sessions": 3
        }
    }
}
"""


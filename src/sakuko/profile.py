#!/usr/bin/python
# -*- coding: utf8 -*-

****************************** Criterial Key **********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@apiDefine critetia_key_body
@apiVersion 1.0.0
@apiParam   (profile_filter:) {String}    criteria_key    Các criteria key để filter khách hàng. Allowed values:<br/>
<li><code>cri_address</code>: Address
<li><code>cri_age</code>: Age
<li><code>cri_birthday</code>: Birthday
<li><code>cri_birthday_period</code>: <PERSON>h nhật theo chu kỳ.
<li><code>cri_business_case</code>: Business case
<li><code>cri_card_level</code>: Loại thẻ
<li><code>cri_card_status</code>: Customer card status
<li><code>cri_city</code>: City
<li><code>cri_created_account_type</code>: Nguồn ghi nhận khách hàng
<li><code>cri_gender</code>: gender
<li><code>cri_hobby</code>: Hobby
<li><code>cri_job</code>: Job
<li><code>cri_marital_status</code>: Tình trạng hôn nhân
<li><code>cri_mkt_action_value</code>: Marketing action value
<li><code>cri_mkt_business_case_id</code>: MKT Business ID
<li><code>cri_mkt_campaign_id</code>: MKT Campaign ID
<li><code>cri_mkt_process_type</code>: Process type
<li><code>cri_mkt_root_process</code>: MKT root_process_id
<li><code>cri_mkt_step</code>: Loại khách hàng marketing
<li><code>cri_operation</code>: Lĩnh vực hoạt động
<li><code>cri_region</code>: Region
<li><code>cri_tags</code>: Lọc theo tags
"""

****************************** Operator Key ***********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@apiDefine operator_key_body
@apiVersion 1.0.0
@apiParam   (profile_filter:) {String="op_is_between","op_is_greater_equal","op_is_in","op_is_equal","op_is_greater","op_is_has","op_is_has_not","op_is_less_equal","op_is_less","op_is_regex"}    operator_key    Các toán tử để filter khách hàng.
"""

************************************** Create Profile *********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/profile/actions/create Create Profile
@apiDescription API Tạo khách hàng.
@apiGroup Profile
@apiVersion 1.0.0
@apiName CreateProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)        {Object}    profile                            Thông tin khách hàng
@apiParam   (Body:)        {Object}    [cards]                            Thông tin thẻ khách hàng.
@apiParam   (profile:)     {Array}    [relationship_data]                       ID của cha/mẹ Profile (Khi muốn add thêm Child vào Profile).
@apiParam   (profile:)     {String}    [customer_id]                            Mã khách hàng trên hệ thống Partner.
@apiParam   (profile:)     {String}    [customer_created_time]                            Ngày tạo mã khách hàng trên hệ thống Partner. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {String}    [partner_point]                            Số điểm tích lũy trên hệ thống Partner.
@apiParam   (profile:)     {Number}    [degree]                            Trình độ.
@apiParam   (profile:)     {Number}    [income_family]                            Tổng thu nhập cả gia đình.
@apiParam   (profile:)     {Number}    [relation_with_childs]                            Mỗi quan hệ giữa profile với các con.
<br/><br/>Allowed values:<br/>
<li><code>1: Mẹ</code></li>
<li><code>2: Bố</code></li>
@apiParam   (profile:)     {Array}    [childs]                            Danh sách con.
@apiParam   (profile:)     {String}    name                            Tên khách hàng
@apiParam   (profile:)     {String}    [phone_number_1]                          Số điện thoại chính của khách hàng.
@apiParam   (profile:)     {String}    [email_1]                                   Email chính của khách hàng.
@apiParam   (profile:)     {List}    [phone_number_2]                        Số điện thoại phụ của khách hàng.
@apiParam   (profile:)     {List}    [email_2]                               Email phụ của khách hàng.
@apiParam   (profile:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (profile:)     {Array}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (profile:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (profile:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (profile:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (profile:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (profile:)     {String}    [tags]                        Nhãn gợi ý tìm kiếm. Format: "tags1;tags2"
@apiParam   (profile:)     {String}    [company]                     Công ty.
@apiParam   (profile:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (profile:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (profile:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (profile:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (profile:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (profile:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (profile:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.
@apiParam   (profile:)     {Object}    [social_user]                             Thông tin người dùng trên mạng xã hội

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.
@apiParam   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiParam   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube

@apiParam   (childs:)     {String}  name                                  Tên con.
@apiParam   (childs:)     {String}    [birthday]                              Ngày sinh của con. Format <code>YYYY-mm-DD</code>.
@apiParam   (childs:)     {Number}  [gender]                                  Giới tính của con.
@apiParam   (childs:)     {Number}  [nth]                                  Con thứ mấy trong gia đình.

@apiParam   (relationship_data:)     {Number}  relationship_type                                  Mối quan hệ với profile đang được khởi tạo.
<br/><br/>Allowed values:<br/>
<li><code>1: Mẹ</code></li>
<li><code>2: Bố</code></li>
@apiParam   (relationship_data:)     {Number}  profile_id                                  Id của Profile có mỗi quan hệ với profile đang được khởi tạo.

@apiSuccess {Object}    profile_info     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    cards        Dữ liệu thẻ khách hàng
@apiSuccess {Object}    process_type        Kiểu thực thi Profile.
<br/>
<li><code>add: Add Profile/ Tạo Profile</code></li>
<li><code>update: Update Profile / Cập nhật Profile</code></li>
<li><code>merge: Merge Profile / Hợp nhất Profile</code></li>

@apiSuccess (profile_info:)     {String}     id                               ID khách hàng
@apiSuccess   (profile_info:)     {String}      name                            Tên khách hàng
@apiSuccess   (profile_info:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (profile_info:)     {Object}     primary_email                     Email chính của khách hàng.
@apiSuccess   (profile_info:)     {Object}     secondary_emails                     Các email phụ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (profile_info:)     {Number}    gender                            Giới tính
@apiSuccess   (profile_info:)     {Array}     address                           Địa chỉ
@apiSuccess   (profile_info:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (profile_info:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (profile_info:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (profile_info:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (profile_info:)     {String}    birthday                          Ngày sinh
@apiSuccess   (profile_info:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (profile_info:)     {Number}    income                            Mức thu nhập
@apiSuccess   (profile_info:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess   (profile_info:)     {Object}    social_user                             Thông tin người dùng trên mạng xã hội

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccess   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiSuccess   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube 

@apiParamExample [json] Body example:
{
  "profile": {
    "created_account_type": 2,
    "phone_number": ["**********"],
    "email": ["<EMAIL>"],
    "social_user": {"social_id": "*********", "social_type": 1, "social_name": "andrew"}
  }
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "address": [], 
      "answers": [], 
      "association": [], 
      "avatar": null, 
      "avg_monthly_consumption": null, 
      "avg_rate": null, 
      "birthday": null, 
      "budget_high_threshold": null, 
      "budget_low_threshold": null, 
      "company": null, 
      "created_account_type": 0, 
      "created_time": "2019-03-27T17:52:46Z", 
      "description": null,
      "relationship_data": [{"profile_id": "71199f63-1870-41fa-995d-b8534cd182f6", "relationship_type":1}]
      "district_code": null, 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "face_id": [], 
      "frequently_demands": [], 
      "gender": 2, 
      "hobby": [], 
      "income_high_threshold": null, 
      "income_low_threshold": null, 
      "income_type": null, 
      "is_non_profile": false, 
      "job": null, 
      "last_time_active": null, 
      "last_time_transaction": null, 
      "lat": null, 
      "location": null, 
      "lon": null, 
      "marital_status": null, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "Giang", 
      "nation": null, 
      "number_interactive": null, 
      "number_transaction": null, 
      "operation": null, 
      "people_id": null, 
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "position": null, 
      "predict": [], 
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      }, 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "province_code": null, 
      "religiousness": null, 
      "salary": null, 
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "social_user": [], 
      "source_id": null, 
      "source_type": null, 
      "state": null, 
      "tags": [], 
      "total_rate": null, 
      "updated_time": "2019-03-27T17:52:46Z", 
      "ward_code": null, 
      "website": null, 
      "workplace": null
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

************************************** Create Profile *********************************
* version: 1.0.1                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/profile/actions/create Create Profile
@apiDescription API Tạo khách hàng.
@apiGroup Profile
@apiVersion 1.0.1
@apiName CreateProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)        {Object}    profile                            Thông tin khách hàng
@apiParam   (Body:)        {Object}    [cards]                            Thông tin thẻ khách hàng.
@apiParam   (profile:)     {Array}    [relationship_data]                       ID của cha/mẹ Profile (Khi muốn add thêm Child vào Profile).
@apiParam   (profile:)     {String}    [customer_id]                            Mã khách hàng trên hệ thống Partner.
@apiParam   (profile:)     {String}    [customer_created_time]                            Ngày tạo mã khách hàng trên hệ thống Partner. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {String}    [partner_point]                            Số điểm tích lũy trên hệ thống Partner.
@apiParam   (profile:)     {Number}    [degree]                            Trình độ.
@apiParam   (profile:)     {Number}    [income_family]                            Tổng thu nhập cả gia đình.
@apiParam   (profile:)     {Number}    [relation_with_childs]                            Mỗi quan hệ giữa profile với các con.
<br/><br/>Allowed values:<br/>
<li><code>1: Mẹ</code></li>
<li><code>2: Bố</code></li>
@apiParam   (profile:)     {Array}    [childs]                            Danh sách con.
@apiParam   (profile:)     {String}    name                            Tên khách hàng
@apiParam   (profile:)     {String}    [phone_number_1]                          Số điện thoại chính của khách hàng.
@apiParam   (profile:)     {String}    [email_1]                                   Email chính của khách hàng.
@apiParam   (profile:)     {List}    [phone_number_2]                        Số điện thoại phụ của khách hàng.
@apiParam   (profile:)     {List}    [email_2]                               Email phụ của khách hàng.
@apiParam   (profile:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (profile:)     {Array}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (profile:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (profile:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (profile:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (profile:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (profile:)     {String}    [tags]                        Nhãn gợi ý tìm kiếm. Format: "tags1;tags2"
@apiParam   (profile:)     {String}    [company]                     Công ty.
@apiParam   (profile:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (profile:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (profile:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (profile:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (profile:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (profile:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (profile:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.
@apiParam   (profile:)     {Object}    [social_user]                             Thông tin người dùng trên mạng xã hội

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.
@apiParam   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiParam   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube

@apiParam   (childs:)     {String}  name                                  Tên con.
@apiParam   (childs:)     {String}    [birthday]                              Ngày sinh của con. Format <code>YYYY-mm-DD</code>.
@apiParam   (childs:)     {Number}  [gender]                                  Giới tính của con.
@apiParam   (childs:)     {Number}  [nth]                                  Con thứ mấy trong gia đình.

@apiParam   (relationship_data:)     {Number}  relationship_type                                  Mối quan hệ với profile đang được khởi tạo.
<br/><br/>Allowed values:<br/>
<li><code>1: Mẹ</code></li>
<li><code>2: Bố</code></li>
@apiParam   (relationship_data:)     {Number}  profile_id                                  Id của Profile có mỗi quan hệ với profile đang được khởi tạo.

@apiSuccess {Object}    profile_info     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    cards        Dữ liệu thẻ khách hàng
@apiSuccess {Object}    process_type        Kiểu thực thi Profile.
<br/>
<li><code>add: Add Profile/ Tạo Profile</code></li>
<li><code>update: Update Profile / Cập nhật Profile</code></li>
<li><code>merge: Merge Profile / Hợp nhất Profile</code></li>

@apiSuccess (profile_info:)     {String}     id                               ID khách hàng
@apiSuccess   (profile_info:)     {String}      name                            Tên khách hàng
@apiSuccess   (profile_info:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (profile_info:)     {Object}     primary_email                     Email chính của khách hàng.
@apiSuccess   (profile_info:)     {Object}     secondary_emails                     Các email phụ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (profile_info:)     {Number}    gender                            Giới tính
@apiSuccess   (profile_info:)     {Array}     address                           Địa chỉ
@apiSuccess   (profile_info:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (profile_info:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (profile_info:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (profile_info:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (profile_info:)     {String}    birthday                          Ngày sinh
@apiSuccess   (profile_info:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (profile_info:)     {Number}    income                            Mức thu nhập
@apiSuccess   (profile_info:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess   (profile_info:)     {Object}    social_user                             Thông tin người dùng trên mạng xã hội

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccess   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiSuccess   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube 

@apiParamExample [json] Body example:
{
  "profile": {
    "created_account_type": 2,
    "phone_number": ["**********"],
    "email": ["<EMAIL>"],
    "social_user": {"social_id": "*********", "social_type": 1, "social_name": "andrew"}
  }
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "address": [], 
      "answers": [], 
      "association": [], 
      "avatar": null, 
      "avg_monthly_consumption": null, 
      "avg_rate": null, 
      "birthday": null, 
      "budget_high_threshold": null, 
      "budget_low_threshold": null, 
      "company": null, 
      "created_account_type": 0, 
      "created_time": "2019-03-27T17:52:46Z", 
      "description": null,
      "relationship_data": [{"profile_id": "71199f63-1870-41fa-995d-b8534cd182f6", "relationship_type":1}]
      "district_code": null, 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "face_id": [], 
      "frequently_demands": [], 
      "gender": 2, 
      "hobby": [], 
      "income_high_threshold": null, 
      "income_low_threshold": null, 
      "income_type": null, 
      "is_non_profile": false, 
      "job": null, 
      "last_time_active": null, 
      "last_time_transaction": null, 
      "lat": null, 
      "location": null, 
      "lon": null, 
      "marital_status": null, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "Giang", 
      "nation": null, 
      "number_interactive": null, 
      "number_transaction": null, 
      "operation": null, 
      "people_id": null, 
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "position": null, 
      "predict": [], 
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      }, 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "province_code": null, 
      "religiousness": null, 
      "salary": null,
      "number_childs": 1,
      "childs": [
        {
          "name": 'con trail',
          "gender": 2,
          "birthday": "2000-05-20",
          "nth": 1
        }
      ],
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "social_user": [], 
      "source_id": null, 
      "source_type": null, 
      "state": null, 
      "tags": [], 
      "total_rate": null, 
      "updated_time": "2019-03-27T17:52:46Z", 
      "ward_code": null, 
      "website": null, 
      "workplace": null
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

**************************************  Update Profile *********************************
* version: 1.0.0                                                                       *
****************************************************************************************
"""
@api {put} [HOST]/profiling/v3.0/profile/actions/update/<profile_id> Update Profile
@apiDescription API cập nhật khách hàng.
@apiGroup Profile
@apiVersion 1.0.0
@apiName UpdateProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    profile_id                             ID của profile.

@apiParam   (Body:)     {Object}    profile                            Thông tin khách hàng
@apiParam   (Body:)     {Object}    [cards]                               Thông tin thẻ khách hàng.

@apiParam   (profile:)     {String}    [customer_id]                            Mã khách hàng trên hệ thống Partner.
@apiParam   (profile:)     {String}    [customer_created_time]                            Ngày tạo mã khách hàng trên hệ thống Partner. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {String}    [partner_point]                            Số điểm tích lũy trên hệ thống Partner.
@apiParam   (profile:)     {Number}    [degree]                            Trình độ.
@apiParam   (profile:)     {Number}    [income_family]                            Tổng thu nhập cả gia đình.
@apiParam   (profile:)     {String}    name                            Tên khách hàng
@apiParam   (profile:)     {String}    [phone_number_1]                          Số điện thoại chính của khách hàng.
@apiParam   (profile:)     {String}    [email_1]                                   Email chính của khách hàng.
@apiParam   (profile:)     {List}    [phone_number_2]                        Số điện thoại phụ của khách hàng.
@apiParam   (profile:)     {List}    [email_2]                               Email phụ của khách hàng.
@apiParam   (profile:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (profile:)     {Array}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (profile:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (profile:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (profile:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (profile:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (profile:)     {String}    [tags]                        Nhãn gợi ý tìm kiếm. Format: "tags1;tags2"
@apiParam   (profile:)     {String}    [company]                        Công ty.

@apiParam   (profile:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (profile:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (profile:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (profile:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (profile:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (profile:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (profile:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.
@apiParam   (profile:)     {Object}    [social_user]                             Thông tin người dùng trên mạng xã hội

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.
@apiParam   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiParam   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube


@apiSuccess {Object}    profile_info     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    cards        Dữ liệu thẻ khách hàng
@apiSuccess {Object}    process_type        Kiểu thực thi Profile.
<br/>
<li><code>add: Add Profile/ Tạo Profile</code></li>
<li><code>update: Update Profile / Cập nhật Profile</code></li>
<li><code>merge: Merge Profile / Hợp nhất Profile</code></li>

@apiSuccess (profile_info:)     {String}     id                               ID khách hàng
@apiSuccess   (profile_info:)     {String}      name                            Tên khách hàng
@apiSuccess   (profile_info:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (profile_info:)     {Object}     primary_email                     Email chính của khách hàng.
@apiSuccess   (profile_info:)     {Object}     secondary_emails                     Các email phụ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (profile_info:)     {Number}    gender                            Giới tính
@apiSuccess   (profile_info:)     {Array}     address                           Địa chỉ
@apiSuccess   (profile_info:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (profile_info:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (profile_info:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (profile_info:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (profile_info:)     {String}    birthday                          Ngày sinh
@apiSuccess   (profile_info:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (profile_info:)     {Number}    income                            Mức thu nhập
@apiSuccess   (profile_info:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess   (profile_info:)     {Object}    social_user                             Thông tin người dùng trên mạng xã hội

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccess   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiSuccess   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube 

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "address": [], 
      "answers": [], 
      "association": [], 
      "avatar": null, 
      "relationship_data": [{"profile_id": "71199f63-1870-41fa-995d-b8534cd182f6", "relationship_type":1}]
      "avg_monthly_consumption": null, 
      "avg_rate": null, 
      "birthday": null, 
      "budget_high_threshold": null, 
      "budget_low_threshold": null, 
      "company": null, 
      "created_account_type": 0, 
      "created_time": "2019-03-27T17:52:46Z", 
      "description": null, 
      "district_code": null, 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "face_id": [], 
      "frequently_demands": [], 
      "gender": 2, 
      "hobby": [], 
      "income_high_threshold": null, 
      "income_low_threshold": null, 
      "income_type": null, 
      "is_non_profile": false, 
      "job": null, 
      "last_time_active": null, 
      "last_time_transaction": null, 
      "lat": null, 
      "location": null, 
      "lon": null, 
      "marital_status": null, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "Giang", 
      "nation": null, 
      "number_interactive": null, 
      "number_transaction": null,
      "operation": null, 
      "people_id": null, 
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "position": null, 
      "predict": [], 
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      }, 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "province_code": null, 
      "religiousness": null, 
      "salary": null, 
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "social_user": [], 
      "source_id": null, 
      "source_type": null, 
      "state": null, 
      "tags": [], 
      "total_rate": null, 
      "updated_time": "2019-03-27T17:52:46Z", 
      "ward_code": null, 
      "website": null, 
      "workplace": null
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

************************************** Update Profile *********************************
* version: 1.0.1                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/v3.0/profile/actions/update/<profile_id> Update Profile
@apiDescription API cập nhật khách hàng.
@apiGroup Profile
@apiVersion 1.0.1
@apiName UpdateProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam      (Resource:)    {String}    profile_id                             ID của profile.

@apiParam   (Body:)     {Object}    profile                            Thông tin khách hàng
@apiParam   (Body:)     {Object}    [cards]                               Thông tin thẻ khách hàng.

@apiParam   (profile:)     {String}    [customer_id]                            Mã khách hàng trên hệ thống Partner.
@apiParam   (profile:)     {String}    [customer_created_time]                            Ngày tạo mã khách hàng trên hệ thống Partner. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {String}    [partner_point]                            Số điểm tích lũy trên hệ thống Partner.
@apiParam   (profile:)     {Number}    [degree]                            Trình độ.
@apiParam   (profile:)     {Number}    [income_family]                            Tổng thu nhập cả gia đình.
@apiParam   (profile:)     {String}    name                            Tên khách hàng
@apiParam   (profile:)     {String}    [phone_number_1]                          Số điện thoại chính của khách hàng.
@apiParam   (profile:)     {String}    [email_1]                                   Email chính của khách hàng.
@apiParam   (profile:)     {List}    [phone_number_2]                        Số điện thoại phụ của khách hàng.
@apiParam   (profile:)     {List}    [email_2]                               Email phụ của khách hàng.
@apiParam   (profile:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (profile:)     {Array}    [address]                               Địa chỉ cụ thể của khách hàng.
@apiParam   (profile:)     {String}    [province_code]                         Mã tỉnh thành.
@apiParam   (profile:)     {String}    [district_code]                         Mã quận huyện.
@apiParam   (profile:)     {String}    [ward_code]                             Mã phường xã.
@apiParam   (profile:)     {Number}    [marital_status]                        Tình trạng hôn nhân
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiParam   (profile:)     {String}    [tags]                        Nhãn gợi ý tìm kiếm. Format: "tags1;tags2"
@apiParam   (profile:)     {String}    [company]                        Công ty.

@apiParam   (profile:)     {String}    [birthday]                              Ngày sinh. Format <code>YYYY-mm-DD</code>.
@apiParam   (profile:)     {Number}    [job]                                   Nghề nghiệp.
@apiParam   (profile:)     {Number}    [operation]                             Lĩnh vực kinh doanh
@apiParam   (profile:)     {String}    [hobby]                                 Danh sách Sở thích. Ngăn cách nhau bởi dấu ;
@apiParam   (profile:)     {Number}    [income_low_threshold]                                 Mức thu nhập thấp.
@apiParam   (profile:)     {Number}    [income_high_threshold]                                Mức thu nhập cao.
@apiParam   (profile:)     {Number}    [budget_low_threshold]                                 Mức tiêu dùng thấp.
@apiParam   (profile:)     {Number}    [budget_high_threshold]                                Mức tiêu dùng cao.
@apiParam   (profile:)     {Object}    [social_user]                             Thông tin người dùng trên mạng xã hội

@apiParam   (cards:)     {String}    [id]                               Id thẻ thành viên
@apiParam   (cards:)     {String}    [code]                             Mã thẻ
@apiParam   (cards:)     {Number=1:Pending 2:Approval 3: Canceled}    [status]                           Trạng thái thẻ.
@apiParam   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiParam   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube


@apiSuccess {Object}    profile_info     Dữ liệu thông tin khách hàng
@apiSuccess {Object}    cards        Dữ liệu thẻ khách hàng
@apiSuccess {Object}    process_type        Kiểu thực thi Profile.
<br/>
<li><code>add: Add Profile/ Tạo Profile</code></li>
<li><code>update: Update Profile / Cập nhật Profile</code></li>
<li><code>merge: Merge Profile / Hợp nhất Profile</code></li>

@apiSuccess (profile_info:)     {String}     id                               ID khách hàng
@apiSuccess   (profile_info:)     {String}      name                            Tên khách hàng
@apiSuccess   (profile_info:)     {Array}     phone_number                       Mảng Số điện thoại của khách hàng.
@apiSuccess   (profile_info:)     {Object}     primary_email                     Email chính của khách hàng.
@apiSuccess   (profile_info:)     {Object}     secondary_emails                     Các email phụ của khách hàng.
@apiSuccess   (profile_info:)     {Number}    created_account_type               Kiểu ghi nhận thông tin khách hàng
@apiSuccess   (profile_info:)     {String}    avatar                            Ảnh đại diện
@apiSuccess   (profile_info:)     {Number}    gender                            Giới tính
@apiSuccess   (profile_info:)     {Array}     address                           Địa chỉ
@apiSuccess   (profile_info:)     {Number}    province_code                     Mã tỉnh thành
@apiSuccess   (profile_info:)     {Number}    district_code                     Mã quận huyện
@apiSuccess   (profile_info:)     {Number}    ward_code                         Mã phường xã
@apiSuccess   (profile_info:)     {Number}    marital_status                    Tình trạng hôn nhân
@apiSuccess   (profile_info:)     {String}    birthday                          Ngày sinh
@apiSuccess   (profile_info:)     {String}    hobby                             Mảng sở thích
@apiSuccess   (profile_info:)     {Number}    income                            Mức thu nhập
@apiSuccess   (profile_info:)     {Number}    budget                            Mức tiêu dùng
@apiSuccess   (profile_info:)     {Object}    social_user                             Thông tin người dùng trên mạng xã hội

@apiSuccess   (cards:)     {String}     id                               Id thẻ mẫu
@apiSuccess   (cards:)     {String}     code                             Mã thẻ
@apiSuccess   (cards:)     {Number}     status                           Trạng thái thẻ

@apiSuccess   (social_user:) {String}  id_social                                  Mã người dùng trên mạng xã hội
@apiSuccess   (social_user:) {Integer} social_type                                Kiểu mạng xã hội. Giá trị: 1-Facebook, 2-Zalo, 3-Instagram, 4-Youtube 

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "address": [], 
      "answers": [], 
      "association": [], 
      "avatar": null, 
      "relationship_data": [{"profile_id": "71199f63-1870-41fa-995d-b8534cd182f6", "relationship_type":1}]
      "avg_monthly_consumption": null, 
      "avg_rate": null, 
      "birthday": null, 
      "budget_high_threshold": null, 
      "budget_low_threshold": null, 
      "company": null, 
      "created_account_type": 0, 
      "created_time": "2019-03-27T17:52:46Z", 
      "description": null, 
      "district_code": null, 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "face_id": [], 
      "frequently_demands": [], 
      "gender": 2, 
      "hobby": [], 
      "income_high_threshold": null, 
      "income_low_threshold": null, 
      "income_type": null, 
      "is_non_profile": false, 
      "job": null, 
      "last_time_active": null, 
      "last_time_transaction": null, 
      "lat": null, 
      "location": null, 
      "lon": null, 
      "marital_status": null, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "Giang", 
      "nation": null, 
      "number_interactive": null, 
      "number_transaction": null,
      "number_childs": 1,
      "childs": [
        {
          "name": 'con trail',
          "gender": 2,
          "birthday": "2000-05-20",
          "nth": 1
        }
      ], 
      "operation": null, 
      "people_id": null, 
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "position": null, 
      "predict": [], 
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      }, 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "province_code": null, 
      "religiousness": null, 
      "salary": null, 
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "social_user": [], 
      "source_id": null, 
      "source_type": null, 
      "state": null, 
      "tags": [], 
      "total_rate": null, 
      "updated_time": "2019-03-27T17:52:46Z", 
      "ward_code": null, 
      "website": null, 
      "workplace": null
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

******************************** Social Upsert Profile ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {post} [HOST]/profiling/v3.0/profile/actions/social/upsert Social Upsert Profile.
@apiDescription Social Upsert Profile
@apiGroup Profile
@apiVersion 1.0.0
@apiName SocialUpsertProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.

@apiParam   (Body:)    {Object}         social_user        Thông tin xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)     {Number}    created_account_type                    Nguồn tạo tài khoản.
<li><code>2: FACEBOOK</code></li>
<li><code>6: ZALO</code></li>
<li><code>7: INSTAGRAM</code></li>
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (Body:)    {String}          [avatar]             Ảnh đại diện.
@apiParam   (Body:)    {Array}          [email]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {Array}          [phone_number]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {String}          [address]             Địa chỉ.
@apiParam   (Body:)    {String}          [birthday]             Ngày sinh.

@apiParam   (Social_User:)    {String}         social_id        Id của mạng mạng xã hội.
@apiParam   (Social_User:)    {String}          social_name             Tên Social của Profile.
@apiParam   (Social_User:)    {Int}            social_type      Mạng xã hội.
<li><code>1: FACEBOOK</code></li>
<li><code>2: ZALO</code></li>
<li><code>3: INSTAGRAM</code></li>
<li><code>4: YOUTUBE</code></li>
@apiParamExample {json} SocialUser-Example:
{"social_id": "*********", "social_type": 1, "social_name": "andrew"}

@apiParamExample [json] Body example:
{
  "created_account_type": 2,
  "gender": 2,
  "social_user": {"social_id": "*********", "social_type": 1, "social_name": "andrew"},
  "avatar: "https://fb.com/images/avatar.jpg"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Tạo khách hàng thành công.", 
    "process_type": "add", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "avatar": null, 
      "birthday": null, 
      "created_account_type": 2, 
      "created_time": "2019-03-27T17:52:46Z", 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "gender": 2, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "andrew",
      "social_name": [{"id":1,"name": "andrew"}],
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "social_user": [{"social_id": "*********", "social_type": 1}], 
      "updated_time": "2019-03-27T17:52:46Z", 
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

******************************** Social Update Profile ********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {put} [HOST]/profiling/v3.0/profile/actions/social/update/<profile_id> Social Update Profile.
@apiDescription Social Update Profile
@apiGroup Profile
@apiVersion 1.0.0
@apiName SocialUpdateProfile
@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiParam   (Query:)    {String}  [business]     Chuỗi query nhận diện campaign của Merchant.

@apiParam   (Body:)    {Array}      [email]              Danh sách các email mà profile này sở hữu. Example: ["<EMAIL>", "<EMAIL>"]
@apiParam   (Body:)    {Array}      [phone_number]       Danh sách các số điện thoại mà profile này sở hữu. Example: ["+***********"]
@apiParam   (Body:)    {Object}         [social_user]        Thông tin xã hội mà profile này sở hữu. Xem Data_Social_User bên dưới.
@apiParam   (Body:)     {Number=1:Unknown 2:Nam 3:Nữ}    [gender]             Giới tính.
@apiParam   (Body:)    {String}          [avatar]             Ảnh đại diện.
@apiParam   (Body:)    {Array}          [email]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {Array}          [phone_number]             Danh sách email thuộc sở hữu của Profile.
@apiParam   (Body:)    {String}          [address]             Địa chỉ.
@apiParam   (Body:)    {String}          [birthday]             Ngày sinh.

@apiParamExample {json} SocialUser-Example:
{"social_id": "*********", "social_type": 1, "social_name": "andrew"}

@apiParamExample [json] Body example:
{
  "gender": 2,
  "phone_number": ["**********"],
  "email": ["<EMAIL>"],
  "social_user": {"social_id": "*********", "social_type": 1, "social_name": "andrew"},
  "avatar: "https://fb.com/images/avatar.jpg"
}

@apiSuccessExample json Response: HTTP/1.1 200 OK
{
  "code": 200, 
  "data": {
    "cards": null, 
    "message": "Cập nhật khách hàng thành công.", 
    "process_type": "update", 
    "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
    "profile_info": {
      "avatar": null, 
      "birthday": null, 
      "created_account_type": 2, 
      "created_time": "2019-03-27T17:52:46Z", 
      "email": ['<EMAIL>', '<EMAIL>'], 
      "gender": 2, 
      "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924", 
      "name": "andrew", 
      "social_name": [{"id":1,"name": "andrew"}],
      "phone_number": [
        "+***********",
        "+***********"
      ], 
      "profile_id": "70088f63-1870-41fa-995d-b8534cd128f8", 
      "social_user": [{"social_id": "*********", "social_type": 1}], 
      "updated_time": "2019-03-27T17:52:46Z", 
    }
  }, 
  "lang": "vi", 
  "message": "request thành công."
}
"""

****************************** List Customer **********************************
* version: 1.0.4                                                              *
* version: 1.0.3                                                              *
* version: 1.0.2                                                              *
* version: 1.0.1                                                              *
* version: 1.0.0                                                              *
*******************************************************************************
"""
@api {post} https://dev.mobio.vn/profiling/v3.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.4
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>
@apiParam      (Body:)     {Array}     [fields]               Danh sách các thuộc tính cần trả ra. Ví dụ: ["profile_id","name","birthday"]
@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "fields": ["profile_id","name"],
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {Array}        phone_number                  Mảng điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán
@apiSuccess  (data)      {Array}         profile_address              Mảng địa chỉ của user
@apiSuccess  (data)      {Object}        primary_email                Email chính của user
@apiSuccess  (data)      {Object}        secondary_emails             Các email phụ của user
@apiSuccess  (data)      {Object}        primary_phone                Điện thoại chính của user
@apiSuccess  (data)      {Object}        secondary_phones             Các số điện thoại phụ của user
@apiSuccess  (data)      {Number}        degree                       Thông tin trình độ học vấn của profile
@apiSuccess  (data)      {Number}        income_family                Thông tin thu nhập gia đình
@apiSuccess  (data)      {Number}        number_childs                Thông tin số lượng con của user
@apiSuccess  (data)      {Array}         childs                       Thông tin con của user
@apiSuccess  (data)      {Array}         transaction_event            Thông tin giao dịch của user
@apiSuccess  (data)      {Array}         answers                      Mảng câu trả lời của user
@apiSuccess  (data)      {Number}        created_account_type         Nguồn ghi nhận thông tin khách hàng
@apiSuccess  (data)      {Number}        salary                       Mức lương của user
@apiSuccess  (data)      {String}        workplace                    Nơi làm việc
@apiSuccess  (data)      {String}        position                     Chức vụ của user
@apiSuccess  (data)      {Array}         tags                         Tag tìm kiếm

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=1:Pending 2:Approval 3:Cancelled}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": ['<EMAIL>', '<EMAIL>'],
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": ["+************"],
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ],
      "primary_email": {
        "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "<EMAIL>", 
        "status": 0
      }, 
      "primary_phone": {
        "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
        "phone_number": "+***********", 
        "status": 0
      },
      "secondary_emails": {
        "secondary": [
          {
            "last_check": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "<EMAIL>", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      }, 
      "secondary_phones": {
        "secondary": [
          {
            "last_verify": "Wed, 27 Mar 2019 17:52:46 GMT", 
            "phone_number": "+***********", 
            "status": 0
          }
        ], 
        "secondary_size": 1
      },
      "profile_address": [
        "dia chi 1",
        "dia chi 2"
      ],
      "tags": [],
      "degree": 1,
      "income_family": 0,
      "number_childs": 0
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
*********
"""
@api {post} https://dev.mobio.vn/profiling/v3.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.3
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging_tokens

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=1:Pending 2:Approval 3:Cancelled}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": "['<EMAIL>', '<EMAIL>']",
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": "+************",
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ]
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
*********
"""
@api {post} https://dev.mobio.vn/profiling/v3.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.2
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiParam      (Query:)     {String}    [sort]   Key name trường thông tin cần sắp xếp:<br />
<li><code>name</code>: Sắp xếp theo họ tên</li>
<li><code>gender</code>: Sắp xếp theo giới tính</li>
<li><code>age</code>: Sắp xếp theo độ tuổi</li>
<li><code>email_1</code>: Sắp xếp theo email 1</li>
@apiParam      (Query:)     {String=asc,desc}    [order]   Sắp xếp theo chiều:<br />
<li><code>asc</code>: tăng dần</li>
<li><code>desc</code>: giảm dần</li>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=1:Pending 2:Approval 3:Cancelled}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": "['<EMAIL>', '<EMAIL>']",
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": "+************",
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ]
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
*********
"""
@api {post} https://dev.mobio.vn/profiling/v3.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.1
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Number}        operation                    Lĩnh vực công tác
<br/><br/>Allowed values:<br/>
<li><code> 1: An ninh - Bảo vệ</code></li>
<li><code> 2: Báo chí - Truyền hình</code></li>
<li><code> 3: Bảo hiểm</code></li>
<li><code> 4: Phiên dịch</code></li>
<li><code> 5: Phim</code></li>
<li><code> 6: Cơ khí chế tạo máy</code></li>
<li><code> 7: thế thao</code></li>
<li><code> 8: Y tế</code></li>
<li><code> 9: Giáo dục</code></li>
<li><code> 10: Ẩm thực</code></li>
<li><code> 11: Khoa học - Kỹ thuật</code></li>
<li><code> 12: Địa chất</code></li>
<li><code> 13: Môi trường</code></li>
<li><code> 14: Nông nghiệp</code></li>
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 
@apiSuccess  (data)      {Array}         cards                        Mảng Đối tượng thẻ của khách hàng
@apiSuccess  (data)      {Array}         predict                      Mảng các field được phân tích dự đoán

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccess  (cards)      {String}         card_name                        Tên thẻ
@apiSuccess  (cards)      {String}         code                             Mã thẻ
@apiSuccess  (cards)      {Number=1:Pending 2:Approval 3:Cancelled}         status                           Trạng thái thẻ

@apiSuccess  (predict)    {String}         key                      Key predict.
@apiSuccess  (predict)    {String}         value                    Giá trị dự đoán.
@apiSuccess  (predict)    {String}         confident                Độ tin cậy

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": "['<EMAIL>', '<EMAIL>']",
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": "+************",
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null,
      "cards": [
        "id": "c6100085-f93d-4f0e-bf38-fec66bc64375",
        "code": "124364",
        "status": 1,
        "user_card_id": "58cda3a9-e0a6-4b4f-95b1-8062678c9304"
      ]
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""
************

"""
@api {post} https://dev.mobio.vn/profiling/v3.0/merchants/<merchant_id>/customers/actions/list [Done] Lấy danh sách khách hàng
@apiDescription Dịch vụ lấy danh sách khách hàng theo các điều kiện filter
@apiGroup Customers
@apiVersion 1.0.0
@apiName ListCustomer

@apiHeader (Headers:) {String} Content-Type <code>application/json</code>
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiParam      (Resource:)    {String}    merchant_id                             ID của merchant.

@apiParam      (Body:)     {String}    search                             Chuỗi tìm kiếm. Tìm theo <code>phone_number</code>, <code>name</code> hoặc <code>email</code>.
@apiParam      (Body:)     {Array}     [profile_filter]       Danh sách điều kiện filter. Xem chi tiết array <code>profile_filter</code>

@apiUse critetia_key_body
@apiUse operator_key_body
@apiParam      (profile_filter:)     {Array}     values                           Mảng các giá trị filter

@apiParamExample    {json}    Body example:
{
  "search": "search_str",
  "profile_filters": [
    {
      "criteria_key": "cri_age",
      "operator_key": "op_is_between",
      "values": [
        18,
        50
      ]
    },
    {
      "criteria_key": "cri_member_join",
      "operator_key": "op_is_between",
      "values": [
        "2017-01-01",
        "2017-09-30"
      ]
    }
  ]
}

@apiSuccess       {Array}                                  data              Mảng danh sách cấu hình thông báo của user
@apiSuccess  (data)      {String}        id                          Id của user
@apiSuccess  (data)      {Number}        mkt_step                        Trạng thái tài khoản. <br/>
<li><code>1: Khách hàng tiềm năng của hệ thống</code>.</li>
<li><code>2: Khách hàng đã có đủ thông tin</code>.</li>
<li><code>3: Chờ sale</code>.</li>
@apiSuccess  (data)      {String}        name                          Tên khách hàng
@apiSuccess  (data)      {String}        phone_number                  Số điện thoại của khách hàng
@apiSuccess  (data)      {Array}         email                         Mảng email của khách hàng
@apiSuccess  (data)       {String}        avatar                       Đường dẫn ảnh đại diện của khách hàng
@apiSuccess  (data)       {DateTime}      created_time                 Thời điểm tạo. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {DateTime}      updated_time                 Thời điểm cập nhật. Ví dụ: 2017-08-07T04:02:28.002Z
@apiSuccess  (data)       {String}        birthday                     Ngày sinh. Format: <code>dd/MM/yyyy</code>
@apiSuccess  (data)       {Number}        gender                       Giới tính
@apiSuccess  (data)       {String}        address                      Địa chỉ
@apiSuccess  (data)       {Number}        province_code                Mã tỉnh thành
@apiSuccess  (data)       {Number}        district_code                Mã quận huyện
@apiSuccess  (data)       {Number}        ward_code                   Mã phường xã
@apiSuccess  (data)       {Number}        marital_status               Tình trạng hôn nhân.
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess  (data)      {Number}        income_low_threshold         Ngưỡng thu nhập thấp.
@apiSuccess  (data)      {Number}        income_high_threshold        Ngưỡng thu nhập cao.
@apiSuccess  (data)      {Number=1:VNĐ 2:USD}    income_unit          Loại tiền.
@apiSuccess  (data)      {Number}        religiousness                Tôn giáo
@apiSuccess  (data)      {Number}        nation                       Dân tộc.
@apiSuccess  (data)      {Number}        job                          Nghề nghiệp.
@apiSuccess  (data)      {Array}         hobby                        Sở thích.
@apiSuccess  (data)      {String}        location                     Vị trí.
@apiSuccess  (data)      {String}        people_id                    Chứng minh thư.
@apiSuccess  (data)      {Array}         frequently_demands           Mảng id nhu cầu thường xuyên của khách hàng.
@apiSuccess  (data)      {Array}         social_user                  Mảng Đối tượng social chứa thông tin về mạng xã hội của user. 

@apiSuccess (social_user)       {String}     id_social         ID của mạng xã hội đăng nhập
@apiSuccess (social_user)       {Number}     social_type       Loại mạng xã hội<br/>
Allowed values:<br/>
<li><code>1:Facebook</code></li>
<li><code>2:Zalo</code></li>
<li><code>3:Instagram</code></li>
<li><code>4:GooglePlus</code></li>
@apiSuccess (social_user)       {String}     access_token      Access token để truy cập social.

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "data": [
    {
      "address": "Tỉnh Thanh Hóa",
      "answers": [],
      "avatar": null,
      "birthday": "1981-07-28T00:00:00Z",
      "business_case_id": "8a47ece4-4556-4f01-b29b-97767c42471f",
      "created_account_type": 0,
      "created_time": "2018-07-27T09:53:21Z",
      "display_name": "Vũ Thị thọ 123",
      "district_code": null,
      "email": "['<EMAIL>', '<EMAIL>']",
      "frequently_demands": [
        {
          "id": 2,
          "name": "Mua sắm online"
        }
      ],
      "gender": 3,
      "hobby": null,
      "id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "income_high_threshold": ********,
      "income_low_threshold": 5000000,
      "income_type": 1,
      "job": null,
      "location": null,
      "marital_status": null,
      "merchant_id": "618261e0-dee6-4440-a744-89f67235b347",
      "mkt_step": 1,
      "nation": null,
      "people_id": null,
      "phone_number": "+************",
      "position": null,
      "predict": [
        {
          "key": "phone_number",
          "value": "+************",
          "confidence": 0.****************
        }
      ],
      "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "province_code": 38,
      "religiousness": null,
      "salary": null,
      "social_user": [
        {
          "id_social": "2139374173003427",
          "social_type": 1
        }
      ],
      "trusted_level": 100,
      "updated_time": "2018-07-28T04:57:35Z",
      "validation": 32,
      "verify_status": null,
      "ward_code": null,
      "workplace": null
    },
    ...
  ],
  "paging": {
    ...
  }
}
"""

#######################################################
##Thêm khách hàng ban đầu
##Version: 1.0.0
##Version: 1.0.1
#######################################################
"""
@api {post} [host]/loyalty/api/v2.0/profile/intialization/users Thêm danh sách khách hàng
@apiGroup Profile
@apiVersion 1.0.1
@apiName IntializationUsers

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Headers:)   {string}   X-Merchant-ID   ID Của tenant .
@apiParam   (Body:)   {Array}   data_profile   Danh sách thông tin khách hàng.
@apiParam   (data_profile)   {string}   phone_number   Số điện thoại khách hàng.
@apiParam   (data_profile)   {string}   email          Email khách hàng.
@apiParam   (data_profile)   {string}   fullname       Họ và tên khách hàng.
@apiParam   (data_profile)   {string}   profile_id     Mã khách hàng.
@apiParam   (data_profile)   {string}   address        Địa chỉ khách hàng.
@apiParam   (data_profile)   {string}   card_id        Id hạng thẻ của khách hàng.
@apiParam   (data_profile)   {string}   code           Mã thẻ khách hàng.
@apiParam   (data_profile)   {integer}  point          Số điểm hiện tại của khách hàng.
@apiParam   (data_profile)   {datetime} birthday       Ngày sinh của khách hàng.
@apiParam   (data_profile)   {integer}  gender         Giới tính của khách hàng 
                                                        <li>1: UNKNOWN </li>
                                                        <li>2: MALE </li>
                                                        <li>3: FEMALE. </li>
@apiParam   (data_profile)   {object}   point_expired  Danh sách thông tin thời hạn điểm của người dùng.
@apiParam   (data_profile)  {integer}   point_expired.point  Số điểm có thời hạn của ngươi dùng
@apiParam   (data_profile)  {integer}   point_expired.point_expired_date  Thời gian hết hạn điểm.

@apiParamExample  {json}  Example
{
  "data_profile": [
    {
      "phone_number": "+84989656365",
      "email": "<EMAIL>",
      "fullname": "Nguyễn Mạnh Quỳnh",
      "profile_id": "4cd1b1d5-ff73-4394-b462-375b55d3c08d",
      "address": "82 Duy tân, Cầu giấy, Hà nội",
      "card_id": "47957ee2-5e21-4db2-ae36-093cf8bda62c",
      "code":"1536528789654",
      "birthday": "1993-10-10T00:00:00",
      "gender":1,
      "point": 8455,
      "point_expired": [{
        "point": 20,
        "point_expired_date": "2020-08-23"
         }
        ]
    },
    {}
  ]
}

@apiSuccess   (Data:)  {stringArray}  data          Danh sách kết quả khách hàng.
@apiSuccess   (data)   {string}       phone_number  Số điện thoại khách hàng
@apiSuccess   (data)   {string}       profile_id    Id khách hàng
@apiSuccess   (data)   {integer}      status        Kết quả xử lý <code> 1 : thành công , 0 : thất bại</code>
@apiSuccess   (data)   {string}       content        Nội dung xử lý
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
        "phone_number": "+84989656365",
        "profile_id": "4cd1b1d5-ff73-4394-b462-375b55d3c08d",
        "status": 1,
        "content": "Xử lý thành công"
    },
    ...
  ]
}
"""


"""
@api {post} /profile/intialization/users Thêm danh sách khách hàng
@apiGroup Profile
@apiVersion 1.0.0
@apiName IntializationUsers

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {Array}   data_profile   Danh sách thông tin khách hàng.
@apiParam   (data_profile)   {string}   phone_number   Số điện thoại khách hàng.
@apiParam   (data_profile)   {string}   email          Email khách hàng.
@apiParam   (data_profile)   {string}   fullname       Họ và tên khách hàng.
@apiParam   (data_profile)   {string}   profile_id     Mã khách hàng.
@apiParam   (data_profile)   {string}   address        Địa chỉ khách hàng.
@apiParam   (data_profile)   {string}   card_id        Id hạng thẻ của khách hàng.
@apiParam   (data_profile)   {string}   code           Mã thẻ khách hàng.
@apiParam   (data_profile)   {integer}  point          Số điểm hiện tại của khách hàng.
@apiParam   (data_profile)   {integer}  point          Số điểm hiện tại của khách hàng.
@apiParam   (data_profile)   {object}   point_expired  Danh sách thông tin thời hạn điểm của người dùng.
@apiParam   (data_profile)  {integer}   point_expired.point  Số điểm có thời hạn của ngươi dùng
@apiParam   (data_profile)  {integer}   point_expired.point_expired_date  Thời gian hết hạn điểm.

@apiParamExample  {json}  Example
{
  "data_profile": [
    {
      "phone_number": "+84989656365",
      "email": "<EMAIL>",
      "fullname": "Nguyễn Mạnh Quỳnh",
      "profile_id": "4cd1b1d5-ff73-4394-b462-375b55d3c08d",
      "address": "82 Duy tân, Cầu giấy, Hà nội",
      "card_id": "47957ee2-5e21-4db2-ae36-093cf8bda62c",
      "code":"1536528789654",
      "point": 8455,
      "point_expired": [{
        "point": 20,
        "point_expired_date": "2020-08-23"
         }
        ]
    },
    {}
  ]
}

@apiSuccess   (Data:)  {stringArray}  data          Danh sách kết quả khách hàng.
@apiSuccess   (data)   {string}       phone_number  Số điện thoại khách hàng
@apiSuccess   (data)   {string}       profile_id    Id khách hàng
@apiSuccess   (data)   {integer}      status        Kết quả xử lý <code> 1 : thành công , 0 : thất bại</code>
@apiSuccess   (data)   {string}       content        Nội dung xử lý
@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
{
  "data": [
    {
        "phone_number": "+84989656365",
        "profile_id": "4cd1b1d5-ff73-4394-b462-375b55d3c08d",
        "status": 1,
        "content": "Xử lý thành công"
    },
    ...
  ]
}
"""

#######################################################
##Thêm mới giao dịch khách hàng
##Version: 1.0.0
#######################################################

"""
@api {post} [host]api/v2.0/transactions/upsert Thêm thông tin giao dịch ban đầu
@apiGroup Profile
@apiVersion 1.0.0
@apiName IntializationTransaction

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {Object}  profile   Thông tin profile thực hiện giao dịch. 
@apiParam   (Body:)   {Object}  transaction   Thông tin giao dịch
@apiParam   (Body:)   {String}  source  Nguồn phát sinh giao dịch. Example: offline, online, affilite

@apiParam   (Profile)   {String}  [phone_number]  Số điện thoại.
@apiParam   (Profile)   {String}  [email]  Địa chỉ thư điện tử.
@apiParam   (Profile)   {Object}  [third_party_info]    Thông tin thêm của profile trên hệ thống của tenant.
@apiParam   (Profile)   {string}  [third_party_info.id] Id profile của tennant.

@apiParam   (Transaction)   {String}  code  Mã giao dịch.
@apiParam   (Transaction)   {String}  action_type   Loại hành động giao dịch
                                                    <code> 
                                                        <ul> 
                                                            <li> buy: mua hàng </li> 
                                                            <li> returns: trả hàng </li> 
                                                        </ul>
                                                    </code>
@apiParam   (Transaction)   {float}   action_time   Thời gian giao dịch.
@apiParam   (Transaction)   {Number}  amount  Tổng số tiền giao dịch.
@apiParam   (Transaction)   {String=VND, USD}  currency_code   Đơn vị tiền tệ.
@apiParam   (Transaction)   {float}   status  Trạng thái giao dịch.
@apiParam   (Transaction)   {Item[]}  [items]  Danh sách item(sản phẩm) trong giao dịch.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>id<span class="label label-optional">optional</span></td>
      <td>String</td>
      <td>Định danh item trên hệ thống Mobio</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên của item</td>
    </tr>
    <tr>
      <td>tags<span class="label label-optional">optional</span></td>
      <td>StringArray</td>
    </tr>
    <tr>
      <td>quantity</td>
      <td>Number</td>
      <td>Số lượng item</td>
    </tr>
    <tr>
      <td>price</td>
      <td>Number</td>
      <td>Đơn giá của item (sản phẩm)</td>
    </tr>
    <tr>
      <td>total_amount</td>
      <td>Number</td>
      <td>tổng giá trị của item (đã nhân giá với số lượng và trừ các khoản khác (sử dụng voucher ...))</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã item (mã sản phẩm)</td>
    </tr>
    <tr>
        <td>supplier</td>
        <td>object </td>
        <td>Thông tin thương hiệu của sản phẩm 
            <table>
                <tr>
                    <td>code</td>
                    <td>string </td>
                    <td>Mã thương hiệu </td>
                </tr>
                <tr>
                    <td>name</td>
                    <td>string </td>
                    <td>Tên thương hiệu</td>
                </tr>
            </table>
        </td>
    </tr>
    
  </tbody>
</table>
@apiParam   (Transaction)   {Voucher[]}   [vouchers]  Danh sách voucher sử dụng trong lần giao dịch.
@apiParam   (Transaction)   {Object}  [store]   Thông tin cửa hàng thực hiện giao dịch.
@apiParam   (Transaction)   {Object}  [payment]   Thông tin thanh toán.
<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>type</td>
      <td>String</td>
      <td>Hình thức thanh toán: atm, cash</td>
    </tr>
    <tr>
      <td>name</td>
      <td>String</td>
      <td>Tên sở hữu hình thức thanh toán. Ví dụ: tên ngân hàng của hình thức ATM</td>
    </tr>
    <tr>
      <td>code</td>
      <td>String</td>
      <td>Mã tương ứng của hình thức thanh toán. Ví dụ: mã thẻ visa, mã thẻ master card</td>
    </tr>
  </tbody>
</table>

@apiParamExample  {json}  Example

{
  "profile": {
    "phone_number": "+84399285833",
    "email": "<EMAIL>",
    "third_party_info": {
      "id": "86a6d10d-7b19-46f0-bdf2-7daef3010680"
    }
  },
  "transaction" :{
    "code": "07e8c3db-a3bf-4508-8f79-297a31e2c02e",
    "action_type": "buy",
    "action_time": 1538703463.440775,
    "amount": 100000,
    "currency_code": "VND",
    "status": 0.0,
    "items": [
      {
        "id": "ea4d37b1-5522-4494-ac21-3a3012b2efb2",
        "name": "",
        "quantity": 2,
        "code": "SP001",
        "price": 100000,
        "total_amount": 200000,
        "supplier":{
            "code": "BG618T7X",
            "name": "TOHATO"
        }
      }
    ],
    "vouchers": [
      {
        "code": "AAIOWNDAFJ"
      }
    ],
    "store": {
      "code": "CH001"
    },
    "payment": {
      "type": "mc",
      "code": "866025029",
      "name": ""
    }
  },
  "source": "offline"
}


@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK
  
{
    "code": 200,
    "message": "Xử lý thành công" 
}
"""


#######################################################
## Cập nhật điểm cho khách hàng
##Version: 1.0.0
#######################################################

"""
@api {post} [host]api/v2.0/profile/actions/update/point  cập nhật điểm khách hàng
@apiGroup Profile
@apiVersion 1.0.0
@apiName UpdatePointProfile

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiParam   (Body:)   {Array}   data_profile           Danh sách thông tin khách hàng.
@apiParam   (data_profile)   {string}   profile_id     Mã khách hàng trên hệ thống tenant.
@apiParam   (data_profile)   {integer}  point          Số điểm hiện tại của khách hàng.
@apiParam   (data_profile)   {object}   point_expired  Danh sách thông tin thời hạn điểm của người dùng.
@apiParam   (data_profile)  {integer}   point_expired.point  Số điểm có thời hạn của ngươi dùng
@apiParam   (data_profile)  {integer}   point_expired.point_expired_date  Thời gian hết hạn điểm.

@apiParamExample  {json}  Example
{
  "data_profile": [
    {
      "profile_id": "RSKK000012897",
      "point": 146,
      "point_expired": [{
        "point": 20,
        "point_expired_date": "2020-08-23"
         }
        ]
    }
  ]
}



@apiSuccessExample   {json}  Response: HTTP/1.1 200 OK

{
    "code": 200,
    "message": "Xử lý thành công" 
}
"""
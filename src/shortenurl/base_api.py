#!/usr/bin/python
# -*- coding: utf8 -*-

# ============================================
# Danh sách phân trang shorten url 
# ============================================
"""
@api {GET} {domain}/shortenurl/api/v1.0/shorten-url-list Danh sách phân trang shorten url  
@apiDescription Danh sách phân trang shorten url 
@apiVersion 1.0.0
@apiGroup ShortenBase
@apiName ShortenList

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse paging

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam          (Query:) {String}        [text_search]   Chuỗi tìm kiếm theo link gốc, link shorten 

@apiSuccess {Array}   data    Danh sách 

@apiSuccess (data) {String}   short_url       Mã shorten url 
@apiSuccess (data) {Integer}   status         trạng thái
@apiSuccess (data) {String}   [expired_date]      ngày hết hạn UTC
@apiSuccess (data) {String}   full_shorten_url    link rút gọn 
@apiSuccess (data) {String}   merchant_id          Mã định danh merchant
@apiSuccess (data) {String}   short_domain       domain shorten             
@apiSuccess (data) {Integer}   source       nguồn tạo, 1: cms, 2: marketing              
@apiSuccess (data) {Integer}   session_click       số lượt click vào shorten url   
@apiSuccess (data) {String}   origin_url       link gốc  

@apiSuccessExample {json} Response: HTTP/1.1 200 OK
{
    "code": 200,
  "data": [
      {
        "expired_date": "20210930075810",           
        "full_shorten_url": "https://t1.mobio.vn/SU9aEHbWga",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "origin_url": "https://vnexpress.net/chu-tich-cuba-don-chu-tich-nuoc-nguyen-xuan-phuc-4359044.html",
        "short_domain": "https://t1.mobio.vn",
        "short_url": "SU9aEHbWga",
        "status": 1,
        "source": 1,    
        "session_click": 23
    }
  ],
  "paging": {
    "page": 1,
    "per_page": 20,
    "total_items": 20,
    "total_pages": 12
  }
  
}

@apiSuccessExample {json} Response error
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": ""
}

"""

# ============================================
# tạo shorten url cho link 
# ============================================
"""
@api {post} {domain}/shortenurl/api/v1.0/shorten-url Tạo shorten url cho link đầy đủ 
@apiDescription Tạo shorten url cho link đầy đủ 
@apiGroup ShortenBase
@apiVersion 1.0.0
@apiName ShortenCreate 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam   (Body)   {String}   short_domain      domain shorten của merchant 
@apiParam   (Body)   {String}   origin_url      link đầy đủ 
@apiParam   (Body)   {Integer}   [expire_day]   số ngày sử dụng link shorten từ thời điểm tạo, nếu ko truyền lên thì shorten url tạo ra sẽ không có hạn sử dụng 
@apiParam   (Body)   {Integer}   status   trạng thái, 1: hoạt động, 2: không hoạt động 

@apiParamExample {json} Body
{
    "short_domain":"https://t1.mobio.vn",
	"origin_url":"https://vnexpress.net/chu-tich-cuba-don-chu-tich-nuoc-nguyen-xuan-phuc-4359044.html",
	"expire_day":10,
	"status":1
}

@apiSuccess {Object}   data    Thông tin kết quả
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String}   short_url       Mã shorten url 
@apiSuccess (data) {Integer}   expire_day     số ngày sử dụng link shorten từ thời điểm tạo
@apiSuccess (data) {Integer}   status         trạng thái
@apiSuccess (data) {String}   [expired_date]      ngày hết hạn UTC
@apiSuccess (data) {String}   full_shorten_url    link rút gọn 
@apiSuccess (data) {String}   merchant_id          Mã định danh merchant
@apiSuccess (data) {String}   short_domain       domain shorten  
@apiSuccess (data) {String}   origin_url       link gốc             


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "expire_day": 10,
        "expired_date": "20210930075810",           
        "full_shorten_url": "https://t1.mobio.vn/SU9aEHbWga",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "origin_url": "https://vnexpress.net/chu-tich-cuba-don-chu-tich-nuoc-nguyen-xuan-phuc-4359044.html",
        "short_domain": "https://t1.mobio.vn",
        "short_url": "SU9aEHbWga",
        "status": 1
    },
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": ""
}

"""

# ============================================
# cập nhật shorten url  
# ============================================
"""
@api {put} {domain}/shortenurl/api/v1.0/shorten-url/<shorten_key> Cập nhật shorten url  
@apiDescription Cập nhật shorten url 
@apiGroup ShortenBase
@apiVersion 1.0.0
@apiName ShortenUpdate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam   (Body)   {String}   short_domain      domain shorten của merchant 
@apiParam   (Body)   {String}   origin_url      link đầy đủ 
@apiParam   (Body)   {Number}   [expire_day]   số ngày sử dụng link shorten từ thời điểm tạo, nếu ko truyền lên thì shorten url tạo ra sẽ không có hạn sử dụng 
@apiParam   (Body)   {Number}   status   trạng thái, 1: hoạt động, 2: không hoạt động 

@apiParamExample {json} Body
{
    "short_domain":"https://t1.mobio.vn",
	"origin_url":"https://vnexpress.net/viet-nam-mua-10-trieu-lieu-vaccine-abdala-4359164.html",
	"expire_day":20,
	"status":1
}


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "expire_day": 20,
        "expired_date": "20211010083032",
        "full_shorten_url": "https://t1.mobio.vn/SU9aEHbWga",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "origin_url": "https://vnexpress.net/viet-nam-mua-10-trieu-lieu-vaccine-abdala-4359164.html",
        "short_domain": "https://t1.mobio.vn",
        "short_url": "SU9aEHbWga",
        "status": 1
    },
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": ""
}

"""

# ============================================
# lấy chi tiết shorten url 
# ============================================
"""
@api {get} {domain}/shortenurl/api/v1.0/shorten-url Lấy chi tiết thông tin shorten url  
@apiDescription Lấy chi tiết thông tin shorten url 
@apiGroup ShortenBase
@apiVersion 1.0.0
@apiName ShortenDetail 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam          (Query:) {String}        short_url   mã shorten url 


@apiSuccess {Object}   data    Thông tin kết quả
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String}   short_url       Mã shorten url 
@apiSuccess (data) {Integer}   status         trạng thái
@apiSuccess (data) {String}   [expired_date]      ngày hết hạn UTC
@apiSuccess (data) {String}   full_shorten_url    link rút gọn 
@apiSuccess (data) {String}   merchant_id          Mã định danh merchant
@apiSuccess (data) {String}   short_domain       domain shorten             
@apiSuccess (data) {Integer}   source       nguồn tạo, 1: cms, 2: marketing              
@apiSuccess (data) {Integer}   session_click       số lượt click vào shorten url   
@apiSuccess (data) {String}   origin_url       link gốc             




@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "expired_date": "20210930075810",           
        "full_shorten_url": "https://t1.mobio.vn/SU9aEHbWga",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "origin_url": "https://vnexpress.net/chu-tich-cuba-don-chu-tich-nuoc-nguyen-xuan-phuc-4359044.html",
        "short_domain": "https://t1.mobio.vn",
        "short_url": "SU9aEHbWga",
        "status": 1,
        "source": 1,    
        "session_click": 23
    },
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": ""
}

"""

# ============================================
# marketing tạo 1 shorten 
# ============================================
"""
@api {post} {domain}/shortenurl/api/v1.0/shorten-url/mkt marketing tạo shorten url  
@apiDescription marketing tạo shorten url  
@apiGroup ShortenBase
@apiVersion 1.0.0
@apiName MKTShortenCreateSignle 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam   (Body)   {String}   [short_domain]      domain shorten của merchant, nếu không có thì sẽ lấy domain mặc định của merchant, nếu merchant ko cấu hình domain shorten nào thì báo lỗi không tạo được shorten   
@apiParam   (Body)   {String}   origin_url      link đầy đủ 
@apiParam   (Body)   {Integer}   [expire_day]   số ngày sử dụng link shorten từ thời điểm tạo, nếu ko truyền lên thì shorten url tạo ra sẽ không có hạn sử dụng 


@apiParamExample {json} Body
{
    "short_domain":"https://t1.mobio.vn",
	"origin_url":"https://vnexpress.net/chu-tich-cuba-don-chu-tich-nuoc-nguyen-xuan-phuc-4359044.html",
	"expire_day":10,
}

@apiSuccess {Object}   data    Thông tin kết quả
@apiSuccess {String}  message Mô tả phản hồi
@apiSuccess {Integer}  code    Mã phản hồi

@apiSuccess (data) {String}   short_url       Mã shorten url 
@apiSuccess (data) {Integer}   expire_day     số ngày sử dụng link shorten từ thời điểm tạo
@apiSuccess (data) {Integer}   status         trạng thái
@apiSuccess (data) {String}   [expired_date]      ngày hết hạn UTC
@apiSuccess (data) {String}   full_shorten_url    link rút gọn 
@apiSuccess (data) {String}   merchant_id          Mã định danh merchant
@apiSuccess (data) {String}   short_domain       domain shorten  
@apiSuccess (data) {String}   origin_url       link gốc             


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
    "code": 200,
    "data": {
        "expire_day": 10,
        "expired_date": "20210930075810",           
        "full_shorten_url": "https://t1.mobio.vn/SU9aEHbWga",
        "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
        "origin_url": "https://vnexpress.net/chu-tich-cuba-don-chu-tich-nuoc-nguyen-xuan-phuc-4359044.html",
        "short_domain": "https://t1.mobio.vn",
        "short_url": "SU9aEHbWga",
        "status": 1
    },
    "lang": "vi",
    "message": "request thành công."
}
@apiSuccessExample {json} Response error
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": ""
}

"""

# ============================================
# marketing tạo nhiều shorten 
# ============================================
"""
@api {post} {domain}/shortenurl/api/v1.0/shorten-url/mkt/bulk marketing tạo nhiều shorten url  
@apiDescription marketing tạo nhiều shorten url  
@apiGroup ShortenBase
@apiVersion 1.0.0
@apiName MKTShortenCreateMulti 

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang

@apiHeader (Headers:) {String} X-Merchant-ID Merchant ID.

@apiParam   (Body)   {String}   [short_domain]      domain shorten của merchant, nếu không có thì sẽ lấy domain mặc định của merchant, nếu merchant ko cấu hình domain shorten nào thì báo lỗi không tạo được shorten   
@apiParam   (Body)   {Array}   data      danh sách link cần shorten 
@apiParam   (Body)   {Integer}   [expire_day]   số ngày sử dụng link shorten từ thời điểm tạo, nếu ko truyền lên mặc định là 30 ngày 

@apiParam   (data)   {String}   origin_url      link đầy đủ cần shorten 

@apiParamExample {json} Body
{
    "short_domain": "https://t1.mobio.vn",
    "data": [
        {
            "origin_url": "https://vnexpress.net/chu-tich-cuba-don-chu-tich-nuoc-nguyen-xuan-phuc-4359044.html"
        },
        {
            "origin_url": "https://vnexpress.net/ly-do-australia-huy-hop-dong-tau-ngam-40-ty-usd-voi-phap-4359083.html"
        }
    ],
    "expire_day": 10,
}


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "data": {
    "data": [
      {
        "full_shorten_url": "https://t1.mobio.vn/SUUHQRwz8s",
        "origin_url": "https://vnexpress.net/chu-tich-cuba-don-chu-tich-nuoc-nguyen-xuan-phuc-4359044.html",
        "short_url": "SUUHQRwz8s"
      },
      {
        "full_shorten_url": "https://t1.mobio.vn/SUu8cNEqsD",
        "origin_url": "https://vnexpress.net/ly-do-australia-huy-hop-dong-tau-ngam-40-ty-usd-voi-phap-4359083.html",
        "short_url": "SUu8cNEqsD"
      }
    ],
    "expire_day": 10,
    "expired_date": "20210930092316",
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "short_domain": "https://t1.mobio.vn",
    "status": 1
  },
  "lang": "vi",
  "message": "request thành công."
}
@apiSuccessExample {json} Response error
{
  "code": 400,
  "errors": "bad_request",
  "lang": "vi",
  "message": ""
}

"""

# ============================================ SETUP UTM PARAMETER ==================================================
"""
@apiDefine ResponseDetailSetupUtmParameter

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của UTM parameter
@apiSuccess {String}            data.merchant_id                         Định danh của tennant
@apiSuccess {String}            data.parameter_id                        Định danh của UMT parameter
@apiSuccess {String}            data.utm_name                            Tên của UTM parameter
@apiSuccess {String}            data.utm_key                             Key của UTM paramter
@apiSuccess {List}              data.utm_default                         Danh sách giá trị của UTM parameter mặc định của hệ thống
@apiSuccess {List}              data.utm_value                           Danh sách giá trị của UTM parameter
@apiSuccess {String}            data.status                             Trạng thái của UTM parameter, nhận giá trị <code>active, inactive</code>
@apiSuccess {String}            data.allow_delete                       Định danh domain được phép xóa hay không,nhận giá trị <code>allow, not_allow</code>
@apiSuccess {String}            data.edit_status                        Định danh việc có được sửa status của UTM paramter không, nhận giá tri <code>allow, not_allow</code>

@apiSuccess {String}            data.create_on                          Thời điểm khởi tạo
@apiSuccess {String}            data.update_on                          Thời điểm cập nhật

"""

# ----------------------- Create UTM parameter -----------------------
"""
@api {POST} {domain}/shortenurl/api/v1.0/utms/parameters                Tạo UTM parameter
@apiGroup UTM parameter
@apiDescription Tạo mới UTM parameter
@apiVersion 1.0.0
@apiName CreateUtmParameter
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}            utm_name                 Tên của UTM parameter
@apiParam   (BODY:)     {String}            utm_key                  Key của UTM parameter
@apiParam   (BODY:)     {List}              utm_value                Giá trị của UTM parameter


@apiParamExample    {json}      BODY:
{
    "utm_name": "Tên tham số UTM"
    "utm_key": "Key tham số UTM"
    "utm_value": ["string"]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm UTM parameter

@apiUse ResponseDetailSetupUtmParameter

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "id",
        "parameter_id": "parameter_id",
        "merchant_id": "merchant_id",        
        "utm_name": "utm name" 
        "utm_key": "utm key" 
        "utm_default": ["string value default"],        
        "utm_value": ["string"],     
        "status": "active",     
        "allow_delete": "allow",     
        "edit_status": "allow",     
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Update UTM parameter  -----------------------
"""
@api {PATCH} {domain}/shortenurl/api/v1.0/utms/parameters                    Cập nhật UTM parameter
@apiGroup UTM parameter
@apiDescription Sửa UTM parameter
@apiVersion 1.0.0
@apiName UpdateUtmParameter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)     {String}           utm_key                  Key của utm cần sửa

@apiParam   (BODY:)     {String}            utm_name                 Tên của UTM parameter
@apiParam   (BODY:)     {List}              utm_value                Giá trị của UTM parameter


@apiParamExample    {json}      BODY:
{
    "utm_name": "Tên tham số UTM"
    "utm_value": ["string"]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa UTM parameter 

@apiUse ResponseDetailSetupUtmParameter

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "id",
        "parameter_id": "parameter_id",
        "merchant_id": "merchant_id",        
        "utm_name": "utm name" 
        "utm_key": "utm key" 
        "utm_default": ["string value default"],        
        "utm_value": ["string"],     
        "status": "active",     
        "allow_delete": "allow",     
        "edit_status": "allow",     
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Delete UTM parameter  -----------------------
"""
@api {DELETE} {domain}/shortenurl/api/v1.0/utms/parameters                   Xóa UTM parameter
@apiGroup UTM parameter
@apiDescription Xóa UTM parameter
@apiVersion 1.0.0
@apiName DeleteUtmParameter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              utm_key        Key của utm cần xóa


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa UTM parameter


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Get list UTM parameter -----------------------
"""
@api {GET} {domain}/shortenurl/api/v1.0/utms/parameters/actions/list              Lấy danh sách UTM parameter
@apiGroup UTM parameter
@apiDescription Lấy danh sách UTM parameter
@apiVersion 1.0.0
@apiName GetListUtmParameter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              search         Tên tham số cần tìm kiếm
@apiParam   (Query:)     {String}              active         Trạng thái của UTM parameter, nhận giá trị <code>active, inactive</code>

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách UTM parameter

@apiUse ResponseDetailSetupUtmParameter

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "_id": "id",
        "parameter_id": "parameter_id",
        "merchant_id": "merchant_id",        
        "utm_name": "utm name" 
        "utm_key": "utm key" 
        "utm_default": ["string value default"],        
        "utm_value": ["string"],     
        "status": "active",     
        "allow_delete": "allow",     
        "edit_status": "allow",     
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}


"""
# ----------------------- Get detail UTM parameter  -----------------------
"""
@api {GET} {domain}/shortenurl/api/v1.0/utms/parameters                   Lấy chi tiết UTM parameter
@apiGroup UTM parameter
@apiDescription Lấy chi tíết UTM parameter
@apiVersion 1.0.0
@apiName GetDetailUtmParameter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              utm_key        Key của utm cần lấy thông tin chi tiết

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết thông tin parameter

@apiUse ResponseDetailSetupUtmParameter

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "id",
        "parameter_id": "parameter_id",
        "merchant_id": "merchant_id",        
        "utm_name": "utm name" 
        "utm_key": "utm key" 
        "utm_default": ["string value default"],        
        "utm_value": ["string"],     
        "status": "active",     
        "allow_delete": "allow",     
        "edit_status": "allow",     
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }
}
"""
# ----------------------- Activate UTM parameter  -----------------------
"""
@api {POST} {domain}/shortenurl/api/v1.0/utms/parameters/actions/activate                   Kích hoạt UTM parameter
@apiGroup UTM parameter
@apiDescription Kích hoạt UTM parameter
@apiVersion 1.0.0
@apiName ActiveUtmParameter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              utm_key        Key của utm cần active

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Kích hoạt UTM parameter

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}
"""
# ----------------------- Deactivate UTM parameter  -----------------------
"""
@api {POST} {domain}/shortenurl/api/v1.0/utms/parameters/actions/deactivate                   Bỏ kích hoạt UTM parameter
@apiGroup UTM parameter
@apiDescription Bỏ kích hoạt UTM parameter
@apiVersion 1.0.0
@apiName InactiveUtmParameter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              utm_key        Key của utm cần inactive

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Bỏ kích hoạt UTM parameter

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}
"""
# ----------------------- Deactivate UTM parameter  -----------------------
"""
@api {GET} {domain}/shortenurl/api/v1.0/utms/parameters/actions/check-is-create                   Kiểm tra có được phép tạo tham số UTM
@apiGroup UTM parameter
@apiDescription Kiểm tra có được phép tạo tham số UTM
@apiVersion 1.0.0
@apiName CheckIsCreateUtmParameter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Kiểm tra có được phép tạo tham số UTM

@apiSuccess {String}            data.is_create                <code>true:Được-tạo, false:Không-đươc-tạo</code>

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "is_create": true
    }
}
"""

# ============================================ UTM TEMPLATE =========================================================
"""
@apiDefine ResponseDetailUtmTemplate

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của UTM template
@apiSuccess {String}            data.merchant_id                         Định danh của tennant
@apiSuccess {String}            data.template_id                         Định danh của UTM template
@apiSuccess {String}            data.template_name                       Tên của UMT template
@apiSuccess {Dictionary}        data.template_value                      Dictionary với key là utm_key và value là utm_value
@apiSuccess {String}            data.template_value.utm_key              Key của tham số UTM
@apiSuccess {String}            data.template_value.utm_value            Value của tham số UTM
@apiSuccess {String}            data.template_value.utm_name             Tên của tham số UTM
@apiSuccess {String}            data.template_value.status               Trạng thái của tham số, giá trị <code>active, inactive</code>
@apiSuccess {String}            data.template_value.allow_delete         Định danh domain được phép xóa hay không,nhận giá trị <code>allow, not_allow</code>
@apiSuccess {String}            data.template_value.edit_status          Định danh việc có được sửa status của UTM paramter không, nhận giá tri <code>allow, not_allow</code>

@apiSuccess {String}            data.create_on                          Thời điểm khởi tạo
@apiSuccess {String}            data.update_on                          Thời điểm cập nhật

"""

# ----------------------- Create UTM template -----------------------
"""
@api {POST} {domain}/shortenurl/api/v1.0/utms/templates                Tạo UTM template
@apiGroup UTM template
@apiDescription Tạo mới UTM template
@apiVersion 1.0.0
@apiName CreateUtmTemplate
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}                template_name                 Tên của UTM template
@apiParam   (BODY:)     {Dictionary}            template_value                Dictionary với key là utm_key và value là utm_value


@apiParamExample    {json}      BODY:
{
    "template_name": "Tên UTM template",
    "template_value": [{
            "utm_campaign": "Campaign 1"
        },
        {
            "utm_term": "Term 1"
        }]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm UTM template

@apiUse ResponseDetailUtmTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "id",
        "merchant_id": "merchant_id",        
        "template_id": "template_id", 
        "template_name": "Tên UTM template", 
        "template_value": [{
            "utm_key": "utm_campaign",
            "utm_value": "campaign_journey_id",
            "utm_name": "Campaign",
            "status": "active",
            "edit_status": "allow",     
            "allow_delete": "allow",   
        }],      
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Update UTM template  -----------------------
"""
@api {PATCH} {domain}/shortenurl/api/v1.0/utms/templates/<template_id>                    Câp nhật UTM template
@apiGroup UTM template
@apiDescription Sửa UTM template
@apiVersion 1.0.0
@apiName UpdateUtmTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (RESOURCE:)     {String}           template_id              Định danh của UTM template

@apiParam   (BODY:)     {String}          template_name                 Tên của UTM template
@apiParam   (BODY:)     {Dictionary}      template_value                Dictionary với key là utm_key và value là utm_value


@apiParamExample    {json}      BODY:
{
    "template_name": "Tên UTM template",
    "template_value": [{
            "utm_campaign": "Campaign 1"
        },
        {
            "utm_term": "Term 1"
        }]
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa UTM template 

@apiUse ResponseDetailUtmTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "id",
        "merchant_id": "merchant_id",        
        "template_id": "template_id", 
        "template_name": "Tên UTM template", 
        "template_value": [{
            "utm_key": "utm_campaign",
            "utm_value": "campaign_journey_id",
            "utm_name": "Campaign",
            "status": "active",
            "edit_status": "allow",     
            "allow_delete": "allow",     
        }],      
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Delete UTM template  -----------------------
"""
@api {DELETE} {domain}/shortenurl/api/v1.0/utms/templates/<template_id>                   Xóa UTM template
@apiGroup UTM template
@apiDescription Xóa UTM template theo template_id
@apiVersion 1.0.0
@apiName DeleteUtmTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (RESOURCE:)     {String}           template_id              Định danh của UTM template


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa UTM template


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Delete list UTM template  -----------------------
"""
@api {POST} {domain}/shortenurl/api/v1.0/utms/templates/actions/delete-list                   Xóa danh sách UTM template
@apiGroup UTM template
@apiDescription Xóa UTM template theo danh sách template_id
@apiVersion 1.0.0
@apiName DeleteListUtmTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {List}        ids                   Danh sách template_id cần xóa  

@apiParamExample    {json}      BODY:
{
    "ids": []
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa UTM template theo danh sách ids


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Get list UTM template -----------------------
"""
@api {GET} {domain}/shortenurl/api/v1.0/utms/templates/actions/list              Lấy danh sách UTM template
@apiGroup UTM template
@apiDescription Lấy danh sách UTM template
@apiVersion 1.0.0
@apiName GetListUtmTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)     {String}              search         Tên mẫu UTM cần tìm kiếm

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách UTM template

@apiUse ResponseDetailUtmTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "_id": "id",
        "merchant_id": "merchant_id",        
        "template_id": "template_id", 
        "template_name": "Tên UTM template", 
        "template_value": [{
            "utm_key": "utm_campaign",
            "utm_value": "campaign_journey_id",
            "utm_name": "Campaign",
            "status": "active",
            "edit_status": "allow",     
            "allow_delete": "allow",    
        }],
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}

"""
# ----------------------- Get detail UTM parameter  -----------------------
"""
@api {GET} {domain}/shortenurl/api/v1.0/utms/templates/<template_id>                   Lấy chi tiết UTM template
@apiGroup UTM template
@apiDescription Lấy chi tíết UTM template
@apiVersion 1.0.0
@apiName GetDetailUtmTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (RESOURCE:)     {String}           template_id              Định danh của UTM template

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết thông tin template

@apiUse ResponseDetailUtmTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "id",
        "merchant_id": "merchant_id",        
        "template_id": "template_id", 
        "template_name": "Tên UTM template", 
        "template_value": [{
            "utm_key": "utm_campaign",
            "utm_value": "campaign_journey_id",
            "utm_name": "Campaign",
            "status": "active",
            "edit_status": "allow",     
            "allow_delete": "allow",    
        }],
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }
}
"""

# ============================================ SHORTEN URL ==========================================================
"""
@apiDefine ResponseDetailShortenUrl

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của UTM shorten url
@apiSuccess {String}            data.merchant_id                        Định danh của tennant
@apiSuccess {String}            data.shorten_id                         Định danh của UTM template
@apiSuccess {String}            data.name_url                           Tên của UMT template
@apiSuccess {String}            data.short_url                          Chuỗi ký tự phía sau domain, đại diện cho url gốc
@apiSuccess {String}            data.short_domain                       Domain của shorten link
@apiSuccess {String}            data.protocol_domain                    Giao thức mạng + domain
@apiSuccess {String}            data.full_shorten_url                   Link đầy đủ đã rút gọn
@apiSuccess {String}            data.full_protocol_shorten_url          Link đầy đủ đã rút gọn (bao bồm cả giao thức mạng)
@apiSuccess {String}            data.origin_url                         Url gốc full domain
@apiSuccess {String}            data.expired_date                       Thời gian short_url hết hiệu lực (UTC)
@apiSuccess {Integer}           data.session_click                      Số lượt chuyển hướng url gốc từ short url
@apiSuccess {String}            data.type_gen                           Loại sinh mã, nhận giá trị <code>manual, auto</code>
@apiSuccess {String}            data.status                             trạng thái của shorten url nhận giá trị <code>active, inactive, un_use</code>. Khi xóa thi chuyển trạng thái sang inactive. Nếu là un_use thì không cho phép edit

@apiSuccess {String}            data.create_on                          Thời điểm khởi tạo
@apiSuccess {String}            data.update_on                          Thời điểm cập nhật

"""

# ----------------------- Create shorten url -----------------------
"""
@api {POST} {domain}/shortenurl/api/v1.0/shorten-urls                Tạo shorten url
@apiGroup Shorten url
@apiDescription Tạo mới shorten url
@apiVersion 1.0.0
@apiName CreateShortenUrl
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}          name_url                 Tên của URL
@apiParam   (BODY:)     {String}          origin_url               Url gốc full domain
@apiParam   (BODY:)     {String}          short_domain             Domain của shorten link
@apiParam   (BODY:)     {String}          protocol_domain          Giao thức mạng + domain
@apiParam   (BODY:)     {String}          type_gen                 Loại sinh mã, nhận giá trị <code>manual, auto</code>
@apiParam   (BODY:)     {String}          [short_url]              Chuỗi ký tự phía sau domain, đại diện cho url gốc


@apiParamExample    {json}      BODY:
{
    "name_url": "Tên URL"
    "origin_url": "https://dantri.com.vn/"
    "short_domain": "sk.mobio.vn/"
    "protocol": "https://sk.mobio.vn/"
    "type_gen": "manual"
    "short_url": "mobio"
}


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm shorten URL

@apiUse ResponseDetailShortenUrl

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "id",
        "merchant_id": "merchant_id",        
        "shorten_id": "template_id" 
        "name_url": "Tên UTM template" 
        "short_url": "mobio",
        "short_domain": "sk.mobio.vn/",
        "protocol_domain": "http://sk.mobio.vn/",
        "full_shorten_url": "sk.mobio.vn/SUmobio",
        "full_protocol_shorten_url": "https://sk.mobio.vn/SUmobio",
        "origin_url": "https://dantri.com.vn/",
        "expired_date": "",
        "session_click": 200,
        "type_gen": "manual",
        "status": "active",
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Update shorten URL  -----------------------
"""
@api {PATCH} {domain}/shortenurl/api/v1.0/shorten-urls                    Câp nhật shorten URL
@apiGroup Shorten url
@apiDescription Sửa shorten URL
@apiVersion 1.0.0
@apiName UpdateUtmTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)     {String}         full_shorten_url       Link đầy đủ đã rút gọn

@apiParam   (BODY:)     {String}          name_url                 Tên của URL
@apiParam   (BODY:)     {String}          type_gen                 Loại sinh mã, nhận giá trị <code>manual, auto</code>
@apiParam   (BODY:)     {String}          short_url                Chuỗi ký tự phía sau domain, đại diện cho url gốc


@apiParamExample    {json}      BODY:
{
    "name_url": "Tên URL",
    "type_gen": "manual",
    "short_url": "mobio",
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa shorten URL 

@apiUse ResponseDetailShortenUrl

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "id",
        "merchant_id": "merchant_id",        
        "shorten_id": "template_id" 
        "name_url": "Tên UTM template" 
        "short_url": "mobio",
        "short_domain": "sk.mobio.vn/",
        "protocol": "http://",
        "full_shorten_url": "sk.mobio.vn/SUmobio",
        "full_protocol_shorten_url": "https://sk.mobio.vn/SUmobio",
        "origin_url": "https://dantri.com.vn/",
        "expired_date": "",
        "session_click": 200,
        "type_gen": "manual",
        "status": "active",
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }
}

"""
# ----------------------- Delete shorten URL  -----------------------
"""
@api {DELETE} {domain}/shortenurl/api/v1.0/shorten-urls                   Xóa shorten URL
@apiGroup Shorten url
@apiDescription Xóa Shorten url theo full_shorten_url
@apiVersion 1.0.0
@apiName DeleteShortenUrl

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}         delete_type            Định danh cho xóa cứng hoặc xóa mềm, nhận giá trị <code>cứng:hard_delete, mềm: soft_delete</code>
@apiParam   (Query:)     {String}         full_shorten_url       Link đầy đủ đã rút gọn

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa shorten URL


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Delete list shorten URL  -----------------------
"""
@api {POST} {domain}/shortenurl/api/v1.0/shorten-urls/actions/delete-list                   Xóa shorten URL theo danh sách
@apiGroup Shorten url
@apiDescription Xóa shorten URL theo danh sách full_shorten_url
@apiVersion 1.0.0
@apiName DeleteListShortenUrl

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}         delete_type            Định danh cho xóa cứng hoặc xóa mềm nhận giá trị <code>cứng:hard_delete, mềm: soft_delete</code>
@apiParam   (BODY:)      {List}           ids                   Danh sách các full_shorten_url cần xóa  

@apiParamExample    {json}      BODY:
{
    "ids": []
}  

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa UTM template theo danh sách ids


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Get list shorten URL -----------------------
"""
@api {POST} {domain}/shortenurl/api/v1.0/shorten-urls/actions/list              Lấy danh sách shorten URL
@apiGroup Shorten url
@apiDescription Lấy danh sách shorten URL
@apiVersion 1.0.0
@apiName GetListShortenUrl

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)     {String}              search        Kí tự cần tìm kiếm theo tên và nội dung của URL
@apiParam   (Query:)     {String}              since         Thời gian bắt đầu, dạng ITC "YYYY-MM-DD"
@apiParam   (Query:)     {String}              until         Thời gian kết thúc, dạng ITC "YYYY-MM-DD"
@apiParam   (Query:)     {String}              sort          Key của cột cần sắp xếp
@apiParam   (Query:)     {String}              order         Tăng dần hoặc giảm dần, chỉ nhận 2 giá trị <code>asc:Tăng-dần, desc:Giảm-dần</code>

@apiParam   (BODY:)      {List}           lst_domain                   Danh sách các full_shorten_url cần lọc 

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách shorten URL

@apiUse ResponseDetailShortenUrl

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "_id": "id",
        "merchant_id": "merchant_id",        
        "shorten_id": "template_id" 
        "name_url": "Tên UTM template" 
        "short_url": "mobio",
        "short_domain": "sk.mobio.vn/",
        "protocol": "http://",
        "full_shorten_url": "sk.mobio.vn/SUmobio",
        "full_protocol_shorten_url": "https://sk.mobio.vn/SUmobio",
        "origin_url": "https://dantri.com.vn/",
        "expired_date": "",
        "session_click": 200,
        "type_gen": "manual",
        "status": "active",
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}

"""
# ----------------------- Get detail shorten URL  -----------------------
"""
@api {GET} {domain}/shortenurl/api/v1.0/shorten-urls                  Lấy chi tiết shorten URL
@apiGroup Shorten url
@apiDescription Lấy chi tíết shorten URL theo shorten_id
@apiVersion 1.0.0
@apiName GetDetailShortenUrl

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}         full_shorten_url       Link đầy đủ đã rút gọn

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết thông tin shorten URL

@apiUse ResponseDetailShortenUrl

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "id",
        "merchant_id": "merchant_id",        
        "shorten_id": "template_id" 
        "name_url": "Tên UTM template" 
        "short_url": "mobio",
        "short_domain": "sk.mobio.vn/",
        "protocol": "http://",
        "full_shorten_url": "sk.mobio.vn/SUmobio",
        "full_protocol_shorten_url": "https://sk.mobio.vn/SUmobio",
        "origin_url": "https://dantri.com.vn/",
        "expired_date": "",
        "session_click": 200,
        "type_gen": "manual",
        "status": "active",
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }
}
"""
# ----------------------- Report-click -----------------------
"""
@api {GET} {domain}/shortenurl/api/v1.0/shorten-urls/<shorten_id>/report-click                Lấy thông tin click cho chart
@apiGroup Shorten url
@apiDescription Lấy thông tin chart 
@apiVersion 1.0.0
@apiName InformationWidget1

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              since                    Thời gian bắt đầu, dạng ITC "YYYY-MM-DD"
@apiParam   (Query:)     {String}              until                    Thời gian kết thúc, dạng ITC "YYYU-MM-DD"

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {List}              data                          Lấy thông tin widget 

@apiSuccess {Dict}              comparison_between_2_time_periods   Thông tin tổng hợp giữa 2 khoảng thời gian
@apiSuccess {Integer}           data.session_click                    Số lượt lượt click theo ngày
@apiSuccess {String}            data.day                             Đinh danh cho ngày

@apiSuccess {Integer}           comparison_between_2_time_periods.summary_before          Tổng lượt click trong khoảng thời gian hiện tại
@apiSuccess {Integer}           comparison_between_2_time_periods.summary_present         Tổng lượt click trong khoảng thời gian trước đó
@apiSuccess {Integer}           total_click                                               Tổng lượt click của shorten_url

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
        {
            "session_click": 1,
            "day": "2022-03-20"
        },
        {
            "session_click": 1,
            "day": "2022-03-19"
        }
    ],
    "comparison_between_2_time_periods": {
        "summary_before": 0,
        "summary_present": 0
    }
    "total_click": 40
}
"""
# ----------------------- Un use shorten url -----------------------
"""
@api {POST} {domain}/shortenurl/api/v1.0/shorten-urls/actions/un-use              Cập nhật trạng thái không sử dụng cho shorten url
@apiGroup Shorten url
@apiDescription Cập nhật lại trạng thái shorten url khi xóa domain
@apiVersion 1.0.0
@apiName UnUseShortenUrl

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (BODY:)     {String}          merchant_id                 Định danh của tennant
@apiParam   (BODY:)     {String}          short_domain                domain đã xóa

@apiParamExample    {json}      BODY:
{
    "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
    "short_domain": "t1.mobio.vn"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Cập nhật trạng thái un use khi xóa domain

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Check shorten is exists -----------------------
"""
@api {POST} {domain}/shortenurl/api/v1.0/shorten-urls/actions/is-exists              Kiểm tra shorten url đã được sử dụng chưa
@apiGroup Shorten url
@apiDescription Kiểm tra shorten url đã được sử dụng chưa
@apiVersion 1.0.0
@apiName ShortenUrlIsExists

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (BODY:)     {String}          short_domain             Domain của shorten link
@apiParam   (BODY:)     {String}          protocol_domain          Giao thức mạng + domain
@apiParam   (BODY:)     {String}          short_url                Chuỗi ký tự phía sau domain, đại diện cho url gốc

@apiParamExample    {json}      BODY:
{
    "short_domain": "url.mobio.io/",
    "protocol_domain": "http://url.mobio.io/",
    "short_url": "truongcl"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Kiểm tra shorten url đã được sử dụng chưa

@apiSuccess {Bool}            data.is_exists                Nếu đã tồn tại trả về True, ngược lại trả về False

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "is_exists": true
    },
}

"""

# ============================================ MANAGER SCHEDULER ====================================================
"""
@apiDefine ResponseDetailManagerScheduler

@apiSuccess {String}            data.id                                <code>ID</code> định danh của UTM manager scheduler
@apiSuccess {String}            data.key_config                        key config
@apiSuccess {Integer}           data.time_sleep                        Thời gian chạy của scheduler
@apiSuccess {Integer}           data.time_range                        Độ rộng thời gian sẽ quét
@apiSuccess {String}            data.last_run                          Thời gian lần chạy cuối cùng

"""

# ----------------------- Create manager scheduler -----------------------
"""
@api {POST} {domain}/shortenurl/api/v1.0/manager-scheduler                Tạo config
@apiGroup Manager scheduler
@apiDescription Tạo mới manager scheduler
@apiVersion 1.0.0
@apiName CreateConfig
@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (BODY:)     {String}          key_config               Key config
@apiParam   (BODY:)     {Integer}         time_sleep               Thời gian chạy của scheduler
@apiParam   (BODY:)     {Integer}         time_range               Độ rộng thời gian sẽ quét
@apiParam   (BODY:)     {String}          last_run                 Thời gian lần chạy cuối cùng


@apiParamExample    {json}      BODY:
{
    "key_config": "psu",
    "time_sleep": 60,
    "time_range": 10,
    "last_run": "2022-09-04"
}


@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Thêm Config

@apiUse ResponseDetailManagerScheduler

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "63185e5d3591625b284ffa46",
        "key_config": "psu",
        "last_run": "20220904000000",
        "time_range": 10,
        "time_sleep": 60
    }
}

"""
# ----------------------- Update config  -----------------------
"""
@api {PATCH} {domain}/shortenurl/api/v1.0/manager-scheduler                    Câp nhật config
@apiGroup Manager scheduler
@apiDescription Sửa config
@apiVersion 1.0.0
@apiName UpdateConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)     {String}         key_config               Key config

@apiParam   (BODY:)     {Integer}         time_sleep               Thời gian chạy của scheduler
@apiParam   (BODY:)     {Integer}         time_range               Độ rộng thời gian sẽ quét
@apiParam   (BODY:)     {String}          last_run                 Thời gian lần chạy cuối cùng


@apiParamExample    {json}      BODY:
{
    "time_sleep": 60,
    "time_range": 10,
    "last_run": "2022-09-04"
}

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Sửa config 

@apiUse ResponseDetailManagerScheduler

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "63185e5d3591625b284ffa46",
        "key_config": "psu",
        "last_run": "20220904000000",
        "time_range": 10,
        "time_sleep": 60
    }
}

"""
# ----------------------- Delete shorten URL  -----------------------
"""
@api {DELETE} {domain}/shortenurl/api/v1.0/manager-scheduler                   Xóa config
@apiGroup Manager scheduler
@apiDescription Xóa Config theo key_config
@apiVersion 1.0.0
@apiName DeleteConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}         key_config               Key config

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Xóa Config


@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {}
}

"""
# ----------------------- Get list config -----------------------
"""
@api {POST} {domain}/shortenurl/api/v1.0/manager-scheduler/actions/list              Lấy danh sách config
@apiGroup Manager scheduler
@apiDescription Lấy danh sách config
@apiVersion 1.0.0
@apiName GetListConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách config

@apiUse ResponseDetailManagerScheduler

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "_id": "63185e5d3591625b284ffa46",
        "key_config": "psu",
        "last_run": "20220904000000",
        "time_range": 10,
        "time_sleep": 60
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}

"""
# ----------------------- Get detail config  -----------------------
"""
@api {GET} {domain}/shortenurl/api/v1.0/manager-scheduler                  Lấy chi tiết config
@apiGroup Manager scheduler
@apiDescription Lấy chi tíết config theo key_config
@apiVersion 1.0.0
@apiName GetDetailConfig

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse merchant_id_header

@apiParam   (Query:)     {String}         key_config               Key config

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy chi tiết thông tin config

@apiUse ResponseDetailManagerScheduler

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": {
        "_id": "63185e5d3591625b284ffa46",
        "key_config": "psu",
        "last_run": "20220904000000",
        "time_range": 10,
        "time_sleep": 60
    }
}
"""

# ============================================ PERSONAL UTM PARAMETER ==================================================
"""
@apiDefine ResponseDetailPersonalUtmParameter

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của UTM parameter
@apiSuccess {String}            data.merchant_id                         Định danh của tennant
@apiSuccess {String}            data.parameter_id                        Định danh của UMT parameter
@apiSuccess {String}            data.utm_name                            Tên của UTM parameter
@apiSuccess {String}            data.utm_key                             Key của UTM paramter
@apiSuccess {List}              data.utm_default                         Danh sách giá trị của UTM parameter mặc định của hệ thống
@apiSuccess {List}              data.utm_value                           Danh sách giá trị của UTM parameter
@apiSuccess {String}            data.status                             Trạng thái của UTM parameter, nhận giá trị <code>active, inactive</code>
@apiSuccess {String}            data.allow_delete                       Định danh domain được phép xóa hay không,nhận giá trị <code>allow, not_allow</code>
@apiSuccess {String}            data.edit_status                        Định danh việc có được sửa status của UTM paramter không, nhận giá tri <code>allow, not_allow</code>

@apiSuccess {String}            data.create_on                          Thời điểm khởi tạo
@apiSuccess {String}            data.update_on                          Thời điểm cập nhật

"""

# ----------------------- Get list personal UTM parameter -----------------------
"""
@api {GET} {domain}/shortenurl/api/v1.0/utms/parameters/actions/public/list              Lấy danh sách UTM parameter
@apiGroup Personal UTM parameter
@apiDescription Lấy danh sách UTM parameter
@apiVersion 1.0.0
@apiName GetPublicListUtmParameter

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header

@apiParam   (Query:)     {String}              search         Tên tham số cần tìm kiếm
@apiParam   (Query:)     {String}              active         Trạng thái của UTM parameter, nhận giá trị <code>active, inactive</code>

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách UTM parameter

@apiUse ResponseDetailPersonalUtmParameter

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "_id": "id",
        "parameter_id": "parameter_id",
        "merchant_id": "merchant_id",        
        "utm_name": "utm name" 
        "utm_key": "utm key" 
        "utm_default": ["string value default"],        
        "utm_value": ["string"],     
        "status": "active",     
        "allow_delete": "allow",     
        "edit_status": "allow",     
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}


"""

# ============================================ PERSONAL UTM TEMPLATE ==================================================
"""
@apiDefine ResponseDetailPersonalUtmTemplate

@apiSuccess {String}            data.id                                 <code>ID</code> định danh của UTM template
@apiSuccess {String}            data.merchant_id                         Định danh của tennant
@apiSuccess {String}            data.template_id                         Định danh của UTM template
@apiSuccess {String}            data.template_name                       Tên của UMT template
@apiSuccess {Dictionary}        data.template_value                      Dictionary 
@apiSuccess {String}            data.template_value.utm_key              Key của tham số UTM
@apiSuccess {String}            data.template_value.utm_value            Value của tham số UTM
@apiSuccess {String}            data.template_value.utm_name             Tên của tham số UTM
@apiSuccess {String}            data.template_value.status               Trạng thái của tham số, giá trị <code>active, inactive</code>
@apiSuccess {String}            data.template_value.allow_delete         Định danh domain được phép xóa hay không,nhận giá trị <code>allow, not_allow</code>
@apiSuccess {String}            data.template_value.edit_status          Định danh việc có được sửa status của UTM paramter không, nhận giá tri <code>allow, not_allow</code>

@apiSuccess {String}            data.create_on                          Thời điểm khởi tạo
@apiSuccess {String}            data.update_on                          Thời điểm cập nhật

"""

# ----------------------- Get list personal UTM template -----------------------
"""
@api {GET} {domain}/shortenurl/api/v1.0/utms/templates/actions/public/list              Lấy danh sách UTM template 
@apiGroup Personal UTM template
@apiDescription Lấy danh sách UTM template
@apiVersion 1.0.0
@apiName GetPublicListUtmTemplate

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header
@apiUse paging

@apiParam   (Query:)     {String}              search         Tên mẫu UTM cần tìm kiếm

@apiSuccess {String}            message                       Mô tả phản hồi
@apiSuccess {Integer}           code                          Mã phản hồi
@apiSuccess {Object}            data                          Lấy danh sách UTM template

@apiUse ResponseDetailPersonalUtmTemplate

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [{
        "_id": "id",
        "merchant_id": "merchant_id",        
        "template_id": "template_id", 
        "template_name": "Tên UTM template", 
        "template_value": [{
            "utm_key": "utm_campaign",
            "utm_value": "campaign_journey_id",
            "utm_name": "Campaign",
            "status": "active",
            "edit_status": "allow",     
            "allow_delete": "allow",    
        }],
        "create_on": "2021-10-03T08:10Z",
        "update_on": "2021-10-03T08:10Z"
    }],
    "paging": {
        "page": page,
        "per_page": per_page,
        "total_page": total_page,
        "total_count": total_count
    }
}

"""

"""
@api {POST} {domain}/shortenurl/api/v1.0/shorten-urls/detail/ids              Lấy danh sách shorten theo ids 
@apiGroup ShortenBase
@apiDescription Lấy thông tin cơ bản là tên 
@apiVersion 1.0.0
@apiName GetDetailByIDS

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500
@apiUse lang
@apiUse json_header
@apiUse merchant_id_header


@apiParam   (BODY:)     {Array}         ids        danh sách id shorten url 


@apiParamExample {json} Info example
{
  "ids": ["63185e5d3591625b284ffa46"]  
}

@apiSuccessExample {json} Response 
{
    "code": 200,
    "message": "request thành công.",
    "data": [
      {
        "shorten_id": "63185e5d3591625b284ffa46",
        "name_url": "báo cáo quý 1",
      }
    ]

}

"""

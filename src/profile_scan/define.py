****************************** Profile <PERSON> ***********************************
* version: 1.0.0                                                              *
*******************************************************************************
"""
@apiDefine profile_scan
@apiVersion 1.0.0
@apiParam      (Input:)     {Object}     [profile_scan]        Option dành riêng cho JB. Lưu ý khi có field này thì dữ liệu cần phải tính toán và trả profile qua luồng queue callback. Xem chi tiết  Object<code>profile_scan</code>

@apiParam      (profile_scan:)       {Array}     field_response_adding            Danh sách các field cần tính toán: Dành riêng cho MKT
@apiParam      (profile_scan:)       {Array}     field_conditions                 Danh sách các field điều kiện
@apiParam      (profile_scan:)       {String}     field_conditions.field_name    Tên trường cá nhân hóa
@apiParam      (profile_scan:)       {String}    field_conditions.field_origin   Trường gốc
@apiParam      (profile_scan:)       {Array}     field_conditions.filter          Filter tính ra giá trị của trường cá nhân hóa; Xem dữ liệu mô tả trong api <code>Profiling</code> > <code>VIB</code> > <code>Danh sách trường gốc</code>
@apiParam      (profile_scan:)       {Array}     field_counts                     Danh sách các field cần tính toán
@apiParam      (profile_scan:)       {String}    field_counts.field_name         Tên trường cá nhân hóa
@apiParam      (profile_scan:)       {String}    field_counts.function_math      Biểu thức tính toán ra giá trị trường cá nhân hóa
"""
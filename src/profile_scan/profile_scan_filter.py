********************** Queue Profile Scan Filter JB Register **************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} profile-scan-filter-jb-register [QUEUE] Profile Scan Filter Register
@apiDescription Kiểm tra profile có thỏa mãn bộ lọc cần phải tính toán không
@apiGroup JourneyBuilder
@apiVersion 1.0.0
@apiName ProfileScanFilterJBRegister

@apiParam      (Input:)     {String}   merchant_id      Merchant ID
@apiParam      (Input:)     {String}   profile_id       Profile ID
@apiParam      (Input:)     {String}   journey_id      journey_id
@apiParam      (Input:)     {String}   jb_start_time    Thời gian start journey builder; <code>VD: 2018-07-27T09:53:21.000Z</code>
@apiParam      (Input:)     {String}   source      Nguồn bên cần lấy dữ liệu: example: EVENT, JOURNEY_BUILDER
@apiParam      (Input:)     {Array}    list_queue      Danh sách queue

@apiParam      (Input:)     {Integer}  position_process      Vị trí xử lí queue trong list_q
@apiParam      (Input:)     {Array}    list_queue.fields      Danh sách field cần lấy
@apiParam      (Input:)     {Int}      [list_queue.personalized]   1: Lấy field format cá nhân hóa; 0: Trả về field gốc; <code>default: null</code>;
@apiParam      (Input:)     {Object}   list_queue.callback  cấu hình callback
@apiParam      (Input:)     {Object}   list_queue.callback.return_false  callback trong trường hợp không thỏa mãn bộ lọc cần phải tính toán, nếu không cần callback thì return_false: null
@apiParam      (Input:)     {String}   list_queue.callback.queue_name  Tên queue callback
@apiParam      (Input:)     {String}   [list_queue.callback.queue_key]  queue_key callback, <code>default: profile_id</code>
@apiParam      (Input:)     {Object}   [list_queue.callback.data]  data trả về qua callback
@apiParam      (Input:)     {Integer}  [list_queue.callback.next_position]  next queue tiếp theo


@apiUse profile_scan

@apiParamExample [json] Input example 1:
{
  "list_queue": [
    {
      "position": 0,
      "fields": [],
      "personalized": 1,
      "profile_scan_filter": [
        {
          "operator_key": "op_is_multiple",
          "criteria_key": "cri_profile_scan_vib_spend_total_multiple",
          "values": [
            {
              "operator_key": "op_is_not_empty",
              "criteria_key": "cri_profile_scan_vib_spend_total",
              "values": [
                "op_is_not_empty"
              ]
            },
            {
              "operator_key": "op_is_between",
              "criteria_key": "cri_profile_scan_vib_spend_time",
              "values": [
                2,
                "2020-05-12T17:00:00Z",
                "2020-08-13T16:59:59Z"
              ]
            }
          ]
        }
      ],
      "callback": {
        "return_false": {
          "queue_name": "jb-queue-callback",
          "queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a"
        },
        "queue_name": "profile-scan-jb-register",
        "queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "data": {
          "field_1": "123",
          "field_2": "321"
        },
        "next_position": 1
      }
    },
    {
      "position": 1,
      "fields": [],
      "personalized": 1,
      "profile_scan": {
        "field_response_adding": [
          "field_test143",
          "field_test143"
        ],
        "field_counts": [
          {
            "function_math": "10 - 10",
            "field_name": "field_test143",
            "field_type": "field_tinh_toan"
          },
          {
            "function_math": "10 - 10",
            "field_name": "field_test143",
            "field_type": "field_tinh_toan"
          }
        ],
        "field_conditions": []
      },
      "callback": {
        "return_false": null,
        "queue_name": "jb-queue-callback",
        "queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "data": {
          "field_1": "123",
          "field_2": "321"
        },
        "next_position": null
      }
    }
  ],
  "position_process": 0,
  "journey_id": "7db1010b-b666-4573-ad04-43a21da66720",
  "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
  "profile_id": "816fffcf-eded-49be-b65c-e8e60ef6fca3",
  "source": "profiling",
  "last_profile": false,
  "jb_start_time": "2018-07-27T09:53:21.000Z",
}

@apiParamExample [json] Input example 2:
{
  "list_queue": [
    {
      "position": 0,
      "fields": [],
      "personalized": 1,
      "profile_scan_filter": [
        {
          "operator_key": "op_is_multiple",
          "criteria_key": "cri_profile_scan_vib_spend_total_multiple",
          "values": [
            {
              "operator_key": "op_is_not_empty",
              "criteria_key": "cri_profile_scan_vib_spend_total",
              "values": [
                "op_is_not_empty"
              ]
            },
            {
              "operator_key": "op_is_between",
              "criteria_key": "cri_profile_scan_vib_spend_time",
              "values": [
                2,
                "2020-05-12T17:00:00Z",
                "2020-08-13T16:59:59Z"
              ]
            }
          ]
        }
      ],
      "callback": {
        "return_false": {
          "queue_name": "jb-queue-callback",
          "queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a"
        },
        "queue_name": "profile-scan-filter-jb-register",
        "queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "data": {
          "field_1": "123",
          "field_2": "321"
        },
        "next_position": null
      }
    }
  ],
  "position_process": 0,
  "journey_id": "7db1010b-b666-4573-ad04-43a21da66720",
  "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
  "profile_id": "816fffcf-eded-49be-b65c-e8e60ef6fca3",
  "source": "profiling",
  "last_profile": false,
  "jb_start_time": "2018-07-27T09:53:21.000Z",
}

@apiSuccessExample  {json}  Callback Queue:
{
    "code": 200,
    "data": [{
        "address": "Tỉnh Thanh Hóa",
        "birthday": "1981-07-28",
        "created_time": "2018-07-27T09:53:21Z",
        "name": "Vũ Thị thọ 123",
        "gender": 3,
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": ["618261e0-dee6-4440-a744-89f67235b347"],
        "updated_time": "2018-07-28T04:57:35Z",
        "source_type": 1,
        "source_id": "435ecfeb-e229-4076-806f-982580475e88",
        "profile_scan_valid": true,
        "is_non_profile": false
    }],
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}
"""
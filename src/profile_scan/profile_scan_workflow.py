********************** Queue Get profile detail by profile_id *************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} profile-scan-wf-register [QUEUE] Get profile by id
@apiDescription Get profile detail by profile_id
@apiGroup Workflow
@apiVersion 1.0.0
@apiName WFGetProfileDetailByID

@apiParam      (Input:)     {Array}    fields      Danh sách field cần lấy
@apiParam      (Input:)     {Int}      [personalized]   1: Lấy field format cá nhân hóa; 0: Trả về field gốc; <code>default: 0</code>
@apiParam      (Input:)     {Object}   callback  cấu hình callback
@apiParam      (Input:)     {String}   callback.queue_name  Tên queue callback
@apiParam      (Input:)     {String}   [callback.queue_key]  queue_key callback, <code>default: profile_id</code>
@apiParam      (Input:)     {Object}   [callback.data]  data trả về qua callback
@apiParam      (Input:)     {String}   source      Nguồn bên cần lấy dữ liệu: example: EVENT, JOURNEY_BUILDER
@apiParam      (Input:)     {Object}   setting     Setting config queue

@apiParamExample [json] Input example:
{
    "merchant_id": "",
    "profile_id": "",
    "fields": ["name", "email", "phone_number"],
    "personalized": 1, // Cho phep lay truong ca nhan hoa
    "callback": {
    	"queue_name": "jb-queue-callback",
    	"queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a", // Nếu không truyền hoặc giá trị null thì callback key profile_id
    	"data": {
    		"field_1": "123",
    		"field_2": "321"
    	}
    },
    "source": "WORKFLOW",
    "setting": {
        "setting_field_1": 1
    }
}

@apiSuccessExample  {json}  Callback Queue:
{
    "code": 200,
    "data_input": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": ""
    },
    "data": {
        "address": "Tỉnh Thanh Hóa",
        "birthday": "1981-07-28",
        "created_time": "2018-07-27T09:53:21Z",
        "name": "Vũ Thị thọ 123",
        "gender": 3,
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": ["618261e0-dee6-4440-a744-89f67235b347"],
        "phone_number": ["+841215150001"],
        "social_user": [
            {
              "id_social": "2139374173003427",
              "social_type": 1
            }
        ],
        "updated_time": "2018-07-28T04:57:35Z",
        "source_type": 1,
        "source_id": "435ecfeb-e229-4076-806f-982580475e88",
        "is_non_profile": false
    },
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}

@apiSuccessExample  {json}  Callback Queue 404:
{
    "code": 404,
    "data_input": {
        "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "merchant_id": "618261e0-dee6-4440-a744-89f67235b347"
    },
    "reason": "profile is merged!",
    "data_callback": {
        "field_1": "123",
        "field_2": "321"
    }
}
"""
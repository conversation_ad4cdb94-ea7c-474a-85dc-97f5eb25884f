********************** Queue Profile Scan JB Register *********************************
* version: 1.0.0                                                                      *
***************************************************************************************
"""
@api {QUEUE} profile-scan-jb-register [QUEUE] Profile Scan Register
@apiDescription Đăng kí lấy dữ liệu trường cá nhân hóa và field cần phải tính toán
@apiGroup JourneyBuilder
@apiVersion 1.0.0
@apiName ProfileScanJBRegister

@apiParam      (Input:)     {String}   merchant_id      Merchant ID
@apiParam      (Input:)     {String}   profile_id       Profile ID
@apiParam      (Input:)     {String}   journey_id      journey_id
@apiParam      (Input:)     {String}   jb_start_time    Thời gian start journey builder; <code>VD: 2018-07-27T09:53:21.000Z</code>
@apiParam      (Input:)     {String}   source      Nguồn bên cần lấy dữ liệu: example: EVENT, JOURNEY_BUILDER
@apiParam      (Input:)     {Array}    list_queue      Danh sách queue


@apiParam      (Input:)     {Integer}  position_process      Vị trí xử lí queue trong list_q
@apiParam      (Input:)     {Array}    list_queue.fields      Danh sách field cần lấy
@apiParam      (Input:)     {Int}      [list_queue.personalized]   1: Lấy field format cá nhân hóa; 0: Trả về field gốc; <code>default: null</code>;
@apiParam      (Input:)     {Object}   list_queue.callback  cấu hình callback
@apiParam      (Input:)     {String}   list_queue.callback.queue_name  Tên queue callback
@apiParam      (Input:)     {String}   [list_queue.callback.queue_key]  queue_key callback, <code>default: profile_id</code>
@apiParam      (Input:)     {Object}   [list_queue.callback.data]  data trả về qua callback

@apiParam      (Input:)     {Array}   [list_queue.triggers]  Thông tin trigger muốn lấy CNH
@apiParam      (Input:)     {String}  list_queue.triggers.trigger_id  trigger_id
@apiParam      (Input:)     {String}  list_queue.triggers.event_id  event_id
@apiParam      (Input:)     {String}  list_queue.triggers.type  Loại trigger <code>dynamic_event</code>;<code>base_event</code>
@apiParam      (Input:)     {Array}   list_queue.triggers.fields  Danh sách field data trigger muốn lấy


@apiUse profile_scan

@apiParamExample [json] Input example:
{
  "journey_id": "7db1010b-b666-4573-ad04-43a21da66720",
  "merchant_id": "1cb2a489-b34a-41b3-aa7d-f1efc580d687",
  "profile_id": "816fffcf-eded-49be-b65c-e8e60ef6fca3",
  "jb_start_time": "2018-07-27T09:53:21.000Z",
  "source": "journey",
  "list_queue": [
    {
      "position": 0,
      "fields": [],
      "personalized": 1,
      "profile_scan": {
        "field_response_adding": [
          "field_test143",
          "field_test143"
        ],
        "field_counts": [
          {
            "function_math": "10 - 10",
            "field_name": "field_test143",
            "field_type": "field_tinh_toan"
          },
          {
            "function_math": "10 - 10",
            "field_name": "field_test143",
            "field_type": "field_tinh_toan"
          }
        ],
        "field_conditions": []
      },
      "triggers": [
        {
          "trigger_id": "trigger_id_1",
          "event_id": "dynamic_event_id_1",
          "type": "dynamic_event",
          "fields": [
            "action_time",
            "status"
          ]
        },
        {
          "trigger_id": "trigger_id_2",
          "event_id": "_base_e152",
          "type": "base_event",
          "fields": [
            "action_time",
            "status",
            "voucher_code"
          ]
        },
        {
          "trigger_id": "trigger_id_3",
          "event_id": "_base_ph_special_day",
          "product_line": "816fffcf-eded-49be-b65c-e8e60ef6fca3",
          "type": "base_event",
          "fields": [
            "action_time",
            "status",
            "due_date"
          ]
        },
        {
          "trigger_id": "trigger_id_4",
          "event_id": "_base_e34",
          "type": "base_event",
          "fields": [
            "action_time",
            "transaction_amount",
            "source",
            "transaction_code",
            "point",
            "rank_point",
            "store"
          ]
        },
      ],
      "filters": [
        {
          "audiences_filter": [],
          "fields": [
            "action_time",
            "status"
          ],
          "criteria_key": [
            "cri_product_holding_v1_product_holding_multiple_1",
            "cri_product_holding_v1_product_holding_multiple_2"
          ],
          "product_line": "fa10d9c2-027d-47c8-9d32-814809c90f3a"
        }
      ],
      "callback": {
        "return_false": false,
        "queue_name": "jb-queue-callback",
        "queue_key": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
        "data": {
          "field_1": "123",
          "field_2": "321"
        },
        "next_position": null
      }
    }
  ],
  "position_process": 0
}

@apiSuccessExample  {json}  Callback Queue:
{
  "code": 200,
  "data": {
    "address": "Tỉnh Thanh Hóa",
    "birthday": "1981-07-28",
    "created_time": "2018-07-27T09:53:21Z",
    "name": "Vũ Thị thọ 123",
    "gender": 3,
    "profile_id": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
    "merchant_id": [
      "618261e0-dee6-4440-a744-89f67235b347"
    ],
    "updated_time": "2018-07-28T04:57:35Z",
    "source_type": 1,
    "source_id": "435ecfeb-e229-4076-806f-982580475e88",
    "is_non_profile": false
  },
  "data_triggers": [
    {
      "trigger_id": "trigger_id_1",
      "event_id": "dynamic_event_id_1",
      "type": "dynamic_event",
      "data": {
        "action_time": "2020-12-31 00:00",
        "status": "OPEN"
      }
    },
    {
      "trigger_id": "trigger_id_2",
      "event_id": "_base_e152",
      "type": "base_event",
      "data": {
        "action_time": "2020-12-31 00:00",
        "status": "CLOSE",
        "voucher_code": "MUA_LA_TRUNG"
      }
    },
    {
      "trigger_id": "trigger_id_3",
      "event_id": "_base_eph_special_day",
      "product_line": "816fffcf-eded-49be-b65c-e8e60ef6fca3",
      "type": "base_event",
      "data": {
        "action_time": "2020-12-31 00:00",
        "status": "ACTIVE",
        "due_date": "2022-12-31 00:00"
      }
    },
    {
      "trigger_id": "trigger_id_4",
      "event_id": "_base_e34",
      "type": "base_event",
      "data": {
        "action_time": "2020-12-31 00:00",
        "transaction_amount": 200000,
        "source": "Offline",
        "transaction_code": "001",
        "point": 100,
        "rank_point": 100,
        "store": "Tòa nhà HL, số 6, ngõ 82 Duy Tân, Dịch Vọng Hậu, Cầu Giấy, Hà Nội",
        "transaction_items": [
            {
              "price" : 490000.0,
              "code" : "AK10661143",
              "total_amount" : 392000,
              "name" : "[AK10661143]  M.Yellow. AK 1 lớp, dây kéo contrast",
              "quantity" : 1,
              "id" : "60c96a4649246196de46d170",
              "currency_code" : "VND",
              "category" : ""
            }
        ]
      }
    }
  ],
  "data_filters": [
    {
      "product_line": "fa10d9c2-027d-47c8-9d32-814809c90f3a",
      "criteria_key": [
        "cri_product_holding_v1_product_holding_multiple_1",
        "cri_product_holding_v1_product_holding_multiple_2"
      ],
      "data": {
        "action_time": "2020-12-31 00:00",
        "status": "OPEN"
      }
    }
  ],
  "data_callback": {
    "field_1": "123",
    "field_2": "321"
  }
}
"""
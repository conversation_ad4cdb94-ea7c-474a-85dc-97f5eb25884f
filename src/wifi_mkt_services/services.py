"""
@apiDefine 401
@apiVersion 1.0.0
@apiHeader (Headers:) {String} Authorization Token/api-key để sử dụng api. Nếu typically là:
<li><code>Basic</code> thì Authenticate là API-Key</li>
@apiHeaderExample Basic Token Example:
{
    "Authorization":"Basic 25630732-2976-444a-ba7a-3ca1b48fcc3a"
}
@apiError (Error 4xx) 401-Unauthorized token/api-key hết hạn hoặc sai định dạng. Yêu cầu login lại.
<li><code>code:</code> 401</li>
<li><code>message:</code> <PERSON><PERSON> tả lỗi.</li>
<br/>
@apiErrorExample    {json}  HTTP/1.1 401
HTTP/1.1 401 Unauthorized
{
    "code": 401,
    "message":"Token is invalid or is expired. Please login again."
}
"""


************************** Import Non-smart wifi-data ***************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} https://api-sunworld.dev.mobio.vn/profiling/v2.0/imports/wifi-data/non_smart Import Non-smart wifi-data
@apiDescription API import dữ liệu non-smart wifi vào MOBIO
@apiGroup Services
@apiVersion 1.0.0
@apiName ImportNonSmartWifiData

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Body:)     {Array}                  data                   Dữ liệu raw_data từ wifi marketing system
@apiParam   (Body:)     {String}                 mbo_business           Chuỗi query nhận diện business của merchant. Example:<br/>
Đối với case Bà Nà Hill: <code>mbo_business=SMARTWF_BANAHILL_0807</code><br/>
Đối với case PhanXiPang: <code>mbo_business=SMARTWF_PXP_0807</code><br/>


@apiParam   (data:)   {String}    name             Tên khách hàng
@apiParam   (data:)   {String}    address          Địa chỉ khách hàng
@apiParam   (data:)   {String}    phone_number     Số điện thoại
@apiParam   (data:)   {Number}    gender           Giới tính. Allowed values:<br>
<li>1: Không xác định</li>
<li>2: Nam</li>
<li>3: Nữ</li>
@apiParam   (data:)   {String}    birthday         Ngày sinh. Format: <code>yyyy-mm-dd</code>
@apiParam   (data:)   {Number}    timestamp        Thời điểm ghi nhận thông tin khách hàng.

@apiParamExample {json} Body example
{
  "mbo_business": "SMARTWF_PXP_0807",
  "data": [
    {
      "name": "Nguyễn Văn A",
      "address": "Hà Nội",
      "phone_number": "0904123456",
      "gender": 2,
      "email": "<EMAIL>",
      "birthday": "1991-08-29",
      "timestamp": 1533547682
    },
    {
      "name": "Nguyễn Văn A",
      "address": "Hà Nội",
      "phone_number": "0904123456",
      "gender": 2,
      "email": "<EMAIL>",
      "birthday": "1991-08-29",
      "timestamp": 1533547682
    },
    ...
  ]
}

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
}

"""



***************************** Query customer info *******************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {get} https://api-sunworld.dev.mobio.vn/profiling/v2.0/users/actions/search Lấy thông tin khách hàng
@apiDescription API query thông tin khách hàng theo số điện thoại.
@apiGroup Services
@apiVersion 1.0.0
@apiName GetInfoByPhone

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500


@apiParam   (Query:)     {Number}     phone_number                                 Query theo số điện thoại. Số điện thoại được chuẩn hóa theo format: <code>84</code>{phone_number}.<br/>Example: <code>&phone_number=***********</code>
@apiParam   (Query:)     {Number}     [email]                                      Email tìm kiếm (nếu có). <br/>Example: <code>&email=<EMAIL></code>
@apiParam   (Query:)    {String}      business     Chuỗi query nhận diện business của merchant. Example:<br/>
Đối với case Bà Nà Hill: <code>&business=SMARTWF_BANAHILL_0807</code><br/>
Đối với case PhanXiPang: <code>&business=SMARTWF_PXP_0807</code><br/>


@apiSuccess     {Object}    data                        Dữ liệu khách hàng
@apiSuccess     {Number}    data..phone                 Số điện thoại khách hàng
@apiSuccess     {Number}    data..age                   Khoảng tuổi của khách hàng. <br/>Với <code>lowest</code> là giá trị thấp nhấp, <code>highest</code> là giá trị cao nhất.
@apiSuccess     {Number}    data..age..lowest           Giá trị tuổi thấp nhất
@apiSuccess     {Number}    data..age..highest          Giá trị tuổi cao nhất
@apiSuccess     {Number}    data..age..confident        Độ tin cậy.  Giá trị nằm trong khoảng <code>[0-1]</code>

@apiSuccess     {Number}    data..marital_status        Tình trạng hôn nhân. 

@apiSuccess     {Number}    data..marital_status.value  Giá trị dữ liệu
<br/><br/>Allowed values:<br/>
<li><code>1: Single / Độc thân</code></li>
<li><code>2: Engaged / Đính hôn</code></li>
<li><code>3: Married / Đã lập gia đình</code></li>
<li><code>4: Separated / Ly thân</code></li>
<li><code>5: Divorced / Ly hôn</code></li>
<li><code>6: Widow / Người goá chồng</code></li>
<li><code>7: Widower / Người goá vợ</code></li>
@apiSuccess     {Number}    data..marital_status..confident        Độ tin cậy. Giá trị nằm trong khoảng <code>[0-1]</code>

@apiSuccess     {Number}    data..gender                Giới tính
@apiSuccess     {Number}    data..gender.value  Giá trị dữ liệu
<li><code>1: Không xác định</code></li>
<li><code>2: Nam</code></li>
<li><code>3: Nữ</code></li>
@apiSuccess     {Number}    data..gender..confident        Độ tin cậy.  Giá trị nằm trong khoảng <code>[0-1]</code>

@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "phone": "***********",
  "age": {
    "lowest": 35,
    "highest": 39,
    "confident": 0.9
  },
  "marital_status": {
    "value": 2,
    "confident": 1.0
  },
  "gender": {
    "value": 3,
    "confident": 0.9
  }
}
"""


************************** Import Wifi marketing data ***************************
* version: 1.0.0                                                                *
*********************************************************************************
"""
@api {post} https://api-sunworld.dev.mobio.vn/profiling/v2.0/imports/wifi-data Import dữ liệu wifi marketing vào MOBIO
@apiDescription API Import dữ liệu wifi marketing vào MOBIO. Wifi marketing system gửi dữ liệu vào API dưới dạng file với mỗi dòng là một json chứa thông tin raw data.
@apiGroup Services
@apiVersion 1.0.0
@apiName ImportWifiData

@apiUse 401
@apiUse 404
@apiUse 405
@apiUse 412
@apiUse 500

@apiParam   (Body:)     {String=Captive-Proximity-Event,Captive-Location-Event}               type                   Loại raw_data.
@apiParam   (Body:)     {Object}                  data                   Dữ liệu raw_data từ wifi marketing system

@apiParamExample {json} Body example:
{
  "event": "Captive-Proximity-Event",
  "data": [
    {
      "mbo_business": "SMARTWF_BANAHILL_0807",
      "calibrate": {
        "session_timeout": 332
      },
      "observations": [
        {
          "ap_mac": "40e3d6cc3a08",
          "ap_name": "PingAP-Aruba1",
          "clients": [
            {
              "mac": "6814018bd9c1",
              "phone": "0912345678",
              "first_seen": 1533547682,
              "last_seen": 1533547682
            },
            ...
          ]
        }
      ]
    },
    {
      "mbo_business": "SMARTWF_PXP_0807",
      "calibrate": {
        "session_timeout": 332
      },
      "observations": [
        {
          "ap_mac": "40e3d6cc3a08",
          "ap_name": "PingAP-Aruba1",
          "clients": [
            {
              "mac": "6814018bd9c1",
              "phone": "0912345678",
              "first_seen": 1533547682,
              "last_seen": 1533547682
            },
            ...
          ]
        }
      ]
    },
    ...
  ]
}


@apiSuccessExample     {json}    Response: HTTP/1.1 200 OK
{
  "code": 200,
  "message": "request thành công",
}
"""
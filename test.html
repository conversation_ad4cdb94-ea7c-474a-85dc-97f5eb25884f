<table>
  <thead>
    <tr>
      <th style="width: 30%">Field</th>
      <th style="width: 10%">Type</th>
      <th style="width: 60%">Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>filter_info</td>
      <td>Array[Object]</td>
      <td>Danh sách các filter lấy từ audience</td>
    </tr>
    <tr>
      <td>call_back_info</td>
      <td>Object</td>
      <td>Thông tin để Factory có thể gửi profile call back trả về khi có action phù hợp:
      <table>
        <thead>
          <tr>
            <th style="width: 30%">Field</th>
            <th style="width: 10%">Type</th>
            <th style="width: 60%">Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>web_hook</td>
            <td>String</td>
            <td>URL để profilingFactory call back khi có trigger event tho<PERSON> mãn
              <li><code>NOTE</code>: để thống nhất profiling chỉ gọi callback qua method <code>POST</code></li>
            </td>
          </tr>
        </tbody>
          </table>
      </td>
    </tr>
    <tr>
      <td>add_on_trigger</td>
      <td>Object</td>
      <td>Các điều kiện đặc biệt của trigger
          <table>
            <thead>
                <tr>
                  <th style="width: 30%">Field</th>
                  <th style="width: 10%">Type</th>
                  <th style="width: 60%">Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                  <td>trigger_special_day<span class="label label-optional">optional</span></td>
                  <td>String</td>
                  <td>Thông tin về ngày cụ thể của trigger ngày đặc biệt. Format: <code>mm-dd</code> </td>
                </tr>
                <tr>
                  <td>trigger_birthday<span class="label label-optional">optional</span></td>
                  <td>Int</td>
                  <td>Khoảng cách ngày trigger so với ngày sinh nhật, <code>trigger_day = birthday + trigger_birthday</code> </td>
                </tr>
                <tr>
                  <td>trigger_wedding_day<span class="label label-optional">optional</span></td>
                  <td>Int</td>
                  <td>Khoảng cách ngày so với ngày cưới, <code>trigger_day = wedding_day + trigger_wedding_day</code></td>
                </tr>
                <tr>
                  <td>trigger_child_birthday<span class="label label-optional">optional</span></td>
                  <td>Int</td>
                  <td>Khoảng cách ngày trigger so với ngày sinh con, <code>trigger_day = child_birthday + trigger_child_birthday</code></td>
                </tr>
                <tr>
                  <td>trigger_transaction_type<span class="label label-optional">optional</span></td>
                  <td>String</td>
                  <td>loại trigger phát sinh giao dịch, in: <code>["first_transaction", "other_transaction"]</code></td>
                </tr>
                <tr>
                  <td>trigger_transaction_trademark<span class="label label-optional">optional</span></td>
                  <td>Array[String]</td>
                  <td>Danh sách các IDs thương hiệu</td>
                </tr>
                <tr>
                  <td>trigger_transaction_lst_category<span class="label label-optional">optional</span></td>
                  <td>Array[String]</td>
                  <td>Danh sách các IDs danh mục sản phẩm</td>
                </tr>
                <tr>
                  <td>trigger_transaction_lst_item<span class="label label-optional">optional</span></td>
                  <td>Array[String]</td>
                  <td>Danh sách các IDs sản phầm</td>
                </tr>
                <tr>
                  <td>trigger_transaction_from_time<span class="label label-optional">optional</span></td>
                  <td>Float</td>
                  <td>Thông tin về ngày bắt đầu tính trigger giao dịch</td>
                </tr>
                <tr>
                  <td>trigger_transaction_to_time<span class="label label-optional">optional</span></td>
                  <td>Float</td>
                  <td>Thông tin về ngày cuối cùng tính trigger giao dịch</td>
                </tr>
                <tr>
                  <td>trigger_social_like_channel<span class="label label-optional">optional</span></td>
                  <td>String</td>
                  <td>Kênh phát sinh lượt LIKE, in: <code>["Facebook", "Instagram", "Youtube"]</code></td>
                </tr>
                <tr>
                  <td>trigger_social_like_page<span class="label label-optional">optional</span></td>
                  <td>Array[String]</td>
                  <td>Danh sách IDs các page phát sinh lượt LIKE</td>
                </tr>
                <tr>
                  <td>trigger_social_comment_channel<span class="label label-optional">optional</span></td>
                  <td>String</td>
                  <td>Kênh phát sinh lượt COMMENT, in: <code>["Facebook", "Instagram", "Youtube"]</code></td>
                </tr>
                <tr>
                  <td>trigger_social_comment_page<span class="label label-optional">optional</span></td>
                  <td>Array[String]</td>
                  <td>Danh sách IDs các page phát sinh lượt COMMENT</td>
                </tr>
                <tr>
                  <td>trigger_social_msg_channel<span class="label label-optional">optional</span></td>
                  <td>String</td>
                  <td>Kênh phát sinh lượt MESSAGE, in: <code>["Facebook", "Zalo"]</code></td>
                </tr>
                <tr>
                  <td>trigger_social_msg_page<span class="label label-optional">optional</span></td>
                  <td>Array[String]</td>
                  <td>Danh sách IDs các page phát sinh lượt MESSAGE</td>
                </tr>
                <tr>
                  <td>trigger_touch_tag<span class="label label-optional">optional</span></td>
                  <td>Array[String]</td>
                  <td>Danh sách Tags phát sinh tương tác</td>
                </tr>
                <tr>
                  <td>trigger_touch_tag_id<span class="label label-optional">optional</span></td>
                  <td>Array[String]</td>
                  <td>Danh sách Tag_ids phát sinh tương tác</td>
                </tr>
                <tr>
                  <td>trigger_ticket_finish<span class="label label-optional">optional</span></td>
                  <td>String</td>
                  <td>Trigger tương tác profile khi ticket hoàn thành , in: <code>["other", "order", "information_support", "resolve_complaint"]</code></td>
                </tr>

                <tr>
                  <td>trigger_ads_mkt<span class="label label-optional">optional</span></td>
                  <td>Object</td>
                  <td> asdad
                  <table>
                    <thead>
                        <tr>
                          <th style="width: 30%">Field</th>
                          <th style="width: 10%">Type</th>
                          <th style="width: 60%">Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                          <td>process_type></td>
                          <td>array[int]</td>
                          <td></td>
                        </tr>

                        <tr>
                          <td>business_case_id></td>
                          <td>string</td>
                          <td></td>
                        </tr>

                        <tr>
                          <td>root_process></td>
                          <td>array[string()]</td>
                          <td></td>
                        </tr>

                        <tr>
                          <td>campaign_id></td>
                          <td>string()</td>
                          <td></td>
                        </tr>

                        <tr>
                          <td>action_value></td>
                          <td>array[int]</td>
                          <td></td>
                        </tr>
                    </tbody>
                    </table>
                  </td>
                </tr>

                <tr>
                  <td>trigger_voucher_id<span class="label label-optional">optional</span></td>
                  <td>array[str]</td>
                  <td>Danh sách voucher_id</td>
                </tr>

                <tr>
                  <td>trigger_voucher_start_time<span class="label label-optional">optional</span></td>
                  <td>int</td>
                  <td>Giới hạn thời gian bắt đầu tính voucher trigger</td>
                </tr>

                <tr>
                  <td>trigger_voucher_end_time<span class="label label-optional">optional</span></td>
                  <td>int</td>
                  <td>Giới hạn thời gian cuối cùng tính voucher trigger</td>
                </tr>

                <tr>
                  <td>cycle_time<span class="label label-optional">optional</span></td>
                  <td>int</td>
                  <td>Thời gian một cycle hữu hiệu (second), <code>default=24*60*60</code></td>
                </tr>
            </tbody>
          </table>
      </td>
    </tr>
  </tbody>
</table>
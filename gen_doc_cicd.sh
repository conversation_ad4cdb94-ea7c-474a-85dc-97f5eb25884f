#!/bin/sh

FOLDER_NAME=$1
echo "You selected $FOLDER_NAME"


# Trích xuất giá trị từ info.json
DOMAIN=$(cat src/$FOLDER_NAME/info.json | jq -r '.domain')
TITLE=$(cat src/$FOLDER_NAME/info.json | jq -r '.title')
PATH_API=$(cat src/$FOLDER_NAME/info.json | jq -r '.path_api')

echo "Domain is: $DOMAIN"
echo "Title is: $TITLE"
echo "Path is: $PATH_API"

DOMAIN="$DOMAIN/$PATH_API"
echo "create backup..."
cp "apidoc.json" "apidoc.bak"
echo "replace text..."
sed -E "s/(\"title\"[ ]*:[ ]*)\"[^\"]+\"/\1\"$TITLE\"/g" apidoc_v2.json > apidoc.tmp
sed -E "s#(\"url\"[ ]*:[ ]*)\"[^\"]+\"#\1\"$DOMAIN\"#g" apidoc.tmp > apidoc.json
echo "======"
cat apidoc.json
echo "======"
echo "generate doc..."
echo "execute: apidoc -i src/common/ -i src/$FOLDER_NAME/ -o dist/$FOLDER_NAME/"
apidoc -i src/common/ -i src/$FOLDER_NAME/ -o dist/$FOLDER_NAME/
echo "restore backup..."
mv apidoc.bak apidoc.json
echo "clean temp..."
rm apidoc.tmp
echo "OK"

# -*- coding: utf-8 -*-
# """
# Author: TruongNV
# Company: MobioVN
# Created Date: 24/09/2019
# Describe:
# """
# # ######################## Transaction_2.0.0  ####################
# # version: 1.0.1
# # created_date: 2020/Feb/05
# # ################################################################
# """
# @api {post} /over-kafka-topic/e46-voucher-loyalty  E46 - Voucher Loyalty
# @apiDescription API collect voucher information, loyalty sent to profileEvent.
# </br><code>NOTE</code>: DOC này không sử dụng API- để giao tiếp, thay vào đó sử dụng kafka-topic <strong>e46-voucher-loyalty</strong>
# @apiVersion 1.0.1
# @apiGroup Loyalty API
# @apiName E46 Voucher Loyalty
#
# @apiParam   (Body:)   {string}      profile_id              ID của profile
# @apiParam   (Body:)   {string}      merchant_id             ID của merchant
# @apiParam   (Body:)   {string}      voucher_code            Code của voucher trên hệ thống
# @apiParam   (Body:)   {string}      session_id              Unique ID của voucher đang action
# @apiParam   (Body:)   {Object}      voucher_info            Thông tin voucher
# <table>
#   <thead>
#     <tr>
#       <th style="width: 30%">Field</th>
#       <th style="width: 10%">Type</th>
#       <th style="width: 60%">Description</th>
#     </tr>
#   </thead>
#   <tbody>
#     <tr>
#       <td>voucher_id</td>
#       <td>string</td>
#       <td>ID của group mà voucher trực thuộc</td>
#     </tr>
#     <tr>
#       <td>voucher_name</td>
#       <td>string</td>
#       <td>Tên của group mà voucher trực thuộc</td>
#     </tr>
#     <tr>
#       <td>expire_time</td>
#       <td>float</td>
#       <td>Thời gian hết hạn của voucher</td>
#     </tr>
#   </tbody>
# </table>
# @apiParam   (Body:)   {Object}      action_info             Thông tin hoạt động
# <table>
#   <thead>
#     <tr>
#       <th style="width: 30%">Field</th>
#       <th style="width: 10%">Type</th>
#       <th style="width: 60%">Description</th>
#     </tr>
#   </thead>
#   <tbody>
#     <tr>
#       <td>action_time</td>
#       <td>float</td>
#       <td>
#         Thời gian xảy ra hoạt động
#       </td>
#     </tr>
#     <tr>
#       <td>action_value</td>
#       <td>float</td>
#       <td>
#         Thời gian xảy ra hoạt động
#         <li>
#             <strong>Allow value</strong>:
#             <br/><code>granted</code>: Khởi tạo và gửi voucher cho module khác
#             <br/><code>verified_fail</code>: Khách hàng sử dụng voucher nhưng không thành công.
#             <br/><code>used</code>: Khách hàng sử dụng thành công voucher.
#         </li>
#       </td>
#     </tr>
#     <tr>
#       <td>err_verify<span class="label label-optional">optional</span></td>
#       <td>string</td>
#       <td>
#         Lý do khi xác thực sử dụng voucher failk chỉ xuất hiện khi <strong>action_value == verified_fail</strong>
#          <li>
#             <strong>Allow value</strong>:
#             <br/><code>voucher_not_exist</code>: Mã voucher không tồn tại hoặc đã hết hạn.
#             <br/><code>voucher_merchant_not_exist</code>: Voucher không được áp dụng cho thương hiệu này.
#             <br/><code>voucher_used</code>: Voucher đã được sử dụng.
#             <br/><code>voucher_store_not_exist</code>: Voucher không được áp dụng cho cửa hàng này.
#         </li>
#        </td>
#     </tr>
#   </tbody>
# </table>
# @apiParam   (Body:)   {Object}      source_info             Thông tin sử dụng
# <table>
#   <thead>
#     <tr>
#       <th style="width: 30%">Field</th>
#       <th style="width: 10%">Type</th>
#       <th style="width: 60%">Description</th>
#     </tr>
#   </thead>
#   <tbody>
#     <tr>
#       <td>name</td>
#       <td>string</td>
#       <td>
#         Tên xác định nguồn ghi nhận tương tác của voucher
#         <li>
#             <strong>Allow value</strong>:
#             <br/><code>app</code>: Hành động xảy ra trên Mobile App.
#             <br/><code>mkt</code>: Hành động tương tác với module MKT của Mobio.
#             <br/><code>transaction</code>: Hành động ghi nhận thông qua transaction.
#             <br/><code>web</code>: Hành động ghi nhận thông qua web.
#         </li>
#       </td>
#     </tr>
#     <tr>
#       <td>campaign_id<span class="label label-optional">optional</span></td>
#       <td>string</td>
#       <td>Thông tin đinh danh khi <strong>name == "mkt"</strong>
#         <li>
#             Nếu field này có giá trị, các field sau cũng <code>bắt buộc có giá trị</code>:
#             <ul>
#                 <strong>root_message_id</strong>
#                 <br/><strong>master_campaign_id</strong>
#                 <br/><strong>audience_id</strong>
#             </ul>
#         </li>
#       </td>
#     </tr>
#     <tr>
#       <td>root_message_id<span class="label label-optional">optional</span></td>
#       <td>string</td>
#       <td>Thông tin đinh danh khi <strong>name == "mkt"</strong>
#         <li>
#             Nếu field này có giá trị, các field sau cũng <code>bắt buộc có giá trị</code>:
#             <ul>
#                 <strong>campaign_id</strong>
#                 <br/><strong>master_campaign_id</strong>
#                 <br/><strong>audience_id</strong>
#             </ul>
#         </li>
#       </td>
#     </tr>
#     <tr>
#       <td>master_campaign_id<span class="label label-optional">optional</span></td>
#       <td>string</td>
#       <td>Thông tin đinh danh khi <strong>name == "mkt"</strong>
#           <li>
#             Nếu field này có giá trị, các field sau cũng <code>bắt buộc có giá trị</code>:
#             <ul>
#                 <strong>campaign_id</strong>
#                 <br/><strong>root_message_id</strong>
#                 <br/><strong>audience_id</strong>
#             </ul>
#         </li>
#       </td>
#     </tr>
#     <tr>
#       <td>audience_id<span class="label label-optional">optional</span></td>
#       <td>string</td>
#       <td>Thông tin đinh danh khi <strong>name == "mkt"</strong>
#         <li>
#             Nếu field này có giá trị, các field sau cũng <code>bắt buộc có giá trị</code>:
#             <ul>
#                 <strong>campaign_id</strong>
#                 <br/><strong>root_message_id</strong>
#                 <br/><strong>master_campaign_id</strong>
#             </ul>
#         </li>
#       </td>
#     </tr>
#     <tr>
#       <td>transaction_id<span class="label label-optional">optional</span></td>
#       <td>string</td>
#       <td>Thông tin đinh danh khi <strong>name == "transaction"</strong></td>
#     </tr>
#     <tr>
#       <td>platform<span class="label label-optional">optional</span></td>
#       <td>string</td>
#       <td>Thông tin đinh danh khi <strong>name == "app"</strong>
#         <li>
#             Nếu field này có giá trị, các field sau cũng <code>bắt buộc có giá trị</code>:
#             <ul>
#                 <strong>model</strong>
#             </ul>
#         </li>
#       </td>
#     </tr>
#     <tr>
#       <td>platform<span class="label label-optional">optional</span></td>
#       <td>string</td>
#       <td>Thông tin đinh danh khi <strong>name == "app"</strong>
#         <li>
#             Nếu field này có giá trị, các field sau cũng <code>bắt buộc có giá trị</code>:
#             <ul>
#                 <strong>model</strong>
#             </ul>
#         </li>
#       </td>
#     </tr>
#   </tbody>
# </table>
#
#
# @apiParamExample    {json}      Body example:
# {
#     "profile_id": "0c633dcb-3cc8-4b95-abb1-2a2277c2064a",
#     "merchant_id": "1b99bdcf-d582-4f49-9715-1b61dfff3924",
#     "voucher_code": "3656438897407",
#     "voucher_info": {
#         "voucher_id": "0981ee4c-b024-4250-9135-50913341a0e9",
#         "voucher_name": "Giảm giá 50%",
#         "expire_time": 1568951662.9
#     },
#     "action_info": {
#         "action_time": 1568951662.9,
#         "action_value": "granted" // ["granted", "verified_fail", "used"]
#         "err_verify": None
#     },
#     "source_info": {
#         "name": "mkt", // ["app", "mkt", "transaction", "web"]
#         "campaign_id": "202ceab3-d91e-4644-8946-8a1d261a4ca9",
#         "master_campaign_id": "202ceab3-d91e-4644-8946-8a1d261a4ca9",
#         "audience_id": "202ceab3-d91e-4644-8946-8a1d261a4ca9",
#         "transaction_id": "202ceab3-d91e-4644-8946-8a1d261a4ca9",
#         "platform": 'ios',
#         "model":"iPhone 8"
#     },
# }
# """
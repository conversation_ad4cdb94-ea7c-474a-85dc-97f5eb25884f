#!/bin/bash
#

PROTOCOL="https:\/\/"

# ============ Host ==============
SOCIAL_HOST="social.dev.mobio.vn"
CALLCENTER_HOST="cc.dev.mobio.vn"
EMAILMARKETING_HOST="emk.dev.mobio.vn"
MARKETING_HOST="mk.dev.mobio.vn"
RETAIL_HOST="dev.mobio.vn"
SESSIONMANAGER_HOST="dev.mobio.vn"
EMAILSENDER_HOST="emsender.dev.mobio.vn"
USERMODELING_HOST="dev.mobio.vn"
VPBANK_HOST="api.bank.mobio.vn"
MOBIO_REPORT_HOST="api.report.mobio.vn"

MOBIO_V2_HOST="dev.mobio.vn"
VPBANK_V2_HOST="api.bank.mobio.vn"
ODOO_HOST="api-sunworld.dev.mobio.vn"
USER_EVENT_HOST="{domain}"
FUNDME_HOST=""
CORENA_WIFI_HOST="dev.mobio.vn"
SUNWORLD_HOST="api-sunworld.mobio.vn"
PROFILING_HOST="dev.mobio.vn"
PROFILING_BANK="dev.mobio.vn"
PROFILING_INTERNAL_HOST="{domain}"
PROFILING_EXTERNAL_HOST="{domain}"

PROFILING_ETL_HOST="{domain}"
PHONGVU_HOST="{domain}"
MARKETING_HOST="dev.mobio.vn"
LOYALTY_HOST="{domain}"
LOYALTY_VOUCHER_HOST="{domain}"
MOBILE_HOST="{domain}"
SAKUKO_HOST="dev.mobio.vn"
BIBOMART_HOST="dev.mobio.vn"
BIDV_HOST="dev.mobio.vn"
PROFILING_INTERNAL_HOST="dev.mobio.vn"
PROFILING_EXTERNAL_HOST="dev.mobio.vn"

ADS_HOST="dev.mobio.vn"
SPAMSCORE_HOST="{domain}"
TEMPLATE_HOST="{domain}"
CHATTOOL_HOST="{domain}"
ADMIN_HOST="{domain}"
TICKET_HOST="{domain}"
COMMENT_MANAGEMENT="{domain}"
SALE_HOST="{domain}"
SALE_MOBILE="{domain}"
SAMSUNG_HOST="dev.mobio.vn"
PRODUCT_HOST="{domain}"
PROFILING_THIRDPARTY_HOST='dev.mobio.vn'
LICENSE_HOST="{domain}"
LICENSE_V2_HOST="{domain}"
VENESA_HOST="{domain}"
MINIPOS_HOST="{domain}"
LANDINGPAGE_HOST="{domain}"
AUDIENCE_HOST="{domain}"
VOUCHER_HOST=""
JOURNEY_HOST="{domain}"
SURVEY_HOST="{domain}"
MEDIA_HOST="{domain}"
DYNAMIC_EVENT="{domain}"
TASK_HOST="{domain}"
NOTE_HOST="{domain}"
MOBIO_TEMPLATE_HOST="{domain}"
SHORTENURL_HOST="{domain}"
SAMSUNG_MICROSITE_HOST="{domain}"
TRANSACTION_HUB_HOST="{domain}"
SALE_MEMO_HOST="{domain}"
PRODUCT_BANK_HOST="{domain}"
MARKET_PLACE_HOST="{domain}"
MARKET_PLACE_REPORT_HOST="{domain}"
KPI_MANAGEMENT_HOST="{domain}"
MONITOR_SYSTEM_HOST="{domain}"
MBO_MAILER_HOST="{domain}"
BACKEND_MOBILE_EIB="{domain}"
LADDER_HOST="{domain}"
KBM_HOST="{domain}"
WFB_HOST="{domain}"
CALCULATION_ATTRIBUTE_HOST="{domain}"
COMMUNICATION_HUB_HOST="{domain}"

# ============= Version ==============
SOCIAL_VERSION="v1.0"
CALLCENTER_VERSION="v1.0"
EMAILMARKETING_VERSION="v1.0"
MARKETING_VERSION="v1.0"
RETAIL_VERSION="v1.0"
SESSIONMANAGER_VERSION="v1.0"
ADMIN_VERSION="v1.0"
ACCOUNTING_VERSION="v1.0"
PUSHSMS_VERSION="v1.0"
EMAILSENDER_VERSION="v1.0"
USERMODELING_VERSION="v2.0"
VPBANK_VERSION="v1.0"
MOBIO_V2_VERSION="v1.0"
ODOO_VERSION="v1.0"
USER_EVENT_VERSION="v1.0"
FUNDME_VERSION="v1.0"
CORENA_WIFI_VERSION="v1.0"
SUNWORLD_VERSION="v1.0"
PROFILING_VERSION="v1.0"
PROFILING_INTERNAL_VERSION="v1.0"
PHONGVU_VERSION="v1.0"
MARKETING_VERSION="v1.1.3"
LOYALTY_VERSION="v2.0"
LOYALTY_VOUCHER_VERSION="v2.0"
SAKUKO_VERSION="v1.0"
BIBOMART_VERSION="v2.0"
BIDV_VERSION='v2.0'
MOBILE_VERSION="v2.0"
ADMIN_VERSION_V2="v2.0"
PROFILING_INTERNAL_VERSION="v3.0"
ADS_VERSION="v1.0"
SPAMSCORE_VERSION="v1.0"
TEMPLATE_VERSION="v2.0"
CHATTOOL_VERSION="v1.0"
TICKET_VERSION="v1.0"
COMMENT_MANAGEMENT_VERSION="v1.0"
SALE_VERSION="v1.0"
SALE_MOBILE_VERSION="v1.0"
SAMSUNG_VERSION="v1.0"
PRODUCT_VERSION="v1.0"
PROFILING_THIRDPARTY_VERSION='v3.0'
USER_EVENT_VERSION='v1.0'
LICENSE_VERSION='v1.0'
LICENSE_V2_VERSION='v1.0'
VENESA_VERSION='v2.0'
MINIPOS_VERSION='v2.1'
LANDINGPAGE_VERSION='v1.1'
AUDIENCE_VERSION="v2.0"
PROFILING_ETL_VERSION="v1.0"
VOUCHER_VERSION="v1.0"
JOURNEY_VERSION="v1.0"
SURVEY_VERSION="v1.0"
MEDIA_VERSION="v1.0"
TASK_VERSION="v1.0"
NOTE_VERSION="v1.0"
MOBIO_TEMPLATE_VERSION="v1.0"
MONITOR_SYSTEM_VERSION="v1.0"
SHORTENURL_VERSION="v1.0"
SAMSUNG_MICROSITE_VERSION="v1.0"
TRANSACTION_HUB_VERSION="v1.0"
SALE_MEMO_VERSION="v1.0"
PRODUCT_BANK_VERSION="v1.0"
MARKET_PLACE_VERSION="v1.0"
MARKET_PLACE_REPORT_VERSION="v1.0"
KPI_MANAGEMENT_VERSION="v1.0"
MBO_MAILER_VERSION="v1.0"
BACKEND_MOBILE_EIB_VERSION="v1.0"
LADDER_VERSION="v1.0"
KBM_VERSION="v1.0"
WFB_VERSION="v1.0"
CALCULATION_ATTRIBUTE_VERSION="v1.0"
COMMUNICATION_HUB_VERSION="v1.0"

# ============ Path ==============
SOCIAL_URI=""
CALLCENTER_URI=""
EMAILMARKETING_URI=""
MARKETING_URI=""
RETAIL_URI=""
SESSIONMANAGER_URI="nm\/api"
ADMIN_URI="adm"
ACCOUNTING_URI="profiling"
PUSHSMS_URI="sms"
EMAILSENDER_URI=""
USERMODELING_URI=""
USER_EVENT_URI="events\/api"
PROFILING_URI="profiling"
PROFILING_INTERNAL_URI=""
PROFILING_EXTERNAL_URI=""

PHONGVU_URI="phongvu"
LOYALTY_URI="loyalty"
LOYALTY_VOUCHER_URI="loyalty"
MOBILE_URI="loyalty\/mobile"
SAKUKO_URI="sakuko"
BIBOMART_URI="bibomart"
BIDV_URI='bidv'
PROFILING_INTERNAL_URL="profiling_internal"
PROFILING_EXTERNAL_URL="profiling_external"

ADS_URI='ads'
SPAMSCORE_URI="spamscore"
TEMPLATE_URI="template"
CHATTOOL_URI="chattool"
TICKET_URI="ticket"
COMMENT_MANAGEMENT_URI="comment_management"
SALE_URI="sale"
SALE_MOBILE_URI="sale_mobile"
SAMSUNG_URI='samsung'
PRODUCT_URI='product'
PRODUCT_BANK_URI='product_bank'
PROFILING_THIRDPARTY_URI='profiling_thirdparty'
LICENSE_URI='license'
LICENSE_V2_URI='license_v2'
VENESA_URI='loyalty'
MINIPOS_URI='minipos'
LANDINGPAGE_URI='landingpage'
AUDIENCE_URI='audience'
PROFILING_ETL_URI="profiling_etl"
VOUCHER_URI="voucher"
JOURNEY_URI="journey"
SURVEY_URI="survey"
SAMSUNG_MICROSITE_URI="samsung_microsite"
MEDIA_URI="media"
DYNAMIC_EVENT_URI="dynamic_event"
TASK_URI="task"
NOTE_URI="note"
MOBIO_TEMPLATE_URI="mobio_template"
SHORTENURL_URI="shortenurl"
TRANSACTION_HUB_URI="transaction_hub"
SALE_MEMO_URI="sale_memo"
MARKET_PLACE_URI="market_place"
MARKET_PLACE_REPORT_URI="market_place_report"
KPI_MANAGEMENT_URI='kpi_management'
MONITOR_SYSTEM_URI="monitor_system"
MBO_MAILER_URI="mbo_mailer"
BACKEND_MOBILE_EIB_URI="backend_mobile_eib"
LADDER_URI="ladder"
KBM_URI="kbm"
WFB_URI="wfb"
CALCULATION_ATTRIBUTE_URI="calculation_attribute"
COMMUNICATION_HUB_URI="communication_hub"


# ============ Title ==============
SOCIAL_TITLE="Social Mobio APIs document"
CALLCENTER_TITLE="CallCenter Mobio APIs document"
EMAILMARKETING_TITLE="EmailMarketing Mobio APIs document"
MARKETING_TITLE="Marketing Mobio APIs document"
RETAIL_TITLE="Mobio APIs document"
SESSIONMANAGER_TITLE="SessionManager Mobio APIs document"
ADMIN_TITLE="Administrator Mobio APIs document"
ACCOUNTING_TITLE="Accounting Mobio APIS document"
PUSHSMS_TITLE="Push SMS APIS document"
EMAILSENDER_TITLE="EmailSender Mobio APIs document"
USERMODELING_TITLE="UserModeling Mobio APIs document"
VPBANK_TITLE="VPBank APIs document"
MOBIO_REPORT_TITLE="Mobio Report APIs document"

MOBIO_V2_TITLE="MOBIO_v2 APIs document"
WIFI_MARKETING_SERVICES_TITLE="Wifi Marketing Services APIs document"
ODOO_TITLE="MOBIO ODOO CRM API Document"
USER_EVENT_TITLE="MOBIO USER EVENT API Document"
FUNDME_TITLE="FundME API Document"
CORENA_WIFI_TITLE="CORENA WIFI MKT APIs document"
SUNWORLD_TITLE="SUNWORLD APIs document"
PROFILING_ON_PREMISE_TITLE="Profiling APIs document"
PROFILING_TITLE="Profiling APIs document"
PROFILING_INTERNAL_TITLE="Profiling Internal APIs document"
PROFILING_EXTERNAL_TITLE="Profiling External APIs document"

PHONGVU_TITLE="PHONGVU APIs document"
MARKETING_TITLE="MarketingStrategy API Documents"
LOYALTY_TITLE="Loyalty API Documents"
LOYALTY_VOUCHER_TITLE="Loyalty API Documents"
SAKUKO_TITLE="Sakuko API Documents"
BIBOMART_TITLE="BiboMart API Documents"
BIDV_TITLE="BIDV API Documents"
MOBILE_TITLE="Mobile API Documents"
PROFILING_INTERNAL_TITLE="Profiling Internal APIs document"
PROFILING_EXTERNAL_TITLE="Profiling External APIs document"

ADS_TITLE="Ads API Documents"
SPAMSCORE_TITLE="API Spamscore Email"
TEMPLATE_TITLE="API Template HTML"
CHATTOOL_TITLE="Chat Tool API"
TICKET_TITLE="Ticket API"
COMMENT_MANAGEMENT_TITLE="Comment Management API"
SALE_TITLE="Sale API document"
SALE_MOBILE_TITLE="Sale Mobile API document"
SAMSUNG_TITLE="SAMSUNG API"
PRODUCT_TITLE="Product API"
PROFILING_THIRDPARTY_TITLE="Profiling Thirdparty Api document"
LICENSE_TITLE="Mobio license API document"
LICENSE_V2_TITLE="Mobio license API document"
VENESA_TITLE="Venesa API Document"
MINIPOS_TITLE="Mobio minipos API document"
LANDINGPAGE_TITLE="Mobio landing-pages API document"
AUDIENCE_TITLE="audience_v2-pages API document"
PROFILING_ETL_TITLE="Profiling Etl API document"
VOUCHER_TITLE="Voucher API document"
JOURNEY_TITLE="Journey API document"
SURVEY_TITLE="Survey API document"
MEDIA_TITLE="Media API document"
DYNAMIC_EVENT_TITLE="Dynamic Event Document"
TASK_TITLE="Task API document"
NOTE_TITLE="Note API document"
MOBIO_TEMPLATE_TITLE="Mobio template API document"
SHORTENURL_TITLE="SHORTEN URL API document"
SAMSUNG_MICROSITE_TITLE="SamSung Microsite API document"
TRANSACTION_HUB_TITLE="Transaction Hub API document"
SALE_MEMO_TITLE="Sale Memo API document"
PRODUCT_BANK_TITLE="Product Bank API document"
MARKET_PLACE_TITLE="Market Place API document"
MARKET_PLACE_REPORT_TITLE="Market Place Report API document"
KPI_MANAGEMENT_TITLE='KpiManagement Docs API'
MONITOR_SYSTEM_TITLE="Monitor system API document"
MBO_MAILER_TITLE="MBO Mailer API Document"
BACKEND_MOBILE_EIB_TITLE="Backend Mobile EIB API Document"
LADDER_TITLE="Ladder API Document"
KBM_TITLE="Knowledge Base API Document"
WFB_TITLE="Web Form Builder API Document"
CALCULATION_ATTRIBUTE_TITLE="Calculation Attribute API Document"
COMMUNICATION_HUB_TITLE="Communication Hub API Document"

# ============ Directory ==============
SOCIAL_DIRECTORY="social"
CALLCENTER_DIRECTORY="callcenter"
EMAILMARKETING_DIRECTORY="emailmarketing"
MARKETING_DIRECTORY="campaign"
RETAIL_DIRECTORY="retails"
INSIGHT_DIRECTORY="insights"
SESSIONMANAGER_DIRECTORY="sessionmanager"
ADMIN_DIRECTORY="administrator"
ADMIN_DIRECTORY_V2="administrator_v2"
ACCOUNTING_DIRECTORY="accounting"
PUSHSMS_DIRECTORY="push-sms"
EMAILSENDER_DIRECTORY="emailsender"
USERMODELING_DIRECTORY="usermodeling"
VPBANK_DIRECTORY="vpbank"
MOBIO_REPORT_DIRECTORY="mobio_report"
MOBIO_V2_DIRECTORY="mobio-v2"
WIFI_MARKETING_SERVICES_DIRECTORY="wifi_mkt_services"
VPBANK_V2_DIRECTORY="vpbank-v2"
ODOO_DIRECTORY="odoo"
USER_EVENT_DIRECTORY="user_event"
FUNDME_DIRECTORY="fundme"
CORENA_WIFI_DIRECTORY="corena_wifi_mkt"
SUNWORLD_DIRECTORY="sunworld"
PROFILING_ON_PREMISE_DIRECTORY="profiling-on-premise"
PROFILING_DIRECTORY="profiling"
PROFILING_BANK_DIRECTORY="profiling_bank"
PROFILING_INTERNAL_DIRECTORY="profiling_internal"
PROFILING_EXTERNAL_DIRECTORY="profiling_external"

PHONGVU_DIRECTORY="phongvu"
MARKETING_DIRECTORY="marketing"
LOYALTY_DIR="loyalty"
LOYALTY_VOUCHER_DIR="loyalty_voucher"
SAKUKO_DIR="sakuko"
BIBOMART_DIR="bibomart"
BIDV_DIR="bidv"
MOBILE_DIR="mobile_sakuko"
PROFILING_INTERNAL_DIRECTORY="profiling_internal"
PROFILING_EXTERNAL_DIRECTORY="profiling_external"

ADS_DIR="ads_automation"
SPAMSCORE_DIR="spamscore"
TEMPLATE_DIR="template_html"
CHATTOOL_DIR="chattool"
TICKET_DIR="ticket"
COMMENT_MANAGEMENT_DIR="comment_management"
SALE_DIR="sale"
SALE_MOBILE_DIR="sale-mobile"
SAMSUNG_DIR="samsung"
PRODUCT_DIR="product"
PROFILING_THIRDPARTY_DIR="profiling_thirdparty"
LICENSE_DIR="license"
LICENSE_V2_DIR="license_v2"
VENESA_DIR="venesa"
MINIPOS_DIR="minipos"
LANDINGPAGE_DIR="landingpage"
AUDIENCE_DIR="audience_v2"
PROFILING_ETL_DIR="profiling_etl"
VOUCHER_DIR="voucher"
JOURNEY_DIR="journey"
SURVEY_DIR="survey"
MEDIA_DIR="media"
DYNAMIC_EVENT_DIR="dynamic_event"
TASK_DIR="task"
NOTE_DIR="note"
MOBIO_TEMPLATE_DIR="mobio_template"
SHORTENURL_DIR="shortenurl"
SAMSUNG_MICROSITE_DIR="samsung_microsite"
TRANSACTION_HUB_DIR="transaction_hub"
SALE_MEMO_DIR="sale-memo"
PRODUCT_BANK_DIR="product_bank"
MARKET_PLACE_DIR="market_place"
MARKET_PLACE_REPORT_DIR="market_place_report"
KPI_MANAGEMENT_DIR="kpi-management"
MONITOR_SYSTEM_DIR="monitor_system"
MBO_MAILER_DIR="mbo_mailer"
BACKEND_MOBILE_EIB_DIR="backend_mobile_eib"
LADDER_DIR="ladder"
KBM_DIR="kbm"
WFB_DIR="web-form-builder"
CALCULATION_ATTRIBUTE_DIR="calculation_attribute"
COMMUNICATION_HUB_DIR="communication_hub"

#=======================================
DOMAIN=""
URI=""
TITLE=""
DIRECTORY=""
VERSION="v1.0"

if [ "$1" = "social" ]
then
    DOMAIN="$PROTOCOL$SOCIAL_HOST$URI\/social\/api\/$VERSION"
    TITLE=$SOCIAL_TITLE
    DIRECTORY=$SOCIAL_DIRECTORY

elif [[ ( "$1" = "callcenter" ) || ( "$1" = "cc" ) ]]
then
    DOMAIN=$CALLCENTER_HOST
    TITLE=$CALLCENTER_TITLE
    DIRECTORY=$CALLCENTER_DIRECTORY

elif [[ ( "$1" = "campaign" ) || ( "$1" = "camp" ) ]]
then
    DOMAIN=$MARKETING_HOST
    TITLE=$MARKETING_TITLE
    DIRECTORY=$MARKETING_DIRECTORY

elif [[ ( "$1" = "emailmarketing" ) || ( "$1" = "emk" ) || ( "$1" = "email" ) ]]
then
    DOMAIN=$EMAILMARKETING_HOST
    TITLE=$EMAILMARKETING_TITLE
    DIRECTORY=$EMAILMARKETING_DIRECTORY

elif [ "$1" = "insights" ]
then
    DOMAIN=$RETAIL_HOST
    TITLE=$RETAIL_TITLE
    DIRECTORY=$RETAIL_DIRECTORY

elif [ "$1" = "retails" ]
then
    DOMAIN=$RETAIL_HOST
    TITLE=$RETAIL_TITLE
    DIRECTORY=$RETAIL_DIRECTORY

elif [[ ( "$1" = "sessionmanager" ) || ( "$1" = "sm" ) ]]
then
    DOMAIN="$PROTOCOL$SESSIONMANAGER_HOST\/$SESSIONMANAGER_URI\/$SESSIONMANAGER_VERSION"
    TITLE=$SESSIONMANAGER_TITLE
    DIRECTORY=$SESSIONMANAGER_DIRECTORY

elif [[ ( "$1" = "administrator" ) || ( "$1" = "adm" ) || ( "$1" = "admin" ) ]]
then
    URI=$ADMIN_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$ADMIN_HOST"
    TITLE=$ADMIN_TITLE
    DIRECTORY=$ADMIN_DIRECTORY

elif [[ ( "$1" = "administrator_v2" ) ]]
then
    URI=$ADMIN_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$RETAIL_HOST$URI\/$ADMIN_VERSION"
    TITLE=$ADMIN_TITLE
    DIRECTORY=$ADMIN_DIRECTORY_V2

elif [[ ( "$1" = "accounting" ) || ( "$1" = "acc" ) ]]
then
    URI=$ACCOUNTING_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$RETAIL_HOST$URI\/$VERSION"
    TITLE=$ACCOUNTING_TITLE
    DIRECTORY=$ACCOUNTING_DIRECTORY

elif [[ ( "$1" = "sms" ) ]]
then
    URI=$PUSHSMS_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$RETAIL_HOST$URI\/$VERSION"
    TITLE=$PUSHSMS_TITLE
    DIRECTORY=$PUSHSMS_DIRECTORY

elif [[ ( "$1" = "emailsender" ) || ( "$1" = "emsender" ) || ( "$1" = "es" ) ]]
then
    URI=$EMAILSENDER_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$EMAILSENDER_HOST$URI\/$VERSION"
    TITLE=$EMAILSENDER_TITLE
    DIRECTORY=$EMAILSENDER_DIRECTORY

elif [[ ( "$1" = "usermodeling" ) || ( "$1" = "um" ) ]]
then
    URI=$USERMODELING_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$USERMODELING_HOST$URI\/$USERMODELING_VERSION"
    TITLE=$USERMODELING_TITLE
    DIRECTORY=$USERMODELING_DIRECTORY

elif [[ ( "$1" = "vpbank" ) ]]
then
    URI=USER_EVENT_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$VPBANK_HOST$URI\/$VERSION"
    TITLE=$VPBANK_TITLE
    DIRECTORY=$VPBANK_DIRECTORY

elif [[ ( "$1" = "mobio_report" ) ]]
then
    URI=USER_EVENT_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$MOBIO_REPORT_HOST$URI\/$VERSION"
    TITLE=$MOBIO_REPORT_TITLE
    DIRECTORY=$MOBIO_REPORT_DIRECTORY

elif [[ ( "$1" = "mobiov2" ) ]]
then
    URI=$ACCOUNTING_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$MOBIO_V2_HOST$URI\/$VERSION"
    TITLE=$MOBIO_V2_TITLE
    DIRECTORY=$MOBIO_V2_DIRECTORY

elif [[ ( "$1" = "wfs" ) || ( "$1" = "wifimktservice" ) ]]
then
    URI=$ACCOUNTING_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$MOBIO_V2_HOST$URI\/$VERSION"
    TITLE=$WIFI_MARKETING_SERVICES_TITLE
    DIRECTORY=$WIFI_MARKETING_SERVICES_DIRECTORY

elif [[ ( "$1" = "vpbankv2" ) ]]
then
    URI=$ACCOUNTING_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$VPBANK_V2_HOST$URI\/$VERSION"
    TITLE=$VPBANK_TITLE
    DIRECTORY=$VPBANK_V2_DIRECTORY

elif [[ ( "$1" = "odoo" ) ]]
then
    URI=$ACCOUNTING_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$ODOO_HOST$URI\/$VERSION"
    TITLE=$ODOO_TITLE
    DIRECTORY=$ODOO_DIRECTORY

elif [[ ( "$1" = "user_event" ) ]]
then
    URI=$USER_EVENT_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    #DOMAIN="$PROTOCOL$USER_EVENT_HOST$URI\/$USER_EVENT_VERSION"
    DOMAIN="https://{domain}/events/api/v1.0"
    TITLE=$USER_EVENT_TITLE
    DIRECTORY=$USER_EVENT_DIRECTORY

elif [[ ( "$1" = "fundme" ) ]]
then
    URI=$ACCOUNTING_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$FUNDME_HOST$URI\/$VERSION"
    TITLE=$FUNDME_TITLE
    DIRECTORY=$FUNDME_DIRECTORY

elif [[ ( "$1" = "corena_wifi_mkt" ) ]]
then
    URI=$ACCOUNTING_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$CORENA_WIFI_HOST$URI\/$VERSION"
    TITLE=$CORENA_WIFI_TITLE
    DIRECTORY=$CORENA_WIFI_DIRECTORY

elif [[ ( "$1" = "sunworld" ) ]]
then
    URI=$ACCOUNTING_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$SUNWORLD_HOST$URI\/$VERSION"
    TITLE=$SUNWORLD_TITLE
    DIRECTORY=$SUNWORLD_DIRECTORY
elif [[ ( "$1" = "ponpremise" ) ]]
then
    URI=$ACCOUNTING_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$URI\/$VERSION"
    TITLE=$PROFILING_ON_PREMISE_TITLE
    DIRECTORY=$PROFILING_ON_PREMISE_DIRECTORY
elif [[ ( "$1" = "profiling" ) ]]
then
    URI=$PROFILING_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROFILING_HOST$PROFILING_URI\/$PROFILING_VERSION"
    TITLE=$PROFILING_TITLE
    DIRECTORY=$PROFILING_DIRECTORY
elif [[ ( "$1" = "profiling_bank" ) ]]
then
    URI=$PROFILING_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROFILING_HOST$PROFILING_URI\/$PROFILING_VERSION"
    TITLE=$PROFILING_TITLE
    DIRECTORY=$PROFILING_BANK_DIRECTORY
elif [[ ( "$1" = "profiling_internal" ) ]]
then
    URI=$PROFILING_INTERNAL_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$PROFILING_INTERNAL_HOST$URI\/$VERSION"
    TITLE=$PROFILING_INTERNAL_TITLE
    DIRECTORY=$PROFILING_INTERNAL_DIRECTORY

elif [[ ( "$1" = "profiling_external" ) ]]
then
    URI=$PROFILING_EXTERNAL_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$PROFILING_EXTERNAL_HOST$URI\/$VERSION"
    TITLE=$PROFILING_EXTERNAL_TITLE
    DIRECTORY=$PROFILING_EXTERNAL_DIRECTORY

elif [[ ( "$1" = "ads" ) ]]
then
    URI=$ADS_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$ADS_HOST$URI\/$VERSION"
    TITLE=$ADS_TITLE
    DIRECTORY=$ADS_DIR

elif [[ ( "$1" = "phongvu" ) ]]
then
    URI=$PHONGVU_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$URI\/$VERSION"
    TITLE=$PHONGVU_TITLE
    DIRECTORY=$PHONGVU_DIRECTORY

elif [[ ( "$1" = "marketing" ) ]]
then
    URI=' '
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    # DOMAIN="$PROTOCOL$URI\/$VERSION"
    DOMAIN=$URI
    TITLE=$MARKETING_TITLE
    DIRECTORY=$MARKETING_DIRECTORY

elif [[ ( "$1" = "loyalty" ) ]]
then
    VERSION=$LOYALTY_VERSION
    URI=$LOYALTY_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
#    DOMAIN="$LOYALTY_HOST$URI\/api\/$VERSION"
    DOMAIN="$LOYALTY_HOST"
    TITLE=$LOYALTY_TITLE
    DIRECTORY=$LOYALTY_DIR

elif [[ ( "$1" = "loyalty_voucher" ) ]]
then
    VERSION=$LOYALTY_VOUCHER_VERSION
    URI=$LOYALTY_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
#    DOMAIN="$LOYALTY_HOST$URI\/api\/$VERSION"
    DOMAIN="$LOYALTY_VOUCHER_HOST"
    TITLE=$LOYALTY_VOUCHER_TITLE
    DIRECTORY=$LOYALTY_VOUCHER_DIR

elif [[ ( "$1" = "sakuko" ) ]]
then
    VERSION=$SAKUKO_VERSION
    URI=$SAKUKO_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
#    DOMAIN="$PROTOCOL$SAKUKO_HOST$URI\/api\/$VERSION"
    DOMAIN=" "
    TITLE=$SAKUKO_TITLE
    DIRECTORY=$SAKUKO_DIR
elif [[ ( "$1" = "bibomart" ) ]]
then
    VERSION=$BIBOMART_VERSION
    URI=$BIBOMART_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$BIBOMART_HOST$URI\/api\/$VERSION"
    TITLE=$BIBOMART_TITLE
    DIRECTORY=$BIBOMART_DIR
elif [[ ( "$1" = "bidv" ) ]]
then
    VERSION=$BIDV_VERSION
    URI=$BIDV_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$BIDV_HOST$URI\/api\/$VERSION"
    TITLE=$BIDV_TITLE
    DIRECTORY=$BIDV_DIR
elif [[ ( "$1" = "mobile" ) ]]
then
    VERSION=$MOBILE_VERSION
    URI=$MOBILE_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$MOBILE_HOST$URI\/api\/$MOBILE_VERSION"
    TITLE=$MOBILE_TITLE
    DIRECTORY=$MOBILE_DIR
elif [[ ( "$1" = "profiling_internal" ) ]]
then
    URI=$PROFILING_INTERNAL_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$URI\/$VERSION"
    TITLE=$PROFILING_INTERNAL_TITLE
    DIRECTORY=$PROFILING_INTERNAL_DIRECTORY
elif [[ ( "$1" = "profiling_external" ) ]]
then
    URI=$PROFILING_EXTERNAL_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$URI\/$VERSION"
    TITLE=$PROFILING_EXTERNAL_TITLE
    DIRECTORY=$PROFILING_EXTERNAL_DIRECTORY
elif [[ ( "$1" = "profiling_thirdparty" ) ]]
then
    URI=$PROFILING_THIRDPARTY_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$URI\/$PROFILING_THIRDPARTY_VERSION"
    TITLE=$PROFILING_THIRDPARTY_TITLE
    DIRECTORY=$PROFILING_THIRDPARTY_DIR
elif [[ ( "$1" = "spamscore" ) ]]
then
    VERSION=$SPAMSCORE_VERSION
    URI=$SPAMSCORE_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$SPAMSCORE_HOST$URI\/api\/$SPAMSCORE_VERSION"
    TITLE=$SPAMSCORE_TITLE
    DIRECTORY=$SPAMSCORE_DIR
elif [[ ( "$1" = "template_html" ) ]]
then
    VERSION=$TEMPLATE_VERSION
    URI=$TEMPLATE_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$TEMPLATE_HOST$URI\/api\/$TEMPLATE_VERSION"
    TITLE=$TEMPLATE_TITLE
    DIRECTORY=$TEMPLATE_DIR
elif [[ ( "$1" = "chattool" ) ]]
then
    VERSION=$CHATTOOL_VERSION
    URI=$CHATTOOL_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$CHATTOOL_HOST$URI\/api\/$CHATTOOL_VERSION"
    TITLE=$CHATTOOL_TITLE
    DIRECTORY=$CHATTOOL_DIR
elif [[ ( "$1" = "ticket" ) ]]
then
    VERSION=$TICKET_VERSION
    URI=$TICKET_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$TICKET_HOST$URI\/api\/$TICKET_VERSION"
    TITLE=$TICKET_TITLE
    DIRECTORY=$TICKET_DIR
elif [[ ( "$1" = "comment_management" ) ]]
then
    VERSION=$COMMENT_MANAGEMENT_VERSION
    URI=$COMMENT_MANAGEMENT_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$COMMENT_MANAGEMENT_HOST"
    TITLE=$COMMENT_MANAGEMENT_TITLE
    DIRECTORY=$COMMENT_MANAGEMENT_DIR
elif [[ ( "$1" = "sale" ) ]]
then
    VERSION=$SALE_VERSION
    URI=$SALE_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$SALE_HOST$URI\/api\/$SALE_VERSION"
    TITLE=$SALE_TITLE
    DIRECTORY=$SALE_DIR
elif [[ ( "$1" = "sale-mobile" ) ]]
then
    VERSION=$SALE_MOBILE_VERSION
    URI=$SALE_MOBILE_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$SALE_MOBILE_HOST$URI\/api\/$SALE_MOBILE_VERSION"
    TITLE=$SALE_MOBILE_TITLE
    DIRECTORY=$SALE_MOBILE_DIR
elif [[ ( "$1" = "sale-memo" ) ]]
then
    VERSION=$SALE_MEMO_VERSION
    URI=$SALE_MEMO_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$SALE_MEMO_HOST$URI\/api\/$SALE_MEMO_VERSION"
    TITLE=$SALE_MEMO_TITLE
    DIRECTORY=$SALE_MEMO_DIR
elif [[ ( "$1" = "mbo-mailer" ) ]]
then
    VERSION=$MBO_MAILER_VERSION
    URI=$MBO_MAILER_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$MBO_MAILER_HOST$URI\/api\/$MBO_MAILER_VERSION"
    TITLE=$MBO_MAILER_TITLE
    DIRECTORY=$MBO_MAILER_DIR
elif [[ ( "$1" = "samsung" ) ]]
then
    VERSION=$SAMSUNG_VERSION
    URI=$SAMSUNG_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$SAMSUNG_HOST$URI\/api\/$SAMSUNG_VERSION"
    TITLE=$SAMSUNG_TITLE
    DIRECTORY=$SAMSUNG_DIR
elif [[ ( "$1" = "product" ) ]]
then
    VERSION=$PRODUCT_VERSION
    URI=$PRODUCT_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PRODUCT_HOST$URI\/api\/$PRODUCT_VERSION"
    TITLE=$PRODUCT_TITLE
    DIRECTORY=$PRODUCT_DIR
elif [[ ( "$1" = "license" ) ]]
then
    VERSION=$LICENSE_VERSION
    URI=$LICENSE_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$LICENSE_HOST$URI\/api\/$LICENSE_VERSION"
    TITLE=$LICENSE_TITLE
    DIRECTORY=$LICENSE_DIR
elif [[ ( "$1" = "license_v2" ) ]]
then
    VERSION=$LICENSE_V2_VERSION
    URI=$LICENSE_V2_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$LICENSE_V2_HOST$URI\/api\/$LICENSE_V2_VERSION"
    TITLE=$LICENSE_V2_TITLE
    DIRECTORY=$LICENSE_V2_DIR
elif [[ ( "$1" = "venesa" ) ]]
then
    VERSION=VENESA_VERSION
    URI=VENESA_URI
    DOMAIN="$VENESA_HOST"
    TITLE=$VENESA_TITLE
    DIRECTORY=$VENESA_DIR
elif [[ ( "$1" = "minipos" ) ]]
then
    VERSION=$MINIPOS_VERSION
    URI=$MINIPOS_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$MINIPOS_HOST$URI\/api\/$MINIPOS_VERSION"
    TITLE=$MINIPOS_TITLE
    DIRECTORY=$MINIPOS_DIR
elif [[ ( "$1" = "landingpage" ) ]]
then
    VERSION=$LANDINGPAGE_VERSION
    URI=$LANDINGPAGE_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$LANDINGPAGE_HOST$URI\/api\/$VERSION"
    TITLE=$LANDINGPAGE_TITLE
    DIRECTORY=$LANDINGPAGE_DIR
elif [[ ( "$1" = "audience_v2" ) ]]
then
    VERSION=$AUDIENCE_VERSION
    URI=$AUDIENCE_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$AUDIENCE_HOST$URI\/api\/$AUDIENCE_VERSION"
    echo "$DOMAIN"
    TITLE=$AUDIENCE_TITLE
    DIRECTORY=$AUDIENCE_DIR
elif [[ ( "$1" = "profiling_etl" ) ]]
then
    VERSION=$PROFILING_ETL_VERSION
    URI=$PROFILING_ETL_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PROTOCOL$PROFILING_ETL_HOST$URI\/api\/$PROFILING_ETL_VERSION"
    echo "$DOMAIN"
    TITLE=$PROFILING_ETL_TITLE
    DIRECTORY=$PROFILING_ETL_DIR
elif [[ ( "$1" = "voucher" ) ]]
then
    VERSION=$VOUCHER_VERSION
    URI=$VOUCHER_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$VOUCHER_HOST"
    TITLE=$VOUCHER_TITLE
    DIRECTORY=$VOUCHER_DIR
elif [[ ( "$1" = "journey" ) ]]
then
    VERSION=$JOURNEY_VERSION
    URI=$JOURNEY_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$JOURNEY_HOST"
    TITLE=$JOURNEY_TITLE
    DIRECTORY=$JOURNEY_DIR
elif [[ ( "$1" = "survey" ) ]]
then
    VERSION=$SURVEY_VERSION
    URI=$SURVEY_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$SURVEY_HOST"
    TITLE=$SURVEY_TITLE
    DIRECTORY=$SURVEY_DIR
elif [[ ( "$1" = "samsung_microsite" ) ]]
then
    VERSION=$SAMSUNG_MICROSITE_VERSION
    URI=$SAMSUNG_MICROSITE_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$SAMSUNG_MICROSITE_HOST"
    TITLE=$SAMSUNG_MICROSITE_TITLE
    DIRECTORY=$SAMSUNG_MICROSITE_DIR
elif [[ ( "$1" = "media" ) ]]
then
    VERSION=$MEDIA_VERSION
    URI=$MEDIA_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$MEDIA_HOST"
    TITLE=$MEDIA_TITLE
    DIRECTORY=$MEDIA_DIR
elif [[ ( "$1" = "task" ) ]]
then
    VERSION=$TASK_VERSION
    URI=$TASK_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$TASK_HOST"
    TITLE=$TASK_TITLE
    DIRECTORY=$TASK_DIR
elif [[ ( "$1" = "note" ) ]]
then
    VERSION=$NOTE_VERSION
    URI=$NOTE_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$NOTE_HOST"
    TITLE=$NOTE_TITLE
    DIRECTORY=$NOTE_DIR
elif [[ ( "$1" = "mobio_template" ) ]]
then
    VERSION=$MOBIO_TEMPLATE_VERSION
    URI=$MOBIO_TEMPLATE_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$MOBIO_TEMPLATE_HOST"
    TITLE=$MOBIO_TEMPLATE_TITLE
    DIRECTORY=$MOBIO_TEMPLATE_DIR
elif [[ ( "$1" = "monitor_system" ) ]]
then
    VERSION=$MONITOR_SYSTEM_VERSION
    URI=$MONITOR_SYSTEM_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$MONITOR_SYSTEM_HOST"
    TITLE=$MONITOR_SYSTEM_TITLE
    DIRECTORY=$MONITOR_SYSTEM_DIR
elif [[ ( "$1" = "shortenurl" ) ]]
then
    VERSION=$SHORTENURL_VERSION
    URI=$SHORTENURL_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$SHORTENURL_HOST"
    TITLE=$SHORTENURL_TITLE
    DIRECTORY=$SHORTENURL_DIR
elif [[ ( "$1" = "profile_scan" ) ]]
then
    VERSION="v1.0"
    URI=""
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="profile_scan"
    TITLE="Profile Scan"
    DIRECTORY="profile_scan"
elif [[ ( "$1" = "dynamic_event" ) ]]
then
    VERSION="v1.0"
    URI=""
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="dynamic_event"
    TITLE="dynamic_event"
    DIRECTORY="dynamic_event"
elif [[ ( "$1" = "transaction_hub" ) ]]
then
    VERSION=""
    URI=""
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$TRANSACTION_HUB_HOST"
    TITLE=$TRANSACTION_HUB_TITLE
    DIRECTORY=$TRANSACTION_HUB_DIR
elif [[ ( "$1" = "product_bank" ) ]]
then
    VERSION=""
    URI=""
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$PRODUCT_BANK_URI"
    TITLE=$PRODUCT_BANK_TITLE
    DIRECTORY=$PRODUCT_BANK_DIR
elif [[ ( "$1" = "market_place" ) ]]
then
    VERSION=""
    URI=""
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$MARKET_PLACE_URI"
    TITLE=$MARKET_PLACE_TITLE
    DIRECTORY=$MARKET_PLACE_DIR
elif [[ ( "$1" = "market_place_report" ) ]]
then
    VERSION=""
    URI=""
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$MARKET_PLACE_REPORT_URI"
    TITLE=$MARKET_PLACE_REPORT_TITLE
    DIRECTORY=$MARKET_PLACE_REPORT_DIR
elif [[ ( "$1" = "kpi-management" ) ]]
then
    VERSION=$KPI_MANAGEMENT_VERSION
    URI=$SALE_MEMO_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$KPI_MANAGEMENT_HOST$URI\/api\/$KPI_MANAGEMENT_VERSION"
    TITLE=$KPI_MANAGEMENT_TITLE
    DIRECTORY=$KPI_MANAGEMENT_DIR
elif [[ ( "$1" = "backend_mobile_eib" ) ]]
then
    VERSION=$BACKEND_MOBILE_EIB_VERSION
    URI=$BACKEND_MOBILE_EIB_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$BACKEND_MOBILE_EIB_HOST$URI\/api\/$BACKEND_MOBILE_EIB_VERSION"
    TITLE=$BACKEND_MOBILE_EIB_TITLE
    DIRECTORY=$BACKEND_MOBILE_EIB_DIR
elif [[ ( "$1" = "ladder" ) ]]
then
    VERSION=$LADDER_VERSION
    URI=$LADDER_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$LADDER_HOST$URI\/api\/$LADDER_VERSION"
    TITLE=$LADDER_TITLE
    DIRECTORY=$LADDER_DIR
elif [[ ( "$1" = "kbm" ) ]]
then
    VERSION=$KBM_VERSION
    URI=$KBM_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$KBM_HOST$URI\/api\/$KBM_VERSION"
    TITLE=$KBM_TITLE
    DIRECTORY=$KBM_DIR
elif [[ ( "$1" = "web-form-builder" ) ]]
then
    VERSION=$WFB_VERSION
    URI=$WFB_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$WFB_HOST$URI\/api\/$WFB_VERSION"
    TITLE=$WFB_TITLE
    DIRECTORY=$WFB_DIR
elif [[ ( "$1" = "calculation_attribute" ) ]]
then
    VERSION=$CCALCULATION_ATTRIBUTE_VERSION
    URI=$CALCULATION_ATTRIBUTE_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$CALCULATION_ATTRIBUTE_HOST$URI\/api\/$CALCULATION_ATTRIBUTE_VERSION"
    TITLE=$CALCULATION_ATTRIBUTE_TITLE
    DIRECTORY=$CALCULATION_ATTRIBUTE_DIR
elif [[ ( "$1" = "communication_hub" ) ]]
then
    VERSION=$COMMUNICATION_HUB_VERSION
    URI=$COMMUNICATION_HUB_URI
    if [ ! -z "$URI" -a "$URI" != " " ];
    then
        URI="\/$URI"
    fi
    DOMAIN="$COMMUNICATION_HUB_HOST$URI\/api\/$COMMUNICATION_HUB_VERSION"
    TITLE=$COMMUNICATION_HUB_TITLE
    DIRECTORY=$COMMUNICATION_HUB_DIR
else
    normal=$(tput sgr0)
    green='\033[0;32m'
    
    echo "gen_doc.sh MODULE_NAME"
    echo "MODULE_NAME: "
    echo "\tSocial CRM: ${green}social${normal}"
    echo "\tCallCenter: ${green}callcenter${normal} or ${green}cc${normal}"
    echo "\tCampaign: ${green}campaign${normal} or ${green}camp${normal}"
    echo "\tEmailMarketing: ${green}emailmarketing${normal} or ${green}emk${normal} or ${green}email${normal}"
    echo "\tCustomer Insights: ${green}insights${normal}"
    echo "\tCustomer Care: ${green}retails${normal}"
    echo "\tSessionManager: ${green}sessionmanager${normal} or ${green}sm${normal}"
    echo "\tAdministrator: ${green}administrator${normal} or ${green}adm${normal} or ${green}admin${normal}"
    echo "\tAccounting: ${green}accounting${normal} or ${green}acc${normal}"
    echo "\tPushSMS: ${green}sms${normal}"
    echo "\tEmailSender: ${green}emailsender${normal} or ${green}emsender${normal} or ${green}es${normal}"
    echo "\tUserModeling: ${green}um${normal} or ${green}usermodeling${normal}"
    echo "\tLoyalty: ${green}loyalty${normal}"
    echo "\tspamscore: ${green}spamscore${normal}"
    echo "\tTemplateHtml: ${green}templatehtml${normal}"
    echo "\tChatTool: ${green}chattool${normal}"
    echo "\tTicket: ${green}ticket${normal}"
    echo "\tSale: ${green}sale${normal}"
    echo "\tProduct: ${green}product${normal}"
    echo "\tProfiling Bank: ${green}profiling_bank${normal}"
    echo "\tJourney: ${green}journey${normal}"
    echo "\tKpiManagement: ${green}kpi_management${normal}"
fi

if [ ! -z "$DOMAIN" ]
then
    if [[("$1" = "chattool")]]
    then
        echo "create backup..."
        cp "apidoc.json" "apidoc.bak"
        echo "copy apidoc_chattool.json > apidoc.json..."
        cp apidoc_chattool.json apidoc.json
        echo "generate doc..."
        echo "execute: apidoc -i src/common/ -i src/$DIRECTORY/ -o dist/$DIRECTORY/"
        apidoc -i src/common/ -i src/$DIRECTORY/ -o dist/$DIRECTORY/
        echo "restore backup..."
        mv apidoc.bak apidoc.json
        echo "OK"
    elif [[("$1" = "profiling")]]
    then
        echo "create backup apidoc_mobio_v2..."
        cp "apidoc_mobio_v2.json" "apidoc.bak"
        echo "replace text apidoc_mobio_v2..."
        sed -E "s/(\"title\"[ ]*:[ ]*)\"[^\"]+\"/\1\"$TITLE\"/g" apidoc_mobio_v2.json > apidoc.tmp
        sed -E "s/(\"url\"[ ]*:[ ]*)\"[^\"]+\"/\1\"$DOMAIN\"/g" apidoc.tmp > apidoc_mobio_v2.json
        echo "generate doc apidoc_mobio_v2..."
        echo "execute: apidoc -i src/common/ -i src/$DIRECTORY/ -o dist/$DIRECTORY/"
        apidoc -i src/common/ -i src/$DIRECTORY/ -o dist/$DIRECTORY/
        echo "restore backup apidoc_mobio_v2..."
        mv apidoc.bak apidoc_mobio_v2.json
        echo "clean temp apidoc_mobio_v2..."
        rm apidoc.tmp
        echo "OK"
    elif [[("$1" = "profiling_bank")]]
    then
        echo "create backup..."
        cp "apidoc_mobio_v2.json" "apidoc.bak"
        echo "replace text..."
        sed -E "s/(\"title\"[ ]*:[ ]*)\"[^\"]+\"/\1\"$TITLE\"/g" apidoc.json > apidoc.tmp
        sed -E "s/(\"url\"[ ]*:[ ]*)\"[^\"]+\"/\1\"$DOMAIN\"/g" apidoc.tmp > apidoc.json
        echo "generate doc..."
        echo "execute: apidoc -i src/common/ -i src/$DIRECTORY/ -o dist/$DIRECTORY/"
        apidoc -i src/common/ -i src/$DIRECTORY/ -o dist/$DIRECTORY/
        echo "restore backup..."
        mv apidoc.bak apidoc.json
        echo "clean temp..."
        rm apidoc.tmp
        echo "OK"
    else
        echo "create backup..."
        cp "apidoc.json" "apidoc.bak"
        echo "replace text..."
        sed -E "s/(\"title\"[ ]*:[ ]*)\"[^\"]+\"/\1\"$TITLE\"/g" apidoc.json > apidoc.tmp
        sed -E "s/(\"url\"[ ]*:[ ]*)\"[^\"]+\"/\1\"$DOMAIN\"/g" apidoc.tmp > apidoc.json
        echo "generate doc..."
        echo "execute: apidoc -i src/common/ -i src/$DIRECTORY/ -o dist/$DIRECTORY/"
        apidoc -i src/common/ -i src/$DIRECTORY/ -o dist/$DIRECTORY/
        echo "restore backup..."
        mv apidoc.bak apidoc.json
        echo "clean temp..."
        rm apidoc.tmp
        echo "OK"
    fi
else
    echo "create backup..."
    cp "apidoc.json" "apidoc.bak"
    echo "replace text..."
    sed -E "s/(\"title\"[ ]*:[ ]*)\"[^\"]+\"/\1\"$TITLE\"/g" apidoc.json > apidoc.tmp
    sed -E "s/(\"url\"[ ]*:[ ]*)\"[^\"]+\"/\1\"$DOMAIN\"/g" apidoc.tmp > apidoc.json
    echo "generate doc..."
    echo "execute: apidoc -i src/common/ -i src/$DIRECTORY/ -o dist/$DIRECTORY/"
    apidoc -i src/common/ -i src/$DIRECTORY/ -o dist/$DIRECTORY/
    echo "restore backup..."
    mv apidoc.bak apidoc.json
    echo "clean temp..."
    rm apidoc.tmp
    echo "OK"
fi

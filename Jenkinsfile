pipeline {
    agent any

    environment {
        DOCKER_IMAGE = 'test-mcr.mobio.vn/tools/apidoc:latest'
        WORK_DIR = "${WORKSPACE}"
        SHORT_BRANCH = "${env.BRANCH_NAME.tokenize('/').last()}"
        REMOTE_HOST = '************'
        REMOTE_USER = 'apidoc'
        REMOTE_PATH = "/media/data/api_docs/${SHORT_BRANCH}"
    }

    stages {
        stage('Build docs') {
            steps {
                script {
                    try {
                        echo "Building docs for branch: ${env.BRANCH_NAME} (short name: ${SHORT_BRANCH})"
                        dir("${WORK_DIR}") {
                            sh """
                                docker run --rm \
                                    -v ${WORK_DIR}:/app \
                                    ${DOCKER_IMAGE} \
                                    /app/gen_doc_cicd.sh ${SHORT_BRANCH}
                            """
                        }
                    } catch (Exception e) {
                        echo "Error during build: ${e.getMessage()}"
                        throw e
                    }
                }
            }
        }

        stage('Transfer to remote server') {
            steps {
                script {
                    try {
                        echo "Transferring dist/${SHORT_BRANCH} to ${REMOTE_HOST}"
                        withCredentials([usernamePassword(credentialsId: 'apidoc-mobiodev', usernameVariable: 'SSH_USER', passwordVariable: 'SSH_PASS')]) {
                            sh """
                                sshpass -p \${SSH_PASS} ssh ${SSH_USER}@${REMOTE_HOST} "mkdir -p ${REMOTE_PATH}"
                                sshpass -p \${SSH_PASS} rsync -avz --delete \
                                    -e "ssh -o StrictHostKeyChecking=no" \
                                    ${WORK_DIR}/dist/${SHORT_BRANCH}/ ${SSH_USER}@${REMOTE_HOST}:${REMOTE_PATH}/
                            """
                        }
                    } catch (Exception e) {
                        echo "Error during transfer: ${e.getMessage()}"
                        throw e
                    }
                }
            }
        }
    }

    post {
        always {
            echo "Cleaning up workspace"
            cleanWs()
        }
        success {
            echo "Documentation build and transfer completed successfully for branch: ${env.BRANCH_NAME}"
        }
        failure {
            echo "Documentation build or transfer failed for branch: ${env.BRANCH_NAME}"
        }
    }
}